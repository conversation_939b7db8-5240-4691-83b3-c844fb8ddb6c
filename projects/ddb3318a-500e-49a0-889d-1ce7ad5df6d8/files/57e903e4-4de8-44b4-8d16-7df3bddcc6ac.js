!function(){"use strict";const e=(()=>{try{const e="__storage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})(),t=(e,t)=>e<t?-1:e>t?1:0,n=e=>e.replace(/"/g,'\\"'),a=(t="",n=new FormData,a=null)=>{const o={url:t,method:"POST",body:n,callback:a};if("/free"===t){const t=t=>{a&&a(t),e&&"chart"in t&&localStorage.setItem("chart",t.chart)};o.callback=t,e?o.body.append("chart",localStorage.getItem("chart")||"{}"):o.body.append("chart","{}")}(e=>{const t=e.querystr?`${e.url}?${e.querystr}`:e.url,n="POST"===e.method&&document.body.dataset.token||null,a=document.querySelector("#page-message");(async(e,t,n=null,a=null,o=null)=>{const r=((e,t=null,n=null)=>{const a={method:e,mode:"same-origin"};return n&&"POST"===e&&(a.headers={"X-CSRF-TOKEN":n}),t&&"POST"===e&&(a.body=t),a})(t,n,a),i=await fetch(e,r);return 404===i.status?window.location="/404":401===i.status||403===i.status?window.location.reload():o&&(i.ok?o.classList.contains("hidden")||o.classList.add("hidden"):(o.querySelector("p").textContent="Something went wrong, please try again",o.classList.remove("hidden"))),i.text()})(t,e.method,e.body,n,a).then((t=>{let n={};try{n=JSON.parse(t),("object"!=typeof n||null===n||Array.isArray(n))&&(n={rejected:!0})}catch(e){n.rejected=e}n.rejected?a&&(a.querySelector("p").textContent="Something went wrong, please try again",a.classList.remove("hidden")):n.redirect?window.location=n.redirect:e.callback&&e.callback(n)}))})(o)},o=(e,t=!1)=>{const n=new Date(e);return t?n.toLocaleDateString():n.toLocaleString()},r=(e,t,n=!1,a=null)=>{for(const[o,r]of Object.entries(t))n?e.setAttributeNS(a,o,r):e.setAttribute(o,r)},i=(e,t)=>{for(const n of e)n.match?n.elem.classList.remove("hidden"):n.elem.classList.add("hidden"),t.appendChild(n.elem)},s=(e,n,a=!1,o=null)=>{const r={...n};let s=[],d=[],l=[];if(e){d=new Fuse(Object.values(n),{keys:["text"],includeScore:!0}).search(e).map((e=>(delete r[e.item.id],{...e.item,score:e.score,match:!0}))),s=Object.values(r).map((e=>({...e,match:!1}))).sort(((e,n)=>t(e.text,n.text)))}else d=Object.values(r).map((e=>({...e,match:!0}))).sort(((e,n)=>t(e.text,n.text)));return l=[...d,...s],a&&o&&i(l,o),l},d={wrapper:"dropdown-button-wrapper",trigger:"dropdown-button",menu:"dropdown-menu",option:"dropdown-option",visible:"visible-state",focused:"focused-state",disabled:"disabled-state"},l={wrapper:"nested-dropdown-button-wrapper",trigger:"nested-dropdown-button",menu:"nested-dropdown-menu",option:"nested-dropdown-option",visible:"visible-state",focused:"focused-state",disabled:"disabled-state"},c={wrapper:"dropdown-field-wrapper",trigger:"dropdown-field",text:"dropdown-text-input",value:"dropdown-value-input",button:"dropdown-button",icon:"dropdown-button-icon",menu:"dropdown-menu",option:"dropdown-option",selected:"selected-state",visible:"visible-state",focused:"focused-state"},u={},p={focused:null},m=e=>u[e.dataset.dropdownify],f=()=>{for(const[e,t]of Object.entries(u)){document.querySelector(`.${t.cls.wrapper}[data-dropdownify="${e}"]`)||delete u[e]}},h=(e,t,n)=>{const a="show"in t&&t.show.length,o="hide"in t&&t.hide.length;"show"===n?(o&&e.classList.remove(...t.hide),a&&e.classList.add(...t.show)):"hide"===n&&(a&&e.classList.remove(...t.show),o&&e.classList.add(...t.hide))},y=(e,t=null)=>{const n=t||m(e),{top:a,bottom:o,left:r,right:i,width:s}=n.trigger.getBoundingClientRect(),d=window.innerHeight,l=(()=>{let{anchor:t,direction:n,matchwidth:a}=e.dataset;return t=t||"bottom",n=n||"right",a="true"===a,{anchor:t,direction:n,matchwidth:a}})(),c=(e.setAttribute("style","display: block;"),l.matchwidth&&e.setAttribute("width",`${s}px`),e.getBoundingClientRect());let u={display:"block"};l.matchwidth&&(u.width=`${s}px`),"left"===l.anchor||"right"===l.anchor?("left"===l.anchor?u.left=r-c.width+"px":u.left=`${i}px`,"up"===l.direction?(u.top=(o>c.height+20?o-c.height:20)+"px",u["max-height"]=d-u.top-20+"px"):(u.top=`${a}px`,u["max-height"]=d-a-20+"px")):("top"===l.anchor?u.top=o-c.height+"px":(u.top=`${o}px`,u["max-height"]=d-o-20+"px"),"left"===l.direction?u.left=i-c.width+"px":u.left=`${r}px`),u=Object.entries(u).reduce(((e,[t,n])=>`${e}${t}:${n};`),""),e.setAttribute("style",u),setTimeout((()=>{"trigger"in n.transition&&h(n.trigger,n.transition.trigger,"show"),e.classList.add(n.cls.visible),n.trigger.getAttribute("aria-expanded")&&n.trigger.setAttribute("aria-expanded","true")}))},b=(e,t=null)=>{const n=t||m(e);e.classList.remove(n.cls.visible),setTimeout((()=>{e.setAttribute("style","display: none;"),n.trigger.getAttribute("aria-expanded")&&n.trigger.setAttribute("aria-expanded","false")}),100),"trigger"in n.transition&&h(n.trigger,n.transition.trigger,"hide");for(const t of e.querySelectorAll(`.${n.cls.option}.${n.cls.focused}`))t.classList.remove(n.cls.focused)},g=(e,t=null)=>{const n=e?e.dataset.dropdownify:null;for(const[e,a]of Object.entries(u)){e!==n&&(!t||t&&t===a.nest)&&a.menu.classList.contains(a.cls.visible)&&b(a.menu,a)}},w=e=>{g(e.target)},v={button:(()=>{const e=(e,t=!1)=>{const n=m(e),{cls:a,menu:o,wrapper:r}=n;o.classList.contains(a.visible)?b(o,n):(y(o,n),t?g(r,n.nest):g(r))},t=t=>{t.stopPropagation(),e(t.currentTarget)},n=t=>{t.stopPropagation(),e(t.currentTarget,!0)},a=e=>{const t=e.currentTarget,{wrapper:n}=m(t);e.stopPropagation(),b(t),g(n)},o=e=>{const t=e.currentTarget,{wrapper:n,nest:a}=m(t);e.stopPropagation(),b(t),a||g(n)},i=(e,t=!1)=>{const{cls:n,menu:a}=e,o=a.querySelector(`.${n.option}.${n.focused}`);let r=null;if(o)o.classList.remove(n.focused),r=t?o.previousElementSibling:o.nextElementSibling;else{const e=[...a.querySelectorAll(`.${n.option}`)];if(e.length){r=e[t?e.length-1:0]}}for(;r;){if(!r.classList.contains(n.disabled)){r.classList.add(n.focused);break}r=t?r.previousElementSibling:r.nextElementSibling}},s=e=>{if(["Tab","Enter","Escape","Esc","ArrowDown","Down","ArrowUp","Up","ArrowRight","Right","ArrowLeft","Left"].includes(e.key)){const t=e.currentTarget,n=m(t),{cls:a,menu:o}=n;if(o.classList.contains(a.visible)){const t=o.querySelector(`.${a.option}.${a.focused}`),r=t?t.dataset.dropdownify:null,s=r?m(t):null;let d=!1;if(s&&s.cls&&s.menu&&(d=s.menu.classList.contains(s.cls.visible)),"Tab"===e.key)b(o,n),d&&b(s.menu,s);else if("Escape"===e.key||"Esc"===e.key)e.stopPropagation(),d?b(s.menu,s):b(o,n);else if(["ArrowDown","Down","ArrowUp","Up"].includes(e.key)){const t=["ArrowUp","Up"].includes(e.key);let a=null;if(d){const e=s.menu,t=s.cls;a=e.querySelector(`.${t.option}.${t.focused}`)}a?i(s,t):(i(n,t),d&&b(s.menu,s))}else if(t)if(d){const t=s.menu,n=s.cls,a=t.querySelector(`.${n.option}.${n.focused}`);"Enter"===e.key?a.click():"ArrowLeft"===e.key||"Left"===e.key?b(s.menu,s):a||i(s)}else"Enter"===e.key&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),r?t.firstElementChild.click():t.click())}}},c=(e,n)=>{e.addEventListener("click",t),e.addEventListener("keydown",s),n.addEventListener("click",o)},p=(e,t)=>{e.addEventListener("click",n),t.addEventListener("click",a)},h=(e,t,n=!1)=>{const a=t.transition||{},o=(()=>{let e={...d};return"cls"in t&&(e={...e,...t.cls}),e})(),i=Object.keys(u);let s=i.length?Math.max(...i.map((e=>Number(e)))):1;for(const i of e){const e=i.querySelector(`.${o.trigger}`),d=i.querySelector(`.${o.menu}`);if(s+=1,u[s]={cls:o,transition:a,menu:d,trigger:e,wrapper:i,nest:null},i.setAttribute("data-dropdownify",s),e.setAttribute("data-dropdownify",s),r(d,{"data-dropdownify":s,id:`dropdownify${s}`}),c(e,d),n){const e=t.nested||{},n=e.transition||{},a=(()=>{let t={...l};return"cls"in e&&(t={...t,...e.cls}),t.container=o.menu,t})(),d=s,c=i.querySelectorAll(`.${a.wrapper}`);for(const e of c){const t=e.querySelector(`.${a.trigger}`),o=e.querySelector(`.${a.menu}`);s+=1,u[s]={cls:a,transition:n,menu:o,trigger:t,wrapper:e,nest:d},e.setAttribute("data-dropdownify",s),t.setAttribute("data-dropdownify",s),r(o,{"data-dropdownify":s,id:`dropdownify${s}`}),p(t,o),document.body.appendChild(o)}}document.body.appendChild(d)}},v=()=>{document.body.dataset.dropdownify||(document.body.addEventListener("click",w),document.body.setAttribute("data-dropdownify",!0))};return{cascadeHide:g,init:(e,t={})=>{let n=d.wrapper;"cls"in t&&"wrapper"in t.cls&&(n=t.cls.wrapper),e.classList.contains(n)&&(f(),v(),h([e],t,t.nest))},initAll:(e={})=>{const t=(()=>{let t=d.wrapper;return"cls"in e&&"wrapper"in e.cls&&(t=e.cls.wrapper),document.querySelectorAll(`.${t}`)})();f(),v(),h(t,e,e.nest)}}})(),field:(()=>{const e=(e,t,n)=>{const a="search"in t&&t.search.length,o="add"in t&&t.add.length,r="clear"in t&&t.clear.length;"search"===n?(o&&e.classList.remove(...t.add),r&&e.classList.remove(...t.clear),a&&e.classList.add(...t.search)):"add"===n?(a&&e.classList.remove(...t.search),r&&e.classList.remove(...t.clear),o&&e.classList.add(...t.add)):"clear"===n&&(a&&e.classList.remove(...t.search),o&&e.classList.remove(...t.add),r&&e.classList.add(...t.clear))},t=(t,n,a,o)=>{if(n.setAttribute("data-state",t),"icontext"in o){a.textContent=o.icontext[t]}"iconcls"in o&&e(a,o.iconcls,t),"button"in o&&e(n,o.button,t)},a=(e,n=!0,a=!0)=>{const{cls:o,transition:r,menu:i,text:s,value:d,button:l,icon:c}=e,u=i.querySelector(`.${o.option}.${o.selected}`);d.value="",s.setAttribute("data-state","hide"),a&&(s.value=""),u&&u.classList.remove(o.selected),t("search",l,c,r),n&&d.dispatchEvent(new Event("change"))},o=(e,n,a=!0)=>{const{cls:o,transition:r,text:i,value:s,button:d,icon:l,menu:c}=n,u=c.querySelector(`.${o.option}.${o.selected}`);e===u&&e.dataset.value===s.value||(u&&u.classList.remove(o.selected),e.classList.add(o.selected),i.value=e.dataset.label,s.value=e.dataset.value,t("clear",d,l,r),a&&s.dispatchEvent(new Event("change")))},d=e=>{const t=e.currentTarget,n=t.parentElement,a=m(n);o(t,a),b(n,a)},l=(e,t,n,a=null)=>{const o=a||m(e),{cls:i}=o,s=document.createDocumentFragment(),l=document.createElement("li"),c=document.createElement("span"),u=document.createTextNode(t),p={class:i.option,"data-label":t,"data-value":n};r(l,p),s.appendChild(l),l.appendChild(c),c.appendChild(u),l.addEventListener("click",d),e.appendChild(s)},h=(e,t)=>{const{callback:a,cls:r,menu:i}=t,s=n(e);l(i,e,e,t),o(i.querySelector(`.${r.option}[data-label="${s}"]`),t),b(i,t),"add"in a&&a.add(t,e,e,u)},v=e=>{const{callback:t}=e;a(e),"clear"in t&&t.clear(e)},k=e=>{const t=e.currentTarget,{state:n}=t.dataset,a=m(t);if(e.stopPropagation(),"clear"===n)v(a);else if("add"===n){const{text:e}=a;h(e.value.trim(),a)}},L=e=>{p.focused=e.currentTarget.dataset.dropdownify},S=e=>{const n=m(e),{callback:o,cls:r,transition:d,menu:l,button:c,icon:u}=n,p=[...l.querySelectorAll(`.${r.option}`)].reduce(((e,t,n)=>(e[n]={elem:t,id:n,text:t.dataset.label},e)),{}),f=e.value.trim();let h=l.querySelector(`.${r.option}.${r.selected}`),g=[];h&&h.dataset.label===f?h=!0:(h&&a(n,!1,!1),h=!1);for(const e of l.querySelectorAll(`.${r.option}.${r.focused}`))e.classList.remove(r.focused);g=s(f,p,!1),"filter"in o&&(g=o.filter(n,g)),f&&g.length&&g[0].match?h?e.setAttribute("data-state","clear"):e.setAttribute("data-state","select"):f?e.setAttribute("data-state","add"):e.setAttribute("data-state","hide"),g.length&&g[0].match?(i(g,l),y(l,n)):(b(l,n),i(g,l)),f?g.length&&f===g[0].text?t(h?"clear":"search",c,u,d):t("add",c,u,d):t("search",c,u,d)},x=e=>{S(e.currentTarget)},E=(e,o=!1)=>{const{cls:r,menu:i,text:s,button:d}=e,l=n(s.value),c=i.querySelector(`.${r.option}.${r.selected}`),u=i.querySelector(`.${r.option}.${r.focused}`);if(u){const e=o?u.previousElementSibling:u.nextElementSibling;e&&!e.classList.contains("hidden")&&(u.classList.remove(r.focused),e.classList.add(r.focused),s.value=e.dataset.label)}else{const e=[...i.querySelectorAll(`.${r.option}`)];for(const[t,n]of e.entries()){if(!o){t||n.classList.contains("hidden")?s.value="":(n.classList.add(r.focused),s.value=n.dataset.label);break}if(n.classList.contains("hidden")){t?(e[t-1].classList.add(r.focused),s.value=e[t-1].dataset.label):s.value="";break}t===e.length-1&&(n.classList.add(r.focused),s.value=n.dataset.label)}}if(c&&c.dataset.label!==s.value&&a(e,!1,!1),"add"===d.dataset.state&&i.querySelector(`.${r.option}[data-label="${l}"]`)){const{icon:n,transition:a}=e;t("search",d,n,a)}},q=e=>{const{text:t,wrapper:n}=e;S(t),g(n)},A=e=>{const{cls:t,text:a,button:r,menu:i}=e,s=a.value.trim();if(s){const a=n(s),r=i.querySelector(`.${t.option}[data-label="${a}"]`);r?(o(r,e),b(i,e)):h(s,e)}else v(e),b(i,e);r.addEventListener("click",k)},$=e=>{if(["Tab","Enter","Escape","Esc","ArrowDown","Down","ArrowUp","Up"].includes(e.key)){const t=e.currentTarget,n=m(t),{cls:a,menu:o}=n,r=o.classList.contains(a.visible);if("Enter"===e.key){const e=t.value.trim(),i=o.querySelector(`.${a.option}.${a.selected}`),s=i?i.dataset.label:"";r||e!==s?A(n):q(n)}else r&&("Tab"===e.key||"Escape"===e.key||"Esc"===e.key?(b(o,n),"Escape"!==e.key&&"Esc"!==e.key||e.stopPropagation()):"ArrowDown"===e.key||"Down"===e.key?E(n):"ArrowUp"!==e.key&&"Up"!==e.key||E(n,!0))}},T=e=>{const t=m(e.currentTarget),{cls:n,menu:a}=t;e.stopPropagation(),a.classList.contains(n.visible)?b(a,t):q(t)},D=(e,t,n,a,o)=>{e.addEventListener("click",T),t.addEventListener("focus",L),t.addEventListener("keydown",$),t.addEventListener("input",x),n.addEventListener("click",k);for(const e of a.querySelectorAll(`.${o.option}`))e.addEventListener("click",d)},P=(e,t)=>{const n=t.callback||{},a=t.transition||{},o=(()=>{let e={...c};return"cls"in t&&(e={...e,...t.cls}),e})(),i=Object.keys(u);let s=i.length?Math.max(...i.map((e=>Number(e)))):1;for(const t of e){const e=t.querySelector(`.${o.trigger}`),i=t.querySelector(`.${o.menu}`),d=t.querySelector(`.${o.text}`),l=t.querySelector(`.${o.value}`),c=t.querySelector(`.${o.button}`),p=c.querySelector(`.${o.icon}`);s+=1,u[s]={cls:o,callback:n,transition:a,wrapper:t,trigger:e,menu:i,text:d,value:l,button:c,icon:p},t.setAttribute("data-dropdownify",s),e.setAttribute("data-dropdownify",s),d.setAttribute("data-dropdownify",s),c.setAttribute("data-dropdownify",s),r(i,{"data-dropdownify":s,"data-matchwidth":!0,id:`dropdownify${s}`}),D(e,d,c,i,o),document.body.appendChild(i)}},H=e=>{"Tab"===e.key&&p.focused&&(A(u[p.focused]),p.focused=null)},C=e=>{if(g(e.target),p.focused){const t=u[p.focused];t.text!==e.target&&(p.focused=null,t.button!==e.target&&t.icon!==e.target&&A(t))}},O=()=>{document.body.dataset.dropdownify?(document.body.removeEventListener("keydown",H),document.body.removeEventListener("click",w),document.body.removeEventListener("click",C),document.body.addEventListener("keydown",H),document.body.addEventListener("click",C)):(document.body.addEventListener("keydown",H),document.body.addEventListener("click",C),document.body.setAttribute("data-dropdownify",!0))};return{cascadeHide:g,appendOption:l,bodyClickHandler:C,init:(e,t={})=>{let n=c.wrapper;"cls"in t&&"wrapper"in t.cls&&(n=t.cls.wrapper),e.classList.contains(n)&&(f(),O(),P([e],t))},initAll:(e={})=>{const t=(()=>{let t=c.wrapper;return"cls"in e&&"wrapper"in e.cls&&(t=e.cls.wrapper),document.querySelectorAll(`.${t}`)})();f(),O(),P(t,e)},clearSelection:(e,t=!0)=>{const n=m(e);a(n,t)},selectOption:(e,t,n=!0)=>{const a=m(e);o(t,a,n)},removeDBEntry:e=>{delete u[e],Object.keys(u).length||(document.body.removeEventListener("keydownn",H),document.body.removeEventListener("click",w),document.body.removeEventListener("click",C),document.body.removeAttribute("data-dropdownify"))}}})()},k={},L={delay:(...e)=>{const[t,n,a,o,r,i]=e;t in k&&n in k[t]&&a in k[t][n]&&(clearTimeout(k[t][n][a].timeout),k[t][n][a].timeout=setTimeout(o,r,i))},throttle:(...e)=>{const t=(e,t,n,a,o)=>{e in k&&t in k[e]&&n in k[e][t]&&(k[e][t][n].timeout=null,a(o))},[n,a,o,r,i,s]=e;n in k&&a in k[n]&&o in k[n][a]&&!k[n][a][o].timeout&&(k[n][a][o].timeout=setTimeout(t,i,n,a,o,r,s))}},S=(()=>{const e=(e,t,n,a=null,o=0)=>{if(e){const[[r,i]]=Object.entries(n);let s=e instanceof Element?e.dataset.eventify:1;if(1!==s||s in k||(k[s]={}),!s){const t=Object.keys(k);s=t.length?Math.max(...t.map((e=>Number(e))))+1:2,e.setAttribute("data-eventify",s),k[s]={}}t in k[s]||(k[s][t]={}),k[s][t][r]=a?{timeout:null,handleEvent:L[a].bind(e,s,t,r,i,o)}:{handleEvent:i},e.addEventListener(t,k[s][t][r])}};return{addListener:e,addListeners:(t,n,a,o=null,r=0)=>{for(const i of document.querySelectorAll(t))e(i,n,a,o,r)},removeListener:(e,t,n)=>{const a=e instanceof Element?e.dataset.eventify:1,[o]=Object.keys(n);a in k&&t in k[a]&&o in k[a][t]&&(e.removeEventListener(t,k[a][t][o]),delete k[a][t][o])},garbageCollection:()=>{for(const e of Object.keys(k)){document.querySelector(`[data-eventify="${e}"]`)||delete k[e]}}}})(),x=e=>{const t=new FormData;t.append("guide",e.currentTarget.value),a("/guide-toggle-handler",t)},E=(e=document)=>[...e.querySelectorAll('a[href], button, input, textarea, select, details,[tabindex]:not([tabindex="-1"])')].filter((e=>!e.hasAttribute("disabled"))),q={escapeCloseHandler:e=>{if("Escape"===e.key||"Esc"===e.key){const e=document.querySelector(".modal-wrapper.visible-state");e&&q.hide(e)}},hide:e=>{const t=e.querySelector(".modal-panel"),n=e.querySelector(".modal-scrim"),a=e.dataset.onhidefocus;if((()=>{for(const e of document.querySelectorAll(".modalize-aria-hidden"))e.removeAttribute("aria-hidden"),e.classList.remove("modalize-aria-hidden");for(const e of document.querySelectorAll(".modalize-tabindex")){const{tabindex:t}=e.dataset;t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex"),e.removeAttribute("data-tabindex"),e.classList.remove("modalize-tabindex")}})(),a){const e=document.querySelector(a);e?e.focus():document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus())}else document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus());t.classList.remove("visible-state","translate-y-0","sm:scale-100"),n.classList.remove("visible-state"),e.classList.remove("visible-state"),t.classList.add("translate-y-4","sm:translate-y-0","sm:scale-95"),setTimeout((()=>e.setAttribute("style","display: none;")),300),document.body.removeEventListener("keydown",q.escapeCloseHandler)},show:e=>{const t=e.querySelector(".modal-panel"),n=e.querySelector(".modal-scrim");document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus()),e.setAttribute("style","display: block;"),setTimeout((()=>{e.classList.add("visible-state"),n.classList.add("visible-state"),t.classList.remove("translate-y-4","sm:translate-y-0","sm:scale-95"),t.classList.add("visible-state","translate-y-0","sm:scale-100")})),document.body.addEventListener("keydown",q.escapeCloseHandler),(e=>{const t=e=>{e.getAttribute("aria-hidden")||(e.classList.add("modalize-aria-hidden"),e.setAttribute("aria-hidden","true"))},n=e=>{const t=e.getAttribute("tabindex");t&&e.setAttribute("data-tabindex",t),e.classList.add("modalize-tabindex"),e.setAttribute("tabindex","-1")};let a=e;for(;a.parentElement&&document.body!==a;){let e=a.nextElementSibling;for(;e;){t(e),n(e);for(const t of E(e))n(t);e=e.nextElementSibling}for(e=a.previousElementSibling;e;){t(e),n(e);for(const t of E(e))n(t);e=e.previousElementSibling}a=a.parentElement}})(e)},clickHandler:e=>{const t=e.currentTarget,n=document.querySelector(`#${t.dataset.modal}-modal-wrapper`);q.hide(n)}},A=e=>{const t=new FormData;t.append("action","copy"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},$=e=>{const t=new FormData;t.append("action","trash"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t,(e=>{e.chart_id&&document.querySelector(`#chart${e.chart_id}`).remove()}))},T={callback:{selectionHandler:()=>{},dragSelectionHandler:()=>{},editSelectionHandler:()=>{},nodeCrosshairHandler:()=>{},noteCrosshairHandler:()=>{},noteHandleHandler:()=>{},showNoteEditor:()=>{},clearLabelData:()=>{},syncDataDesignPanels:()=>{},chartModalCardHandler:()=>{}},classes:{elem:{link:"chart-link",linknote:"chart-linknote",node:"chart-node",note:"chart-note"},text:{linknote:["chart-linknote-name","chart-linknote-label","chart-linknote-content"],node:["chart-node-name","chart-node-label","chart-node-content"],note:["chart-note-content"]}},css:{".chart-node":{fill:"#ffffff",stroke:"rgba(156, 163, 175, 1)","stroke-dasharray":"none"},".chart-node-name":{fill:"rgba(55, 65, 81, 1)","font-size":"large","font-style":"normal","font-weight":"bold","text-anchor":"middle","text-decoration-line":"none"},".chart-node-label":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"},".chart-node-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-link":{stroke:"rgba(156, 163, 175, 1)","stroke-dasharray":"none"},".chart-linknote":{fill:"rgba(0, 0, 0, 0)",stroke:"rgba(0, 0, 0, 0)","stroke-dasharray":"none"},".chart-linknote-name":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-linknote-label":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"},".chart-linknote-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-note":{fill:"rgba(0, 0, 0, 0)",stroke:"rgba(0, 0, 0, 0)","stroke-dasharray":"none"},".chart-note-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"}},cursor:{node:"enabled",note:"enabled",select:"active"},layout:{linknote:{position:{content:"right",label:"left"}},node:{position:{content:"right",label:"left"}}},modal:{chart:{action:"add"},link:{action:"addlink"},node:{action:"addnode",x:null,y:null}},title:{current:"Untitled chart",default:"Untitled chart"},selections:{all:0,dragids:{link:[],linknote:[],node:[],note:[]},ids:{link:[],linknote:[],node:[],note:[]}},shiftpath:{bottom:1,left:0,right:1,top:0}},D={dropdownify:{button:{nest:!0},field:{callback:{add:(e,t,a,o)=>{if(e.wrapper.dataset.linked){const r=o[document.querySelector(`#${e.wrapper.dataset.linked}`).dataset.dropdownify],i=n(a);r.menu.querySelector(`.dropdown-option[data-value="${i}"]`)||v.field.appendOption(r.menu,t,a)}else{const r=(()=>{let t=e.wrapper;for(;t.parentElement&&!t.classList.contains("dropdown-sync-group");)t=t.parentElement;return t})();if(r.classList.contains("dropdown-sync-group")){const e=r.querySelectorAll(".dropdown-field-wrapper");for(const r of e){const e=o[r.dataset.dropdownify],i=n(a);e.menu.querySelector(`.dropdown-option[data-value="${i}"]`)||v.field.appendOption(e.menu,t,a)}}}},clear:e=>{v.field.cascadeHide(e.wrapper)},filter:(e,n)=>{let a=n;if(["link-modal-parent","link-modal-child","parent-dropdown-card","child-dropdown-card"].includes(e.wrapper.id)){const n=new Set;let o=null;if(["link-modal-parent","link-modal-child"].includes(e.wrapper.id)){let t=null;if((T.selections.ids.link.length||T.selections.ids.linknote.length)&&([t]=[...T.selections.ids.link,...T.selections.ids.linknote]),"link-modal-parent"===e.wrapper.id){if(o=document.querySelector('input[name="link-modal-child-value"]').value.trim(),o)for(const e of T.links)e.child_id!==o||t&&e.id===t||n.add(e.parent_id)}else if("link-modal-child"===e.wrapper.id&&(o=document.querySelector('input[name="link-modal-parent-value"]').value.trim(),o))for(const e of T.links)e.parent_id!==o||t&&e.id===t||n.add(e.child_id)}else if(o=document.querySelector('input[name="chart-modal-node-value"]').value.trim(),o)if("parent-dropdown-card"===e.wrapper.id)for(const e of T.links)e.child_id===o&&n.add(e.parent_id);else if("child-dropdown-card"===e.wrapper.id)for(const e of T.links)e.parent_id===o&&n.add(e.child_id);if(o){n.add(o);for(const e of a)n.has(e.elem.dataset.value)&&(e.match=!1);a=a.sort(((e,n)=>e.match!==n.match?e.match?-1:1:e.match?"score"in e&&"score"in n?e.score-n.score:t(e.text,n.text):0))}}return a}},transition:{button:{search:[],add:["cursor-pointer"],clear:["cursor-pointer"]},iconcls:{search:["text-gray-400"],add:["text-orange-500"],clear:["text-orange-500"]},icontext:{search:"search",add:"add_circle",clear:"cancel"},trigger:{show:[],hide:["rounded-b-md"]}}}},dynamify:{callback:{add:e=>{const t=e.dataset.modal,n=e.querySelector(".dropdown-field-wrapper"),a=document.querySelector(`#dropdownify${n.dataset.dropdownify}`).cloneNode(!0),o=e.querySelector(".clear-dynamic-item"),r=e.querySelectorAll(`input[name="${t}-modal-label-value"], input[name="${t}-modal-data"]`);if(n.appendChild(a),v.field.init(n,D.dropdownify.field),v.field.clearSelection(n,!1),v.field.cascadeHide(),o.addEventListener("click",T.callback.clearLabelData),r.length)for(const e of r)e.addEventListener("change",T.callback.syncDataDesignPanels)},remove:e=>{const t=e.querySelector(".dropdown-field-wrapper").dataset.dropdownify;document.querySelector(`#dropdownify${t}`).remove(),v.field.removeDBEntry(t)}}}},P={filter:{private:!0,shared:!0,invited:!0,removed:!1},sort:{sort:"date",order:"asc"}},H=e=>{if("Enter"===e.key&&e.currentTarget===document.activeElement){const{url:t}=e.currentTarget.dataset;t&&(window.location=t)}},C=e=>{const{url:t}=e.currentTarget.dataset;e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),t&&(window.location=t)},O=e=>{e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),window.location=e.currentTarget.dataset.url},I=e=>{const t=new FormData;t.append("action","untrash"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},j=e=>{const t=new FormData;t.append("action","delete"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},F=e=>{const t=new FormData;t.append("action","trashpermission"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},_=e=>{const t=new FormData;t.append("action","untrashpermission"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},N=e=>{const t=new FormData;t.append("action","deletepermission"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},z=e=>{const t=new FormData;t.append("action","acceptinvite"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},U=e=>{const t=new FormData;t.append("action","rejectinvite"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},J=e=>{const t=new FormData;t.append("action","deleteinvite"),t.append("id",e.currentTarget.dataset.id),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),a(window.location.pathname,t)},R=e=>{const t=document.querySelector("#remove-modal-wrapper"),n=t.querySelector('input[name="id"]');e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),n.value=e.currentTarget.dataset.id;for(const e of t.querySelectorAll('input[type="radio"]'))"trashpermission"===e.value?e.checked=!0:e.checked=!1;q.show(t)},B=e=>{const t=document.querySelector("#remove-modal-wrapper"),n=t.querySelector("form"),o=new FormData(n);e.preventDefault(),a(window.location.pathname,o,(e=>{const n=t.querySelector("#remove-modal-message");e.error&&e.error.length?(n.querySelector("p").textContent=e.error.join(" "),n.classList.remove("hidden")):(n.classList.add("hidden"),q.hide(t))}))},M=(n={})=>{const a=document.querySelector("#chart-data-list"),o=document.querySelectorAll(".chart-data"),r=n;for(const t of Object.keys(P))if(!(t in r)){let n=null;e&&(n=localStorage.getItem(t),n&&(n=JSON.parse(n))),n||(n=P[t]),r[t]=n}if(!("search"in r)){const e=document.querySelector("#search");let t="";e&&(t=e.value.trim()),r.search={value:t}}if(r.search.value){const e=[...o].reduce(((e,t)=>(e[t.id]={elem:t,id:t.id,text:t.dataset.name},e)),{}),t=s(r.search.value,e,!1);for(const e of t)e.match?e.elem.setAttribute("data-searchsort","show"):e.elem.setAttribute("data-searchsort","hide"),a.appendChild(e.elem)}else{const{sort:e,order:n}=r.sort,i=document.querySelector(`.sort-charts[data-sort="${e}"]`),s=[...o].sort(((a,o)=>{const r=a.dataset[e],i=o.dataset[e];return"desc"===n?t(r,i):t(i,r)}));for(const e of s)e.setAttribute("data-searchsort","show"),a.appendChild(e);for(const t of document.querySelectorAll(".sort-charts"))if(t.dataset.sort!==e){t.querySelector(".material-icons").textContent=""}if(i){const e=i.querySelector(".material-icons");"asc"===n?(i.dataset.order="desc",e.textContent=i.dataset.desc):"desc"===n&&(i.dataset.order="asc",e.textContent=i.dataset.asc)}}for(const[e,t]of Object.entries(r.filter)){const n=document.querySelectorAll(`.chart-data[data-status="${e}"]`),a=document.querySelector(`.filter-charts[data-filter="${e}"]`),{on:o,off:r}=a.dataset;if(t){a.dataset.state="on",a.classList.remove(...r.split(",")),a.classList.add(...o.split(","));for(const e of n)"show"===e.dataset.searchsort?e.classList.remove("hidden"):e.classList.add("hidden")}else{a.dataset.state="off",a.classList.remove(...o.split(",")),a.classList.add(...r.split(","));for(const e of n)e.classList.add("hidden")}}},K=t=>{const n=t.currentTarget,{filter:a,state:o}=n.dataset,r="off"===o;let i=null;M({filter:{[a]:r}}),e&&(i=localStorage.getItem("filter"),i=i?JSON.parse(i):{...P.filter},i[a]=r,localStorage.setItem("filter",JSON.stringify(i)))},X=t=>{const n=t.currentTarget,{sort:a,order:o}=n.dataset;let r=null;M({sort:{sort:a,order:o}}),e&&(r=localStorage.getItem("sort"),r=r?JSON.parse(r):{...P.sort},r.sort=a,r.order=o,localStorage.setItem("sort",JSON.stringify(r)))},G=e=>{const t=e.currentTarget.value.trim();M({search:{value:t}})};window.onload=()=>{const e=document.querySelector("#tour-button");v.button.initAll(D.dropdownify.button);for(const e of document.querySelectorAll(".iso-d-str"))e.textContent=o(e.dateTime,!0);for(const e of document.querySelectorAll(".iso-dt-str"))e.textContent=o(e.dateTime);var t;M({}),document.querySelector("#remove-modal-wrapper form").addEventListener("submit",B),S.addListeners(".chart-data.clickable-state","keydown",{viewChartKeydownHandler:H}),S.addListeners(".chart-data.clickable-state","click",{viewChartHandler:C}),S.addListeners(".edit-chart.clickable-state","click",{editChartHandler:O}),S.addListeners(".copy-chart.clickable-state","click",{copyHandler:A}),S.addListeners(".trash-chart","click",{trashHandler:$}),S.addListeners(".untrash-chart","click",{untrashChartHandler:I}),S.addListeners(".delete-chart","click",{deleteChartHandler:j}),S.addListeners(".accept-invite","click",{acceptInviteHandler:z}),S.addListeners(".reject-invite","click",{rejectInviteHandler:U}),S.addListeners(".delete-invite","click",{deleteInviteHandler:J}),S.addListeners(".trash-permission","click",{trashPermissionHandler:F}),S.addListeners(".untrash-permission","click",{untrashPermissionHandler:_}),S.addListeners(".delete-permission","click",{deletePermissionHandler:N}),S.addListeners(".remove-or-trash","click",{removeOrTrashHandler:R}),S.addListeners(".filter-charts","click",{filterHandler:K}),S.addListeners(".sort-charts","click",{sortHandler:X}),S.addListener(document.querySelector("#search"),"input",{searchHandler:G}),S.addListeners(".modal-scrim, .modal-close-button","click",{clickHandler:q.clickHandler}),S.addListener(document.querySelector("#guide-toggle-input"),"change",{guideHandler:x}),e&&"true"===e.dataset.auto&&(e.click(),t="t1",setTimeout((()=>{if("t1"===t||"t2"===t){const e=new FormData;e.append("tour",t),a("/tour-handler",e)}}),500))}}();
//# sourceMappingURL=home.min.js.map