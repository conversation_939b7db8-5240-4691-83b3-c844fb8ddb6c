!function(){"use strict";const e=(()=>{try{const e="__storage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})(),t=(e,t)=>{if(e.length){const n=new Set;for(const a of e){let e=window.getComputedStyle(a).getPropertyValue(t);"stroke-dasharray"===t&&"none"!==e&&(e=e.split(",").map((e=>parseInt(e,10))).join(" ")),n.add(e)}if(1===n.size)return[...n][0]}return null},n=(e,t)=>e<t?-1:e>t?1:0,a=e=>e.replace(/"/g,'\\"'),o=e=>{const t=e.querystr?`${e.url}?${e.querystr}`:e.url,n="POST"===e.method&&document.body.dataset.token||null,a=document.querySelector("#page-message");(async(e,t,n=null,a=null,o=null)=>{const r=((e,t=null,n=null)=>{const a={method:e,mode:"same-origin"};return n&&"POST"===e&&(a.headers={"X-CSRF-TOKEN":n}),t&&"POST"===e&&(a.body=t),a})(t,n,a),l=await fetch(e,r);if(404===l.status)window.location="/404";else if(401===l.status||403===l.status)window.location.reload();else if(o)if(l.ok)o.classList.contains("hidden")||o.classList.add("hidden");else{o.querySelector("p").textContent="Something went wrong, please try again",o.classList.remove("hidden")}return l.text()})(t,e.method,e.body,n,a).then((t=>{let n={};try{n=JSON.parse(t),("object"!=typeof n||null===n||Array.isArray(n))&&(n={rejected:!0})}catch(e){n.rejected=e}n.rejected?a&&(a.querySelector("p").textContent="Something went wrong, please try again",a.classList.remove("hidden")):n.redirect?window.location=n.redirect:e.callback&&e.callback(n)}))},r=(t="",n=new FormData,a=null)=>{const r={url:t,method:"POST",body:n,callback:a};if("/free"===t){const t=t=>{a&&a(t),e&&"chart"in t&&localStorage.setItem("chart",t.chart)};r.callback=t,e?r.body.append("chart",localStorage.getItem("chart")||"{}"):r.body.append("chart","{}")}o(r)},l=e=>({center:"middle",end:"right",left:"start",middle:"center",right:"end",start:"left"}[e]),s=(e,t,n=!1,a=null)=>{for(const o of t)n?e.removeAttributeNS(a,o):e.removeAttribute(o)},i=e=>{for(;e.firstChild;)e.removeChild(e.firstChild)},d=(e,t,n=!1,a=null)=>{for(const[o,r]of Object.entries(t))n?e.setAttributeNS(a,o,r):e.setAttribute(o,r)},c=(e,t)=>{for(const n of e)n.match?n.elem.classList.remove("hidden"):n.elem.classList.add("hidden"),t.appendChild(n.elem)},u={wrapper:"dropdown-button-wrapper",trigger:"dropdown-button",menu:"dropdown-menu",option:"dropdown-option",visible:"visible-state",focused:"focused-state",disabled:"disabled-state"},m={wrapper:"nested-dropdown-button-wrapper",trigger:"nested-dropdown-button",menu:"nested-dropdown-menu",option:"nested-dropdown-option",visible:"visible-state",focused:"focused-state",disabled:"disabled-state"},h={wrapper:"dropdown-field-wrapper",trigger:"dropdown-field",text:"dropdown-text-input",value:"dropdown-value-input",button:"dropdown-button",icon:"dropdown-button-icon",menu:"dropdown-menu",option:"dropdown-option",selected:"selected-state",visible:"visible-state",focused:"focused-state"},p={},y={focused:null},g=e=>p[e.dataset.dropdownify],f=()=>{for(const[e,t]of Object.entries(p)){document.querySelector(`.${t.cls.wrapper}[data-dropdownify="${e}"]`)||delete p[e]}},b=(e,t,n)=>{const a="show"in t&&t.show.length,o="hide"in t&&t.hide.length;"show"===n?(o&&e.classList.remove(...t.hide),a&&e.classList.add(...t.show)):"hide"===n&&(a&&e.classList.remove(...t.show),o&&e.classList.add(...t.hide))},x=(e,t=null)=>{const n=t||g(e),{top:a,bottom:o,left:r,right:l,width:s}=n.trigger.getBoundingClientRect(),i=window.innerHeight,d=(()=>{let{anchor:t,direction:n,matchwidth:a}=e.dataset;return t=t||"bottom",n=n||"right",a="true"===a,{anchor:t,direction:n,matchwidth:a}})(),c=(e.setAttribute("style","display: block;"),d.matchwidth&&e.setAttribute("width",`${s}px`),e.getBoundingClientRect());let u={display:"block"};d.matchwidth&&(u.width=`${s}px`),"left"===d.anchor||"right"===d.anchor?("left"===d.anchor?u.left=r-c.width+"px":u.left=`${l}px`,"up"===d.direction?(u.top=(o>c.height+20?o-c.height:20)+"px",u["max-height"]=i-u.top-20+"px"):(u.top=`${a}px`,u["max-height"]=i-a-20+"px")):("top"===d.anchor?u.top=o-c.height+"px":(u.top=`${o}px`,u["max-height"]=i-o-20+"px"),"left"===d.direction?u.left=l-c.width+"px":u.left=`${r}px`),u=Object.entries(u).reduce(((e,[t,n])=>`${e}${t}:${n};`),""),e.setAttribute("style",u),setTimeout((()=>{"trigger"in n.transition&&b(n.trigger,n.transition.trigger,"show"),e.classList.add(n.cls.visible),n.trigger.getAttribute("aria-expanded")&&n.trigger.setAttribute("aria-expanded","true")}))},w=(e,t=null)=>{const n=t||g(e);e.classList.remove(n.cls.visible),setTimeout((()=>{e.setAttribute("style","display: none;"),n.trigger.getAttribute("aria-expanded")&&n.trigger.setAttribute("aria-expanded","false")}),100),"trigger"in n.transition&&b(n.trigger,n.transition.trigger,"hide");for(const t of e.querySelectorAll(`.${n.cls.option}.${n.cls.focused}`))t.classList.remove(n.cls.focused)},v=(e,t=null)=>{const n=e?e.dataset.dropdownify:null;for(const[e,a]of Object.entries(p)){e!==n&&(!t||t&&t===a.nest)&&a.menu.classList.contains(a.cls.visible)&&w(a.menu,a)}},k=e=>{v(e.target)},S={button:(()=>{const e=(e,t=!1)=>{const n=g(e),{cls:a,menu:o,wrapper:r}=n;o.classList.contains(a.visible)?w(o,n):(x(o,n),t?v(r,n.nest):v(r))},t=t=>{t.stopPropagation(),e(t.currentTarget)},n=t=>{t.stopPropagation(),e(t.currentTarget,!0)},a=e=>{const t=e.currentTarget,{wrapper:n}=g(t);e.stopPropagation(),w(t),v(n)},o=e=>{const t=e.currentTarget,{wrapper:n,nest:a}=g(t);e.stopPropagation(),w(t),a||v(n)},r=(e,t=!1)=>{const{cls:n,menu:a}=e,o=a.querySelector(`.${n.option}.${n.focused}`);let r=null;if(o)o.classList.remove(n.focused),r=t?o.previousElementSibling:o.nextElementSibling;else{const e=[...a.querySelectorAll(`.${n.option}`)];if(e.length){r=e[t?e.length-1:0]}}for(;r;){if(!r.classList.contains(n.disabled)){r.classList.add(n.focused);break}r=t?r.previousElementSibling:r.nextElementSibling}},l=e=>{if(["Tab","Enter","Escape","Esc","ArrowDown","Down","ArrowUp","Up","ArrowRight","Right","ArrowLeft","Left"].includes(e.key)){const t=e.currentTarget,n=g(t),{cls:a,menu:o}=n;if(o.classList.contains(a.visible)){const t=o.querySelector(`.${a.option}.${a.focused}`),l=t?t.dataset.dropdownify:null,s=l?g(t):null;let i=!1;if(s&&s.cls&&s.menu&&(i=s.menu.classList.contains(s.cls.visible)),"Tab"===e.key)w(o,n),i&&w(s.menu,s);else if("Escape"===e.key||"Esc"===e.key)e.stopPropagation(),i?w(s.menu,s):w(o,n);else if(["ArrowDown","Down","ArrowUp","Up"].includes(e.key)){const t=["ArrowUp","Up"].includes(e.key);let a=null;if(i){const e=s.menu,t=s.cls;a=e.querySelector(`.${t.option}.${t.focused}`)}a?r(s,t):(r(n,t),i&&w(s.menu,s))}else if(t)if(i){const t=s.menu,n=s.cls,a=t.querySelector(`.${n.option}.${n.focused}`);"Enter"===e.key?a.click():"ArrowLeft"===e.key||"Left"===e.key?w(s.menu,s):a||r(s)}else"Enter"===e.key&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),l?t.firstElementChild.click():t.click())}}},s=(e,n)=>{e.addEventListener("click",t),e.addEventListener("keydown",l),n.addEventListener("click",o)},i=(e,t)=>{e.addEventListener("click",n),t.addEventListener("click",a)},c=(e,t,n=!1)=>{const a=t.transition||{},o=(()=>{let e={...u};return"cls"in t&&(e={...e,...t.cls}),e})(),r=Object.keys(p);let l=r.length?Math.max(...r.map((e=>Number(e)))):1;for(const r of e){const e=r.querySelector(`.${o.trigger}`),c=r.querySelector(`.${o.menu}`);if(l+=1,p[l]={cls:o,transition:a,menu:c,trigger:e,wrapper:r,nest:null},r.setAttribute("data-dropdownify",l),e.setAttribute("data-dropdownify",l),d(c,{"data-dropdownify":l,id:`dropdownify${l}`}),s(e,c),n){const e=t.nested||{},n=e.transition||{},a=(()=>{let t={...m};return"cls"in e&&(t={...t,...e.cls}),t.container=o.menu,t})(),s=l,c=r.querySelectorAll(`.${a.wrapper}`);for(const e of c){const t=e.querySelector(`.${a.trigger}`),o=e.querySelector(`.${a.menu}`);l+=1,p[l]={cls:a,transition:n,menu:o,trigger:t,wrapper:e,nest:s},e.setAttribute("data-dropdownify",l),t.setAttribute("data-dropdownify",l),d(o,{"data-dropdownify":l,id:`dropdownify${l}`}),i(t,o),document.body.appendChild(o)}}document.body.appendChild(c)}},h=()=>{document.body.dataset.dropdownify||(document.body.addEventListener("click",k),document.body.setAttribute("data-dropdownify",!0))};return{cascadeHide:v,init:(e,t={})=>{let n=u.wrapper;"cls"in t&&"wrapper"in t.cls&&(n=t.cls.wrapper),e.classList.contains(n)&&(f(),h(),c([e],t,t.nest))},initAll:(e={})=>{const t=(()=>{let t=u.wrapper;return"cls"in e&&"wrapper"in e.cls&&(t=e.cls.wrapper),document.querySelectorAll(`.${t}`)})();f(),h(),c(t,e,e.nest)}}})(),field:(()=>{const e=(e,t,n)=>{const a="search"in t&&t.search.length,o="add"in t&&t.add.length,r="clear"in t&&t.clear.length;"search"===n?(o&&e.classList.remove(...t.add),r&&e.classList.remove(...t.clear),a&&e.classList.add(...t.search)):"add"===n?(a&&e.classList.remove(...t.search),r&&e.classList.remove(...t.clear),o&&e.classList.add(...t.add)):"clear"===n&&(a&&e.classList.remove(...t.search),o&&e.classList.remove(...t.add),r&&e.classList.add(...t.clear))},t=(t,n,a,o)=>{if(n.setAttribute("data-state",t),"icontext"in o){a.textContent=o.icontext[t]}"iconcls"in o&&e(a,o.iconcls,t),"button"in o&&e(n,o.button,t)},o=(e,n=!0,a=!0)=>{const{cls:o,transition:r,menu:l,text:s,value:i,button:d,icon:c}=e,u=l.querySelector(`.${o.option}.${o.selected}`);i.value="",s.setAttribute("data-state","hide"),a&&(s.value=""),u&&u.classList.remove(o.selected),t("search",d,c,r),n&&i.dispatchEvent(new Event("change"))},r=(e,n,a=!0)=>{const{cls:o,transition:r,text:l,value:s,button:i,icon:d,menu:c}=n,u=c.querySelector(`.${o.option}.${o.selected}`);e===u&&e.dataset.value===s.value||(u&&u.classList.remove(o.selected),e.classList.add(o.selected),l.value=e.dataset.label,s.value=e.dataset.value,t("clear",i,d,r),a&&s.dispatchEvent(new Event("change")))},l=e=>{const t=e.currentTarget,n=t.parentElement,a=g(n);r(t,a),w(n,a)},s=(e,t,n,a=null)=>{const o=a||g(e),{cls:r}=o,s=document.createDocumentFragment(),i=document.createElement("li"),c=document.createElement("span"),u=document.createTextNode(t),m={class:r.option,"data-label":t,"data-value":n};d(i,m),s.appendChild(i),i.appendChild(c),c.appendChild(u),i.addEventListener("click",l),e.appendChild(s)},i=(e,t)=>{const{callback:n,cls:o,menu:l}=t,i=a(e);s(l,e,e,t),r(l.querySelector(`.${o.option}[data-label="${i}"]`),t),w(l,t),"add"in n&&n.add(t,e,e,p)},u=e=>{const{callback:t}=e;o(e),"clear"in t&&t.clear(e)},m=e=>{const t=e.currentTarget,{state:n}=t.dataset,a=g(t);if(e.stopPropagation(),"clear"===n)u(a);else if("add"===n){const{text:e}=a;i(e.value.trim(),a)}},b=e=>{y.focused=e.currentTarget.dataset.dropdownify},S=e=>{const a=g(e),{callback:r,cls:l,transition:s,menu:i,button:d,icon:u}=a,m=[...i.querySelectorAll(`.${l.option}`)].reduce(((e,t,n)=>(e[n]={elem:t,id:n,text:t.dataset.label},e)),{}),h=e.value.trim();let p=i.querySelector(`.${l.option}.${l.selected}`),y=[];p&&p.dataset.label===h?p=!0:(p&&o(a,!1,!1),p=!1);for(const e of i.querySelectorAll(`.${l.option}.${l.focused}`))e.classList.remove(l.focused);y=((e,t,a=!1,o=null)=>{const r={...t};let l=[],s=[],i=[];e?(s=new Fuse(Object.values(t),{keys:["text"],includeScore:!0}).search(e).map((e=>(delete r[e.item.id],{...e.item,score:e.score,match:!0}))),l=Object.values(r).map((e=>({...e,match:!1}))).sort(((e,t)=>n(e.text,t.text)))):s=Object.values(r).map((e=>({...e,match:!0}))).sort(((e,t)=>n(e.text,t.text)));return i=[...s,...l],a&&o&&c(i,o),i})(h,m,!1),"filter"in r&&(y=r.filter(a,y)),h&&y.length&&y[0].match?p?e.setAttribute("data-state","clear"):e.setAttribute("data-state","select"):h?e.setAttribute("data-state","add"):e.setAttribute("data-state","hide"),y.length&&y[0].match?(c(y,i),x(i,a)):(w(i,a),c(y,i)),h?y.length&&h===y[0].text?t(p?"clear":"search",d,u,s):t("add",d,u,s):t("search",d,u,s)},$=e=>{S(e.currentTarget)},L=(e,n=!1)=>{const{cls:r,menu:l,text:s,button:i}=e,d=a(s.value),c=l.querySelector(`.${r.option}.${r.selected}`),u=l.querySelector(`.${r.option}.${r.focused}`);if(u){const e=n?u.previousElementSibling:u.nextElementSibling;e&&!e.classList.contains("hidden")&&(u.classList.remove(r.focused),e.classList.add(r.focused),s.value=e.dataset.label)}else{const e=[...l.querySelectorAll(`.${r.option}`)];for(const[t,a]of e.entries()){if(!n){t||a.classList.contains("hidden")?s.value="":(a.classList.add(r.focused),s.value=a.dataset.label);break}if(a.classList.contains("hidden")){t?(e[t-1].classList.add(r.focused),s.value=e[t-1].dataset.label):s.value="";break}t===e.length-1&&(a.classList.add(r.focused),s.value=a.dataset.label)}}if(c&&c.dataset.label!==s.value&&o(e,!1,!1),"add"===i.dataset.state&&l.querySelector(`.${r.option}[data-label="${d}"]`)){const{icon:n,transition:a}=e;t("search",i,n,a)}},q=e=>{const{text:t,wrapper:n}=e;S(t),v(n)},A=e=>{const{cls:t,text:n,button:o,menu:l}=e,s=n.value.trim();if(s){const n=a(s),o=l.querySelector(`.${t.option}[data-label="${n}"]`);o?(r(o,e),w(l,e)):i(s,e)}else u(e),w(l,e);o.addEventListener("click",m)},E=e=>{if(["Tab","Enter","Escape","Esc","ArrowDown","Down","ArrowUp","Up"].includes(e.key)){const t=e.currentTarget,n=g(t),{cls:a,menu:o}=n,r=o.classList.contains(a.visible);if("Enter"===e.key){const e=t.value.trim(),l=o.querySelector(`.${a.option}.${a.selected}`),s=l?l.dataset.label:"";r||e!==s?A(n):q(n)}else r&&("Tab"===e.key||"Escape"===e.key||"Esc"===e.key?(w(o,n),"Escape"!==e.key&&"Esc"!==e.key||e.stopPropagation()):"ArrowDown"===e.key||"Down"===e.key?L(n):"ArrowUp"!==e.key&&"Up"!==e.key||L(n,!0))}},C=e=>{const t=g(e.currentTarget),{cls:n,menu:a}=t;e.stopPropagation(),a.classList.contains(n.visible)?w(a,t):q(t)},N=(e,t,n,a,o)=>{e.addEventListener("click",C),t.addEventListener("focus",b),t.addEventListener("keydown",E),t.addEventListener("input",$),n.addEventListener("click",m);for(const e of a.querySelectorAll(`.${o.option}`))e.addEventListener("click",l)},O=(e,t)=>{const n=t.callback||{},a=t.transition||{},o=(()=>{let e={...h};return"cls"in t&&(e={...e,...t.cls}),e})(),r=Object.keys(p);let l=r.length?Math.max(...r.map((e=>Number(e)))):1;for(const t of e){const e=t.querySelector(`.${o.trigger}`),r=t.querySelector(`.${o.menu}`),s=t.querySelector(`.${o.text}`),i=t.querySelector(`.${o.value}`),c=t.querySelector(`.${o.button}`),u=c.querySelector(`.${o.icon}`);l+=1,p[l]={cls:o,callback:n,transition:a,wrapper:t,trigger:e,menu:r,text:s,value:i,button:c,icon:u},t.setAttribute("data-dropdownify",l),e.setAttribute("data-dropdownify",l),s.setAttribute("data-dropdownify",l),c.setAttribute("data-dropdownify",l),d(r,{"data-dropdownify":l,"data-matchwidth":!0,id:`dropdownify${l}`}),N(e,s,c,r,o),document.body.appendChild(r)}},j=e=>{"Tab"===e.key&&y.focused&&(A(p[y.focused]),y.focused=null)},T=e=>{if(v(e.target),y.focused){const t=p[y.focused];t.text!==e.target&&(y.focused=null,t.button!==e.target&&t.icon!==e.target&&A(t))}},M=()=>{document.body.dataset.dropdownify?(document.body.removeEventListener("keydown",j),document.body.removeEventListener("click",k),document.body.removeEventListener("click",T),document.body.addEventListener("keydown",j),document.body.addEventListener("click",T)):(document.body.addEventListener("keydown",j),document.body.addEventListener("click",T),document.body.setAttribute("data-dropdownify",!0))};return{cascadeHide:v,appendOption:s,bodyClickHandler:T,init:(e,t={})=>{let n=h.wrapper;"cls"in t&&"wrapper"in t.cls&&(n=t.cls.wrapper),e.classList.contains(n)&&(f(),M(),O([e],t))},initAll:(e={})=>{const t=(()=>{let t=h.wrapper;return"cls"in e&&"wrapper"in e.cls&&(t=e.cls.wrapper),document.querySelectorAll(`.${t}`)})();f(),M(),O(t,e)},clearSelection:(e,t=!0)=>{const n=g(e);o(n,t)},selectOption:(e,t,n=!0)=>{const a=g(e);r(t,a,n)},removeDBEntry:e=>{delete p[e],Object.keys(p).length||(document.body.removeEventListener("keydownn",j),document.body.removeEventListener("click",k),document.body.removeEventListener("click",T),document.body.removeAttribute("data-dropdownify"))}}})()},$={},L={delay:(...e)=>{const[t,n,a,o,r,l]=e;t in $&&n in $[t]&&a in $[t][n]&&(clearTimeout($[t][n][a].timeout),$[t][n][a].timeout=setTimeout(o,r,l))},throttle:(...e)=>{const t=(e,t,n,a,o)=>{e in $&&t in $[e]&&n in $[e][t]&&($[e][t][n].timeout=null,a(o))},[n,a,o,r,l,s]=e;n in $&&a in $[n]&&o in $[n][a]&&!$[n][a][o].timeout&&($[n][a][o].timeout=setTimeout(t,l,n,a,o,r,s))}},q=(()=>{const e=(e,t,n,a=null,o=0)=>{if(e){const[[r,l]]=Object.entries(n);let s=e instanceof Element?e.dataset.eventify:1;if(1!==s||s in $||($[s]={}),!s){const t=Object.keys($);s=t.length?Math.max(...t.map((e=>Number(e))))+1:2,e.setAttribute("data-eventify",s),$[s]={}}t in $[s]||($[s][t]={}),$[s][t][r]=a?{timeout:null,handleEvent:L[a].bind(e,s,t,r,l,o)}:{handleEvent:l},e.addEventListener(t,$[s][t][r])}};return{addListener:e,addListeners:(t,n,a,o=null,r=0)=>{for(const l of document.querySelectorAll(t))e(l,n,a,o,r)},removeListener:(e,t,n)=>{const a=e instanceof Element?e.dataset.eventify:1,[o]=Object.keys(n);a in $&&t in $[a]&&o in $[a][t]&&(e.removeEventListener(t,$[a][t][o]),delete $[a][t][o])},garbageCollection:()=>{for(const e of Object.keys($)){document.querySelector(`[data-eventify="${e}"]`)||delete $[e]}}}})(),A=(e=document)=>[...e.querySelectorAll('a[href], button, input, textarea, select, details,[tabindex]:not([tabindex="-1"])')].filter((e=>!e.hasAttribute("disabled"))),E={escapeCloseHandler:e=>{if("Escape"===e.key||"Esc"===e.key){const e=document.querySelector(".modal-wrapper.visible-state");e&&E.hide(e)}},hide:e=>{const t=e.querySelector(".modal-panel"),n=e.querySelector(".modal-scrim"),a=e.dataset.onhidefocus;if((()=>{for(const e of document.querySelectorAll(".modalize-aria-hidden"))e.removeAttribute("aria-hidden"),e.classList.remove("modalize-aria-hidden");for(const e of document.querySelectorAll(".modalize-tabindex")){const{tabindex:t}=e.dataset;t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex"),e.removeAttribute("data-tabindex"),e.classList.remove("modalize-tabindex")}})(),a){const e=document.querySelector(a);e?e.focus():document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus())}else document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus());t.classList.remove("visible-state","translate-y-0","sm:scale-100"),n.classList.remove("visible-state"),e.classList.remove("visible-state"),t.classList.add("translate-y-4","sm:translate-y-0","sm:scale-95"),setTimeout((()=>e.setAttribute("style","display: none;")),300),document.body.removeEventListener("keydown",E.escapeCloseHandler)},show:e=>{const t=e.querySelector(".modal-panel"),n=e.querySelector(".modal-scrim");document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus()),e.setAttribute("style","display: block;"),setTimeout((()=>{e.classList.add("visible-state"),n.classList.add("visible-state"),t.classList.remove("translate-y-4","sm:translate-y-0","sm:scale-95"),t.classList.add("visible-state","translate-y-0","sm:scale-100")})),document.body.addEventListener("keydown",E.escapeCloseHandler),(e=>{const t=e=>{e.getAttribute("aria-hidden")||(e.classList.add("modalize-aria-hidden"),e.setAttribute("aria-hidden","true"))},n=e=>{const t=e.getAttribute("tabindex");t&&e.setAttribute("data-tabindex",t),e.classList.add("modalize-tabindex"),e.setAttribute("tabindex","-1")};let a=e;for(;a.parentElement&&document.body!==a;){let e=a.nextElementSibling;for(;e;){t(e),n(e);for(const t of A(e))n(t);e=e.nextElementSibling}for(e=a.previousElementSibling;e;){t(e),n(e);for(const t of A(e))n(t);e=e.previousElementSibling}a=a.parentElement}})(e)},clickHandler:e=>{const t=e.currentTarget,n=document.querySelector(`#${t.dataset.modal}-modal-wrapper`);E.hide(n)}},C=e=>{const t=new FormData;t.append("guide",e.currentTarget.value),r("/guide-toggle-handler",t)},N={callback:{selectionHandler:()=>{},dragSelectionHandler:()=>{},editSelectionHandler:()=>{},nodeCrosshairHandler:()=>{},noteCrosshairHandler:()=>{},noteHandleHandler:()=>{},showNoteEditor:()=>{},clearLabelData:()=>{},syncDataDesignPanels:()=>{},chartModalCardHandler:()=>{}},classes:{elem:{link:"chart-link",linknote:"chart-linknote",node:"chart-node",note:"chart-note"},text:{linknote:["chart-linknote-name","chart-linknote-label","chart-linknote-content"],node:["chart-node-name","chart-node-label","chart-node-content"],note:["chart-note-content"]}},css:{".chart-node":{fill:"#ffffff",stroke:"rgba(156, 163, 175, 1)","stroke-dasharray":"none"},".chart-node-name":{fill:"rgba(55, 65, 81, 1)","font-size":"large","font-style":"normal","font-weight":"bold","text-anchor":"middle","text-decoration-line":"none"},".chart-node-label":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"},".chart-node-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-link":{stroke:"rgba(156, 163, 175, 1)","stroke-dasharray":"none"},".chart-linknote":{fill:"rgba(0, 0, 0, 0)",stroke:"rgba(0, 0, 0, 0)","stroke-dasharray":"none"},".chart-linknote-name":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-linknote-label":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"},".chart-linknote-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"bold","text-anchor":"start","text-decoration-line":"none"},".chart-note":{fill:"rgba(0, 0, 0, 0)",stroke:"rgba(0, 0, 0, 0)","stroke-dasharray":"none"},".chart-note-content":{fill:"rgba(107, 114, 128, 1)","font-size":"small","font-style":"normal","font-weight":"normal","text-anchor":"start","text-decoration-line":"none"}},cursor:{node:"enabled",note:"enabled",select:"active"},layout:{linknote:{position:{content:"right",label:"left"}},node:{position:{content:"right",label:"left"}}},modal:{chart:{action:"add"},link:{action:"addlink"},node:{action:"addnode",x:null,y:null}},title:{current:"Untitled chart",default:"Untitled chart"},selections:{all:0,dragids:{link:[],linknote:[],node:[],note:[]},ids:{link:[],linknote:[],node:[],note:[]}},shiftpath:{bottom:1,left:0,right:1,top:0}},O={dropdownify:{button:{nest:!0},field:{callback:{add:(e,t,n,o)=>{if(e.wrapper.dataset.linked){const r=o[document.querySelector(`#${e.wrapper.dataset.linked}`).dataset.dropdownify],l=a(n);r.menu.querySelector(`.dropdown-option[data-value="${l}"]`)||S.field.appendOption(r.menu,t,n)}else{const r=(()=>{let t=e.wrapper;for(;t.parentElement&&!t.classList.contains("dropdown-sync-group");)t=t.parentElement;return t})();if(r.classList.contains("dropdown-sync-group")){const e=r.querySelectorAll(".dropdown-field-wrapper");for(const r of e){const e=o[r.dataset.dropdownify],l=a(n);e.menu.querySelector(`.dropdown-option[data-value="${l}"]`)||S.field.appendOption(e.menu,t,n)}}}},clear:e=>{S.field.cascadeHide(e.wrapper)},filter:(e,t)=>{let a=t;if(["link-modal-parent","link-modal-child","parent-dropdown-card","child-dropdown-card"].includes(e.wrapper.id)){const t=new Set;let o=null;if(["link-modal-parent","link-modal-child"].includes(e.wrapper.id)){let n=null;if((N.selections.ids.link.length||N.selections.ids.linknote.length)&&([n]=[...N.selections.ids.link,...N.selections.ids.linknote]),"link-modal-parent"===e.wrapper.id){if(o=document.querySelector('input[name="link-modal-child-value"]').value.trim(),o)for(const e of N.links)e.child_id!==o||n&&e.id===n||t.add(e.parent_id)}else if("link-modal-child"===e.wrapper.id&&(o=document.querySelector('input[name="link-modal-parent-value"]').value.trim(),o))for(const e of N.links)e.parent_id!==o||n&&e.id===n||t.add(e.child_id)}else if(o=document.querySelector('input[name="chart-modal-node-value"]').value.trim(),o)if("parent-dropdown-card"===e.wrapper.id)for(const e of N.links)e.child_id===o&&t.add(e.parent_id);else if("child-dropdown-card"===e.wrapper.id)for(const e of N.links)e.parent_id===o&&t.add(e.child_id);if(o){t.add(o);for(const e of a)t.has(e.elem.dataset.value)&&(e.match=!1);a=a.sort(((e,t)=>e.match!==t.match?e.match?-1:1:e.match?"score"in e&&"score"in t?e.score-t.score:n(e.text,t.text):0))}}return a}},transition:{button:{search:[],add:["cursor-pointer"],clear:["cursor-pointer"]},iconcls:{search:["text-gray-400"],add:["text-orange-500"],clear:["text-orange-500"]},icontext:{search:"search",add:"add_circle",clear:"cancel"},trigger:{show:[],hide:["rounded-b-md"]}}}},dynamify:{callback:{add:e=>{const t=e.dataset.modal,n=e.querySelector(".dropdown-field-wrapper"),a=document.querySelector(`#dropdownify${n.dataset.dropdownify}`).cloneNode(!0),o=e.querySelector(".clear-dynamic-item"),r=e.querySelectorAll(`input[name="${t}-modal-label-value"], input[name="${t}-modal-data"]`);if(n.appendChild(a),S.field.init(n,O.dropdownify.field),S.field.clearSelection(n,!1),S.field.cascadeHide(),o.addEventListener("click",N.callback.clearLabelData),r.length)for(const e of r)e.addEventListener("change",N.callback.syncDataDesignPanels)},remove:e=>{const t=e.querySelector(".dropdown-field-wrapper").dataset.dropdownify;document.querySelector(`#dropdownify${t}`).remove(),S.field.removeDBEntry(t)}}}},j=e=>{e.currentTarget.parentElement.classList.add("invisible")},T=()=>{if(e){let e=localStorage.getItem("history");return e=e?JSON.parse(e):[],e}return[]},M=()=>{if(e){let e=localStorage.getItem("index");return e=e?parseInt(e,10):0,e}return 0},H=e=>{const t={},n=document.createDocumentFragment(),a=document.querySelector("#chart-header"),o=document.querySelector("#chart"),r=document.querySelector("#canvas"),l=4*d3.zoomTransform(r).k,s=3*l;let{top:i,left:d,bottom:c,right:u,height:m,width:h}=e.getBoundingClientRect();i-=a.getBoundingClientRect().bottom,c=i+m-l,u=d+h-l,t.sides=["top","left","bottom","right"].reduce(((e,t)=>(e[t]={x:"right"===t?u:d,y:"bottom"===t?c:i,h:"top"===t||"bottom"===t?l:m,w:"left"===t||"right"===t?l:h},e)),{}),i-=l,d-=l,c-=l,u-=l,m=s,h=s,t.corners=["topleft","bottomleft","bottomright","topright"].reduce(((e,t)=>(e[t]={x:"topleft"===t||"bottomleft"===t?d:u,y:"topleft"===t||"topright"===t?i:c,h:m,w:h},e)),{});for(const[a,o]of Object.entries({...t.sides,...t.corners})){const t=document.createElement("div");t.className=`${a} selectedhandle`,t.dataset.handle=a,t.dataset.breadth=l,t.dataset.id=e.dataset.id,t.style.left=`${o.x}px`,t.style.top=`${o.y}px`,t.style.height=`${o.h}px`,t.style.width=`${o.w}px`,n.appendChild(t),t.addEventListener("mousedown",N.callback.noteHandleHandler)}o.appendChild(n)},z=e=>{const t=document.querySelectorAll(`.selectedhandle[data-id="${e.dataset.id}"]`);for(const e of t)e.remove()},_=(e,t)=>{const n=`.chart-link-ghost[data-id="${t.dataset.id}"]`;for(const t of document.querySelectorAll(n))"select"===e?t.classList.add("selected"):"clear"===e&&t.classList.remove("selected")},D=e=>{const t=d3.select(e.parentNode),n=e.tagName.toLowerCase();"rect"===n?t.append(n).attr("class","chart-elem-outline").attr("x",e.x.baseVal.value).attr("y",e.y.baseVal.value).attr("width",e.width.baseVal.value).attr("height",e.height.baseVal.value).attr("rx",e.rx.baseVal.value).attr("ry",e.ry.baseVal.value).attr("data-id",e.id):"path"===n&&t.append(n).attr("class","chart-elem-outline").attr("d",e.getAttributeNS(null,"d")).attr("data-id",e.id),e.classList.add("selected"),e.classList.contains("chart-note")&&"newnote"!==e.id&&H(e),e.classList.contains("chart-link")&&_("select",e)},J=e=>{const t=document.querySelector(`.chart-elem-outline[data-id="${e.id}"]`);t&&t.remove(),e.classList.remove("selected"),e.classList.contains("chart-note")&&z(e),e.classList.contains("chart-link")&&_("clear",e)},P=e=>{e.classList.remove("selected")},I=e=>{(()=>{if(Object.keys(N.style).length)for(const[e,t]of Object.entries(N.style))e in N.css&&(N.css[e]={...t});const e=Object.entries(N.css).reduce(((e,[t,n])=>`${e}${t}{${Object.entries(n).reduce(((e,[t,n])=>`${e}${t}:${n};`),"")}}`),"");document.querySelector("#style").textContent=e})(),(e=>{const t=(e,t)=>{for(const n of Object.keys(N.css[t]))N.css[t][n]=window.getComputedStyle(e).getPropertyValue(n)},n=document.createDocumentFragment(),a="http://www.w3.org/2000/svg",o=document.createElementNS(a,"svg"),r=document.createElementNS(a,"rect"),l=document.createElementNS(a,"path"),s=document.createElementNS(a,"text"),i=document.createElementNS(a,"tspan");o.setAttributeNS(null,"style","visibility: hidden;"),n.appendChild(o),o.appendChild(r),o.appendChild(l),o.appendChild(s),s.appendChild(i),i.appendChild(document.createTextNode("x")),e.appendChild(n);for(const[e,n]of Object.entries(N.classes.elem))if(e in N.classes.text){r.setAttributeNS(null,"class",n),t(r,`.${n}`);for(const n of N.classes.text[e])i.setAttributeNS(null,"class",n),t(i,`.${n}`)}else l.setAttributeNS(null,"class",n),t(l,`.${n}`);o.remove()})(e)},B=(e,t,n,a)=>{const o=n,r=e[`.${t}`];for(const e of o.getAttributeNames())o.removeAttribute(e);if(o.setAttributeNS(null,"class",t),r){let e="";for(const[t,n]of Object.entries(r))e+=`${t}: ${n}; `;o.setAttributeNS(null,"style",e)}return o.textContent=a,o.getBoundingClientRect()},F=({h:e,w:t},{l:n,r:a},o)=>{const r=n+a+o/2,l=r>t?r:t,s={left:2*Math.round(n/2),right:2*Math.round(a/2)};return s.text={height:2*Math.round(e/2),width:2*Math.round(l/2)},s.wrapper={height:s.text.height+o,width:s.text.width+o,radius:Math.round(s.text.height/10)},s},U=e=>{const t=!!N.nodes.length,n=document.createDocumentFragment(),a="http://www.w3.org/2000/svg",o=document.createElementNS(a,"svg"),r=document.createElementNS(a,"text"),s=document.createElementNS(a,"tspan");o.setAttributeNS(null,"style","visibility: hidden;"),s.setAttributeNS(null,"font-size","medium"),n.appendChild(o),o.appendChild(r),r.appendChild(s),s.appendChild(document.createTextNode("x")),e.appendChild(n),N.domrect=r.getBoundingClientRect(),N.lineheight=1.2,N.gutter=Math.round(1.5*N.domrect.height*N.gutterscale),N.doublegutter=2*N.gutter,N.pad=Math.round(N.domrect.width),N.doublepad=2*N.pad,N.textpad=Math.round(N.pad/2),N.doubletextpad=2*N.textpad,(e=>{for(const t of["linknote","node"]){const n=`chart-${t}-`;for(const a of N[`${t}s`]){const o=a.data.some((e=>e.label)),r=a.data.some((e=>e.content)),s={content:0,label:0};"position"in a.layout||(a.layout.position={...N.layout[t].position}),a.layout.align={};for(const e of["content","label","name"]){const t=`.${n}${e}`;let o=N.css[t]["text-anchor"];t in a.style&&"text-anchor"in a.style[t]&&(o=a.style[t]["text-anchor"]),a.layout.align[e]=l(o)}if(a.layout.column={},o&&r)for(const e of["content","label"]){const t=a.layout.position[e];a.layout.column[e]=["left","right"].includes(t)?t:"span"}else a.layout.column.content="span",a.layout.column.label="span";for(const[t,o]of a.data.entries()){o.domrect={content:{height:0,width:0},label:{height:0,width:0}};for(const t of["content","label"]){const r=o[t];r&&(o.domrect[t]=B(a.style,`${n}${t}`,e,r)),o.domrect[t].height*=N.lineheight}s.label<o.domrect.label.width&&(s.label=o.domrect.label.width),s.content<o.domrect.content.width&&(s.content=o.domrect.content.width),"span"===a.layout.column.content?(o.height=o.domrect.content.height+o.domrect.label.height,o.height&&t!==a.data.length-1&&(o.height+=N.doubletextpad)):o.height=Math.max(o.domrect.content.height,o.domrect.label.height)}if(a.domrect={name:{height:0,width:0}},a.name&&(a.domrect={name:B(a.style,`${n}name`,e,a.name)},a.domrect.name.height*=N.lineheight,a.domrect.name.height+=N.doubletextpad),a.height=a.domrect.name.height+a.data.reduce(((e,t)=>e+t.height),0),"span"===a.layout.column.label)a.datawidth=Math.max(s.label,s.content),a.width=Math.max(a.domrect.name.width,a.datawidth);else{const e="linknote"===t?N.textpad:N.pad,n=a.layout.column;n[n.label]=Math.max(...a.data.map((e=>e.domrect.label.width))),n[n.content]=Math.max(...a.data.map((e=>e.domrect.content.width))),a.datawidth=s.label+s.content+e,a.width=Math.max(a.domrect.name.width,a.datawidth)}"linknote"===t&&(a.box=F({h:a.height,w:a.width},{l:a.layout.column.left||0,r:a.layout.column.right||0},N.doubletextpad))}}})(s),t&&"wrap"===N.wrap&&(e=>{const t=1.78;let n=Math.max(...N.nodes.map((e=>e.height))),a=Math.max(...N.nodes.map((e=>e.width)));if(a/n>t){for(const n of N.nodes)if(n.breaks=[],n.best={lines:[n.name],height:n.height,width:n.width,ratio:n.width/n.height},n.width===n.domrect.name.width&&n.best.ratio>t){for(let e=0;e<n.name.length;e+=1)/\s/.test(n.name[e])&&n.breaks.push(e);if(n.breaks.length){const a=n.domrect.name.height-N.doubletextpad,o={lines:[]};for(let r=2;r<n.breaks.length+2;r+=1){const l=Math.ceil(n.name.length/r);let s=[];for(let e=1;e<r;e+=1){const t=l*e;if(n.breaks.length>1){for(let e=1;e<n.breaks.length;e+=1)if(Math.abs(n.breaks[e]-t)>Math.abs(n.breaks[e-1]-t)){s.push(n.breaks[e-1]);break}}else s=[...n.breaks]}s.unshift(0),s.push(n.name.length);for(let e=1;e<s.length;e+=1)o.lines.push(n.name.slice(s[e-1],s[e]));o.domrect=B(n.style,"chart-node-name",e,[...o.lines].sort(((e,t)=>t.length-e.length))[0]),o.width=o.domrect.width>n.datawidth?o.domrect.width:n.datawidth,o.height=n.height+a*o.lines.length,o.ratio=o.width/o.height,Math.abs(o.ratio-t)<Math.abs(n.best.ratio-t)&&(n.best=o)}}}n=Math.max(...N.nodes.map((e=>e.best.height))),a=Math.max(...N.nodes.map((e=>e.best.width)));for(const t of N.nodes)if(t.domrect.name.width>a&&t.breaks.length){const n=(()=>{let e=Math.ceil(t.domrect.name.width/a);return t.breaks.length+1<e&&(e=t.breaks.length+1),e})(),o=Math.ceil(t.name.length/n);let r=[];for(let e=1;e<n;e+=1){const n=o*e;if(t.breaks.length>1){for(let e=1;e<t.breaks.length;e+=1)if(Math.abs(t.breaks[e]-n)>Math.abs(t.breaks[e-1]-n)){r.push(t.breaks[e-1]);break}}else r=[...t.breaks]}r.unshift(0),r.push(t.name.length),t.namearr=[];for(let e=1;e<r.length;e+=1)t.namearr.push(t.name.slice(r[e-1],r[e]).trim());t.namearr=t.namearr.map(((n,a,o)=>{const r=B(t.style,"chart-node-name",e,n);return r.height*=N.lineheight,a===o.length-1&&(r.height+=N.doubletextpad),{text:n,domrect:r}})),t.domrect.name.height=t.namearr.reduce(((e,t)=>e+t.domrect.height),0),t.height=t.domrect.name.height+t.data.reduce(((e,t)=>e+t.height),0),t.width=Math.max(...t.namearr.map((e=>e.domrect.width)),t.datawidth)}}})(s),(e=>{const t="chart-note-content",n=`.${t}`;for(const a of N.notes){const{box:o,text:r}=a.coordinates;let s=N.css[n]["text-anchor"];n in a.style&&"text-anchor"in a.style[n]&&(s=a.style[n]["text-anchor"]),a.layout={align:l(s)},a.stripped=a.data.filter((e=>"text"===e.identifier)).map((e=>e.text)).join(" "),a.lines=a.data.reduce(((e,t,n)=>(n&&"wrap"===t.identifier?e[e.length-1]=`${e[e.length-1]}${t.text}`:e.push(t.text),e)),[]),a.lines=a.lines.map((n=>{const o=B(a.style,t,e,n||"x");return{height:o.height,width:o.width,text:n}}),[]),a.textheight=a.lines.reduce(((e,t)=>e+t.height),0),a.textwidth=Math.max(...a.lines.map((e=>e.width))),a.height=a.textheight*(o.h/r.h),a.width=a.textwidth*(o.w/r.w)}})(s),o.remove(),N.box={},N.box.node=F({h:t?Math.max(...N.nodes.map((e=>e.height))):N.domrect.height,w:t?Math.max(...N.nodes.map((e=>e.width))):N.domrect.width},{l:t?Math.max(...N.nodes.map((e=>e.layout.column.left||0))):0,r:t?Math.max(...N.nodes.map((e=>e.layout.column.right||0))):0},N.doublepad),N.box.node.all={height:N.box.node.wrapper.height+N.doublegutter,width:N.box.node.wrapper.width+N.doublegutter}},R=d3.line().x((e=>e.x)).y((e=>e.y)).curve(d3.curveBasis),V=d3.line().x((e=>e.x)).y((e=>e.y)).curve(d3.curveBasisOpen),K=d3.line().x((e=>e.x)).y((e=>e.y)).curve(d3.curveLinear),W=e=>"basis"===N.linecurve?R(e):K(e),X=(e,t,n,a,o,r,s)=>{const i=n.layout.column[t]||"span",d=e;return d.align=d.align||{},d.anchor=d.anchor||{},d.dx=d.dx||{},d.position=d.position||{},d.align[t]=n.layout.align[t]||"center","label"===t?d.position[t]=n.layout.position[t]||"left":"content"===t&&(d.position[t]=n.layout.position[t]||"right"),d.anchor[t]=l(d.align[t]),"left"===d.align[t]?d.dx[t]="right"===i?o+a:0:"right"===d.align[t]?d.dx[t]="left"===i?o:s:"center"===d.align[t]&&(d.dx[t]="span"===i?s/2:"left"===i?o/2:o+a+r/2),d},Z=(e,t,n,a,o,r,l,s)=>{const i=e.style[`.${t}`]||{};i["text-anchor"]=n.anchor[a],o.append("tspan").text(r).attr("class",t).attr("x",n.x).attr("y",l+s/2).attr("dx",n.dx[a]).attr("dy","0.5ex").attr("style",(()=>i?Object.entries(i).map((([e,t])=>`${e}: ${t};`)).join(" "):null)).attr("data-align",n.align[a]).attr("data-position",(()=>"name"!==a?n.position[a]:null)).attr("data-lookup",(()=>"chart-node-label"===t?"node-label":"chart-linknote-label"===t?"link-label":null))},G=(e,t,n,a)=>{let{y:o}=n;if(e.namearr)for(const r of e.namearr)Z(e,`${t}name`,n,"name",a,r.text,o,r.domrect.height),o+=r.domrect.height;else e.name&&(Z(e,`${t}name`,n,"name",a,e.name,o,e.domrect.name.height),o+=e.domrect.name.height);for(const r of e.data){const l=n.position.label,s=["top","bottom"].includes(l),i=["top","left"].includes(l)?["label","content"]:["content","label"];for(const l of i)if(r[l]){const i=s?r.domrect[l].height:r.height;Z(e,`${t}${l}`,n,l,a,r[l],o,i),s&&(o+=i)}o+=s?r.height-r.domrect.label.height-r.domrect.content.height:r.height}},Q=(e,t)=>{const n="path"===e;for(const e of["x","y"]){const a=n?t[e]:t[e].baseVal.value;if(a<N[e].min)N[e].min=a;else if(a>N[e].max){let o=a;n||("x"===e?o+=t.width.baseVal.value:"y"===e&&(o+=t.height.baseVal.value)),N[e].max=o}}},Y=(e,t,n,a)=>{const o=a;o.d=500,"center"===o.func||"preserve"===o.func||"fit"===o.func?("center"===o.func||"fit"===o.func?(o.canvas={h:n.h,w:n.w},o.chart={h:N.y.max-N.y.min,w:N.x.max-N.x.min},o.k=Math.min(1,o.canvas.h/o.chart.h,o.canvas.w/o.chart.w),"center"===o.func&&o.k<.25&&(o.k=.25,(e=>{const t=document.querySelector("#chart-message");t.querySelector("#chart-message-text").textContent=e,t.classList.remove("invisible"),setTimeout((()=>t.classList.add("invisible")),15e3)})("Zoom out or pan to see the entire chart.")),o.chart.h*=o.k,o.chart.w*=o.k,o.x=(o.canvas.w-o.chart.w)/2-N.x.min*o.k,o.y=(o.canvas.h-o.chart.h)/2-N.y.min*o.k):"preserve"===o.func&&(o.d=0),e.transition().duration(o.d).call(t.transform,d3.zoomIdentity.translate(o.x,o.y).scale(o.k))):e.transition().duration(o.d).call(t.scaleBy,o.k)},ee=e=>{const t=e.getBoundingClientRect(),n={h:window.innerHeight-t.top,w:window.innerWidth},a=d3.select("#chart").append("svg").attr("id","canvas").attr("viewBox",[0,0,n.w,n.h]).attr("height",n.h).attr("width",n.w),o=a.append("g").attr("id","group"),s=d3.zoom().filter((()=>!(d3.event.ctrlKey||d3.event.button||"dblclick"===d3.event.type||d3.event.target.classList.contains("prevent-zoom")||document.querySelector("#canvas .selected")))).on("start",(()=>{for(const e of N.selections.ids.note)z(document.querySelector(`#note${e}`))})).on("zoom",(()=>{const{k:e,x:t,y:n}=d3.event.transform;[e,t,n].some((e=>Number.isNaN(e)))||o.attr("transform",d3.event.transform)})).on("end",(()=>{for(const e of N.selections.ids.note)H(document.querySelector(`#note${e}`))})),i=o.append("g").attr("id","linkgroup"),d=o.append("g").attr("id","linkghostgroup"),c=o.append("g").attr("id","nodegroup"),u=o.append("g").attr("id","nodetextgroup"),m=o.append("g").attr("id","linknotegroup"),h=o.append("g").attr("id","linknotetextgroup"),p=o.append("g").attr("id","notegroup"),y=o.append("g").attr("id","notetextgroup"),g=i.selectAll("path").data(N.links),f=d.selectAll("path").data(N.links),b=c.selectAll("rect").data(N.nodes),x=u.selectAll("text").data(N.nodes),w=m.selectAll("rect").data(N.linknotes),v=h.selectAll("text").data(N.linknotes),k=p.selectAll("rect").data(N.notes),S=y.selectAll("text").data(N.notes),$={};g.exit().remove(),g.enter().append("path").merge(g).attr("class",(()=>{let e="chart-link chart-elem";return N.designer&&"manual"===N.mode&&(e+=" draggable"),e})).attr("id",(e=>`link${e.id}`)).attr("data-id",(e=>e.id)).attr("data-parent",(e=>e.parent_id)).attr("data-child",(e=>e.child_id)).attr("data-path",(e=>JSON.stringify(e.path))).attr("data-data",(e=>{const t=e.data.map((e=>({label:e.label,content:e.content})));return JSON.stringify(t)})).attr("style",(e=>{const t=e.style[".chart-link"];return t?Object.entries(t).map((([e,t])=>`${e}: ${t};`)).join(" "):null})).each(((e,t,n)=>{const a=d3.select(n[t]);var o;$[e.id]={scaledpath:(o=e.path,o.map(((e,t)=>{const n={...e};let a=.5,r=.5;if(!t||t===o.length-1){const n=e,l=t?o[t-1]:o[t+1];if(n.x!==l.x){const e=n.x>l.x?"left":"right";a=N.shiftpath[e]}else{const e=n.y>l.y?"top":"bottom";r=N.shiftpath[e]}}return n.x+=a,n.y+=r,n.x=N.x.scale(n.x),n.x<N.x.min?N.x.min=n.x:n.x>N.x.max&&(N.x.max=n.x),n.y=N.y.scale(n.y),n.y<N.y.min?N.y.min=n.y:n.y>N.y.max&&(N.y.max=n.y),n})))},$[e.id].d=W($[e.id].scaledpath),a.attr("d",$[e.id].d).attr("data-scaledpath",JSON.stringify($[e.id].scaledpath))})),N.designer&&(f.exit().remove(),"manual"===N.mode?f.enter().append("g").merge(f).attr("data-id",(e=>e.id)).each(((e,t,n)=>{((e,t,n)=>{const a=d3.select(e),o="basis"===N.linecurve,r=[],l=[],s=[];let i=[];for(const[e,n]of t.entries()){if(i.length<2)i.push(n);else{let e;e=i[0].x===i[1].x?"x":"y",n[e]===i[0][e]?i.push(n):(l.push("x"===e?"y":"x"),s.push([...i]),i=[{...i[i.length-1]},n])}e===t.length-1&&(l.push("x"===l[l.length-1]?"y":"x"),s.push(i))}if(o)for(const[e,t]of s.entries()){const n=[...t];e?n.unshift(s[e-1][s[e-1].length-2]):n.unshift(t[0],t[0]),e<s.length-1?n.push(s[e+1][1]):n.push(t[t.length-1],t[t.length-1]),r.push(n)}for(const[e,t]of s.entries())a.append("path").attr("class",(()=>{let t="chart-link-ghost";return e&&e!==s.length-1&&("x"===l[e]?t+=" draggable ns":"y"===l[e]&&(t+=" draggable ew")),t})).attr("d",o?V(r[e]):W(t)).attr("data-scaledpath",JSON.stringify(t)).attr("data-id",n).attr("data-index",e).attr("data-axis",l[e])})(n[t],$[e.id].scaledpath,e.id)})):f.enter().append("path").merge(f).attr("class","chart-link-ghost").attr("d",(e=>$[e.id].d)).attr("data-id",(e=>e.id))),b.exit().remove(),b.enter().append("rect").merge(b).attr("class",(()=>{let e="chart-node chart-elem";return N.designer&&"manual"===N.mode&&(e+=" draggable"),e})).attr("id",(e=>`node${e.id}`)).attr("data-id",(e=>e.id)).attr("x",(e=>{const t=N.x.scale(e.x)+N.gutter,n=t+N.box.node.wrapper.width;return t<N.x.min?N.x.min=t:n>N.x.max&&(N.x.max=n),t})).attr("y",(e=>{const t=N.y.scale(e.y)+N.gutter,n=t+N.box.node.wrapper.height;return t<N.y.min?N.y.min=t:n>N.y.max&&(N.y.max=n),t})).attr("height",N.box.node.wrapper.height).attr("width",N.box.node.wrapper.width).attr("rx",N.box.node.wrapper.radius).attr("ry",N.box.node.wrapper.radius).attr("style",(e=>{const t=e.style[".chart-node"];return t?Object.entries(t).map((([e,t])=>`${e}: ${t};`)).join(" "):null})).attr("data-name",(e=>e.name)).attr("data-data",(e=>{const t=e.data.map((e=>({label:e.label,content:e.content})));return JSON.stringify(t)})).attr("data-position",(e=>JSON.stringify(e.layout.position))),x.exit().remove(),x.enter().append("text").merge(x).attr("class","chart-node-text").attr("id",(e=>`nodetext${e.id}`)).attr("data-id",(e=>e.id)).each(((e,t,n)=>{const a=d3.select(n[t]);let o={x:N.x.scale(e.x)+N.gutter+N.pad,y:N.y.scale(e.y)+N.gutter+N.pad};a.attr("x",o.x).attr("y",o.y);for(const t of["name","label","content"])o=X(o,t,e,N.pad,N.box.node.left,N.box.node.right,N.box.node.text.width);G(e,"chart-node-",o,a)})),w.exit().remove(),w.enter().append("rect").merge(w).attr("class",(()=>{let e="chart-linknote chart-elem";return N.designer&&"manual"===N.mode&&(e+=" draggable"),e})).attr("id",(e=>`linknote${e.id}`)).attr("data-id",(e=>e.id)).attr("x",(e=>{const t="manual"===N.mode,n="coordinates"in e.layout,{x:a,y:o}=t&&n?{x:N.x.scale(e.layout.coordinates.x),y:N.y.scale(e.layout.coordinates.y)}:((e,t,n,a)=>{const o=(e,t,n,a,o)=>{let r=e;return t<n?r-=a+o:r+=o,r},r=(e,t,n,a,o)=>{let r=e;return e<t?r+=n-a:r-=n-a,r-=o/2,r},l=e.reduce(((t,n,a)=>(a&&t.push({start:{x:e[a-1].x,y:e[a-1].y},end:{x:n.x,y:n.y},len:Math.abs(n.x-e[a-1].x)+Math.abs(n.y-e[a-1].y)}),t)),[]),s=l.reduce(((e,t)=>e+t.len),0)/2;let i=null,d=null,c=0;for(const u of l){if(!(c+u.len<=s)){u.start.x!==u.end.x?(i=r(u.start.x,u.end.x,s,c,n),d=o(u.start.y,e[0].y,e[e.length-1].y,t,a)):(i=o(u.start.x,e[0].x,e[e.length-1].x,n,a),d=r(u.start.y,u.end.y,s,c,t));break}c+=u.len}return i<N.x.min?N.x.min=i:i+n>N.x.max&&(N.x.max=i+n),d<N.y.min?N.y.min=d:d+t>N.y.max&&(N.y.max=d+t),{x:i,y:d}})($[e.id].scaledpath,e.box.wrapper.height,e.box.wrapper.width,N.textpad);if(t&&!n){const t=new FormData;t.append("action","move"),t.append("data",JSON.stringify({linknotes:{[e.id]:{x:N.x.scalenote(a),y:N.y.scalenote(o)}}})),r(window.location.pathname,t)}return $[e.id].x=a,$[e.id].y=o,a<N.x.min?N.x.min=a:a+e.box.wrapper.width>N.x.max&&(N.x.max=a+e.box.wrapper.width),o<N.y.min?N.y.min=o:o+e.box.wrapper.height>N.y.max&&(N.y.max=o+e.box.wrapper.height),$[e.id].x})).attr("y",(e=>$[e.id].y)).attr("height",(e=>e.box.wrapper.height)).attr("width",(e=>e.box.wrapper.width)).attr("rx",(e=>e.box.wrapper.radius)).attr("ry",(e=>e.box.wrapper.radius)).attr("data-parent",(e=>e.parent_id)).attr("data-child",(e=>e.child_id)).attr("data-data",(e=>{const t=e.data.map((e=>({label:e.label,content:e.content})));return JSON.stringify(t)})).attr("data-position",(e=>JSON.stringify(e.layout.position))).attr("style",(e=>{const t=e.style[".chart-linknote"];return t?Object.entries(t).map((([e,t])=>`${e}: ${t};`)).join(" "):null})),v.exit().remove(),v.enter().append("text").merge(v).attr("class","chart-linknote-text").attr("id",(e=>`linknotetext${e.id}`)).attr("data-id",(e=>e.id)).each(((e,t,n)=>{const a=d3.select(n[t]);let o={x:$[e.id].x+N.textpad,y:$[e.id].y+N.textpad};a.attr("x",o.x).attr("y",o.y);for(const t of["name","label","content"])o=X(o,t,e,N.textpad,e.box.left,e.box.right,e.box.text.width);G(e,"chart-linknote-",o,a)})),k.exit().remove(),k.enter().append("rect").merge(k).attr("class",(()=>{let e="chart-note chart-elem";return N.designer&&(e+=" draggable"),e})).attr("id",(e=>`note${e.id}`)).attr("data-id",(e=>e.id)).attr("x",(e=>{const t=N.x.scale(e.coordinates.box.x),n=t+e.width;return t<N.x.min?N.x.min=t:n>N.x.max&&(N.x.max=n),t})).attr("y",(e=>{const t=N.y.scale(e.coordinates.box.y),n=t+e.height;return t<N.y.min?N.y.min=t:n>N.y.max&&(N.y.max=n),t})).attr("height",(e=>e.height)).attr("width",(e=>e.width)).attr("pointer-events","visible").attr("style",(e=>{const t=e.style[".chart-note"];return t?Object.entries(t).map((([e,t])=>`${e}: ${t};`)).join(" "):null})),S.exit().remove(),S.enter().append("text").merge(S).attr("class","chart-note-text").attr("id",(e=>`notetext${e.id}`)).attr("data-id",(e=>e.id)).attr("data-content",(e=>JSON.stringify(e.data))).each(((e,t,n)=>{const a=d3.select(n[t]),o={align:e.layout.align||"left",style:e.style[".chart-note-content"]||{},x:N.x.scale(e.coordinates.text.x),y:N.y.scale(e.coordinates.text.y)};a.attr("x",o.x).attr("y",o.y).attr("data-align",o.align),o.anchor=l(o.align),"left"===o.align?o.dx=0:"right"===o.align?o.dx=e.textwidth:o.dx=e.textwidth/2,o.style["text-anchor"]=o.anchor;for(const t of e.lines)o.y+=t.height,a.append("tspan").text(t.text).attr("class","chart-note-content").attr("x",o.x).attr("y",o.y).attr("dx",o.dx).attr("style",(()=>o.style?Object.entries(o.style).map((([e,t])=>`${e}: ${t};`)).join(" "):null))}));for(const e of document.querySelectorAll(".chart-elem")){const t=e.tagName.toLowerCase();if("path"===t)for(const n of JSON.parse(e.dataset.scaledpath))Q(t,n);else Q(t,e)}N.x.min-=N.gutter,N.x.max+=N.gutter,N.y.min-=N.gutter,N.y.max+=N.gutter,Y(a,s,n,N.zoom),a.call(s);for(const e of document.querySelectorAll(".zoom-in"))e.addEventListener("click",(()=>Y(a,s,n,{k:1.5})));for(const e of document.querySelectorAll(".zoom-out"))e.addEventListener("click",(()=>Y(a,s,n,{k:2/3})));for(const e of document.querySelectorAll(".fit-screen"))e.addEventListener("click",(()=>Y(a,s,n,{func:"fit"})))},te=(e,t)=>{if("active"===t?(e.classList.remove("disabled-state"),e.classList.add("active-state"),e.removeAttribute("disabled")):"disabled"===t?(e.classList.remove("active-state"),e.classList.add("disabled-state"),e.setAttribute("disabled",!0)):"enabled"===t&&(e.classList.remove("active-state","disabled-state"),e.removeAttribute("disabled")),e.classList.contains("toggle-font-style")||e.classList.contains("toggle-font-style-node")||e.classList.contains("toggle-font-style-link")){const n=e;n.dataset.nextstate="active"===t?"inactive":"active"}},ne=(e,t)=>{for(const n of e)te(n,t)},ae=(e,t=null)=>{const n=t||e.currentTarget,a=document.querySelector(`#${n.dataset.type}${n.dataset.id}`);if(n.classList.contains("selected"))if(e.ctrlKey)P(n),J(a);else{for(const e of document.querySelectorAll(".chart-elem.selected"))if(e!==a){const t=document.querySelector(`.tab-item[data-id="${e.dataset.id}"]`);P(t),J(e)}2===e.detail&&N.callback.editSelectionHandler(e,a)}else e.ctrlKey||e.metaKey||e.shiftKey||(()=>{for(const e of document.querySelectorAll(".tab-item.selected"))P(e)})(),(e=>{e.classList.add("selected")})(n),N.callback.selectionHandler(e,a)},oe=()=>{const e=[],n=[],a=[],o={editable:[".edit-chart-elements"],selectedelem:[".remove-chart-elements",'.color-picker-button[data-picker="line"]',".style-lines",".line-style-menu"],selectedlinknotenode:[".toggle-text-position"],selectedlinknotenodenote:[".align-text",".toggle-font-style",".font-size-menu",".size-font",'.color-picker-button[data-picker="text"]','.color-picker-button[data-picker="fill"]',".format-text-menu",".text-layout-menu"],oneselectednodeornone:[".add-chart"],elem:[".zoom-in",".zoom-out",".fit-screen",".download-menu",".select-all-menu"],link:['.select-all[data-target="link"]',".linkstyle-menu"],linknote:['.select-all[data-target="linknote"]'],node:['.select-all[data-target="node"]'],note:['.select-all[data-target="note"]'],gap:[".grow-gap",".shrink-gap",".gap-menu"]};if(!document.querySelector("#diveditable")){const r={},s={},i=Object.keys(N.classes.elem);(()=>{let e=!1;const t=document.querySelector("#canvas"),n=N.classes.elem;N.selections.all=0;for(const[e,t]of Object.entries(n)){const n=[...document.querySelectorAll(`.${t}.selected`)],a=n.filter((e=>e.classList.contains("draggable")));N.selections.all+=n.length,N.selections.ids[e]=n.map((e=>e.dataset.id)),N.selections.dragids[e]=a.map((e=>e.dataset.id))}for(const t of Object.values(N.selections.dragids))if(t.length){e=!0;break}e?t.addEventListener("mousedown",N.callback.dragSelectionHandler):t.removeEventListener("mousedown",N.callback.dragSelectionHandler)})();for(const e of i)r[e]=document.querySelectorAll(`.${N.classes.elem[e]}`).length,s[e]=N.selections.ids[e].length;if(r.elem=i.reduce(((e,t)=>e+r[t]),0),s.all=N.selections.all,s.notnode=s.link+s.linknote+s.note,s.nodenote=s.node+s.note,s.linklinknote=s.link+s.linknote,s.notelink=s.note+s.link,s.nodelinknote=s.node+s.linknote,1===s.all||!s.notnode&&s.node>1||!s.nodenote&&s.linklinknote>1?n.push(...o.editable):a.push(...o.editable),s.all){const a=t((()=>{const e=[];for(const[t,n]of Object.entries(N.selections.ids))for(const a of n)e.push(document.querySelector(`#${t}${a}`));return e})(),"stroke-dasharray");a&&e.push(`.style-lines[data-value="${a}"]`),n.push(...o.selectedelem)}else a.push(...o.selectedelem);if(!s.notelink&&s.nodelinknote?n.push(...o.selectedlinknotenode):a.push(...o.selectedlinknotenode),!s.link&&s.all){const a=(()=>{const e=[],{linknote:t,node:n,note:a}=N.selections.ids;for(const[o,r]of Object.entries({linknote:t,node:n,note:a}))for(const t of r)e.push(...document.querySelectorAll(`.chart-${o}-text[data-id="${t}"] tspan`));return e})();for(const n of["font-weight","font-style","text-decoration-line","text-anchor"]){let o=t(a,n);o&&("font-weight"!==n||700!==o&&"700"!==o||(o="bold"),"text-anchor"!==n?e.push(`.toggle-font-style[data-active-value="${o}"]`):(o=l(o)||"left",e.push(`.align-text[data-value="${o}"]`)))}n.push(...o.selectedlinknotenodenote)}else a.push(...o.selectedlinknotenodenote);"auto"!==N.mode||s.all&&1!==s.node?a.push(...o.oneselectednodeornone):n.push(...o.oneselectednodeornone),r.elem?n.push(...o.elem):a.push(...o.elem),r.link?n.push(...o.link):a.push(...o.link),r.linknote?n.push(...o.linknote):a.push(...o.linknote),r.node?n.push(...o.node):a.push(...o.node),r.note?n.push(...o.note):a.push(...o.note),"auto"===N.mode&&r.node>1?n.push(...o.gap):a.push(...o.gap),n.length&&ne(document.querySelectorAll(n.join(", ")),"enabled"),a.length&&ne(document.querySelectorAll(a.join(", ")),"disabled"),e.length&&ne(document.querySelectorAll(e.join(", ")),"active"),(e=>{const t=document.querySelector("#canvas");"active"===e.select?(t.classList.remove("crosshair","prevent-zoom"),t.removeEventListener("mousedown",N.callback.noteCrosshairHandler),t.removeEventListener("click",N.callback.nodeCrosshairHandler)):(t.classList.add("crosshair","prevent-zoom"),"active"===e.node?(t.removeEventListener("mousedown",N.callback.drawNote),t.addEventListener("click",N.callback.nodeCrosshairHandler)):"active"===e.note&&(t.removeEventListener("click",N.callback.nodeCrosshairHandler),t.addEventListener("mousedown",N.callback.noteCrosshairHandler))),ne(document.querySelectorAll(".add-node"),e.node),ne(document.querySelectorAll(".add-note"),e.note)})(N.cursor)}},re=(t={},a={})=>{const o=document.querySelector("#chart");((e,t,n=!1)=>{if(t&&t.dataset.json){const e=document.querySelector("#chart-nodes"),n=document.querySelector("#chart-links"),a=document.querySelector("#chart-notes"),o=document.querySelector("#chart-style");N.nodes=e?JSON.parse(e.textContent):[],N.links=n?JSON.parse(n.textContent):[],N.notes=a?JSON.parse(a.textContent):[],N.style=o?JSON.parse(o.textContent):{},e&&e.remove(),n&&n.remove(),a&&a.remove(),o&&o.remove(),N.columns=Number(t.dataset.columns)>1?Number(t.dataset.columns):2,N.rows=Number(t.dataset.rows)>1?Number(t.dataset.rows):2,N.gutterscale=t.dataset.gutterscale||4/3,N.linecurve=t.dataset.linecurve||"linear",N.mode=t.dataset.mode||"auto",N.wrap=t.dataset.wrap||"wrap",s(t,["data-columns","data-gutterscale","data-linecurve","data-json","data-mode","data-wrap","data-rows"])}else{for(const t of["links","nodes","notes","style"])t in e&&"string"==typeof e[t]&&(N[t]=JSON.parse(e[t]));N.columns=Number(e.columns)>1?Number(e.columns):N.columns,N.rows=Number(e.rows)>1?Number(e.rows):N.rows,N.gutterscale=e.gutterscale||N.gutterscale,N.linecurve=e.linecurve||N.linecurve,N.mode=e.mode||N.mode,N.wrap=e.wrap||N.wrap||"wrap"}if(N.linknotes=N.links.filter((e=>e.data.length)),N.designer=!!document.querySelector("#pg-chart-design, #pg-chart-free"),N.zoom=e.zoom||{func:"center"},n){const e=d3.zoomTransform(d3.select("#canvas").node());N.zoom={func:"preserve",x:e.x,y:e.y,k:e.k}}})(t,o,a.preservetransform),(e=>{i(e),q.garbageCollection()})(o),I(o),U(o),(()=>{if(N.x={max:2,min:1},N.y={max:2,min:1},N.x.scalelinear=d3.scaleLinear().domain([1,N.columns]).range([0,N.box.node.all.width*(N.columns-1)]),N.y.scalelinear=d3.scaleLinear().domain([1,N.rows]).range([0,N.box.node.all.height*(N.rows-1)]),N.x.scale=e=>N.x.scalelinear(Number(e)),N.y.scale=e=>N.y.scalelinear(Number(e)),N.x.scalenode=e=>(Number(e)-N.gutter)/N.x.scale(2)+1,N.y.scalenode=e=>(Number(e)-N.gutter)/N.y.scale(2)+1,N.x.scalenote=e=>Number(e)/N.x.scale(2)+1,N.y.scalenote=e=>Number(e)/N.y.scale(2)+1,N.shiftpath.top=N.gutter/N.box.node.all.height,N.shiftpath.left=N.gutter/N.box.node.all.width,N.shiftpath.bottom=1-N.shiftpath.top,N.shiftpath.right=1-N.shiftpath.left,N.nodes.length){const e=N.nodes[0];N.x.min=N.x.scale(e.x)+N.gutter,N.x.max=N.x.min+N.box.node.wrapper.width,N.y.min=N.y.scale(e.y)+N.gutter,N.y.max=N.y.min+N.box.node.wrapper.height}else if(N.notes.length){const e=N.notes[0];N.x.min=N.x.scale(e.coordinates.box.x),N.x.max=N.x.min+e.width,N.y.min=N.y.scale(e.coordinates.box.y),N.y.max=N.y.min+e.height}})(),ee(o),"history"in a&&!a.history||((()=>{const t=e=>{try{localStorage.setItem("index",JSON.stringify(e.length-1)),localStorage.setItem("history",JSON.stringify(e))}catch(n){e.length>1&&t(e.slice(1))}};if(e){const e=M();let n=T();e<n.length-1&&(n=n.slice(0,e+1)),n.push({columns:N.columns,rows:N.rows,nodes:N.nodes,links:N.links,notes:N.notes,gutterscale:N.gutterscale,linecurve:N.linecurve,mode:N.mode,wrap:N.wrap,style:"string"==typeof N.style?JSON.parse(N.style):N.style}),t(n)}})(),N.designer&&(()=>{if(e){const e=M(),t=T().length-1;for(const n of document.querySelectorAll(".restore-chart"))e&&e<t||e&&"undo"===n.dataset.undoredo||e<t&&"redo"===n.dataset.undoredo?te(n,"enabled"):te(n,"disabled")}})()),N.designer&&((()=>{const e={},{nodes:t,links:a,notes:o}=N,r=t.reduce(((e,t)=>(e[t.id]=t.name,e)),{});e.nodes=t.map((e=>({list_item:`\n        <li class="node-item tab-item" id="tabnode${e.id}" data-id="${e.id}" data-type="node">\n          <div class="node-item-name">${e.name}</div>\n        </li>`,...e}))).sort(((e,t)=>n(e.name.toLowerCase(),t.name.toLowerCase()))),e.notes=o.sort(((e,t)=>{const a=e.stripped.toLowerCase(),o=t.stripped.toLowerCase();return n(a,o)})).map((e=>({list_item:`\n        <li class="note-item tab-item" id="tabnote${e.id}" data-id="${e.id}" data-type="note">\n          <div class="note-item-data">\n            <div class="note-item-data-item">${e.stripped}</div>\n          </div>\n        </li>`,...e}))),e.links=a.map((e=>({list_item:`\n        <li class="link-item tab-item" id="tablink${e.id}" data-id="${e.id}" data-type="link">\n          <div class="link-item-parent">${r[e.parent_id]}</div>\n          <div class="link-item-line"></div>\n          <div class="link-item-child">${r[e.child_id]}</div>\n        </li>`,parent_name:r[e.parent_id],child_name:r[e.child_id],...e}))).sort(((e,t)=>{const a=`${e.parent_name.toLowerCase()}${e.child_name.toLowerCase()}`,o=`${t.parent_name.toLowerCase()}${t.child_name.toLowerCase()}`;return n(a,o)}));for(const t of["nodes","links","notes"]){const n=document.querySelector(`#${t}-count`),a=document.querySelector(`#${t}-tab`);n.textContent=N[t].length,i(a);let o="";for(const n of e[t])o+=n.list_item;a.innerHTML=o}for(const e of document.querySelectorAll(".tab-item"))e.addEventListener("click",ae)})(),q.addListeners("#canvas, .chart-elem, .chart-link-ghost, .chart-note-text","click",{selectionHandler:N.callback.selectionHandler}),a.reselect&&(()=>{for(const[e,t]of Object.entries(N.selections.ids))for(const n of t)D(document.querySelector(`#${e}${n}`))})(),a.buttonstate&&oe(),(e=>{for(const t of document.querySelectorAll(".auto-wrap"))te(t,"nowrap"===e?"enabled":"active")})(N.wrap),(e=>{for(const t of document.querySelectorAll(".linkstyle-button"))e?t.dataset.linecurve===e?te(t,"active"):te(t,"enabled"):te(t,"disabled")})(N.links.length?N.linecurve:null),(e=>{for(const t of document.querySelectorAll(".mode-button"))e?t.dataset.mode===e?te(t,"active"):te(t,"enabled"):"auto"===t.dataset.mode?te(t,"active"):te(t,"enabled")})(N.mode))},le=e=>{const t=(e,t)=>{const n=(()=>{const e={".chart-link":{fill:"none","stroke-width":"2"},".chart-node":{"stroke-width":"2"},".chart-note":{"stroke-width":"2"},".chart-note-content":{"white-space":"pre"}};for(const t of document.querySelector("#style").sheet.cssRules){const n=t.selectorText.trim();let a=t.cssText.replace(n,"").replace(/[{}]/g,"").trim();a=a.split(";").reduce(((e,t)=>{if(t.trim()){const[n,a]=t.split(":");e[n.trim()]=a.trim()}return e}),{}),e[n]=n in e?{...e[n],...a}:a}return e})(),a=e.querySelector("#linkghostgroup");a&&a.remove();for(const t of e.querySelectorAll(".chart-elem-outline"))t.remove();for(const n of e.querySelectorAll("rect, text, tspan")){const e=parseFloat(n.getAttribute("x")),a=parseFloat(n.getAttribute("y"));n.setAttributeNS(null,"x",e+t.tx),n.setAttributeNS(null,"y",a+t.ty)}for(const n of e.querySelectorAll("path")){const e=JSON.parse(n.dataset.scaledpath).map((e=>({x:e.x+t.tx,y:e.y+t.ty})));n.setAttributeNS(null,"d",W(e))}e.removeAttribute("id");for(const t of Object.keys(e.dataset))e.removeAttribute(`data-${t.replace(/([A-Z])/g,"-$1").toLowerCase()}`);for(const t of e.querySelectorAll("*")){const e=t.style.cssText.split(";").reduce(((e,t)=>{if(t.trim()){const[n,a]=t.split(":");e[n.trim()]=a.trim()}return e}),{});s(t,["id","pointer-events"]);for(const e of["chart-elem","draggable","selected"])t.classList.remove(e);for(const a of t.classList.values())if(`.${a}`in n){const o={...n[`.${a}`],...e};t.setAttributeNS(null,"style",Object.entries(o).reduce(((e,[t,n])=>`${e}${t}:${n};`),""));break}for(const e of Object.keys(t.dataset))t.removeAttribute(`data-${e.replace(/([A-Z])/g,"-$1").toLowerCase()}`);t.removeAttribute("class")}},n=e.currentTarget.dataset.ext;if(n){const e=document.querySelector("main").id,a=document.querySelector("#chart-name"),o=["pg-chart-view","pg-chart-public"].includes(e)?a.textContent:a.value,r=(()=>{let e=n,t=o.slice(0,100);return t=t.replace(/[^a-z0-9]/gi," ").trim(),t=t.replace(/\s+/g,"-"),t=t.toLowerCase(),e.startsWith("png")&&"png"!==e&&(e="png"),`${t}.${e}`})(),l=(e=>{const t={h:N.y.max-N.y.min,w:N.x.max-N.x.min,tx:N.x.min?-1*N.x.min:0,ty:N.y.min?-1*N.y.min:0};let n=1;if(e.startsWith("png")&&"png"!==e){const[n,a]=e.slice(3).split("x").map((e=>parseFloat(e)));if(t.w/t.h>n/a){const e=t.w*(a/n);t.ty+=(e-t.h)/2,t.h=e}else{const e=t.h*(n/a);t.tx+=(e-t.w)/2,t.w=e}}"svg"!==e&&(n=4096/(t.h>t.w?t.h:t.w),n=n>1?Math.trunc(n):Math.floor(100*n)/100);return t.h=Math.ceil(t.h*n),t.w=Math.ceil(t.w*n),t.k=n,t})(n),s=(e=>{const n=document.querySelector("#canvas"),a=n.cloneNode(!0);for(const e of n.querySelectorAll("text")){const t={};for(const[n,a]of[...e.querySelectorAll("tspan")].entries())t[n]=window.getComputedStyle(a).getPropertyValue("font-size");if(Object.keys(t).length){const n=a.querySelector(`#${e.id}`);if(n)for(const[e,a]of[...n.querySelectorAll("tspan")].entries())if(e in t){const n=a.style.cssText.split(";").reduce(((e,t)=>{if(t.trim()){const[n,a]=t.split(":");e[n.trim()]=a.trim()}return e}),{});n["font-size"]=t[e],a.setAttributeNS(null,"style",Object.entries(n).reduce(((e,[t,n])=>`${e}${t}:${n};`),""))}}}return t(a,e),((e,t,n)=>{let a="";for(const[e,n]of["font-family","font-size"].entries())e&&(a+=" "),a+=`${n}: ${t.getPropertyValue(n)};`;e.setAttributeNS(null,"viewBox",`0, 0, ${n.w}, ${n.h}`),e.setAttributeNS(null,"height",n.h),e.setAttributeNS(null,"width",n.w),e.setAttributeNS(null,"style",a),e.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),e.firstChild.setAttributeNS(null,"transform",`translate(0, 0), scale(${n.k})`)})(a,window.getComputedStyle(n),e),a})(l);if("svg"===n){const e=(e=>{let t='<?xml version="1.0" encoding="UTF-8" standalone="no"?>';return t+=(new XMLSerializer).serializeToString(e),t=t.replace(/></g,">\n\r<"),t})(s);saveAs(new Blob([e]),r)}else{const{w:t,h:a}=l,o=((e,t)=>{const n=document.createElement("canvas");return n.width=e,n.height=t,n})(t,a),i=o.getContext("2d"),d=(new XMLSerializer).serializeToString(s),c=new Blob([d],{type:"image/svg+xml;charset=utf-8"}),u=window.URL||window.webkitURL||window,m=u.createObjectURL(c),h=new Image;h.onload=()=>{if(i.fillStyle="png"===n?"rgba(0, 0, 0, 0)":"rgba(255, 255, 255, 1)",i.fillRect(0,0,t,a),i.drawImage(h,0,0,t,a),u.revokeObjectURL(m),"pdf"!==n){const e=n.startsWith("png")?"image/png":"image/jpeg";o.toBlob((e=>{saveAs(e,r)}),e)}else{const{jsPDF:n}=window.jspdf,l=t>a?"l":"p",s=new n({orientation:l,unit:"pt",format:"a4",compress:!0}),d={w:"l"===l?841.8888:595.27584,h:"l"===l?595.27584:841.8888},c={format:"PNG",width:t,height:a,compression:"FAST"},u=a/t;(a>d.h||t>d.w)&&(a/d.h>t/d.w?(c.width=d.h*(1/u),c.height=d.h):(c.width=d.w,c.height=d.w*u)),c.x=(d.w-c.width)/2,c.y=(d.h-c.height)/2;for(const e of["x","y","width","height"])c[e]=Math.trunc(c[e]);if("pg-chart-free"!==e)c.imageData=o.toDataURL(),s.addImage(c),saveAs(new Blob([s.output("blob")],{type:"application/octet-stream;charset=utf-8"}),r);else if("pg-chart-free"===e){const e=new Image;e.onload=()=>{t>360&&a>72&&i.drawImage(e,t-122,a-24,t,a),c.imageData=o.toDataURL(),s.addImage(c),saveAs(new Blob([s.output("blob")],{type:"application/octet-stream;charset=utf-8"}),r)},e.src="/static/img/logos/lexchart-logo-425x72.png"}}},h.src=m}}},se=e=>{e.currentTarget.parentElement.remove()},ie=()=>{const e=document.querySelector("#share-modal-wrapper");(e=>{const t=e.querySelector(".share-modal-search"),n=document.querySelector(`#dropdownify${t.dataset.dropdownify}`);S.field.clearSelection(t,!1),i(n);for(const t of e.querySelectorAll(".owner-name, .owner-email"))t.textContent="";for(const t of e.querySelectorAll(".share-person:not(.hidden)"))t.remove()})(e),((e="",t=null,n=null)=>{o({url:e,method:"GET",querystr:t,callback:n})})(window.location.pathname,"action=share",(t=>{"error"in t&&t.error.length||(((e,t)=>{const n={name:e.querySelector(".owner-name"),email:e.querySelector(".owner-email")},a=e.querySelector('input[name="share-modal-public"]'),o=e.querySelector(".share-people"),r=o.querySelector(".share-person.hidden"),l=e.querySelector(".share-modal-search"),s=document.querySelector(`#dropdownify${l.dataset.dropdownify}`);e.querySelector('input[name="share-modal-search-email"]').setAttribute("data-lookup",JSON.stringify(t.lookup));for(const[e,n]of Object.entries(t.lookup)){const t=n?`${n} <${e}>`:e;S.field.appendOption(s,t,e)}n.name.textContent=t.owner.name,n.email.textContent=t.owner.email;for(const e of t.collaborators){const t=r.cloneNode(!0),n=t.querySelector(".share-name"),a=t.querySelector(".share-email"),l=t.querySelector('input[name="share-modal-email"]'),s=t.querySelector('select[name="share-modal-permission"]'),i=t.querySelector(".share-status"),d=t.querySelector(".remove-share");t.classList.remove("hidden"),n.textContent=e.name?e.name:e.email,a.textContent=e.name?e.email:"",l.value=e.email,s.value=e.permission,i.textContent=i.dataset[e.status],"pending"===e.status?i.classList.add(i.dataset.color):i.classList.remove(i.dataset.color),d.addEventListener("click",se),o.appendChild(t)}if(a){const e="public"in t&&t.public;a.checked=e}})(e,t),(e=>{const t=e.querySelector(".modal-section:first-child"),n=e.querySelector(".modal-section:last-child"),a=e.querySelector(".modal-message"),o=parseFloat(window.getComputedStyle(document.documentElement).fontSize);let r=.96*window.innerHeight-(a.classList.contains("hidden")?6*o:4*o);n?parseFloat(window.getComputedStyle(n).getPropertyValue("grid-column-start"))>1?(t.style.maxHeight=`${r}px`,n.style.maxHeight=`${r}px`):(r/=2,t.style.maxHeight=`${r}px`,n.style.maxHeight=`${r}px`):t.style.maxHeight=`${r}px`})(e),E.show(e))}))},de=e=>{const t=document.querySelector("#share-modal-wrapper"),n=t.querySelector('input[name="share-modal-search-text"]'),o=e.currentTarget.value.trim();if(o){const e=t.querySelector(".share-modal-search"),r=t.querySelector('input[name="share-modal-search-email"]'),l=(r.value=o,r.reportValidity());if(l)if(((e,t)=>{if(t===e.querySelector(".owner-email").textContent)return!0;for(const n of e.querySelectorAll('input[name="share-modal-email"]'))if(t===n.value)return!0;return!1})(t,o)){const t=document.querySelector(`#dropdownify${e.dataset.dropdownify}`),n=a(o),r=t.querySelectorAll(`.dropdown-option[data-value="${n}"]`);if(r.length>1)for(const e of r)if(e.dataset.label===e.dataset.value){e.remove();break}}else{const e=JSON.parse(r.dataset.lookup),n=t.querySelector(".share-people"),a=n.querySelector(".share-person.hidden").cloneNode(!0),l=a.querySelector(".share-name"),s=a.querySelector(".share-email"),i=a.querySelector('input[name="share-modal-email"]'),d=a.querySelector('select[name="share-modal-permission"]'),c=a.querySelector(".remove-share");a.classList.remove("hidden"),i.value=o,d.value="view",c.addEventListener("click",se),o in e&&e[o]?(l.textContent=e[o],s.textContent=o):(l.textContent=o,s.textContent=""),n.appendChild(a)}else{const t=document.querySelector(`#dropdownify${e.dataset.dropdownify}`),n=a(o),r=t.querySelector(`.dropdown-option[data-value="${n}"]`);r&&r.remove()}r.value="",S.field.clearSelection(e,!1),l||(n.value=o)}},ce=e=>{navigator.clipboard.writeText(e.currentTarget.dataset.link)},ue=()=>{const e=document.querySelector("#share-modal-wrapper"),t=e.querySelector('input[name="share-modal-public"]'),n=[...e.querySelectorAll(".share-person:not(.hidden)")].reduce(((e,t)=>{const n=t.querySelector('input[name="share-modal-email"]').value,a=t.querySelector('select[name="share-modal-permission"]').value;return e[n]=a,e}),{}),a=new FormData;a.append("action","share"),a.append("data",JSON.stringify(n)),t&&a.append("public",t.checked),r(window.location.pathname,a,(t=>{const n=e.querySelector("#share-modal-message");t.error&&t.error.length?(n.querySelector("p").textContent=t.error.join(" "),n.classList.remove("hidden")):(n.classList.add("hidden"),E.hide(e))}))},me=()=>{if(e){const e=T(),t=M(),n={reselect:!!document.querySelector("#pg-chart-design"),history:!1};re(e[t],n)}};window.onload=()=>{e&&(localStorage.removeItem("index"),localStorage.removeItem("history")),re({}),S.field.initAll(O.dropdownify.field),S.button.initAll(O.dropdownify.button),q.addListeners(".modal-scrim, .modal-close-button","click",{clickHandler:E.clickHandler}),q.addListener(window,"resize",{renderChartFromHistory:me},"delay",167),q.addListeners(".file-export","click",{downloadHandler:le}),q.addListeners(".share-chart","click",{showShareModal:ie}),q.addListeners(".copy-link","click",{copyLinkToClipboard:ce}),q.addListener(document.querySelector('input[name="share-modal-search-value"]'),"change",{sharePermissionHandler:de}),q.addListener(document.querySelector("#share-modal-submit"),"click",{shareModalSubmitHandler:ue}),document.querySelector("#chart-message-close").addEventListener("click",j),q.addListener(document.querySelector("#guide-toggle-input"),"change",{guideHandler:C})}}();
//# sourceMappingURL=chart_view.min.js.map