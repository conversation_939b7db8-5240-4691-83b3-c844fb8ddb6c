!function(){"use strict";const e={},t={delay:(...t)=>{const[o,n,a,i,l,s]=t;o in e&&n in e[o]&&a in e[o][n]&&(clearTimeout(e[o][n][a].timeout),e[o][n][a].timeout=setTimeout(i,l,s))},throttle:(...t)=>{const o=(t,o,n,a,i)=>{t in e&&o in e[t]&&n in e[t][o]&&(e[t][o][n].timeout=null,a(i))},[n,a,i,l,s,r]=t;n in e&&a in e[n]&&i in e[n][a]&&!e[n][a][i].timeout&&(e[n][a][i].timeout=setTimeout(o,s,n,a,i,l,r))}},o=(()=>{const o=(o,n,a,i=null,l=0)=>{if(o){const[[s,r]]=Object.entries(a);let c=o instanceof Element?o.dataset.eventify:1;if(1!==c||c in e||(e[c]={}),!c){const t=Object.keys(e);c=t.length?Math.max(...t.map((e=>Number(e))))+1:2,o.setAttribute("data-eventify",c),e[c]={}}n in e[c]||(e[c][n]={}),e[c][n][s]=i?{timeout:null,handleEvent:t[i].bind(o,c,n,s,r,l)}:{handleEvent:r},o.addEventListener(n,e[c][n][s])}};return{addListener:o,addListeners:(e,t,n,a=null,i=0)=>{for(const l of document.querySelectorAll(e))o(l,t,n,a,i)},removeListener:(t,o,n)=>{const a=t instanceof Element?t.dataset.eventify:1,[i]=Object.keys(n);a in e&&o in e[a]&&i in e[a][o]&&(t.removeEventListener(o,e[a][o][i]),delete e[a][o][i])},garbageCollection:()=>{for(const t of Object.keys(e)){document.querySelector(`[data-eventify="${t}"]`)||delete e[t]}}}})(),n=(e=document)=>[...e.querySelectorAll('a[href], button, input, textarea, select, details,[tabindex]:not([tabindex="-1"])')].filter((e=>!e.hasAttribute("disabled"))),a={escapeCloseHandler:e=>{if("Escape"===e.key||"Esc"===e.key){const e=document.querySelector(".modal-wrapper.visible-state");e&&a.hide(e)}},hide:e=>{const t=e.querySelector(".modal-panel"),o=e.querySelector(".modal-scrim"),n=e.dataset.onhidefocus;if((()=>{for(const e of document.querySelectorAll(".modalize-aria-hidden"))e.removeAttribute("aria-hidden"),e.classList.remove("modalize-aria-hidden");for(const e of document.querySelectorAll(".modalize-tabindex")){const{tabindex:t}=e.dataset;t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex"),e.removeAttribute("data-tabindex"),e.classList.remove("modalize-tabindex")}})(),n){const e=document.querySelector(n);e?e.focus():document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus())}else document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus());t.classList.remove("visible-state","translate-y-0","sm:scale-100"),o.classList.remove("visible-state"),e.classList.remove("visible-state"),t.classList.add("translate-y-4","sm:translate-y-0","sm:scale-95"),setTimeout((()=>e.setAttribute("style","display: none;")),300),document.body.removeEventListener("keydown",a.escapeCloseHandler)},show:e=>{const t=e.querySelector(".modal-panel"),o=e.querySelector(".modal-scrim");document.activeElement&&document.activeElement!==document.body&&(document.activeElement.blur(),window.focus()),e.setAttribute("style","display: block;"),setTimeout((()=>{e.classList.add("visible-state"),o.classList.add("visible-state"),t.classList.remove("translate-y-4","sm:translate-y-0","sm:scale-95"),t.classList.add("visible-state","translate-y-0","sm:scale-100")})),document.body.addEventListener("keydown",a.escapeCloseHandler),(e=>{const t=e=>{e.getAttribute("aria-hidden")||(e.classList.add("modalize-aria-hidden"),e.setAttribute("aria-hidden","true"))},o=e=>{const t=e.getAttribute("tabindex");t&&e.setAttribute("data-tabindex",t),e.classList.add("modalize-tabindex"),e.setAttribute("tabindex","-1")};let a=e;for(;a.parentElement&&document.body!==a;){let e=a.nextElementSibling;for(;e;){t(e),o(e);for(const t of n(e))o(t);e=e.nextElementSibling}for(e=a.previousElementSibling;e;){t(e),o(e);for(const t of n(e))o(t);e=e.previousElementSibling}a=a.parentElement}})(e)},clickHandler:e=>{const t=e.currentTarget,o=document.querySelector(`#${t.dataset.modal}-modal-wrapper`);a.hide(o)}},i=(()=>{try{const e="__storage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})(),l=e=>{const t=e.querystr?`${e.url}?${e.querystr}`:e.url,o="POST"===e.method&&document.body.dataset.token||null,n=document.querySelector("#page-message");(async(e,t,o=null,n=null,a=null)=>{const i=((e,t=null,o=null)=>{const n={method:e,mode:"same-origin"};return o&&"POST"===e&&(n.headers={"X-CSRF-TOKEN":o}),t&&"POST"===e&&(n.body=t),n})(t,o,n),l=await fetch(e,i);if(404===l.status)window.location="/404";else if(401===l.status||403===l.status)window.location.reload();else if(a)if(l.ok)a.classList.contains("hidden")||a.classList.add("hidden");else{a.querySelector("p").textContent="Something went wrong, please try again",a.classList.remove("hidden")}return l.text()})(t,e.method,e.body,o,n).then((t=>{let o={};try{o=JSON.parse(t),("object"!=typeof o||null===o||Array.isArray(o))&&(o={rejected:!0})}catch(e){o.rejected=e}o.rejected?n&&(n.querySelector("p").textContent="Something went wrong, please try again",n.classList.remove("hidden")):o.redirect?window.location=o.redirect:e.callback&&e.callback(o)}))},s={authed:!1,timeout:null,pages:["pg-account","pg-account-edit","pg-account-delete","pg-account-hash","pg-account-enable-mfa","pg-account-disable-mfa","pg-account-recovery-codes","pg-chart-free","pg-chart-design","pg-chart-view","pg-home","pg-import","pg-super"]},r=()=>{const e=document.querySelector("#timeout-modal-wrapper"),t=new FormData;t.append("action","reissue"),((e="",t=new FormData,o=null)=>{const n={url:e,method:"POST",body:t,callback:o};if("/free"===e){const e=e=>{o&&o(e),i&&"chart"in e&&localStorage.setItem("chart",e.chart)};n.callback=e,i?n.body.append("chart",localStorage.getItem("chart")||"{}"):n.body.append("chart","{}")}l(n)})("/prevent-timeout-reissuer",t,(t=>{const o=e.querySelector("#timeout-modal-message");t.error&&t.error.length?(o.querySelector("p").textContent=t.error.join(" "),o.classList.remove("hidden")):(o.classList.add("hidden"),a.hide(e))}))},c=()=>{null!==s.timeout&&(clearTimeout(s.timeout),s.timeout=null),s.authed&&(s.authed=!1,window.location="/logout")},d=(e,t)=>{if(e){const o=18e5,n=168e4,i=new Date-new Date(1e3*e);if(i>o)c();else{const e=i>n?o-i:n-i;null!==s.timeout&&clearTimeout(s.timeout),s.timeout=setTimeout(t,e),s.authed||(s.authed=!0),i>n&&(()=>{const e=document.querySelector("#timeout-modal-wrapper");e&&a.show(e)})()}}else c()},u=()=>{((e="",t=null,o=null)=>{l({url:e,method:"GET",querystr:t,callback:o})})("/inactivity-timeout-poller","action=poll",(e=>{let t=null;"error"in e&&e.error.length||!e.timestamp||(t=e.timestamp),d(t,u)}))};window.onload=()=>{(e=>{s.pages.includes(e)&&(d(parseInt(document.body.dataset.timestamp,10),u),o.addListeners("#timeout-modal-scrim, #timeout-modal-submit","click",{timeoutModalHandler:r}))})(document.querySelector("main").id)}}();
//# sourceMappingURL=poller.min.js.map