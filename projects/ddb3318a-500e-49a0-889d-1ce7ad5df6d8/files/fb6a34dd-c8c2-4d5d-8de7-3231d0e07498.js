/**
 * Fuse.js v6.4.6 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2021 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */
var e,t;e=this,t=function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e){return Array.isArray?Array.isArray(e):"[object Array]"===y(e)}function u(e){return"string"==typeof e}function l(e){return"number"==typeof e}function d(t){return!0===t||!1===t||function(t){return function(t){return"object"===e(t)}(t)&&null!==t}(t)&&"[object Boolean]"==y(t)}function f(e){return null!=e}function v(e){return!e.trim().length}function y(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var g=function(e){return"Pattern length exceeds max of ".concat(e,".")},p=Object.prototype.hasOwnProperty,m=function(){function e(n){var r=this;t(this,e),this._keys=[],this._keyMap={};var i=0;n.forEach((function(e){var t=b(e);i+=t.weight,r._keys.push(t),r._keyMap[t.id]=t,i+=t.weight})),this._keys.forEach((function(e){e.weight/=i}))}return r(e,[{key:"get",value:function(e){return this._keyMap[e]}},{key:"keys",value:function(){return this._keys}},{key:"toJSON",value:function(){return JSON.stringify(this._keys)}}]),e}();function b(e){var t=null,n=null,r=null,i=1;if(u(e)||h(e))r=e,t=k(e),n=M(e);else{if(!p.call(e,"name"))throw new Error(function(e){return"Missing ".concat(e," property in key")}("name"));var o=e.name;if(r=o,p.call(e,"weight")&&(i=e.weight)<=0)throw new Error(function(e){return"Property 'weight' in key '".concat(e,"' must be a positive integer")}(o));t=k(o),n=M(o)}return{path:t,id:n,weight:i,src:r}}function k(e){return h(e)?e:e.split(".")}function M(e){return h(e)?e.join("."):e}var x=a({},{isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:function(e,t){return e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1}},{},{includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},{},{location:0,threshold:.6,distance:100},{},{useExtendedSearch:!1,getFn:function(e,t){var n=[],r=!1;return function e(t,i,o){if(f(t))if(i[o]){var a=t[i[o]];if(!f(a))return;if(o===i.length-1&&(u(a)||l(a)||d(a)))n.push(function(e){return null==e?"":function(e){if("string"==typeof e)return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(e)}(a));else if(h(a)){r=!0;for(var c=0,s=a.length;c<s;c+=1)e(a[c],i,o+1)}else i.length&&e(a,i,o+1)}else n.push(t)}(e,u(t)?t.split("."):t,0),r?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1}),w=/[^ ]+/g;function L(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3,t=new Map,n=Math.pow(10,e);return{get:function(e){var r=e.match(w).length;if(t.has(r))return t.get(r);var i=1/Math.sqrt(r),o=parseFloat(Math.round(i*n)/n);return t.set(r,o),o},clear:function(){t.clear()}}}var _=function(){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.getFn,i=void 0===r?x.getFn:r;t(this,e),this.norm=L(3),this.getFn=i,this.isCreated=!1,this.setIndexRecords()}return r(e,[{key:"setSources",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.docs=e}},{key:"setIndexRecords",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.records=e}},{key:"setKeys",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.keys=t,this._keysMap={},t.forEach((function(t,n){e._keysMap[t.id]=n}))}},{key:"create",value:function(){var e=this;!this.isCreated&&this.docs.length&&(this.isCreated=!0,u(this.docs[0])?this.docs.forEach((function(t,n){e._addString(t,n)})):this.docs.forEach((function(t,n){e._addObject(t,n)})),this.norm.clear())}},{key:"add",value:function(e){var t=this.size();u(e)?this._addString(e,t):this._addObject(e,t)}},{key:"removeAt",value:function(e){this.records.splice(e,1);for(var t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}},{key:"getValueForItemAtKeyId",value:function(e,t){return e[this._keysMap[t]]}},{key:"size",value:function(){return this.records.length}},{key:"_addString",value:function(e,t){if(f(e)&&!v(e)){var n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}}},{key:"_addObject",value:function(e,t){var n=this,r={i:t,$:{}};this.keys.forEach((function(t,i){var o=n.getFn(e,t.path);if(f(o))if(h(o))!function(){for(var e=[],t=[{nestedArrIndex:-1,value:o}];t.length;){var a=t.pop(),c=a.nestedArrIndex,s=a.value;if(f(s))if(u(s)&&!v(s)){var l={v:s,i:c,n:n.norm.get(s)};e.push(l)}else h(s)&&s.forEach((function(e,n){t.push({nestedArrIndex:n,value:e})}))}r.$[i]=e}();else if(!v(o)){var a={v:o,n:n.norm.get(o)};r.$[i]=a}})),this.records.push(r)}},{key:"toJSON",value:function(){return{keys:this.keys,records:this.records}}}]),e}();function S(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.getFn,i=void 0===r?x.getFn:r,o=new _({getFn:i});return o.setKeys(e.map(b)),o.setSources(t),o.create(),o}function O(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.errors,r=void 0===n?0:n,i=t.currentLocation,o=void 0===i?0:i,a=t.expectedLocation,c=void 0===a?0:a,s=t.distance,h=void 0===s?x.distance:s,u=t.ignoreLocation,l=void 0===u?x.ignoreLocation:u,d=r/e.length;if(l)return d;var f=Math.abs(c-o);return h?d+f/h:f?1:d}function A(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:x.minMatchCharLength,n=[],r=-1,i=-1,o=0,a=e.length;o<a;o+=1){var c=e[o];c&&-1===r?r=o:c||-1===r||((i=o-1)-r+1>=t&&n.push([r,i]),r=-1)}return e[o-1]&&o-r>=t&&n.push([r,o-1]),n}function j(e){for(var t={},n=0,r=e.length;n<r;n+=1){var i=e.charAt(n);t[i]=(t[i]||0)|1<<r-n-1}return t}var E=function(){function e(n){var r=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=i.location,a=void 0===o?x.location:o,c=i.threshold,s=void 0===c?x.threshold:c,h=i.distance,u=void 0===h?x.distance:h,l=i.includeMatches,d=void 0===l?x.includeMatches:l,f=i.findAllMatches,v=void 0===f?x.findAllMatches:f,y=i.minMatchCharLength,g=void 0===y?x.minMatchCharLength:y,p=i.isCaseSensitive,m=void 0===p?x.isCaseSensitive:p,b=i.ignoreLocation,k=void 0===b?x.ignoreLocation:b;if(t(this,e),this.options={location:a,threshold:s,distance:u,includeMatches:d,findAllMatches:v,minMatchCharLength:g,isCaseSensitive:m,ignoreLocation:k},this.pattern=m?n:n.toLowerCase(),this.chunks=[],this.pattern.length){var M=function(e,t){r.chunks.push({pattern:e,alphabet:j(e),startIndex:t})},w=this.pattern.length;if(w>32){for(var L=0,_=w%32,S=w-_;L<S;)M(this.pattern.substr(L,32),L),L+=32;if(_){var O=w-32;M(this.pattern.substr(O),O)}}else M(this.pattern,0)}}return r(e,[{key:"searchIn",value:function(e){var t=this.options,n=t.isCaseSensitive,r=t.includeMatches;if(n||(e=e.toLowerCase()),this.pattern===e){var i={isMatch:!0,score:0};return r&&(i.indices=[[0,e.length-1]]),i}var o=this.options,a=o.location,s=o.distance,h=o.threshold,u=o.findAllMatches,l=o.minMatchCharLength,d=o.ignoreLocation,f=[],v=0,y=!1;this.chunks.forEach((function(t){var n=t.pattern,i=t.alphabet,o=t.startIndex,p=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.location,o=void 0===i?x.location:i,a=r.distance,c=void 0===a?x.distance:a,s=r.threshold,h=void 0===s?x.threshold:s,u=r.findAllMatches,l=void 0===u?x.findAllMatches:u,d=r.minMatchCharLength,f=void 0===d?x.minMatchCharLength:d,v=r.includeMatches,y=void 0===v?x.includeMatches:v,p=r.ignoreLocation,m=void 0===p?x.ignoreLocation:p;if(t.length>32)throw new Error(g(32));for(var b,k=t.length,M=e.length,w=Math.max(0,Math.min(o,M)),L=h,_=w,S=f>1||y,j=S?Array(M):[];(b=e.indexOf(t,_))>-1;){var E=O(t,{currentLocation:b,expectedLocation:w,distance:c,ignoreLocation:m});if(L=Math.min(E,L),_=b+k,S)for(var I=0;I<k;)j[b+I]=1,I+=1}_=-1;for(var C=[],F=1,P=k+M,N=1<<k-1,$=0;$<k;$+=1){for(var D=0,K=P;D<K;){var z=O(t,{errors:$,currentLocation:w+K,expectedLocation:w,distance:c,ignoreLocation:m});z<=L?D=K:P=K,K=Math.floor((P-D)/2+D)}P=K;var J=Math.max(1,w-K+1),R=l?M:Math.min(w+K,M)+k,T=Array(R+2);T[R+1]=(1<<$)-1;for(var U=R;U>=J;U-=1){var q=U-1,B=n[e.charAt(q)];if(S&&(j[q]=+!!B),T[U]=(T[U+1]<<1|1)&B,$&&(T[U]|=(C[U+1]|C[U])<<1|1|C[U+1]),T[U]&N&&(F=O(t,{errors:$,currentLocation:q,expectedLocation:w,distance:c,ignoreLocation:m}))<=L){if(L=F,(_=q)<=w)break;J=Math.max(1,2*w-_)}}var V=O(t,{errors:$+1,currentLocation:w,expectedLocation:w,distance:c,ignoreLocation:m});if(V>L)break;C=T}var G={isMatch:_>=0,score:Math.max(.001,F)};if(S){var H=A(j,f);H.length?y&&(G.indices=H):G.isMatch=!1}return G}(e,n,i,{location:a+o,distance:s,threshold:h,findAllMatches:u,minMatchCharLength:l,includeMatches:r,ignoreLocation:d}),m=p.isMatch,b=p.score,k=p.indices;m&&(y=!0),v+=b,m&&k&&(f=[].concat(c(f),c(k)))}));var p={isMatch:y,score:y?v/this.chunks.length:1};return y&&r&&(p.indices=f),p}}]),e}(),I=[];function C(e,t){for(var n=0,r=I.length;n<r;n+=1){var i=I[n];if(i.condition(e,t))return new i(e,t)}return new E(e,t)}function F(e,t){var n=t.ignoreFieldNorm,r=void 0===n?x.ignoreFieldNorm:n;e.forEach((function(e){var t=1;e.matches.forEach((function(e){var n=e.key,i=e.norm,o=e.score,a=n?n.weight:null;t*=Math.pow(0===o&&a?Number.EPSILON:o,(a||1)*(r?1:i))})),e.score=t}))}function P(e,t){var n=e.matches;t.matches=[],f(n)&&n.forEach((function(e){if(f(e.indices)&&e.indices.length){var n={indices:e.indices,value:e.value};e.key&&(n.key=e.key.src),e.idx>-1&&(n.refIndex=e.idx),t.matches.push(n)}}))}function N(e,t){t.score=e.score}function $(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.includeMatches,i=void 0===r?x.includeMatches:r,o=n.includeScore,a=void 0===o?x.includeScore:o,c=[];return i&&c.push(P),a&&c.push(N),e.map((function(e){var n=e.idx,r={item:t[n],refIndex:n};return c.length&&c.forEach((function(t){t(e,r)})),r}))}var D=function(){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;if(t(this,e),this.options=a({},x,{},r),this.options.useExtendedSearch)throw new Error("Extended search is not available");this._keyStore=new m(this.options.keys),this.setCollection(n,i)}return r(e,[{key:"setCollection",value:function(e,t){if(this._docs=e,t&&!(t instanceof _))throw new Error("Incorrect 'index' type");this._myIndex=t||S(this.options.keys,this._docs,{getFn:this.options.getFn})}},{key:"add",value:function(e){f(e)&&(this._docs.push(e),this._myIndex.add(e))}},{key:"remove",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!1},t=[],n=0,r=this._docs.length;n<r;n+=1){var i=this._docs[n];e(i,n)&&(this.removeAt(n),n-=1,r-=1,t.push(i))}return t}},{key:"removeAt",value:function(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}},{key:"getIndex",value:function(){return this._myIndex}},{key:"search",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.limit,r=void 0===n?-1:n,i=this.options,o=i.includeMatches,a=i.includeScore,c=i.shouldSort,s=i.sortFn,h=i.ignoreFieldNorm,d=u(e)?u(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return F(d,{ignoreFieldNorm:h}),c&&d.sort(s),l(r)&&r>-1&&(d=d.slice(0,r)),$(d,this._docs,{includeMatches:o,includeScore:a})}},{key:"_searchStringList",value:function(e){var t=C(e,this.options),n=this._myIndex.records,r=[];return n.forEach((function(e){var n=e.v,i=e.i,o=e.n;if(f(n)){var a=t.searchIn(n),c=a.isMatch,s=a.score,h=a.indices;c&&r.push({item:n,idx:i,matches:[{score:s,value:n,norm:o,indices:h}]})}})),r}},{key:"_searchLogical",value:function(e){throw new Error("Logical search is not available")}},{key:"_searchObjectList",value:function(e){var t=this,n=C(e,this.options),r=this._myIndex,i=r.keys,o=r.records,a=[];return o.forEach((function(e){var r=e.$,o=e.i;if(f(r)){var s=[];i.forEach((function(e,i){s.push.apply(s,c(t._findMatches({key:e,value:r[i],searcher:n})))})),s.length&&a.push({idx:o,item:r,matches:s})}})),a}},{key:"_findMatches",value:function(e){var t=e.key,n=e.value,r=e.searcher;if(!f(n))return[];var i=[];if(h(n))n.forEach((function(e){var n=e.v,o=e.i,a=e.n;if(f(n)){var c=r.searchIn(n),s=c.isMatch,h=c.score,u=c.indices;s&&i.push({score:h,key:t,value:n,idx:o,norm:a,indices:u})}}));else{var o=n.v,a=n.n,c=r.searchIn(o),s=c.isMatch,u=c.score,l=c.indices;s&&i.push({score:u,key:t,value:o,norm:a,indices:l})}return i}}]),e}();return D.version="6.4.6",D.createIndex=S,D.parseIndex=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.getFn,r=void 0===n?x.getFn:n,i=e.keys,o=e.records,a=new _({getFn:r});return a.setKeys(i),a.setIndexRecords(o),a},D.config=x,D},"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).Fuse=t();