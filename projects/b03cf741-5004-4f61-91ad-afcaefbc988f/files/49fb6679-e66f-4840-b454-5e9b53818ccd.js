"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[7824],{59452:(e,t,n)=>{n.d(t,{A:()=>c});var i=n(65043),o=n(41473),r=n(61342),l=n(27999),a=n(31312),s=n(56650),d=n(70579);const c=i.memo((e=>{let{children:t,orgId:n,chartId:i}=e;return(0,d.jsx)(r.A,{children:(0,d.jsx)(l.A,{children:(0,d.jsx)(o.A,{children:(0,d.jsx)(s.A,{children:(0,d.jsx)(a.A,{orgId:n,chartId:i,children:t})})})})})}))},72614:(e,t,n)=>{n.d(t,{A:()=>je});var i=n(57528),o=n(65043),r=n(61531),l=n(5571),a=n(40454),s=n(96364),d=n(37869),c=n(14556),h=n(81635),u=n(19367),p=n(9368),m=n(70579);const x=e=>{let{member:t}=e;const{firstName:n,lastName:i,photo:o,memberPhotoColor:l}=t,a="".concat(n," ").concat(i),x=(0,c.wA)(null),g=(0,c.d4)(u.Jf),A=!(0,p.A)()||(null===g||void 0===g?void 0:g.showRoleDetails);return(0,m.jsxs)(r.A,{display:"flex",alignItems:"center",onClick:()=>(e=>{e.id&&e.roleId&&A&&x((0,h.gN)({activePersonId:e.id,selectedRoleId:e.roleId,mode:"view"}))})(t),children:[(0,m.jsx)(d.A,{person:{name:a,photo:o,memberPhotoColor:l},size:27,handle:!0}),(0,m.jsxs)(s.A,{variant:"body2",style:{marginLeft:5},children:[n," ",i]})]})};var g,A=n(72119);const v=A.Ay.div(g||(g=(0,i.A)(["\n  font-size: 10px;\n  letter-spacing: 2.5px;\n  line-height: 8px;\n  padding: 10px 16px;\n  border-radius: 4px;\n  background-color: ",";\n  text-transform: uppercase;\n  width: fit-content;\n\n"])),(e=>e.color)),f=e=>{let{label:t,color:n}=e;return(0,m.jsx)(r.A,{children:(0,m.jsx)(v,{color:n,children:t})})},y=e=>{let{boardOfDirectors:t}=e;const{chairs:n,inside:i,outside:o,observers:s}=t;return(0,m.jsx)(m.Fragment,{children:(0,m.jsxs)(r.A,{bgcolor:"white",borderRadius:6,px:7,py:3,mb:5,children:[(0,m.jsx)(r.A,{py:3,children:(0,m.jsxs)(a.A,{container:!0,spacing:4,children:[(0,m.jsx)(a.A,{item:!0,xs:3,mb:2,children:(0,m.jsx)(f,{label:"chair",color:"#D8F6F3"})}),(0,m.jsx)(a.A,{item:!0,xs:9,children:(0,m.jsx)(a.A,{container:!0,spacing:2,children:n&&n.length>0&&n.map((e=>(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(x,{member:e,name:"Jonathan Grode"})},e.id)))})})]})}),(0,m.jsx)(l.A,{}),(0,m.jsx)(r.A,{py:3,children:(0,m.jsxs)(a.A,{container:!0,spacing:4,children:[(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(f,{label:"inside directors",color:"#EEECFB"})}),(0,m.jsx)(a.A,{item:!0,xs:9,children:(0,m.jsx)(a.A,{container:!0,spacing:2,children:i&&i.length>0&&i.map((e=>(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(x,{member:e,name:"Jonathan Grode"})},e.id)))})})]})}),(0,m.jsx)(l.A,{}),(0,m.jsx)(r.A,{py:3,children:(0,m.jsxs)(a.A,{container:!0,spacing:4,children:[(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(f,{label:"outside directors",color:"#ECF3FE"})}),(0,m.jsx)(a.A,{item:!0,xs:9,children:(0,m.jsx)(a.A,{container:!0,spacing:2,children:o&&o.length>0&&o.map((e=>(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(x,{member:e,name:"Jonathan Grode"})},e.id)))})})]})}),(0,m.jsx)(l.A,{}),(0,m.jsx)(r.A,{py:3,children:(0,m.jsxs)(a.A,{container:!0,spacing:4,children:[(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(f,{label:"observers",color:"#FFF8EB"})}),(0,m.jsx)(a.A,{item:!0,xs:9,children:(0,m.jsx)(a.A,{container:!0,spacing:2,children:s&&s.length>0&&s.map((e=>(0,m.jsx)(a.A,{item:!0,xs:3,children:(0,m.jsx)(x,{member:e,name:"Jonathan Grode"})},e.id)))})})]})})]})})};var j,b,C,w,I,E,D=n(75156),S=n(24241),k=n(84);const T={chair:"#D8F6F3",inside:"#EEECFB",outside:"#E5F4FF",observer:"#FFF8EB"},R=A.Ay.div(j||(j=(0,i.A)(["\n  background-color: #FFFFFF;\n  padding:18px;\n  border-radius: 6px;\n  min-height: 280px;\n  height: 100%;\n  position: relative;\n"]))),N=A.Ay.div(b||(b=(0,i.A)(["\n  display: flex;\n"]))),F=A.Ay.div(C||(C=(0,i.A)(["\n  width: 70px;\n  height: 70px;\n  border-radius: 50%;\n  border: 2px solid #E0E0E0;\n  box-sizing: border-box;\n  display: flex;\n  justify-content: center;\n  filter: drop-shadow(0px 0px 1px rgba(61, 83, 245, 0.05)) drop-shadow(0px 1px 3px rgba(61, 83, 245, 0.1));\n  "]))),P=A.Ay.div(w||(w=(0,i.A)(["\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  align-self:center;\n  border: 2px solid #E0E0E0;\n  box-sizing: border-box;\n  filter: drop-shadow(0px 0px 1px rgba(61, 83, 245, 0.05)) drop-shadow(0px 1px 3px rgba(61, 83, 245, 0.1));\n  z-index:1;\n  "]))),O=A.Ay.div(I||(I=(0,i.A)(["\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: 10px 16px;\n  height: 27px;\n  background: ",";\n  border-radius: 4px;\n  margin: auto 0;\n  margin-left: -8px;\n"])),(e=>T[e.type])),L=A.Ay.div(E||(E=(0,i.A)(["\n  display: flex;\n  width: 100%;\n  justify-content: flex-end;\n"]))),M=e=>{let{member:t,type:n,memberCommittees:i,chartCommittees:o}=e;const{firstName:a,lastName:c,roleTitle:h,joiningDate:u,joiningDateAsYear:x,photo:g,memberPhotoColor:A,linkedIn:v}=t,f="".concat(a," ").concat(c),y=u&&S.c9.fromISO(u),j=u&&(x?y.toFormat("yyyy"):y.toLocaleString(S.c9.DATE_MED)),{openDialog:b}=(0,k.A)("editMember"),C=(0,p.A)();return(0,m.jsxs)(R,{children:[!C&&(0,m.jsx)(L,{onClick:()=>b({member:t,memberCommittees:i,chartCommittees:o}),children:(0,m.jsx)(D.Ay,{icon:"Edit",size:"xs"})}),(0,m.jsxs)(N,{children:[(0,m.jsx)(F,{children:(0,m.jsx)(P,{children:(0,m.jsx)(d.A,{person:{name:f,photo:g,memberPhotoColor:A},size:56,handle:!0})})}),(0,m.jsx)(O,{type:n,children:(0,m.jsx)(s.A,{fontSize:"10px",style:{letterSpacing:"2.5px",textTransform:"uppercase"},children:n})})]}),(0,m.jsxs)(r.A,{my:1,children:[(0,m.jsxs)(s.A,{fontSize:"16px",weight:"bold",children:[a," ",c]}),(0,m.jsx)(s.A,{fontSize:"12px",style:{wordBreak:"break-word"},children:h})]}),(0,m.jsx)(l.A,{}),(0,m.jsx)(s.A,{fontSize:"11px",children:(0,m.jsxs)("em",{children:["Member since ",j]})}),(0,m.jsx)(r.A,{mt:2,mb:3,maxWidth:"fit-content",minHeight:"50px",children:i&&i.map((e=>(0,m.jsxs)(s.A,{fontSize:"10px",color:"#757575",style:{textDecorationLine:"underline",lineHeight:"16px"},children:[" ",e.name," Committee, ",e.role," "]})))}),v&&(0,m.jsx)(r.A,{display:"flex",justifyContent:"flex-start",minHeight:"25px",position:"absolute",bottom:"10px",mt:2,children:(0,m.jsx)("a",{href:v,target:"_blank",children:(0,m.jsx)(D.Ay,{icon:"LinkedInInverse",size:"lg",color:"#9E9E9E",style:{marginRight:20}})})})]})};var z;const B=(0,A.Ay)(r.A)(z||(z=(0,i.A)(["\n  width: 216px;\n  margin-right: 11px;\n  margin-bottom: 14px;\n  filter: drop-shadow(0px 0px 1px rgba(48, 49, 51, 0.05))\n    drop-shadow(0px 1px 1px rgba(48, 49, 51, 0.05));\n  flex: none;\n  flex-grow: 0;\n"]))),Q=e=>{let{boardOfDirectors:t}=e;const{chairs:n,inside:i,outside:o,observers:l,committees:a}=t,d=(0,p.A)(),c=e=>{const t=[];return null===a||void 0===a||a.forEach((n=>{var i;const o=null===n||void 0===n||null===(i=n.members)||void 0===i?void 0:i.find((t=>t.memberId===e._id));o&&t.push({committeeId:n._id,name:n.name,role:o.role})})),t};return(0,m.jsxs)(r.A,{mb:5,children:[0===(null===n||void 0===n?void 0:n.length)&&0===(null===i||void 0===i?void 0:i.length)&&0===(null===o||void 0===o?void 0:o.length)&&0===(null===l||void 0===l?void 0:l.length)&&(0,m.jsxs)(r.A,{display:!d&&"flex",justifyContent:!d&&"space-between",p:7,textAlign:"center",children:[(0,m.jsx)(r.A,{width:60}),(0,m.jsx)(r.A,{children:(0,m.jsx)(s.A,{fontSize:"14px",children:"No Board Members have been added"})}),(0,m.jsx)(r.A,{width:160})]}),(0,m.jsxs)(r.A,{display:"flex",flexWrap:"wrap",flexDirection:"row",children:[n&&n.length>0&&n.map((e=>(0,m.jsx)(B,{children:(0,m.jsx)(M,{member:e,type:n.length>1?"co-chair":"chair",memberCommittees:c(e),chartCommittees:a})},e._id))),i&&i.length>0&&i.map((e=>(0,m.jsx)(B,{children:(0,m.jsx)(M,{member:e,type:"inside",memberCommittees:c(e),chartCommittees:a})},e._id))),o&&o.length>0&&o.map((e=>(0,m.jsx)(B,{children:(0,m.jsx)(M,{member:e,type:"outside",memberCommittees:c(e),chartCommittees:a})},e._id))),l&&l.length>0&&l.map((e=>(0,m.jsx)(B,{children:(0,m.jsx)(M,{member:e,type:"observer",memberCommittees:c(e),chartCommittees:a})},e._id)))]})]})};var H,_=n(30105),W=n(91688),G=n(21773);const q=A.Ay.div(H||(H=(0,i.A)(["\n  background-color: #ffffff;\n  padding: 22px;\n  min-height: 250px;\n  height: 100%;\n  border-radius: 6px;\n  position: relative;\n  width: 100%;\n"]))),V=e=>{let{committee:t,committeeCardHeight:n}=e;const i=t.startDate&&S.c9.fromISO(t.startDate),o=t.startDate&&i.toLocaleString(S.c9.DATE_MED),{downloadFile:l}=(0,G.A)(),a=t.endDate&&S.c9.fromISO(t.endDate),d=t.endDate&&a.toLocaleString(S.c9.DATE_MED),{openDialog:c}=(0,k.A)("editCommittee"),{orgId:h}=(0,W.useParams)(),u=(0,p.A)();return(0,m.jsxs)(q,{p:2,style:t.subCommittees.length>0?{minWidth:"max-content",height:n}:{height:n},className:"committee_card",children:[!u&&(0,m.jsx)(r.A,{display:"flex",justifyContent:"flex-end",onClick:()=>c({committee:t}),children:(0,m.jsx)(D.Ay,{icon:"Edit",size:"xs"})}),(0,m.jsxs)(r.A,{style:t.charterFile&&{marginBottom:36},children:[(0,m.jsx)(s.A,{fontSize:"14px",weight:"bold",style:{wordBreak:"break-word"},children:t.name}),(0,m.jsxs)(s.A,{fontSize:"11px",style:{lineHeight:3},children:[o," ",d?"- ".concat(d):"- Present"]}),t.members&&t.members.map((e=>(0,m.jsx)(m.Fragment,{children:"Chair"===e.role&&(0,m.jsx)(m.Fragment,{children:(0,m.jsxs)(r.A,{display:"flex",justifyContent:"flex-start",alignItems:"center",children:[(0,m.jsx)(s.A,{fontSize:"11px",weight:"medium",style:{marginRight:5,lineHeight:2.5},children:e.memberName}),(0,m.jsx)(D.Ay,{icon:"Chair",size:"lg",color:"#30A99B"})]},e._id)})}))),t.members&&t.members.map((e=>(0,m.jsx)(m.Fragment,{children:"Chair"!==e.role&&(0,m.jsx)(s.A,{fontSize:"11px",style:{lineHeight:2.5},children:e.memberName},e._id)})))]}),t.charterFile&&(0,m.jsx)(r.A,{mt:4,paddingTop:2,position:"absolute",bottom:"20px",children:(0,m.jsx)(_.A,{variant:"outlined",color:"primary",style:{borderRadius:"4px",textTransform:"capitalize"},onClick:()=>l({url:"/organizations/".concat(h,"/attachments/").concat(t.charterFile.code)}),children:(0,m.jsxs)(r.A,{display:"flex",justifyContent:"flex-start",alignItems:"center",children:[(0,m.jsx)(D.Ay,{icon:"CharterFile",size:"sm",color:"#5C2DBF",style:{marginRight:5}}),(0,m.jsx)(s.A,{fontSize:"11px",weight:"medium",color:"primary",children:"View charter"})]})})})]})};var U;const J=A.Ay.div(U||(U=(0,i.A)(["\n  background-color: #ffffff;\n  padding: 17px;\n  min-height: 250px;\n  height: 100%;\n  min-width: max-content;\n  border-radius: 6px;\n  position: relative;\n"]))),K=e=>{let{committee:t,parentCommittee:n}=e;const{downloadFile:i}=(0,G.A)(),o=t.startDate&&S.c9.fromISO(t.startDate),a=t.startDate&&o.toLocaleString(S.c9.DATE_MED),d=t.endDate&&S.c9.fromISO(t.endDate),c=t.endDate&&d.toLocaleString(S.c9.DATE_MED),{openDialog:h}=(0,k.A)("editCommittee"),{orgId:u}=(0,W.useParams)(),x=(0,p.A)();return(0,m.jsxs)(J,{p:2,children:[!x&&(0,m.jsx)(r.A,{display:"flex",justifyContent:"flex-end",onClick:()=>h({committee:t,parentCommittee:n}),children:(0,m.jsx)(D.Ay,{icon:"Edit",size:"xs"})}),(0,m.jsxs)(r.A,{style:t.charterFile&&{marginBottom:36},children:[(0,m.jsx)(s.A,{fontSize:"12px",weight:"bold",style:{wordBreak:"break-word"},children:t.name}),(0,m.jsx)(s.A,{fontSize:"12px",weight:"bold",children:"Sub-Committee"}),(0,m.jsxs)(s.A,{fontSize:"11px",style:{lineHeight:3},children:[a," ",c?"- ".concat(c):"- Present"]}),(0,m.jsx)(l.A,{}),t.members&&t.members.map((e=>(0,m.jsx)(m.Fragment,{children:"Chair"===e.role&&(0,m.jsxs)(r.A,{display:"flex",justifyContent:"flex-start",alignItems:"center",children:[(0,m.jsx)(s.A,{fontSize:"11px",weight:"medium",style:{marginRight:5,lineHeight:2.5},children:e.memberName}),(0,m.jsx)(D.Ay,{icon:"Chair",size:"lg",color:"#30A99B"})]})}))),t.members&&t.members.map((e=>(0,m.jsx)(m.Fragment,{children:"Chair"!==e.role&&(0,m.jsx)(s.A,{fontSize:"11px",style:{lineHeight:2.5},children:e.memberName})})))]}),t.charterFile&&(0,m.jsx)(r.A,{mt:4,paddingTop:2,position:"absolute",bottom:"20px",children:(0,m.jsx)(_.A,{variant:"outlined",color:"primary",style:{borderRadius:"4px",textTransform:"capitalize"},target:"_blank",rel:"noopener noreferrer",onClick:()=>i({url:"/organizations/".concat(u,"/attachments/").concat(t.charterFile.code)}),children:(0,m.jsxs)(r.A,{display:"flex",justifyContent:"flex-start",alignItems:"center",children:[(0,m.jsx)(D.Ay,{icon:"CharterFile",size:"sm",color:"#5C2DBF",style:{marginRight:5}}),(0,m.jsx)(s.A,{fontSize:"11px",weight:"medium",color:"primary",children:"View charter"})]})})})]})};var Z=n(43331),Y=n(356);const X=()=>{const e=(0,c.d4)(Z.BF),{openDialog:t}=(0,k.A)("addNewCommittee"),n=(0,p.A)();return(0,m.jsxs)(r.A,{display:!n&&"flex",pb:5,justifyContent:!n&&"space-between",textAlign:"center",children:[(0,m.jsx)(r.A,{width:60}),(0,m.jsxs)(r.A,{children:[(0,m.jsx)(s.A,{variant:"caption",children:e.name}),(0,m.jsx)(s.A,{variant:"h5",weight:"bold",children:"Board Committees"})]}),(0,m.jsx)(r.A,{children:!n&&(0,m.jsxs)(_.A,{variant:"outlined",onClick:()=>{t(),Y.A.trackEvent({eventName:"GOVERNANCE_CHART_CLICK_ADD_NEW_COMMITTEE"})},children:[(0,m.jsx)(D.Ay,{icon:"AddCircle",style:{marginRight:8,fontSize:16}}),(0,m.jsx)(s.A,{variant:"subtitle2",style:{textTransform:"capitalize"},children:"Add committee"})]})})]})};var $,ee;const te=A.Ay.div($||($=(0,i.A)(["\n  height: 30px;\n  width: 1px;\n  background-color: #E0E0E0; \n  margin: 0 auto;\n"]))),ne=(0,A.Ay)(r.A)(ee||(ee=(0,i.A)(["\n  margin-right:11px;\n  margin-bottom: 14px;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: flex-start;\n  padding: 0px;\n  gap: 10px;\n  width: 216px;\n  filter: drop-shadow(0px 0px 1px rgba(48, 49, 51, 0.05)) drop-shadow(0px 1px 1px rgba(48, 49, 51, 0.05));\n  flex: none;\n  flex-grow: 0;\n"]))),ie=e=>{let{boardOfDirectors:t}=e;const{committees:n}=t,i=Array.from(document.getElementsByClassName("committee_card")),o=i.length>0?Math.max(...i.map((e=>e.clientHeight)))+"px":"216px",l=(0,p.A)();return(0,m.jsxs)(r.A,{my:5,children:[(0,m.jsx)(X,{}),0===n.length&&(0,m.jsxs)(r.A,{display:!l&&"flex",p:5,justifyContent:!l&&"space-between",textAlign:"center",children:[(0,m.jsx)(r.A,{width:50}),(0,m.jsx)(r.A,{children:(0,m.jsx)(s.A,{fontSize:"14px",children:"No Committees have been defined"})}),(0,m.jsx)(r.A,{width:160})]}),(0,m.jsx)(r.A,{display:"flex",flexWrap:"wrap",flexDirection:"row",children:n&&n.length>0&&n.map((e=>(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(ne,{children:(0,m.jsxs)(a.A,{container:!0,direction:"column",style:e.subCommittees.length>0?{display:"block"}:{display:"flex"},children:[(0,m.jsx)(a.A,{item:!0,xs:12,children:(0,m.jsx)(V,{committee:e,committeeCardHeight:o})}),(0,m.jsx)(a.A,{item:!0,xs:12,children:(0,m.jsx)(r.A,{display:"flex",justifyContent:"center",children:e.subCommittees&&e.subCommittees.map((t=>(0,m.jsx)(m.Fragment,{children:t.name&&(0,m.jsxs)(r.A,{display:"grid",width:"200px",mr:2,children:[(0,m.jsx)(te,{}),(0,m.jsx)(K,{committee:t,parentCommittee:e})]},t.id)})))})})]})},e._id)})))})]})};var oe,re=n(18885),le=n(3359),ae=n(20495),se=n(67503),de=n(64759),ce=n(72703),he=n(59691);const ue=A.Ay.a(oe||(oe=(0,i.A)(["\n  display: flex;\n  padding-left: 8px;\n  color: #9e9e9e;\n"]))),pe=(0,A.Ay)(re.A)((e=>{let{theme:t}=e;return{"&:nth-of-type(odd)":{backgroundColor:t.palette.action.hover},"&:last-child td, &:last-child th":{border:0}}})),me=e=>{let{boardOfDirectors:t}=e;const{downloadFile:n}=(0,G.A)(),{committees:i}=t,l=[...i],[a,d]=(0,o.useState)(null),h=(0,c.d4)(Z.BF),u=(0,p.A)();(0,o.useEffect)((()=>{if(l&&l.length>0){let e=[];if(l.forEach((t=>{t.members&&t.members.length>0&&(e=e.concat(t.members))})),e.length>0){const t="memberId",n=[...new Map(e.map((e=>[e[t],e]))).values()];d(n)}}}),[i]);const x=(e,t)=>{var n;const i=null===e||void 0===e||null===(n=e.members)||void 0===n?void 0:n.find((e=>{let{memberId:n}=e;return n===t.memberId}));if(i){if("Chair"===i.role)return(0,m.jsx)(D.Ay,{icon:"Chair",size:"lg",color:"#30A99B"});if("Member"===i.role)return(0,m.jsx)(D.Ay,{icon:"CheckCircleFilled",size:"lg",color:"#30A99B"})}};return(0,m.jsxs)(r.A,{pt:5,pb:5,children:[(0,m.jsx)(X,{}),l&&0===l.length?(0,m.jsxs)(r.A,{display:!u&&"flex",p:5,justifyContent:!u&&"space-between",textAlign:"center",children:[(0,m.jsx)(r.A,{width:50}),(0,m.jsx)(r.A,{children:(0,m.jsx)(s.A,{fontSize:"14px",children:"No Committees have been defined"})}),(0,m.jsx)(r.A,{width:160})]}):(0,m.jsx)(le.A,{component:ae.A,children:(0,m.jsxs)(se.A,{children:[(0,m.jsx)(de.A,{children:(0,m.jsxs)(re.A,{children:[(0,m.jsx)(ce.A,{width:"20%",children:"Name"}),l&&l.map((e=>(0,m.jsxs)(ce.A,{style:{textAlign:"center"},children:[e.name,e.charterFile&&(0,m.jsx)(r.A,{display:"inline-flex",children:(0,m.jsx)(ue,{onClick:()=>n({url:"/organizations/".concat(h.id,"/attachments/").concat(e.charterFile.code)}),children:(0,m.jsx)(D.Ay,{icon:"ExternalLink",size:"xs"})})})]},e.id)))]})}),(0,m.jsx)(he.A,{children:a&&a.length>0&&(0,m.jsx)(m.Fragment,{children:a.map((e=>(0,m.jsxs)(pe,{children:[(0,m.jsx)(ce.A,{children:e.memberName}),l.map((t=>(0,m.jsx)(ce.A,{style:{textAlign:"center"},children:x(t,e)},t.id)))]},e.id)))})})]})})]})},xe=e=>{let{boardOfDirectors:t}=e;const n=(0,c.d4)(Z.BF),i=(0,c.d4)(u.Mk),{openDialog:o}=(0,k.A)("addNewMember"),l=(0,p.A)();return(0,m.jsxs)(r.A,{display:!l&&"flex",pb:5,justifyContent:!l&&"space-between",textAlign:"center",children:[(0,m.jsx)(r.A,{width:60}),(0,m.jsxs)(r.A,{children:[(0,m.jsx)(s.A,{variant:"caption",children:n.name}),(0,m.jsx)(s.A,{variant:"h5",weight:"bold",children:i.name})]}),(0,m.jsx)(r.A,{children:!l&&(0,m.jsxs)(_.A,{variant:"outlined",onClick:()=>(o({boardOfDirectors:t}),void Y.A.trackEvent({eventName:"GOVERNANCE_CHART_CLICK_ADD_NEW_MEMBER"})),children:[(0,m.jsx)(D.Ay,{icon:"AddUser",style:{marginRight:8,fontSize:16}}),(0,m.jsx)(s.A,{variant:"subtitle2",style:{textTransform:"capitalize"},children:"Add member"})]})})]})};var ge,Ae=n(92294),ve=n(10047),fe=n(9037);const ye=(0,A.Ay)(r.A)(ge||(ge=(0,i.A)(["\n  padding: 40px 128px 0;\n  background-color: #fcfcfc;\n  height: 100%;\n  position: relative;\n  overflow: auto;\n  width: 100%;\n"]))),je=()=>{const{viewOption:e}=(0,o.useContext)(fe.r),t=(0,c.d4)(Z.BF),n=(0,c.d4)(u.Mk),i=(0,c.d4)(Ae.R),{getGovernanceChart:r}=(0,ve.A)();return(0,o.useEffect)((()=>{!async function(){const e=t.id,i=n.id;await r(e,i)}()}),[]),(0,m.jsxs)(ye,{children:[(0,m.jsx)(xe,{}),(null===i||void 0===i?void 0:i.governanceChart)&&(0,m.jsxs)(m.Fragment,{children:["list"===e?(0,m.jsx)(y,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}):(0,m.jsx)(Q,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}),(0,m.jsx)(l.A,{}),"list"===e?(0,m.jsx)(me,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}):(0,m.jsx)(ie,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart})]})]})}},10047:(e,t,n)=>{n.d(t,{A:()=>s});var i=n(40364),o=n(14556),r=n(43862),l=n(43331),a=n(89471);const s=()=>{const e=(0,o.wA)(),t=(0,o.d4)(a.Pe);return{getGovernanceChart:async(t,n)=>await e(i.F8.getGovernanceChart({orgId:t,chartId:n})),addMemberToGovernanceChart:async(t,n,o)=>await e(i.F8.addMemberToGovernanceChart({orgId:t,chartId:n,data:o})),updateGovernanceChartObject:async(t,n,o)=>await e(i.F8.updateGovernanceChartObject({orgId:t,chartId:n,data:o})),deleteMemberWithType:async(t,n,o,r)=>await e(i.F8.deleteMemberWithType({orgId:t,chartId:n,memberId:o,memberType:r})),addNewCommittee:async(t,n,o)=>await e(i.F8.addNewCommittee({orgId:t,chartId:n,data:o})),updateCommittee:async(t,n,o)=>await e(i.F8.updateCommittee({orgId:t,chartId:n,data:o})),getAllBoardMembers:async(t,n)=>await e(i.F8.getAllBoardMembers({orgId:t,chartId:n})),deleteCommitteeCharter:async(t,n,o)=>await e(i.F8.deleteCommitteeCharter({orgId:t,chartId:n,code:o})),deleteCommittee:async(t,n,o)=>await e(i.F8.deleteCommittee({orgId:t,chartId:n,committeeId:o})),createRole:async(n,i,o)=>await e(r.h7.create({orgId:n,chartId:i,data:{...o,role:{...o.role,chart:t}}})),updateRole:async(t,n,i)=>await e(r.h7.update({orgId:t,chartId:n,data:i})),createSignedAttachmentUrl:async(t,n,i)=>await e(l.UY.createSignedAttachmentUrl({orgId:t,chartId:n,fileOptions:i}))}}},92294:(e,t,n)=>{n.d(t,{R:()=>i});const i=e=>e.governanceChart},71276:(e,t,n)=>{n.d(t,{A:()=>O});var i,o=n(65043),r=n(14556),l=n(91688),a=n(62582),s=n(57528),d=n(72119),c=n(96446),h=n(78396);const u=(0,d.Ay)(c.A)(i||(i=(0,s.A)(["\n  position: relative;\n  z-index: 1;\n  width: ","px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n  a > button {\n    width: 100%;\n  }\n  top: 0;\n  bottom: 0;\n  border-right: 1px solid #e7e7e7;\n"])),h.Ay.chart.navWidth);var p,m=n(9579),x=n(75156),g=n(82244),A=n(49092),v=n(66856),f=n(17339);const y=(0,d.Ay)(f.A).attrs({disableRipple:!0,disableFocusRipple:!0})(p||(p=(0,s.A)(["\n  ","\n"])),(e=>{let{theme:t}=e;return"\n    border-radius:0;\n    color: ".concat(t.palette.grey[600],';\n    &[active="true"], &:hover {\n        background-color: ').concat(t.palette.primary.main,";\n        color: ").concat(t.palette.common.white,";\n        border-radius:0;\n    }\n  ")}));var j=n(69219);const b=[{activeRoute:"organizationCharts",type:"link",tooltip:"Dashboard",icon:"Dashboard",name:"dashboard",separate:!0,anchorDataAttribute:"org-home-toolbar"},{activeRoute:"chartView",type:"link",tooltip:"Chart",icon:"Chart",name:"chart",disbledWithAI:!0},{activeRoute:"directoryView",type:"link",tooltip:"Directory",icon:"Directory",name:"directory",anchorDataAttribute:"directory-view-toolbar",disbledWithAI:!0},{activeRoute:"photoboardView",type:"link",tooltip:"Photoboard",icon:"Photoboard",name:"photoboard",disbledWithAI:!0},{activeRoute:"spreadsheetView",type:"link",tooltip:"Spreadsheet",icon:"Spreadsheet",name:"spreadsheet",separate:!0,disbledWithAI:!0},{activeRoute:"chartAiInsights",type:"link",tooltip:"AiInsights",icon:"AiInsights",name:"aiInsights",separate:!0},{activeRoute:"chartTheme",type:"link",tooltip:"ChartTheme",icon:"Theme",name:"theme",anchorDataAttribute:"themes-btn-toolbar",disbledWithAI:!0},{activeRoute:"directoryTheme",type:"link",tooltip:"DirectoryTheme",icon:"Theme",name:"theme",disbledWithAI:!0},{activeRoute:"photoboardTheme",type:"link",tooltip:"PhotoboardTheme",icon:"Theme",name:"theme",disbledWithAI:!0},{type:"button",tooltip:"Settings",icon:"Settings",name:"settings",disbledWithAI:!0}],C=[{activeRoute:"organizationCharts",type:"link",tooltip:"Dashboard",icon:"Dashboard",name:"dashboard",separate:!0},{activeRoute:"chartView",type:"link",tooltip:"List",icon:"ListView",name:"list",disbledWithAI:!0},{activeRoute:"chartView",type:"link",tooltip:"Grid",icon:"GridView",name:"grid",separate:!0,disbledWithAI:!0},{type:"button",tooltip:"Settings",icon:"Settings",name:"settings"}],w=[{activeRoute:"organizationCharts",type:"link",tooltip:"Dashboard",icon:"Dashboard",name:"dashboard",separate:!0},{activeRoute:"chartView",type:"link",tooltip:"Chart",icon:"Chart",name:"chart",disbledWithAI:!0},{activeRoute:"directoryView",type:"link",tooltip:"Directory",icon:"Directory",name:"directory",disbledWithAI:!0},{activeRoute:"photoboardView",type:"link",tooltip:"Photoboard",icon:"Photoboard",name:"photoboard",separate:!0,disbledWithAI:!0},{activeRoute:"chartTheme",type:"link",tooltip:"ChartTheme",icon:"Theme",name:"theme"},{activeRoute:"directoryTheme",type:"link",tooltip:"DirectoryTheme",icon:"Theme",name:"theme"},{activeRoute:"photoboardTheme",type:"link",tooltip:"PhotoboardTheme",icon:"Theme",name:"theme"},{type:"button",tooltip:"Settings",icon:"Settings",name:"settings"}];var I=n(48853);const E=e=>e.chart.integration,D=e=>{var t,n;const i=e.chart.integration;return i?null===(t=i.params)||void 0===t||null===(n=t.options)||void 0===n?void 0:n.integrationType:null};var S=n(19367),k=n(9037),T=n(356),R=n(43940),N=n(84),F=n(22440),P=n(70579);function O(){const e=(0,l.useHistory)(),{t:t}=(0,A.B)(),{isActiveRoute:n}=function(){const{pathname:e}=(0,l.useLocation)(),t=(0,o.useCallback)((()=>Object.keys(v.wZ).find((t=>{let n=v.wZ[t];if("function"===typeof n)try{return!!(0,l.matchPath)(e,{path:n().toString()||"nomatch",exact:!0})}catch(i){return!1}return!1}))),[e]);return{isActiveRoute:e=>e===t(),getActiveRoute:t}}(),i=(0,l.useParams)(),{orgId:s,resource:d,resourceAction:p}=i||{},f=(0,r.d4)(E),O=(0,r.d4)(D),{openDialog:L}=(0,N.A)("chartSettingsPlus"),M=p===h.uI.AI_INSIGHTS,{userHasMinAccess:z}=(0,I.A)(),B=!!f&&z(h.td.ADMIN),Q=(0,r.d4)(S.Mk),{onTourEventComplete:H}=(0,R.M)({}),[_]=(0,F.A)(),W=_<1441,{userType:G}=(0,I.A)(),q=(0,r.d4)(j.uo),V=Q.type===h.XD.BOARD_OF_DIRECTORS?C:Q.type===h.XD.MATRIX?w:b,U=(!G||"owner"!==G&&"admin"!==G?"editor"===G?V.filter((e=>"settings"!==e.name&&"theme"!==e.name)):"public"===q?V.filter((e=>"settings"!==e.name&&"theme"!==e.name&&"dashboard"!==e.name&&"spreadsheet"!==e.name)):V.filter((e=>"settings"!==e.name&&"theme"!==e.name&&"spreadsheet"!==e.name)):"chart"===d?V.filter((e=>"directoryTheme"!==e.activeRoute&&"photoboardTheme"!==e.activeRoute)):"directory"===d?V.filter((e=>"chartTheme"!==e.activeRoute&&"photoboardTheme"!==e.activeRoute)):V.filter((e=>"chartTheme"!==e.activeRoute&&"directoryTheme"!==e.activeRoute))).filter((e=>M||"aiInsights"!=e.name));U[U.length-1].separate=!1;const{viewOption:J,setViewOption:K}=(0,o.useContext)(k.r),Z=(e,t)=>Q.type===h.XD.BOARD_OF_DIRECTORS&&n(e)&&"chartView"===e?(J||K("list"),t===J):e&&n(e),Y=e=>{if(e&&"function"===typeof v.wZ[e])return v.wZ[e]({...i})},X=t=>()=>{"integrationConnection"===t&&e.push((0,v.ve)({orgId:s,importType:O}),{appRoute:!0}),"settings"===t&&L()},$=e=>{"directory"===d&&"theme"===e&&T.A.trackEvent({eventName:"DIRECTORY_VIEW_THEME_SETTINGS"}),"spreadsheet"===e&&T.A.trackEvent({eventName:"SPREADSHEET_VIEW"}),K("grid"===e?"grid":"list"===e?"list":"")};return(0,P.jsxs)(u,{overflow:W?"auto":"hidden",children:[U.map((e=>{const n=(e=>e.disbledWithAI&&M)(e);return(0,P.jsxs)(o.Fragment,{children:[(0,P.jsx)(m.Ay,{enterDelay:0,title:t("Toolbar.Tooltip.".concat(e.tooltip)),placement:"right",children:"link"===e.type&&(0,P.jsx)(a.N_,{"data-tour-anchor":null===e||void 0===e?void 0:e.anchorDataAttribute,onClick:n?()=>{}:()=>(null===e||void 0===e?void 0:e.anchorDataAttribute)&&H({event:"".concat(null===e||void 0===e?void 0:e.anchorDataAttribute,"--click")}),to:n?"#":t=>t?{...t,pathname:Y(e.activeRoute)}:{pathname:Y(e.activeRoute)},children:"spreadsheet"===e.name?(0,P.jsx)(y,{disabled:n,color:"inherit",active:Z(e.activeRoute,e.name).toString(),disableRipple:!0,"aria-describedby":"chart"===e.name&&"import_tooltip_1",onClick:n?()=>{}:()=>$(e.name),style:{padding:"12px 18px"},children:(0,P.jsx)(x.Ay,{color:"inherit",icon:e.icon,fontSize:"default"})}):(0,P.jsx)(y,{disabled:n,color:"inherit",active:Z(e.activeRoute,e.name).toString(),disableRipple:!0,"aria-describedby":"chart"===e.name&&"import_tooltip_1",onClick:n?()=>{}:()=>$(e.name),children:(0,P.jsx)(x.Ay,{color:"inherit",icon:e.icon,fontSize:"default"})})})||(0,P.jsx)(y,{color:"inherit",active:!0,disableRipple:!0,disabled:n,onClick:n?()=>{}:X(e.name),children:(0,P.jsx)(x.Ay,{color:"inherit",icon:e.icon,fontSize:"default"})})}),e.separate&&(0,P.jsx)(c.A,{display:"flex",justifyContent:"center",py:2,children:(0,P.jsx)(g.A,{weight:1,width:24})})]},"left-toolbar-button-".concat(e.name))})),B&&(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(c.A,{display:"flex",justifyContent:"center",py:2,children:(0,P.jsx)(g.A,{weight:1,width:24})}),(0,P.jsx)(m.Ay,{title:t("Toolbar.Tooltip.Integration"),placement:"right",children:(0,P.jsx)(y,{color:"inherit",active:!0,disableRipple:!0,onClick:X("integrationConnection"),children:(0,P.jsx)(x.Ay,{color:"inherit",icon:"IntegrationConnection",fontSize:"default"})})},"show_integation_tooltip_".concat(B))]})]})}},97337:(e,t,n)=>{n.d(t,{A:()=>cn});var i,o,r=n(57528),l=n(65043),a=n(96446),s=n(32143),d=n(17392),c=n(49092),h=n(91688),u=n(75156),p=n(70318),m=n(80539),x=n(94363),g=n(77887),A=n(695),v=n(96364),f=n(20495),y=n(72119),j=n(78396),b=n(70579);const C=y.Ay.div(i||(i=(0,r.A)(["\n  border-radius: 10px;\n  position: absolute;\n  right: 0;\n  padding-right: ","px;\n  padding-left: ","px;\n\n  width: auto;\n  max-width: 100%;\n\n  "," {\n    max-width: 200px;\n    overflow: hidden;\n  }\n  ","\n"])),(e=>{let{theme:t}=e;return t.spacing(2)}),(e=>{let{theme:t}=e;return t.spacing(2)}),g.A,(e=>{let{theme:t}=e;return"\n    z-index: ".concat(t.zIndex.modal,";\n  ")})),w=(0,y.Ay)(f.A)(o||(o=(0,r.A)(["\n  overflow: auto;\n  max-width: 100%;\n  background: #ffffff;\n"]))),I=e=>{let{children:t,appHeaderHeight:n}=e;return(0,b.jsx)(C,{style:{top:"".concat(n||j.Ay.toolbars.headerHeight,"px")},children:(0,b.jsx)(w,{children:t})})};var E=n(56070),D=n(43862),S=n(61),k=n(75687),T=n(61531),R=n(71233),N=n(64759),F=n(14370),P=n(59691),O=n(40454),L=n(38325),M=n(14556),z=n(23993),B=n(10621),Q=n(81635),H=n(24251),_=n(9579),W=n(72835),G=n(7743),q=n(61258),V=n(50330),U=n(59177),J=n(74593);const K=e=>{let{fields:t,handleSearch:n,handleClose:i,methods:o}=e;const{handleSubmit:r,register:l}=o;return(0,b.jsx)(I,{children:(0,b.jsxs)(a.A,{mx:2,children:[(0,b.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,b.jsx)(v.A,{variant:"h6",children:"Advanced Search"}),(0,b.jsx)(p.A,{size:"medium",onClick:i,children:(0,b.jsx)(u.gF,{icon:"Close",size:"lg"})})]}),(0,b.jsx)(a.A,{mt:1,mb:1,overflow:"auto",maxHeight:400,width:300,minwidth:300,maxWidth:300,children:(0,b.jsx)(q.Op,{...o,children:(0,b.jsx)("form",{id:"advancedSearchForm",onSubmit:r(n),children:t.map(((e,t)=>(0,b.jsx)(a.A,{mt:1,children:(0,b.jsx)(V.A,{model:"member",field:e,inputRef:l,name:"".concat(e.model,".").concat(e.id),label:"".concat((0,U.Sn)("member"===e.model?"Person":e.model)," ").concat((0,U.Sn)(e.label)),fullWidth:!0,autoFocus:0===t},"advancedSearchField_".concat(e.id))})))})})}),(0,b.jsx)(a.A,{mb:1,children:(0,b.jsx)(J.A,{form:"advancedSearchForm",fullWidth:!0,variant:"outlined",color:"primary",type:"submit",children:"Search"})})]})})};var Z=n(48853);const Y=["firstName","lastName","orientation"],X=["attachment"],$=e=>{let{handleClose:t,handleAdvancedSearch:n}=e;const i=(0,M.d4)(G.dS),{userHasMinAccess:o}=(0,Z.A)(),r=o(j.td.EDITOR),l=(0,q.mN)(),a=i.map((e=>(e=>!X.includes(null===e||void 0===e?void 0:e.type)&&!Y.includes(e.name))(e)?e:null)).filter((e=>e&&"location"!==e.type)).filter((e=>!!r||(null===e||void 0===e?void 0:e.searchable)));return(0,b.jsx)(K,{handleClose:t,handleSearch:e=>{let t={};for(let[n,i]of Object.entries(e)){t[n]={};for(let[e,o]of Object.entries(i))(o||null!==o&&void 0!==o&&o.length)&&(t[n][e]=o)}n(null,t)},fields:a,methods:l})};var ee=n(85865),te=n(4598),ne=n(32115),ie=n(37294);const oe=e=>{let{filterCount:t,showDepartment:n,handleClose:i,handleShowDepartment:o}=e;return(0,b.jsx)(I,{children:(0,b.jsx)(a.A,{mx:2,p:1,children:(0,b.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"baseline",children:[(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:2,justifyContent:"flex-start",children:[(0,b.jsxs)(ee.A,{variant:ne.Eq.h4,color:ie.Qs.Neutrals[900],children:["Filtered Results (",t,")"]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"row",gap:2,children:[(0,b.jsx)(te.A,{size:"small",checked:n,name:"showDepartment",onChange:()=>o()}),(0,b.jsx)(ee.A,{variant:ne.Eq.bodyMD,color:ie.Qs.Neutrals[700],children:"Include Department"})]})]}),(0,b.jsx)(a.A,{ml:1,children:(0,b.jsx)(d.A,{size:"medium",onClick:i,children:(0,b.jsx)(u.gF,{icon:"Close",size:"lg"})})})]})})})};var re=n(65025),le=n(19367),ae=n(9368),se=n(14241),de=n(94916),ce=n(79091);const he=e=>{let{expandable:t=!1}=e;const[n,i]=(0,l.useState)("default"),[o,r]=(0,l.useState)(!1),[a,s]=(0,l.useState)(!1),[d,c]=(0,de.A)("showDepartmentOnSearch",!1,!0),{query:u,advancedQuery:p,handleAdvancedQueryChange:m,handleQueryChange:x,handleQueryClear:g}=(0,re.A)();let{fieldRef:A,setFieldRef:v}=(0,H.A)(["find"]);const f=(0,M.wA)(),y=(0,M.d4)(se.s1),{resource:j}=(0,h.useParams)(),C="chart"===j,w=(0,M.d4)(se.ZH),I=(0,M.d4)(se.zk),D=()=>{g(),m({}),A.current.value=""},k=()=>{g(),A.current.value=""},R=()=>{i("default"),D()},N=()=>{k(),i("advanced")},F=(e,t)=>{g(),m(t),i("default")},P=()=>{f((0,S.F8)()),f((0,ce.cl)())};(0,l.useEffect)((()=>(C||P(),()=>{P()})),[C]);const O=!t||a?"auto":125,L=C&&("default"===n&&!!((null===u||void 0===u?void 0:u.length)>0||Object.keys(p).length)||"advanced"===n);return(0,b.jsx)(ue,{open:L,query:u,advancedQuery:p,handleClose:D,searchType:n,handleCloseAdvancedSearch:R,handleAdvancedSearchClick:N,handleAdvancedSearch:F,resource:j,showDepartmentOnSearch:d,children:(0,b.jsxs)(T.A,{"data-tour-anchor":"search-chart-toolbar",children:[(0,b.jsx)(T.A,{mx:1,children:(0,b.jsx)(_.Ay,{open:o,title:"Search anything in the chart",placement:"right",arrow:!0,disableHoverListener:!0,children:(0,b.jsx)(T.A,{width:O,children:(0,b.jsx)(E.A,{placeholder:"Search in Chart",onChange:x,inputRef:e=>{v(e)},query:u,handleClear:k,onFocus:()=>{r(!0),s(!0),setTimeout((()=>{r(!1)}),2e3)},onBlur:()=>{s(!1),r(!1)},showDropdown:!0,handleDropdownClick:N,handleDropdownCloseClick:R,isDropdownOpen:"advanced"===n,disabled:"advanced"===n})})})}),L&&"advanced"===n&&(0,b.jsx)($,{handleClose:R,handleAdvancedSearch:F}),y&&!!I&&(0,b.jsx)(oe,{filterCount:I.length,showDepartment:d,handleClose:P,handleShowDepartment:()=>{const e=!d;c(e),f((0,S.N$)(e)),f((0,S.Lt)({searchResults:e?w:I,searchResultWithDepartment:w,searchResultWithoutDepartment:I,filterChart:!0}))}})]})})},ue=e=>{var t;let{query:n,advancedQuery:i,handleClose:o,open:r,children:a,handleAdvancedSearchClick:s,searchType:d,showDepartmentOnSearch:h}=e;const{t:f}=(0,c.B)(),y=(0,M.wA)(),C=Object.keys(i).length>0,{userHasMinAccess:w}=(0,Z.A)(),E=e=>C?(0,z.yF)(e,{advancedQuery:i}):(0,z.ui)(e,{query:n}),H=(0,M.d4)(G.kA),_=(0,M.d4)(E),q=(0,M.d4)((e=>C?(0,z.CA)(e,{advancedQuery:i}):(0,z.A0)(e,{query:n}))),{rows:V,totalCount:J,page:K,rowsPerPage:Y,handleChangePage:X}=(0,k.s)({dataSelector:E,defaultValues:{rowsPerPage:10}}),$=(0,M.d4)(le.Jf),ee=(0,ae.A)(),te=!ee||(null===$||void 0===$?void 0:$.showRoleDetails),ne=!w(j.td.EDITOR)||ee,ie=null===(t=H.find((e=>"role"===e.model&&"name"===e.name&&e.isDefault)))||void 0===t?void 0:t.searchable;(0,l.useEffect)((()=>{X(null,0)}),[n]);const oe=e=>async()=>{const{role:{id:t}}=e;var n;te&&y((0,Q.gN)({activePersonId:null===(n=e.member)||void 0===n?void 0:n.id,selectedRoleId:t,mode:"view"}));await y((0,D.mO)({roleId:t})),o()},re=e=>async t=>{t&&t.stopPropagation(),y((0,S.EJ)(e)),o()},se=e=>async t=>{const{role:{id:n}}=e;var i;(t&&t.stopPropagation(),te)&&y((0,Q.gN)({activePersonId:null===(i=e.member)||void 0===i?void 0:i.id,selectedRoleId:n,mode:"view"}));y((0,D.mO)({roleId:n})),o()},de=(e,t)=>{var n;return"department"===t.type?H.find((t=>t.id===e.key))?"Department Title":"":(null===(n=H.find((t=>t.id===e.key)))||void 0===n?void 0:n.label)||""};return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(R.A,{invisible:!0,open:r,onClick:o}),a,r&&"default"===d&&(0,b.jsxs)(I,{children:[(0,b.jsxs)(T.A,{mx:2,display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,b.jsxs)(v.A,{variant:"h6",children:["Results ",V.length," of ",J]}),(0,b.jsx)(p.A,{size:"medium",onClick:o,children:(0,b.jsx)(u.Ay,{icon:"Close"})})]}),(0,b.jsx)(T.A,{overflow:"auto",maxHeight:400,width:800,minwidth:800,maxWidth:800,children:(0,b.jsxs)(x.A,{"aria-label":"dashboard",size:"small",style:{tableLayout:"auto"},children:[(0,b.jsx)(N.A,{children:(0,b.jsxs)(A.A,{children:[(0,b.jsx)(g.A,{align:"left"}),(0,b.jsx)(g.A,{align:"left",children:(0,b.jsx)(F.A,{children:"Name"})}),ne&&!ie?(0,b.jsx)(b.Fragment,{}):(0,b.jsx)(g.A,{align:"left",children:(0,b.jsx)(F.A,{children:"Title"})}),(0,b.jsx)(g.A,{align:"left",children:(0,b.jsx)(F.A,{children:"Match"})}),(0,b.jsx)(g.A,{align:"left",children:"Actions"})]})}),(0,b.jsx)(P.A,{children:V.map((e=>{const{role:t,member:n,match:i}=e;return(0,b.jsxs)(A.A,{onClick:oe(e),hover:!0,children:[(0,b.jsx)(g.A,{scope:"row",children:(0,b.jsx)(m.A,{width:30,height:30,src:null===n||void 0===n?void 0:n.photo,name:(null===n||void 0===n?void 0:n.name)||(0,B.mA)(t),overrideColor:null===n||void 0===n?void 0:n.memberPhotoColor})},"custom-fields-bodyrow-".concat(t.id,"-cell-photo")),(0,b.jsx)(g.A,{scope:"row",children:null===n||void 0===n?void 0:n.name},"custom-fields-bodyrow-".concat(t.id,"-cell-name")),ne&&!ie?(0,b.jsx)(b.Fragment,{}):(0,b.jsx)(g.A,{scope:"row",children:(0,B.mA)(t)},"custom-fields-bodyrow-".concat(t.id,"-cell-title")),(0,b.jsxs)(g.A,{scope:"row",children:[(0,b.jsx)(v.A,{variant:"overline",fontSize:"10px",children:de(i,t)}),(0,b.jsx)(v.A,{variant:"body2",children:(0,B.vK)(e[i.model][i.key])?(0,U.RP)(e[i.model][i.key],25):""})]},"custom-fields-bodyrow-".concat(t.id,"-cell-match")),(0,b.jsx)(g.A,{children:(0,b.jsxs)(T.A,{textAlign:"center",display:"flex",children:[(0,b.jsx)(T.A,{mr:1,children:(0,b.jsx)(p.A,{size:"small",onClick:se(e),children:(0,b.jsx)(u.Ay,{icon:"Target"})})}),(0,b.jsx)(p.A,{size:"small",onClick:re(e),children:(0,b.jsx)(u.Ay,{icon:"Pin"})})]})})]},"custom-fields-bodyrow-".concat(t.id))}))})]})}),(0,b.jsx)(T.A,{borderTop:"solid 1px #dddddd",children:(0,b.jsxs)(O.A,{container:!0,justifyContent:"space-between",alignItems:"center",children:[(0,b.jsx)(O.A,{item:!0,children:(0,b.jsxs)(O.A,{container:!0,children:[(0,b.jsx)(T.A,{ml:1,children:(0,b.jsx)(W.A,{color:"primary",onClick:s,children:"Advanced Search"})}),(0,b.jsx)(T.A,{ml:1,children:(0,b.jsx)(W.A,{color:"primary",onClick:()=>{o(),y((0,S.Lt)({searchResults:h?q:_,searchResultWithDepartment:q,searchResultWithoutDepartment:_,filterChart:!0}))},disabled:0===J,children:"Filter chart"})})]})}),(0,b.jsx)(O.A,{item:!0,children:(0,b.jsx)(L.A,{component:"div",count:J,rowsPerPage:Y,page:K,backIconButtonProps:{"aria-label":f("Common.Tables.backIconButtonText")},nextIconButtonProps:{"aria-label":f("Common.Tables.nextIconButtonText")},rowsPerPageOptions:[],onPageChange:X,onRowsPerPageChange:()=>{},backIconButtonText:f("Common.Tables.backIconButtonText"),nextIconButtonText:f("Common.Tables.nextIconButtonText"),labelDisplayedRows:e=>{let{from:t,to:n,count:i}=e;return"".concat(t,"-").concat(n," ").concat(f("Common.Tables.of")," ").concat(i)}})})]})})]})]})};var pe,me=n(48655),xe=n(99229),ge=n(82244);const Ae=(0,y.Ay)(me.A)(pe||(pe=(0,r.A)(['\n  background-color: #ffffff;\n  margin-top: 8px;\n  margin-bottom: 8px;\n  text-align: left;\n  .MuiInputBase-input {\n    padding: 10.5px 4px;\n  }\n  .MuiOutlinedInput-adornedEnd {\n    padding-right: 8px;\n  }\n  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {\n    padding: 4.5px 8px;\n  }\n']))),ve=()=>{let{fieldRef:e,setFieldRef:t}=(0,H.A)(["find"]);const[n,i]=(0,l.useState)(),[o,r]=(0,l.useState)(!1),[a,s]=(0,l.useState)([]),[d,c]=(0,l.useState)(0),h=o?"auto":125,p=()=>{i(null),s([]),null===a||void 0===a||a.forEach((e=>{var t;return null===e||void 0===e||null===(t=e.classList)||void 0===t?void 0:t.remove("search-highlight")})),c(0),e.current.value="",r(!1)},m=()=>{var e,t;a.forEach((e=>{var t;return null===e||void 0===e||null===(t=e.classList)||void 0===t?void 0:t.remove("search-highlight")}));const i=d===a.length-1?d:d>a.length?a.length-1:d+1;c(i),null===(e=a[i])||void 0===e||e.scrollIntoView({block:"center",behavior:"smooth"}),null===(t=a[i])||void 0===t||t.classList.add("search-highlight");const o=Array.from(document.querySelectorAll(".rg-cell:not(.rg-header-cell)")).filter((e=>{var t;return null===(t=e.innerText)||void 0===t?void 0:t.toLowerCase().includes(n)}));if(o.length>0){let e=[...a,...o].filter(((e,t,n)=>n.findIndex((t=>t.getAttribute("data-cell-colidx")===e.getAttribute("data-cell-colidx")&&t.getAttribute("data-cell-rowidx")===e.getAttribute("data-cell-rowidx")))===t));s(e)}};return(0,b.jsx)(T.A,{width:h,children:(0,b.jsx)(Ae,{variant:"outlined",placeholder:"Find",onChange:e=>{i(e.target.value);const t=Array.from(document.querySelectorAll(".rg-cell:not(.rg-header-cell)"));if(t.forEach((e=>{var t;return null===e||void 0===e||null===(t=e.classList)||void 0===t?void 0:t.remove("search-highlight")})),e.target.value){var n;const i=t.filter((t=>{var n,i;return null===(n=t.innerText)||void 0===n?void 0:n.toLowerCase().includes(null===(i=e.target.value)||void 0===i?void 0:i.toLowerCase())}));s(i),null===i||void 0===i||null===(n=i[0])||void 0===n||n.classList.add("search-highlight"),c(0)}},inputRef:e=>{t(e)},value:n,handleClear:p,onFocus:()=>{r(!0)},onKeyDown:e=>{13==e.keyCode&&(null===a||void 0===a?void 0:a.length)>0&&m()},InputProps:{startAdornment:(0,b.jsx)(b.Fragment,{children:(0,b.jsx)(xe.A,{position:"start",children:(0,b.jsx)(u.Ay,{icon:"Search"})})}),endAdornment:(0,b.jsx)(b.Fragment,{children:n&&(null===n||void 0===n?void 0:n.length)>0&&(0,b.jsxs)(b.Fragment,{children:[a.length>0&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(xe.A,{position:"end",children:[(0,b.jsxs)(v.A,{style:{opacity:.8},children:[d+1,"/",a.length]}),(0,b.jsx)(ge.A,{color:"#cccccc",weight:1,height:30,orientation:"vers",style:{marginLeft:10}})]}),(0,b.jsx)(xe.A,{position:"end",onClick:(()=>{var e,t;a.forEach((e=>{var t;return null===e||void 0===e||null===(t=e.classList)||void 0===t?void 0:t.remove("search-highlight")}));const i=0===d?d:d-1;c(i),null===(e=a[i])||void 0===e||e.scrollIntoView({block:"center",behavior:"smooth"}),null===(t=a[i])||void 0===t||t.classList.add("search-highlight");const o=Array.from(document.querySelectorAll(".rg-cell:not(.rg-header-cell)")).filter((e=>{var t;return null===(t=e.innerText)||void 0===t?void 0:t.toLowerCase().includes(n)}));if(o.length>0){let e=[...a,...o].filter(((e,t,n)=>n.findIndex((t=>t.getAttribute("data-cell-colidx")===e.getAttribute("data-cell-colidx")&&t.getAttribute("data-cell-rowidx")===e.getAttribute("data-cell-rowidx")))===t));s(e)}})||void 0,children:(0,b.jsx)(u.Ay,{icon:"Previous",size:"lg",style:{cursor:"pointer",marginLeft:10}})}),(0,b.jsx)(xe.A,{position:"end",onClick:m||void 0,children:(0,b.jsx)(u.Ay,{icon:"Next",size:"lg",style:{cursor:"pointer",marginLeft:10}})})]}),(0,b.jsx)(xe.A,{position:"end",onClick:p||void 0,children:(0,b.jsx)(u.Ay,{icon:"Close",size:"lg",style:{cursor:"pointer",marginLeft:10}})})]})})}})})};var fe,ye=n(22264),je=n(62582),be=n(66856),Ce=n(43331),we=n(42006),Ie=n(44676),Ee=n(55357),De=n(8289);const Se=(0,y.Ay)(De.A)(fe||(fe=(0,r.A)(["\n  border-radius: 5px;\n  padding: 7px;\n  .MuiTypography-root,\n  "," {\n    color: #666;\n    font-size: 13px;\n  }\n  ","\n"])),u.Ay,(e=>{let{component:t}=e;return"button"===t?"&:hover {\n    text-decoration: none;\n    color: #333333;\n    background: #eeeeee;\n  }":" &:hover {\n      text-decoration: none;\n  }"}));var ke;const Te=(0,y.Ay)(T.A)(ke||(ke=(0,r.A)(["\n  padding: 7px 2px;\n  color: #666;\n  display: flex;\n  align-items: center;\n  .MuiTypography-root {\n    color: #666;\n    font-size: 13px;\n  }\n"])));var Re;const Ne=(0,y.Ay)(T.A)(Re||(Re=(0,r.A)(["\n  @media (max-width: 1140px) {\n    visibility: hidden;\n    width: 0;\n  }\n  @media (max-width: 920px) and (min-width: 500px) {\n    visibility: visible;\n    width: auto;\n  }\n"])));var Fe=n(86597),Pe=n(47088);const Oe=e=>{let{anchorEl:t,handleClose:n}=e;const{orgId:i}=(0,h.useParams)(),o=(0,h.useHistory)(),r=e=>()=>{const t=(0,be.K7)({orgId:i,resource:e});o.push(t),n()};return(0,b.jsxs)(Ie.A,{id:"breadcrumb-resource-popover",open:!0,anchorEl:t,onClose:n,getContentAnchorEl:null,anchorOrigin:{vertical:"bottom",horizontal:"left"},children:[(0,b.jsx)(Ee.A,{onClick:r("charts"),children:"Charts"}),(0,b.jsx)(Ee.A,{onClick:r("people"),children:"People"}),(0,b.jsx)(Ee.A,{onClick:r("settings"),children:"Settings"})]})},Le=e=>{let{anchorEl:t,handleClose:n}=e;const i=(0,h.useParams)(),o=(0,h.useHistory)(),r=e=>()=>{o.push((0,be.si)({...i,resource:e})),n()};return(0,b.jsxs)(Ie.A,{id:"breadcrumb-resource-popover",open:!0,anchorEl:t,onClose:n,getContentAnchorEl:null,anchorOrigin:{vertical:"bottom",horizontal:"left"},children:[(0,b.jsx)(Ee.A,{onClick:r("chart"),children:"Chart"}),(0,b.jsx)(Ee.A,{onClick:r("photoboard"),children:"Photoboard"}),(0,b.jsx)(Ee.A,{onClick:r("directory"),children:"Directory"})]})},Me=e=>{let{anchorEl:t,handleClose:n}=e;const i=(0,h.useParams)(),{orgId:o}=i,r=(0,h.useHistory)(),l=e=>()=>{r.push((0,be.si)({...i,chartId:e.id,resourceAction:e.status===Pe.N4.Draft?j.uI.AI_INSIGHTS:j.uI.DEFAULT})),n()},a=(0,M.d4)(Ce.Ot),s=a.length<=10?10:6;return(0,b.jsxs)(Ie.A,{id:"breadcrumb-resource-popover",open:!0,anchorEl:t,onClose:n,getContentAnchorEl:null,anchorOrigin:{vertical:"bottom",horizontal:"left"},children:[a.slice(0,s).map((e=>(0,b.jsx)(Ee.A,{onClick:l(e),children:(0,U.RP)(e.name,20)},e.id))),s<a.length&&(0,b.jsx)(Ee.A,{children:(0,b.jsx)(je.N_,{to:(0,be.r2)({orgId:o}),children:"...more"})})]})},ze=e=>{let{anchorEl:t,handleClose:n}=e;const i=(0,h.useHistory)(),o=(0,Fe.A)(),r=(0,M.d4)(we.Ok),l=r.length<=10?10:6;return(0,b.jsxs)(Ie.A,{id:"breadcrumb-resource-popover",open:!0,anchorEl:t,onClose:n,getContentAnchorEl:null,anchorOrigin:{vertical:"bottom",horizontal:"left"},children:[r.slice(0,l).map((e=>{return(0,b.jsx)(Ee.A,{onClick:(t=e.id,()=>{i.push((0,be.K7)({orgId:t,resource:"charts"})),n()}),children:e.name});var t})),l<r.length&&(0,b.jsx)(Ee.A,{children:(0,b.jsx)(je.N_,{to:null!==o&&void 0!==o&&o.id?(0,be.r2)({orgId:null===o||void 0===o?void 0:o.id}):(0,be.ze)(),children:"...more"})})]})};function Be(e){let{base:t}=e;const n=(0,M.wA)(),[i,o]=(0,l.useState)(null),[r,a]=(0,l.useState)(null),{resource:s}=(0,h.useParams)(),{name:d}=(0,M.d4)(Ce.BF),{id:c,name:m}=(0,M.d4)(le.Mk),x=(0,M.d4)(z.vr),g=x?"".concat(x.roleName," "):"",A=e=>t=>{o(null===t||void 0===t?void 0:t.currentTarget),a(e)},f=()=>{a(null)},y=(0,b.jsx)(Te,{mx:1,component:"span",children:(0,b.jsx)(u.Ay,{icon:"BreadCrumbConnector",size:"lg"})});return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(Ne,{display:"flex",children:[(0,b.jsx)(Se,{component:"button",size:"small",onClick:"dashboard"!==t?A("organizations"):void 0,color:"primary",children:(0,b.jsx)(v.A,{variant:"overline",color:"primary",children:"dashboard"===t?"Dashboard":(0,U.RP)(d,20)})}),c&&(0,b.jsxs)(b.Fragment,{children:[y,(0,b.jsx)(Se,{component:"button",size:"small",color:"primary",onClick:A("charts"),children:(0,b.jsx)(v.A,{variant:"overline",color:"primary",children:(0,U.RP)(m,20)})})]}),x&&(0,b.jsxs)(b.Fragment,{children:[y,(0,b.jsx)(Se,{component:"span",size:"small",color:"primary",children:(0,b.jsxs)(T.A,{display:"flex",alignItems:"center",children:[(0,b.jsx)(v.A,{variant:"overline",color:"primary",noWrap:!0,children:(0,U.RP)(g,20)}),(0,b.jsx)(_.Ay,{title:"Remove pin",placement:"bottom",children:(0,b.jsx)(p.A,{onClick:()=>{n((0,S.nZ)())},size:"medium",children:(0,b.jsx)(u.Ay,{icon:"Close",size:"xs"})})})]})})]})||(0,b.jsxs)(b.Fragment,{children:[y,(0,b.jsx)(Se,{component:"button",size:"small",color:"primary",onClick:A("resource-".concat(t)),children:(0,b.jsx)(v.A,{variant:"overline",color:"primary",children:s})})]})]}),"resource-organizations"===r&&(0,b.jsx)(Oe,{handleClose:f,anchorEl:i}),"resource-charts"===r&&(0,b.jsx)(Le,{handleClose:f,anchorEl:i}),"charts"===r&&(0,b.jsx)(Me,{handleClose:f,anchorEl:i}),"organizations"===r&&(0,b.jsx)(ze,{handleClose:f,anchorEl:i})]})}const Qe=[{type:"button",tooltip:"HideColumn",icon:"HideColumn",name:"hideColumn",resources:["spreadsheet"],boardOfDirectors:!1},{type:"button",tooltip:"Sort",icon:"Sort",name:"sortColumn",resources:["spreadsheet"],boardOfDirectors:!1},{type:"button",tooltip:"Help",icon:"Help",name:"help",resources:["spreadsheet"],boardOfDirectors:!1,separate:!0},{type:"button",tooltip:"Group",icon:"Group",name:"groupBy",resources:["directory","photoboard"],boardOfDirectors:!1},{type:"button",tooltip:"Sort",icon:"Sort",name:"sortBy",separate:!0,resources:["directory","photoboard"],boardOfDirectors:!1},{type:"button",tooltip:"Undo",icon:"Undo",name:"undo",resources:["chart"],accessRestricted:["admin","owner"],boardOfDirectors:!1},{type:"button",tooltip:"Redo",icon:"Redo",name:"redo",separate:!0,resources:["chart"],accessRestricted:["admin","owner"],boardOfDirectors:!1},{type:"button",tooltip:"Levels",icon:"Levels",name:"levels",resources:["chart"],anchorDataAttribute:"levels-btn-toolbar",boardOfDirectors:!1},{type:"button",tooltip:"ZoomIn",icon:"ZoomIn",name:"zoomIn",resources:["chart"],anchorDataAttribute:"zoom-in-btn-toolbar",boardOfDirectors:!1},{type:"button",tooltip:"ZoomOut",icon:"ZoomOut",name:"zoomOut",resources:["chart"],boardOfDirectors:!1},{type:"button",tooltip:"FitScreen",icon:"FullScreen",name:"fitScreen",separate:!0,resources:["chart"],boardOfDirectors:!1},{type:"button",tooltip:"Import",icon:"GetApp",name:"import",anchorDataAttribute:"import-btn-toolbar",resources:["chart","directory","photoboard"],accessRestricted:["admin","owner"],boardOfDirectors:!1},{type:"button",tooltip:"Export",icon:"Publish",name:"export",separate:!0,resources:["chart","directory","photoboard"],accessRestricted:["admin","owner"],boardOfDirectors:!1},{type:"button",tooltip:"Share",icon:"Share",name:"share",anchorDataAttribute:"share-btn-toolbar",resources:["chart","directory","photoboard"],accessRestricted:["admin","owner"],boardOfDirectors:!0},{type:"button",tooltip:"Print",icon:"PrintOutlined",name:"print",separate:!0,resources:["chart","directory","photoboard"],accessRestricted:["editor","viewer","admin","owner","public"],boardOfDirectors:!1},{type:"button",tooltip:"TalentPool",icon:"TalentPool",name:"talentPool",anchorDataAttribute:"talent-pool-btn-toolbar",resources:["chart","directory","photoboard"],accessRestricted:["editor","viewer","admin","owner"],boardOfDirectors:!0},{type:"button",tooltip:"Legend",icon:"Legend",name:"legend",separate:!0,resources:["chart","directory","photoboard"],accessRestricted:["admin","owner","editor","viewer"],boardOfDirectors:!1}];var He,_e,We,Ge,qe,Ve=n(84),Ue=n(53109),Je=n(77739),Ke=n(43940);const Ze=(0,y.Ay)(a.A).attrs((e=>{let{theme:t,active:n}=e;return{borderBottom:n?"solid 4px ".concat(t.palette.secondary.main):"none"}}))(He||(He=(0,r.A)([""]))),Ye=(0,y.Ay)(a.A)(_e||(_e=(0,r.A)(["\n  @media (max-width: 920px) {\n    visibility: hidden;\n    width: 0;\n  }\n"]))),Xe=(0,y.Ay)(u.gF)(We||(We=(0,r.A)(["\n  @keyframes fadeIn {\n    0% {\n      opacity: 0.1;\n    }\n    100% {\n      opacity: 1;\n    }\n  }\n"]))),$e=(0,y.Ay)(a.A)(Ge||(Ge=(0,r.A)(["\n  @media (max-width: 920px) {\n    visibility: hidden;\n    width: 0;\n  }\n  @media (min-width: 920px) {\n    visibility: hidden;\n    width: 0;\n  }\n  @media (max-width: 920px) {\n    visibility: visible;\n    width: auto;\n  }\n"]))),et=e=>{let{name:t,active:n,separate:i,tooltipTitle:o,icon:r,handleButtonClick:l,disabled:s,anchorDataAttribute:c=null}=e;const{onTourEventComplete:h}=(0,Ke.M)({});return(0,b.jsxs)(Ye,{display:"flex",alignItems:"center",children:[(0,b.jsx)(Je.A,{title:o,placement:"bottom",arrow:!0,children:(0,b.jsx)(Ze,{active:n&&!s,children:(0,b.jsx)(d.A,{disabled:s,onClick:e=>(c&&h({event:"".concat(c,"--click")}),l(e)),name:t,color:"help"===t?"primary":void 0,style:"help"===t?{animation:"fadeIn 2s infinite linear"}:{},"data-tour-anchor":c,children:(0,b.jsx)(Xe,{size:"lg",icon:r})})})}),i&&(0,b.jsx)(a.A,{mx:2,children:(0,b.jsx)(ge.A,{color:"#cccccc",weight:1,height:j.Ay.chart.topSectionHeight-32,orientation:"vers"})})]},"toolbar-button-".concat(t))},tt=(0,y.Ay)(a.A)(qe||(qe=(0,r.A)(["\n  position: relative;\n  border-bottom: solid 1px #dddddd;\n  @media (max-width: 1140px) and (min-width: 500px) {\n    justify-content: flex-end;\n  }\n  @media (max-width: 920px) {\n    justify-content: space-between;\n  }\n"])));var nt,it=n(67264),ot=n(41020);const rt=(0,y.Ay)(ot.Ay).attrs({anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"}})(nt||(nt=(0,r.A)(["\n  .MuiPopover-paper {\n    margin-top: 8px;\n    width: ",";\n    max-width: ",";\n    max-height: "," !important;\n  }\n"])),(e=>{let{width:t}=e;return t||"auto"}),(e=>{let{maxWidth:t}=e;return t||"300px"}),"500px"),lt=e=>{let{title:t,numLevels:n,handleChangeLevelsVisible:i}=e;const o=[...Array(n).keys()];return(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",children:[(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,color:ie.Qs.Neutrals[800],children:t}),(0,b.jsx)(a.A,{display:"flex",gap:2,flexWrap:"wrap",children:o.map(((e,t)=>t!==o.length-1?(0,b.jsx)(p.A,{color:"primary",size:"small",onClick:i(e+1),children:(0,b.jsx)(ee.A,{variant:ne.Eq.bodyMD,color:ie.Qs.Violet[800],children:e+1})},"levelNumOption_".concat(e)):(0,b.jsx)(p.A,{size:"small",onClick:i("all"),color:"primary",children:(0,b.jsx)(ee.A,{variant:ne.Eq.bodyMD,color:ie.Qs.Violet[800],children:"All"})},"levelNumOption_".concat(e))))})]})};var at=n(89656),st=n(51962),dt=n(67784);const ct=e=>{let{title:t,themeChartOptions:n,compactLabel:i="compact mode",handleToggleCompact:o,handleChangeStackDirection:r,handleChangeStackColumns:l,handleChangeStackRows:d,handleChangeSharedStackDirection:c,handleChangeSharedStackColumns:h,handleChangeSharedStackRows:u}=e;return(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:2,children:[(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",mt:-1,children:[(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,color:ie.Qs.Neutrals[800],children:t}),(0,b.jsxs)(a.A,{display:"flex",alignItems:"center",mt:.5,gap:1,children:[(0,b.jsx)(st.A,{size:"small",checked:!!n.compactMode,name:"compactMode",onChange:o,sx:{padding:0}}),(0,b.jsx)(ee.A,{variant:ne.Eq.bodySM,color:ie.Qs.Neutrals[600],children:i})]})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:2,children:[(0,b.jsxs)(dt.A,{fullWidth:!0,defaultValue:n.stackDirection,label:"Lowest Level Display Direction",required:!0,select:!0,onChange:r,children:[(0,b.jsx)(s.A,{value:"horizontal",children:"Horizontal"}),(0,b.jsx)(s.A,{value:"vertical",children:"Vertical"}),(0,b.jsx)(s.A,{value:"grid",children:"Grid"}),(0,b.jsx)(s.A,{value:"cluster",children:"Cluster"})]}),["grid","cluster"].includes(n.stackDirection)&&(0,b.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,b.jsx)(a.A,{mr:1,children:(0,b.jsx)(dt.A,{type:"number",InputProps:{inputProps:{min:1}},label:"Min columns",fullWidth:!0,defaultValue:n.stackColumns,onChange:l})}),(0,b.jsx)(a.A,{ml:1,children:(0,b.jsx)(dt.A,{type:"number",InputProps:{inputProps:{min:1}},label:"Max rows",fullWidth:!0,defaultValue:n.stackRows,onChange:d})})]}),(0,b.jsxs)(dt.A,{select:!0,label:"Shared Role Display Direction",fullWidth:!0,defaultValue:n.stackDirectionShared,onChange:c,children:[(0,b.jsx)(s.A,{value:"condensed",children:"Condensed List"}),(0,b.jsx)(s.A,{value:"horizontal",children:"Horizontal"}),(0,b.jsx)(s.A,{value:"vertical",children:"Vertical"}),(0,b.jsx)(s.A,{value:"grid",children:"Grid"})]}),"grid"===n.stackDirectionShared&&(0,b.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,b.jsx)(a.A,{mr:1,children:(0,b.jsx)(dt.A,{type:"number",InputProps:{inputProps:{min:1}},label:"Min columns",fullWidth:!0,defaultValue:n.stackColumnsShared,onChange:h})}),(0,b.jsx)(a.A,{ml:1,children:(0,b.jsx)(dt.A,{type:"number",InputProps:{inputProps:{min:1}},label:"Max rows",fullWidth:!0,defaultValue:n.stackRowsShared,onChange:u})})]})]})]})};var ht=n(20252);const ut=e=>{var t,n,i;return null===(t=e.chart)||void 0===t||null===(n=t.info)||void 0===n||null===(i=n.alias)||void 0===i?void 0:i.roleId},pt=e=>{let{open:t,anchorEl:n,handleClose:i}=e;const o=(0,M.wA)(),{createStackSettingsHandler:r,copyDefaultTheme:s}=(0,ht.A)({sample:!1}),{t:d}=(0,c.B)(),h=(0,M.d4)(z.Js),u=(0,M.d4)(at.z0),[p,m]=(0,l.useState)(!1),{userHasMinAccess:x}=(0,Z.A)(),g=(0,M.d4)(ut),A=(0,M.d4)(at.QD),v=e=>{const t=r(e);return A||p?p?void 0:async e=>(e&&e.persist(),m(!0),await s(),m(!1),t(e)):t},f=()=>{},y=v("compactMode")||f,C=v("stackDirection")||f,w=v("stackColumns")||f,I=v("stackRows")||f,E=v("stackDirectionShared")||f,S=v("stackColumnsShared")||f,k=v("stackRowsShared")||f,T={open:t,anchorEl:n,onClose:()=>i(!1)};return(0,b.jsx)(rt,{...T,children:(0,b.jsxs)(a.A,{m:2,gap:2,display:"flex",flexDirection:"column",children:[(0,b.jsx)(lt,{title:d("Toolbar.Dropdowns.Levels.Title"),numLevels:h,handleChangeLevelsVisible:e=>()=>{o("all"===e?(0,D.Fc)({roleId:"root",collapsed:!1,topRoleId:g}):(0,D.Qk)({numLevels:e,topRoleId:g})),i({fitToScreen:!0})}}),x(j.td.ADMIN)&&(0,b.jsx)(ct,{title:d("Toolbar.Dropdowns.Layout.Title"),compactLabel:d("Toolbar.Dropdowns.Layout.CompactLabel"),themeChartOptions:u,handleToggleCompact:y,handleChangeStackDirection:C,handleChangeStackColumns:w,handleChangeStackRows:I,handleChangeSharedStackDirection:E,handleChangeSharedStackColumns:S,handleChangeSharedStackRows:k})]})})};var mt=n(70669),xt=n(38887);const gt=e=>{let{open:t,anchorEl:n,handleClose:i}=e;const{groupBy:o,handleGroupByChange:r}=(0,re.A)(),l={open:t,anchorEl:n,onClose:()=>i()},a=(0,M.d4)(G.Xi);let s=o?a.findIndex((e=>e.model===o.model&&e.attr===o.attr)):0;s=s>=0?s:0;return(0,b.jsx)(rt,{...l,children:(0,b.jsx)(T.A,{m:2,children:(0,b.jsx)(xt.A,{label:"Group by:",groupId:"groupby","aria-label":"groupby",handleOnChange:e=>{const t=parseInt(e.target.value);r(t>0?a[t]:null),i()},value:s.toString(),values:a.map(((e,t)=>({value:t.toString(),label:e.label})))})})})};var At=n(55005);const vt=e=>{let{open:t,anchorEl:n,handleClose:i}=e;const{orderBy:o,createSortByHandler:r}=(0,re.A)(),[l,s]=o||[],d={open:t,anchorEl:n,onClose:()=>i()},c=[{name:"",model:"member",label:"None",attr:""},{name:"",model:"role",label:"Department Role",attr:"department",isDefault:!0},{name:"",model:"role",label:"Location Role",attr:"location",isDefault:!0},...(0,M.d4)(At.A).map((e=>({...e,attr:e.id})))];let h=o?c.findIndex((e=>e.model===l&&e.attr===s)):0;h=h>=0?h:0;return(0,b.jsx)(rt,{...d,children:(0,b.jsx)(a.A,{m:2,children:(0,b.jsx)(xt.A,{label:"Sort by:",groupId:"sortby","aria-label":"sortby",handleOnChange:e=>{var t,n;const o=parseInt(null===(t=e.target)||void 0===t?void 0:t.value)>0?c[parseInt(null===(n=e.target)||void 0===n?void 0:n.value)]:null;r(null===o||void 0===o?void 0:o.attr,null===o||void 0===o?void 0:o.model)(),i()},value:h.toString(),values:c.map(((e,t)=>({value:t.toString(),label:e.label})))})})})};var ft=n(53193),yt=n(43098),jt=n(12395);const bt=e=>{var t,n,i,o;let{open:r,anchorEl:l,handleClose:s}=e;const d={open:r,anchorEl:l,onClose:()=>s()},c=(0,M.wA)(),h=(0,M.d4)(yt.mF),u=(0,M.d4)(At.A),p=(0,M.d4)(G.gJ),m=[null===(t=p[B.x2.FIRSTNAME])||void 0===t?void 0:t.id,null===(n=p[B.x2.LASTNAME])||void 0===n?void 0:n.id,null===(i=p[B.x2.EMAIL])||void 0===i?void 0:i.id,null===p||void 0===p||null===(o=p.roleName)||void 0===o?void 0:o.id],x=[...u.filter((e=>!e.isDefault||!m.includes(e.id))).map((e=>({...e,attr:e.id,checked:!1})))];return(0,b.jsx)(rt,{...d,children:(0,b.jsx)(a.A,{m:1,children:(0,b.jsxs)(ft.A,{component:"fieldset",children:[(0,b.jsx)(a.A,{m:1,children:(0,b.jsx)(ee.A,{variant:ne.Eq.h2,mb:1,children:"Hide Columns:"})}),null===x||void 0===x?void 0:x.map((e=>(0,b.jsxs)(a.A,{display:"flex",alignItems:"center",justifyContent:"flex-start",children:[(0,b.jsx)(st.A,{checked:null===h||void 0===h?void 0:h.includes(e.id),name:e.label,onChange:t=>((e,t)=>{let n;e.target.checked?(n=[...h,t.id],c((0,jt.vv)([...n]))):(n=[...h],n.splice(h.indexOf(t.id),1),c((0,jt.vv)([...n])));const i=u.filter((e=>!n.includes(e.id)));c((0,jt.G5)([...i]))})(t,e)},e.id),(0,b.jsx)(ee.A,{variant:ne.Eq.bodyMD,children:e.label})]},e.id)))]})})})};var Ct=n(3523);const wt=e=>{let{logo:t,name:n}=e;const i=j.Ay.chart.breadCrumbHeight-16;return(0,b.jsxs)(T.A,{display:"flex",alignItems:"center",children:[(0,b.jsx)(T.A,{mr:2,display:"flex",children:t&&(0,b.jsx)("img",{height:i,src:(0,Ct.K7)(t),alt:""})}),(0,b.jsx)(v.A,{children:n})]})},It=()=>{const e=(0,M.d4)(Ce.BF),t=(0,M.d4)(le.Jf),{logo:n,name:i}=e||{},o=(null===t||void 0===t?void 0:t.showLogo)||!1;return(0,b.jsx)(wt,{logo:o&&n,name:i})},Et=()=>{const e=(0,M.wA)();return{undoAction:async(t,n,i)=>{switch(null===t||void 0===t?void 0:t.eventType){case"ROLE_UPDATE":await e(D.h7.update({...t.arg,isUndo:n,isRedo:i}));break;case"ROLE_CREATE":await e(D.h7.removeRole({...t.arg,isUndo:n,isRedo:i}));break;case"ROLE_REMOVE":await e(D.h7.create({...t.arg,isUndo:n,isRedo:i}));break;case"DROP_PEOPLE":await e(D.h7.assignPeople({...t.arg,isUndo:n,isRedo:i}));break;case"ASSIGN_PEOPLE":await e(D.h7.dropPeople({...t.arg,isUndo:n,isRedo:i}));break;case"MOVE_ROLE":await e(D.h7.moveRoleInChart({...t.arg,isUndo:n,isRedo:i}))}}}},Dt=e=>{var t;const n=null===e||void 0===e||null===(t=e.history)||void 0===t?void 0:t.undo;return n[n.length-1]},St=e=>{var t;const n=null===e||void 0===e||null===(t=e.history)||void 0===t?void 0:t.redo;return n[n.length-1]};var kt=n(36735),Tt=n(83462),Rt=n(4219),Nt=n(35316),Ft=n(42518),Pt=n(24115),Ot=n(98433),Lt=n(57546),Mt=n(21773),zt=n(63336),Bt=n(79650),Qt=n(71806),Ht=n(73460),_t=n(28076),Wt=n(39652),Gt=n(6803),qt=n(10611),Vt=n(67202),Ut=n(15813),Jt=n(82072),Kt=n(64418),Zt=n(2173);const Yt=e=>{let{printJob:t,pId:n,handleDownload:i,handleCompleted:o}=e;const r=(0,M.wA)(),[s,d]=(0,l.useState)(null),{chartId:c,orgId:p}=(0,h.useParams)(),[m,x]=(0,l.useState)(null),{confirmAction:g}=(0,Kt.A)(),[A,v]=(0,Zt.A)((()=>i(t.id))),f=(0,l.useRef)(),y=(0,l.useRef)(0),[j,C]=(0,l.useState)("Connecting to printer..."),[w,I]=(0,Zt.A)((async()=>{g({title:"Regenerate Print",message:"Are you sure you want to regenerate this print?",execFunc:async()=>{const{error:e,payload:i}=await r(Ot.kv.addPrintJobPublic({orgId:p,chartId:c,publicId:n,historyId:t.id,printTemplate:t.jobDetails.publicPrintTemplateId,isPublicPrint:!0}));e||x(null===i||void 0===i?void 0:i.currentRequested)}})})),E=async()=>{C("Getting file download link..."),o(),x(null)},D=e=>{clearInterval(f.current),y.current=0,r(Ot.kv.cancelPrintJob({orgId:p,chartId:c,jobId:t.id}));let n="";n=e===Jt.M.CONNECTION?"Failed to connect to printer, try again or contact support":"Could not complete export, try again or contact support",C(n)};return(0,l.useEffect)((()=>(null!==m&&void 0!==m&&m.id&&(f.current=setInterval((()=>{(async()=>{const{error:e,payload:t}=await r(Ot.R0.getJobStatus({jobId:m.id}));if(e)return void console.log(e);const{data:{jobStatus:n,status:i}}=t;i===Ut.A8.FAILED?D(Jt.M.UNKNOWN):i===Ut.A8.DEQUEUED&&y.current<1800?(y.current++,C(n||n.curPage?"File almost ready for download...":"Generating page "+Math.min(n.curPage,n.numPages)+" of "+n.numPages+"...")):i!==Ut.A8.DEQUEUED&&i!==Ut.A8.COMPLETE?(y.current++,y.current<60?C(n||n.curPage?"Connecting to secure channel...":"Generating Page "+Math.min(n.curPage,n.numPages)+" of "+n.numPages+"..."):D(Jt.M.CONNECTION)):i===Ut.A8.COMPLETE?(clearInterval(f.current),setTimeout((async()=>{await E()}),5e3),console.log(E)):y.current>=1800&&D(Jt.M.UNKNOWN)})()}),1500)),()=>{clearInterval(f.current)})),[m]),(0,b.jsxs)(a.A,{display:"flex",gap:1,width:"100%",children:[(0,b.jsxs)(zt.A,{variant:"outlined",sx:{padding:"10px 8px 10px 16px",width:"100%"},children:[(0,b.jsxs)(a.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",gap:1,children:[(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,textOverflow:"ellipsis",noWrap:!0,children:t.fileInfo.name}),(0,b.jsx)(a.A,{onMouseEnter:e=>d(e.currentTarget),onMouseLeave:()=>d(null),display:"flex",alignItems:"center",justifyContent:"center",marginRight:1,children:(0,b.jsx)(u.gF,{icon:"InfoSolid",size:"sm",color:ie.Qs.Neutrals[500]})}),(0,b.jsx)(Vt.lR,{sx:{pointerEvents:"none",marginLeft:"4px"},open:Boolean(s),anchorEl:s,onClose:()=>d(null),anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"left"},children:(0,b.jsx)(Bt.A,{component:a.A,p:2,paddingTop:1,border:"1px solid ".concat(ie.Qs.Neutrals[400]),borderRadius:"4px",display:"flex",flexDirection:"column",gap:3,children:(0,b.jsx)(Qt.A,{padding:"none",children:(0,b.jsxs)(Ht.A,{children:[(0,b.jsxs)(_t.A,{children:[(0,b.jsx)(Wt.A,{sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200],paddingRight:1},children:(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,children:"File:"})}),(0,b.jsx)(Wt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200]},children:(0,b.jsx)(ee.A,{variant:ne.Eq.bodySM,children:(e=>{switch(e){case"application/pdf":return"PDF";case"image/png":return"Image";case"application/zip":return"ZIP";case"application/ppt":return"PPT"}})(t.fileInfo.type)})})]}),(0,b.jsxs)(_t.A,{children:[(0,b.jsx)(Wt.A,{sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200],paddingRight:1},children:(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,children:"Pages:"})}),(0,b.jsx)(Wt.A,{align:"right",sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200]},children:(0,b.jsx)(ee.A,{variant:ne.Eq.bodySM,children:t.jobDetails.pageCount.toString()})})]}),(0,b.jsxs)(_t.A,{children:[(0,b.jsx)(Wt.A,{sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200],paddingRight:1},children:(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,children:"Break by:"})}),(0,b.jsx)(Wt.A,{align:"right",sx:{paddingY:.5,borderColor:ie.Qs.Neutrals[200]},children:(0,b.jsx)(ee.A,{variant:ne.Eq.bodySM,children:(0,Gt.A)(t.jobDetails.multiPageBreakBy)})})]})]})})})})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:1,mt:.5,children:[(0,b.jsx)(ee.A,{variant:ne.Eq.caption,color:ie.Qs.Neutrals[600],children:"".concat(new Date(t.jobDetails.enqueued_at).toLocaleTimeString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"}))}),m&&(0,b.jsxs)(a.A,{children:[(0,b.jsx)(qt.A,{sx:{marginY:"6px",marginRight:"8px"}}),(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingXS,color:ie.Qs.Neutrals[600],children:j})]}),(0,b.jsx)(ee.A,{variant:ne.Eq.subheadingSM,color:ie.Qs.Neutrals[600],marginTop:1,children:"Template: ".concat(t.jobDetails.publicPrintTemplateName)})]})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,b.jsxs)(Ft.A,{variant:"contained",onClick:v,disabled:!!A||!!w||!!m,children:[(0,b.jsx)(u.gF,{icon:"DownloadOutline",size:"lg"}),"Download"]}),(0,b.jsx)(Je.A,{title:t.allowRegenerate?"Refresh print":"Next refresh available in ".concat(Math.ceil(t.timeRemainingToRegenerate.timeDifference)," ").concat(t.timeRemainingToRegenerate.unit),arrow:!0,placement:"right",children:(0,b.jsxs)(Ft.A,{variant:"outlined",onClick:I,disabled:!!A||!!w||!!m||!t.allowRegenerate,children:[(0,b.jsx)(u.gF,{icon:"Redo",size:"lg"}),"Regenerate"]})})]})]})},Xt=e=>{let{open:t,handleClose:n}=e;const{chartId:i,orgId:o}=(0,h.useParams)(),r=(0,Lt.A)().pId,s=(0,M.wA)(),{downloadFile:c}=(0,Mt.A)(),[p,m]=(0,l.useState)(!0),[x,g]=(0,l.useState)([]);async function A(){var e;const{payload:t,error:n}=await s(Ot.kv.getPublicPrintFiles({chartId:i,orgId:o,pId:r}));if(m(!1),n)return;const l=(null===(e=(null===t||void 0===t?void 0:t.printHistory)||[])||void 0===e?void 0:e.filter((e=>{var t,n;return"chart"===(null===e||void 0===e||null===(t=e.jobDetails)||void 0===t||null===(n=t.setup)||void 0===n?void 0:n.resource)})))||[];g(l)}(0,l.useEffect)((()=>{A()}),[]);const v=async e=>{const t="/organizations/".concat(o,"/charts/").concat(i,"/exports/public/").concat(r,"/history/").concat(e,"/download");await c({url:t,fileName:void 0,fileType:void 0,onError:void 0,isPublicDownload:void 0})},[f,y]=(0,Zt.A)((async()=>{await A()}));return(0,b.jsxs)(Tt.A,{open:t,onClose:n,fullWidth:!0,maxWidth:"sm",children:[(0,b.jsxs)(Rt.A,{children:[(0,b.jsx)(ee.A,{variant:ne.Eq.h1,color:"inherit",children:"Download prints"}),(0,b.jsx)(d.A,{onClick:n,children:(0,b.jsx)(u.gF,{icon:"Close",size:"sm"})})]}),(0,b.jsx)(Nt.A,{sx:{backgroundColor:ie.Qs.Neutrals[200]},children:(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gap:3,justifyContent:"center",alignItems:"center",mt:4,children:[(0,b.jsx)(Pt.A,{loading:p||!!f,children:(null===x||void 0===x?void 0:x.length)>0?x.map(((e,t)=>(0,b.jsx)(a.A,{width:"100%",children:(0,b.jsx)(Yt,{printJob:e,pId:r,handleDownload:v,handleCompleted:y})},t))):(0,b.jsx)(ee.A,{variant:ne.Eq.bodyMD,children:"Prints will appear here when they are available. If nothing appears within 15 minutes, please contact the chart owner."})}),(0,b.jsx)(Ft.A,{onClick:n,color:"primary",variant:"outlined",children:"Close"})]})})]})};var $t=n(30105),en=n(45904),tn=n(66588),nn=n(72220);const on=()=>{const e=(0,M.wA)(),t=(0,h.useParams)(),{refreshDynamicFieldValues:n,name:i}=(0,M.d4)(le.Mk),o=(0,M.d4)(nn.sh),r=(0,l.useRef)(!0),{show:a}=(0,tn.A)(),{orgId:s,chartId:d}=t||{},[c,u]=(0,l.useState)(!1),p=async()=>{u(!0);const{error:t}=await e(Ue.wz.refreshDynamicFieldValues({orgId:s,chartId:d}));if(t)return a("Error occurred during custom fields computation.","error",3e3,{vertical:"top",horizontal:"center"}),void u(!1);await e(Ue.te.refreshRoles({orgId:s,chartId:d})),a("Custom fields values refreshed.","success",3e3,{vertical:"top",horizontal:"center"}),u(!1)};return(0,l.useEffect)((()=>{o&&i&&n&&r.current&&(p(),r.current=!1)}),[i,n,o]),(0,b.jsx)(b.Fragment,{children:o&&n&&(0,b.jsx)(T.A,{mx:3,children:(0,b.jsxs)($t.A,{variant:"outlined",style:{color:"red",borderColor:"red"},onClick:p,disabled:c,children:["Refresh",c&&(0,b.jsx)(T.A,{display:"flex",ml:2,children:(0,b.jsx)(en.A,{color:"red",size:20})})]})})})};var rn,ln,an=n(356);const sn=(0,y.Ay)(a.A)(rn||(rn=(0,r.A)(["\n  position: fixed;\n  bottom: 0;\n  display: flex;\n  z-index: 999;\n  padding: 12px;\n  width: 100%;\n  justify-content: center;\n  align-items: center;\n  background: #ffffff;\n  left: 0;\n  right: 0;\n  grid-gap: 12px;\n  font-size: 16px;\n  color: #303030;\n  font-weight: 500;\n"]))),dn=y.Ay.a(ln||(ln=(0,r.A)(["\n  background: #5c2dbf;\n  color: #fff;\n  padding: 4px 14px;\n  border-radius: 4px;\n"]))),cn=()=>{const e=(0,M.wA)(),{t:t}=(0,c.B)(),{chartContainerRef:n}=(0,ye.A)(),i=(0,h.useParams)(),o=(0,Lt.A)(),r="1"===o.promote||"true"===o.promote,[p,m]=(0,l.useState)(null),[x,g]=(0,l.useState)(null),[A,f]=(0,l.useState)(null),[y,C]=(0,l.useState)(!1),{orgId:w,chartId:I,resource:E,resourceAction:D}=i||{},S=D===j.uI.AI_INSIGHTS,k=(0,M.d4)(mt.Q$),[T,R]=(0,de.A)("showLegend",!1,!0),N=(0,M.d4)(ce.tO),F=(0,M.d4)(le.Zx),{userHasMinAccess:P,userType:O}=(0,Z.A)(),L=(0,M.d4)(le.Jf),{name:z,type:B,templateId:H}=(0,M.d4)(le.Mk),_=(0,M.d4)(Dt),W=(0,M.d4)(St),G=(0,M.d4)(kt.A),q=(0,ae.A)(),{open:V}=(0,Ve.A)("export2Dialog"),{open:U,openDialog:J}=(0,Ve.A)("sortColumn"),{openDialog:K}=(0,Ve.A)("spreadsheetHelp"),{open:Y,openDialog:X}=(0,Ve.A)("shareChartDialog"),{undoAction:$}=Et(),ee=Boolean(p),te=()=>{G===j.uI.PUBLIC&&C(!0)};let ne={undo:()=>{$(_,!0,!1)},redo:()=>{$(W,!1,!0)},levels:e=>{f("levels"),g(e.currentTarget)},groupBy:e=>{f("groupBy"),g(e.currentTarget)},sortBy:e=>{f("sortBy"),g(e.currentTarget)},fitScreen:()=>{const t=n.current;if(t){const{offsetWidth:n,offsetHeight:i}=t,o=j.Ay.chart.padding,r=document.getElementById("chartSvg");if(r){const{width:t,height:l}=r.getBBox(),a=Math.min(n/(t+2*o),1),s=Math.min(i/(l+2*o),1),d=Math.min(a,s);e((0,Ue.pW)(d))}}},share:()=>{X({orgId:w,chartId:I,params:i})},legend:()=>{"chart"===E?R(!T):e(N?(0,ce.Qs)():(0,ce.wt)())},talentPool:()=>{k?e((0,mt.zi)()):(e((0,Q.Ch)()),e((0,ce.Qs)()),e((0,mt.tg)()))},hideColumn:e=>{f("hideColumns"),g(e.currentTarget)},sortColumn:()=>{J()},help:()=>{K()}};G===j.uI.PUBLIC&&(ne={...ne,print:te});const{handleButtonActionClick:ie}=(0,it.A)({...ne}),oe=e=>{f(null),g(null),null!==e&&void 0!==e&&e.fitToScreen&&ne.fitScreen()},re=e=>{if(!L)return!0;switch(e){case"export":return L.export;case"print":return L.print;case"legend":return G!==j.uI.PUBLIC;case"search":return L.search;default:return!0}},se=e=>{switch(e){case"undo":return!_;case"redo":return!W;case"import":case"export":case"print":case"share":case"legend":case"talentPool":return S}},ue=P(j.td.VIEWER)||q&&re("search"),pe=Qe.filter((e=>{let{resources:t,accessRestricted:n,name:i,boardOfDirectors:o}=e;const r=!n||n.includes(O),l=P(j.td.ADMIN)||re(i),a=(e=>{var t;return"legend"!==e||O!==j.td.PUBLIC||(null===F||void 0===F?void 0:F.visible)&&(null===F||void 0===F||null===(t=F.rules)||void 0===t?void 0:t.length)})(i),s="chart"!==E||"board of directors"!==B||o;return a&&r&&l&&(!(null!==t&&void 0!==t&&t.length)||t.includes(E))&&s})),me="public"===O,xe="public"!==O,ge=e=>{switch(e){case"talentPool":return k;case"legend":return"chart"===E?T:N;case"levels":return"levels"===A;case"hideColumn":return"hideColumns"===A;case"sortColumn":return U;case"groupBy":return"groupBy"===A;case"sortBy":return"sortBy"===A;case"share":case"help":return Y;case"export":return V;default:return!1}},Ae=pe.map((e=>{return(0,l.createElement)(et,{...e,key:"visibleToolbarButton".concat(e.name),active:ge(e.name),tooltipTitle:t("Toolbar.Tooltip.".concat(e.tooltip)),handleButtonClick:ie(e.name),disabled:se(e.name),anchorDataAttribute:null===e||void 0===e?void 0:e.anchorDataAttribute,separate:(n=e,"print"===n.name&&re("print")?ue:"fitScreen"===n.name?re("print")||ue:n.separate)});var n})),fe=pe.map((e=>(0,b.jsxs)(s.A,{onClick:ie(e.name),disabled:se(e.name),children:[(0,b.jsx)(d.A,{children:(0,b.jsx)(u.Ay,{icon:e.icon})}),(0,b.jsx)(v.A,{children:t("Toolbar.Tooltip.".concat(e.tooltip))})]},"mobileToolbarButton".concat(e.name)))),je=(0,b.jsx)(Ie.A,{anchorEl:p,anchorOrigin:{vertical:"top",horizontal:"right"},id:"toolbarTopMenu",keepMounted:!0,transformOrigin:{vertical:"top",horizontal:"right"},open:ee,onClose:()=>{m(null)},children:fe});let be=me&&(null===L||void 0===L?void 0:L.showOrgName),Ce=!xe&&(null===L||void 0===L?void 0:L.showChartName);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(tt,{px:2,display:"flex",justifyContent:"space-between",alignItems:"center",height:j.Ay.chart.topSectionHeight,children:[xe&&(0,b.jsx)(Be,{base:"charts"}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"row",alignItems:"center",alignContent:"center",justifyContent:"space-between",children:[be&&(0,b.jsx)(It,{}),Ce&&be&&(0,b.jsx)(a.A,{ml:1,mr:1,children:(0,b.jsx)(v.A,{children:"/ "})}),Ce&&(0,b.jsx)(a.A,{display:"flex",alignItems:"center",children:(0,b.jsx)(v.A,{children:"".concat(z)})})]}),H&&"chart"===E&&r&&(0,b.jsxs)(sn,{children:[(0,b.jsx)("div",{children:"This chart originates from an Organimi template!"}),(0,b.jsx)(dn,{target:"_blank",rel:"noreferrer",onClick:()=>{an.A.trackEvent({eventName:"TRY_TEMPLATE_CLICKED",extraParams:{templateId:H,chartName:z,source:"wiki-chart"}})},href:"".concat("https://app.organimi.com","/?utilizeTemplateTour=").concat(H),children:"Try this template"})]}),(0,b.jsxs)(a.A,{display:"flex",alignItems:"center",gap:1,children:[(0,b.jsx)(on,{}),Ae,(0,b.jsx)($e,{children:(0,b.jsx)(d.A,{onClick:e=>{m(e.currentTarget)},children:(0,b.jsx)(u.Ay,{icon:"EllipsesV",size:"xs"})})}),ue&&("spreadsheet"===E?(0,b.jsx)(ve,{expandable:!0}):(0,b.jsx)(he,{expandable:!0}))]})]}),je,(0,b.jsx)(bt,{open:"hideColumns"===A,anchorEl:x,handleClose:oe}),(0,b.jsx)(pt,{open:"levels"===A,anchorEl:x,handleClose:oe}),(0,b.jsx)(gt,{open:"groupBy"===A,anchorEl:x,handleClose:oe}),(0,b.jsx)(vt,{open:"sortBy"===A,anchorEl:x,handleClose:oe}),y&&(0,b.jsx)(Xt,{open:y,handleClose:()=>{G===j.uI.PUBLIC&&C(!1)}})]})}},97631:(e,t,n)=>{n.d(t,{g:()=>c});n(65043);var i=n(20965),o=n(96446),r=n(85865),l=n(42518);const a=n.p+"static/media/create-your-first-legend.c94ed726c4b4edbec5a77117500330b0.svg";var s=n(32115),d=n(70579);const c=e=>{let{handleCreateLegendClick:t,title:n,description:c}=e;return(0,d.jsx)(o.A,{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",children:(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",maxWidth:(0,i.A)(400),gap:(0,i.A)(30),children:[(0,d.jsx)("img",{src:a,alt:"Empty state"}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",gap:(0,i.A)(16),justifyContent:"center",alignItems:"center",children:[(0,d.jsx)(r.A,{variant:s.Eq.h4,textAlign:"center",children:n}),c&&(0,d.jsx)(r.A,{variant:s.Eq.bodyMD,textAlign:"center",children:c})]}),(0,d.jsx)(l.A,{variant:"contained",color:"primary",size:"medium",onClick:t,children:"New legend item"})]})})}},89119:(e,t,n)=>{n.d(t,{_m:()=>X,Se:()=>Z,L5:()=>I,VV:()=>R,Q6:()=>ae});var i=n(65043),o=n(34535),r=n(96446),l=n(63336),a=n(30279),s=n(85865),d=n(20965),c=n(98022),h=n(37294),u=n(14556),p=n(79091),m=n(7866),x=n(78396),g=n(69219),A=n(75156),v=n(32115),f=n(98621),y=n(98433),j=n(94916),b=n(61),C=n(70579);const w=(0,o.Ay)(r.A)({minWidth:(0,d.A)(193),maxWidth:(0,d.A)(200),display:"flex",flexDirection:"column",borderRadius:(0,d.A)(5),border:"1px ".concat(h.Qs.Neutrals[300]," solid"),backgroundColor:h.Qs.Neutrals[0],cursor:"default",transition:"padding 0.3s ease-in-out, gap 0.3s ease-in-out"}),I=e=>{let{handleManageLegendsClick:t,disableAllActions:n=!1,legendItems:o=[]}=e;const I=(0,i.useMemo)((()=>(0,f.C)(o,"")),[o]),[E,D]=(0,i.useState)(!1),S=(0,u.wA)(),[k]=(0,j.A)("showDepartmentOnSearch",!1,!0),T=(0,u.d4)(m.Ru),N=(0,u.d4)(g.uo),F=[x.uI.PRINT,x.uI.PRINT_PREVIEW].includes(N),P=(0,u.d4)(y.DD);return(0,C.jsxs)(w,{component:l.A,variant:c.vR.low,paddingBottom:E?0:n?(0,d.A)(8):(0,d.A)(16),gap:E?0:(0,d.A)(16),maxHeight:n?"100%":(0,d.A)(390),...F&&{boxShadow:"none"},...F&&P.ownPage&&{width:"100%",height:"100%",maxWidth:"100% !important"},children:[(0,C.jsx)(R,{legendCollapsed:E,handleToggleCollapse:e=>{null===e||void 0===e||e.stopPropagation(),null===e||void 0===e||e.preventDefault(),D((e=>!e))},onManageLegendsClick:t}),(0,C.jsx)(a.A,{in:!E||n,component:r.A,sx:{padding:"0 ".concat((0,d.A)(16)),overflowY:"auto",flex:"1 1 auto"},children:null!==o&&void 0!==o&&o.length?(0,C.jsx)(X,{legends:I,mode:Z.Display,handleAddLegendItemToFilter:async e=>{S((0,p.l)([...T,e])),S((0,b.N$)(k))},handleRemoveLegendItemFromFilter:async e=>{S((0,p.l)(T.filter((t=>t!==e)))),S((0,b.N$)(k))},selectedLegendItems:T,disableAllActions:n}):(0,C.jsxs)(r.A,{display:"grid",gap:1,width:"100%",textAlign:"center",children:[(0,C.jsx)("span",{children:(0,C.jsx)(A.Ay,{icon:"Legend",size:"lg",color:h.Qs.Neutrals[600]})}),(0,C.jsxs)(s.A,{variant:v.Eq.tabSM,color:h.Qs.Neutrals[600],children:[" ","Legend is empty"]})]})})]})};var E=n(42518),D=n(48853),S=n(59177),k=n(89656);const T=(0,o.Ay)(r.A)({display:"flex",flexDirection:"row",justifyContent:"space-between",padding:"".concat((0,d.A)(8)," ").concat((0,d.A)(16)),alignItems:"center",backgroundColor:h.Qs.Neutrals[200]}),R=e=>{let{legendCollapsed:t,handleToggleCollapse:n,onManageLegendsClick:i}=e;const{userHasMinAccess:o}=(0,D.A)(),l=o(x.td.ADMIN),a=(0,u.d4)(g.uo),c=(0,u.d4)(k.y$),p=l&&a===x.uI.DEFAULT,m=[x.uI.PRINT,x.uI.PRINT_PREVIEW].includes(a),f=null!==c&&void 0!==c&&c.title?p?(0,S.RP)(null===c||void 0===c?void 0:c.title,10):(0,S.RP)(null===c||void 0===c?void 0:c.title,30):"LEGEND";return(0,C.jsxs)(T,{children:[(0,C.jsxs)(r.A,{display:"flex",gap:(0,d.A)(8),alignItems:"center",children:[(0,C.jsx)(s.A,{variant:v.Eq.tabSM,color:h.Qs.Neutrals[600],children:f}),p&&(0,C.jsx)(E.A,{variant:"outlined",size:"xsmall",color:"primary",onClick:i,children:"Manage"})]}),m?null:t?(0,C.jsx)(s.A,{variant:v.Eq.tabSM,color:h.Qs.Neutrals[600],onClick:n,display:"contents",children:(0,C.jsx)(A.gF,{icon:"FilterExpandRegular",size:"xs"})},"legend-display-header-expand-".concat(t)):(0,C.jsx)(s.A,{variant:v.Eq.tabSM,color:h.Qs.Neutrals[600],onClick:n,display:"contents",children:(0,C.jsx)(A.gF,{icon:"MinusSquare",size:"xs"})},"legend-display-header-minus-".concat(t))]})};var N=n(57528),F=n(39336),P=n(16528);const O=(0,o.Ay)(r.A)((e=>{let{selectedToFilter:t,selectedToEdit:n,disableAllActions:i=!1}=e;const o={display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:(0,d.A)(8),padding:"".concat((0,d.A)(1)," ").concat((0,d.A)(8)),transition:"all 0.3s ease-in-out",".legend-item-close-icon":{display:"none !important"},".legend-item-check-icon":{display:"none !important"}};return i&&delete o["&:hover"],t&&(o[".legend-item-check-icon"]={display:"block !important"},o["&:hover"]={...o["&:hover"],border:"1px transparent solid",".legend-item-close-icon":{display:"block !important"},".legend-item-check-icon":{display:"none !important"}}),n&&(o.backgroundColor=h.Qs.Blue[0]),o})),L=e=>{let{legend:t,selectedToFilter:n,selectedToEdit:i,onLegendItemClick:o,disableAllActions:l}=e;const a=(0,u.d4)(k.y$),{badgeShape:c}=a||{};return(0,C.jsxs)(O,{selectedToFilter:n,selectedToEdit:i,onClick:()=>{o&&o(t)},disableAllActions:l,children:[(0,C.jsxs)(r.A,{display:"inherit",gap:"inherit",alignItems:"center",children:[t.icon?(0,C.jsx)(r.A,{children:(0,C.jsx)(P.r,{icon:t.icon,iconColor:t.color,sx:{width:(0,d.A)(20),height:(0,d.A)(20),fontSize:(0,d.A)(16),color:t.color,display:"flex"}})},"".concat(t.id,"-").concat(t.icon,"-").concat(t.color)):(0,C.jsx)(r.A,{width:(0,d.A)(16),height:(0,d.A)(16),minWidth:(0,d.A)(16),minHeight:(0,d.A)(16),display:"flex",alignItems:"center",justifyContent:"center",bgcolor:t.icon?"transparent":t.color,borderRadius:"circle"===c?"50%":"0px",padding:(0,d.A)(2)}),(0,C.jsx)(s.A,{variant:v.Eq.bodySM,color:h.Qs.Neutrals[900],children:t.label})]}),!l&&(0,C.jsx)(A.gF,{className:"legend-item-check-icon",icon:"CheckCircle",size:"xs",color:h.Qs.Blue[500]}),!l&&(0,C.jsx)(s.A,{variant:v.Eq.tabSM,color:h.Qs.Neutrals[600],onClick:()=>{o&&o(t)},display:"contents",children:(0,C.jsx)(A.gF,{className:"legend-item-close-icon",icon:"CloseCircle",size:"xs",color:h.Qs.Neutrals[700]})})]})};var M,z,B=n(99085),Q=n(91688),H=n(49194),_=n(72119),W=n(70318),G=n(30105),q=n(53109),V=n(26805),U=n.n(V);const J=(0,_.Ay)(r.A)(M||(M=(0,N.A)(["\n  border-radius: 4px;\n  padding: 6px 8px 6px 5px;\n  .drag-element {\n    visibility: hidden;\n  }\n  .legend-edit {\n    visibility: hidden;\n    font-size: 12px;\n    padding: 0;\n  }\n  &:hover {\n    background-color: ",";\n    cursor: pointer;\n    .drag-element {\n      visibility: visible;\n    }\n    .legend-edit {\n      visibility: visible;\n    }\n  }\n"])),h.Qs.Neutrals[200]),K=(0,_.Ay)(G.A)(z||(z=(0,N.A)(["\n  padding: 3px 12px;\n  text-transform: none;\n  position: absolute;\n  bottom: 30px;\n  width: 210px;\n"])));let Z=function(e){return e.Manage="Manage",e.Display="Display",e}({}),Y=function(e){return e.item="item",e.folder="folder",e}({});const X=e=>{let{legends:t={},mode:n=Z.Display,handleLegendEditSelection:o,handleAddLegendItemToFilter:l,handleRemoveLegendItemFromFilter:c,selectedLegendItems:x=[],disableAllActions:g=!1}=e;const f=Object.keys(t).reduce(((e,t)=>(e[t]=!0,e)),{}),[y,j]=(0,i.useState)(f),[b,w]=(0,i.useState)(x),I=(0,u.wA)(),{orgId:E,chartId:D}=(0,Q.useParams)(),S=(0,u.d4)(m.Zx),k=(0,u.d4)(m.rn)||[],T=i.useMemo((()=>{const e=Object.keys(t).map((e=>({folder:e,values:t[e]})));return k.filter((t=>!e.some((e=>e.folder===t&&e.values.length>0)))).forEach((t=>{e.push({folder:t,values:[{id:"empty",folder:t,color:"#d7cfcf",theme:B.z5.Light,label:"Empty",icon:null}]})})),e}),[t,k]);(0,i.useEffect)((()=>{const e=x.filter((e=>e));w(e)}),[x]),(0,i.useEffect)((()=>{const e=Object.keys(t),n=Object.keys(y);if(e.length!==n.length-1){const t=e.reduce(((e,t)=>(n.includes(t)?e[t]=y[t]:e[t]=!0,e)),{});j(t)}const i={...t};null===k||void 0===k||k.forEach((e=>{i[e]||(i[e]=[{id:"empty",folder:e,color:"#d7cfcf",theme:B.z5.Light,label:"Empty",icon:null}],j((t=>({...t,[e]:!0}))))}));const o=null===k||void 0===k?void 0:k.filter((e=>!Object.keys(t).some((n=>n===e&&t[n].length>0))));I((0,q.Ww)(o))}),[t]);const R=e=>t=>{null===t||void 0===t||t.stopPropagation(),null===t||void 0===t||t.preventDefault(),j((t=>({...t,[e]:!t[e]})))},N=e=>{if(!g)if(n===Z.Manage)w([null===e||void 0===e?void 0:e.id]),null===o||void 0===o||o(null===e||void 0===e?void 0:e.id);else if(n===Z.Display){b.includes(null===e||void 0===e?void 0:e.id)?null===c||void 0===c||c(null===e||void 0===e?void 0:e.id):null===l||void 0===l||l(null===e||void 0===e?void 0:e.id),w((t=>t.includes(null===e||void 0===e?void 0:e.id)?t.filter((t=>t!==(null===e||void 0===e?void 0:e.id))):[...t,null===e||void 0===e?void 0:e.id]))}},P=async e=>{await I(p.wL.deleteFolderName({orgId:E,chartId:D,data:{folder:e,id:S.id}}))},O=async e=>{await I(p.wL.deleteFolder({orgId:E,chartId:D,data:{folder:e,id:S.id}}))},M=async e=>{const n=k.filter((t=>t!==e));null===n||void 0===n||n.forEach((e=>{j((t=>({...t,[e]:!0})))}));const i=null===n||void 0===n?void 0:n.filter((e=>!Object.keys(t).some((n=>n===e&&t[n].length>0))));I((0,q.Ww)(i))};return(0,C.jsxs)(r.A,{display:"flex",flexDirection:"column",gap:n===Z.Display&&(0,d.A)(16)||(0,d.A)(0),minHeight:"auto",children:[n===Z.Manage&&(0,C.jsxs)(C.Fragment,{children:[Object.keys(t).includes(B.FH)&&(0,C.jsx)(s.A,{variant:"bodySM",color:h.Qs.Neutrals[500],children:"UNFILED ITEMS"}),(0,C.jsx)(H.A,{items:T,itemKey:"folder",handleItemsReordered:(e,t)=>{(async(e,t)=>{if(n===Z.Manage){const n=T,[i]=n.splice(t,1);n.splice(e,0,i);const o=n.flatMap((e=>e.values.map((e=>"empty"!==e.id?e.id:null))));await I(p.wL.updateLegend({orgId:E,chartId:D,data:{rules:o}}))}})(e,t)},ListComponent:r.A,handleSort:void 0,type:Y.folder,emptyItem:(0,C.jsx)(r.A,{p:3,children:(0,C.jsx)(s.A,{variant:v.Eq.bodySM,color:h.Ay.secondaryGrey.main,children:"Empty"})}),renderItem:e=>{let{folder:i,values:l}=e;return(0,C.jsxs)(r.A,{display:"flex",flexDirection:"column",gap:y[i]?(0,d.A)(4):0,marginTop:i!==B.FH?(0,d.A)(6):0,children:[i!==B.FH&&(0,C.jsxs)(J,{component:"span",display:"flex",gap:1,alignItems:"center",children:[k.includes(i)?(0,C.jsx)(r.A,{paddingRight:1,width:"10%"}):(0,C.jsx)(r.A,{paddingRight:1,alignItems:"center",justifyContent:"center",display:"flex",style:{cursor:"grab"},width:"10%",children:(0,C.jsx)(A.Ay,{color:h.Qs.Neutrals[700],icon:"DragGrid",className:"drag-element"})}),(0,C.jsx)(ae,{title:i,handleToggleCollapse:R(i),open:y[i],disableAllActions:g,allowFolderEdit:n===Z.Manage,allowFolderOptions:n===Z.Manage,handleSaveFolder:(c=i,async e=>{if(n===Z.Manage)if(k.includes(c)){const t=k.map((t=>t===c?e:t));I((0,q.Ww)(t))}else{const n=t[c].map((e=>"empty"!==e.id?e.id:null));await I(p.wL.updateFolderName({orgId:E,chartId:D,data:{folder:e,folderLegendItems:n,id:S.id}}))}}),deleteFolderName:P,deleteFolder:O,legendLocalFolders:k,handleDeleteLocalFolder:M})]}),(0,C.jsx)(a.A,{in:y[i]||i===B.FH||g,children:(0,C.jsx)(r.A,{display:"flex",flexDirection:"column",gap:(0,d.A)(2),paddingLeft:i!==B.FH?(0,d.A)(38):0,children:(0,C.jsx)(H.A,{items:l,itemKey:"id",handleItemsReordered:(e,i,o,r,l)=>{(async(e,i,o,r,l)=>{if(n===Z.Manage){var a;const n=T.flatMap((e=>e.values.map((e=>e)))),d=null===(a=T.find((e=>r?e.folder===r:e.folder===B.FH)))||void 0===a?void 0:a.values[e],c=d?"empty"!==(null===d||void 0===d?void 0:d.id)?Object.values(t).flat().indexOf(d):n.indexOf(d):-1;if(r!==o)await I(p.wL.updateLegendRulePositionAndFolderName({orgId:E,chartId:D,ruleId:l.id,data:{folder:r===B.FH?"":r,id:S.id,position:-1===c?0:c}}));else if(l){var s;const e=null===(s=T.find((e=>r?e.folder===r:e.folder===B.FH)))||void 0===s?void 0:s.values[i],o=e?Object.values(t).flat().indexOf(e):-1,a=U()(n,{$splice:[[o,1],[c,0,l]]}).map((e=>"empty"!==e.id?e.id:null));await I(p.wL.updateLegend({orgId:E,chartId:D,data:{rules:a}}))}}})(e,i,o,r,l)},ListComponent:r.A,handleSort:void 0,type:Y.item,emptyItem:(0,C.jsx)(r.A,{p:3,children:(0,C.jsx)(s.A,{variant:v.Eq.bodySM,color:h.Ay.secondaryGrey.main,children:"Empty"})}),renderItem:e=>(0,C.jsxs)(J,{component:"span",display:"flex",alignItems:"center",gap:1,justifyContent:"space-between",children:[(0,C.jsxs)(r.A,{display:"flex",children:["empty"!==(null===e||void 0===e?void 0:e.id)&&(0,C.jsx)(r.A,{alignItems:"center",justifyContent:"center",display:"flex",style:{cursor:"grab"},children:(0,C.jsx)(A.Ay,{color:h.Qs.Neutrals[700],icon:"DragGrid",className:"drag-element"})}),(0,C.jsx)(L,{open:y[i],legend:e,selectedToFilter:b.includes(null===e||void 0===e?void 0:e.id),selectedToEdit:b.includes(null===e||void 0===e?void 0:e.id),disableAllActions:g})]}),"empty"!==(null===e||void 0===e?void 0:e.id)&&(0,C.jsx)(W.A,{onClick:()=>null===o||void 0===o?void 0:o(null===e||void 0===e?void 0:e.id),style:{padding:0},children:(0,C.jsx)(A.Ay,{icon:"Edit",className:"legend-edit",color:h.Qs.Neutrals[700]})})]})})})}),i===B.FH&&(0,C.jsx)(F.A,{sx:{borderBottomWidth:"0.02rem",borderColor:h.Qs.Neutrals[300],paddingTop:0}})]});var c}}),(0,C.jsx)(K,{variant:"outlined",onClick:async()=>{const e=(()=>{const e=["Folder"],t=e[Math.floor(Math.random()*e.length)];return"".concat(t," ").concat(Math.floor(1e3*Math.random()))})(),n=[...k,e];null===n||void 0===n||n.forEach((e=>{j((t=>({...t,[e]:!0})))}));const i=null===n||void 0===n?void 0:n.filter((e=>!Object.keys(t).some((n=>n===e&&t[n].length>0))));I((0,q.Ww)(i))},children:(0,C.jsxs)(r.A,{display:"flex",gap:1,alignItems:"center",children:[(0,C.jsx)(A.Ay,{icon:"Add",variant:"sm"}),(0,C.jsx)(s.A,{variant:v.Eq.labelLG,color:h.Qs.Neutrals[700],children:"Add new folder"})]})})]}),n===Z.Display&&(0,C.jsx)(C.Fragment,{children:Object.keys(t).map((e=>(0,C.jsxs)(r.A,{display:"flex",flexDirection:"column",gap:y[e]?(0,d.A)(4):0,children:[e!==B.FH?(0,C.jsx)(ae,{title:e,handleToggleCollapse:R(e),open:y[e],disableAllActions:g,allowFolderEdit:!1}):null,(0,C.jsx)(a.A,{in:y[e]||e===B.FH||g,children:(0,C.jsx)(r.A,{display:"flex",flexDirection:"column",gap:(0,d.A)(2),children:t[e].map((t=>(0,C.jsx)(r.A,{style:{padding:"6px 0 6px 0"},children:(0,C.jsx)(L,{open:y[e],legend:t,onLegendItemClick:N,selectedToFilter:b.includes(null===t||void 0===t?void 0:t.id)&&n===Z.Display,disableAllActions:g})})))})})]})))})]})};var $=n(67784),ee=n(2173),te=n(24115),ne=n(17339),ie=n(44676),oe=n(55357),re=n(84);const le=(0,o.Ay)(s.A)((e=>{let{allowEdit:t}=e,n={padding:"".concat((0,d.A)(1)," ").concat((0,d.A)(1))};return t&&(n={...n,"&:hover":{cursor:"pointer",backgroundColor:h.Qs.Violet[0],borderRadius:(0,d.A)(4),".legend-folder-edit-icon":{display:"block",cursor:"pointer"}},transition:"all 0.3s ease-in-out",".legend-folder-edit-icon":{display:"none"}}),n})),ae=e=>{let{title:t="Folder Name",handleToggleCollapse:n,open:o=!0,disableAllActions:l=!1,allowFolderEdit:a=!1,allowFolderOptions:c=!1,handleSaveFolder:u,deleteFolderName:p,deleteFolder:m,legendLocalFolders:x,handleDeleteLocalFolder:g}=e;const[f,y]=i.useState(""),[j,b]=i.useState(!1),[w,I]=i.useState(null),D=Boolean(w),{openDialog:S}=(0,re.A)("legendFolderDelete"),k=()=>{b(!0)},[T,R]=(0,ee.A)((async()=>{await(null===u||void 0===u?void 0:u(f.trim())),b(!1)})),N=()=>{I(null)},F=()=>{null===p||void 0===p||p(f)},P=()=>{null===m||void 0===m||m(f)},O=[{name:"Rename",color:h.Qs.Neutrals[600]},{name:!l&&o?"Collapse":"Expand",color:h.Qs.Neutrals[600]},{name:"Delete",color:h.Qs.Error[500]}].map((e=>{return(0,C.jsx)(oe.A,{onClick:(t=e.name,async e=>{"Rename"===t?k():"Collapse"===t||"Expand"===t?n(e):"Delete"===t&&(null!==x&&void 0!==x&&x.includes(f)?null===g||void 0===g||g(f):S({folder:f,handleDeleteFolderName:F,handleDeleteFolder:P})),N()}),defaultChecked:!1,children:(0,C.jsx)(s.A,{color:e.color,variant:"bodySM",children:e.name})},"folderMenu".concat(e.name));var t}));(0,i.useEffect)((()=>(y(t),()=>{y("")})),[t]);const L=(0,C.jsx)(ie.A,{anchorEl:w,anchorOrigin:{vertical:"top",horizontal:"left"},id:"folderMenu",keepMounted:!0,transformOrigin:{vertical:"top",horizontal:"left"},open:D,onClose:N,children:O});return(0,C.jsxs)(r.A,{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",gap:(0,d.A)(12),width:"90%",children:[l?null:o?(0,C.jsx)(s.A,{variant:v.Eq.subheadingLG,onClick:n,display:"contents",children:(0,C.jsx)(A.gF,{icon:"Previous"})},"".concat(t,"-previous-icon")):(0,C.jsx)(s.A,{variant:v.Eq.subheadingLG,onClick:n,display:"contents",children:(0,C.jsx)(A.gF,{icon:"Next"})},"".concat(t,"-next-icon")),j?(0,C.jsx)(te.A,{transparent:!0,loading:T,spinnerSize:16,children:(0,C.jsxs)(r.A,{display:"flex",alignItems:"center",gap:(0,d.A)(8),children:[(0,C.jsx)($.A,{autoFocus:!0,size:"small",value:f,onChange:e=>{y(e.target.value)}}),(0,C.jsx)(E.A,{variant:"iconOnly",color:"secondaryGrey",size:"small",onClick:()=>{b(!1),y(t)},disabled:T,children:(0,C.jsx)(A.gF,{icon:"Close",size:"sm"})}),(0,C.jsx)(E.A,{variant:"iconOnly",color:"success",size:"small",disabled:T||!f.trim(),onClick:R,children:(0,C.jsx)(A.gF,{icon:"CheckRegular",size:"sm"})})]})}):(0,C.jsxs)(r.A,{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",children:[(0,C.jsx)(le,{variant:v.Eq.subheadingLG,display:"flex",gap:(0,d.A)(6),alignItems:"center",allowEdit:a,children:f}),(0,C.jsxs)(r.A,{display:"flex",children:[a&&(0,C.jsx)(ne.A,{style:{padding:0},onClick:k,children:(0,C.jsx)(A.Ay,{icon:"Edit",className:"legend-edit",color:h.Qs.Neutrals[700]})}),c&&(0,C.jsx)(ne.A,{style:{paddingTop:0,paddingBottom:0,paddingRight:8,paddingLeft:8},onClick:e=>{I(e.currentTarget)},children:(0,C.jsx)(A.Ay,{icon:"EllipsesV",className:"legend-edit",color:h.Qs.Neutrals[700]})})]})]}),L]})};n(91812),n(97631)},99085:(e,t,n)=>{n.d(t,{DC:()=>p,F0:()=>A,FH:()=>u,N9:()=>x,Ve:()=>s,Vy:()=>g,dX:()=>l,fG:()=>h,rh:()=>d,ux:()=>a,wK:()=>m,z5:()=>c});var i=n(37294),o=n(10621),r=n(47088);let l=function(e){return e.Add="add",e.AddFirst="addFirst",e.Edit="edit",e.Empty="empty",e.List="list",e}({}),a=function(e){return e.EQ="EQ",e.GT="GT",e.GE="GE",e.LT="LT",e.LE="LE",e.CONTAINS="CONTAINS",e.NOT_CONTAINS="NOT_CONTAINS",e}({});const s={EQ:"Equal",GT:"Greater Than",GE:"Greater Than or Equal",LT:"Less Than",LE:"Less Than or Equal",CONTAINS:"Contains",NOT_CONTAINS:"Doesn't Contain"};let d=function(e){return e.AND="AND",e.OR="OR",e}({}),c=function(e){return e.Light="light",e.Dark="dark",e}({});const h={id:"",label:"",color:i.o3.MintGreen,icon:"",theme:c.Light,join:d.AND,comparators:[],displayType:r.tI.ICON},u="NO_FOLDER",p={isDefault:!1,label:"Role ** is vacant **",model:r.oL.Role,name:"unfilled",id:"unfilled",type:r.yw.String,access:r.Tl.Protected,value:{label:"Unfilled",model:o.A2.ROLE,name:"unfilled",id:"unfilled",type:o.ZE.STRING},typeMetadata:{}},m={[r.yw.Chart]:[],[r.yw.Boolean]:[a.EQ],[r.yw.Currency]:[a.EQ,a.GT,a.GE,a.LT,a.LE],[r.yw.Date]:[a.EQ,a.GT,a.GE,a.LT,a.LE],[r.yw.Location]:[a.EQ,a.CONTAINS],[r.yw.Number]:[a.EQ,a.GT,a.GE,a.LT,a.LE],[r.yw.RichText]:[a.CONTAINS,a.EQ],[r.yw.String]:[a.EQ,a.CONTAINS],[r.yw.Switch]:[a.EQ],[r.yw.Tags]:[a.CONTAINS,a.NOT_CONTAINS],[r.yw.Url]:[a.CONTAINS,a.EQ]},x=22,g=45,A=10},98621:(e,t,n)=>{n.d(t,{C:()=>r,z:()=>l});var i=n(99085),o=n(47088);const r=(e,t)=>{let n=e;t&&(n=e.filter((e=>e.label.toLowerCase().includes(t.toLowerCase()))));const o=n.reduce(((e,t)=>{const n=t.folder||i.FH;return e[n]||(e[n]=[]),e[n].push(t),e}),{}),r={};return o[i.FH]&&(r[i.FH]=o[i.FH]),Object.keys(o).forEach((e=>{e!==i.FH&&(r[e]=o[e])})),r},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.yw.String;return i.wK[e].map((e=>({value:i.ux[e],label:i.Ve[i.ux[e]]})))||[]}},69278:(e,t,n)=>{n.d(t,{N:()=>h,e:()=>f});var i=n(65043),o=n(89119),r=n(14556),l=n(7866),a=n(78396),s=n(79091),d=n(69219),c=n(70579);const h=e=>{let{showLegendOnPublicView:t}=e;const n=(0,r.d4)(l.T3),h=(0,r.d4)(d.uo),u=(0,r.wA)(),p=[a.uI.PRINT,a.uI.PRINT_PREVIEW].includes(h);(0,i.useEffect)((()=>{p&&u((0,s.l)([]))}),[p]);const m=!t||!(null===n||void 0===n||!n.length);return(0,c.jsx)(c.Fragment,{children:m&&(0,c.jsx)(o.L5,{handleManageLegendsClick:()=>{u((0,s.wt)(null))},disableAllActions:p,legendItems:n})})};var u=n(91688),p=n(96446),m=n(99085),x=n(19367),g=n(57546),A=n(94916),v=n(98433);const f=e=>{let{children:t}=e;const n=(0,r.d4)(v.DD),o=(0,r.d4)(d.uo),[l]=(0,A.A)("showLegend",!1,!0),s=(0,r.d4)(x.Jf),{prevChartId:f}=(0,g.A)(),y=(0,u.useParams)(),j=[a.uI.PRINT,a.uI.PRINT_PREVIEW].includes(o),b=(0,i.useMemo)((()=>{if(j){if("topLeft"===n.position)return{left:m.N9,top:m.N9,bottom:void 0,right:void 0};if("topRight"===n.position)return{left:void 0,top:m.N9,bottom:void 0,right:m.N9};if("bottomLeft"===n.position)return{left:m.N9,top:void 0,bottom:m.N9,right:void 0};if("bottomRight"===n.position)return{left:void 0,top:void 0,bottom:m.N9,right:m.N9}}return f?{left:m.F0,top:m.Vy,bottom:void 0,right:void 0}:{left:m.N9,top:m.N9,bottom:0,right:0}}),[j,n]),C=(0,i.useMemo)((()=>(null===y||void 0===y?void 0:y.resourceAction)!==a.uI.THEME&&((null===y||void 0===y?void 0:y.resourceAction)!==a.uI.AI_INSIGHTS&&(j?n.visible:o===a.uI.PUBLIC?null===s||void 0===s?void 0:s.showLegend:l))),[[null===y||void 0===y?void 0:y.resourceAction,n]]);return(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(p.A,{position:"relative",width:"100%",height:"100%",overflow:"hidden",children:[t,C&&(0,c.jsx)(p.A,{position:"absolute",zIndex:11,top:b.top?"".concat(b.top,"px"):void 0,left:b.left?"".concat(b.left,"px"):void 0,bottom:b.bottom?"".concat(b.bottom,"px"):void 0,right:b.right?"".concat(b.right,"px"):void 0,children:(0,c.jsx)(h,{showLegendOnPublicView:o===a.uI.PUBLIC||j})},"legend-container-".concat(b.left,"-").concat(b.top,"-").concat(b.bottom,"-").concat(b.right))]})})}},7866:(e,t,n)=>{n.d(t,{E0:()=>g,Ru:()=>v,T3:()=>h,Un:()=>u,ZG:()=>A,Zx:()=>d,lM:()=>p,rn:()=>c});var i=n(80192),o=n(99085),r=n(7743),l=n(10621),a=n(59177),s=n(47088);const d=e=>{var t;return null===(t=e.chart.info)||void 0===t?void 0:t.legend},c=e=>e.chart.legendFolders,h=(0,i.Mz)(d,(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(null===e||void 0===e?void 0:e.rules)||[]})),u=(0,i.Mz)(h,c,((e,t)=>{const n=e.reduce(((e,t)=>t.folder?(e.includes(t.folder)||e.push(t.folder),e):e),[]);return Array.from(new Set([...n,...t||[]]))})),p=(0,i.Mz)(h,(e=>e.reduce(((e,t)=>(e[t.id]=t,e)),{}))),m=(e,t)=>e.model===l.A2.ROLE&&t.model===l.A2.MEMBER?-1:e.model===l.A2.MEMBER&&t.model===l.A2.ROLE?1:0,x=(0,i.Mz)(r.dS,(e=>e.filter((e=>![s.yw.Attachment,s.yw.Computed,s.yw.Rollup,s.yw.IconPickList].includes(e.type))).sort(m).map((e=>({...e,label:"".concat(e.model===l.A2.MEMBER?"Person":(0,a.ZH)(l.A2.ROLE)," ").concat(e.label),value:e}))))),g=(0,i.Mz)(x,(e=>[o.DC,...e])),A=(0,i.Mz)(g,(e=>e.reduce(((e,t)=>(e[t.id]=t,e)),{}))),v=e=>e.legend.filterBadges},82072:(e,t,n)=>{n.d(t,{M:()=>v,T:()=>f});var i=n(65043),o=n(63336),r=n(96446),l=n(85865),a=n(17392),s=n(10611),d=n(14556),c=n(32115),h=n(98433),u=n(15813),p=n(91688),m=n(2173),x=n(75156),g=n(37294),A=n(70579);let v=function(e){return e.CONNECTION="connection",e.UNKNOWN="unknown",e}({});const f=e=>{let{index:t,printJob:n,handleCancel:f,handleCompleted:y}=e;const j=(0,d.wA)(),b=(0,i.useRef)(),C=(0,i.useRef)(0),[w,I]=(0,i.useState)("Connecting to printer..."),{orgId:E,chartId:D}=(0,p.useParams)(),{failed:S}=(0,d.d4)(h.W8),k=async()=>{I("Getting file download link..."),y(n.fileCode)},[T,R]=(0,m.A)((async()=>{f(n.id)})),N=e=>{clearInterval(b.current),C.current=0,j(h.kv.cancelPrintJob({orgId:E,chartId:D,jobId:n.id}));let t="";t=e===v.CONNECTION?"Failed to connect to printer, try again or contact support":"Could not complete export, try again or contact support",I(t),j((0,h.b0)([{...n,error:t},...S]))};(0,i.useEffect)((()=>(n.id&&(b.current=setInterval((()=>{(async()=>{const{error:e,payload:t}=await j(h.R0.getJobStatus({jobId:n.id}));if(e)return void console.log(e);const{data:{jobStatus:i,status:o}}=t;o===u.A8.FAILED?N(v.UNKNOWN):o===u.A8.DEQUEUED&&C.current<1800?(C.current++,I(i||i.curPage?"File almost ready for download...":"Generating page "+Math.min(i.curPage,i.numPages)+" of "+i.numPages+"...")):o!==u.A8.DEQUEUED&&o!==u.A8.COMPLETE?(C.current++,C.current<60?I(i||i.curPage?"Connecting to secure channel...":"Generating Page "+Math.min(i.curPage,i.numPages)+" of "+i.numPages+"..."):N(v.CONNECTION)):o===u.A8.COMPLETE?(clearInterval(b.current),setTimeout((async()=>{await k()}),5e3),console.log(k)):C.current>=1800&&N(v.UNKNOWN)})()}),1500)),()=>{clearInterval(b.current)})),[n.id]);return(0,A.jsxs)(o.A,{variant:"outlined",sx:{padding:"10px 8px 10px 16px"},children:[(0,A.jsxs)(r.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",gap:1,children:[(0,A.jsx)(l.A,{variant:c.Eq.subheadingSM,textOverflow:"ellipsis",noWrap:!0,children:"Print ".concat(t+1)}),(0,A.jsx)(a.A,{onClick:R,disabled:!!T,sx:{color:g.Qs.Neutrals[500]},children:(0,A.jsx)(x.gF,{icon:"Close",size:"xs"})})]}),(0,A.jsx)(l.A,{variant:c.Eq.caption,color:g.Qs.Neutrals[600],children:"".concat(new Date(n.enqueued).toLocaleTimeString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"}))}),(0,A.jsx)(s.A,{sx:{marginY:"6px",marginRight:"8px"}}),(0,A.jsx)(l.A,{variant:c.Eq.subheadingXS,color:g.Qs.Neutrals[600],children:w})]})}},67202:(e,t,n)=>{n.d(t,{H_:()=>m,OV:()=>p,Ql:()=>g,ZK:()=>x,gb:()=>v,lR:()=>y,ls:()=>f,uV:()=>A});var i=n(34535),o=n(42518),r=n(67784),l=n(69570),a=n(40710),s=n(78492),d=n(14256),c=n(41020),h=n(15813),u=n(37294);const p=(0,i.Ay)(o.A)({height:h.lL}),m=(0,i.Ay)(r.A)({"& .MuiInputBase-root":{backgroundColor:u.Qs.Neutrals[0],cursor:"pointer",height:h.lL,minWidth:"fit-content"}}),x=(0,i.Ay)(r.A)({"& .MuiInputBase-root":{backgroundColor:"#DCECFF",cursor:"pointer",paddingRight:0,paddingLeft:0,color:u.Qs.Neutrals[900]}}),g=(0,i.Ay)(l.A)({padding:"12px 20px !important",height:h.nH,borderBottom:"1px solid ".concat(u.Qs.Neutrals[400]),"&.MuiAccordionSummary-root.Mui-expanded":{borderBottom:"0px !important"}}),A=(0,i.Ay)(a.A)({backgroundColor:u.Qs.Neutrals[200],padding:"0 !important"}),v=(0,i.Ay)(s.A)({display:"flex",flexDirection:"column",gap:"10px"}),f=(0,i.Ay)(d.A)({padding:"0 9px",color:u.Qs.Neutrals[700]}),y=(0,i.Ay)(c.Ay)({"& .MuiPopover-paper":{borderRadius:"4px",maxHeight:"calc(100vh - 200px) !important"}})},24968:(e,t,n)=>{n.d(t,{A:()=>C});var i=n(65043),o=n(14556),r=n(66856),l=n(53109),a=n(43331),s=n(84091),d=n(97105),c=n(57546),h=n(78396),u=n(36138),p=n(94234),m=n(3643),x=n(43781),g=n(49015),A=n(19367),v=n(43862),f=n(81635),y=n(59177),j=n(66588),b=n(23993);function C(e,t){let{chartId:n,orgId:C,mode:w}=t;const I=(0,o.wA)(),[E,D]=(0,i.useState)("chart"===e),[S,k]=(0,i.useState)("organization"===e),[T,R]=(0,i.useState)("chartSharing"===e),N=(0,o.d4)(A.Jf),F=(0,x.A)({orgId:C}),P=(0,u.u)([(0,r.si)()]),O=(0,c.A)(),L=O.pId,M=O.jobId,z=(null===O||void 0===O?void 0:O.role)||null,B=(null===O||void 0===O?void 0:O.email)||null,Q=(0,o.d4)((e=>(0,b.Cq)(e,B))),H=B&&(0,y.B9)(B)&&!E&&!S?Q:null,{show:_}=(0,j.A)(),W=(0,m.A)(),G=W?a.no:a.UY,q=W?s.rk:s.OH;(0,i.useEffect)((()=>{"organization"===e&&C&&async function(){if(C){k(!0);let e=[I(G.get({orgId:C,jobId:M}))];w!==h.uI.PUBLIC&&e.push(I(G.getCharts({orgId:C,jobId:M}))),await Promise.all(e),k(!1)}}()}),[C]),(0,i.useEffect)((()=>("chart"===e&&n&&C&&async function(){if(C&&n){var e,t;D(!0);let i=[await I(W?l.wz.get({orgId:C,chartId:n,mode:null===P||void 0===P||null===(e=P.params)||void 0===e?void 0:e.base,pId:L,jobId:M}):l.te.get({orgId:C,chartId:n,mode:null===P||void 0===P||null===(t=P.params)||void 0===t?void 0:t.base,pId:L})),I(W?l.wz.getRoles({orgId:C,chartId:n,jobId:M}):l.te.getRoles({orgId:C,chartId:n}))];i.push(I(q.getPeople({orgId:C,jobId:M}))),w===h.uI.PUBLIC&&i.push(I(l.te.getLinkedCharts({orgId:C,chartId:n}))),w===h.uI.PUBLIC||W||i.push(I(l.te.getChartIntegration({orgId:C,chartId:n}))),W&&i.push(I(p.Ww.getPrintThemes({jobId:M}))),W||w===h.uI.PUBLIC||i.push(I(g.lf.getUserTours({orgId:C,utilizeTemplateTour:null===O||void 0===O?void 0:O.utilizeTemplateTour}))),await Promise.all(i),D(!1)}}(),()=>{})),[n]),(0,i.useEffect)((()=>{!z||B||E||S||I((0,v.mO)({roleId:z})),!z||E||S||B||!(w!==h.uI.PUBLIC||w===h.uI.PUBLIC&&null!==N&&void 0!==N&&N.showRoleDetails)||I((0,f.gN)({selectedRoleId:z,mode:"view"}))}),[z,N,E,S]);const V=(0,i.useRef)(!1);return(0,i.useEffect)((()=>{!V.current&&H&&(null!==H&&void 0!==H&&H.roleId?(I((0,v.mO)({roleId:null===H||void 0===H?void 0:H.roleId})),(w!==h.uI.PUBLIC||w===h.uI.PUBLIC&&null!==N&&void 0!==N&&N.showRoleDetails)&&I((0,f.gN)({mode:"view",activePersonId:null===H||void 0===H?void 0:H.member,selectedRoleId:null===H||void 0===H?void 0:H.roleId})),V.current=!0):null!==H&&void 0!==H&&H.roleId||_("No results found for the email","error",5e3))}),[N,H]),(0,i.useEffect)((()=>("chartSharing"===e&&n&&C&&async function(){C&&n&&(R(!0),await I(d.Bf.getChartUsers({orgId:C,chartId:n})),R(!1))}(),()=>{})),[n,C]),E||S||T||F}}}]);
//# sourceMappingURL=7824.c8f66d4b.chunk.js.map