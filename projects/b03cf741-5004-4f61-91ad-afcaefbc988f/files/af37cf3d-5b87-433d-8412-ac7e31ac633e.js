"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[3760],{31510:(e,n,t)=>{t.d(n,{A:()=>a});var l,r=t(57528),i=t(72119),o=t(43867);const a=(0,i.Ay)(o.A)(l||(l=(0,r.A)(["\n  &.MuiDialogContent-dividers {\n    padding: 0 0 0;\n  }\n"])))},55871:(e,n,t)=>{t.d(n,{A:()=>c});var l=t(40454),r=t(61531),i=t(80539),o=t(75156),a=t(96364),d=t(59177),s=t(70579);const c=e=>{let{person:n,showEmail:t=!1}=e;const{firstName:c="",lastName:m="",photo:h,id:u,email:p,roleDefaults:g}=n;let x=Boolean(c)||Boolean(m)?"".concat(c," ").concat(m):p;const v="loadMore"===u,b="emailNotValid"===u,A=!v&&!b&&t;return(0,s.jsxs)(l.A,{container:!0,alignContent:"center",alignItems:"center",children:[(0,s.jsxs)(r.A,{mr:2,children:[v&&(0,s.jsx)(r.A,{children:(0,s.jsx)(o.Ay,{icon:"LoadMore",size:"x2"})}),!v&&b&&(0,s.jsx)(r.A,{children:(0,s.jsx)(o.Ay,{icon:"ExclamationTriangle",size:"x2"})}),!v&&!b&&(0,s.jsx)(i.A,{width:30,height:30,src:h,name:x,overrideColor:null===n||void 0===n?void 0:n.memberPhotoColor})]}),(0,s.jsxs)(l.A,{item:!0,xs:!0,children:[(0,s.jsx)(a.A,{display:"inline",children:"".concat(c," ").concat(m," ")}),(c||m)&&p&&t&&"| ",t&&Boolean(p)?(0,s.jsx)(a.A,{display:"inline",children:p}):A&&(0,s.jsx)(r.A,{display:"inline",children:(0,s.jsx)(a.A,{fontStyle:"italic",display:"inline",children:"No email found."})}),(0,d.aG)(null===g||void 0===g?void 0:g.title)&&(0,s.jsx)(a.A,{children:null!==g&&void 0!==g&&g.title?g.title:""})]})]})}},94341:(e,n,t)=>{t.d(n,{A:()=>h});var l=t(65043),r=t(93865),i=t(18858);const o={FIELD:"field",TOP_ROLE_ID:"topRole",COLOR_RULE:"colorRule",THEME_FIELD:"themeField",PAGE_BREAK_ROLES:"pageBreakRoles",PICK_LIST_ITEM:"pickListItem"},a=e=>{let{handleDrop:n,handleDrag:t,index:a,type:d,disabled:s}=e;const c=(0,l.useRef)(null),[{isOver:m},h]=(0,r.H)({accept:o[d],drop(e){if(!c.current)return;const t=e.initIndex;n(t,a)},hover(e){if(!c.current)return;const n=e.index,l=a;n!==l&&(t(n,l),e.index=l)},collect:e=>({isOver:e.isOver()})}),[{isDragging:u},p]=(0,i.i)({item:{type:o[d],index:a,initIndex:a},collect:e=>({isDragging:e.isDragging()})});return!s&&p(h(c)),{ref:c,isOver:m,isDragging:u}};var d=t(26805),s=t.n(d),c=t(70579);const m=e=>{let{children:n,index:t,handleDrop:l,handleDrag:r,style:i,dragStyle:o,overStyle:d,disabled:s=!1,type:m}=e;i=i||{};const{ref:h,isDragging:u,isOver:p}=a({type:m,handleDrop:l,handleDrag:r,index:t,disabled:s}),g=u?.2:1;return i=u?o||{}:p&&d||{},(0,c.jsx)("div",{ref:h,style:{...i,opacity:g},children:n})},h=e=>{let{idField:n,getIdField:t,type:r,items:i=[],renderListItem:o,handleDrop:a}=e;const[d,h]=(0,l.useState)(i);if("function"!==typeof o)return null;const u=(0,l.useCallback)(((e,n)=>{const t=d[e];h(s()(d,{$splice:[[e,1],[n,0,t]]}))}),[d]);return(0,l.useEffect)((()=>{h(i)}),[i]),d.map(((e,l)=>{const i=n||"function"===typeof t&&t(e);return(0,c.jsx)(m,{index:l,type:r,handleDrop:a,handleDrag:u,children:"function"===typeof o&&o(e,l)},"draggable-".concat(r,"-").concat(e[i||"id"]||l))}))}},53760:(e,n,t)=>{t.r(n),t.d(n,{default:()=>Xe});var l,r=t(57528),i=t(65043),o=t(5816),a=t(76031),d=t(31510),s=t(74593),c=t(84),m=t(61531),h=t(9579),u=t(17339),p=t(5571),g=t(40454),x=t(52907),v=t(14556),b=t(43862),A=t(66856),j=t(36138),f=t(61258),y=t(2173),I=t(96364),C=t(55357),w=t(84866),k=t(7743),R=t(172),D=t(96446),S=t(6803),M=t(23851),L=t(77739),E=t(8266),O=t(80539),N=t(61071),F=t(70318),T=t(75156),z=t(23993),P=t(45418),V=t(78396),W=t(72119),H=t(10621),_=t(26225),B=t(70579);const G=(0,W.Ay)(D.A)(l||(l=(0,r.A)(["\n  position: absolute;\n  width: 100%;\n  background: rgba(220, 220, 220, 0.3);\n  padding: 8px;\n  z-index: 999;\n  top: 0;\n  bottom: 0;\n  left: 0;\n"]))),q=e=>{var n,t,l,r;let{roleId:o,handleChange:a,hideHierarchyOption:d,currentParentId:s,readOnly:m}=e;const h=(0,v.d4)((e=>(0,z.gn)(e.roles,o))),u=(0,v.d4)((e=>(0,_.p4)(e,o))),p=(0,v.d4)((e=>(0,P.Yk)(e,{ids:(null===h||void 0===h?void 0:h.members)||[]}))),g=(0,v.d4)((e=>(0,P.Yk)(e,{ids:(null===u||void 0===u?void 0:u.members)||[]})));let x=!1;(null===u||void 0===u?void 0:u.type)===V.mv.HIDDEN&&(x=!0);const b=(0,i.useMemo)((()=>{let e="";const n=g.map((e=>e.name)).join(", ");if(e=n.length>40?"".concat(n,"..."):n,!e){var t,l;const n=null===u||void 0===u||null===(t=u.fields)||void 0===t||null===(l=t.find((e=>"name"===e.name)))||void 0===l?void 0:l.id;e=(null===u||void 0===u?void 0:u[n])||""}return e}),[g,u]),A=(0,i.useMemo)((()=>{let e="";const n=p.map((e=>e.name)).join(", ");if(e=n.length>40?"".concat(n,"..."):n,!e){var t,l;const n=null===h||void 0===h||null===(t=h.fields)||void 0===t||null===(l=t.find((e=>"name"===e.name)))||void 0===l?void 0:l.id;e=(null===h||void 0===h?void 0:h[n])||""}return e}),[p,h]),j=(e,n)=>{a(e,n)},{openDialog:f}=(0,c.A)("changeManager",{onSelected:j}),y=()=>{f({handleChange:j,hideHierarchyOption:d,currentParentId:s,roleId:o})},C=()=>{j(null)},w="department"===(null===h||void 0===h?void 0:h.type)||"location"===(null===h||void 0===h?void 0:h.type);return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(D.A,{mb:2,children:w&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(D.A,{children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"regular",children:(0,S.A)(null===h||void 0===h?void 0:h.type)})}),(0,B.jsxs)(R.A,{button:!0,alignItems:"center",ContainerComponent:"div",onClick:!m&&y,children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(M.A,{sizes:"medium",children:(0,B.jsx)(O.A,{name:A,overrideColor:null===(n=p[0])||void 0===n?void 0:n.memberPhotoColor,src:null===(t=p[0])||void 0===t?void 0:t.photo})}),(0,B.jsx)(N.A,{primary:s&&(null===h||void 0===h?void 0:h.type)!==V.mv.HIDDEN?(0,B.jsx)(I.A,{variant:"body1",children:(0,H.mA)(h||"manager")}):(0,B.jsx)(I.A,{color:"primary",children:m?"no manager":"select manager"})}),m&&(0,B.jsx)(L.A,{title:"Relationship is currently synced to a third party integration. Disable your sync to allow manual changes to this field",placement:"bottom",arrow:!0,children:(0,B.jsx)(G,{children:(0,B.jsx)(E.A,{children:(0,B.jsx)(T.gF,{icon:"Lock",size:"lg"})})})}),!m&&(0,B.jsxs)(E.A,{children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(D.A,{mr:1,display:"inline-block",children:(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:C,size:"small",children:(0,B.jsx)(T.gF,{icon:"Close",size:"lg"})})}),(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:y,size:"small",children:(0,B.jsx)(T.gF,{icon:"Edit",size:"sm"})})]})]})]})}),(0,B.jsx)(D.A,{children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"regular",children:"Manager"})}),(0,B.jsxs)(R.A,{button:!0,alignItems:"center",ContainerComponent:"div",onClick:!m&&y,children:[s&&!x&&u&&(0,B.jsx)(M.A,{sizes:"medium",children:(0,B.jsx)(O.A,{name:b,overrideColor:null===(l=g[0])||void 0===l?void 0:l.memberPhotoColor,src:null===(r=g[0])||void 0===r?void 0:r.photo})}),(0,B.jsx)(N.A,{primary:s&&!x&&u?(0,B.jsx)(I.A,{variant:"body1",children:(0,H.mA)(u||"manager")}):(0,B.jsx)(I.A,{color:"primary",children:m?"no manager":"select manager"}),secondary:(0,B.jsx)(I.A,{variant:"body2",color:"primary",children:b})}),m&&(0,B.jsx)(L.A,{title:"Relationship is currently synced to a third party integration. Disable your sync to allow manual changes to this field",placement:"bottom",arrow:!0,children:(0,B.jsx)(G,{children:(0,B.jsx)(E.A,{children:(0,B.jsx)(T.gF,{icon:"Lock",size:"lg"})})})}),!m&&!w&&(0,B.jsxs)(E.A,{children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(D.A,{mr:1,display:"inline-block",children:(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:C,size:"small",children:(0,B.jsx)(T.gF,{icon:"Close",size:"lg"})})}),(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:y,size:"small",children:(0,B.jsx)(T.gF,{icon:"Edit",size:"sm"})})]})]})]})};var Y=t(90694),U=t(96942);const K=e=>{let{displayStyle:n,handleChange:t,handleRemove:l,label:r=""}=e;return(0,B.jsxs)(m.A,{display:"flex",gridGap:16,alignItems:"center",flexWrap:"nowrap",children:[(0,B.jsxs)(m.A,{flex:1,display:"flex",gridGap:!0,alignItems:"center",children:[(0,B.jsx)(m.A,{onClick:l,display:"inline",mr:2,children:(0,B.jsx)(T.Ay,{icon:"Remove"})}),(0,B.jsx)(I.A,{variant:"body1",children:r})]}),(0,B.jsx)(U.Ay,{initialColor:n.color,handleChangeAvatarBackground:e=>{t({target:{value:e,name:"color"}})}}),(0,B.jsx)(m.A,{width:80,clone:!0,children:(0,B.jsxs)(w.A,{select:!0,name:"style",label:"style",defaultValue:n.style,onChange:t,size:"small",children:[(0,B.jsx)(C.A,{value:"dashed",children:"- - - -"},"dottedStyle-dashed"),(0,B.jsx)(C.A,{value:"dotted",children:".........."},"dottedStyle-dotted"),(0,B.jsx)(C.A,{value:"solid",children:(0,B.jsx)("hr",{style:{width:50}})},"dottedStyle-solid")]})})]})},Q=e=>{let{dottedReports:n=[]}=e;const[t,l]=(0,i.useState)(n.filter((e=>e)).map((e=>e.roleId))),[r,o]=(0,i.useState)(n.filter((e=>e)).reduce(((e,n)=>(e[n.roleId]={...n},e)),{})),{setValue:a,register:d}=(0,f.xW)(),s=(0,v.d4)((e=>(0,z.uN)(e,{roleIds:t})));(0,i.useEffect)((()=>{d({name:"role.dottedReports",type:"custom"})}),[]),(0,i.useEffect)((()=>{const e=t.map((e=>({...r[e],roleId:e})));a("role.dottedReports",e)}),[r]);const c=e=>n=>{const{name:t,value:l}=n.target;o((n=>({...n,[e]:{...n[e],[t]:l}})))},m=e=>()=>{l((n=>{const t=[...n],l=t.indexOf(e);return-1!==l&&t.splice(l,1),t})),o((n=>{const t={...n};return t[e]=null,delete t[e],t}))};return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(Y.A,{disableClearable:!0,hideInputValue:!0,label:"Search for Additional Reports",handleSelect:e=>{let{role:{id:n}}=e;return()=>{let e=[...t],r=e.indexOf(n);-1!==r?e.splice(r,1):e.push(n),l(e),o((n=>e.reduce(((e,t)=>(e[t]={style:"dashed",color:"#aaaaaa",...n[t]},e)),{})))}}}),s.filter((e=>e)).map((e=>{let{id:n}=e||{};const t=(0,H.mA)(e||{});return(0,B.jsx)(K,{label:t,displayStyle:r[n],handleRemove:m(n),handleChange:c(n)})}))]})};var J=t(43577),X=t(49157),$=t(79091),Z=t(80192);const ee=e=>{var n,t;return null===(n=e.chart)||void 0===n||null===(t=n.integration)||void 0===t?void 0:t.syncEnabled},ne=(0,Z.Mz)((e=>e.chart.integration),(e=>{var n;if(!(null===e||void 0===e?void 0:e.syncEnabled))return{};return((null===e||void 0===e||null===(n=e.params)||void 0===n?void 0:n.columnMap)||[]).reduce(((e,n)=>{if(null===n||void 0===n||!n.selectedValue)return e;return e["".concat(n.model,".").concat(n.fieldId)]=!0,e}),{})}));var te=t(48853);const le=[{value:"inherit",label:"Apply Theme Setting"},{value:"horizontal",label:"Horizontal"},{value:"vertical",label:"Vertical"},{value:"grid",label:"Grid"},{value:"cluster",label:"Cluster"}],re=[{value:"inherit",label:"Apply Theme Setting"},{value:"condensed",label:"Condensed List"},{value:"horizontal",label:"Horizontal"},{value:"vertical",label:"Vertical"},{value:"grid",label:"Grid"}],ie=e=>{var n,t,l,r,o,a,d,s;let{role:u,roleType:p,showDisplayDirection:x}=e;const b=(0,v.wA)(),{closeDialog:A}=(0,c.A)("newRole"),j=(0,v.d4)(ne),{props:{role:y={}}}=(0,c.A)("newRole"),{register:k,watch:R,control:D}=(0,f.xW)(),S=(0,v.d4)(z.LH),M=(0,i.useRef)(null),{userHasMinAccess:L}=(0,te.A)(),E=L(V.td.ADMIN),O=R("role.stackOverrides.direction")||(null===u||void 0===u||null===(n=u.stackOverrides)||void 0===n?void 0:n.direction),N=R("role.sharedStackOverrides.direction")||(null===u||void 0===u||null===(t=u.sharedStackOverrides)||void 0===t?void 0:t.direction),F=S[null===y||void 0===y?void 0:y.id],P=F&&(null===F||void 0===F?void 0:F.bgcolor),W=F&&(null===F||void 0===F?void 0:F.inherited)&&!!P,H=F&&(null===F||void 0===F?void 0:F.isSmartColor);return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Card Styling"}),(0,B.jsxs)(m.A,{mr:2,children:[!j["role.bgcolor"]&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsxs)(g.A,{item:!0,ref:M,children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"thin",children:"Role Color"})}),(0,B.jsx)(f.xI,{name:"role.bgcolor",defaultValue:y.bgcolor||"#ffffff",control:D,render:e=>{let{onChange:n}=e;return(0,B.jsxs)(m.A,{display:"flex",children:[(0,B.jsx)(U.Ay,{initialColor:y.bgcolor||"#ffffff",handleChangeAvatarBackground:e=>{n(e)},showClear:!0}),(H||W)&&(0,B.jsx)(h.Ay,{arrow:!0,title:H?"Color determined from the legend rule":"Color inherited from manager, or further up the company structure",children:(0,B.jsx)(m.A,{height:20,width:20,margin:"13px",children:(0,B.jsx)(T.Ay,{icon:"ColorInherit",size:"lg",color:P})})})]})}})]}),(0,B.jsxs)(g.A,{item:!0,children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"body2",align:"left",children:"Apply Below"})}),(0,B.jsx)(g.A,{component:"label",container:!0,alignItems:"center",spacing:1,children:(0,B.jsx)(g.A,{item:!0,children:(0,B.jsx)(f.xI,{name:"role.propagateBg",defaultValue:y.propagateBg||"on",control:D,render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(J.A,{checked:"on"===t,onChange:e=>{n(e.target.checked?"on":"off")}})}})})})]}),(0,B.jsx)(X.A,{width:90,item:!0,onClick:()=>{b((0,$.wt)({})),A()},children:E&&(0,B.jsx)(g.A,{width:50,component:"label",children:(0,B.jsx)(I.A,{variant:"body2",align:"center",color:"primary",children:"+ Add Smart Legend & Color Rules"})})})]}),(0,B.jsxs)(m.A,{my:1,children:[(0,B.jsx)(I.A,{variant:"body2",align:"left",children:"Nudge Card Down (# of levels)"}),(0,B.jsx)(m.A,{width:150,children:(0,B.jsx)(w.A,{name:"role.dropLevel",fullWidth:!0,defaultValue:y.dropLevel,type:"number",label:"Levels",inputRef:k()})})]}),x&&(0,B.jsx)(f.xI,{as:(0,B.jsx)(w.A,{label:"Display Direction for Direct Reports",fullWidth:!0,select:!0,children:le.map((e=>{let{label:n,value:t}=e;return(0,B.jsx)(C.A,{value:t,children:n},"display-direction-direct-report-".concat(t))}))}),name:"role.stackOverrides.direction",control:D,defaultValue:(null===u||void 0===u||null===(l=u.stackOverrides)||void 0===l?void 0:l.direction)||"inherit"}),["grid","cluster"].includes(O)&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{mr:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.stackOverrides.minColumns",label:"Min Grid Columns",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(r=u.stackOverrides)||void 0===r?void 0:r.minColumns)||3})})}),(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{ml:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.stackOverrides.maxRows",label:"Max Grid Rows",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(o=u.stackOverrides)||void 0===o?void 0:o.maxRows)||3})})})]}),(0,B.jsxs)(m.A,{mt:1,children:["shared"===p&&(0,B.jsx)(f.xI,{as:(0,B.jsx)(w.A,{label:"Display Direction for Shared Roles",fullWidth:!0,select:!0,children:re.map((e=>{let{label:n,value:t}=e;return(0,B.jsx)(C.A,{value:t,children:n},"display-direction-shared-role-".concat(t))}))}),name:"role.sharedStackOverrides.direction",control:D,defaultValue:(null===u||void 0===u||null===(a=u.sharedStackOverrides)||void 0===a?void 0:a.direction)||"inherit"}),"shared"===p&&"grid"===N&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{mr:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.sharedStackOverrides.minColumns",label:"Min Grid Columns",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(d=u.sharedStackOverrides)||void 0===d?void 0:d.minColumns)||3})})}),(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{ml:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.sharedStackOverrides.maxRows",label:"Max Grid Rows",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(s=u.sharedStackOverrides)||void 0===s?void 0:s.maxRows)||3})})})]})]})]})]})};var oe=t(48283),ae=t(50330),de=t(19367),se=t(91357),ce=t(18519);const me={name:!0},he=e=>{let{allowChangeManager:n,roleType:t,role:l,rel:r,chartList:o=[],orgId:a,isAiInsightsView:d=!1}=e;const{register:s,control:h,setValue:u}=(0,f.xW)(),{parent:p,dottedReports:x=[],id:b}=l,A=(0,v.d4)(k.KN),j=(0,v.d4)(ee),y=(0,v.d4)(ne),{openDialog:R}=(0,c.A)("customFields"),D=(0,i.useMemo)((()=>(0,oe.Cs)(t)),[t]),S=/(single|department)/.test(t),M=(0,v.d4)((e=>(0,z.a2)(e,{roleId:b}))),L=(0,v.d4)(de.G0),E=!!b,O=(0,oe.yy)({role:l,chartType:L}),N=(0,oe.zv)({role:l,chartType:L}),F=O||N,T={model:"role",name:"orientation",type:"boolean",displayType:"switch",choices:[],isRestricted:!1,access:"protected",display:"Assistant Position",labelLeft:"Left",labelRight:"Right",themeHidden:!0,importHidden:!0};return(0,B.jsxs)(m.A,{pl:3,pr:2,py:2,children:[(0,B.jsx)(m.A,{my:1,"aria-describedby":"chartFromScratch_tooltip_2",children:(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Role Information"})}),(0,B.jsxs)(m.A,{mr:2,mb:2,children:[A.filter((e=>!e.isDefault||e.isDefault&&D[e.name])).map((e=>(0,B.jsxs)(g.A,{container:!0,children:[S&&e.isDefault&&e.name===H.dj.NAME&&!l.id&&(0,B.jsx)(m.A,{pr:2,children:(0,B.jsx)(X.A,{item:!0,width:70,children:(0,B.jsx)(w.A,{inputRef:s,name:"quantity",defaultValue:1,type:"number",label:"Quantity"})})}),e.name===H.dj.NAME&&e.isDefault&&D.chartLink&&(0,B.jsx)(f.xI,{name:"role.embedded_chart",control:h,rules:{required:"Please select a chart to link"},render:e=>{let{value:n,onChange:t}=e;return(0,B.jsxs)(w.A,{select:!0,value:n,fullWidth:!0,labelId:"new-simple-embedded-chart",label:"Select Chart",color:"primary",onChange:e=>t(e.target.value),children:[(0,B.jsx)(C.A,{value:"",children:(0,B.jsx)("em",{children:"Select Chart"})}),o.map((e=>(0,B.jsx)(C.A,{value:e.id,children:e.name},e.id)))]})}}),(0,B.jsx)(g.A,{item:!0,xs:!0,children:t===ce.T.Embedded&&e.isDefault&&D.chartLink&&e.name===H.dj.NAME?(0,B.jsx)(B.Fragment,{children:(null===l||void 0===l?void 0:l.id)&&(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(e.id)],autoFocus:!0,orgId:a,model:"role",modelId:null===l||void 0===l?void 0:l.id,name:"role.".concat(e.id),disabled:!0,inputRef:s({required:!!me[e.name]}),field:e,value:l[e.id],label:D.title||e.label||e.name})}):(0,B.jsx)(se.j,{field:e,children:(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(e.id)],autoFocus:e.name===H.dj.NAME,orgId:a,model:"role",modelId:null===l||void 0===l?void 0:l.id,name:"role.".concat(e.id),inputRef:s({required:!!me[e.name]}),field:e,value:l[e.id],label:e.isDefault&&e.name===H.dj.NAME?D.title||e.label||e.name:e.isDefault&&e.name===H.dj.DESCRIPTION?D.description||e.label||e.name:e.label||e.name})})})]},"role-input-".concat(e.id)))),"assistant"===t&&(0,B.jsx)(g.A,{item:!0,xs:!0,children:(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(T.name)],name:"role.".concat(T.name),type:T.displayType,field:T,value:l.orientation,label:T.display})}),(0,B.jsx)(m.A,{mt:1,children:(0,B.jsx)(I.A,{variant:"body2",component:"a",onClick:()=>{R({field:{model:"role"}})},"aria-describedby":"chartFromScratch_tooltip_2",children:"Add More Fields\xa0>>"})})]}),!d&&!D.chartLink&&!F&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Connections"})}),(0,B.jsxs)(m.A,{mr:2,mb:2,children:[E&&D.manager&&(0,B.jsx)(m.A,{mb:1,children:(0,B.jsx)(f.xI,{defaultValue:p,control:h,name:"role.parent",render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(q,{readOnly:j,currentParentId:t,roleId:t,handleChange:(e,t)=>{n(e),u("moveHierarchy",t)}})}})}),!E&&n&&(0,B.jsx)(m.A,{mb:1,children:(0,B.jsx)(f.xI,{defaultValue:r,control:h,name:"rel",render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(q,{roleId:null===t||void 0===t?void 0:t.id,currentParentId:null===t||void 0===t?void 0:t.id,readOnly:j,hideHierarchyOption:!0,handleChange:e=>n({id:e,position:"below"})})}})}),D.dottedReports&&(0,B.jsx)(Q,{dottedReports:x})]})]}),(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(ie,{role:l,roleType:t,showDisplayDirection:M})})]})};var ue,pe=t(48655),ge=t(72835),xe=t(71433),ve=t(5387),be=t(8713),Ae=t(56175);const je=W.Ay.div(ue||(ue=(0,r.A)(["\n  display: inline-table;\n  border-radius: 50%;\n  min-width: 30px;\n  height: 30px;\n  border: solid #999 1px;\n"]))),fe=e=>(0,B.jsx)(je,{children:(0,B.jsx)(T.Ay,{...e})});var ye;const Ie=(0,W.Ay)(ge.A)(ye||(ye=(0,r.A)(["\n  padding-left: 4px;\n  .MuiButton-label {\n    justify-content: flex-start;\n  }\n"])));var Ce;const we=W.Ay.div(Ce||(Ce=(0,r.A)(["\n  ","\n"])),(e=>{let{hidden:n}=e;return"\n    transition: height 1s;\n    ".concat(n?"height: 0;":"height: auto;","\n  ")}));var ke=t(43331),Re=t(89656);const De=e=>{let{src:n,badgeBottom:t="0px",badgeRight:l="10px",badgeFontSize:r="14px",badgeColor:i="#005DC9",badgeBgColor:o="#ffffff",badgeOpacity:a="100%",badgeIcon:d="LinkedIn",badgeClickLink:s="#",name:c}=e;const m={card:{position:"relative"},overlay:{position:"absolute",bottom:t,right:l,fontSize:r,color:i,backgroundColor:o,opacity:a,width:r,height:r,lineHeight:r,textAlign:"center",borderRadius:"50%"}},h=(0,v.d4)(Re.YY),u=(0,B.jsx)("a",{href:s,target:"_blank",rel:"noopener noreferrer",children:(0,B.jsx)(T.Ay,{size:"xs",icon:d,color:i})});return(0,B.jsxs)("div",{style:m.card,children:[(0,B.jsx)(O.A,{sizes:"medium",name:c,src:n||h,style:m.media,alt:"Photo of ".concat(c)}),(0,B.jsx)("div",{style:m.overlay,children:u})]})};var Se,Me=t(3342),Le=t(70512),Ee=t(5560),Oe=t(59177);const Ne=(0,W.Ay)(T.Ay)(Se||(Se=(0,r.A)(["\n  cursor: move;\n"]))),Fe=e=>{var n,t,l,r;let{member:o,index:a,handleRemoveMember:d,handleToggleMemberDetails:s,activeOpenIndex:h,isShared:u}=e;const x=(0,v.d4)(ee),b=(0,v.d4)(ne),A=a===h,{openDialog:j}=(0,c.A)("memberPhoto"),{register:y,unregister:C,watch:w,setValue:R,getValues:D,setError:S,clearErrors:M,formState:{isDirty:L,errors:E}}=(0,f.xW)(),{id:F}=o,T=(0,v.d4)(k.gP),z=(0,v.d4)(ke.BF),P=(0,v.d4)(k.gJ),V=null===(n=P[H.x2.FIRSTNAME])||void 0===n?void 0:n.id,W=null===(t=P[H.x2.LASTNAME])||void 0===t?void 0:t.id,_=null===(l=P[H.x2.EMAIL])||void 0===l?void 0:l.id,G=null===(r=P[H.x2.LINKEDIN])||void 0===r?void 0:r.id,q=w("members[".concat(F,"].").concat(V),o[V]||""),Y=w("members[".concat(F,"].").concat(W),o[W]||""),U=w("members[".concat(F,"].").concat(_),o[_]||""),K=w("members[".concat(F,"].newMemberPhoto")),Q=w("members[".concat(F,"].photo"),o.photo||""),J=(0,i.useMemo)((()=>K===Ee.MemberPhotoFieldDeletedValue?null:"string"===typeof K&&K?K:Q),[F,K,Q]),$=q||Y?"".concat(q," ").concat(Y):"< name >",Z=w("members[".concat(F,"].memberPhotoColor")),[te,le]=(0,i.useState)(A);(0,i.useEffect)((()=>{y({name:"members[".concat(F,"].newMemberPhoto")})}),[F]);const re=(e,n)=>t=>{R("members[".concat(F,"].dataChanged"),!0);!Me.A.MemberFieldValidators[e.name]||Me.A.MemberFieldValidators[e.name](t)?M(n):S(n,{message:Me.A.ERROR_MESSAGES[e.name],shouldFocus:!0})},ie=e=>{j({member:"".concat(q," ").concat(Y),handleChangePhoto:oe,handleLinkedInSearch:ce,handleSearchResult:de,type:"photo",mode:e,organization:z,initialAvatarBackgroundColor:Z,photo:o.photo})},oe=e=>{Object.keys(e).forEach((n=>{var t;let l=n;if("newPhoto"===n){if("string"===typeof e[n]&&e[n].length&&e[n].startsWith("/securefiles/"))return;l="newMemberPhoto"}const r=null!==(t=e[n])&&void 0!==t&&t.deleted?Ee.MemberPhotoFieldDeletedValue:e[n];R("members[".concat(F,"].").concat(l),r,{shouldDirty:!0}),R("members[".concat(F,"].dataChanged"),!0)}))},de=(e,n)=>{R("members[".concat(F,"].newMemberPhoto"),e,{shouldDirty:!0}),R("members[".concat(F,"].").concat(G),n,{shouldDirty:!0}),R("members[".concat(F,"].dataChanged"),!0)},ce=(e,n)=>{D("members[".concat(F,"].").concat(V))||D("members[".concat(F,"].").concat(W))||(R("members[".concat(F,"].").concat(V),e),R("members[".concat(F,"].").concat(W),n))};(0,i.useEffect)((()=>(y({name:"members[".concat(F,"].id"),type:"custom"}),y({name:"members[".concat(F,"].photo"),type:"custom"}),y({name:"members[".concat(F,"].").concat(G),type:"custom"}),y({name:"members[".concat(F,"].organization"),type:"custom"}),y({name:"members[".concat(F,"].dataChanged"),type:"custom"}),y({name:"members[".concat(F,"].memberPhotoColor"),type:"custom"}),R("members[".concat(F,"].id"),o.id),R("members[".concat(F,"].photo"),o.photo),R("members[".concat(F,"].").concat(G),o[G]),R("members[".concat(F,"].organization"),o.organization),R("members[".concat(F,"].memberPhotoColor"),o.memberPhotoColor),R("members[".concat(F,"].dataChanged"),L),()=>{C("members[".concat(F,"].id")),C("members[".concat(F,"].organization")),C("members[".concat(F,"].dataChanged")),C("members[".concat(F,"].photo")),C("members[".concat(F,"].memberPhotoColor")),C("members[".concat(F,"].").concat(G))})),[]);const me=o[G]&&Me.A.MemberFieldValidators.linkedIn(o[G]);(0,i.useEffect)((()=>{var e;null!==E&&void 0!==E&&null!==(e=E.members)&&void 0!==e&&e[F]?le(!0):le(a===h)}),[E,a,h]);const he=e=>e.id===V?y({required:!0}):e.id===_?y({pattern:Le.eT}):y;return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsxs)(ve.A,{alignItems:"flex-start",button:!0,component:"a",ContainerComponent:"div",children:[(0,B.jsx)(be.A,{sizes:"medium",children:o[G]&&me?(0,B.jsx)(De,{src:J,name:$,badgeClickLink:o[G]}):(0,B.jsx)(O.A,{alt:"Photo of ".concat($),src:J,name:$,sizes:"medium",overrideColor:Z})}),(0,B.jsx)(N.A,{primary:(0,B.jsx)(I.A,{variant:"body1",children:$}),secondary:(0,B.jsx)(I.A,{variant:"body2",color:"primary",children:(0,Oe.RP)(U,30)})}),u&&(0,B.jsx)(Ae.A,{children:(0,B.jsx)(Ne,{icon:"DragGrid",size:"lg"})})]}),(0,B.jsx)(p.A,{}),(0,B.jsxs)(m.A,{display:"flex",justifyContent:"center",gridGap:8,wrap:"nowrap",children:[(0,B.jsx)(X.A,{item:!0,width:63,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:s(a),children:[(0,B.jsx)(fe,{icon:"Edit"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Edit"})]})}),(0,B.jsx)(X.A,{item:!0,width:78,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:()=>ie("upload"),children:[(0,B.jsx)(fe,{icon:"Camera"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Photo"})]})}),(0,B.jsx)(X.A,{item:!0,width:98,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:()=>ie("search"),children:[(0,B.jsx)(fe,{icon:"LinkedIn"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Connect"})]})}),!x&&(0,B.jsx)(g.A,{item:!0,xs:!0,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:d(a),children:[(0,B.jsx)(fe,{icon:"Remove"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Remove"})]})})]}),(0,B.jsx)(p.A,{}),(0,B.jsx)(we,{hidden:!te,children:(0,B.jsx)(m.A,{p:2,children:T.filter((e=>!e.viewOnly)).map((e=>{var n;return(0,B.jsx)(se.j,{field:e,children:(0,B.jsx)(ae.A,{syncLocked:b["member.".concat(e.id)],orgId:null===z||void 0===z?void 0:z.id,model:"member",modelId:o.id,field:e,inputRef:he(e),onFieldChange:re(e,"members[".concat(F,"].").concat(e.id)),name:"members[".concat(F,"].").concat(e.id),defaultValue:o[e.id],label:e.label,value:o[e.id],fullWidth:!0,error:(null===E||void 0===E?void 0:E.members)&&(null===E||void 0===E?void 0:E.members[F])&&(null===(n=E.members[F][e.id])||void 0===n?void 0:n.message)},"memberFormField_".concat(e.id))})}))})})]})};var Te=t(37259);const ze=e=>{let{roleType:n="department",handleNewMember:t}=e;const l=(()=>{switch(n){case"single":case"shared":case"assistant":return{src:Te.A,text:(0,B.jsxs)("p",{children:["Either ",(0,B.jsx)("strong",{children:"search roster"})," of existing people or"," ",(0,B.jsx)("a",{onClick:t,children:"add a new person"})," to assign one to this role"]}),title:""};case"department":return{src:Te.A,text:"You can display multiple departments on an org chart and color them accordingly.",title:"Department",height:"100%",learnMore:!0};case"location":return{src:Te.A,text:"Display multiple office locations on an org chart and color them accordingly.",title:"Location",height:"100%",learnMore:!0};case"embedded":return{src:Te.A,text:"You can link to another org chart in your account",title:"Link to Another Chart",height:"100%",learnMore:!0};default:return{src:Te.A,text:"You can display multiple departments on an org chart and color them accordingly.",title:"Department",height:"100%"}}})();return(0,B.jsxs)(m.A,{p:4,textAlign:"center",height:l.height||"auto",bgcolor:"grey.100",children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"center",weight:"bold",children:l.title}),(0,B.jsxs)(m.A,{mt:2,children:[(0,B.jsx)(m.A,{my:4,children:(0,B.jsx)("img",{src:l.src,alt:"empty ".concat(n),width:100})}),(0,B.jsx)("p",{children:l.text}),l.learnMore&&(0,B.jsx)("p",{children:(0,B.jsxs)("a",{href:"https://organimi.zendesk.com/hc/en-us/sections/************-Using-Different-Role-Types-in-Organimi-v5",target:"_blank",rel:"noopener noreferrer",children:[(0,B.jsx)(T.Ay,{icon:"Help",size:"sm"})," Learn more about role types"]})})]})]})};var Pe,Ve=t(94341),We=t(26805),He=t.n(We);const _e=W.Ay.div(Pe||(Pe=(0,r.A)(["\n  position: absolute;\n  top: 155px;\n  left: 24px;\n  bottom: 0;\n  right: 24px;\n  overflow: auto;\n  z-index: 2;\n  .MuiAutocomplete-listbox {\n    max-height: none;\n  }\n  .MuiAutocomplete-paper {\n    border: solid 1px #ccc;\n  }\n"]))),Be=e=>{var n;let{orgFields:t=[],roleType:l="single",firstNameFieldId:r="firstName",lastNameFieldId:o="lastName"}=e;const a=(0,v.d4)(ee),{register:d,watch:s,setValue:h}=(0,f.xW)(),{props:{members:u=[],role:p}}=(0,c.A)("newRole"),x=null===p||void 0===p?void 0:p.expectedMemberCount,{openDialog:b}=(0,c.A)("customFields"),A=(0,i.useRef)(null),j="shared"===l,[y,C]=(0,i.useState)(u),[w,k]=(0,i.useState)((()=>u.length&&"shared"!==l?0:-1)),R=(0,H.dP)(t,H.dj.HIREBYDATE),D=(null===p||void 0===p||null===(n=p.fields)||void 0===n?void 0:n.find((e=>e.id===R)))||(null===t||void 0===t?void 0:t.find((e=>e.id===R))),S=/(single|assistant|shared)/.test(l),M=s("quantity")||1,L=s("role.vacant");(0,i.useEffect)((()=>{d("memberIds"),h("memberIds",y.map((e=>e.id)))}),[]),(0,i.useEffect)((()=>{l&&null!==y&&void 0!==y&&y.length&&(!j&&S?(E(null,[...y]),h("memberIds",y.map((e=>e.id)))):S||(E(null,[]),h("memberIds",[])))}),[l]);const E=function(e){let n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(e&&e.key&&("Backspace"===e.key||"Delete"===e.key))return;"shared"===l?(n=null!==t&&void 0!==t&&t.length?t:[...y],k(w+1)):M<t.length?(n=[...[...y].slice(0,M-1),t[t.length-1]],k(0)):(n=t,k(0));const r=n.reduce(((e,n)=>(-1===e.findIndex((e=>e.id===n.id))&&e.push(n),e)),[]);C(r),h("memberIds",r.map((e=>e.id)))},O=()=>{const e=A.current.value||"",[n="",t=""]=(null===e||void 0===e?void 0:e.trim().split(" "))||[];let l=j?[...y]:[];const i={};i[r]=n,i[o]=t,i.isNew=!0,i.id="new"+(new Date).getTime().toString(),l=[i,...l],E(null,l),A.current.focus(),A.current.blur(),h("memberIds",l.map((e=>e.id)))},N=e=>()=>{let n=[];[null,void 0].includes(e)||(n=[...y],n.splice(e,1)),C(n),h("memberIds",n.map((e=>e.id)))},F=e=>()=>{k(e===w?-1:e)};if(!S)return(0,B.jsx)(ze,{roleType:l});const T="shared"===l&&(0,B.jsx)(m.A,{bgcolor:"white",mt:1,children:(0,B.jsx)(pe.A,{defaultValue:x,placeholder:"eg: 5",label:"# of Expected Members (optional)",variant:"outlined",fullWidth:!0,type:"number",size:"small",name:"role.expectedMemberCount",inputRef:d({valueAsNumber:!0})})});return L&&y.length&&N(null)(),(0,B.jsx)(m.A,{py:3,px:2,bgcolor:"grey.100",height:"100%",position:"relative",children:(0,B.jsxs)(m.A,{children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",display:"block",children:"Assign People to Role"}),(0,B.jsx)(m.A,{mt:1,mb:1,children:!a&&(0,B.jsx)(m.A,{"aria-describedby":"chartFromScratch_tooltip_3",children:(0,B.jsxs)(g.A,{container:!0,alignItems:"center",children:[(0,B.jsx)(J.A,{name:"role.vacant",inputRef:d,defaultChecked:L,size:"medium"}),(0,B.jsx)(I.A,{variant:"body1",children:"Role is vacant"})]})})}),L&&(0,B.jsxs)(m.A,{children:[(0,B.jsx)(ae.A,{label:(null===D||void 0===D?void 0:D.label)||"Hire Date",name:"role.".concat(R),value:p[R],field:D}),T]}),!L&&(0,B.jsxs)(B.Fragment,{children:[!a&&(0,B.jsx)(m.A,{"aria-describedby":"practiceChart_tooltip_8",children:(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{xs:!0,item:!0,children:(0,B.jsx)(xe.A,{selected:y,hideTags:!0,handleSelect:E,searchInputRef:A,disableClearable:!0,initOpen:0===y.length,children:e=>{let{children:n}=e;return(0,B.jsx)(_e,{children:n})}})}),(0,B.jsx)(g.A,{item:!0,children:(0,B.jsx)(m.A,{mt:1,pl:2,children:(0,B.jsx)(ge.A,{variant:"contained",color:"secondary",onClick:O,children:"+ New"})})})]})}),0===y.length&&(0,B.jsx)(ze,{roleType:l,handleNewMember:O}),"shared"===l?(0,B.jsx)(Ve.A,{idField:"id",type:"COLOR_RULE",items:y,renderListItem:(e,n)=>(0,B.jsx)(m.A,{mb:2,border:1,borderColor:"grey.400",borderRadius:4,bgcolor:"common.white",children:(0,B.jsx)(Fe,{member:e,index:n,handleRemoveMember:N,handleToggleMemberDetails:F,activeOpenIndex:w,isShared:"shared"===l})},"newRoleMember_".concat((null===e||void 0===e?void 0:e.id)||n)),handleDrop:(e,n)=>{const t=y[e],l=He()(y,{$splice:[[e,1],[n,0,t]]});C(l),h("memberIds",l.map((e=>e.id)))}}):y.map(((e,n)=>(0,B.jsx)(m.A,{mb:2,border:1,borderColor:"grey.400",borderRadius:4,bgcolor:"common.white",children:(0,B.jsx)(Fe,{member:e,index:n,handleRemoveMember:N,handleToggleMemberDetails:F,activeOpenIndex:w,isShared:!1})},"newRoleMember_".concat(e.id||n)))),T,y.length>0&&(0,B.jsx)(I.A,{variant:"body2",component:"a",align:"right",onClick:()=>{b({field:{model:"member"}})},children:"Add More Fields\xa0>>"})]})]})})};var Ge,qe,Ye=t(52906),Ue=t(54762),Ke=t(24115);const Qe=(0,W.Ay)(m.A)(Ge||(Ge=(0,r.A)(["\n  width: 100%;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n  text-align: center;\n  color: #666;\n  background: rgba(255, 155, 0, 0.2);\n  border-top: solid 1px orange;\n  border-bottom: solid 1px orange;\n"]))),Je=(0,W.Ay)(I.A)(qe||(qe=(0,r.A)(["\n  font-size: 14px;\n"]))),Xe=e=>{var n,t,l,r;let{open:I}=e;const C=(0,v.wA)(),{params:{resourceAction:w}}=(0,j.u)([(0,A.si)()]),R=w===V.uI.AI_INSIGHTS,D=(0,v.d4)(ee),{openDialog:S}=(0,c.A)("duplicatePersonFoundSelectableDialog"),{toggleDialog:M,props:{rel:L,role:E={},members:O=[],quantity:N,allowChangeManager:F,isTalentPoolDrop:z}}=(0,c.A)("newRole"),P=(0,f.mN)({defaultValues:{members:O||[],moveHierarchy:!0}}),{handleSubmit:W,setValue:_,register:G,unregister:q}=P,Y=(0,v.d4)(ke.lz),[U,K]=(0,i.useState)(E.type||"single"),{params:{orgId:Q,chartId:J}}=(0,j.u)([(0,A.N9)(),(0,A.si)()]),X=(0,v.d4)(Ue.Pe),$=(0,v.d4)(k.kA),Z=(0,v.d4)(k.gJ),ne=(0,v.d4)(k.rq),te=null===Z||void 0===Z||null===(n=Z.roleName)||void 0===n?void 0:n.id,le=null===Z||void 0===Z||null===(t=Z.firstName)||void 0===t?void 0:t.id,re=null===Z||void 0===Z||null===(l=Z.lastName)||void 0===l?void 0:l.id,ie=(0,H.dP)($,H.dj.HIREBYDATE),oe=null===E||void 0===E||null===(r=E.fields)||void 0===r?void 0:r.find((e=>e.id===ie)),ae=async function(e,n){var t,l,r;let i,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,d=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;if(void 0===(null===(t=e)||void 0===t||null===(l=t.role)||void 0===l?void 0:l[ie])&&(e.role[ie]=(null===oe||void 0===oe?void 0:oe.value)||null),"embedded"===U){const{name:n}=Y.find((n=>n.id===e.role.embedded_chart))||{name:"embedded chart"};e.role[te]=n}if(null!==a&&d){var s,c;const n=null===(s=e)||void 0===s||null===(c=s.memberIds)||void 0===c?void 0:c[a];e.members[n]=d}e=(e=>(e.role&&(e.role=(0,H.YW)(e.role,$.filter((e=>e.model===H.A2.ROLE)))),e.members&&(e.members=Object.entries(e.members).reduce(((e,n)=>{let[t,l]=n;return e[t]=(0,H.YW)(l,$.filter((e=>e.model===H.A2.MEMBER))),e}),{})),e))(e),i=E.id?b.h7.update:b.h7.create;const{error:m,payload:h}=await C(i({orgId:Q,chartId:J,data:{forceDuplicate:o,role:{id:E.id,...e.role,chart:X,type:U},rel:(F?e.rel:L)||{id:"root",position:"below"},members:e.members?(e.memberIds||[]).map((n=>e.members[n])).filter((e=>e)):[],quantity:e.quantity,moveHierarchy:E.parent!==e.role.parent&&e.moveHierarchy,moveOne:E.parent!==e.role.parent&&!e.moveHierarchy}}));null!==h&&void 0!==h&&h.duplicatesFound&&null!==h&&void 0!==h&&null!==(r=h.duplicates)&&void 0!==r&&r.length?S({fields:ne,duplicatePeople:h.duplicates.map(H.SB),action:ae.bind(null,e,null,!0,null===h||void 0===h?void 0:h.duplicatePersonIndex)}):m||M()},[de,se]=(0,y.A)(W(ae,(()=>{}))),ce=/(single|assistant|shared)/.test(U);(0,i.useEffect)((()=>(G("moveHierarchy"),G("role.teamId"),G("role.functionId"),_("moveHierarchy",!0),_("role.teamId",null===E||void 0===E?void 0:E.teamId),_("role.functionId",null===E||void 0===E?void 0:E.functionId),()=>{q("moveHierarchy")})),[]),(0,i.useEffect)((()=>{if(O){var e;const n=O[0]||{},t=(0,Oe.aG)(null===n||void 0===n||null===(e=n.roleDefaults)||void 0===e?void 0:e.title);t&&0===Object.keys(E).length&&(E[te]=t)}}),[O]);const me=()=>{de||M()};return(0,B.jsxs)(a.A,{open:I,onClose:me,scroll:"paper",children:[(0,B.jsx)(o.A,{onClose:me,align:"center",children:E.id?"Update Role":"New Role"}),(0,B.jsx)(m.A,{width:800,p:0,pr:0,pl:0,clone:!0,children:(0,B.jsx)(d.A,{dividers:!0,children:(0,B.jsx)(f.Op,{...P,children:(0,B.jsx)(Ke.A,{loading:de,transparent:!0,zIndex:3,children:(0,B.jsxs)("form",{id:"newRoleForm",onSubmit:se,children:[D&&(0,B.jsxs)(Qe,{children:[(0,B.jsx)(Je,{children:"Some fields are locked with a synced integration and cannot be changed manually"}),(0,B.jsx)(h.Ay,{placement:"below",title:"This chart is integrated and has fields matched to the data fields in the source integration. If you would like to change these field manually, then deselect them in the integration setup",children:(0,B.jsx)(u.A,{size:"small",children:(0,B.jsx)(T.Ay,{icon:"Help"})})})]}),!D&&(0,B.jsx)(m.A,{mb:2,mt:2,children:(0,B.jsx)(Ye.A,{handleClick:e=>()=>{K(e)},selectedType:U,numRows:1,my:0,isAiInsightsView:R})}),!D&&(0,B.jsx)(p.A,{}),(0,B.jsxs)(g.A,{container:!0,children:[(0,B.jsx)(g.A,{item:!0,xs:ce?6:8,children:(0,B.jsx)(he,{orgId:Q,setValue:_,allowChangeManager:F,roleType:U,role:E,rel:L,quantity:N,chartList:Y.filter((e=>e.id!==J)),isAiInsightsView:R})}),(0,B.jsx)(g.A,{item:!0,xs:ce?6:4,children:(0,B.jsx)(Be,{orgFields:$,roleType:U,currentTitle:(0,H.mA)(E),isTalentPoolDrop:z,roleNameFieldId:te,firstNameFieldId:le,lastNameFieldId:re})})]})]})})})})}),(0,B.jsx)(x.A,{children:(0,B.jsxs)(g.A,{container:!0,justifyContent:"center",children:[(0,B.jsx)(m.A,{m:2,children:(0,B.jsx)(s.A,{fullWidth:!0,disabled:de,variant:"outlined",onClick:me,color:"primary",children:"Cancel"})}),(0,B.jsx)(m.A,{m:2,children:(0,B.jsx)(s.A,{fullWidth:!0,disabled:de,variant:"contained",color:"primary",type:"submit",form:"newRoleForm",children:"Save"})})]})})]})}},52906:(e,n,t)=>{t.d(n,{A:()=>v});var l,r,i=t(57528),o=t(72119),a=t(75156),d=t(96364),s=t(40454),c=t(61531),m=t(37294),h=t(70579);const u=(0,o.Ay)(s.A)(l||(l=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:n,disabled:t=!1}=e;return"\n    padding: ".concat(n.spacing(1),"px;\n    border-radius: 30px;\n    background-color: ").concat(t?m.Qs.Neutrals[200]:n.palette.grey[100],";\n    color: ").concat(t?m.Qs.Neutrals[400]:n.palette.grey[400],";\n    height:50px;\n    width: 50px;\n    margin: 0 auto;\n  ")})),p=(0,o.Ay)(s.A)(r||(r=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:n,selected:t,disabled:l}=e;return"\n    border-radius: 8px;\n    text-align: center;\n    cursor: ".concat(l?"not-allowed":"pointer",";\n    ").concat(t&&!l&&"p {\n          color: ".concat(n.palette.info.dark,";\n        }\n        .iconWrapper {\n          background-color: #d6ebf9;\n          color: #4793cf;\n        }"),"\n    ").concat(l&&"\n      p {\n        color: red;\n      }\n      .iconWrapper {\n        color: ".concat(n.palette.grey[400],";\n      }\n    "),"\n    ").concat(!l&&"\n      &:hover p {\n        color: ".concat(n.palette.info.dark,";\n      }\n      &:hover .iconWrapper {\n        color: ").concat(n.palette.info.dark,";\n        background: ").concat(n.palette.info.light,";\n      }\n    "),"\n  ")})),g=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant",separate:!0},{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],x=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant"}],[{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],v=e=>{let{handleClick:n,selectedType:t,numRows:l=1,my:r=2,isAiInsightsView:i=!1}=e;const o=1===l?120:160,v=e=>"department"===e?"practiceChart_tooltip_3":"single"===e?"chartFromScratch_tooltip_6":"";return(1===l?g:x).map(((e,l)=>(0,h.jsx)(c.A,{my:r,children:(0,h.jsx)(s.A,{container:!0,justifyContent:"center",children:e.map((e=>{let{name:l,label:r,icon:s,separate:g,disableWithAI:x}=e;const b=i&&x;return(0,h.jsx)(p,{onClick:b?()=>{}:n(l),item:!0,selected:t===l,"aria-describedby":v(l),disabled:b,children:(0,h.jsxs)(c.A,{width:o,borderColor:"grey.200",border:g?1:0,borderTop:0,borderLeft:0,borderBottom:0,id:"chart_tooltip_".concat(l),children:[(0,h.jsx)(u,{container:!0,className:"iconWrapper",direction:"column",justifyContent:"center",alignItems:"center",disabled:b,children:(0,h.jsx)(a.Ay,{icon:s,size:"lg"})}),(0,h.jsx)(d.A,{variant:"body1",color:b?m.Qs.Neutrals[500]:m.Qs.Neutrals[800],children:r})]})},"roleType".concat(l))}))})},"roleTypeGroup".concat(l))))}},71433:(e,n,t)=>{t.d(n,{A:()=>h});var l=t(65043),r=t(90349),i=t(56070),o=t(55871),a=t(85899),d=t(59177),s=t(49092),c=t(10621),m=t(70579);const h=e=>{let{handleSelect:n,selected:t,label:h="Search People",hideTags:u=!1,placeholder:p="Search by Name",searchInputRef:g=null,disableClearable:x,children:v,initOpen:b=!1,searchOrg:A=null,freeSolo:j=!1,showEmailInSearch:f=!1,requiredAttributesInResult:y=[]}=e;const I={},[C,w]=(0,l.useState)(b),{t:k}=(0,s.B)(),{query:R,people:D,loading:S,totalResults:M,handleInputChange:L,handleLoadMore:E}=(0,a.A)({emptySearch:!0,searchOrg:A}),O=(e,n)=>{"select-option"!==n&&w(!1)};let N=[...D.map((e=>({...e,firstName:(0,c.II)(e),lastName:(0,c.US)(e),email:(0,c.zY)(e)})))];return M>D.length&&N.push({id:"loadMore",firstName:"Load",lastName:"More"}),0===M&&j&&((0,d.B9)(R)?N.push({email:R}):N=[{id:"emailNotValid",firstName:k("General.Text.EmailNotValid")}]),y.length&&(N=N.filter((e=>{for(let n=0;n<y.length;n++)e[y[n]]||"loadMore"===e.id||"emailNotValid"===e.id?e.noEmailFound=!1:e.noEmailFound=!0;return!0}))),v&&"function"===typeof v&&(I.PopperComponent=v),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(r.Ay,{id:"autocomplete-member-search",disableClearable:x,clearOnBlur:!1,fullWidth:!0,freeSolo:j,autoHighlight:!0,open:C,onOpen:()=>{w(!0)},openOnFocus:!1,onClose:O,getOptionLabel:e=>"".concat((0,c.II)(e)," ").concat((0,c.US)(e)),getOptionDisabled:e=>e.noEmailFound,options:N,filterOptions:e=>e,loading:S,multiple:!0,onChange:(e,t)=>{var l;const{id:r}=t[t.length-1]||{},i=(0,c.zY)(t[t.length-1])||(null===(l=t[t.length-1])||void 0===l?void 0:l.email);if("loadMore"===r)return e&&e.stopPropagation(),w(!0),E();(r||i)&&"emailNotValid"!==r?(n(e,t),O()):n(e,t||[])},onInputChange:L,value:t,renderOption:e=>(0,m.jsx)(o.A,{person:e,showEmail:f}),...I,renderInput:e=>(0,m.jsx)(i.A,{...e,inputRef:g,hideTags:u,label:h,placeholder:p,fullWidth:!0})})})}},90694:(e,n,t)=>{t.d(n,{A:()=>b});var l=t(65043),r=t(90349),i=t(56070),o=t(34976),a=t(96364),d=t(40454),s=t(61531),c=t(80539),m=t(10621),h=t(70579);const u=["location","department","embedded"],p=e=>{let{handleClick:n,option:t={role:{members:[]}},vacantText:l}=e;const{role:r={members:[]}}=t||{},{members:i="",type:o}=r,[p]=i||[],{photo:g}=(0,m.LS)(p||{}),x=(0,m.mA)(r),v=(0,m.II)(p),b=(0,m.US)(p),A=v||b?"".concat(v||""," ").concat(b||""):l,j=!u.includes(o);return(0,h.jsxs)(d.A,{container:!0,onClick:n,alignContent:"center",alignItems:"center",children:[(0,h.jsx)(s.A,{mr:2,clone:!0,children:(0,h.jsx)(c.A,{width:30,height:30,name:j?A:x,src:g})}),(0,h.jsxs)(d.A,{item:!0,xs:!0,children:[j?(0,h.jsx)(a.A,{children:A}):null,(0,h.jsx)(a.A,{children:x})]})]})};var g=t(14556),x=t(89656),v=t(7743);const b=e=>{var n;let{hideInputValue:t,disableClearable:a,handleSelect:d,selected:s,label:c="Search Roles",placeholder:u="Search position or person",multiple:b=!0,chartId:A,orgId:j}=e;const[f,y]=(0,l.useState)(!1),{chartOptions:{vacantText:I}}=(0,g.d4)(x.P0),C=(0,g.d4)(v.gJ),w=null===C||void 0===C||null===(n=C.roleName)||void 0===n?void 0:n.id,{query:k,results:R,loading:D,handleInputChange:S}=(0,o.A)({emptySearch:!0,model:"role",fieldId:w,defaultChartId:A,defaultOrgId:j}),M=b&&t?()=>{}:null;return(0,h.jsx)(r.Ay,{id:"autocomplete-role-search",disableClearable:a,renderTags:M,fullWidth:!0,open:f,defaultValue:s,onOpen:()=>{y(!0)},onClose:()=>{y(!1)},getOptionLabel:e=>"".concat((0,m.mA)(e.role)),options:R,filterOptions:e=>e,noOptionsText:k?"No roles found":"Start typing to ".concat(c.toLowerCase()),loading:D,multiple:b,renderOption:e=>(0,h.jsx)(p,{handleClick:d(e),option:e,vacantText:I}),renderInput:e=>(0,h.jsx)(i.A,{...e,onChange:e=>S(e.target.value),label:c,placeholder:u,fullWidth:!0})})}},48283:(e,n,t)=>{t.d(n,{Cs:()=>r,yy:()=>i,zv:()=>o});var l=t(78396);function r(e){let n={title:"Role Title *",description:"Role Description",name:!0,color:!0,propagateBg:!0,location:!1,locationAddress:!0,chartLink:!1,dottedReports:!0,manager:!0,dropLevel:!0,leftRight:!1,customFields:!0,smartLegendLink:!0};switch(e){case"assistant":n.orientation=!0,n.dropLevel=!1;break;case"location":n.description=!1,n.dottedReports=!1,n.locationAddress=!0,n.title="Location Name",n.description="Location Description";break;case"embedded":n.chartLink=!0,n.dottedReports=!1,n.location=!1,n.locationAddress=!1,n.dottedReport=!1,n.title="Linked Chart",n.description=!1,n.propagateBg=!1,n.manager=!1,n.dottedReports=!1,n.dropLevel=!1,n.customFields=!1,n.smartLegendLink=!1;break;case"department":n.title="Department Title *",n.description="Department Description",n.locationAddress=!1}return n}function i(e){let{role:n,chartType:t}=e;return t===l.XD.MATRIX&&!(null!==n&&void 0!==n&&n.parent)&&!(null!==n&&void 0!==n&&n.type)!==l.mv.HIDDEN&&!n.functionId&&!n.teamId}function o(e){let{role:n,chartType:t}=e;return t===l.XD.MATRIX&&[l.mv.TEAM,l.mv.FUNCTION,l.mv.HIDDEN].includes(n.type)}},34976:(e,n,t)=>{t.d(n,{A:()=>m});var l=t(65043),r=t(61),i=t(86255),o=t(66856),a=t(14556),d=t(36138),s=t(19367),c=t(48283);const m=e=>{let{model:n,fieldId:t,defaultChartId:m,defaultOrgId:h}=e;const u=(0,a.wA)(),[p,g]=(0,l.useState)(!1),[x,v]=(0,l.useState)([]),[b,A]=(0,l.useState)(""),j=(0,a.d4)(s.G0),{params:{orgId:f,chartId:y}}=(0,d.u)([(0,o.si)(),(0,o.K7)()]),I=y||m,C=f||h,{eventDebounce:w}=(0,i.A)(),k=w((e=>{A(e)}),500),R=(0,l.useCallback)((e=>{v([]),g(!0),k(e)}),[]),D=(0,l.useMemo)((()=>{if("role"===n)return r.JP.findRoles;throw new Error("Search not yet handled")}),[n]);return(0,l.useEffect)((()=>{b?(async()=>{const{payload:e}=await u(D({orgId:C,chartId:I,query:b,fields:t}));g(!1);const n=((null===e||void 0===e?void 0:e.results)||[]).filter((e=>{const n=(0,c.yy)({role:e.role,chartType:j}),t=(0,c.zv)({role:e.role,chartType:j});return!(n||t)}));v(n)})():(v([]),g(!1))}),[b]),{query:b,results:x,loading:p,handleInputChange:R}}},23851:(e,n,t)=>{t.d(n,{A:()=>v});var l=t(98587),r=t(58168),i=t(65043),o=t(69292),a=t(68606),d=t(51347),s=t(34535),c=t(72876),m=t(57056),h=t(32400);function u(e){return(0,h.Ay)("MuiListItemAvatar",e)}(0,m.A)("MuiListItemAvatar",["root","alignItemsFlexStart"]);var p=t(70579);const g=["className"],x=(0,s.Ay)("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(e,n)=>{const{ownerState:t}=e;return[n.root,"flex-start"===t.alignItems&&n.alignItemsFlexStart]}})((e=>{let{ownerState:n}=e;return(0,r.default)({minWidth:56,flexShrink:0},"flex-start"===n.alignItems&&{marginTop:8})})),v=i.forwardRef((function(e,n){const t=(0,c.A)({props:e,name:"MuiListItemAvatar"}),{className:s}=t,m=(0,l.default)(t,g),h=i.useContext(d.A),v=(0,r.default)({},t,{alignItems:h.alignItems}),b=(e=>{const{alignItems:n,classes:t}=e,l={root:["root","flex-start"===n&&"alignItemsFlexStart"]};return(0,a.A)(l,u,t)})(v);return(0,p.jsx)(x,(0,r.default)({className:(0,o.A)(b.root,s),ownerState:v,ref:n},m))}))}}]);
//# sourceMappingURL=3760.92ca97a3.chunk.js.map