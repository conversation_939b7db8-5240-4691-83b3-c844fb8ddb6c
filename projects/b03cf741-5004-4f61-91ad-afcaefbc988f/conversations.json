{"5ad06fb6-5a40-46e9-ab3e-3b1c420f72a2": [{"role": "user", "content": "How does the error handling work?", "timestamp": 1753982235.777576, "metadata": {}}, {"role": "assistant", "content": "**Quick Summary**: \nThe codebase implements error handling through a combination of try-catch patterns, error status checks, and user notifications using a toast/alert system. Errors are handled both for async operations and UI interactions.\n\n**Key Code Sections**:\n\nThe following function handles print job cancellation with error handling:\n```javascript\nconst I = async n => {\n  s({\n    title: \"Cancel Print\",\n    message: \"Are you sure you want to cancel this print?\",\n    execFunc: async () => {\n      const {error: l} = await i(Fe.kv.cancelPrintJob({orgId: e, chartId: t, jobId: n}));\n      l ? r(\"Failed to cancel print job\", \"error\") \n        : (r(\"Print job cancelled\", \"success\"), j());\n    },\n    cancelButtonText: \"No\",\n    confirmButtonText: \"Yes\",\n    cancelFunc: () => {}\n  });\n}\n```\n- Uses a confirmation dialog before action\n- Handles API errors through destructured error object\n- Provides user feedback through toast notifications for both success/failure\n\nThe following function handles custom field refresh errors:\n```javascript\nconst p = async () => {\n  u(!0);  // Set loading state\n  const {error: t} = await e(Ue.wz.refreshDynamicFieldValues({orgId: s, chartId: d}));\n  if (t) {\n    a(\"Error occurred during custom fields computation.\", \"error\", 3e3, {\n      vertical: \"top\", horizontal: \"center\"\n    });\n    u(!1);\n    return;\n  }\n  // Success path\n  await e(Ue.te.refreshRoles({orgId: s, chartId: d}));\n  a(\"Custom fields values refreshed.\", \"success\", 3e3, {\n    vertical: \"top\", horizontal: \"center\"\n  });\n  u(!1);\n}\n```\n- Implements loading state management\n- Uses early return pattern for error cases\n- Provides positioned toast notifications\n\n**Analysis**:\n- Error handling follows a consistent pattern using destructured {error} objects\n- Uses a centralized notification system (likely toast/alerts) via function 'r' or 'a'\n- Implements user confirmation for destructive actions\n- Includes loading state management for async operations\n- Provides specific error messages for different failure scenarios\n\n**Recommendations**:\n1. Consider implementing error retry logic for transient failures\n2. Add error logging/tracking for better monitoring\n3. Standardize error message duration (varies between 2000ms and 3000ms)\n4. Consider implementing global error boundary for unhandled exceptions\n5. Add error codes for better error categorization and handling", "timestamp": 1753982250.2807071, "metadata": {"sources": [{"rank": 1, "chunk_id": "enhanced_chunk_271", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.4836466312408447, "preview": "\",2e3):r(\"File name updated successfully\",\"success\",2e3)},I=async n=>{s({title:\"Cancel Print\",message:\"Are you sure you want to cancel this print?\",execFunc:async()=>{const{error:l}=await i(Fe.kv.cancelPrintJob({orgId:e,chartId:t,jobId:n}));l?r(\"Failed to cancel print job\",\"error\"):(r(\"Print job cancelled\",\"success\"),j())},cancelButtonText:\"No\",confirmButtonText:\"Yes\",cancelFunc:()=>{}})},w=async ...", "full_content": "\",2e3):r(\"File name updated successfully\",\"success\",2e3)},I=async n=>{s({title:\"Cancel Print\",message:\"Are you sure you want to cancel this print?\",execFunc:async()=>{const{error:l}=await i(Fe.kv.cancelPrintJob({orgId:e,chartId:t,jobId:n}));l?r(\"Failed to cancel print job\",\"error\"):(r(\"Print job cancelled\",\"success\"),j())},cancelButtonText:\"No\",confirmButtonText:\"Yes\",cancelFunc:()=>{}})},w=async e=>{await j(),console.log(\"Job completed\",e),i((0,Fe.io)({fileCode:e})),await A()},"}, {"rank": 2, "chunk_id": "enhanced_chunk_269", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.512467622756958, "preview": "Id:n,saveFile:!0}));l?r(\"Failed to save file\",\"error\",2e3):r(\"File saved successfully\",\"success\",2e3)},b=async n=>{s({title:\"Delete File\",message:\"Are you sure you want to delete this file?\",execFunc:async()=>{const{error:l}=await i(Fe.kv.deletePrintHistory({orgId:e,chartId:t,historyId:n}));l?r(\"Failed to delete file\",\"error\",2e3):r(\"File deleted successfully\",\"success\",2e3)},cancelFunc:()=>{}})},...", "full_content": "Id:n,saveFile:!0}));l?r(\"Failed to save file\",\"error\",2e3):r(\"File saved successfully\",\"success\",2e3)},b=async n=>{s({title:\"Delete File\",message:\"Are you sure you want to delete this file?\",execFunc:async()=>{const{error:l}=await i(Fe.kv.deletePrintHistory({orgId:e,chartId:t,historyId:n}));l?r(\"Failed to delete file\",\"error\",2e3):r(\"File deleted successfully\",\"success\",2e3)},cancelFunc:()=>{}})},y=async i=>{const n=\"/organizations/\".concat(e,\"/charts/\").concat(t,\""}, {"rank": 3, "chunk_id": "enhanced_chunk_652", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.525148868560791, "preview": ".A)(),{orgId:s,chartId:d}=t||{},[c,u]=(0,l.useState)(!1),p=async()=>{u(!0);const{error:t}=await e(Ue.wz.refreshDynamicFieldValues({orgId:s,chartId:d}));if(t)return a(\"Error occurred during custom fields computation.\",\"error\",3e3,{vertical:\"top\",horizontal:\"center\"}),void u(!1);await e(Ue.te.refreshRoles({orgId:s,chartId:d})),a(\"Custom fields values refreshed.\",\"success\",3e3,{vertical:\"top\",horizon...", "full_content": ".A)(),{orgId:s,chartId:d}=t||{},[c,u]=(0,l.useState)(!1),p=async()=>{u(!0);const{error:t}=await e(Ue.wz.refreshDynamicFieldValues({orgId:s,chartId:d}));if(t)return a(\"Error occurred during custom fields computation.\",\"error\",3e3,{vertical:\"top\",horizontal:\"center\"}),void u(!1);await e(Ue.te.refreshRoles({orgId:s,chartId:d})),a(\"Custom fields values refreshed.\",\"success\",3e3,{vertical:\"top\",horizontal:\"center\"}),u(!1)};return(0,l.useEffect)((()=>{o&&i&&n"}, {"rank": 4, "chunk_id": "enhanced_chunk_176", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.525882601737976, "preview": "),(0,ie.jsx)(Oe.OV,{variant:\"contained\",onClick:async()=>{o(!0);const{error:n}=await e(Fe.kv.addPrintJob({orgId:t,chartId:i,type:a.fileType,data:{...a,pageCount:c,tableOfContentPageCount:j,tocRowIndexPageNumberMap:h}}));n?p({title:\"Error\",message:\"Error sending job to printer, please try again.\"}):await e(Fe.kv.getInProgressPrintJobs({orgId:t,chartId:i})),o(!1)},disabled:s,children:\"Print to File\"...", "full_content": "),(0,ie.jsx)(Oe.OV,{variant:\"contained\",onClick:async()=>{o(!0);const{error:n}=await e(Fe.kv.addPrintJob({orgId:t,chartId:i,type:a.fileType,data:{...a,pageCount:c,tableOfContentPageCount:j,tocRowIndexPageNumberMap:h}}));n?p({title:\"Error\",message:\"Error sending job to printer, please try again.\"}):await e(Fe.kv.getInProgressPrintJobs({orgId:t,chartId:i})),o(!1)},disabled:s,children:\"Print to File\"})]})};var Ze=i(90889),et=i(39948),tt=i(53193),it=i"}, {"rank": 5, "chunk_id": "enhanced_chunk_634", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.5347907543182373, "preview": "ayload:t}=await r(Ot.R0.getJobStatus({jobId:m.id}));if(e)return void console.log(e);const{data:{jobStatus:n,status:i}}=t;i===Ut.A8.FAILED?D(Jt.M.UNKNOWN):i===Ut.A8.DEQUEUED&&y.current<1800?(y.current++,C(n||n.curPage?\"File almost ready for download...\":\"Generating page \"+Math.min(n.curPage,n.numPages)+\" of \"+n.numPages+\"...\")):i!==Ut.A8.DEQUEUED&&i!==Ut.A8.COMPLETE?(y.current++,y.current<60?C(n||n...", "full_content": "ayload:t}=await r(Ot.R0.getJobStatus({jobId:m.id}));if(e)return void console.log(e);const{data:{jobStatus:n,status:i}}=t;i===Ut.A8.FAILED?D(Jt.M.UNKNOWN):i===Ut.A8.DEQUEUED&&y.current<1800?(y.current++,C(n||n.curPage?\"File almost ready for download...\":\"Generating page \"+Math.min(n.curPage,n.numPages)+\" of \"+n.numPages+\"...\")):i!==Ut.A8.DEQUEUED&&i!==Ut.A8.COMPLETE?(y.current++,y.current<60?C(n||n.curPage?\"Connecting to secure channel...\":\"Generating Page \"+Math.min"}], "model": "claude-3-5-sonnet-20241022", "tokens": 2212}}, {"role": "user", "content": "Includes loading state management for async operations - explain how this works", "timestamp": 1753982586.4306638, "metadata": {}}, {"role": "assistant", "content": "**Quick Summary**: \nThe code implements an async job status polling system for file generation/printing, with loading states, error handling, and user feedback through status messages.\n\n**Key Code Sections**:\n\nThe following function handles job status polling:\n```javascript\n(async () => {\n  const {error: e, payload: t} = await j(h.R0.getJobStatus({jobId: n.id}));\n  if (e) return void console.log(e);\n  const {data: {jobStatus: i, status: o}} = t;\n  \n  if (o === u.A8.FAILED) {\n    N(v.UNKNOWN);\n  } else if (o === u.A8.DEQUEUED && C.current < 1800) {\n    C.current++;\n    I(i || i.curPage \n      ? \"File almost ready for download...\"\n      : \"Generating page \" + Math.min(i.curPage, i.numPages) + \" of \" + i.numPages + \"...\");\n  }\n  // ... (truncated for brevity)\n```\n\nLine-by-line explanation:\n- Sets up an interval to poll job status every 1.5 seconds\n- Makes API call to check job status using jobId\n- Handles different status responses (FAILED, DEQUEUED, COMPLETE)\n- Updates loading message based on current page progress\n- Tracks retry attempts with C.current counter\n- Clears interval when job completes or fails\n\n**Analysis**:\n- Uses setInterval for continuous polling (every 1500ms)\n- Implements a retry counter (C.current) with max attempts (1800)\n- Provides progressive feedback about page generation\n- Handles multiple job states: FAILED, DEQUEUED, COMPLETE\n- Automatically cleans up interval on completion or component unmount\n\n**Recommendations**:\n1. Add error retry logic with exponential backoff\n2. Implement timeout handling for long-running jobs\n3. Consider using WebSocket instead of polling for real-time updates\n4. Add loading state persistence for better UX during page refreshes", "timestamp": 1753982604.765842, "metadata": {"sources": [{"rank": 1, "chunk_id": "enhanced_chunk_746", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.2406724691390991, "preview": "(n.id&&(b.current=setInterval((()=>{(async()=>{const{error:e,payload:t}=await j(h.R0.getJobStatus({jobId:n.id}));if(e)return void console.log(e);const{data:{jobStatus:i,status:o}}=t;o===u.A8.FAILED?N(v.UNKNOWN):o===u.A8.DEQUEUED&&C.current<1800?(C.current++,I(i||i.curPage?\"File almost ready for download...\":\"Generating page \"+Math.min(i.curPage,i.numPages)+\" of \"+i.numPages+\"...\")):o!==u.A8.DEQUEU...", "full_content": "(n.id&&(b.current=setInterval((()=>{(async()=>{const{error:e,payload:t}=await j(h.R0.getJobStatus({jobId:n.id}));if(e)return void console.log(e);const{data:{jobStatus:i,status:o}}=t;o===u.A8.FAILED?N(v.UNKNOWN):o===u.A8.DEQUEUED&&C.current<1800?(C.current++,I(i||i.curPage?\"File almost ready for download...\":\"Generating page \"+Math.min(i.curPage,i.numPages)+\" of \"+i.numPages+\"...\")):o!==u.A8.DEQUEUED&&o!==u.A8.COMPLETE?(C.current++,C.current<60?I(i"}, {"rank": 2, "chunk_id": "enhanced_chunk_632", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.2958542108535767, "preview": ",I]=(0,Zt.A)((async()=>{g({title:\"Regenerate Print\",message:\"Are you sure you want to regenerate this print?\",execFunc:async()=>{const{error:e,payload:i}=await r(Ot.kv.addPrintJobPublic({orgId:p,chartId:c,publicId:n,historyId:t.id,printTemplate:t.jobDetails.publicPrintTemplateId,isPublicPrint:!0}));e||x(null===i||void 0===i?void 0:i.currentRequested)}})})),E=async()=>{C(\"Getting file download link...", "full_content": ",I]=(0,Zt.A)((async()=>{g({title:\"Regenerate Print\",message:\"Are you sure you want to regenerate this print?\",execFunc:async()=>{const{error:e,payload:i}=await r(Ot.kv.addPrintJobPublic({orgId:p,chartId:c,publicId:n,historyId:t.id,printTemplate:t.jobDetails.publicPrintTemplateId,isPublicPrint:!0}));e||x(null===i||void 0===i?void 0:i.currentRequested)}})})),E=async()=>{C(\"Getting file download link...\"),o(),x(null)},D=e=>{clearInterval(f.current),y.current=0,r(Ot.kv.cancelPrintJob({org"}, {"rank": 3, "chunk_id": "enhanced_chunk_646", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.3089609146118164, "preview": "}=e;const{chartId:i,orgId:o}=(0,h.useParams)(),r=(0,Lt.A)().pId,s=(0,M.wA)(),{downloadFile:c}=(0,Mt.A)(),[p,m]=(0,l.useState)(!0),[x,g]=(0,l.useState)([]);async function A(){var e;const{payload:t,error:n}=await s(Ot.kv.getPublicPrintFiles({chartId:i,orgId:o,pId:r}));if(m(!1),n)return;const l=(null===(e=(null===t||void 0===t?void 0:t.printHistory)||[])||void 0===e?void", "full_content": "}=e;const{chartId:i,orgId:o}=(0,h.useParams)(),r=(0,Lt.A)().pId,s=(0,M.wA)(),{downloadFile:c}=(0,Mt.A)(),[p,m]=(0,l.useState)(!0),[x,g]=(0,l.useState)([]);async function A(){var e;const{payload:t,error:n}=await s(Ot.kv.getPublicPrintFiles({chartId:i,orgId:o,pId:r}));if(m(!1),n)return;const l=(null===(e=(null===t||void 0===t?void 0:t.printHistory)||[])||void 0===e?void"}, {"rank": 4, "chunk_id": "enhanced_chunk_553", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.3126790523529053, "preview": "\"===e.model&&\"name\"===e.name&&e.isDefault)))||void 0===t?void 0:t.searchable;(0,l.useEffect)((()=>{X(null,0)}),[n]);const oe=e=>async()=>{const{role:{id:t}}=e;var n;te&&y((0,Q.gN)({activePersonId:null===(n=e.member)||void 0===n?void 0:n.id,selectedRoleId:t,mode:\"view\"}));await y((0,D.mO)({roleId:t})),o()},re=e=>async t=>{t&&t.stopPropagation(),y((0,S.EJ)(e)),o()},se=e=>async t", "full_content": "\"===e.model&&\"name\"===e.name&&e.isDefault)))||void 0===t?void 0:t.searchable;(0,l.useEffect)((()=>{X(null,0)}),[n]);const oe=e=>async()=>{const{role:{id:t}}=e;var n;te&&y((0,Q.gN)({activePersonId:null===(n=e.member)||void 0===n?void 0:n.id,selectedRoleId:t,mode:\"view\"}));await y((0,D.mO)({roleId:t})),o()},re=e=>async t=>{t&&t.stopPropagation(),y((0,S.EJ)(e)),o()},se=e=>async t"}, {"rank": 5, "chunk_id": "enhanced_chunk_747", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.315750241279602, "preview": "...\")):o!==u.A8.DEQUEUED&&o!==u.A8.COMPLETE?(C.current++,C.current<60?I(i||i.curPage?\"Connecting to secure channel...\":\"Generating Page \"+Math.min(i.curPage,i.numPages)+\" of \"+i.numPages+\"...\"):N(v.CONNECTION)):o===u.A8.COMPLETE?(clearInterval(b.current),setTimeout((async()=>{await k()}),5e3),console.log(k)):C.current>=1800&&N(v.UNKNOWN)})()}),1500)),()=>{clearInterval(b.current)})),[n.id]);return...", "full_content": "...\")):o!==u.A8.DEQUEUED&&o!==u.A8.COMPLETE?(C.current++,C.current<60?I(i||i.curPage?\"Connecting to secure channel...\":\"Generating Page \"+Math.min(i.curPage,i.numPages)+\" of \"+i.numPages+\"...\"):N(v.CONNECTION)):o===u.A8.COMPLETE?(clearInterval(b.current),setTimeout((async()=>{await k()}),5e3),console.log(k)):C.current>=1800&&N(v.UNKNOWN)})()}),1500)),()=>{clearInterval(b.current)})),[n.id]);return(0,A.jsxs)(o.A,{variant:\"outlined\",sx:{padding:\"10px 8px 10px "}], "model": "claude-3-5-sonnet-20241022", "tokens": 2134}}], "42deb9ef-b825-4953-9c92-3051e6dfc6a6": [{"role": "user", "content": "Explain the main functions in this codebase", "timestamp": 1753983690.5727308, "metadata": {}}, {"role": "assistant", "content": "Let me analyze these code chunks and provide a structured response.\n\n**Quick Summary**: \nThe codebase appears to be a React application with functionality for managing boards, search features, and UI positioning. The code uses JSX and includes state management.\n\n**Key Code Sections**:\n\n1. The following function handles search functionality:\n```javascript\nconst O = !t || a ? \"auto\" : 125;\nconst L = C && (\"default\" === n && !!((null === u || void 0 === u ? void 0 : u.length) > 0 \n    || Object.keys(p).length) || \"advanced\" === n);\nreturn (0,b.jsx)(ue, {\n    open: L,\n    query: u,\n    advancedQuery: p,\n    handleClose: D,\n    searchType: n,\n    handleCloseAdvancedSearch: R,\n    handleAdvancedSearchClick: N,\n    handleAdvancedSearch: F,\n})\n```\n- Determines search component visibility based on state conditions\n- Manages both default and advanced search modes\n- Renders a search UI component with various handlers\n\n2. The following function defines position constants and enums:\n```javascript\nlet l = function(e) {\n    return e.Add = \"add\",\n    e.AddFirst = \"addFirst\",\n    e.Edit = \"edit\",\n    e.Empty = \"empty\",\n    e.List = \"list\",\n    e\n}({})\n```\n- Defines an enum for different view states\n- Used for managing UI component rendering modes\n\n**Analysis**:\n- The codebase implements a component-based architecture\n- Uses React hooks (useEffect, useMemo) for state and side-effect management\n- Implements conditional rendering based on state variables\n- Handles null checks extensively using optional chaining\n\n**Recommendations**:\n1. Consider implementing TypeScript to reduce null checking boilerplate\n2. Consolidate position-related logic into a dedicated hook or utility\n3. Add error boundaries around main components\n4. Consider using a state management library for complex state interactions\n\nNote: Some context appears to be missing as there are references to undefined variables and components. A full analysis would require additional code chunks.", "timestamp": 1753983704.2758641, "metadata": {"sources": [{"rank": 1, "chunk_id": "enhanced_chunk_507", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.3498215675354004, "preview": "?(0,m.jsx)(y,{boardOfDirectors:null===i||void 0===i?void 0:i.governance<PERSON>hart}):(0,m.jsx)(Q,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}),(0,m.jsx)(l.A,{}),\"list\"===e?(0,m.jsx)(me,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}):(0,m.jsx)(ie,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart})]})]})}},10047:(e,t,n)=>{n.d(t", "full_content": "?(0,m.jsx)(y,{boardOfDirectors:null===i||void 0===i?void 0:i.governance<PERSON>hart}):(0,m.jsx)(Q,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}),(0,m.jsx)(l.A,{}),\"list\"===e?(0,m.jsx)(me,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart}):(0,m.jsx)(ie,{boardOfDirectors:null===i||void 0===i?void 0:i.governanceChart})]})]})}},10047:(e,t,n)=>{n.d(t"}, {"rank": 2, "chunk_id": "enhanced_chunk_736", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.4325603246688843, "preview": "left:void 0,top:m.N9,bottom:void 0,right:m.N9};if(\"bottomLeft\"===n.position)return{left:m.N9,top:void 0,bottom:m.N9,right:void 0};if(\"bottomRight\"===n.position)return{left:void 0,top:void 0,bottom:m.N9,right:m.N9}}return f?{left:m.F0,top:m.Vy,bottom:void 0,right:void 0}:{left:m.N9,top:m.N9,bottom:0,right:0}}),[j,n]),C=(0,i.useMemo)((()=>(null===y||void 0===y?void 0:y.resourceAction)!==a.uI.THEME&&", "full_content": "left:void 0,top:m.N9,bottom:void 0,right:m.N9};if(\"bottomLeft\"===n.position)return{left:m.N9,top:void 0,bottom:m.N9,right:void 0};if(\"bottomRight\"===n.position)return{left:void 0,top:void 0,bottom:m.N9,right:m.N9}}return f?{left:m.F0,top:m.Vy,bottom:void 0,right:void 0}:{left:m.N9,top:m.N9,bottom:0,right:0}}),[j,n]),C=(0,i.useMemo)((()=>(null===y||void 0===y?void 0:y.resourceAction)!==a.uI.THEME&&"}, {"rank": 3, "chunk_id": "enhanced_chunk_547", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.455681562423706, "preview": ")=>{g(),m(t),i(\"default\")},P=()=>{f((0,S.F8)()),f((0,ce.cl)())};(0,l.useEffect)((()=>(C||P(),()=>{P()})),[C]);const O=!t||a?\"auto\":125,L=C&&(\"default\"===n&&!!((null===u||void 0===u?void 0:u.length)>0||Object.keys(p).length)||\"advanced\"===n);return(0,b.jsx)(ue,{open:L,query:u,advancedQuery:p,handleClose:D,searchType:n,handleCloseAdvancedSearch:R,handleAdvancedSearchClick:N,handleAdvancedSearch:F,", "full_content": ")=>{g(),m(t),i(\"default\")},P=()=>{f((0,S.F8)()),f((0,ce.cl)())};(0,l.useEffect)((()=>(C||P(),()=>{P()})),[C]);const O=!t||a?\"auto\":125,L=C&&(\"default\"===n&&!!((null===u||void 0===u?void 0:u.length)>0||Object.keys(p).length)||\"advanced\"===n);return(0,b.jsx)(ue,{open:L,query:u,advancedQuery:p,handleClose:D,searchType:n,handleCloseAdvancedSearch:R,handleAdvancedSearchClick:N,handleAdvancedSearch:F,"}, {"rank": 4, "chunk_id": "enhanced_chunk_726", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.4561281204223633, "preview": "[700]})})]})]}),L]})};n(91812),n(97631)},99085:(e,t,n)=>{n.d(t,{DC:()=>p,F0:()=>A,FH:()=>u,N9:()=>x,Ve:()=>s,Vy:()=>g,dX:()=>l,fG:()=>h,rh:()=>d,ux:()=>a,wK:()=>m,z5:()=>c});var i=n(37294),o=n(10621),r=n(47088);let l=function(e){return e.Add=\"add\",e.AddFirst=\"addFirst\",e.Edit=\"edit\",e.Empty=\"empty\",e.List=\"list\",e}({", "full_content": "[700]})})]})]}),L]})};n(91812),n(97631)},99085:(e,t,n)=>{n.d(t,{DC:()=>p,F0:()=>A,FH:()=>u,N9:()=>x,Ve:()=>s,Vy:()=>g,dX:()=>l,fG:()=>h,rh:()=>d,ux:()=>a,wK:()=>m,z5:()=>c});var i=n(37294),o=n(10621),r=n(47088);let l=function(e){return e.Add=\"add\",e.AddFirst=\"addFirst\",e.Edit=\"edit\",e.Empty=\"empty\",e.List=\"list\",e}({"}, {"rank": 5, "chunk_id": "enhanced_chunk_698", "file_path": "unknown", "token_count": 150, "char_count": 0, "distance": 1.471213936805725, "preview": "{if(!g)if(n===Z.Manage)w([null===e||void 0===e?void 0:e.id]),null===o||void 0===o||o(null===e||void 0===e?void 0:e.id);else if(n===Z.Display){b.includes(null===e||void 0===e?void 0:e.id)?null===c||void 0===c||c(null===e||void 0===e?void 0:e.id):null===l||void 0===l||l(null===e||void 0===e?void 0:e.id),w((t=>t.includes(null===e||void 0===", "full_content": "{if(!g)if(n===Z.Manage)w([null===e||void 0===e?void 0:e.id]),null===o||void 0===o||o(null===e||void 0===e?void 0:e.id);else if(n===Z.Display){b.includes(null===e||void 0===e?void 0:e.id)?null===c||void 0===c||c(null===e||void 0===e?void 0:e.id):null===l||void 0===l||l(null===e||void 0===e?void 0:e.id),w((t=>t.includes(null===e||void 0==="}], "model": "claude-3-5-sonnet-20241022", "tokens": 2106}}]}