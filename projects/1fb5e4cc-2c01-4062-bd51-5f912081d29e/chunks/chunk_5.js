"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[3760],{31510:(e,n,t)=>{t.d(n,{A:()=>a});var l,r=t(57528),i=t(72119),o=t(43867);const a=(0,i.Ay)(o.A)(l||(l=(0,r.A)(["\n  &.MuiDialogContent-dividers {\n    padding: 0 0 0;\n  }\n"])))},55871:(e,n,t)=>{t.d(n,{A:()=>c});var l=t(40454),r=t(61531),i=t(80539),o=t(75156),a=t(96364),d=t(59177),s=t(70579);const c=e=>{let{person:n,showEmail:t=!1}=e;const{firstName:c="",lastName:m="",photo:h,id:u,email:p,roleDefaults:g}=n;let x=Boolean(c)||Boolean(m)?"".concat(c," ").concat(m):p;const v="loadMore"===u,b="emailNotValid"===u,A=!v&&!b&&t;return(0,s.jsxs)(l.A,{container:!0,alignContent:"center",alignItems:"center",children:[(0,s.jsxs)(r.A,{mr:2,children:[v&&(0,s.jsx)(r.A,{children:(0,s.jsx)(o.Ay,{icon:"LoadMore",size:"x2"})}),!v&&b&&(0,s.jsx)(r.A,{children:(0,s.jsx)(o.Ay,{icon:"ExclamationTriangle",size:"x2"})}),!v&&!b&&(0,s.jsx)(i.A,{width:30,height:30,src:h,name:x,overrideColor:null===n||void 0===n?void 0:n.memberPhotoColor})]}),(0,s.jsxs)(l.A,{item:!0,xs:!0,children:[(0,s.jsx)(a.A,{display:"inline",children:"".concat(c," ").concat(m," ")}),(c||m)&&p&&t&&"| ",t&&Boolean(p)?(0,s.jsx)(a.A,{display:"inline",children:p}):A&&(0,s.jsx)(r.A,{display:"inline",children:(0,s.jsx)(a.A,{fontStyle:"italic",display:"inline",children:"No email found."})}),(0,d.aG)(null===g||void 0===g?void 0:g.title)&&(0,s.jsx)(a.A,{children:null!==g&&void 0!==g&&g.title?g.title:""})]})]})}},94341:(e,n,t)=>{t.d(n,{A:()=>h});var l=t(65043),r=t(93865),i=t(18858);const o={FIELD:"field",TOP_ROLE_ID:"topRole",COLOR_RULE:"colorRule",THEME_FIELD:"themeField",PAGE_BREAK_ROLES:"pageBreakRoles",PICK_LIST_ITEM:"pickListItem"},a=e=>{let{handleDrop:n,handleDrag:t,index:a,type:d,disabled:s}=e;const c=(0,l.useRef)(null),[{isOver:m},h]=(0,r.H)({accept:o[d],drop(e){if(!c.current)return;const t=e.initIndex;n(t,a)},hover(e){if(!c.current)return;const n=e.index,l=a;n!==l&&(t(n,l),e.index=l)},collect:e=>({isOver:e.isOver()})}),[{isDragging:u},p]=(0,i.i)({item:{type:o[d],index:a,initIndex:a},collect:e=>({isDragging:e.isDragging()})});return!s&&p(h(c)),{ref:c,isOver:m,isDragging:u}};var d=t(26805),s=t.n(d),c=t(70579);const m=e=>{let{children:n,index:t,handleDrop:l,handleDrag:r,style:i,dragStyle:o,overStyle:d,disabled:s=!1,type:m}=e;i=i||{};const{ref:h,isDragging:u,isOver:p}=a({type:m,handleDrop:l,handleDrag:r,index:t,disabled:s}),g=u?.2:1;return i=u?o||{}:p&&d||{},(0,c.jsx)("div",{ref:h,style:{...i,opacity:g},children:n})},h=e=>{let{idField:n,getIdField:t,type:r,items:i=[],renderListItem:o,handleDrop:a}=e;const[d,h]=(0,l.useState)(i);if("function"!==typeof o)return null;const u=(0,l.useCallback)(((e,n)=>{const t=d[e];h(s()(d,{$splice:[[e,1],[n,0,t]]}))}),[d]);return(0,l.useEffect)((()=>{h(i)}),[i]),d.map(((e,l)=>{const i=n||"function"===typeof t&&t(e);return(0,c.jsx)(m,{index:l,type:r,handleDrop:a,handleDrag:u,children:"function"===typeof o&&o(e,l)},"draggable-".concat(r,"-").concat(e[i||"id"]||l))}))}},53760:(e,n,t)=>{t.r(n),t.d(n,{default:()=>Xe});var l,r=t(57528),i=t(65043),o=t(5816),a=t(76031),d=t(31510),s=t(74593),c=t(84),m=t(61531),h=t(9579),u=t(17339),p=t(5571),g=t(40454),x=t(52907),v=t(14556),b=t(43862),A=t(66856),j=t(36138),f=t(61258),y=t(2173),I=t(96364),C=t(55357),w=t(84866),k=t(7743),R=t(172),D=t(96446),S=t(6803),M=t(23851),L=t(77739),E=t(8266),O=t(80539),N=t(61071),F=t(70318),T=t(75156),z=t(23993),P=t(45418),V=t(78396),W=t(72119),H=t(10621),_=t(26225),B=t(70579);const G=(0,W.Ay)(D.A)(l||(l=(0,r.A)(["\n  position: absolute;\n  width: 100%;\n  background: rgba(220, 220, 220, 0.3);\n  padding: 8px;\n  z-index: 999;\n  top: 0;\n  bottom: 0;\n  left: 0;\n"]))),q=e=>{var n,t,l,r;let{roleId:o,handleChange:a,hideHierarchyOption:d,currentParentId:s,readOnly:m}=e;const h=(0,v.d4)((e=>(0,z.gn)(e.roles,o))),u=(0,v.d4)((e=>(0,_.p4)(e,o))),p=(0,v.d4)((e=>(0,P.Yk)(e,{ids:(null===h||void 0===h?void 0:h.members)||[]}))),g=(0,v.d4)((e=>(0,P.Yk)(e,{ids:(null===u||void 0===u?void 0:u.members)||[]})));let x=!1;(null===u||void 0===u?void 0:u.type)===V.mv.HIDDEN&&(x=!0);const b=(0,i.useMemo)((()=>{let e="";const n=g.map((e=>e.name)).join(", ");if(e=n.length>40?"".concat(n,"..."):n,!e){var t,l;const n=null===u||void 0===u||null===(t=u.fields)||void 0===t||null===(l=t.find((e=>"name"===e.name)))||void 0===l?void 0:l.id;e=(null===u||void 0===u?void 0:u[n])||""}return e}),[g,u]),A=(0,i.useMemo)((()=>{let e="";const n=p.map((e=>e.name)).join(", ");if(e=n.length>40?"".concat(n,"..."):n,!e){var t,l;const n=null===h||void 0===h||null===(t=h.fields)||void 0===t||null===(l=t.find((e=>"name"===e.name)))||void 0===l?void 0:l.id;e=(null===h||void 0===h?void 0:h[n])||""}return e}),[p,h]),j=(e,n)=>{a(e,n)},{openDialog:f}=(0,c.A)("changeManager",{onSelected:j}),y=()=>{f({handleChange:j,hideHierarchyOption:d,currentParentId:s,roleId:o})},C=()=>{j(null)},w="department"===(null===h||void 0===h?void 0:h.type)||"location"===(null===h||void 0===h?void 0:h.type);return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(D.A,{mb:2,children:w&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(D.A,{children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"regular",children:(0,S.A)(null===h||void 0===h?void 0:h.type)})}),(0,B.jsxs)(R.A,{button:!0,alignItems:"center",ContainerComponent:"div",onClick:!m&&y,children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(M.A,{sizes:"medium",children:(0,B.jsx)(O.A,{name:A,overrideColor:null===(n=p[0])||void 0===n?void 0:n.memberPhotoColor,src:null===(t=p[0])||void 0===t?void 0:t.photo})}),(0,B.jsx)(N.A,{primary:s&&(null===h||void 0===h?void 0:h.type)!==V.mv.HIDDEN?(0,B.jsx)(I.A,{variant:"body1",children:(0,H.mA)(h||"manager")}):(0,B.jsx)(I.A,{color:"primary",children:m?"no manager":"select manager"})}),m&&(0,B.jsx)(L.A,{title:"Relationship is currently synced to a third party integration. Disable your sync to allow manual changes to this field",placement:"bottom",arrow:!0,children:(0,B.jsx)(G,{children:(0,B.jsx)(E.A,{children:(0,B.jsx)(T.gF,{icon:"Lock",size:"lg"})})})}),!m&&(0,B.jsxs)(E.A,{children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(D.A,{mr:1,display:"inline-block",children:(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:C,size:"small",children:(0,B.jsx)(T.gF,{icon:"Close",size:"lg"})})}),(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:y,size:"small",children:(0,B.jsx)(T.gF,{icon:"Edit",size:"sm"})})]})]})]})}),(0,B.jsx)(D.A,{children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"regular",children:"Manager"})}),(0,B.jsxs)(R.A,{button:!0,alignItems:"center",ContainerComponent:"div",onClick:!m&&y,children:[s&&!x&&u&&(0,B.jsx)(M.A,{sizes:"medium",children:(0,B.jsx)(O.A,{name:b,overrideColor:null===(l=g[0])||void 0===l?void 0:l.memberPhotoColor,src:null===(r=g[0])||void 0===r?void 0:r.photo})}),(0,B.jsx)(N.A,{primary:s&&!x&&u?(0,B.jsx)(I.A,{variant:"body1",children:(0,H.mA)(u||"manager")}):(0,B.jsx)(I.A,{color:"primary",children:m?"no manager":"select manager"}),secondary:(0,B.jsx)(I.A,{variant:"body2",color:"primary",children:b})}),m&&(0,B.jsx)(L.A,{title:"Relationship is currently synced to a third party integration. Disable your sync to allow manual changes to this field",placement:"bottom",arrow:!0,children:(0,B.jsx)(G,{children:(0,B.jsx)(E.A,{children:(0,B.jsx)(T.gF,{icon:"Lock",size:"lg"})})})}),!m&&!w&&(0,B.jsxs)(E.A,{children:[s&&!((null===h||void 0===h?void 0:h.type)===V.mv.HIDDEN)&&(0,B.jsx)(D.A,{mr:1,display:"inline-block",children:(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:C,size:"small",children:(0,B.jsx)(T.gF,{icon:"Close",size:"lg"})})}),(0,B.jsx)(F.A,{edge:"end","aria-label":"edit manager",onClick:y,size:"small",children:(0,B.jsx)(T.gF,{icon:"Edit",size:"sm"})})]})]})]})};var Y=t(90694),U=t(96942);const K=e=>{let{displayStyle:n,handleChange:t,handleRemove:l,label:r=""}=e;return(0,B.jsxs)(m.A,{display:"flex",gridGap:16,alignItems:"center",flexWrap:"nowrap",children:[(0,B.jsxs)(m.A,{flex:1,display:"flex",gridGap:!0,alignItems:"center",children:[(0,B.jsx)(m.A,{onClick:l,display:"inline",mr:2,children:(0,B.jsx)(T.Ay,{icon:"Remove"})}),(0,B.jsx)(I.A,{variant:"body1",children:r})]}),(0,B.jsx)(U.Ay,{initialColor:n.color,handleChangeAvatarBackground:e=>{t({target:{value:e,name:"color"}})}}),(0,B.jsx)(m.A,{width:80,clone:!0,children:(0,B.jsxs)(w.A,{select:!0,name:"style",label:"style",defaultValue:n.style,onChange:t,size:"small",children:[(0,B.jsx)(C.A,{value:"dashed",children:"- - - -"},"dottedStyle-dashed"),(0,B.jsx)(C.A,{value:"dotted",children:".........."},"dottedStyle-dotted"),(0,B.jsx)(C.A,{value:"solid",children:(0,B.jsx)("hr",{style:{width:50}})},"dottedStyle-solid")]})})]})},Q=e=>{let{dottedReports:n=[]}=e;const[t,l]=(0,i.useState)(n.filter((e=>e)).map((e=>e.roleId))),[r,o]=(0,i.useState)(n.filter((e=>e)).reduce(((e,n)=>(e[n.roleId]={...n},e)),{})),{setValue:a,register:d}=(0,f.xW)(),s=(0,v.d4)((e=>(0,z.uN)(e,{roleIds:t})));(0,i.useEffect)((()=>{d({name:"role.dottedReports",type:"custom"})}),[]),(0,i.useEffect)((()=>{const e=t.map((e=>({...r[e],roleId:e})));a("role.dottedReports",e)}),[r]);const c=e=>n=>{const{name:t,value:l}=n.target;o((n=>({...n,[e]:{...n[e],[t]:l}})))},m=e=>()=>{l((n=>{const t=[...n],l=t.indexOf(e);return-1!==l&&t.splice(l,1),t})),o((n=>{const t={...n};return t[e]=null,delete t[e],t}))};return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(Y.A,{disableClearable:!0,hideInputValue:!0,label:"Search for Additional Reports",handleSelect:e=>{let{role:{id:n}}=e;return()=>{let e=[...t],r=e.indexOf(n);-1!==r?e.splice(r,1):e.push(n),l(e),o((n=>e.reduce(((e,t)=>(e[t]={style:"dashed",color:"#aaaaaa",...n[t]},e)),{})))}}}),s.filter((e=>e)).map((e=>{let{id:n}=e||{};const t=(0,H.mA)(e||{});return(0,B.jsx)(K,{label:t,displayStyle:r[n],handleRemove:m(n),handleChange:c(n)})}))]})};var J=t(43577),X=t(49157),$=t(79091),Z=t(80192);const ee=e=>{var n,t;return null===(n=e.chart)||void 0===n||null===(t=n.integration)||void 0===t?void 0:t.syncEnabled},ne=(0,Z.Mz)((e=>e.chart.integration),(e=>{var n;if(!(null===e||void 0===e?void 0:e.syncEnabled))return{};return((null===e||void 0===e||null===(n=e.params)||void 0===n?void 0:n.columnMap)||[]).reduce(((e,n)=>{if(null===n||void 0===n||!n.selectedValue)return e;return e["".concat(n.model,".").concat(n.fieldId)]=!0,e}),{})}));var te=t(48853);const le=[{value:"inherit",label:"Apply Theme Setting"},{value:"horizontal",label:"Horizontal"},{value:"vertical",label:"Vertical"},{value:"grid",label:"Grid"},{value:"cluster",label:"Cluster"}],re=[{value:"inherit",label:"Apply Theme Setting"},{value:"condensed",label:"Condensed List"},{value:"horizontal",label:"Horizontal"},{value:"vertical",label:"Vertical"},{value:"grid",label:"Grid"}],ie=e=>{var n,t,l,r,o,a,d,s;let{role:u,roleType:p,showDisplayDirection:x}=e;const b=(0,v.wA)(),{closeDialog:A}=(0,c.A)("newRole"),j=(0,v.d4)(ne),{props:{role:y={}}}=(0,c.A)("newRole"),{register:k,watch:R,control:D}=(0,f.xW)(),S=(0,v.d4)(z.LH),M=(0,i.useRef)(null),{userHasMinAccess:L}=(0,te.A)(),E=L(V.td.ADMIN),O=R("role.stackOverrides.direction")||(null===u||void 0===u||null===(n=u.stackOverrides)||void 0===n?void 0:n.direction),N=R("role.sharedStackOverrides.direction")||(null===u||void 0===u||null===(t=u.sharedStackOverrides)||void 0===t?void 0:t.direction),F=S[null===y||void 0===y?void 0:y.id],P=F&&(null===F||void 0===F?void 0:F.bgcolor),W=F&&(null===F||void 0===F?void 0:F.inherited)&&!!P,H=F&&(null===F||void 0===F?void 0:F.isSmartColor);return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Card Styling"}),(0,B.jsxs)(m.A,{mr:2,children:[!j["role.bgcolor"]&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsxs)(g.A,{item:!0,ref:M,children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"body2",align:"left",weight:"thin",children:"Role Color"})}),(0,B.jsx)(f.xI,{name:"role.bgcolor",defaultValue:y.bgcolor||"#ffffff",control:D,render:e=>{let{onChange:n}=e;return(0,B.jsxs)(m.A,{display:"flex",children:[(0,B.jsx)(U.Ay,{initialColor:y.bgcolor||"#ffffff",handleChangeAvatarBackground:e=>{n(e)},showClear:!0}),(H||W)&&(0,B.jsx)(h.Ay,{arrow:!0,title:H?"Color determined from the legend rule":"Color inherited from manager, or further up the company structure",children:(0,B.jsx)(m.A,{height:20,width:20,margin:"13px",children:(0,B.jsx)(T.Ay,{icon:"ColorInherit",size:"lg",color:P})})})]})}})]}),(0,B.jsxs)(g.A,{item:!0,children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"body2",align:"left",children:"Apply Below"})}),(0,B.jsx)(g.A,{component:"label",container:!0,alignItems:"center",spacing:1,children:(0,B.jsx)(g.A,{item:!0,children:(0,B.jsx)(f.xI,{name:"role.propagateBg",defaultValue:y.propagateBg||"on",control:D,render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(J.A,{checked:"on"===t,onChange:e=>{n(e.target.checked?"on":"off")}})}})})})]}),(0,B.jsx)(X.A,{width:90,item:!0,onClick:()=>{b((0,$.wt)({})),A()},children:E&&(0,B.jsx)(g.A,{width:50,component:"label",children:(0,B.jsx)(I.A,{variant:"body2",align:"center",color:"primary",children:"+ Add Smart Legend & Color Rules"})})})]}),(0,B.jsxs)(m.A,{my:1,children:[(0,B.jsx)(I.A,{variant:"body2",align:"left",children:"Nudge Card Down (# of levels)"}),(0,B.jsx)(m.A,{width:150,children:(0,B.jsx)(w.A,{name:"role.dropLevel",fullWidth:!0,defaultValue:y.dropLevel,type:"number",label:"Levels",inputRef:k()})})]}),x&&(0,B.jsx)(f.xI,{as:(0,B.jsx)(w.A,{label:"Display Direction for Direct Reports",fullWidth:!0,select:!0,children:le.map((e=>{let{label:n,value:t}=e;return(0,B.jsx)(C.A,{value:t,children:n},"display-direction-direct-report-".concat(t))}))}),name:"role.stackOverrides.direction",control:D,defaultValue:(null===u||void 0===u||null===(l=u.stackOverrides)||void 0===l?void 0:l.direction)||"inherit"}),["grid","cluster"].includes(O)&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{mr:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.stackOverrides.minColumns",label:"Min Grid Columns",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(r=u.stackOverrides)||void 0===r?void 0:r.minColumns)||3})})}),(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{ml:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.stackOverrides.maxRows",label:"Max Grid Rows",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(o=u.stackOverrides)||void 0===o?void 0:o.maxRows)||3})})})]}),(0,B.jsxs)(m.A,{mt:1,children:["shared"===p&&(0,B.jsx)(f.xI,{as:(0,B.jsx)(w.A,{label:"Display Direction for Shared Roles",fullWidth:!0,select:!0,children:re.map((e=>{let{label:n,value:t}=e;return(0,B.jsx)(C.A,{value:t,children:n},"display-direction-shared-role-".concat(t))}))}),name:"role.sharedStackOverrides.direction",control:D,defaultValue:(null===u||void 0===u||null===(a=u.sharedStackOverrides)||void 0===a?void 0:a.direction)||"inherit"}),"shared"===p&&"grid"===N&&(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{mr:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.sharedStackOverrides.minColumns",label:"Min Grid Columns",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(d=u.sharedStackOverrides)||void 0===d?void 0:d.minColumns)||3})})}),(0,B.jsx)(g.A,{item:!0,xs:6,children:(0,B.jsx)(m.A,{ml:1,children:(0,B.jsx)(w.A,{inputRef:k,name:"role.sharedStackOverrides.maxRows",label:"Max Grid Rows",type:"number",step:"1",min:"1",defaultValue:(null===u||void 0===u||null===(s=u.sharedStackOverrides)||void 0===s?void 0:s.maxRows)||3})})})]})]})]})]})};var oe=t(48283),ae=t(50330),de=t(19367),se=t(91357),ce=t(18519);const me={name:!0},he=e=>{let{allowChangeManager:n,roleType:t,role:l,rel:r,chartList:o=[],orgId:a,isAiInsightsView:d=!1}=e;const{register:s,control:h,setValue:u}=(0,f.xW)(),{parent:p,dottedReports:x=[],id:b}=l,A=(0,v.d4)(k.KN),j=(0,v.d4)(ee),y=(0,v.d4)(ne),{openDialog:R}=(0,c.A)("customFields"),D=(0,i.useMemo)((()=>(0,oe.Cs)(t)),[t]),S=/(single|department)/.test(t),M=(0,v.d4)((e=>(0,z.a2)(e,{roleId:b}))),L=(0,v.d4)(de.G0),E=!!b,O=(0,oe.yy)({role:l,chartType:L}),N=(0,oe.zv)({role:l,chartType:L}),F=O||N,T={model:"role",name:"orientation",type:"boolean",displayType:"switch",choices:[],isRestricted:!1,access:"protected",display:"Assistant Position",labelLeft:"Left",labelRight:"Right",themeHidden:!0,importHidden:!0};return(0,B.jsxs)(m.A,{pl:3,pr:2,py:2,children:[(0,B.jsx)(m.A,{my:1,"aria-describedby":"chartFromScratch_tooltip_2",children:(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Role Information"})}),(0,B.jsxs)(m.A,{mr:2,mb:2,children:[A.filter((e=>!e.isDefault||e.isDefault&&D[e.name])).map((e=>(0,B.jsxs)(g.A,{container:!0,children:[S&&e.isDefault&&e.name===H.dj.NAME&&!l.id&&(0,B.jsx)(m.A,{pr:2,children:(0,B.jsx)(X.A,{item:!0,width:70,children:(0,B.jsx)(w.A,{inputRef:s,name:"quantity",defaultValue:1,type:"number",label:"Quantity"})})}),e.name===H.dj.NAME&&e.isDefault&&D.chartLink&&(0,B.jsx)(f.xI,{name:"role.embedded_chart",control:h,rules:{required:"Please select a chart to link"},render:e=>{let{value:n,onChange:t}=e;return(0,B.jsxs)(w.A,{select:!0,value:n,fullWidth:!0,labelId:"new-simple-embedded-chart",label:"Select Chart",color:"primary",onChange:e=>t(e.target.value),children:[(0,B.jsx)(C.A,{value:"",children:(0,B.jsx)("em",{children:"Select Chart"})}),o.map((e=>(0,B.jsx)(C.A,{value:e.id,children:e.name},e.id)))]})}}),(0,B.jsx)(g.A,{item:!0,xs:!0,children:t===ce.T.Embedded&&e.isDefault&&D.chartLink&&e.name===H.dj.NAME?(0,B.jsx)(B.Fragment,{children:(null===l||void 0===l?void 0:l.id)&&(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(e.id)],autoFocus:!0,orgId:a,model:"role",modelId:null===l||void 0===l?void 0:l.id,name:"role.".concat(e.id),disabled:!0,inputRef:s({required:!!me[e.name]}),field:e,value:l[e.id],label:D.title||e.label||e.name})}):(0,B.jsx)(se.j,{field:e,children:(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(e.id)],autoFocus:e.name===H.dj.NAME,orgId:a,model:"role",modelId:null===l||void 0===l?void 0:l.id,name:"role.".concat(e.id),inputRef:s({required:!!me[e.name]}),field:e,value:l[e.id],label:e.isDefault&&e.name===H.dj.NAME?D.title||e.label||e.name:e.isDefault&&e.name===H.dj.DESCRIPTION?D.description||e.label||e.name:e.label||e.name})})})]},"role-input-".concat(e.id)))),"assistant"===t&&(0,B.jsx)(g.A,{item:!0,xs:!0,children:(0,B.jsx)(ae.A,{syncLocked:y["role.".concat(T.name)],name:"role.".concat(T.name),type:T.displayType,field:T,value:l.orientation,label:T.display})}),(0,B.jsx)(m.A,{mt:1,children:(0,B.jsx)(I.A,{variant:"body2",component:"a",onClick:()=>{R({field:{model:"role"}})},"aria-describedby":"chartFromScratch_tooltip_2",children:"Add More Fields\xa0>>"})})]}),!d&&!D.chartLink&&!F&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",children:"Connections"})}),(0,B.jsxs)(m.A,{mr:2,mb:2,children:[E&&D.manager&&(0,B.jsx)(m.A,{mb:1,children:(0,B.jsx)(f.xI,{defaultValue:p,control:h,name:"role.parent",render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(q,{readOnly:j,currentParentId:t,roleId:t,handleChange:(e,t)=>{n(e),u("moveHierarchy",t)}})}})}),!E&&n&&(0,B.jsx)(m.A,{mb:1,children:(0,B.jsx)(f.xI,{defaultValue:r,control:h,name:"rel",render:e=>{let{onChange:n,value:t}=e;return(0,B.jsx)(q,{roleId:null===t||void 0===t?void 0:t.id,currentParentId:null===t||void 0===t?void 0:t.id,readOnly:j,hideHierarchyOption:!0,handleChange:e=>n({id:e,position:"below"})})}})}),D.dottedReports&&(0,B.jsx)(Q,{dottedReports:x})]})]}),(0,B.jsx)(m.A,{my:1,children:(0,B.jsx)(ie,{role:l,roleType:t,showDisplayDirection:M})})]})};var ue,pe=t(48655),ge=t(72835),xe=t(71433),ve=t(5387),be=t(8713),Ae=t(56175);const je=W.Ay.div(ue||(ue=(0,r.A)(["\n  display: inline-table;\n  border-radius: 50%;\n  min-width: 30px;\n  height: 30px;\n  border: solid #999 1px;\n"]))),fe=e=>(0,B.jsx)(je,{children:(0,B.jsx)(T.Ay,{...e})});var ye;const Ie=(0,W.Ay)(ge.A)(ye||(ye=(0,r.A)(["\n  padding-left: 4px;\n  .MuiButton-label {\n    justify-content: flex-start;\n  }\n"])));var Ce;const we=W.Ay.div(Ce||(Ce=(0,r.A)(["\n  ","\n"])),(e=>{let{hidden:n}=e;return"\n    transition: height 1s;\n    ".concat(n?"height: 0;":"height: auto;","\n  ")}));var ke=t(43331),Re=t(89656);const De=e=>{let{src:n,badgeBottom:t="0px",badgeRight:l="10px",badgeFontSize:r="14px",badgeColor:i="#005DC9",badgeBgColor:o="#ffffff",badgeOpacity:a="100%",badgeIcon:d="LinkedIn",badgeClickLink:s="#",name:c}=e;const m={card:{position:"relative"},overlay:{position:"absolute",bottom:t,right:l,fontSize:r,color:i,backgroundColor:o,opacity:a,width:r,height:r,lineHeight:r,textAlign:"center",borderRadius:"50%"}},h=(0,v.d4)(Re.YY),u=(0,B.jsx)("a",{href:s,target:"_blank",rel:"noopener noreferrer",children:(0,B.jsx)(T.Ay,{size:"xs",icon:d,color:i})});return(0,B.jsxs)("div",{style:m.card,children:[(0,B.jsx)(O.A,{sizes:"medium",name:c,src:n||h,style:m.media,alt:"Photo of ".concat(c)}),(0,B.jsx)("div",{style:m.overlay,children:u})]})};var Se,Me=t(3342),Le=t(70512),Ee=t(5560),Oe=t(59177);const Ne=(0,W.Ay)(T.Ay)(Se||(Se=(0,r.A)(["\n  cursor: move;\n"]))),Fe=e=>{var n,t,l,r;let{member:o,index:a,handleRemoveMember:d,handleToggleMemberDetails:s,activeOpenIndex:h,isShared:u}=e;const x=(0,v.d4)(ee),b=(0,v.d4)(ne),A=a===h,{openDialog:j}=(0,c.A)("memberPhoto"),{register:y,unregister:C,watch:w,setValue:R,getValues:D,setError:S,clearErrors:M,formState:{isDirty:L,errors:E}}=(0,f.xW)(),{id:F}=o,T=(0,v.d4)(k.gP),z=(0,v.d4)(ke.BF),P=(0,v.d4)(k.gJ),V=null===(n=P[H.x2.FIRSTNAME])||void 0===n?void 0:n.id,W=null===(t=P[H.x2.LASTNAME])||void 0===t?void 0:t.id,_=null===(l=P[H.x2.EMAIL])||void 0===l?void 0:l.id,G=null===(r=P[H.x2.LINKEDIN])||void 0===r?void 0:r.id,q=w("members[".concat(F,"].").concat(V),o[V]||""),Y=w("members[".concat(F,"].").concat(W),o[W]||""),U=w("members[".concat(F,"].").concat(_),o[_]||""),K=w("members[".concat(F,"].newMemberPhoto")),Q=w("members[".concat(F,"].photo"),o.photo||""),J=(0,i.useMemo)((()=>K===Ee.MemberPhotoFieldDeletedValue?null:"string"===typeof K&&K?K:Q),[F,K,Q]),$=q||Y?"".concat(q," ").concat(Y):"< name >",Z=w("members[".concat(F,"].memberPhotoColor")),[te,le]=(0,i.useState)(A);(0,i.useEffect)((()=>{y({name:"members[".concat(F,"].newMemberPhoto")})}),[F]);const re=(e,n)=>t=>{R("members[".concat(F,"].dataChanged"),!0);!Me.A.MemberFieldValidators[e.name]||Me.A.MemberFieldValidators[e.name](t)?M(n):S(n,{message:Me.A.ERROR_MESSAGES[e.name],shouldFocus:!0})},ie=e=>{j({member:"".concat(q," ").concat(Y),handleChangePhoto:oe,handleLinkedInSearch:ce,handleSearchResult:de,type:"photo",mode:e,organization:z,initialAvatarBackgroundColor:Z,photo:o.photo})},oe=e=>{Object.keys(e).forEach((n=>{var t;let l=n;if("newPhoto"===n){if("string"===typeof e[n]&&e[n].length&&e[n].startsWith("/securefiles/"))return;l="newMemberPhoto"}const r=null!==(t=e[n])&&void 0!==t&&t.deleted?Ee.MemberPhotoFieldDeletedValue:e[n];R("members[".concat(F,"].").concat(l),r,{shouldDirty:!0}),R("members[".concat(F,"].dataChanged"),!0)}))},de=(e,n)=>{R("members[".concat(F,"].newMemberPhoto"),e,{shouldDirty:!0}),R("members[".concat(F,"].").concat(G),n,{shouldDirty:!0}),R("members[".concat(F,"].dataChanged"),!0)},ce=(e,n)=>{D("members[".concat(F,"].").concat(V))||D("members[".concat(F,"].").concat(W))||(R("members[".concat(F,"].").concat(V),e),R("members[".concat(F,"].").concat(W),n))};(0,i.useEffect)((()=>(y({name:"members[".concat(F,"].id"),type:"custom"}),y({name:"members[".concat(F,"].photo"),type:"custom"}),y({name:"members[".concat(F,"].").concat(G),type:"custom"}),y({name:"members[".concat(F,"].organization"),type:"custom"}),y({name:"members[".concat(F,"].dataChanged"),type:"custom"}),y({name:"members[".concat(F,"].memberPhotoColor"),type:"custom"}),R("members[".concat(F,"].id"),o.id),R("members[".concat(F,"].photo"),o.photo),R("members[".concat(F,"].").concat(G),o[G]),R("members[".concat(F,"].organization"),o.organization),R("members[".concat(F,"].memberPhotoColor"),o.memberPhotoColor),R("members[".concat(F,"].dataChanged"),L),()=>{C("members[".concat(F,"].id")),C("members[".concat(F,"].organization")),C("members[".concat(F,"].dataChanged")),C("members[".concat(F,"].photo")),C("members[".concat(F,"].memberPhotoColor")),C("members[".concat(F,"].").concat(G))})),[]);const me=o[G]&&Me.A.MemberFieldValidators.linkedIn(o[G]);(0,i.useEffect)((()=>{var e;null!==E&&void 0!==E&&null!==(e=E.members)&&void 0!==e&&e[F]?le(!0):le(a===h)}),[E,a,h]);const he=e=>e.id===V?y({required:!0}):e.id===_?y({pattern:Le.eT}):y;return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsxs)(ve.A,{alignItems:"flex-start",button:!0,component:"a",ContainerComponent:"div",children:[(0,B.jsx)(be.A,{sizes:"medium",children:o[G]&&me?(0,B.jsx)(De,{src:J,name:$,badgeClickLink:o[G]}):(0,B.jsx)(O.A,{alt:"Photo of ".concat($),src:J,name:$,sizes:"medium",overrideColor:Z})}),(0,B.jsx)(N.A,{primary:(0,B.jsx)(I.A,{variant:"body1",children:$}),secondary:(0,B.jsx)(I.A,{variant:"body2",color:"primary",children:(0,Oe.RP)(U,30)})}),u&&(0,B.jsx)(Ae.A,{children:(0,B.jsx)(Ne,{icon:"DragGrid",size:"lg"})})]}),(0,B.jsx)(p.A,{}),(0,B.jsxs)(m.A,{display:"flex",justifyContent:"center",gridGap:8,wrap:"nowrap",children:[(0,B.jsx)(X.A,{item:!0,width:63,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:s(a),children:[(0,B.jsx)(fe,{icon:"Edit"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Edit"})]})}),(0,B.jsx)(X.A,{item:!0,width:78,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:()=>ie("upload"),children:[(0,B.jsx)(fe,{icon:"Camera"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Photo"})]})}),(0,B.jsx)(X.A,{item:!0,width:98,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:()=>ie("search"),children:[(0,B.jsx)(fe,{icon:"LinkedIn"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Connect"})]})}),!x&&(0,B.jsx)(g.A,{item:!0,xs:!0,children:(0,B.jsxs)(Ie,{fullWidth:!0,onClick:d(a),children:[(0,B.jsx)(fe,{icon:"Remove"}),"\xa0",(0,B.jsx)(I.A,{variant:"overline",children:"Remove"})]})})]}),(0,B.jsx)(p.A,{}),(0,B.jsx)(we,{hidden:!te,children:(0,B.jsx)(m.A,{p:2,children:T.filter((e=>!e.viewOnly)).map((e=>{var n;return(0,B.jsx)(se.j,{field:e,children:(0,B.jsx)(ae.A,{syncLocked:b["member.".concat(e.id)],orgId:null===z||void 0===z?void 0:z.id,model:"member",modelId:o.id,field:e,inputRef:he(e),onFieldChange:re(e,"members[".concat(F,"].").concat(e.id)),name:"members[".concat(F,"].").concat(e.id),defaultValue:o[e.id],label:e.label,value:o[e.id],fullWidth:!0,error:(null===E||void 0===E?void 0:E.members)&&(null===E||void 0===E?void 0:E.members[F])&&(null===(n=E.members[F][e.id])||void 0===n?void 0:n.message)},"memberFormField_".concat(e.id))})}))})})]})};var Te=t(37259);const ze=e=>{let{roleType:n="department",handleNewMember:t}=e;const l=(()=>{switch(n){case"single":case"shared":case"assistant":return{src:Te.A,text:(0,B.jsxs)("p",{children:["Either ",(0,B.jsx)("strong",{children:"search roster"})," of existing people or"," ",(0,B.jsx)("a",{onClick:t,children:"add a new person"})," to assign one to this role"]}),title:""};case"department":return{src:Te.A,text:"You can display multiple departments on an org chart and color them accordingly.",title:"Department",height:"100%",learnMore:!0};case"location":return{src:Te.A,text:"Display multiple office locations on an org chart and color them accordingly.",title:"Location",height:"100%",learnMore:!0};case"embedded":return{src:Te.A,text:"You can link to another org chart in your account",title:"Link to Another Chart",height:"100%",learnMore:!0};default:return{src:Te.A,text:"You can display multiple departments on an org chart and color them accordingly.",title:"Department",height:"100%"}}})();return(0,B.jsxs)(m.A,{p:4,textAlign:"center",height:l.height||"auto",bgcolor:"grey.100",children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"center",weight:"bold",children:l.title}),(0,B.jsxs)(m.A,{mt:2,children:[(0,B.jsx)(m.A,{my:4,children:(0,B.jsx)("img",{src:l.src,alt:"empty ".concat(n),width:100})}),(0,B.jsx)("p",{children:l.text}),l.learnMore&&(0,B.jsx)("p",{children:(0,B.jsxs)("a",{href:"https://organimi.zendesk.com/hc/en-us/sections/************-Using-Different-Role-Types-in-Organimi-v5",target:"_blank",rel:"noopener noreferrer",children:[(0,B.jsx)(T.Ay,{icon:"Help",size:"sm"})," Learn more about role types"]})})]})]})};var Pe,Ve=t(94341),We=t(26805),He=t.n(We);const _e=W.Ay.div(Pe||(Pe=(0,r.A)(["\n  position: absolute;\n  top: 155px;\n  left: 24px;\n  bottom: 0;\n  right: 24px;\n  overflow: auto;\n  z-index: 2;\n  .MuiAutocomplete-listbox {\n    max-height: none;\n  }\n  .MuiAutocomplete-paper {\n    border: solid 1px #ccc;\n  }\n"]))),Be=e=>{var n;let{orgFields:t=[],roleType:l="single",firstNameFieldId:r="firstName",lastNameFieldId:o="lastName"}=e;const a=(0,v.d4)(ee),{register:d,watch:s,setValue:h}=(0,f.xW)(),{props:{members:u=[],role:p}}=(0,c.A)("newRole"),x=null===p||void 0===p?void 0:p.expectedMemberCount,{openDialog:b}=(0,c.A)("customFields"),A=(0,i.useRef)(null),j="shared"===l,[y,C]=(0,i.useState)(u),[w,k]=(0,i.useState)((()=>u.length&&"shared"!==l?0:-1)),R=(0,H.dP)(t,H.dj.HIREBYDATE),D=(null===p||void 0===p||null===(n=p.fields)||void 0===n?void 0:n.find((e=>e.id===R)))||(null===t||void 0===t?void 0:t.find((e=>e.id===R))),S=/(single|assistant|shared)/.test(l),M=s("quantity")||1,L=s("role.vacant");(0,i.useEffect)((()=>{d("memberIds"),h("memberIds",y.map((e=>e.id)))}),[]),(0,i.useEffect)((()=>{l&&null!==y&&void 0!==y&&y.length&&(!j&&S?(E(null,[...y]),h("memberIds",y.map((e=>e.id)))):S||(E(null,[]),h("memberIds",[])))}),[l]);const E=function(e){let n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(e&&e.key&&("Backspace"===e.key||"Delete"===e.key))return;"shared"===l?(n=null!==t&&void 0!==t&&t.length?t:[...y],k(w+1)):M<t.length?(n=[...[...y].slice(0,M-1),t[t.length-1]],k(0)):(n=t,k(0));const r=n.reduce(((e,n)=>(-1===e.findIndex((e=>e.id===n.id))&&e.push(n),e)),[]);C(r),h("memberIds",r.map((e=>e.id)))},O=()=>{const e=A.current.value||"",[n="",t=""]=(null===e||void 0===e?void 0:e.trim().split(" "))||[];let l=j?[...y]:[];const i={};i[r]=n,i[o]=t,i.isNew=!0,i.id="new"+(new Date).getTime().toString(),l=[i,...l],E(null,l),A.current.focus(),A.current.blur(),h("memberIds",l.map((e=>e.id)))},N=e=>()=>{let n=[];[null,void 0].includes(e)||(n=[...y],n.splice(e,1)),C(n),h("memberIds",n.map((e=>e.id)))},F=e=>()=>{k(e===w?-1:e)};if(!S)return(0,B.jsx)(ze,{roleType:l});const T="shared"===l&&(0,B.jsx)(m.A,{bgcolor:"white",mt:1,children:(0,B.jsx)(pe.A,{defaultValue:x,placeholder:"eg: 5",label:"# of Expected Members (optional)",variant:"outlined",fullWidth:!0,type:"number",size:"small",name:"role.expectedMemberCount",inputRef:d({valueAsNumber:!0})})});return L&&y.length&&N(null)(),(0,B.jsx)(m.A,{py:3,px:2,bgcolor:"grey.100",height:"100%",position:"relative",children:(0,B.jsxs)(m.A,{children:[(0,B.jsx)(I.A,{variant:"subtitle2",align:"left",weight:"bold",display:"block",children:"Assign People to Role"}),(0,B.jsx)(m.A,{mt:1,mb:1,children:!a&&(0,B.jsx)(m.A,{"aria-describedby":"chartFromScratch_tooltip_3",children:(0,B.jsxs)(g.A,{container:!0,alignItems:"center",children:[(0,B.jsx)(J.A,{name:"role.vacant",inputRef:d,defaultChecked:L,size:"medium"}),(0,B.jsx)(I.A,{variant:"body1",children:"Role is vacant"})]})})}),L&&(0,B.jsxs)(m.A,{children:[(0,B.jsx)(ae.A,{label:(null===D||void 0===D?void 0:D.label)||"Hire Date",name:"role.".concat(R),value:p[R],field:D}),T]}),!L&&(0,B.jsxs)(B.Fragment,{children:[!a&&(0,B.jsx)(m.A,{"aria-describedby":"practiceChart_tooltip_8",children:(0,B.jsxs)(g.A,{container:!0,justifyContent:"space-between",children:[(0,B.jsx)(g.A,{xs:!0,item:!0,children:(0,B.jsx)(xe.A,{selected:y,hideTags:!0,handleSelect:E,searchInputRef:A,disableClearable:!0,initOpen:0===y.length,children:e=>{let{children:n}=e;return(0,B.jsx)(_e,{children:n})}})}),(0,B.jsx)(g.A,{item:!0,children:(0,B.jsx)(m.A,{mt:1,pl:2,children:(0,B.jsx)(ge.A,{variant:"contained",color:"secondary",onClick:O,children:"+ New"})})})]})}),0===y.length&&(0,B.jsx)(ze,{roleType:l,handleNewMember:O}),"shared"===l?(0,B.jsx)(Ve.A,{idField:"id",type:"COLOR_RULE",items:y,renderListItem:(e,n)=>(0,B.jsx)(m.A,{mb:2,border:1,borderColor:"grey.400",borderRadius:4,bgcolor:"common.white",children:(0,B.jsx)(Fe,{member:e,index:n,handleRemoveMember:N,handleToggleMemberDetails:F,activeOpenIndex:w,isShared:"shared"===l})},"newRoleMember_".concat((null===e||void 0===e?void 0:e.id)||n)),handleDrop:(e,n)=>{const t=y[e],l=He()(y,{$splice:[[e,1],[n,0,t]]});C(l),h("memberIds",l.map((e=>e.id)))}}):y.map(((e,n)=>(0,B.jsx)(m.A,{mb:2,border:1,borderColor:"grey.400",borderRadius:4,bgcolor:"common.white",children:(0,B.jsx)(Fe,{member:e,index:n,handleRemoveMember:N,handleToggleMemberDetails:F,activeOpenIndex:w,isShared:!1})},"newRoleMember_".concat(e.id||n)))),T,y.length>0&&(0,B.jsx)(I.A,{variant:"body2",component:"a",align:"right",onClick:()=>{b({field:{model:"member"}})},children:"Add More Fields\xa0>>"})]})]})})};var Ge,qe,Ye=t(52906),Ue=t(54762),Ke=t(24115);const Qe=(0,W.Ay)(m.A)(Ge||(Ge=(0,r.A)(["\n  width: 100%;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n  text-align: center;\n  color: #666;\n  background: rgba(255, 155, 0, 0.2);\n  border-top: solid 1px orange;\n  border-bottom: solid 1px orange;\n"]))),Je=(0,W.Ay)(I.A)(qe||(qe=(0,r.A)(["\n  font-size: 14px;\n"]))),Xe=e=>{var n,t,l,r;let{open:I}=e;const C=(0,v.wA)(),{params:{resourceAction:w}}=(0,j.u)([(0,A.si)()]),R=w===V.uI.AI_INSIGHTS,D=(0,v.d4)(ee),{openDialog:S}=(0,c.A)("duplicatePersonFoundSelectableDialog"),{toggleDialog:M,props:{rel:L,role:E={},members:O=[],quantity:N,allowChangeManager:F,isTalentPoolDrop:z}}=(0,c.A)("newRole"),P=(0,f.mN)({defaultValues:{members:O||[],moveHierarchy:!0}}),{handleSubmit:W,setValue:_,register:G,unregister:q}=P,Y=(0,v.d4)(ke.lz),[U,K]=(0,i.useState)(E.type||"single"),{params:{orgId:Q,chartId:J}}=(0,j.u)([(0,A.N9)(),(0,A.si)()]),X=(0,v.d4)(Ue.Pe),$=(0,v.d4)(k.kA),Z=(0,v.d4)(k.gJ),ne=(0,v.d4)(k.rq),te=null===Z||void 0===Z||null===(n=Z.roleName)||void 0===n?void 0:n.id,le=null===Z||void 0===Z||null===(t=Z.firstName)||void 0===t?void 0:t.id,re=null===Z||void 0===Z||null===(l=Z.lastName)||void 0===l?void 0:l.id,ie=(0,H.dP)($,H.dj.HIREBYDATE),oe=null===E||void 0===E||null===(r=E.fields)||void 0===r?void 0:r.find((e=>e.id===ie)),ae=async function(e,n){var t,l,r;let i,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,d=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;if(void 0===(null===(t=e)||void 0===t||null===(l=t.role)||void 0===l?void 0:l[ie])&&(e.role[ie]=(null===oe||void 0===oe?void 0:oe.value)||null),"embedded"===U){const{name:n}=Y.find((n=>n.id===e.role.embedded_chart))||{name:"embedded chart"};e.role[te]=n}if(null!==a&&d){var s,c;const n=null===(s=e)||void 0===s||null===(c=s.memberIds)||void 0===c?void 0:c[a];e.members[n]=d}e=(e=>(e.role&&(e.role=(0,H.YW)(e.role,$.filter((e=>e.model===H.A2.ROLE)))),e.members&&(e.members=Object.entries(e.members).reduce(((e,n)=>{let[t,l]=n;return e[t]=(0,H.YW)(l,$.filter((e=>e.model===H.A2.MEMBER))),e}),{})),e))(e),i=E.id?b.h7.update:b.h7.create;const{error:m,payload:h}=await C(i({orgId:Q,chartId:J,data:{forceDuplicate:o,role:{id:E.id,...e.role,chart:X,type:U},rel:(F?e.rel:L)||{id:"root",position:"below"},members:e.members?(e.memberIds||[]).map((n=>e.members[n])).filter((e=>e)):[],quantity:e.quantity,moveHierarchy:E.parent!==e.role.parent&&e.moveHierarchy,moveOne:E.parent!==e.role.parent&&!e.moveHierarchy}}));null!==h&&void 0!==h&&h.duplicatesFound&&null!==h&&void 0!==h&&null!==(r=h.duplicates)&&void 0!==r&&r.length?S({fields:ne,duplicatePeople:h.duplicates.map(H.SB),action:ae.bind(null,e,null,!0,null===h||void 0===h?void 0:h.duplicatePersonIndex)}):m||M()},[de,se]=(0,y.A)(W(ae,(()=>{}))),ce=/(single|assistant|shared)/.test(U);(0,i.useEffect)((()=>(G("moveHierarchy"),G("role.teamId"),G("role.functionId"),_("moveHierarchy",!0),_("role.teamId",null===E||void 0===E?void 0:E.teamId),_("role.functionId",null===E||void 0===E?void 0:E.functionId),()=>{q("moveHierarchy")})),[]),(0,i.useEffect)((()=>{if(O){var e;const n=O[0]||{},t=(0,Oe.aG)(null===n||void 0===n||null===(e=n.roleDefaults)||void 0===e?void 0:e.title);t&&0===Object.keys(E).length&&(E[te]=t)}}),[O]);const me=()=>{de||M()};return(0,B.jsxs)(a.A,{open:I,onClose:me,scroll:"paper",children:[(0,B.jsx)(o.A,{onClose:me,align:"center",children:E.id?"Update Role":"New Role"}),(0,B.jsx)(m.A,{width:800,p:0,pr:0,pl:0,clone:!0,children:(0,B.jsx)(d.A,{dividers:!0,children:(0,B.jsx)(f.Op,{...P,children:(0,B.jsx)(Ke.A,{loading:de,transparent:!0,zIndex:3,children:(0,B.jsxs)("form",{id:"newRoleForm",onSubmit:se,children:[D&&(0,B.jsxs)(Qe,{children:[(0,B.jsx)(Je,{children:"Some fields are locked with a synced integration and cannot be changed manually"}),(0,B.jsx)(h.Ay,{placement:"below",title:"This chart is integrated and has fields matched to the data fields in the source integration. If you would like to change these field manually, then deselect them in the integration setup",children:(0,B.jsx)(u.A,{size:"small",children:(0,B.jsx)(T.Ay,{icon:"Help"})})})]}),!D&&(0,B.jsx)(m.A,{mb:2,mt:2,children:(0,B.jsx)(Ye.A,{handleClick:e=>()=>{K(e)},selectedType:U,numRows:1,my:0,isAiInsightsView:R})}),!D&&(0,B.jsx)(p.A,{}),(0,B.jsxs)(g.A,{container:!0,children:[(0,B.jsx)(g.A,{item:!0,xs:ce?6:8,children:(0,B.jsx)(he,{orgId:Q,setValue:_,allowChangeManager:F,roleType:U,role:E,rel:L,quantity:N,chartList:Y.filter((e=>e.id!==J)),isAiInsightsView:R})}),(0,B.jsx)(g.A,{item:!0,xs:ce?6:4,children:(0,B.jsx)(Be,{orgFields:$,roleType:U,currentTitle:(0,H.mA)(E),isTalentPoolDrop:z,roleNameFieldId:te,firstNameFieldId:le,lastNameFieldId:re})})]})]})})})})}),(0,B.jsx)(x.A,{children:(0,B.jsxs)(g.A,{container:!0,justifyContent:"center",children:[(0,B.jsx)(m.A,{m:2,children:(0,B.jsx)(s.A,{fullWidth:!0,disabled:de,variant:"outlined",onClick:me,color:"primary",children:"Cancel"})}),(0,B.jsx)(m.A,{m:2,children:(0,B.jsx)(s.A,{fullWidth:!0,disabled:de,variant:"contained",color:"primary",type:"submit",form:"newRoleForm",children:"Save"})})]})})]})}},52906:(e,n,t)=>{t.d(n,{A:()=>v});var l,r,i=t(57528),o=t(72119),a=t(75156),d=t(96364),s=t(40454),c=t(61531),m=t(37294),h=t(70579);const u=(0,o.Ay)(s.A)(l||(l=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:n,disabled:t=!1}=e;return"\n    padding: ".concat(n.spacing(1),"px;\n    border-radius: 30px;\n    background-color: ").concat(t?m.Qs.Neutrals[200]:n.palette.grey[100],";\n    color: ").concat(t?m.Qs.Neutrals[400]:n.palette.grey[400],";\n    height:50px;\n    width: 50px;\n    margin: 0 auto;\n  ")})),p=(0,o.Ay)(s.A)(r||(r=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:n,selected:t,disabled:l}=e;return"\n    border-radius: 8px;\n    text-align: center;\n    cursor: ".concat(l?"not-allowed":"pointer",";\n    ").concat(t&&!l&&"p {\n          color: ".concat(n.palette.info.dark,";\n        }\n        .iconWrapper {\n          background-color: #d6ebf9;\n          color: #4793cf;\n        }"),"\n    ").concat(l&&"\n      p {\n        color: red;\n      }\n      .iconWrapper {\n        color: ".concat(n.palette.grey[400],";\n      }\n    "),"\n    ").concat(!l&&"\n      &:hover p {\n        color: ".concat(n.palette.info.dark,";\n      }\n      &:hover .iconWrapper {\n        color: ").concat(n.palette.info.dark,";\n        background: ").concat(n.palette.info.light,";\n      }\n    "),"\n  ")})),g=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant",separate:!0},{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],x=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant"}],[{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],v=e=>{let{handleClick:n,selectedType:t,numRows:l=1,my:r=2,isAiInsightsView:i=!1}=e;const o=1===l?120:160,v=e=>"department"===e?"practiceChart_tooltip_3":"single"===e?"chartFromScratch_tooltip_6":"";return(1===l?g:x).map(((e,l)=>(0,h.jsx)(c.A,{my:r,children:(0,h.jsx)(s.A,{container:!0,justifyContent:"center",children:e.map((e=>{let{name:l,label:r,icon:s,separate:g,disableWithAI:x}=e;const b=i&&x;return(0,h.jsx)(p,{onClick:b?()=>{}:n(l),item:!0,selected:t===l,"aria-describedby":v(l),disabled:b,children:(0,h.jsxs)(c.A,{width:o,borderColor:"grey.200",border:g?1:0,borderTop:0,borderLeft:0,borderBottom:0,id:"chart_tooltip_".concat(l),children:[(0,h.jsx)(u,{container:!0,className:"iconWrapper",direction:"column",justifyContent:"center",alignItems:"center",disabled:b,children:(0,h.jsx)(a.Ay,{icon:s,size:"lg"})}),(0,h.jsx)(d.A,{variant:"body1",color:b?m.Qs.Neutrals[500]:m.Qs.Neutrals[800],children:r})]})},"roleType".concat(l))}))})},"roleTypeGroup".concat(l))))}},71433:(e,n,t)=>{t.d(n,{A:()=>h});var l=t(65043),r=t(90349),i=t(56070),o=t(55871),a=t(85899),d=t(59177),s=t(49092),c=t(10621),m=t(70579);const h=e=>{let{handleSelect:n,selected:t,label:h="Search People",hideTags:u=!1,placeholder:p="Search by Name",searchInputRef:g=null,disableClearable:x,children:v,initOpen:b=!1,searchOrg:A=null,freeSolo:j=!1,showEmailInSearch:f=!1,requiredAttributesInResult:y=[]}=e;const I={},[C,w]=(0,l.useState)(b),{t:k}=(0,s.B)(),{query:R,people:D,loading:S,totalResults:M,handleInputChange:L,handleLoadMore:E}=(0,a.A)({emptySearch:!0,searchOrg:A}),O=(e,n)=>{"select-option"!==n&&w(!1)};let N=[...D.map((e=>({...e,firstName:(0,c.II)(e),lastName:(0,c.US)(e),email:(0,c.zY)(e)})))];return M>D.length&&N.push({id:"loadMore",firstName:"Load",lastName:"More"}),0===M&&j&&((0,d.B9)(R)?N.push({email:R}):N=[{id:"emailNotValid",firstName:k("General.Text.EmailNotValid")}]),y.length&&(N=N.filter((e=>{for(let n=0;n<y.length;n++)e[y[n]]||"loadMore"===e.id||"emailNotValid"===e.id?e.noEmailFound=!1:e.noEmailFound=!0;return!0}))),v&&"function"===typeof v&&(I.PopperComponent=v),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(r.Ay,{id:"autocomplete-member-search",disableClearable:x,clearOnBlur:!1,fullWidth:!0,freeSolo:j,autoHighlight:!0,open:C,onOpen:()=>{w(!0)},openOnFocus:!1,onClose:O,getOptionLabel:e=>"".concat((0,c.II)(e)," ").concat((0,c.US)(e)),getOptionDisabled:e=>e.noEmailFound,options:N,filterOptions:e=>e,loading:S,multiple:!0,onChange:(e,t)=>{var l;const{id:r}=t[t.length-1]||{},i=(0,c.zY)(t[t.length-1])||(null===(l=t[t.length-1])||void 0===l?void 0:l.email);if("loadMore"===r)return e&&e.stopPropagation(),w(!0),E();(r||i)&&"emailNotValid"!==r?(n(e,t),O()):n(e,t||[])},onInputChange:L,value:t,renderOption:e=>(0,m.jsx)(o.A,{person:e,showEmail:f}),...I,renderInput:e=>(0,m.jsx)(i.A,{...e,inputRef:g,hideTags:u,label:h,placeholder:p,fullWidth:!0})})})}},90694:(e,n,t)=>{t.d(n,{A:()=>b});var l=t(65043),r=t(90349),i=t(56070),o=t(34976),a=t(96364),d=t(40454),s=t(61531),c=t(80539),m=t(10621),h=t(70579);const u=["location","department","embedded"],p=e=>{let{handleClick:n,option:t={role:{members:[]}},vacantText:l}=e;const{role:r={members:[]}}=t||{},{members:i="",type:o}=r,[p]=i||[],{photo:g}=(0,m.LS)(p||{}),x=(0,m.mA)(r),v=(0,m.II)(p),b=(0,m.US)(p),A=v||b?"".concat(v||""," ").concat(b||""):l,j=!u.includes(o);return(0,h.jsxs)(d.A,{container:!0,onClick:n,alignContent:"center",alignItems:"center",children:[(0,h.jsx)(s.A,{mr:2,clone:!0,children:(0,h.jsx)(c.A,{width:30,height:30,name:j?A:x,src:g})}),(0,h.jsxs)(d.A,{item:!0,xs:!0,children:[j?(0,h.jsx)(a.A,{children:A}):null,(0,h.jsx)(a.A,{children:x})]})]})};var g=t(14556),x=t(89656),v=t(7743);const b=e=>{var n;let{hideInputValue:t,disableClearable:a,handleSelect:d,selected:s,label:c="Search Roles",placeholder:u="Search position or person",multiple:b=!0,chartId:A,orgId:j}=e;const[f,y]=(0,l.useState)(!1),{chartOptions:{vacantText:I}}=(0,g.d4)(x.P0),C=(0,g.d4)(v.gJ),w=null===C||void 0===C||null===(n=C.roleName)||void 0===n?void 0:n.id,{query:k,results:R,loading:D,handleInputChange:S}=(0,o.A)({emptySearch:!0,model:"role",fieldId:w,defaultChartId:A,defaultOrgId:j}),M=b&&t?()=>{}:null;return(0,h.jsx)(r.Ay,{id:"autocomplete-role-search",disableClearable:a,renderTags:M,fullWidth:!0,open:f,defaultValue:s,onOpen:()=>{y(!0)},onClose:()=>{y(!1)},getOptionLabel:e=>"".concat((0,m.mA)(e.role)),options:R,filterOptions:e=>e,noOptionsText:k?"No roles found":"Start typing to ".concat(c.toLowerCase()),loading:D,multiple:b,renderOption:e=>(0,h.jsx)(p,{handleClick:d(e),option:e,vacantText:I}),renderInput:e=>(0,h.jsx)(i.A,{...e,onChange:e=>S(e.target.value),label:c,placeholder:u,fullWidth:!0})})}},48283:(e,n,t)=>{t.d(n,{Cs:()=>r,yy:()=>i,zv:()=>o});var l=t(78396);function r(e){let n={title:"Role Title *",description:"Role Description",name:!0,color:!0,propagateBg:!0,location:!1,locationAddress:!0,chartLink:!1,dottedReports:!0,manager:!0,dropLevel:!0,leftRight:!1,customFields:!0,smartLegendLink:!0};switch(e){case"assistant":n.orientation=!0,n.dropLevel=!1;break;case"location":n.description=!1,n.dottedReports=!1,n.locationAddress=!0,n.title="Location Name",n.description="Location Description";break;case"embedded":n.chartLink=!0,n.dottedReports=!1,n.location=!1,n.locationAddress=!1,n.dottedReport=!1,n.title="Linked Chart",n.description=!1,n.propagateBg=!1,n.manager=!1,n.dottedReports=!1,n.dropLevel=!1,n.customFields=!1,n.smartLegendLink=!1;break;case"department":n.title="Department Title *",n.description="Department Description",n.locationAddress=!1}return n}function i(e){let{role:n,chartType:t}=e;return t===l.XD.MATRIX&&!(null!==n&&void 0!==n&&n.parent)&&!(null!==n&&void 0!==n&&n.type)!==l.mv.HIDDEN&&!n.functionId&&!n.teamId}function o(e){let{role:n,chartType:t}=e;return t===l.XD.MATRIX&&[l.mv.TEAM,l.mv.FUNCTION,l.mv.HIDDEN].includes(n.type)}},34976:(e,n,t)=>{t.d(n,{A:()=>m});var l=t(65043),r=t(61),i=t(86255),o=t(66856),a=t(14556),d=t(36138),s=t(19367),c=t(48283);const m=e=>{let{model:n,fieldId:t,defaultChartId:m,defaultOrgId:h}=e;const u=(0,a.wA)(),[p,g]=(0,l.useState)(!1),[x,v]=(0,l.useState)([]),[b,A]=(0,l.useState)(""),j=(0,a.d4)(s.G0),{params:{orgId:f,chartId:y}}=(0,d.u)([(0,o.si)(),(0,o.K7)()]),I=y||m,C=f||h,{eventDebounce:w}=(0,i.A)(),k=w((e=>{A(e)}),500),R=(0,l.useCallback)((e=>{v([]),g(!0),k(e)}),[]),D=(0,l.useMemo)((()=>{if("role"===n)return r.JP.findRoles;throw new Error("Search not yet handled")}),[n]);return(0,l.useEffect)((()=>{b?(async()=>{const{payload:e}=await u(D({orgId:C,chartId:I,query:b,fields:t}));g(!1);const n=((null===e||void 0===e?void 0:e.results)||[]).filter((e=>{const n=(0,c.yy)({role:e.role,chartType:j}),t=(0,c.zv)({role:e.role,chartType:j});return!(n||t)}));v(n)})():(v([]),g(!1))}),[b]),{query:b,results:x,loading:p,handleInputChange:R}}},23851:(e,n,t)=>{t.d(n,{A:()=>v});var l=t(98587),r=t(58168),i=t(65043),o=t(69292),a=t(68606),d=t(51347),s=t(34535),c=t(72876),m=t(57056),h=t(32400);function u(e){return(0,h.Ay)("MuiListItemAvatar",e)}(0,m.A)("MuiListItemAvatar",["root","alignItemsFlexStart"]);var p=t(70579);const g=["className"],x=(0,s.Ay)("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(e,n)=>{const{ownerState:t}=e;return[n.root,"flex-start"===t.alignItems&&n.alignItemsFlexStart]}})((e=>{let{ownerState:n}=e;return(0,r.default)({minWidth:56,flexShrink:0},"flex-start"===n.alignItems&&{marginTop:8})})),v=i.forwardRef((function(e,n){const t=(0,c.A)({props:e,name:"MuiListItemAvatar"}),{className:s}=t,m=(0,l.default)(t,g),h=i.useContext(d.A),v=(0,r.default)({},t,{alignItems:h.alignItems}),b=(e=>{const{alignItems:n,classes:t}=e,l={root:["root","flex-start"===n&&"alignItemsFlexStart"]};return(0,a.A)(l,u,t)})(v);return(0,p.jsx)(x,(0,r.default)({className:(0,o.A)(b.root,s),ownerState:v,ref:n},m))}))}}]);
//# sourceMappingURL=3760.92ca97a3.chunk.js.map

// === 3657.7d33e090.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[3657],{55871:(e,t,n)=>{n.d(t,{A:()=>c});var l=n(40454),a=n(61531),i=n(80539),o=n(75156),r=n(96364),s=n(59177),d=n(70579);const c=e=>{let{person:t,showEmail:n=!1}=e;const{firstName:c="",lastName:h="",photo:m,id:p,email:u,roleDefaults:A}=t;let x=Boolean(c)||Boolean(h)?"".concat(c," ").concat(h):u;const b="loadMore"===p,g="emailNotValid"===p,j=!b&&!g&&n;return(0,d.jsxs)(l.A,{container:!0,alignContent:"center",alignItems:"center",children:[(0,d.jsxs)(a.A,{mr:2,children:[b&&(0,d.jsx)(a.A,{children:(0,d.jsx)(o.Ay,{icon:"LoadMore",size:"x2"})}),!b&&g&&(0,d.jsx)(a.A,{children:(0,d.jsx)(o.Ay,{icon:"ExclamationTriangle",size:"x2"})}),!b&&!g&&(0,d.jsx)(i.A,{width:30,height:30,src:m,name:x,overrideColor:null===t||void 0===t?void 0:t.memberPhotoColor})]}),(0,d.jsxs)(l.A,{item:!0,xs:!0,children:[(0,d.jsx)(r.A,{display:"inline",children:"".concat(c," ").concat(h," ")}),(c||h)&&u&&n&&"| ",n&&Boolean(u)?(0,d.jsx)(r.A,{display:"inline",children:u}):j&&(0,d.jsx)(a.A,{display:"inline",children:(0,d.jsx)(r.A,{fontStyle:"italic",display:"inline",children:"No email found."})}),(0,s.aG)(null===A||void 0===A?void 0:A.title)&&(0,d.jsx)(r.A,{children:null!==A&&void 0!==A&&A.title?A.title:""})]})]})}},63657:(e,t,n)=>{n.r(t),n.d(t,{default:()=>F});var l=n(65043),a=n(76031),i=n(84),o=n(61531),r=n(43867),s=n(40454),d=n(55357),c=n(43577),h=n(52907),m=n(96364),p=n(84866),u=n(49157),A=n(75156),x=n(82244),b=n(14556),g=n(61258),j=n(2173),y=n(52906),f=n(43862),v=n(43331),C=n(7743),I=n(71433),S=n(5816),w=n(72835),N=n(74593),R=n(59177),k=n(48283),L=n(85279),W=n(10621),T=n(54762),D=n(43940),q=n(24115),E=n(70579);const F=e=>{var t;let{open:n}=e;const F=(0,b.wA)(),M=(0,l.useRef)(),[_,O]=(0,l.useState)("single"),[P,V]=(0,l.useState)(!1),[B,z]=(0,l.useState)([]),{toggleDialog:Q,props:{rel:U}}=(0,i.A)("newRoleSimple"),{openDialog:H}=(0,i.A)("newRole"),{register:X,handleSubmit:Y,getValues:G,setValue:J,control:K}=(0,g.mN)(),Z=(0,b.d4)(v.lz),$=(0,l.useMemo)((()=>(0,k.Cs)(_)),[_]),ee=(0,b.d4)(C.KN),te=(0,b.d4)(C.gJ),ne=null===te||void 0===te||null===(t=te.roleName)||void 0===t?void 0:t.id,le=(0,W.dP)(ee,W.dj.HIREBYDATE),ae=null===ee||void 0===ee?void 0:ee.find((e=>e.id===le)),ie=(0,l.useCallback)((()=>{Q()}),[Q]),{onTourEventComplete:oe}=(0,D.M)({}),re=(0,b.d4)(v.mn),se=(0,b.d4)(T.Pq),de=(0,b.d4)(T.Pe),[ce,he]=(0,j.A)(Y((async e=>{if("embedded"===_){const{name:t}=Z.find((t=>t.id===e.role.embedded_chart))||{name:"embedded chart"};e.role[ne]=t}e.role=(0,W.YW)(e.role,ee);const{error:t,payload:n}=await F(f.h7.create({orgId:re,chartId:se,data:{role:{type:_,...e.role,chart:de},rel:U,members:B,quantity:e.quantity}}));if(!t){var l,a;const e=null===n||void 0===n||null===(l=n.roles)||void 0===l||null===(a=l[0])||void 0===a?void 0:a.id;oe({event:"role-doalog-simple-role-created",data:{roleId:e,roleCard:e}}),ie()}}))),me=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const n=G("quantity")||1;if("shared"===_){const e=[...new Map((t||[]).map((e=>[e.id,e]))).values()];z(e)}else n<t.length?z((e=>[...[...e].slice(0,n-1),t[t.length-1]])):z(t)},pe=/(single|department)/.test(_),ue=/(single|assistant|shared)/.test(_),Ae=/(shared)/.test(_),xe=B,be=!Ae&&G("quantity")||0;(0,l.useLayoutEffect)((()=>{!Ae&&ue?me(null,[...B]):ue||me(null,[])}),[_]);const ge=()=>{ce||ie()};return(0,E.jsx)(E.Fragment,{children:(0,E.jsxs)(a.A,{open:n,onClose:ge,children:[(0,E.jsx)(S.A,{align:"center",onClose:ge,children:"New Role"}),(0,E.jsx)(o.A,{width:550,px:4,clone:!0,children:(0,E.jsx)(r.A,{dividers:!0,children:(0,E.jsx)(q.A,{loading:ce,transparent:!0,children:(0,E.jsxs)("form",{onSubmit:he,id:"newRoleSimpleForm",children:[(0,E.jsx)(o.A,{mb:4,children:(0,E.jsx)(y.A,{handleClick:e=>()=>{O(e),"shared"!==e&&"department"!==e&&J("quantity",1)},selectedType:_,numRows:2})}),(0,E.jsx)(o.A,{my:2,clone:!0,children:(0,E.jsxs)(s.A,{container:!0,alignItems:"flex-start",children:[pe&&(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(u.A,{item:!0,width:80,children:(0,E.jsx)(p.A,{type:"number",name:"quantity",id:"role-quantity",label:"Quantity",color:"primary",defaultValue:1,inputProps:{min:1,max:100},fullWidth:!0,inputRef:X,"aria-describedby":"practiceChart_tooltip_7"})}),(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(A.Ay,{icon:"Close"})})]}),(0,E.jsxs)(s.A,{item:!0,xs:!0,children:["embedded"!==_&&(0,E.jsx)(p.A,{autoFocus:!0,fullWidth:!0,name:"role.".concat(ne),id:"new-simple-role-title",label:$.title,color:"primary",required:!0,inputRef:X({required:!0}),"data-tour-anchor":"role-dialog-simple-role-title-field"}),"embedded"===_&&(0,E.jsx)(g.xI,{name:"role.embedded_chart",control:K,defaultValue:"",render:e=>{let{name:t,onChange:n}=e;return(0,E.jsxs)(p.A,{select:!0,name:t,fullWidth:!0,labelId:"new-simple-embedded-chart",label:"Select Chart",color:"primary",required:!0,onChange:e=>{n(e.target.value)},children:[(0,E.jsx)(d.A,{value:"",children:(0,E.jsx)("em",{children:"Select Chart"})}),Z.map((e=>(0,E.jsx)(d.A,{value:e.id,children:e.name})))]})}}),(0,E.jsx)("div",{children:(0,E.jsx)("a",{onClick:()=>{H({rel:U,role:{type:_,[ne]:G("role.".concat(ne))},quantity:G("quantity")||1,members:B,from:"simple"},!0)},children:"Enter More Information \xbb"})})]})]})}),(0,E.jsx)(x.A,{orientation:"hors",weight:1}),(0,E.jsx)(s.A,{container:!0,justifyContent:"space-between",alignItems:"flex-start",children:ue&&(0,E.jsx)(E.Fragment,{children:(0,E.jsx)(m.A,{variant:"subtitle2",align:"left",display:"block",weight:"bold",children:"Assign People to Role"})})}),ue&&(0,E.jsx)(o.A,{my:1,children:(0,E.jsxs)(s.A,{container:!0,justifyContent:"left",children:[(0,E.jsx)(c.A,{checked:P,onChange:()=>{V(!P)},color:"primary",name:"vacantRole",inputProps:{"aria-label":"Role is vacant"}}),(0,E.jsx)(o.A,{m:1,children:(0,E.jsx)(m.A,{variant:"body2",children:"Role is Vacant"})})]})}),!P&&ue&&(0,E.jsxs)(s.A,{container:!0,justifyContent:"flex-start",children:[(0,E.jsx)(s.A,{item:!0}),(0,E.jsx)(s.A,{item:!0,xs:!0,children:(0,E.jsx)(o.A,{m:2,clone:!0,children:(0,E.jsx)(I.A,{disableClearable:!0,searchInputRef:M,handleSelect:me,options:xe,limit:be,selected:B})})}),(0,E.jsx)(o.A,{mb:1,mt:1,ml:2,children:(0,E.jsxs)(w.A,{variant:"contained",color:"secondary",onClick:()=>{var e;const t=null===(e=M.current)||void 0===e?void 0:e.value,n=(l=t,{...(0,R.St)(l||""),isNew:!0});var l;let a;a="shared"===_?[n,...B]:B,H({rel:U,role:{type:_,[ne]:G("role.".concat(ne))},quantity:G("quantity")||1,members:a,from:"simple"},!0)},children:[(0,E.jsx)(A.Ay,{icon:"Add"})," \xa0 New"]})})]}),P&&ue&&(0,E.jsx)(s.A,{container:!0,children:(0,E.jsx)(g.xI,{control:K,name:"role.".concat(le),render:e=>{let{onChange:t,value:n,name:l}=e;return(0,E.jsx)(L.A,{label:ae.label||"Planned hire date",clearable:!!n,type:"date",onChange:t,value:n,field:l,classes:"dateInputFullWidth"})}})})]})})})}),(0,E.jsx)(h.A,{children:(0,E.jsxs)(s.A,{container:!0,justifyContent:"center",children:[(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(N.A,{fullWidth:!0,disabled:ce,variant:"outlined",color:"primary",onClick:ge,children:"Cancel"})}),(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(N.A,{fullWidth:!0,disabled:ce,variant:"contained",color:"primary",type:"submit",form:"newRoleSimpleForm",children:"Save"})})]})})]})})}},52906:(e,t,n)=>{n.d(t,{A:()=>b});var l,a,i=n(57528),o=n(72119),r=n(75156),s=n(96364),d=n(40454),c=n(61531),h=n(37294),m=n(70579);const p=(0,o.Ay)(d.A)(l||(l=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:t,disabled:n=!1}=e;return"\n    padding: ".concat(t.spacing(1),"px;\n    border-radius: 30px;\n    background-color: ").concat(n?h.Qs.Neutrals[200]:t.palette.grey[100],";\n    color: ").concat(n?h.Qs.Neutrals[400]:t.palette.grey[400],";\n    height:50px;\n    width: 50px;\n    margin: 0 auto;\n  ")})),u=(0,o.Ay)(d.A)(a||(a=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:t,selected:n,disabled:l}=e;return"\n    border-radius: 8px;\n    text-align: center;\n    cursor: ".concat(l?"not-allowed":"pointer",";\n    ").concat(n&&!l&&"p {\n          color: ".concat(t.palette.info.dark,";\n        }\n        .iconWrapper {\n          background-color: #d6ebf9;\n          color: #4793cf;\n        }"),"\n    ").concat(l&&"\n      p {\n        color: red;\n      }\n      .iconWrapper {\n        color: ".concat(t.palette.grey[400],";\n      }\n    "),"\n    ").concat(!l&&"\n      &:hover p {\n        color: ".concat(t.palette.info.dark,";\n      }\n      &:hover .iconWrapper {\n        color: ").concat(t.palette.info.dark,";\n        background: ").concat(t.palette.info.light,";\n      }\n    "),"\n  ")})),A=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant",separate:!0},{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],x=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant"}],[{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],b=e=>{let{handleClick:t,selectedType:n,numRows:l=1,my:a=2,isAiInsightsView:i=!1}=e;const o=1===l?120:160,b=e=>"department"===e?"practiceChart_tooltip_3":"single"===e?"chartFromScratch_tooltip_6":"";return(1===l?A:x).map(((e,l)=>(0,m.jsx)(c.A,{my:a,children:(0,m.jsx)(d.A,{container:!0,justifyContent:"center",children:e.map((e=>{let{name:l,label:a,icon:d,separate:A,disableWithAI:x}=e;const g=i&&x;return(0,m.jsx)(u,{onClick:g?()=>{}:t(l),item:!0,selected:n===l,"aria-describedby":b(l),disabled:g,children:(0,m.jsxs)(c.A,{width:o,borderColor:"grey.200",border:A?1:0,borderTop:0,borderLeft:0,borderBottom:0,id:"chart_tooltip_".concat(l),children:[(0,m.jsx)(p,{container:!0,className:"iconWrapper",direction:"column",justifyContent:"center",alignItems:"center",disabled:g,children:(0,m.jsx)(r.Ay,{icon:d,size:"lg"})}),(0,m.jsx)(s.A,{variant:"body1",color:g?h.Qs.Neutrals[500]:h.Qs.Neutrals[800],children:a})]})},"roleType".concat(l))}))})},"roleTypeGroup".concat(l))))}},71433:(e,t,n)=>{n.d(t,{A:()=>m});var l=n(65043),a=n(90349),i=n(56070),o=n(55871),r=n(85899),s=n(59177),d=n(49092),c=n(10621),h=n(70579);const m=e=>{let{handleSelect:t,selected:n,label:m="Search People",hideTags:p=!1,placeholder:u="Search by Name",searchInputRef:A=null,disableClearable:x,children:b,initOpen:g=!1,searchOrg:j=null,freeSolo:y=!1,showEmailInSearch:f=!1,requiredAttributesInResult:v=[]}=e;const C={},[I,S]=(0,l.useState)(g),{t:w}=(0,d.B)(),{query:N,people:R,loading:k,totalResults:L,handleInputChange:W,handleLoadMore:T}=(0,r.A)({emptySearch:!0,searchOrg:j}),D=(e,t)=>{"select-option"!==t&&S(!1)};let q=[...R.map((e=>({...e,firstName:(0,c.II)(e),lastName:(0,c.US)(e),email:(0,c.zY)(e)})))];return L>R.length&&q.push({id:"loadMore",firstName:"Load",lastName:"More"}),0===L&&y&&((0,s.B9)(N)?q.push({email:N}):q=[{id:"emailNotValid",firstName:w("General.Text.EmailNotValid")}]),v.length&&(q=q.filter((e=>{for(let t=0;t<v.length;t++)e[v[t]]||"loadMore"===e.id||"emailNotValid"===e.id?e.noEmailFound=!1:e.noEmailFound=!0;return!0}))),b&&"function"===typeof b&&(C.PopperComponent=b),(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(a.Ay,{id:"autocomplete-member-search",disableClearable:x,clearOnBlur:!1,fullWidth:!0,freeSolo:y,autoHighlight:!0,open:I,onOpen:()=>{S(!0)},openOnFocus:!1,onClose:D,getOptionLabel:e=>"".concat((0,c.II)(e)," ").concat((0,c.US)(e)),getOptionDisabled:e=>e.noEmailFound,options:q,filterOptions:e=>e,loading:k,multiple:!0,onChange:(e,n)=>{var l;const{id:a}=n[n.length-1]||{},i=(0,c.zY)(n[n.length-1])||(null===(l=n[n.length-1])||void 0===l?void 0:l.email);if("loadMore"===a)return e&&e.stopPropagation(),S(!0),T();(a||i)&&"emailNotValid"!==a?(t(e,n),D()):t(e,n||[])},onInputChange:W,value:n,renderOption:e=>(0,h.jsx)(o.A,{person:e,showEmail:f}),...C,renderInput:e=>(0,h.jsx)(i.A,{...e,inputRef:A,hideTags:p,label:m,placeholder:u,fullWidth:!0})})})}},48283:(e,t,n)=>{n.d(t,{Cs:()=>a,yy:()=>i,zv:()=>o});var l=n(78396);function a(e){let t={title:"Role Title *",description:"Role Description",name:!0,color:!0,propagateBg:!0,location:!1,locationAddress:!0,chartLink:!1,dottedReports:!0,manager:!0,dropLevel:!0,leftRight:!1,customFields:!0,smartLegendLink:!0};switch(e){case"assistant":t.orientation=!0,t.dropLevel=!1;break;case"location":t.description=!1,t.dottedReports=!1,t.locationAddress=!0,t.title="Location Name",t.description="Location Description";break;case"embedded":t.chartLink=!0,t.dottedReports=!1,t.location=!1,t.locationAddress=!1,t.dottedReport=!1,t.title="Linked Chart",t.description=!1,t.propagateBg=!1,t.manager=!1,t.dottedReports=!1,t.dropLevel=!1,t.customFields=!1,t.smartLegendLink=!1;break;case"department":t.title="Department Title *",t.description="Department Description",t.locationAddress=!1}return t}function i(e){let{role:t,chartType:n}=e;return n===l.XD.MATRIX&&!(null!==t&&void 0!==t&&t.parent)&&!(null!==t&&void 0!==t&&t.type)!==l.mv.HIDDEN&&!t.functionId&&!t.teamId}function o(e){let{role:t,chartType:n}=e;return n===l.XD.MATRIX&&[l.mv.TEAM,l.mv.FUNCTION,l.mv.HIDDEN].includes(t.type)}}}]);
//# sourceMappingURL=3657.7d33e090.chunk.js.map

// === 1802.854278ce.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[1802],{31802:(e,t,a)=>{a.d(t,{Ay:()=>ae});var r=a(98587),n=a(58168),o=a(65043),l=a(69292),i=a(33662),s=a(90540),u=a(68606),c=a(22144),d=a(41944),p=a(47040),m=a(40932),v=a(47042),b=a(63844),h=a(24626),f=a(92088);var g=a(29279);function y(e,t){return e-t}function x(e,t){var a;const{index:r}=null!=(a=e.reduce(((e,a,r)=>{const n=Math.abs(t-a);return null===e||n<e.distance||n===e.distance?{distance:n,index:r}:e}),null))?a:{};return r}function k(e,t){if(void 0!==t.current&&e.changedTouches){const a=e;for(let e=0;e<a.changedTouches.length;e+=1){const r=a.changedTouches[e];if(r.identifier===t.current)return{x:r.clientX,y:r.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function S(e,t,a){return 100*(e-t)/(a-t)}function A(e,t,a){const r=Math.round((e-a)/t)*t+a;return Number(r.toFixed(function(e){if(Math.abs(e)<1){const t=e.toExponential().split("e-"),a=t[0].split(".")[1];return(a?a.length:0)+parseInt(t[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}(t)))}function w(e){let{values:t,newValue:a,index:r}=e;const n=t.slice();return n[r]=a,n.sort(y)}function L(e){let{sliderRef:t,activeIndex:a,setActive:r}=e;var n,o;const l=(0,c.A)(t.current);var i;null!=(n=t.current)&&n.contains(l.activeElement)&&Number(null==l||null==(o=l.activeElement)?void 0:o.getAttribute("data-index"))===a||(null==(i=t.current)||i.querySelector('[type="range"][data-index="'.concat(a,'"]')).focus());r&&r(a)}function C(e,t){return"number"===typeof e&&"number"===typeof t?e===t:"object"===typeof e&&"object"===typeof t&&function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:(e,t)=>e===t;return e.length===t.length&&e.every(((e,r)=>a(e,t[r])))}(e,t)}const R={horizontal:{offset:e=>({left:"".concat(e,"%")}),leap:e=>({width:"".concat(e,"%")})},"horizontal-reverse":{offset:e=>({right:"".concat(e,"%")}),leap:e=>({width:"".concat(e,"%")})},vertical:{offset:e=>({bottom:"".concat(e,"%")}),leap:e=>({height:"".concat(e,"%")})}},z=e=>e;let P;function T(){return void 0===P&&(P="undefined"===typeof CSS||"function"!==typeof CSS.supports||CSS.supports("touch-action","none")),P}function I(e){const{"aria-labelledby":t,defaultValue:a,disabled:r=!1,disableSwap:l=!1,isRtl:i=!1,marks:s=!1,max:u=100,min:P=0,name:I,onChange:N,onChangeCommitted:M,orientation:E="horizontal",rootRef:j,scale:O=z,step:V=1,shiftStep:F=10,tabIndex:D,value:Y}=e,Q=o.useRef(),[X,B]=o.useState(-1),[K,W]=o.useState(-1),[H,U]=o.useState(!1),$=o.useRef(0),[_,q]=(0,d.A)({controlled:Y,default:null!=a?a:P,name:"Slider"}),G=N&&((e,t,a)=>{const r=e.nativeEvent||e,n=new r.constructor(r.type,r);Object.defineProperty(n,"target",{writable:!0,value:{value:t,name:I}}),N(n,t,a)}),J=Array.isArray(_);let Z=J?_.slice().sort(y):[_];Z=Z.map((e=>null==e?P:(0,p.A)(e,P,u)));const ee=!0===s&&null!==V?[...Array(Math.floor((u-P)/V)+1)].map(((e,t)=>({value:P+V*t}))):s||[],te=ee.map((e=>e.value)),{isFocusVisibleRef:ae,onBlur:re,onFocus:ne,ref:oe}=(0,m.A)(),[le,ie]=o.useState(-1),se=o.useRef(),ue=(0,v.A)(oe,se),ce=(0,v.A)(j,ue),de=e=>t=>{var a;const r=Number(t.currentTarget.getAttribute("data-index"));ne(t),!0===ae.current&&ie(r),W(r),null==e||null==(a=e.onFocus)||a.call(e,t)},pe=e=>t=>{var a;re(t),!1===ae.current&&ie(-1),W(-1),null==e||null==(a=e.onBlur)||a.call(e,t)},me=(e,t)=>{const a=Number(e.currentTarget.getAttribute("data-index")),r=Z[a],n=te.indexOf(r);let o=t;if(ee&&null==V){const e=te[te.length-1];o=o>e?e:o<te[0]?te[0]:o<r?te[n-1]:te[n+1]}if(o=(0,p.A)(o,P,u),J){l&&(o=(0,p.A)(o,Z[a-1]||-1/0,Z[a+1]||1/0));const e=o;o=w({values:Z,newValue:o,index:a});let t=a;l||(t=o.indexOf(e)),L({sliderRef:se,activeIndex:t})}q(o),ie(a),G&&!C(o,_)&&G(e,o,a),M&&M(e,o)},ve=e=>t=>{var a;if(null!==V){const e=Number(t.currentTarget.getAttribute("data-index")),a=Z[e];let r=null;("ArrowLeft"===t.key||"ArrowDown"===t.key)&&t.shiftKey||"PageDown"===t.key?r=Math.max(a-F,P):(("ArrowRight"===t.key||"ArrowUp"===t.key)&&t.shiftKey||"PageUp"===t.key)&&(r=Math.min(a+F,u)),null!==r&&(me(t,r),t.preventDefault())}null==e||null==(a=e.onKeyDown)||a.call(e,t)};(0,b.A)((()=>{var e;r&&se.current.contains(document.activeElement)&&(null==(e=document.activeElement)||e.blur())}),[r]),r&&-1!==X&&B(-1),r&&-1!==le&&ie(-1);const be=o.useRef();let he=E;i&&"horizontal"===E&&(he+="-reverse");const fe=e=>{let{finger:t,move:a=!1}=e;const{current:r}=se,{width:n,height:o,bottom:i,left:s}=r.getBoundingClientRect();let c,d;if(c=0===he.indexOf("vertical")?(i-t.y)/o:(t.x-s)/n,-1!==he.indexOf("-reverse")&&(c=1-c),d=function(e,t,a){return(a-t)*e+t}(c,P,u),V)d=A(d,V,P);else{const e=x(te,d);d=te[e]}d=(0,p.A)(d,P,u);let m=0;if(J){m=a?be.current:x(Z,d),l&&(d=(0,p.A)(d,Z[m-1]||-1/0,Z[m+1]||1/0));const e=d;d=w({values:Z,newValue:d,index:m}),l&&a||(m=d.indexOf(e),be.current=m)}return{newValue:d,activeIndex:m}},ge=(0,h.A)((e=>{const t=k(e,Q);if(!t)return;if($.current+=1,"mousemove"===e.type&&0===e.buttons)return void ye(e);const{newValue:a,activeIndex:r}=fe({finger:t,move:!0});L({sliderRef:se,activeIndex:r,setActive:B}),q(a),!H&&$.current>2&&U(!0),G&&!C(a,_)&&G(e,a,r)})),ye=(0,h.A)((e=>{const t=k(e,Q);if(U(!1),!t)return;const{newValue:a}=fe({finger:t,move:!0});B(-1),"touchend"===e.type&&W(-1),M&&M(e,a),Q.current=void 0,ke()})),xe=(0,h.A)((e=>{if(r)return;T()||e.preventDefault();const t=e.changedTouches[0];null!=t&&(Q.current=t.identifier);const a=k(e,Q);if(!1!==a){const{newValue:t,activeIndex:r}=fe({finger:a});L({sliderRef:se,activeIndex:r,setActive:B}),q(t),G&&!C(t,_)&&G(e,t,r)}$.current=0;const n=(0,c.A)(se.current);n.addEventListener("touchmove",ge,{passive:!0}),n.addEventListener("touchend",ye,{passive:!0})})),ke=o.useCallback((()=>{const e=(0,c.A)(se.current);e.removeEventListener("mousemove",ge),e.removeEventListener("mouseup",ye),e.removeEventListener("touchmove",ge),e.removeEventListener("touchend",ye)}),[ye,ge]);o.useEffect((()=>{const{current:e}=se;return e.addEventListener("touchstart",xe,{passive:T()}),()=>{e.removeEventListener("touchstart",xe),ke()}}),[ke,xe]),o.useEffect((()=>{r&&ke()}),[r,ke]);const Se=S(J?Z[0]:P,P,u),Ae=S(Z[Z.length-1],P,u)-Se,we=e=>t=>{var a;null==(a=e.onMouseLeave)||a.call(e,t),W(-1)};return{active:X,axis:he,axisProps:R,dragging:H,focusedThumbIndex:le,getHiddenInputProps:function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var o;const l=(0,g.h)(a),s={onChange:(c=l||{},e=>{var t;null==(t=c.onChange)||t.call(c,e),me(e,e.target.valueAsNumber)}),onFocus:de(l||{}),onBlur:pe(l||{}),onKeyDown:ve(l||{})};var c;const d=(0,n.default)({},l,s);return(0,n.default)({tabIndex:D,"aria-labelledby":t,"aria-orientation":E,"aria-valuemax":O(u),"aria-valuemin":O(P),name:I,type:"range",min:e.min,max:e.max,step:null===e.step&&e.marks?"any":null!=(o=e.step)?o:void 0,disabled:r},a,d,{style:(0,n.default)({},f.A,{direction:i?"rtl":"ltr",width:"100%",height:"100%"})})},getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,g.h)(e),a={onMouseDown:(o=t||{},e=>{var t;if(null==(t=o.onMouseDown)||t.call(o,e),r)return;if(e.defaultPrevented)return;if(0!==e.button)return;e.preventDefault();const a=k(e,Q);if(!1!==a){const{newValue:t,activeIndex:r}=fe({finger:a});L({sliderRef:se,activeIndex:r,setActive:B}),q(t),G&&!C(t,_)&&G(e,t,r)}$.current=0;const n=(0,c.A)(se.current);n.addEventListener("mousemove",ge,{passive:!0}),n.addEventListener("mouseup",ye)})};var o;const l=(0,n.default)({},t,a);return(0,n.default)({},e,{ref:ce},l)},getThumbProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,g.h)(e),a={onMouseOver:(r=t||{},e=>{var t;null==(t=r.onMouseOver)||t.call(r,e);const a=Number(e.currentTarget.getAttribute("data-index"));W(a)}),onMouseLeave:we(t||{})};var r;return(0,n.default)({},e,t,a)},marks:ee,open:K,range:J,rootRef:ce,trackLeap:Ae,trackOffset:Se,values:Z,getThumbStyle:e=>({pointerEvents:-1!==X&&X!==e?"none":void 0})}}var N=a(67266),M=a(10875),E=a(44350),j=a(34535),O=a(47123);const V=e=>!e||!(0,s.g)(e);var F=a(6803),D=a(57056),Y=a(32400);function Q(e){return(0,Y.Ay)("MuiSlider",e)}const X=(0,D.A)("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]);var B=a(70579);const K=["aria-label","aria-valuetext","aria-labelledby","component","components","componentsProps","color","classes","className","disableSwap","disabled","getAriaLabel","getAriaValueText","marks","max","min","name","onChange","onChangeCommitted","orientation","shiftStep","size","step","scale","slotProps","slots","tabIndex","track","value","valueLabelDisplay","valueLabelFormat"],W=(0,E.h)("MuiSlider");function H(e){return e}const U=(0,j.Ay)("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,t["color".concat((0,F.A)(a.color))],"medium"!==a.size&&t["size".concat((0,F.A)(a.size))],a.marked&&t.marked,"vertical"===a.orientation&&t.vertical,"inverted"===a.track&&t.trackInverted,!1===a.track&&t.trackFalse]}})((e=>{let{theme:t}=e;var a;return{borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},["&.".concat(X.disabled)]:{pointerEvents:"none",cursor:"default",color:(t.vars||t).palette.grey[400]},["&.".concat(X.dragging)]:{["& .".concat(X.thumb,", & .").concat(X.track)]:{transition:"none"}},variants:[...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e},style:{color:(t.vars||t).palette[e].main}}))),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}})),$=(0,j.Ay)("span",{name:"MuiSlider",slot:"Rail",overridesResolver:(e,t)=>t.rail})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),_=(0,j.Ay)("span",{name:"MuiSlider",slot:"Track",overridesResolver:(e,t)=>t.track})((e=>{let{theme:t}=e;var a;return{display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:t.transitions.create(["left","width","bottom","height"],{duration:t.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e,track:"inverted"},style:(0,n.default)({},t.vars?{backgroundColor:t.vars.palette.Slider["".concat(e,"Track")],borderColor:t.vars.palette.Slider["".concat(e,"Track")]}:(0,n.default)({backgroundColor:(0,N.a)(t.palette[e].main,.62),borderColor:(0,N.a)(t.palette[e].main,.62)},t.applyStyles("dark",{backgroundColor:(0,N.e$)(t.palette[e].main,.5)}),t.applyStyles("dark",{borderColor:(0,N.e$)(t.palette[e].main,.5)})))})))]}})),q=(0,j.Ay)("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.thumb,t["thumbColor".concat((0,F.A)(a.color))],"medium"!==a.size&&t["thumbSize".concat((0,F.A)(a.size))]]}})((e=>{let{theme:t}=e;var a;return{position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:t.transitions.create(["box-shadow","left","bottom"],{duration:t.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(t.vars||t).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},["&.".concat(X.disabled)]:{"&:hover":{boxShadow:"none"}},variants:[...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e},style:{["&:hover, &.".concat(X.focusVisible)]:(0,n.default)({},t.vars?{boxShadow:"0px 0px 0px 8px rgba(".concat(t.vars.palette[e].mainChannel," / 0.16)")}:{boxShadow:"0px 0px 0px 8px ".concat((0,N.X4)(t.palette[e].main,.16))},{"@media (hover: none)":{boxShadow:"none"}}),["&.".concat(X.active)]:(0,n.default)({},t.vars?{boxShadow:"0px 0px 0px 14px rgba(".concat(t.vars.palette[e].mainChannel," / 0.16)}")}:{boxShadow:"0px 0px 0px 14px ".concat((0,N.X4)(t.palette[e].main,.16))})}}))),{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}}]}})),G=(0,j.Ay)((function(e){const{children:t,className:a,value:r}=e,n=(e=>{const{open:t}=e;return{offset:(0,l.A)(t&&X.valueLabelOpen),circle:X.valueLabelCircle,label:X.valueLabelLabel}})(e);return t?o.cloneElement(t,{className:(0,l.A)(t.props.className)},(0,B.jsxs)(o.Fragment,{children:[t.props.children,(0,B.jsx)("span",{className:(0,l.A)(n.offset,a),"aria-hidden":!0,children:(0,B.jsx)("span",{className:n.circle,children:(0,B.jsx)("span",{className:n.label,children:r})})})]})):null}),{name:"MuiSlider",slot:"ValueLabel",overridesResolver:(e,t)=>t.valueLabel})((e=>{let{theme:t}=e;return(0,n.default)({zIndex:1,whiteSpace:"nowrap"},t.typography.body2,{fontWeight:500,transition:t.transitions.create(["transform"],{duration:t.transitions.duration.shortest}),position:"absolute",backgroundColor:(t.vars||t).palette.grey[600],borderRadius:2,color:(t.vars||t).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},["&.".concat(X.valueLabelOpen)]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},["&.".concat(X.valueLabelOpen)]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:t.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]})})),J=(0,j.Ay)("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>(0,O.A)(e)&&"markActive"!==e,overridesResolver:(e,t)=>{const{markActive:a}=e;return[t.mark,a&&t.markActive]}})((e=>{let{theme:t}=e;return{position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(t.vars||t).palette.background.paper,opacity:.8}}]}})),Z=(0,j.Ay)("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>(0,O.A)(e)&&"markLabelActive"!==e,overridesResolver:(e,t)=>t.markLabel})((e=>{let{theme:t}=e;return(0,n.default)({},t.typography.body2,{color:(t.vars||t).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(t.vars||t).palette.text.primary}}]})})),ee=e=>{let{children:t}=e;return t},te=o.forwardRef((function(e,t){var a,c,d,p,m,v,b,h,f,g,y,x,k,A,w,L,C,R,z,P,T,N,E,j;const O=W({props:e,name:"MuiSlider"}),D=(0,M.I)(),{"aria-label":Y,"aria-valuetext":X,"aria-labelledby":te,component:ae="span",components:re={},componentsProps:ne={},color:oe="primary",classes:le,className:ie,disableSwap:se=!1,disabled:ue=!1,getAriaLabel:ce,getAriaValueText:de,marks:pe=!1,max:me=100,min:ve=0,orientation:be="horizontal",shiftStep:he=10,size:fe="medium",step:ge=1,scale:ye=H,slotProps:xe,slots:ke,track:Se="normal",valueLabelDisplay:Ae="off",valueLabelFormat:we=H}=O,Le=(0,r.default)(O,K),Ce=(0,n.default)({},O,{isRtl:D,max:me,min:ve,classes:le,disabled:ue,disableSwap:se,orientation:be,marks:pe,color:oe,size:fe,step:ge,shiftStep:he,scale:ye,track:Se,valueLabelDisplay:Ae,valueLabelFormat:we}),{axisProps:Re,getRootProps:ze,getHiddenInputProps:Pe,getThumbProps:Te,open:Ie,active:Ne,axis:Me,focusedThumbIndex:Ee,range:je,dragging:Oe,marks:Ve,values:Fe,trackOffset:De,trackLeap:Ye,getThumbStyle:Qe}=I((0,n.default)({},Ce,{rootRef:t}));Ce.marked=Ve.length>0&&Ve.some((e=>e.label)),Ce.dragging=Oe,Ce.focusedThumbIndex=Ee;const Xe=(e=>{const{disabled:t,dragging:a,marked:r,orientation:n,track:o,classes:l,color:i,size:s}=e,c={root:["root",t&&"disabled",a&&"dragging",r&&"marked","vertical"===n&&"vertical","inverted"===o&&"trackInverted",!1===o&&"trackFalse",i&&"color".concat((0,F.A)(i)),s&&"size".concat((0,F.A)(s))],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",s&&"thumbSize".concat((0,F.A)(s)),i&&"thumbColor".concat((0,F.A)(i))],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return(0,u.A)(c,Q,l)})(Ce),Be=null!=(a=null!=(c=null==ke?void 0:ke.root)?c:re.Root)?a:U,Ke=null!=(d=null!=(p=null==ke?void 0:ke.rail)?p:re.Rail)?d:$,We=null!=(m=null!=(v=null==ke?void 0:ke.track)?v:re.Track)?m:_,He=null!=(b=null!=(h=null==ke?void 0:ke.thumb)?h:re.Thumb)?b:q,Ue=null!=(f=null!=(g=null==ke?void 0:ke.valueLabel)?g:re.ValueLabel)?f:G,$e=null!=(y=null!=(x=null==ke?void 0:ke.mark)?x:re.Mark)?y:J,_e=null!=(k=null!=(A=null==ke?void 0:ke.markLabel)?A:re.MarkLabel)?k:Z,qe=null!=(w=null!=(L=null==ke?void 0:ke.input)?L:re.Input)?w:"input",Ge=null!=(C=null==xe?void 0:xe.root)?C:ne.root,Je=null!=(R=null==xe?void 0:xe.rail)?R:ne.rail,Ze=null!=(z=null==xe?void 0:xe.track)?z:ne.track,et=null!=(P=null==xe?void 0:xe.thumb)?P:ne.thumb,tt=null!=(T=null==xe?void 0:xe.valueLabel)?T:ne.valueLabel,at=null!=(N=null==xe?void 0:xe.mark)?N:ne.mark,rt=null!=(E=null==xe?void 0:xe.markLabel)?E:ne.markLabel,nt=null!=(j=null==xe?void 0:xe.input)?j:ne.input,ot=(0,i.Q)({elementType:Be,getSlotProps:ze,externalSlotProps:Ge,externalForwardedProps:Le,additionalProps:(0,n.default)({},V(Be)&&{as:ae}),ownerState:(0,n.default)({},Ce,null==Ge?void 0:Ge.ownerState),className:[Xe.root,ie]}),lt=(0,i.Q)({elementType:Ke,externalSlotProps:Je,ownerState:Ce,className:Xe.rail}),it=(0,i.Q)({elementType:We,externalSlotProps:Ze,additionalProps:{style:(0,n.default)({},Re[Me].offset(De),Re[Me].leap(Ye))},ownerState:(0,n.default)({},Ce,null==Ze?void 0:Ze.ownerState),className:Xe.track}),st=(0,i.Q)({elementType:He,getSlotProps:Te,externalSlotProps:et,ownerState:(0,n.default)({},Ce,null==et?void 0:et.ownerState),className:Xe.thumb}),ut=(0,i.Q)({elementType:Ue,externalSlotProps:tt,ownerState:(0,n.default)({},Ce,null==tt?void 0:tt.ownerState),className:Xe.valueLabel}),ct=(0,i.Q)({elementType:$e,externalSlotProps:at,ownerState:Ce,className:Xe.mark}),dt=(0,i.Q)({elementType:_e,externalSlotProps:rt,ownerState:Ce,className:Xe.markLabel}),pt=(0,i.Q)({elementType:qe,getSlotProps:Pe,externalSlotProps:nt,ownerState:Ce});return(0,B.jsxs)(Be,(0,n.default)({},ot,{children:[(0,B.jsx)(Ke,(0,n.default)({},lt)),(0,B.jsx)(We,(0,n.default)({},it)),Ve.filter((e=>e.value>=ve&&e.value<=me)).map(((e,t)=>{const a=S(e.value,ve,me),r=Re[Me].offset(a);let i;return i=!1===Se?-1!==Fe.indexOf(e.value):"normal"===Se&&(je?e.value>=Fe[0]&&e.value<=Fe[Fe.length-1]:e.value<=Fe[0])||"inverted"===Se&&(je?e.value<=Fe[0]||e.value>=Fe[Fe.length-1]:e.value>=Fe[0]),(0,B.jsxs)(o.Fragment,{children:[(0,B.jsx)($e,(0,n.default)({"data-index":t},ct,!(0,s.g)($e)&&{markActive:i},{style:(0,n.default)({},r,ct.style),className:(0,l.A)(ct.className,i&&Xe.markActive)})),null!=e.label?(0,B.jsx)(_e,(0,n.default)({"aria-hidden":!0,"data-index":t},dt,!(0,s.g)(_e)&&{markLabelActive:i},{style:(0,n.default)({},r,dt.style),className:(0,l.A)(Xe.markLabel,dt.className,i&&Xe.markLabelActive),children:e.label})):null]},t)})),Fe.map(((e,t)=>{const a=S(e,ve,me),r=Re[Me].offset(a),o="off"===Ae?ee:Ue;return(0,B.jsx)(o,(0,n.default)({},!(0,s.g)(o)&&{valueLabelFormat:we,valueLabelDisplay:Ae,value:"function"===typeof we?we(ye(e),t):we,index:t,open:Ie===t||Ne===t||"on"===Ae,disabled:ue},ut,{children:(0,B.jsx)(He,(0,n.default)({"data-index":t},st,{className:(0,l.A)(Xe.thumb,st.className,Ne===t&&Xe.active,Ee===t&&Xe.focusVisible),style:(0,n.default)({},r,Qe(t),st.style),children:(0,B.jsx)(qe,(0,n.default)({"data-index":t,"aria-label":ce?ce(t):Y,"aria-valuenow":ye(e),"aria-labelledby":te,"aria-valuetext":de?de(ye(e),t):X,value:Fe[t]},pt))}))}),t)}))]}))})),ae=te}}]);
//# sourceMappingURL=1802.854278ce.chunk.js.map

// === 1649.a3a6757b.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[1649],{1649:(e,t,n)=>{n.r(t),n.d(t,{default:()=>D});var o=n(65043),r=n(80045),i=n(64467),a=n(58168),u=n(43024),s=n(71745),c=n(12899),l=n(97950),d=n(79892),f=n(60768),m=n(32158);function v(e){return e.substring(2).toLowerCase()}const p=function(e){var t=e.children,n=e.disableReactTree,r=void 0!==n&&n,i=e.mouseEvent,a=void 0===i?"onClick":i,u=e.onClickAway,s=e.touchEvent,c=void 0===s?"onTouchEnd":s,p=o.useRef(!1),g=o.useRef(null),E=o.useRef(!1),h=o.useRef(!1);o.useEffect((function(){return setTimeout((function(){E.current=!0}),0),function(){E.current=!1}}),[]);var A=o.useCallback((function(e){g.current=l.findDOMNode(e)}),[]),b=(0,f.A)(t.ref,A),w=(0,m.A)((function(e){var t=h.current;if(h.current=!1,E.current&&g.current&&!function(e){return document.documentElement.clientWidth<e.clientX||document.documentElement.clientHeight<e.clientY}(e))if(p.current)p.current=!1;else{var n;if(e.composedPath)n=e.composedPath().indexOf(g.current)>-1;else n=!(0,d.A)(g.current).documentElement.contains(e.target)||g.current.contains(e.target);n||!r&&t||u(e)}})),x=function(e){return function(n){h.current=!0;var o=t.props[e];o&&o(n)}},C={ref:b};return!1!==c&&(C[c]=x(c)),o.useEffect((function(){if(!1!==c){var e=v(c),t=(0,d.A)(g.current),n=function(){p.current=!0};return t.addEventListener(e,w),t.addEventListener("touchmove",n),function(){t.removeEventListener(e,w),t.removeEventListener("touchmove",n)}}}),[w,c]),!1!==a&&(C[a]=x(a)),o.useEffect((function(){if(!1!==a){var e=v(a),t=(0,d.A)(g.current);return t.addEventListener(e,w),function(){t.removeEventListener(e,w)}}}),[w,a]),o.createElement(o.Fragment,null,o.cloneElement(t,C))};var g=n(74822),E=n(146),h=n(51575),A=n(20495),b=n(82454),w=o.forwardRef((function(e,t){var n=e.action,i=e.classes,s=e.className,c=e.message,l=e.role,d=void 0===l?"alert":l,f=(0,r.A)(e,["action","classes","className","message","role"]);return o.createElement(A.A,(0,a.default)({role:d,square:!0,elevation:6,className:(0,u.A)(i.root,s),ref:t},f),o.createElement("div",{className:i.message},c),n?o.createElement("div",{className:i.action},n):null)}));const x=(0,s.A)((function(e){var t="light"===e.palette.type?.8:.98,n=(0,b.tL)(e.palette.background.default,t);return{root:(0,a.default)({},e.typography.body2,(0,i.A)({color:e.palette.getContrastText(n),backgroundColor:n,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:e.shape.borderRadius,flexGrow:1},e.breakpoints.up("sm"),{flexGrow:"initial",minWidth:288})),message:{padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}}}),{name:"MuiSnackbarContent"})(w);var C=o.forwardRef((function(e,t){var n=e.action,i=e.anchorOrigin,s=(i=void 0===i?{vertical:"bottom",horizontal:"center"}:i).vertical,l=i.horizontal,d=e.autoHideDuration,f=void 0===d?null:d,v=e.children,A=e.classes,b=e.className,w=e.ClickAwayListenerProps,C=e.ContentProps,k=e.disableWindowBlurListener,y=void 0!==k&&k,L=e.message,R=e.onClose,T=e.onEnter,j=e.onEntered,I=e.onEntering,N=e.onExit,O=e.onExited,M=e.onExiting,S=e.onMouseEnter,D=e.onMouseLeave,B=e.open,z=e.resumeHideDuration,P=e.TransitionComponent,H=void 0===P?h.A:P,W=e.transitionDuration,J=void 0===W?{enter:c.p0.enteringScreen,exit:c.p0.leavingScreen}:W,q=e.TransitionProps,F=(0,r.A)(e,["action","anchorOrigin","autoHideDuration","children","classes","className","ClickAwayListenerProps","ContentProps","disableWindowBlurListener","message","onClose","onEnter","onEntered","onEntering","onExit","onExited","onExiting","onMouseEnter","onMouseLeave","open","resumeHideDuration","TransitionComponent","transitionDuration","TransitionProps"]),_=o.useRef(),G=o.useState(!0),V=G[0],X=G[1],U=(0,m.A)((function(){R&&R.apply(void 0,arguments)})),Y=(0,m.A)((function(e){R&&null!=e&&(clearTimeout(_.current),_.current=setTimeout((function(){U(null,"timeout")}),e))}));o.useEffect((function(){return B&&Y(f),function(){clearTimeout(_.current)}}),[B,f,Y]);var K=function(){clearTimeout(_.current)},Q=o.useCallback((function(){null!=f&&Y(null!=z?z:.5*f)}),[f,z,Y]);return o.useEffect((function(){if(!y&&B)return window.addEventListener("focus",Q),window.addEventListener("blur",K),function(){window.removeEventListener("focus",Q),window.removeEventListener("blur",K)}}),[y,Q,B]),!B&&V?null:o.createElement(p,(0,a.default)({onClickAway:function(e){R&&R(e,"clickaway")}},w),o.createElement("div",(0,a.default)({className:(0,u.A)(A.root,A["anchorOrigin".concat((0,g.A)(s)).concat((0,g.A)(l))],b),onMouseEnter:function(e){S&&S(e),K()},onMouseLeave:function(e){D&&D(e),Q()},ref:t},F),o.createElement(H,(0,a.default)({appear:!0,in:B,onEnter:(0,E.A)((function(){X(!1)}),T),onEntered:j,onEntering:I,onExit:N,onExited:(0,E.A)((function(){X(!0)}),O),onExiting:M,timeout:J,direction:"top"===s?"down":"up"},q),v||o.createElement(x,(0,a.default)({message:L,action:n},C)))))}));const k=(0,s.A)((function(e){var t={top:8},n={bottom:8},o={justifyContent:"flex-end"},r={justifyContent:"flex-start"},u={top:24},s={bottom:24},c={right:24},l={left:24},d={left:"50%",right:"auto",transform:"translateX(-50%)"};return{root:{zIndex:e.zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center"},anchorOriginTopCenter:(0,a.default)({},t,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},u,d))),anchorOriginBottomCenter:(0,a.default)({},n,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},s,d))),anchorOriginTopRight:(0,a.default)({},t,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},u,c))),anchorOriginBottomRight:(0,a.default)({},n,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},s,c))),anchorOriginTopLeft:(0,a.default)({},t,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},u,l))),anchorOriginBottomLeft:(0,a.default)({},n,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},s,l)))}}),{flip:!1,name:"MuiSnackbar"})(C);var y=n(30105),L=n(80575),R=n(30458),T=n(14556),j=n(32422),I=n(53815),N=n(91688),O=n(66856),M=n(84),S=n(70579);const D=e=>{let{jobId:t,title:n,open:r,onComplete:i,alertBodyMessage:a=""}=e;const u=(0,N.useHistory)(),[s,c]=(0,o.useState)(null),[l,d]=(0,o.useState)(!1),f=(0,T.wA)(),[m,v]=(0,o.useState)(n||"Job Running In Background"),[p,g]=(0,o.useState)("info"),{closeDialog:E}=(0,M.A)("backgroundJobIndicator");(0,o.useEffect)((()=>{const e=e=>(e.preventDefault(),e.returnValue="","");return t&&r?window.addEventListener("beforeunload",e):window.removeEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}}),[t,r]);const[h,A]=(0,L.A)({minDelay:1e3,maxRetries:300,onSubmit:async e=>{let{jobId:t}=e;return await f(R.zU.getJobStatus({jobId:t}))},onError:async()=>{g("error")},shouldRetry:e=>{var t,n;let{payload:o}=e;return"queued"===(null===o||void 0===o||null===(t=o.data)||void 0===t?void 0:t.status)||"dequeued"===(null===o||void 0===o||null===(n=o.data)||void 0===n?void 0:n.status)},onComplete:i||(e=>{var t,o;if("failed"===(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.status))g("error"),v("Could not complete ".concat(n));else if("complete"===(null===e||void 0===e||null===(o=e.data)||void 0===o?void 0:o.status)){const t=null===e||void 0===e?void 0:e.data;if(g("success"),v("".concat(n," complete")),null!==t&&void 0!==t&&t.result)try{const e=JSON.parse(null===t||void 0===t?void 0:t.result);null!==e&&void 0!==e&&e.chart?c(null===e||void 0===e?void 0:e.chart):v("".concat(n," complete"))}catch(r){v("".concat(n," complete"))}else v("".concat(n," complete"))}E()})});(0,o.useEffect)((()=>{t&&(g("info"),v("".concat(n)||"Job Running In Background"),d(!1),c(null),A({jobId:t}))}),[t]);return r&&(0,S.jsx)(k,{open:("info"===p&&h||"error"===p||"success"===p)&&!l,anchorOrigin:{vertical:"bottom",horizontal:"left"},autoHideDuration:"error"===p?2e3:"success"===p?5e3:null,children:(0,S.jsxs)(j.A,{onClose:()=>{d(!0)},severity:p,children:[a?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(I.A,{children:[m," "]}),a]}):(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(I.A,{children:m})}),s&&(0,S.jsx)(y.A,{size:"small",variant:"contained",color:"primary",onClick:()=>{var e;e=s,c(null),d(!0),g("info"),u.push((0,O.si)({orgId:e.organization,chartId:e.id||e._id,resource:"chart",resourceAction:"view",base:"protected"}))},children:"Visit Chart"})]})})}},53815:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(58168),r=n(80045),i=n(65043),a=n(71745),u=n(66187),s=n(43024),c=i.forwardRef((function(e,t){var n=e.classes,a=e.className,c=(0,r.A)(e,["classes","className"]);return i.createElement(u.A,(0,o.default)({gutterBottom:!0,component:"div",ref:t,className:(0,s.A)(n.root,a)},c))}));const l=(0,a.A)((function(e){return{root:{fontWeight:e.typography.fontWeightMedium,marginTop:-2}}}),{name:"MuiAlertTitle"})(c)}}]);
//# sourceMappingURL=1649.a3a6757b.chunk.js.map

// === 927.ffef0aca.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[927],{927:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var r=n(91688),o=n(61531),a=n(84187),s=n(72835),c=n(96364),i=n(10035),l=n(86825),d=n(70579);const h=e=>{const{completeTour:t,addSteps:n,goNext:h,skipTour:u,MACRO_TOURS:p,setActiveTour:g}=e,x=(0,r.useHistory)(),m=async e=>(t({userInput:{nextSelectedStep:e},resetActiveTour:!1}),"finish"===e?(n({steps:[{...p.macro_helpBtn,actions:[{type:"back"},{type:"complete"}]}]}),h()):"themes"===e?g({tour:"themesTour",stepsToAdd:[{...p.macro_chartThemesBtn}]}):"share"===e?g({tour:"shareOptionsTour",stepsToAdd:[{...p.macro_chartShareBtn}]}):"import"===e?(x.push("/dashboard/import"),g({tour:"importsTour"})):"integration"===e?(x.push("/dashboard/integrations"),g({tour:null})):void 0);return(0,d.jsxs)(i.A,{...e,onDialogClose:u,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},children:[(0,d.jsx)("img",{src:l.t.confetti,alt:"Congratulations",style:{position:"absolute",left:"5%",height:"250px",width:"900px"}}),(0,d.jsx)(o.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,d.jsxs)(o.A,{mt:5,display:"flex",flexDirection:"column",gridGap:4,children:[(0,d.jsx)(c.A,{variant:"h1",align:"center",weight:"bold",style:{zIndex:2},children:"Basic training complete!"}),(0,d.jsx)(c.A,{align:"center",variant:"body1",style:{color:"#626262",zIndex:2},children:"You've mastered the Organimi basics. Easy, right? Now you're set to craft incredible charts independently. But there's much more to explore - discover how Organimi can elevate your charts even further."}),(0,d.jsx)(o.A,{mt:4,mb:10,alignSelf:"center",children:(0,d.jsx)(s.A,{variant:"contained",style:{width:250},color:"primary",onClick:()=>m("finish"),children:"Explore your chart!"})}),(0,d.jsx)(c.A,{variant:"body1",weight:"bold",align:"center",children:"Or, learn how to use other great features with these quick tutorials:"}),(0,d.jsxs)(o.A,{display:"flex",justifyContent:"space-evenly",gridGap:16,mt:2,children:[(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("themes"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.style,alt:"Large organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Styling your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Learn to apply preset styles or create custom themes."})]})]})}),(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("share"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.share,alt:"Small organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Sharing your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Invite others to review or collaborate on your charts."})]})]})})]})]})})]})}},10035:(e,t,n)=>{n.d(t,{A:()=>b});var r=n(57528),o=n(35007),a=n(70567),s=n(61531),c=n(35801),i=n(43867),l=n(52907),d=n(71233),h=n(5816),u=n(65043),p=n(72119);const g=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:t,dynamicAnchorDataAttribute:n},activeTourUserInput:r,skipTour:o}=e;let a=null;const s=(0,u.useRef)(!0);if(n){const e=n,t=null===r||void 0===r?void 0:r[e];a=e&&t?"".concat(e,"_").concat(t):null}const c=a||t,i=(0,u.useRef)(),l=(0,u.useRef)(0),d=c&&g(c),[h,p]=(0,u.useState)(null);return(0,u.useEffect)((()=>(i.current&&clearInterval(i.current),c&&(i.current=setInterval((()=>{l.current++;const e=c&&g(c);e&&(clearInterval(i.current),p(e||null),l.current=0),!e&&l.current>60&&s.current&&(clearInterval(i.current),s.current=!1,o({errored:!0}))}),100)),()=>{i.current&&clearInterval(i.current)})),[c]),(0,u.useEffect)((()=>()=>{i.current&&clearInterval(i.current)}),[]),d||h||null}var m,v=n(64418),f=n(70579);const y="tour-highlight-anchor",A=(0,p.Ay)(o.A)(m||(m=(0,r.A)(["\n  ","\n"])),(e=>{let{theme:t,width:n,anchorOffset:r,zIndex:o=1e4}=e;return"\n    z-index: ".concat(o,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(t.breakpoints.values[n]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),b=e=>{let{children:t,onDialogClose:n,actionsUI:r,...o}=e;const{activeTourStep:p}=o,g=(0,u.useRef)(null),m=(0,a.A)(),[b,w]=(0,u.useState)(null),j=x(o),{confirmAction:I}=(0,v.A)(),{anchorClass:S,backdrop:z,arrow:C,highlightAnchor:T,ignoreAnchorZIndex:k,zIndex:D,anchorOffset:O,backdropAboveModal:B,position:L,showDialogUI:R=!1}=p||{},V=[!0,!1].includes(z)?z:!!j,_=[!0,!1].includes(C)?C:!!j,E=[!0,!1].includes(T)?T:V&&!!j,G=L||"left-start",M=(B?m.zIndex.modal:m.zIndex.drawer)+1,P=M+1;if((0,u.useEffect)((()=>{if(j)return V&&!k&&(j.style.zIndex=(j.style.zIndex||0)+P),E&&j.classList.toggle(y),S&&j.classList.toggle(S),()=>{j&&(V&&!k&&(j.style.zIndex=j.style.zIndex-P),E&&j.classList.toggle(y),S&&j.classList.toggle(S))}}),[j]),null!==p&&void 0!==p&&p.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(s.A,{children:[R&&(0,f.jsxs)(c.A,{open:!0,onClose:()=>{n&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{n()}})},fullWidth:!(null===p||void 0===p||!p.size),maxWidth:(null===p||void 0===p?void 0:p.size)||"sm",children:[(0,f.jsx)(s.A,{zIndex:1,children:(0,f.jsx)(h.A,{densePadding:!0,onClose:n||null})}),(0,f.jsx)(i.A,{style:{padding:0},children:t}),r&&(0,f.jsx)(l.A,{style:{display:"block",padding:"24px"},children:r})]}),!R&&j&&(0,f.jsx)(d.A,{open:V,style:{zIndex:M},children:(0,f.jsxs)(A,{open:!0,ref:g,anchorEl:j,placement:G,zIndex:D,width:(null===p||void 0===p?void 0:p.size)||"sm",showBackdrop:V,anchorOffset:O,modifiers:{arrow:{enabled:_,element:b}},children:[_&&(0,f.jsx)(s.A,{className:"arrow",ref:w,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),t]})})]})}},86825:(e,t,n)=>{n.d(t,{t:()=>o});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),o={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=927.ffef0aca.chunk.js.map
