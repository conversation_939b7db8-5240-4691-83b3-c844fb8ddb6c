//# sourceMappingURL=main.9e9a31d6.js.map

// === 9620.a9f36491.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9620],{79620:(n,e,t)=>{t.r(e),t.d(e,{LetsStart:()=>s,default:()=>d});var r=t(61531),o=t(72835),a=t(96364),c=t(10035),l=t(86825),i=t(70579);const s=n=>{const{goNext:e,activeTourUserInput:t,skipTour:s}=n,d=!!(null!==t&&void 0!==t&&t.companyName&&null!==t&&void 0!==t&&t.jobTitle&&null!==t&&void 0!==t&&t.jobFunction&&null!==t&&void 0!==t&&t.industryType);return(0,i.jsx)(c.A,{...n,onDialogClose:()=>{d?s():e()},activeTourStep:{...(null===n||void 0===n?void 0:n.activeTourStep)||{},size:"sm",showDialogUI:!0},actionsUI:(0,i.jsx)(r.A,{display:"flex",justifyContent:"flex-end",children:(0,i.jsx)(o.A,{size:"large",color:"primary",variant:"contained",onClick:()=>{d?e({increment:2}):e()},children:"Let's go!"})}),children:(0,i.jsx)(r.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,i.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:32,children:[(0,i.jsx)("img",{width:250,height:250,src:l.t.buildFirstChart,alt:"Let's build your first chart"}),(0,i.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,i.jsx)(a.A,{variant:"h1",style:{fontWeight:"bold"},children:"Let's build your first chart."}),(0,i.jsx)(a.A,{variant:"subtitle2",style:{color:"#626262"},children:"We'll guide your through each step."})]})]})})})},d=s},10035:(n,e,t)=>{t.d(e,{A:()=>w});var r=t(57528),o=t(35007),a=t(70567),c=t(61531),l=t(35801),i=t(43867),s=t(52907),d=t(71233),u=t(5816),g=t(65043),h=t(72119);const p=n=>document.querySelector("#".concat(n,', [data-tour-anchor="').concat(n,'"]'));function x(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:e,dynamicAnchorDataAttribute:t},activeTourUserInput:r,skipTour:o}=n;let a=null;const c=(0,g.useRef)(!0);if(t){const n=t,e=null===r||void 0===r?void 0:r[n];a=n&&e?"".concat(n,"_").concat(e):null}const l=a||e,i=(0,g.useRef)(),s=(0,g.useRef)(0),d=l&&p(l),[u,h]=(0,g.useState)(null);return(0,g.useEffect)((()=>(i.current&&clearInterval(i.current),l&&(i.current=setInterval((()=>{s.current++;const n=l&&p(l);n&&(clearInterval(i.current),h(n||null),s.current=0),!n&&s.current>60&&c.current&&(clearInterval(i.current),c.current=!1,o({errored:!0}))}),100)),()=>{i.current&&clearInterval(i.current)})),[l]),(0,g.useEffect)((()=>()=>{i.current&&clearInterval(i.current)}),[]),d||u||null}var m,v=t(64418),f=t(70579);const y="tour-highlight-anchor",A=(0,h.Ay)(o.A)(m||(m=(0,r.A)(["\n  ","\n"])),(n=>{let{theme:e,width:t,anchorOffset:r,zIndex:o=1e4}=n;return"\n    z-index: ".concat(o,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(e.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),w=n=>{let{children:e,onDialogClose:t,actionsUI:r,...o}=n;const{activeTourStep:h}=o,p=(0,g.useRef)(null),m=(0,a.A)(),[w,b]=(0,g.useState)(null),I=x(o),{confirmAction:j}=(0,v.A)(),{anchorClass:z,backdrop:C,arrow:k,highlightAnchor:S,ignoreAnchorZIndex:T,zIndex:D,anchorOffset:L,backdropAboveModal:V,position:O,showDialogUI:F=!1}=h||{},U=[!0,!1].includes(C)?C:!!I,P=[!0,!1].includes(k)?k:!!I,B=[!0,!1].includes(S)?S:U&&!!I,E=O||"left-start",M=(V?m.zIndex.modal:m.zIndex.drawer)+1,R=M+1;if((0,g.useEffect)((()=>{if(I)return U&&!T&&(I.style.zIndex=(I.style.zIndex||0)+R),B&&I.classList.toggle(y),z&&I.classList.toggle(z),()=>{I&&(U&&!T&&(I.style.zIndex=I.style.zIndex-R),B&&I.classList.toggle(y),z&&I.classList.toggle(z))}}),[I]),null!==h&&void 0!==h&&h.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(c.A,{children:[F&&(0,f.jsxs)(l.A,{open:!0,onClose:()=>{t&&j({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===h||void 0===h||!h.size),maxWidth:(null===h||void 0===h?void 0:h.size)||"sm",children:[(0,f.jsx)(c.A,{zIndex:1,children:(0,f.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,f.jsx)(i.A,{style:{padding:0},children:e}),r&&(0,f.jsx)(s.A,{style:{display:"block",padding:"24px"},children:r})]}),!F&&I&&(0,f.jsx)(d.A,{open:U,style:{zIndex:M},children:(0,f.jsxs)(A,{open:!0,ref:p,anchorEl:I,placement:E,zIndex:D,width:(null===h||void 0===h?void 0:h.size)||"sm",showBackdrop:U,anchorOffset:L,modifiers:{arrow:{enabled:P,element:w}},children:[P&&(0,f.jsx)(c.A,{className:"arrow",ref:b,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),e]})})]})}},86825:(n,e,t)=>{t.d(e,{t:()=>o});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),o={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=9620.a9f36491.chunk.js.map

// === 9530.c070bb25.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9530],{89530:(e,a,t)=>{t.r(a),t.d(a,{default:()=>ie});var n=t(65043),r=t(59691),o=t(61531),l=t(40454),i=t(9579),c=t(38325),s=t(14370),d=t(16853),h=t(64759),m=t(67503),g=t(77887),p=t(695),u=t(72835),A=t(86852),x=t(53462),b=t(43331),C=t(53109),y=t(75687),f=t(49092),v=t(84),j=t(64418),w=t(66856),I=t(91688),T=t(14556),S=t(31777),k=t(67264),N=t(51836),O=t(64021),P=t(75156),z=t(24241),R=t(42006),D=t(59177),_=t(97105),B=t(49015),F=t(47556);const E=t.p+"static/media/logo.0cf248a2d9390f8ce578d777b5920f8f.svg";var G=t(9037),M=t(356),H=t(58168),L=t(80045),U=t(43024),Q=t(74822),Z=t(71745),V=n.forwardRef((function(e,a){var t=e.classes,r=e.className,o=e.color,l=void 0===o?"grey":o,i=e.variant,c=void 0===i?"default":i,s=(0,L.A)(e,["classes","className","color","variant"]);return n.createElement("span",(0,H.default)({className:(0,U.A)(t.root,r,"inherit"!==l&&t["".concat(c).concat((0,Q.A)(l))]),ref:a},s))}));const W=(0,Z.A)((function(e){return{root:{display:"flex",alignSelf:"baseline",borderStyle:"solid",borderWidth:2,padding:4,borderRadius:"50%",boxShadow:e.shadows[2],marginTop:8,marginBottom:8},defaultGrey:{borderColor:"transparent",color:e.palette.grey[50],backgroundColor:e.palette.grey[400]},outlinedGrey:{boxShadow:"none",color:e.palette.grey.contrastText,borderColor:e.palette.grey[400],backgroundColor:"transparent"},defaultPrimary:{borderColor:"transparent",color:e.palette.primary.contrastText,backgroundColor:e.palette.primary.main},outlinedPrimary:{boxShadow:"none",backgroundColor:"transparent",borderColor:e.palette.primary.main},defaultSecondary:{borderColor:"transparent",color:e.palette.secondary.contrastText,backgroundColor:e.palette.secondary.main},outlinedSecondary:{boxShadow:"none",backgroundColor:"transparent",borderColor:e.palette.secondary.main}}}),{name:"MuiTimelineDot"})(V);var q=t(66588),Y=t(90930),J=t(26019),K=t(43940),X=t(47088),$=t(78396),ee=t(85865),ae=t(37294),te=t(50543),ne=t(63296),re=t(86597),oe=t(70579);const le=[{name:"name",label:"Chart Name",align:"left",sortable:!0,variant:"link"},{name:"updated",label:"Last Updated",align:"left",sortable:!0},{name:"roleCount",label:"Total Roles",align:"left",sortable:!0},{name:"chartType",label:"Chart Type",align:"right",sortable:!1},{name:"alias",label:"Alias",align:"right",sortable:!1}],ie=()=>{var e;const a=(0,I.useHistory)(),{orgId:t}=(0,I.useParams)(),{t:H}=(0,f.B)(),[L,U]=(0,n.useState)(""),Q=(0,T.wA)(),{show:Z}=(0,q.A)(),{gotoOwnershipChart:V}=(0,ne.A)(),ie=(0,re.A)(),ce=(0,T.d4)(b.qi),se=(0,T.d4)(b.wO),de=(0,T.d4)(R.eW),{openDialog:he}=(0,v.A)("copyMove"),{openDialog:me}=(0,v.A)("importFile"),{confirmAction:ge}=(0,j.A)(),{openDialog:pe}=(0,v.A)("export2Dialog"),{openDialog:ue}=(0,v.A)("deleteChart"),{openDialog:Ae}=(0,v.A)("editChart"),{toggleDialog:xe}=(0,v.A)("newChart"),{onTourEventComplete:be}=(0,K.M)({featureTours:["utilizeTemplateTour","onboarding"]}),Ce=(0,F.A)(),ye=(0,T.d4)(Y.Qn),{owners:fe,subsidiaries:ve}=(0,T.d4)((0,te.a2)(t||"")),je=((null===fe||void 0===fe?void 0:fe.length)||0)+((null===ve||void 0===ve?void 0:ve.length)||0),{rows:we,page:Ie,order:Te,createSortHandler:Se,totalCount:ke,handleSearch:Ne,rowsPerPage:Oe,handleChangePage:Pe,handleChangeRowsPerPage:ze}=(0,y.s)({dataSelector:b.wO,defaultValues:{order:"asc",orderBy:"name",rowsPerPage:25}}),Re=we.reduce(((e,a)=>{e.push(a);const t=ce[null===a||void 0===a?void 0:a.id];return null!==t&&void 0!==t&&t.length&&e.push(...t),e}),ce.shared||[]);if(je){const e=[...fe||[],...ve||[]].sort(((e,a)=>{const t=new Date(e.updatedAt||"").getTime();return new Date(a.updatedAt||"").getTime()-t}))[0].updatedAt;Re.unshift({id:"".concat(ie.name," Ownership Chart"),name:"Ownership Chart",organization:t,updated:(new Date).toISOString(),type:X.NB.Traditional,counts:{role:je+1},updated_at:e,isOwnership:!0})}const De=ke+((null===(e=ce.shared)||void 0===e?void 0:e.length)||0)+(je?1:0),{setViewOption:_e}=(0,n.useContext)(G.r),{isItemSelected:Be,handleSelectAll:Fe,handleSelectItem:Ee,resetSelected:Ge,selected:Me}=(0,y.U)({selectField:"id"}),He=async()=>{xe()},Le=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"traditional";return o=>{o&&o.stopPropagation(),be({event:"charts-list-chart--click"}),"ownership"===r?V(ie)(null):a.push((0,w.si)({orgId:e,chartId:t.id,resource:"chart",resourceAction:t.status===X.N4.Published?$.uI.DEFAULT:$.uI.AI_INSIGHTS,base:"protected"})),n&&_e(n)}},Ue=e=>a=>{a.stopPropagation(),ge({title:"Are you sure you want to remove your access to this shared chart?",message:e.name,execFunc:async()=>{const{error:a}=await Q(_.Bf.removeChartAccess({orgId:e.organization,chartId:e.id}));a||(Ge(),1===se.length&&se[0].organization===e.organization&&(Q((0,B.Uu)({id:e.organization})),Ce()))}})},{handleButtonActionClick:Qe}=(0,k.A)({delete:()=>ue({title:"Confirm delete of ".concat(Me.length," chart(s)"),execFunc:async()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Delete Chart",chartNames:Me.reduce(((e,a)=>{const t=se.find((e=>e.id===a));return t&&e.push(t.name),e}),[])}});const{error:e}=await Q(C.te.delete({data:Me.map((e=>({id:e})))}));e||Ge()}}),edit:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Chart Settings"}});const e=Me[0],a=ye(e);a?Ae({chart:a}):Z("Could not find chart to edit","error")},export:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Export Chart"}}),pe({chartId:Me[0],orgId:t})},print:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Print Chart"}});const e=Me[0];a.push((0,w.Qo)({orgId:t,chartId:e,resource:"chart",base:"protected"}))},fileCopy:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Copy Chart"}});const e=Me[0],a=(se.find((a=>{var t;return(null===a||void 0===a||null===(t=a.id)||void 0===t?void 0:t.toString())===e}))||{}).name;he({mode:"copy",chartId:e,orgId:t,selector:b.wO,chartName:a})},import:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Import Chart"}}),me()}}),Ze=Re.filter((e=>"ownership"!==e.type));return(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)(o.A,{p:2,borderBottom:"solid 1px #ddd",children:(0,oe.jsxs)(l.A,{container:!0,justifyContent:"owner"===de||"admin"===de?"space-between":"flex-end",alignItems:"center",children:[(0,oe.jsx)(l.A,{item:!0,xs:!0,children:("owner"===de||"admin"===de)&&(0,oe.jsx)(S.A,{buttonGroup:"organization_chartlist",selectedCount:Me.length,handleActionClick:Qe,getIsButtonValid:e=>{if(Me.map((e=>ye(e))).find((e=>e.status===X.N4.Draft)))return"delete"===e;if(1===Me.length){const a=ye(Me[0]);return null===a||void 0===a||!a.alias||!a.alias.roleId||-1===["fileCopy"].indexOf(e)}return[]},userType:de})}),(0,oe.jsx)(l.A,{item:!0,children:(0,oe.jsxs)(l.A,{container:!0,alignItems:"center",children:[(0,oe.jsx)(A.A,{spacing:{right:2},children:(0,oe.jsx)(x.A,{handleSearch:e=>{var a;const t=null===e||void 0===e||null===(a=e.target)||void 0===a?void 0:a.value;U(t||""),Ne(e)},handleClear:()=>{U(""),Ne()},query:L,placeholder:H("Common.Labels.SearchCharts"),defaultValue:""})}),("owner"===de||"admin"===de)&&(0,oe.jsx)(u.A,{"data-tour-anchor":"new-chart-button-dashboard",variant:"contained",color:"primary",onClick:He,children:H("Common.Buttons.NewChart")})]})})]})}),De>0&&(0,oe.jsx)(o.A,{flex:1,overflow:"auto",height:"100%",children:(0,oe.jsxs)(m.A,{"aria-label":"dashboard",size:"small",stickyHeader:!0,children:[(0,oe.jsx)(h.A,{children:(0,oe.jsxs)(p.A,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",children:(0,oe.jsx)(d.A,{indeterminate:Me.length>0&&Me.length<Ze.length,checked:Me.length===Ze.length,onClick:Fe(Ze),inputProps:{"aria-label":"Select all charts"},size:"small"})}),le.map(((e,a)=>"alias"===e.name?(0,oe.jsx)(g.A,{children:"File Type"},"organization-chartlist-headcell-".concat(e.name,"- ").concat(a)):"chartType"===e.name?(0,oe.jsx)(g.A,{children:"Chart Type"},"organization-chartlist-headcell-".concat(e.name,"- ").concat(a)):e.sortable?(0,oe.jsx)(g.A,{align:e.align||"left",children:(0,oe.jsx)(s.A,{onClick:Se(e.name),direction:Te,children:e.label})},"organization-chartlist-headcell-".concat(e.name)):(0,oe.jsx)(g.A,{align:e.align||"left",children:e.label},"organization-chartlist-headcell-".concat(e.name))))]})}),(0,oe.jsx)(r.A,{children:Re.map(((e,a)=>{var t,n;return(0,oe.jsx)(J.FF,{"data-tour-anchor":0===a?"charts-list-first-chart":"charts-list-chart",onClick:Le(e.organization,e,"list",e.type),hover:!0,style:{position:"relative"},children:(0,oe.jsxs)(oe.Fragment,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",style:{paddingLeft:null!==(t=e.alias)&&void 0!==t&&t.roleId?28:18,borderLeft:null!==(n=e.alias)&&void 0!==n&&n.roleId?"solid 4px #3CD3C2":"none"},children:(0,oe.jsx)(d.A,{checked:Be(e),onClick:Ee(e),inputProps:{"aria-label":"Select chart ".concat(e.name)},size:"small",disabled:"ownership"===(null===e||void 0===e?void 0:e.type)})}),le.map((a=>{var t,n,r,l,c;return"updated"===a.name?(0,oe.jsx)(g.A,{scope:"row",children:(0,oe.jsx)(ee.A,{variant:"bodySM",color:ae.Qs.Neutrals[800],children:z.c9.fromISO(e[a.name]).toLocaleString(z.c9.DATE_FULL)})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"type"===a.name?(0,oe.jsx)(g.A,{scope:"row",children:"board of directors"===e[a.name]?(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)("img",{src:E,alt:"Board of directors",width:20,height:20,style:{marginRight:5,verticalAlign:"bottom"}}),"Board of Directors"]}):e[a.name].charAt(0).toUpperCase()+e[a.name].slice(1)},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"chartType"===a.name?(0,oe.jsx)(g.A,{children:(0,oe.jsx)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:(0,oe.jsx)(ee.A,{variant:"bodySM",fontWeight:e.status===X.N4.Draft?"600":"normal",color:e.status===X.N4.Draft?ae.Qs.Info[600]:ae.Qs.Neutrals[800],children:(r=e||"",r.status===X.N4.Draft?"AI Suggested":(0,D.Sn)(r.type))})})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"alias"===a.name?(0,oe.jsx)(g.A,{children:(null===(l=e.alias)||void 0===l?void 0:l.roleId)&&(0,oe.jsxs)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:[(0,oe.jsx)(W,{variant:"outlined",color:"secondary"}),(0,oe.jsx)(J.iU,{children:"Subchart"})]})||(0,oe.jsxs)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:[(0,oe.jsx)(W,{variant:"outlined",color:"primary"}),(0,oe.jsx)(J.iU,{children:null!==e&&void 0!==e&&e.isOwnership?"Ownership":"Main"})]})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"roleCount"===a.name?(0,oe.jsx)(g.A,{children:(0,oe.jsx)(ee.A,{variant:"bodySM",color:ae.Qs.Neutrals[900],children:(null===e||void 0===e||null===(c=e.counts)||void 0===c?void 0:c.role)||"--"})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):(0,oe.jsxs)(g.A,{scope:"row",children:["name"===a.name&&(0,oe.jsx)(ee.A,{variant:"bodyMD",color:ae.Qs.Neutrals[900],children:e[a.name]}),"name"===a.name&&(null===(t=e.chartIntegration)||void 0===t?void 0:t.syncEnabled)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Synced",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",children:(0,oe.jsx)(P.Ay,{icon:"Sync",size:"lg",color:"#3CD3C2"})})})]}),"name"===a.name&&e.shareAccess&&("public"===e.shareAccess||"chart"===e.shareAccess)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Shared",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",children:(0,oe.jsx)(P.Ay,{icon:"Shared",size:"lg",color:"#5C2DBF"})})})]}),"name"===a.name&&e.shareAccess&&"chart"===e.shareAccess&&"saml-login"!==(null===e||void 0===e||null===(n=e.permission)||void 0===n?void 0:n.type)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Remove Shared Access",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",onClick:Ue(e),children:(0,oe.jsx)(P.Ay,{icon:"RemoveAccess",size:"lg",color:"#5C2DBF"})})})]})]},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name))}))]})},"organization-chartlist-bodyrow-".concat(e.id))}))})]})}),De>Oe&&(0,oe.jsx)(o.A,{position:"sticky",left:0,right:0,bottom:0,bgcolor:"#ffffff",borderTop:"solid 1px #ddd",children:(0,oe.jsx)(c.A,{rowsPerPageOptions:[25,50,100,250],component:"div",count:ke,rowsPerPage:Oe,page:Ie,backIconButtonProps:{"aria-label":H("Common.Tables.backIconButtonText")},nextIconButtonProps:{"aria-label":H("Common.Tables.nextIconButtonText")},onPageChange:Pe,onRowsPerPageChange:ze,labelRowsPerPage:H("Common.Tables.labelRowsPerPage"),backIconButtonText:H("Common.Tables.backIconButtonText"),nextIconButtonText:H("Common.Tables.nextIconButtonText"),labelDisplayedRows:e=>{let{from:a,to:t,count:n}=e;return"".concat(a,"-").concat(t," ").concat(H("Common.Tables.of")," ").concat(n)}})}),!De&&("admin"===de||"owner"===de)&&(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)(m.A,{"aria-label":"dashboard",size:"small",stickyHeader:!0,children:(0,oe.jsx)(h.A,{children:(0,oe.jsxs)(p.A,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",children:(0,oe.jsx)(d.A,{indeterminate:Me.length>0&&Me.length<Re.length,checked:Me.length===Re.length,onClick:Fe(Re),inputProps:{"aria-label":"Select all charts"},size:"small"})}),le.map((e=>"alias"===e.name?(0,oe.jsx)(g.A,{children:"File Type"},"organization-chartlist-headcell-".concat(e.name)):"chartType"===e.name?(0,oe.jsx)(g.A,{children:"Chart Type"},"organization-chartlist-headcell-".concat(e.name)):e.sortable?(0,oe.jsx)(g.A,{align:e.align||"left",children:(0,oe.jsx)(s.A,{onClick:Se(e.name),direction:Te,children:e.label})},"organization-chartlist-headcell-".concat(e.name)):(0,oe.jsx)(g.A,{align:e.align||"left",children:e.label},"organization-chartlist-headcell-".concat(e.name))))]})})}),(0,oe.jsx)(o.A,{flexGrow:1,display:"flex",justifyContent:"flex-start",mt:4,flexDirection:"column",children:(0,oe.jsx)(O.A,{text:H("General.Text.LetsBuildYourFirstChart"),image:N.A,handleClick:He,buttonText:H("General.Text.CreatYourFirstChart"),isButtonVisible:"owner"===de||"admin"===de})})]})]})}}}]);
//# sourceMappingURL=9530.c070bb25.chunk.js.map

// === 9454.1da5939b.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9454],{3359:(t,e,o)=>{o.d(e,{A:()=>c});var r=o(58168),n=o(80045),a=o(65043),l=o(43024),i=o(71745),s=a.forwardRef((function(t,e){var o=t.classes,i=t.className,s=t.component,c=void 0===s?"div":s,d=(0,n.A)(t,["classes","className","component"]);return a.createElement(c,(0,r.default)({ref:e,className:(0,l.A)(o.root,i)},d))}));const c=(0,i.A)({root:{width:"100%",overflowX:"auto"}},{name:"MuiTableContainer"})(s)},25754:(t,e,o)=>{o.d(e,{A:()=>S});var r=o(98587),n=o(58168),a=o(65043),l=(o(30805),o(69292)),i=o(68606),s=o(34535),c=o(72876),d=o(15294),u=o(60587),f=o(57056),b=o(32400);function v(t){return(0,b.Ay)("MuiAvatarGroup",t)}const p=(0,f.A)("MuiAvatarGroup",["root","avatar"]);var m=o(70579);const h=["children","className","component","componentsProps","max","renderSurplus","slotProps","spacing","total","variant"],g={small:-16,medium:null},A=(0,s.Ay)("div",{name:"MuiAvatarGroup",slot:"Root",overridesResolver:(t,e)=>(0,n.default)({["& .".concat(p.avatar)]:e.avatar},e.root)})((t=>{let{theme:e,ownerState:o}=t;const r=o.spacing&&void 0!==g[o.spacing]?g[o.spacing]:-o.spacing;return{["& .".concat(d.A.root)]:{border:"2px solid ".concat((e.vars||e).palette.background.default),boxSizing:"content-box",marginLeft:null!=r?r:-8,"&:last-child":{marginLeft:0}},display:"flex",flexDirection:"row-reverse"}})),S=a.forwardRef((function(t,e){var o;const s=(0,c.A)({props:t,name:"MuiAvatarGroup"}),{children:d,className:f,component:b="div",componentsProps:p={},max:g=5,renderSurplus:S,slotProps:w={},spacing:x="medium",total:y,variant:B="circular"}=s,C=(0,r.default)(s,h);let R=g<2?2:g;const M=(0,n.default)({},s,{max:g,spacing:x,component:b,variant:B}),I=(t=>{const{classes:e}=t;return(0,i.A)({root:["root"],avatar:["avatar"]},v,e)})(M),N=a.Children.toArray(d).filter((t=>a.isValidElement(t))),E=y||N.length;E===R&&(R+=1),R=Math.min(E+1,R);const z=Math.min(N.length,R-1),L=Math.max(E-R,E-z,0),T=S?S(L):"+".concat(L),k=null!=(o=w.additionalAvatar)?o:p.additionalAvatar;return(0,m.jsxs)(A,(0,n.default)({as:b,ownerState:M,className:(0,l.A)(I.root,f),ref:e},C,{children:[L?(0,m.jsx)(u.A,(0,n.default)({variant:B},k,{className:(0,l.A)(I.avatar,null==k?void 0:k.className),children:T})):null,N.slice(0,z).reverse().map((t=>a.cloneElement(t,{className:(0,l.A)(t.props.className,I.avatar),variant:t.props.variant||B})))]}))}))},39948:(t,e,o)=>{o.d(e,{A:()=>x});var r=o(98587),n=o(58168),a=o(65043),l=o(69292),i=o(68606),s=o(67266),c=o(11640),d=o(6803),u=o(34535),f=o(72876),b=o(57056),v=o(32400);function p(t){return(0,v.Ay)("MuiButtonGroup",t)}const m=(0,b.A)("MuiButtonGroup",["root","contained","outlined","text","disableElevation","disabled","firstButton","fullWidth","vertical","grouped","groupedHorizontal","groupedVertical","groupedText","groupedTextHorizontal","groupedTextVertical","groupedTextPrimary","groupedTextSecondary","groupedOutlined","groupedOutlinedHorizontal","groupedOutlinedVertical","groupedOutlinedPrimary","groupedOutlinedSecondary","groupedContained","groupedContainedHorizontal","groupedContainedVertical","groupedContainedPrimary","groupedContainedSecondary","lastButton","middleButton"]);var h=o(74221),g=o(93053),A=o(70579);const S=["children","className","color","component","disabled","disableElevation","disableFocusRipple","disableRipple","fullWidth","orientation","size","variant"],w=(0,u.Ay)("div",{name:"MuiButtonGroup",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[{["& .".concat(m.grouped)]:e.grouped},{["& .".concat(m.grouped)]:e["grouped".concat((0,d.A)(o.orientation))]},{["& .".concat(m.grouped)]:e["grouped".concat((0,d.A)(o.variant))]},{["& .".concat(m.grouped)]:e["grouped".concat((0,d.A)(o.variant)).concat((0,d.A)(o.orientation))]},{["& .".concat(m.grouped)]:e["grouped".concat((0,d.A)(o.variant)).concat((0,d.A)(o.color))]},{["& .".concat(m.firstButton)]:e.firstButton},{["& .".concat(m.lastButton)]:e.lastButton},{["& .".concat(m.middleButton)]:e.middleButton},e.root,e[o.variant],!0===o.disableElevation&&e.disableElevation,o.fullWidth&&e.fullWidth,"vertical"===o.orientation&&e.vertical]}})((t=>{let{theme:e,ownerState:o}=t;return(0,n.default)({display:"inline-flex",borderRadius:(e.vars||e).shape.borderRadius},"contained"===o.variant&&{boxShadow:(e.vars||e).shadows[2]},o.disableElevation&&{boxShadow:"none"},o.fullWidth&&{width:"100%"},"vertical"===o.orientation&&{flexDirection:"column"},{["& .".concat(m.grouped)]:(0,n.default)({minWidth:40,"&:hover":(0,n.default)({},"contained"===o.variant&&{boxShadow:"none"})},"contained"===o.variant&&{boxShadow:"none"}),["& .".concat(m.firstButton,",& .").concat(m.middleButton)]:(0,n.default)({},"horizontal"===o.orientation&&{borderTopRightRadius:0,borderBottomRightRadius:0},"vertical"===o.orientation&&{borderBottomRightRadius:0,borderBottomLeftRadius:0},"text"===o.variant&&"horizontal"===o.orientation&&{borderRight:e.vars?"1px solid rgba(".concat(e.vars.palette.common.onBackgroundChannel," / 0.23)"):"1px solid ".concat("light"===e.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"),["&.".concat(m.disabled)]:{borderRight:"1px solid ".concat((e.vars||e).palette.action.disabled)}},"text"===o.variant&&"vertical"===o.orientation&&{borderBottom:e.vars?"1px solid rgba(".concat(e.vars.palette.common.onBackgroundChannel," / 0.23)"):"1px solid ".concat("light"===e.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"),["&.".concat(m.disabled)]:{borderBottom:"1px solid ".concat((e.vars||e).palette.action.disabled)}},"text"===o.variant&&"inherit"!==o.color&&{borderColor:e.vars?"rgba(".concat(e.vars.palette[o.color].mainChannel," / 0.5)"):(0,s.X4)(e.palette[o.color].main,.5)},"outlined"===o.variant&&"horizontal"===o.orientation&&{borderRightColor:"transparent"},"outlined"===o.variant&&"vertical"===o.orientation&&{borderBottomColor:"transparent"},"contained"===o.variant&&"horizontal"===o.orientation&&{borderRight:"1px solid ".concat((e.vars||e).palette.grey[400]),["&.".concat(m.disabled)]:{borderRight:"1px solid ".concat((e.vars||e).palette.action.disabled)}},"contained"===o.variant&&"vertical"===o.orientation&&{borderBottom:"1px solid ".concat((e.vars||e).palette.grey[400]),["&.".concat(m.disabled)]:{borderBottom:"1px solid ".concat((e.vars||e).palette.action.disabled)}},"contained"===o.variant&&"inherit"!==o.color&&{borderColor:(e.vars||e).palette[o.color].dark},{"&:hover":(0,n.default)({},"outlined"===o.variant&&"horizontal"===o.orientation&&{borderRightColor:"currentColor"},"outlined"===o.variant&&"vertical"===o.orientation&&{borderBottomColor:"currentColor"})}),["& .".concat(m.lastButton,",& .").concat(m.middleButton)]:(0,n.default)({},"horizontal"===o.orientation&&{borderTopLeftRadius:0,borderBottomLeftRadius:0},"vertical"===o.orientation&&{borderTopRightRadius:0,borderTopLeftRadius:0},"outlined"===o.variant&&"horizontal"===o.orientation&&{marginLeft:-1},"outlined"===o.variant&&"vertical"===o.orientation&&{marginTop:-1})})})),x=a.forwardRef((function(t,e){const o=(0,f.A)({props:t,name:"MuiButtonGroup"}),{children:s,className:u,color:b="primary",component:v="div",disabled:m=!1,disableElevation:x=!1,disableFocusRipple:y=!1,disableRipple:B=!1,fullWidth:C=!1,orientation:R="horizontal",size:M="medium",variant:I="outlined"}=o,N=(0,r.default)(o,S),E=(0,n.default)({},o,{color:b,component:v,disabled:m,disableElevation:x,disableFocusRipple:y,disableRipple:B,fullWidth:C,orientation:R,size:M,variant:I}),z=(t=>{const{classes:e,color:o,disabled:r,disableElevation:n,fullWidth:a,orientation:l,variant:s}=t,c={root:["root",s,"vertical"===l&&"vertical",a&&"fullWidth",n&&"disableElevation"],grouped:["grouped","grouped".concat((0,d.A)(l)),"grouped".concat((0,d.A)(s)),"grouped".concat((0,d.A)(s)).concat((0,d.A)(l)),"grouped".concat((0,d.A)(s)).concat((0,d.A)(o)),r&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return(0,i.A)(c,p,e)})(E),L=a.useMemo((()=>({className:z.grouped,color:b,disabled:m,disableElevation:x,disableFocusRipple:y,disableRipple:B,fullWidth:C,size:M,variant:I})),[b,m,x,y,B,C,M,I,z.grouped]),T=(0,c.A)(s),k=T.length,P=t=>{const e=0===t,o=t===k-1;return e&&o?"":e?z.firstButton:o?z.lastButton:z.middleButton};return(0,A.jsx)(w,(0,n.default)({as:v,role:"group",className:(0,l.A)(z.root,u),ref:e,ownerState:E},N,{children:(0,A.jsx)(h.A.Provider,{value:L,children:T.map(((t,e)=>(0,A.jsx)(g.A.Provider,{value:P(e),children:t},e)))})}))}))},10611:(t,e,o)=>{o.d(e,{A:()=>F});var r=o(57528),n=o(98587),a=o(58168),l=o(65043),i=o(69292),s=o(68606),c=o(83290),d=o(67266),u=o(10875),f=o(6803),b=o(34535),v=o(72876),p=o(57056),m=o(32400);function h(t){return(0,m.Ay)("MuiLinearProgress",t)}(0,p.A)("MuiLinearProgress",["root","colorPrimary","colorSecondary","determinate","indeterminate","buffer","query","dashed","dashedColorPrimary","dashedColorSecondary","bar","barColorPrimary","barColorSecondary","bar1Indeterminate","bar1Determinate","bar1Buffer","bar2Indeterminate","bar2Buffer"]);var g,A,S,w,x,y,B=o(70579);const C=["className","color","value","valueBuffer","variant"];let R,M,I,N,E,z;const L=(0,c.i7)(R||(R=g||(g=(0,r.A)(["\n  0% {\n    left: -35%;\n    right: 100%;\n  }\n\n  60% {\n    left: 100%;\n    right: -90%;\n  }\n\n  100% {\n    left: 100%;\n    right: -90%;\n  }\n"])))),T=(0,c.i7)(M||(M=A||(A=(0,r.A)(["\n  0% {\n    left: -200%;\n    right: 100%;\n  }\n\n  60% {\n    left: 107%;\n    right: -8%;\n  }\n\n  100% {\n    left: 107%;\n    right: -8%;\n  }\n"])))),k=(0,c.i7)(I||(I=S||(S=(0,r.A)(["\n  0% {\n    opacity: 1;\n    background-position: 0 -23px;\n  }\n\n  60% {\n    opacity: 0;\n    background-position: 0 -23px;\n  }\n\n  100% {\n    opacity: 1;\n    background-position: -200px -23px;\n  }\n"])))),P=(t,e)=>"inherit"===e?"currentColor":t.vars?t.vars.palette.LinearProgress["".concat(e,"Bg")]:"light"===t.palette.mode?(0,d.a)(t.palette[e].main,.62):(0,d.e$)(t.palette[e].main,.5),W=(0,b.Ay)("span",{name:"MuiLinearProgress",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.root,e["color".concat((0,f.A)(o.color))],e[o.variant]]}})((t=>{let{ownerState:e,theme:o}=t;return(0,a.default)({position:"relative",overflow:"hidden",display:"block",height:4,zIndex:0,"@media print":{colorAdjust:"exact"},backgroundColor:P(o,e.color)},"inherit"===e.color&&"buffer"!==e.variant&&{backgroundColor:"none","&::before":{content:'""',position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"currentColor",opacity:.3}},"buffer"===e.variant&&{backgroundColor:"transparent"},"query"===e.variant&&{transform:"rotate(180deg)"})})),j=(0,b.Ay)("span",{name:"MuiLinearProgress",slot:"Dashed",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.dashed,e["dashedColor".concat((0,f.A)(o.color))]]}})((t=>{let{ownerState:e,theme:o}=t;const r=P(o,e.color);return(0,a.default)({position:"absolute",marginTop:0,height:"100%",width:"100%"},"inherit"===e.color&&{opacity:.3},{backgroundImage:"radial-gradient(".concat(r," 0%, ").concat(r," 16%, transparent 42%)"),backgroundSize:"10px 10px",backgroundPosition:"0 -23px"})}),(0,c.AH)(N||(N=w||(w=(0,r.A)(["\n    animation: "," 3s infinite linear;\n  "]))),k)),H=(0,b.Ay)("span",{name:"MuiLinearProgress",slot:"Bar1",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.bar,e["barColor".concat((0,f.A)(o.color))],("indeterminate"===o.variant||"query"===o.variant)&&e.bar1Indeterminate,"determinate"===o.variant&&e.bar1Determinate,"buffer"===o.variant&&e.bar1Buffer]}})((t=>{let{ownerState:e,theme:o}=t;return(0,a.default)({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",backgroundColor:"inherit"===e.color?"currentColor":(o.vars||o).palette[e.color].main},"determinate"===e.variant&&{transition:"transform .".concat(4,"s linear")},"buffer"===e.variant&&{zIndex:1,transition:"transform .".concat(4,"s linear")})}),(t=>{let{ownerState:e}=t;return("indeterminate"===e.variant||"query"===e.variant)&&(0,c.AH)(E||(E=x||(x=(0,r.A)(["\n      width: auto;\n      animation: "," 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\n    "]))),L)})),X=(0,b.Ay)("span",{name:"MuiLinearProgress",slot:"Bar2",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.bar,e["barColor".concat((0,f.A)(o.color))],("indeterminate"===o.variant||"query"===o.variant)&&e.bar2Indeterminate,"buffer"===o.variant&&e.bar2Buffer]}})((t=>{let{ownerState:e,theme:o}=t;return(0,a.default)({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left"},"buffer"!==e.variant&&{backgroundColor:"inherit"===e.color?"currentColor":(o.vars||o).palette[e.color].main},"inherit"===e.color&&{opacity:.3},"buffer"===e.variant&&{backgroundColor:P(o,e.color),transition:"transform .".concat(4,"s linear")})}),(t=>{let{ownerState:e}=t;return("indeterminate"===e.variant||"query"===e.variant)&&(0,c.AH)(z||(z=y||(y=(0,r.A)(["\n      width: auto;\n      animation: "," 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;\n    "]))),T)})),F=l.forwardRef((function(t,e){const o=(0,v.A)({props:t,name:"MuiLinearProgress"}),{className:r,color:l="primary",value:c,valueBuffer:d,variant:b="indeterminate"}=o,p=(0,n.default)(o,C),m=(0,a.default)({},o,{color:l,variant:b}),g=(t=>{const{classes:e,variant:o,color:r}=t,n={root:["root","color".concat((0,f.A)(r)),o],dashed:["dashed","dashedColor".concat((0,f.A)(r))],bar1:["bar","barColor".concat((0,f.A)(r)),("indeterminate"===o||"query"===o)&&"bar1Indeterminate","determinate"===o&&"bar1Determinate","buffer"===o&&"bar1Buffer"],bar2:["bar","buffer"!==o&&"barColor".concat((0,f.A)(r)),"buffer"===o&&"color".concat((0,f.A)(r)),("indeterminate"===o||"query"===o)&&"bar2Indeterminate","buffer"===o&&"bar2Buffer"]};return(0,s.A)(n,h,e)})(m),A=(0,u.I)(),S={},w={bar1:{},bar2:{}};if("determinate"===b||"buffer"===b)if(void 0!==c){S["aria-valuenow"]=Math.round(c),S["aria-valuemin"]=0,S["aria-valuemax"]=100;let t=c-100;A&&(t=-t),w.bar1.transform="translateX(".concat(t,"%)")}else 0;if("buffer"===b)if(void 0!==d){let t=(d||0)-100;A&&(t=-t),w.bar2.transform="translateX(".concat(t,"%)")}else 0;return(0,B.jsxs)(W,(0,a.default)({className:(0,i.A)(g.root,r),ownerState:m,role:"progressbar"},S,{ref:e},p,{children:["buffer"===b?(0,B.jsx)(j,{className:g.dashed,ownerState:m}):null,(0,B.jsx)(H,{className:g.bar1,ownerState:m,style:w.bar1}),"determinate"===b?null:(0,B.jsx)(X,{className:g.bar2,ownerState:m,style:w.bar2})]}))}))},23851:(t,e,o)=>{o.d(e,{A:()=>h});var r=o(98587),n=o(58168),a=o(65043),l=o(69292),i=o(68606),s=o(51347),c=o(34535),d=o(72876),u=o(57056),f=o(32400);function b(t){return(0,f.Ay)("MuiListItemAvatar",t)}(0,u.A)("MuiListItemAvatar",["root","alignItemsFlexStart"]);var v=o(70579);const p=["className"],m=(0,c.Ay)("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.root,"flex-start"===o.alignItems&&e.alignItemsFlexStart]}})((t=>{let{ownerState:e}=t;return(0,n.default)({minWidth:56,flexShrink:0},"flex-start"===e.alignItems&&{marginTop:8})})),h=a.forwardRef((function(t,e){const o=(0,d.A)({props:t,name:"MuiListItemAvatar"}),{className:c}=o,u=(0,r.default)(o,p),f=a.useContext(s.A),h=(0,n.default)({},o,{alignItems:f.alignItems}),g=(t=>{const{alignItems:e,classes:o}=t,r={root:["root","flex-start"===e&&"alignItemsFlexStart"]};return(0,i.A)(r,b,o)})(h);return(0,v.jsx)(m,(0,n.default)({className:(0,l.A)(g.root,c),ownerState:h,ref:e},u))}))},79650:(t,e,o)=>{o.d(e,{A:()=>m});var r=o(58168),n=o(98587),a=o(65043),l=o(69292),i=o(68606),s=o(72876),c=o(34535),d=o(57056),u=o(32400);function f(t){return(0,u.Ay)("MuiTableContainer",t)}(0,d.A)("MuiTableContainer",["root"]);var b=o(70579);const v=["className","component"],p=(0,c.Ay)("div",{name:"MuiTableContainer",slot:"Root",overridesResolver:(t,e)=>e.root})({width:"100%",overflowX:"auto"}),m=a.forwardRef((function(t,e){const o=(0,s.A)({props:t,name:"MuiTableContainer"}),{className:a,component:c="div"}=o,d=(0,n.default)(o,v),u=(0,r.default)({},o,{component:c}),m=(t=>{const{classes:e}=t;return(0,i.A)({root:["root"]},f,e)})(u);return(0,b.jsx)(p,(0,r.default)({ref:e,as:c,className:(0,l.A)(m.root,a),ownerState:u},d))}))},69869:(t,e,o)=>{o.d(e,{A:()=>Q});var r=o(98587),n=o(58168),a=o(65043),l=(o(30805),o(69292)),i=o(33662),s=o(68606),c=o(10875),d=o(34535),u=o(72876),f=o(26240),b=o(80950);let v;function p(){if(v)return v;const t=document.createElement("div"),e=document.createElement("div");return e.style.width="10px",e.style.height="1px",t.appendChild(e),t.dir="rtl",t.style.fontSize="14px",t.style.width="4px",t.style.height="1px",t.style.position="absolute",t.style.top="-1000px",t.style.overflow="scroll",document.body.appendChild(t),v="reverse",t.scrollLeft>0?v="default":(t.scrollLeft=1,0===t.scrollLeft&&(v="negative")),document.body.removeChild(t),v}function m(t,e){const o=t.scrollLeft;if("rtl"!==e)return o;switch(p()){case"negative":return t.scrollWidth-t.clientWidth+o;case"reverse":return t.scrollWidth-t.clientWidth-o;default:return o}}function h(t){return(1+Math.sin(Math.PI*t-Math.PI/2))/2}var g=o(55013),A=o(36078),S=o(70579);const w=["onChange"],x={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};var y=o(3900),B=o(51639),C=o(75429),R=o(57056),M=o(32400);function I(t){return(0,M.Ay)("MuiTabScrollButton",t)}const N=(0,R.A)("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),E=["className","slots","slotProps","direction","orientation","disabled"],z=(0,d.Ay)(C.A,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.root,o.orientation&&e[o.orientation]]}})((t=>{let{ownerState:e}=t;return(0,n.default)({width:40,flexShrink:0,opacity:.8,["&.".concat(N.disabled)]:{opacity:0}},"vertical"===e.orientation&&{width:"100%",height:40,"& svg":{transform:"rotate(".concat(e.isRtl?-90:90,"deg)")}})})),L=a.forwardRef((function(t,e){var o,a;const d=(0,u.A)({props:t,name:"MuiTabScrollButton"}),{className:f,slots:b={},slotProps:v={},direction:p}=d,m=(0,r.default)(d,E),h=(0,c.I)(),g=(0,n.default)({isRtl:h},d),A=(t=>{const{classes:e,orientation:o,disabled:r}=t,n={root:["root",o,r&&"disabled"]};return(0,s.A)(n,I,e)})(g),w=null!=(o=b.StartScrollButtonIcon)?o:y.A,x=null!=(a=b.EndScrollButtonIcon)?a:B.A,C=(0,i.Q)({elementType:w,externalSlotProps:v.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:g}),R=(0,i.Q)({elementType:x,externalSlotProps:v.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:g});return(0,S.jsx)(z,(0,n.default)({component:"div",className:(0,l.A)(A.root,f),ref:e,role:null,ownerState:g,tabIndex:null},m,{children:"left"===p?(0,S.jsx)(w,(0,n.default)({},C)):(0,S.jsx)(x,(0,n.default)({},R))}))}));var T=o(93319);function k(t){return(0,M.Ay)("MuiTabs",t)}const P=(0,R.A)("MuiTabs",["root","vertical","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]);var W=o(22427);const j=["aria-label","aria-labelledby","action","centered","children","className","component","allowScrollButtonsMobile","indicatorColor","onChange","orientation","ScrollButtonComponent","scrollButtons","selectionFollowsFocus","slots","slotProps","TabIndicatorProps","TabScrollButtonProps","textColor","value","variant","visibleScrollbar"],H=(t,e)=>t===e?t.firstChild:e&&e.nextElementSibling?e.nextElementSibling:t.firstChild,X=(t,e)=>t===e?t.lastChild:e&&e.previousElementSibling?e.previousElementSibling:t.lastChild,F=(t,e,o)=>{let r=!1,n=o(t,e);for(;n;){if(n===t.firstChild){if(r)return;r=!0}const e=n.disabled||"true"===n.getAttribute("aria-disabled");if(n.hasAttribute("tabindex")&&!e)return void n.focus();n=o(t,n)}},O=(0,d.Ay)("div",{name:"MuiTabs",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[{["& .".concat(P.scrollButtons)]:e.scrollButtons},{["& .".concat(P.scrollButtons)]:o.scrollButtonsHideMobile&&e.scrollButtonsHideMobile},e.root,o.vertical&&e.vertical]}})((t=>{let{ownerState:e,theme:o}=t;return(0,n.default)({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex"},e.vertical&&{flexDirection:"column"},e.scrollButtonsHideMobile&&{["& .".concat(P.scrollButtons)]:{[o.breakpoints.down("sm")]:{display:"none"}}})})),D=(0,d.Ay)("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.scroller,o.fixed&&e.fixed,o.hideScrollbar&&e.hideScrollbar,o.scrollableX&&e.scrollableX,o.scrollableY&&e.scrollableY]}})((t=>{let{ownerState:e}=t;return(0,n.default)({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap"},e.fixed&&{overflowX:"hidden",width:"100%"},e.hideScrollbar&&{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},e.scrollableX&&{overflowX:"auto",overflowY:"hidden"},e.scrollableY&&{overflowY:"auto",overflowX:"hidden"})})),q=(0,d.Ay)("div",{name:"MuiTabs",slot:"FlexContainer",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[e.flexContainer,o.vertical&&e.flexContainerVertical,o.centered&&e.centered]}})((t=>{let{ownerState:e}=t;return(0,n.default)({display:"flex"},e.vertical&&{flexDirection:"column"},e.centered&&{justifyContent:"center"})})),Y=(0,d.Ay)("span",{name:"MuiTabs",slot:"Indicator",overridesResolver:(t,e)=>e.indicator})((t=>{let{ownerState:e,theme:o}=t;return(0,n.default)({position:"absolute",height:2,bottom:0,width:"100%",transition:o.transitions.create()},"primary"===e.indicatorColor&&{backgroundColor:(o.vars||o).palette.primary.main},"secondary"===e.indicatorColor&&{backgroundColor:(o.vars||o).palette.secondary.main},e.vertical&&{height:"100%",width:2,right:0})})),V=(0,d.Ay)((function(t){const{onChange:e}=t,o=(0,r.default)(t,w),l=a.useRef(),i=a.useRef(null),s=()=>{l.current=i.current.offsetHeight-i.current.clientHeight};return(0,g.A)((()=>{const t=(0,b.A)((()=>{const t=l.current;s(),t!==l.current&&e(l.current)})),o=(0,A.A)(i.current);return o.addEventListener("resize",t),()=>{t.clear(),o.removeEventListener("resize",t)}}),[e]),a.useEffect((()=>{s(),e(l.current)}),[e]),(0,S.jsx)("div",(0,n.default)({style:x,ref:i},o))}))({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),G={};const Q=a.forwardRef((function(t,e){const o=(0,u.A)({props:t,name:"MuiTabs"}),d=(0,f.A)(),v=(0,c.I)(),{"aria-label":g,"aria-labelledby":w,action:x,centered:y=!1,children:B,className:C,component:R="div",allowScrollButtonsMobile:M=!1,indicatorColor:I="primary",onChange:N,orientation:E="horizontal",ScrollButtonComponent:z=L,scrollButtons:P="auto",selectionFollowsFocus:Q,slots:K={},slotProps:_={},TabIndicatorProps:U={},TabScrollButtonProps:$={},textColor:J="primary",value:Z,variant:tt="standard",visibleScrollbar:et=!1}=o,ot=(0,r.default)(o,j),rt="scrollable"===tt,nt="vertical"===E,at=nt?"scrollTop":"scrollLeft",lt=nt?"top":"left",it=nt?"bottom":"right",st=nt?"clientHeight":"clientWidth",ct=nt?"height":"width",dt=(0,n.default)({},o,{component:R,allowScrollButtonsMobile:M,indicatorColor:I,orientation:E,vertical:nt,scrollButtons:P,textColor:J,variant:tt,visibleScrollbar:et,fixed:!rt,hideScrollbar:rt&&!et,scrollableX:rt&&!nt,scrollableY:rt&&nt,centered:y&&!rt,scrollButtonsHideMobile:!M}),ut=(t=>{const{vertical:e,fixed:o,hideScrollbar:r,scrollableX:n,scrollableY:a,centered:l,scrollButtonsHideMobile:i,classes:c}=t,d={root:["root",e&&"vertical"],scroller:["scroller",o&&"fixed",r&&"hideScrollbar",n&&"scrollableX",a&&"scrollableY"],flexContainer:["flexContainer",e&&"flexContainerVertical",l&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",i&&"scrollButtonsHideMobile"],scrollableX:[n&&"scrollableX"],hideScrollbar:[r&&"hideScrollbar"]};return(0,s.A)(d,k,c)})(dt),ft=(0,i.Q)({elementType:K.StartScrollButtonIcon,externalSlotProps:_.startScrollButtonIcon,ownerState:dt}),bt=(0,i.Q)({elementType:K.EndScrollButtonIcon,externalSlotProps:_.endScrollButtonIcon,ownerState:dt});const[vt,pt]=a.useState(!1),[mt,ht]=a.useState(G),[gt,At]=a.useState(!1),[St,wt]=a.useState(!1),[xt,yt]=a.useState(!1),[Bt,Ct]=a.useState({overflow:"hidden",scrollbarWidth:0}),Rt=new Map,Mt=a.useRef(null),It=a.useRef(null),Nt=()=>{const t=Mt.current;let e,o;if(t){const o=t.getBoundingClientRect();e={clientWidth:t.clientWidth,scrollLeft:t.scrollLeft,scrollTop:t.scrollTop,scrollLeftNormalized:m(t,v?"rtl":"ltr"),scrollWidth:t.scrollWidth,top:o.top,bottom:o.bottom,left:o.left,right:o.right}}if(t&&!1!==Z){const t=It.current.children;if(t.length>0){const e=t[Rt.get(Z)];0,o=e?e.getBoundingClientRect():null}}return{tabsMeta:e,tabMeta:o}},Et=(0,T.A)((()=>{const{tabsMeta:t,tabMeta:e}=Nt();let o,r=0;if(nt)o="top",e&&t&&(r=e.top-t.top+t.scrollTop);else if(o=v?"right":"left",e&&t){const n=v?t.scrollLeftNormalized+t.clientWidth-t.scrollWidth:t.scrollLeft;r=(v?-1:1)*(e[o]-t[o]+n)}const n={[o]:r,[ct]:e?e[ct]:0};if(isNaN(mt[o])||isNaN(mt[ct]))ht(n);else{const t=Math.abs(mt[o]-n[o]),e=Math.abs(mt[ct]-n[ct]);(t>=1||e>=1)&&ht(n)}})),zt=function(t){let{animation:e=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e?function(t,e,o){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>{};const{ease:a=h,duration:l=300}=r;let i=null;const s=e[t];let c=!1;const d=()=>{c=!0},u=r=>{if(c)return void n(new Error("Animation cancelled"));null===i&&(i=r);const d=Math.min(1,(r-i)/l);e[t]=a(d)*(o-s)+s,d>=1?requestAnimationFrame((()=>{n(null)})):requestAnimationFrame(u)};s===o?n(new Error("Element already at target position")):requestAnimationFrame(u)}(at,Mt.current,t,{duration:d.transitions.duration.standard}):Mt.current[at]=t},Lt=t=>{let e=Mt.current[at];nt?e+=t:(e+=t*(v?-1:1),e*=v&&"reverse"===p()?-1:1),zt(e)},Tt=()=>{const t=Mt.current[st];let e=0;const o=Array.from(It.current.children);for(let r=0;r<o.length;r+=1){const n=o[r];if(e+n[st]>t){0===r&&(e=t);break}e+=n[st]}return e},kt=()=>{Lt(-1*Tt())},Pt=()=>{Lt(Tt())},Wt=a.useCallback((t=>{Ct({overflow:null,scrollbarWidth:t})}),[]),jt=(0,T.A)((t=>{const{tabsMeta:e,tabMeta:o}=Nt();if(o&&e)if(o[lt]<e[lt]){const r=e[at]+(o[lt]-e[lt]);zt(r,{animation:t})}else if(o[it]>e[it]){const r=e[at]+(o[it]-e[it]);zt(r,{animation:t})}})),Ht=(0,T.A)((()=>{rt&&!1!==P&&yt(!xt)}));a.useEffect((()=>{const t=(0,b.A)((()=>{Mt.current&&Et()}));let e;const o=o=>{o.forEach((t=>{t.removedNodes.forEach((t=>{var o;null==(o=e)||o.unobserve(t)})),t.addedNodes.forEach((t=>{var o;null==(o=e)||o.observe(t)}))})),t(),Ht()},r=(0,A.A)(Mt.current);let n;return r.addEventListener("resize",t),"undefined"!==typeof ResizeObserver&&(e=new ResizeObserver(t),Array.from(It.current.children).forEach((t=>{e.observe(t)}))),"undefined"!==typeof MutationObserver&&(n=new MutationObserver(o),n.observe(It.current,{childList:!0})),()=>{var o,a;t.clear(),r.removeEventListener("resize",t),null==(o=n)||o.disconnect(),null==(a=e)||a.disconnect()}}),[Et,Ht]),a.useEffect((()=>{const t=Array.from(It.current.children),e=t.length;if("undefined"!==typeof IntersectionObserver&&e>0&&rt&&!1!==P){const o=t[0],r=t[e-1],n={root:Mt.current,threshold:.99},a=new IntersectionObserver((t=>{At(!t[0].isIntersecting)}),n);a.observe(o);const l=new IntersectionObserver((t=>{wt(!t[0].isIntersecting)}),n);return l.observe(r),()=>{a.disconnect(),l.disconnect()}}}),[rt,P,xt,null==B?void 0:B.length]),a.useEffect((()=>{pt(!0)}),[]),a.useEffect((()=>{Et()})),a.useEffect((()=>{jt(G!==mt)}),[jt,mt]),a.useImperativeHandle(x,(()=>({updateIndicator:Et,updateScrollButtons:Ht})),[Et,Ht]);const Xt=(0,S.jsx)(Y,(0,n.default)({},U,{className:(0,l.A)(ut.indicator,U.className),ownerState:dt,style:(0,n.default)({},mt,U.style)}));let Ft=0;const Ot=a.Children.map(B,(t=>{if(!a.isValidElement(t))return null;const e=void 0===t.props.value?Ft:t.props.value;Rt.set(e,Ft);const o=e===Z;return Ft+=1,a.cloneElement(t,(0,n.default)({fullWidth:"fullWidth"===tt,indicator:o&&!vt&&Xt,selected:o,selectionFollowsFocus:Q,onChange:N,textColor:J,value:e},1!==Ft||!1!==Z||t.props.tabIndex?{}:{tabIndex:0}))})),Dt=(()=>{const t={};t.scrollbarSizeListener=rt?(0,S.jsx)(V,{onChange:Wt,className:(0,l.A)(ut.scrollableX,ut.hideScrollbar)}):null;const e=rt&&("auto"===P&&(gt||St)||!0===P);return t.scrollButtonStart=e?(0,S.jsx)(z,(0,n.default)({slots:{StartScrollButtonIcon:K.StartScrollButtonIcon},slotProps:{startScrollButtonIcon:ft},orientation:E,direction:v?"right":"left",onClick:kt,disabled:!gt},$,{className:(0,l.A)(ut.scrollButtons,$.className)})):null,t.scrollButtonEnd=e?(0,S.jsx)(z,(0,n.default)({slots:{EndScrollButtonIcon:K.EndScrollButtonIcon},slotProps:{endScrollButtonIcon:bt},orientation:E,direction:v?"left":"right",onClick:Pt,disabled:!St},$,{className:(0,l.A)(ut.scrollButtons,$.className)})):null,t})();return(0,S.jsxs)(O,(0,n.default)({className:(0,l.A)(ut.root,C),ownerState:dt,ref:e,as:R},ot,{children:[Dt.scrollButtonStart,Dt.scrollbarSizeListener,(0,S.jsxs)(D,{className:ut.scroller,ownerState:dt,style:{overflow:Bt.overflow,[nt?"margin".concat(v?"Left":"Right"):"marginBottom"]:et?void 0:-Bt.scrollbarWidth},ref:Mt,children:[(0,S.jsx)(q,{"aria-label":g,"aria-labelledby":w,"aria-orientation":"vertical"===E?"vertical":null,className:ut.flexContainer,ownerState:dt,onKeyDown:t=>{const e=It.current,o=(0,W.A)(e).activeElement;if("tab"!==o.getAttribute("role"))return;let r="horizontal"===E?"ArrowLeft":"ArrowUp",n="horizontal"===E?"ArrowRight":"ArrowDown";switch("horizontal"===E&&v&&(r="ArrowRight",n="ArrowLeft"),t.key){case r:t.preventDefault(),F(e,o,X);break;case n:t.preventDefault(),F(e,o,H);break;case"Home":t.preventDefault(),F(e,null,H);break;case"End":t.preventDefault(),F(e,null,X)}},ref:It,role:"tablist",children:Ot}),vt&&Xt]}),Dt.scrollButtonEnd]}))}))},3900:(t,e,o)=>{o.d(e,{A:()=>a});o(65043);var r=o(66734),n=o(70579);const a=(0,r.A)((0,n.jsx)("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft")},51639:(t,e,o)=>{o.d(e,{A:()=>a});o(65043);var r=o(66734),n=o(70579);const a=(0,r.A)((0,n.jsx)("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight")}}]);
//# sourceMappingURL=9454.1da5939b.chunk.js.map

// === 8248.e6f71649.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[8248],{58248:(n,e,t)=>{t.r(e),t.d(e,{default:()=>f});var o,i,l,r=t(57528),c=t(30105),a=t(61531),s=t(17339),d=t(96364),u=t(10035),p=t(75156),x=t(72119),h=t(70579);const v=x.Ay.img(o||(o=(0,r.A)(["\n  ","\n"])),(n=>{let{zoomedImageOnRight:e}=n;return" width: 100%;\n      cursor: zoom-in;\n      border-radius: 6px;\n\n      &:hover {\n        border: 1px solid grey;\n        border-radius: 16px;\n        z-index: 1;\n        position: absolute;\n        width: 200%;\n        ".concat(e?"right: 0":"left: 0",";\n        top: 0;\n      }\n")})),g=(0,x.Ay)(c.A)(i||(i=(0,r.A)(["\n  background: white;\n  color: #005dc9;\n\n  :hover {\n    background: white;\n  }\n"]))),m=(0,x.Ay)(c.A)(l||(l=(0,r.A)(["\n  border-color: white;\n  color: white;\n"]))),f=n=>{var e,t,o,i,l,r,x;let{children:f,...A}=n;const{completeTour:w,goBack:k,goNext:y,skipTour:b,activeTourStep:j,activeTourDef:z,activeTourStepIndex:I}=A,C=null===z||void 0===z||null===(e=z.steps)||void 0===e?void 0:e.length,T=!z.isMacroTour&&!!C&&"".concat(I+1," / ").concat(C),L=(null===j||void 0===j||null===(t=j.actions)||void 0===t?void 0:t.reduce(((n,e)=>("next"===(null===e||void 0===e?void 0:e.type)&&(n.next=e),"back"===(null===e||void 0===e?void 0:e.type)&&(n.back=e),"skip"===(null===e||void 0===e?void 0:e.type)&&(n.skip=e),"complete"===(null===e||void 0===e?void 0:e.type)&&(n.complete=e),n)),{}))||{},S=z.isMacroTour||!(null===j||void 0===j||null===(o=j.actions)||void 0===o||!o.find((n=>["next","back","complete"].includes(null===n||void 0===n?void 0:n.type))));return(0,h.jsx)(u.A,{...A,children:(0,h.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:8,children:[(0,h.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,h.jsx)(d.A,{style:{flex:1,fontSize:"11px"},color:"white",children:(null===z||void 0===z?void 0:z.title)||""}),(0,h.jsx)(s.A,{size:"small",onClick:"macro_helpBtn"===(null===j||void 0===j?void 0:j.id)?w:b,children:(0,h.jsx)(p.Ay,{color:"grey",icon:"Close",size:"sm"})})]}),(null===j||void 0===j?void 0:j.title)&&(0,h.jsxs)(a.A,{display:"flex",alignItems:"center",gridGap:16,children:[(0,h.jsx)(d.A,{color:"white",children:T||""}),(0,h.jsx)(d.A,{color:"white",variant:"h4",weight:"bold",children:null===j||void 0===j?void 0:j.title})]}),(null===j||void 0===j?void 0:j.videoLink)&&(0,h.jsx)("video",{src:"".concat("https://assets-organimi.s3.amazonaws.com").concat(null===j||void 0===j?void 0:j.videoLink),autoPlay:!0,controls:!0}),(null===j||void 0===j?void 0:j.imageLink)&&(0,h.jsx)(v,{src:"".concat("https://assets-organimi.s3.amazonaws.com").concat(null===j||void 0===j?void 0:j.imageLink),alt:"tour.png",zoomedImageOnRight:null===j||void 0===j?void 0:j.zoomedImageOnRight}),(null===j||void 0===j?void 0:j.text)&&(0,h.jsx)(a.A,{mt:2,flex:1,children:(0,h.jsx)(d.A,{variant:"caption",color:"white",children:null===j||void 0===j?void 0:j.text})}),S&&(0,h.jsxs)(a.A,{mt:4,display:"flex",justifyContent:"space-between",children:[(0,h.jsxs)(a.A,{display:"flex",gridGap:8,children:[!!L.back&&(0,h.jsx)(m,{size:"small",variant:"outlined",onClick:k,children:(null===(i=L.back)||void 0===i?void 0:i.text)||"Back"}),!(!L.skip&&!z.isMacroTour)&&(0,h.jsx)(c.A,{style:{color:"grey",justifyContent:"left"},size:"small",variant:"text",onClick:b,children:(null===(l=L.skip)||void 0===l?void 0:l.text)||"Skip"})]}),L.next&&(0,h.jsx)(g,{size:"small",variant:"outlined",onClick:y,children:(null===(r=L.next)||void 0===r?void 0:r.text)||"Next"}),L.complete&&(0,h.jsx)(g,{size:"small",color:"secondary",variant:"outlined",onClick:w,children:(null===(x=L.complete)||void 0===x?void 0:x.text)||"Finish"})]}),f]})})}},10035:(n,e,t)=>{t.d(e,{A:()=>k});var o=t(57528),i=t(35007),l=t(70567),r=t(61531),c=t(35801),a=t(43867),s=t(52907),d=t(71233),u=t(5816),p=t(65043),x=t(72119);const h=n=>document.querySelector("#".concat(n,', [data-tour-anchor="').concat(n,'"]'));function v(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:e,dynamicAnchorDataAttribute:t},activeTourUserInput:o,skipTour:i}=n;let l=null;const r=(0,p.useRef)(!0);if(t){const n=t,e=null===o||void 0===o?void 0:o[n];l=n&&e?"".concat(n,"_").concat(e):null}const c=l||e,a=(0,p.useRef)(),s=(0,p.useRef)(0),d=c&&h(c),[u,x]=(0,p.useState)(null);return(0,p.useEffect)((()=>(a.current&&clearInterval(a.current),c&&(a.current=setInterval((()=>{s.current++;const n=c&&h(c);n&&(clearInterval(a.current),x(n||null),s.current=0),!n&&s.current>60&&r.current&&(clearInterval(a.current),r.current=!1,i({errored:!0}))}),100)),()=>{a.current&&clearInterval(a.current)})),[c]),(0,p.useEffect)((()=>()=>{a.current&&clearInterval(a.current)}),[]),d||u||null}var g,m=t(64418),f=t(70579);const A="tour-highlight-anchor",w=(0,x.Ay)(i.A)(g||(g=(0,o.A)(["\n  ","\n"])),(n=>{let{theme:e,width:t,anchorOffset:o,zIndex:i=1e4}=n;return"\n    z-index: ".concat(i,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(e.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),k=n=>{let{children:e,onDialogClose:t,actionsUI:o,...i}=n;const{activeTourStep:x}=i,h=(0,p.useRef)(null),g=(0,l.A)(),[k,y]=(0,p.useState)(null),b=v(i),{confirmAction:j}=(0,m.A)(),{anchorClass:z,backdrop:I,arrow:C,highlightAnchor:T,ignoreAnchorZIndex:L,zIndex:S,anchorOffset:D,backdropAboveModal:R,position:O,showDialogUI:B=!1}=x||{},M=[!0,!1].includes(I)?I:!!b,E=[!0,!1].includes(C)?C:!!b,_=[!0,!1].includes(T)?T:M&&!!b,F=O||"left-start",G=(R?g.zIndex.modal:g.zIndex.drawer)+1,N=G+1;if((0,p.useEffect)((()=>{if(b)return M&&!L&&(b.style.zIndex=(b.style.zIndex||0)+N),_&&b.classList.toggle(A),z&&b.classList.toggle(z),()=>{b&&(M&&!L&&(b.style.zIndex=b.style.zIndex-N),_&&b.classList.toggle(A),z&&b.classList.toggle(z))}}),[b]),null!==x&&void 0!==x&&x.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(r.A,{children:[B&&(0,f.jsxs)(c.A,{open:!0,onClose:()=>{t&&j({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===x||void 0===x||!x.size),maxWidth:(null===x||void 0===x?void 0:x.size)||"sm",children:[(0,f.jsx)(r.A,{zIndex:1,children:(0,f.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,f.jsx)(a.A,{style:{padding:0},children:e}),o&&(0,f.jsx)(s.A,{style:{display:"block",padding:"24px"},children:o})]}),!B&&b&&(0,f.jsx)(d.A,{open:M,style:{zIndex:G},children:(0,f.jsxs)(w,{open:!0,ref:h,anchorEl:b,placement:F,zIndex:S,width:(null===x||void 0===x?void 0:x.size)||"sm",showBackdrop:M,anchorOffset:D,modifiers:{arrow:{enabled:E,element:k}},children:[E&&(0,f.jsx)(r.A,{className:"arrow",ref:y,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),e]})})]})}}}]);
//# sourceMappingURL=8248.e6f71649.chunk.js.map

// === 7824.c8f66d4b.chunk.js ===

