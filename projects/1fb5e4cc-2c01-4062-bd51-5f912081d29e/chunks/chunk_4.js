"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[4592],{32042:(e,t,i)=>{i.d(t,{Pw:()=>a});var n,l,r,s=i(57528),o=i(72119);o.Ay.div(n||(n=(0,s.A)(["\n  position: relative;\n  min-height: 300px;\n\n  flex-shrink: 0;\n  border-radius: 10px;\n  border: 1px solid var(--Neutral-400, #cbcbcb);\n  background: var(--Neutral-100, #fcfcfc);\n"]))),o.Ay.div(l||(l=(0,s.A)(["\n  display: inline-flex;\n  padding: 4px 10px;\n  justify-content: center;\n  align-items: center;\n  gap: 10px;\n  border-radius: 6px;\n  border: 1px solid var(--Neutral-600, #828282);\n  background: var(--Neutral-0, #fff);\n"])));const a=o.Ay.div(r||(r=(0,s.A)(["\n  margin-left: -1px;\n  width: 12px;\n  height: 12px;\n  flex-shrink: 0;\n  margin-top: -12px;\n  border-bottom: 1px solid var(--Neutral-600, #828282);\n  border-left: 1px solid var(--Neutral-600, #828282);\n  border-bottom-left-radius: 4px;\n"])))},84592:(e,t,i)=>{i.r(t),i.d(t,{default:()=>qi});var n=i(65043),l=i(96446),r=i(58053),s=i(39659),o=i(72614),a=i(71276),d=i(91688),c=i(36138),u=i(14556),h=i(43331),x=i(93545),p=i(24115),g=i(49157),m=i(85725),v=i(20447),j=i(78396),A=i(66856),f=i(42543),b=i(40454),y=i(69219),C=i(81635),I=i(61),w=i(70669),N=i(97626),E=i(59452),S=i(24968),D=i(97337),L=i(19367),T=i(25492),P=i(69278),k=i(79091),R=i(83462),z=i(4219),O=i(85865),F=i(17392),Q=i(35316),M=i(75156),H=i(32115),q=i(97631),_=i(37294),W=i(2173),B=i(7866),G=i(99085),V=i(67784),X=i(77739),U=i(42518),Y=i(20965),J=i(61258),K=i(91812),$=i(43845),Z=i(688),ee=i(47088),te=i(24241),ie=i(70579);const ne=e=>{var t,i,n;let{rule:r,onEditClick:s,onDeleteClick:o,disableDelete:a}=e;const d=te.c9.fromISO(null===r||void 0===r?void 0:r.value),c=(null===r||void 0===r||null===(t=r.field)||void 0===t?void 0:t.type)===ee.yw.Date&&d.isValid?d.toLocaleString():null===r||void 0===r?void 0:r.value;return(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,ie.jsxs)(l.A,{display:"flex",gap:(0,Y.A)(6),alignItems:"center",children:[(0,ie.jsx)($.A,{size:"xsmall",label:"IF"}),(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],display:"inline",children:[" ".concat(null===r||void 0===r||null===(i=r.field)||void 0===i?void 0:i.label," "),"unfilled"!==(null===r||void 0===r||null===(n=r.field)||void 0===n?void 0:n.id)&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(O.A,{variant:"inherit",color:_.Qs.Neutrals[500],display:"inline",children:G.Ve[null===r||void 0===r?void 0:r.operator]})," ".concat(c," ")]})]})]}),(0,ie.jsx)(l.A,{children:(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[700],display:"flex",gap:(0,Y.A)(12),alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:"inherit",color:"inherit",onClick:s,children:(0,ie.jsx)(M.gF,{icon:"Edit",size:"xs"})}),(0,ie.jsx)(X.A,{title:a?"You cannot delete a rule while other rules are being updated.":"",arrow:!0,placement:"top-end",children:(0,ie.jsx)(O.A,{variant:"inherit",color:a?_.Qs.Neutrals[500]:"inherit",onClick:a?()=>{}:o,children:(0,ie.jsx)(M.gF,{icon:"Delete",size:"xs"})})})]})})]})};var le=i(47593),re=i(24876),se=i(98621);const oe=(0,n.forwardRef)(((e,t)=>{var i;let{ruleIndex:r,onCancelEditClick:s,onSaveRuleClick:o,initialRule:a={field:"",operator:G.ux.CONTAINS,value:""}}=e;const d=(0,J.mN)({defaultValues:a,shouldFocusError:!0,mode:"all"}),c=async()=>{await Promise.resolve(m())};(0,n.useImperativeHandle)(t,(()=>({triggerSave:c})));const{control:h}=d,x=(0,u.d4)(B.E0),p=(0,n.useMemo)((()=>x.reduce(((e,t)=>(e[t.id]=t,e)),{})),[x]),g=null===(i=x.filter((e=>"role"===e.model&&"name"===e.name))[0])||void 0===i?void 0:i.id;(0,n.useEffect)((()=>{!d.watch("field")&&g&&d.setValue("field",g)}),[g]),(0,n.useEffect)((()=>{const e=p[a.field];a.field&&e.id&&[ee.yw.Tags,ee.yw.Location].includes(e.type)&&d.setValue("value",a.value.split(","))}),[a.field]),(0,n.useEffect)((()=>{(d.watch("field")!==a.field||d.formState.isDirty)&&d.setValue("value","")}),[d.watch("field")]);const m=async()=>{const e={...d.getValues()},t=p[e.field];var i;[ee.yw.Tags,ee.yw.Location].includes(t.type)?e.value=(null===(i=e.value)||void 0===i?void 0:i.toString())||"":[ee.yw.Switch,ee.yw.Boolean].includes(t.type)&&(e.value=e.value?"true":"false");!(e.field!==G.DC.id&&[ee.yw.String,ee.yw.RichText,ee.yw.Tags,ee.yw.Url,ee.yw.Location,ee.yw.Date,ee.yw.Number,ee.yw.Currency].includes(t.type))||""!==e.value&&void 0!==e.value&&null!==e.value?await o(e):d.setError("value",{type:"required",message:"This field is required",shouldFocus:!0})},v=p[d.watch("field")],j=(0,n.useMemo)((()=>null!==v&&void 0!==v&&v.id?(0,se.z)(null===v||void 0===v?void 0:v.type):[]),[null===v||void 0===v?void 0:v.id,se.z]);(0,n.useEffect)((()=>{var e;const t=d.getValues("operator");var i;!j.find((e=>e.value===t))&&j[0]&&t!==(null===(e=j[0])||void 0===e?void 0:e.value)&&d.setValue("operator",null===(i=j[0])||void 0===i?void 0:i.value,{shouldDirty:!0})}),[j]);const A=d.watch("value");return(0,n.useEffect)((()=>{""!==A&&void 0!==A&&null!==A&&d.clearErrors("value")}),[A]),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",gap:(0,Y.A)(14),children:[(0,ie.jsx)(J.Op,{...d,children:(0,ie.jsx)("form",{id:"rule-form-".concat(r),style:{width:"100%"},children:(0,ie.jsxs)(l.A,{display:"flex",gap:(0,Y.A)(6),width:"100%",children:[(0,ie.jsx)(J.xI,{name:"field",control:h,render:e=>(0,ie.jsx)(V.A,{variant:"outlined",size:"small",select:!0,placeholder:"Select Field",fullWidth:!0,...e,defaultValue:g,SelectProps:{MenuProps:{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"}}},children:x.map(((e,t)=>(0,ie.jsx)(le.A,{value:null===e||void 0===e?void 0:e.id,divider:t<x.length-1,children:e.label})))})}),"unfilled"!==d.watch("field")&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(J.xI,{name:"operator",control:h,render:e=>(0,ie.jsx)(V.A,{variant:"outlined",size:"small",select:!0,placeholder:"Select Operator",fullWidth:!0,...e,SelectProps:{MenuProps:{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"}}},children:j.map(((e,t)=>(0,ie.jsx)(le.A,{value:e.value,divider:t<j.length-1,children:e.label})))})}),(0,ie.jsx)(re.A,{name:"value",field:p[d.watch("field")]||{},autoFocus:!0,required:!0,fullWidth:!0,error:d.formState.errors.value,localSearchFieldProps:{limitTags:0,multiple:!1}})]})]})})}),(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[700],display:"flex",gap:(0,Y.A)(12),children:[(0,ie.jsx)("span",{children:(0,ie.jsx)(U.A,{variant:"iconOnly",color:"secondaryGrey",size:"small",onClick:s,children:(0,ie.jsx)(M.gF,{icon:"Close",size:"sm"})})}),(0,ie.jsx)("span",{children:(0,ie.jsx)(U.A,{variant:"iconOnly",color:"success",size:"small",onClick:m,children:(0,ie.jsx)(M.gF,{icon:"Check",size:"sm"})})})]})]})})),ae=n.forwardRef(((e,t)=>{const[i,r]=(0,n.useState)({}),[s,o]=(0,n.useState)(null),a=Boolean(s),d=(0,J.xW)(),{watch:c,setValue:h}=d,x=c("comparators"),p=(0,u.d4)(B.ZG);(0,n.useEffect)((()=>{r({})}),[c("id")]);const g=e=>{o(e.currentTarget)},m=e=>()=>{d.register("join"),h("join",e,{shouldDirty:!0,shouldValidate:!0}),o(null)},v=e=>()=>{const t=[...x].filter(((t,i)=>i!==e));d.unregister("comparators"),d.register("comparators"),h("comparators",t,{shouldDirty:!0})},j=e=>()=>{r((t=>({...t,[e]:"edit"})))},A=e=>()=>{if("add"===i[e]){const t=[...x].filter(((t,i)=>i!==e));d.unregister("comparators"),d.register("comparators"),h("comparators",t)}r((t=>({...t,[e]:"view"})))},f=(0,n.useMemo)((()=>Object.values(i).includes("add")),[i]),b=(0,n.useMemo)((()=>Object.values(i).includes("edit")),[i]);return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:(0,Y.A)(12),children:[(0,ie.jsxs)(O.A,{variant:H.Eq.labelLG,display:"flex",gap:(0,Y.A)(6),alignItems:"center",children:["Rules",(0,ie.jsx)(X.A,{title:"Set conditions to automatically apply legend items and badges to roles in the chart.",arrow:!0,placement:"right",children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"ImportContact",color:_.Qs.Neutrals[500],size:"xs"})})})]}),x.map(((e,n)=>(0,ie.jsxs)(ie.Fragment,{children:[["edit","add"].includes(i[n])?(0,ie.jsx)(oe,{ref:t,ruleIndex:n,onCancelEditClick:A(n),onSaveRuleClick:e=>(async(e,t)=>{var i;d.unregister("comparators[".concat(e,"]")),d.register("comparators[".concat(e,"]"));const n="unfilled"===t.field?null===(i=p[t.field])||void 0===i?void 0:i.value:p[t.field];d.setValue("comparators[".concat(e,"]"),{...t,field:{...n}},{shouldDirty:!0}),r((t=>({...t,[e]:"view"})))})(n,e),initialRule:"add"===i[n]?void 0:{...e,field:e.field.id}}):(0,ie.jsx)(ne,{rule:e,onEditClick:j(n),onDeleteClick:v(n),disableDelete:b||f}),n<x.length-1&&(0,ie.jsx)(l.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:(0,ie.jsx)($.A,{variant:"outlined",color:"primary",label:(0,ie.jsxs)(ie.Fragment,{children:[c("join")||"AND",(0,ie.jsx)(M.gF,{icon:"Next",size:"xxs",color:_.Qs.Neutrals[900]})]}),size:"xsmall",onClick:g})})]})))]}),(0,ie.jsx)(l.A,{display:"flex",justifyContent:"flex-start",children:(0,ie.jsx)(X.A,{title:f?"You can only add one rule at a time.":"",arrow:!0,placement:"right",children:(0,ie.jsx)("span",{children:(0,ie.jsxs)(U.A,{sx:{paddingLeft:"0 !important"},variant:"text",startIcon:(0,ie.jsx)(M.gF,{icon:"Add",size:"sm"}),size:"small",onClick:()=>{d.register("comparators[".concat(x.length,"]")),h("comparators[".concat(x.length,"]"),{}),r((e=>({...e,[x.length]:"add"})))},disabled:f,children:[" ","Add Rule"]})})})}),(0,ie.jsx)(Z.A,{open:a,anchorEl:s,onClose:()=>{o(null)},children:Object.values(G.rh).map((e=>(0,ie.jsx)(le.A,{onClick:m(e),selected:c("join")===e,children:e})))})]})}));var de=i(80286),ce=i(68586),ue=i(176),he=i(86255),xe=i(84373),pe=i(16528);const ge="CLEAR_ICON_SELECTION",me=[ge,...de.JY],ve=[];for(let _i=0;_i<me.length;_i+=6)ve.push(me.slice(_i,_i+6));const je=e=>{let{initialIcon:t="",color:i,onChange:r}=e;const[s,o]=(0,n.useState)(i),[a,d]=(0,n.useState)("all"),[c,u]=(0,n.useState)(""),[h,x]=(0,n.useState)(),[p,g]=(0,n.useState)(null),[m,v]=(0,n.useState)(me),[j,A]=(0,n.useState)(ve),f=Boolean(p),{eventDebounce:b}=(0,he.A)();(0,n.useEffect)((()=>{if("all"!==a||c){let e=m;if("all"!==a&&(e=de.UH[a].icons),c){e=de.bA.filter((e=>e.includes(c))).flatMap((e=>de.ji[e]));const t=me.filter((e=>{var t,i,n;const l=e.replace(/-/g," "),r=c.replace(/-/g," ");return l.toLowerCase().includes(r.toLowerCase())||(null===(t=de.Wb[e])||void 0===t||null===(i=t.label)||void 0===i||null===(n=i.toLowerCase())||void 0===n?void 0:n.includes(null===c||void 0===c?void 0:c.toLowerCase()))}));e=[...e,...t]}e=[ge,...Array.from(new Set(e))],v(e)}else A(ve)}),[a,c]),(0,n.useEffect)((()=>{if(f){if(m.length){const e=[];for(let t=0;t<m.length;t+=6)e.push(m.slice(t,t+6));A(e)}else A([[]]);return()=>{A(ve)}}}),[m]),(0,n.useEffect)((()=>{x(t)}),[t]),(0,n.useEffect)((()=>(o(i),()=>{o(null)})),[i]);const y=e=>{d(e.target.value)},C=b((e=>{u(e.target.value.trim().toLowerCase())}),500),I=e=>{C(e)},w=e=>{if(p)return x(t),void g(null);g(e.currentTarget)},N=e=>{let{columnIndex:t,key:i,rowIndex:n,style:r,isScrolling:o}=e;const d=j[n][t];if(!d)return null;const u=d===h;return(0,ie.jsx)(l.A,{style:r,display:"flex",alignItems:"center",justifyContent:"center",children:o?(0,ie.jsx)(xe.A,{variant:"rect",height:(0,Y.A)(16),width:(0,Y.A)(16)}):(0,ie.jsx)(l.A,{borderRadius:(0,Y.A)(2),border:u?"".concat((0,Y.A)(2)," solid ").concat(_.Qs.Violet[200]):"".concat((0,Y.A)(1)," solid ").concat(_.Qs.Neutrals[300]),width:(0,Y.A)(u?20:16),height:(0,Y.A)(u?20:16),minHeight:(0,Y.A)(u?20:16),minWidth:(0,Y.A)(u?20:16),maxWidth:(0,Y.A)(u?20:16),maxHeight:(0,Y.A)(u?20:16),display:"flex",alignItems:"center",justifyContent:"center",sx:{cursor:"pointer"},onClick:()=>(e=>{x(e!==ge?e:"")})(d),children:d===ge?(0,ie.jsx)(l.A,{display:"flex",flexDirection:"row",width:"100%",border:"1px solid ".concat(_.Qs.Error[500]),sx:{transform:"rotate(135deg)"}}):(0,ie.jsx)(pe.r,{icon:d,iconColor:s,sx:{fontSize:(0,Y.A)(12),color:s,display:"flex"}})})},"".concat(i,"-").concat(s,"-").concat(c,"-").concat(a,"-").concat(h,"-").concat(d,"-").concat(n,"-").concat(t))};return(0,ie.jsx)(ce.A,{swatchElement:(0,ie.jsx)(l.A,{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",children:h&&(0,ie.jsx)(pe.r,{icon:h,iconColor:s,sx:{fontSize:(0,Y.A)(16),color:s,display:"flex"}})},"".concat(s,"-").concat(h)),popperElement:(0,ie.jsxs)(l.A,{width:(0,Y.A)(196),height:(0,Y.A)(275),padding:"".concat((0,Y.A)(11)," ").concat((0,Y.A)(10)),display:"flex",flexDirection:"column",border:"1px solid ".concat(_.Qs.Neutrals[400]),borderRadius:"0 ".concat((0,Y.A)(2)," ").concat((0,Y.A)(2)," ").concat((0,Y.A)(2)),gap:(0,Y.A)(14),children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:(0,Y.A)(8),children:[(0,ie.jsx)(V.A,{placeholder:"Search badges",variant:"outlined",InputProps:{endAdornment:(0,ie.jsx)(M.gF,{icon:"Search",size:"xs"})},size:"small",autoFocus:!0,autoComplete:"off",onChange:I}),(0,ie.jsxs)(V.A,{variant:"outlined",select:!0,size:"small",value:a,onChange:y,children:[(0,ie.jsx)(le.A,{value:"all",children:"All Categories"}),Object.keys(de.UH).map((e=>(0,ie.jsx)(le.A,{value:e,children:de.UH[e].label},e)))]})]}),(0,ie.jsx)(ue.xA,{cellRenderer:N,columnCount:j[0].length,columnWidth:26,height:181,rowCount:j.length,rowHeight:26,width:174,autoWidth:!0}),(0,ie.jsxs)(l.A,{display:"flex",gap:(0,Y.A)(6),justifyContent:"flex-end",children:[(0,ie.jsx)(U.A,{variant:"text",onClick:w,size:"xsmall",children:"Cancel"}),(0,ie.jsx)(U.A,{variant:"contained",size:"xsmall",onClick:()=>(r(h),void g(null)),children:"Save"})]})]}),popperOpen:f,handleToggleSwatch:w,popperAnchorEl:p})};var Ae=i(64418);const fe=e=>{let{methods:t,name:i}=e;const r=t.getValues().folder,[s,o]=(0,n.useState)([]),[a,d]=(0,n.useState)(!1),[c,h]=(0,n.useState)(!1),x=(0,u.d4)(B.Un),p=(0,n.useRef)(),g=(0,n.useMemo)((()=>[...s,...x]),[x,s]),m=()=>{h(!0)},v=e=>{if("new-folder"===(e.currentTarget||e.target).id)return t.setValue("folder",null),void d(!0);d(!1),h(!1)},j=async e=>{e.stopPropagation(),e.preventDefault();const i=p.current.value;t.setValue("folder",i.trim(),{shouldDirty:!0}),o([...s,i]),d(!1),h(!1)},A=e=>{d(!1),e.stopPropagation(),e.preventDefault(),t.setValue("folder",r)},f=e=>{e.preventDefault()};return(0,ie.jsx)(J.xI,{name:i,control:t.control,render:e=>(0,ie.jsxs)(V.A,{variant:"outlined",id:"legend-folder",label:"Folder",labelPlacement:"top",...e,onChange:t=>{t.target.value&&e.onChange(t)},select:!0,SelectProps:{open:c,onOpen:m,onClose:v,value:e.value||G.FH},value:e.value||G.FH,children:[a&&(0,ie.jsx)(le.A,{onClickCapture:f,onKeyDown:e=>e.stopPropagation(),divider:!0,children:(0,ie.jsx)("form",{id:"new-folder-form",style:{width:"100%"},children:(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between !important",children:[(0,ie.jsx)(V.A,{variant:"outlined",required:!0,name:"newFolder",inputRef:p,autoFocus:!0,size:"small",onClick:e=>e.stopPropagation()}),(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[700],display:"flex",gap:(0,Y.A)(12),alignItems:"center",children:[(0,ie.jsx)(U.A,{variant:"iconOnly",color:"secondaryGrey",size:"small",onClick:A,children:(0,ie.jsx)(M.gF,{icon:"Close",size:"sm"})}),(0,ie.jsx)(U.A,{variant:"iconOnly",color:"success",size:"small",onClick:j,children:(0,ie.jsx)(M.gF,{icon:"CheckRegular",size:"sm"})})]})]})})}),(0,ie.jsx)(le.A,{value:G.FH,divider:!0,children:"Select folder"}),g.map(((e,t)=>(0,ie.jsx)(le.A,{divider:t<g.length-1,value:e,children:e},e))),!a&&(0,ie.jsx)(U.A,{color:"primary",startIcon:(0,ie.jsx)(M.gF,{icon:"Add",size:"sm"}),size:"small",id:"new-folder",sx:{marginLeft:(0,Y.A)(16),marginTop:(0,Y.A)(6)},children:"New folder"})]})})};var be=i(6904),ye=i(31287),Ce=i(51481),Ie=i(73083),we=i(87958),Ne=i(356);let Ee=function(e){return e.CREATE="create",e.EDIT="edit",e}({});const Se=e=>{let{legend:t,mode:i,handleSave:r,handleCancel:s}=e;const o=(0,u.wA)(),{orgId:a,chartId:c}=(0,d.useParams)(),[h,x]=n.useState(ee.tI.ICON),{confirmAction:g}=(0,Ae.A)(),m=(0,J.mN)({defaultValues:{...G.fG},mode:"onChange",shouldUnregister:!1}),v=(0,n.useRef)(null);(0,n.useEffect)((()=>{var e;null!==t&&void 0!==t&&t.id&&(m.register("id"),m.register("theme"),m.register("icon"),m.register("join"),m.register("folder"),m.reset({...t,folder:t.folder||G.FH}),m.setValue("icon",t.icon?t.icon:""),m.register("displayType"),null===(e=t.comparators)||void 0===e||e.forEach(((e,t)=>{m.register("comparators.".concat(t)),m.setValue("comparators.".concat(t),{...JSON.parse(JSON.stringify(e))})})),x(t.displayType||ee.tI.ICON))}),[t]);const{handleSubmit:j}=m,[A,f]=(0,W.A)(j((async()=>{var e;await(null===(e=v.current)||void 0===e?void 0:e.triggerSave());const t=m.getValues();t.folder=t.folder===G.FH?"":t.folder,t.icon=t.displayType===ee.tI.COLORBAR?"":t.icon||"",await r(!0,{...t,ruleId:null===t||void 0===t?void 0:t.id}),null===s||void 0===s||s()}))),b=!Object.keys(m.formState.errors).length;return(0,ie.jsx)(J.Op,{...m,children:(0,ie.jsx)(p.A,{loading:A,transparent:!0,children:(0,ie.jsx)("form",{id:"legend-form",onSubmit:f,style:{height:"100%"},children:(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",flexDirection:"column",height:"100%",children:[(0,ie.jsxs)(l.A,{minHeight:(0,Y.A)(400),minWidth:"100%",width:"100%",display:"flex",flexDirection:"column",maxHeight:(0,Y.A)(400),gap:3,children:[(0,ie.jsx)(O.A,{variant:H.Eq.h4,children:i===Ee.EDIT?"Edit legend item":"Create your first legend"}),(0,ie.jsx)(l.A,{flex:1,sx:{overflowY:"auto"},paddingRight:(0,Y.A)(16),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsx)(J.xI,{name:"label",control:m.control,defaultValue:t.label,render:e=>(0,ie.jsx)(V.A,{variant:"outlined",id:"legend-name",label:"Name",labelPlacement:"top",required:!0,...e})}),(0,ie.jsxs)(l.A,{gap:1,display:"flex",flexDirection:"column",children:[(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,children:"Item color"}),(0,ie.jsx)(l.A,{display:"flex",gap:2,children:(0,ie.jsx)(J.xI,{name:"color",control:m.control,rules:{required:!0,validate:e=>/^#(?:[0-9a-f]{3}|[0-9a-f]{6})$/i.test(e)},render:e=>(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(K.A,{initialColor:e.value,onChange:t=>{e.onChange(t)}}),(0,ie.jsx)(V.A,{variant:"outlined",id:"legend-color",fullWidth:!0,...e,value:e.value,error:!!m.errors.color,helperText:m.errors.color&&"Invalid color"})]})})})]}),(0,ie.jsx)(l.A,{gap:1,display:"flex",flexDirection:"column",alignItems:"flex-start",children:(0,ie.jsxs)(be.A,{children:[(0,ie.jsx)(l.A,{my:1,children:(0,ie.jsx)(ye.A,{component:"legend",children:(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,color:_.Qs.Neutrals[700],children:"Select where to visually highlight matches on your chart:"})})}),(0,ie.jsx)(l.A,{marginLeft:2,children:(0,ie.jsx)(J.xI,{name:"displayType",control:m.control,defaultValue:h,render:e=>(0,ie.jsxs)(Ce.A,{"aria-label":"displayType",name:"displayType",onChange:i=>{e.onChange(i),(e=>{const i=e.target.value;x(i),Ne.A.trackEvent({eventName:"LEGEND_DISPLAY_TYPE_CHANGED",extraParams:{legendId:null===t||void 0===t?void 0:t.id,displayType:i}})})(i)},value:h,defaultValue:h,children:[(0,ie.jsx)(Ie.A,{value:ee.tI.ICON,control:(0,ie.jsx)(we.A,{color:"primary",size:"small"}),label:(0,ie.jsx)(O.A,{variant:H.Eq.caption,children:"Badge Bar: This allows multiple matches to appear clearly without overriding your chart's role colors."})}),(0,ie.jsx)(Ie.A,{value:ee.tI.COLORBAR,control:(0,ie.jsx)(we.A,{color:"primary",size:"small"}),label:(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"flex-start",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.caption,children:"Role Color Bar: Override the role's color bar."}),(0,ie.jsx)(X.A,{placement:"top",title:"Note: This option supports only one match at a time.",arrow:!0,children:(0,ie.jsx)("div",{style:{marginLeft:"4px"},children:(0,ie.jsx)(M.Ay,{icon:"Info",size:"sm"})})})]})})]})})})]})}),h!==ee.tI.COLORBAR&&(0,ie.jsxs)(l.A,{gap:1,display:"flex",flexDirection:"column",children:[(0,ie.jsxs)(O.A,{variant:H.Eq.labelLG,display:"inline",children:["Badge"," ",(0,ie.jsx)(O.A,{variant:H.Eq.caption,display:"inline",color:_.Qs.Neutrals[700],children:"(Optional)"})]}),(0,ie.jsx)(l.A,{display:"flex",gap:2,alignItems:"center",children:(0,ie.jsx)(J.xI,{name:"icon",control:m.control,render:e=>{var t;return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(je,{initialIcon:e.value,color:m.watch("color"),onChange:t=>{e.onChange(t)}}),(0,ie.jsx)(V.A,{variant:"outlined",id:"legend-badge",fullWidth:!0,value:(null===(t=de.Wb[e.value])||void 0===t?void 0:t.label)||"",InputProps:{readOnly:!0}})]})}})})]}),(0,ie.jsx)(fe,{methods:m,name:"folder"}),(0,ie.jsx)(ae,{ref:v})]})})]}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",children:[(0,ie.jsx)(U.A,{variant:"text",color:"error",onClick:async()=>{g({title:"Delete legend item",message:"Are you sure you want to delete this legend item?",confirmButtonText:"Delete",cancelButtonText:"Cancel",execFunc:async()=>{await o(k.wL.deleteRule({orgId:a,chartId:c,ruleId:t.id})),Ne.A.trackEvent({eventName:"LEGEND_ITEM_DELETED",extraParams:{legendId:null===t||void 0===t?void 0:t.id}}),null===s||void 0===s||s()},cancelFunc:void 0})},startIcon:(0,ie.jsx)(M.gF,{icon:"Delete",size:"sm"}),sx:{paddingLeft:"0 !important"},children:"Delete legend item"}),(0,ie.jsxs)(l.A,{display:"flex",gap:2,children:[(0,ie.jsx)(U.A,{variant:"outlined",color:"primary",onClick:s,children:"Cancel"}),(0,ie.jsx)(U.A,{variant:"contained",color:"primary",type:"submit",form:"legend-form",disabled:!b,children:"Save changes"})]})]})]})})})})};var De=i(89119);const Le=e=>{let{handleLegendItemClick:t,activeLegendItemId:i=""}=e;const[r,s]=(0,n.useState)(""),o=(0,u.d4)(B.T3),{eventDebounce:a}=(0,he.A)(),d=(0,n.useMemo)((()=>(0,se.C)(o,r)),[r,o]),c=a((e=>{t(""),s(e.target.value)}),500);return(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:(0,Y.A)(12),maxHeight:(0,Y.A)(400),minWidth:(0,Y.A)(225),maxWidth:(0,Y.A)(225),children:[(0,ie.jsx)(O.A,{variant:H.Eq.h4,children:"Your legend items"}),(0,ie.jsx)(V.A,{placeholder:"Search legend item",variant:"outlined",InputProps:{endAdornment:(0,ie.jsx)(M.gF,{icon:"Search",size:"xs"})},onChange:e=>{c(e)}}),(0,ie.jsx)(l.A,{flex:1,sx:{overflowY:"scroll"},paddingRight:(0,Y.A)(16),children:(0,ie.jsx)(De._m,{legends:d,mode:De.Se.Manage,handleLegendEditSelection:t,selectedLegendItems:[i]})})]})},Te=e=>{let{open:t,handleClose:i,openMode:r=G.dX.Empty}=e;const[s,o]=(0,n.useState)(r),[a,c]=(0,n.useState)(null),h=(0,u.d4)(B.Zx),x=(0,u.d4)(B.T3),g=(0,u.d4)(B.lM),{orgId:m,chartId:v}=(0,d.useParams)(),j=(0,u.wA)();(0,n.useEffect)((()=>{var e;if(s===G.dX.AddFirst&&1===x.length)c(null===(e=x[0])||void 0===e?void 0:e.id);else if(s===G.dX.Add&&x.length>0){var t;c(null===(t=x[x.length-1])||void 0===t?void 0:t.id)}else s===G.dX.Empty&&x.length>0&&o(G.dX.List)}),[x,s]),(0,n.useEffect)((()=>{null!==h&&void 0!==h&&h.id||f()}),[h]);const[A,f]=(0,W.A)((async function(){null!==h&&void 0!==h&&h.id||await j(k.wL.createLegend({orgId:m,chartId:v,data:{title:"Legend"}}))})),[b,y]=(0,W.A)((async function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{label:null!==x&&void 0!==x&&x.length?"Badge ".concat(x.length+1):"Badge 1",theme:G.z5.Light,icon:null,color:_.Qs.Violet[500]};await j(k.wL.addUpdateRule({orgId:m,chartId:v,data:t})),Ne.A.trackEvent({eventName:"LEGEND_ITEM_CREATED",extraParams:{rule:t.label}}),s===G.dX.AddFirst&&e&&(o(G.dX.List),c(""))})),C=async()=>{0===x.length?o(G.dX.AddFirst):o(G.dX.Add),await y(!1)},I=()=>{c(null),[G.dX.Add,G.dX.AddFirst,G.dX.Edit].includes(s)&&o(G.dX.List)},{showAddFirst:w,showEmptyState:N,showLegendList:E}=(0,n.useMemo)((()=>{const e=x.length<=1&&s===G.dX.AddFirst||(x.length<1||s===G.dX.AddFirst)&&b;return{showAddFirst:e,showEmptyState:0===x.length||0===x.length&&s===G.dX.Empty,showLegendList:(x.length>=1&&[G.dX.Add,G.dX.Edit,G.dX.List].includes(s)||b)&&!e}}),[x,s,b]);return(0,ie.jsxs)(R.A,{open:t,onClose:i,maxWidth:"md",children:[(0,ie.jsxs)(z.A,{children:[(0,ie.jsx)(O.A,{color:"inherit",variant:H.Eq.h1,children:"Manage legend items"}),(0,ie.jsx)(F.A,{onClick:i,children:(0,ie.jsx)(M.gF,{icon:"Close",size:"sm",color:"inherit"})})]}),(0,ie.jsx)(Q.A,{children:(0,ie.jsxs)(l.A,{minWidth:(0,Y.A)(750),minHeight:(0,Y.A)(500),children:[(0,ie.jsxs)(p.A,{loading:A,transparent:!0,children:[N&&!w&&(0,ie.jsx)(l.A,{display:"flex",alignItems:"center",justifyContent:"center",height:(0,Y.A)(500),children:(0,ie.jsx)(q.g,{title:"Create your first legend",description:"Legends allow you to apply colors and badges to roles on your org chart, helping clarify team structures at a glance. Set up your first legend to see it in action.",handleCreateLegendClick:C})}),w&&(0,ie.jsx)(p.A,{loading:b,transparent:!0,children:(0,ie.jsx)(l.A,{paddingTop:(0,Y.A)(26),display:"flex",justifyContent:"center",children:(0,ie.jsx)(Se,{legend:g[a]||G.fG,mode:Ee.CREATE,handleSave:y,handleCancel:I})})})]}),E&&(0,ie.jsxs)(l.A,{display:"flex",gap:(0,Y.A)(36),paddingTop:(0,Y.A)(26),paddingBottom:(0,Y.A)(16),height:"100%",minHeight:(0,Y.A)(500),children:[(0,ie.jsx)(Le,{handleLegendItemClick:e=>{c(e)},activeLegendItemId:a}),(0,ie.jsx)(l.A,{flex:1,children:(0,ie.jsx)(l.A,{width:"100%",display:"flex",justifyContent:a?"flex-start":"center",alignItems:a?"flex-start":"center",height:"100%",children:a||b?(0,ie.jsx)(p.A,{loading:b,transparent:!0,children:(0,ie.jsx)(Se,{legend:g[a]||G.fG,mode:Ee.EDIT,handleSave:y,handleCancel:I})}):(0,ie.jsx)(q.g,{title:"Create a new legend item or edit existing items on the left.",handleCreateLegendClick:C})})})]})]})})]})};var Pe=i(19924),ke=i(32143),Re=i(6803),ze=i(39336),Oe=i(67202),Fe=i(98433),Qe=i(89656),Me=i(8924),He=i(15813),qe=i(84),_e=i(92517),We=i(66588),Be=i(94642),Ge=i(23993);const Ve=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Fe.kT),i=(0,u.d4)(Qe.xu),r=(0,u.d4)(Fe.r2),s=(0,u.d4)(Me.EK),o=(0,u.d4)(Fe.Ut),a=(0,u.d4)(Fe.qG),h=(0,u.d4)(Fe.xy),x=(0,u.d4)(Fe.r2),p=(0,u.d4)(Ge.aC),g=(0,u.d4)(Fe.eI),{openDialog:m}=(0,qe.A)("createNewTemplate"),{confirmAction:v}=(0,Ae.A)(),{orgId:j}=(0,d.useParams)(),{params:f}=(0,c.u)([(0,A.si)()]),{show:b}=(0,We.A)(),y=(0,d.useHistory)(),{resetPageNumber:C}=(0,Be.A)();(0,n.useEffect)((()=>{""===x&&e((0,Fe.j$)(s))}),[s,e,x]);const[I,w]=(0,W.A)((async()=>{if(!a)return;const t=(0,_e.CQ)(h);await e(Fe.kv.update({orgId:j,templateId:a,data:t})),b("Print settings saved","success",2e3)})),N=t=>{t!==He._6.DIRECTORY&&t!==He._6.PHOTOBOARD||(e((0,Fe.o4)({...g,visible:!1})),e((0,Fe.L$)({...p,pagination:He.ti.MULTIPLE,outputType:He.yQ.STANDARD,pageFormat:{size:He.Nn[He.yQ.STANDARD][0],orientation:He.t4.LANDSCAPE}}))),e((0,Fe.mh)(t)),y.push((0,A.si)({...f,resource:t}))};return(0,n.useEffect)((()=>{N(t)}),[t]),(0,ie.jsxs)(l.A,{display:"flex",paddingX:"20px",bgcolor:_.Qs.Violet[0],borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.h2,justifySelf:"start",minWidth:"fit-content",children:"Print settings"}),(0,ie.jsxs)(l.A,{display:"flex",width:"100%",justifyContent:"end",gap:1,alignItems:"center",children:[(0,ie.jsxs)(l.A,{display:"flex",gap:1.5,alignItems:"center",margin:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"What to print:"}),(0,ie.jsx)(Oe.H_,{value:t,select:!0,onChange:e=>N(e.target.value),children:Object.values(He._6).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))})]}),(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}}),(0,ie.jsxs)(l.A,{display:"flex",gap:1.5,alignItems:"center",margin:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Theme"}),(0,ie.jsx)(Oe.H_,{value:r||s,select:!0,onChange:t=>e((0,Fe.j$)(t.target.value)),children:i.map((e=>(0,ie.jsx)(ke.A,{value:e.id,children:e.name},e.id)))})]}),(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}}),(0,ie.jsxs)(l.A,{display:"flex",gap:1.5,alignItems:"center",marginLeft:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Template"}),(0,ie.jsxs)(Oe.H_,{value:a||"noTemplates",select:!0,onChange:t=>{C(),e((0,Fe.sM)(void 0)),e((0,Fe.iZ)(t.target.value))},disabled:!o.length,children:[o.map((e=>(0,ie.jsx)(ke.A,{value:e.id,children:e.name},e.id))),(!o.length||!a)&&(0,ie.jsx)(ke.A,{value:"noTemplates",disabled:!0,children:"No template selected"})]})]}),(0,ie.jsx)(X.A,{title:"Save settings to selected template",children:(0,ie.jsx)("span",{children:(0,ie.jsx)(F.A,{color:"primary",onClick:w,disabled:!a||!!I,children:(0,ie.jsx)(M.gF,{icon:"Save",size:"lg"})})})}),(0,ie.jsx)(X.A,{title:"Delete selected template",children:(0,ie.jsx)("span",{children:(0,ie.jsx)(F.A,{color:"primary",onClick:()=>{a&&v({message:"This will delete this print template. Are you sure?",title:"Delete Print Template",execFunc:async()=>{await e(Fe.kv.delete({orgId:j,templateId:a}))},cancelFunc:()=>{}})},disabled:!a,children:(0,ie.jsx)(M.gF,{icon:"Delete",size:"lg"})})})}),(0,ie.jsx)(Oe.OV,{variant:"outlined",onClick:m,children:"Create print template"})]})]})};var Xe=i(13888),Ue=i(80192);const Ye=(0,Ue.Mz)(Ge.Xp,Fe.db,((e,t)=>{const i=["tableOfContents","pageCount"],n=Object.keys(e).filter((e=>!i.includes(e))),l=[];for(let o=0;o<(null===(r=e.tableOfContents)||void 0===r?void 0:r.totalToCPages);o++){var r;l.push({pageType:"tableOfContents",pageNumber:o+1+(null!==t&&void 0!==t&&t.visible?1:0)})}const s={};for(let o=0;o<n.length;o++){const t=n[o];s[o]=e[t].pageNumber}return{listOfPages:n,tocRowIndexPageNumberMap:s,tableOfContentPages:l}})),Je=(0,Ue.Mz)((e=>{var t,i;return null===(t=e.chart)||void 0===t||null===(i=t.info)||void 0===i?void 0:i.legend}),(e=>{var t;return(null===(t=(null===e||void 0===e?void 0:e.rules)||[])||void 0===t?void 0:t.length)>0}));var Ke=i(6124);const $e=()=>{const e=(0,u.wA)(),{orgId:t,chartId:i}=(0,d.useParams)(),r=(0,d.useHistory)(),[s,o]=(0,n.useState)(!1),a=(0,u.d4)(Xe.$x),{pageCount:c}=(0,Be.A)(),{tocRowIndexPageNumberMap:h,tableOfContentPages:x}=(0,u.d4)(Ye),{openDialog:p}=(0,qe.A)("alert"),{resetPageReady:g}=(0,Ke.A)(),m=(0,u.d4)(Fe.db),v=(0,u.d4)(Fe.DD),j=(0,n.useMemo)((()=>{let e=x.length;return m.visible&&e++,v.visible&&v.ownPage&&e++,e}),[x,m,v]);return(0,ie.jsxs)(l.A,{display:"flex",padding:2,bgcolor:_.Qs.Neutrals[200],justifyContent:"space-between",borderTop:"1px solid ".concat(_.Qs.Neutrals[400]),children:[(0,ie.jsx)(Oe.OV,{variant:"outlined",onClick:()=>{g(),r.push((0,A.Wx)({base:"protected",chartId:i,orgId:t}))},children:"Cancel"}),(0,ie.jsx)(Oe.OV,{variant:"contained",onClick:async()=>{o(!0);const{error:n}=await e(Fe.kv.addPrintJob({orgId:t,chartId:i,type:a.fileType,data:{...a,pageCount:c,tableOfContentPageCount:j,tocRowIndexPageNumberMap:h}}));n?p({title:"Error",message:"Error sending job to printer, please try again."}):await e(Fe.kv.getInProgressPrintJobs({orgId:t,chartId:i})),o(!1)},disabled:s,children:"Print to File"})]})};var Ze=i(90889),et=i(39948),tt=i(53193),it=i(68577),nt=i(4598),lt=i(10621),rt=i(49194),st=i(86697),ot=i(35721),at=i(80539);const dt=["location","department","embedded"],ct=e=>{let{option:t,vacantText:i,handleSelect:n}=e;const{role:r}=t,{members:s,type:o}=r,[a]=s||[],{photo:d}=(0,lt.LS)(a||{}),c=(0,lt.mA)(r),u=(0,lt.Bx)(a)||i,h=!dt.includes(o);return(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",sx:{cursor:"pointer","&:hover":{backgroundColor:_.Qs.Neutrals[200]}},padding:1,gap:1,onClick:()=>null===n||void 0===n?void 0:n(t),children:[(0,ie.jsx)(l.A,{marginX:1,children:(0,ie.jsx)(at.A,{width:30,height:30,name:h?u:c,src:d,avatarId:null===a||void 0===a?void 0:a.id,variant:void 0,shape:void 0,children:void 0,fontSize:1})}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",children:[h&&(0,ie.jsx)(O.A,{variant:H.Eq.subheadingMD,children:u}),(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,flexWrap:"wrap",children:c})]})]})};var ut=i(63336),ht=i(37918),xt=i(26353),pt=i(8266),gt=i(74079),mt=i(45418);const vt=e=>{var t,i;let{role:n,removeAt:r,index:s,anchored:o=!1}=e;const{personName:a,name:d,firstRoleMember:c,id:h}=n,x=(0,u.d4)(mt.xt),p=(0,u.d4)(gt.A),{photo:g}=(0,lt.LS)(x[c||(null===(t=p[h])||void 0===t||null===(i=t.members)||void 0===i?void 0:i[0])]||{});return(0,ie.jsx)(ut.A,{variant:"outlined",children:(0,ie.jsxs)(ht.Ay,{sx:{paddingRight:5,paddingLeft:1,cursor:"grab"},children:[(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[o?(0,ie.jsx)(M.gF,{icon:"Anchor",size:"sm",color:_.Qs.Neutrals[400]}):(0,ie.jsx)(M.gF,{icon:"DragGrid",size:"sm",color:_.Qs.Neutrals[400]}),(a||g)&&(0,ie.jsx)(at.A,{width:30,height:30,name:a,src:g,avatarId:void 0,variant:void 0,shape:void 0,children:void 0,fontSize:1})]}),(0,ie.jsx)(xt.A,{primary:(0,ie.jsx)(O.A,{variant:H.Eq.subheadingMD,children:a}),secondary:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:d}),sx:{paddingLeft:1.5,display:"flex",flexDirection:"column"},disableTypography:!0}),!o&&r&&(0,ie.jsx)(pt.A,{sx:{right:8},children:(0,ie.jsx)(F.A,{onClick:()=>r(s),children:(0,ie.jsx)(M.gF,{icon:"Close",size:"sm"})})})]})})};var jt=i(34976),At=i(7743);const ft=e=>{var t;let{roles:i,handleMove:n,removeAt:r,handleSelect:s,multiple:o=!1,children:a}=e;const d=(0,u.d4)(At.gJ),c=null===d||void 0===d||null===(t=d.roleName)||void 0===t?void 0:t.id,{results:h,loading:x,handleInputChange:p,query:g}=(0,jt.A)({model:"role",fieldId:c,defaultChartId:void 0,defaultOrgId:void 0});return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(st.A,{options:h.filter((e=>!i.some((t=>t.id===e.role.id)))).reduce(((e,t)=>e.some((e=>e.role.id===t.role.id))?e:[...e,t]),[]),renderInput:e=>(0,ie.jsx)(Oe.H_,{...e,placeholder:"Search for ".concat(o?"roles":"a role")}),loading:x,onInputChange:(e,t)=>{p(t)},fullWidth:!0,getOptionLabel:e=>{var t;return(null===(t=e.role)||void 0===t?void 0:t.id)||""},filterOptions:e=>e,renderOption:(e,t)=>(0,ie.jsx)(l.A,{...e,component:"li",sx:{minHeight:"fit-content !important"},children:(0,ie.jsx)(ct,{option:t,vacantText:"Role"})},t.role.id),value:i,onChange:(e,t)=>{t&&s(t)},noOptionsText:g?"No roles found":"Start typing to search for ".concat(o?"roles":"a role"),slotProps:{paper:{sx:{typography:H.uT.bodySM}}}}),a,(0,ie.jsx)(rt.A,{type:"TOP_ROLE_ID",items:i,handleItemsReordered:n,renderItem:(e,t)=>(0,ie.jsx)(vt,{role:e,removeAt:r,index:t},e.id),itemKey:"",ListComponent:e=>(0,ie.jsx)(ot.A,{disablePadding:!0,sx:{gap:1,display:"flex",flexDirection:"column"},...e}),emptyItem:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700],children:"No role".concat(o?"s":""," selected")})})]})};var bt=i(91945);const yt=()=>{var e;const t=(0,u.wA)(),i=(0,u.d4)(Ge.GB),n=(0,u.d4)(Ge.aC),{addPageBreak:l,removePageBreak:r}=(0,bt.A)(),s=(0,u.d4)(mt.xt),{chartId:o}=(0,d.useParams)();return(0,ie.jsx)(ft,{roles:Object.values(n.pageBreaks).flat(),handleMove:(e,i)=>{const l=[...Object.values(n.pageBreaks).flat()],[r]=l.splice(i,1);l.splice(e,0,r),t((0,Fe.GE)({chartId:o,pageBreaks:l}))},removeAt:e=>{r({role:Object.values(n.pageBreaks).flat()[e]})},handleSelect:e=>{let{role:t}=e;l({role:t})},multiple:!0,children:(0,ie.jsx)(vt,{role:{id:i.id,name:(0,lt.mA)(i),personName:null!==(e=i.members)&&void 0!==e&&e[0]&&s[i.members[0]]?(0,lt.Bx)(s[i.members[0]]||""):""},index:0,anchored:!0})})},Ct=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Ge.aC),{range:i,topRoles:r,pagination:s,breakBy:o,outputType:a,pageFormat:d,nLevels:c}=t,h=(0,u.d4)(Fe.kT),x=(0,u.d4)(L.G0),p={traditional:[{value:He.N0.BESTFIT,label:"Best Fit (Beta)"},{value:He.N0.LEVEL,label:"By Level"},{value:He.N0.DEPARTMENT,label:"By Department"},{value:He.N0.LOCATION,label:"By Location"},{value:He.N0.MANUAL,label:"Manual page breaks"}],matrix:[{value:He.N0.TEAM,label:"By Team"},{value:He.N0.FUNCTION,label:"By Function"},{value:He.N0.FUNCTIONTEAM,label:"By Function & Team"}]},g=i=>{e((0,Fe.L$)({...t,range:i}))};return(0,n.useEffect)((()=>{p[x].find((e=>e.value===o))||e((0,Fe.L$)({...t,breakBy:p[x][0].value}))}),[x,o]),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Print Range"}),(0,ie.jsx)(X.A,{title:He.z7.printRange,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsxs)(et.A,{variant:"outlined",size:"small",children:[(0,ie.jsx)(U.A,{fullWidth:!0,value:He.J_.FULL,onClick:()=>g(He.J_.FULL),variant:i===He.J_.FULL?"contained":"outlined",color:i===He.J_.FULL?"primary":"secondaryGrey",sx:{color:i===He.J_.FULL?"white":"inherit",fontWeight:"normal !important"},children:"Entire chart"}),(0,ie.jsx)(U.A,{fullWidth:!0,value:He.J_.ROLES,onClick:()=>g(He.J_.ROLES),variant:i===He.J_.ROLES?"contained":"outlined",color:i===He.J_.ROLES?"primary":"secondaryGrey",sx:{color:i===He.J_.ROLES?"white":"inherit",fontWeight:"normal !important"},children:"Specific sections"})]}),t.range===He.J_.ROLES&&(0,ie.jsx)(ft,{roles:r,handleMove:(i,n)=>{const l=[...r],[s]=l.splice(n,1);l.splice(i,0,s),e((0,Fe.L$)({...t,topRoles:l}))},removeAt:i=>{const n=[...r];n.splice(i,1),e((0,Fe.L$)({...t,topRoles:n}))},handleSelect:i=>{let{role:n}=i;const{id:l,members:r}=n,s=(0,lt.mA)(n);let o,a="";if(r&&r.length){const[{name:e,photo:t}]=r;a=e||(0,lt.Bx)(r[0])||"",o=t}e((0,Fe.L$)({...t,topRoles:[{id:l,name:s,personName:a,personPhoto:o}]}))}})]}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Pagination"}),(0,ie.jsx)(X.A,{title:He.z7.pagination,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(tt.A,{children:(0,ie.jsxs)(Oe.gb,{value:s,onChange:i=>{return n=i.target.value,void e((0,Fe.L$)({...t,pagination:n,nLevels:n===He.ti.SINGLE?0:c||3}));var n},children:[(0,ie.jsx)(it.A,{value:He.ti.SINGLE,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"Single page",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}},disabled:h===He._6.DIRECTORY||h===He._6.PHOTOBOARD}),(0,ie.jsx)(it.A,{value:He.ti.MULTIPLE,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"Multiple pages",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}}})]})})]}),s===He.ti.MULTIPLE&&!(h===He._6.DIRECTORY||h===He._6.PHOTOBOARD)&&(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Auto-break options"}),(0,ie.jsx)(X.A,{title:He.z7.breakBy,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(Oe.H_,{select:!0,variant:"outlined",value:o,onChange:i=>{return n=i.target.value,e((0,Fe.sM)(void 0)),void(n===He.N0.LEVEL?e((0,Fe.L$)({...t,breakBy:n,nLevels:3})):e((0,Fe.L$)({...t,breakBy:n})));var n},children:p[x].map((e=>(0,ie.jsx)(ke.A,{value:e.value,disabled:e.value===He.N0.BESTFIT&&a===He.yQ.SCREEN,children:e.label},e.value)))}),o===He.N0.MANUAL&&(0,ie.jsx)(yt,{})]}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Output type"}),(0,ie.jsx)(X.A,{title:He.z7.outputType,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(tt.A,{children:(0,ie.jsxs)(Oe.gb,{value:a,onChange:i=>{return n=i.target.value,e((0,Fe.sM)(void 0)),void(n!==He.yQ.SCREEN||o!==He.N0.BESTFIT?e((0,Fe.L$)({...t,outputType:n,pageFormat:{size:He.Nn[n][0],orientation:He.t4.LANDSCAPE}})):e((0,Fe.L$)({...t,outputType:n,pageFormat:{size:He.Nn[n][0],orientation:He.t4.LANDSCAPE},breakBy:He.N0.LEVEL})));var n},children:[(0,ie.jsx)(it.A,{value:He.yQ.SCREEN,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"Screen/Email PDF",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}},disabled:h===He._6.DIRECTORY||h===He._6.PHOTOBOARD}),(0,ie.jsx)(it.A,{value:He.yQ.STANDARD,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"Print-ready (Standard) PDF",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}}}),(0,ie.jsx)(it.A,{value:He.yQ.LARGE,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"Print-ready (Large format) PDF",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}}}),(0,ie.jsx)(it.A,{value:He.yQ.POWERPOINT,control:(0,ie.jsx)(Oe.ls,{size:"small"}),label:"PowerPoint Slides",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}}})]})})]}),a!==He.yQ.SCREEN&&(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Page format"}),(0,ie.jsx)(X.A,{title:He.z7.pageFormat,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(Oe.H_,{select:!0,variant:"outlined",value:He.q2[d.size].labels[d.orientation],children:He.Nn[a].map((i=>Object.keys(He.q2[i].labels).map((n=>(0,ie.jsx)(ke.A,{value:He.q2[i].labels[n],onClick:()=>{return l={size:i,orientation:n},e((0,Fe.sM)(void 0)),void e((0,Fe.L$)({...t,pageFormat:l}));var l},children:He.q2[i].labels[n]},i+n)))))})]}),(o===He.N0.LEVEL||s===He.ti.SINGLE)&&(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:"Levels to print"}),(0,ie.jsx)(X.A,{title:He.z7.nLevels,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsxs)(tt.A,{component:l.A,display:"flex",flexDirection:"column",gap:1.5,width:"100%",children:[s===He.ti.SINGLE&&(0,ie.jsx)(it.A,{control:(0,ie.jsx)(nt.A,{onChange:(i,n)=>(i=>{e(i?(0,Fe.L$)({...t,nLevels:0}):(0,Fe.L$)({...t,nLevels:3}))})(n),checked:c<=0}),label:"Include all hierarchy levels",labelPlacement:"start",sx:{marginLeft:0,width:"100%",justifyContent:"space-between"},slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[800]}}}),c>0&&(0,ie.jsx)(it.A,{control:(0,ie.jsx)(Oe.H_,{sx:{maxWidth:60},type:"number",inputProps:{min:s===He.ti.MULTIPLE?2:0},defaultValue:c,onChange:i=>{var n;Number(i.target.value)>=0&&("number"!==typeof(n=Number(i.target.value))||isNaN(n)||n<=0||e((0,Fe.L$)({...t,nLevels:n})))}}),label:"Number of levels to include",labelPlacement:"start",sx:{marginLeft:0,width:"100%",justifyContent:"space-between"},slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[800]}}})]})]})]})};var It=i(51962),wt=i(88446),Nt=i(48853),Et=i(49092),St=i(83972);const Dt=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Fe.kT),i=(0,u.d4)(L.G0),{pagination:r}=(0,u.d4)(Ge.aC),s=(0,u.d4)(Fe.X5),o=(0,u.d4)(Fe.pl),{header:a,footer:c}=o,{userHasMinAccess:x}=(0,Nt.A)(),p=x(j.td.ADMIN),{t:g}=(0,Et.B)(),{name:m,logo:v}=(0,u.d4)(h.BF),{openDialog:A}=(0,qe.A)("memberPhoto"),{orgId:f}=(0,d.useParams)(),b=(0,u.d4)(St._l);(0,n.useEffect)((()=>{null===c.text&&e((0,Fe.eE)({...c,text:"Confidential - Property of ".concat(b.name)}))}),[b.name,c]);const y=t=>{e((0,Fe.WH)(t))},C=t=>{e((0,Fe.WH)({...a,subtitle:t}))},I=t=>{e((0,Fe.eE)(t))},w=async t=>{const i={logo:"string"===typeof t.newPhoto?t.newPhoto:null,name:m};await(async t=>{await e(h.no.update({orgId:f,data:t}))})(i)},N=r===He.ti.MULTIPLE&&s>1&&t===He._6.CHART&&i===j.XD.TRADITIONAL;return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Header"}),(0,ie.jsx)(nt.A,{checked:a.visible,onChange:(e,t)=>y({...a,visible:t})})]}),a.visible&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(Oe.H_,{placeholder:"Header text",value:a.text,onChange:e=>y({...a,text:e.target.value})}),(0,ie.jsxs)(l.A,{display:"grid",gridTemplateColumns:"1fr 1fr",gap:3,children:[(0,ie.jsx)(Oe.H_,{select:!0,value:a.size,onChange:e=>y({...a,size:e.target.value}),label:"Size",required:!0,children:Object.values(He.or).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsx)(Oe.H_,{select:!0,value:a.position,onChange:e=>y({...a,position:e.target.value}),label:"Position",required:!0,children:Object.values(He.yX).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsx)(Oe.H_,{select:!0,value:a.fontWeight,onChange:e=>y({...a,fontWeight:e.target.value}),label:"Font weight",required:!0,children:Object.values(He.IT).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:"3px",justifyContent:"space-between",children:[(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800],children:"Text color"}),(0,ie.jsx)(K.A,{initialColor:a.textColor,onChange:e=>y({...a,textColor:e})})]})]}),(0,ie.jsxs)(tt.A,{component:l.A,display:"flex",flexDirection:"column",gap:2,children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:a.subtitle.visible,onChange:(e,t)=>C({...a.subtitle,visible:t}),size:"small",sx:{paddingY:0}}),label:"Include subtitle",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}}),a.subtitle.visible&&(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:.5,children:[(0,ie.jsx)(Oe.H_,{placeholder:"Subtitle text",value:a.subtitle.text,onChange:e=>C({...a.subtitle,text:e.target.value}),disabled:N&&a.subtitle.useToC}),N&&(0,ie.jsx)(it.A,{control:(0,ie.jsx)(nt.A,{checked:a.subtitle.useToC,onChange:(e,t)=>C({...a.subtitle,useToC:t}),size:"small"}),label:"Use table of contents as subtitle",labelPlacement:"start",sx:{marginLeft:0,width:"100%",justifyContent:"space-between"},slotProps:{typography:{variant:H.Eq.subheadingXS,color:_.Qs.Neutrals[800]}}})]})]}),(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:a.companyLogo,onChange:(e,t)=>y({...a,companyLogo:t}),size:"small",sx:{paddingY:0}}),label:"Company logo",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}},disabled:!v}),p&&(0,ie.jsx)(wt.A,{onClick:()=>{A({organization:m,handleChangePhoto:w,type:"logo",allowAvatars:!1,allowBatchUpload:!1,allowLinkedInSearch:!1,square:!1,photo:v,entity:"organization",photoMaxWidth:1e3})},variant:H.Eq.caption,children:g(v?"Organization.Links.ChangeLogo":"Organization.Links.UploadLogo")})]}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:a.ignoreMargin,onChange:(e,t)=>y({...a,ignoreMargin:t}),size:"small",sx:{paddingY:0}}),label:"Ignore header margin",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}}),(0,ie.jsx)(X.A,{title:He.z7.ignoreHeaderMargin,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]})]})]})]})}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Footer"}),(0,ie.jsx)(nt.A,{checked:c.visible,onChange:(e,t)=>I({...c,visible:t})})]}),c.visible&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:.5,children:[(0,ie.jsx)(Oe.H_,{placeholder:"Footer text",value:c.text,onChange:e=>I({...c,text:e.target.value}),disabled:c.useTimestamp}),(0,ie.jsx)(it.A,{control:(0,ie.jsx)(nt.A,{checked:c.useTimestamp,onChange:(e,t)=>I({...c,useTimestamp:t}),size:"small"}),label:"Use timestamp as footer",labelPlacement:"start",sx:{marginLeft:0,width:"100%",justifyContent:"space-between"},slotProps:{typography:{variant:H.Eq.subheadingXS,color:_.Qs.Neutrals[800]}}})]}),(0,ie.jsxs)(l.A,{display:"grid",gridTemplateColumns:"1fr 1fr",gap:3,children:[(0,ie.jsx)(Oe.H_,{select:!0,value:c.size,onChange:e=>I({...c,size:e.target.value}),label:"Size",required:!0,children:Object.values(He.or).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsx)(Oe.H_,{select:!0,value:c.position,onChange:e=>I({...c,position:e.target.value}),label:"Position",required:!0,children:Object.values(He.yX).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))})]}),(0,ie.jsx)(tt.A,{component:l.A,display:"flex",flexDirection:"column",gap:2,children:(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:c.organimiLogo,onChange:(e,t)=>I({...c,organimiLogo:t}),size:"small",sx:{paddingY:0}}),label:"Organimi logo",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}})})]})]})})]})},Lt=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Fe.kT),{pagination:i}=(0,u.d4)(Ge.aC),r=(0,u.d4)(Je),s=(0,u.d4)(Fe.IU),o=(0,u.d4)(Fe.DD),a=t=>{e((0,Fe.gb)(t))},d=t=>{e((0,Fe.$o)(t))};return(0,n.useEffect)((()=>{He.KO[t].map((e=>e.value)).includes(o.position)||d({...o,position:He.KO[t][0].value})}),[t]),(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsxs)(l.A,{display:"flex",gap:2,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Legend"}),!r&&(0,ie.jsx)(X.A,{title:"Legend not found for this chart",children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(nt.A,{disabled:!r,checked:o.visible,onChange:(e,t)=>d({...o,visible:t})})]}),o.visible&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(Oe.H_,{select:!0,value:o.position,onChange:e=>d({...o,position:e.target.value}),label:"Position",required:!0,disabled:!r||o.ownPage,children:He.KO[t].map((e=>{let{value:t,label:i}=e;return(0,ie.jsx)(ke.A,{value:t,children:i},t)}))}),(0,ie.jsx)(tt.A,{children:(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:o.ownPage,disabled:!r,onChange:(e,t)=>d({...o,ownPage:t}),size:"small",sx:{paddingY:0}}),label:"Place legend on its own page",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}})})]})]})}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Page numbers"}),(0,ie.jsx)(nt.A,{checked:s.pageNumbers,onChange:(e,t)=>a({...s,pageNumbers:t})})]}),i===He.ti.MULTIPLE&&t===He._6.CHART&&(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"Multi-page indicators"}),(0,ie.jsx)(nt.A,{checked:s.pageIndicators,onChange:(e,t)=>a({...s,pageIndicators:t})})]}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,children:"LinkedIn icons"}),(0,ie.jsx)(nt.A,{checked:s.linkedInIcons,onChange:(e,t)=>a({...s,linkedInIcons:t})})]}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingLG,sx:{display:"flex",gap:1,alignItems:"center"},children:["Role colors",(0,ie.jsx)(X.A,{title:He.z7.roleColors,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(nt.A,{checked:s.roleColors,onChange:(e,t)=>a({...s,roleColors:t})})]}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingLG,sx:{display:"flex",gap:1,alignItems:"center"},children:["Role counts",(0,ie.jsx)(X.A,{title:He.z7.roleCounts,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(nt.A,{checked:s.roleCounts,onChange:(e,t)=>a({...s,roleCounts:t})})]}),t===He._6.CHART&&(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingLG,sx:{display:"flex",gap:1,alignItems:"center"},children:["Chart navigation",(0,ie.jsx)(X.A,{title:He.z7.chartNavigation,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsxs)(Oe.H_,{select:!0,value:s.chartNavigation,onChange:e=>a({...s,chartNavigation:e.target.value}),children:[(0,ie.jsx)(ke.A,{value:He.xq.MANAGER_PATH,children:"Show manager path"}),(0,ie.jsx)(ke.A,{value:He.xq.PARENT_PAGE_LINK,children:"Show parent page link"}),(0,ie.jsx)(ke.A,{value:He.xq.NO_PARENT_LINK,children:"Do not show link back"})]})]})]})})]})};var Tt=i(79650),Pt=i(71806),kt=i(73460),Rt=i(28076),zt=i(39652);const Ot=e=>{let{label:t,onChange:i,controlled:r,fullWidth:s=!1,fontSize:o,fontWeight:a,color:d,disabled:c=!1,noButtons:u=!1,wrap:h=!1}=e;const[x,p]=(0,n.useState)(!1),[g,m]=(0,n.useState)(t),v=e=>{e.preventDefault(),i(g),p(!1),r&&r.setEdit(!1)},j=(0,n.useMemo)((()=>{const e=document.createElement("canvas").getContext("2d");if(!e)return{width:"auto",height:"auto"};let t=getComputedStyle(document.body).fontFamily;t="".concat(a||(o?"400":H.uT.subheadingSM.fontWeight)," ").concat(o?"".concat(o,"px"):H.uT.subheadingSM.fontSize," ").concat(t),e.font=t;const i=e.measureText(g);return{width:"".concat(i.width,"px"),height:o?"".concat(1.43*o,"px"):"14px"}}),[o,a,g]);return(0,n.useEffect)((()=>{r||(m(t),p(!1))}),[t]),(0,ie.jsx)(l.A,{display:"flex",alignItems:"center",gap:1,flexShrink:1,overflow:"hidden",children:null!==r&&void 0!==r&&r.edit||x?(0,ie.jsxs)("form",{style:{display:"flex",alignItems:"center",gap:"4px"},onSubmit:v,onReset:e=>{e.preventDefault(),m(t),p(!1),r&&r.setEdit(!1)},children:[(0,ie.jsx)(Oe.ZK,{value:g,onChange:e=>m(e.target.value),autoFocus:!0,sx:{marginRight:"4px",width:s?"100%":j.width},InputProps:{disableUnderline:!0,style:{fontSize:o,fontWeight:a,height:j.height}},variant:"standard",onBlur:e=>{u&&v(e)}}),(0,ie.jsx)(U.A,{variant:"iconOnly",size:"small",type:"submit",color:"success",sx:{display:u?"none":"flex"},children:(0,ie.jsx)(M.gF,{icon:"Check",size:"xs"})}),(0,ie.jsx)(U.A,{variant:"iconOnly",size:"small",type:"reset",color:"secondaryGrey",sx:{display:u?"none":"flex"},children:(0,ie.jsx)(M.gF,{icon:"Close",size:"xs"})})]}):(0,ie.jsx)(ie.Fragment,{children:(0,ie.jsx)(O.A,{variant:o||a?"inherit":H.Eq.subheadingSM,textOverflow:"ellipsis",noWrap:!h,onClick:()=>{c||(r?r.onClickLabel():(m(t),p(!0)))},sx:{":hover":{textDecoration:c?"none":"underline",cursor:c?"default":"pointer"}},fontSize:o,fontWeight:a,color:d,width:s?"100%":"auto",children:t})})})},Ft=e=>{var t;let{printJob:i,handleSave:r,handleDelete:s,handleDownload:o,handleNameChange:a}=e;const[d,c]=(0,n.useState)(null),[u,h]=(0,W.A)((()=>r(i.id))),[x,p]=(0,W.A)((()=>s(i.id))),[g,m]=(0,W.A)((()=>o(i.id)));return(0,ie.jsxs)(ut.A,{variant:"outlined",sx:{padding:"10px 8px 2px 16px",borderColor:i.new?_.Qs.Violet[500]:_.Qs.Neutrals[400]},children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",gap:1,children:[(0,ie.jsx)(Ot,{label:i.fileInfo.name,onChange:e=>a(i.id,e),fullWidth:!0}),(0,ie.jsx)(l.A,{onMouseEnter:e=>c(e.currentTarget),onMouseLeave:()=>c(null),display:"flex",alignItems:"center",justifyContent:"center",marginRight:1,children:(0,ie.jsx)(M.gF,{icon:"InfoSolid",size:"sm",color:_.Qs.Neutrals[500]})}),(0,ie.jsx)(Oe.lR,{sx:{pointerEvents:"none",marginLeft:"4px"},open:Boolean(d),anchorEl:d,onClose:()=>c(null),anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"left"},children:(0,ie.jsxs)(Tt.A,{component:l.A,p:2,paddingTop:1,border:"1px solid ".concat(_.Qs.Neutrals[400]),borderRadius:"4px",display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsx)(Pt.A,{padding:"none",children:(0,ie.jsxs)(kt.A,{children:[(0,ie.jsxs)(Rt.A,{children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingRight:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,children:"File:"})}),(0,ie.jsx)(zt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:(e=>{switch(e){case"application/pdf":return"PDF";case"image/png":return"Image";case"application/zip":return"ZIP";case"application/ppt":return"PPT"}})(i.fileInfo.type)})})]}),(0,ie.jsxs)(Rt.A,{children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingRight:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,children:"Pages:"})}),(0,ie.jsx)(zt.A,{align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:i.jobDetails.pageCount.toString()})})]}),(0,ie.jsxs)(Rt.A,{children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingRight:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,children:"Break by:"})}),(0,ie.jsx)(zt.A,{align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:(0,Re.A)(i.jobDetails.multiPageBreakBy)})})]})]})}),(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:(null===(t=i.jobDetails.user)||void 0===t?void 0:t.username)||(i.fileInfo.isPublic?"Public Print":"")})]})})]}),(0,ie.jsx)(O.A,{variant:H.Eq.caption,color:_.Qs.Neutrals[600],children:"".concat(new Date(i.jobDetails.enqueued_at).toLocaleTimeString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"}))}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",marginTop:1.5,children:[i.allowDownload&&!i.fileSaved&&i.fileExpiry&&(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(M.gF,{icon:"ExclamationCircle",size:"sm",color:_.Qs.Warning[500]}),(0,ie.jsx)(O.A,{variant:H.Eq.caption,color:_.Qs.Warning[500],children:"Expires in ".concat(i.fileExpiry)})]}),i.fileSaved&&(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(M.gF,{icon:"CheckCircleSolid",size:"sm",color:_.Qs.Success[500]}),(0,ie.jsx)(O.A,{variant:H.Eq.caption,color:_.Qs.Neutrals[700],children:"Saved to Organimi Drive"})]}),(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",children:[!i.fileSaved&&(0,ie.jsx)(F.A,{onClick:h,disabled:!!u||!i.allowDownload,sx:{color:_.Qs.Neutrals[900]},children:(0,ie.jsx)(M.gF,{icon:"Save",size:"sm"})}),(0,ie.jsx)(F.A,{onClick:p,disabled:!!x||!!g,sx:{color:_.Qs.Neutrals[900]},children:(0,ie.jsx)(M.gF,{icon:"Delete",size:"sm"})}),(0,ie.jsx)(F.A,{onClick:m,disabled:!i.allowDownload,sx:{color:_.Qs.Neutrals[900]},children:(0,ie.jsx)(M.gF,{icon:"DownloadOutline",size:"sm"})})]})]})]})};var Qt=i(21773),Mt=i(82072);const Ht=e=>{let{printJob:t}=e;const i=(0,u.wA)(),{failed:n}=(0,u.d4)(Fe.W8);return(0,ie.jsxs)(ut.A,{variant:"outlined",sx:{padding:"10px 8px 10px 16px",borderColor:_.Qs.Error[500]},children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",gap:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,textOverflow:"ellipsis",noWrap:!0,children:"Print failed"}),(0,ie.jsx)(F.A,{onClick:()=>i((0,Fe.b0)(n.filter((e=>e.id!==t.id)))),sx:{color:_.Qs.Neutrals[500]},children:(0,ie.jsx)(M.gF,{icon:"Close",size:"xs"})})]}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.caption,color:_.Qs.Neutrals[600],children:"".concat(new Date(t.enqueued).toLocaleTimeString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"}))}),(0,ie.jsx)(O.A,{variant:H.Eq.caption,textOverflow:"ellipsis",noWrap:!0,children:"Job ID: ".concat(t.id)}),(0,ie.jsx)(O.A,{variant:H.Eq.subheadingXS,color:_.Qs.Neutrals[600],mb:.5,children:"Error: ".concat(t.error)})]})]})},qt=()=>{const{orgId:e,chartId:t}=(0,d.useParams)(),i=(0,u.wA)(),{show:r}=(0,We.A)(),{confirmAction:s}=(0,Ae.A)(),{downloadFile:o}=(0,Qt.A)(),[a,c]=(0,n.useState)(!1),[h,x]=(0,n.useState)(!1),{completed:g,inProgress:m,failed:v}=(0,u.d4)(Fe.W8),j=async()=>{c(!0),await i(Fe.kv.getPrintHistory({orgId:e,chartId:t})),c(!1)},A=async()=>{x(!0),await i(Fe.kv.getInProgressPrintJobs({orgId:e,chartId:t})),x(!1)};(0,n.useEffect)((()=>{j(),A()}),[]);const f=async n=>{const{error:l}=await i(Fe.kv.updateSaveStatusOfFileInPrintHistory({orgId:e,chartId:t,historyId:n,saveFile:!0}));l?r("Failed to save file","error",2e3):r("File saved successfully","success",2e3)},b=async n=>{s({title:"Delete File",message:"Are you sure you want to delete this file?",execFunc:async()=>{const{error:l}=await i(Fe.kv.deletePrintHistory({orgId:e,chartId:t,historyId:n}));l?r("Failed to delete file","error",2e3):r("File deleted successfully","success",2e3)},cancelFunc:()=>{}})},y=async i=>{const n="/organizations/".concat(e,"/charts/").concat(t,"/exports/history/").concat(i,"/download");await o({url:n,fileName:void 0,fileType:void 0,onError:void 0,isPublicDownload:void 0})},C=async(n,l)=>{const{error:s}=await i(Fe.kv.updateFileNameInPrintHistory({orgId:e,chartId:t,historyId:n,fileName:l}));s?r("Failed to update file name","error",2e3):r("File name updated successfully","success",2e3)},I=async n=>{s({title:"Cancel Print",message:"Are you sure you want to cancel this print?",execFunc:async()=>{const{error:l}=await i(Fe.kv.cancelPrintJob({orgId:e,chartId:t,jobId:n}));l?r("Failed to cancel print job","error"):(r("Print job cancelled","success"),j())},cancelButtonText:"No",confirmButtonText:"Yes",cancelFunc:()=>{}})},w=async e=>{await j(),console.log("Job completed",e),i((0,Fe.io)({fileCode:e})),await A()},N=(0,n.useMemo)((()=>[...g.map((e=>(0,ie.jsx)(Ft,{printJob:e,handleSave:f,handleDelete:b,handleDownload:y,handleNameChange:C},e.id))),...v.map((e=>(0,ie.jsx)(Ht,{printJob:e},e.id)))].sort(((e,t)=>{const i=new Date(e.props.printJob.enqueued||e.props.printJob.jobDetails.enqueued_at);return new Date(t.props.printJob.enqueued||t.props.printJob.jobDetails.enqueued_at).getTime()-i.getTime()}))),[g,v]);return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsx)(p.A,{loading:h,children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:2,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,color:_.Qs.Neutrals[600],children:"In Progress"}),m.map(((e,t)=>(0,ie.jsx)(Mt.T,{index:m.length-1-t,printJob:e,handleCancel:I,handleCompleted:w},t)))]})})}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsx)(p.A,{loading:a,children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:2,children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingLG,color:_.Qs.Neutrals[600],children:"Completed"}),N]})})})]})},_t=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Fe.db),{header:i,footer:n}=(0,u.d4)(Fe.pl),r=t=>{e((0,Fe.l8)(t))};return(0,ie.jsx)(ie.Fragment,{children:(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",p:2.5,borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",children:[(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingLG,sx:{display:"flex",gap:1,alignItems:"center"},children:["Include cover page",(0,ie.jsx)(X.A,{title:He.z7.coverPage,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(nt.A,{checked:t.visible,onChange:(e,i)=>r({...t,visible:i})})]}),t.visible&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(Oe.H_,{placeholder:"Title text",value:t.title,onChange:e=>r({...t,title:e.target.value}),label:"Title",required:!0}),(0,ie.jsx)(Oe.H_,{placeholder:"Subtitle text",value:t.subtitle,onChange:e=>r({...t,subtitle:e.target.value}),label:"Subtitle",required:!0}),(0,ie.jsxs)(l.A,{display:"grid",gridTemplateColumns:"1fr 1fr",gap:3,children:[(0,ie.jsx)(Oe.H_,{select:!0,value:t.size,onChange:e=>r({...t,size:e.target.value}),label:"Size",required:!0,children:Object.values(He.or).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsx)(Oe.H_,{select:!0,value:t.position,onChange:e=>r({...t,position:e.target.value}),label:"Position",required:!0,children:Object.values(He.yX).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsx)(Oe.H_,{select:!0,value:t.fontWeight,onChange:e=>r({...t,fontWeight:e.target.value}),label:"Font weight",required:!0,children:Object.values(He.IT).map((e=>(0,ie.jsx)(ke.A,{value:e,children:(0,Re.A)(e)},e)))}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:"3px",justifyContent:"space-between",children:[(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800],children:"Text color"}),(0,ie.jsx)(K.A,{initialColor:t.textColor,onChange:e=>r({...t,textColor:e})})]})]}),(0,ie.jsxs)(tt.A,{component:l.A,display:"flex",flexDirection:"column",gap:2,children:[(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:t.date.visible,onChange:(e,i)=>r({...t,date:{...t.date,visible:i}}),size:"small",sx:{paddingY:0}}),label:"Include date",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}}),t.date.visible&&(0,ie.jsx)(Oe.H_,{select:!0,label:"Date format",value:t.date.format,onChange:e=>r({...t,date:{...t.date,format:e.target.value}}),required:!0,children:Object.keys(He.Bd).map((e=>(0,ie.jsx)(ke.A,{value:e,children:e},e)))}),(i.visible||n.visible)&&(0,ie.jsx)(it.A,{control:(0,ie.jsx)(It.A,{checked:t.showHeaderFooter,onChange:(e,i)=>r({...t,showHeaderFooter:i}),size:"small",sx:{paddingY:0}}),label:"Show header and footer on cover page",slotProps:{typography:{variant:H.Eq.labelLG,color:_.Qs.Neutrals[800]}}})]})]})]})})})},Wt=()=>{const{completed:e,inProgress:t,failed:i}=(0,u.d4)(Fe.W8),r=[{name:"Page Setup",component:(0,ie.jsx)(Ct,{}),el:(0,n.useRef)(null)},{name:"Header & Footer",component:(0,ie.jsx)(Dt,{}),el:(0,n.useRef)(null)},{name:"Cover Page",component:(0,ie.jsx)(_t,{}),el:(0,n.useRef)(null)},{name:"Chart Elements",component:(0,ie.jsx)(Lt,{}),el:(0,n.useRef)(null)},{name:"Print History",component:(0,ie.jsx)(qt,{}),el:(0,n.useRef)(null)}],[s,o]=(0,n.useState)("Page Setup"),a=e=>(t,i)=>{o(i?e:null)};return(0,n.useEffect)((()=>{(t.length>0||e.find((e=>e.new))||i.length>0)&&o("Print History")}),[e,t,i]),(0,n.useEffect)((()=>{setTimeout((()=>{var e,t;null===(e=r.find((e=>s===e.name)))||void 0===e||null===(t=e.el.current)||void 0===t||t.scrollIntoView({behavior:"smooth"})}),500)}),[s]),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",minWidth:He.iP,width:He.iP,height:"100%",borderRight:"1px solid ".concat(_.Qs.Neutrals[400]),overflow:"auto",sx:{scrollbarWidth:"thin"},children:r.map((e=>(0,ie.jsxs)(Ze.A,{square:!0,disableGutters:!0,expanded:s===e.name,onChange:a(e.name),ref:e.el,children:[(0,ie.jsx)(Oe.Ql,{expandIcon:(0,ie.jsx)(M.gF,{icon:"ExpandDown",size:"lg",color:_.Qs.Neutrals[900]}),children:(0,ie.jsx)(O.A,{variant:H.Eq.h3,children:e.name})}),(0,ie.jsx)(Oe.uV,{children:e.component})]},e.name)))})};var Bt=i(31802);const Gt=()=>{const e=(0,u.wA)(),[t,i]=(0,n.useState)(null),{breakBy:r,pageBreaks:s}=(0,u.d4)(Ge.aC),o=(0,u.d4)(Fe.eI),a=(0,u.d4)(gt.A),d=(0,u.d4)(Ge.Xp),c=(0,u.d4)(mt.xt),[h,x]=(0,n.useState)(null),[p,g]=(0,n.useState)(null),[m,v]=(0,n.useState)(null),{changePageBreakName:A}=(0,bt.A)(),{setPageNumber:f}=(0,Be.A)(),b=(0,u.d4)(Fe.db),y=(0,u.d4)(Fe.DD),{listOfPages:C,tableOfContentPages:I}=(0,u.d4)(Ye),w=(0,n.useMemo)((()=>{var e,t;let i=0;return b.visible&&i++,null!==d&&void 0!==d&&null!==(e=d.tableOfContents)&&void 0!==e&&e.visible&&(i+=(null===d||void 0===d||null===(t=d.tableOfContents)||void 0===t?void 0:t.totalToCPages)||0),i+1}),[d,b]);return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)(U.A,{variant:Boolean(t)?"contained":"text",color:"primary",fullWidth:!0,sx:{height:"100%",borderRadius:0},onClick:e=>i(e.currentTarget),children:[(0,ie.jsx)(M.gF,{icon:"ListView",size:"lg"}),(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,minWidth:"fit-content",children:"Table of Contents"})]}),(0,ie.jsx)(Oe.lR,{open:Boolean(t),anchorEl:t,onClose:()=>{i(null),x(null),v(null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},children:(0,ie.jsxs)(Tt.A,{component:l.A,p:1,border:"1px solid ".concat(_.Qs.Violet[500]),borderRadius:"4px",display:"flex",flexDirection:"column",gap:3,children:[(0,ie.jsx)(Pt.A,{padding:"none",sx:{flex:1},children:(0,ie.jsxs)(kt.A,{children:[b.visible&&(0,ie.jsxs)(Rt.A,{sx:{height:"32px"},children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1,paddingRight:1.5},children:(0,ie.jsx)(M.gF,{icon:"Circle",size:"sm",color:_.Qs.Violet[500]})}),(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(Ot,{label:"Cover Page",onChange:()=>{},controlled:{edit:!1,setEdit:()=>{},onClickLabel:()=>f(1)}})}),(0,ie.jsx)(zt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,padding:1,children:"1"})})]}),o.visible&&I.map(((e,t)=>(0,ie.jsxs)(Rt.A,{hover:!0,sx:{height:"32px"},children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1,paddingRight:1.5},children:(0,ie.jsx)(M.gF,{icon:"Circle",size:"sm",color:_.Qs.Violet[500]})}),(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(Ot,{label:"Table of Contents ".concat(t>0?"cont'd...":""),onChange:()=>{},controlled:{edit:p===t,setEdit:e=>g(e?t:null),onClickLabel:()=>f(e.pageNumber)}})}),(0,ie.jsx)(zt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,padding:1,children:e.pageNumber})})]},e.pageNumber))),y.visible&&y.ownPage&&(0,ie.jsxs)(Rt.A,{sx:{height:"32px"},children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1,paddingRight:1.5},children:(0,ie.jsx)(M.gF,{icon:"Circle",size:"sm",color:_.Qs.Violet[500]})}),(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(Ot,{label:"Legend",onChange:()=>{},controlled:{edit:!1,setEdit:()=>{},onClickLabel:()=>f(w)}})}),(0,ie.jsx)(zt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1},children:(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,padding:1,children:w})})]}),C.map(((e,t)=>{var i,n,l,u;const p=d[e],g=(null===(i=d[e])||void 0===i?void 0:i.prevTopId)===j.Uz,b=a[e],y=c[null===(n=a[e])||void 0===n||null===(l=n.members)||void 0===l?void 0:l[0]],{photo:C}=(0,lt.LS)(y||{});return(0,ie.jsxs)(Rt.A,{onMouseEnter:()=>{x(t)},onMouseLeave:()=>x(null),hover:!0,sx:{height:"32px"},children:[(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1,paddingRight:1.5},children:(0,ie.jsx)(at.A,{width:16,height:16,name:(0,lt.Bx)(y)||(0,lt.mA)(b),src:C,avatarId:void 0,variant:void 0,shape:void 0,children:void 0,fontSize:.5})}),(0,ie.jsx)(zt.A,{sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200]},children:(0,ie.jsx)(Ot,{label:(g?o.rootLabel:null===(u=s.find((t=>t.id===e)))||void 0===u?void 0:u.name)||(0,lt.mA)(a[e]),onChange:t=>((e,t)=>{A(e,t)})(e,t),controlled:{edit:m===t,setEdit:e=>v(e?t:null),onClickLabel:()=>f(p.pageNumber)}})}),(0,ie.jsx)(zt.A,{padding:"none",align:"right",sx:{paddingY:.5,borderColor:_.Qs.Neutrals[200],paddingX:1},children:h!==t||m===t||r!==He.N0.MANUAL&&0!==t?(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,padding:1,children:p.pageNumber}):(0,ie.jsx)(F.A,{size:"small",onClick:()=>{v(t)},children:(0,ie.jsx)(M.gF,{icon:"Edit",size:"xs"})})})]},"".concat(p.pageNumber,"-").concat(e))}))]})}),(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",width:"100%",alignItems:"center",gap:3,px:1,pb:1,children:[(0,ie.jsxs)(O.A,{variant:H.Eq.subheadingLG,sx:{display:"flex",gap:1,alignItems:"center"},children:["Show in print",(0,ie.jsx)(X.A,{title:He.z7.tocShowInPrint,children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(nt.A,{checked:o.visible,onChange:(t,i)=>{e((0,Fe.o4)({...o,visible:i}))}})]})]})})]})};var Vt=i(22440);const Xt=e=>{let{containerRef:t}=e;const i=(0,u.wA)(),r=(0,u.d4)(Fe.kT),s=(0,u.d4)(L.G0),o=(0,u.d4)(Fe.ez),a=(0,u.d4)(Ge.aC),[d]=(0,Vt.A)(),c=d<1300,{pagination:h,pageFormat:{size:x,orientation:p},breakBy:g,bestFitScale:m}=a,v=(0,u.d4)(Fe.kU),A=p===He.t4.PORTRAIT?He.q2[x].width:He.q2[x].height,f=p===He.t4.PORTRAIT?He.q2[x].height:He.q2[x].width,b=He.ek[x],{pageCount:y}=(0,Be.A)(),{nextPage:C,prevPage:I}=(0,Be.A)(),w=(0,u.d4)((e=>{var t;return(null===(t=e.print)||void 0===t?void 0:t.fitScale)||1})),N=()=>{if(!t.current)return;if("auto"===x)return void i((0,Fe.Qp)(w));const{width:e,height:n}=t.current.getBoundingClientRect(),l=e/(A*b),r=n/(f*b);i((0,Fe.Qp)(Math.min(l,r)))};(0,n.useEffect)((()=>{N()}),[x,p]);const[E,S]=(0,n.useState)(m),{eventDebounce:D}=(0,he.A)(),T=e=>{S(e),D((()=>{i((0,Fe.sM)(void 0)),i((0,Fe.L$)({...a,bestFitScale:e}))}),500)()};(0,n.useEffect)((()=>{S(m)}),[m]);const P=[He.Xp.x25,He.Xp.x50,He.Xp.x75,He.Xp.x100,He.Xp.x125,He.Xp.x150,He.Xp.x200,He.Xp.x300,He.Xp.x500],k=h===He.ti.MULTIPLE&&y>1&&r===He._6.CHART&&s===j.XD.TRADITIONAL;return(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid ".concat(_.Qs.Neutrals[400]),height:He.nH,zIndex:3,bgcolor:_.Qs.Neutrals[0],children:[(0,ie.jsxs)(l.A,{display:"flex",height:"100%",alignItems:"center",width:"fit-content",children:[(k||g===He.N0.BESTFIT)&&(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(Gt,{}),(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}})]}),(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,height:"100%",p:2,minWidth:"fit-content",children:[(0,ie.jsx)(F.A,{size:"small",onClick:I,disabled:1===o,color:"primary",children:(0,ie.jsx)(M.gF,{icon:"ArrowLeft",size:"sm"})}),(0,ie.jsx)(O.A,{variant:H.Eq.bodySM,children:"Page ".concat(o," of ").concat(y)}),(0,ie.jsx)(F.A,{size:"small",onClick:C,disabled:o===y,color:"primary",children:(0,ie.jsx)(M.gF,{icon:"ArrowRight",size:"sm"})})]})]}),g===He.N0.BESTFIT&&(0,ie.jsxs)(l.A,{display:"flex",height:"100%",alignItems:"center",gap:3,mr:1,children:[(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}}),(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",gap:4,height:"100%",children:[(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,children:[(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,minWidth:"max-content",children:"Best fit scale:"}),(0,ie.jsx)(X.A,{title:"Scale used to determine minimum scale before automatically breaking the chart into multiple pages.",children:(0,ie.jsx)("span",{children:(0,ie.jsx)(M.gF,{icon:"HelpSolid",size:"sm",color:_.Qs.Neutrals[400]})})})]}),c&&(0,ie.jsx)(Oe.H_,{select:!0,value:E,onChange:e=>T(Number(e.target.value)),SelectProps:{renderValue:()=>"".concat(Math.round(100*Number(v)),"%")},sx:{width:"fit-content"},children:P.map((e=>(0,ie.jsxs)(ke.A,{value:e,children:[Math.round(100*Number(e)),"%"]},e)))}),!c&&(0,ie.jsx)(Bt.Ay,{value:E,onChange:(e,t)=>T(t),min:.1,max:1,step:.1,marks:[{value:.1,label:"10%"},{value:.2,label:"20%"},{value:.3,label:"30%"},{value:.4,label:"40%"},{value:.5,label:"50%"},{value:.6,label:"60%"},{value:.7,label:"70%"},{value:.8,label:"80%"},{value:.9,label:"90%"},{value:1,label:"100%"}],valueLabelDisplay:"auto",slotProps:{markLabel:{style:{...H.uT.caption}}},sx:{minWidth:300}})]}),(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}})]}),(0,ie.jsxs)(l.A,{display:"flex",height:"100%",alignItems:"center",width:"fit-content",children:[(0,ie.jsxs)(l.A,{display:"flex",gap:1,mr:1,alignItems:"center",minWidth:"fit-content",children:[(0,ie.jsx)(Oe.H_,{select:!0,value:v,onChange:e=>i((0,Fe.Qp)(Number(e.target.value))),SelectProps:{renderValue:()=>"".concat(Math.round(100*Number(v)),"%")},sx:{width:"fit-content"},children:P.map((e=>(0,ie.jsxs)(ke.A,{value:e,children:[Math.round(100*Number(e)),"%"]},e)))}),(0,ie.jsx)(F.A,{onClick:()=>{const e=Object.values(He.Xp).reduce(((e,t)=>Number(t)<v&&Number(t)>e?Number(t):e),0);e&&i((0,Fe.Qp)(e))},disabled:v===Number(He.Xp.x25),children:(0,ie.jsx)(M.gF,{icon:"ZoomOut",size:"sm"})}),(0,ie.jsx)(F.A,{onClick:()=>{const e=Object.values(He.Xp).reduce(((e,t)=>Number(t)>v&&Number(t)<e?Number(t):e),Number(He.Xp.x500));e&&i((0,Fe.Qp)(e))},disabled:v===Number(He.Xp.x500),children:(0,ie.jsx)(M.gF,{icon:"ZoomIn",size:"sm"})})]}),(0,ie.jsx)(ze.A,{orientation:"vertical",flexItem:!0,sx:{width:0}}),(0,ie.jsxs)(U.A,{variant:"text",color:"primary",fullWidth:!0,sx:{height:"100%",borderRadius:0},onClick:N,children:[(0,ie.jsx)(M.gF,{icon:"Fit",size:"lg"}),(0,ie.jsx)(O.A,{variant:H.Eq.labelLG,minWidth:"fit-content",children:"Fit"})]})]})]})};var Ut=i(25754),Yt=i(13228);const Jt=()=>{var e,t;const i=(0,u.d4)(Ge.Xp),n=(0,u.d4)(gt.A),r=(0,u.d4)(mt.xt),s=(0,u.d4)(Fe.ez),{pageBreaks:o}=(0,u.d4)(Ge.aC),a=(0,u.d4)(Fe.eI),d=(0,u.d4)(y.uo),c=(0,u.d4)(Qe.P0),{listOfPages:h}=(0,u.d4)(Ye),{setPageNumber:x}=(0,Be.A)(),p=(0,u.d4)(Fe.db),g=h.slice((s-1-(p.visible?1:0))*(null===i||void 0===i||null===(e=i.tableOfContents)||void 0===e?void 0:e.maxRowsPerPage),(s-(p.visible?1:0))*(null===i||void 0===i||null===(t=i.tableOfContents)||void 0===t?void 0:t.maxRowsPerPage));return(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",height:"100%",width:"100%",bgcolor:c.chartOptions.backgroundColor,color:(0,Yt.w5)(c.chartOptions.backgroundColor),padding:"10%",gap:4,children:[(0,ie.jsx)(O.A,{variant:H.Eq.h1,color:(0,Yt.w5)(c.chartOptions.backgroundColor),children:"Table of Contents"}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",children:g.map((e=>{var t,s;const u=n[e]||{},h=u.members||[],p=(null===(t=i[e])||void 0===t?void 0:t.prevTopId)===j.Uz;return(0,ie.jsxs)(l.A,{mb:1,display:"flex",alignItems:"center",style:{cursor:"pointer"},onClick:()=>{x(i[e].pageNumber)},component:d===j.uI.PRINT?"a":"div",href:d===j.uI.PRINT?"#page_".concat(i[e].pageNumber):void 0,color:(0,Yt.w5)(c.chartOptions.backgroundColor),children:[(0,ie.jsx)(l.A,{children:h.length?(0,ie.jsx)(Ut.A,{max:2,children:h.map((e=>{const t=r[e]||{};return(0,ie.jsx)(at.A,{src:(null===t||void 0===t?void 0:t.photo)||"",name:(0,lt.Bx)(t)||(0,lt.mA)(u),size:40,style:{backgroundColor:null===t||void 0===t?void 0:t.memberPhotoColor},avatarId:void 0,variant:void 0,shape:void 0,children:void 0},e)}))}):(0,ie.jsx)(at.A,{name:(0,lt.mA)(u),size:40,avatarId:void 0,variant:void 0,shape:void 0,children:void 0,src:""})}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",ml:1,children:(0,ie.jsx)(O.A,{variant:H.Eq.bodyMD,children:(p?a.rootLabel:null===(s=o.find((t=>t.id===e)))||void 0===s?void 0:s.name)||(0,lt.mA)(u)})}),(0,ie.jsx)(l.A,{ml:2,mr:1,flexGrow:1,height:"1px",border:"1px dashed lightgray",id:"page_".concat(i[e].pageNumber)}),(0,ie.jsx)(l.A,{ml:1,children:(0,ie.jsx)(O.A,{variant:H.Eq.bodyMD,children:i[e].pageNumber})})]},e)}))})]})};var Kt=i(3523);const $t=e=>{let{showCoverPage:t,showTableOfContents:i,showLegend:r}=e;const{chartId:s}=(0,d.useParams)(),{header:o}=(0,u.d4)(Fe.pl),{subtitle:a}=o,{name:c,logo:x}=(0,u.d4)(h.BF),p=(0,u.d4)(Fe.kT),g=(0,u.d4)(L.G0),m=(0,u.d4)(Ge.Xp),v=(0,u.d4)(Fe.ez),A=(0,u.d4)(Fe.X5),{pagination:f,pageBreaks:b,breakBy:y}=(0,u.d4)(Ge.aC),C=(0,u.d4)(Fe.eI),I=(0,u.d4)(gt.A),w=(0,n.useMemo)((()=>Object.keys(m).find((e=>m[e].pageNumber===v))||""),[v,m]),{changePageBreakName:N}=(0,bt.A)(),E=(0,n.useMemo)((()=>{var e;return(null===(e=m[w])||void 0===e?void 0:e.prevTopId)===j.Uz}),[w,m]),S=f===He.ti.MULTIPLE&&A>1&&p===He._6.CHART&&g===j.XD.TRADITIONAL,D=(0,n.useMemo)((()=>{var e;return a.useToC&&S?t?"Cover Page":i?"Table of Contents":r?"Legend":E?C.rootLabel||(0,lt.mA)(I[w]):(null===(e=b.find((e=>e.id===w)))||void 0===e?void 0:e.name)||(0,lt.mA)(I[w]):""}),[w,b,s,i,C,S,a,t,r,E,I]);return(0,ie.jsxs)(l.A,{display:"flex",justifyContent:o.position===He.yX.CENTER?"center":"space-between",flexDirection:o.position===He.yX.LEFT?"row":"row-reverse",textAlign:o.position,alignItems:"center",bgcolor:o.ignoreMargin?"transparent":_.Qs.Neutrals[200],px:3,py:1,gap:1,width:"100%",className:"printHeader",top:0,position:o.ignoreMargin?"absolute":"relative",zIndex:12,children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",minHeight:"20px",justifyContent:"center",children:[(0,ie.jsx)(O.A,{fontSize:(0,_e.fx)(o.size),fontWeight:(0,_e.sJ)(o.fontWeight),color:o.textColor,children:o.text}),a.visible&&(0,ie.jsx)(Ot,{fontSize:(0,_e.fx)(o.size)/1.5,color:o.textColor,label:D||a.text,onChange:e=>N(w,e),disabled:t||i||!D||y!==He.N0.MANUAL&&!E,noButtons:!0})]}),o.companyLogo&&x&&(0,ie.jsx)("img",{src:(0,Kt.K7)(x),alt:"".concat(c," logo"),style:o.ignoreMargin?{maxHeight:"48px",height:"100%"}:{height:0,minHeight:"100%"},width:"auto"})]})};var Zt=i(75995);const ei=()=>{const{footer:e}=(0,u.d4)(Fe.pl),{pageNumbers:t}=(0,u.d4)(Fe.IU),{pageCount:i}=(0,Be.A)(),r=(0,u.d4)(Fe.ez),s=(0,n.useMemo)((()=>(e.text||e.useTimestamp)&&(0,ie.jsx)(l.A,{minHeight:"14px",alignItems:"center",children:(0,ie.jsx)(O.A,{fontSize:(0,_e.fx)(e.size)/1.5,children:e.useTimestamp?(new Date).toLocaleString("sv",{timeZoneName:"short"}):e.text})})),[e.text,e.size,e.useTimestamp]),o=(0,n.useMemo)((()=>e.organimiLogo&&(0,ie.jsx)("img",{src:Zt.A,alt:"Organimi logo",style:{height:0,minHeight:"100%"},width:"auto"})),[e.organimiLogo]),a=(0,n.useMemo)((()=>(0,ie.jsx)(O.A,{fontSize:(0,_e.fx)(e.size)/1.5,visibility:t?"visible":"hidden",children:"Page ".concat(r," of ").concat(i)})),[t,e.size,r,i]),d=(0,n.useMemo)((()=>{switch(e.position){case He.yX.LEFT:return t?[s,o,a]:[s,"",o];case He.yX.CENTER:return[a,s,o];case He.yX.RIGHT:return t?[a,o,s]:[o,"",s]}}),[e.position,s,o,a]);return(0,ie.jsx)(ie.Fragment,{children:(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"space-between",textAlign:e.position,alignItems:"center",bgcolor:_.Qs.Neutrals[200],px:3,py:1,gap:1,width:"100%",className:"printFooter",position:"relative",children:[d[0]||null,(0,ie.jsx)(l.A,{position:"absolute",display:"flex",justifyContent:"center",width:"100%",left:0,height:"100%",py:"inherit",children:d[1]||null}),d[2]||null]})})},ti=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Fe.db);return(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",height:"100%",width:"100%",padding:"10%",gap:4,color:t.textColor,alignItems:t.position===He.yX.CENTER?"center":t.position===He.yX.LEFT?"flex-start":"flex-end",justifyContent:"center",textAlign:t.position,id:"page_1",children:[(0,ie.jsx)(Ot,{fontSize:3*(0,_e.fx)(t.size),fontWeight:(0,_e.sJ)(t.fontWeight),color:t.textColor,label:t.title,onChange:i=>e((0,Fe.l8)({...t,title:i})),noButtons:!0,wrap:!0}),(0,ie.jsx)(Ot,{fontSize:1.5*(0,_e.fx)(t.size),color:t.textColor,label:t.subtitle,onChange:i=>e((0,Fe.l8)({...t,subtitle:i})),noButtons:!0,wrap:!0}),t.date.visible&&(0,ie.jsx)(O.A,{fontSize:(0,_e.fx)(t.size),color:"".concat(t.textColor,"99"),children:He.Bd[t.date.format]})]})};var ii=i(70494),ni=i(80700);const li=()=>{var e,t;const{orgId:i}=(0,d.useParams)(),n=(0,u.d4)(Qe.P0),{data:r}=(0,ii.A)(),s=(null===n||void 0===n||null===(e=n.data)||void 0===e||null===(t=e.photoboard)||void 0===t?void 0:t.layout)||"layout-0";return(0,ie.jsx)(l.A,{position:"absolute",visibility:"hidden",id:"dummyPhotoboardItem",children:(0,ie.jsx)(ni._,{layout:s,theme:n,items:[r[0]],pureData:[],orgId:i,layoutClasses:""})})},ri=e=>{let{page:t}=e;return(0,ie.jsx)(l.A,{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",width:"100%",padding:"10%",id:"page_".concat(t),children:(0,ie.jsx)(P.N,{showLegendOnPublicView:!0})})};var si=i(53109);const oi=e=>{var t,i,r;let{children:s}=e;const{header:o,footer:a}=(0,u.d4)(Fe.pl),d=(0,u.d4)(Qe.P0),c=(0,u.d4)(Ge.Xp),{pageCount:h}=(0,Be.A)(),{printType:x}=(0,u.d4)(Fe.xy),p=(0,u.d4)(Fe.ez),{pageNumbers:g}=(0,u.d4)(Fe.IU),m=(0,u.d4)(Fe.db),v=(0,u.d4)(Fe.DD);(0,ii.A)();const j=a.visible&&(a.useTimestamp||a.text||a.organimiLogo),A=m.visible&&1===p,f=(0,n.useMemo)((()=>{if(v.visible&&v.ownPage){var e,t;let i=0;return m.visible&&i++,null!==c&&void 0!==c&&null!==(e=c.tableOfContents)&&void 0!==e&&e.visible&&(i+=null===c||void 0===c||null===(t=c.tableOfContents)||void 0===t?void 0:t.totalToCPages),p===i+1}return!1}),[v,m,p,c]),b=null!==c&&void 0!==c&&null!==(t=c.tableOfContents)&&void 0!==t&&t.visible&&m.visible?p>1&&p<=(null===c||void 0===c||null===(i=c.tableOfContents)||void 0===i?void 0:i.totalToCPages)+1:p<=(null===c||void 0===c||null===(r=c.tableOfContents)||void 0===r?void 0:r.totalToCPages)&&x===He._6.CHART,y=(0,n.useMemo)((()=>A?(0,ie.jsx)(ti,{}):b?(0,ie.jsx)(Jt,{}):f?(0,ie.jsx)(ri,{page:p}):s),[A,b,s,p,f]),C=(0,n.useMemo)((()=>x===He._6.CHART||A||b||f?d.chartOptions.backgroundColor:_.Qs.Neutrals[0]),[x,A,b,f,d]);return(0,ie.jsxs)(ie.Fragment,{children:[o.visible&&!(A&&!m.showHeaderFooter)&&(0,ie.jsx)($t,{showTableOfContents:b,showCoverPage:A,showLegend:f}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",flex:1,position:"relative",overflow:"hidden",bgcolor:C,width:"100%",height:"100%",children:[y,g&&!j&&(0,ie.jsx)(O.A,{fontSize:"8px",position:"absolute",bottom:16,left:24,color:(0,Yt.w5)(C),children:"Page ".concat(p," of ").concat(h)})]}),j&&!(A&&!m.showHeaderFooter)&&(0,ie.jsx)(ei,{}),x===He._6.PHOTOBOARD&&(0,ie.jsx)(li,{})]})},ai=e=>{let{children:t}=e;const i=(0,u.wA)(),r=(0,u.d4)(y.uo),{pageFormat:{size:s,orientation:o}}=(0,u.d4)(Ge.aC),a=(0,u.d4)(Fe.kU),{eventDebounce:d}=(0,he.A)(),c=(0,n.useRef)(null),h=o===He.t4.PORTRAIT?He.q2[s].width:He.q2[s].height,x=o===He.t4.PORTRAIT?He.q2[s].height:He.q2[s].width,p=He.ek[s],g=d((()=>{const e=null===c||void 0===c?void 0:c.current;!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const t=c.current;if(!t)return;const n=t.scrollWidth,l=t.offsetWidth/2,r=Math.ceil(n/l),s=e/n,o=Math.floor(s*r);i((0,si.yX)(o))}(null===e||void 0===e?void 0:e.scrollLeft)}),50);return(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",height:"100%",width:"100%",minWidth:0,children:[r===j.uI.PRINT_PREVIEW&&(0,ie.jsx)(Xt,{containerRef:c}),(0,ie.jsx)(l.A,{ref:c,overflow:"auto"===s?"hidden":"auto",onScroll:"auto"===s||r!=j.uI.PRINT_PREVIEW?void 0:g,height:"100%",alignContent:"center",justifyItems:(()=>{if(!c.current)return;const{width:e}=c.current.getBoundingClientRect();return e>h*p*a})()?"center":"flex-start",sx:{backgroundColor:_.Qs.Neutrals[100],backgroundImage:"linear-gradient(to right, ".concat(_.Qs.Neutrals[300],"80 1px, transparent 1px),\n                            linear-gradient(to bottom, ").concat(_.Qs.Neutrals[300],"80 1px, transparent 1px);"),backgroundSize:"16px 16px",backgroundPosition:"12px 12px"},children:"auto"!==s?(0,ie.jsx)(l.A,{width:h*p*a,height:x*p*a,boxShadow:8,children:(0,ie.jsx)(l.A,{width:h*p,height:x*p,sx:{scale:a.toString(),transformOrigin:"top left"},display:"flex",flexDirection:"column",children:(0,ie.jsx)(oi,{children:t})})}):(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",width:"100%",height:"100%",justifyContent:"center",alignItems:"center",flexGrow:1,position:"relative",children:(0,ie.jsx)(oi,{children:t})})})]})};var di=i(57546),ci=i(3643);var ui=i(65491);const hi=()=>{const e=(0,u.wA)(),t=(0,u.d4)(ui.A),i=(0,u.d4)(Ge.Vs),l=(0,n.useMemo)((()=>Object.keys(i).reduce(((e,t)=>{const{w1:n,w0:l,h1:r,h0:s}=i[t];return e[t]={cardWidth:n,recursiveWidth:l,cardHeight:r,recursiveHeight:s},e}),{})),[i]),{handleChangeDirectReportDirection:r}=(0,bt.A)(),s=(0,u.d4)(gt.A),{pageFormat:{size:o,orientation:a},bestFitScale:d}=(0,u.d4)(Ge.aC),{chart:{padding:c}}=j.Ay,h={width:(a===He.t4.PORTRAIT?He.q2[o].width*He.ek[o]:He.q2[o].height*He.ek[o])-c,height:(a===He.t4.PORTRAIT?He.q2[o].height*He.ek[o]:He.q2[o].width*He.ek[o])-c};return{recomputeBestFit:()=>{console.log("Recomputing Best Fit...");const{pageMap:i,directReportDirectionOverrides:n}=(e=>{var t,i,n;let{parentChildMap:l,pageSize:r,nodeDims:s,scale:o=1}=e;const{width:a,height:d}=r,c={};if(console.log(l,s),!s)throw new Error("Node dimensions not found");const u=null===(t=l.root)||void 0===t?void 0:t[0];if(!u)throw new Error("Root ID not found in parent-child map");const h=Object.entries(s).reduce(((e,t)=>{let[i,{cardWidth:n,recursiveWidth:l,cardHeight:r,recursiveHeight:s}]=t;return e[i]={cardWidth:n*o,recursiveWidth:l*o,cardHeight:r*o,recursiveHeight:s*o},e}),{});let x=1;const p=1.2*((null===(i=h[u])||void 0===i?void 0:i.cardWidth)||0),g=1.2*((null===(n=h[u])||void 0===n?void 0:n.cardHeight)||0);console.log("CARD_WIDTH",p,"CARD_HEIGHT",g,r);const m=p&&(Math.round(a/p)||1),v=g&&(Math.floor(d/g)||1)-2;console.log("MAX_COLUMNS",m,"MAX_ROWS",v);const j={},A=(e,t)=>{var i,n;const r=[...l[e]],s=t?null===(i=c[t].partitions.find((t=>t.ids.includes(e))))||void 0===i?void 0:i.pageNumber:void 0;if(c[e]={pageNumber:x,partitions:[],prevPageNumber:s},!r.length)return;if((null===(n=h[e])||void 0===n?void 0:n.recursiveWidth)<=a){if(r.every((e=>{var t;return!(null!==(t=l[e])&&void 0!==t&&t.length)}))){const t=Math.ceil(r.length/(m*v)),i=Math.ceil(r.length/t);for(let n=0;n<r.length;n+=i){const t=r.slice(n,n+i);c[e].partitions.push({pageNumber:x,ids:t}),console.log("5curIds",t,"pageNumber",x),x++}return void(j[e]={directReportDirection:"grid",directReportColumns:m,directReportRows:v})}c[e].partitions.push({pageNumber:x,ids:r}),console.log("0curIds",r,"pageNumber",x),x++;const t=r.filter((e=>{var t,i;return((null===(t=h[e])||void 0===t?void 0:t.recursiveHeight)||(null===(i=h[e])||void 0===i?void 0:i.cardHeight)||0)>d}));for(const i of t)A(i,e);return}const o=r.sort(((e,t)=>{var i,n,l,r;const s=(null===(i=h[e])||void 0===i?void 0:i.recursiveWidth)||(null===(n=h[e])||void 0===n?void 0:n.cardWidth)||0;return((null===(l=h[t])||void 0===l?void 0:l.recursiveWidth)||(null===(r=h[t])||void 0===r?void 0:r.cardWidth)||0)-s}));let u=[],p=0;const f=[];let b=0,y=!1;for(const A of o){var C,I,w,N,E;const t=(null===(C=h[A])||void 0===C?void 0:C.recursiveWidth)||(null===(I=h[A])||void 0===I?void 0:I.cardWidth)||0;0===u.length&&0===(null===(w=l[A])||void 0===w?void 0:w.length)&&(y=!0);const i=(null===(N=h[A])||void 0===N?void 0:N.recursiveHeight)||(null===(E=h[A])||void 0===E?void 0:E.cardHeight)||0;if(t>a||i+2*g>d)f.push(A);else if(p+t<=a||y&&u.length<m*(v-1)){p+=t,u.push(A);for(let t=b;t<f.length;t++){var S;const i=f[t],n=(null===(S=h[i])||void 0===S?void 0:S.cardWidth)||0;if(!(p+n<=a||y&&u.length<m*(v-1))){c[e].partitions.push({pageNumber:x,ids:u}),console.log("1curIds",u,"pageNumber",x),x++,b=t,u=[],p=0,y&&(j[e]={directReportDirection:"grid",directReportColumns:m,directReportRows:v-1});break}p+=n,u.push(i),b=t+1}}else{var D;u.length>0&&(c[e].partitions.push({pageNumber:x,ids:u}),console.log("2curIds",u,"pageNumber",x),x++,y&&(j[e]={directReportDirection:"grid",directReportColumns:m,directReportRows:v-1}),0===(null===(D=l[A])||void 0===D?void 0:D.length)&&(y=!0)),u=[A],p=t}}for(let l=b;l<f.length;l++){var L;const t=f[l],i=(null===(L=h[t])||void 0===L?void 0:L.cardWidth)||0;p+i<=a||y?(p+=i,u.push(t)):(u.length>0&&(c[e].partitions.push({pageNumber:x,ids:u}),console.log("3curIds",u,"pageNumber",x),x++,y=!0),u=[t],p=i)}u.length>0&&(c[e].partitions.push({pageNumber:x,ids:u}),console.log("4curIds",u,"pageNumber",x),x++);for(const l of f)A(l,e)};return A(u),{pageMap:c,directReportDirectionOverrides:j}})({parentChildMap:t,pageSize:h,nodeDims:l,scale:d});e((0,Fe.sM)(i)),console.log("Best Fit:",i),n&&Object.keys(n).forEach((e=>{const t=s[e];r(t)(n[e])})),console.log("Direct Report Direction Overrides:",n)}}},xi=e=>{let{children:t}=e;const i=(0,u.wA)(),{orgId:r,chartId:s}=(0,d.useParams)(),{pageCount:o}=(0,Be.A)(),a=(0,u.d4)(Fe.ez),{setPageNumber:c,nextPage:h}=(0,Be.A)(),{firstPageNumber:x,jobId:g}=(0,di.A)(),[m,v]=(0,n.useState)(!1),A=(0,u.d4)(y.uo),f=(0,ci.A)(),{updatePageReady:b,resetPageReady:C}=(0,Ke.A)(),{breakBy:I,outputType:w,pageFormat:N,bestFitScale:E}=(0,u.d4)(Ge.aC),S=(0,u.d4)(Fe.mU),{recomputeBestFit:D}=hi(),{directReportDirectionOverrides:L}=(0,u.d4)(Fe.IU),{topRole:T}=(0,di.A)(),P=(0,u.d4)((e=>(0,Ge.is)(e,T))),k=(null===P||void 0===P?void 0:P.members)&&(null===P||void 0===P?void 0:P.members[0]),R=(0,u.d4)((e=>(0,mt.Ar)(e.people,k)));(0,n.useEffect)((()=>{g||(i((0,Fe.TK)()),(async()=>{v(!0),await i(Fe.kv.getPrintTemplates({orgId:r})),v(!1),P&&await i((0,Fe.dY)({id:P.id,name:(0,lt.mA)(P),personName:(null===R||void 0===R?void 0:R.name)||""}))})())}),[]),(0,n.useEffect)((()=>{i((0,Fe.T0)(o)),a>o&&c(o)}),[o]);const z=(0,ie.jsx)("button",{id:"nextPagePrintButton",onClick:h,style:{zIndex:9999,position:"absolute",display:"none"},children:"next page"});return(0,n.useEffect)((()=>{g&&async function(){C();const{error:e,payload:t}=await i(f?Fe.R0.getPrintJobSettings({jobId:g}):Fe.kv.getPrintJobSettings({orgId:r,chartId:s,jobId:g}));e||t.params.setup.page.multiOptions.breakBy===He.N0.BESTFIT||(x&&c(Number(x)),b())}()}),[g]),(0,n.useEffect)((()=>{I!==He.N0.BESTFIT||w===He.yQ.SCREEN||S||(C(),i((0,Fe.OW)({chartId:s})),null!==L&&void 0!==L&&L[s]||(D(),x&&c(Number(x)),b()))}),[I,w,N,S,L,E]),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",height:"100%",width:"100%",children:[A===j.uI.PRINT_PREVIEW&&(0,ie.jsx)(Ve,{}),(0,ie.jsxs)(l.A,{display:"flex",flexGrow:1,overflow:"hidden",width:"100%",children:[A===j.uI.PRINT_PREVIEW&&(0,ie.jsx)(Wt,{}),z,(0,ie.jsx)(ai,{children:(0,ie.jsx)(p.A,{loading:m,children:t})})]}),A===j.uI.PRINT_PREVIEW&&(0,ie.jsx)($e,{})]})};var pi=i(8868),gi=i(6463),mi=i(69869),vi=i(24056),ji=i(84882),Ai=i(37091),fi=i(10268);const bi=e=>{let{handleClose:t,open:i}=e;const r=(0,u.d4)(gi.$r),[s,o]=(0,n.useState)(Ai.hC.LINK),a=r(s),{handleApproveSuggestion:d,suggestionIdsPending:c,handleChangeSuggestionManager:h}=(0,fi.n)(),x=e=>{const t=c.includes(e.id);switch(e.status){case Ai.TK.PENDING:return(0,ie.jsxs)(l.A,{display:"flex",gap:1,children:[(0,ie.jsx)(U.A,{disabled:t,variant:"outlined",size:"small",color:"black",onClick:()=>{d(e)},children:"Approve"}),(0,ie.jsx)(U.A,{disabled:!!t,variant:"outlined",size:"small",color:"black",onClick:()=>{h(e)},children:"Reject"})]});case Ai.TK.APPROVED:return(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(M.me,{name:"check",color:_.Qs.Success[500],variant:"light",fontSize:12}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[600],children:"Approved"})]});case Ai.TK.APPLIED:return(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(M.me,{name:"check",color:_.Qs.Success[500],variant:"light",fontSize:12}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[600],children:"Applied"})]});case Ai.TK.REJECTED:return(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(M.me,{name:"x",color:_.Qs.Error[800],variant:"light",fontSize:12}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[600],children:"Rejected"})]});default:return null}};return(0,ie.jsxs)(R.A,{open:i,onClose:t,maxWidth:"md",fullWidth:!0,children:[(0,ie.jsxs)(z.A,{children:[(0,ie.jsx)(O.A,{color:_.Qs.Neutrals[0],children:"Verification Log"}),(0,ie.jsx)(F.A,{"aria-label":"close",onClick:t,children:(0,ie.jsx)(M.me,{name:"remove",variant:"light",fontSize:12,color:"white"})})]}),(0,ie.jsx)(Q.A,{children:(0,ie.jsxs)(l.A,{pt:3,pb:1,sx:{minHeight:500},children:[(0,ie.jsxs)(mi.A,{onChange:(e,t)=>o(t),value:s,sx:{pb:3},children:[(0,ie.jsx)(vi.A,{color:"primary",label:"Relationships",value:Ai.hC.LINK}),(0,ie.jsx)(vi.A,{color:"primary",label:"Roles",value:Ai.hC.ROLE})]}),(0,ie.jsx)(l.A,{maxHeight:500,overflow:"auto",children:(0,ie.jsxs)(Pt.A,{size:"small",stickyHeader:!0,children:[(0,ie.jsx)(ji.A,{children:(0,ie.jsxs)(Rt.A,{children:[(0,ie.jsx)(zt.A,{children:"Role name"}),(0,ie.jsx)(zt.A,{children:"Reports to"}),(0,ie.jsx)(zt.A,{children:"Status / Action"})]})}),(0,ie.jsx)(kt.A,{children:a.map((e=>(0,ie.jsxs)(Rt.A,{children:[(0,ie.jsx)(zt.A,{children:e.roleTitle}),(0,ie.jsx)(zt.A,{children:e.parentTitle}),(0,ie.jsx)(zt.A,{children:(0,ie.jsx)(p.A,{loading:c.includes(e.id),transparent:!0,children:x(e)})})]},"".concat(e.id,"-verify"))))})]})}),0===(a||[]).length&&(0,ie.jsx)(l.A,{p:2,children:(0,ie.jsx)(O.A,{variant:"bodyMD",color:_.Qs.Neutrals[600],fontWeight:300,children:"No suggested roles found for this chart"})})]})})]})};var yi=i(45956);i(59177);const Ci=e=>{let{color:t,children:i}=e;return(0,ie.jsx)(l.A,{borderRadius:8,bgcolor:t,minWidth:12,minHeight:20,px:1,textAlign:"center",children:i})},Ii=e=>{let{confidence:t,value:i}=e,n=_.Qs.Neutrals[200];switch(t){case yi.r_.HIGH:n=_.Qs.Success[500];break;case yi.r_.MEDIUM:n=_.Qs.Warning[500];break;case yi.r_.LOW:n=_.Qs.Error[500];break;default:n=_.Qs.Neutrals[200]}return(0,ie.jsx)(Ci,{color:n,children:i})},wi=e=>{let{label:t,description:i,color:n}=e;return(0,ie.jsxs)(l.A,{py:2,display:"flex",alignItems:"flex-start",gap:2,children:[(0,ie.jsx)(Ci,{color:n}),(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,ie.jsx)(O.A,{variant:"subheadingSM",color:_.Qs.Neutrals[900],children:t}),(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[800],children:i})]})]})},Ni=e=>{let{body:t,children:i,maxHeight:n}=e;const r={};return n&&(r.maxHeight=n,r.overflow="auto"),(0,ie.jsxs)(l.A,{children:[(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[800],children:t}),(0,ie.jsx)(l.A,{...r,children:i})]})},Ei=e=>{let{pillType:t,title:i,expanded:n,children:r,handleExpand:s}=e;return(0,ie.jsxs)(l.A,{display:"flex",justifyContent:"center",flexDirection:"column",children:[(0,ie.jsxs)(l.A,{onClick:()=>s(t),px:2,py:1,display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"solid 1px #e0e0e0",children:[(0,ie.jsx)(O.A,{variant:"h3",color:_.Qs.Neutrals[900],children:i}),(0,ie.jsx)(F.A,{sx:{transform:n?"rotate(180deg)":"rotate(0deg)"},children:(0,ie.jsx)(M.me,{name:"chevron-down",fontSize:12,variant:"solid",color:_.Qs.Neutrals[900]})})]}),n?(0,ie.jsx)(l.A,{p:2,bgcolor:_.Qs.Neutrals[200],children:r}):null]})};i(32042);var Si=i(23851),Di=i(43862);const Li={[yi.r_.HIGH]:"High-confidence",[yi.r_.MEDIUM]:"Medium-confidence",[yi.r_.LOW]:"Low-confidence"},Ti=e=>{let{confidenceRange:t,children:i,handleClick:n,open:r,count:s=0}=e;const o=Li[t];return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",alignItems:"flex-start",color:_.Qs.Neutrals[0],borderRadius:2,width:"100%",onClick:n,sx:{cursor:n?"pointer":"default",opacity:!1===r?.7:1},children:(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",p:2,pb:1,borderTop:"solid 1px #e0e0e0",children:[(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,children:[(0,ie.jsx)(Ii,{confidence:t,value:s}),(0,ie.jsx)(O.A,{variant:"h3",color:_.Qs.Neutrals[900],children:o})]}),(0,ie.jsx)(l.A,{children:(0,ie.jsx)(M.me,{name:r?"chevron-up":"chevron-down",color:_.Qs.Neutrals[500],fontSize:16,variant:"sharp"})},"pill-icon-".concat(o,"-").concat(r))]})}),i]})},Pi=e=>{let{visibleCount:t=0,suggestions:i=[],handleApproveSuggestion:r,suggestionIdsPending:s,handleApproveBulkSuggestions:o,handleChangeManager:a,handleApproveBranch:d}=e;const{show:c}=(0,We.A)(),h=(0,u.wA)(),[x,g]=(0,n.useState)([]),[m,v]=(0,n.useState)(null),[A,f]=(0,n.useState)(yi.r_.HIGH),b=(i||[]).reduce(((e,t)=>(t.confidence>yi.r_.HIGH?e[yi.r_.HIGH]=[...e[yi.r_.HIGH]||[],t]:t.confidence>yi.r_.MEDIUM?e[yi.r_.MEDIUM]=[...e[yi.r_.MEDIUM]||[],t]:e[yi.r_.LOW]=[...e[yi.r_.LOW]||[],t],e)),{[yi.r_.HIGH]:[],[yi.r_.MEDIUM]:[],[yi.r_.LOW]:[]}),y=i.reduce(((e,t)=>(t.status===Ai.TK.APPROVED?e[Ai.TK.APPROVED].push(t):t.status===Ai.TK.PENDING?e[Ai.TK.PENDING].push(t):t.status===Ai.TK.REJECTED&&e[Ai.TK.REJECTED].push(t),e)),{[Ai.TK.APPROVED]:[],[Ai.TK.PENDING]:[],[Ai.TK.REJECTED]:[]}),C=t-y[Ai.TK.PENDING].length;return(0,ie.jsxs)(l.A,{width:j.Ay.dialogs.aiInsightsWidth,borderRight:"1px solid ".concat(_.Qs.Neutrals[300]),borderLeft:"1px solid ".concat(_.Qs.Neutrals[300]),display:"flex",flexDirection:"column",overflow:"auto",position:"relative",flexShrink:0,zIndex:1,bgcolor:_.Qs.Neutrals[0],height:"100%",py:2,children:[(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:2,p:2,children:[(0,ie.jsx)(O.A,{variant:"h2",color:_.Qs.Violet[500],children:"Role review & approval"}),i.length>0&&(0,ie.jsxs)(l.A,{display:"flex",gap:2,alignItems:"center",children:[(0,ie.jsxs)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[800],fontWeight:300,children:[C," of ",t," roles approved"]}),(0,ie.jsx)(M.me,{name:"circle",variant:"solid",color:_.Qs.Neutrals[900],fontSize:4}),(0,ie.jsxs)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[800],fontWeight:300,children:[y[Ai.TK.PENDING].length," pending"]})]})]}),(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",overflow:"auto",height:"100%",flex:1,children:[yi.r_.HIGH,yi.r_.MEDIUM,yi.r_.LOW].map((e=>{const t=Li[e];return(0,ie.jsxs)(Ti,{confidenceRange:e,handleClick:()=>{f(A===e?null:e)},open:A===e,count:b[e].length,children:[0===b[e].length&&A===e?(0,ie.jsx)(l.A,{display:"flex",alignItems:"center",justifyContent:"center",color:_.Qs.Neutrals[500],p:2,children:(0,ie.jsxs)(O.A,{variant:"bodyMD",children:["No suggestions remaining with ",t.toLowerCase()," roles."]})}):null,(0,ie.jsx)(l.A,{mt:2,display:"flex",flexDirection:"column",gap:2,flexShrink:0,overflow:"auto",flex:A===e?1:void 0,children:A===e&&b[e].length>0&&(0,ie.jsx)(ot.A,{sx:{width:"100%"},children:b[e].map((e=>{var t;return(0,ie.jsx)(ht.Ay,{onMouseOver:()=>v(e.id),sx:{bgcolor:x.includes(e.id)?"rgba(197,253,246, 0.4)":e.id===m?_.Qs.Violet[0]:"inherit",borderBottom:"1px solid ".concat(_.Qs.Neutrals[300])},children:(0,ie.jsxs)(p.A,{loading:s.includes(e.id),transparent:!0,display:"flex",children:[(0,ie.jsx)(Si.A,{sx:{display:"flex",justifyContent:"flex-start",flexDirection:"column",minWidth:36,minHeight:50},children:(0,ie.jsx)(It.A,{checked:x.includes(e.id),color:"default",size:"medium",sx:{padding:"4px"},onChange:t=>{g((i=>t.target.checked?[...i,e.id]:i.filter((t=>t!==e.id))))}})}),(0,ie.jsx)(xt.A,{primary:(0,ie.jsx)(O.A,{variant:"bodyMD",color:_.Qs.Neutrals[900],component:"div",children:e.roleTitle}),secondary:(0,ie.jsx)(l.A,{my:1,children:(0,ie.jsxs)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[600],component:"div",children:["Reports to ",e.parentTitle]})})}),e.id===m&&(0,ie.jsxs)(l.A,{position:"absolute",top:0,right:0,display:"flex",gap:1,height:"100%",alignItems:"flex-start",children:[(0,ie.jsx)(X.A,{title:"Approve this suggestion",children:(0,ie.jsx)(U.A,{color:"success",size:"small",variant:"outlined",onClick:()=>{(async e=>{await r(e),g((t=>t.includes(e.id)?t.filter((t=>t!==e.id)):t))})(e)},sx:{minWidth:16,padding:0,display:"inline-block"},style:{padding:0},children:(0,ie.jsx)(M.me,{name:"check",variant:"solid",color:_.Qs.Success[500],fontSize:16,overrideStyles:{display:"flex",width:16}})})}),(0,ie.jsx)(X.A,{title:"Edit reporting",children:(0,ie.jsx)(U.A,{color:"warning",size:"small",variant:"outlined",sx:{minWidth:16,padding:0,display:"inline-block"},style:{padding:0},onClick:()=>a({suggestion:e}),children:(0,ie.jsx)(M.me,{name:"pencil",variant:"solid",fontSize:16,overrideStyles:{display:"flex",width:16}})})}),(0,ie.jsx)(X.A,{title:"Approve this entire branch",children:(0,ie.jsx)(U.A,{color:"primary",size:"small",variant:"outlined",onClick:()=>{d(e)},sx:{minWidth:16,padding:0,display:"inline-block"},style:{padding:0},children:(0,ie.jsx)(M.me,{name:"list-tree",variant:"solid",fontSize:16,overrideStyles:{display:"flex",width:16}})})}),(null===e||void 0===e||null===(t=e.existingRole)||void 0===t?void 0:t.id)&&(0,ie.jsx)(X.A,{title:"Find in chart",children:(0,ie.jsx)(U.A,{color:"secondaryGrey",size:"small",variant:"contained",onClick:()=>{var t,i;(i=null===(t=e.existingRole)||void 0===t?void 0:t.id)&&(console.log("Finding suggestion with ID: ".concat(i," in the chart")),h((0,Di.mO)({roleId:i})))},sx:{minWidth:16,padding:0,display:"inline-block"},style:{padding:0},children:(0,ie.jsx)(M.me,{name:"magnifying-glass",variant:"solid",fontSize:16,overrideStyles:{display:"flex",width:16}})})})]})]})},e.id)}))})})]},"pill-".concat(t))}))}),(null===x||void 0===x?void 0:x.length)>0&&(0,ie.jsxs)(l.A,{top:0,right:0,left:0,bgcolor:_.Qs.Success[600],p:2,zIndex:2,display:"flex",gap:1,alignItems:"center",justifyContent:"space-between",children:[(0,ie.jsxs)(l.A,{display:"flex",gap:1,alignItems:"center",children:[(0,ie.jsx)(Ci,{color:_.Qs.Neutrals[0],children:(0,ie.jsx)(O.A,{variant:"bodyMD",color:_.Qs.Success[700],children:x.length})}),(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[0],children:"roles selected"}),(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[0],component:"a",sx:{textDecoration:"underline"},onClick:()=>g([]),children:"Clear all"})]}),(0,ie.jsx)(U.A,{variant:"contained",color:"white",size:"small",onClick:async()=>{if(0!==x.length)try{await o(x),g([])}catch(e){console.error("Error approving bulk suggestions:",e),c("Something went wrong. Could not approve suggestions in bulk.","error")}},children:(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Success[800],children:"Approve selected"})})]}),0===(null===x||void 0===x?void 0:x.length)&&(0,ie.jsx)(l.A,{display:"flex",flexDirection:"column",justifyContent:"flex-end",gap:2,children:(0,ie.jsx)(l.A,{bgcolor:_.Qs.Neutrals[200],textAlign:"center",p:2,children:(0,ie.jsx)(O.A,{textAlign:"center",variant:"bodySM",color:_.Qs.Neutrals[800],children:"Select multiple roles to approve in bulk"})})})]})};var ki=i(94916);const Ri=e=>{let{loading:t,handleShowVerificationLog:i,handleToggleMode:r,handleDeleteAutobuildChart:s}=e;const[o,a]=(0,n.useState)(yi.pz.CONFIDENCE_LEVELS),[d]=(0,Vt.A)(),c=d<1441,u=e=>{a((t=>t===e?null:e))};return(0,ie.jsxs)(l.A,{width:j.Ay.dialogs.aiInsightsWidth,borderLeft:"1px solid ".concat(_.Qs.Neutrals[300]),display:"flex",flexDirection:"column",overflow:c?"hidden":"auto",position:"relative",flexShrink:0,zIndex:1,bgcolor:_.Qs.Neutrals[0],height:"100%",children:[(0,ie.jsx)(p.A,{loading:t,transparent:!0,height:"auto",children:(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(l.A,{pt:4,pb:1,px:2,children:(0,ie.jsx)(l.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:(0,ie.jsx)(O.A,{variant:"h2",color:_.Qs.Violet[500],children:"Fine-tune your chart"})})}),(0,ie.jsx)(l.A,{px:2,pt:2,pb:1,children:(0,ie.jsx)(O.A,{variant:"bodySM",color:_.Qs.Neutrals[800],children:"Evaluate your current chart setup to let Organimi AI generate a more accurate chart."})}),(0,ie.jsx)(l.A,{p:2,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:2,borderBottom:"solid 1px #e0e0e0",children:(0,ie.jsx)(U.A,{variant:"contained",fullWidth:!0,onClick:r,children:"Review AI Suggestions"})})]})}),(0,ie.jsxs)(l.A,{style:c?{overflow:"auto"}:{},children:[(0,ie.jsx)(Ei,{pillType:yi.pz.CONFIDENCE_LEVELS,title:yi.HD[yi.pz.CONFIDENCE_LEVELS],expanded:o===yi.pz.CONFIDENCE_LEVELS,handleExpand:u,children:(0,ie.jsx)(Ni,{body:"Confidence levels indicate how certain the AI is about each role and relationship in your chart, helping you identify areas that may need review.",children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:1,children:[(0,ie.jsx)(wi,{color:yi.F4[yi.r_.HIGH],label:"High confidence",description:"AI is confident in the accuracy of the role or relationship."}),(0,ie.jsx)(wi,{color:yi.F4[yi.r_.MEDIUM],label:"Moderate confidence",description:"AI suggests reviewing this role to confirm accuracy."}),(0,ie.jsx)(wi,{color:yi.F4[yi.r_.LOW],label:"Low confidence",description:"AI has limited data on this role. Manual adjustment is recommended."})]})})}),(0,ie.jsx)(Ei,{pillType:yi.pz.IMPROVE_CHART,title:yi.HD[yi.pz.IMPROVE_CHART],expanded:o===yi.pz.IMPROVE_CHART,handleExpand:u,children:(0,ie.jsx)(Ni,{body:"Refine your chart by addressing gaps, verifying relationships, and ensuring key roles are accurately represented.",children:(0,ie.jsxs)(l.A,{display:"flex",flexDirection:"column",gap:2,py:2,children:[(0,ie.jsxs)(l.A,{display:"flex",gap:2,justifyContent:"flex-start",alignItems:"center",children:[(0,ie.jsx)(l.A,{children:(0,ie.jsx)(M.me,{name:"square-dashed",variant:"solid",color:_.Qs.Violet[500],fontSize:30,inlineSvg:!0})}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[800],children:"Review roles and connections suggested by Organimi AI"})]}),(0,ie.jsxs)(l.A,{display:"flex",gap:2,justifyContent:"flex-start",alignItems:"center",children:[(0,ie.jsx)(l.A,{children:(0,ie.jsx)(M.me,{name:"user-magnifying-glass",variant:"solid",color:_.Qs.Violet[500],fontSize:30,inlineSvg:!0})}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[800],children:"Double-check critical information titles, and reporting lines to avoid errors."})]}),(0,ie.jsxs)(l.A,{display:"flex",gap:2,justifyContent:"flex-start",alignItems:"center",children:[(0,ie.jsx)(l.A,{children:(0,ie.jsx)(M.me,{name:"square-right",variant:"solid",color:_.Qs.Violet[500],fontSize:30,inlineSvg:!0})}),(0,ie.jsx)(O.A,{variant:"caption",color:_.Qs.Neutrals[800],children:"Add or update metadata to improve chart utility."})]})]})})}),(0,ie.jsx)(Ei,{pillType:yi.pz.LOG,title:yi.HD[yi.pz.LOG],expanded:o===yi.pz.LOG,handleExpand:u,children:(0,ie.jsx)(Ni,{body:"The Verification Log tracks AI suggestions accepted or rejected during chart creation, offering transparency and a history of decisions for easy review and adjustments.",children:(0,ie.jsx)(l.A,{p:2,children:(0,ie.jsx)(U.A,{variant:"outlined",onClick:i,children:"Open Verification Log"})})})})]}),(0,ie.jsx)(l.A,{position:"static",bottom:0,width:"100%",p:2,zIndex:2,children:(0,ie.jsx)(U.A,{variant:"outlined",fullWidth:!0,onClick:s,color:"error",children:(0,ie.jsx)(O.A,{variant:"labelLG",color:"inherit",children:"Reject and delete chart"})})})]})};var zi=i(18729),Oi=i(67254);const Fi=()=>{const[e,t]=(0,n.useState)(!1),{confirmAction:i}=(0,Ae.A)(),l=(0,u.d4)((e=>{var t,i;return null===(t=e.chart)||void 0===t||null===(i=t.insights)||void 0===i?void 0:i.mode}))||"info",{open:r}=(0,qe.A)("welcomeAutobuild"),{openDialog:s}=(0,qe.A)("changeSuggestionManager"),{openDialog:o}=(0,qe.A)("approveSuggestionBranch"),a=(0,u.wA)();(()=>{const{openDialog:e}=(0,qe.A)("welcomeAutobuild"),t=(0,c.u)((0,A.Yj)()).params,[i,l]=(0,ki.A)("feature.autobuildAttempted-".concat(t.chartId),!1,!0),r=(0,u.d4)(gi.e2);(0,n.useEffect)((()=>{!i&&r.length>0&&(e(),l(!0))}),[i,r])})();const h=(0,d.useParams)(),x=(0,d.useHistory)(),[p,g]=(0,n.useState)(!1),[m]=(0,ki.A)("feature.autobuildAttempted-".concat(h.chartId),!1,!0),v=(0,u.d4)(gi.e2),j=(0,u.d4)(gi.Xo),f=(0,u.d4)((e=>{var t;return null===(t=e.chart)||void 0===t?void 0:t.info})),{handleApproveSuggestion:b,handleApproveBulkSuggestions:y,suggestionIdsPending:C}=(0,fi.n)(),[I,w]=(0,W.A)((async()=>{await a(si.wz.getChartInsights({chartId:h.chartId}))}));(0,n.useEffect)((()=>{null!==f&&void 0!==f&&f.id&&w()}),[null===f||void 0===f?void 0:f.id]);const N=!!(I||(null===C||void 0===C?void 0:C.length)>0);return(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsx)(bi,{open:p,handleClose:()=>{g(!1)}}),"info"===l&&(0,ie.jsx)(Ri,{handleDeleteAutobuildChart:()=>i({execFunc:async()=>{const{error:e}=await a(si.wz.removeAutobuildChart({chartId:h.chartId}));e||x.push((0,A.r2)({...h}))},title:"Are you sure you want to reject the suggested chart?",message:"This action is irreversible"}),handleShowVerificationLog:()=>{g(!0)},handleToggleMode:()=>{a("suggestions"===l?(0,si.A)("info"):(0,si.A)("suggestions"))},loading:N}),"suggestions"===l&&(0,ie.jsx)(Pi,{suggestionIdsPending:C,suggestions:v,visibleCount:j,handleApproveSuggestion:b,handleApproveBulkSuggestions:y,handleChangeManager:s,handleApproveBranch:e=>o({handleApprove:y,suggestion:e})}),(0,ie.jsx)(zi.A,{open:!e&&m&&!r,onClose:()=>{t(!0)},autoHideDuration:24e3,disableWindowBlurListener:!0,anchorOrigin:{vertical:"bottom",horizontal:"left"},children:(0,ie.jsx)(Oi.A,{severity:"info",variant:"outlined",onClose:()=>{t(!0)},sx:{maxWidth:400,bgcolor:"background.paper"},children:"This chart is an AI generated draft, it must be reviewed and accepted to use Organimi sharing, printing and exports"})})]})},Qi=()=>{const e=(0,u.d4)((e=>{var t,i;return(null===(t=e.chart)||void 0===t||null===(i=t.insights)||void 0===i?void 0:i.mode)||"info"})),t=(0,u.wA)(),{openDialog:i}=(0,qe.A)("approveAutobuildChart"),n=(0,u.d4)(gi.oZ),r=n>-1;return(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",justifyContent:"space-between",height:"100%",px:2,width:"100%",bgcolor:r?_.Qs.Violet[500]:_.Qs.Warning[100],color:r?_.Qs.Neutrals[0]:_.Qs.Warning[900],children:[(0,ie.jsxs)(l.A,{display:"flex",alignItems:"center",gap:1,flex:1,children:[(0,ie.jsx)(l.A,{display:"inline-block",fontSize:32,mx:1,children:(0,ie.jsx)(M.me,{name:"circle-info",variant:"solid",fontSize:"inherit"})}),r?(0,ie.jsxs)(l.A,{display:"flex",alignItems:"flex-start",flexDirection:"column",gap:1,children:[(0,ie.jsxs)(O.A,{variant:"bodyLG",color:"inherit",fontWeight:"bold",children:[n||""," roles need your review"]}),(0,ie.jsx)(O.A,{variant:"bodySM",color:"inherit",children:"You're viewing this chart in Review Mode and will not be saved until you review and approve all roles."})]}):(0,ie.jsxs)(l.A,{display:"flex",alignItems:"flex-start",flexDirection:"column",gap:1,children:[(0,ie.jsx)(O.A,{variant:"bodyLG",color:"inherit",fontWeight:"bold",children:"Almost Done!"}),(0,ie.jsx)(O.A,{variant:"bodySM",color:"inherit",children:"You've reviewed the recommendations for this chart. Save your chart now to keep your changes"})]})]}),(0,ie.jsxs)(l.A,{gap:2,display:"flex",alignItems:"center",children:[(0,ie.jsxs)(U.A,{variant:"contained",color:"success",size:"medium",onClick:i,children:[(0,ie.jsx)(M.me,{name:"check-double",variant:"light"}),(0,ie.jsx)(O.A,{variant:"labelLG",color:"inherit",children:r?"Approve all suggestions":"Save chart"})]}),"info"!==e&&(0,ie.jsxs)(U.A,{variant:"contained",color:"white",size:"medium",onClick:()=>{t((0,si.A)("info"))},children:[(0,ie.jsx)(M.me,{name:"money-check-pen",variant:"light"}),(0,ie.jsx)(O.A,{variant:"labelLG",color:"inherit",children:"Close review"})]}),"info"===e&&(0,ie.jsxs)(U.A,{variant:"contained",color:"white",size:"medium",onClick:()=>{t((0,si.A)("suggestions"))},children:[(0,ie.jsx)(M.me,{name:"money-check-pen",variant:"light"}),(0,ie.jsx)(O.A,{variant:"labelLG",color:"inherit",children:"Review suggestions"})]})]})]})};var Mi=i(29794);const Hi=e=>{let{resource:t,showPrintResource:i,...n}=e;const l=(0,u.d4)(L.G0),a=(0,u.d4)(Fe.DD);return"chart"===t?l===j.XD.MATRIX?i&&a.ownPage?(0,ie.jsx)(s.A,{}):(0,ie.jsx)(P.e,{children:(0,ie.jsx)(s.A,{})}):l===j.XD.BOARD_OF_DIRECTORS?(0,ie.jsx)(o.A,{}):i&&a.ownPage?(0,ie.jsx)(r.A,{...n}):(0,ie.jsx)(P.e,{children:(0,ie.jsx)(r.A,{...n})}):"spreadsheet"===t&&l===j.XD.TRADITIONAL||"photoboard"===t||"directory"===t?(0,ie.jsx)(T.E,{resource:t,...n}):void 0},qi=()=>{var e,t,i;const{appHeaderHeight:r}=(0,Mi.A)(),{params:{orgId:s,chartId:o,resource:L,base:T,resourceAction:P}}=(0,c.u)([(0,A.yv)(),(0,A.si)()]),R=(0,u.wA)(),z=(0,d.useHistory)(),O=(0,S.A)("chart",{chartId:o,orgId:s}),F=(0,S.A)("organization",{chartId:o,orgId:s}),Q="print"===T?"print":P===j.uI.PRINT_PREVIEW?j.uI.PRINT_PREVIEW:j.uI.DEFAULT,M=O||F,H=(0,u.d4)(k.tO),q=(0,u.d4)(Pe.hK),_=!M&&(null===q||void 0===q||null===(e=q.data)||void 0===e||null===(t=e.detailsPane)||void 0===t||null===(i=t.layout)||void 0===i?void 0:i.name);(0,n.useEffect)((()=>(async function(){await R((0,y.Wq)({primary:Q,secondary:P}))}(),()=>{})),[Q,P]),(0,n.useEffect)((()=>{R((0,I.nZ)())}),[Q,o]),(0,n.useEffect)((()=>{var e,t;return null!==z&&void 0!==z&&null!==(e=z.location)&&void 0!==e&&null!==(t=e.state)&&void 0!==t&&t.showTalent&&R((0,w.tg)()),()=>{R((0,C.Ch)()),R((0,w.zi)()),R((0,h.hb)())}}),[]),(0,n.useEffect)((()=>{Q!==j.uI.DEFAULT&&(R((0,C.Ch)()),R((0,w.zi)()))}),[Q]);const W="theme"===P,B=(0,u.d4)(w.Q$)&&!W,G=(0,u.d4)(C.t0),V="print"!==Q&&(P===j.uI.DEFAULT||P===j.uI.THEME||P===j.uI.SETTINGS||P===j.uI.AI_INSIGHTS),X=P===j.uI.AI_INSIGHTS,U=Q===j.uI.PRINT||Q===j.uI.PRINT_PREVIEW,Y=Q===pi.$.VIEW,J="print"!==Q,K=G||B,$=G&&_&&"layout-3"!==_&&"layout-4"!==_?j.Ay.dialogs.profileCardWidth:0,Z=(0,u.d4)(Fe.xy);return(0,ie.jsxs)(E.A,{resourceAction:P,orgId:s,chartId:o,children:[(0,ie.jsxs)(m.A,{container:!0,direction:"column",wrap:"nowrap",children:[J&&(0,ie.jsx)(g.A,{item:!0,direction:"vers",height:r,children:(0,ie.jsx)("div",{style:{height:65}})}),(0,ie.jsx)(b.A,{item:!0,xs:!0,children:(0,ie.jsx)(m.A,{container:!0,wrap:"nowrap",children:(0,ie.jsx)(b.A,{item:!0,xs:!0,children:(0,ie.jsx)(m.A,{container:!0,direction:"column",children:(0,ie.jsx)(b.A,{item:!0,xs:!0,children:(0,ie.jsxs)(m.A,{container:!0,direction:"row",wrap:"nowrap",children:[(0,ie.jsx)(b.A,{item:!0,xs:!0,children:(0,ie.jsxs)(m.A,{container:!0,direction:"column",wrap:"nowrap",children:[V&&(0,ie.jsx)(g.A,{item:!0,direction:"vers",height:j.Ay.chart.topSectionHeight,children:(0,ie.jsx)(D.A,{resource:L})}),X&&(0,ie.jsx)(l.A,{height:j.Ay.ai.toolbarHeight,children:(0,ie.jsx)(Qi,{})}),(0,ie.jsx)(v.A,{item:!0,xs:!0,children:(0,ie.jsxs)(l.A,{display:"flex",flexWrap:"nowrap",position:"relative",height:"100%",children:[Y&&(0,ie.jsx)(a.A,{}),W&&(0,ie.jsx)(x.LG,{}),U&&(0,ie.jsx)(p.A,{loading:M,children:(0,ie.jsx)(xi,{children:(0,ie.jsx)(Hi,{resource:L,printSettings:Z,showPrintResource:U})})}),!U&&(0,ie.jsx)(p.A,{loading:M,children:(0,ie.jsx)(Hi,{resource:L,chartId:o,orgId:s,showPrintResource:U})}),X&&(0,ie.jsx)(Fi,{})]})})]})}),K&&(0,ie.jsx)(g.A,{item:!0,width:$,children:G?(0,ie.jsx)(f.A,{}):(0,ie.jsx)(N.A,{})})]})})})})})})]}),H&&(0,ie.jsx)(Te,{open:H,handleClose:()=>{R((0,k.Qs)())}})]})}},48283:(e,t,i)=>{i.d(t,{Cs:()=>l,yy:()=>r,zv:()=>s});var n=i(78396);function l(e){let t={title:"Role Title *",description:"Role Description",name:!0,color:!0,propagateBg:!0,location:!1,locationAddress:!0,chartLink:!1,dottedReports:!0,manager:!0,dropLevel:!0,leftRight:!1,customFields:!0,smartLegendLink:!0};switch(e){case"assistant":t.orientation=!0,t.dropLevel=!1;break;case"location":t.description=!1,t.dottedReports=!1,t.locationAddress=!0,t.title="Location Name",t.description="Location Description";break;case"embedded":t.chartLink=!0,t.dottedReports=!1,t.location=!1,t.locationAddress=!1,t.dottedReport=!1,t.title="Linked Chart",t.description=!1,t.propagateBg=!1,t.manager=!1,t.dottedReports=!1,t.dropLevel=!1,t.customFields=!1,t.smartLegendLink=!1;break;case"department":t.title="Department Title *",t.description="Department Description",t.locationAddress=!1}return t}function r(e){let{role:t,chartType:i}=e;return i===n.XD.MATRIX&&!(null!==t&&void 0!==t&&t.parent)&&!(null!==t&&void 0!==t&&t.type)!==n.mv.HIDDEN&&!t.functionId&&!t.teamId}function s(e){let{role:t,chartType:i}=e;return i===n.XD.MATRIX&&[n.mv.TEAM,n.mv.FUNCTION,n.mv.HIDDEN].includes(t.type)}},34976:(e,t,i)=>{i.d(t,{A:()=>u});var n=i(65043),l=i(61),r=i(86255),s=i(66856),o=i(14556),a=i(36138),d=i(19367),c=i(48283);const u=e=>{let{model:t,fieldId:i,defaultChartId:u,defaultOrgId:h}=e;const x=(0,o.wA)(),[p,g]=(0,n.useState)(!1),[m,v]=(0,n.useState)([]),[j,A]=(0,n.useState)(""),f=(0,o.d4)(d.G0),{params:{orgId:b,chartId:y}}=(0,a.u)([(0,s.si)(),(0,s.K7)()]),C=y||u,I=b||h,{eventDebounce:w}=(0,r.A)(),N=w((e=>{A(e)}),500),E=(0,n.useCallback)((e=>{v([]),g(!0),N(e)}),[]),S=(0,n.useMemo)((()=>{if("role"===t)return l.JP.findRoles;throw new Error("Search not yet handled")}),[t]);return(0,n.useEffect)((()=>{j?(async()=>{const{payload:e}=await x(S({orgId:I,chartId:C,query:j,fields:i}));g(!1);const t=((null===e||void 0===e?void 0:e.results)||[]).filter((e=>{const t=(0,c.yy)({role:e.role,chartType:f}),i=(0,c.zv)({role:e.role,chartType:f});return!(t||i)}));v(t)})():(v([]),g(!1))}),[j]),{query:j,results:m,loading:p,handleInputChange:E}}}}]);
//# sourceMappingURL=4592.f1cfbcb8.chunk.js.map

// === 4259.73ba6d10.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[4259],{34259:(e,n,t)=>{t.r(n),t.d(n,{RolesVsPeople:()=>I,VideoOverlay:()=>j,VideoPlayer:()=>b,default:()=>k});var o,r,i=t(57528),l=t(61531),a=t(72835),s=t(96364),c=t(10035),d=t(86825),h=t(65043),p=t(72119),u=t(75156),g=t(14556),x=t(36138),f=t(66856),m=t(83972),v=t(66588),y=t(91688),A=t(70669),w=t(70579);const b=(0,p.Ay)(l.A)(o||(o=(0,i.A)(["\n  position: fixed;\n  display: flex;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  z-index: 1;\n"]))),j=(0,p.Ay)(l.A)(r||(r=(0,i.A)(["\n  width: 100%;\n  color: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.4);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  &:hover {\n    background: rgba(0, 0, 0, 0.1);\n  }\n"]))),I=e=>{var n,t;const{goNext:o,goBack:r,skipTour:i,completeTour:p,setActiveTour:I}=e,{show:k}=(0,v.A)(),C=(0,y.useHistory)(),z=(0,g.wA)(),T=(0,g.d4)(m._l),{isExact:V}=(0,x.u)((0,f.si)()),[P,S]=(0,h.useState)(!1),D=null===T||void 0===T?void 0:T.id,O=null===T||void 0===T||null===(n=T.charts)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.id,W=()=>{if(!V)return D&&O?C.push((0,f.Wx)({base:"protected",chartId:O,orgId:D})):(setTimeout((()=>{I({tour:null})})),k("Open any chart to proceed","info"))};return(0,w.jsx)(c.A,{...e,onDialogClose:i,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,w.jsxs)(l.A,{display:"flex",justifyContent:"space-between",children:[(0,w.jsx)(a.A,{variant:"outlined",onClick:r,children:"Go Back"}),(0,w.jsx)(a.A,{size:"large",color:"primary",variant:"contained",onClick:()=>null!==T&&void 0!==T&&T.charts.length?(p({userInput:{hasExistingCharts:!0},resetActiveTour:!1}),setTimeout((()=>{I({tour:"chartTour"})})),W(),void z((0,A.tg)())):o(),children:"Continue"})]}),children:(0,w.jsx)(l.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,w.jsxs)(l.A,{display:"flex",flexDirection:"column",gridGap:24,children:[(0,w.jsxs)(l.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,w.jsx)(s.A,{variant:"h1",style:{fontWeight:"bold"},children:"Roles vs. People"}),(0,w.jsxs)(s.A,{variant:"body1",style:{color:"#626262"},children:["Think of an org chart in two parts:",(0,w.jsx)("span",{style:{fontWeight:"bold"},children:" Roles "}),"(the positions) & ",(0,w.jsx)("span",{style:{fontWeight:"bold"},children:"People "}),"(who fill those positions). Let's set up Roles first!"]})]}),(0,w.jsxs)(l.A,{alignItems:"center",justifyContent:"space-between",display:"flex",gridGap:16,children:[(0,w.jsx)(l.A,{flex:3,children:(0,w.jsx)("img",{width:"100%",src:d.t.roleVsPeople,alt:"Roles vs. people"})}),(0,w.jsxs)(l.A,{flex:1,display:"flex",flexDirection:"column",alignItems:"center",children:[(0,w.jsxs)(l.A,{minWidth:150,height:100,position:"relative",mb:2,children:[(0,w.jsx)(j,{onClick:()=>S(!0),children:(0,w.jsx)(u.Ay,{icon:"PlaySolid",size:"x4"})}),(0,w.jsx)("img",{height:"100%",width:"100%",src:d.t.roleVsPeopleIntroVidThumbnail})]}),(0,w.jsx)(s.A,{variant:"body1",style:{fontWeight:"bold"},children:"Watch & learn"}),(0,w.jsx)(s.A,{variant:"body1",style:{color:"#626262"},children:"30 sec video tutorial"})]})]}),P&&(0,w.jsx)(b,{p:8,onClick:()=>S(!1),children:(0,w.jsxs)(l.A,{display:"flex",justifyContent:"center",width:"100%",position:"relative",height:"100%",onClick:e=>e.stopPropagation(),children:[(0,w.jsx)("video",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:d.t.roleVsPeopleIntroVid,autoPlay:!0,controls:!0,title:"Organimi Help Walk Through",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowfullscreen:!0}),(0,w.jsx)(l.A,{position:"fixed",right:16,top:16,children:(0,w.jsx)(a.A,{variant:"contained",color:"primary",onClick:()=>{S(!1)},children:"Close Video"})})]})})]})})})},k=I},10035:(e,n,t)=>{t.d(n,{A:()=>w});var o=t(57528),r=t(35007),i=t(70567),l=t(61531),a=t(35801),s=t(43867),c=t(52907),d=t(71233),h=t(5816),p=t(65043),u=t(72119);const g=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:o,skipTour:r}=e;let i=null;const l=(0,p.useRef)(!0);if(t){const e=t,n=null===o||void 0===o?void 0:o[e];i=e&&n?"".concat(e,"_").concat(n):null}const a=i||n,s=(0,p.useRef)(),c=(0,p.useRef)(0),d=a&&g(a),[h,u]=(0,p.useState)(null);return(0,p.useEffect)((()=>(s.current&&clearInterval(s.current),a&&(s.current=setInterval((()=>{c.current++;const e=a&&g(a);e&&(clearInterval(s.current),u(e||null),c.current=0),!e&&c.current>60&&l.current&&(clearInterval(s.current),l.current=!1,r({errored:!0}))}),100)),()=>{s.current&&clearInterval(s.current)})),[a]),(0,p.useEffect)((()=>()=>{s.current&&clearInterval(s.current)}),[]),d||h||null}var f,m=t(64418),v=t(70579);const y="tour-highlight-anchor",A=(0,u.Ay)(r.A)(f||(f=(0,o.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:o,zIndex:r=1e4}=e;return"\n    z-index: ".concat(r,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),w=e=>{let{children:n,onDialogClose:t,actionsUI:o,...r}=e;const{activeTourStep:u}=r,g=(0,p.useRef)(null),f=(0,i.A)(),[w,b]=(0,p.useState)(null),j=x(r),{confirmAction:I}=(0,m.A)(),{anchorClass:k,backdrop:C,arrow:z,highlightAnchor:T,ignoreAnchorZIndex:V,zIndex:P,anchorOffset:S,backdropAboveModal:D,position:O,showDialogUI:W=!1}=u||{},R=[!0,!1].includes(C)?C:!!j,L=[!0,!1].includes(z)?z:!!j,B=[!0,!1].includes(T)?T:R&&!!j,E=O||"left-start",G=(D?f.zIndex.modal:f.zIndex.drawer)+1,U=G+1;if((0,p.useEffect)((()=>{if(j)return R&&!V&&(j.style.zIndex=(j.style.zIndex||0)+U),B&&j.classList.toggle(y),k&&j.classList.toggle(k),()=>{j&&(R&&!V&&(j.style.zIndex=j.style.zIndex-U),B&&j.classList.toggle(y),k&&j.classList.toggle(k))}}),[j]),null!==u&&void 0!==u&&u.hiddenStep)return(0,v.jsx)(v.Fragment,{});return(0,v.jsxs)(l.A,{children:[W&&(0,v.jsxs)(a.A,{open:!0,onClose:()=>{t&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===u||void 0===u||!u.size),maxWidth:(null===u||void 0===u?void 0:u.size)||"sm",children:[(0,v.jsx)(l.A,{zIndex:1,children:(0,v.jsx)(h.A,{densePadding:!0,onClose:t||null})}),(0,v.jsx)(s.A,{style:{padding:0},children:n}),o&&(0,v.jsx)(c.A,{style:{display:"block",padding:"24px"},children:o})]}),!W&&j&&(0,v.jsx)(d.A,{open:R,style:{zIndex:G},children:(0,v.jsxs)(A,{open:!0,ref:g,anchorEl:j,placement:E,zIndex:P,width:(null===u||void 0===u?void 0:u.size)||"sm",showBackdrop:R,anchorOffset:S,modifiers:{arrow:{enabled:L,element:w}},children:[L&&(0,v.jsx)(l.A,{className:"arrow",ref:b,children:(0,v.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,v.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}},86825:(e,n,t)=>{t.d(n,{t:()=>r});const o="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),r={buildFirstChart:"".concat(o,"/buildFirstChart.svg"),fromScratch:"".concat(o,"/fromScratch.svg"),largeOrgs:"".concat(o,"/largeOrgs.svg"),smallOrgs:"".concat(o,"/smallOrgs.svg"),realBrands:"".concat(o,"/realBrands.svg"),roleVsPeople:"".concat(o,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(o,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(o,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(o,"/import.svg"),addManually:"".concat(o,"/addManually.svg"),integrate:"".concat(o,"/integrate.svg"),welcome:"".concat(o,"/welcome.svg"),confetti:"".concat(o,"/confetti.png"),style:"".concat(o,"/style.svg"),integrate2:"".concat(o,"/integrate2.svg"),import2:"".concat(o,"/import2.svg"),share:"".concat(o,"/share.svg"),cards:"".concat(o,"/cards.svg"),hierarchy:"".concat(o,"/hierarchy.svg"),navigatingChart:"".concat(o,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=4259.73ba6d10.chunk.js.map

// === 3760.92ca97a3.chunk.js ===

