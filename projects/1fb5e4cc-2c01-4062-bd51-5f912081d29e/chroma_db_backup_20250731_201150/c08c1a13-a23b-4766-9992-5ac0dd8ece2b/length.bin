e;r&&se.current.contains(document.activeElement)&&(null==(e=document.activeElement)||e.blur())}),[r]),r&&-1!==X&&B(-1),r&&-1!==le&&ie(-1);const be=o.useRef();let he=E;i&&"horizontal"===E&&(he+="-reverse");const fe=e=>{let{finger:t,move:a=!1}=e;const{current:r}=se,{width:n,height:o,bottom:i,left:s}=r.getBoundingClientRect();let c,d;if(c=0===he.indexOf("vertical")?(i-t.y)/o:(t.x-s)/n,-1!==he.indexOf("-reverse")&&(c=1-c),d=function(e,t,a){return(a-t)*e+t}(c,P,u),V)d=A(d,V,P);else{const e=x(te,d);d=te[e]}d=(0,p.A)(d,P,u);let m=0;if(J){m=a?be.current:x(Z,d),l&&(d=(0,p.A)(d,Z[m-1]||-1/0,Z[m+1]||1/0));const e=d;d=w({values:Z,newValue:d,index:m}),l&&a||(m=d.indexOf(e),be.current=m)}return{newValue:d,activeIndex:m}},ge=(0,h.A)((e=>{const t=k(e,Q);if(!t)return;if($.current+=1,"mousemove"===e.type&&0===e.buttons)return void ye(e);const{newValue:a,activeIndex:r}=fe({finger:t,move:!0});L({sliderRef:se,activeIndex:r,setActive:B}),q(a),!H&&$.current>2&&U(!0),G&&!C(a,_)&&G(e,a,r)})),ye=(0,h.A)((e=>{const t=k(e,Q);if(U(!1),!t)return;const{newValue:a}=fe({finger:t,move:!0});B(-1),"touchend"===e.type&&W(-1),M&&M(e,a),Q.current=void 0,ke()})),xe=(0,h.A)((e=>{if(r)return;T()||e.preventDefault();const t=e.changedTouches[0];null!=t&&(Q.current=t.identifier);const a=k(e,Q);if(!1!==a){const{newValue:t,activeIndex:r}=fe({finger:a});L({sliderRef:se,activeIndex:r,setActive:B}),q(t),G&&!C(t,_)&&G(e,t,r)}$.current=0;const n=(0,c.A)(se.current);n.addEventListener("touchmove",ge,{passive:!0}),n.addEventListener("touchend",ye,{passive:!0})})),ke=o.useCallback((()=>{const e=(0,c.A)(se.current);e.removeEventListener("mousemove",ge),e.removeEventListener("mouseup",ye),e.removeEventListener("touchmove",ge),e.removeEventListener("touchend",ye)}),[ye,ge]);o.useEffect((()=>{const{current:e}=se;return e.addEventListener("touchstart",xe,{passive:T()}),()=>{e.removeEventListener("touchstart",xe),ke()}}),[ke,xe]),o.useEffect((()=>{r&&ke()}),[r,ke]);const Se=S(J?Z[0]:P,P,u),Ae=S(Z[Z.length-1],P,u)-Se,we=e=>t=>{var a;null==(a=e.onMouseLeave)||a.call(e,t),W(-1)};return{active:X,axis:he,axisProps:R,dragging:H,focusedThumbIndex:le,getHiddenInputProps:function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var o;const l=(0,g.h)(a),s={onChange:(c=l||{},e=>{var t;null==(t=c.onChange)||t.call(c,e),me(e,e.target.valueAsNumber)}),onFocus:de(l||{}),onBlur:pe(l||{}),onKeyDown:ve(l||{})};var c;const d=(0,n.default)({},l,s);return(0,n.default)({tabIndex:D,"aria-labelledby":t,"aria-orientation":E,"aria-valuemax":O(u),"aria-valuemin":O(P),name:I,type:"range",min:e.min,max:e.max,step:null===e.step&&e.marks?"any":null!=(o=e.step)?o:void 0,disabled:r},a,d,{style:(0,n.default)({},f.A,{direction:i?"rtl":"ltr",width:"100%",height:"100%"})})},getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,g.h)(e),a={onMouseDown:(o=t||{},e=>{var t;if(null==(t=o.onMouseDown)||t.call(o,e),r)return;if(e.defaultPrevented)return;if(0!==e.button)return;e.preventDefault();const a=k(e,Q);if(!1!==a){const{newValue:t,activeIndex:r}=fe({finger:a});L({sliderRef:se,activeIndex:r,setActive:B}),q(t),G&&!C(t,_)&&G(e,t,r)}$.current=0;const n=(0,c.A)(se.current);n.addEventListener("mousemove",ge,{passive:!0}),n.addEventListener("mouseup",ye)})};var o;const l=(0,n.default)({},t,a);return(0,n.default)({},e,{ref:ce},l)},getThumbProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,g.h)(e),a={onMouseOver:(r=t||{},e=>{var t;null==(t=r.onMouseOver)||t.call(r,e);const a=Number(e.currentTarget.getAttribute("data-index"));W(a)}),onMouseLeave:we(t||{})};var r;return(0,n.default)({},e,t,a)},marks:ee,open:K,range:J,rootRef:ce,trackLeap:Ae,trackOffset:Se,values:Z,getThumbStyle:e=>({pointerEvents:-1!==X&&X!==e?"none":void 0})}}var N=a(67266),M=a(10875),E=a(44350),j=a(34535),O=a(47123);const V=e=>!e||!(0,s.g)(e);var F=a(6803),D=a(57056),Y=a(32400);function Q(e){return(0,Y.Ay)("MuiSlider",e)}const X=(0,D.A)("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]);var B=a(70579);const K=["aria-label","aria-valuetext","aria-labelledby","component","components","componentsProps","color","classes","className","disableSwap","disabled","getAriaLabel","getAriaValueText","marks","max","min","name","onChange","onChangeCommitted","orientation","shiftStep","size","step","scale","slotProps","slots","tabIndex","track","value","valueLabelDisplay","valueLabelFormat"],W=(0,E.h)("MuiSlider");function H(e){return e}const U=(0,j.Ay)("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,t["color".concat((0,F.A)(a.color))],"medium"!==a.size&&t["size".concat((0,F.A)(a.size))],a.marked&&t.marked,"vertical"===a.orientation&&t.vertical,"inverted"===a.track&&t.trackInverted,!1===a.track&&t.trackFalse]}})((e=>{let{theme:t}=e;var a;return{borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},["&.".concat(X.disabled)]:{pointerEvents:"none",cursor:"default",color:(t.vars||t).palette.grey[400]},["&.".concat(X.dragging)]:{["& .".concat(X.thumb,", & .").concat(X.track)]:{transition:"none"}},variants:[...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e},style:{color:(t.vars||t).palette[e].main}}))),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}})),$=(0,j.Ay)("span",{name:"MuiSlider",slot:"Rail",overridesResolver:(e,t)=>t.rail})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),_=(0,j.Ay)("span",{name:"MuiSlider",slot:"Track",overridesResolver:(e,t)=>t.track})((e=>{let{theme:t}=e;var a;return{display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:t.transitions.create(["left","width","bottom","height"],{duration:t.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e,track:"inverted"},style:(0,n.default)({},t.vars?{backgroundColor:t.vars.palette.Slider["".concat(e,"Track")],borderColor:t.vars.palette.Slider["".concat(e,"Track")]}:(0,n.default)({backgroundColor:(0,N.a)(t.palette[e].main,.62),borderColor:(0,N.a)(t.palette[e].main,.62)},t.applyStyles("dark",{backgroundColor:(0,N.e$)(t.palette[e].main,.5)}),t.applyStyles("dark",{borderColor:(0,N.e$)(t.palette[e].main,.5)})))})))]}})),q=(0,j.Ay)("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.thumb,t["thumbColor".concat((0,F.A)(a.color))],"medium"!==a.size&&t["thumbSize".concat((0,F.A)(a.size))]]}})((e=>{let{theme:t}=e;var a;return{position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:t.transitions.create(["box-shadow","left","bottom"],{duration:t.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(t.vars||t).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},["&.".concat(X.disabled)]:{"&:hover":{boxShadow:"none"}},variants:[...Object.keys((null!=(a=t.vars)?a:t).palette).filter((e=>{var a;return(null!=(a=t.vars)?a:t).palette[e].main})).map((e=>({props:{color:e},style:{["&:hover, &.".concat(X.focusVisible)]:(0,n.default)({},t.vars?{boxShadow:"0px 0px 0px 8px rgba(".concat(t.vars.palette[e].mainChannel," / 0.16)")}:{boxShadow:"0px 0px 0px 8px ".concat((0,N.X4)(t.palette[e].main,.16))},{"@media (hover: none)":{boxShadow:"none"}}),["&.".concat(X.active)]:(0,n.default)({},t.vars?{boxShadow:"0px 0px 0px 14px rgba(".concat(t.vars.palette[e].mainChannel," / 0.16)}")}:{boxShadow:"0px 0px 0px 14px ".concat((0,N.X4)(t.palette[e].main,.16))})}}))),{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}}]}})),G=(0,j.Ay)((function(e){const{children:t,className:a,value:r}=e,n=(e=>{const{open:t}=e;return{offset:(0,l.A)(t&&X.valueLabelOpen),circle:X.valueLabelCircle,label:X.valueLabelLabel}})(e);return t?o.cloneElement(t,{className:(0,l.A)(t.props.className)},(0,B.jsxs)(o.Fragment,{children:[t.props.children,(0,B.jsx)("span",{className:(0,l.A)(n.offset,a),"aria-hidden":!0,children:(0,B.jsx)("span",{className:n.circle,children:(0,B.jsx)("span",{className:n.label,children:r})})})]})):null}),{name:"MuiSlider",slot:"ValueLabel",overridesResolver:(e,t)=>t.valueLabel})((e=>{let{theme:t}=e;return(0,n.default)({zIndex:1,whiteSpace:"nowrap"},t.typography.body2,{fontWeight:500,transition:t.transitions.create(["transform"],{duration:t.transitions.duration.shortest}),position:"absolute",backgroundColor:(t.vars||t).palette.grey[600],borderRadius:2,color:(t.vars||t).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},["&.".concat(X.valueLabelOpen)]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},["&.".concat(X.valueLabelOpen)]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:t.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]})})),J=(0,j.Ay)("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>(0,O.A)(e)&&"markActive"!==e,overridesResolver:(e,t)=>{const{markActive:a}=e;return[t.mark,a&&t.markActive]}})((e=>{let{theme:t}=e;return{position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(t.vars||t).palette.background.paper,opacity:.8}}]}})),Z=(0,j.Ay)("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>(0,O.A)(e)&&"markLabelActive"!==e,overridesResolver:(e,t)=>t.markLabel})((e=>{let{theme:t}=e;return(0,n.default)({},t.typography.body2,{color:(t.vars||t).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(t.vars||t).palette.text.primary}}]})})),ee=e=>{let{children:t}=e;return t},te=o.forwardRef((function(e,t){var a,c,d,p,m,v,b,h,f,g,y,x,k,A,w,L,C,R,z,P,T,N,E,j;const O=W({props:e,name:"MuiSlider"}),D=(0,M.I)(),{"aria-label":Y,"aria-valuetext":X,"aria-labelledby":te,component:ae="span",components:re={},componentsProps:ne={},color:oe="primary",classes:le,className:ie,disableSwap:se=!1,disabled:ue=!1,getAriaLabel:ce,getAriaValueText:de,marks:pe=!1,max:me=100,min:ve=0,orientation:be="horizontal",shiftStep:he=10,size:fe="medium",step:ge=1,scale:ye=H,slotProps:xe,slots:ke,track:Se="normal",valueLabelDisplay:Ae="off",valueLabelFormat:we=H}=O,Le=(0,r.default)(O,K),Ce=(0,n.default)({},O,{isRtl:D,max:me,min:ve,classes:le,disabled:ue,disableSwap:se,orientation:be,marks:pe,color:oe,size:fe,step:ge,shiftStep:he,scale:ye,track:Se,valueLabelDisplay:Ae,valueLabelFormat:we}),{axisProps:Re,getRootProps:ze,getHiddenInputProps:Pe,getThumbProps:Te,open:Ie,active:Ne,axis:Me,focusedThumbIndex:Ee,range:je,dragging:Oe,marks:Ve,values:Fe,trackOffset:De,trackLeap:Ye,getThumbStyle:Qe}=I((0,n.default)({},Ce,{rootRef:t}));Ce.marked=Ve.length>0&&Ve.some((e=>e.label)),Ce.dragging=Oe,Ce.focusedThumbIndex=Ee;const Xe=(e=>{const{disabled:t,dragging:a,marked:r,orientation:n,track:o,classes:l,color:i,size:s}=e,c={root:["root",t&&"disabled",a&&"dragging",r&&"marked","vertical"===n&&"vertical","inverted"===o&&"trackInverted",!1===o&&"trackFalse",i&&"color".concat((0,F.A)(i)),s&&"size".concat((0,F.A)(s))],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",s&&"thumbSize".concat((0,F.A)(s)),i&&"thumbColor".concat((0,F.A)(i))],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return(0,u.A)(c,Q,l)})(Ce),Be=null!=(a=null!=(c=null==ke?void 0:ke.root)?c:re.Root)?a:U,Ke=null!=(d=null!=(p=null==ke?void 0:ke.rail)?p:re.Rail)?d:$,We=null!=(m=null!=(v=null==ke?void 0:ke.track)?v:re.Track)?m:_,He=null!=(b=null!=(h=null==ke?void 0:ke.thumb)?h:re.Thumb)?b:q,Ue=null!=(f=null!=(g=null==ke?void 0:ke.valueLabel)?g:re.ValueLabel)?f:G,$e=null!=(y=null!=(x=null==ke?void 0:ke.mark)?x:re.Mark)?y:J,_e=null!=(k=null!=(A=null==ke?void 0:ke.markLabel)?A:re.MarkLabel)?k:Z,qe=null!=(w=null!=(L=null==ke?void 0:ke.input)?L:re.Input)?w:"input",Ge=null!=(C=null==xe?void 0:xe.root)?C:ne.root,Je=null!=(R=null==xe?void 0:xe.rail)?R:ne.rail,Ze=null!=(z=null==xe?void 0:xe.track)?z:ne.track,et=null!=(P=null==xe?void 0:xe.thumb)?P:ne.thumb,tt=null!=(T=null==xe?void 0:xe.valueLabel)?T:ne.valueLabel,at=null!=(N=null==xe?void 0:xe.mark)?N:ne.mark,rt=null!=(E=null==xe?void 0:xe.markLabel)?E:ne.markLabel,nt=null!=(j=null==xe?void 0:xe.input)?j:ne.input,ot=(0,i.Q)({elementType:Be,getSlotProps:ze,externalSlotProps:Ge,externalForwardedProps:Le,additionalProps:(0,n.default)({},V(Be)&&{as:ae}),ownerState:(0,n.default)({},Ce,null==Ge?void 0:Ge.ownerState),className:[Xe.root,ie]}),lt=(0,i.Q)({elementType:Ke,externalSlotProps:Je,ownerState:Ce,className:Xe.rail}),it=(0,i.Q)({elementType:We,externalSlotProps:Ze,additionalProps:{style:(0,n.default)({},Re[Me].offset(De),Re[Me].leap(Ye))},ownerState:(0,n.default)({},Ce,null==Ze?void 0:Ze.ownerState),className:Xe.track}),st=(0,i.Q)({elementType:He,getSlotProps:Te,externalSlotProps:et,ownerState:(0,n.default)({},Ce,null==et?void 0:et.ownerState),className:Xe.thumb}),ut=(0,i.Q)({elementType:Ue,externalSlotProps:tt,ownerState:(0,n.default)({},Ce,null==tt?void 0:tt.ownerState),className:Xe.valueLabel}),ct=(0,i.Q)({elementType:$e,externalSlotProps:at,ownerState:Ce,className:Xe.mark}),dt=(0,i.Q)({elementType:_e,externalSlotProps:rt,ownerState:Ce,className:Xe.markLabel}),pt=(0,i.Q)({elementType:qe,getSlotProps:Pe,externalSlotProps:nt,ownerState:Ce});return(0,B.jsxs)(Be,(0,n.default)({},ot,{children:[(0,B.jsx)(Ke,(0,n.default)({},lt)),(0,B.jsx)(We,(0,n.default)({},it)),Ve.filter((e=>e.value>=ve&&e.value<=me)).map(((e,t)=>{const a=S(e.value,ve,me),r=Re[Me].offset(a);let i;return i=!1===Se?-1!==Fe.indexOf(e.value):"normal"===Se&&(je?e.value>=Fe[0]&&e.value<=Fe[Fe.length-1]:e.value<=Fe[0])||"inverted"===Se&&(je?e.value<=Fe[0]||e.value>=Fe[Fe.length-1]:e.value>=Fe[0]),(0,B.jsxs)(o.Fragment,{children:[(0,B.jsx)($e,(0,n.default)({"data-index":t},ct,!(0,s.g)($e)&&{markActive:i},{style:(0,n.default)({},r,ct.style),className:(0,l.A)(ct.className,i&&Xe.markActive)})),null!=e.label?(0,B.jsx)(_e,(0,n.default)({"aria-hidden":!0,"data-index":t},dt,!(0,s.g)(_e)&&{markLabelActive:i},{style:(0,n.default)({},r,dt.style),className:(0,l.A)(Xe.markLabel,dt.className,i&&Xe.markLabelActive),children:e.label})):null]},t)})),Fe.map(((e,t)=>{const a=S(e,ve,me),r=Re[Me].offset(a),o="off"===Ae?ee:Ue;return(0,B.jsx)(o,(0,n.default)({},!(0,s.g)(o)&&{valueLabelFormat:we,valueLabelDisplay:Ae,value:"function"===typeof we?we(ye(e),t):we,index:t,open:Ie===t||Ne===t||"on"===Ae,disabled:ue},ut,{children:(0,B.jsx)(He,(0,n.default)({"data-index":t},st,{className:(0,l.A)(Xe.thumb,st.className,Ne===t&&Xe.active,Ee===t&&Xe.focusVisible),style:(0,n.default)({},r,Qe(t),st.style),children:(0,B.jsx)(qe,(0,n.default)({"data-index":t,"aria-label":ce?ce(t):Y,"aria-valuenow":ye(e),"aria-labelledby":te,"aria-valuetext":de?de(ye(e),t):X,value:Fe[t]},pt))}))}),t)}))]}))})),ae=te}}]);
//# sourceMappingURL=1802.854278ce.chunk.js.map

// === 1649.a3a6757b.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[1649],{1649:(e,t,n)=>{n.r(t),n.d(t,{default:()=>D});var o=n(65043),r=n(80045),i=n(64467),a=n(58168),u=n(43024),s=n(71745),c=n(12899),l=n(97950),d=n(79892),f=n(60768),m=n(32158);function v(e){return e.substring(2).toLowerCase()}const p=function(e){var t=e.children,n=e.disableReactTree,r=void 0!==n&&n,i=e.mouseEvent,a=void 0===i?"onClick":i,u=e.onClickAway,s=e.touchEvent,c=void 0===s?"onTouchEnd":s,p=o.useRef(!1),g=o.useRef(null),E=o.useRef(!1),h=o.useRef(!1);o.useEffect((function(){return setTimeout((function(){E.current=!0}),0),function(){E.current=!1}}),[]);var A=o.useCallback((function(e){g.current=l.findDOMNode(e)}),[]),b=(0,f.A)(t.ref,A),w=(0,m.A)((function(e){var t=h.current;if(h.current=!1,E.current&&g.current&&!function(e){return document.documentElement.clientWidth<e.clientX||document.documentElement.clientHeight<e.clientY}(e))if(p.current)p.current=!1;else{var n;if(e.composedPath)n=e.composedPath().indexOf(g.current)>-1;else n=!(0,d.A)(g.current).documentElement.contains(e.target)||g.current.contains(e.target);n||!r&&t||u(e)}})),x=function(e){return function(n){h.current=!0;var o=t.props[e];o&&o(n)}},C={ref:b};return!1!==c&&(C[c]=x(c)),o.useEffect((function(){if(!1!==c){var e=v(c),t=(0,d.A)(g.current),n=function(){p.current=!0};return t.addEventListener(e,w),t.addEventListener("touchmove",n),function(){t.removeEventListener(e,w),t.removeEventListener("touchmove",n)}}}),[w,c]),!1!==a&&(C[a]=x(a)),o.useEffect((function(){if(!1!==a){var e=v(a),t=(0,d.A)(g.current);return t.addEventListener(e,w),function(){t.removeEventListener(e,w)}}}),[w,a]),o.createElement(o.Fragment,null,o.cloneElement(t,C))};var g=n(74822),E=n(146),h=n(51575),A=n(20495),b=n(82454),w=o.forwardRef((function(e,t){var n=e.action,i=e.classes,s=e.className,c=e.message,l=e.role,d=void 0===l?"alert":l,f=(0,r.A)(e,["action","classes","className","message","role"]);return o.createElement(A.A,(0,a.default)({role:d,square:!0,elevation:6,className:(0,u.A)(i.root,s),ref:t},f),o.createElement("div",{className:i.message},c),n?o.createElement("div",{className:i.action},n):null)}));const x=(0,s.A)((function(e){var t="light"===e.palette.type?.8:.98,n=(0,b.tL)(e.palette.background.default,t);return{root:(0,a.default)({},e.typography.body2,(0,i.A)({color:e.palette.getContrastText(n),backgroundColor:n,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:e.shape.borderRadius,flexGrow:1},e.breakpoints.up("sm"),{flexGrow:"initial",minWidth:288})),message:{padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}}}),{name:"MuiSnackbarContent"})(w);var C=o.forwardRef((function(e,t){var n=e.action,i=e.anchorOrigin,s=(i=void 0===i?{vertical:"bottom",horizontal:"center"}:i).vertical,l=i.horizontal,d=e.autoHideDuration,f=void 0===d?null:d,v=e.children,A=e.classes,b=e.className,w=e.ClickAwayListenerProps,C=e.ContentProps,k=e.disableWindowBlurListener,y=void 0!==k&&k,L=e.message,R=e.onClose,T=e.onEnter,j=e.onEntered,I=e.onEntering,N=e.onExit,O=e.onExited,M=e.onExiting,S=e.onMouseEnter,D=e.onMouseLeave,B=e.open,z=e.resumeHideDuration,P=e.TransitionComponent,H=void 0===P?h.A:P,W=e.transitionDuration,J=void 0===W?{enter:c.p0.enteringScreen,exit:c.p0.leavingScreen}:W,q=e.TransitionProps,F=(0,r.A)(e,["action","anchorOrigin","autoHideDuration","children","classes","className","ClickAwayListenerProps","ContentProps","disableWindowBlurListener","message","onClose","onEnter","onEntered","onEntering","onExit","onExited","onExiting","onMouseEnter","onMouseLeave","open","resumeHideDuration","TransitionComponent","transitionDuration","TransitionProps"]),_=o.useRef(),G=o.useState(!0),V=G[0],X=G[1],U=(0,m.A)((function(){R&&R.apply(void 0,arguments)})),Y=(0,m.A)((function(e){R&&null!=e&&(clearTimeout(_.current),_.current=setTimeout((function(){U(null,"timeout")}),e))}));o.useEffect((function(){return B&&Y(f),function(){clearTimeout(_.current)}}),[B,f,Y]);var K=function(){clearTimeout(_.current)},Q=o.useCallback((function(){null!=f&&Y(null!=z?z:.5*f)}),[f,z,Y]);return o.useEffect((function(){if(!y&&B)return window.addEventListener("focus",Q),window.addEventListener("blur",K),function(){window.removeEventListener("focus",Q),window.removeEventListener("blur",K)}}),[y,Q,B]),!B&&V?null:o.createElement(p,(0,a.default)({onClickAway:function(e){R&&R(e,"clickaway")}},w),o.createElement("div",(0,a.default)({className:(0,u.A)(A.root,A["anchorOrigin".concat((0,g.A)(s)).concat((0,g.A)(l))],b),onMouseEnter:function(e){S&&S(e),K()},onMouseLeave:function(e){D&&D(e),Q()},ref:t},F),o.createElement(H,(0,a.default)({appear:!0,in:B,onEnter:(0,E.A)((function(){X(!1)}),T),onEntered:j,onEntering:I,onExit:N,onExited:(0,E.A)((function(){X(!0)}),O),onExiting:M,timeout:J,direction:"top"===s?"down":"up"},q),v||o.createElement(x,(0,a.default)({message:L,action:n},C)))))}));const k=(0,s.A)((function(e){var t={top:8},n={bottom:8},o={justifyContent:"flex-end"},r={justifyContent:"flex-start"},u={top:24},s={bottom:24},c={right:24},l={left:24},d={left:"50%",right:"auto",transform:"translateX(-50%)"};return{root:{zIndex:e.zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center"},anchorOriginTopCenter:(0,a.default)({},t,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},u,d))),anchorOriginBottomCenter:(0,a.default)({},n,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},s,d))),anchorOriginTopRight:(0,a.default)({},t,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},u,c))),anchorOriginBottomRight:(0,a.default)({},n,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},s,c))),anchorOriginTopLeft:(0,a.default)({},t,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},u,l))),anchorOriginBottomLeft:(0,a.default)({},n,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},s,l)))}}),{flip:!1,name:"MuiSnackbar"})(C);var y=n(30105),L=n(80575),R=n(30458),T=n(14556),j=n(32422),I=n(53815),N=n(91688),O=n(66856),M=n(84),S=n(70579);const D=e=>{let{jobId:t,title:n,open:r,onComplete:i,alertBodyMessage:a=""}=e;const u=(0,N.useHistory)(),[s,c]=(0,o.useState)(null),[l,d]=(0,o.useState)(!1),f=(0,T.wA)(),[m,v]=(0,o.useState)(n||"Job Running In Background"),[p,g]=(0,o.useState)("info"),{closeDialog:E}=(0,M.A)("backgroundJobIndicator");(0,o.useEffect)((()=>{const e=e=>(e.preventDefault(),e.returnValue="","");return t&&r?window.addEventListener("beforeunload",e):window.removeEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}}),[t,r]);const[h,A]=(0,L.A)({minDelay:1e3,maxRetries:300,onSubmit:async e=>{let{jobId:t}=e;return await f(R.zU.getJobStatus({jobId:t}))},onError:async()=>{g("error")},shouldRetry:e=>{var t,n;let{payload:o}=e;return"queued"===(null===o||void 0===o||null===(t=o.data)||void 0===t?void 0:t.status)||"dequeued"===(null===o||void 0===o||null===(n=o.data)||void 0===n?void 0:n.status)},onComplete:i||(e=>{var t,o;if("failed"===(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.status))g("error"),v("Could not complete ".concat(n));else if("complete"===(null===e||void 0===e||null===(o=e.data)||void 0===o?void 0:o.status)){const t=null===e||void 0===e?void 0:e.data;if(g("success"),v("".concat(n," complete")),null!==t&&void 0!==t&&t.result)try{const e=JSON.parse(null===t||void 0===t?void 0:t.result);null!==e&&void 0!==e&&e.chart?c(null===e||void 0===e?void 0:e.chart):v("".concat(n," complete"))}catch(r){v("".concat(n," complete"))}else v("".concat(n," complete"))}E()})});(0,o.useEffect)((()=>{t&&(g("info"),v("".concat(n)||"Job Running In Background"),d(!1),c(null),A({jobId:t}))}),[t]);return r&&(0,S.jsx)(k,{open:("info"===p&&h||"error"===p||"success"===p)&&!l,anchorOrigin:{vertical:"bottom",horizontal:"left"},autoHideDuration:"error"===p?2e3:"success"===p?5e3:null,children:(0,S.jsxs)(j.A,{onClose:()=>{d(!0)},severity:p,children:[a?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(I.A,{children:[m," "]}),a]}):(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(I.A,{children:m})}),s&&(0,S.jsx)(y.A,{size:"small",variant:"contained",color:"primary",onClick:()=>{var e;e=s,c(null),d(!0),g("info"),u.push((0,O.si)({orgId:e.organization,chartId:e.id||e._id,resource:"chart",resourceAction:"view",base:"protected"}))},children:"Visit Chart"})]})})}},53815:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(58168),r=n(80045),i=n(65043),a=n(71745),u=n(66187),s=n(43024),c=i.forwardRef((function(e,t){var n=e.classes,a=e.className,c=(0,r.A)(e,["classes","className"]);return i.createElement(u.A,(0,o.default)({gutterBottom:!0,component:"div",ref:t,className:(0,s.A)(n.root,a)},c))}));const l=(0,a.A)((function(e){return{root:{fontWeight:e.typography.fontWeightMedium,marginTop:-2}}}),{name:"MuiAlertTitle"})(c)}}]);
//# sourceMappingURL=1649.a3a6757b.chunk.js.map

// === 927.ffef0aca.chunk.js ===

"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[927],{927:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var r=n(91688),o=n(61531),a=n(84187),s=n(72835),c=n(96364),i=n(10035),l=n(86825),d=n(70579);const h=e=>{const{completeTour:t,addSteps:n,goNext:h,skipTour:u,MACRO_TOURS:p,setActiveTour:g}=e,x=(0,r.useHistory)(),m=async e=>(t({userInput:{nextSelectedStep:e},resetActiveTour:!1}),"finish"===e?(n({steps:[{...p.macro_helpBtn,actions:[{type:"back"},{type:"complete"}]}]}),h()):"themes"===e?g({tour:"themesTour",stepsToAdd:[{...p.macro_chartThemesBtn}]}):"share"===e?g({tour:"shareOptionsTour",stepsToAdd:[{...p.macro_chartShareBtn}]}):"import"===e?(x.push("/dashboard/import"),g({tour:"importsTour"})):"integration"===e?(x.push("/dashboard/integrations"),g({tour:null})):void 0);return(0,d.jsxs)(i.A,{...e,onDialogClose:u,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},children:[(0,d.jsx)("img",{src:l.t.confetti,alt:"Congratulations",style:{position:"absolute",left:"5%",height:"250px",width:"900px"}}),(0,d.jsx)(o.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,d.jsxs)(o.A,{mt:5,display:"flex",flexDirection:"column",gridGap:4,children:[(0,d.jsx)(c.A,{variant:"h1",align:"center",weight:"bold",style:{zIndex:2},children:"Basic training complete!"}),(0,d.jsx)(c.A,{align:"center",variant:"body1",style:{color:"#626262",zIndex:2},children:"You've mastered the Organimi basics. Easy, right? Now you're set to craft incredible charts independently. But there's much more to explore - discover how Organimi can elevate your charts even further."}),(0,d.jsx)(o.A,{mt:4,mb:10,alignSelf:"center",children:(0,d.jsx)(s.A,{variant:"contained",style:{width:250},color:"primary",onClick:()=>m("finish"),children:"Explore your chart!"})}),(0,d.jsx)(c.A,{variant:"body1",weight:"bold",align:"center",children:"Or, learn how to use other great features with these quick tutorials:"}),(0,d.jsxs)(o.A,{display:"flex",justifyContent:"space-evenly",gridGap:16,mt:2,children:[(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("themes"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.style,alt:"Large organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Styling your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Learn to apply preset styles or create custom themes."})]})]})}),(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("share"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.share,alt:"Small organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Sharing your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Invite others to review or collaborate on your charts."})]})]})})]})]})})]})}},10035:(e,t,n)=>{n.d(t,{A:()=>b});var r=n(57528),o=n(35007),a=n(70567),s=n(61531),c=n(35801),i=n(43867),l=n(52907),d=n(71233),h=n(5816),u=n(65043),p=n(72119);const g=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:t,dynamicAnchorDataAttribute:n},activeTourUserInput:r,skipTour:o}=e;let a=null;const s=(0,u.useRef)(!0);if(n){const e=n,t=null===r||void 0===r?void 0:r[e];a=e&&t?"".concat(e,"_").concat(t):null}const c=a||t,i=(0,u.useRef)(),l=(0,u.useRef)(0),d=c&&g(c),[h,p]=(0,u.useState)(null);return(0,u.useEffect)((()=>(i.current&&clearInterval(i.current),c&&(i.current=setInterval((()=>{l.current++;const e=c&&g(c);e&&(clearInterval(i.current),p(e||null),l.current=0),!e&&l.current>60&&s.current&&(clearInterval(i.current),s.current=!1,o({errored:!0}))}),100)),()=>{i.current&&clearInterval(i.current)})),[c]),(0,u.useEffect)((()=>()=>{i.current&&clearInterval(i.current)}),[]),d||h||null}var m,v=n(64418),f=n(70579);const y="tour-highlight-anchor",A=(0,p.Ay)(o.A)(m||(m=(0,r.A)(["\n  ","\n"])),(e=>{let{theme:t,width:n,anchorOffset:r,zIndex:o=1e4}=e;return"\n    z-index: ".concat(o,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(t.breakpoints.values[n]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),b=e=>{let{children:t,onDialogClose:n,actionsUI:r,...o}=e;const{activeTourStep:p}=o,g=(0,u.useRef)(null),m=(0,a.A)(),[b,w]=(0,u.useState)(null),j=x(o),{confirmAction:I}=(0,v.A)(),{anchorClass:S,backdrop:z,arrow:C,highlightAnchor:T,ignoreAnchorZIndex:k,zIndex:D,anchorOffset:O,backdropAboveModal:B,position:L,showDialogUI:R=!1}=p||{},V=[!0,!1].includes(z)?z:!!j,_=[!0,!1].includes(C)?C:!!j,E=[!0,!1].includes(T)?T:V&&!!j,G=L||"left-start",M=(B?m.zIndex.modal:m.zIndex.drawer)+1,P=M+1;if((0,u.useEffect)((()=>{if(j)return V&&!k&&(j.style.zIndex=(j.style.zIndex||0)+P),E&&j.classList.toggle(y),S&&j.classList.toggle(S),()=>{j&&(V&&!k&&(j.style.zIndex=j.style.zIndex-P),E&&j.classList.toggle(y),S&&j.classList.toggle(S))}}),[j]),null!==p&&void 0!==p&&p.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(s.A,{children:[R&&(0,f.jsxs)(c.A,{open:!0,onClose:()=>{n&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{n()}})},fullWidth:!(null===p||void 0===p||!p.size),maxWidth:(null===p||void 0===p?void 0:p.size)||"sm",children:[(0,f.jsx)(s.A,{zIndex:1,children:(0,f.jsx)(h.A,{densePadding:!0,onClose:n||null})}),(0,f.jsx)(i.A,{style:{padding:0},children:t}),r&&(0,f.jsx)(l.A,{style:{display:"block",padding:"24px"},children:r})]}),!R&&j&&(0,f.jsx)(d.A,{open:V,style:{zIndex:M},children:(0,f.jsxs)(A,{open:!0,ref:g,anchorEl:j,placement:G,zIndex:D,width:(null===p||void 0===p?void 0:p.size)||"sm",showBackdrop:V,anchorOffset:O,modifiers:{arrow:{enabled:_,element:b}},children:[_&&(0,f.jsx)(s.A,{className:"arrow",ref:w,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),t]})})]})}},86825:(e,t,n)=>{n.d(t,{t:()=>o});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),o={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=927.ffef0aca.chunk.js.map
ovePageBreak:r}=(0,bt.A)(),s=(0,u.d4)(mt.xt),{chartId:o}=(0,d.useParams)();return(0,ie.jsx)(ft,{roles:Object.values(n.pageBreaks).flat(),handleMove:(e,i)=>{const l=[...Object.values(n.pageBreaks).flat()],[r]=l.splice(i,1);l.splice(e,0,r),t((0,Fe.GE)({chartId:o,pageBreaks:l}))},removeAt:e=>{r({role:Object.values(n.pageBreaks).flat()[e]})},handleSelect:e=>{let{role:t}=e;l({role:t})},multiple:!0,children:(0,ie.jsx)(vt,{role:{id:i.id,name:(0,lt.mA)(i),personName:null!==(e=i.members)&&void 0!==e&&e[0]&&s[i.members[0]]?(0,lt.Bx)(s[i.members[0]]||\"\"):\"\"},index:0,anchored:!0})})},Ct=()=>{const e=(0,u.wA)(),t=(0,u.d4)(Ge.aC),{range:i,topRoles:r,pagination:s,breakBy:o,outputType:a,pageFormat:d,nLevels:c}=t,h=(0,u.d4)(Fe.kT),x=(0,u.d4)(L.G0),p={traditional:[{value:He.N0.BESTFIT,label:\"Best Fit (Beta)\"},{value:He.N0.LEVEL,label:\"By Level\"},{value:He.N0.DEPARTMENT,label:\"By Department\"},{value:He.N0.LOCATION,label:\"By Location\"},{value:He.N0.MANUAL,label:\"Manual page breaks\"}],matrix:[{value:He.N0.TEAM,label:\"By Team\"},{value:He.N0.FUNCTION,label:\"By Function\"},{value:He.N0.FUNCTIONTEAM,label:\"By Function & Team\"}]},g=i=>{e((0,Fe.L$)({...t,range:i}))};return(0,n.useEffect)((()=>{p[x].find((e=>e.value===o))||e((0,Fe.L$)({...t,breakBy:p[x][0].value}))}),[x,o]),(0,ie.jsxs)(l.A,{display:\"flex\",flexDirection:\"column\",gap:3,p:2.5,borderBottom:\"1px solid \".concat(_.Qs.Neutrals[400]),children:[(0,ie.jsxs)(l.A,{display:\"flex\",flexDirection:\"column\",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:\"flex\",justifyContent:\"space-between\",width:\"100%\",alignItems:\"center\",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:\"Print Range\"}),(0,ie.jsx)(X.A,{title:He.z7.printRange,children:(0,ie.jsx)(\"span\",{children:(0,ie.jsx)(M.gF,{icon:\"HelpSolid\",size:\"sm\",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsxs)(et.A,{variant:\"outlined\",size:\"small\",children:[(0,ie.jsx)(U.A,{fullWidth:!0,value:He.J_.FULL,onClick:()=>g(He.J_.FULL),variant:i===He.J_.FULL?\"contained\":\"outlined\",color:i===He.J_.FULL?\"primary\":\"secondaryGrey\",sx:{color:i===He.J_.FULL?\"white\":\"inherit\",fontWeight:\"normal !important\"},children:\"Entire chart\"}),(0,ie.jsx)(U.A,{fullWidth:!0,value:He.J_.ROLES,onClick:()=>g(He.J_.ROLES),variant:i===He.J_.ROLES?\"contained\":\"outlined\",color:i===He.J_.ROLES?\"primary\":\"secondaryGrey\",sx:{color:i===He.J_.ROLES?\"white\":\"inherit\",fontWeight:\"normal !important\"},children:\"Specific sections\"})]}),t.range===He.J_.ROLES&&(0,ie.jsx)(ft,{roles:r,handleMove:(i,n)=>{const l=[...r],[s]=l.splice(n,1);l.splice(i,0,s),e((0,Fe.L$)({...t,topRoles:l}))},removeAt:i=>{const n=[...r];n.splice(i,1),e((0,Fe.L$)({...t,topRoles:n}))},handleSelect:i=>{let{role:n}=i;const{id:l,members:r}=n,s=(0,lt.mA)(n);let o,a=\"\";if(r&&r.length){const[{name:e,photo:t}]=r;a=e||(0,lt.Bx)(r[0])||\"\",o=t}e((0,Fe.L$)({...t,topRoles:[{id:l,name:s,personName:a,personPhoto:o}]}))}})]}),(0,ie.jsxs)(l.A,{display:\"flex\",flexDirection:\"column\",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:\"flex\",justifyContent:\"space-between\",width:\"100%\",alignItems:\"center\",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:\"Pagination\"}),(0,ie.jsx)(X.A,{title:He.z7.pagination,children:(0,ie.jsx)(\"span\",{children:(0,ie.jsx)(M.gF,{icon:\"HelpSolid\",size:\"sm\",color:_.Qs.Neutrals[400]})})})]}),(0,ie.jsx)(tt.A,{children:(0,ie.jsxs)(Oe.gb,{value:s,onChange:i=>{return n=i.target.value,void e((0,Fe.L$)({...t,pagination:n,nLevels:n===He.ti.SINGLE?0:c||3}));var n},children:[(0,ie.jsx)(it.A,{value:He.ti.SINGLE,control:(0,ie.jsx)(Oe.ls,{size:\"small\"}),label:\"Single page\",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}},disabled:h===He._6.DIRECTORY||h===He._6.PHOTOBOARD}),(0,ie.jsx)(it.A,{value:He.ti.MULTIPLE,control:(0,ie.jsx)(Oe.ls,{size:\"small\"}),label:\"Multiple pages\",slotProps:{typography:{variant:H.Eq.bodySM,color:_.Qs.Neutrals[700]}}})]})})]}),s===He.ti.MULTIPLE&&!(h===He._6.DIRECTORY||h===He._6.PHOTOBOARD)&&(0,ie.jsxs)(l.A,{display:\"flex\",flexDirection:\"column\",gap:1.5,children:[(0,ie.jsxs)(l.A,{display:\"flex\",justifyContent:\"space-between\",width:\"100%\",alignItems:\"center\",children:[(0,ie.jsx)(O.A,{variant:H.Eq.subheadingSM,color:_.Qs.Neutrals[800],children:\"Auto-break options\"}),(0,ie.jsx)(X.A,{title:He.z7.breakBy,children:(0,ie.jsx