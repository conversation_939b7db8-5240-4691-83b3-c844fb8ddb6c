"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9620],{79620:(n,e,t)=>{t.r(e),t.d(e,{LetsStart:()=>s,default:()=>d});var r=t(61531),o=t(72835),a=t(96364),c=t(10035),l=t(86825),i=t(70579);const s=n=>{const{goNext:e,activeTourUserInput:t,skipTour:s}=n,d=!!(null!==t&&void 0!==t&&t.companyName&&null!==t&&void 0!==t&&t.jobTitle&&null!==t&&void 0!==t&&t.jobFunction&&null!==t&&void 0!==t&&t.industryType);return(0,i.jsx)(c.A,{...n,onDialogClose:()=>{d?s():e()},activeTourStep:{...(null===n||void 0===n?void 0:n.activeTourStep)||{},size:"sm",showDialogUI:!0},actionsUI:(0,i.jsx)(r.A,{display:"flex",justifyContent:"flex-end",children:(0,i.jsx)(o.A,{size:"large",color:"primary",variant:"contained",onClick:()=>{d?e({increment:2}):e()},children:"Let's go!"})}),children:(0,i.jsx)(r.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,i.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:32,children:[(0,i.jsx)("img",{width:250,height:250,src:l.t.buildFirstChart,alt:"Let's build your first chart"}),(0,i.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,i.jsx)(a.A,{variant:"h1",style:{fontWeight:"bold"},children:"Let's build your first chart."}),(0,i.jsx)(a.A,{variant:"subtitle2",style:{color:"#626262"},children:"We'll guide your through each step."})]})]})})})},d=s},10035:(n,e,t)=>{t.d(e,{A:()=>w});var r=t(57528),o=t(35007),a=t(70567),c=t(61531),l=t(35801),i=t(43867),s=t(52907),d=t(71233),u=t(5816),g=t(65043),h=t(72119);const p=n=>document.querySelector("#".concat(n,', [data-tour-anchor="').concat(n,'"]'));function x(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:e,dynamicAnchorDataAttribute:t},activeTourUserInput:r,skipTour:o}=n;let a=null;const c=(0,g.useRef)(!0);if(t){const n=t,e=null===r||void 0===r?void 0:r[n];a=n&&e?"".concat(n,"_").concat(e):null}const l=a||e,i=(0,g.useRef)(),s=(0,g.useRef)(0),d=l&&p(l),[u,h]=(0,g.useState)(null);return(0,g.useEffect)((()=>(i.current&&clearInterval(i.current),l&&(i.current=setInterval((()=>{s.current++;const n=l&&p(l);n&&(clearInterval(i.current),h(n||null),s.current=0),!n&&s.current>60&&c.current&&(clearInterval(i.current),c.current=!1,o({errored:!0}))}),100)),()=>{i.current&&clearInterval(i.current)})),[l]),(0,g.useEffect)((()=>()=>{i.current&&clearInterval(i.current)}),[]),d||u||null}var m,v=t(64418),f=t(70579);const y="tour-highlight-anchor",A=(0,h.Ay)(o.A)(m||(m=(0,r.A)(["\n  ","\n"])),(n=>{let{theme:e,width:t,anchorOffset:r,zIndex:o=1e4}=n;return"\n    z-index: ".concat(o,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(e.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),w=n=>{let{children:e,onDialogClose:t,actionsUI:r,...o}=n;const{activeTourStep:h}=o,p=(0,g.useRef)(null),m=(0,a.A)(),[w,b]=(0,g.useState)(null),I=x(o),{confirmAction:j}=(0,v.A)(),{anchorClass:z,backdrop:C,arrow:k,highlightAnchor:S,ignoreAnchorZIndex:T,zIndex:D,anchorOffset:L,backdropAboveModal:V,position:O,showDialogUI:F=!1}=h||{},U=[!0,!1].includes(C)?C:!!I,P=[!0,!1].includes(k)?k:!!I,B=[!0,!1].includes(S)?S:U&&!!I,E=O||"left-start",M=(V?m.zIndex.modal:m.zIndex.drawer)+1,R=M+1;if((0,g.useEffect)((()=>{if(I)return U&&!T&&(I.style.zIndex=(I.style.zIndex||0)+R),B&&I.classList.toggle(y),z&&I.classList.toggle(z),()=>{I&&(U&&!T&&(I.style.zIndex=I.style.zIndex-R),B&&I.classList.toggle(y),z&&I.classList.toggle(z))}}),[I]),null!==h&&void 0!==h&&h.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(c.A,{children:[F&&(0,f.jsxs)(l.A,{open:!0,onClose:()=>{t&&j({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===h||void 0===h||!h.size),maxWidth:(null===h||void 0===h?void 0:h.size)||"sm",children:[(0,f.jsx)(c.A,{zIndex:1,children:(0,f.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,f.jsx)(i.A,{style:{padding:0},children:e}),r&&(0,f.jsx)(s.A,{style:{display:"block",padding:"24px"},children:r})]}),!F&&I&&(0,f.jsx)(d.A,{open:U,style:{zIndex:M},children:(0,f.jsxs)(A,{open:!0,ref:p,anchorEl:I,placement:E,zIndex:D,width:(null===h||void 0===h?void 0:h.size)||"sm",showBackdrop:U,anchorOffset:L,modifiers:{arrow:{enabled:P,element:w}},children:[P&&(0,f.jsx)(c.A,{className:"arrow",ref:b,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),e]})})]})}},86825:(n,e,t)=>{t.d(e,{t:()=>o});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),o={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=9620.a9f36491.chunk.js.map