"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[927],{927:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var r=n(91688),o=n(61531),a=n(84187),s=n(72835),c=n(96364),i=n(10035),l=n(86825),d=n(70579);const h=e=>{const{completeTour:t,addSteps:n,goNext:h,skipTour:u,MACRO_TOURS:p,setActiveTour:g}=e,x=(0,r.useHistory)(),m=async e=>(t({userInput:{nextSelectedStep:e},resetActiveTour:!1}),"finish"===e?(n({steps:[{...p.macro_helpBtn,actions:[{type:"back"},{type:"complete"}]}]}),h()):"themes"===e?g({tour:"themesTour",stepsToAdd:[{...p.macro_chartThemesBtn}]}):"share"===e?g({tour:"shareOptionsTour",stepsToAdd:[{...p.macro_chartShareBtn}]}):"import"===e?(x.push("/dashboard/import"),g({tour:"importsTour"})):"integration"===e?(x.push("/dashboard/integrations"),g({tour:null})):void 0);return(0,d.jsxs)(i.A,{...e,onDialogClose:u,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},children:[(0,d.jsx)("img",{src:l.t.confetti,alt:"Congratulations",style:{position:"absolute",left:"5%",height:"250px",width:"900px"}}),(0,d.jsx)(o.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,d.jsxs)(o.A,{mt:5,display:"flex",flexDirection:"column",gridGap:4,children:[(0,d.jsx)(c.A,{variant:"h1",align:"center",weight:"bold",style:{zIndex:2},children:"Basic training complete!"}),(0,d.jsx)(c.A,{align:"center",variant:"body1",style:{color:"#626262",zIndex:2},children:"You've mastered the Organimi basics. Easy, right? Now you're set to craft incredible charts independently. But there's much more to explore - discover how Organimi can elevate your charts even further."}),(0,d.jsx)(o.A,{mt:4,mb:10,alignSelf:"center",children:(0,d.jsx)(s.A,{variant:"contained",style:{width:250},color:"primary",onClick:()=>m("finish"),children:"Explore your chart!"})}),(0,d.jsx)(c.A,{variant:"body1",weight:"bold",align:"center",children:"Or, learn how to use other great features with these quick tutorials:"}),(0,d.jsxs)(o.A,{display:"flex",justifyContent:"space-evenly",gridGap:16,mt:2,children:[(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("themes"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.style,alt:"Large organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Styling your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Learn to apply preset styles or create custom themes."})]})]})}),(0,d.jsx)(a.A,{style:{flex:1},onClick:()=>m("share"),children:(0,d.jsxs)(o.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",gridGap:16,alignItems:"center",children:[(0,d.jsx)(o.A,{mb:1,children:(0,d.jsx)("img",{height:60,src:l.t.share,alt:"Small organizations"})}),(0,d.jsxs)(o.A,{display:"flex",flexDirection:"column",children:[(0,d.jsx)(c.A,{variant:"body1",style:{fontWeight:"bold"},children:"Sharing your chart"}),(0,d.jsx)(c.A,{variant:"body2",color:"textSecondary",children:"Invite others to review or collaborate on your charts."})]})]})})]})]})})]})}},10035:(e,t,n)=>{n.d(t,{A:()=>b});var r=n(57528),o=n(35007),a=n(70567),s=n(61531),c=n(35801),i=n(43867),l=n(52907),d=n(71233),h=n(5816),u=n(65043),p=n(72119);const g=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:t,dynamicAnchorDataAttribute:n},activeTourUserInput:r,skipTour:o}=e;let a=null;const s=(0,u.useRef)(!0);if(n){const e=n,t=null===r||void 0===r?void 0:r[e];a=e&&t?"".concat(e,"_").concat(t):null}const c=a||t,i=(0,u.useRef)(),l=(0,u.useRef)(0),d=c&&g(c),[h,p]=(0,u.useState)(null);return(0,u.useEffect)((()=>(i.current&&clearInterval(i.current),c&&(i.current=setInterval((()=>{l.current++;const e=c&&g(c);e&&(clearInterval(i.current),p(e||null),l.current=0),!e&&l.current>60&&s.current&&(clearInterval(i.current),s.current=!1,o({errored:!0}))}),100)),()=>{i.current&&clearInterval(i.current)})),[c]),(0,u.useEffect)((()=>()=>{i.current&&clearInterval(i.current)}),[]),d||h||null}var m,v=n(64418),f=n(70579);const y="tour-highlight-anchor",A=(0,p.Ay)(o.A)(m||(m=(0,r.A)(["\n  ","\n"])),(e=>{let{theme:t,width:n,anchorOffset:r,zIndex:o=1e4}=e;return"\n    z-index: ".concat(o,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(t.breakpoints.values[n]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),b=e=>{let{children:t,onDialogClose:n,actionsUI:r,...o}=e;const{activeTourStep:p}=o,g=(0,u.useRef)(null),m=(0,a.A)(),[b,w]=(0,u.useState)(null),j=x(o),{confirmAction:I}=(0,v.A)(),{anchorClass:S,backdrop:z,arrow:C,highlightAnchor:T,ignoreAnchorZIndex:k,zIndex:D,anchorOffset:O,backdropAboveModal:B,position:L,showDialogUI:R=!1}=p||{},V=[!0,!1].includes(z)?z:!!j,_=[!0,!1].includes(C)?C:!!j,E=[!0,!1].includes(T)?T:V&&!!j,G=L||"left-start",M=(B?m.zIndex.modal:m.zIndex.drawer)+1,P=M+1;if((0,u.useEffect)((()=>{if(j)return V&&!k&&(j.style.zIndex=(j.style.zIndex||0)+P),E&&j.classList.toggle(y),S&&j.classList.toggle(S),()=>{j&&(V&&!k&&(j.style.zIndex=j.style.zIndex-P),E&&j.classList.toggle(y),S&&j.classList.toggle(S))}}),[j]),null!==p&&void 0!==p&&p.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(s.A,{children:[R&&(0,f.jsxs)(c.A,{open:!0,onClose:()=>{n&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{n()}})},fullWidth:!(null===p||void 0===p||!p.size),maxWidth:(null===p||void 0===p?void 0:p.size)||"sm",children:[(0,f.jsx)(s.A,{zIndex:1,children:(0,f.jsx)(h.A,{densePadding:!0,onClose:n||null})}),(0,f.jsx)(i.A,{style:{padding:0},children:t}),r&&(0,f.jsx)(l.A,{style:{display:"block",padding:"24px"},children:r})]}),!R&&j&&(0,f.jsx)(d.A,{open:V,style:{zIndex:M},children:(0,f.jsxs)(A,{open:!0,ref:g,anchorEl:j,placement:G,zIndex:D,width:(null===p||void 0===p?void 0:p.size)||"sm",showBackdrop:V,anchorOffset:O,modifiers:{arrow:{enabled:_,element:b}},children:[_&&(0,f.jsx)(s.A,{className:"arrow",ref:w,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),t]})})]})}},86825:(e,t,n)=>{n.d(t,{t:()=>o});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),o={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=927.ffef0aca.chunk.js.map