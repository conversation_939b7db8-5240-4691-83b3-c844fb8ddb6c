"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[1649],{1649:(e,t,n)=>{n.r(t),n.d(t,{default:()=>D});var o=n(65043),r=n(80045),i=n(64467),a=n(58168),u=n(43024),s=n(71745),c=n(12899),l=n(97950),d=n(79892),f=n(60768),m=n(32158);function v(e){return e.substring(2).toLowerCase()}const p=function(e){var t=e.children,n=e.disableReactTree,r=void 0!==n&&n,i=e.mouseEvent,a=void 0===i?"onClick":i,u=e.onClickAway,s=e.touchEvent,c=void 0===s?"onTouchEnd":s,p=o.useRef(!1),g=o.useRef(null),E=o.useRef(!1),h=o.useRef(!1);o.useEffect((function(){return setTimeout((function(){E.current=!0}),0),function(){E.current=!1}}),[]);var A=o.useCallback((function(e){g.current=l.findDOMNode(e)}),[]),b=(0,f.A)(t.ref,A),w=(0,m.A)((function(e){var t=h.current;if(h.current=!1,E.current&&g.current&&!function(e){return document.documentElement.clientWidth<e.clientX||document.documentElement.clientHeight<e.clientY}(e))if(p.current)p.current=!1;else{var n;if(e.composedPath)n=e.composedPath().indexOf(g.current)>-1;else n=!(0,d.A)(g.current).documentElement.contains(e.target)||g.current.contains(e.target);n||!r&&t||u(e)}})),x=function(e){return function(n){h.current=!0;var o=t.props[e];o&&o(n)}},C={ref:b};return!1!==c&&(C[c]=x(c)),o.useEffect((function(){if(!1!==c){var e=v(c),t=(0,d.A)(g.current),n=function(){p.current=!0};return t.addEventListener(e,w),t.addEventListener("touchmove",n),function(){t.removeEventListener(e,w),t.removeEventListener("touchmove",n)}}}),[w,c]),!1!==a&&(C[a]=x(a)),o.useEffect((function(){if(!1!==a){var e=v(a),t=(0,d.A)(g.current);return t.addEventListener(e,w),function(){t.removeEventListener(e,w)}}}),[w,a]),o.createElement(o.Fragment,null,o.cloneElement(t,C))};var g=n(74822),E=n(146),h=n(51575),A=n(20495),b=n(82454),w=o.forwardRef((function(e,t){var n=e.action,i=e.classes,s=e.className,c=e.message,l=e.role,d=void 0===l?"alert":l,f=(0,r.A)(e,["action","classes","className","message","role"]);return o.createElement(A.A,(0,a.default)({role:d,square:!0,elevation:6,className:(0,u.A)(i.root,s),ref:t},f),o.createElement("div",{className:i.message},c),n?o.createElement("div",{className:i.action},n):null)}));const x=(0,s.A)((function(e){var t="light"===e.palette.type?.8:.98,n=(0,b.tL)(e.palette.background.default,t);return{root:(0,a.default)({},e.typography.body2,(0,i.A)({color:e.palette.getContrastText(n),backgroundColor:n,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:e.shape.borderRadius,flexGrow:1},e.breakpoints.up("sm"),{flexGrow:"initial",minWidth:288})),message:{padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}}}),{name:"MuiSnackbarContent"})(w);var C=o.forwardRef((function(e,t){var n=e.action,i=e.anchorOrigin,s=(i=void 0===i?{vertical:"bottom",horizontal:"center"}:i).vertical,l=i.horizontal,d=e.autoHideDuration,f=void 0===d?null:d,v=e.children,A=e.classes,b=e.className,w=e.ClickAwayListenerProps,C=e.ContentProps,k=e.disableWindowBlurListener,y=void 0!==k&&k,L=e.message,R=e.onClose,T=e.onEnter,j=e.onEntered,I=e.onEntering,N=e.onExit,O=e.onExited,M=e.onExiting,S=e.onMouseEnter,D=e.onMouseLeave,B=e.open,z=e.resumeHideDuration,P=e.TransitionComponent,H=void 0===P?h.A:P,W=e.transitionDuration,J=void 0===W?{enter:c.p0.enteringScreen,exit:c.p0.leavingScreen}:W,q=e.TransitionProps,F=(0,r.A)(e,["action","anchorOrigin","autoHideDuration","children","classes","className","ClickAwayListenerProps","ContentProps","disableWindowBlurListener","message","onClose","onEnter","onEntered","onEntering","onExit","onExited","onExiting","onMouseEnter","onMouseLeave","open","resumeHideDuration","TransitionComponent","transitionDuration","TransitionProps"]),_=o.useRef(),G=o.useState(!0),V=G[0],X=G[1],U=(0,m.A)((function(){R&&R.apply(void 0,arguments)})),Y=(0,m.A)((function(e){R&&null!=e&&(clearTimeout(_.current),_.current=setTimeout((function(){U(null,"timeout")}),e))}));o.useEffect((function(){return B&&Y(f),function(){clearTimeout(_.current)}}),[B,f,Y]);var K=function(){clearTimeout(_.current)},Q=o.useCallback((function(){null!=f&&Y(null!=z?z:.5*f)}),[f,z,Y]);return o.useEffect((function(){if(!y&&B)return window.addEventListener("focus",Q),window.addEventListener("blur",K),function(){window.removeEventListener("focus",Q),window.removeEventListener("blur",K)}}),[y,Q,B]),!B&&V?null:o.createElement(p,(0,a.default)({onClickAway:function(e){R&&R(e,"clickaway")}},w),o.createElement("div",(0,a.default)({className:(0,u.A)(A.root,A["anchorOrigin".concat((0,g.A)(s)).concat((0,g.A)(l))],b),onMouseEnter:function(e){S&&S(e),K()},onMouseLeave:function(e){D&&D(e),Q()},ref:t},F),o.createElement(H,(0,a.default)({appear:!0,in:B,onEnter:(0,E.A)((function(){X(!1)}),T),onEntered:j,onEntering:I,onExit:N,onExited:(0,E.A)((function(){X(!0)}),O),onExiting:M,timeout:J,direction:"top"===s?"down":"up"},q),v||o.createElement(x,(0,a.default)({message:L,action:n},C)))))}));const k=(0,s.A)((function(e){var t={top:8},n={bottom:8},o={justifyContent:"flex-end"},r={justifyContent:"flex-start"},u={top:24},s={bottom:24},c={right:24},l={left:24},d={left:"50%",right:"auto",transform:"translateX(-50%)"};return{root:{zIndex:e.zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center"},anchorOriginTopCenter:(0,a.default)({},t,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},u,d))),anchorOriginBottomCenter:(0,a.default)({},n,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({},s,d))),anchorOriginTopRight:(0,a.default)({},t,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},u,c))),anchorOriginBottomRight:(0,a.default)({},n,o,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({left:"auto"},s,c))),anchorOriginTopLeft:(0,a.default)({},t,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},u,l))),anchorOriginBottomLeft:(0,a.default)({},n,r,(0,i.A)({},e.breakpoints.up("sm"),(0,a.default)({right:"auto"},s,l)))}}),{flip:!1,name:"MuiSnackbar"})(C);var y=n(30105),L=n(80575),R=n(30458),T=n(14556),j=n(32422),I=n(53815),N=n(91688),O=n(66856),M=n(84),S=n(70579);const D=e=>{let{jobId:t,title:n,open:r,onComplete:i,alertBodyMessage:a=""}=e;const u=(0,N.useHistory)(),[s,c]=(0,o.useState)(null),[l,d]=(0,o.useState)(!1),f=(0,T.wA)(),[m,v]=(0,o.useState)(n||"Job Running In Background"),[p,g]=(0,o.useState)("info"),{closeDialog:E}=(0,M.A)("backgroundJobIndicator");(0,o.useEffect)((()=>{const e=e=>(e.preventDefault(),e.returnValue="","");return t&&r?window.addEventListener("beforeunload",e):window.removeEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}}),[t,r]);const[h,A]=(0,L.A)({minDelay:1e3,maxRetries:300,onSubmit:async e=>{let{jobId:t}=e;return await f(R.zU.getJobStatus({jobId:t}))},onError:async()=>{g("error")},shouldRetry:e=>{var t,n;let{payload:o}=e;return"queued"===(null===o||void 0===o||null===(t=o.data)||void 0===t?void 0:t.status)||"dequeued"===(null===o||void 0===o||null===(n=o.data)||void 0===n?void 0:n.status)},onComplete:i||(e=>{var t,o;if("failed"===(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.status))g("error"),v("Could not complete ".concat(n));else if("complete"===(null===e||void 0===e||null===(o=e.data)||void 0===o?void 0:o.status)){const t=null===e||void 0===e?void 0:e.data;if(g("success"),v("".concat(n," complete")),null!==t&&void 0!==t&&t.result)try{const e=JSON.parse(null===t||void 0===t?void 0:t.result);null!==e&&void 0!==e&&e.chart?c(null===e||void 0===e?void 0:e.chart):v("".concat(n," complete"))}catch(r){v("".concat(n," complete"))}else v("".concat(n," complete"))}E()})});(0,o.useEffect)((()=>{t&&(g("info"),v("".concat(n)||"Job Running In Background"),d(!1),c(null),A({jobId:t}))}),[t]);return r&&(0,S.jsx)(k,{open:("info"===p&&h||"error"===p||"success"===p)&&!l,anchorOrigin:{vertical:"bottom",horizontal:"left"},autoHideDuration:"error"===p?2e3:"success"===p?5e3:null,children:(0,S.jsxs)(j.A,{onClose:()=>{d(!0)},severity:p,children:[a?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(I.A,{children:[m," "]}),a]}):(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(I.A,{children:m})}),s&&(0,S.jsx)(y.A,{size:"small",variant:"contained",color:"primary",onClick:()=>{var e;e=s,c(null),d(!0),g("info"),u.push((0,O.si)({orgId:e.organization,chartId:e.id||e._id,resource:"chart",resourceAction:"view",base:"protected"}))},children:"Visit Chart"})]})})}},53815:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(58168),r=n(80045),i=n(65043),a=n(71745),u=n(66187),s=n(43024),c=i.forwardRef((function(e,t){var n=e.classes,a=e.className,c=(0,r.A)(e,["classes","className"]);return i.createElement(u.A,(0,o.default)({gutterBottom:!0,component:"div",ref:t,className:(0,s.A)(n.root,a)},c))}));const l=(0,a.A)((function(e){return{root:{fontWeight:e.typography.fontWeightMedium,marginTop:-2}}}),{name:"MuiAlertTitle"})(c)}}]);
//# sourceMappingURL=1649.a3a6757b.chunk.js.map