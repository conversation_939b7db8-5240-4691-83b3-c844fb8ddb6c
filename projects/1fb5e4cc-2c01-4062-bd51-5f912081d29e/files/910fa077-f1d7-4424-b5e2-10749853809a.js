"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9895],{49895:(e,n,t)=>{t.r(n),t.d(n,{INDUSTRIES:()=>I,ROLES:()=>A,UserDetailsForm:()=>w,default:()=>T});var l,r=t(57528),o=t(73083),a=t(61531),i=t(9579),c=t(51481),s=t(87958),d=t(72119),u=t(72835),p=t(96364),g=t(61258),h=t(84866),x=t(75156),m=t(10035),v=t(86825),y=t(43331),f=t(14556),b=t(70579);const j=(0,d.Ay)(o.A)(l||(l=(0,r.A)(["\n  ","\n"])),(e=>"\n    position: relative;\n    display: flex;\n    align-items: center;\n    border: ".concat(null!==e&&void 0!==e&&e.highlight?2:1,"px solid ").concat(null!==e&&void 0!==e&&e.highlight?e.theme.palette.secondary.main:"#c4c4c4",';\n    border-radius: 6px;\n    height: 60px;\n    justify-content: flex-start;\n    gap: 8px;\n    padding: 0px 32px 0px 8px;\n    flex: 1;\n    margin: 0;\n    min-width: 170px;\n\n    input[type="radio"]:checked {\n      font-weight: bold;\n    }\n\n    .MuiRadio-root {\n      padding: 0;\n    }\n  '))),A=[{label:"HR & Operations",value:"HR & Operations"},{label:"Consultant",value:"Consultant"},{label:"Recruiter",value:"Recruiter"},{label:"Assistant",value:"Assistant"},{label:"C-suite",value:"C Suite"},{label:"Student",value:"Student"},{label:"Sales",value:"Sales"},{label:"IT",value:"IT"}],I=[{label:"Non Profit",value:"Non Profit"},{label:"Government",value:"Government"},{label:"Education",value:"Education"},{label:"Business",value:"Business"}],w=e=>{var n,t,l,r;const{goNext:o,userDetails:d,activeTourUserInput:w,activeOrg:T,activeTourStatus:C}=e,z=(0,f.wA)(),{control:S,getValues:D,errors:k,handleSubmit:R,register:W}=(0,g.mN)({defaultValues:{companyName:w.companyName||null,jobTitle:w.jobTitle||null,jobFunction:w.jobFunction||null,industryType:w.industryType||null},mode:"all",shouldFocusError:!0});return(0,b.jsx)(m.A,{...e,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,b.jsx)(a.A,{mt:2,display:"flex",justifyContent:"flex-end",children:(0,b.jsx)(u.A,{size:"large",color:"primary",variant:"contained",onClick:R((async()=>{const{companyName:e,jobTitle:n,jobFunction:t,industryType:l}=D()||{};"My Organization"===(null===T||void 0===T?void 0:T.name)&&d.canWriteAll&&"pending"===C&&await z(y.UY.update({orgId:T.id,data:{name:e},ignoreErrorHandler:!0})),o({userInput:{companyName:e,jobTitle:n,jobFunction:t,industryType:l}})})),children:"Continue"})}),children:(0,b.jsx)(a.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:28,children:[(0,b.jsxs)(a.A,{mb:1,display:"flex",flexDirection:"column",gridGap:8,children:[(0,b.jsx)(a.A,{mb:2,children:(0,b.jsx)("img",{width:80,height:80,src:v.t.welcome,alt:"Let's build your first chart"})}),(0,b.jsxs)(p.A,{variant:"h1",style:{fontWeight:"bold"},children:["Welcome, ",null===d||void 0===d?void 0:d.firstName,"."]}),(0,b.jsx)(p.A,{variant:"subtitle2",style:{color:"#626262"},children:"Tell us a bit about you so we can customize your experience."})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:4,children:[(0,b.jsxs)(a.A,{display:"flex",gridGap:8,alignItems:"center",children:[(0,b.jsx)(p.A,{variant:"body1",style:{fontWeight:"bold"},children:"What's your organization name?"}),(0,b.jsx)(i.Ay,{title:"HelperText",placement:"bottom",children:(0,b.jsx)(x.Ay,{icon:"Info",size:"lg",color:"#5c5c5c"})})]}),(0,b.jsx)(g.xI,{name:"companyName",control:S,render:e=>{var n;let{name:t,onChange:l,value:r,onBlur:o}=e;return(0,b.jsx)(h.A,{name:t,value:r||null,onChange:l,onBlur:o,fullWidth:!0,autoFocus:!0,error:!(null===k||void 0===k||!k.companyName),helperText:null===k||void 0===k||null===(n=k.companyName)||void 0===n?void 0:n.message,inputRef:W({required:"* Organization name is required"})})}})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:4,children:[(0,b.jsxs)(a.A,{display:"flex",gridGap:8,alignItems:"center",children:[(0,b.jsx)(p.A,{variant:"body1",style:{fontWeight:"bold"},children:"What's your job title?"}),(0,b.jsx)(i.Ay,{title:"HelperText",placement:"bottom",children:(0,b.jsx)(x.Ay,{icon:"Info",size:"lg",color:"#5c5c5c"})})]}),(0,b.jsx)(g.xI,{name:"jobTitle",control:S,render:e=>{var n;let{name:t,onChange:l,value:r,onBlur:o}=e;return(0,b.jsx)(h.A,{name:t,value:r||null,onChange:l,onBlur:o,fullWidth:!0,error:!(null===k||void 0===k||!k.jobTitle),helperText:null===k||void 0===k||null===(n=k.jobTitle)||void 0===n?void 0:n.message,inputRef:W({required:"* Job itle is required"})})}})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:16,children:[(0,b.jsxs)(a.A,{display:"flex",gridGap:8,alignItems:"center",children:[(0,b.jsx)(p.A,{variant:"body1",style:{fontWeight:"bold"},children:"What best describes your job function?"}),!(null===k||void 0===k||null===(n=k.jobFunction)||void 0===n||!n.message)&&(0,b.jsxs)(p.A,{variant:"caption",color:"error",children:["* ",null===k||void 0===k||null===(t=k.jobFunction)||void 0===t?void 0:t.message]}),(0,b.jsx)(a.A,{children:(0,b.jsx)(i.Ay,{title:"HelperText",placement:"bottom",children:(0,b.jsx)(x.Ay,{icon:"Info",size:"lg",color:"#5c5c5c"})})})]}),(0,b.jsx)(g.xI,{name:"jobFunction",control:S,render:e=>{let{name:n,onChange:t,value:l,onBlur:r}=e;return(0,b.jsx)(c.A,{name:n,row:!0,value:l,onChange:t,onBlur:r,style:{gap:"16px"},children:A.map((e=>(0,b.jsx)(j,{highlight:l===e.value,value:e.value,control:(0,b.jsx)(s.A,{color:"secondary",inputRef:W({required:"Selection required"})}),label:e.label})))})}})]}),(0,b.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:16,children:[(0,b.jsxs)(a.A,{display:"flex",gridGap:8,alignItems:"center",children:[(0,b.jsx)(p.A,{variant:"body1",style:{fontWeight:"bold"},children:"What industry do you work in?"}),!(null===k||void 0===k||null===(l=k.industryType)||void 0===l||!l.message)&&(0,b.jsxs)(p.A,{variant:"caption",color:"error",children:["* ",null===k||void 0===k||null===(r=k.industryType)||void 0===r?void 0:r.message]}),(0,b.jsx)(a.A,{children:(0,b.jsx)(i.Ay,{title:"HelperText",placement:"bottom",children:(0,b.jsx)(x.Ay,{icon:"Info",size:"lg",color:"#5c5c5c"})})})]}),(0,b.jsx)(g.xI,{name:"industryType",control:S,render:e=>{let{name:n,onChange:t,value:l,onBlur:r}=e;return(0,b.jsx)(c.A,{name:n,row:!0,value:l,onChange:t,onBlur:r,style:{gap:"16px"},required:!0,inputRef:W({required:"Selection required"}),children:I.map((e=>(0,b.jsx)(j,{highlight:l===e.value,value:e.value,control:(0,b.jsx)(s.A,{color:"secondary",inputRef:W({required:"Selection required"})}),label:e.label})))})}})]})]})})})},T=w},10035:(e,n,t)=>{t.d(n,{A:()=>j});var l=t(57528),r=t(35007),o=t(70567),a=t(61531),i=t(35801),c=t(43867),s=t(52907),d=t(71233),u=t(5816),p=t(65043),g=t(72119);const h=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:l,skipTour:r}=e;let o=null;const a=(0,p.useRef)(!0);if(t){const e=t,n=null===l||void 0===l?void 0:l[e];o=e&&n?"".concat(e,"_").concat(n):null}const i=o||n,c=(0,p.useRef)(),s=(0,p.useRef)(0),d=i&&h(i),[u,g]=(0,p.useState)(null);return(0,p.useEffect)((()=>(c.current&&clearInterval(c.current),i&&(c.current=setInterval((()=>{s.current++;const e=i&&h(i);e&&(clearInterval(c.current),g(e||null),s.current=0),!e&&s.current>60&&a.current&&(clearInterval(c.current),a.current=!1,r({errored:!0}))}),100)),()=>{c.current&&clearInterval(c.current)})),[i]),(0,p.useEffect)((()=>()=>{c.current&&clearInterval(c.current)}),[]),d||u||null}var m,v=t(64418),y=t(70579);const f="tour-highlight-anchor",b=(0,g.Ay)(r.A)(m||(m=(0,l.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:l,zIndex:r=1e4}=e;return"\n    z-index: ".concat(r,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(l||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(l||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(l||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(l||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(l||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),j=e=>{let{children:n,onDialogClose:t,actionsUI:l,...r}=e;const{activeTourStep:g}=r,h=(0,p.useRef)(null),m=(0,o.A)(),[j,A]=(0,p.useState)(null),I=x(r),{confirmAction:w}=(0,v.A)(),{anchorClass:T,backdrop:C,arrow:z,highlightAnchor:S,ignoreAnchorZIndex:D,zIndex:k,anchorOffset:R,backdropAboveModal:W,position:B,showDialogUI:F=!1}=g||{},N=[!0,!1].includes(C)?C:!!I,G=[!0,!1].includes(z)?z:!!I,O=[!0,!1].includes(S)?S:N&&!!I,q=B||"left-start",E=(W?m.zIndex.modal:m.zIndex.drawer)+1,V=E+1;if((0,p.useEffect)((()=>{if(I)return N&&!D&&(I.style.zIndex=(I.style.zIndex||0)+V),O&&I.classList.toggle(f),T&&I.classList.toggle(T),()=>{I&&(N&&!D&&(I.style.zIndex=I.style.zIndex-V),O&&I.classList.toggle(f),T&&I.classList.toggle(T))}}),[I]),null!==g&&void 0!==g&&g.hiddenStep)return(0,y.jsx)(y.Fragment,{});return(0,y.jsxs)(a.A,{children:[F&&(0,y.jsxs)(i.A,{open:!0,onClose:()=>{t&&w({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===g||void 0===g||!g.size),maxWidth:(null===g||void 0===g?void 0:g.size)||"sm",children:[(0,y.jsx)(a.A,{zIndex:1,children:(0,y.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,y.jsx)(c.A,{style:{padding:0},children:n}),l&&(0,y.jsx)(s.A,{style:{display:"block",padding:"24px"},children:l})]}),!F&&I&&(0,y.jsx)(d.A,{open:N,style:{zIndex:E},children:(0,y.jsxs)(b,{open:!0,ref:h,anchorEl:I,placement:q,zIndex:k,width:(null===g||void 0===g?void 0:g.size)||"sm",showBackdrop:N,anchorOffset:R,modifiers:{arrow:{enabled:G,element:j}},children:[G&&(0,y.jsx)(a.A,{className:"arrow",ref:A,children:(0,y.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,y.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}},86825:(e,n,t)=>{t.d(n,{t:()=>r});const l="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),r={buildFirstChart:"".concat(l,"/buildFirstChart.svg"),fromScratch:"".concat(l,"/fromScratch.svg"),largeOrgs:"".concat(l,"/largeOrgs.svg"),smallOrgs:"".concat(l,"/smallOrgs.svg"),realBrands:"".concat(l,"/realBrands.svg"),roleVsPeople:"".concat(l,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(l,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(l,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(l,"/import.svg"),addManually:"".concat(l,"/addManually.svg"),integrate:"".concat(l,"/integrate.svg"),welcome:"".concat(l,"/welcome.svg"),confetti:"".concat(l,"/confetti.png"),style:"".concat(l,"/style.svg"),integrate2:"".concat(l,"/integrate2.svg"),import2:"".concat(l,"/import2.svg"),share:"".concat(l,"/share.svg"),cards:"".concat(l,"/cards.svg"),hierarchy:"".concat(l,"/hierarchy.svg"),navigatingChart:"".concat(l,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=9895.9e76bece.chunk.js.map