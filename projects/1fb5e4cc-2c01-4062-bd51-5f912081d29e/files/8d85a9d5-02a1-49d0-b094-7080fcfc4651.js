"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[5862],{55862:(e,n,t)=>{t.r(n),t.d(n,{AddPeople:()=>W,default:()=>M});var r,i=t(57528),o=t(87603),l=t(28269),a=t(77325),s=t(61531),d=t(72835),c=t(96364),u=t(10035),p=t(50330),h=t(14556),x=t(7743),m=t(61258),f=t(3342),g=t(10621),y=t(70512),A=t(45418),v=t(72119),j=t(46392),w=t(176),b=t(80539),I=t(2173),C=t(84091),S=t(24115),D=t(66588),T=t(65043),k=t(66856),z=t(91688),E=t(70669),F=t(75156),R=t(64418),L=t(91357),P=t(70579);const G=(0,v.Ay)(o.A)(r||(r=(0,i.A)(["\n  height: 58px;\n\n  border-left: 0;\n  border-right: 0;\n\n  border-bottom: 1px solid #d9d9d9;\n  padding: 12px 0;\n"]))),W=e=>{const{goBack:n,skipTour:t,activeOrg:r,completeTour:i,setActiveTour:o,activeTourUserInput:v,removeStep:W,activeTourStepIndex:M}=e,O=null===r||void 0===r?void 0:r.id,U=(0,h.wA)(),{show:B}=(0,D.A)(),N=(0,z.useHistory)(),V=(0,h.d4)(A.wr),H=(0,h.d4)(x.gJ),_=(0,h.d4)(x.gP),{confirmAction:Y}=(0,R.A)(),q=H[g.x2.FIRSTNAME],Z=null===q||void 0===q?void 0:q.id,J=H[g.x2.LASTNAME],$=null===J||void 0===J?void 0:J.id,K=H[g.x2.EMAIL],Q=null===K||void 0===K?void 0:K.id,X=H[g.x2.PHONE],ee=null===X||void 0===X?void 0:X.id,ne=[[q,J],[K,X]],te=(0,m.mN)({defaultValues:{[Z]:"",[$]:"",[Q]:"",[ee]:"",dataChanged:!1}}),{register:re,setValue:ie,setError:oe,clearErrors:le,formState:{errors:ae},reset:se,handleSubmit:de,getValues:ce}=te,[ue,pe]=(0,I.A)((async()=>{await U(C.OH.getPeople({orgId:O}))}));(0,T.useEffect)((()=>{pe()}),[O]);const he=e=>{let{index:n,isScrolling:t,isVisible:r,key:i,style:o}=e;if(!V[n])return null;if(t&&!r)return(0,P.jsx)("div",{style:o,"data-tour-anchor":"talent-pool-person-card",children:(0,P.jsx)(j.A,{ListItemEl:G})},i);const d=V[n],u=(0,g.II)(d),p=(0,g.US)(d),h=(0,g.zY)(d);return(0,P.jsx)("div",{style:o,children:(0,P.jsxs)(G,{ContainerComponent:"div",isFirst:0===n,children:[(0,P.jsx)(l.A,{style:{display:"contents",height:"100%"},children:(0,P.jsx)(P.Fragment,{children:(0,P.jsx)(b.A,{overrideColor:d.memberPhotoColor,name:"".concat(u," ").concat(p),width:26,height:26})})}),(0,P.jsxs)(a.A,{style:{padding:"8px",margin:0,marginLeft:"8px"},children:[(0,P.jsx)(c.A,{variant:"body2",weight:"medium",noWrap:!0,color:"#000",children:"".concat(u," ").concat(p)}),(0,P.jsx)(s.A,{width:"90%",children:(0,P.jsx)(c.A,{variant:"body2",noWrap:!0,color:"#999",children:h})})]})]})},i)},xe=()=>ue?[1,2,3,4,5,6,7,8,9,0].map((()=>!V.length&&(0,P.jsx)(j.A,{ListItemEl:G}))):(e=>{let{title:n,message:t}=e;return(0,P.jsxs)(s.A,{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",flexDirection:"column",gridGap:12,children:[(0,P.jsx)(c.A,{variant:"h3",children:n}),(0,P.jsx)(s.A,{mt:1,children:(0,P.jsx)(c.A,{align:"center",children:t})})]})})({title:"No people in your roster",message:"Add people to your roster to start building your chart."}),me=(e,n)=>t=>{ie("dataChanged",!0);!f.A.MemberFieldValidators[e.name]||f.A.MemberFieldValidators[e.name](t)?le(n):oe(n,{message:f.A.ERROR_MESSAGES[e.name],shouldFocus:!0})},fe=e=>e.id===Z?re({required:!0}):e.id===Q?re({pattern:y.eT}):re,[ge,ye]=(0,I.A)((e=>de((async(e,n)=>{await(async e=>{const n=(0,g.YW)(e,_),{error:t}=await U(C.OH.addPerson({orgId:O,data:{...n},forceDuplicate:!0}));t||(se(),B("Person added successfully","success",2e3))})(e),"function"===typeof n&&await n()}))(e)));return(0,P.jsx)(u.A,{...e,onDialogClose:t,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,P.jsx)(s.A,{display:"flex",justifyContent:"flex-end",children:(0,P.jsx)(d.A,{size:"medium",color:"primary",variant:"contained",onClick:()=>{const e=ce(Z),n=ce($),t=ce(Q),r=ce(ee),l=()=>{i({userInput:{peopleCount:V.length},resetActiveTour:!1}),null!==v&&void 0!==v&&v.chartId&&(setTimeout((()=>o({tour:"chartTour"}))),N.push((0,k.Wx)({base:"protected",chartId:null===v||void 0===v?void 0:v.chartId,orgId:O})),U((0,E.tg)()))};if(!V.length)return B("Please add at least one person","info");e&&n?Y({title:"Unsaved Changes",message:"It appears you have edited data in the person form. How do you want to continue?",cancelButtonText:"Save and add more",confirmButtonText:"Save and continue",cancelFunc:async()=>{await ye()},execFunc:async()=>{await ye(l)}}):e||n||t||r?Y({title:"Unsaved Changes",message:"It appears you have edited data in the person form. Do you want to continue without saving the person?",cancelButtonText:"Continue editing",confirmButtonText:"Skip adding person",execFunc:l}):l()},disabled:ge,children:"Continue"})}),children:(0,P.jsx)(s.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,P.jsxs)(s.A,{display:"flex",flexDirection:"column",gridGap:32,children:[(0,P.jsx)(c.A,{variant:"h1",style:{fontWeight:"bold"},children:"Add People"}),(0,P.jsxs)(s.A,{display:"flex",flexDirection:"column",gridGap:16,children:[(0,P.jsxs)(s.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,P.jsx)(c.A,{variant:"h4",style:{fontWeight:"bold"},children:"Manual Entry"}),(0,P.jsxs)(d.A,{variant:"outlined",onClick:async()=>{await W({stepIndex:M}),n()},size:"small",color:"primary",children:[(0,P.jsx)(F.Ay,{icon:"ArrowLeft"})," \xa0 Choose a different method"]})]}),(0,P.jsx)(c.A,{variant:"body1",style:{color:"#626262"},children:'First and last names are mandatory for every person - all other fields are optional, and you can add more fields specific to your people from the "People" tab on your dashboard.'})]}),(0,P.jsxs)(s.A,{display:"flex",justifyContent:"space-between",gridGap:50,children:[(0,P.jsx)(s.A,{flex:1,height:"450px",display:"flex",flexDirection:"column",justifyContent:"space-between",children:(0,P.jsxs)(s.A,{display:"flex",flexDirection:"column",gridGap:"28px",children:[(0,P.jsx)(c.A,{variant:"h4",style:{fontWeight:"bold"},children:"New Person"}),(0,P.jsx)(m.Op,{...te,children:(0,P.jsx)(S.A,{transparent:!0,loading:ge,children:(0,P.jsxs)("form",{id:"user-tour-member-form",style:{width:"100%"},onSubmit:ye,children:[(0,P.jsx)(s.A,{display:"flex",flexDirection:"column",gridGap:8,children:ne.map((e=>(0,P.jsx)(s.A,{display:"flex",gridGap:"12px",flexDirection:"row",justifyContent:"space-between",children:e.map((e=>{var n;return(0,P.jsx)(L.j,{field:e,children:(0,P.jsx)(p.A,{orgId:O,model:"member",field:e,inputRef:fe(e),onFieldChange:me(e,e.id),name:e.id,defaultValue:e.id,label:e.label,fullWidth:!0,error:ae&&(null===(n=ae[e.id])||void 0===n?void 0:n.message)},"memberFormField_".concat(e.id))})}))})))}),(0,P.jsx)(s.A,{display:"flex",gridGap:"16px",justifyContent:"flex-end",children:(0,P.jsx)(d.A,{variant:"contained",color:"primary",form:"user-tour-member-form",type:"submit",disabled:ge,children:"Save person"})})]})})})]})}),(0,P.jsx)(s.A,{flex:1,height:"450px",display:"flex",flexDirection:"column",justifyContent:"space-between",children:(0,P.jsxs)(s.A,{display:"flex",flexDirection:"column",gridGap:"28px",children:[(0,P.jsxs)(c.A,{variant:"h4",weight:"bold",display:"inline",style:{display:"flex",flexDirection:"row",justifyContent:"space-between"},children:["Your Roster"," ",(0,P.jsxs)(c.A,{variant:"caption",display:"inline",children:[V.length," ",1===V.length?"person":"people"]})]}),(0,P.jsx)(w.t$,{children:e=>{let{width:n}=e;return(0,P.jsx)(w.B8,{height:300,rowCount:V.length,rowHeight:58,overscanRowCount:10,rowRenderer:he,noRowsRenderer:xe,width:n})}})]})})]})]})})})},M=W},10035:(e,n,t)=>{t.d(n,{A:()=>j});var r=t(57528),i=t(35007),o=t(70567),l=t(61531),a=t(35801),s=t(43867),d=t(52907),c=t(71233),u=t(5816),p=t(65043),h=t(72119);const x=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:r,skipTour:i}=e;let o=null;const l=(0,p.useRef)(!0);if(t){const e=t,n=null===r||void 0===r?void 0:r[e];o=e&&n?"".concat(e,"_").concat(n):null}const a=o||n,s=(0,p.useRef)(),d=(0,p.useRef)(0),c=a&&x(a),[u,h]=(0,p.useState)(null);return(0,p.useEffect)((()=>(s.current&&clearInterval(s.current),a&&(s.current=setInterval((()=>{d.current++;const e=a&&x(a);e&&(clearInterval(s.current),h(e||null),d.current=0),!e&&d.current>60&&l.current&&(clearInterval(s.current),l.current=!1,i({errored:!0}))}),100)),()=>{s.current&&clearInterval(s.current)})),[a]),(0,p.useEffect)((()=>()=>{s.current&&clearInterval(s.current)}),[]),c||u||null}var f,g=t(64418),y=t(70579);const A="tour-highlight-anchor",v=(0,h.Ay)(i.A)(f||(f=(0,r.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:r,zIndex:i=1e4}=e;return"\n    z-index: ".concat(i,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),j=e=>{let{children:n,onDialogClose:t,actionsUI:r,...i}=e;const{activeTourStep:h}=i,x=(0,p.useRef)(null),f=(0,o.A)(),[j,w]=(0,p.useState)(null),b=m(i),{confirmAction:I}=(0,g.A)(),{anchorClass:C,backdrop:S,arrow:D,highlightAnchor:T,ignoreAnchorZIndex:k,zIndex:z,anchorOffset:E,backdropAboveModal:F,position:R,showDialogUI:L=!1}=h||{},P=[!0,!1].includes(S)?S:!!b,G=[!0,!1].includes(D)?D:!!b,W=[!0,!1].includes(T)?T:P&&!!b,M=R||"left-start",O=(F?f.zIndex.modal:f.zIndex.drawer)+1,U=O+1;if((0,p.useEffect)((()=>{if(b)return P&&!k&&(b.style.zIndex=(b.style.zIndex||0)+U),W&&b.classList.toggle(A),C&&b.classList.toggle(C),()=>{b&&(P&&!k&&(b.style.zIndex=b.style.zIndex-U),W&&b.classList.toggle(A),C&&b.classList.toggle(C))}}),[b]),null!==h&&void 0!==h&&h.hiddenStep)return(0,y.jsx)(y.Fragment,{});return(0,y.jsxs)(l.A,{children:[L&&(0,y.jsxs)(a.A,{open:!0,onClose:()=>{t&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===h||void 0===h||!h.size),maxWidth:(null===h||void 0===h?void 0:h.size)||"sm",children:[(0,y.jsx)(l.A,{zIndex:1,children:(0,y.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,y.jsx)(s.A,{style:{padding:0},children:n}),r&&(0,y.jsx)(d.A,{style:{display:"block",padding:"24px"},children:r})]}),!L&&b&&(0,y.jsx)(c.A,{open:P,style:{zIndex:O},children:(0,y.jsxs)(v,{open:!0,ref:x,anchorEl:b,placement:M,zIndex:z,width:(null===h||void 0===h?void 0:h.size)||"sm",showBackdrop:P,anchorOffset:E,modifiers:{arrow:{enabled:G,element:j}},children:[G&&(0,y.jsx)(l.A,{className:"arrow",ref:w,children:(0,y.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,y.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}}}]);
//# sourceMappingURL=5862.ed1b4692.chunk.js.map