"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[6222],{76222:(e,n,t)=>{t.r(n),t.d(n,{SelectRolesOption:()=>x,default:()=>m});var r=t(65043),a=t(61531),o=t(84187),i=t(72835),l=t(96364),c=t(14556),s=t(53109),d=t(24115),h=t(10035),g=t(86825),u=t(66588),p=t(70579);const x=e=>{var n;const{MACRO_TOURS:t,addSteps:x,activeOrg:m,goBack:f,goNext:v,skipTour:y,activeTourDef:b}=e,[A,j]=(0,r.useState)(!1),{show:w}=(0,u.A)(),I=(0,c.wA)(),S=null===b||void 0===b||null===(n=b.steps)||void 0===n?void 0:n.find((e=>e.id===t.macro_onboardingTemplates.id)),C=async e=>(S||await x({steps:[{...t.macro_onboardingTemplates}],index:4}),v({userInput:{rolesOptionSelection:e}})),k=async()=>{var e,n;if(null===m||void 0===m||!m.id)return;j(!0);const{error:t,payload:r}=await I(s.te.create({orgId:m.id,data:{chart:{name:"".concat(m.name||"First"," Chart"),type:"traditional"}}}));if(j(!1),!t&&null!==r&&void 0!==r&&null!==(e=r.chart)&&void 0!==e&&e.id&&null!==r&&void 0!==r&&null!==(n=r.chart)&&void 0!==n&&n.organization){var a;w("Chart created successfully","success");v({increment:S?2:1,userInput:{rolesOptionSelection:"manual",chartId:null===r||void 0===r||null===(a=r.chart)||void 0===a?void 0:a.id}})}};return(0,p.jsx)(h.A,{...e,onDialogClose:y,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,p.jsxs)(a.A,{display:"flex",justifyContent:"space-between",children:[(0,p.jsxs)(a.A,{display:"flex",gridGap:32,children:[(0,p.jsx)(i.A,{variant:"outlined",onClick:f,disabled:A,children:"Go Back"}),(0,p.jsx)(i.A,{size:"small",style:{color:"#919191"},disabled:A,variant:"text",onClick:y,children:"I'll choose a starting point later"})]}),(0,p.jsx)(i.A,{style:{textAlign:"right"},size:"large",color:"primary",variant:"contained",onClick:k,disabled:A,children:"Next"})]}),children:(0,p.jsx)(d.A,{transparent:!0,loading:A,children:(0,p.jsx)(a.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,p.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:32,children:[(0,p.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,p.jsx)(l.A,{variant:"h1",style:{fontWeight:"bold"},children:"Start faster with an example."}),(0,p.jsx)(l.A,{variant:"body1",style:{color:"#626262"},children:"Choose a starting point to automatically include roles that fit your organization's structure or start from scratch."})]}),(0,p.jsx)(l.A,{variant:"body1",style:{fontWeight:"bold",alignSelf:"center"},children:"How would you like to start?"}),(0,p.jsxs)(a.A,{display:"flex",justifyContent:"space-evenly",gridGap:16,children:[(0,p.jsx)(o.A,{style:{height:"100%",flex:1},onClick:()=>C("templates"),children:(0,p.jsxs)(a.A,{p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",flexDirection:"column",gridGap:8,children:[(0,p.jsx)(a.A,{mb:1,children:(0,p.jsx)("img",{height:100,src:g.t.largeOrgs,alt:"Large organizations"})}),(0,p.jsx)(l.A,{variant:"body1",style:{fontWeight:"bold"},children:"Organization templates"}),(0,p.jsx)(l.A,{variant:"body2",color:"textSecondary",children:"Choose from common business and organization categories."})]})}),(0,p.jsx)(o.A,{style:{height:"100%",flex:1},onClick:()=>C("brands"),children:(0,p.jsxs)(a.A,{p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",flexDirection:"column",gridGap:8,children:[(0,p.jsx)(a.A,{mb:1,children:(0,p.jsx)("img",{height:100,src:g.t.realBrands,alt:"Real brands"})}),(0,p.jsx)(l.A,{variant:"body1",style:{fontWeight:"bold"},children:"Real Brands"}),(0,p.jsx)(l.A,{variant:"body2",color:"textSecondary",children:"Start with the org hierarchy of well-known, worldwide brands."})]})}),(0,p.jsx)(o.A,{style:{height:"100%",flex:1},onClick:k,children:(0,p.jsxs)(a.A,{p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",flexDirection:"column",gridGap:8,children:[(0,p.jsx)(a.A,{mb:1,children:(0,p.jsx)("img",{height:100,src:g.t.fromScratch,alt:"Start from scratch"})}),(0,p.jsx)(l.A,{variant:"body1",style:{fontWeight:"bold"},children:"Start from scratch"}),(0,p.jsx)(l.A,{variant:"body2",color:"textSecondary",children:"Begin with a basic org structure that you can build on as needed."})]})})]})]})})})})},m=x},10035:(e,n,t)=>{t.d(n,{A:()=>A});var r=t(57528),a=t(35007),o=t(70567),i=t(61531),l=t(35801),c=t(43867),s=t(52907),d=t(71233),h=t(5816),g=t(65043),u=t(72119);const p=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:r,skipTour:a}=e;let o=null;const i=(0,g.useRef)(!0);if(t){const e=t,n=null===r||void 0===r?void 0:r[e];o=e&&n?"".concat(e,"_").concat(n):null}const l=o||n,c=(0,g.useRef)(),s=(0,g.useRef)(0),d=l&&p(l),[h,u]=(0,g.useState)(null);return(0,g.useEffect)((()=>(c.current&&clearInterval(c.current),l&&(c.current=setInterval((()=>{s.current++;const e=l&&p(l);e&&(clearInterval(c.current),u(e||null),s.current=0),!e&&s.current>60&&i.current&&(clearInterval(c.current),i.current=!1,a({errored:!0}))}),100)),()=>{c.current&&clearInterval(c.current)})),[l]),(0,g.useEffect)((()=>()=>{c.current&&clearInterval(c.current)}),[]),d||h||null}var m,f=t(64418),v=t(70579);const y="tour-highlight-anchor",b=(0,u.Ay)(a.A)(m||(m=(0,r.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:r,zIndex:a=1e4}=e;return"\n    z-index: ".concat(a,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(r||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),A=e=>{let{children:n,onDialogClose:t,actionsUI:r,...a}=e;const{activeTourStep:u}=a,p=(0,g.useRef)(null),m=(0,o.A)(),[A,j]=(0,g.useState)(null),w=x(a),{confirmAction:I}=(0,f.A)(),{anchorClass:S,backdrop:C,arrow:k,highlightAnchor:z,ignoreAnchorZIndex:D,zIndex:O,anchorOffset:T,backdropAboveModal:R,position:B,showDialogUI:G=!1}=u||{},L=[!0,!1].includes(C)?C:!!w,V=[!0,!1].includes(k)?k:!!w,W=[!0,!1].includes(z)?z:L&&!!w,U=B||"left-start",_=(R?m.zIndex.modal:m.zIndex.drawer)+1,F=_+1;if((0,g.useEffect)((()=>{if(w)return L&&!D&&(w.style.zIndex=(w.style.zIndex||0)+F),W&&w.classList.toggle(y),S&&w.classList.toggle(S),()=>{w&&(L&&!D&&(w.style.zIndex=w.style.zIndex-F),W&&w.classList.toggle(y),S&&w.classList.toggle(S))}}),[w]),null!==u&&void 0!==u&&u.hiddenStep)return(0,v.jsx)(v.Fragment,{});return(0,v.jsxs)(i.A,{children:[G&&(0,v.jsxs)(l.A,{open:!0,onClose:()=>{t&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===u||void 0===u||!u.size),maxWidth:(null===u||void 0===u?void 0:u.size)||"sm",children:[(0,v.jsx)(i.A,{zIndex:1,children:(0,v.jsx)(h.A,{densePadding:!0,onClose:t||null})}),(0,v.jsx)(c.A,{style:{padding:0},children:n}),r&&(0,v.jsx)(s.A,{style:{display:"block",padding:"24px"},children:r})]}),!G&&w&&(0,v.jsx)(d.A,{open:L,style:{zIndex:_},children:(0,v.jsxs)(b,{open:!0,ref:p,anchorEl:w,placement:U,zIndex:O,width:(null===u||void 0===u?void 0:u.size)||"sm",showBackdrop:L,anchorOffset:T,modifiers:{arrow:{enabled:V,element:A}},children:[V&&(0,v.jsx)(i.A,{className:"arrow",ref:j,children:(0,v.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,v.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}},86825:(e,n,t)=>{t.d(n,{t:()=>a});const r="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),a={buildFirstChart:"".concat(r,"/buildFirstChart.svg"),fromScratch:"".concat(r,"/fromScratch.svg"),largeOrgs:"".concat(r,"/largeOrgs.svg"),smallOrgs:"".concat(r,"/smallOrgs.svg"),realBrands:"".concat(r,"/realBrands.svg"),roleVsPeople:"".concat(r,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(r,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(r,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(r,"/import.svg"),addManually:"".concat(r,"/addManually.svg"),integrate:"".concat(r,"/integrate.svg"),welcome:"".concat(r,"/welcome.svg"),confetti:"".concat(r,"/confetti.png"),style:"".concat(r,"/style.svg"),integrate2:"".concat(r,"/integrate2.svg"),import2:"".concat(r,"/import2.svg"),share:"".concat(r,"/share.svg"),cards:"".concat(r,"/cards.svg"),hierarchy:"".concat(r,"/hierarchy.svg"),navigatingChart:"".concat(r,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=6222.d68152a1.chunk.js.map