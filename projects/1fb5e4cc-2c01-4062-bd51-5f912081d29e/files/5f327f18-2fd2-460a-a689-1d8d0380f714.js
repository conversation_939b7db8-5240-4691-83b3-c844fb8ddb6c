"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[5858],{35858:(e,n,t)=>{t.r(n),t.d(n,{SelectPeopleOption:()=>g,default:()=>h});var o=t(65043),r=t(61531),a=t(84187),l=t(72835),i=t(96364),c=t(10035),s=t(86825),d=t(70579);const p="Manual, drag and drop",u="Import Local File",g=e=>{const{goBack:n,goNext:t,skipTour:g,MACRO_TOURS:h,addSteps:x}=e,m=(0,o.useRef)(null),f=async e=>(m.current=e,"manual"===e?(x({steps:[{...h.macro_onboardingAddPeople}]}),t({userInput:{peopleOptionSelection:p}})):"imports"===e?(x({steps:[{...h.macro_onboardingImportPeople}]}),t({userInput:{peopleOptionSelection:u}})):void 0);return(0,d.jsx)(c.A,{...e,onDialogClose:g,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,d.jsxs)(r.A,{display:"flex",justifyContent:"space-between",children:[(0,d.jsxs)(r.A,{display:"flex",gridGap:32,children:[(0,d.jsx)(l.A,{variant:"outlined",onClick:n,children:"Go Back"}),(0,d.jsx)(l.A,{size:"small",style:{color:"#919191"},variant:"text",onClick:g,children:"I'll add people later"})]}),(0,d.jsx)(l.A,{style:{textAlign:"right"},size:"large",color:"primary",variant:"contained",onClick:()=>f("manual"),children:"Next"})]}),children:(0,d.jsx)(r.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,d.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:32,children:[(0,d.jsxs)(r.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,d.jsx)(i.A,{variant:"h1",style:{fontWeight:"bold"},children:"Add People"}),(0,d.jsx)(i.A,{variant:"body1",style:{color:"#626262"},children:"Now, let\u2019s add some people to fill those roles. You can manually add them, import a file, or use one of our automated integrations."})]}),(0,d.jsx)(i.A,{variant:"body1",style:{fontWeight:"bold",alignSelf:"center"},children:"How would you like to add people?"}),(0,d.jsxs)(r.A,{display:"flex",justifyContent:"space-evenly",gridGap:16,children:[(0,d.jsx)(a.A,{style:{height:"100%",flex:1},onClick:()=>f("imports"),children:(0,d.jsxs)(r.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",flexDirection:"column",gridGap:8,children:[(0,d.jsx)(r.A,{mb:1,children:(0,d.jsx)("img",{height:75,src:s.t.import,alt:"Large organizations"})}),(0,d.jsx)(i.A,{variant:"body1",style:{fontWeight:"bold"},children:"Import a file"}),(0,d.jsx)(i.A,{variant:"body2",color:"textSecondary",children:"Upload a CSV or XLS file with your full list of people."})]})}),(0,d.jsx)(a.A,{style:{height:"100%",flex:1},onClick:()=>f("manual"),children:(0,d.jsxs)(r.A,{height:"100%",p:3,border:"1px solid #ececec",borderRadius:6,display:"flex",flexDirection:"column",gridGap:8,children:[(0,d.jsx)(r.A,{mb:1,children:(0,d.jsx)("img",{height:75,src:s.t.addManually,alt:"Small organizations"})}),(0,d.jsx)(i.A,{variant:"body1",style:{fontWeight:"bold"},children:"Add manually"}),(0,d.jsx)(i.A,{variant:"body2",color:"textSecondary",children:"Create new people individually, one-by-one."})]})})]})]})})})},h=g},10035:(e,n,t)=>{t.d(n,{A:()=>b});var o=t(57528),r=t(35007),a=t(70567),l=t(61531),i=t(35801),c=t(43867),s=t(52907),d=t(71233),p=t(5816),u=t(65043),g=t(72119);const h=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:o,skipTour:r}=e;let a=null;const l=(0,u.useRef)(!0);if(t){const e=t,n=null===o||void 0===o?void 0:o[e];a=e&&n?"".concat(e,"_").concat(n):null}const i=a||n,c=(0,u.useRef)(),s=(0,u.useRef)(0),d=i&&h(i),[p,g]=(0,u.useState)(null);return(0,u.useEffect)((()=>(c.current&&clearInterval(c.current),i&&(c.current=setInterval((()=>{s.current++;const e=i&&h(i);e&&(clearInterval(c.current),g(e||null),s.current=0),!e&&s.current>60&&l.current&&(clearInterval(c.current),l.current=!1,r({errored:!0}))}),100)),()=>{c.current&&clearInterval(c.current)})),[i]),(0,u.useEffect)((()=>()=>{c.current&&clearInterval(c.current)}),[]),d||p||null}var m,f=t(64418),v=t(70579);const y="tour-highlight-anchor",A=(0,g.Ay)(r.A)(m||(m=(0,o.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:o,zIndex:r=1e4}=e;return"\n    z-index: ".concat(r,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),b=e=>{let{children:n,onDialogClose:t,actionsUI:o,...r}=e;const{activeTourStep:g}=r,h=(0,u.useRef)(null),m=(0,a.A)(),[b,j]=(0,u.useState)(null),w=x(r),{confirmAction:I}=(0,f.A)(),{anchorClass:C,backdrop:S,arrow:k,highlightAnchor:z,ignoreAnchorZIndex:D,zIndex:O,anchorOffset:T,backdropAboveModal:L,position:P,showDialogUI:R=!1}=g||{},V=[!0,!1].includes(S)?S:!!w,G=[!0,!1].includes(k)?k:!!w,M=[!0,!1].includes(z)?z:V&&!!w,U=P||"left-start",B=(L?m.zIndex.modal:m.zIndex.drawer)+1,W=B+1;if((0,u.useEffect)((()=>{if(w)return V&&!D&&(w.style.zIndex=(w.style.zIndex||0)+W),M&&w.classList.toggle(y),C&&w.classList.toggle(C),()=>{w&&(V&&!D&&(w.style.zIndex=w.style.zIndex-W),M&&w.classList.toggle(y),C&&w.classList.toggle(C))}}),[w]),null!==g&&void 0!==g&&g.hiddenStep)return(0,v.jsx)(v.Fragment,{});return(0,v.jsxs)(l.A,{children:[R&&(0,v.jsxs)(i.A,{open:!0,onClose:()=>{t&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===g||void 0===g||!g.size),maxWidth:(null===g||void 0===g?void 0:g.size)||"sm",children:[(0,v.jsx)(l.A,{zIndex:1,children:(0,v.jsx)(p.A,{densePadding:!0,onClose:t||null})}),(0,v.jsx)(c.A,{style:{padding:0},children:n}),o&&(0,v.jsx)(s.A,{style:{display:"block",padding:"24px"},children:o})]}),!R&&w&&(0,v.jsx)(d.A,{open:V,style:{zIndex:B},children:(0,v.jsxs)(A,{open:!0,ref:h,anchorEl:w,placement:U,zIndex:O,width:(null===g||void 0===g?void 0:g.size)||"sm",showBackdrop:V,anchorOffset:T,modifiers:{arrow:{enabled:G,element:b}},children:[G&&(0,v.jsx)(l.A,{className:"arrow",ref:j,children:(0,v.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,v.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}},86825:(e,n,t)=>{t.d(n,{t:()=>r});const o="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),r={buildFirstChart:"".concat(o,"/buildFirstChart.svg"),fromScratch:"".concat(o,"/fromScratch.svg"),largeOrgs:"".concat(o,"/largeOrgs.svg"),smallOrgs:"".concat(o,"/smallOrgs.svg"),realBrands:"".concat(o,"/realBrands.svg"),roleVsPeople:"".concat(o,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(o,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(o,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(o,"/import.svg"),addManually:"".concat(o,"/addManually.svg"),integrate:"".concat(o,"/integrate.svg"),welcome:"".concat(o,"/welcome.svg"),confetti:"".concat(o,"/confetti.png"),style:"".concat(o,"/style.svg"),integrate2:"".concat(o,"/integrate2.svg"),import2:"".concat(o,"/import2.svg"),share:"".concat(o,"/share.svg"),cards:"".concat(o,"/cards.svg"),hierarchy:"".concat(o,"/hierarchy.svg"),navigatingChart:"".concat(o,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=5858.24d95fc2.chunk.js.map