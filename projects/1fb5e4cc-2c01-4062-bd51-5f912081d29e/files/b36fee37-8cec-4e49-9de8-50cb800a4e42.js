"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[8248],{58248:(n,e,t)=>{t.r(e),t.d(e,{default:()=>f});var o,i,l,r=t(57528),c=t(30105),a=t(61531),s=t(17339),d=t(96364),u=t(10035),p=t(75156),x=t(72119),h=t(70579);const v=x.Ay.img(o||(o=(0,r.A)(["\n  ","\n"])),(n=>{let{zoomedImageOnRight:e}=n;return" width: 100%;\n      cursor: zoom-in;\n      border-radius: 6px;\n\n      &:hover {\n        border: 1px solid grey;\n        border-radius: 16px;\n        z-index: 1;\n        position: absolute;\n        width: 200%;\n        ".concat(e?"right: 0":"left: 0",";\n        top: 0;\n      }\n")})),g=(0,x.Ay)(c.A)(i||(i=(0,r.A)(["\n  background: white;\n  color: #005dc9;\n\n  :hover {\n    background: white;\n  }\n"]))),m=(0,x.Ay)(c.A)(l||(l=(0,r.A)(["\n  border-color: white;\n  color: white;\n"]))),f=n=>{var e,t,o,i,l,r,x;let{children:f,...A}=n;const{completeTour:w,goBack:k,goNext:y,skipTour:b,activeTourStep:j,activeTourDef:z,activeTourStepIndex:I}=A,C=null===z||void 0===z||null===(e=z.steps)||void 0===e?void 0:e.length,T=!z.isMacroTour&&!!C&&"".concat(I+1," / ").concat(C),L=(null===j||void 0===j||null===(t=j.actions)||void 0===t?void 0:t.reduce(((n,e)=>("next"===(null===e||void 0===e?void 0:e.type)&&(n.next=e),"back"===(null===e||void 0===e?void 0:e.type)&&(n.back=e),"skip"===(null===e||void 0===e?void 0:e.type)&&(n.skip=e),"complete"===(null===e||void 0===e?void 0:e.type)&&(n.complete=e),n)),{}))||{},S=z.isMacroTour||!(null===j||void 0===j||null===(o=j.actions)||void 0===o||!o.find((n=>["next","back","complete"].includes(null===n||void 0===n?void 0:n.type))));return(0,h.jsx)(u.A,{...A,children:(0,h.jsxs)(a.A,{display:"flex",flexDirection:"column",gridGap:8,children:[(0,h.jsxs)(a.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,h.jsx)(d.A,{style:{flex:1,fontSize:"11px"},color:"white",children:(null===z||void 0===z?void 0:z.title)||""}),(0,h.jsx)(s.A,{size:"small",onClick:"macro_helpBtn"===(null===j||void 0===j?void 0:j.id)?w:b,children:(0,h.jsx)(p.Ay,{color:"grey",icon:"Close",size:"sm"})})]}),(null===j||void 0===j?void 0:j.title)&&(0,h.jsxs)(a.A,{display:"flex",alignItems:"center",gridGap:16,children:[(0,h.jsx)(d.A,{color:"white",children:T||""}),(0,h.jsx)(d.A,{color:"white",variant:"h4",weight:"bold",children:null===j||void 0===j?void 0:j.title})]}),(null===j||void 0===j?void 0:j.videoLink)&&(0,h.jsx)("video",{src:"".concat("https://assets-organimi.s3.amazonaws.com").concat(null===j||void 0===j?void 0:j.videoLink),autoPlay:!0,controls:!0}),(null===j||void 0===j?void 0:j.imageLink)&&(0,h.jsx)(v,{src:"".concat("https://assets-organimi.s3.amazonaws.com").concat(null===j||void 0===j?void 0:j.imageLink),alt:"tour.png",zoomedImageOnRight:null===j||void 0===j?void 0:j.zoomedImageOnRight}),(null===j||void 0===j?void 0:j.text)&&(0,h.jsx)(a.A,{mt:2,flex:1,children:(0,h.jsx)(d.A,{variant:"caption",color:"white",children:null===j||void 0===j?void 0:j.text})}),S&&(0,h.jsxs)(a.A,{mt:4,display:"flex",justifyContent:"space-between",children:[(0,h.jsxs)(a.A,{display:"flex",gridGap:8,children:[!!L.back&&(0,h.jsx)(m,{size:"small",variant:"outlined",onClick:k,children:(null===(i=L.back)||void 0===i?void 0:i.text)||"Back"}),!(!L.skip&&!z.isMacroTour)&&(0,h.jsx)(c.A,{style:{color:"grey",justifyContent:"left"},size:"small",variant:"text",onClick:b,children:(null===(l=L.skip)||void 0===l?void 0:l.text)||"Skip"})]}),L.next&&(0,h.jsx)(g,{size:"small",variant:"outlined",onClick:y,children:(null===(r=L.next)||void 0===r?void 0:r.text)||"Next"}),L.complete&&(0,h.jsx)(g,{size:"small",color:"secondary",variant:"outlined",onClick:w,children:(null===(x=L.complete)||void 0===x?void 0:x.text)||"Finish"})]}),f]})})}},10035:(n,e,t)=>{t.d(e,{A:()=>k});var o=t(57528),i=t(35007),l=t(70567),r=t(61531),c=t(35801),a=t(43867),s=t(52907),d=t(71233),u=t(5816),p=t(65043),x=t(72119);const h=n=>document.querySelector("#".concat(n,', [data-tour-anchor="').concat(n,'"]'));function v(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:e,dynamicAnchorDataAttribute:t},activeTourUserInput:o,skipTour:i}=n;let l=null;const r=(0,p.useRef)(!0);if(t){const n=t,e=null===o||void 0===o?void 0:o[n];l=n&&e?"".concat(n,"_").concat(e):null}const c=l||e,a=(0,p.useRef)(),s=(0,p.useRef)(0),d=c&&h(c),[u,x]=(0,p.useState)(null);return(0,p.useEffect)((()=>(a.current&&clearInterval(a.current),c&&(a.current=setInterval((()=>{s.current++;const n=c&&h(c);n&&(clearInterval(a.current),x(n||null),s.current=0),!n&&s.current>60&&r.current&&(clearInterval(a.current),r.current=!1,i({errored:!0}))}),100)),()=>{a.current&&clearInterval(a.current)})),[c]),(0,p.useEffect)((()=>()=>{a.current&&clearInterval(a.current)}),[]),d||u||null}var g,m=t(64418),f=t(70579);const A="tour-highlight-anchor",w=(0,x.Ay)(i.A)(g||(g=(0,o.A)(["\n  ","\n"])),(n=>{let{theme:e,width:t,anchorOffset:o,zIndex:i=1e4}=n;return"\n    z-index: ".concat(i,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(e.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),k=n=>{let{children:e,onDialogClose:t,actionsUI:o,...i}=n;const{activeTourStep:x}=i,h=(0,p.useRef)(null),g=(0,l.A)(),[k,y]=(0,p.useState)(null),b=v(i),{confirmAction:j}=(0,m.A)(),{anchorClass:z,backdrop:I,arrow:C,highlightAnchor:T,ignoreAnchorZIndex:L,zIndex:S,anchorOffset:D,backdropAboveModal:R,position:O,showDialogUI:B=!1}=x||{},M=[!0,!1].includes(I)?I:!!b,E=[!0,!1].includes(C)?C:!!b,_=[!0,!1].includes(T)?T:M&&!!b,F=O||"left-start",G=(R?g.zIndex.modal:g.zIndex.drawer)+1,N=G+1;if((0,p.useEffect)((()=>{if(b)return M&&!L&&(b.style.zIndex=(b.style.zIndex||0)+N),_&&b.classList.toggle(A),z&&b.classList.toggle(z),()=>{b&&(M&&!L&&(b.style.zIndex=b.style.zIndex-N),_&&b.classList.toggle(A),z&&b.classList.toggle(z))}}),[b]),null!==x&&void 0!==x&&x.hiddenStep)return(0,f.jsx)(f.Fragment,{});return(0,f.jsxs)(r.A,{children:[B&&(0,f.jsxs)(c.A,{open:!0,onClose:()=>{t&&j({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===x||void 0===x||!x.size),maxWidth:(null===x||void 0===x?void 0:x.size)||"sm",children:[(0,f.jsx)(r.A,{zIndex:1,children:(0,f.jsx)(u.A,{densePadding:!0,onClose:t||null})}),(0,f.jsx)(a.A,{style:{padding:0},children:e}),o&&(0,f.jsx)(s.A,{style:{display:"block",padding:"24px"},children:o})]}),!B&&b&&(0,f.jsx)(d.A,{open:M,style:{zIndex:G},children:(0,f.jsxs)(w,{open:!0,ref:h,anchorEl:b,placement:F,zIndex:S,width:(null===x||void 0===x?void 0:x.size)||"sm",showBackdrop:M,anchorOffset:D,modifiers:{arrow:{enabled:E,element:k}},children:[E&&(0,f.jsx)(r.A,{className:"arrow",ref:y,children:(0,f.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,f.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),e]})})]})}}}]);
//# sourceMappingURL=8248.e6f71649.chunk.js.map