"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[3657],{55871:(e,t,n)=>{n.d(t,{A:()=>c});var l=n(40454),a=n(61531),i=n(80539),o=n(75156),r=n(96364),s=n(59177),d=n(70579);const c=e=>{let{person:t,showEmail:n=!1}=e;const{firstName:c="",lastName:h="",photo:m,id:p,email:u,roleDefaults:A}=t;let x=Boolean(c)||Boolean(h)?"".concat(c," ").concat(h):u;const b="loadMore"===p,g="emailNotValid"===p,j=!b&&!g&&n;return(0,d.jsxs)(l.A,{container:!0,alignContent:"center",alignItems:"center",children:[(0,d.jsxs)(a.A,{mr:2,children:[b&&(0,d.jsx)(a.A,{children:(0,d.jsx)(o.Ay,{icon:"LoadMore",size:"x2"})}),!b&&g&&(0,d.jsx)(a.A,{children:(0,d.jsx)(o.Ay,{icon:"ExclamationTriangle",size:"x2"})}),!b&&!g&&(0,d.jsx)(i.A,{width:30,height:30,src:m,name:x,overrideColor:null===t||void 0===t?void 0:t.memberPhotoColor})]}),(0,d.jsxs)(l.A,{item:!0,xs:!0,children:[(0,d.jsx)(r.A,{display:"inline",children:"".concat(c," ").concat(h," ")}),(c||h)&&u&&n&&"| ",n&&Boolean(u)?(0,d.jsx)(r.A,{display:"inline",children:u}):j&&(0,d.jsx)(a.A,{display:"inline",children:(0,d.jsx)(r.A,{fontStyle:"italic",display:"inline",children:"No email found."})}),(0,s.aG)(null===A||void 0===A?void 0:A.title)&&(0,d.jsx)(r.A,{children:null!==A&&void 0!==A&&A.title?A.title:""})]})]})}},63657:(e,t,n)=>{n.r(t),n.d(t,{default:()=>F});var l=n(65043),a=n(76031),i=n(84),o=n(61531),r=n(43867),s=n(40454),d=n(55357),c=n(43577),h=n(52907),m=n(96364),p=n(84866),u=n(49157),A=n(75156),x=n(82244),b=n(14556),g=n(61258),j=n(2173),y=n(52906),f=n(43862),v=n(43331),C=n(7743),I=n(71433),S=n(5816),w=n(72835),N=n(74593),R=n(59177),k=n(48283),L=n(85279),W=n(10621),T=n(54762),D=n(43940),q=n(24115),E=n(70579);const F=e=>{var t;let{open:n}=e;const F=(0,b.wA)(),M=(0,l.useRef)(),[_,O]=(0,l.useState)("single"),[P,V]=(0,l.useState)(!1),[B,z]=(0,l.useState)([]),{toggleDialog:Q,props:{rel:U}}=(0,i.A)("newRoleSimple"),{openDialog:H}=(0,i.A)("newRole"),{register:X,handleSubmit:Y,getValues:G,setValue:J,control:K}=(0,g.mN)(),Z=(0,b.d4)(v.lz),$=(0,l.useMemo)((()=>(0,k.Cs)(_)),[_]),ee=(0,b.d4)(C.KN),te=(0,b.d4)(C.gJ),ne=null===te||void 0===te||null===(t=te.roleName)||void 0===t?void 0:t.id,le=(0,W.dP)(ee,W.dj.HIREBYDATE),ae=null===ee||void 0===ee?void 0:ee.find((e=>e.id===le)),ie=(0,l.useCallback)((()=>{Q()}),[Q]),{onTourEventComplete:oe}=(0,D.M)({}),re=(0,b.d4)(v.mn),se=(0,b.d4)(T.Pq),de=(0,b.d4)(T.Pe),[ce,he]=(0,j.A)(Y((async e=>{if("embedded"===_){const{name:t}=Z.find((t=>t.id===e.role.embedded_chart))||{name:"embedded chart"};e.role[ne]=t}e.role=(0,W.YW)(e.role,ee);const{error:t,payload:n}=await F(f.h7.create({orgId:re,chartId:se,data:{role:{type:_,...e.role,chart:de},rel:U,members:B,quantity:e.quantity}}));if(!t){var l,a;const e=null===n||void 0===n||null===(l=n.roles)||void 0===l||null===(a=l[0])||void 0===a?void 0:a.id;oe({event:"role-doalog-simple-role-created",data:{roleId:e,roleCard:e}}),ie()}}))),me=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const n=G("quantity")||1;if("shared"===_){const e=[...new Map((t||[]).map((e=>[e.id,e]))).values()];z(e)}else n<t.length?z((e=>[...[...e].slice(0,n-1),t[t.length-1]])):z(t)},pe=/(single|department)/.test(_),ue=/(single|assistant|shared)/.test(_),Ae=/(shared)/.test(_),xe=B,be=!Ae&&G("quantity")||0;(0,l.useLayoutEffect)((()=>{!Ae&&ue?me(null,[...B]):ue||me(null,[])}),[_]);const ge=()=>{ce||ie()};return(0,E.jsx)(E.Fragment,{children:(0,E.jsxs)(a.A,{open:n,onClose:ge,children:[(0,E.jsx)(S.A,{align:"center",onClose:ge,children:"New Role"}),(0,E.jsx)(o.A,{width:550,px:4,clone:!0,children:(0,E.jsx)(r.A,{dividers:!0,children:(0,E.jsx)(q.A,{loading:ce,transparent:!0,children:(0,E.jsxs)("form",{onSubmit:he,id:"newRoleSimpleForm",children:[(0,E.jsx)(o.A,{mb:4,children:(0,E.jsx)(y.A,{handleClick:e=>()=>{O(e),"shared"!==e&&"department"!==e&&J("quantity",1)},selectedType:_,numRows:2})}),(0,E.jsx)(o.A,{my:2,clone:!0,children:(0,E.jsxs)(s.A,{container:!0,alignItems:"flex-start",children:[pe&&(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(u.A,{item:!0,width:80,children:(0,E.jsx)(p.A,{type:"number",name:"quantity",id:"role-quantity",label:"Quantity",color:"primary",defaultValue:1,inputProps:{min:1,max:100},fullWidth:!0,inputRef:X,"aria-describedby":"practiceChart_tooltip_7"})}),(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(A.Ay,{icon:"Close"})})]}),(0,E.jsxs)(s.A,{item:!0,xs:!0,children:["embedded"!==_&&(0,E.jsx)(p.A,{autoFocus:!0,fullWidth:!0,name:"role.".concat(ne),id:"new-simple-role-title",label:$.title,color:"primary",required:!0,inputRef:X({required:!0}),"data-tour-anchor":"role-dialog-simple-role-title-field"}),"embedded"===_&&(0,E.jsx)(g.xI,{name:"role.embedded_chart",control:K,defaultValue:"",render:e=>{let{name:t,onChange:n}=e;return(0,E.jsxs)(p.A,{select:!0,name:t,fullWidth:!0,labelId:"new-simple-embedded-chart",label:"Select Chart",color:"primary",required:!0,onChange:e=>{n(e.target.value)},children:[(0,E.jsx)(d.A,{value:"",children:(0,E.jsx)("em",{children:"Select Chart"})}),Z.map((e=>(0,E.jsx)(d.A,{value:e.id,children:e.name})))]})}}),(0,E.jsx)("div",{children:(0,E.jsx)("a",{onClick:()=>{H({rel:U,role:{type:_,[ne]:G("role.".concat(ne))},quantity:G("quantity")||1,members:B,from:"simple"},!0)},children:"Enter More Information \xbb"})})]})]})}),(0,E.jsx)(x.A,{orientation:"hors",weight:1}),(0,E.jsx)(s.A,{container:!0,justifyContent:"space-between",alignItems:"flex-start",children:ue&&(0,E.jsx)(E.Fragment,{children:(0,E.jsx)(m.A,{variant:"subtitle2",align:"left",display:"block",weight:"bold",children:"Assign People to Role"})})}),ue&&(0,E.jsx)(o.A,{my:1,children:(0,E.jsxs)(s.A,{container:!0,justifyContent:"left",children:[(0,E.jsx)(c.A,{checked:P,onChange:()=>{V(!P)},color:"primary",name:"vacantRole",inputProps:{"aria-label":"Role is vacant"}}),(0,E.jsx)(o.A,{m:1,children:(0,E.jsx)(m.A,{variant:"body2",children:"Role is Vacant"})})]})}),!P&&ue&&(0,E.jsxs)(s.A,{container:!0,justifyContent:"flex-start",children:[(0,E.jsx)(s.A,{item:!0}),(0,E.jsx)(s.A,{item:!0,xs:!0,children:(0,E.jsx)(o.A,{m:2,clone:!0,children:(0,E.jsx)(I.A,{disableClearable:!0,searchInputRef:M,handleSelect:me,options:xe,limit:be,selected:B})})}),(0,E.jsx)(o.A,{mb:1,mt:1,ml:2,children:(0,E.jsxs)(w.A,{variant:"contained",color:"secondary",onClick:()=>{var e;const t=null===(e=M.current)||void 0===e?void 0:e.value,n=(l=t,{...(0,R.St)(l||""),isNew:!0});var l;let a;a="shared"===_?[n,...B]:B,H({rel:U,role:{type:_,[ne]:G("role.".concat(ne))},quantity:G("quantity")||1,members:a,from:"simple"},!0)},children:[(0,E.jsx)(A.Ay,{icon:"Add"})," \xa0 New"]})})]}),P&&ue&&(0,E.jsx)(s.A,{container:!0,children:(0,E.jsx)(g.xI,{control:K,name:"role.".concat(le),render:e=>{let{onChange:t,value:n,name:l}=e;return(0,E.jsx)(L.A,{label:ae.label||"Planned hire date",clearable:!!n,type:"date",onChange:t,value:n,field:l,classes:"dateInputFullWidth"})}})})]})})})}),(0,E.jsx)(h.A,{children:(0,E.jsxs)(s.A,{container:!0,justifyContent:"center",children:[(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(N.A,{fullWidth:!0,disabled:ce,variant:"outlined",color:"primary",onClick:ge,children:"Cancel"})}),(0,E.jsx)(o.A,{m:2,children:(0,E.jsx)(N.A,{fullWidth:!0,disabled:ce,variant:"contained",color:"primary",type:"submit",form:"newRoleSimpleForm",children:"Save"})})]})})]})})}},52906:(e,t,n)=>{n.d(t,{A:()=>b});var l,a,i=n(57528),o=n(72119),r=n(75156),s=n(96364),d=n(40454),c=n(61531),h=n(37294),m=n(70579);const p=(0,o.Ay)(d.A)(l||(l=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:t,disabled:n=!1}=e;return"\n    padding: ".concat(t.spacing(1),"px;\n    border-radius: 30px;\n    background-color: ").concat(n?h.Qs.Neutrals[200]:t.palette.grey[100],";\n    color: ").concat(n?h.Qs.Neutrals[400]:t.palette.grey[400],";\n    height:50px;\n    width: 50px;\n    margin: 0 auto;\n  ")})),u=(0,o.Ay)(d.A)(a||(a=(0,i.A)(["\n  ","\n"])),(e=>{let{theme:t,selected:n,disabled:l}=e;return"\n    border-radius: 8px;\n    text-align: center;\n    cursor: ".concat(l?"not-allowed":"pointer",";\n    ").concat(n&&!l&&"p {\n          color: ".concat(t.palette.info.dark,";\n        }\n        .iconWrapper {\n          background-color: #d6ebf9;\n          color: #4793cf;\n        }"),"\n    ").concat(l&&"\n      p {\n        color: red;\n      }\n      .iconWrapper {\n        color: ".concat(t.palette.grey[400],";\n      }\n    "),"\n    ").concat(!l&&"\n      &:hover p {\n        color: ".concat(t.palette.info.dark,";\n      }\n      &:hover .iconWrapper {\n        color: ").concat(t.palette.info.dark,";\n        background: ").concat(t.palette.info.light,";\n      }\n    "),"\n  ")})),A=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant",separate:!0},{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],x=[[{name:"single",label:"Single",icon:"User",separate:!0},{name:"shared",label:"Shared",icon:"Shared",separate:!0},{name:"assistant",label:"Assistant",icon:"Assistant"}],[{name:"department",label:"Department",icon:"Chart",separate:!0,disableWithAI:!0},{name:"embedded",label:"Link to Chart",icon:"Chart",separate:!0,disableWithAI:!0},{name:"location",label:"Location",icon:"Location",disableWithAI:!0}]],b=e=>{let{handleClick:t,selectedType:n,numRows:l=1,my:a=2,isAiInsightsView:i=!1}=e;const o=1===l?120:160,b=e=>"department"===e?"practiceChart_tooltip_3":"single"===e?"chartFromScratch_tooltip_6":"";return(1===l?A:x).map(((e,l)=>(0,m.jsx)(c.A,{my:a,children:(0,m.jsx)(d.A,{container:!0,justifyContent:"center",children:e.map((e=>{let{name:l,label:a,icon:d,separate:A,disableWithAI:x}=e;const g=i&&x;return(0,m.jsx)(u,{onClick:g?()=>{}:t(l),item:!0,selected:n===l,"aria-describedby":b(l),disabled:g,children:(0,m.jsxs)(c.A,{width:o,borderColor:"grey.200",border:A?1:0,borderTop:0,borderLeft:0,borderBottom:0,id:"chart_tooltip_".concat(l),children:[(0,m.jsx)(p,{container:!0,className:"iconWrapper",direction:"column",justifyContent:"center",alignItems:"center",disabled:g,children:(0,m.jsx)(r.Ay,{icon:d,size:"lg"})}),(0,m.jsx)(s.A,{variant:"body1",color:g?h.Qs.Neutrals[500]:h.Qs.Neutrals[800],children:a})]})},"roleType".concat(l))}))})},"roleTypeGroup".concat(l))))}},71433:(e,t,n)=>{n.d(t,{A:()=>m});var l=n(65043),a=n(90349),i=n(56070),o=n(55871),r=n(85899),s=n(59177),d=n(49092),c=n(10621),h=n(70579);const m=e=>{let{handleSelect:t,selected:n,label:m="Search People",hideTags:p=!1,placeholder:u="Search by Name",searchInputRef:A=null,disableClearable:x,children:b,initOpen:g=!1,searchOrg:j=null,freeSolo:y=!1,showEmailInSearch:f=!1,requiredAttributesInResult:v=[]}=e;const C={},[I,S]=(0,l.useState)(g),{t:w}=(0,d.B)(),{query:N,people:R,loading:k,totalResults:L,handleInputChange:W,handleLoadMore:T}=(0,r.A)({emptySearch:!0,searchOrg:j}),D=(e,t)=>{"select-option"!==t&&S(!1)};let q=[...R.map((e=>({...e,firstName:(0,c.II)(e),lastName:(0,c.US)(e),email:(0,c.zY)(e)})))];return L>R.length&&q.push({id:"loadMore",firstName:"Load",lastName:"More"}),0===L&&y&&((0,s.B9)(N)?q.push({email:N}):q=[{id:"emailNotValid",firstName:w("General.Text.EmailNotValid")}]),v.length&&(q=q.filter((e=>{for(let t=0;t<v.length;t++)e[v[t]]||"loadMore"===e.id||"emailNotValid"===e.id?e.noEmailFound=!1:e.noEmailFound=!0;return!0}))),b&&"function"===typeof b&&(C.PopperComponent=b),(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(a.Ay,{id:"autocomplete-member-search",disableClearable:x,clearOnBlur:!1,fullWidth:!0,freeSolo:y,autoHighlight:!0,open:I,onOpen:()=>{S(!0)},openOnFocus:!1,onClose:D,getOptionLabel:e=>"".concat((0,c.II)(e)," ").concat((0,c.US)(e)),getOptionDisabled:e=>e.noEmailFound,options:q,filterOptions:e=>e,loading:k,multiple:!0,onChange:(e,n)=>{var l;const{id:a}=n[n.length-1]||{},i=(0,c.zY)(n[n.length-1])||(null===(l=n[n.length-1])||void 0===l?void 0:l.email);if("loadMore"===a)return e&&e.stopPropagation(),S(!0),T();(a||i)&&"emailNotValid"!==a?(t(e,n),D()):t(e,n||[])},onInputChange:W,value:n,renderOption:e=>(0,h.jsx)(o.A,{person:e,showEmail:f}),...C,renderInput:e=>(0,h.jsx)(i.A,{...e,inputRef:A,hideTags:p,label:m,placeholder:u,fullWidth:!0})})})}},48283:(e,t,n)=>{n.d(t,{Cs:()=>a,yy:()=>i,zv:()=>o});var l=n(78396);function a(e){let t={title:"Role Title *",description:"Role Description",name:!0,color:!0,propagateBg:!0,location:!1,locationAddress:!0,chartLink:!1,dottedReports:!0,manager:!0,dropLevel:!0,leftRight:!1,customFields:!0,smartLegendLink:!0};switch(e){case"assistant":t.orientation=!0,t.dropLevel=!1;break;case"location":t.description=!1,t.dottedReports=!1,t.locationAddress=!0,t.title="Location Name",t.description="Location Description";break;case"embedded":t.chartLink=!0,t.dottedReports=!1,t.location=!1,t.locationAddress=!1,t.dottedReport=!1,t.title="Linked Chart",t.description=!1,t.propagateBg=!1,t.manager=!1,t.dottedReports=!1,t.dropLevel=!1,t.customFields=!1,t.smartLegendLink=!1;break;case"department":t.title="Department Title *",t.description="Department Description",t.locationAddress=!1}return t}function i(e){let{role:t,chartType:n}=e;return n===l.XD.MATRIX&&!(null!==t&&void 0!==t&&t.parent)&&!(null!==t&&void 0!==t&&t.type)!==l.mv.HIDDEN&&!t.functionId&&!t.teamId}function o(e){let{role:t,chartType:n}=e;return n===l.XD.MATRIX&&[l.mv.TEAM,l.mv.FUNCTION,l.mv.HIDDEN].includes(t.type)}}}]);
//# sourceMappingURL=3657.7d33e090.chunk.js.map