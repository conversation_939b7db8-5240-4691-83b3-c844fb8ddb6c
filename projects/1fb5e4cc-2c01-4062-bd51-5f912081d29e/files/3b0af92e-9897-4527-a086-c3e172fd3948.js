"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[4259],{34259:(e,n,t)=>{t.r(n),t.d(n,{RolesVsPeople:()=>I,VideoOverlay:()=>j,VideoPlayer:()=>b,default:()=>k});var o,r,i=t(57528),l=t(61531),a=t(72835),s=t(96364),c=t(10035),d=t(86825),h=t(65043),p=t(72119),u=t(75156),g=t(14556),x=t(36138),f=t(66856),m=t(83972),v=t(66588),y=t(91688),A=t(70669),w=t(70579);const b=(0,p.Ay)(l.A)(o||(o=(0,i.A)(["\n  position: fixed;\n  display: flex;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  z-index: 1;\n"]))),j=(0,p.Ay)(l.A)(r||(r=(0,i.A)(["\n  width: 100%;\n  color: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.4);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  &:hover {\n    background: rgba(0, 0, 0, 0.1);\n  }\n"]))),I=e=>{var n,t;const{goNext:o,goBack:r,skipTour:i,completeTour:p,setActiveTour:I}=e,{show:k}=(0,v.A)(),C=(0,y.useHistory)(),z=(0,g.wA)(),T=(0,g.d4)(m._l),{isExact:V}=(0,x.u)((0,f.si)()),[P,S]=(0,h.useState)(!1),D=null===T||void 0===T?void 0:T.id,O=null===T||void 0===T||null===(n=T.charts)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.id,W=()=>{if(!V)return D&&O?C.push((0,f.Wx)({base:"protected",chartId:O,orgId:D})):(setTimeout((()=>{I({tour:null})})),k("Open any chart to proceed","info"))};return(0,w.jsx)(c.A,{...e,onDialogClose:i,activeTourStep:{...(null===e||void 0===e?void 0:e.activeTourStep)||{},size:"md",showDialogUI:!0},actionsUI:(0,w.jsxs)(l.A,{display:"flex",justifyContent:"space-between",children:[(0,w.jsx)(a.A,{variant:"outlined",onClick:r,children:"Go Back"}),(0,w.jsx)(a.A,{size:"large",color:"primary",variant:"contained",onClick:()=>null!==T&&void 0!==T&&T.charts.length?(p({userInput:{hasExistingCharts:!0},resetActiveTour:!1}),setTimeout((()=>{I({tour:"chartTour"})})),W(),void z((0,A.tg)())):o(),children:"Continue"})]}),children:(0,w.jsx)(l.A,{p:5,display:"flex",justifyContent:"space-between",flexDirection:"column",gridGap:64,children:(0,w.jsxs)(l.A,{display:"flex",flexDirection:"column",gridGap:24,children:[(0,w.jsxs)(l.A,{display:"flex",flexDirection:"column",gridGap:12,children:[(0,w.jsx)(s.A,{variant:"h1",style:{fontWeight:"bold"},children:"Roles vs. People"}),(0,w.jsxs)(s.A,{variant:"body1",style:{color:"#626262"},children:["Think of an org chart in two parts:",(0,w.jsx)("span",{style:{fontWeight:"bold"},children:" Roles "}),"(the positions) & ",(0,w.jsx)("span",{style:{fontWeight:"bold"},children:"People "}),"(who fill those positions). Let's set up Roles first!"]})]}),(0,w.jsxs)(l.A,{alignItems:"center",justifyContent:"space-between",display:"flex",gridGap:16,children:[(0,w.jsx)(l.A,{flex:3,children:(0,w.jsx)("img",{width:"100%",src:d.t.roleVsPeople,alt:"Roles vs. people"})}),(0,w.jsxs)(l.A,{flex:1,display:"flex",flexDirection:"column",alignItems:"center",children:[(0,w.jsxs)(l.A,{minWidth:150,height:100,position:"relative",mb:2,children:[(0,w.jsx)(j,{onClick:()=>S(!0),children:(0,w.jsx)(u.Ay,{icon:"PlaySolid",size:"x4"})}),(0,w.jsx)("img",{height:"100%",width:"100%",src:d.t.roleVsPeopleIntroVidThumbnail})]}),(0,w.jsx)(s.A,{variant:"body1",style:{fontWeight:"bold"},children:"Watch & learn"}),(0,w.jsx)(s.A,{variant:"body1",style:{color:"#626262"},children:"30 sec video tutorial"})]})]}),P&&(0,w.jsx)(b,{p:8,onClick:()=>S(!1),children:(0,w.jsxs)(l.A,{display:"flex",justifyContent:"center",width:"100%",position:"relative",height:"100%",onClick:e=>e.stopPropagation(),children:[(0,w.jsx)("video",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:d.t.roleVsPeopleIntroVid,autoPlay:!0,controls:!0,title:"Organimi Help Walk Through",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowfullscreen:!0}),(0,w.jsx)(l.A,{position:"fixed",right:16,top:16,children:(0,w.jsx)(a.A,{variant:"contained",color:"primary",onClick:()=>{S(!1)},children:"Close Video"})})]})})]})})})},k=I},10035:(e,n,t)=>{t.d(n,{A:()=>w});var o=t(57528),r=t(35007),i=t(70567),l=t(61531),a=t(35801),s=t(43867),c=t(52907),d=t(71233),h=t(5816),p=t(65043),u=t(72119);const g=e=>document.querySelector("#".concat(e,', [data-tour-anchor="').concat(e,'"]'));function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{activeTourStep:{anchorDataAttribute:n,dynamicAnchorDataAttribute:t},activeTourUserInput:o,skipTour:r}=e;let i=null;const l=(0,p.useRef)(!0);if(t){const e=t,n=null===o||void 0===o?void 0:o[e];i=e&&n?"".concat(e,"_").concat(n):null}const a=i||n,s=(0,p.useRef)(),c=(0,p.useRef)(0),d=a&&g(a),[h,u]=(0,p.useState)(null);return(0,p.useEffect)((()=>(s.current&&clearInterval(s.current),a&&(s.current=setInterval((()=>{c.current++;const e=a&&g(a);e&&(clearInterval(s.current),u(e||null),c.current=0),!e&&c.current>60&&l.current&&(clearInterval(s.current),l.current=!1,r({errored:!0}))}),100)),()=>{s.current&&clearInterval(s.current)})),[a]),(0,p.useEffect)((()=>()=>{s.current&&clearInterval(s.current)}),[]),d||h||null}var f,m=t(64418),v=t(70579);const y="tour-highlight-anchor",A=(0,u.Ay)(r.A)(f||(f=(0,o.A)(["\n  ","\n"])),(e=>{let{theme:n,width:t,anchorOffset:o,zIndex:r=1e4}=e;return"\n    z-index: ".concat(r,";\n    border-radius: 15px;\n    \n    padding: 24px;\n    \n    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%),\n    linear-gradient(0deg, #005DC9, #005DC9);\n\n    color: white;\n    \n    max-width: ").concat(n.breakpoints.values[t]-200,'px;\n    width: 30vw;\n\n    .arrow {\n      position: absolute;\n\n      path {\n        fill: #014DA5;\n      }\n    }\n\n    &[x-placement*="center"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] {\n      margin-top: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="top"] {\n      margin-bottom: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="right"] {\n      margin-left: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="left"] {\n      margin-right: ').concat(o||"60px",';\n    }\n\n    &[x-placement*="bottom"] .arrow {\n      top: -40px;\n      transform: rotate(-90deg);\n    }\n\n    &[x-placement*="top"] .arrow {\n      bottom: -40px;\n      transform: rotate(90deg);\n    }\n\n    &[x-placement*="right"] .arrow {\n      left: -40px;\n      transform: rotate(180deg);\n    }\n\n    &[x-placement*="left"] .arrow {\n      right: -40px;\n    }\n  ')})),w=e=>{let{children:n,onDialogClose:t,actionsUI:o,...r}=e;const{activeTourStep:u}=r,g=(0,p.useRef)(null),f=(0,i.A)(),[w,b]=(0,p.useState)(null),j=x(r),{confirmAction:I}=(0,m.A)(),{anchorClass:k,backdrop:C,arrow:z,highlightAnchor:T,ignoreAnchorZIndex:V,zIndex:P,anchorOffset:S,backdropAboveModal:D,position:O,showDialogUI:W=!1}=u||{},R=[!0,!1].includes(C)?C:!!j,L=[!0,!1].includes(z)?z:!!j,B=[!0,!1].includes(T)?T:R&&!!j,E=O||"left-start",G=(D?f.zIndex.modal:f.zIndex.drawer)+1,U=G+1;if((0,p.useEffect)((()=>{if(j)return R&&!V&&(j.style.zIndex=(j.style.zIndex||0)+U),B&&j.classList.toggle(y),k&&j.classList.toggle(k),()=>{j&&(R&&!V&&(j.style.zIndex=j.style.zIndex-U),B&&j.classList.toggle(y),k&&j.classList.toggle(k))}}),[j]),null!==u&&void 0!==u&&u.hiddenStep)return(0,v.jsx)(v.Fragment,{});return(0,v.jsxs)(l.A,{children:[W&&(0,v.jsxs)(a.A,{open:!0,onClose:()=>{t&&I({title:"Skip Tour",message:"Are your sure you want to skip the tour?",execFunc:()=>{t()}})},fullWidth:!(null===u||void 0===u||!u.size),maxWidth:(null===u||void 0===u?void 0:u.size)||"sm",children:[(0,v.jsx)(l.A,{zIndex:1,children:(0,v.jsx)(h.A,{densePadding:!0,onClose:t||null})}),(0,v.jsx)(s.A,{style:{padding:0},children:n}),o&&(0,v.jsx)(c.A,{style:{display:"block",padding:"24px"},children:o})]}),!W&&j&&(0,v.jsx)(d.A,{open:R,style:{zIndex:G},children:(0,v.jsxs)(A,{open:!0,ref:g,anchorEl:j,placement:E,zIndex:P,width:(null===u||void 0===u?void 0:u.size)||"sm",showBackdrop:R,anchorOffset:S,modifiers:{arrow:{enabled:L,element:w}},children:[L&&(0,v.jsx)(l.A,{className:"arrow",ref:b,children:(0,v.jsx)("svg",{height:"50",width:"50",viewBox:"0 0 51 169",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,v.jsx)("path",{d:"M51 84.5L-2.01166e-07 169L-2.01166e-07 0L51 84.5Z"})})}),n]})})]})}},86825:(e,n,t)=>{t.d(n,{t:()=>r});const o="".concat("https://assets-organimi.s3.amazonaws.com","/").concat("product-tour"),r={buildFirstChart:"".concat(o,"/buildFirstChart.svg"),fromScratch:"".concat(o,"/fromScratch.svg"),largeOrgs:"".concat(o,"/largeOrgs.svg"),smallOrgs:"".concat(o,"/smallOrgs.svg"),realBrands:"".concat(o,"/realBrands.svg"),roleVsPeople:"".concat(o,"/roleVsPeople.svg"),roleVsPeopleIntroVid:"".concat(o,"/ShortIntroAnimationVideo.mp4"),roleVsPeopleIntroVidThumbnail:"".concat(o,"/ShortIntroAnimationVideoThumbnail.png"),import:"".concat(o,"/import.svg"),addManually:"".concat(o,"/addManually.svg"),integrate:"".concat(o,"/integrate.svg"),welcome:"".concat(o,"/welcome.svg"),confetti:"".concat(o,"/confetti.png"),style:"".concat(o,"/style.svg"),integrate2:"".concat(o,"/integrate2.svg"),import2:"".concat(o,"/import2.svg"),share:"".concat(o,"/share.svg"),cards:"".concat(o,"/cards.svg"),hierarchy:"".concat(o,"/hierarchy.svg"),navigatingChart:"".concat(o,"/navigatingChart.svg")}}}]);
//# sourceMappingURL=4259.73ba6d10.chunk.js.map