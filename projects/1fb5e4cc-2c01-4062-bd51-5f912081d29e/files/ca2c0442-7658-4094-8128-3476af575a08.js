"use strict";(self.webpackChunkorganimi_app=self.webpackChunkorganimi_app||[]).push([[9530],{89530:(e,a,t)=>{t.r(a),t.d(a,{default:()=>ie});var n=t(65043),r=t(59691),o=t(61531),l=t(40454),i=t(9579),c=t(38325),s=t(14370),d=t(16853),h=t(64759),m=t(67503),g=t(77887),p=t(695),u=t(72835),A=t(86852),x=t(53462),b=t(43331),C=t(53109),y=t(75687),f=t(49092),v=t(84),j=t(64418),w=t(66856),I=t(91688),T=t(14556),S=t(31777),k=t(67264),N=t(51836),O=t(64021),P=t(75156),z=t(24241),R=t(42006),D=t(59177),_=t(97105),B=t(49015),F=t(47556);const E=t.p+"static/media/logo.0cf248a2d9390f8ce578d777b5920f8f.svg";var G=t(9037),M=t(356),H=t(58168),L=t(80045),U=t(43024),Q=t(74822),Z=t(71745),V=n.forwardRef((function(e,a){var t=e.classes,r=e.className,o=e.color,l=void 0===o?"grey":o,i=e.variant,c=void 0===i?"default":i,s=(0,L.A)(e,["classes","className","color","variant"]);return n.createElement("span",(0,H.default)({className:(0,U.A)(t.root,r,"inherit"!==l&&t["".concat(c).concat((0,Q.A)(l))]),ref:a},s))}));const W=(0,Z.A)((function(e){return{root:{display:"flex",alignSelf:"baseline",borderStyle:"solid",borderWidth:2,padding:4,borderRadius:"50%",boxShadow:e.shadows[2],marginTop:8,marginBottom:8},defaultGrey:{borderColor:"transparent",color:e.palette.grey[50],backgroundColor:e.palette.grey[400]},outlinedGrey:{boxShadow:"none",color:e.palette.grey.contrastText,borderColor:e.palette.grey[400],backgroundColor:"transparent"},defaultPrimary:{borderColor:"transparent",color:e.palette.primary.contrastText,backgroundColor:e.palette.primary.main},outlinedPrimary:{boxShadow:"none",backgroundColor:"transparent",borderColor:e.palette.primary.main},defaultSecondary:{borderColor:"transparent",color:e.palette.secondary.contrastText,backgroundColor:e.palette.secondary.main},outlinedSecondary:{boxShadow:"none",backgroundColor:"transparent",borderColor:e.palette.secondary.main}}}),{name:"MuiTimelineDot"})(V);var q=t(66588),Y=t(90930),J=t(26019),K=t(43940),X=t(47088),$=t(78396),ee=t(85865),ae=t(37294),te=t(50543),ne=t(63296),re=t(86597),oe=t(70579);const le=[{name:"name",label:"Chart Name",align:"left",sortable:!0,variant:"link"},{name:"updated",label:"Last Updated",align:"left",sortable:!0},{name:"roleCount",label:"Total Roles",align:"left",sortable:!0},{name:"chartType",label:"Chart Type",align:"right",sortable:!1},{name:"alias",label:"Alias",align:"right",sortable:!1}],ie=()=>{var e;const a=(0,I.useHistory)(),{orgId:t}=(0,I.useParams)(),{t:H}=(0,f.B)(),[L,U]=(0,n.useState)(""),Q=(0,T.wA)(),{show:Z}=(0,q.A)(),{gotoOwnershipChart:V}=(0,ne.A)(),ie=(0,re.A)(),ce=(0,T.d4)(b.qi),se=(0,T.d4)(b.wO),de=(0,T.d4)(R.eW),{openDialog:he}=(0,v.A)("copyMove"),{openDialog:me}=(0,v.A)("importFile"),{confirmAction:ge}=(0,j.A)(),{openDialog:pe}=(0,v.A)("export2Dialog"),{openDialog:ue}=(0,v.A)("deleteChart"),{openDialog:Ae}=(0,v.A)("editChart"),{toggleDialog:xe}=(0,v.A)("newChart"),{onTourEventComplete:be}=(0,K.M)({featureTours:["utilizeTemplateTour","onboarding"]}),Ce=(0,F.A)(),ye=(0,T.d4)(Y.Qn),{owners:fe,subsidiaries:ve}=(0,T.d4)((0,te.a2)(t||"")),je=((null===fe||void 0===fe?void 0:fe.length)||0)+((null===ve||void 0===ve?void 0:ve.length)||0),{rows:we,page:Ie,order:Te,createSortHandler:Se,totalCount:ke,handleSearch:Ne,rowsPerPage:Oe,handleChangePage:Pe,handleChangeRowsPerPage:ze}=(0,y.s)({dataSelector:b.wO,defaultValues:{order:"asc",orderBy:"name",rowsPerPage:25}}),Re=we.reduce(((e,a)=>{e.push(a);const t=ce[null===a||void 0===a?void 0:a.id];return null!==t&&void 0!==t&&t.length&&e.push(...t),e}),ce.shared||[]);if(je){const e=[...fe||[],...ve||[]].sort(((e,a)=>{const t=new Date(e.updatedAt||"").getTime();return new Date(a.updatedAt||"").getTime()-t}))[0].updatedAt;Re.unshift({id:"".concat(ie.name," Ownership Chart"),name:"Ownership Chart",organization:t,updated:(new Date).toISOString(),type:X.NB.Traditional,counts:{role:je+1},updated_at:e,isOwnership:!0})}const De=ke+((null===(e=ce.shared)||void 0===e?void 0:e.length)||0)+(je?1:0),{setViewOption:_e}=(0,n.useContext)(G.r),{isItemSelected:Be,handleSelectAll:Fe,handleSelectItem:Ee,resetSelected:Ge,selected:Me}=(0,y.U)({selectField:"id"}),He=async()=>{xe()},Le=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"traditional";return o=>{o&&o.stopPropagation(),be({event:"charts-list-chart--click"}),"ownership"===r?V(ie)(null):a.push((0,w.si)({orgId:e,chartId:t.id,resource:"chart",resourceAction:t.status===X.N4.Published?$.uI.DEFAULT:$.uI.AI_INSIGHTS,base:"protected"})),n&&_e(n)}},Ue=e=>a=>{a.stopPropagation(),ge({title:"Are you sure you want to remove your access to this shared chart?",message:e.name,execFunc:async()=>{const{error:a}=await Q(_.Bf.removeChartAccess({orgId:e.organization,chartId:e.id}));a||(Ge(),1===se.length&&se[0].organization===e.organization&&(Q((0,B.Uu)({id:e.organization})),Ce()))}})},{handleButtonActionClick:Qe}=(0,k.A)({delete:()=>ue({title:"Confirm delete of ".concat(Me.length," chart(s)"),execFunc:async()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Delete Chart",chartNames:Me.reduce(((e,a)=>{const t=se.find((e=>e.id===a));return t&&e.push(t.name),e}),[])}});const{error:e}=await Q(C.te.delete({data:Me.map((e=>({id:e})))}));e||Ge()}}),edit:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Chart Settings"}});const e=Me[0],a=ye(e);a?Ae({chart:a}):Z("Could not find chart to edit","error")},export:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Export Chart"}}),pe({chartId:Me[0],orgId:t})},print:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Print Chart"}});const e=Me[0];a.push((0,w.Qo)({orgId:t,chartId:e,resource:"chart",base:"protected"}))},fileCopy:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Copy Chart"}});const e=Me[0],a=(se.find((a=>{var t;return(null===a||void 0===a||null===(t=a.id)||void 0===t?void 0:t.toString())===e}))||{}).name;he({mode:"copy",chartId:e,orgId:t,selector:b.wO,chartName:a})},import:()=>{M.A.trackEvent({eventName:"ORGANIZATION_CHARTS_MORE_ACTION",extraParams:{action:"Import Chart"}}),me()}}),Ze=Re.filter((e=>"ownership"!==e.type));return(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)(o.A,{p:2,borderBottom:"solid 1px #ddd",children:(0,oe.jsxs)(l.A,{container:!0,justifyContent:"owner"===de||"admin"===de?"space-between":"flex-end",alignItems:"center",children:[(0,oe.jsx)(l.A,{item:!0,xs:!0,children:("owner"===de||"admin"===de)&&(0,oe.jsx)(S.A,{buttonGroup:"organization_chartlist",selectedCount:Me.length,handleActionClick:Qe,getIsButtonValid:e=>{if(Me.map((e=>ye(e))).find((e=>e.status===X.N4.Draft)))return"delete"===e;if(1===Me.length){const a=ye(Me[0]);return null===a||void 0===a||!a.alias||!a.alias.roleId||-1===["fileCopy"].indexOf(e)}return[]},userType:de})}),(0,oe.jsx)(l.A,{item:!0,children:(0,oe.jsxs)(l.A,{container:!0,alignItems:"center",children:[(0,oe.jsx)(A.A,{spacing:{right:2},children:(0,oe.jsx)(x.A,{handleSearch:e=>{var a;const t=null===e||void 0===e||null===(a=e.target)||void 0===a?void 0:a.value;U(t||""),Ne(e)},handleClear:()=>{U(""),Ne()},query:L,placeholder:H("Common.Labels.SearchCharts"),defaultValue:""})}),("owner"===de||"admin"===de)&&(0,oe.jsx)(u.A,{"data-tour-anchor":"new-chart-button-dashboard",variant:"contained",color:"primary",onClick:He,children:H("Common.Buttons.NewChart")})]})})]})}),De>0&&(0,oe.jsx)(o.A,{flex:1,overflow:"auto",height:"100%",children:(0,oe.jsxs)(m.A,{"aria-label":"dashboard",size:"small",stickyHeader:!0,children:[(0,oe.jsx)(h.A,{children:(0,oe.jsxs)(p.A,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",children:(0,oe.jsx)(d.A,{indeterminate:Me.length>0&&Me.length<Ze.length,checked:Me.length===Ze.length,onClick:Fe(Ze),inputProps:{"aria-label":"Select all charts"},size:"small"})}),le.map(((e,a)=>"alias"===e.name?(0,oe.jsx)(g.A,{children:"File Type"},"organization-chartlist-headcell-".concat(e.name,"- ").concat(a)):"chartType"===e.name?(0,oe.jsx)(g.A,{children:"Chart Type"},"organization-chartlist-headcell-".concat(e.name,"- ").concat(a)):e.sortable?(0,oe.jsx)(g.A,{align:e.align||"left",children:(0,oe.jsx)(s.A,{onClick:Se(e.name),direction:Te,children:e.label})},"organization-chartlist-headcell-".concat(e.name)):(0,oe.jsx)(g.A,{align:e.align||"left",children:e.label},"organization-chartlist-headcell-".concat(e.name))))]})}),(0,oe.jsx)(r.A,{children:Re.map(((e,a)=>{var t,n;return(0,oe.jsx)(J.FF,{"data-tour-anchor":0===a?"charts-list-first-chart":"charts-list-chart",onClick:Le(e.organization,e,"list",e.type),hover:!0,style:{position:"relative"},children:(0,oe.jsxs)(oe.Fragment,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",style:{paddingLeft:null!==(t=e.alias)&&void 0!==t&&t.roleId?28:18,borderLeft:null!==(n=e.alias)&&void 0!==n&&n.roleId?"solid 4px #3CD3C2":"none"},children:(0,oe.jsx)(d.A,{checked:Be(e),onClick:Ee(e),inputProps:{"aria-label":"Select chart ".concat(e.name)},size:"small",disabled:"ownership"===(null===e||void 0===e?void 0:e.type)})}),le.map((a=>{var t,n,r,l,c;return"updated"===a.name?(0,oe.jsx)(g.A,{scope:"row",children:(0,oe.jsx)(ee.A,{variant:"bodySM",color:ae.Qs.Neutrals[800],children:z.c9.fromISO(e[a.name]).toLocaleString(z.c9.DATE_FULL)})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"type"===a.name?(0,oe.jsx)(g.A,{scope:"row",children:"board of directors"===e[a.name]?(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)("img",{src:E,alt:"Board of directors",width:20,height:20,style:{marginRight:5,verticalAlign:"bottom"}}),"Board of Directors"]}):e[a.name].charAt(0).toUpperCase()+e[a.name].slice(1)},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"chartType"===a.name?(0,oe.jsx)(g.A,{children:(0,oe.jsx)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:(0,oe.jsx)(ee.A,{variant:"bodySM",fontWeight:e.status===X.N4.Draft?"600":"normal",color:e.status===X.N4.Draft?ae.Qs.Info[600]:ae.Qs.Neutrals[800],children:(r=e||"",r.status===X.N4.Draft?"AI Suggested":(0,D.Sn)(r.type))})})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"alias"===a.name?(0,oe.jsx)(g.A,{children:(null===(l=e.alias)||void 0===l?void 0:l.roleId)&&(0,oe.jsxs)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:[(0,oe.jsx)(W,{variant:"outlined",color:"secondary"}),(0,oe.jsx)(J.iU,{children:"Subchart"})]})||(0,oe.jsxs)(o.A,{display:"flex",alignItems:"center",gridGap:8,children:[(0,oe.jsx)(W,{variant:"outlined",color:"primary"}),(0,oe.jsx)(J.iU,{children:null!==e&&void 0!==e&&e.isOwnership?"Ownership":"Main"})]})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):"roleCount"===a.name?(0,oe.jsx)(g.A,{children:(0,oe.jsx)(ee.A,{variant:"bodySM",color:ae.Qs.Neutrals[900],children:(null===e||void 0===e||null===(c=e.counts)||void 0===c?void 0:c.role)||"--"})},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name)):(0,oe.jsxs)(g.A,{scope:"row",children:["name"===a.name&&(0,oe.jsx)(ee.A,{variant:"bodyMD",color:ae.Qs.Neutrals[900],children:e[a.name]}),"name"===a.name&&(null===(t=e.chartIntegration)||void 0===t?void 0:t.syncEnabled)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Synced",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",children:(0,oe.jsx)(P.Ay,{icon:"Sync",size:"lg",color:"#3CD3C2"})})})]}),"name"===a.name&&e.shareAccess&&("public"===e.shareAccess||"chart"===e.shareAccess)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Shared",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",children:(0,oe.jsx)(P.Ay,{icon:"Shared",size:"lg",color:"#5C2DBF"})})})]}),"name"===a.name&&e.shareAccess&&"chart"===e.shareAccess&&"saml-login"!==(null===e||void 0===e||null===(n=e.permission)||void 0===n?void 0:n.type)&&(0,oe.jsxs)(oe.Fragment,{children:["\xa0 \xa0",(0,oe.jsx)(i.Ay,{title:"Remove Shared Access",placement:"top",arrow:!0,children:(0,oe.jsx)(o.A,{display:"inline-block",onClick:Ue(e),children:(0,oe.jsx)(P.Ay,{icon:"RemoveAccess",size:"lg",color:"#5C2DBF"})})})]})]},"organization-chartlist-bodyrow-".concat(e.id,"-cell-").concat(a.name))}))]})},"organization-chartlist-bodyrow-".concat(e.id))}))})]})}),De>Oe&&(0,oe.jsx)(o.A,{position:"sticky",left:0,right:0,bottom:0,bgcolor:"#ffffff",borderTop:"solid 1px #ddd",children:(0,oe.jsx)(c.A,{rowsPerPageOptions:[25,50,100,250],component:"div",count:ke,rowsPerPage:Oe,page:Ie,backIconButtonProps:{"aria-label":H("Common.Tables.backIconButtonText")},nextIconButtonProps:{"aria-label":H("Common.Tables.nextIconButtonText")},onPageChange:Pe,onRowsPerPageChange:ze,labelRowsPerPage:H("Common.Tables.labelRowsPerPage"),backIconButtonText:H("Common.Tables.backIconButtonText"),nextIconButtonText:H("Common.Tables.nextIconButtonText"),labelDisplayedRows:e=>{let{from:a,to:t,count:n}=e;return"".concat(a,"-").concat(t," ").concat(H("Common.Tables.of")," ").concat(n)}})}),!De&&("admin"===de||"owner"===de)&&(0,oe.jsxs)(oe.Fragment,{children:[(0,oe.jsx)(m.A,{"aria-label":"dashboard",size:"small",stickyHeader:!0,children:(0,oe.jsx)(h.A,{children:(0,oe.jsxs)(p.A,{children:[("owner"===de||"admin"===de)&&(0,oe.jsx)(g.A,{padding:"checkbox",children:(0,oe.jsx)(d.A,{indeterminate:Me.length>0&&Me.length<Re.length,checked:Me.length===Re.length,onClick:Fe(Re),inputProps:{"aria-label":"Select all charts"},size:"small"})}),le.map((e=>"alias"===e.name?(0,oe.jsx)(g.A,{children:"File Type"},"organization-chartlist-headcell-".concat(e.name)):"chartType"===e.name?(0,oe.jsx)(g.A,{children:"Chart Type"},"organization-chartlist-headcell-".concat(e.name)):e.sortable?(0,oe.jsx)(g.A,{align:e.align||"left",children:(0,oe.jsx)(s.A,{onClick:Se(e.name),direction:Te,children:e.label})},"organization-chartlist-headcell-".concat(e.name)):(0,oe.jsx)(g.A,{align:e.align||"left",children:e.label},"organization-chartlist-headcell-".concat(e.name))))]})})}),(0,oe.jsx)(o.A,{flexGrow:1,display:"flex",justifyContent:"flex-start",mt:4,flexDirection:"column",children:(0,oe.jsx)(O.A,{text:H("General.Text.LetsBuildYourFirstChart"),image:N.A,handleClick:He,buttonText:H("General.Text.CreatYourFirstChart"),isButtonVisible:"owner"===de||"admin"===de})})]})]})}}}]);
//# sourceMappingURL=9530.c070bb25.chunk.js.map