 ? window.fetch : fetch), YD() && ("undefined" !== typeof global && global.XMLHttpRequest ? QD = global.XMLHttpRequest : "undefined" !== typeof window && window.XMLHttpRequest && (QD = window.XMLHttpRequest)), "function" === typeof ActiveXObject && ("undefined" !== typeof global && global.ActiveXObject ? JD = global.ActiveXObject : "undefined" !== typeof window && window.ActiveXObject && (JD = window.ActiveXObject)), $D || !eF || QD || JD || ($D = XD || eF), "function" !== typeof $D && ($D = void 0); var nF = function(e, t) { if (t && "object" === tF(t)) { var n = ""; for (var r in t) n += "&" + encodeURIComponent(r) + "=" + encodeURIComponent(t[r]); if (!n) return e;
                    e = e + (-1 !== e.indexOf("?") ? "&" : "?") + n.slice(1) } return e },
            rF = function(e, t, n) { $D(e, t).then((function(e) { if (!e.ok) return n(e.statusText || "Error", { status: e.status });
                    e.text().then((function(t) { n(null, { status: e.status, data: t }) })).catch(n) })).catch(n) },
            aF = !1; const oF = function(e, t, n, r) { return "function" === typeof n && (r = n, n = void 0), r = r || function() {}, $D ? function(e, t, n, r) { e.queryStringParams && (t = nF(t, e.queryStringParams)); var a = ZD({}, "function" === typeof e.customHeaders ? e.customHeaders() : e.customHeaders);
                n && (a["Content-Type"] = "application/json"); var o = "function" === typeof e.requestOptions ? e.requestOptions(n) : e.requestOptions,
                    i = ZD({ method: n ? "POST" : "GET", body: n ? e.stringify(n) : void 0, headers: a }, aF ? {} : o); try { rF(t, i, r) } catch (l) { if (!o || 0 === Object.keys(o).length || !l.message || l.message.indexOf("not implemented") < 0) return r(l); try { Object.keys(o).forEach((function(e) { delete i[e] })), rF(t, i, r), aF = !0 } catch (s) { r(s) } } }(e, t, n, r) : YD() || "function" === typeof ActiveXObject ? function(e, t, n, r) { n && "object" === tF(n) && (n = nF("", n).slice(1)), e.queryStringParams && (t = nF(t, e.queryStringParams)); try { var a;
                    (a = QD ? new QD : new JD("MSXML2.XMLHTTP.3.0")).open(n ? "POST" : "GET", t, 1), e.crossDomain || a.setRequestHeader("X-Requested-With", "XMLHttpRequest"), a.withCredentials = !!e.withCredentials, n && a.setRequestHeader("Content-Type", "application/x-www-form-urlencoded"), a.overrideMimeType && a.overrideMimeType("application/json"); var o = e.customHeaders; if (o = "function" === typeof o ? o() : o)
                        for (var i in o) a.setRequestHeader(i, o[i]);
                    a.onreadystatechange = function() { a.readyState > 3 && r(a.status >= 400 ? a.statusText : null, { status: a.status, data: a.responseText }) }, a.send(n) } catch (l) { console && console.log(l) } }(e, t, n, r) : void r(new Error("No fetch and no xhr implementation found!")) };

        function iF(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var lF = function() { return { loadPath: "/locales/{{lng}}/{{ns}}.json", addPath: "/locales/add/{{lng}}/{{ns}}", allowMultiLoading: !1, parse: function(e) { return JSON.parse(e) }, stringify: JSON.stringify, parsePayload: function(e, t, n) { return function(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }({}, t, n || "") }, request: oF, reloadInterval: "undefined" === typeof window && 36e5, customHeaders: {}, queryStringParams: {}, crossDomain: !1, withCredentials: !1, overrideMimeType: !1, requestOptions: { mode: "cors", credentials: "same-origin", cache: "default" } } },
            sF = function() {
                function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.services = t, this.options = n, this.allOptions = r, this.type = "backend", this.init(t, n, r) } var t, n, r; return t = e, n = [{ key: "init", value: function(e) { var t = this,
                            n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                            r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                        this.services = e, this.options = ZD(n, this.options || {}, lF()), this.allOptions = r, this.services && this.options.reloadInterval && setInterval((function() { return t.reload() }), this.options.reloadInterval) } }, { key: "readMulti", value: function(e, t, n) { this._readAny(e, e, t, t, n) } }, { key: "read", value: function(e, t, n) { this._readAny([e], e, [t], t, n) } }, { key: "_readAny", value: function(e, t, n, r, a) { var o, i = this,
                            l = this.options.loadPath; "function" === typeof this.options.loadPath && (l = this.options.loadPath(e, n)), (l = function(e) { return !!e && "function" === typeof e.then }(o = l) ? o : Promise.resolve(o)).then((function(o) { if (!o) return a(null, {}); var l = i.services.interpolator.interpolate(o, { lng: e.join("+"), ns: n.join("+") });
                            i.loadUrl(l, a, t, r) })) } }, { key: "loadUrl", value: function(e, t, n, r) { var a = this;
                        this.options.request(this.options, e, void 0, (function(o, i) { if (i && (i.status >= 500 && i.status < 600 || !i.status)) return t("failed loading " + e + "; status code: " + i.status, !0); if (i && i.status >= 400 && i.status < 500) return t("failed loading " + e + "; status code: " + i.status, !1); if (!i && o && o.message && o.message.indexOf("Failed to fetch") > -1) return t("failed loading " + e + ": " + o.message, !0); if (o) return t(o, !1); var l, s; try { l = "string" === typeof i.data ? a.options.parse(i.data, n, r) : i.data } catch (c) { s = "failed parsing " + e + " to json" } if (s) return t(s, !1);
                            t(null, l) })) } }, { key: "create", value: function(e, t, n, r, a) { var o = this; if (this.options.addPath) { "string" === typeof e && (e = [e]); var i = this.options.parsePayload(t, n, r),
                                l = 0,
                                s = [],
                                c = [];
                            e.forEach((function(n) { var r = o.options.addPath; "function" === typeof o.options.addPath && (r = o.options.addPath(n, t)); var d = o.services.interpolator.interpolate(r, { lng: n, ns: t });
                                o.options.request(o.options, d, i, (function(t, n) { l += 1, s.push(t), c.push(n), l === e.length && a && a(s, c) })) })) } } }, { key: "reload", value: function() { var e = this,
                            t = this.services,
                            n = t.backendConnector,
                            r = t.languageUtils,
                            a = t.logger,
                            o = n.language; if (!o || "cimode" !== o.toLowerCase()) { var i = [],
                                l = function(e) { r.toResolveHierarchy(e).forEach((function(e) { i.indexOf(e) < 0 && i.push(e) })) };
                            l(o), this.allOptions.preload && this.allOptions.preload.forEach((function(e) { return l(e) })), i.forEach((function(t) { e.allOptions.ns.forEach((function(e) { n.read(t, e, "read", null, null, (function(r, o) { r && a.warn("loading namespace ".concat(e, " for language ").concat(t, " failed"), r), !r && o && a.log("loaded namespace ".concat(e, " for language ").concat(t), o), n.loaded("".concat(t, "|").concat(e), r, o) })) })) })) } } }], n && iF(t.prototype, n), r && iF(t, r), Object.defineProperty(t, "prototype", { writable: !1 }), e }();
        sF.type = "backend"; const cF = sF; var dF = [],
            uF = dF.forEach,
            hF = dF.slice; var mF, pF = function(e, t, n, r) { var a, o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : { path: "/" }; if (n) { var i = new Date;
                    i.setTime(i.getTime() + 60 * n * 1e3), a = "; expires=" + i.toUTCString() } else a = "";
                r = r ? "domain=" + r + ";" : "", o = Object.keys(o).reduce((function(e, t) { return e + ";" + t.replace(/([A-Z])/g, (function(e) { return "-" + e.toLowerCase() })) + "=" + o[t] }), ""), document.cookie = e + "=" + encodeURIComponent(t) + a + ";" + r + o },
            fF = function(e) { for (var t = e + "=", n = document.cookie.split(";"), r = 0; r < n.length; r++) { for (var a = n[r];
                        " " === a.charAt(0);) a = a.substring(1, a.length); if (0 === a.indexOf(t)) return a.substring(t.length, a.length) } return null },
            vF = { name: "cookie", lookup: function(e) { var t; if (e.lookupCookie && "undefined" !== typeof document) { var n = fF(e.lookupCookie);
                        n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupCookie && "undefined" !== typeof document && pF(t.lookupCookie, e, t.cookieMinutes, t.cookieDomain, t.cookieOptions) } },
            gF = { name: "querystring", lookup: function(e) { var t; if ("undefined" !== typeof window)
                        for (var n = window.location.search.substring(1).split("&"), r = 0; r < n.length; r++) { var a = n[r].indexOf("="); if (a > 0) n[r].substring(0, a) === e.lookupQuerystring && (t = n[r].substring(a + 1)) }
                    return t } }; try { mF = "undefined" !== window && null !== window.localStorage; var yF = "i18next.translate.boo";
            window.localStorage.setItem(yF, "foo"), window.localStorage.removeItem(yF) } catch (FF) { mF = !1 } var bF, wF = { name: "localStorage", lookup: function(e) { var t; if (e.lookupLocalStorage && mF) { var n = window.localStorage.getItem(e.lookupLocalStorage);
                    n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupLocalStorage && mF && window.localStorage.setItem(t.lookupLocalStorage, e) } }; try { bF = "undefined" !== window && null !== window.sessionStorage; var zF = "i18next.translate.boo";
            window.sessionStorage.setItem(zF, "foo"), window.sessionStorage.removeItem(zF) } catch (FF) { bF = !1 } var xF = { name: "sessionStorage", lookup: function(e) { var t; if (e.lookupsessionStorage && bF) { var n = window.sessionStorage.getItem(e.lookupsessionStorage);
                        n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupsessionStorage && bF && window.sessionStorage.setItem(t.lookupsessionStorage, e) } },
            AF = { name: "navigator", lookup: function(e) { var t = []; if ("undefined" !== typeof navigator) { if (navigator.languages)
                            for (var n = 0; n < navigator.languages.length; n++) t.push(navigator.languages[n]);
                        navigator.userLanguage && t.push(navigator.userLanguage), navigator.language && t.push(navigator.language) } return t.length > 0 ? t : void 0 } },
            kF = { name: "htmlTag", lookup: function(e) { var t, n = e.htmlTag || ("undefined" !== typeof document ? document.documentElement : null); return n && "function" === typeof n.getAttribute && (t = n.getAttribute("lang")), t } },
            SF = { name: "path", lookup: function(e) { var t; if ("undefined" !== typeof window) { var n = window.location.pathname.match(/\/([a-zA-Z-]*)/g); if (n instanceof Array)
                            if ("number" === typeof e.lookupFromPathIndex) { if ("string" !== typeof n[e.lookupFromPathIndex]) return;
                                t = n[e.lookupFromPathIndex].replace("/", "") } else t = n[0].replace("/", "") } return t } },
            MF = { name: "subdomain", lookup: function(e) { var t; if ("undefined" !== typeof window) { var n = window.location.href.match(/(?:http[s]*\:\/\/)*(.*?)\.(?=[^\/]*\..{2,5})/gi);
                        n instanceof Array && (t = "number" === typeof e.lookupFromSubdomainIndex ? n[e.lookupFromSubdomainIndex].replace("http://", "").replace("https://", "").replace(".", "") : n[0].replace("http://", "").replace("https://", "").replace(".", "")) } return t } }; var EF = function() {
            function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                (0, lD.A)(this, e), this.type = "languageDetector", this.detectors = {}, this.init(t, n) } return (0, sD.A)(e, [{ key: "init", value: function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                    this.services = e, this.options = function(e) { return uF.call(hF.call(arguments, 1), (function(t) { if (t)
                                for (var n in t) void 0 === e[n] && (e[n] = t[n]) })), e }(t, this.options || {}, { order: ["querystring", "cookie", "localStorage", "sessionStorage", "navigator", "htmlTag"], lookupQuerystring: "lng", lookupCookie: "i18next", lookupLocalStorage: "i18nextLng", caches: ["localStorage"], excludeCacheFor: ["cimode"], checkWhitelist: !0, checkForSimilarInWhitelist: !1 }), this.options.checkForSimilarInWhitelist && (this.options.checkWhitelist = !0), this.options.lookupFromUrlIndex && (this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex), this.i18nOptions = n, this.addDetector(vF), this.addDetector(gF), this.addDetector(wF), this.addDetector(xF), this.addDetector(AF), this.addDetector(kF), this.addDetector(SF), this.addDetector(MF) } }, { key: "addDetector", value: function(e) { this.detectors[e.name] = e } }, { key: "detect", value: function(e) { var t = this;
                    e || (e = this.options.order); var n, r = []; if (e.forEach((function(e) { if (t.detectors[e]) { var n = t.detectors[e].lookup(t.options);
                                n && "string" === typeof n && (n = [n]), n && (r = r.concat(n)) } })), r.forEach((function(e) { if (!n) { var r = t.services.languageUtils.formatLanguageCode(e);
                                t.options.checkWhitelist && !t.services.languageUtils.isWhitelisted(r) || (n = r), !n && t.options.checkForSimilarInWhitelist && (n = t.getSimilarInWhitelist(r)) } })), !n) { var a = this.i18nOptions.fallbackLng; "string" === typeof a && (a = [a]), a || (a = []), n = "[object Array]" === Object.prototype.toString.apply(a) ? a[0] : a[0] || a.default && a.default[0] } return n } }, { key: "cacheUserLanguage", value: function(e, t) { var n = this;
                    t || (t = this.options.caches), t && (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(e) > -1 || t.forEach((function(t) { n.detectors[t] && n.detectors[t].cacheUserLanguage(e, n.options) }))) } }, { key: "getSimilarInWhitelist", value: function(e) { var t = this; if (this.i18nOptions.whitelist) { if (e.includes("-")) { var n = e.split("-")[0],
                                r = this.services.languageUtils.formatLanguageCode(n); if (this.services.languageUtils.isWhitelisted(r)) return r;
                            e = r } var a = this.i18nOptions.whitelist.find((function(n) { var r = t.services.languageUtils.formatLanguageCode(n); if (r.startsWith(e)) return r })); return a || void 0 } } }]), e }();
        EF.type = "languageDetector"; const CF = EF; let TF = { fallbackLng: "en", debug: !0, whitelist: ["en"], interpolation: { escapeValue: !1 } }; /develop.organimi.com/.test(window.location.origin) ? TF = { ...TF, backend: { loadPath: "https://organimi-client-develop.s3.amazonaws.com/locales/{{lng}}/{{ns}}.json" } } : /localhost/.test(window.location.origin) || (TF = { ...TF, backend: { loadPath: "https://organimi-client.s3.amazonaws.com/locales/{{lng}}/{{ns}}.json" } }), BD.use(cF).use(CF).use(WD.r9).init(TF); var HF = n(49244),
            LF = n(22696); const IF = () => { var e, t; const n = (document.location.pathname.match(/^\/(\w+)\//) || [])[1] || null,
                r = i().parse(null === (e = document) || void 0 === e || null === (t = e.location) || void 0 === t ? void 0 : t.search, { ignoreQueryPrefix: !0 }) || {},
                a = r.pId,
                o = r.cbId; return n ? "embed" === n ? "embed.".concat(a, ".") : "public" === n ? "public.".concat(a, ".") : "community" === n ? "cb.".concat(o, ".") : "" : "" };
        window.localStorageSafe = { localStoreSupport: function() { try { return "localStorage" in window && null !== window.localStorage } catch (FF) { return !1 } }, cookieSupport: function() { return !!navigator.cookieEnabled }, setItem: function(e, t) { if (this.localStoreSupport()) { const n = IF();
                    localStorage.setItem("".concat(n).concat(e), t) } else this.cookieSupport() ? document.cookie = e + "=" + t + "; path=/" : window.localStorageFallback = { ...window.localStorageFallback || {}, [e]: t } }, getItem: function(e) { var t; if (!this.localStoreSupport()) { if (this.cookieSupport()) { for (var n = e + "=", r = document.cookie.split(";"), a = 0; a < r.length; a++) { for (var o = r[a];
                                " " === o.charAt(0);) o = o.substring(1, o.length); if (0 === o.indexOf(n)) switch (t = o.substring(n.length, o.length)) {
                                case "true":
                                    return !0;
                                case "false":
                                    return !1;
                                default:
                                    return t } } return null } return (window.localStorageFallback || {})[e] } { const n = IF(); if (t = localStorage.getItem("".concat(n).concat(e)), !n && "publicId" === e) return localStorage.removeItem("publicId"), null; switch (t) {
                        case "true":
                            return !0;
                        case "false":
                            return !1;
                        default:
                            return t } } }, removeItem: function(e) { if (this.localStoreSupport()) { const t = IF(); return localStorage.removeItem("".concat(t).concat(e)), !0 } return this.cookieSupport() ? (document.cookie = e + "=; path=/", !0) : (delete(window.localStorageFallback || {})[e], !0) } }; var jF = n(31672),
            VF = n.n(jF); const OF = () => ((0, a.useEffect)((() => { VF().load({ google: { families: ["Source Sans Pro:400,600,700,400italic,700italic", "Poppins:300,400,500,600,700"] } }) }), []), null);
        (0, LF.setDefaultBreakpoints)([{ xs: 0 }, { small: 662 }, { medium: 800 }]); const RF = () => { const [e] = (0, un.A)("isDesktopMode", !1, !0), t = window.location.pathname.includes("embed/"), n = window.location.pathname.includes("community/"); return (0, we.jsx)(LF.BreakpointProvider, { children: e || t || n ? (0, we.jsx)(rj, {}) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(LF.Breakpoint, { xs: !0, only: !0, className: "breakpoint", children: (0, we.jsx)(fP, {}) }), (0, we.jsx)(LF.Breakpoint, { small: !0, up: !0, className: "breakpoint", children: (0, we.jsx)(rj, {}) })] }) }) },
            PF = e => { const { which: t, ctrlKey: n, metaKey: r } = e;
                (n || r || 91 === t || 93 === t) && "wheel" === e.type && e.preventDefault() };
        document.addEventListener("keydown", PF), document.addEventListener("wheel", PF); const DF = document.getElementById("root");
        (0, r.H)(DF).render((0, we.jsx)(UP.Ay, { injectFirst: !0, children: (0, we.jsx)(rD, { theme: YE, children: (0, we.jsx)(qP.A, { theme: rH.A, children: (0, we.jsx)(ee.NP, { theme: rH.A, children: (0, we.jsx)(ae.Kq, { store: WP, children: (0, we.jsxs)(HF.A, { children: [(0, we.jsx)(OF, {}), (0, we.jsx)(RF, {})] }) }) }) }) }) })), "serviceWorker" in navigator && navigator.serviceWorker.ready.then((e => { e.unregister() })) })() })();
//# sourceMappingURL=main.9e9a31d6.js.map
ZOe = (0, mt.A)(a.createElement("path", { d: "M4 14h4v-4H4v4zm0 5h4v-4H4v4zM4 9h4V5H4v4zm5 5h12v-4H9v4zm0 5h12v-4H9v4zM9 5v4h12V5H9z" }), "ViewList"),
                    YOe = (0, mt.A)(a.createElement("path", { d: "M3 5v14h17V5H3zm4 2v2H5V7h2zm-2 6v-2h2v2H5zm0 2h2v2H5v-2zm13 2H9v-2h9v2zm0-4H9v-2h9v2zm0-4H9V7h9v2z" }), "ViewListOutlined"),
                    XOe = (0, mt.A)(a.createElement("path", { d: "M4 14h2c.55 0 1-.45 1-1v-2c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v2c0 .55.45 1 1 1zm0 5h2c.55 0 1-.45 1-1v-2c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v2c0 .55.45 1 1 1zM4 9h2c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v2c0 .55.45 1 1 1zm5 5h10c.55 0 1-.45 1-1v-2c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v2c0 .55.45 1 1 1zm0 5h10c.55 0 1-.45 1-1v-2c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v2c0 .55.45 1 1 1zM8 6v2c0 .55.45 1 1 1h10c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1z" }), "ViewListRounded"),
                    $Oe = (0, mt.A)(a.createElement("path", { d: "M3 14h4v-4H3v4zm0 5h4v-4H3v4zM3 9h4V5H3v4zm5 5h12v-4H8v4zm0 5h12v-4H8v4zM8 5v4h12V5H8z" }), "ViewListSharp"),
                    QOe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M5 11h2v2H5zm0 4h2v2H5zm0-8h2v2H5zm4 0h9v2H9zm0 8h9v2H9zm0-4h9v2H9z", opacity: ".3" }), a.createElement("path", { d: "M3 5v14h17V5H3zm4 12H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V7h2v2zm11 8H9v-2h9v2zm0-4H9v-2h9v2zm0-4H9V7h9v2z" })), "ViewListTwoTone"),
                    JOe = (0, mt.A)(a.createElement("path", { d: "M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z" }), "ViewModule"),
                    eRe = (0, mt.A)(a.createElement("path", { d: "M4 5v13h17V5H4zm10 2v3.5h-3V7h3zM6 7h3v3.5H6V7zm0 9v-3.5h3V16H6zm5 0v-3.5h3V16h-3zm8 0h-3v-3.5h3V16zm-3-5.5V7h3v3.5h-3z" }), "ViewModuleOutlined"),
                    tRe = (0, mt.A)(a.createElement("path", { d: "M5 11h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1H5c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm0 7h3c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1H5c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm6 0h3c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm6 0h3c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm-6-7h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm5-5v4c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1z" }), "ViewModuleRounded"),
                    nRe = (0, mt.A)(a.createElement("path", { d: "M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z" }), "ViewModuleSharp"),
                    rRe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "2-4.63-9.32-9.83-9.84zM17 1.01L7 1c-1.1 0-2 .9-2 2v7.37c.69.16 1.36.37 2 .64V5h10v13h-3.03c.52 1.25.84 2.59.95 4H17c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99z\" }), \"TapAndPlayRounded\"),\n                    xCe = (0, mt.A)(a.createElement(\"path\", { d: \"M2 16v2c2.76 0 5 2.24 5 5h2c0-3.87-3.13-7-7-7zm0 4v3h3c0-1.66-1.34-3-3-3zm0-8v2c4.97 0 9 4.03 9 9h2c0-6.08-4.92-11-11-11zM5 1v9.37c.69.16 1.36.37 2 .64V5h10v13h-3.03c.52 1.25.84 2.59.95 4H19V1H5z\" }), \"TapAndPlaySharp\"),\n                    ACe = (0, mt.A)(a.createElement(\"path\", { d: \"M2 16v2c2.76 0 5 2.24 5 5h2c0-3.87-3.13-7-7-7zm0 4v3h3c0-1.66-1.34-3-3-3zm0-8v2c4.97 0 9 4.03 9 9h2c0-6.08-4.92-11-11-11zM17 1.01L7 1c-1.1 0-2 .9-2 2v7.37c.69.16 1.36.37 2 .64V5h10v13h-3.03c.52 1.25.84 2.59.95 4H17c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99z\" }), \"TapAndPlayTwoTone\"),\n                    kCe = (0, mt.A)(a.createElement(\"path\", { d: \"M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z\" }), \"Telegram\"),\n                    SCe = (0, mt.A)(a.createElement(\"path\", { d: \"M14 6l-3.75 5 2.85 3.8-1.6 1.2C9.81 13.75 7 10 7 10l-6 8h22L14 6z\" }), \"Terrain\"),\n                    MCe = (0, mt.A)(a.createElement(\"path\", { d: \"M14 6l-4.22 5.63 1.25 1.67L14 9.33 19 16h-8.46l-4.01-5.37L1 18h22L14 6zM5 16l1.52-2.03L8.04 16H5z\" }), \"TerrainOutlined\"),\n                    ECe = (0, mt.A)(a.createElement(\"path\", { d: \"M13.2 7.07L10.25 11l2.25 3c.33.44.24 1.07-.2 1.4-.44.33-1.07.25-1.4-.2-1.05-1.4-2.31-3.07-3.1-4.14-.4-.53-1.2-.53-1.6 0l-4 5.33c-.49.67-.02 1.61.8 1.61h18c.82 0 1.29-.94.8-1.6l-7-9.33c-.4-.54-1.2-.54-1.6 0z\" }), \"TerrainRounded\"),\n                    CCe = (0, mt.A)(a.createElement(\"path\", { d: \"M14 6l-3.75 5 2.85 3.8-1.6 1.2C9.81 13.75 7 10 7 10l-6 8h22L14 6z\" }), \"TerrainSharp\"),\n                    TCe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement(\"path\", { d: \"M5 16h3.04l-1.52-2.03z\", opacity: \".3\" }), a.createElement(\"path\", { d: \"M9.78 11.63l1.25 1.67L14 9.33 19 16h-8.46l-4.01-5.37L1 18h22L14 6l-4.22 5.63zM5 16l1.52-2.03L8.04 16H5z\" })), \"TerrainTwoTone\"),\n                    HCe = (0, mt.A)(a.createElement(\"path\", { d: \"M2.5 4v3h5v12h3V7h5V4h-13zm19 5h-9v3h3v7h3v-7h3V9z\" }), \"TextFields\"),\n                    LCe = (0, mt.A)(a.createElement(\"path\", { d: \"M2.5 4v3h5v12h3V7h5V4h-13zm19 5h-9v3h3v7h3v-7h3V9z\" }), \"TextFieldsOutlined\"),\n                    ICe = (0, mt.A)(a.createElement(\"path\", { d: \"M2.5 5.5C2.5 6.33 3.17 7 4 7h3.5v10.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V7H14c.83 0 1.5-.67 1.5-1.5S14.83 4 14 4H4c-.83 0-1.5.67-1.5 1.5zM20 9h-6c-.83 0-1.5.67-1.5 1.5S13.17 12 14 12h1.5v5.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V12H20c.83 0 1.5-.67 1.5-1.5S20.83 9 20 9z\" }), \"TextFieldsRounded\"),\n                    jCe = (0, mt.A)(a.createElement(\"path\", { d: \"M2.5 4v3h5v12h3V7h5V4h-13zm19 5h-9v3h3v7h3v-7h3V9z\" }), \"TextFieldsSharp\"),\n                    VCe = (0, mt.A)(a.createElement(\"path\", { d: \"M12.5 12h3v7h3v-7h3V9h-9zm3-8h-13v3h5v12h3V7h5z\" }), \"TextFieldsTwoTone\"),\n                    OCe = (0, mt.A)(a.createElement(\"path\", { d: \"M5 17v2h14v-2H5zm4.5-4.2h5l.9 2.2h2.1L12.75 4h-1.5L6.5 15h2.1l.9-2.2zM12 5.98L13.87 11h-3.74L12 5.98z\" }), \"TextFormat\"),\n                    RCe = (0, mt.A)(a.createElement(\"path\", { d: \"M5 17v2h14v-2H5zm4.5-4.2h5l.9 2.2h2.1L12.75 4h-1.5L6.5 15h2.1l.9-2.2zM12 5.98L13.87 11h-3.74L12 5.98z\" }), \"TextFormatOutlined\"),\n                    PCe = (0, mt.A)(a.createElement(\"path\", { d: \"M5 18c0 .55.45 1 1 1h12c.55 0 1-.45 1-1s-.45-1-1-1H6c-.55 0-1 .45-1 1zm4.5-5.2h5l.66 1.6c.15.36.5.6.89.6.69 0 1.15-.71.88-1.34l-3.88-8.97C12.87 4.27 12.46 4 12 4c-.46 0-.87.27-1.05.69l-3.88 8.97c-.27.63.2 1.34.89 1.34.39 0 .74-.24.89-.6l.65-1.6zM12 5.98L13.87 11h-3.74L12 5.98z\" }), \"TextFormatRounded\"),\n                    DCe = (0, mt.A)(a.createElement(\"path\", { d: \"M5 17v2h14v-2H5zm4.5-4.2h5l.9 2.2h2.1L12.75 4h-1.5L6.5 15h2.1l.9-2.2zM12 5.98L13.87 11h-3.74L12 5.98z\" }), \"TextFormatSharp\"),\n                    FCe = (0, mt.A)(a.createElement(\"path\", { d: \"M5 17v2h14v-2H5zm4.5-4.2h5l.9 2.2h2.1L12.75 4h-1.5L6.5 15h2.1l.9-2.2zM12 5.98L13.87 11h-3.74L12 5.98z\" }), \"TextFormatTwoTone\"),\n                    NCe = (0, mt.A)(a.createElement(\"path\", { d: \"M3 12v1.5l11 4.75v-2.1l-2.2-.9v-5l2.2-.9v-2.1L3 12zm7 2.62l-5.02-1.87L10 10.88v3.74zm8-10.37l-3 3h2v12.5h2V7.25h2l-3-3z\" }), \"TextRotateUp\"),\n                    _Ce = (0, mt.A)(a.createElement(\"path\", { d: \"M18 4l-3 3h2v13h2V7h2l-3-3zm-6.2 11.5v-5l2.2-.9V7.5L3 12.25v1.5l11 4.75v-2.1l-2.2-.9zM4.98 13L10 11.13v3.74L4.98 13z\" }), \"TextRotateUpOutlined\"),\n                    BCe = (0, mt.A)(a.createElement(\"path\", { d: \"M18.35 4.35c-.2-.2-.51-.2-.71 0l-1.79 1.79c-.31.32-.09.86.36.86H17v12c0 .55.45 1 1 1s1-.45 1-1V7h.79c.45 0 .67-.54.35-.85l-1.79-1.8zM11.8 15.5v-5l1.6-.66c.36-.14.6-.49.6-.88 0-.69-.71-1.15-1.34-.88l-8.97 3.88c-.42.17-.69.58-.69 1.04 0 .46.27.87.69 1.05l8.97 3.88c.63.27 1.34-.2 1.34-.89 0-.39-.24-.74-.6-.89l-1.6-.65zM4.98 13L10 11.13v3.74L4.98 13z\" }), \"TextRotateUpRounded\"),\n                    WCe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 4l-3 3h2v13h2V7h2l-3-3zm-6.2 11.5v-5l2.2-.9V7.5L3 12.25v1.5l11 4.75v-2.1l-2.2-.9zM4.98 13L10 11.13v3.74L4.98 13z\" }), \"TextRotateUpSharp\"),\n                    UCe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 4l-3 3h2v13h2V7h2l-3-3zm-6.2 11.5v-5l2.2-.9V7.5L3 12.25v1.5l11 4.75v-2.1l-2.2-.9zM4.98 13L10 11.13v3.74L4.98 13z\" }), \"TextRotateUpTwoTone\"),\n                    qCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15.75 5h-1.5L9.5 16h2.1l.9-2.2h5l.9 2.2h2.1L15.75 5zm-2.62 7L15 6.98 16.87 12h-3.74zM6 19.75l3-3H7V4.25H5v12.5H3l3 3z\" }), \"TextRotateVertical\"),\n                    GCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15.75 5h-1.5L9.5 16h2.1l.9-2.2h5l.9 2.2h2.1L15.75 5zm-2.62 7L15 6.98 16.87 12h-3.74zM6 20l3-3H7V4H5v13H3l3 3z\" }), \"TextRotateVerticalOutlined\"),\n                    KCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15 5c-.46 0-.87.27-1.05.69l-3.88 8.97c-.27.63.2 1.34.89 1.34.39 0 .74-.24.89-.6l.66-1.6h5l.66 1.6c.15.36.5.6.89.6.69 0 1.15-.71.88-1.34l-3.88-8.97C15.87 5.27 15.46 5 15 5zm-1.87 7L15 6.98 16.87 12h-3.74zm-6.78 7.64l1.79-1.79c.32-.31.1-.85-.35-.85H7V5c0-.55-.45-1-1-1s-1 .44-1 1v12h-.79c-.45 0-.67.54-.35.85l1.79 1.79c.19.2.51.2.7 0z\" }), \"TextRotateVerticalRounded\"),\n                    ZCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15.75 5h-1.5L9.5 16h2.1l.9-2.2h5l.9 2.2h2.1L15.75 5zm-2.62 7L15 6.98 16.87 12h-3.74zM6 20l3-3H7V4H5v13H3l3 3z\" }), \"TextRotateVerticalSharp\"),\n                    YCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15.75 5h-1.5L9.5 16h2.1l.9-2.2h5l.9 2.2h2.1L15.75 5zm-2.62 7L15 6.98 16.87 12h-3.74zM6 20l3-3H7V4H5v13H3l3 3z\" }), \"TextRotateVerticalTwoTone\"),\n                    XCe = (0, mt.A)(a.createElement(\"path\", { d: \"M19.4 4.91l-1.06-1.06L7.2 8.27l1.48 1.48 2.19-.92 3.54 3.54-.92 2.19 1.48 1.48L19.4 4.91zm-6.81 3.1l4.87-2.23-2.23 4.87-2.64-2.64zM14.27 21v-4.24l-1.41 1.41-8.84-8.84-1.42 1.42 8.84 8.84L10.03 21h4.24z\" }), \"TextRotationAngledown\"),\n                    $Ce = (0, mt.A)(a.createElement(\"path\", { d: \"M15 21v-4.24l-1.41 1.41-9.2-9.19-1.41 1.41 9.19 9.19L10.76 21H15zM11.25 8.48l3.54 3.54-.92 2.19 1.48 1.48 4.42-11.14-1.06-1.05L7.57 7.92 9.06 9.4l2.19-.92zm6.59-3.05l-2.23 4.87-2.64-2.64 4.87-2.23z\" }), \"TextRotationAngledownOutlined\"),\n                    QCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15 20.5v-2.54c0-.45-.54-.67-.85-.35l-.56.56L5.1 9.68a.9959.9959 0 00-1.41 0c-.39.39-.39 1.02 0 1.41l8.49 8.49-.56.56c-.32.32-.1.86.34.86h2.54c.28 0 .5-.23.5-.5zM11.25 8.48l3.54 3.54-.67 1.6c-.15.36-.07.77.21 1.05.49.49 1.31.32 1.57-.32l3.61-9.09c.17-.42.07-.91-.25-1.23-.32-.32-.8-.42-1.23-.25l-9.1 3.6c-.64.25-.81 1.08-.32 1.57.27.27.68.35 1.04.2l1.6-.67zm6.59-3.05l-2.23 4.87-2.64-2.64 4.87-2.23z\" }), \"TextRotationAngledownRounded\"),\n                    JCe = (0, mt.A)(a.createElement(\"path\", { d: \"M15 21v-4.24l-1.41 1.41-9.2-9.19-1.41 1.41 9.19 9.19L10.76 21H15zM11.25 8.48l3.54 3.54-.92 2.19 1.48 1.48 4.42-11.14-1.06-1.05L7.57 7.92 9.06 9.4l2.19-.92zm6.59-3.05l-2.23 4.87-2.64-2.64 4.87-2.23z\" }), \"TextRotationAngledownSharp\"),\n                    eTe = (0, mt.A)(a.createElement(\"path\", { d: \"M15 21v-4.24l-1.41 1.41-9.2-9.19-1.41 1.41 9.19 9.19L10.76 21H15zM11.25 8.48l3.54 3.54-.92 2.19 1.48 1.48 4.42-11.14-1.06-1.05L7.57 7.92 9.06 9.4l2.19-.92zm6.59-3.05l-2.23 4.87-2.64-2.64 4.87-2.23z\" }), \"TextRotationAngledownTwoTone\"),\n                    tTe = (0, mt.A)(a.createElement(\"path\", { d: \"M4.49 4.21L3.43 5.27 7.85 16.4l1.48-1.48-.92-2.19 3.54-3.54 2.19.92 1.48-1.48L4.49 4.21zm3.09 6.8L5.36 6.14l4.87 2.23-2.65 2.64zm12.99-1.68h-4.24l1.41 1.41-8.84 8.84L10.32 21l8.84-8.84 1.41 1.41V9.33z\" }), \"TextRotationAngleup\"),\n                    nTe = (0, mt.A)(a.createElement(\"path\", { d: \"M16.76 9l1.41 1.41-9.19 9.19 1.41 1.41 9.19-9.19L21 13.24V9h-4.24zm-8.28 3.75l3.54-3.54 2.19.92 1.48-1.48L4.56 4.23 3.5 5.29l4.42 11.14 1.48-1.48-.92-2.2zm-.82-1.72L5.43 6.16l4.87 2.23-2.64 2.64z\" }), \"TextRotationAngleupOutlined\"),\n                    rTe = (0, mt.A)(a.createElement(\"path\", { d: \"M17.61 9.85l.56.56-8.48 8.49c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0l8.49-8.49.56.56c.31.32.85.1.85-.34V9.5c0-.28-.22-.5-.5-.5h-2.54c-.44 0-.66.54-.35.85zm-9.13 2.9l3.54-3.54 1.6.67c.36.15.77.07 1.05-.21.49-.49.32-1.31-.32-1.57L5.26 4.5c-.43-.16-.91-.06-1.23.26-.32.32-.42.8-.25 1.23l3.61 9.09c.25.64 1.08.81 1.57.32.28-.28.36-.69.21-1.05l-.69-1.6zm-.82-1.72L5.43 6.16l4.87 2.23-2.64 2.64z\" }), \"TextRotationAngleupRounded\"),\n                    aTe = (0, mt.A)(a.createElement(\"path\", { d: \"M16.76 9l1.41 1.41-9.19 9.19 1.41 1.41 9.19-9.19L21 13.24V9h-4.24zm-8.28 3.75l3.54-3.54 2.19.92 1.48-1.48L4.56 4.23 3.5 5.29l4.42 11.14 1.48-1.48-.92-2.2zm-.82-1.72L5.43 6.16l4.87 2.23-2.64 2.64z\" }), \"TextRotationAngleupSharp\"),\n                    oTe = (0, mt.A)(a.createElement(\"path\", { d: \"M16.76 9l1.41 1.41-9.19 9.19 1.41 1.41 9.19-9.19L21 13.24V9h-4.24zm-8.28 3.75l3.54-3.54 2.19.92 1.48-1.48L4.56 4.23 3.5 5.29l4.42 11.14 1.48-1.48-.92-2.2zm-.82-1.72L5.43 6.16l4.87 2.23-2.64 2.64z\" }), \"TextRotationAngleupTwoTone\"),\n                    iTe = (0, mt.A)(a.createElement(\"path\", { d: \"M21 12v-1.5L10 5.75v2.1l2.2.9v5l-2.2.9v2.1L21 12zm-7-2.62l5.02 1.87L14 13.12V9.38zM6 19.75l3-3H7V4.25H5v12.5H3l3 3z\" }), \"TextRotationDown\"),\n                    lTe = (0, mt.A)(a.createElement(\"path\", { d: \"M6 20l3-3H7V4H5v13H3l3 3zm6.2-11.5v5l-2.2.9v2.1l11-4.75v-1.5L10 5.5v2.1l2.2.9zm6.82 2.5L14 12.87V9.13L19.02 11z\" }), \"TextRotationDownOutlined\"),\n                    sTe = (0, mt.A)(a.createElement(\"path\", { d: \"M6.35 19.65l1.79-1.79c.32-.32.1-.86-.35-.86H7V5c0-.55-.45-1-1-1s-1 .45-1 1v12h-.79c-.45 0-.67.54-.35.85l1.79 1.79c.19.2.51.2.7.01zM12.2 8.5v5l-1.6.66c-.36.15-.6.5-.6.89 0 .69.71 1.15 1.34.88l8.97-3.88c.42-.18.69-.59.69-1.05 0-.46-.27-.87-.69-1.05l-8.97-3.88c-.63-.27-1.34.2-1.34.89 0 .39.24.74.6.89l1.6.65zm6.82 2.5L14 12.87V9.13L19.02 11z\" }), \"TextRotationDownRounded\"),\n                    cTe = (0, mt.A)(a.createElement(\"path\", { d: \"M6 20l3-3H7V4H5v13H3l3 3zm6.2-11.5v5l-2.2.9v2.1l11-4.75v-1.5L10 5.5v2.1l2.2.9zm6.82 2.5L14 12.87V9.13L19.02 11z\" }), \"TextRotationDownSharp\"),\n                    dTe = (0, mt.A)(a.createElement(\"path\", { d: \"M6 20l3-3H7V4H5v13H3l3 3zm6.2-11.5v5l-2.2.9v2.1l11-4.75v-1.5L10 5.5v2.1l2.2.9zm6.82 2.5L14 12.87V9.13L19.02 11z\" }), \"TextRotationDownTwoTone\"),\n                    uTe = (0, mt.A)(a.createElement(\"path\", { d: \"M12.75 3h-1.5L6.5 14h2.1l.9-2.2h5l.9 2.2h2.1L12.75 3zm-2.62 7L12 4.98 13.87 10h-3.74zm10.37 8l-3-3v2H5v2h12.5v2l3-3z\" }), \"TextRotationNone\"),\n                    hTe = (0, mt.A)(a.createElement(\"path\", { d: \"M21 18l-3-3v2H5v2h13v2l3-3zM9.5 11.8h5l.9 2.2h2.1L12.75 3h-1.5L6.5 14h2.1l.9-2.2zM12 4.98L13.87 10h-3.74L12 4.98z\" }), \"TextRotationNoneOutlined\"),\n                    mTe = (0, mt.A)(a.createElement(\"path\", { d: \"M20.65 17.65l-1.79-1.79c-.32-.32-.86-.1-.86.35V17H6c-.55 0-1 .45-1 1s.45 1 1 1h12v.79c0 .45.54.67.85.35l1.79-1.79c.2-.19.2-.51.01-.7zM9.5 11.8h5l.66 1.6c.15.36.5.6.89.6.69 0 1.15-.71.88-1.34l-3.88-8.97C12.87 3.27 12.46 3 12 3c-.46 0-.87.27-1.05.69l-3.88 8.97c-.27.63.2 1.34.89 1.34.39 0 .74-.24.89-.6l.65-1.6zM12 4.98L13.87 10h-3.74L12 4.98z\" }), \"TextRotationNoneRounded\"),\n                    pTe = (0, mt.A)(a.createElement(\"path\", { d: \"M21 18l-3-3v2H5v2h13v2l3-3zM9.5 11.8h5l.9 2.2h2.1L12.75 3h-1.5L6.5 14h2.1l.9-2.2zM12 4.98L13.87 10h-3.74L12 4.98z\" }), \"TextRotationNoneSharp\"),\n                    fTe = (0, mt.A)(a.createElement(\"path\", { d: \"M21 18l-3-3v2H5v2h13v2l3-3zM9.5 11.8h5l.9 2.2h2.1L12.75 3h-1.5L6.5 14h2.1l.9-2.2zM12 4.98L13.87 10h-3.74L12 4.98z\" }), \"TextRotationNoneTwoTone\"),\n                    vTe = (0, mt.A)(a.createElement(\"path\", { d: \"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM9 11H7V9h2v2zm4 0h-2V9h2v2zm4 0h-2V9h2v2z\" }), \"Textsms\"),\n                    gTe = (0, mt.A)(a.createElement(\"path\", { d: \"M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12zM7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z\" }), \"TextsmsOutlined\"),\n                    yTe = (0, mt.A)(a.createElement(\"path\", { d: \"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM9 11H7V9h2v2zm4 0h-2V9h2v2zm4 0h-2V9h2v2z\" }), \"TextsmsRounded\"),\n                    bTe = (0, mt.A)(a.createElement(\"path\", { d: \"M22 2H2.01L2 22l4-4h16V2zM9 11H7V9h2v2zm4 0h-2V9h2v2zm4 0h-2V9h2v2z\" }), \"TextsmsSharp\"),\n                    wTe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement(\"path\", { d: \"M4 18l2-2h14V4H4v14zm11-9h2v2h-2V9zm-4 0h2v2h-2V9zM7 9h2v2H7V9z\", opacity: \".3\" }), a.createElement(\"path\", { d: \"M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12zM7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z\" })), \"TextsmsTwoTone\"),\n                    zTe = (0, mt.A)(a.createElement(\"path\", { d: \"M19.51 3.08L3.08 19.51c.09.34.27.65.51.9.25.24.56.42.9.51L20.93 4.49c-.19-.69-.73-1.23-1.42-1.41zM11.88 3L3 11.88v2.83L14.71 3h-2.83zM5 3c-1.1 0-2 .9-2 2v2l4-4H5zm14 18c.55 0 1.05-.22 1.41-.59.37-.36.59-.86.59-1.41v-2l-4 4h2zm-9.71 0h2.83L21 12.12V9.29L9.29 21z\" }), \"Texture\"),\n                    xTe = (0, mt.A)(a.createElement(\"path\", { d: \"M19.51 3.08L3.08 19.51c.09.34.27.65.51.9.25.24.56.42.9.51L20.93 4.49c-.19-.69-.73-1.23-1.42-1.41zM11.88 3L3 11.88v2.83L14.71 3h-2.83zM5 3c-1.1 0-2 .9-2 2v2l4-4H5zm14 18c.55 0 1.05-.22 1.41-.59.37-.36.59-.86.59-1.41v-2l-4 4h2zm-9.71 0h2.83L21 12.12V9.29L9.29 21z\" }), \"TextureOutlined\"),\n                    ATe = (0, mt.A)(a.createElement(\"path\", { d: \"M19.58 3.08L3.15 19.51c.09.34.27.65.51.9.25.24.56.42.9.51L21 4.49c-.19-.69-.73-1.23-1.42-1.41zM11.95 3l-8.88 8.88v2.83L14.78 3h-2.83zM5.07 3c-1.1 0-2 .9-2 2v2l4-4h-2zm14 18c.55 0 1.05-.22 1.41-.59.37-.36.59-.86.59-1.41v-2l-4 4h2zm-9.71 0h2.83l8.88-8.88V9.29L9.36 21z\" }), \"TextureRounded\"),\n                    kTe = (0, mt.A)(a.createElement(\"path\", { d: \"M19.66 3L3.07 19.59V21h1.41L21.07 4.42V3zm-7.71 0l-8.88 8.88v2.83L14.78 3zM3.07 3v4l4-4zm18 18v-4l-4 4zm-8.88 0l8.88-8.88V9.29L9.36 21z\" }), \"TextureSharp\"),\n                    STe = (0, mt.A)(a.createElement(\"path\", { d: \"M11.88 3L3 11.88v2.83L14.71 3zM3 5v2l4-4H5c-1.1 0-2 .9-2 2zm16.51-1.92L3.08 19.51c.09.34.27.65.51.9.25.24.56.42.9.51L20.93 4.49c-.19-.69-.73-1.23-1.42-1.41zM21 9.29L9.29 21h2.83L21 12.12zm-.59 11.12c.37-.36.59-.86.59-1.41v-2l-4 4h2c.55 0 1.05-.22 1.41-.59z\" }), \"TextureTwoTone\"),\n                    MTe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z\" }), \"Theaters\"),\n                    ETe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm6 10h-4V5h4v14zm4-2h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z\" }), \"TheatersOutlined\"),\n                    CTe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 4v1h-2V4c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v1H6V4c0-.55-.45-1-1-1s-1 .45-1 1v16c0 .55.45 1 1 1s1-.45 1-1v-1h2v1c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-1h2v1c0 .55.45 1 1 1s1-.45 1-1V4c0-.55-.45-1-1-1s-1 .45-1 1zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z\" }), \"TheatersRounded\"),\n                    TTe = (0, mt.A)(a.createElement(\"path\", { d: \"M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z\" }), \"TheatersSharp\"),\n                    HTe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement(\"path\", { d: \"M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm6 10h-4V5h4v14zm4-2h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z\" }), a.createElement(\"path\", { d: \"M10 5h4v14h-4z\", opacity: \".3\" })), \"TheatersTwoTone\"),\n                    LTe = (0, mt.A)(a.createElem