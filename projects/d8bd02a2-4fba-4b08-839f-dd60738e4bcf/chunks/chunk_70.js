                (0, a.useEffect)((() => { ve.A.trackEvent({ eventName: "MOBILE_VIEW_OPEN" }) }), []); const { params: { base: u } } = c || { params: {} }, { pathname: h } = (0, $.useLocation)(), m = !uP.includes(u) && "/getstarted" !== h && "/thankyou" !== h && "/failed" !== h && "/welcome" !== h && "/forgotRequested" !== h; return t && !e || !o ? (0, we.jsx)(gI, {}) : o && n ? (0, we.jsx)(gI, { message: "Account Loading" }) : !t || l || "/thankyou" === h || uP.includes(u) ? !t || d || uP.includes(u) ? r && !uP.includes(u) ? (0, we.jsx)(At, {}) : t ? i ? (0, we.jsx)($.Redirect, { to: i }) : (0, we.jsxs)(a.Suspense, { fallback: () => {}, children: [(0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", flex: 1, overflow: "auto", children: [m && (0, we.jsx)(iO, {}), ["charts", "people"].includes(s) && (0, we.jsx)($.Route, { path: (0, ne.K7)(), component: hP }), (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.A$)(), component: At }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.Mz)(), component: zj }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mF)(), component: St }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.qM)(), component: TH }), (0, we.jsx)($.Route, { path: (0, ne.r2)(), component: JV }), (0, we.jsx)($.Route, { path: (0, ne.Zm)(), component: JV }), (0, we.jsx)($.Route, { path: (0, ne.ze)(), component: JV }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.si)(), render: e => { let { match: t } = e; const { params: { base: n } } = t || {}; return (0, we.jsx)(cP, {}) } }), (0, we.jsx)($.Redirect, { to: (0, ne.ze)() })] })] }), (0, we.jsx)($.Route, { path: [(0, ne.K7)(), (0, ne.si)(), (0, ne.ze)()], component: yO })] }) : (0, we.jsx)(a.Suspense, { fallback: () => {}, children: (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.kz)(), component: cj }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.iD)(), component: dj }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.A$)(), component: At }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.yZ)(), component: kt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mF)(), component: St }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.wO)(), component: xt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.qM)(), component: TH }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.si)(), render: e => { let { match: t } = e; const { params: { base: n } } = t || {}; return uP.includes(n) ? (0, we.jsx)(AI, { children: (0, we.jsx)(cP, {}) }) : (0, we.jsx)($.Redirect, { to: (0, ne.iD)() }) } }), (0, we.jsx)($.Redirect, { to: (0, ne.iD)() })] }) }) : (0, we.jsx)(dn.A, { transparent: !0, loading: n, children: (0, we.jsx)(mP, {}) }) : (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.Mz)(), component: zj }), (0, we.jsx)($.Redirect, { to: (0, ne.Mz)() }), ";"] }) },
            fP = () => (0, we.jsx)(NI.A, { children: (0, we.jsx)(DI.A, { children: (0, we.jsx)(dP, { children: (0, we.jsx)(CI.Ay, { children: (0, we.jsx)(X.Kd, { children: (0, we.jsx)($.Route, { path: "/:base?", component: pP }) }) }) }) }) }); var vP = n(77048); const gP = (e, t) => { let { payload: n } = t; const { changedCharts: r, theme: a } = n; for (let o of r || []) { const { id: t } = o || {}, n = e.charts.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === t)); if (-1 !== n) { const t = e.charts[n];
                        e.charts[n] = { ...t, theme: (null === a || void 0 === a ? void 0 : a.id) || t.theme, v6FormatMigrated: !0 } } } },
            yP = (0, Pr.Z0)({ name: "dashboard", initialState: { charts: [] }, extraReducers: { "import/getAvailableIntegrations/fulfilled": (e, t) => { let { payload: { available: n } } = t;
                        e.integrationsAvailable = n }, "user/getCharts/fulfilled": (e, t) => { e.charts = t.payload.charts }, "chart/delete/fulfilled": (e, t) => { var n, r;
                        null !== (n = t.payload) && void 0 !== n && null !== (r = n.charts) && void 0 !== r && r.length && (e.charts = e.charts.filter((e => !t.payload.charts.map((e => e.id)).includes(e.id)))) }, "chart/createAliasLink/fulfilled": (e, t) => { e.charts.push(t.payload.chart) }, "chart/create/fulfilled": (e, t) => { e.charts.push(t.payload.chart) }, "chart/createDemo/fulfilled": (e, t) => { var n; const r = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.chart;
                        r && e.charts.unshift(r) }, "chart/migrateFormat/fulfilled": gP, "chart/get/fulfilled": gP, "userManagement/removeChartAccess/fulfilled": (e, t) => { let { payload: n } = t;
                        null !== n && void 0 !== n && n.chartId && (e.charts = e.charts.filter((e => e.id !== n.chartId))) }, "roles/mergeChartLink/fulfilled": (e, t) => { let { meta: { arg: { embeddedChartId: n } } } = t;
                        n && (e.charts = e.charts.filter((e => e.id !== n))) } } }),
            bP = yP.reducer; var wP = n(97105),
            zP = n(98433),
            xP = n(69645),
            AP = n(71887),
            kP = n(79091),
            SP = n(70669);

        function MP(e) { return { ...(0, Cn.SB)(e), name: e.name || (0, Cn.Bx)(e), firstName: (0, Cn.II)(e), lastName: (0, Cn.US)(e) } } const EP = (0, Pr.Z0)({ name: "importPhotos", initialState: { people: [] }, extraReducers: { "people/getPeopleForPhotos/fulfilled": (e, t) => { e.people = t.payload.people.map(MP) } } }).reducer; var CP = n(73179),
            TP = n(40364),
            HP = n(23986),
            LP = n(39534),
            IP = n(12395),
            jP = n(31286),
            VP = n(43243),
            OP = n(83972),
            RP = n(31297),
            PP = n(53841); const DP = (0, vP.HY)({ search: YR.Ay, appInfo: NS.Ay, roles: jS.Ay, organization: nn.Ay, user: me.Ay, dashboard: bP, chart: VS.Ay, profileCard: $S.Ay, userManagement: wP.Ay, people: IS.Ay, themes: XI.Ay, fields: On.A, print: zP.Ay, license: qH.Ay, import: xS.Ay, integration: AP.Ay, legend: kP.Ay, talentPool: SP.Ay, importPhotos: EP, history: CP.Ay, communityBuild: $T.Ay, governanceChart: TP.Ay, shareChart: xI.Ay, client: HP.Ay, webhook: LP.Ay, lambda: Gn.Ay, report: pr.Ay, localStorageReducer: xP.A, spreadsheet: IP.Ay, notification: jP.Ay, cleanup: VP.Ay, snapshots: qn.Ay, audit: Xr, tour: OP.Ay, templates: RP.A, ownership: NT.Ay, userlifecycle: PP.Ay }); var FP = n(46987),
            NP = n.n(FP),
            _P = n(81780); const BP = { "roles/update/fulfilled": { eventType: "ROLE_UPDATE" }, "roles/create/fulfilled": { eventType: "ROLE_CREATE" }, "roles/removeRole/fulfilled": { eventType: "ROLE_REMOVE" }, "roles/dropPeople/fulfilled": { eventType: "DROP_PEOPLE" }, "roles/assignPeople/fulfilled": { eventType: "ASSIGN_PEOPLE" }, "roles/moveRoleInChart/fulfilled": { eventType: "MOVE_ROLE" } },
            WP = (0, Pr.U1)({ reducer: DP, middleware: [...(0, Pr.Ks)({ immutableCheck: !1, serializableCheck: !1 }), e => t => n => { const { type: r, meta: a, payload: o } = n, { getState: i, dispatch: l } = e, s = i(), { isTrackingEnabled: c } = s.history; if (Object.keys(BP).includes(r) && c) { const { eventType: e } = BP[r]; let t = JSON.parse(JSON.stringify(a.arg)); const n = s.roles,
                            i = s.people; let c = !0; switch (e) {
                            case "ROLE_UPDATE":
                                { var d, u, h; let e = null === (d = a.arg) || void 0 === d || null === (u = d.data) || void 0 === u ? void 0 : u.role;e = n.entities[e.id], t.data.role = e; const r = (null === (h = e) || void 0 === h ? void 0 : h.members) || [];t.data.members = r.map((e => i.entities[e])); break }
                            case "ROLE_CREATE":
                                var m, p; if (null !== o && void 0 !== o && o.duplicatesFound) return;
                                t.roleId = null === o || void 0 === o || null === (m = o.roles) || void 0 === m || null === (p = m[0]) || void 0 === p ? void 0 : p.id; break;
                            case "ROLE_REMOVE":
                                { let e = (0, _P.Sd)(n.entities || {}, t.roleId);t.data = {}; const r = n.entities[t.roleId],
                                        a = r.members.map((e => i.entities[e])),
                                        o = ["children", "parent", "members"],
                                        l = {};Object.entries(r).forEach((e => { let [t, n] = e;
                                        o.includes(t) || (l[t] = n) })), t.data.rel = e, t.data.members = a, t.data.role = l, delete t.roleId; break }
                            case "DROP_PEOPLE":
                                { var f;t.data = {}; let e = null === (f = a.arg) || void 0 === f ? void 0 : f.roleId,
                                        r = n.entities[e];t.data.members = r.members.map((e => ({ id: e }))); break }
                            case "ASSIGN_PEOPLE":
                                delete t.data; break;
                            case "MOVE_ROLE":
                                { let e = t.data.role.id,
                                        { moveHierarchy: r, moveOne: a } = t.data,
                                        o = (0, _P.Sd)(n.entities || {}, e, !0, a, r);t.data = { role: { ...t.data.role }, moveHierarchy: r, moveOne: a, rel: o }; break } }!c && t.isUndo && l((0, CP.qi)({ arg: t, eventType: e, pushRedo: !1 })), c && (t.isRedo || t.isUndo || (l((0, CP.UO)()), l((0, CP.CN)({ eventType: e, arg: t }))), t.isRedo ? l((0, CP.A2)({ arg: t, eventType: e })) : t.isUndo && l((0, CP.qi)({ arg: t, eventType: e, pushRedo: !0 }))) } return t(n) }, NP().reduxMiddleware()], devTools: !1 });
        Boolean("localhost" === window.location.hostname || "[::1]" === window.location.hostname || window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)); var UP = n(81130),
            qP = n(35966); const GP = a.createContext(null);

        function KP() { return a.useContext(GP) } const ZP = "function" === typeof Symbol && Symbol.for ? Symbol.for("mui.nested") : "__THEME_NESTED__"; const YP = function(e) { const { children: t, theme: n } = e, r = KP(), o = a.useMemo((() => { const e = null === r ? n : function(e, t) { if ("function" === typeof t) return t(e); return (0, l.default)({}, e, t) }(r, n); return null != e && (e[ZP] = null !== r), e }), [n, r]); return (0, we.jsx)(GP.Provider, { value: o, children: t }) }; var XP = n(55756),
            $P = n(67082); const QP = {};

        function JP(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; return a.useMemo((() => { const a = e && t[e] || t; if ("function" === typeof n) { const o = n(a),
                        i = e ? (0, l.default)({}, t, {
                            [e]: o }) : o; return r ? () => i : i } return e ? (0, l.default)({}, t, {
                    [e]: n }) : (0, l.default)({}, t, n) }), [e, t, n, r]) } const eD = function(e) { const { children: t, theme: n, themeId: r } = e, a = (0, $P.A)(QP), o = KP() || QP, i = JP(r, a, n), l = JP(r, o, n, !0), s = "rtl" === i.direction; return (0, we.jsx)(YP, { theme: l, children: (0, we.jsx)(XP.T.Provider, { value: i, children: (0, we.jsx)(OC.A, { value: s, children: t }) }) }) }; var tD = n(13375); const nD = ["theme"];

        function rD(e) { let { theme: t } = e, n = (0, ZM.default)(e, nD); const r = t[tD.A]; return (0, we.jsx)(eD, (0, l.default)({}, n, { themeId: r ? tD.A : void 0, theme: r || t })) } var aD = n(82284),
            oD = n(64467);

        function iD(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? Object(arguments[t]) : {},
                    r = Object.keys(n); "function" === typeof Object.getOwnPropertySymbols && r.push.apply(r, Object.getOwnPropertySymbols(n).filter((function(e) { return Object.getOwnPropertyDescriptor(n, e).enumerable }))), r.forEach((function(t) {
                    (0, oD.A)(e, t, n[t]) })) } return e } var lD = n(23029),
            sD = n(92901),
            cD = n(56822),
            dD = n(53954),
            uD = n(9417),
            hD = n(85501),
            mD = { type: "logger", log: function(e) { this.output("log", e) }, warn: function(e) { this.output("warn", e) }, error: function(e) { this.output("error", e) }, output: function(e, t) { console && console[e] && console[e].apply(console, t) } },
            pD = function() {
                function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    (0, lD.A)(this, e), this.init(t, n) } return (0, sD.A)(e, [{ key: "init", value: function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                        this.prefix = t.prefix || "i18next:", this.logger = e || mD, this.options = t, this.debug = t.debug } }, { key: "setDebug", value: function(e) { this.debug = e } }, { key: "log", value: function() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return this.forward(t, "log", "", !0) } }, { key: "warn", value: function() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return this.forward(t, "warn", "", !0) } }, { key: "error", value: function() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return this.forward(t, "error", "") } }, { key: "deprecate", value: function() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return this.forward(t, "warn", "WARNING DEPRECATED: ", !0) } }, { key: "forward", value: function(e, t, n, r) { return r && !this.debug ? null : ("string" === typeof e[0] && (e[0] = "".concat(n).concat(this.prefix, " ").concat(e[0])), this.logger[t](e)) } }, { key: "create", value: function(t) { return new e(this.logger, iD({}, { prefix: "".concat(this.prefix, ":").concat(t, ":") }, this.options)) } }]), e }(),
            fD = new pD,
            vD = function() {
                function e() {
                    (0, lD.A)(this, e), this.observers = {} } return (0, sD.A)(e, [{ key: "on", value: function(e, t) { var n = this; return e.split(" ").forEach((function(e) { n.observers[e] = n.observers[e] || [], n.observers[e].push(t) })), this } }, { key: "off", value: function(e, t) { this.observers[e] && (t ? this.observers[e] = this.observers[e].filter((function(e) { return e !== t })) : delete this.observers[e]) } }, { key: "emit", value: function(e) { for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
                        this.observers[e] && [].concat(this.observers[e]).forEach((function(e) { e.apply(void 0, n) }));
                        this.observers["*"] && [].concat(this.observers["*"]).forEach((function(t) { t.apply(t, [e].concat(n)) })) } }]), e }();

        function gD() { var e, t, n = new Promise((function(n, r) { e = n, t = r })); return n.resolve = e, n.reject = t, n }

        function yD(e) { return null == e ? "" : "" + e }

        function bD(e, t, n) {
            function r(e) { return e && e.indexOf("###") > -1 ? e.replace(/###/g, ".") : e }

            function a() { return !e || "string" === typeof e } for (var o = "string" !== typeof t ? [].concat(t) : t.split("."); o.length > 1;) { if (a()) return {}; var i = r(o.shift());!e[i] && n && (e[i] = new n), e = Object.prototype.hasOwnProperty.call(e, i) ? e[i] : {} } return a() ? {} : { obj: e, k: r(o.shift()) } }

        function wD(e, t, n) { var r = bD(e, t, Object);
            r.obj[r.k] = n }

        function zD(e, t) { var n = bD(e, t),
                r = n.obj,
                a = n.k; if (r) return r[a] }

        function xD(e, t, n) { var r = zD(e, n); return void 0 !== r ? r : zD(t, n) }

        function AD(e, t, n) { for (var r in t) "__proto__" !== r && "constructor" !== r && (r in e ? "string" === typeof e[r] || e[r] instanceof String || "string" === typeof t[r] || t[r] instanceof String ? n && (e[r] = t[r]) : AD(e[r], t[r], n) : e[r] = t[r]); return e }

        function kD(e) { return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&") } var SD = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#39;", "/": "&#x2F;" };

        function MD(e) { return "string" === typeof e ? e.replace(/[&<>"'\/]/g, (function(e) { return SD[e] })) : e } var ED = "undefined" !== typeof window && window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf("MSIE") > -1,
            CD = function(e) {
                function t(e) { var n, r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : { ns: ["translation"], defaultNS: "translation" }; return (0, lD.A)(this, t), n = (0, cD.A)(this, (0, dD.A)(t).call(this)), ED && vD.call((0, uD.A)(n)), n.data = e || {}, n.options = r, void 0 === n.options.keySeparator && (n.options.keySeparator = "."), n } return (0, hD.A)(t, e), (0, sD.A)(t, [{ key: "addNamespaces", value: function(e) { this.options.ns.indexOf(e) < 0 && this.options.ns.push(e) } }, { key: "removeNamespaces", value: function(e) { var t = this.options.ns.indexOf(e);
                        t > -1 && this.options.ns.splice(t, 1) } }, { key: "getResource", value: function(e, t, n) { var r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {},
                            a = void 0 !== r.keySeparator ? r.keySeparator : this.options.keySeparator,
                            o = [e, t]; return n && "string" !== typeof n && (o = o.concat(n)), n && "string" === typeof n && (o = o.concat(a ? n.split(a) : n)), e.indexOf(".") > -1 && (o = e.split(".")), zD(this.data, o) } }, { key: "addResource", value: function(e, t, n, r) { var a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : { silent: !1 },
                            o = this.options.keySeparator;
                        void 0 === o && (o = "."); var i = [e, t];
                        n && (i = i.concat(o ? n.split(o) : n)), e.indexOf(".") > -1 && (r = t, t = (i = e.split("."))[1]), this.addNamespaces(t), wD(this.data, i, r), a.silent || this.emit("added", e, t, n, r) } }, { key: "addResources", value: function(e, t, n) { var r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : { silent: !1 }; for (var a in n) "string" !== typeof n[a] && "[object Array]" !== Object.prototype.toString.apply(n[a]) || this.addResource(e, t, a, n[a], { silent: !0 });
                        r.silent || this.emit("added", e, t, n) } }, { key: "addResourceBundle", value: function(e, t, n, r, a) { var o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : { silent: !1 },
                            i = [e, t];
                        e.indexOf(".") > -1 && (r = n, n = t, t = (i = e.split("."))[1]), this.addNamespaces(t); var l = zD(this.data, i) || {};
                        r ? AD(l, n, a) : l = iD({}, l, n), wD(this.data, i, l), o.silent || this.emit("added", e, t, n) } }, { key: "removeResourceBundle", value: function(e, t) { this.hasResourceBundle(e, t) && delete this.data[e][t], this.removeNamespaces(t), this.emit("removed", e, t) } }, { key: "hasResourceBundle", value: function(e, t) { return void 0 !== this.getResource(e, t) } }, { key: "getResourceBundle", value: function(e, t) { return t || (t = this.options.defaultNS), "v1" === this.options.compatibilityAPI ? iD({}, {}, this.getResource(e, t)) : this.getResource(e, t) } }, { key: "getDataByLanguage", value: function(e) { return this.data[e] } }, { key: "toJSON", value: function() { return this.data } }]), t }(vD),
            TD = { processors: {}, addPostProcessor: function(e) { this.processors[e.name] = e }, handle: function(e, t, n, r, a) { var o = this; return e.forEach((function(e) { o.processors[e] && (t = o.processors[e].process(t, n, r, a)) })), t } },
            HD = {},
            LD = function(e) {
                function t(e) { var n, r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return (0, lD.A)(this, t), n = (0, cD.A)(this, (0, dD.A)(t).call(this)), ED && vD.call((0, uD.A)(n)),
                        function(e, t, n) { e.forEach((function(e) { t[e] && (n[e] = t[e]) })) }(["resourceStore", "languageUtils", "pluralResolver", "interpolator", "backendConnector", "i18nFormat", "utils"], e, (0, uD.A)(n)), n.options = r, void 0 === n.options.keySeparator && (n.options.keySeparator = "."), n.logger = fD.create("translator"), n } return (0, hD.A)(t, e), (0, sD.A)(t, [{ key: "changeLanguage", value: function(e) { e && (this.language = e) } }, { key: "exists", value: function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : { interpolation: {} },
                            n = this.resolve(e, t); return n && void 0 !== n.res } }, { key: "extractFromKey", value: function(e, t) { var n = void 0 !== t.nsSeparator ? t.nsSeparator : this.options.nsSeparator;
                        void 0 === n && (n = ":"); var r = void 0 !== t.keySeparator ? t.keySeparator : this.options.keySeparator,
                            a = t.ns || this.options.defaultNS; if (n && e.indexOf(n) > -1) { var o = e.match(this.interpolator.nestingRegexp); if (o && o.length > 0) return { key: e, namespaces: a }; var i = e.split(n);
                            (n !== r || n === r && this.options.ns.indexOf(i[0]) > -1) && (a = i.shift()), e = i.join(r) } return "string" === typeof a && (a = [a]), { key: e, namespaces: a } } }, { key: "translate", value: function(e, n, r) { var a = this; if ("object" !== (0, aD.A)(n) && this.options.overloadTranslationOptionHandler && (n = this.options.overloadTranslationOptionHandler(arguments)), n || (n = {}), void 0 === e || null === e) return "";
                        Array.isArray(e) || (e = [String(e)]); var o = void 0 !== n.keySeparator ? n.keySeparator : this.options.keySeparator,
                            i = this.extractFromKey(e[e.length - 1], n),
                            l = i.key,
                            s = i.namespaces,
                            c = s[s.length - 1],
                            d = n.lng || this.language,
                            u = n.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode; if (d && "cimode" === d.toLowerCase()) { if (u) { var h = n.nsSeparator || this.options.nsSeparator; return c + h + l } return l } var m = this.resolve(e, n),
                            p = m && m.res,
                            f = m && m.usedKey || l,
                            v = m && m.exactUsedKey || l,
                            g = Object.prototype.toString.apply(p),
                            y = void 0 !== n.joinArrays ? n.joinArrays : this.options.joinArrays,
                            b = !this.i18nFormat || this.i18nFormat.handleAsObject; if (b && p && ("string" !== typeof p && "boolean" !== typeof p && "number" !== typeof p) && ["[object Number]", "[object Function]", "[object RegExp]"].indexOf(g) < 0 && ("string" !== typeof y || "[object Array]" !== g)) { if (!n.returnObjects && !this.options.returnObjects) return this.logger.warn("accessing an object - but returnObjects options is not enabled!"), this.options.returnedObjectHandler ? this.options.returnedObjectHandler(f, p, n) : "key '".concat(l, " (").concat(this.language, ")' returned an object instead of string."); if (o) { var w = "[object Array]" === g,
                                    z = w ? [] : {},
                                    x = w ? v : f; for (var A in p)
                                    if (Object.prototype.hasOwnProperty.call(p, A)) { var k = "".concat(x).concat(o).concat(A);
                                        z[A] = this.translate(k, iD({}, n, { joinArrays: !1, ns: s })), z[A] === k && (z[A] = p[A]) } p = z } } else if (b && "string" === typeof y && "[object Array]" === g)(p = p.join(y)) && (p = this.extendTranslation(p, e, n, r));
                        else { var S = !1,
                                M = !1,
                                E = void 0 !== n.count && "string" !== typeof n.count,
                                C = t.hasDefaultValue(n),
                                T = E ? this.pluralResolver.getSuffix(d, n.count) : "",
                                H = n["defaultValue".concat(T)] || n.defaultValue;!this.isValidLookup(p) && C && (S = !0, p = H), this.isValidLookup(p) || (M = !0, p = l); var L = C && H !== p && this.options.updateMissing; if (M || S || L) { if (this.logger.log(L ? "updateKey" : "missingKey", d, c, l, L ? H : p), o) { var I = this.resolve(l, iD({}, n, { keySeparator: !1 }));
                                    I && I.res && this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.") } var j = [],
                                    V = this.languageUtils.getFallbackCodes(this.options.fallbackLng, n.lng || this.language); if ("fallback" === this.options.saveMissingTo && V && V[0])
                                    for (var O = 0; O < V.length; O++) j.push(V[O]);
                                else "all" === this.options.saveMissingTo ? j = this.languageUtils.toResolveHierarchy(n.lng || this.language) : j.push(n.lng || this.language); var R = function(e, t, r) { a.options.missingKeyHandler ? a.options.missingKeyHandler(e, c, t, L ? r : p, L, n) : a.backendConnector && a.backendConnector.saveMissing && a.backendConnector.saveMissing(e, c, t, L ? r : p, L, n), a.emit("missingKey", e, c, t, p) };
                                this.options.saveMissing && (this.options.saveMissingPlurals && E ? j.forEach((function(e) { a.pluralResolver.getSuffixes(e).forEach((function(t) { R([e], l + t, n["defaultValue".concat(t)] || H) })) })) : R(j, l, H)) } p = this.extendTranslation(p, e, n, m, r), M && p === l && this.options.appendNamespaceToMissingKey && (p = "".concat(c, ":").concat(l)), M && this.options.parseMissingKeyHandler && (p = this.options.parseMissingKeyHandler(p)) } return p } }, { key: "extendTranslation", value: function(e, t, n, r, a) { var o = this; if (this.i18nFormat && this.i18nFormat.parse) e = this.i18nFormat.parse(e, n, r.usedLng, r.usedNS, r.usedKey, { resolved: r });
                        else if (!n.skipInterpolation) { n.interpolation && this.interpolator.init(iD({}, n, { interpolation: iD({}, this.options.interpolation, n.interpolation) })); var i, l = n.interpolation && n.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables; if (l) { var s = e.match(this.interpolator.nestingRegexp);
                                i = s && s.length } var c = n.replace && "string" !== typeof n.replace ? n.replace : n; if (this.options.interpolation.defaultVariables && (c = iD({}, this.options.interpolation.defaultVariables, c)), e = this.interpolator.interpolate(e, c, n.lng || this.language, n), l) { var d = e.match(this.interpolator.nestingRegexp);
                                i < (d && d.length) && (n.nest = !1) }!1 !== n.nest && (e = this.interpolator.nest(e, (function() { for (var e = arguments.length, r = new Array(e), i = 0; i < e; i++) r[i] = arguments[i]; return a && a[0] === r[0] && !n.context ? (o.logger.warn("It seems you are nesting recursively key: ".concat(r[0], " in key: ").concat(t[0])), null) : o.translate.apply(o, r.concat([t])) }), n)), n.interpolation && this.interpolator.reset() } var u = n.postProcess || this.options.postProcess,
                            h = "string" === typeof u ? [u] : u; return void 0 !== e && null !== e && h && h.length && !1 !== n.applyPostProcessor && (e = TD.handle(h, e, t, this.options && this.options.postProcessPassResolved ? iD({ i18nResolved: r }, n) : n, this)), e } }, { key: "resolve", value: function(e) { var t, n, r, a, o, i = this,
                            l = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return "string" === typeof e && (e = [e]), e.forEach((function(e) { if (!i.isValidLookup(t)) { var s = i.extractFromKey(e, l),
                                    c = s.key;
                                n = c; var d = s.namespaces;
                                i.options.fallbackNS && (d = d.concat(i.options.fallbackNS)); var u = void 0 !== l.count && "string" !== typeof l.count,
                                    h = void 0 !== l.context && "string" === typeof l.context && "" !== l.context,
                                    m = l.lngs ? l.lngs : i.languageUtils.toResolveHierarchy(l.lng || i.language, l.fallbackLng);
                                d.forEach((function(e) { i.isValidLookup(t) || (o = e, !HD["".concat(m[0], "-").concat(e)] && i.utils && i.utils.hasLoadedNamespace && !i.utils.hasLoadedNamespace(o) && (HD["".concat(m[0], "-").concat(e)] = !0, i.logger.warn('key "'.concat(n, '" for languages "').concat(m.join(", "), '" won\'t get resolved as namespace "').concat(o, '" was not yet loaded'), "This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")), m.forEach((function(n) { if (!i.isValidLookup(t)) { a = n; var o, s, d = c,
                                                m = [d]; if (i.i18nFormat && i.i18nFormat.addLookupKeys) i.i18nFormat.addLookupKeys(m, c, n, e, l);
                                            else u && (o = i.pluralResolver.getSuffix(n, l.count)), u && h && m.push(d + o), h && m.push(d += "".concat(i.options.contextSeparator).concat(l.context)), u && m.push(d += o); for (; s = m.pop();) i.isValidLookup(t) || (r = s, t = i.getResource(n, e, s, l)) } }))) })) } })), { res: t, usedKey: n, exactUsedKey: r, usedLng: a, usedNS: o } } }, { key: "isValidLookup", value: function(e) { return void 0 !== e && !(!this.options.returnNull && null === e) && !(!this.options.returnEmptyString && "" === e) } }, { key: "getResource", value: function(e, t, n) { var r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {}; return this.i18nFormat && this.i18nFormat.getResource ? this.i18nFormat.getResource(e, t, n, r) : this.resourceStore.getResource(e, t, n, r) } }], [{ key: "hasDefaultValue", value: function(e) { var t = "defaultValue"; for (var n in e)
                            if (Object.prototype.hasOwnProperty.call(e, n) && t === n.substring(0, 12) && void 0 !== e[n]) return !0; return !1 } }]), t }(vD);

        function ID(e) { return e.charAt(0).toUpperCase() + e.slice(1) } var jD = function() {
                function e(t) {
                    (0, lD.A)(this, e), this.options = t, this.whitelist = this.options.supportedLngs || !1, this.supportedLngs = this.options.supportedLngs || !1, this.logger = fD.create("languageUtils") } return (0, sD.A)(e, [{ key: "getScriptPartFromCode", value: function(e) { if (!e || e.indexOf("-") < 0) return null; var t = e.split("-"); return 2 === t.length ? null : (t.pop(), "x" === t[t.length - 1].toLowerCase() ? null : this.formatLanguageCode(t.join("-"))) } }, { key: "getLanguagePartFromCode", value: function(e) { if (!e || e.indexOf("-") < 0) return e; var t = e.split("-"); return this.formatLanguageCode(t[0]) } }, { key: "formatLanguageCode", value: function(e) { if ("string" === typeof e && e.indexOf("-") > -1) { var t = ["hans", "hant", "latn", "cyrl", "cans", "mong", "arab"],
                                n = e.split("-"); return this.options.lowerCaseLng ? n = n.map((function(e) { return e.toLowerCase() })) : 2 === n.length ? (n[0] = n[0].toLowerCase(), n[1] = n[1].toUpperCase(), t.indexOf(n[1].toLowerCase()) > -1 && (n[1] = ID(n[1].toLowerCase()))) : 3 === n.length && (n[0] = n[0].toLowerCase(), 2 === n[1].length && (n[1] = n[1].toUpperCase()), "sgn" !== n[0] && 2 === n[2].length && (n[2] = n[2].toUpperCase()), t.indexOf(n[1].toLowerCase()) > -1 && (n[1] = ID(n[1].toLowerCase())), t.indexOf(n[2].toLowerCase()) > -1 && (n[2] = ID(n[2].toLowerCase()))), n.join("-") } return this.options.cleanCode || this.options.lowerCaseLng ? e.toLowerCase() : e } }, { key: "isWhitelisted", value: function(e) { return this.logger.deprecate("languageUtils.isWhitelisted", 'function "isWhitelisted" will be renamed to "isSupportedCode" in the next major - please make sure to rename it\'s usage asap.'), this.isSupportedCode(e) } }, { key: "isSupportedCode", value: function(e) { return ("languageOnly" === this.options.load || this.options.nonExplicitSupportedLngs) && (e = this.getLanguagePartFromCode(e)), !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(e) > -1 } }, { key: "getBestMatchFromCodes", value: function(e) { var t, n = this; return e ? (e.forEach((function(e) { if (!t) { var r = n.formatLanguageCode(e);
                                n.options.supportedLngs && !n.isSupportedCode(r) || (t = r) } })), !t && this.options.supportedLngs && e.forEach((function(e) { if (!t) { var r = n.getLanguagePartFromCode(e); if (n.isSupportedCode(r)) return t = r;
                                t = n.options.supportedLngs.find((function(e) { if (0 === e.indexOf(r)) return e })) } })), t || (t = this.getFallbackCodes(this.options.fallbackLng)[0]), t) : null } }, { key: "getFallbackCodes", value: function(e, t) { if (!e) return []; if ("function" === typeof e && (e = e(t)), "string" === typeof e && (e = [e]), "[object Array]" === Object.prototype.toString.apply(e)) return e; if (!t) return e.default || []; var n = e[t]; return n || (n = e[this.getScriptPartFromCode(t)]), n || (n = e[this.formatLanguageCode(t)]), n || (n = e[this.getLanguagePartFromCode(t)]), n || (n = e.default), n || [] } }, { key: "toResolveHierarchy", value: function(e, t) { var n = this,
                            r = this.getFallbackCodes(t || this.options.fallbackLng || [], e),
                            a = [],
                            o = function(e) { e && (n.isSupportedCode(e) ? a.push(e) : n.logger.warn("rejecting language code not found in supportedLngs: ".concat(e))) }; return "string" === typeof e && e.indexOf("-") > -1 ? ("languageOnly" !== this.options.load && o(this.formatLanguageCode(e)), "languageOnly" !== this.options.load && "currentOnly" !== this.options.load && o(this.getScriptPartFromCode(e)), "currentOnly" !== this.options.load && o(this.getLanguagePartFromCode(e))) : "string" === typeof e && o(this.formatLanguageCode(e)), r.forEach((function(e) { a.indexOf(e) < 0 && o(n.formatLanguageCode(e)) })), a } }]), e }(),
            VD = [{ lngs: ["ach", "ak", "am", "arn", "br", "fil", "gun", "ln", "mfe", "mg", "mi", "oc", "pt", "pt-BR", "tg", "tl", "ti", "tr", "uz", "wa"], nr: [1, 2], fc: 1 }, { lngs: ["af", "an", "ast", "az", "bg", "bn", "ca", "da", "de", "dev", "el", "en", "eo", "es", "et", "eu", "fi", "fo", "fur", "fy", "gl", "gu", "ha", "hi", "hu", "hy", "ia", "it", "kn", "ku", "lb", "mai", "ml", "mn", "mr", "nah", "nap", "nb", "ne", "nl", "nn", "no", "nso", "pa", "pap", "pms", "ps", "pt-PT", "rm", "sco", "se", "si", "so", "son", "sq", "sv", "sw", "ta", "te", "tk", "ur", "yo"], nr: [1, 2], fc: 2 }, { lngs: ["ay", "bo", "cgg", "fa", "ht", "id", "ja", "jbo", "ka", "kk", "km", "ko", "ky", "lo", "ms", "sah", "su", "th", "tt", "ug", "vi", "wo", "zh"], nr: [1], fc: 3 }, { lngs: ["be", "bs", "cnr", "dz", "hr", "ru", "sr", "uk"], nr: [1, 2, 5], fc: 4 }, { lngs: ["ar"], nr: [0, 1, 2, 3, 11, 100], fc: 5 }, { lngs: ["cs", "sk"], nr: [1, 2, 5], fc: 6 }, { lngs: ["csb", "pl"], nr: [1, 2, 5], fc: 7 }, { lngs: ["cy"], nr: [1, 2, 3, 8], fc: 8 }, { lngs: ["fr"], nr: [1, 2], fc: 9 }, { lngs: ["ga"], nr: [1, 2, 3, 7, 11], fc: 10 }, { lngs: ["gd"], nr: [1, 2, 3, 20], fc: 11 }, { lngs: ["is"], nr: [1, 2], fc: 12 }, { lngs: ["jv"], nr: [0, 1], fc: 13 }, { lngs: ["kw"], nr: [1, 2, 3, 4], fc: 14 }, { lngs: ["lt"], nr: [1, 2, 10], fc: 15 }, { lngs: ["lv"], nr: [1, 2, 0], fc: 16 }, { lngs: ["mk"], nr: [1, 2], fc: 17 }, { lngs: ["mnk"], nr: [0, 1, 2], fc: 18 }, { lngs: ["mt"], nr: [1, 2, 11, 20], fc: 19 }, { lngs: ["or"], nr: [2, 1], fc: 2 }, { lngs: ["ro"], nr: [1, 2, 20], fc: 20 }, { lngs: ["sl"], nr: [5, 1, 2, 3], fc: 21 }, { lngs: ["he", "iw"], nr: [1, 2, 20, 21], fc: 22 }],
            OD = { 1: function(e) { return Number(e > 1) }, 2: function(e) { return Number(1 != e) }, 3: function(e) { return 0 }, 4: function(e) { return Number(e % 10 == 1 && e % 100 != 11 ? 0 : e % 10 >= 2 && e % 10 <= 4 && (e % 100 < 10 || e % 100 >= 20) ? 1 : 2) }, 5: function(e) { return Number(0 == e ? 0 : 1 == e ? 1 : 2 == e ? 2 : e % 100 >= 3 && e % 100 <= 10 ? 3 : e % 100 >= 11 ? 4 : 5) }, 6: function(e) { return Number(1 == e ? 0 : e >= 2 && e <= 4 ? 1 : 2) }, 7: function(e) { return Number(1 == e ? 0 : e % 10 >= 2 && e % 10 <= 4 && (e % 100 < 10 || e % 100 >= 20) ? 1 : 2) }, 8: function(e) { return Number(1 == e ? 0 : 2 == e ? 1 : 8 != e && 11 != e ? 2 : 3) }, 9: function(e) { return Number(e >= 2) }, 10: function(e) { return Number(1 == e ? 0 : 2 == e ? 1 : e < 7 ? 2 : e < 11 ? 3 : 4) }, 11: function(e) { return Number(1 == e || 11 == e ? 0 : 2 == e || 12 == e ? 1 : e > 2 && e < 20 ? 2 : 3) }, 12: function(e) { return Number(e % 10 != 1 || e % 100 == 11) }, 13: function(e) { return Number(0 !== e) }, 14: function(e) { return Number(1 == e ? 0 : 2 == e ? 1 : 3 == e ? 2 : 3) }, 15: function(e) { return Number(e % 10 == 1 && e % 100 != 11 ? 0 : e % 10 >= 2 && (e % 100 < 10 || e % 100 >= 20) ? 1 : 2) }, 16: function(e) { return Number(e % 10 == 1 && e % 100 != 11 ? 0 : 0 !== e ? 1 : 2) }, 17: function(e) { return Number(1 == e || e % 10 == 1 && e % 100 != 11 ? 0 : 1) }, 18: function(e) { return Number(0 == e ? 0 : 1 == e ? 1 : 2) }, 19: function(e) { return Number(1 == e ? 0 : 0 == e || e % 100 > 1 && e % 100 < 11 ? 1 : e % 100 > 10 && e % 100 < 20 ? 2 : 3) }, 20: function(e) { return Number(1 == e ? 0 : 0 == e || e % 100 > 0 && e % 100 < 20 ? 1 : 2) }, 21: function(e) { return Number(e % 100 == 1 ? 1 : e % 100 == 2 ? 2 : e % 100 == 3 || e % 100 == 4 ? 3 : 0) }, 22: function(e) { return Number(1 == e ? 0 : 2 == e ? 1 : (e < 0 || e > 10) && e % 10 == 0 ? 2 : 3) } }; var RD = function() {
                function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                    (0, lD.A)(this, e), this.languageUtils = t, this.options = n, this.logger = fD.create("pluralResolver"), this.rules = function() { var e = {}; return VD.forEach((function(t) { t.lngs.forEach((function(n) { e[n] = { numbers: t.nr, plurals: OD[t.fc] } })) })), e }() } return (0, sD.A)(e, [{ key: "addRule", value: function(e, t) { this.rules[e] = t } }, { key: "getRule", value: function(e) { return this.rules[e] || this.rules[this.languageUtils.getLanguagePartFromCode(e)] } }, { key: "needsPlural", value: function(e) { var t = this.getRule(e); return t && t.numbers.length > 1 } }, { key: "getPluralFormsOfKey", value: function(e, t) { return this.getSuffixes(e).map((function(e) { return t + e })) } }, { key: "getSuffixes", value: function(e) { var t = this,
                            n = this.getRule(e); return n ? n.numbers.map((function(n) { return t.getSuffix(e, n) })) : [] } }, { key: "getSuffix", value: function(e, t) { var n = this,
                            r = this.getRule(e); if (r) { var a = r.noAbs ? r.plurals(t) : r.plurals(Math.abs(t)),
                                o = r.numbers[a];
                            this.options.simplifyPluralSuffix && 2 === r.numbers.length && 1 === r.numbers[0] && (2 === o ? o = "plural" : 1 === o && (o = "")); var i = function() { return n.options.prepend && o.toString() ? n.options.prepend + o.toString() : o.toString() }; return "v1" === this.options.compatibilityJSON ? 1 === o ? "" : "number" === typeof o ? "_plural_".concat(o.toString()) : i() : "v2" === this.options.compatibilityJSON || this.options.simplifyPluralSuffix && 2 === r.numbers.length && 1 === r.numbers[0] ? i() : this.options.prepend && a.toString() ? this.options.prepend + a.toString() : a.toString() } return this.logger.warn("no plural rule found for: ".concat(e)), "" } }]), e }(),
            PD = function() {
                function e() { var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                    (0, lD.A)(this, e), this.logger = fD.create("interpolator"), this.options = t, this.format = t.interpolation && t.interpolation.format || function(e) { return e }, this.init(t) } return (0, sD.A)(e, [{ key: "init", value: function() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                        e.interpolation || (e.interpolation = { escapeValue: !0 }); var t = e.interpolation;
                        this.escape = void 0 !== t.escape ? t.escape : MD, this.escapeValue = void 0 === t.escapeValue || t.escapeValue, this.useRawValueToEscape = void 0 !== t.useRawValueToEscape && t.useRawValueToEscape, this.prefix = t.prefix ? kD(t.prefix) : t.prefixEscaped || "{{", this.suffix = t.suffix ? kD(t.suffix) : t.suffixEscaped || "}}", this.formatSeparator = t.formatSeparator ? t.formatSeparator : t.formatSeparator || ",", this.unescapePrefix = t.unescapeSuffix ? "" : t.unescapePrefix || "-", this.unescapeSuffix = this.unescapePrefix ? "" : t.unescapeSuffix || "", this.nestingPrefix = t.nestingPrefix ? kD(t.nestingPrefix) : t.nestingPrefixEscaped || kD("$t("), this.nestingSuffix = t.nestingSuffix ? kD(t.nestingSuffix) : t.nestingSuffixEscaped || kD(")"), this.nestingOptionsSeparator = t.nestingOptionsSeparator ? t.nestingOptionsSeparator : t.nestingOptionsSeparator || ",", this.maxReplaces = t.maxReplaces ? t.maxReplaces : 1e3, this.alwaysFormat = void 0 !== t.alwaysFormat && t.alwaysFormat, this.resetRegExp() } }, { key: "reset", value: function() { this.options && this.init(this.options) } }, { key: "resetRegExp", value: function() { var e = "".concat(this.prefix, "(.+?)").concat(this.suffix);
                        this.regexp = new RegExp(e, "g"); var t = "".concat(this.prefix).concat(this.unescapePrefix, "(.+?)").concat(this.unescapeSuffix).concat(this.suffix);
                        this.regexpUnescape = new RegExp(t, "g"); var n = "".concat(this.nestingPrefix, "(.+?)").concat(this.nestingSuffix);
                        this.nestingRegexp = new RegExp(n, "g") } }, { key: "interpolate", value: function(e, t, n, r) { var a, o, i, l = this,
                            s = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};

                        function c(e) { return e.replace(/\$/g, "$$$$") } var d = function(e) { if (e.indexOf(l.formatSeparator) < 0) { var a = xD(t, s, e); return l.alwaysFormat ? l.format(a, void 0, n) : a } var o = e.split(l.formatSeparator),
                                i = o.shift().trim(),
                                c = o.join(l.formatSeparator).trim(); return l.format(xD(t, s, i), c, n, r) };
                        this.resetRegExp(); var u = r && r.missingInterpolationHandler || this.options.missingInterpolationHandler,
                            h = r && r.interpolation && r.interpolation.skipOnVariables || this.options.interpolation.skipOnVariables; return [{ regex: this.regexpUnescape, safeValue: function(e) { return c(e) } }, { regex: this.regexp, safeValue: function(e) { return l.escapeValue ? c(l.escape(e)) : c(e) } }].forEach((function(t) { for (i = 0; a = t.regex.exec(e);) { if (void 0 === (o = d(a[1].trim())))
                                    if ("function" === typeof u) { var n = u(e, a, r);
                                        o = "string" === typeof n ? n : "" } else { if (h) { o = a[0]; continue } l.logger.warn("missed to pass in variable ".concat(a[1], " for interpolating ").concat(e)), o = "" } else "string" === typeof o || l.useRawValueToEscape || (o = yD(o)); if (e = e.replace(a[0], t.safeValue(o)), t.regex.lastIndex = 0, ++i >= l.maxReplaces) break } })), e } }, { key: "nest", value: function(e, t) { var n, r, a = this,
                            o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
                            i = iD({}, o);

                        function l(e, t) { var n = this.nestingOptionsSeparator; if (e.indexOf(n) < 0) return e; var r = e.split(new RegExp("".concat(n, "[ ]*{"))),
                                a = "{".concat(r[1]);
                            e = r[0], a = (a = this.interpolate(a, i)).replace(/'/g, '"'); try { i = JSON.parse(a), t && (i = iD({}, t, i)) } catch (o) { return this.logger.warn("failed parsing options string in nesting for key ".concat(e), o), "".concat(e).concat(n).concat(a) } return delete i.defaultValue, e } for (i.applyPostProcessor = !1, delete i.defaultValue; n = this.nestingRegexp.exec(e);) { var s = [],
                                c = !1; if (n[0].includes(this.formatSeparator) && !/{.*}/.test(n[1])) { var d = n[1].split(this.formatSeparator).map((function(e) { return e.trim() }));
                                n[1] = d.shift(), s = d, c = !0 } if ((r = t(l.call(this, n[1].trim(), i), i)) && n[0] === e && "string" !== typeof r) return r; "string" !== typeof r && (r = yD(r)), r || (this.logger.warn("missed to resolve ".concat(n[1], " for nesting ").concat(e)), r = ""), c && (r = s.reduce((function(e, t) { return a.format(e, t, o.lng, o) }), r.trim())), e = e.replace(n[0], r), this.regexp.lastIndex = 0 } return e } }]), e }(); var DD = function(e) {
            function t(e, n, r) { var a, o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {}; return (0, lD.A)(this, t), a = (0, cD.A)(this, (0, dD.A)(t).call(this)), ED && vD.call((0, uD.A)(a)), a.backend = e, a.store = n, a.services = r, a.languageUtils = r.languageUtils, a.options = o, a.logger = fD.create("backendConnector"), a.state = {}, a.queue = [], a.backend && a.backend.init && a.backend.init(r, o.backend, o), a } return (0, hD.A)(t, e), (0, sD.A)(t, [{ key: "queueLoad", value: function(e, t, n, r) { var a = this,
                        o = [],
                        i = [],
                        l = [],
                        s = []; return e.forEach((function(e) { var r = !0;
                        t.forEach((function(t) { var l = "".concat(e, "|").concat(t);!n.reload && a.store.hasResourceBundle(e, t) ? a.state[l] = 2 : a.state[l] < 0 || (1 === a.state[l] ? i.indexOf(l) < 0 && i.push(l) : (a.state[l] = 1, r = !1, i.indexOf(l) < 0 && i.push(l), o.indexOf(l) < 0 && o.push(l), s.indexOf(t) < 0 && s.push(t))) })), r || l.push(e) })), (o.length || i.length) && this.queue.push({ pending: i, loaded: {}, errors: [], callback: r }), { toLoad: o, pending: i, toLoadLanguages: l, toLoadNamespaces: s } } }, { key: "loaded", value: function(e, t, n) { var r = e.split("|"),
                        a = r[0],
                        o = r[1];
                    t && this.emit("failedLoading", a, o, t), n && this.store.addResourceBundle(a, o, n), this.state[e] = t ? -1 : 2; var i = {};
                    this.queue.forEach((function(n) {! function(e, t, n, r) { var a = bD(e, t, Object),
                                o = a.obj,
                                i = a.k;
                            o[i] = o[i] || [], r && (o[i] = o[i].concat(n)), r || o[i].push(n) }(n.loaded, [a], o),
                        function(e, t) { for (var n = e.indexOf(t); - 1 !== n;) e.splice(n, 1), n = e.indexOf(t) }(n.pending, e), t && n.errors.push(t), 0 !== n.pending.length || n.done || (Object.keys(n.loaded).forEach((function(e) { i[e] || (i[e] = []), n.loaded[e].length && n.loaded[e].forEach((function(t) { i[e].indexOf(t) < 0 && i[e].push(t) })) })), n.done = !0, n.errors.length ? n.callback(n.errors) : n.callback()) })), this.emit("loaded", i), this.queue = this.queue.filter((function(e) { return !e.done })) } }, { key: "read", value: function(e, t, n) { var r = this,
                        a = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0,
                        o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 350,
                        i = arguments.length > 5 ? arguments[5] : void 0; return e.length ? this.backend[n](e, t, (function(l, s) { l && s && a < 5 ? setTimeout((function() { r.read.call(r, e, t, n, a + 1, 2 * o, i) }), o) : i(l, s) })) : i(null, {}) } }, { key: "prepareLoading", value: function(e, t) { var n = this,
                        r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
                        a = arguments.length > 3 ? arguments[3] : void 0; if (!this.backend) return this.logger.warn("No backend was added via i18next.use. Will not load resources."), a && a(); "string" === typeof e && (e = this.languageUtils.toResolveHierarchy(e)), "string" === typeof t && (t = [t]); var o = this.queueLoad(e, t, r, a); if (!o.toLoad.length) return o.pending.length || a(), null;
                    o.toLoad.forEach((function(e) { n.loadOne(e) })) } }, { key: "load", value: function(e, t, n) { this.prepareLoading(e, t, {}, n) } }, { key: "reload", value: function(e, t, n) { this.prepareLoading(e, t, { reload: !0 }, n) } }, { key: "loadOne", value: function(e) { var t = this,
                        n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
                        r = e.split("|"),
                        a = r[0],
                        o = r[1];
                    this.read(a, o, "read", void 0, void 0, (function(r, i) { r && t.logger.warn("".concat(n, "loading namespace ").concat(o, " for language ").concat(a, " failed"), r), !r && i && t.logger.log("".concat(n, "loaded namespace ").concat(o, " for language ").concat(a), i), t.loaded(e, r, i) })) } }, { key: "saveMissing", value: function(e, t, n, r, a) { var o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : {};
                    this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(t) ? this.logger.warn('did not save key "'.concat(n, '" as the namespace "').concat(t, '" was not yet loaded'), "This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!") : void 0 !== n && null !== n && "" !== n && (this.backend && this.backend.create && this.backend.create(e, t, n, r, null, iD({}, o, { isUpdate: a })), e && e[0] && this.store.addResource(e[0], t, n, r)) } }]), t }(vD);

        function FD(e) { return "string" === typeof e.ns && (e.ns = [e.ns]), "string" === typeof e.fallbackLng && (e.fallbackLng = [e.fallbackLng]), "string" === typeof e.fallbackNS && (e.fallbackNS = [e.fallbackNS]), e.whitelist && (e.whitelist && e.whitelist.indexOf("cimode") < 0 && (e.whitelist = e.whitelist.concat(["cimode"])), e.supportedLngs = e.whitelist), e.nonExplicitWhitelist && (e.nonExplicitSupportedLngs = e.nonExplicitWhitelist), e.supportedLngs && e.supportedLngs.indexOf("cimode") < 0 && (e.supportedLngs = e.supportedLngs.concat(["cimode"])), e }

        function ND() {} var _D = function(e) {
            function t() { var e, n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    r = arguments.length > 1 ? arguments[1] : void 0; if ((0, lD.A)(this, t), e = (0, cD.A)(this, (0, dD.A)(t).call(this)), ED && vD.call((0, uD.A)(e)), e.options = FD(n), e.services = {}, e.logger = fD, e.modules = { external: [] }, r && !e.isInitialized && !n.isClone) { if (!e.options.initImmediate) return e.init(n, r), (0, cD.A)(e, (0, uD.A)(e));
                    setTimeout((function() { e.init(n, r) }), 0) } return e } return (0, hD.A)(t, e), (0, sD.A)(t, [{ key: "init", value: function() { var e = this,
                        t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                        n = arguments.length > 1 ? arguments[1] : void 0;

                    function r(e) { return e ? "function" === typeof e ? new e : e : null } if ("function" === typeof t && (n = t, t = {}), t.whitelist && !t.supportedLngs && this.logger.deprecate("whitelist", 'option "whitelist" will be renamed to "supportedLngs" in the next major - please make sure to rename this option asap.'), t.nonExplicitWhitelist && !t.nonExplicitSupportedLngs && this.logger.deprecate("whitelist", 'options "nonExplicitWhitelist" will be renamed to "nonExplicitSupportedLngs" in the next major - please make sure to rename this option asap.'), this.options = iD({}, { debug: !1, initImmediate: !0, ns: ["translation"], defaultNS: ["translation"], fallbackLng: ["dev"], fallbackNS: !1, whitelist: !1, nonExplicitWhitelist: !1, supportedLngs: !1, nonExplicitSupportedLngs: !1, load: "all", preload: !1, simplifyPluralSuffix: !0, keySeparator: ".", nsSeparator: ":", pluralSeparator: "_", contextSeparator: "_", partialBundledLanguages: !1, saveMissing: !1, updateMissing: !1, saveMissingTo: "fallback", saveMissingPlurals: !0, missingKeyHandler: !1, missingInterpolationHandler: !1, postProcess: !1, postProcessPassResolved: !1, returnNull: !0, returnEmptyString: !0, returnObjects: !1, joinArrays: !1, returnedObjectHandler: !1, parseMissingKeyHandler: !1, appendNamespaceToMissingKey: !1, appendNamespaceToCIMode: !1, overloadTranslationOptionHandler: function(e) { var t = {}; if ("object" === (0, aD.A)(e[1]) && (t = e[1]), "string" === typeof e[1] && (t.defaultValue = e[1]), "string" === typeof e[2] && (t.tDescription = e[2]), "object" === (0, aD.A)(e[2]) || "object" === (0, aD.A)(e[3])) { var n = e[3] || e[2];
                                    Object.keys(n).forEach((function(e) { t[e] = n[e] })) } return t }, interpolation: { escapeValue: !0, format: function(e, t, n, r) { return e }, prefix: "{{", suffix: "}}", formatSeparator: ",", unescapePrefix: "-", nestingPrefix: "$t(", nestingSuffix: ")", nestingOptionsSeparator: ",", maxReplaces: 1e3, skipOnVariables: !1 } }, this.options, FD(t)), this.format = this.options.interpolation.format, n || (n = ND), !this.options.isClone) { this.modules.logger ? fD.init(r(this.modules.logger), this.options) : fD.init(null, this.options); var a = new jD(this.options);
                        this.store = new CD(this.options.resources, this.options); var o = this.services;
                        o.logger = fD, o.resourceStore = this.store, o.languageUtils = a, o.pluralResolver = new RD(a, { prepend: this.options.pluralSeparator, compatibilityJSON: this.options.compatibilityJSON, simplifyPluralSuffix: this.options.simplifyPluralSuffix }), o.interpolator = new PD(this.options), o.utils = { hasLoadedNamespace: this.hasLoadedNamespace.bind(this) }, o.backendConnector = new DD(r(this.modules.backend), o.resourceStore, o, this.options), o.backendConnector.on("*", (function(t) { for (var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++) r[a - 1] = arguments[a];
                            e.emit.apply(e, [t].concat(r)) })), this.modules.languageDetector && (o.languageDetector = r(this.modules.languageDetector), o.languageDetector.init(o, this.options.detection, this.options)), this.modules.i18nFormat && (o.i18nFormat = r(this.modules.i18nFormat), o.i18nFormat.init && o.i18nFormat.init(this)), this.translator = new LD(this.services, this.options), this.translator.on("*", (function(t) { for (var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++) r[a - 1] = arguments[a];
                            e.emit.apply(e, [t].concat(r)) })), this.modules.external.forEach((function(t) { t.init && t.init(e) })) } if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) { var i = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);
                        i.length > 0 && "dev" !== i[0] && (this.options.lng = i[0]) } this.services.languageDetector || this.options.lng || this.logger.warn("init: no languageDetector is used and no lng is defined");
                    ["getResource", "hasResourceBundle", "getResourceBundle", "getDataByLanguage"].forEach((function(t) { e[t] = function() { var n; return (n = e.store)[t].apply(n, arguments) } }));
                    ["addResource", "addResources", "addResourceBundle", "removeResourceBundle"].forEach((function(t) { e[t] = function() { var n; return (n = e.store)[t].apply(n, arguments), e } })); var l = gD(),
                        s = function() { var t = function(t, r) { e.isInitialized && e.logger.warn("init: i18next is already initialized. You should call init just once!"), e.isInitialized = !0, e.options.isClone || e.logger.log("initialized", e.options), e.emit("initialized", e.options), l.resolve(r), n(t, r) }; if (e.languages && "v1" !== e.options.compatibilityAPI && !e.isInitialized) return t(null, e.t.bind(e));
                            e.changeLanguage(e.options.lng, t) }; return this.options.resources || !this.options.initImmediate ? s() : setTimeout(s, 0), l } }, { key: "loadResources", value: function(e) { var t = this,
                        n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ND,
                        r = "string" === typeof e ? e : this.language; if ("function" === typeof e && (n = e), !this.options.resources || this.options.partialBundledLanguages) { if (r && "cimode" === r.toLowerCase()) return n(); var a = [],
                            o = function(e) { e && t.services.languageUtils.toResolveHierarchy(e).forEach((function(e) { a.indexOf(e) < 0 && a.push(e) })) }; if (r) o(r);
                        else this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((function(e) { return o(e) }));
                        this.options.preload && this.options.preload.forEach((function(e) { return o(e) })), this.services.backendConnector.load(a, this.options.ns, n) } else n(null) } }, { key: "reloadResources", value: function(e, t, n) { var r = gD(); return e || (e = this.languages), t || (t = this.options.ns), n || (n = ND), this.services.backendConnector.reload(e, t, (function(e) { r.resolve(), n(e) })), r } }, { key: "use", value: function(e) { if (!e) throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()"); if (!e.type) throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()"); return "backend" === e.type && (this.modules.backend = e), ("logger" === e.type || e.log && e.warn && e.error) && (this.modules.logger = e), "languageDetector" === e.type && (this.modules.languageDetector = e), "i18nFormat" === e.type && (this.modules.i18nFormat = e), "postProcessor" === e.type && TD.addPostProcessor(e), "3rdParty" === e.type && this.modules.external.push(e), this } }, { key: "changeLanguage", value: function(e, t) { var n = this;
                    this.isLanguageChangingTo = e; var r = gD();
                    this.emit("languageChanging", e); var a = function(e) { var a = "string" === typeof e ? e : n.services.languageUtils.getBestMatchFromCodes(e);
                        a && (n.language || (n.language = a, n.languages = n.services.languageUtils.toResolveHierarchy(a)), n.translator.language || n.translator.changeLanguage(a), n.services.languageDetector && n.services.languageDetector.cacheUserLanguage(a)), n.loadResources(a, (function(e) {! function(e, a) { a ? (n.language = a, n.languages = n.services.languageUtils.toResolveHierarchy(a), n.translator.changeLanguage(a), n.isLanguageChangingTo = void 0, n.emit("languageChanged", a), n.logger.log("languageChanged", a)) : n.isLanguageChangingTo = void 0, r.resolve((function() { return n.t.apply(n, arguments) })), t && t(e, (function() { return n.t.apply(n, arguments) })) }(e, a) })) }; return e || !this.services.languageDetector || this.services.languageDetector.async ? !e && this.services.languageDetector && this.services.languageDetector.async ? this.services.languageDetector.detect(a) : a(e) : a(this.services.languageDetector.detect()), r } }, { key: "getFixedT", value: function(e, t) { var n = this,
                        r = function e(t, r) { var a; if ("object" !== (0, aD.A)(r)) { for (var o = arguments.length, i = new Array(o > 2 ? o - 2 : 0), l = 2; l < o; l++) i[l - 2] = arguments[l];
                                a = n.options.overloadTranslationOptionHandler([t, r].concat(i)) } else a = iD({}, r); return a.lng = a.lng || e.lng, a.lngs = a.lngs || e.lngs, a.ns = a.ns || e.ns, n.t(t, a) }; return "string" === typeof e ? r.lng = e : r.lngs = e, r.ns = t, r } }, { key: "t", value: function() { var e; return this.translator && (e = this.translator).translate.apply(e, arguments) } }, { key: "exists", value: function() { var e; return this.translator && (e = this.translator).exists.apply(e, arguments) } }, { key: "setDefaultNamespace", value: function(e) { this.options.defaultNS = e } }, { key: "hasLoadedNamespace", value: function(e) { var t = this,
                        n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; if (!this.isInitialized) return this.logger.warn("hasLoadedNamespace: i18next was not initialized", this.languages), !1; if (!this.languages || !this.languages.length) return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty", this.languages), !1; var r = this.languages[0],
                        a = !!this.options && this.options.fallbackLng,
                        o = this.languages[this.languages.length - 1]; if ("cimode" === r.toLowerCase()) return !0; var i = function(e, n) { var r = t.services.backendConnector.state["".concat(e, "|").concat(n)]; return -1 === r || 2 === r }; if (n.precheck) { var l = n.precheck(this, i); if (void 0 !== l) return l } return !!this.hasResourceBundle(r, e) || (!this.services.backendConnector.backend || !(!i(r, e) || a && !i(o, e))) } }, { key: "loadNamespaces", value: function(e, t) { var n = this,
                        r = gD(); return this.options.ns ? ("string" === typeof e && (e = [e]), e.forEach((function(e) { n.options.ns.indexOf(e) < 0 && n.options.ns.push(e) })), this.loadResources((function(e) { r.resolve(), t && t(e) })), r) : (t && t(), Promise.resolve()) } }, { key: "loadLanguages", value: function(e, t) { var n = gD(); "string" === typeof e && (e = [e]); var r = this.options.preload || [],
                        a = e.filter((function(e) { return r.indexOf(e) < 0 })); return a.length ? (this.options.preload = r.concat(a), this.loadResources((function(e) { n.resolve(), t && t(e) })), n) : (t && t(), Promise.resolve()) } }, { key: "dir", value: function(e) { if (e || (e = this.languages && this.languages.length > 0 ? this.languages[0] : this.language), !e) return "rtl"; return ["ar", "shu", "sqr", "ssh", "xaa", "yhd", "yud", "aao", "abh", "abv", "acm", "acq", "acw", "acx", "acy", "adf", "ads", "aeb", "aec", "afb", "ajp", "apc", "apd", "arb", "arq", "ars", "ary", "arz", "auz", "avl", "ayh", "ayl", "ayn", "ayp", "bbz", "pga", "he", "iw", "ps", "pbt", "pbu", "pst", "prp", "prd", "ug", "ur", "ydd", "yds", "yih", "ji", "yi", "hbo", "men", "xmn", "fa", "jpr", "peo", "pes", "prs", "dv", "sam"].indexOf(this.services.languageUtils.getLanguagePartFromCode(e)) >= 0 ? "rtl" : "ltr" } }, { key: "createInstance", value: function() { return new t(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, arguments.length > 1 ? arguments[1] : void 0) } }, { key: "cloneInstance", value: function() { var e = this,
                        n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                        r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ND,
                        a = iD({}, this.options, n, { isClone: !0 }),
                        o = new t(a); return ["store", "services", "language"].forEach((function(t) { o[t] = e[t] })), o.services = iD({}, this.services), o.services.utils = { hasLoadedNamespace: o.hasLoadedNamespace.bind(o) }, o.translator = new LD(o.services, o.options), o.translator.on("*", (function(e) { for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
                        o.emit.apply(o, [e].concat(n)) })), o.init(a, r), o.translator.options = o.options, o.translator.backendConnector.services.utils = { hasLoadedNamespace: o.hasLoadedNamespace.bind(o) }, o } }]), t }(vD); const BD = new _D; var WD = n(75);

        function UD(e) { return UD = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, UD(e) } var qD = [],
            GD = qD.forEach,
            KD = qD.slice;

        function ZD(e) { return GD.call(KD.call(arguments, 1), (function(t) { if (t)
                    for (var n in t) void 0 === e[n] && (e[n] = t[n]) })), e }

        function YD() { return "function" === typeof XMLHttpRequest || "object" === ("undefined" === typeof XMLHttpRequest ? "undefined" : UD(XMLHttpRequest)) } const XD = n.p + "static/media/getFetch.40f37ddea2378391108f.cjs"; var $D, QD, JD, eF = n.t(XD);

        function tF(e) { return tF = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, tF(e) } "function" === typeof fetch && ($D = "undefined" !== typeof global && global.fetch ? global.fetch : "undefined" !== typeof window && window.fetch ? window.fetch : fetch), YD() && ("undefined" !== typeof global && global.XMLHttpRequest ? QD = global.XMLHttpRequest : "undefined" !== typeof window && window.XMLHttpRequest && (QD = window.XMLHttpRequest)), "function" === typeof ActiveXObject && ("undefined" !== typeof global && global.ActiveXObject ? JD = global.ActiveXObject : "undefined" !== typeof window && window.ActiveXObject && (JD = window.ActiveXObject)), $D || !eF || QD || JD || ($D = XD || eF), "function" !== typeof $D && ($D = void 0); var nF = function(e, t) { if (t && "object" === tF(t)) { var n = ""; for (var r in t) n += "&" + encodeURIComponent(r) + "=" + encodeURIComponent(t[r]); if (!n) return e;
                    e = e + (-1 !== e.indexOf("?") ? "&" : "?") + n.slice(1) } return e },
            rF = function(e, t, n) { $D(e, t).then((function(e) { if (!e.ok) return n(e.statusText || "Error", { status: e.status });
                    e.text().then((function(t) { n(null, { status: e.status, data: t }) })).catch(n) })).catch(n) },
            aF = !1; const oF = function(e, t, n, r) { return "function" === typeof n && (r = n, n = void 0), r = r || function() {}, $D ? function(e, t, n, r) { e.queryStringParams && (t = nF(t, e.queryStringParams)); var a = ZD({}, "function" === typeof e.customHeaders ? e.customHeaders() : e.customHeaders);
                n && (a["Content-Type"] = "application/json"); var o = "function" === typeof e.requestOptions ? e.requestOptions(n) : e.requestOptions,
                    i = ZD({ method: n ? "POST" : "GET", body: n ? e.stringify(n) : void 0, headers: a }, aF ? {} : o); try { rF(t, i, r) } catch (l) { if (!o || 0 === Object.keys(o).length || !l.message || l.message.indexOf("not implemented") < 0) return r(l); try { Object.keys(o).forEach((function(e) { delete i[e] })), rF(t, i, r), aF = !0 } catch (s) { r(s) } } }(e, t, n, r) : YD() || "function" === typeof ActiveXObject ? function(e, t, n, r) { n && "object" === tF(n) && (n = nF("", n).slice(1)), e.queryStringParams && (t = nF(t, e.queryStringParams)); try { var a;
                    (a = QD ? new QD : new JD("MSXML2.XMLHTTP.3.0")).open(n ? "POST" : "GET", t, 1), e.crossDomain || a.setRequestHeader("X-Requested-With", "XMLHttpRequest"), a.withCredentials = !!e.withCredentials, n && a.setRequestHeader("Content-Type", "application/x-www-form-urlencoded"), a.overrideMimeType && a.overrideMimeType("application/json"); var o = e.customHeaders; if (o = "function" === typeof o ? o() : o)
                        for (var i in o) a.setRequestHeader(i, o[i]);
                    a.onreadystatechange = function() { a.readyState > 3 && r(a.status >= 400 ? a.statusText : null, { status: a.status, data: a.responseText }) }, a.send(n) } catch (l) { console && console.log(l) } }(e, t, n, r) : void r(new Error("No fetch and no xhr implementation found!")) };

        function iF(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var lF = function() { return { loadPath: "/locales/{{lng}}/{{ns}}.json", addPath: "/locales/add/{{lng}}/{{ns}}", allowMultiLoading: !1, parse: function(e) { return JSON.parse(e) }, stringify: JSON.stringify, parsePayload: function(e, t, n) { return function(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }({}, t, n || "") }, request: oF, reloadInterval: "undefined" === typeof window && 36e5, customHeaders: {}, queryStringParams: {}, crossDomain: !1, withCredentials: !1, overrideMimeType: !1, requestOptions: { mode: "cors", credentials: "same-origin", cache: "default" } } },
            sF = function() {
                function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.services = t, this.options = n, this.allOptions = r, this.type = "backend", this.init(t, n, r) } var t, n, r; return t = e, n = [{ key: "init", value: function(e) { var t = this,
                            n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                            r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                        this.services = e, this.options = ZD(n, this.options || {}, lF()), this.allOptions = r, this.services && this.options.reloadInterval && setInterval((function() { return t.reload() }), this.options.reloadInterval) } }, { key: "readMulti", value: function(e, t, n) { this._readAny(e, e, t, t, n) } }, { key: "read", value: function(e, t, n) { this._readAny([e], e, [t], t, n) } }, { key: "_readAny", value: function(e, t, n, r, a) { var o, i = this,
                            l = this.options.loadPath; "function" === typeof this.options.loadPath && (l = this.options.loadPath(e, n)), (l = function(e) { return !!e && "function" === typeof e.then }(o = l) ? o : Promise.resolve(o)).then((function(o) { if (!o) return a(null, {}); var l = i.services.interpolator.interpolate(o, { lng: e.join("+"), ns: n.join("+") });
                            i.loadUrl(l, a, t, r) })) } }, { key: "loadUrl", value: function(e, t, n, r) { var a = this;
                        this.options.request(this.options, e, void 0, (function(o, i) { if (i && (i.status >= 500 && i.status < 600 || !i.status)) return t("failed loading " + e + "; status code: " + i.status, !0); if (i && i.status >= 400 && i.status < 500) return t("failed loading " + e + "; status code: " + i.status, !1); if (!i && o && o.message && o.message.indexOf("Failed to fetch") > -1) return t("failed loading " + e + ": " + o.message, !0); if (o) return t(o, !1); var l, s; try { l = "string" === typeof i.data ? a.options.parse(i.data, n, r) : i.data } catch (c) { s = "failed parsing " + e + " to json" } if (s) return t(s, !1);
                            t(null, l) })) } }, { key: "create", value: function(e, t, n, r, a) { var o = this; if (this.options.addPath) { "string" === typeof e && (e = [e]); var i = this.options.parsePayload(t, n, r),
                                l = 0,
                                s = [],
                                c = [];
                            e.forEach((function(n) { var r = o.options.addPath; "function" === typeof o.options.addPath && (r = o.options.addPath(n, t)); var d = o.services.interpolator.interpolate(r, { lng: n, ns: t });
                                o.options.request(o.options, d, i, (function(t, n) { l += 1, s.push(t), c.push(n), l === e.length && a && a(s, c) })) })) } } }, { key: "reload", value: function() { var e = this,
                            t = this.services,
                            n = t.backendConnector,
                            r = t.languageUtils,
                            a = t.logger,
                            o = n.language; if (!o || "cimode" !== o.toLowerCase()) { var i = [],
                                l = function(e) { r.toResolveHierarchy(e).forEach((function(e) { i.indexOf(e) < 0 && i.push(e) })) };
                            l(o), this.allOptions.preload && this.allOptions.preload.forEach((function(e) { return l(e) })), i.forEach((function(t) { e.allOptions.ns.forEach((function(e) { n.read(t, e, "read", null, null, (function(r, o) { r && a.warn("loading namespace ".concat(e, " for language ").concat(t, " failed"), r), !r && o && a.log("loaded namespace ".concat(e, " for language ").concat(t), o), n.loaded("".concat(t, "|").concat(e), r, o) })) })) })) } } }], n && iF(t.prototype, n), r && iF(t, r), Object.defineProperty(t, "prototype", { writable: !1 }), e }();
        sF.type = "backend"; const cF = sF; var dF = [],
            uF = dF.forEach,
            hF = dF.slice; var mF, pF = function(e, t, n, r) { var a, o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : { path: "/" }; if (n) { var i = new Date;
                    i.setTime(i.getTime() + 60 * n * 1e3), a = "; expires=" + i.toUTCString() } else a = "";
                r = r ? "domain=" + r + ";" : "", o = Object.keys(o).reduce((function(e, t) { return e + ";" + t.replace(/([A-Z])/g, (function(e) { return "-" + e.toLowerCase() })) + "=" + o[t] }), ""), document.cookie = e + "=" + encodeURIComponent(t) + a + ";" + r + o },
            fF = function(e) { for (var t = e + "=", n = document.cookie.split(";"), r = 0; r < n.length; r++) { for (var a = n[r];
                        " " === a.charAt(0);) a = a.substring(1, a.length); if (0 === a.indexOf(t)) return a.substring(t.length, a.length) } return null },
            vF = { name: "cookie", lookup: function(e) { var t; if (e.lookupCookie && "undefined" !== typeof document) { var n = fF(e.lookupCookie);
                        n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupCookie && "undefined" !== typeof document && pF(t.lookupCookie, e, t.cookieMinutes, t.cookieDomain, t.cookieOptions) } },
            gF = { name: "querystring", lookup: function(e) { var t; if ("undefined" !== typeof window)
                        for (var n = window.location.search.substring(1).split("&"), r = 0; r < n.length; r++) { var a = n[r].indexOf("="); if (a > 0) n[r].substring(0, a) === e.lookupQuerystring && (t = n[r].substring(a + 1)) }
                    return t } }; try { mF = "undefined" !== window && null !== window.localStorage; var yF = "i18next.translate.boo";
            window.localStorage.setItem(yF, "foo"), window.localStorage.removeItem(yF) } catch (FF) { mF = !1 } var bF, wF = { name: "localStorage", lookup: function(e) { var t; if (e.lookupLocalStorage && mF) { var n = window.localStorage.getItem(e.lookupLocalStorage);
                    n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupLocalStorage && mF && window.localStorage.setItem(t.lookupLocalStorage, e) } }; try { bF = "undefined" !== window && null !== window.sessionStorage; var zF = "i18next.translate.boo";
            window.sessionStorage.setItem(zF, "foo"), window.sessionStorage.removeItem(zF) } catch (FF) { bF = !1 } var xF = { name: "sessionStorage", lookup: function(e) { var t; if (e.lookupsessionStorage && bF) { var n = window.sessionStorage.getItem(e.lookupsessionStorage);
                        n && (t = n) } return t }, cacheUserLanguage: function(e, t) { t.lookupsessionStorage && bF && window.sessionStorage.setItem(t.lookupsessionStorage, e) } },
            AF = { name: "navigator", lookup: function(e) { var t = []; if ("undefined" !== typeof navigator) { if (navigator.languages)
                            for (var n = 0; n < navigator.languages.length; n++) t.push(navigator.languages[n]);
                        navigator.userLanguage && t.push(navigator.userLanguage), navigator.language && t.push(navigator.language) } return t.length > 0 ? t : void 0 } },
            kF = { name: "htmlTag", lookup: function(e) { var t, n = e.htmlTag || ("undefined" !== typeof document ? document.documentElement : null); return n && "function" === typeof n.getAttribute && (t = n.getAttribute("lang")), t } },
            SF = { name: "path", lookup: function(e) { var t; if ("undefined" !== typeof window) { var n = window.location.pathname.match(/\/([a-zA-Z-]*)/g); if (n instanceof Array)
                            if ("number" === typeof e.lookupFromPathIndex) { if ("string" !== typeof n[e.lookupFromPathIndex]) return;
                                t = n[e.lookupFromPathIndex].replace("/", "") } else t = n[0].replace("/", "") } return t } },
            MF = { name: "subdomain", lookup: function(e) { var t; if ("undefined" !== typeof window) { var n = window.location.href.match(/(?:http[s]*\:\/\/)*(.*?)\.(?=[^\/]*\..{2,5})/gi);
                        n instanceof Array && (t = "number" === typeof e.lookupFromSubdomainIndex ? n[e.lookupFromSubdomainIndex].replace("http://", "").replace("https://", "").replace(".", "") : n[0].replace("http://", "").replace("https://", "").replace(".", "")) } return t } }; var EF = function() {
            function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                (0, lD.A)(this, e), this.type = "languageDetector", this.detectors = {}, this.init(t, n) } return (0, sD.A)(e, [{ key: "init", value: function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                    this.services = e, this.options = function(e) { return uF.call(hF.call(arguments, 1), (function(t) { if (t)
                                for (var n in t) void 0 === e[n] && (e[n] = t[n]) })), e }(t, this.options || {}, { order: ["querystring", "cookie", "localStorage", "sessionStorage", "navigator", "htmlTag"], lookupQuerystring: "lng", lookupCookie: "i18next", lookupLocalStorage: "i18nextLng", caches: ["localStorage"], excludeCacheFor: ["cimode"], checkWhitelist: !0, checkForSimilarInWhitelist: !1 }), this.options.checkForSimilarInWhitelist && (this.options.checkWhitelist = !0), this.options.lookupFromUrlIndex && (this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex), this.i18nOptions = n, this.addDetector(vF), this.addDetector(gF), this.addDetector(wF), this.addDetector(xF), this.addDetector(AF), this.addDetector(kF), this.addDetector(SF), this.addDetector(MF) } }, { key: "addDetector", value: function(e) { this.detectors[e.name] = e } }, { key: "detect", value: function(e) { var t = this;
                    e || (e = this.options.order); var n, r = []; if (e.forEach((function(e) { if (t.detectors[e]) { var n = t.detectors[e].lookup(t.options);
                                n && "string" === typeof n && (n = [n]), n && (r = r.concat(n)) } })), r.forEach((function(e) { if (!n) { var r = t.services.languageUtils.formatLanguageCode(e);
                                t.options.checkWhitelist && !t.services.languageUtils.isWhitelisted(r) || (n = r), !n && t.options.checkForSimilarInWhitelist && (n = t.getSimilarInWhitelist(r)) } })), !n) { var a = this.i18nOptions.fallbackLng; "string" === typeof a && (a = [a]), a || (a = []), n = "[object Array]" === Object.prototype.toString.apply(a) ? a[0] : a[0] || a.default && a.default[0] } return n } }, { key: "cacheUserLanguage", value: function(e, t) { var n = this;
                    t || (t = this.options.caches), t && (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(e) > -1 || t.forEach((function(t) { n.detectors[t] && n.detectors[t].cacheUserLanguage(e, n.options) }))) } }, { key: "getSimilarInWhitelist", value: function(e) { var t = this; if (this.i18nOptions.whitelist) { if (e.includes("-")) { var n = e.split("-")[0],
                                r = this.services.languageUtils.formatLanguageCode(n); if (this.services.languageUtils.isWhitelisted(r)) return r;
                            e = r } var a = this.i18nOptions.whitelist.find((function(n) { var r = t.services.languageUtils.formatLanguageCode(n); if (r.startsWith(e)) return r })); return a || void 0 } } }]), e }();
        EF.type = "languageDetector"; const CF = EF; let TF = { fallbackLng: "en", debug: !0, whitelist: ["en"], interpolation: { escapeValue: !1 } }; /develop.organimi.com/.test(window.location.origin) ? TF = { ...TF, backend: { loadPath: "https://organimi-client-develop.s3.amazonaws.com/locales/{{lng}}/{{ns}}.json" } } : /localhost/.test(window.location.origin) || (TF = { ...TF, backend: { loadPath: "https://organimi-client.s3.amazonaws.com/locales/{{lng}}/{{ns}}.json" } }), BD.use(cF).use(CF).use(WD.r9).init(TF); var HF = n(49244),
            LF = n(22696); const IF = () => { var e, t; const n = (document.location.pathname.match(/^\/(\w+)\//) || [])[1] || null,
                r = i().parse(null === (e = document) || void 0 === e || null === (t = e.location) || void 0 === t ? void 0 : t.search, { ignoreQueryPrefix: !0 }) || {},
                a = r.pId,
                o = r.cbId; return n ? "embed" === n ? "embed.".concat(a, ".") : "public" === n ? "public.".concat(a, ".") : "community" === n ? "cb.".concat(o, ".") : "" : "" };
        window.localStorageSafe = { localStoreSupport: function() { try { return "localStorage" in window && null !== window.localStorage } catch (FF) { return !1 } }, cookieSupport: function() { return !!navigator.cookieEnabled }, setItem: function(e, t) { if (this.localStoreSupport()) { const n = IF();
                    localStorage.setItem("".concat(n).concat(e), t) } else this.cookieSupport() ? document.cookie = e + "=" + t + "; path=/" : window.localStorageFallback = { ...window.localStorageFallback || {}, [e]: t } }, getItem: function(e) { var t; if (!this.localStoreSupport()) { if (this.cookieSupport()) { for (var n = e + "=", r = document.cookie.split(";"), a = 0; a < r.length; a++) { for (var o = r[a];
                                " " === o.charAt(0);) o = o.substring(1, o.length); if (0 === o.indexOf(n)) switch (t = o.substring(n.length, o.length)) {
                                case "true":
                                    return !0;
                                case "false":
                                    return !1;
                                default:
                                    return t } } return null } return (window.localStorageFallback || {})[e] } { const n = IF(); if (t = localStorage.getItem("".concat(n).concat(e)), !n && "publicId" === e) return localStorage.removeItem("publicId"), null; switch (t) {
                        case "true":
                            return !0;
                        case "false":
                            return !1;
                        default:
                            return t } } }, removeItem: function(e) { if (this.localStoreSupport()) { const t = IF(); return localStorage.removeItem("".concat(t).concat(e)), !0 } return this.cookieSupport() ? (document.cookie = e + "=; path=/", !0) : (delete(window.localStorageFallback || {})[e], !0) } }; var jF = n(31672),
            VF = n.n(jF); const OF = () => ((0, a.useEffect)((() => { VF().load({ google: { families: ["Source Sans Pro:400,600,700,400italic,700italic", "Poppins:300,400,500,600,700"] } }) }), []), null);
        (0, LF.setDefaultBreakpoints)([{ xs: 0 }, { small: 662 }, { medium: 800 }]); const RF = () => { const [e] = (0, un.A)("isDesktopMode", !1, !0), t = window.location.pathname.includes("embed/"), n = window.location.pathname.includes("community/"); return (0, we.jsx)(LF.BreakpointProvider, { children: e || t || n ? (0, we.jsx)(rj, {}) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(LF.Breakpoint, { xs: !0, only: !0, className: "breakpoint", children: (0, we.jsx)(fP, {}) }), (0, we.jsx)(LF.Breakpoint, { small: !0, up: !0, className: "breakpoint", children: (0, we.jsx)(rj, {}) })] }) }) },
            PF = e => { const { which: t, ctrlKey: n, metaKey: r } = e;
                (n || r || 91 === t || 93 === t) && "wheel" === e.type && e.preventDefault() };
        document.addEventListener("keydown", PF), document.addEventListener("wheel", PF); const DF = document.getElementById("root");
        (0, r.H)(DF).render((0, we.jsx)(UP.Ay, { injectFirst: !0, children: (0, we.jsx)(rD, { theme: YE, children: (0, we.jsx)(qP.A, { theme: rH.A, children: (0, we.jsx)(ee.NP, { theme: rH.A, children: (0, we.jsx)(ae.Kq, { store: WP, children: (0, we.jsxs)(HF.A, { children: [(0, we.jsx)(OF, {}), (0, we.jsx)(RF, {})] }) }) }) }) }) })), "serviceWorker" in navigator && navigator.serviceWorker.ready.then((e => { e.unregister() })) })() })();
//# sourceMappingURL=main.9e9a31d6.js.map
