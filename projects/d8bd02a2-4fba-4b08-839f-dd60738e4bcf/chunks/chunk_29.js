                    U = n(172),
                    q = n(18858),
                    G = n(22264),
                    K = n(22818),
                    Z = n(72119),
                    Y = n(10621),
                    X = n(59177),
                    $ = n(70579); const Q = (0, Z.Ay)(l.A)(O || (O = (0, r.A)(["\n  visibility: hidden;\n  position: absolute;\n  background-color: #fff;\n  height: ", ";\n  width: ", ";\n  border-radius: 50%;\n  display: flex;\n  z-index: 1100;\n  justify-content: center;\n  align-items: center;\n"])), (e => { let { size: t } = e; return "".concat(t, "px") }), (e => { let { size: t } = e; return "".concat(t, "px") })),
                    J = (0, Z.Ay)(F.A)(R || (R = (0, r.A)(["\n  cursor: ", ";\n  // height: ", ";\n  min-width: ", ";\n  // border-radius: 50%;\n  display: flex;\n  z-index: 1100;\n  justify-content: center;\n  align-items: center;\n  margin-right: 6px;\n  background-color: #fff;\n  width: 85%;\n  height: 100%;\n  border-radius: 5px;\n"])), (e => { let { showMoveCursor: t } = e; return t ? "move" : "default" }), (e => { let { size: t } = e; return "".concat(t, "px") }), (e => { let { size: t } = e; return "".concat(t, "px") })),
                    ee = (0, Z.Ay)(U.A)(P || (P = (0, r.A)(["\n  padding: 0;\n  height: 100%;\n  &:hover {\n    .member-image {\n      opacity: 100%;\n    }\n    ", " {\n      visibility: visible;\n      transform: rotate(-4.21deg);\n    }\n    .hover-icons {\n      visibility: visible;\n    }\n  }\n"])), Q),
                    te = (0, Z.Ay)(l.A)(D || (D = (0, r.A)(["\n  background-color: #f9f9f9;\n  height: 100%;\n  border-bottom-left-radius: 6px;\n  border-top-left-radius: 6px;\n  margin-right: 8px;\n  min-width: 30px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"]))),
                    ne = "No Title Assigned",
                    re = e => { let { allowEdit: t, allowClickOrDrag: n, avatarSize: r = 30, canFindMember: o, handleSelectItem: i, member: d, photoComponent: u, roleText: h, onCancel: m, onFindClick: p, onEditClick: f, onItemDrag: v } = e, { id: g, memberPhotoColor: y, roleDefaults: b } = d; const w = (0, Y.J$)(d),
                            z = (0, Y.II)(d),
                            x = (0, Y.US)(d); let A = h || (0, X.aG)(null === b || void 0 === b ? void 0 : b.title); const { dropZoneCardRef: S, newActionCardState: E } = (0, G.A)(), [, C] = E, [, T] = S.current, [{ isDragging: H }, L] = (0, q.i)({ item: { name: g, type: K.A.PERSON }, collect: e => ({ isDragging: e.isDragging(), handlerId: e.getHandlerId() }), end() { "function" === typeof T && T(null), m() } });
                        (0, a.useEffect)((() => { H && (v(), "function" === typeof C && C(null)) }), [H]); return (0, $.jsxs)(ee, { ContainerComponent: "div", onClick: t ? i({ id: g }) : void 0, size: r, children: [(0, $.jsxs)(J, { size: r, showMoveCursor: n, ref: n ? L : null, children: [(0, $.jsx)(N.A, { style: { display: "contents", height: "100%" }, children: (0, $.jsxs)($.Fragment, { children: [(0, $.jsx)(te, { children: (0, $.jsx)(M.Ay, { icon: "TalentpoolDrag", color: "#656565", size: "lg" }) }), u || (0, $.jsx)(B.A, { overrideColor: y, name: "".concat(z, " ").concat(x), width: r, height: r })] }) }), (0, $.jsxs)(W.A, { style: { padding: "8px", margin: 0 }, children: [(0, $.jsxs)(c.A, { container: !0, spacing: 1, children: [(0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(k.A, { variant: "body2", weight: "medium", noWrap: !0, color: "#000", children: "".concat(z, " ").concat(x) }) }), w && (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)("a", { href: w, target: "_blank", children: (0, $.jsx)(M.Ay, { color: "#005DC9", icon: "LinkedIn", size: "md", fontSize: "md" }) }) })] }), (0, $.jsx)(s.Ay, { title: A || ne, placement: "top-start", arrow: !0, children: (0, $.jsx)(l.A, { width: "90%", children: (0, $.jsx)(k.A, { variant: "body2", noWrap: !0, color: "#999", children: A || ne }) }) })] })] }), (0, $.jsx)(l.A, { position: "absolute", right: 0, className: "hover-icons", visibility: "hidden", width: "50px", zIndex: 1301, height: "100%", paddingRight: "16px", paddingLeft: "16px", bgcolor: "#F5F5F5", borderRadius: "0 5px 5spx 0", children: (0, $.jsxs)(l.A, { display: "flex", flexDirection: "column", spacing: 1, justifyContent: "center", height: "100%", alignItems: "center", children: [t && (0, $.jsx)(s.Ay, { title: "Edit member", arrow: !0, placement: "left-start", children: (0, $.jsx)(_.A, { onClick: f, size: "small", children: (0, $.jsx)(M.Ay, { icon: "Edit", size: "xs", fontSize: "xs" }) }) }), (0, $.jsx)(s.Ay, { title: o ? "Find Member in chart" : "Member not yet assigned to any role in the chart", arrow: !0, placement: "left-start", children: (0, $.jsx)("span", { children: (0, $.jsx)(_.A, { onClick: p, size: "small", disabled: !o, children: (0, $.jsx)(M.Ay, { icon: "FindLocation", size: "xs", fontSize: "xs" }) }) }) })] }) })] }) }; var ae = n(46392),
                    oe = n(37259),
                    ie = n(64021),
                    le = n(49092),
                    se = n(49768),
                    ce = n(55357); const de = e => { let { anchorEl: t, handleClose: n } = e; const r = () => { n() }; return (0, $.jsxs)(se.A, { id: "photoboard-group-popover", open: !0, anchorEl: t, onClose: n, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, children: [(0, $.jsx)(ce.A, { onClick: r, children: "Coming Soon" }), (0, $.jsx)(ce.A, { onClick: r, children: "---------" }), (0, $.jsx)(ce.A, { onClick: r, children: "---------" }), (0, $.jsx)(ce.A, { onClick: r, children: "---------" })] }) }; var ue = n(55005); const he = e => { let { anchorEl: t, handleClose: n, createSortHandler: r, orderBy: a } = e; const o = (0, E.d4)(ue.A).filter((e => e.isDefault && (e.name === Y.x2.FIRSTNAME || e.name === Y.x2.LASTNAME))).map((e => ({ ...e, attr: e.id }))); return (0, $.jsx)(se.A, { id: "talentpool-sort-popover", open: !0, anchorEl: t, onClose: n, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, children: o.map((e => { let { label: t, name: o, id: i } = e; return (0, $.jsx)(ce.A, { onClick: (l = o, e => { r(l)(e), n() }), selected: o === a, children: t }, "sort-talentpool-".concat(i)); var l })) }) }; var me, pe, fe = n(52643),
                    ve = n(35033); const ge = (0, Z.Ay)(l.A)(me || (me = (0, r.A)(["\n  cursor: pointer;\n  .MuiTabs-indicator {\n    background-color: ", ";\n  }\n  &:hover ", " {\n    // color: #ffffff;\n  }\n  &:hover {\n    // background: ", ";\n  }\n"])), (e => { let { theme: t } = e; return t.palette.grey[400] }), k.A, (e => { let { theme: t } = e; return t.palette.grey[400] })),
                    ye = (0, Z.Ay)(c.A)(pe || (pe = (0, r.A)(["\n  border-top: solid #cccccc 1px;\n"]))),
                    be = e => { let { tabs: t = [], activeTab: n, handleClickCurry: r } = e; return (0, $.jsx)(ye, { container: !0, justifyContent: "center", alignItems: "center", children: (0, $.jsx)(ve.A, { value: n, children: t.map((e => { let { name: t, id: a } = e; return (0, $.jsx)(c.A, { id: "tab_".concat(a), item: !0, xs: 4, onClick: r ? r(a) : void 0, children: (0, $.jsx)(ge, { display: "flex", style: { borderBottom: n === a ? "4px solid #35CDBA" : "1px solid #FFF" }, justifyContent: "center", children: (0, $.jsx)(fe.A, { value: a, label: t, id: "talentpool-".concat(a), style: { fontSize: "14px" } }, "talentpool-".concat(a)) }) }) })) }) }) }; var we = n(37869),
                    ze = n(84),
                    xe = n(36138),
                    Ae = n(66856),
                    ke = n(19367),
                    Se = n(79091),
                    Me = n(48853),
                    Ee = n(75687),
                    Ce = n(72835),
                    Te = n(43862); const He = e => { let { anchorEl: t, handleClose: n, menuItems: r } = e; return (0, $.jsx)(l.A, { marginTop: "8px", children: (0, $.jsx)(se.A, { id: "talentpool-additional-menu-popover", open: !0, anchorEl: t, onClose: n, getContentAnchorEl: null, anchorOrigin: { vertical: 32, horizontal: "left" }, children: r.map((e => (0, $.jsxs)(ce.A, { onClick: null === e || void 0 === e ? void 0 : e.onClick, children: [(0, $.jsx)(N.A, { children: (0, $.jsx)(M.Ay, { icon: null === e || void 0 === e ? void 0 : e.icon }) }), null === e || void 0 === e ? void 0 : e.label] }, "additonal-menu-talentpool-".concat(e.name)))) }) }) }; var Le, Ie = n(66588),
                    je = n(94916),
                    Ve = n(43940),
                    Oe = n(70725),
                    Re = n(29794); const Pe = (0, Z.Ay)(Ce.A)(Le || (Le = (0, r.A)(["\n  padding: 0;\n  min-width: 45px;\n  min-height: 38px;\n  margin-right: 8px;\n"]))),
                    De = [{ name: "All", id: "all" }, { name: "Unassigned", id: "unassigned" }, { name: "Assigned", id: "assigned" }],
                    Fe = ["single", "shared", "assistant"],
                    Ne = () => { var e, t; const n = (0, E.wA)(),
                            { t: r } = (0, le.B)(),
                            d = (0, a.useRef)(null),
                            { params: { orgId: u, chartId: h } } = (0, xe.u)([(0, Ae.si)()]),
                            { appHeaderHeight: m } = (0, Re.A)(),
                            p = (0, E.d4)(V.GJ),
                            f = (0, E.d4)(V.KO),
                            v = (0, E.d4)(V.VL),
                            g = (0, E.d4)(j.wr),
                            y = (0, E.d4)(ke.kw),
                            b = (0, E.d4)(ke.Mk),
                            [w, O] = (0, je.A)("recent.talentPoolTab"),
                            { userHasMinAccess: R } = (0, Me.A)(),
                            { onTourEventComplete: P } = (0, Ve.M)({}),
                            { chartControlState: D, loadingCardRef: F } = (0, G.A)() || {},
                            { height: N } = function(e) { const [t, n] = (0, a.useState)(null), r = (0, a.useCallback)((() => { if (e && e.current) { const { width: t, height: r } = e.current.getBoundingClientRect();
                                        n({ width: t, height: r }) } }), [e]); return (0, a.useEffect)((() => (r(), window.addEventListener("resize", r), () => { window.removeEventListener("resize", r) })), [r]), { ...t } }(d),
                            [_, B] = (0, a.useState)(w || "all"),
                            [W, U] = (0, a.useState)(null),
                            [q, Z] = (0, a.useState)(null),
                            [Q, J] = (0, a.useState)("asc"),
                            [ee, te] = (0, a.useState)(Y.x2.FIRSTNAME),
                            [ne, se] = (0, a.useState)(null),
                            [ce, ue] = (0, a.useState)(""),
                            [me, pe] = (0, a.useState)(null),
                            [fe, ve] = (0, a.useState)({ show: !1 }),
                            ge = y && y.syncEnabled,
                            ye = !ge && R(i.td.EDITOR),
                            Ce = R(i.td.EDITOR),
                            Le = R(i.td.ADMIN),
                            { openDialog: Fe } = (0, ze.A)("alert"),
                            { openDialog: Ne } = (0, ze.A)("assignPersonFromTalentPoolDialog"),
                            { openDialog: _e } = (0, ze.A)("unsyncChart"),
                            { openDialog: Be } = (0, ze.A)("cleanupDialog"),
                            { show: We } = (0, Ie.A)(),
                            Ue = Boolean(me),
                            qe = (e, t, n) => { var r, a, o, i; let l = (0, X.Ok)((null === e || void 0 === e || null === (r = e.fields) || void 0 === r || null === (a = r.find((e => e.name === n))) || void 0 === a ? void 0 : a.value) || "").toLowerCase(),
                                    s = (0, X.Ok)((null === t || void 0 === t || null === (o = t.fields) || void 0 === o || null === (i = o.find((e => e.name === n))) || void 0 === i ? void 0 : i.value) || "").toLowerCase(); return l.localeCompare(s) },
                            Ge = (0, a.useCallback)((e => "desc" !== Q ? (t, n) => qe(t, n, e) : (t, n) => -qe(t, n, e)), [Q]),
                            Ke = (0, a.useMemo)((() => { const e = e => { var t, n; return (null === (t = (0, Y.II)(e)) || void 0 === t ? void 0 : t.toLowerCase().includes(ce.toLowerCase())) || (null === (n = (0, Y.US)(e)) || void 0 === n ? void 0 : n.toLowerCase().includes(ce.toLowerCase())) || "".concat((0, Y.II)(e), " ").concat((0, Y.US)(e) ? (0, Y.US)(e) : "").toLowerCase().includes(ce.toLowerCase()) }; switch (_) {
                                    case "unassigned":
                                        return g.filter((t => t.id && !p.includes(t.id) && e(t))).sort(Ge(ee));
                                    case "assigned":
                                        return g.filter((t => t.id && p.includes(t.id) && e(t))).sort(Ge(ee));
                                    case "all":
                                        return g.filter((t => t.id && e(t))).sort(Ge(ee));
                                    default:
                                        return [] } }), [p, _, ce, g, Q, ee]),
                            Ze = () => { pe(null), D.current = { type: "DEFAULT", draggedItemType: "", item: null, itemClickOrDragCB: null, itemClickOrDragErrorCB: null, itemAssignOrCancelCB: null } },
                            Ye = e => { const { role: t } = e, { item: n } = D.current;
                                Ne({ orgId: u, chartId: h, role: t, member: n, onSubmit: () => { const [, e] = F.current || []; "function" === typeof e && e(null), Ze() }, loadingCardRef: F }) },
                            Xe = e => { Fe({ title: "Failed to assign", message: (null === e || void 0 === e ? void 0 : e.message) || "Something went wrong." }) },
                            $e = e => () => {
                                (e => { D.current = { type: "TALENTPOOL", dragItemType: K.A.PERSON, item: e, itemClickOrDragCB: Ye, itemClickOrDragErrorCB: Xe, itemAssignOrCancelCB: Ze } })(e) },
                            Qe = () => { ge && _e({ chartId: b.id }) },
                            { isItemSelected: Je, handleSelectItem: et, resetSelected: tt } = (0, Ee.U)({ selectField: "id" });
                        (0, a.useEffect)((() => (tt(), () => { tt() })), []); const nt = () => { Be({ orgId: null === b || void 0 === b ? void 0 : b.organization }) },
                            rt = e => { var t; let { index: r, isScrolling: a, isVisible: o, key: i, style: c } = e; if (!Ke[r]) return null; if (a && !o) return (0, $.jsx)("div", { style: c, "data-tour-anchor": "talent-pool-person-card", children: (0, $.jsx)(l.A, { marginBottom: 1, borderRadius: "borderRadius", border: .5, borderColor: "#bbbbbc", height: 66, flexWrap: !0, children: (0, $.jsx)(ae.A, {}) }) }, i); let { id: d } = Ke[r], u = f[d], h = u && u.join(", "); return (0, $.jsx)("div", { style: c, children: (0, $.jsx)(s.Ay, { open: Ue && d === me, arrow: !0, title: "Click on any role in chart to assign me", placement: "top", children: (0, $.jsx)(l.A, { borderRadius: 5, border: .5, borderColor: "#bbbbbc", height: 60, marginBottom: 1, children: (0, $.jsx)(re, { allowClickOrDrag: ye, allowEdit: Ce, avatarSize: 30, canFindMember: v[null === (t = Ke[r]) || void 0 === t ? void 0 : t.id], handleSelectItem: et, isItemSelected: Je, member: Ke[r], onCancel: Ze, onEditClick: (p = Ke[r], e => { e.stopPropagation(), ve({ show: !0, mode: "edit", person: p }) }), onFindClick: (m = Ke[r], e => { var t;
                                                    e.stopPropagation(); const r = v[null === m || void 0 === m ? void 0 : m.id] || [];
                                                    n((0, Te.mO)({ roleId: null === (t = r[0]) || void 0 === t ? void 0 : t.id })) }), onItemDrag: ye ? $e(Ke[r]) : Qe, photoComponent: (0, $.jsx)(we.A, { person: Ke[r], size: 30 }), roleText: h }) }) }) }, i); var m, p },
                            at = () => { const e = e => { let { title: t, message: n } = e; return (0, $.jsxs)(k.A, { variant: "h3", children: [t, (0, $.jsx)(l.A, { mt: 1, children: (0, $.jsx)(k.A, { children: n }) })] }) }; return null !== fe && void 0 !== fe && fe.show ? (0, $.jsx)($.Fragment, {}) : g ? ce ? (0, $.jsx)(ie.A, { text: e({ title: r("General.Text.NoMemberFound") }), image: oe.A }) : 0 === g.length ? (0, $.jsx)(ie.A, { text: e({ title: r("General.Text.TalentPoolNoMemberInOrgTitle"), message: r("General.Text.TalentPoolNoMemberInOrgMessage") }), image: oe.A }) : "assigned" === _ ? (0, $.jsx)(ie.A, { text: e({ title: r("General.Text.TalentPoolNoAssignedMemberTitle"), message: r("General.Text.TalentPoolNoAssignedMemberMessage") }), image: oe.A }) : "unassigned" === _ ? (0, $.jsx)(ie.A, { text: e({ title: r("General.Text.TalentPoolNoUnassignedMemberTitle"), message: r("General.Text.TalentPoolNoUnassignedMemberMessage") }), image: oe.A }) : (0, $.jsx)(ie.A, { text: r("General.Text.NothingHere"), image: oe.A }) : [1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map((() => !g.length && (0, $.jsx)(l.A, { marginBottom: 1, borderRadius: "borderRadius", border: 1, borderColor: "#6b6b6b", height: 66, flexWrap: !0, children: (0, $.jsx)(ae.A, {}) }))) },
                            ot = (0, $.jsx)(I.t$, { children: e => { let { width: t } = e; return (0, $.jsx)(I.B8, { style: { overflowX: "hidden" }, height: N, rowCount: Ke.length, rowHeight: 74, overscanRowCount: 10, rowRenderer: rt, noRowsRenderer: at, width: t }) } }),
                            it = () => { Z(null) };
                        (0, a.useEffect)((() => { n((0, Se.Qs)()) }), []); const lt = [{ icon: "Broom", onClick: nt, label: "Cleanup", name: "cleanup" }]; return (0, $.jsxs)($.Fragment, { children: [(0, $.jsx)(o.Ay, { top: i.Ay.toolbars.chartToolbarHeight, zIndexType: "modal", width: i.Ay.dialogs.talentPoolCardWidth, appHeaderHeight: m - i.Ay.toolbars.chartToolbarHeight, right: 0, "data-tour-anchor": "talent-pool-main-dialog", children: (0, $.jsxs)(H.A, { container: !0, direction: "column", justifyContent: "flex-start", wrap: "nowrap", children: [(0, $.jsx)(c.A, { item: !0, style: { backgroundColor: "#5C2DBF" }, children: (0, $.jsx)(l.A, { mt: 1, mr: 2, ml: 2, children: ne ? (0, $.jsx)(z, { direction: "left", in: ne, mountOnEnter: !0, unmountOnExit: !0, children: (0, $.jsx)(c.A, { container: !0, justifyContent: "space-between", alignItems: "center", alignContent: "center", children: (0, $.jsx)(c.A, { item: !0, xs: 2, children: (0, $.jsx)(l.A, { pt: 1, children: (0, $.jsx)(s.Ay, { title: "Close search", children: (0, $.jsx)(S.A, { onClick: () => { se(!1), ue("") }, children: (0, $.jsx)(M.Ay, { icon: "LeftArrow" }) }) }) }) }) }) }) : (0, $.jsxs)(c.A, { container: !0, justifyContent: "space-between", children: [(0, $.jsx)(A.W, { container: !0, justifyContent: "flex-start", children: (0, $.jsx)(l.A, { pt: 1, pl: 1, display: "flex", justifyContent: "flex-start", height: 50, clone: !0, children: (0, $.jsx)(c.A, { item: !0, xs: !0, children: (0, $.jsx)(k.A, { style: { fontSize: 20, fontWeight: 600 }, color: "#FFF", children: "Talent Pool" }) }) }) }), (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(S.A, { onClick: () => { n((0, C.zi)()) }, children: (0, $.jsx)(M.Ay, { icon: "Close", color: "#FFF" }) }) })] }) }) }), (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(l.A, { px: 2, pr: 1, children: (0, $.jsx)(be, { tabs: De, handleClickCurry: e => () => { O(e), B(e) }, activeTab: _, rowCount: null === Ke || void 0 === Ke ? void 0 : Ke.length }) }) }), (0, $.jsx)(c.A, { item: !0, children: (0, $.jsxs)(c.A, { container: !0, justifyContent: "space-between", alignItems: "center", alignContent: "center", style: { gridGap: 1 }, children: [(0, $.jsx)(l.A, { py: 2, pl: 2, pr: 1, item: !0, flex: 1, children: (0, $.jsx)(T.A, { children: (0, $.jsx)(x.A, { size: "small", variant: "outlined", placeholder: "Find people", value: ce, fullWidth: !0, onChange: e => { ue(e.target.value) }, autoFocus: !0 }) }) }), (0, $.jsx)(s.Ay, { title: "Sort", placement: "top", arrow: !0, children: (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(Pe, { variant: "outlined", color: "primary", size: "small", onClick: (st = "sort", e => { U(null === e || void 0 === e ? void 0 : e.currentTarget), Z(st) }), children: (0, $.jsx)(M.Ay, { icon: "ListSort", size: "lg" }) }) }) }), Le && (0, $.jsx)(s.Ay, { title: "Cleanup", placement: "top", arrow: !0, children: (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(Pe, { variant: "outlined", color: "primary", size: "small", onClick: nt, children: (0, $.jsx)(M.Ay, { icon: "Broom", size: "lg" }) }) }) }), Ce && (0, $.jsx)(s.Ay, { title: "Add Person", placement: "top", arrow: !0, children: (0, $.jsx)(c.A, { item: !0, children: (0, $.jsx)(Pe, { variant: "contained", color: "primary", size: "small", onClick: () => { P({ event: "talent-pool-add-person-btn--click" }), ve({ show: !0, mode: "add" }) }, "data-tour-anchor": "talent-pool-add-person-btn", children: (0, $.jsx)(M.Ay, { icon: "AddUser", size: "lg" }) }) }) })] }) }), (null === fe || void 0 === fe ? void 0 : fe.show) && (0, $.jsx)(l.A, { item: !0, p: 2, pr: 1, style: { flex: 2 }, children: (0, $.jsx)(l.A, { border: "1px solid #bbbbbc", borderRadius: 5, height: "100%", children: (0, $.jsx)(Oe.A, { mode: fe.mode, person: fe.person || {}, activeChartRole: (null === (e = fe.person) || void 0 === e ? void 0 : e.id) && p.includes(null === (t = fe.person) || void 0 === t ? void 0 : t.id), onCancelCB: () => ve({ show: !1 }), onSaveCB: () => { ve({ show: !1 }), We(fe.mode ? "Changes saved successfully." : "Person created successfully.") } }) }) }), (0, $.jsx)(L.A, { item: !0, xs: !0, children: (0, $.jsx)(l.A, { position: "absolute", top: 0, bottom: 0, right: 0, px: 2, pr: 1, width: "100%", overflow: "hidden", ref: d, children: ot }) })] }) }), (0, $.jsxs)($.Fragment, { children: ["Filter" === q && (0, $.jsx)(de, { handleClose: it, anchorEl: W }), "sort" === q && (0, $.jsx)(he, { createSortHandler: e => () => { J(ee === e && "asc" === Q ? "desc" : "asc"), te(e) }, handleClose: it, anchorEl: W, orderBy: ee }), "menu" === q && (0, $.jsx)(He, { handleClose: it, anchorEl: W, menuItems: lt })] })] }); var st } }, 70669: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => c, Q$: () => i, tg: () => l, zi: () => s }); var r = n(80907); const a = "talentPool",
                    o = ((0, n(9787).a)({ slice: a, scope: "talentPools" }), (0, r.Z0)({ name: a, initialState: { open: !1, props: {} }, reducers: { openTalentPool: (e, t) => { let { payload: n = {} } = t;
                                e.open = !0, e.props = n || {} }, closeTalentPool: e => { e.open = !1, e.props = {} } }, extraReducers: {} })),
                    i = e => e.talentPool.open,
                    { openTalentPool: l, closeTalentPool: s } = o.actions,
                    c = o.reducer }, 649: (e, t, n) => { "use strict";
                n.r(t), n.d(t, { default: () => y }); var r = n(65043),
                    a = n(78492),
                    o = n(96446),
                    i = n(85865),
                    l = n(68577),
                    s = n(14256),
                    c = n(14556),
                    d = n(84),
                    u = n(94234),
                    h = n(86674),
                    m = n(32115),
                    p = n(37294),
                    f = n(2173),
                    v = n(24115),
                    g = n(70579);

                function y() { const [e, t] = (0, r.useState)(u.cD.makeDefault), n = (0, c.wA)(), { closeDialog: y, props: { orgId: b, themeId: w, chartId: z } } = (0, d.A)("applyTheme"), x = () => { n((0, u.y0)()), y() }, [A, k] = (0, f.A)((async () => { if (e !== u.cD.makeDefault && z) { const { error: e } = await n(u.ZO.setActive({ orgId: b, chartId: z, themeId: w, mode: u.cD.applyToChart }));
                            e || x() } else if (e !== u.cD.makeDefault) x();
                        else { const { error: e } = await n(u.ZO.setActive({ orgId: b, chartId: z, themeId: w, mode: u.cD.makeDefault }));
                            e || x() } })); return (0, g.jsx)(h.A, { maxWidth: "md", handleClose: x, handleSuccess: k, title: "Apply theme to", btnText: { success: "Submit", cancel: "Cancel" }, render: () => (0, g.jsx)(v.A, { loading: A, transparent: !0, children: (0, g.jsxs)(a.A, { "aria-label": "theme", value: e, name: "applyTo", onChange: e => t(e.target.value), children: [(0, g.jsx)(o.A, { mt: 3, children: (0, g.jsx)(i.A, { variant: m.Eq.labelLG, color: p.Qs.Neutrals[900], children: "Make theme the default for which?" }) }), (0, g.jsx)(l.A, { value: u.cD.makeDefault, control: (0, g.jsx)(s.A, { color: "primary", size: "small" }), label: (0, g.jsx)(i.A, { variant: m.Eq.bodySM, children: "Make it the default theme for this organization" }) }), (0, g.jsx)(l.A, { value: u.cD.applyToChart, control: (0, g.jsx)(s.A, { color: "primary", size: "small" }), label: (0, g.jsx)(i.A, { variant: m.Eq.bodySM, children: "This chart only" }) })] }) }) }) } }, 14279: (e, t, n) => { "use strict";
                n.r(t), n.d(t, { default: () => g }); var r = n(96446),
                    a = n(67784),
                    o = n(78492),
                    i = n(85865),
                    l = n(68577),
                    s = n(14256),
                    c = n(14556),
                    d = n(84),
                    u = n(94234),
                    h = n(86674),
                    m = n(61258),
                    p = n(32115),
                    f = n(37294),
                    v = n(70579);

                function g() { const e = (0, c.wA)(),
                        { closeDialog: t, props: { onSuccess: n, dashboardMode: g } } = (0, d.A)("createTheme"); return (0, v.jsx)(h.A, { maxWidth: "md", handleClose: () => { e((0, u.y0)()), t() }, handleSuccess: async r => { n(null, null, r), e((0, u.y0)()), t() }, title: "Create new theme", btnText: { success: "Submit", cancel: "Cancel" }, render: e => { var t; let { register: n, control: c, formState: { errors: d } } = e; return (0, v.jsxs)(r.A, { width: "400px", gap: 2, pt: 2, children: [(0, v.jsx)(a.A, { fullWidth: !0, required: !0, label: "Theme Name", name: "name", inputRef: n({ required: "Theme name is required" }), error: d.name, helperText: null === d || void 0 === d || null === (t = d.name) || void 0 === t ? void 0 : t.message }), !g && (0, v.jsx)(v.Fragment, { children: (0, v.jsx)(m.xI, { name: "applyTo", control: c, defaultValue: u.cD.applyToChart, render: e => { let { onChange: t, value: n } = e; return (0, v.jsx)(v.Fragment, { children: (0, v.jsxs)(o.A, { "aria-label": "theme", onChange: e => t(e.target.value), value: n, children: [(0, v.jsx)(r.A, { mt: 3, children: (0, v.jsx)(i.A, { variant: p.Eq.labelLG, color: f.Qs.Neutrals[900], children: "How should the theme be applied?" }) }), (0, v.jsx)(l.A, { value: u.cD.makeDefault, control: (0, v.jsx)(s.A, { color: "primary", size: "small" }), label: (0, v.jsx)(i.A, { variant: p.Eq.bodySM, color: f.Qs.Neutrals[700], children: "Make it the default theme for this organization" }) }), !g && (0, v.jsx)(l.A, { value: u.cD.applyToChart, control: (0, v.jsx)(s.A, { color: "primary", size: "small" }), label: (0, v.jsx)(i.A, { variant: p.Eq.bodySM, color: f.Qs.Neutrals[700], children: "Apply theme to this chart" }) }), g && (0, v.jsx)(l.A, { value: u.cD.applyToNothing, control: (0, v.jsx)(s.A, { color: "primary", size: "small" }), label: (0, v.jsx)(i.A, { variant: p.Eq.bodySM, color: f.Qs.Neutrals[700], children: "Only view / edit theme" }) })] }) }) } }) })] }) } }) } }, 93545: (e, t, n) => { "use strict";
                n.d(t, { LG: () => mr }); var r, a = n(57528),
                    o = n(65043),
                    i = n(96446),
                    l = n(85865),
                    s = n(17392),
                    c = n(42518),
                    d = n(85725),
                    u = n(91688),
                    h = n(57546),
                    m = n(14556),
                    p = n(89656),
                    f = n(24738),
                    v = n(20252),
                    g = n(79091),
                    y = n(81635),
                    b = n(70669),
                    w = n(39336),
                    z = n(77739),
                    x = n(32143),
                    A = n(96364),
                    k = n(9989),
                    S = n(72119); const M = (0, S.Ay)(k.A)(r || (r = (0, a.A)(["\n  .MuiListItem-gutters {\n    padding: 0;\n  }\n"]))); var E, C, T = n(26805),
                    H = n.n(T),
                    L = n(94234),
                    I = n(75156),
                    j = n(84),
                    V = n(72835),
                    O = n(79243),
                    R = n(32115),
                    P = n(37294),
                    D = n(70579); const F = (0, S.Ay)(O.A)(E || (E = (0, a.A)(["\n  .Mui-disabled {\n    background: ", ";\n    color: white;\n    border-color: ", ";\n    height: 24px;\n  }\n"])), P.Qs.Neutrals[700], P.Qs.Neutrals[700]),
                    N = (0, S.Ay)(V.A)(C || (C = (0, a.A)(["\n  color: ", ";\n  border: solid 1px ", ";\n  font-size: ", ";\n\n  ", "\n\n  ", "\n"])), P.Qs.Neutrals[800], P.Qs.Neutrals[500], (e => e.fontSize), (e => { let { $forceDisabled: t } = e; return "\n    ".concat(t ? "\n        &.Mui-disabled {\n          background: ".concat(P.Qs.Neutrals[300], ";\n          color: ").concat(P.Qs.Neutrals[500], ";\n          border-color: ").concat(P.Qs.Neutrals[500], ";\n          cursor: not-allowed;\n          pointer-events: all;\n\n          &:hover{\n            background: ").concat(P.Qs.Neutrals[300], ";\n          }\n        }\n    ") : "", " \n  ") }), (e => { let { $selected: t } = e; return "\n    ".concat(t ? "\n      background: ".concat(P.Qs.Neutrals[700], ";\n      color: white;\n      border-color: ").concat(P.Qs.Neutrals[700], ";\n      height: 24px;\n      &:hover {\n        color: ").concat(P.Qs.Neutrals[800], ";\n      }\n    ") : "", " \n  ") })),
                    _ = e => { let { name: t, data: n, value: r, onChange: a, label: o, fullWidth: i = !1, multi: s, forceDisabled: c = !1 } = e; const d = (e, t) => () => { if (s && Array.isArray(r)) { const n = r.indexOf(t); if (-1 === n) a(e, [...r, t]);
                                else { const t = [...r];
                                    t.splice(n, 1), a(e, t) } } else a(e, t) }; return (0, D.jsx)(D.Fragment, { children: (0, D.jsx)(F, { fullWidth: i, size: "small", variant: "outlined", color: "primary", "aria-label": "outlined primary button group", children: n.map((e => (0, D.jsx)(N, { fontSize: "Field Border" === o && "14px", onClick: d(t, e.value), disabled: c || !s && r === e.value, $selected: s && Array.isArray(r) && r.includes(e.value), $forceDisabled: c, children: (0, D.jsx)(l.A, { variant: R.Eq.caption, color: "inherit", children: e.label }) }, e.value))) }) }) },
                    B = e => { let { label: t, data: n, value: r, name: a, onChange: o, singleLine: s = !1, multi: c = !1, disabled: d = !1 } = e; const u = (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: t }),
                            h = (0, D.jsx)(_, { fullWidth: !0, data: n, value: r, name: a, onChange: o, label: t, multi: c, forceDisabled: d }); return s ? (0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", alignItems: "center", gap: 2, flex: 1, children: [u, h] }) : (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", children: [(0, D.jsx)(i.A, { mb: 1, children: u }), h] }) }; var W = n(61531),
                    U = n(91812); const q = e => { let { label: t, color: n, handleChangeBackground: r } = e; return (0, D.jsxs)(W.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: [(0, D.jsx)(W.A, { flex: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: t }) }), (0, D.jsx)(W.A, { display: "flex", gap: 1, children: (0, D.jsx)(U.A, { initialColor: n, onChange: r }) })] }) }; var G = n(67784); const K = e => { let { label: t, value: n, name: r, onChange: a, options: o, children: s, singleLine: c = !1, defaultValue: d, disabled: u } = e; const h = (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: t }),
                        m = { name: r, onChange: a };
                    n && (m.value = n), d && (m.defaultValue = d); const p = (0, D.jsxs)(G.A, { select: !0, ...m, size: "medium", style: { background: "#ffffff" }, disabled: u, children: [null === o || void 0 === o ? void 0 : o.map((e => (0, D.jsx)(x.A, { value: e.value, children: e.label }, e.value))), s] }); return c ? (0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", alignItems: "center", gap: 1, children: [h, p] }) : (0, D.jsxs)(i.A, { display: "flex", gap: 1, flexDirection: "column", children: [h, p] }) }; var Z, Y, X = n(84866),
                    $ = n(17339);
                (0, S.Ay)(X.A)(Z || (Z = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  margin-top:0;\n  margin-bottom:0;\n  width:90%;\n  .Mui-disabled {\n    color: ".concat(t.palette.primary.main, ";\n    border-color:").concat(t.palette.primary.main, ";\n  }\n  .MuiOutlinedInput-adornedEnd{\n    padding-right: 5px;\n  }\n") })), (0, S.Ay)($.A)(Y || (Y = (0, a.A)(["\n  font-size: 20px;\n  padding: 4px;\n"]))); var Q = n(37918),
                    J = n(98587),
                    ee = n(58168),
                    te = n(69292),
                    ne = n(68606),
                    re = n(34535),
                    ae = n(72876),
                    oe = n(71424),
                    ie = n(51347); const le = ["className"],
                    se = (0, re.Ay)("div", { name: "MuiListItemIcon", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, "flex-start" === n.alignItems && t.alignItemsFlexStart] } })((e => { let { theme: t, ownerState: n } = e; return (0, ee.default)({ minWidth: 56, color: (t.vars || t).palette.action.active, flexShrink: 0, display: "inline-flex" }, "flex-start" === n.alignItems && { marginTop: 8 }) })),
                    ce = o.forwardRef((function(e, t) { const n = (0, ae.A)({ props: e, name: "MuiListItemIcon" }),
                            { className: r } = n,
                            a = (0, J.default)(n, le),
                            i = o.useContext(ie.A),
                            l = (0, ee.default)({}, n, { alignItems: i.alignItems }),
                            s = (e => { const { alignItems: t, classes: n } = e, r = { root: ["root", "flex-start" === t && "alignItemsFlexStart"] }; return (0, ne.A)(r, oe.f, n) })(l); return (0, D.jsx)(se, (0, ee.default)({ className: (0, te.A)(s.root, r), ownerState: l, ref: t }, a)) })); var de, ue, he, me, pe, fe, ve = n(51962),
                    ge = n(26353),
                    ye = n(8266),
                    be = n(30279),
                    we = n(49157),
                    ze = n(30105),
                    xe = n(20495),
                    Ae = n(78396); const ke = (0, S.Ay)(W.A)(de || (de = (0, a.A)(["\n  background: #f8f8f8;\n  padding: 8px;\n  border-radius: 10px;\n"]))),
                    Se = (0, S.Ay)(we.A)(ue || (ue = (0, a.A)(["\n  overflow: auto;\n  height: 100%;\n"]))),
                    Me = ((0, S.Ay)(ze.A)(he || (he = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    background: none;\n    border-radius: 16px;\n    border: 2px dashed ".concat(t.palette.grey[600], ";\n    color: ").concat(t.palette.grey[600], ";\n    box-shadow: none;\n    height: 90%;\n    padding: 55px 0;\n\n    span {\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n\n      svg{\n        margin-bottom: 10px;\n      }\n    }\n\n    &:hover {\n      box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);\n      background: none;\n      border: 2px dashed ").concat(t.palette.grey[700], ";\n      color: ").concat(t.palette.grey[700], ";\n    }\n") })), (0, S.Ay)(xe.A)(me || (me = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, dashboardMode: n = !1 } = e; return "\nposition: absolute;\nleft: ".concat(.7 * Ae.Ay.dialogs.themeSettingsWidth, "px;\nz-index: 2;\nwidth: max-content;\nbox-shadow: none;\npadding: 30px 40px;\nwidth: calc(100vw - ").concat(.7 * Ae.Ay.dialogs.themeSettingsWidth + (n ? Ae.Ay.dashboard.navWidth + 2 * Ae.Ay.dashboard.contentMargin + 2 * Ae.Ay.dashboard.contentBorder : Ae.Ay.chart.navWidth), "px);\nbackground: #fdfdfd;\nborder-bottom: 1px solid ").concat(t.palette.grey[300], ";\nborder-left: 1px solid ").concat(t.palette.grey[300], ";\nborder-radius: 0;\n") })), (0, S.Ay)(V.A)(pe || (pe = (0, a.A)(["\n  height: 50px;\n  width: 260px;\n  &:disabled {\n    cursor: not-allowed;\n  }\n"]))), (0, S.Ay)(xe.A)(fe || (fe = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  position: relative;\n  height: 100%;\n  border: solid 1px ".concat(t.palette.grey[200], ";\n  border-radius: 0px;\n  padding: 0px ").concat(t.spacing(1), "px;\n  .MuiSelect-root{\n    background:white;\n  }\n  .MuiIconButton-root {\n    padding:9px !important;\n    border-radius:0;\n  }\n  .MuiIconButton-root.Mui-disabled{\n    color:").concat(t.palette.primary.main, "\n  }\n  ") }))); var Ee = n(4598); const Ce = e => { let { label: t, checked: n, onChange: r, name: a, disabled: o } = e; return (0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", gap: 2, alignItems: "center", children: ["string" === typeof t ? (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: t }) : t, (0, D.jsx)(Ee.A, { checked: n, onChange: r, name: a, disabled: o })] }) }; var Te, He = n(21933); const Le = (0, S.Ay)(Q.Ay)(Te || (Te = (0, a.A)(["\n  ", "\n"])), (e => { let { $active: t = !1 } = e; return "\n    ".concat(t ? "background: #eeeeee;" : "background: transparent;", "\n    &:hover {\n      background: #eeeeee;\n    }\n  ") })),
                    Ie = e => { var t; let { inTheme: n, fieldKey: r, fieldLabel: a, fieldType: o, font: c, open: d, handleFieldClick: u, handleChecked: h, handleFontChange: m } = e; const p = c && Object.keys(c).reduce(((e, t) => { switch (t) {
                                    case "weight":
                                        c[t] > 400 && e.push("bold"); break;
                                    case "italic":
                                        c[t] && e.push("italic"); break;
                                    case "underline":
                                        c[t] && e.push("underline"); break;
                                    case "strikethrough":
                                        c[t] && e.push("strikethrough") } return e }), []),
                            f = { cursor: n ? "move" : "default" }; return (0, D.jsxs)("div", { style: f, children: [(0, D.jsxs)(Le, { $active: d, children: [(0, D.jsx)(ce, { children: (0, D.jsx)(ve.A, { size: "small", disableRipple: !0, checked: n, onClick: h }) }), (0, D.jsx)(ge.A, { primary: (0, D.jsx)(i.A, { maxWidth: n ? 180 : "100%", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[700], children: a }) }) }), n && (0, D.jsx)(ye.A, { children: (0, D.jsx)(s.A, { size: "small", onClick: u, children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: d ? "minus" : "gear", size: "sm", variant: "light" }) }, "icon-".concat(d, "-").concat(r)) }) })] }), n && (0, D.jsx)(be.A, { in: d, timeout: "auto", unmountOnExit: !0, children: (0, D.jsxs)(Me, { elevation: 0, children: [
                                        ["date", "computed"].includes(o) && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Date Format", value: (null === c || void 0 === c || null === (t = c.dateFormat) || void 0 === t ? void 0 : t.value) || "yyyy-MM-dd", color: "primary", onChange: e => { let t = He.zg.find((t => t.value.value === e.target.value));
                                                    m("dateFormat", t.value) }, singleLine: !0, options: He.zg.map((e => ({ value: e.value.value, label: e.label }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: c.size, color: "primary", onChange: e => { m("size", e.target.value) }, singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Color", handleChangeBackground: e => { m("color", e) }, color: c.color, showReset: !1 }) }), "boolean" !== o && "iconPicklist" !== o && (0, D.jsx)(i.A, { my: 2, className: "dummy", children: (0, D.jsx)(Ce, { label: "Show Field Label", checked: c.showLabel, onChange: e => { m("showLabel", e.target.checked) }, name: "showLabel" }) }), "iconPicklist" === o && (0, D.jsx)(i.A, { my: 2, className: "dummy", children: (0, D.jsx)(Ce, { label: "Show Icon Label", checked: c.showIconLabel, onChange: e => { m("showIconLabel", e.target.checked) }, name: "showIconLabel" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: p, onChange: (e, t) => { m("format", t) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 4, children: (0, D.jsx)(B, { label: "Case", value: c.case || "none", onChange: (e, t) => { m("case", t) }, singleLine: !0, data: He.ML }) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Border", data: He.du, value: c.border, name: "border", singleLine: !0, onChange: (e, t) => { m("border", t) } }) })
                                    ] }) })] }, r) },
                    je = e => { let { inTheme: t, content: n, fieldKey: r, fieldLabel: a, fieldType: o, font: c, open: d, handleFieldClick: u, handleChecked: h, handleFontChange: m } = e; const p = { cursor: t ? "move" : "default" },
                            f = c && Object.keys(c).reduce(((e, t) => { switch (t) {
                                    case "weight":
                                        c[t] > 400 && e.push("bold"); break;
                                    case "italic":
                                        c[t] && e.push("italic"); break;
                                    case "underline":
                                        c[t] && e.push("underline"); break;
                                    case "strikethrough":
                                        c[t] && e.push("strikethrough") } return e }), []); return (0, D.jsxs)("div", { style: p, children: [(0, D.jsxs)(Le, { children: [(0, D.jsx)(ce, { children: (0, D.jsx)(ve.A, { size: "small", disableRipple: !0, checked: t, onClick: h }) }), (0, D.jsx)(ge.A, { primary: (0, D.jsx)(i.A, { maxWidth: t ? 180 : "100%", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[700], children: a }) }) }), t && (0, D.jsx)(ye.A, { children: (0, D.jsx)(s.A, { size: "small", onClick: u, children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: d ? "minus" : "gear", size: "xs", variant: "light" }) }, "icon-".concat(d, "-").concat(r)) }) })] }, r), t && (0, D.jsx)(be.A, { in: d && !(null !== n && void 0 !== n && n.commonStyle), timeout: "auto", unmountOnExit: !0, children: (0, D.jsxs)(Me, { elevation: 0, children: [
                                        ["date", "computed"].includes(o) && (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(K, { label: "Date Format", value: c.dateFormat.value, color: "primary", onChange: e => { let t = He.zg.find((t => t.value.value === e.target.value));
                                                    m("dateFormat", t.value) }, singleLine: !0, options: He.zg.map((e => ({ value: e.value.value, label: e.label }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Font Color", color: (null === c || void 0 === c ? void 0 : c.color) || "#151515", handleChangeBackground: e => m("color", e) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: null === c || void 0 === c ? void 0 : c.size, color: "primary", onChange: e => m("size", e.target.value), singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: f, onChange: async (e, t) => { m("format", t) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Case", fullwidth: !0, value: c.case || "none", onChange: (e, t) => { m("case", t) }, singleLine: !0, data: He.ML }) }) })
                                    ] }) })] }) }; var Ve = n(40454),
                    Oe = n(80045),
                    Re = (n(2086), n(43024));

                function Pe(e, t) { return void 0 !== t && void 0 !== e && (Array.isArray(t) ? t.indexOf(e) >= 0 : e === t) } var De = n(71745),
                    Fe = n(74822),
                    Ne = o.forwardRef((function(e, t) { var n = e.children,
                            r = e.classes,
                            a = e.className,
                            i = e.exclusive,
                            l = void 0 !== i && i,
                            s = e.onChange,
                            c = e.orientation,
                            d = void 0 === c ? "horizontal" : c,
                            u = e.size,
                            h = void 0 === u ? "medium" : u,
                            m = e.value,
                            p = (0, Oe.A)(e, ["children", "classes", "className", "exclusive", "onChange", "orientation", "size", "value"]),
                            f = function(e, t) { if (s) { var n, r = m && m.indexOf(t);
                                    m && r >= 0 ? (n = m.slice()).splice(r, 1) : n = m ? m.concat(t) : [t], s(e, n) } },
                            v = function(e, t) { s && s(e, m === t ? null : t) }; return o.createElement("div", (0, ee.default)({ role: "group", className: (0, Re.A)(r.root, a, "vertical" === d && r.vertical), ref: t }, p), o.Children.map(n, (function(e) { return o.isValidElement(e) ? o.cloneElement(e, { className: (0, Re.A)(r.grouped, r["grouped".concat((0, Fe.A)(d))], e.props.className), onChange: l ? v : f, selected: void 0 === e.props.selected ? Pe(e.props.value, m) : e.props.selected, size: e.props.size || h }) : null }))) })); const _e = (0, De.A)((function(e) { return { root: { display: "inline-flex", borderRadius: e.shape.borderRadius }, vertical: { flexDirection: "column" }, grouped: {}, groupedHorizontal: { "&:not(:first-child)": { marginLeft: -1, borderLeft: "1px solid transparent", borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }, "&:not(:last-child)": { borderTopRightRadius: 0, borderBottomRightRadius: 0 } }, groupedVertical: { "&:not(:first-child)": { marginTop: -1, borderTop: "1px solid transparent", borderTopLeftRadius: 0, borderTopRightRadius: 0 }, "&:not(:last-child)": { borderBottomLeftRadius: 0, borderBottomRightRadius: 0 } } } }), { name: "MuiToggleButtonGroup" })(Ne); var Be = n(82454),
                    We = n(75992),
                    Ue = o.forwardRef((function(e, t) { var n = e.children,
                            r = e.classes,
                            a = e.className,
                            i = e.disabled,
                            l = void 0 !== i && i,
                            s = e.disableFocusRipple,
                            c = void 0 !== s && s,
                            d = e.onChange,
                            u = e.onClick,
                            h = e.selected,
                            m = e.size,
                            p = void 0 === m ? "medium" : m,
                            f = e.value,
                            v = (0, Oe.A)(e, ["children", "classes", "className", "disabled", "disableFocusRipple", "onChange", "onClick", "selected", "size", "value"]); return o.createElement(We.A, (0, ee.default)({ className: (0, Re.A)(r.root, a, l && r.disabled, h && r.selected, "medium" !== p && r["size".concat((0, Fe.A)(p))]), disabled: l, focusRipple: !c, ref: t, onClick: function(e) { u && (u(e, f), e.isDefaultPrevented()) || d && d(e, f) }, onChange: d, value: f, "aria-pressed": h }, v), o.createElement("span", { className: r.label }, n)) })); const qe = (0, De.A)((function(e) { return { root: (0, ee.default)({}, e.typography.button, { boxSizing: "border-box", borderRadius: e.shape.borderRadius, padding: 11, border: "1px solid ".concat((0, Be.X4)(e.palette.action.active, .12)), color: (0, Be.X4)(e.palette.action.active, .38), "&$selected": { color: e.palette.action.active, backgroundColor: (0, Be.X4)(e.palette.action.active, .12), "&:hover": { backgroundColor: (0, Be.X4)(e.palette.action.active, .15) }, "& + &": { borderLeft: 0, marginLeft: 0 } }, "&$disabled": { color: (0, Be.X4)(e.palette.action.disabled, .12) }, "&:hover": { textDecoration: "none", backgroundColor: (0, Be.X4)(e.palette.text.primary, .05), "@media (hover: none)": { backgroundColor: "transparent" }, "&$disabled": { backgroundColor: "transparent" } } }), disabled: {}, selected: {}, label: { width: "100%", display: "inherit", alignItems: "inherit", justifyContent: "inherit" }, sizeSmall: { padding: 7, fontSize: e.typography.pxToRem(13) }, sizeLarge: { padding: 15, fontSize: e.typography.pxToRem(15) } } }), { name: "MuiToggleButton" })(Ue),
                    Ge = e => { let { content: t, handleContentSettings: n, handleContentFormatSettings: r } = e; const a = t && Object.keys(t).reduce(((e, n) => { switch (n) {
                                case "weight":
                                    t[n] > 400 && e.push("bold"); break;
                                case "italic":
                                    t[n] && e.push("italic"); break;
                                case "underline":
                                    t[n] && e.push("underline"); break;
                                case "strikethrough":
                                    t[n] && e.push("strikethrough") } return e }), []); return (0, D.jsx)("div", { style: { border: "1px solid white", marginBottom: ".5rem" }, children: (0, D.jsxs)(Me, { elevation: 0, children: [(0, D.jsx)(W.A, { my: 3, children: (0, D.jsx)(q, { label: "Font Color", color: (null === t || void 0 === t ? void 0 : t.color) || "#151515", handleChangeBackground: e => n("color", e) }) }), (0, D.jsx)(W.A, { my: 3, children: (0, D.jsx)(K, { label: "Font size", value: null === t || void 0 === t ? void 0 : t.size, color: "primary", onChange: e => n("size", e.target.value), singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(W.A, { my: 3, children: (0, D.jsxs)(Ve.A, { container: !0, alignItems: "center", justify: "space-between", children: [(0, D.jsx)(A.A, { variant: "body1", gutterBottom: !0, style: { flex: 1 }, children: "Font style" }), (0, D.jsxs)(_e, { fullwidth: !0, value: a, onChange: async (e, t) => { r(t) }, style: { flex: 2 }, children: [(0, D.jsx)(qe, { value: "bold", style: { flex: 1 }, children: (0, D.jsx)(I.Ay, { icon: "Bold" }) }), (0, D.jsx)(qe, { value: "italic", style: { flex: 1 }, children: (0, D.jsx)(I.Ay, { icon: "Italic" }) }), (0, D.jsx)(qe, { value: "underline", style: { flex: 1 }, children: (0, D.jsx)(I.Ay, { icon: "Underline" }) }), (0, D.jsx)(qe, { value: "strikethrough", style: { flex: 1 }, children: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }) })] })] }) })] }) }) }; var Ke = n(64418),
                    Ze = n(59177); const Ye = e => { var t, n, r; let { theme: a, roleType: o = "single", children: s } = e; const c = null === (t = (0, m.d4)(f.nh)) || void 0 === t ? void 0 : t[o],
                        { handleToggleRoleTypeOverride: d } = (0, v.A)({ sample: !1 }),
                        { confirmAction: u } = (0, Ke.A)(); return (0, D.jsxs)(D.Fragment, { children: [!c && (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsxs)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[900], textAlign: "center", children: ["Some properties can be overridden for specific role types. Enable below to customize the theme for ", (() => { switch (o) {
                                        case "shared":
                                        case "assistant":
                                            return "".concat((0, Ze.ZH)(o), " roles");
                                        default:
                                            return "".concat((0, Ze.ZH)(o), "s") } })()] }) }), (0, D.jsx)(ke, { children: (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", alignItems: "center", gap: 1, children: [(0, D.jsx)(i.A, { children: (0, D.jsxs)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], textAlign: "center", children: ["Override ", (0, D.jsx)("b", { children: o }), " roles"] }) }), (0, D.jsx)(Ee.A, { checked: null === a || void 0 === a || null === (n = a.roleTypeOverrides) || void 0 === n || null === (r = n[o]) || void 0 === r ? void 0 : r.enabled, onChange: () => { var e, t;
                                        null !== a && void 0 !== a && null !== (e = a.roleTypeOverrides) && void 0 !== e && null !== (t = e[o]) && void 0 !== t && t.enabled ? u({ title: "Disable ".concat(o, " override"), message: (0, D.jsx)(D.Fragment, { children: "This action reset your override settings. \n            Are you sure you want to disable ".concat(o, " role override?") }), execFunc: d(o) }) : d(o)() }, name: "primary" })] }) }), c && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), s] })] }) }; var Xe = n(70318),
                    $e = n(19367); const Qe = e => { let { label: t, type: n, defaultValue: r, onBlur: a, inputProps: o, name: s } = e; return (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", gap: 1, children: [(0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, children: t }), (0, D.jsx)(G.A, { fullWidth: !0, type: n, defaultValue: r, onBlur: a, inputProps: o, name: s })] }) }; var Je, et, tt;
                n(53492);
                S.Ay.div(Je || (Je = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, selected: n } = e; return "\n  position:relative;\n  width: 100%;\n  height: auto;\n  display:flex;\n  flex-direction:column;\n  align-items:center;\n  justify-content:center;\n  cursor:pointer;\n  padding: 10px;\n  border:2px solid transparent;\n  ".concat(n && "\n    border:2px solid ".concat(t.palette.primary.main, ";\n    border-radius: 16px;\n    box-shadow: 1px 1px 5px 0px rgb(0 0 0 / 50%);\n  ") || "", "\n  :hover{\n    border:2px solid ").concat(t.palette.primary.main, ";\n    border-radius: 16px;\n    box-shadow: 1px 1px 5px 0px rgb(0 0 0 / 50%);\n  }\n  .Button-root{\n    position:absolute;\n    right:0px;\n    top:0px;\n  }\n  ") })), S.Ay.div(et || (et = (0, a.A)(["\nposition:absolute;\ntop -10px;\nleft: -10px;\nbackground: #fdfdfd;\n"]))), S.Ay.div(tt || (tt = (0, a.A)(["\nposition:absolute;\ntop -10px;\nright: -10px;\nbackground: #fdfdfd;\n"]))); var nt = n(45256),
                    rt = n(49194); const at = [...nt.T],
                    ot = e => { var t, n, r, a; let { innerRef: s, theme: c, isOverrideMode: d = !1, overrideRoleType: u = "single", showTitle: h = !1 } = e; const { base: { cards: v } } = c, g = (0, m.d4)(f.V1), y = (0, m.d4)(f.rj), b = (0, o.useMemo)((() => y(u)), [u, y]), k = !(null === c || void 0 === c || null === (t = c.base) || void 0 === t || null === (n = t.photos) || void 0 === n || null === (r = n.standard) || void 0 === r || !r.visible), [S, E] = (0, o.useState)(b), C = (0, m.d4)(p.P0), [T, V] = (0, o.useState)(), { openDialog: O } = (0, j.A)("customFields"), F = (0, m.wA)();
                        (0, o.useEffect)((() => { E(b) }), [b]); const N = e => d ? { ...C, roleTypeOverrides: { ...C.roleTypeOverrides, [u]: { ...C.roleTypeOverrides[u], fields: e } } } : { ...C, data: { ...C.data, chart: e } },
                            _ = (0, o.useCallback)(((e, t) => { const n = S.inTheme[e],
                                    r = H()(S.inTheme, { $splice: [
                                            [e, 1],
                                            [t, 0, n]
                                        ] });
                                E({ ...S, inTheme: [...r] }), (async e => { const t = e.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle })));
                                    await F(L.ZO.update({ orgId: g, themeId: C.id, data: N(t) })) })(r) }), [S.inTheme]),
                            W = e => () => { V(e === T ? null : e) },
                            U = e => async t => { let n = {}; if (t.target.checked) { const t = S.notInTheme.find((t => t.fieldKey === e)),
                                        r = S.notInTheme.filter((e => e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model));
                                    n = S.inTheme.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))), r.forEach((e => { var t, r; let a = "url" === (null === e || void 0 === e || null === (t = e.fieldInfo) || void 0 === t ? void 0 : t.type),
                                            o = "tags" === (null === e || void 0 === e || null === (r = e.fieldInfo) || void 0 === r ? void 0 : r.type);
                                        n.push({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: { underline: a, showLabel: a, color: a ? "#0645AD" : "#151515", border: o ? "round" : "none" } }) })) } else { const t = S.inTheme.find((t => t.fieldKey === e));
                                    n = S.inTheme.filter((e => !(e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model))).map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))) } await F(L.ZO.update({ orgId: g, themeId: C.id, data: N(n) })) }, q = async e => { const t = S.inTheme.find((t => t.fieldKey === e.fieldKey)); let n = []; switch (e.prop) {
                                    case "color":
                                    case "align":
                                    case "size":
                                    case "border":
                                    case "case":
                                    case "showLabel":
                                    case "showIconLabel":
                                    case "dateFormat":
                                        n = S.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, [e.prop]: e.value } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })); break;
                                    case "format":
                                        n = S.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, weight: e.value.includes("bold") ? 700 : 400, italic: !!e.value.includes("italic"), underline: !!e.value.includes("underline"), strikethrough: !!e.value.includes("strikethrough") } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })) } await F(L.ZO.update({ orgId: g, themeId: C.id, data: N(n) })) }; return (0, D.jsxs)("div", { ref: s, children: [h && (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingMD, color: P.Qs.Neutrals[900], children: "Fields" }) }), (0, D.jsxs)(i.A, { maxHeight: 400, overflow: "auto", children: [(0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (0, D.jsx)(rt.A, { items: (null === S || void 0 === S ? void 0 : S.inTheme) || [], itemKey: "fieldKey", handleItemsReordered: _, ListComponent: i.A, emptyItem: "Empty", renderItem: (e, t) => { var n; let { fieldKey: r, fieldInfo: a, fieldStyle: o, hidden: i = !1 } = e; return !i && (0, D.jsx)(Ie, { inTheme: !0, checked: !0, index: t, fieldKey: r, fieldLabel: (null === a || void 0 === a || null === (n = a.labelOverride) || void 0 === n ? void 0 : n[u]) || a.label, fieldType: null === a || void 0 === a ? void 0 : a.type, font: o, moveField: _, open: r === T, handleFieldClick: W(r), handleChecked: U(r), handleFontChange: (e, t) => q({ fieldKey: r, prop: e, value: t }) }, r) } }) }), (0, D.jsx)(M, { component: "div", disablePadding: !0, children: null === S || void 0 === S || null === (a = S.notInTheme) || void 0 === a ? void 0 : a.filter((e => !e.hidden)).map((e => { var t; let { fieldKey: n, fieldInfo: r, fieldStyle: a } = e; return (0, D.jsx)(Ie, { inTheme: !1, handleChecked: U(n), checked: !1, fieldKey: n, fieldLabel: (null === r || void 0 === r || null === (t = r.labelOverride) || void 0 === t ? void 0 : t[u]) || r.label, font: a, open: n === T, handleFieldClick: null, handleFontChange: q }, n) })) })] }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), g && (0, D.jsx)(A.A, { variant: "body2", component: "a", align: "right", onClick: O, children: "Add More Fields\xa0>>" }), !d && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(z.A, { title: k ? "Change photo alignment to update fields alignment" : "", children: (0, D.jsx)(i.A, { my: 3, display: "flex", alignItems: "center", justifyContent: "space-between", children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", width: "100%", children: (0, D.jsx)(B, { label: "Field Alignment", fullwidth: !0, value: v.textAlign, onChange: async (e, t) => { null !== t && await F(L.ZO.update({ orgId: g, themeId: C.id, data: { ...C, base: { ...C.base, cards: { ...C.base.cards, textAlign: t } } } })) }, disabled: k, data: [{ label: (0, D.jsx)(I.Ay, { icon: "AlignLeft" }), value: "left" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignCenter" }), value: "center" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignRight" }), value: "right" }] }) }) }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(K, { label: "Font Family", value: v.fontFamily || "poppins", name: "autoStrategy", onChange: e => {
                                            (async e => { await F(L.ZO.update({ orgId: g, themeId: C.id, data: { ...C, base: { ...C.base, cards: { ...C.base.cards, fontFamily: e.target.value } } } })) })(e) }, children: at.map((e => (0, D.jsx)(x.A, { value: e.value, children: (0, D.jsx)(A.A, { style: { fontSize: 12, fontFamily: "".concat(e.value, ", ").concat(nt.n[e.value] || "sans-serif") }, children: e.displayName }) }))) }) })] })] }) }; var it = n(91693),
                    lt = n(68840); const st = e => { var t, n, r, a; let { innerRef: s, isOverrideMode: c = !1, overrideRoleType: d = "single", showVancantPhotoSettings: h = !0, showNoPhotoSettings: f = !0, showAbovePositionSetting: v = !0, showTitle: g = !1 } = e; const y = (0, m.d4)(p.P0),
                            b = c ? { ...null === y || void 0 === y || null === (t = y.roleTypeOverrides) || void 0 === t || null === (n = t[d]) || void 0 === n ? void 0 : n.photos } : { ...null === y || void 0 === y || null === (r = y.base) || void 0 === r ? void 0 : r.photos },
                            z = { ...b },
                            { orgId: x } = (0, u.useParams)(),
                            A = (0, m.wA)(),
                            k = (0, o.useRef)(),
                            { openDialog: S } = (0, j.A)("cropper"),
                            [M, E] = (0, o.useState)(null); let C = [{ label: "Left", value: "left" }, { label: "Right", value: "right" }, { label: "Center", value: "center" }, { label: "Top Left", value: "topLeft" }, { label: "Top Right", value: "topRight" }];
                        v && C.push({ label: "Above Left", value: "aboveLeft" }, { label: "Above Right", value: "aboveRight" }, { label: "Above", value: "above" }); const T = [{ label: "Circle", value: "circle", variant: "circular" }, { label: "Rounded", value: "oval", variant: "rounded" }, { label: "Square", value: "square", variant: "square" }, { label: "Fill", value: "fill", variant: "fill" }],
                            H = T.find((e => e.value === z.shape)); "fill" === (null === H || void 0 === H ? void 0 : H.value) && (C = C.filter((e => ["left", "right", "center"].includes(e.value)))); const I = e => c ? { ...y, roleTypeOverrides: { ...y.roleTypeOverrides, [d]: { ...y.roleTypeOverrides[d], photos: e } } } : { ...y, base: { ...y.base, photos: e } },
                            V = async e => { let t; switch (e.target.name) {
                                    case "standard":
                                    case "noPhoto":
                                    case "vacant":
                                        t = { ...b, [e.target.name]: { ...b[e.target.name], visible: e.target.checked } }; break;
                                    default:
                                        t = { ...b, [e.target.name]: e.target.checked } } await A(L.ZO.update({ orgId: x, themeId: y.id, data: I(t) })) }, O = e => { e && e.stopPropagation(), E("noPhoto"), k.current.click() }, F = e => async t => { t && t.stopPropagation(), await A(L.ZO.update({ orgId: x, themeId: y.id, data: I({ ...b, noPhoto: { ...b.noPhoto, imageAvatarId: e } }) })) }, N = [{ id: "initials", image: (0, lt.Or)("initials") }, { id: "avatar1", image: (0, lt.Or)("avatar1") }, { id: "avatar2", image: (0, lt.Or)("avatar2") }, { id: "avatar3", image: (0, lt.Or)("avatar3") }], _ = [{ id: "vacant1", image: (0, lt.Or)("vacant1") }, { id: "vacant2", image: (0, lt.Or)("vacant2") }, { id: "vacant3", image: (0, lt.Or)("vacant3") }, { id: "vacant4", image: (0, lt.Or)("vacant4") }], W = e => { e && e.stopPropagation(), E("vacant"), k.current.click() }, U = e => async t => { t && t.stopPropagation(), _.find((t => t.id === e)).isNew && (E("vacant"), k.current.click()), await A(L.ZO.update({ orgId: x, themeId: y.id, data: I({ ...b, vacant: { ...b.vacant, imageAvatarId: e } }) })) }, q = async (e, t) => { let n; switch (e) {
                                    case "position":
                                    case "shape":
                                        n = { ...b, [e]: t }; break;
                                    default:
                                        n = { ...b, standard: { ...b.standard, size: t }, noPhoto: { ...b.noPhoto, size: t }, vacant: { ...b.vacant, size: t } } } await A(L.ZO.update({ orgId: x, themeId: y.id, data: I(n) })) }, G = async e => { await A(L.ZO.update({ orgId: x, themeId: y.id, data: I({ ...b, [M]: { ...b[M], customImageUrl: e } }) })) }, Z = (null === H || void 0 === H ? void 0 : H.variant) || "circular"; return (0, D.jsxs)(i.A, { ref: s, mb: 4, children: [(0, D.jsx)("input", { type: "file", name: "fileUploader", ref: k, id: "fileUploader", style: { display: "none" }, accept: ".jpg,.JPG,.jpeg,.JPEG,.png,.PNG", onChange: e => { if (e.target.files && e.target.files.length > 0) { const t = new FileReader;
                                        t.addEventListener("load", (() => { S({ image: t.result, onUpload: G }) })), t.readAsDataURL(e.target.files[0]) } } }), g && (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingMD, color: P.Qs.Neutrals[900], children: "Photos" }) }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show Photos", checked: z.standard.visible, onChange: V, name: "standard" }) }), z.standard.visible && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Shape", data: T, value: z.shape, name: "shape", onChange: q }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Photo Position", value: z.position, name: "position", onChange: e => q(e.target.name, e.target.value), options: C }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Size", data: [{ label: "Small", value: 60 }, { label: "Medium", value: 80 }, { label: "Large", value: 100 }, { label: "X-Large", value: 120 }], value: z.standard.size, name: "size", onChange: q }) }), f && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show when no photo is available", checked: z.noPhoto.visible, onChange: V, name: "noPhoto" }) }), (null === z || void 0 === z || null === (a = z.noPhoto) || void 0 === a ? void 0 : a.visible) && (0, D.jsx)(i.A, { display: "flex", gap: 1, flexWrap: "wrap", children: N.map((e => (0, D.jsx)(it.A, { selected: e.id === z.noPhoto.imageAvatarId, id: e.id, image: "".concat(e.image), handleClick: F, handleEdit: O, customImageUrl: z.noPhoto.customImageUrl, isNew: e.isNew, variant: Z }))) })] }), h && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show photo for vacant roles", checked: z.vacant.visible, onChange: V, name: "vacant" }) }), z.vacant.visible && (0, D.jsx)(i.A, { gap: 1, display: "flex", flexWrap: "wrap", children: _.map((e => (0, D.jsx)(it.A, { selected: e.id === z.vacant.imageAvatarId, id: e.id, image: e.image, customImageUrl: z.vacant.customImageUrl, handleClick: U, handleEdit: W, isNew: e.isNew }))) })] })] })] }) },
                    ct = e => { let { innerRef: t, theme: n } = e; return (0, D.jsx)("div", { ref: t, children: (0, D.jsxs)(Ye, { roleType: "assistant", theme: n, children: [(0, D.jsx)(st, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "assistant" }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(ot, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "assistant" })] }) }) },
                    dt = [{ label: "Rounded", value: "oval" }, { label: "Square", value: "square" }],
                    ut = [{ label: "Thin", value: .5 }, { label: "Regular", value: 1 }, { label: "Thick", value: 2 }, { label: "Thicker", value: 3 }],
                    ht = e => { let { innerRef: t, theme: n } = e; const { base: { cards: r } } = n, a = (0, m.d4)(p.P0), { orgId: o } = (0, u.useParams)(), s = (0, m.wA)(), c = async function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const n = {
                                [e.target.name]: t ? e.target.value : e.target.checked };
                            await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, base: { ...a.base, cards: { ...a.base.cards, cardFrame: { ...a.base.cards.cardFrame, size: { ...a.base.cards.cardFrame.size, ...n } } } } } })) }; return (0, D.jsxs)(i.A, { ref: t, children: [(0, D.jsx)(Ce, { label: "Show Card Border", checked: r.cardFrame.visible, onChange: async e => { switch (e.target.name) {
                                        case "cardFrame":
                                            await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, base: { ...a.base, cards: { ...a.base.cards, cardFrame: { ...a.base.cards.cardFrame, visible: e.target.checked } } } } })); break;
                                        case "roleCounts":
                                            await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, orgStructure: { ...a.orgStructure, roleCounts: { ...a.orgStructure.roleCounts, visible: e.target.checked } } } })) } }, name: "cardFrame" }), r.cardFrame.visible && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Frame Thickness", data: ut, value: r.cardFrame.thickness, name: "thickness", onChange: async (e, t) => { await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, base: { ...a.base, cards: { ...a.base.cards, cardFrame: { ...a.base.cards.cardFrame, [e]: t } } } } })) } }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Card Shape", data: dt, value: r.cardFrame.shape, name: "primary.style", onChange: async (e, t) => { await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, base: { ...a.base, cards: { ...a.base.cards, cardFrame: { ...a.base.cards.cardFrame, shape: t } } } } })) } }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Card Frame Color", handleChangeBackground: async e => { await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, base: { ...a.base, cards: { ...a.base.cards, cardFrame: { ...a.base.cards.cardFrame, color: e } } } } })) }, color: r.cardFrame.color, showReset: !0 }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Card Size" }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Automatically fit content", checked: r.cardFrame.size.auto, onChange: e => c(e, !1), name: "auto" }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Qe, { label: "Card Width", type: "number", defaultValue: r.cardFrame.size.width, onBlur: e => c(e, !0), inputProps: { step: 10 }, name: "width" }) }), (0, D.jsx)(i.A, { my: 3, children: r.cardFrame.size.auto ? (0, D.jsx)(K, { label: "Auto Height Strategy", value: r.cardFrame.size.autoStrategy, name: "autoStrategy", onChange: e => c(e, !0), options: [{ value: "compact", label: "Best Fit" }, { value: "type", label: "Role types share heights" }, { value: "all", label: "All roles have same height" }] }) : (0, D.jsx)(Qe, { label: "Height", type: "number", defaultValue: r.cardFrame.size.height, name: "height", inputProps: { step: 10 }, onBlur: e => c(e, !0) }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Vacant Positions" }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Qe, { name: "vacantText", defaultValue: n.chartOptions.vacantText, label: "Show Text for Vacant Roles", fullWidth: !0, onBlur: e => async function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const n = {
                                            [e.target.name]: t ? e.target.value : e.target.checked };
                                        await s(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, chartOptions: { ...a.chartOptions, ...n } } })) }(e, !0) }) })] }) }; var mt = n(31287),
                    pt = n(72952),
                    ft = function(e) { return e.countByPeople = "countByPeople", e.inclDept = "inclDept", e.inclLocation = "inclLocation", e.inclUnassigned = "inclUnassigned", e.inclSharedMembers = "inclSharedMembers", e }(ft || {}); const vt = [{ id: ft.countByPeople, label: "Count by unique people" }, { id: ft.inclDept, label: "Include departments" }, { id: ft.inclLocation, label: "Include locations" }, { id: ft.inclUnassigned, label: "Include vacant positions" }, { id: ft.inclSharedMembers, label: "Include all people in shared roles" }],
                    gt = e => { let { innerRef: t, theme: n } = e; const r = (0, m.wA)(),
                            { orgId: a } = (0, u.useParams)(),
                            o = (null === n || void 0 === n ? void 0 : n.orgStructure) || {},
                            s = (null === o || void 0 === o ? void 0 : o.roleCounts) || {},
                            c = async e => { const t = e.target.name,
                                    i = e.target.checked;
                                await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, orgStructure: { ...o, roleCounts: { ...o.roleCounts, rules: { ...o.roleCounts.rules, [t]: i } } } } })) }; return (0, D.jsxs)("div", { ref: t, children: [(0, D.jsx)(Ce, { label: "Show counts on cards", checked: s.visible, onChange: async e => { await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, orgStructure: { ...n.orgStructure, roleCounts: { ...n.orgStructure.roleCounts, visible: e.target.checked } } } })) }, name: "roleCounts" }), vt.map((e => { var t; return (0, D.jsx)(i.A, { mt: 1, children: (0, D.jsxs)(i.A, { display: "flex", alignItems: "center", children: [(0, D.jsx)(ve.A, { size: "small", disableRipple: !0, onChange: c, name: e.id, checked: null === s || void 0 === s ? void 0 : s.rules[e.id], disabled: !s.visible || e.id !== ft.countByPeople && (null === s || void 0 === s || null === (t = s.rules) || void 0 === t ? void 0 : t.countByPeople) }), (0, D.jsx)(mt.A, { children: (0, D.jsx)(l.A, { variant: "bodySM", color: P.Qs.Neutrals[700], children: e.label }) })] }) }, e.id) })), (0, D.jsxs)(i.A, { my: 2, children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { disabled: !s.visible, label: "Position of counts", value: s.position, name: "countPosition", onChange: async e => { await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, orgStructure: { ...n.orgStructure, roleCounts: { ...n.orgStructure.roleCounts, position: e.target.value } } } })) }, options: [{ value: pt.d5.below, label: "Below card" }, { value: pt.d5.insideLeft, label: "Inside card left" }, { value: "insideRight", label: "Inside card right" }] }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { disabled: !s.visible, label: "Expand / collapse button style", value: s.expandStyle, name: "countPosition", onChange: async e => { await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, orgStructure: { ...n.orgStructure, roleCounts: { ...n.orgStructure.roleCounts, expandStyle: e.target.value } } } })) }, options: [{ value: pt.$.Visible, label: "Always show" }, { value: pt.$.Hover, label: "Show on card hover" }] }) })] })] }) },
                    yt = [{ value: "card", label: "Card" }, { value: "label", label: "Label" }],
                    bt = e => { var t; let { innerRef: n, theme: r } = e; const a = (0, m.wA)(),
                            { orgId: o } = (0, u.useParams)(),
                            i = (null === r || void 0 === r || null === (t = r.roleTypeOverrides) || void 0 === t ? void 0 : t.department) || {}; return (0, D.jsx)("div", { ref: n, children: (0, D.jsxs)(Ye, { roleType: "department", theme: r, children: [(0, D.jsx)(W.A, { my: 4, children: (0, D.jsx)(B, { label: "Display Style", data: yt, value: i.displayType, name: "displayType", onChange: async (e, t) => { await a(L.ZO.update({ orgId: o, themeId: r.id, data: { ...r, roleTypeOverrides: { ...r.roleTypeOverrides, department: { ...(r.roleTypeOverrides || {}).department, displayType: t } } } })) } }) }), (0, D.jsx)(W.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Show borders around departments", checked: null === i || void 0 === i ? void 0 : i.borderVisible, onChange: async e => { await a(L.ZO.update({ orgId: o, themeId: r.id, data: { ...r, roleTypeOverrides: { ...r.roleTypeOverrides, department: { ...(r.roleTypeOverrides || {}).department, borderVisible: e.target.checked } } } })) }, name: "department" }) }), (null === i || void 0 === i ? void 0 : i.borderVisible) && (0, D.jsx)(W.A, { my: 3, children: (0, D.jsx)(q, { label: "Department Border Color", handleChangeBackground: async e => { await a(L.ZO.update({ orgId: o, themeId: r.id, data: { ...r, roleTypeOverrides: { ...r.roleTypeOverrides, department: { ...(r.roleTypeOverrides || {}).department, borderColor: e } } } })) }, color: null === i || void 0 === i ? void 0 : i.borderColor, showReset: !1 }) }), (0, D.jsx)(ot, { showTitle: !0, theme: r, isOverrideMode: !0, overrideRoleType: "department" }), (0, D.jsx)(W.A, { mt: 3, children: (0, D.jsx)(B, { label: "Field Alignment", fullwidth: !0, value: i.textAlign || "center", onChange: async (e, t) => { null !== t && await a(L.ZO.update({ orgId: o, themeId: r.id, data: { ...r, roleTypeOverrides: { ...r.roleTypeOverrides, department: { ...(r.roleTypeOverrides || {}).department, textAlign: t } } } })) }, data: [{ label: (0, D.jsx)(I.Ay, { icon: "AlignLeft" }), value: "left" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignCenter" }), value: "center" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignRight" }), value: "right" }] }) })] }) }) },
                    wt = e => { let { innerRef: t, theme: n } = e; const { chartOptions: { legend: r } } = n, a = (0, m.d4)(p.P0), { orgId: o } = (0, u.useParams)(), l = (0, m.wA)(); return (0, D.jsxs)("div", { ref: t, children: [(0, D.jsx)(i.A, { children: (0, D.jsx)(Qe, { label: "Legend Title", defaultValue: r.title, onBlur: e => async function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const n = {
                                            [e.target.name]: t ? e.target.value : e.target.checked };
                                        await l(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, chartOptions: { ...a.chartOptions, legend: { ...a.chartOptions.legend, ...n } } } })) }(e, !0), inputProps: { step: 10 }, name: "title" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Badge Shape", data: [{ label: "Square", value: "square" }, { label: "Circle", value: "circle" }], value: r.badgeShape, name: "badgeShape", onChange: async (e, t) => { await l(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, chartOptions: { ...a.chartOptions, legend: { ...a.chartOptions.legend, [e]: t } } } })) } }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Badge Bar Position", value: r.badgeBarPosition, name: "badgeBarPosition", onChange: async e => { await l(L.ZO.update({ orgId: o, themeId: a.id, data: { ...a, chartOptions: { ...a.chartOptions, legend: { ...a.chartOptions.legend, [e.target.name]: e.target.value } } } })) }, options: [{ value: "top", label: "Top" }, { value: "left", label: "Left" }, { value: "hidden", label: "Hidden" }] }) })] }) },
                    zt = e => { var t, n, r, a, o, s; let { innerRef: c, theme: d } = e; const { base: { lines: h } } = d, { orgId: p } = (0, u.useParams)(), f = [{ label: "Thin", value: .5 }, { label: "Regular", value: 1 }, { label: "Thick", value: 2 }, { label: "Thicker", value: 3 }], v = (0, m.wA)(), g = async e => { await v(L.ZO.update({ orgId: p, themeId: d.id, data: { ...d, base: { ...d.base, lines: { ...d.base.lines, [e.target.name]: { ...d.base.lines[e.target.name], visible: e.target.checked } } } } })) }, y = async (e, t) => { let n, r, a; switch (e) {
                                case "primary.style":
                                    n = { ...d.base.lines.primary, style: t }; break;
                                case "primary.thickness":
                                    n = { ...d.base.lines.primary, thickness: t }; break;
                                case "secondary.style":
                                    r = { ...d.base.lines.secondary, style: t }; break;
                                case "secondary.thickness":
                                    r = { ...d.base.lines.secondary, thickness: t } } n ? a = { ...d.base.lines, primary: n } : r && (a = { ...d.base.lines, secondary: r }), await v(L.ZO.update({ orgId: p, themeId: d.id, data: { ...d, base: { ...d.base, lines: a } } })) }; return (0, D.jsxs)("div", { ref: c, children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Show Connecting Lines", checked: null === h || void 0 === h || null === (t = h.primary) || void 0 === t ? void 0 : t.visible, onChange: g, name: "primary" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Line Style", data: [{ label: "Dotted", value: "dotted" }, { label: "Dashed", value: "dashed" }, { label: "Solid", value: "solid" }], value: null === h || void 0 === h || null === (n = h.primary) || void 0 === n ? void 0 : n.style, name: "primary.style", onChange: y }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Line Thickness", data: f, value: null === h || void 0 === h || null === (r = h.primary) || void 0 === r ? void 0 : r.thickness, name: "primary.thickness", onChange: y }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Line Color", handleChangeBackground: async e => { await v(L.ZO.update({ orgId: p, themeId: d.id, data: { ...d, base: { ...d.base, lines: { ...d.base.lines, primary: { ...d.base.lines.primary, color: e } } } } })) }, color: null === h || void 0 === h || null === (a = h.primary) || void 0 === a ? void 0 : a.color, showReset: !1 }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Secondary Roles (Dotted)" }), (0, D.jsx)(i.A, { mb: 2, mt: 3, children: (0, D.jsx)(Ce, { label: "Show Connecting Lines", checked: null === h || void 0 === h || null === (o = h.secondary) || void 0 === o ? void 0 : o.visible, onChange: g, name: "secondary" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Line Thickness", data: f, value: null === h || void 0 === h || null === (s = h.secondary) || void 0 === s ? void 0 : s.thickness, name: "secondary.thickness", onChange: y }) })] }) },
                    xt = e => { let { innerRef: t, theme: n } = e; return (0, D.jsx)("div", { ref: t, children: (0, D.jsxs)(Ye, { roleType: "location", theme: n, children: [(0, D.jsx)(st, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "location", showVancantPhotoSettings: !1, showNoPhotoSettings: !1 }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(ot, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "location" })] }) }) };
                n(86852), n(96942); var At, kt, St = n(8924),
                    Mt = n(2173),
                    Et = n(24115),
                    Ct = n(53109),
                    Tt = n(86597),
                    Ht = n(43331),
                    Lt = n(48853); const It = (0, re.Ay)(i.A)(At || (At = (0, a.A)(["\n  border: solid 1px ", ";\n  border-radius: 8px;\n  &.hoverVisible {\n    display: none;\n  }\n  ", "\n"])), P.Ay.secondaryGrey.light, (e => { let { $active: t } = e; return "\n    ".concat(t ? "\n      border-color: ".concat(P.Ay.primary.main, ";\n      color: ").concat(P.Ay.primary.main, ";\n    ") : "\n      border-color: ".concat(P.Ay.secondaryGrey.light, ";\n      color: ").concat(P.Ay.secondaryGrey.main, ";\n    "), "\n  ") })),
                    jt = (0, re.Ay)(i.A)(kt || (kt = (0, a.A)(["\n  border: solid 1px ", ";\n  border-radius: 8px;\n  height: 32px;\n  flex: 1;\n  ", "\n\n  &:hover .hoverVisible {\n    display: inline-block;\n  }\n"])), P.Ay.secondaryGrey.light, (e => { let { $active: t } = e; return "\n    ".concat(t ? "border-color: ".concat(P.Ay.primary.main, ";") : "", " \n  ") })),
                    Vt = e => { var t; let { dashboardMode: n } = e; const r = (0, m.wA)(),
                            a = (0, m.d4)(St.hO),
                            d = (0, m.d4)(St.EK),
                            u = (0, m.d4)(St.Jk),
                            { openDialog: h } = (0, j.A)("applyTheme"),
                            { confirmAction: p } = (0, Ke.A)(),
                            f = (0, Tt.A)(),
                            g = null === f || void 0 === f ? void 0 : f.id,
                            y = (0, m.d4)($e.Mk),
                            b = (0, m.d4)(St.SQ),
                            w = (0, m.d4)(St.PS),
                            [x, A] = (0, o.useState)(!1),
                            { userHasMinAccess: k } = (0, Lt.A)(),
                            S = k(Ae.td.ADMIN),
                            M = !(null === y || void 0 === y || null === (t = y.alias) || void 0 === t || !t.rootChartId);
                        (0, o.useEffect)((() => {!w && n ? (A(!0), (async e => { await Promise.all([r(Ht.UY.get({ orgId: e })), r(Ht.UY.getCharts({ orgId: e }))]), A(!1) })(g)) : A(!1) }), [g]); const { handleEditCurrentTheme: E, handleSetActiveTheme: C, handleDuplicateCurrentTheme: T } = (0, v.A)({ sample: n }), [H, V] = (0, Mt.A)((async e => { n ? p({ execFunc: async () => { await r(L.ZO.setActive({ orgId: g, themeId: e, mode: L.cD.makeDefault })) }, title: "Make Default", message: "This will make this theme the default theme for this organization. Are you sure?" }) : h({ orgId: g, themeId: e, charts: b(e), chartId: null === y || void 0 === y ? void 0 : y.id }) })), [O, F] = (0, Mt.A)((async e => { n ? r((0, Ct.Kj)(e)) : await C(e, L.cD.applyToChart) })), N = x || H || O; return (0, D.jsx)(Et.A, { loading: N, transparent: !0, children: (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", gap: 2, overflow: "auto", children: [(0, D.jsxs)(i.A, { px: 1, display: "flex", flexDirection: "column", gap: 2, children: [(0, D.jsx)(l.A, { variant: R.Eq.h4, component: "h4", children: "Themes" }), (0, D.jsx)(l.A, { variant: R.Eq.bodySM, align: "left", color: P.Qs.Neutrals[800], children: "To change the appearance of your charts and directories, use a preset theme or create a custom theme." })] }), (0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", gap: 2, alignItems: "flex-end", px: 1, children: [(0, D.jsx)(l.A, { variant: R.Eq.subheadingMD, component: "h6", children: "My Custom Themes" }), (0, D.jsxs)(c.A, { size: "small", color: "primary", variant: "contained", onClick: () => T(), "data-tour-anchor": "new-theme-btn", disabled: M, children: [(0, D.jsx)(I.gF, { icon: "Add" }), " New"] })] }), (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", gap: 1, maxHeight: 300, overflow: "auto", px: 1, children: [0 === ((null === a || void 0 === a ? void 0 : a.customThemes) || []).length && (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[500], children: "No custom themes created yet" }), ((null === a || void 0 === a ? void 0 : a.customThemes) || []).map((e => { let { name: t, id: a } = e; return (0, D.jsxs)(i.A, { display: "flex", gap: 1, justifyContent: "space-between", alignItems: "center", style: { cursor: M && a !== d ? "not-allowed" : "initial" }, children: [(0, D.jsx)(z.A, { title: M ? "Change themes from the main chart" : "", children: (0, D.jsxs)(jt, { $active: a === d, display: "flex", justifyContent: "space-between", alignItems: "center", px: 1, onClick: () => !M && F(a), bgcolor: M && a !== d ? P.Qs.Neutrals[200] : "inherit", children: [(0, D.jsx)(l.A, { variant: R.Eq.caption, color: M && a !== d ? P.Qs.Neutrals[600] : "secondaryGrey", component: "a", children: t }), u === a && (0, D.jsx)(It, { $active: a === d, component: "span", px: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.caption, color: "secondaryGrey", component: "a", children: "Default" }) }), !M && u !== a && S && a && (0, D.jsx)(It, { className: "hoverVisible", component: "span", px: 1, onClick: e => { e.stopPropagation(), V(a) }, children: (0, D.jsx)(l.A, { variant: R.Eq.caption, color: "secondaryGrey", component: "a", children: "Set as default" }) })] }) }), S && (0, D.jsx)(s.A, { disabled: M && a !== d, size: "small", onClick: () => (async e => { e && (n || d === e ? n && r((0, Ct.Kj)(e)) : await C(e, L.cD.applyToChart), E(null)) })(a), children: (0, D.jsx)(I.gF, { icon: "Pencil", size: "sm" }) })] }, "default-theme-".concat(a, "-").concat(t)) }))] }), (0, D.jsx)(i.A, { display: "flex", gap: 2, px: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingMD, component: "h6", children: "Organimi Themes" }) }), (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", gap: 1, maxHeight: 300, overflow: "auto", px: 1, children: a.defaultThemes.map((e => { let { name: t, id: n } = e; return (0, D.jsxs)(i.A, { display: "flex", gap: 1, justifyContent: "space-between", alignItems: "center", style: { cursor: M && n !== d ? "not-allowed" : "initial" }, children: [(0, D.jsx)(z.A, { title: M ? "Change themes from the main chart" : "", children: (0, D.jsxs)(jt, { $active: n === d, px: 1, display: "flex", justifyContent: "space-between", alignItems: "center", onClick: () => !M && F(n), bgcolor: M && n !== d ? P.Qs.Neutrals[200] : "inherit", children: [(0, D.jsx)(l.A, { variant: R.Eq.caption, color: M && n !== d ? P.Qs.Neutrals[600] : "secondaryGrey", component: "a", children: t }), !M && u === n && (0, D.jsx)(It, { $active: n === d, px: 1, component: "a", children: (0, D.jsx)(l.A, { variant: R.Eq.caption, color: "secondaryGrey", component: "span", children: "Default" }) }), !M && u !== n && (0, D.jsx)(It, { className: "hoverVisible", component: "span", px: 1, onClick: e => { e.stopPropagation(), n && V(n) }, children: (0, D.jsx)(l.A, { variant: R.Eq.caption, color: "secondaryGrey", component: "a", children: "Set as default" }) })] }) }), S && (0, D.jsx)(s.A, { disabled: M, onClick: () => T(n), size: "small", children: (0, D.jsx)(I.gF, { icon: "Copy", color: P.Ay.secondaryGrey.main, size: "sm" }) })] }, "default-theme-".concat(n)) })) })] }) }) },
                    Ot = e => { let { innerRef: t, theme: n } = e; return (0, D.jsx)("div", { ref: t, children: (0, D.jsxs)(Ye, { roleType: "shared", theme: n, children: [(0, D.jsx)(st, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "shared", showAbovePositionSetting: !1 }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(ot, { showTitle: !0, theme: n, isOverrideMode: !0, overrideRoleType: "shared" })] }) }) }; var Rt = n(9579); const Pt = e => { let { innerRef: t, theme: n } = e; const r = (0, m.wA)(),
                            { orgId: a } = (0, u.useParams)(),
                            o = (null === n || void 0 === n ? void 0 : n.chartOptions) || {},
                            s = async function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const i = {
                                    [e.target.name]: t ? e.target.value : e.target.checked };
                                await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, chartOptions: { ...o, ...i } } })) }, c = async e => { const t = e.target.name,
                                    i = "orderAutomatically" === t ? e.target.checked : e.target.value;
                                await r(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, chartOptions: { ...o, [t]: i } } })) }; return (0, D.jsxs)("div", { ref: t, children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Automatically sort roles", checked: null === o || void 0 === o ? void 0 : o.orderAutomatically, onChange: c, name: "orderAutomatically" }) }), (null === o || void 0 === o ? void 0 : o.orderAutomatically) && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Order roles by", value: null === o || void 0 === o ? void 0 : o.orderBy, name: "orderBy", onChange: c, options: [{ value: "firstName", label: "First Name" }, { value: "lastName", label: "Last Name" }, { value: "name", label: "Role Title" }, { value: "department", label: "Size of department" }] }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: (0, D.jsxs)(i.A, { display: "flex", alignItems: "center", children: [(0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: "Stack roles whenever possible" }), (0, D.jsx)(Rt.Ay, { placement: "top", title: "(Compact Mode) When turned off, stack settings will not be applied if a sibling position has reporting roles", arrow: !0, children: (0, D.jsx)("div", { style: { marginLeft: "4px" }, children: (0, D.jsx)(I.Ay, { icon: "HelpSolid", size: "sm" }) }) })] }), checked: n.chartOptions.compactMode, onChange: e => s(e, !1), name: "compactMode" }) }), (0, D.jsxs)(i.A, { my: 3, children: [(0, D.jsxs)(i.A, { display: "flex", justifyContent: "flex-start", alignItems: "end", children: [(0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, children: "Lowest level display direction" }), (0, D.jsx)(Rt.Ay, { placement: "top", title: "Note: You cannot stack role cards with direct reports.", arrow: !0, children: (0, D.jsx)("div", { style: { marginLeft: "4px" }, children: (0, D.jsx)(I.Ay, { icon: "HelpSolid", size: "sm" }) }) })] }), (0, D.jsx)(K, { name: "stackDirection", value: n.chartOptions.stackDirection, onChange: e => s(e, !0), options: [{ value: "horizontal", label: "Horizontal" }, { value: "vertical", label: "Vertical" }, { value: "grid", label: "As a Grid" }, { value: "cluster", label: "As a Cluster" }] })] }), ["grid", "cluster"].includes(n.chartOptions.stackDirection) && (0, D.jsx)(i.A, { my: 3, children: (0, D.jsxs)(Ve.A, { container: !0, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(Ve.A, { item: !0, xs: 5, children: (0, D.jsx)(G.A, { label: "Columns", type: "number", InputProps: { inputProps: { min: 1 } }, defaultValue: n.chartOptions.stackColumns, name: "stackColumns", onBlur: e => s(e, !0) }) }), (0, D.jsx)(Ve.A, { item: !0, xs: 2, children: (0, D.jsx)(A.A, { variant: "body1", align: "center", children: "X" }) }), (0, D.jsx)(Ve.A, { item: !0, xs: 5, children: (0, D.jsx)(G.A, { label: "Rows", type: "number", InputProps: { inputProps: { min: 1 } }, defaultValue: n.chartOptions.stackRows, name: "stackRows", onBlur: e => s(e, !0) }) })] }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(K, { name: "stackDirectionShared", label: "Shared role display direction", value: n.chartOptions.stackDirectionShared, onChange: e => s(e, !0), options: [{ value: "condensed", label: "Condensed List" }, { value: "horizontal", label: "Horizontal" }, { value: "vertical", label: "Vertical" }, { value: "grid", label: "As a Grid" }] }) }), "grid" === n.chartOptions.stackDirectionShared && (0, D.jsx)(i.A, { my: 3, children: (0, D.jsxs)(Ve.A, { container: !0, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(Ve.A, { item: !0, xs: 5, children: (0, D.jsx)(G.A, { label: "Columns", type: "number", InputProps: { inputProps: { min: 1 } }, defaultValue: n.chartOptions.stackColumnsShared, name: "stackColumnsShared", onBlur: e => s(e, !0) }) }), (0, D.jsx)(Ve.A, { item: !0, xs: 2, children: (0, D.jsx)(A.A, { variant: "body1", align: "center", children: "X" }) }), (0, D.jsx)(Ve.A, { item: !0, xs: 5, children: (0, D.jsx)(G.A, { label: "Rows", type: "number", InputProps: { inputProps: { min: 1 } }, defaultValue: n.chartOptions.stackRowsShared, name: "stackRowsShared", onBlur: e => s(e, !0) }) })] }) })] }) },
                    Dt = [...nt.T],
                    Ft = e => { let { innerRef: t, theme: n } = e; const { base: { table: r } } = n, { orgId: a } = (0, u.useParams)(), o = (0, m.wA)(), s = r && Object.keys(r).reduce(((e, t) => { switch (t) {
                                case "weight":
                                    r[t] > 400 && e.push("bold"); break;
                                case "italic":
                                    r[t] && e.push("italic"); break;
                                case "underline":
                                    r[t] && e.push("underline"); break;
                                case "strikethrough":
                                    r[t] && e.push("strikethrough") } return e }), []), c = async (e, t) => { let r = {
                                [e]: t };
                            await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, table: { ...n.base.table, ...r } } } })) }; return (0, D.jsxs)("div", { ref: t, children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show Header", checked: null === r || void 0 === r ? void 0 : r.showHeader, onChange: () => c("showHeader", !(null !== r && void 0 !== r && r.showHeader)), name: "tableHeader" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Dense Padding", checked: null === r || void 0 === r ? void 0 : r.densePadding, onChange: () => c("densePadding", !(null !== r && void 0 !== r && r.densePadding)), name: "densePadding" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Show Badges", checked: null === r || void 0 === r ? void 0 : r.showLegend, onChange: () => c("showLegend", !(null !== r && void 0 !== r && r.showLegend)), name: "showLegend" }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Header Display" }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Font Color", color: (null === r || void 0 === r ? void 0 : r.color) || "#151515", handleChangeBackground: e => c("color", e) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Background Color", color: (null === r || void 0 === r ? void 0 : r.headerBackgroundColor) || "#FFFFFF", handleChangeBackground: e => c("headerBackgroundColor", e) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: null === r || void 0 === r ? void 0 : r.size, color: "primary", onChange: e => c("size", e.target.value), singleLine: !0, labelFlex: 2, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(i.A, { my: 2, display: "flex", gap: 1, justifyContent: "space-between", children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: s, onChange: async (e, t) => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, table: { ...n.base.table, weight: t.includes("bold") ? 700 : 400, italic: !!t.includes("italic"), underline: !!t.includes("underline"), strikethrough: !!t.includes("strikethrough") } } } })) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Case", name: "case", singleLine: !0, value: (null === r || void 0 === r ? void 0 : r.case) || "none", onChange: async (e, t) => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, table: { ...n.base.table, case: t } } } })) }, data: He.ML }) }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Content Display" }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font Family", value: (null === r || void 0 === r ? void 0 : r.fontFamily) || "poppins", name: "fontFamily", singleLine: !0, labelFlex: 2, onChange: e => c("fontFamily", e.target.value), children: Dt.map((e => (0, D.jsx)(x.A, { value: e.value, children: (0, D.jsx)(l.A, { variant: "inherit", color: "inherit", fontFamily: e.value, children: e.displayName }) }))) }) }), (0, D.jsx)(i.A, { my: 3, display: "flex", alignItems: "center", justifyContent: "space-between", flex: 1, children: (0, D.jsx)(B, { label: "Align", fullwidth: !0, value: null === r || void 0 === r ? void 0 : r.align, onChange: async (e, t) => { null !== t && await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, table: { ...n.base.table, align: t } } } })) }, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "AlignLeft" }), value: "left" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignCenter" }), value: "center" }, { label: (0, D.jsx)(I.Ay, { icon: "AlignRight" }), value: "right" }] }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(q, { label: "Cell Background Color", color: (null === r || void 0 === r ? void 0 : r.cellBackgroundColor) || "#FFFFFF", handleChangeBackground: e => c("cellBackgroundColor", e) }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Alternate Cell Background", checked: null === r || void 0 === r ? void 0 : r.alternateCellBackground, onChange: () => c("alternateCellBackground", !(null !== r && void 0 !== r && r.alternateCellBackground)), name: "alternateCellBackground" }) }), (null === r || void 0 === r ? void 0 : r.alternateCellBackground) && (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(q, { label: "Alternate Cell Background Color", color: (null === r || void 0 === r ? void 0 : r.alternateCellBackgroundColor) || "#FFFFFF", handleChangeBackground: e => c("alternateCellBackgroundColor", e) }) })] }) }; var Nt = n(7743),
                    _t = n(65025); const Bt = e => { var t, n, r, a, o; let { innerRef: s, theme: c } = e; const { data: { directory: d }, base: { table: h } } = c, p = (0, m.wA)(), { orgId: f } = (0, u.useParams)(), v = [{ name: "", model: "member", label: "None", attr: "", order: "asc" }, { name: "", model: "role", label: "Department Role", attr: "department", isDefault: !0, order: "asc" }, { name: "", model: "role", label: "Location Role", attr: "location", isDefault: !0, order: "asc" }, ...(0, m.d4)(Nt.kA).map((e => ({ ...e, attr: e.id, order: "asc" })))], g = (0, m.d4)(Nt.Xi), { handleGroupByChange: y } = (0, _t.A)(), b = async (e, t) => { y(null), t && (e = !1), await p(L.ZO.update({ orgId: f, themeId: c.id, data: { ...c, base: { ...c.base, table: { ...c.base.table, includeVacantPositions: e, showPeople: t } } } })) }; return (0, D.jsxs)("div", { ref: s, children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(K, { defaultValue: (null === d || void 0 === d || null === (t = d.groupBy) || void 0 === t ? void 0 : t.label) || "None", name: "groupBy", label: "Default Group By", singleLine: !0, labelFlex: 2, children: g.map(((e, t) => (0, D.jsx)(x.A, { value: e.label, onClick: t => (async (e, t) => { "None" === t.label && (t = null), y(t), await p(L.ZO.update({ orgId: f, themeId: c.id, data: { ...c, data: { ...c.data, directory: { ...c.data.directory, groupBy: t } } } })) })(0, e), children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: e.label }) }, t))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { defaultValue: (null === d || void 0 === d || null === (n = d.orderBy) || void 0 === n ? void 0 : n.label) || "None", name: "orderBy", label: "Default Sort By", singleLine: !0, labelFlex: 2, children: v.map(((e, t) => (0, D.jsx)(x.A, { value: e.label, onClick: t => (async (e, t) => { "None" === t.label && (t = null), await p(L.ZO.update({ orgId: f, themeId: c.id, data: { ...c, data: { ...c.data, directory: { ...c.data.directory, orderBy: t } } } })) })(0, e), children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: e.label }) }, t))) }) }), (null === d || void 0 === d || null === (r = d.orderBy) || void 0 === r ? void 0 : r.label) && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsxs)(K, { label: "Default Sort Order", defaultValue: null === d || void 0 === d || null === (a = d.orderBy) || void 0 === a ? void 0 : a.order, value: null === d || void 0 === d || null === (o = d.orderBy) || void 0 === o ? void 0 : o.order, name: "sortOrder", singleLine: !0, labelFlex: 2, onChange: e => {
                                        (async e => { let t = { ...d.orderBy, order: e.target.value };
                                            await p(L.ZO.update({ orgId: f, themeId: c.id, data: { ...c, data: { ...c.data, directory: { ...c.data.directory, orderBy: t } } } })) })(e) }, children: [(0, D.jsx)(x.A, { value: "asc", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: "Ascending" }) }, "asc"), (0, D.jsx)(x.A, { value: "desc", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: "Descending" }) }, "desc")] }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Include Vacant Positions", checked: null === h || void 0 === h ? void 0 : h.includeVacantPositions, onChange: () => b(!(null !== h && void 0 !== h && h.includeVacantPositions), null === h || void 0 === h ? void 0 : h.showPeople), name: "includeVacantPositions", disabled: null === h || void 0 === h ? void 0 : h.showPeople }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Unique By People", checked: null === h || void 0 === h ? void 0 : h.showPeople, onChange: () => b(null === h || void 0 === h ? void 0 : h.includeVacantPositions, !(null !== h && void 0 !== h && h.showPeople)), name: "showPeople" }) })] }) },
                    Wt = e => { var t, n; let { innerRef: r, theme: a, overrideRoleType: l = "single" } = e; const { base: { content: s } } = a, c = (0, m.d4)(f.V1), d = (0, m.d4)(f.IV)(l), u = (0, m.d4)(p.P0), [h, v] = (0, o.useState)(), g = (0, m.wA)(), y = e => ({ ...u, data: { ...u.data, directory: { ...u.data.directory, columns: e } } }), b = (0, o.useCallback)(((e, t) => { const n = d.inTheme[e];
                            (async e => { const t = e.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle })));
                                await g(L.ZO.update({ orgId: c, themeId: u.id, data: y(t) })) })(H()(d.inTheme, { $splice: [
                                    [e, 1],
                                    [t, 0, n]
                                ] })) }), [d.inTheme]), w = e => () => { v(e === h ? null : e) }, z = e => async t => { let n = {}; if (t.target.checked) { const t = d.notInTheme.find((t => t.fieldKey === e && !t.hidden)),
                                    r = d.notInTheme.filter((e => e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model && !e.hidden));
                                n = d.inTheme.filter((e => !e.hidden)).map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))), r.forEach((e => { n.push({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: null === e || void 0 === e ? void 0 : e.fieldStyle }) })) } else { const t = d.inTheme.find((t => t.fieldKey === e && !t.hidden));
                                n = d.inTheme.filter((e => !(e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model && !e.hidden))).map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))) } await g(L.ZO.update({ orgId: c, themeId: u.id, data: y(n) })) }, x = async e => { const t = d.inTheme.find((t => t.fieldKey === e.fieldKey)); let n = []; switch (e.prop) {
                                case "color":
                                case "align":
                                case "size":
                                case "case":
                                case "dateFormat":
                                    n = d.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, [e.prop]: e.value } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })); break;
                                case "format":
                                    n = d.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, weight: e.value.includes("bold") ? 700 : 400, italic: !!e.value.includes("italic"), underline: !!e.value.includes("underline"), strikethrough: !!e.value.includes("strikethrough") } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })) } await g(L.ZO.update({ orgId: c, themeId: u.id, data: y(n) })) }, A = async (e, t) => { let n = {
                                [e]: t };
                            await g(L.ZO.update({ orgId: c, themeId: u.id, data: { ...u, base: { ...u.base, content: { ...u.base.content, ...n } } } })) }; return (0, D.jsxs)("div", { ref: r, children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Apply Common Style", checked: null === s || void 0 === s ? void 0 : s.commonStyle, onChange: () => A("commonStyle", !(null !== s && void 0 !== s && s.commonStyle)), name: "commonStyle" }) }), (null === s || void 0 === s ? void 0 : s.commonStyle) && (0, D.jsx)(Ge, { content: s, handleContentSettings: A, handleContentFormatSettings: async e => { await g(L.ZO.update({ orgId: c, themeId: u.id, data: { ...u, base: { ...u.base, content: { ...u.base.content, weight: e.includes("bold") ? 700 : 400, italic: !!e.includes("italic"), underline: !!e.includes("underline"), strikethrough: !!e.includes("strikethrough") } } } })) } }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === d || void 0 === d || null === (t = d.inTheme) || void 0 === t ? void 0 : t.length) > 0 && (0, D.jsx)(rt.A, { items: (null === d || void 0 === d ? void 0 : d.inTheme) || [], itemKey: "fieldKey", handleItemsReordered: b, ListComponent: i.A, emptyItem: "Empty", renderItem: (e, t) => { var n; let { fieldKey: r, fieldInfo: a, fieldStyle: o, hidden: i = !1 } = e; return !i && (0, D.jsx)(je, { inTheme: !0, content: s, checked: !0, index: t, fieldKey: r, fieldLabel: (null === a || void 0 === a || null === (n = a.labelOverride) || void 0 === n ? void 0 : n[l]) || a.label, fieldType: null === a || void 0 === a ? void 0 : a.type, font: o, moveField: b, open: r === h && !(null !== s && void 0 !== s && s.commonStyle), handleFieldClick: w(r), handleChecked: z(r), handleFontChange: (e, t) => x({ fieldKey: r, prop: e, value: t }) }, r) } }) }), (0, D.jsx)(M, { component: "div", disablePadding: !0, children: null === d || void 0 === d || null === (n = d.notInTheme) || void 0 === n ? void 0 : n.filter((e => !e.hidden)).map(((e, t) => { var n; let { fieldKey: r, fieldInfo: a, fieldStyle: o } = e; return (0, D.jsx)(je, { inTheme: !1, content: s, handleChecked: z(r), checked: !1, fieldKey: r, fieldLabel: (null === a || void 0 === a || null === (n = a.labelOverride) || void 0 === n ? void 0 : n[l]) || a.label, font: o, open: r === h, handleFieldClick: null, handleFontChange: () => x() }, "".concat(r, "_").concat(t)) })) })] }) },
                    Ut = e => { var t, n, r, a, l, s, c, d, h, f; let { innerRef: v, isOverrideMode: g = !1, overrideRoleType: y = "single", showVancantPhotoSettings: b = !0, showNoPhotoSettings: z = !0 } = e; const x = (0, m.d4)(p.P0),
                            A = g ? { ...null === x || void 0 === x || null === (t = x.roleTypeOverrides) || void 0 === t || null === (n = t[y]) || void 0 === n ? void 0 : n.photos } : { ...null === x || void 0 === x || null === (r = x.base) || void 0 === r ? void 0 : r.directoryPhotos },
                            k = { ...A },
                            { orgId: S } = (0, u.useParams)(),
                            M = (0, m.wA)(),
                            E = (0, o.useRef)(),
                            { openDialog: C } = (0, j.A)("cropper"),
                            [T, H] = (0, o.useState)(null),
                            I = e => g ? { ...x, roleTypeOverrides: { ...x.roleTypeOverrides, [y]: { ...x.roleTypeOverrides[y], directoryPhotos: e } } } : { ...x, base: { ...x.base, directoryPhotos: e } },
                            V = async e => { let t; switch (e.target.name) {
                                    case "standard":
                                    case "noPhoto":
                                    case "vacant":
                                        t = { ...A, [e.target.name]: { ...A[e.target.name], visible: e.target.checked } }; break;
                                    default:
                                        t = { ...A, [e.target.name]: e.target.checked } } await M(L.ZO.update({ orgId: S, themeId: x.id, data: I(t) })) }, O = e => { e && e.stopPropagation(), H("noPhoto"), E.current.click() }, R = e => async t => { t && t.stopPropagation(), await M(L.ZO.update({ orgId: S, themeId: x.id, data: I({ ...A, noPhoto: { ...A.noPhoto, imageAvatarId: e } }) })) }, F = [{ id: "initials", image: (0, lt.Or)("initials") }, { id: "avatar1", image: (0, lt.Or)("avatar1") }, { id: "avatar2", image: (0, lt.Or)("avatar2") }, { id: "avatar3", image: (0, lt.Or)("avatar3") }], N = [{ id: "vacant1", image: (0, lt.Or)("vacant1") }, { id: "vacant2", image: (0, lt.Or)("vacant2") }, { id: "vacant3", image: (0, lt.Or)("vacant3") }, { id: "vacant4", image: (0, lt.Or)("vacant4") }], _ = e => { e && e.stopPropagation(), H("vacant"), E.current.click() }, W = e => async t => { t && t.stopPropagation(), N.find((t => t.id === e)).isNew && (H("vacant"), E.current.click()), await M(L.ZO.update({ orgId: S, themeId: x.id, data: I({ ...A, vacant: { ...A.vacant, imageAvatarId: e } }) })) }, U = async e => { await M(L.ZO.update({ orgId: S, themeId: x.id, data: I({ ...A, [T]: { ...A[T], customImageUrl: e } }) })) }, q = [{ label: "Circle", value: "circle", variant: "circular" }, { label: "Rounded", value: "oval", variant: "rounded" }, { label: "Square", value: "square", variant: "square" }].find((e => e.value === (null === k || void 0 === k ? void 0 : k.shape))), G = (null === q || void 0 === q ? void 0 : q.variant) || "circular"; return (0, D.jsxs)("div", { ref: v, children: [(0, D.jsx)("input", { type: "file", name: "fileUploader", ref: E, id: "fileUploader", style: { display: "none" }, accept: ".jpg,.JPG,.jpeg,.JPEG,.png,.PNG", onChange: e => { if (e.target.files && e.target.files.length > 0) { const t = new FileReader;
                                        t.addEventListener("load", (() => { C({ image: t.result, onUpload: U }) })), t.readAsDataURL(e.target.files[0]) } } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show Photos", checked: null === k || void 0 === k || null === (a = k.standard) || void 0 === a ? void 0 : a.visible, onChange: V, name: "standard" }) }), (null === k || void 0 === k || null === (l = k.standard) || void 0 === l ? void 0 : l.visible) && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Size", data: [{ label: "Small", value: 40 }, { label: "Medium", value: 60 }, { label: "Large", value: 80 }], value: null === k || void 0 === k || null === (s = k.standard) || void 0 === s ? void 0 : s.size, name: "size", onChange: async (e, t) => { let n; if ("shape" === e) n = { ...A, [e]: t };
                                            else n = { ...A, standard: { ...A.standard, size: t }, noPhoto: { ...A.noPhoto, size: t }, vacant: { ...A.vacant, size: t } };
                                            await M(L.ZO.update({ orgId: S, themeId: x.id, data: I(n) })) } }) }), z && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show when no photo is available", checked: null === k || void 0 === k || null === (c = k.noPhoto) || void 0 === c ? void 0 : c.visible, onChange: V, name: "noPhoto" }) }), (null === k || void 0 === k || null === (d = k.noPhoto) || void 0 === d ? void 0 : d.visible) && (0, D.jsx)(i.A, { display: "flex", gap: 1, children: F.map((e => { var t, n; return (0, D.jsx)(it.A, { selected: e.id === (null === k || void 0 === k || null === (t = k.noPhoto) || void 0 === t ? void 0 : t.imageAvatarId), id: e.id, image: "".concat(e.image), handleClick: R, handleEdit: O, customImageUrl: null === k || void 0 === k || null === (n = k.noPhoto) || void 0 === n ? void 0 : n.customImageUrl, isNew: e.isNew, variant: G }) })) })] }), b && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show photo for vacant roles", checked: null === k || void 0 === k || null === (h = k.vacant) || void 0 === h ? void 0 : h.visible, onChange: V, name: "vacant" }) }), (null === k || void 0 === k || null === (f = k.vacant) || void 0 === f ? void 0 : f.visible) && (0, D.jsx)(i.A, { display: "flex", gap: 1, children: N.map((e => { var t, n; return (0, D.jsx)(it.A, { selected: e.id === (null === k || void 0 === k || null === (t = k.vacant) || void 0 === t ? void 0 : t.imageAvatarId), id: e.id, image: e.image, customImageUrl: null === k || void 0 === k || null === (n = k.vacant) || void 0 === n ? void 0 : n.customImageUrl, handleClick: W, handleEdit: _, isNew: e.isNew }) })) })] })] })] }) }; var qt; const Gt = (0, S.Ay)(i.A)(qt || (qt = (0, a.A)(["\n  margin-left: -8px;\n  margin-right: -8px;\n  display: grid;\n  grid-template-columns: 24px auto 12px;\n  align-items: center;\n  cursor: pointer;\n  padding: 2.5px 8px;\n  border-radius: 4px;\n  &:hover {\n    background: #888888;\n  }\n  &:hover svg,\n  &:hover .MuiTypography-root {\n    color: #ffffff !important;\n  }\n  ", "\n"])), (e => { let { selected: t } = e; return "\n    ".concat(t ? "\n      background: #444444;\n      svg, .MuiTypography-root { color: #ffffff !important;}\n    " : "", "\n  ") })),
                    Kt = e => { let { settingAreas: t, settings: n, selectedSetting: r = "cards", handleSettingClick: a, isOverrideEnabled: o } = e; return (0, D.jsx)(i.A, { mx: 1, children: Object.keys(t).map((e => (0, D.jsxs)("div", { children: [(0, D.jsx)(i.A, { my: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: "primary", children: t[e] }) }), (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", gap: 1, children: n.filter((n => n.area === t[e] && !n.hidden)).map((e => (0, D.jsxs)(Gt, { id: "theme_tooltip_".concat(e.slug), selected: e.slug === r, onClick: a(e.slug), "data-tour-anchor": null === e || void 0 === e ? void 0 : e.anchorDataAttribute, children: [(0, D.jsx)(I.me, { name: e.icon || "user", variant: "light", color: P.Qs.Neutrals[900] }), (0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[600], component: "span", children: e.name }) }), o(e.area, e.value) && (0, D.jsx)(i.A, { display: "inline", children: (0, D.jsx)(I.me, { name: "check", color: "#3CD3C2" }) })] }, e.id))) })] }, e))) }) },
                    Zt = e => { let { settings: t, selectedSetting: n = "table", handleSettingClick: r } = e; return (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", gap: 1, py: 1, mx: 1, children: t.filter((e => !e.hidden)).map((e => (0, D.jsxs)(Gt, { id: "theme_tooltip_".concat(e.slug), selected: e.slug === n, onClick: r(e.slug), "data-tour-anchor": null === e || void 0 === e ? void 0 : e.anchorDataAttribute, children: [(0, D.jsx)(I.me, { name: e.icon || "user", variant: "light", color: P.Qs.Neutrals[900] }), (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[600], children: e.name })] }, e.id))) }) }; var Yt, Xt, $t, Qt, Jt, en, tn, nn, rn = n(97563),
                    an = n(66856),
                    on = n(356),
                    ln = n(27017);
                (0, S.Ay)(W.A)(Yt || (Yt = (0, a.A)(["\n  background: #f8f8f8;\n  padding: 20px;\n  border-radius: 10px;\n"]))), (0, S.Ay)(we.A)(Xt || (Xt = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  box-shadow: 1px 0px 5px 0px rgba(0,0,0,0.1);\n  background: ".concat(t.palette.grey[50], ";\n  overflow: auto;\n  ") })), (0, S.Ay)(we.A)($t || ($t = (0, a.A)(["\n  ", "\n"])), (e => { let { width: t } = e; return "\n  background: #fdfdfd;\n  box-shadow: 1px 0px 5px 0px rgba(0,0,0,0.1);\n  height: 100%;\n  width:".concat(t, "px;\n  max-width:").concat(t, "px;\n  ") })), (0, S.Ay)(ze.A)(Qt || (Qt = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    background: none;\n    border-radius: 16px;\n    border: 2px dashed ".concat(t.palette.grey[600], ";\n    color: ").concat(t.palette.grey[600], ";\n    box-shadow: none;\n    height: 90%;\n    padding: 55px 0;\n\n    span {\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n\n      svg{\n        margin-bottom: 10px;\n      }\n    }\n\n    &:hover {\n      box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);\n      background: none;\n      border: 2px dashed ").concat(t.palette.grey[700], ";\n      color: ").concat(t.palette.grey[700], ";\n    }\n") })), (0, S.Ay)(xe.A)(Jt || (Jt = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, dashboardMode: n = !1 } = e; return "\n  position: absolute;\n  left: ".concat(.7 * Ae.Ay.dialogs.themeSettingsWidth, "px;\n  z-index: 2;\n  width: max-content;\n  box-shadow: none;\n  padding: 30px 40px;\n  width: calc(100vw - ").concat(.7 * Ae.Ay.dialogs.themeSettingsWidth + (n ? Ae.Ay.dashboard.navWidth : Ae.Ay.chart.navWidth), "px);\n  background: #fdfdfd;\n  border-bottom: 1px solid ").concat(t.palette.grey[300], ";\n  border-left: 1px solid ").concat(t.palette.grey[300], ";\n  border-radius: 0;\n") })), (0, S.Ay)(V.A)(en || (en = (0, a.A)(["\n  height: 50px;\n  width: 260px;\n  &:disabled {\n    cursor: not-allowed;\n  }\n"]))), (0, S.Ay)(xe.A)(tn || (tn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, width: n } = e; return "\n  position: absolute;\n  height: calc(100% - 82px);\n  box-shadow: none;\n  width: ".concat(n, "px;\n  z-index:1;\n  .Mui-selected{\n    color:").concat(t.palette.primary.main, "\n  }\n  ") })); const sn = (0, S.Ay)(xe.A)(nn || (nn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  position: relative;\n  height: 100%;\n  border: solid 1px ".concat(t.palette.grey[200], ";\n  border-radius: 0px;\n  padding: 0px ").concat(t.spacing(3), "px;\n  .MuiSelect-root{\n    background:white;\n  }\n  .MuiIconButton-root {\n    padding:9px !important;\n    border-radius:0;\n  }\n  .MuiIconButton-root.Mui-disabled{\n    color:").concat(t.palette.primary.main, "\n  }\n  ") })),
                    cn = e => { var t; let { inTheme: n, fieldKey: r, fieldLabel: a, fieldType: o, font: s, open: c, hideEmptyField: d, handleFieldClick: u, handleChecked: h, handleFontChange: m } = e; const p = s && Object.keys(s).reduce(((e, t) => { switch (t) {
                                    case "weight":
                                        s[t] > 400 && e.push("bold"); break;
                                    case "italic":
                                        s[t] && e.push("italic"); break;
                                    case "underline":
                                        s[t] && e.push("underline"); break;
                                    case "strikethrough":
                                        s[t] && e.push("strikethrough") } return e }), []),
                            f = { border: "1px solid white", marginBottom: ".5rem", cursor: n ? "move" : "default" }; return (0, D.jsxs)("div", { style: f, children: [(0, D.jsxs)(Le, { $active: c, children: [(0, D.jsx)(ln.A, { children: (0, D.jsx)(ve.A, { disableRipple: !0, disableFocusRipple: !0, checked: n, onClick: h }) }), (0, D.jsx)(i.A, { maxWidth: n ? 180 : "100%", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[700], children: a }) }), n && (0, D.jsx)(ye.A, { children: (0, D.jsx)(Xe.A, { size: "small", onClick: u, children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: c ? "minus" : "gear", size: "sm", variant: "light" }) }, "icon-".concat(c, "-").concat(r)) }) })] }), n && (0, D.jsx)(be.A, { in: c, timeout: "auto", unmountOnExit: !0, children: (0, D.jsxs)(sn, { elevation: 0, children: ["date" === o && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Date Format", value: null === s || void 0 === s || null === (t = s.dateFormat) || void 0 === t ? void 0 : t.value, color: "primary", onChange: e => { let t = He.zg.find((t => t.value.value === e.target.value));
                                                m("dateFormat", t.value) }, singleLine: !0, options: He.zg.map((e => ({ value: e.value.value, label: e.label }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: s.size, color: "primary", onChange: e => { m("size", e.target.value) }, singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: p, onChange: (e, t) => { m("format", t) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Case", singleLine: !0, value: (null === s || void 0 === s ? void 0 : s.case) || "none", onChange: (e, t) => { m("case", t) }, data: He.ML }) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Color", handleChangeBackground: e => { m("color", e) }, color: null === s || void 0 === s ? void 0 : s.color, showReset: !1 }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Hide if no value set", checked: d, onChange: () => { m("hideEmptyField", !d) }, name: "hideEmptyField" }) })] }) })] }) }; var dn = n(74772); const un = e => { var t, n, r, a; let { innerRef: l, isOverrideMode: s = !1, overrideRoleType: c = "single" } = e; const d = (0, m.d4)(f.V1),
                            u = (0, m.d4)(p.P0),
                            h = null === u || void 0 === u || null === (t = u.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n || null === (r = n.layout) || void 0 === r ? void 0 : r.name,
                            v = dn.A.find((e => e.id === h)),
                            g = (0, m.d4)(f.Dy),
                            y = (0, o.useMemo)((() => g(c, null === v || void 0 === v ? void 0 : v.fields)), [c, g]),
                            [b, w] = (0, o.useState)(y),
                            [z, x] = (0, o.useState)(),
                            A = (0, m.wA)();
                        (0, o.useEffect)((() => { w(y) }), [y]); const k = e => s ? { ...u, roleTypeOverrides: { ...u.roleTypeOverrides, [c]: { ...u.roleTypeOverrides[c], fields: e } } } : { ...u, data: { ...u.data, detailsPane: { ...u.data.detailsPane, fields: e } } },
                            S = (0, o.useCallback)(((e, t) => { const n = b.inTheme[e],
                                    r = H()(b.inTheme, { $splice: [
                                            [e, 1],
                                            [t, 0, n]
                                        ] });
                                w({ ...b, inTheme: [...r] }), (async e => { const t = e.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle })));
                                    await A(L.ZO.update({ orgId: d, themeId: u.id, data: k(t) })) })(r) }), [b.inTheme]),
                            E = e => () => { x(e === z ? null : e) },
                            C = e => async t => { let n = {}; if (t.target.checked) { const t = b.notInTheme.find((t => t.fieldKey === e)),
                                        r = b.notInTheme.filter((e => e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model));
                                    n = b.inTheme.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))), r.forEach((e => { n.push({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515" } }) })) } else { const t = b.inTheme.find((t => t.fieldKey === e));
                                    n = b.inTheme.filter((e => !(e.fieldInfo.name === t.fieldInfo.name && e.fieldInfo.model === t.fieldInfo.model))).map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: e.fieldStyle }))) } await A(L.ZO.update({ orgId: d, themeId: u.id, data: k(n) })) }, T = async e => { const t = b.inTheme.find((t => t.fieldKey === e.fieldKey)); let n; switch (e.prop) {
                                    case "color":
                                    case "align":
                                    case "size":
                                    case "border":
                                    case "hideEmptyField":
                                    case "case":
                                    case "dateFormat":
                                        n = b.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, [e.prop]: e.value } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })); break;
                                    case "format":
                                        n = b.inTheme.map((n => { var r, a; return n.fieldInfo.name === (null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.model) ? { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: { ...n.fieldStyle, weight: e.value.includes("bold") ? 600 : 400, italic: !!e.value.includes("italic"), underline: !!e.value.includes("underline"), strikethrough: !!e.value.includes("strikethrough") } } : { field: null === n || void 0 === n ? void 0 : n.fieldInfo, fontDetails: n.fieldStyle } })) } await A(L.ZO.update({ orgId: d, themeId: u.id, data: k(n) })) }; return (0, D.jsx)("div", { ref: l, children: (0, D.jsxs)(i.A, { my: 2, maxHeight: 600, overflow: "auto", children: [(0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (0, D.jsx)(rt.A, { items: (null === b || void 0 === b ? void 0 : b.inTheme) || [], itemKey: "fieldKey", handleItemsReordered: S, ListComponent: i.A, emptyItem: "Empty", renderItem: (e, t) => { var n; let { fieldKey: r, fieldInfo: a, fieldStyle: o, hidden: i = !1 } = e; return !i && (0, D.jsx)(cn, { inTheme: !0, checked: !0, index: t, fieldKey: r, fieldLabel: (null === a || void 0 === a || null === (n = a.labelOverride) || void 0 === n ? void 0 : n[c]) || a.label, fieldType: null === a || void 0 === a ? void 0 : a.type, font: o, moveField: S, open: r === z, hideEmptyField: void 0 === (null === o || void 0 === o ? void 0 : o.hideEmptyField) || (null === o || void 0 === o ? void 0 : o.hideEmptyField), handleFieldClick: E(r), handleChecked: C(r), handleFontChange: (e, t) => T({ fieldKey: r, prop: e, value: t }) }, r) } }) }), (0, D.jsx)(M, { component: "div", disablePadding: !0, children: null === b || void 0 === b || null === (a = b.notInTheme) || void 0 === a ? void 0 : a.filter((e => !e.hidden)).map(((e, t) => { var n; let { fieldKey: r, fieldInfo: a, fieldStyle: o } = e; return (0, D.jsx)(cn, { inTheme: !1, handleChecked: C(r), checked: !1, fieldKey: r, fieldLabel: (null === a || void 0 === a || null === (n = a.labelOverride) || void 0 === n ? void 0 : n[c]) || a.label, font: o, open: r === z, handleFieldClick: null, handleFontChange: T }, t) })) })] }) }) },
                    hn = e => { var t; let { inTheme: n, content: r, fieldType: a, font: o, open: l, section: s, handleFontChange: c } = e; const d = { cursor: n ? "move" : "default", width: "100%" },
                            u = o && Object.keys(o).reduce(((e, t) => { switch (t) {
                                    case "weight":
                                        o[t] > 400 && e.push("bold"); break;
                                    case "italic":
                                        o[t] && e.push("italic"); break;
                                    case "underline":
                                        o[t] && e.push("underline"); break;
                                    case "strikethrough":
                                        o[t] && e.push("strikethrough") } return e }), []); return (0, D.jsx)("div", { style: d, children: n && (0, D.jsx)(be.A, { in: l && !(null !== r && void 0 !== r && r.commonStyle), timeout: "auto", unmountOnExit: !0, children: (0, D.jsxs)(sn, { elevation: 0, children: ["date" === a && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Date Format", value: null === o || void 0 === o || null === (t = o.dateFormat) || void 0 === t ? void 0 : t.value, color: "primary", onChange: e => { let t = He.zg.find((t => t.value.value === e.target.value));
                                                c("dateFormat", t.value) }, singleLine: !0, options: He.zg.map((e => ({ value: e.value.value, label: e.label }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Font Color", color: (null === o || void 0 === o ? void 0 : o.color) || "#151515", handleChangeBackground: e => c("color", e) }) }), "icon-section" !== s && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: null === o || void 0 === o ? void 0 : o.size, color: "primary", onChange: e => c("size", e.target.value), singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: u, onChange: async (e, t) => { c("format", t) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(i.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Case", singleLine: !0, value: (null === o || void 0 === o ? void 0 : o.case) || "none", onChange: (e, t) => { c("case", t) }, data: He.ML }) }) })] })] }) }) }) }; var mn = n(10621); const pn = e => { var t, n, r, a, s, c, d, u, h, v; let { theme: g, overrideRoleType: y = "single" } = e; const { data: { detailsPane: b } } = g, { base: { content: z } } = g, x = (0, m.d4)(p.P0), A = null === x || void 0 === x || null === (t = x.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n || null === (r = n.layout) || void 0 === r ? void 0 : r.name, k = dn.A.find((e => e.id === A)), S = (0, m.d4)(f.wB), E = (0, o.useMemo)((() => S(y, null === k || void 0 === k ? void 0 : k.fields)), [y, S]), C = null === b || void 0 === b || null === (a = b.layout) || void 0 === a ? void 0 : a.banner, T = (0, m.wA)(), H = (0, m.d4)(f.V1), [j, V] = (0, o.useState)([]), [O, F] = (0, o.useState)([]), [N, _] = (0, o.useState)(E), [B, W] = (0, o.useState)(), [U, G] = (0, o.useState)(), Z = null === k || void 0 === k ? void 0 : k.mainSectionFieldsCount, Y = (0, m.d4)(f.Dy), X = (0, o.useMemo)((() => Y(y, null === k || void 0 === k ? void 0 : k.fields)), [y, Y]), [$, Q] = (0, o.useState)(X);
                        (0, o.useEffect)((() => { _(E), Q($); const e = { fieldInfo: { id: "none", name: "none", label: "None", type: "string" }, fieldKey: "none", fieldStyle: {} }; let t = [{ ...e }, ...E.inTheme, ...E.notInTheme]; const n = [...new Map(t.map((e => [e.fieldKey, e]))).values()];
                            V(n); let r = n.filter((e => e.fieldInfo.name === mn.x2.EMAIL || e.fieldInfo.name === mn.x2.LINKEDIN || e.fieldInfo.name === mn.x2.PHONE));
                            F([{ ...e }, ...r]) }), [E, X]); const J = e => { let t, n = X.inTheme.map((e => ({ field: null === e || void 0 === e ? void 0 : e.fieldInfo, fontDetails: { ...e.fieldStyle } }))); return t = x.data.detailsPane ? x.data.detailsPane : { layout: { name: "layout-1", banner: { fields: e, color: "#00ACC0", showLegends: !1, showRoleColorAsBannerColor: !0 } } }, { ...x, data: { ...x.data, detailsPane: { ...t, layout: { ...t.layout, banner: { ...t.layout.banner, fields: e } }, fields: n } } } },
                            ee = (e, t) => () => { W(e === B ? null : e), G(t === U ? null : t) },
                            te = async (e, t, n) => { var r, a, o, i;
                                W(null), G(null); let l = {};
                                l = N.inTheme.filter((e => !e.hidden)).map((e => ({ field: { ...null === e || void 0 === e ? void 0 : e.fieldInfo, section: null === e || void 0 === e ? void 0 : e.fieldSection }, fontDetails: e.fieldStyle }))); const s = j.find((t => t.fieldKey === e && !t.hidden)); switch (t) {
                                    case "main-section":
                                        l[n] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: null === (r = k.fields[n]) || void 0 === r ? void 0 : r.fontDetails }; break;
                                    case "icon-section":
                                        var c = "linkedIn" === (null === s || void 0 === s || null === (a = s.fieldInfo) || void 0 === a ? void 0 : a.name) ? { ...null === (o = k.fields[n + Z]) || void 0 === o ? void 0 : o.fontDetails, color: "#005dc9" } : { ...null === (i = k.fields[n + Z]) || void 0 === i ? void 0 : i.fontDetails, color: "#AEAEAE" };
                                        l[n + Z] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: c } } await T(L.ZO.update({ orgId: H, themeId: null === x || void 0 === x ? void 0 : x.id, data: J(l) })) }, ne = async e => { const t = N.inTheme.find(((t, n) => t.fieldKey === e.fieldKey && n === e.index)); let n = []; switch (e.prop) {
                                    case "color":
                                    case "align":
                                    case "size":
                                    case "case":
                                    case "dateFormat":
                                        n = N.inTheme.map(((n, r) => { var a, o; return n.fieldInfo.name === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.model) && r === e.index ? { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: { ...n.fieldStyle, [e.prop]: e.value } } : { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: n.fieldStyle } })); break;
                                    case "format":
                                        n = N.inTheme.map(((n, r) => { var a, o; return n.fieldInfo.name === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.model) && r === e.index ? { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: { ...n.fieldStyle, weight: e.value.includes("bold") ? 700 : 400, italic: !!e.value.includes("italic"), underline: !!e.value.includes("underline"), strikethrough: !!e.value.includes("strikethrough") } } : { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: n.fieldStyle } })) } await T(L.ZO.update({ orgId: H, themeId: null === x || void 0 === x ? void 0 : x.id, data: J(n) })) }; return (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show Role Color as Banner color", checked: null === C || void 0 === C ? void 0 : C.showRoleColorAsBannerColor, onChange: () => (async e => { let t;
                                        t = x.data.detailsPane ? x.data.detailsPane : { layout: { name: "layout-1", banner: { fields: [], color: "#00ACC0", showLegends: !1, showRoleColorAsBannerColor: e } } }, await T(L.ZO.update({ orgId: H, themeId: x.id, data: { ...x, data: { ...x.data, detailsPane: { ...t, layout: { ...t.layout, banner: { ...t.layout.banner, showRoleColorAsBannerColor: e } } } } } })) })(!(null !== C && void 0 !== C && C.showRoleColorAsBannerColor)), name: "showRoleColorAsBannerColor" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Default Color", initialColor: (null === C || void 0 === C ? void 0 : C.color) || "#00ACC0", handleChangeBackground: async e => { let t;
                                        t = x.data.detailsPane ? x.data.detailsPane : { layout: { name: "layout-1", banner: { fields: [], color: e, showLegends: !1, showRoleColorAsBannerColor: !0 } } }, await T(L.ZO.update({ orgId: H, themeId: x.id, data: { ...x, data: { ...x.data, detailsPane: { ...t, layout: { ...t.layout, banner: { ...t.layout.banner, color: e } } } } } })) }, color: (null === C || void 0 === C ? void 0 : C.color) || "#00ACC0", showReset: !1 }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(Ce, { label: "Show Badges", checked: null === C || void 0 === C ? void 0 : C.showLegends, onChange: () => (async e => { let t;
