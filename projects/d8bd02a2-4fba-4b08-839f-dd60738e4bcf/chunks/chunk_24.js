                    k = n(72119),
                    S = n(96364),
                    M = n(35033),
                    E = n(66486),
                    C = n(74706),
                    T = n(55340),
                    H = n(82513),
                    L = n(80313),
                    I = n(9368),
                    j = n(41450),
                    V = n(10621),
                    O = n(14556),
                    R = n(23993),
                    P = n(37294),
                    D = n(75156),
                    F = n(70579); const N = 100,
                    _ = (0, k.Ay)(y.A)(r || (r = (0, g.A)(["\n  background: #fff;\n  margin: 0px auto;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"]))),
                    B = (0, k.Ay)(y.A)(a || (a = (0, g.A)(["\n  flex: 0 0 auto;\n"]))),
                    W = (0, k.Ay)(y.A)(o || (o = (0, g.A)([""]))),
                    U = (0, k.Ay)(y.A)(i || (i = (0, g.A)(["\n  width: 100%;\n  height: auto;\n  background: #fff;\n  margin: 0px auto;\n  padding: 10px 0px;\n  text-align: center;\n  gap: 10px;\n  display: inline-grid;\n  position: relative;\n"]))),
                    q = (0, k.Ay)(y.A)(l || (l = (0, g.A)(["\n  ", "\n"])), (e => { let { shape: t } = e; return "\n    border-radius: ".concat(t, ";\n    background-color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n") })),
                    G = (0, k.Ay)(y.A)(s || (s = (0, g.A)(["\n  display: flex;\n  justify-content: center;\n  padding-top: 60px;\n"]))),
                    K = (0, k.Ay)(y.A)(c || (c = (0, g.A)(["\n  display: flex;\n  flex-direction: column;\n  grid-gap: 12px;\n"]))),
                    Z = (0, k.Ay)(y.A)(d || (d = (0, g.A)(["\n  display: flex;\n  justify-content: center;\n  gap: 12px;\n  padding-top: 10px;\n"]))),
                    Y = (0, k.Ay)(y.A)(u || (u = (0, g.A)(["\n  height: auto;\n  background: #fff;\n  display: block;\n"]))),
                    X = (0, k.Ay)(y.A)(h || (h = (0, g.A)([""]))),
                    $ = (0, k.Ay)(y.A)(m || (m = (0, g.A)(["\n  ", "\n"])), (e => { let { bannerColor: t } = e; return "\n  .MuiTabs-indicator{\n    background-color: ".concat(t, ";\n  }\n") })),
                    Q = (0, k.Ay)(y.A)(p || (p = (0, g.A)(["\n  flex: 1 1 auto;\n  position: relative;\n  overflow-y: hidden;\n"]))),
                    J = (0, k.Ay)(b.A)(f || (f = (0, g.A)(["\n  position: absolute !important;\n  bottom: -15px;\n  right: 16px;\n  background-color: #555555 !important;\n  color: #ffffff;\n  &:hover {\n    background-color: #444444 !important;\n  }\n"]))),
                    ee = (0, k.Ay)(b.A)(v || (v = (0, g.A)(["\n  position: absolute !important;\n  right: 0;\n  top: 5px;\n  z-index: 1;\n"]))),
                    te = e => { let { value: t, hidden: n, children: r } = e; return (0, F.jsx)("div", { "aria-labelledby": "details-pane-layout1-".concat(t), hidden: n, style: { overflow: "auto", position: "relative", height: "100%" }, children: r }) },
                    ne = e => { let { fields: t, member: n, role: r, isSample: a, orgId: o } = e; return (0, F.jsx)(Y, { p: 2, children: t.filter((e => !(null !== e && void 0 !== e && e.hidden || !a && (e => { var t, n; return void 0 === (null === e || void 0 === e || null === (t = e.fieldStyle) || void 0 === t ? void 0 : t.hideEmptyField) || (null === e || void 0 === e || null === (n = e.fieldStyle) || void 0 === n ? void 0 : n.hideEmptyField) })(e) && (0, V.i6)(e, n, r)))).map(((e, t) => { var i, l, s, c, d; const u = "member" === (null === e || void 0 === e || null === (i = e.fieldInfo) || void 0 === i ? void 0 : i.model) ? n : r; return (0, F.jsxs)(X, { p: 2, children: [(0, F.jsxs)(x.A, { fontSize: 14, color: P.Qs.Neutrals[800], fontWeight: 600, children: [null === e || void 0 === e || null === (l = e.fieldInfo) || void 0 === l ? void 0 : l.label, ":"] }), (0, F.jsx)(C.A, { objectId: null === u || void 0 === u ? void 0 : u.id, field: null === e || void 0 === e ? void 0 : e.fieldInfo, value: a ? "Sample value" : "member" === (null === e || void 0 === e || null === (s = e.fieldInfo) || void 0 === s ? void 0 : s.model) ? null === n || void 0 === n ? void 0 : n[null === e || void 0 === e || null === (c = e.fieldInfo) || void 0 === c ? void 0 : c.id] : null === r || void 0 === r ? void 0 : r[null === e || void 0 === e || null === (d = e.fieldInfo) || void 0 === d ? void 0 : d.id], fontDetails: null === e || void 0 === e ? void 0 : e.fieldStyle, section: null === e || void 0 === e ? void 0 : e.fieldSection, orgId: o }, t)] }, t) })) }) },
                    re = e => { let { role: t, personRoles: n, roleNameFieldId: r, isSample: a } = e; const o = (0, I.A)(),
                            i = e => { const t = e.find((e => e.id === r)); return t ? t.value : "" }; return (0, F.jsx)(Y, { children: (0, F.jsx)(X, { p: 4, children: a ? (0, F.jsx)(S.A, { children: " Role 1" }) : (0, F.jsxs)(F.Fragment, { children: [o && (0, F.jsx)(y.A, { mt: 1, mb: 2, children: (0, F.jsx)(T.A, { active: t.id, name: t[r], chartName: t.chartName, id: t.id, chartId: t.chartId, orgId: t.orgId, isClickable: !1 }) }), null === n || void 0 === n ? void 0 : n.map((e => (0, F.jsx)(T.A, { active: (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id), name: i(e.fields), chartName: null === e || void 0 === e ? void 0 : e.chartName, id: null === e || void 0 === e ? void 0 : e.id, chartId: null === e || void 0 === e ? void 0 : e.chartId, orgId: null === e || void 0 === e ? void 0 : e.orgId, isClickable: !0 }, null === e || void 0 === e ? void 0 : e.id)))] }) }) }) },
                    ae = e => { let { isMobile: t, role: n, connections: r, isSample: a } = e; return a ? (0, F.jsx)(Y, { p: 2, children: (0, F.jsx)(X, { p: 2, children: (0, F.jsx)(S.A, { children: " Connection 1" }) }) }) : (0, F.jsx)(Y, { p: 2, children: (0, F.jsx)(X, { p: 2, children: (0, F.jsx)(y.A, { mt: 1, mb: 2, children: (0, F.jsx)(H.Ay, { isMobile: t, role: n, connections: r }) }) }) }) },
                    oe = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m; let { role: p, member: f, personRoles: v, showMoreThanOnePerson: g, handlePrevSharedPerson: b, handleNextSharedPerson: x, connections: k, roleNameFieldId: S, theme: T, themeFieldsWithStyles: H, themeBannerFieldsWithStyles: I, isSample: V, editAccess: P, onClick: Y, cardWidth: X, onClose: oe, isMobile: ie, orgId: le } = e; const [se, ce] = (0, A.useState)("data"), de = null === T || void 0 === T || null === (t = T.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n || null === (r = n.layout) || void 0 === r ? void 0 : r.banner, ue = null === T || void 0 === T || null === (a = T.data) || void 0 === a || null === (o = a.detailsPane) || void 0 === o ? void 0 : o.photos, he = null === (i = (0, O.d4)(R.LH)[null === p || void 0 === p ? void 0 : p.id]) || void 0 === i ? void 0 : i.bgcolor; let me; switch (null === ue || void 0 === ue ? void 0 : ue.shape) {
                            case "circular":
                            default:
                                me = "9999px"; break;
                            case "oval":
                                me = "16px"; break;
                            case "square":
                                me = "0px" } const pe = (0, A.useMemo)((() => { const e = [{ value: "data", label: "Data" }]; return (V || (v || []).length > 0) && e.push({ value: "roles", label: "Roles" }), e.push({ value: "connections", label: "Connections" }), e }), [v]),
                            fe = e => ({ data: (0, F.jsx)(ne, { fields: (null === H || void 0 === H ? void 0 : H.inTheme) || [], member: f, role: p, isSample: V, orgId: le }), roles: (0, F.jsx)(re, { role: p, personRoles: v, roleNameFieldId: S, isSample: V }), connections: (0, F.jsx)(ae, { isMobile: ie, member: f, role: p, connections: k, isSample: V }) } [e]),
                            ve = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m; return "member" === (null === I || void 0 === I || null === (t = I.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === f || void 0 === f ? void 0 : f[null === I || void 0 === I || null === (o = I.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === p || void 0 === p ? void 0 : p[null !== (c = null === I || void 0 === I || null === (d = I.inTheme) || void 0 === d || null === (u = d.mainSection) || void 0 === u || null === (h = u[e]) || void 0 === h || null === (m = h.fieldInfo) || void 0 === m ? void 0 : m.id) && void 0 !== c ? c : ""] },
                            ge = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m; return "member" === (null === I || void 0 === I || null === (t = I.inTheme) || void 0 === t || null === (n = t.iconSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === f || void 0 === f ? void 0 : f[null === I || void 0 === I || null === (o = I.inTheme) || void 0 === o || null === (i = o.iconSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === p || void 0 === p ? void 0 : p[null !== (c = null === I || void 0 === I || null === (d = I.inTheme) || void 0 === d || null === (u = d.iconSection) || void 0 === u || null === (h = u[e]) || void 0 === h || null === (m = h.fieldInfo) || void 0 === m ? void 0 : m.id) && void 0 !== c ? c : ""] },
                            ye = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m; let { index: v } = e; const g = "member" === (null === I || void 0 === I || null === (t = I.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[v]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? f : p; return (0, F.jsx)(C.A, { objectId: null === g || void 0 === g ? void 0 : g.id, orgId: le, field: null === I || void 0 === I || null === (o = I.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[v]) || void 0 === l ? void 0 : l.fieldInfo, value: ve(v), fontDetails: null === I || void 0 === I || null === (s = I.inTheme) || void 0 === s || null === (c = s.mainSection) || void 0 === c || null === (d = c[v]) || void 0 === d ? void 0 : d.fieldStyle, section: null === I || void 0 === I || null === (u = I.inTheme) || void 0 === u || null === (h = u.mainSection) || void 0 === h || null === (m = h[v]) || void 0 === m ? void 0 : m.fieldSection }) },
                            be = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m; let { index: v } = e; const g = "member" === (null === I || void 0 === I || null === (t = I.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[v]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? f : p; return (0, F.jsx)(C.A, { objectId: null === g || void 0 === g ? void 0 : g.id, orgId: le, field: null === I || void 0 === I || null === (o = I.inTheme) || void 0 === o || null === (i = o.iconSection) || void 0 === i || null === (l = i[v]) || void 0 === l ? void 0 : l.fieldInfo, value: ge(v), fontDetails: null === I || void 0 === I || null === (s = I.inTheme) || void 0 === s || null === (c = s.iconSection) || void 0 === c || null === (d = c[v]) || void 0 === d ? void 0 : d.fieldStyle, section: null === I || void 0 === I || null === (u = I.inTheme) || void 0 === u || null === (h = u.iconSection) || void 0 === h || null === (m = h[v]) || void 0 === m ? void 0 : m.fieldSection }) },
                            we = null !== de && void 0 !== de && de.showRoleColorAsBannerColor && he && "#ffffff" !== he && "#FFFFFF" !== he ? he : (null === de || void 0 === de ? void 0 : de.color) || "#00ACC0"; return (0, F.jsxs)(_, { width: X || 490, height: "100%", style: V ? { border: "1px solid #d5d5d5" } : { border: "0px solid #d5d5d5" }, children: [(0, F.jsxs)(B, { children: [!V && (0, F.jsx)(ee, { color: "inherit", onClick: oe, children: (0, F.jsx)(D.gF, { icon: "Close", size: "lg" }) }), (0, F.jsx)(y.A, { height: 120 + ((null === ue || void 0 === ue || null === (l = ue.standard) || void 0 === l ? void 0 : l.size) || N) / 2 + 10, children: (0, F.jsxs)(W, { width: "100%", height: "".concat(70 + ((null === ue || void 0 === ue || null === (s = ue.standard) || void 0 === s ? void 0 : s.size) || N) / 2, "px"), bgcolor: we, position: "relative", children: [(0, F.jsx)(G, { children: (0, F.jsx)(q, { width: ((null === ue || void 0 === ue || null === (c = ue.standard) || void 0 === c ? void 0 : c.size) || N) + 10, height: ((null === ue || void 0 === ue || null === (d = ue.standard) || void 0 === d ? void 0 : d.size) || N) + 10, shape: me, padding: 1, position: "relative", children: (0, F.jsx)(E.A, { size: (null === ue || void 0 === ue || null === (u = ue.standard) || void 0 === u ? void 0 : u.size) || N, shape: null === ue || void 0 === ue ? void 0 : ue.shape, person: f, role: p }) }) }), !V && P && (0, F.jsx)(w.Ay, { item: !0, children: (0, F.jsx)(J, { color: "inherit", onClick: Y, children: (0, F.jsx)(D.gF, { icon: "Edit", size: "lg", color: "white" }) }) })] }) }), (0, F.jsxs)(U, { children: [g && (0, F.jsx)(j.A, { handleLeftClick: b, handleRightClick: x }), (0, F.jsxs)(K, { children: [ve(0) && (0, F.jsx)(ye, { index: 0 }), ve(1) && (0, F.jsx)(ye, { index: 1 }), ve(2) && (0, F.jsx)(ye, { index: 2 }), (0, F.jsx)(Z, { children: null === I || void 0 === I || null === (h = I.inTheme) || void 0 === h || null === (m = h.iconSection) || void 0 === m ? void 0 : m.map(((e, t) => (0, F.jsx)(be, { index: t }, t))) }), (null === de || void 0 === de ? void 0 : de.showLegends) && !V && (0, F.jsx)(y.A, { display: "flex", py: 1, justifyContent: "center", children: (0, F.jsx)(L.n, { role: p, person: f }) })] })] })] }), (0, F.jsxs)(Q, { children: [(0, F.jsx)($, { bannerColor: we, children: (0, F.jsx)(M.A, { onChange: (e, t) => (e => { ce(e) })(t), value: se, variant: "fullWidth", children: pe.map((e => (0, F.jsx)(z.A, { value: e.value, label: e.label, id: "details-pane-layout1-".concat(e.value) }, "details-pane-layout1-".concat(e.value)))) }) }), pe.map((e => (0, F.jsx)(te, { hidden: se !== e.value, value: e.value, children: (0, F.jsx)(y.A, { pb: 2, children: fe(e.value) }) }, "details-pane-layout1-".concat(e.value))))] })] }) } }, 88159: (e, t, n) => { "use strict";
                n.d(t, { A: () => ie }); var r, a, o, i, l, s, c, d, u, h, m, p, f, v, g, y, b, w = n(57528),
                    z = n(96446),
                    x = n(17392),
                    A = n(68903),
                    k = n(30279),
                    S = n(65043),
                    M = n(72119),
                    E = n(96364),
                    C = n(11322),
                    T = n(66486),
                    H = n(74706),
                    L = n(82513),
                    I = n(55340),
                    j = n(80313),
                    V = n(9368),
                    O = n(41450),
                    R = n(10621),
                    P = n(14556),
                    D = n(23993),
                    F = n(85865),
                    N = n(37294),
                    _ = n(75156),
                    B = n(70579); const W = (0, M.Ay)(z.A)(r || (r = (0, w.A)(["\n  background: #fff;\n  margin: 0px auto;\n  display: flex;\n  flex-direction: column;\n"]))),
                    U = (0, M.Ay)(z.A)(a || (a = (0, w.A)(["\n  height: auto;\n  flex: 0 0 auto;\n"]))),
                    q = (0, M.Ay)(z.A)(o || (o = (0, w.A)(["\n  display: flex;\n  justify-content: center;\n  flex-direction: column;\n  align-items: center;\n  height: 100%;\n"]))),
                    G = (0, M.Ay)(z.A)(i || (i = (0, w.A)(["\n  width: 85%;\n  border-radius: 4px;\n  border: 1px solid #e5e5e5;\n  background: #fff;\n  margin-top: -20%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n"]))),
                    K = (0, M.Ay)(z.A)(l || (l = (0, w.A)(["\n  padding: 20px;\n  margin: auto 0;\n"]))),
                    Z = (0, M.Ay)(z.A)(s || (s = (0, w.A)(["\n  width: 100%;\n  height: auto;\n  background: #fff;\n  margin: 0px auto;\n  padding: 20px 20px 20px 0;\n  display: flex;\n  flex-direction: column;\n  grid-gap: 8px;\n"]))),
                    Y = (0, M.Ay)(E.A)(c || (c = (0, w.A)([""]))),
                    X = (0, M.Ay)(E.A)(d || (d = (0, w.A)([""]))),
                    $ = (0, M.Ay)(E.A)(u || (u = (0, w.A)([""]))),
                    Q = (0, M.Ay)(z.A)(h || (h = (0, w.A)(["\n  display: flex;\n  gap: 20px;\n  padding-top: 10px;\n"]))),
                    J = (0, M.Ay)(z.A)(m || (m = (0, w.A)(["\n  width: 85%;\n  height: auto;\n  border-radius: 4px;\n  border: 1px solid #e5e5e5;\n  background: #fff;\n  margin: 16px auto;\n  display: block;\n  padding: 10px 20px;\n"]))),
                    ee = (0, M.Ay)(E.A)(p || (p = (0, w.A)(["\n  color: #545353;\n  font-size: 15px;\n  font-style: normal;\n  font-weight: 700;\n  line-height: normal;\n"]))),
                    te = (0, M.Ay)(z.A)(f || (f = (0, w.A)(["\n  display: flex;\n"]))),
                    ne = (0, M.Ay)(E.A)(v || (v = (0, w.A)(["\n  text-align: left;\n"]))),
                    re = (0, M.Ay)(z.A)(g || (g = (0, w.A)(["\n  position: relative;\n  overflow: auto;\n  flex: 1 1 auto;\n"]))),
                    ae = (0, M.Ay)(x.A)(y || (y = (0, w.A)(["\n  position: absolute !important;\n  top: -16px;\n  right: -16px;\n  background-color: #555555 !important;\n  color: #ffffff;\n  z-index: 1;\n  &:hover {\n    background-color: #444444 !important;\n  }\n"]))),
                    oe = (0, M.Ay)(x.A)(b || (b = (0, w.A)(["\n  position: absolute !important;\n  right: 0;\n  top: 5px;\n  z-index: 1;\n"]))),
                    ie = e => { var t, n, r, a, o, i, l, s, c; let { isMobile: d, role: u, member: h, personRoles: m, showMoreThanOnePerson: p, handlePrevSharedPerson: f, handleNextSharedPerson: v, connections: g, roleNameFieldId: y, theme: b, themeFieldsWithStyles: w, themeBannerFieldsWithStyles: x, isSample: M, editAccess: ie, onClick: le, cardWidth: se, onClose: ce, orgId: de } = e; const [ue, he] = (0, S.useState)("data"), { banner: me } = (null === b || void 0 === b || null === (t = b.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n ? void 0 : n.layout) || {}, { photos: pe } = (null === b || void 0 === b || null === (r = b.data) || void 0 === r ? void 0 : r.detailsPane) || {}, fe = (0, V.A)(), ve = null === (a = (0, P.d4)(D.LH)[null === u || void 0 === u ? void 0 : u.id]) || void 0 === a ? void 0 : a.bgcolor, ge = e => { he(e) }, ye = e => { var t, n, r, a, o, i, l, s, c, d, m, p, f; return "member" === (null === x || void 0 === x || null === (t = x.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === h || void 0 === h ? void 0 : h[null === x || void 0 === x || null === (o = x.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === u || void 0 === u ? void 0 : u[null !== (c = null === x || void 0 === x || null === (d = x.inTheme) || void 0 === d || null === (m = d.mainSection) || void 0 === m || null === (p = m[e]) || void 0 === p || null === (f = p.fieldInfo) || void 0 === f ? void 0 : f.id) && void 0 !== c ? c : ""] }, be = e => { var t, n, r, a, o, i, l, s, c, d, m, p, f; return "member" === (null === x || void 0 === x || null === (t = x.inTheme) || void 0 === t || null === (n = t.iconSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === h || void 0 === h ? void 0 : h[null === x || void 0 === x || null === (o = x.inTheme) || void 0 === o || null === (i = o.iconSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === u || void 0 === u ? void 0 : u[null !== (c = null === x || void 0 === x || null === (d = x.inTheme) || void 0 === d || null === (m = d.iconSection) || void 0 === m || null === (p = m[e]) || void 0 === p || null === (f = p.fieldInfo) || void 0 === f ? void 0 : f.id) && void 0 !== c ? c : ""] }, we = e => { var t, n, r, a, o, i, l, s, c, d, m, p, f; let { index: v } = e; const g = "member" === (null === x || void 0 === x || null === (t = x.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[v]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? h : u; return (0, B.jsx)(H.A, { objectId: null === g || void 0 === g ? void 0 : g.id, field: null === x || void 0 === x || null === (o = x.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[v]) || void 0 === l ? void 0 : l.fieldInfo, value: ye(v), fontDetails: null === x || void 0 === x || null === (s = x.inTheme) || void 0 === s || null === (c = s.mainSection) || void 0 === c || null === (d = c[v]) || void 0 === d ? void 0 : d.fieldStyle, section: null === x || void 0 === x || null === (m = x.inTheme) || void 0 === m || null === (p = m.mainSection) || void 0 === p || null === (f = p[v]) || void 0 === f ? void 0 : f.fieldSection, orgId: de }) }, ze = e => { var t, n, r, a, o, i, l, s, c, d, m, p, f; let { index: v } = e; const g = "member" === (null === x || void 0 === x || null === (t = x.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[v]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? h : u; return (0, B.jsx)(H.A, { objectId: null === g || void 0 === g ? void 0 : g.id, field: null === x || void 0 === x || null === (o = x.inTheme) || void 0 === o || null === (i = o.iconSection) || void 0 === i || null === (l = i[v]) || void 0 === l ? void 0 : l.fieldInfo, value: be(v), fontDetails: null === x || void 0 === x || null === (s = x.inTheme) || void 0 === s || null === (c = s.iconSection) || void 0 === c || null === (d = c[v]) || void 0 === d ? void 0 : d.fieldStyle, section: null === x || void 0 === x || null === (m = x.inTheme) || void 0 === m || null === (p = m.iconSection) || void 0 === p || null === (f = p[v]) || void 0 === f ? void 0 : f.fieldSection, orgId: de }) }, xe = e => { const t = null === e || void 0 === e ? void 0 : e.find((e => e.id === y)); return t ? t.value : "" }, Ae = null !== me && void 0 !== me && me.showRoleColorAsBannerColor && ve && "#ffffff" !== ve && "#FFFFFF" !== ve ? ve : (null === me || void 0 === me ? void 0 : me.color) || "#00ACC0"; return (0, B.jsxs)(W, { width: se || 490, height: "100%", style: M ? { border: "1px solid #d5d5d5" } : { border: "0px solid #d5d5d5" }, children: [!M && (0, B.jsx)(oe, { color: "inherit", onClick: ce, children: (0, B.jsx)(_.gF, { icon: "Close", size: "lg" }) }), (0, B.jsx)(U, { children: (0, B.jsxs)(q, { width: "100%", children: [(0, B.jsx)(z.A, { width: "100%", height: "160px", bgcolor: Ae }), (0, B.jsxs)(G, { boxShadow: 1, children: [p && (0, B.jsx)(O.A, { handleLeftClick: f, handleRightClick: v }), !M && ie && (0, B.jsx)(A.Ay, { item: !0, children: (0, B.jsx)(ae, { color: "primary", onClick: le, children: (0, B.jsx)(_.gF, { icon: "Edit", size: "lg", color: N.Qs.Neutrals[0] }) }) }), (0, B.jsx)(K, { style: p ? { paddingLeft: 45, paddingRight: 45 } : {}, children: (0, B.jsx)(T.A, { size: (null === pe || void 0 === pe || null === (o = pe.standard) || void 0 === o ? void 0 : o.size) || 100, shape: null === pe || void 0 === pe ? void 0 : pe.shape, person: h, role: u }) }), (0, B.jsxs)(Z, { children: [ye(0) && (0, B.jsx)(Y, { children: (0, B.jsx)(we, { index: 0 }) }), ye(1) && (0, B.jsx)(X, { children: (0, B.jsx)(we, { index: 1 }) }), ye(2) && (0, B.jsx)($, { children: (0, B.jsx)(we, { index: 2 }) }), (0, B.jsx)(Q, { children: null === x || void 0 === x || null === (i = x.inTheme) || void 0 === i || null === (l = i.iconSection) || void 0 === l ? void 0 : l.map(((e, t) => (0, B.jsx)(ze, { index: t }, t))) }), (null === me || void 0 === me ? void 0 : me.showLegends) && !M && (0, B.jsx)(z.A, { display: "flex", py: 2, justifyContent: "flex-start", children: (0, B.jsx)(j.n, { role: u, person: h }) })] })] })] }) }), (0, B.jsxs)(re, { children: [(0, B.jsxs)(J, { onClick: () => ge("data"), boxShadow: 1, children: [(0, B.jsxs)(z.A, { p: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: [(0, B.jsx)(ee, { variant: "h6", children: "Data" }), "data" !== ue && (0, B.jsx)(C.A, { color: "primary" })] }), (0, B.jsx)(k.A, { in: "data" === ue, unmountOnExit: !0, children: null === w || void 0 === w || null === (s = w.inTheme) || void 0 === s || null === (c = s.filter((e => !(null !== e && void 0 !== e && e.hidden || !M && (e => { var t, n; return void 0 === (null === e || void 0 === e || null === (t = e.fieldStyle) || void 0 === t ? void 0 : t.hideEmptyField) || (null === e || void 0 === e || null === (n = e.fieldStyle) || void 0 === n ? void 0 : n.hideEmptyField) })(e) && (0, R.i6)(e, h, u))))) || void 0 === c ? void 0 : c.map(((e, t) => { var n, r, a, o, i; const l = "member" === (null === e || void 0 === e || null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.model) ? h : u; return (0, B.jsxs)(te, { p: 2, display: "flex", flexDirection: "column", children: [(0, B.jsxs)(F.A, { fontSize: 14, color: N.Qs.Neutrals[800], fontWeight: 600, children: [null === e || void 0 === e || null === (r = e.fieldInfo) || void 0 === r ? void 0 : r.label, ":"] }), (0, B.jsx)(ne, { children: (0, B.jsx)(H.A, { objectId: null === l || void 0 === l ? void 0 : l.id, field: null === e || void 0 === e ? void 0 : e.fieldInfo, value: M ? "Sample value" : "member" === (null === e || void 0 === e || null === (a = e.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === h || void 0 === h ? void 0 : h[null === e || void 0 === e || null === (o = e.fieldInfo) || void 0 === o ? void 0 : o.id] : null === u || void 0 === u ? void 0 : u[null === e || void 0 === e || null === (i = e.fieldInfo) || void 0 === i ? void 0 : i.id], fontDetails: null === e || void 0 === e ? void 0 : e.fieldStyle, section: null === e || void 0 === e ? void 0 : e.fieldSection, orgId: de }, t) })] }, t) })) })] }), !d && (M || (m || []).length > 0) && (0, B.jsxs)(J, { onClick: () => ge("roles"), boxShadow: 1, children: [(0, B.jsxs)(z.A, { p: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: [(0, B.jsx)(ee, { children: "Roles" }), "roles" !== ue && (0, B.jsx)(C.A, { color: "primary" })] }), (0, B.jsx)(k.A, { in: "roles" === ue, unmountOnExit: !0, children: (0, B.jsx)(z.A, { p: 2, children: M ? (0, B.jsx)(E.A, { children: " Role 1" }) : (0, B.jsxs)(B.Fragment, { children: [fe && (0, B.jsx)(z.A, { mt: 1, mb: 2, children: (0, B.jsx)(I.A, { active: u.id, name: u[y], chartName: u.chartName, id: u.id, chartId: u.chartId, orgId: u.orgId, isClickable: !1 }) }), null === m || void 0 === m ? void 0 : m.map((e => (0, B.jsx)(I.A, { active: (null === e || void 0 === e ? void 0 : e.id) === (null === u || void 0 === u ? void 0 : u.id), name: xe(e.fields), chartName: null === e || void 0 === e ? void 0 : e.chartName, id: null === e || void 0 === e ? void 0 : e.id, chartId: null === e || void 0 === e ? void 0 : e.chartId, orgId: null === e || void 0 === e ? void 0 : e.orgId, isClickable: !0 }, null === e || void 0 === e ? void 0 : e.id)))] }) }) })] }), (0, B.jsxs)(J, { onClick: () => ge("connections"), boxShadow: 1, children: [(0, B.jsxs)(z.A, { p: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: [(0, B.jsx)(ee, { children: "Connections" }), "connections" !== ue && (0, B.jsx)(C.A, { color: "primary" })] }), (0, B.jsx)(k.A, { in: "connections" === ue, unmountOnExit: !0, children: (0, B.jsx)(z.A, { p: 2, children: M ? (0, B.jsx)(E.A, { children: " Connection 1" }) : (0, B.jsx)(B.Fragment, { children: (0, B.jsx)(L.Ay, { isMobile: d, role: u, connections: g }) }) }) })] })] })] }) } }, 8622: (e, t, n) => { "use strict";
                n.d(t, { A: () => we }); var r, a = n(57528),
                    o = n(65043),
                    i = n(96364),
                    l = n(64759),
                    s = n(70567),
                    c = n(61531),
                    d = n(18885),
                    u = n(14370),
                    h = n(59691),
                    m = n(40454),
                    p = n(9989),
                    f = n(44235),
                    v = n(38325),
                    g = n(86852),
                    y = n(84),
                    b = n(49092),
                    w = n(49157),
                    z = n(5387),
                    x = n(14556),
                    A = n(85725),
                    k = n(85657),
                    S = n(77454),
                    M = n(79043),
                    E = n(91688),
                    C = n(72703),
                    T = n(10621),
                    H = n(42517),
                    L = n(75156),
                    I = n(72119),
                    j = n(23993),
                    V = n(45256),
                    O = n(24241),
                    R = n(80286),
                    P = n(96446),
                    D = n(70579); const F = (0, I.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { fontDetails: t, tableTheme: n } = e; return "\n    color: ".concat(null === t || void 0 === t ? void 0 : t.color, ";\n    font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n    font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n    font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n    text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n    text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n    font-family:  ").concat(null === n || void 0 === n ? void 0 : n.fontFamily, ", ").concat(V.n[null === n || void 0 === n ? void 0 : n.fontFamily] || "sans-serif", ";\n  ") })),
                    N = e => { let { item: t, cell: n, value: r, objectId: a, orgId: o, fontDetails: i, tableTheme: l } = e; const s = (n.displayType || n.type || "").toLowerCase(),
                            c = (null === n || void 0 === n ? void 0 : n.isDefault) && "departmentRole" === (null === n || void 0 === n ? void 0 : n.name),
                            d = (0, x.d4)(j.KO); if (c) return (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: null === t || void 0 === t ? void 0 : t.department }); if ("string" === s) { if (null !== l && void 0 !== l && l.showPeople && "role" === (null === n || void 0 === n ? void 0 : n.model) && "name" === (null === n || void 0 === n ? void 0 : n.name)) { let e = d[t.memberId],
                                    n = e && e.join(", "); return (0, T.vK)(n) && (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: n }) } return (0, T.vK)(r) && (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }) } if ("number" === s) return (0, T.vK)(r) && (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }); if ("url" === s) return (0, T.x7)(r) && (0, D.jsx)("a", { href: r, target: "_blank", rel: "noopener noreferrer", children: (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: n.label }) }) || r && (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: n.label }) || ""; if ("email" === s) return (0, T.mb)(r) && (0, D.jsx)("a", { href: "mailto:".concat(r), children: (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: n.label }) }) || (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: n.label }); if ("phone" === s) return (0, T.vK)(r) && (0, D.jsx)("a", { href: "tel:".concat(r), target: "_blank", rel: "noopener noreferrer", children: (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }) }) || (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }); if ("attachment" === s) return r ? (0, D.jsx)(H.A, { orgId: o, model: n.model, name: n.attr, objectId: a, extension: null === r || void 0 === r ? void 0 : r.extension, label: n.label }) : ""; if ("boolean" === s) return (0, D.jsx)(L.Ay, { icon: "Checkbox", size: "lg", color: r ? null === i || void 0 === i ? void 0 : i.color : "#eeeeee" }); if ("switch" === s) return (0, D.jsx)(L.Ay, { icon: "Toggle", size: "lg", color: r ? null === i || void 0 === i ? void 0 : i.color : "#eeeeee", style: { fontSize: (null === i || void 0 === i ? void 0 : i.size) + "px" } }); if ("tags" === s) return (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: (0, T.Hc)(r) }); if ("date" === s) return (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: (0, T.Os)(r, null === i || void 0 === i ? void 0 : i.dateFormat) }); if ("computed" === s) { return O.c9.fromISO(r).isValid ? (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: (0, T.Os)(r, null === i || void 0 === i ? void 0 : i.dateFormat) }) : (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }) } return "location" === s ? (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }) : "currency" === s ? (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: (0, T.Jw)(r, null === n || void 0 === n || null === (u = n.typeMetadata) || void 0 === u ? void 0 : u.currency) }) : "rollup" === s ? (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r ? (0, T.$)(r, n) : "" }) : "iconpicklist" === s && r && R.Md[r] ? (0, D.jsxs)(P.A, { display: "flex", gap: 1, alignItems: "center", children: [(0, D.jsx)("img", { src: "".concat("https://assets-organimi.s3.amazonaws.com").concat(R.Md[r].path), width: 24 }), (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: R.Md[r].label })] }) : (0, D.jsx)(F, { fontDetails: i, tableTheme: l, variant: "body2", children: r }); var u }; var _, B = n(43331),
                    W = n(37869),
                    U = n(57546),
                    q = n(19367),
                    G = n(9368); const K = (0, I.Ay)(d.A)((e => { let { tableTheme: t } = e; return { "&:nth-of-type(odd)": { backgroundColor: null === t || void 0 === t ? void 0 : t.cellBackgroundColor }, "&:nth-of-type(even)": { backgroundColor: null !== t && void 0 !== t && t.alternateCellBackground ? null === t || void 0 === t ? void 0 : t.alternateCellBackgroundColor : null === t || void 0 === t ? void 0 : t.cellBackgroundColor }, "&:last-child td, &:last-child th": { border: 0 } } })),
                    Z = (0, I.Ay)(C.A)(_ || (_ = (0, a.A)(["\n  ", "\n"])), (e => { let { fontDetails: t, tableTheme: n } = e; return "\n    color: ".concat(null === t || void 0 === t ? void 0 : t.color, ";\n    font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n    font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n    font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n    text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n    text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n    font-family:  ").concat(null === n || void 0 === n ? void 0 : n.fontFamily, ", ").concat(V.n[null === n || void 0 === n ? void 0 : n.fontFamily] || "sans-serif", ";\n    a{\n      color: ").concat(null === t || void 0 === t ? void 0 : t.color, ";\n      font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n      font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n      font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n      text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n      font-family:  ").concat(null === n || void 0 === n ? void 0 : n.fontFamily, ", ").concat(V.n[null === n || void 0 === n ? void 0 : n.fontFamily] || "sans-serif", ";\n      text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n    }\n  ") })),
                    Y = e => { var t; let { badges: n, item: r, handleItemClick: a, headCells: o, person: i, role: l, tableTheme: s, photoTheme: c, contentTheme: d, key: u } = e; const h = (0, x.d4)(B.mn),
                            { model: m, key: p, query: f } = r.match || {},
                            v = e => e.model === T.A2.MEMBER && e.isDefault,
                            { resource: g } = (0, U.A)(),
                            y = (0, x.d4)(q.Jf),
                            b = !(0, G.A)() || (null === y || void 0 === y ? void 0 : y.showRoleDetails); let w = "".concat(null === l || void 0 === l ? void 0 : l.id, "-").concat(null === (t = r.member) || void 0 === t ? void 0 : t.id); return (0, D.jsxs)(K, { id: w, className: "directory-row", onClick: !g && b && a || void 0, tableTheme: s, children: [(null === s || void 0 === s ? void 0 : s.showLegend) && (0, D.jsx)(C.A, { padding: "checkbox", children: n }), o.map((e => { var t, n, a; const o = e => f && e.model === m && e.attr === p,
                                    u = "people-".concat(e.model, "-").concat(e.attr); var g, y, b, w, z; return "avatar" === e.name && null !== c && void 0 !== c && null !== (t = c.standard) && void 0 !== t && t.visible ? (0, D.jsx)(C.A, { padding: "default", children: (0, D.jsx)(W.A, { size: (null === c || void 0 === c || null === (g = c.standard) || void 0 === g ? void 0 : g.size) || 40, person: i, role: l, directory: !0 }) }, u) : e.name === T.x2.EMAIL && v(e) ? (0, D.jsx)(Z, { scope: "item", highlight: o({ model: "member", attr: e.attr }), fontDetails: null !== d && void 0 !== d && d.commonStyle ? d : null === e || void 0 === e ? void 0 : e.fontDetails, tableTheme: s, align: null === s || void 0 === s ? void 0 : s.align, children: (0, D.jsx)("a", { href: "mailto:".concat(null === (y = r[e.model]) || void 0 === y ? void 0 : y[e.attr]), target: "blank", children: null === (b = r[e.model]) || void 0 === b ? void 0 : b[e.attr] }) }, u) : e.name === T.x2.PHONE && v(e) ? (0, D.jsx)(Z, { scope: "item", highlight: o({ model: "member", attr: e.attr }), fontDetails: null !== d && void 0 !== d && d.commonStyle ? d : null === e || void 0 === e ? void 0 : e.fontDetails, tableTheme: s, align: null === s || void 0 === s ? void 0 : s.align, children: (0, D.jsx)("a", { href: "tel:".concat(null === (w = r[e.model]) || void 0 === w ? void 0 : w[e.attr]), children: null === (z = r[e.model]) || void 0 === z ? void 0 : z[e.attr] }) }, u) : (0, D.jsx)(Z, { scope: "item", highlight: o(e), fontDetails: null !== d && void 0 !== d && d.commonStyle ? d : null === e || void 0 === e ? void 0 : e.fontDetails, tableTheme: s, align: null === s || void 0 === s ? void 0 : s.align, children: (0, D.jsx)(N, { item: r, orgId: h, objectId: null === (n = r[e.model]) || void 0 === n ? void 0 : n.id, cell: e, value: null === (a = r[e.model]) || void 0 === a ? void 0 : a[e.attr], fontDetails: null !== d && void 0 !== d && d.commonStyle ? d : null === e || void 0 === e ? void 0 : e.fontDetails, tableTheme: s }) }, u) }))] }, u) }; var X, $, Q = n(94363),
                    J = n(77887),
                    ee = n(70318),
                    te = n(82244),
                    ne = n(12176),
                    re = n(26225),
                    ae = n(7743),
                    oe = n(1619),
                    ie = n(356),
                    le = n(60038),
                    se = n(64021),
                    ce = n(81635),
                    de = n(6124),
                    ue = n(65025),
                    he = n(94642),
                    me = n(89656),
                    pe = n(70494),
                    fe = n(91383),
                    ve = n(19924),
                    ge = n(31056); const ye = (0, I.Ay)(Q.A)(X || (X = (0, a.A)([""]))),
                    be = (0, I.Ay)(l.A)($ || ($ = (0, a.A)(["\n  ", "\n"])), (e => { let { tableTheme: t } = e; return "\n    background-color: ".concat(null === t || void 0 === t ? void 0 : t.headerBackgroundColor, ";\n    .MuiTableCell-head{\n      color: ").concat(null === t || void 0 === t ? void 0 : t.color, ";\n      font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n      font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n      font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n      text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n      text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n      font-family:  ").concat(null === t || void 0 === t ? void 0 : t.fontFamily, ", ").concat(V.n[null === t || void 0 === t ? void 0 : t.fontFamily] || "sans-serif", ";\n    }\n  ") })),
                    we = e => { var t; const { defaults: n } = { ...e, defaults: { ...e.defaults || {} } }; let { resource: r, base: a, resourceAction: C } = (0, E.useParams)();
                        C = C || n.resourceAction || "view", r = r || n.resource; const { t: H } = (0, b.B)(), I = (0, s.A)(), { openDialog: j } = (0, y.A)("newRole"), [V, O] = (0, o.useState)(0), [R, P] = (0, o.useState)(25), F = "printPreview" === C, N = "print" === a, _ = N || F, B = (0, x.d4)(re.BN), W = (0, x.d4)(re.FH), U = (0, x.d4)(re.Zx), q = (0, x.d4)(ae.gJ), G = (0, x.d4)(me.ZM), K = (0, x.d4)(re.oL), { printSettings: { chartElements: { legend: { visible: Z, position: X = "", ownPage: $ } } } } = (0, he.A)(), Q = (0, x.d4)(me.P0), { table: we, directoryPhotos: ze, content: xe } = Q.base, { directory: Ae } = Q.data, ke = _ && Z && !$ && (null === U || void 0 === U || null === (t = U.rules) || void 0 === t ? void 0 : t.length) > 0, Se = (0, x.d4)(re.Sr), { updatePageReady: Me } = (0, de.A)(), Ee = (0, ue.A)(), { order: Ce, groupBy: Te, query: He, handleGroupByChange: Le, createSortByHandler: Ie } = Ee, je = (N || F) && (null === Ae || void 0 === Ae ? void 0 : Ae.orderBy) || (null === Ee || void 0 === Ee ? void 0 : Ee.orderBy), Ve = (0, x.d4)(re.Wf), Oe = (0, x.wA)(), { data: Re, printData: Pe, selectedGroup: De, groups: Fe, setSelectedGroup: Ne, tableHeadersRef: _e, legendsElemRef: Be } = (0, pe.A)(); let We = Re;
                        _ && (We = Pe); const Ue = (0, o.useCallback)((e => { var t, n, r; return !He && "" !== He || oe.A.filterItem(e, He, [null === (t = q[T.x2.FIRSTNAME]) || void 0 === t ? void 0 : t.id, null === (n = q[T.x2.LASTNAME]) || void 0 === n ? void 0 : n.id, null === (r = q[T.x2.LINKEDIN]) || void 0 === r ? void 0 : r.id]) }), [He]),
                            [qe, Ge] = (0, o.useMemo)((() => { const e = We.filter(Ue),
                                    t = _ ? e.length : V * R + R; return [e.slice(V * R, t), e.length] }), [We, V, R, je, Ue, a, G]);
                        (0, o.useLayoutEffect)((() => { O(0) }), [R, Te]); const Ke = e => () => { Ne(e), O(0), r && !_ && ie.A.trackEvent({ eventName: "".concat(r.toUpperCase, "_GROUPBY_CLICK"), extraParams: e && e.title ? e.title : "None" }) },
                            Ze = async () => { Le(null) }, Ye = () => { j({ allowChangeManager: !0 }) };
                        (0, o.useEffect)((() => { r && ie.A.trackEvent({ eventName: "".concat(r.toUpperCase(), "_OPEN") }) }), []), (0, o.useEffect)((() => { N || F || Me() }), []); const { rules: Xe = [] } = "directory" === r ? U || {} : ("printPreview" === Se || "print" === Se) && Z && U || {}, $e = e => { var t; const n = W[null === (t = e.role) || void 0 === t ? void 0 : t.id]; return (0, D.jsx)(c.A, { display: "flex", justifyContent: "center", children: Xe.filter((t => { var r; let { id: a } = t; var o; return n[a] && n[a][null === (r = e.member) || void 0 === r ? void 0 : r.id] ? n[a][null === (o = e.member) || void 0 === o ? void 0 : o.id] : !0 === n[a] })).map((e => { const { color: t, icon: n, label: r } = e; return (0, D.jsx)(ge.FV, { shape: K, margin: "2px", color: t, label: r, icon: n }, "badge-".concat(r)) })) }) }, Qe = { circle: "Circle", square: "Square", stretch: "Square" }, Je = (0, x.d4)(ve.hK), et = e => { let { children: t } = e; return (0, D.jsxs)(c.A, { m: _ ? 0 : 1, flex: 1, children: [(0, D.jsxs)(ye, { className: "directory", size: null !== we && void 0 !== we && we.densePadding ? "small" : "medium", tableTheme: we, children: [(null === we || void 0 === we ? void 0 : we.showLegend) && ke && X.includes("top") && (0, D.jsx)(l.A, { id: "directoryViewTableHeader_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_")), children: (0, D.jsxs)(d.A, { ref: Be, children: [(0, D.jsx)(J.A, { children: null !== U && void 0 !== U && U.title ? U.title : "Legend" }), (0, D.jsx)(J.A, { colSpan: G.length, align: "start", children: (0, D.jsx)(c.A, { display: "flex", flexDirection: "row", children: (0, D.jsx)(c.A, { display: "flex", flexDirection: "row", children: null === U || void 0 === U ? void 0 : U.rules.map((e => (0, D.jsx)(c.A, { ml: 1, children: (0, D.jsx)(fe.A, { rule: e, isPrint: !0, shape: Qe[Je.chartOptions.legend.badgeShape] || "Circle" }, null === e || void 0 === e ? void 0 : e.id) }))) }) }) })] }) }, "directoryViewTableHeaderKey_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_"))), (null === we || void 0 === we ? void 0 : we.showHeader) && (0, D.jsx)(be, { tableTheme: we, id: "directoryViewTableHeader_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_")), children: (0, D.jsxs)(d.A, { ref: _e, id: "directoryViewTableHeaderRow_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_")), children: [(null === we || void 0 === we ? void 0 : we.showLegend) && (0, D.jsx)(J.A, { align: "center" }, "people-head-badges"), G.map((e => !_ && e.id && e.sortable ? (0, D.jsx)(J.A, { align: (null === we || void 0 === we ? void 0 : we.align) || "left", children: (0, D.jsx)(u.A, { onClick: Ie(e.attr, e.model), direction: Ce, children: ["role.team", "role.function"].includes(null === e || void 0 === e ? void 0 : e.name) ? "role.team" === (null === e || void 0 === e ? void 0 : e.name) ? (null === Ve || void 0 === Ve ? void 0 : Ve.matrixSideLabel) || e.label : (null === Ve || void 0 === Ve ? void 0 : Ve.matrixTopLabel) || e.label : e.label }) }, "people-head-".concat(e.id)) : (0, D.jsx)(J.A, { align: (null === we || void 0 === we ? void 0 : we.align) || "left", children: e.label }, e.name)))] }, "directoryViewTableHeaderRowKey_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_"))) }, "directoryViewTableHeaderKey_".concat(We.map((e => { var t; return null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id })).join("_"))), t.length > 0 && (0, D.jsx)(h.A, { children: t }), (null === we || void 0 === we ? void 0 : we.showLegend) && ke && X.includes("bottom") && (0, D.jsx)(l.A, { children: (0, D.jsxs)(d.A, { ref: Be, children: [(0, D.jsx)(J.A, { children: null !== U && void 0 !== U && U.title ? U.title : "Legend" }), (0, D.jsx)(J.A, { colSpan: G.length, align: "start", children: (0, D.jsx)(c.A, { display: "flex", flexDirection: "row", children: (0, D.jsx)(c.A, { display: "flex", flexDirection: "row", children: null === U || void 0 === U ? void 0 : U.rules.map(((e, t) => (0, D.jsx)(c.A, { ml: t > 0 ? 1.5 : 0, children: (0, D.jsx)(fe.A, { rule: e, isPrint: !0, shape: Qe[Je.chartOptions.legend.badgeShape] || "Circle" }, e.id) }))) }) }) })] }) })] }), 0 === t.length && (0, D.jsx)(m.A, { container: !0, justifyContent: "center", alignItems: "center", style: { marginTop: 32 }, children: (0, D.jsx)(se.A, { text: H("General.Text.NothingHere"), image: le.A, handleClick: Ye, buttonText: "New Role", isButtonVisible: !1 }) })] }) }; return (0, D.jsx)(D.Fragment, { children: (0, D.jsxs)(M.A, { container: !0, justifyContent: "flex-start", isPrintScreen: N, isPrintPreview: F, children: [Te && !_ && (0, D.jsx)(c.A, { border: 1, borderTop: 0, borderLeft: 0, borderBottom: 0, borderColor: "grey.200", style: { backgroundColor: I.palette.grey[100] }, clone: !0, children: (0, D.jsx)(w.A, { item: !0, width: 375, maxOnly: "department" !== (null === Te || void 0 === Te ? void 0 : Te.attr), children: (0, D.jsx)(c.A, { pl: 3, clone: !0, children: (0, D.jsxs)(A.A, { container: !0, justifyContent: "flex-start", direction: "column", children: [(0, D.jsxs)(c.A, { pt: 1, mr: 3, children: [(0, D.jsxs)(m.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, D.jsx)(c.A, { pl: 2, children: (0, D.jsx)(i.A, { variant: "subtitle1", display: "inline", children: (null === Te || void 0 === Te ? void 0 : Te.label) || "" }) }), (0, D.jsx)(c.A, { ml: 2, children: (0, D.jsx)(ee.A, { onClick: Ze, children: (0, D.jsx)(L.Ay, { icon: "Close" }) }) })] }), (0, D.jsx)(te.A, { weight: 1, orientation: "hors" })] }), (0, D.jsx)(k.A, { item: !0, xs: !0, children: (0, D.jsx)(c.A, { mr: 3, children: (0, D.jsxs)(p.A, { className: "content", children: [0 === Fe.length && (0, D.jsxs)(c.A, { ml: 2, children: [(0, D.jsxs)(i.A, { variant: "body1", color: "inherit", children: ['"Group by ', null === Te || void 0 === Te ? void 0 : Te.title, '"'] }), (0, D.jsx)(i.A, { variant: "body2", color: "inherit", children: "No groups found" }), (0, D.jsx)(i.A, { display: "block", variant: "body2", children: (0, D.jsx)("a", { onClick: Ze, children: "Clear groups" }) })] }), Fe.map((e => (0, D.jsx)(z.A, { button: !0, onClick: Ke(e), selected: (null === De || void 0 === De ? void 0 : De.id) === e.id, children: (0, D.jsxs)(m.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, D.jsx)(ne.A, { variant: "body1", color: "inherit", children: e.name }), (0, D.jsx)(g.A, { spacing: { right: 1 }, children: (0, D.jsx)(f.A, { badgeContent: e.peopleCount || 0, color: "primary", overlap: "rectangular" }) })] }) }, e.id)))] }) }) })] }) }) }) }), (0, D.jsxs)(S.A, { item: !0, xs: !0, isPrintScreen: N, style: { display: "flex", flexDirection: "column" }, children: ["directory" === r ? (0, D.jsx)(et, { children: qe.map((e => { var t, n, r, a, o, i, l; return (0, D.jsx)(Y, { role: null === e || void 0 === e ? void 0 : e.role, person: null === e || void 0 === e ? void 0 : e.member, headCells: G, colors: B[null === e || void 0 === e || null === (t = e.role) || void 0 === t ? void 0 : t.id], badges: $e(e), item: e, handleItemClick: !_ && (i = null === e || void 0 === e || null === (a = e.member) || void 0 === a ? void 0 : a.id, l = null === e || void 0 === e || null === (o = e.role) || void 0 === o ? void 0 : o.id, () => { Oe((0, ce.gN)({ activePersonId: i, mode: "view", selectedRoleId: l })) }), tableTheme: we, photoTheme: ze, contentTheme: xe }, "directoryRow_".concat(null === e || void 0 === e || null === (n = e.member) || void 0 === n ? void 0 : n.id, "_").concat(null === e || void 0 === e || null === (r = e.role) || void 0 === r ? void 0 : r.id)) })) }) : (0, D.jsx)(et, { children: qe }), Ge > 0 && !_ && (0, D.jsx)(c.A, { position: "sticky", left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", borderTop: "solid 1px #ddd", children: (0, D.jsx)(v.A, { rowsPerPageOptions: [25, 50, 100, 250], component: "div", count: Ge, rowsPerPage: R, page: V, backIconButtonProps: { "aria-label": H("Common.Tables.backIconButtonText") }, nextIconButtonProps: { "aria-label": H("Common.Tables.nextIconButtonText") }, onPageChange: (e, t) => { O(t) }, onRowsPerPageChange: e => { P(parseInt(e.target.value, 10)), O(0) }, labelRowsPerPage: H("Common.Tables.labelRowsPerPage"), backIconButtonText: H("Common.Tables.backIconButtonText"), nextIconButtonText: H("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: t, to: n, count: r } = e; return "".concat(t, "-").concat(n, " ").concat(H("Common.Tables.of"), " ").concat(r) } }) })] })] }) }) } }, 80700: (e, t, n) => { "use strict";
                n.d(t, { _: () => Ge }); var r, a = n(65043),
                    o = n(57528),
                    i = n(40454),
                    l = n(61531),
                    s = n(72119),
                    c = n(96364),
                    d = n(10621),
                    u = n(75156),
                    h = n(14556),
                    m = n(23993),
                    p = n(70579); const f = (0, s.Ay)(c.A)(r || (r = (0, o.A)(["\n  ", "\n"])), (e => { let { fontDetails: t } = e; return "\n    color: ".concat(null === t || void 0 === t ? void 0 : t.color, ";\n    line-height:1.2;\n    font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n    font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n    font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n    text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n    text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n  ") })),
                    v = e => { let { item: t, field: n, value: r, fontDetails: a, photoboardTheme: o, section: i } = e; const l = ((null === n || void 0 === n ? void 0 : n.displayType) || (null === n || void 0 === n ? void 0 : n.type) || "").toLowerCase(),
                            s = (0, h.d4)(m.KO),
                            c = e => { null === e || void 0 === e || e.stopPropagation() }; if ("string" === l) { if (null !== o && void 0 !== o && o.showPeople && "role" === (null === n || void 0 === n ? void 0 : n.model) && "name" === (null === n || void 0 === n ? void 0 : n.name)) { let e = s[t.memberId],
                                    n = e && e.join(", "); return (0, d.vK)(n) && n && (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: n }) } return (null === n || void 0 === n ? void 0 : n.name) === d.x2.PHONE && r ? "icon-section" !== i && "hover-section" !== i ? (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: r }) : (0, p.jsx)("a", { onClick: c, href: "tel:".concat(r), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "PhoneSolid", size: "lg", color: null === a || void 0 === a ? void 0 : a.color }) }) : (null === n || void 0 === n ? void 0 : n.name) === d.x2.EMAIL && r ? "icon-section" !== i && "hover-section" !== i ? (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: r }) : (0, p.jsx)("a", { onClick: c, href: "mailto:".concat(r), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "Email", size: "lg", color: null === a || void 0 === a ? void 0 : a.color }) }) : r ? (0, d.vK)(r) && (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: r }) : (0, p.jsx)(p.Fragment, {}) } return "url" === l && r ? "icon-section" !== i && "hover-section" !== i ? (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: r }) : (0, d.x7)(r) && (null === n || void 0 === n ? void 0 : n.name) === d.x2.LINKEDIN && (0, p.jsx)("a", { onClick: c, href: r, target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "LinkedInInverse", size: "lg", color: null === a || void 0 === a ? void 0 : a.color }) }) : (0, p.jsx)(f, { fontDetails: a, variant: "body2", children: r }) }; var g, y, b = n(78300),
                    w = n(80539),
                    z = n(96382),
                    x = n(89656); const A = 90,
                    k = (0, s.Ay)(w.A)(g || (g = (0, o.A)(["\n", "\n"])), (e => { let { shape: t } = e; return "\n    border-radius: ".concat(t, ";\n") })),
                    S = (0, s.Ay)(b.A)(y || (y = (0, o.A)(["\n", "\n"])), (e => { let { shape: t } = e; return "\n    border-radius: ".concat(t, ";\n") })),
                    M = e => { let { size: t = A, shape: n = "circular", person: r, role: a } = e; const o = (0, h.d4)(x.tS),
                            i = (0, h.d4)(x.Ud); let s, c, m; switch (n) {
                            case "circular":
                            default:
                                m = "9999px"; break;
                            case "oval":
                                m = "16px"; break;
                            case "square":
                                m = "0px" } if (r) { const e = (0, d.II)(r),
                                n = r.name || (e ? "".concat(e, " ").concat((0, d.US)(r)) : "");
                            s = (0, p.jsx)(k, { shape: m, width: t, height: t, src: o, name: n, overrideColor: r.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") }, onError: e => { e.target.setAttribute("processed", "true"), e.target.setAttribute("href", z.A) } } }), c = (0, p.jsx)(k, { shape: m, width: t, height: t, src: r.photo || o, name: n, overrideColor: r.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") } }, children: s }) } else if (a) { const { type: e } = a || {};
                            c = /single|assistant|shared/i.test(e) ? (0, p.jsx)(k, { shape: m, width: t, height: t, src: i, name: "va", imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") } }, children: s }) : (0, p.jsx)(S, { shape: m, width: t, height: t, name: "ii", imgProps: { onLoad: e => { e.target.setAttribute("processed", "true") } }, children: (0, p.jsx)(u.Ay, { icon: "Building", size: "x3" }) }) } return (0, p.jsx)(l.A, { position: "relative", children: c }) }; var E, C, T = n(13020); const H = (0, s.Ay)("div")(E || (E = (0, o.A)(["\n  height: 100%;\n  cursor: pointer;\n  text-align: center;\n"]))),
                    L = s.Ay.ul(C || (C = (0, o.A)(["\n  ", "\n"])), (e => { var t; let { photoSize: n } = e; return "\n    display: grid;\n    list-style: none;\n    margin: 0;\n    padding: 20px;\n    grid-template-columns: repeat(auto-fit, minmax(".concat(null === (t = (0, T.o)("layout-0", n)) || void 0 === t ? void 0 : t.width, "px, 1fr));\n    \n    li {\n      display: list-item;\n      text-align: center;\n    }\n    .items-center {\n      column-gap: 1.5rem;\n      align-items: center;\n      display: flex;\n    }\n    .rounded-full {\n      vertical-align: middle;\n    }\n    .text-base {\n      letter-spacing: -0.025em;\n      line-height: 2rem;\n      margin: 0;\n      --tw-text-opacity: 1;\n      overflow-wrap: break-word;\n      width: 100%;\n    }\n    .text-sm {\n      --tw-text-opacity: 1;\n      color: rgb(79 70 229 / var(--tw-text-opacity));\n      line-height: 1.5rem;\n      font-weight: 600;\n      font-size: 0.875rem;\n      margin: 0;\n    }\n    .social-links{\n      column-gap: 1.5rem;\n      justify-content:center;\n      display: flex;\n      margin: 0;\n      padding: 0;\n      list-style: none;\n      li{\n        margin:0;\n        padding:0;\n        border-bottom: none;\n      }\n      a{\n        --tw-text-opacity: 1;\n        color: rgb(156 163 175 / var(--tw-text-opacity));\n        padding:0;\n        margin:0;\n      }\n    }\n  ") })),
                    I = e => { var t, n, r; let { items: a, theme: o, themeFieldsWithStyles: s, orgId: c, handleClick: d } = e; const u = null === o || void 0 === o || null === (t = o.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos; return (0, p.jsx)(L, { name: "layout-0", photoSize: null === u || void 0 === u || null === (r = u.standard) || void 0 === r ? void 0 : r.size, children: a.map((e => { var t, n, r, a, h, m, f, g, y; return (0, p.jsx)("li", { onClick: d && d(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), className: "item photoboard-item", children: (0, p.jsx)(i.A, { item: !0, children: (0, p.jsx)(l.A, { children: (0, p.jsx)(l.A, { p: "".concat(24, "px"), children: (0, p.jsx)(H, { children: (0, p.jsxs)(i.A, { container: !0, direction: "column", justifyContent: "flex-start", alignItems: "center", style: { gridGap: 12 }, children: [(0, p.jsx)("div", { className: "rounded-full", children: (0, p.jsx)(M, { size: (null === u || void 0 === u || null === (h = u.standard) || void 0 === h ? void 0 : h.size) || 100, shape: null === u || void 0 === u ? void 0 : u.shape, person: null === e || void 0 === e ? void 0 : e.member, role: e.role }) }), null === s || void 0 === s || null === (m = s.inTheme) || void 0 === m || null === (f = m.mainSection) || void 0 === f ? void 0 : f.map((t => { var n, r, a, i, l, s; return (0, p.jsx)("div", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: c, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (s = o.data) || void 0 === s ? void 0 : s.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), (0, p.jsx)("ul", { className: "social-links", children: null === s || void 0 === s || null === (g = s.inTheme) || void 0 === g || null === (y = g.iconSection) || void 0 === y ? void 0 : y.map((t => { var n, r, a, i, l, s, d, u, h; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: c, objectId: null === (i = e[null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (s = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === s ? void 0 : s[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }) })) })] }) }) }) }) }, "photoboardTile_".concat(null === (r = e.member) || void 0 === r ? void 0 : r.id, "_").concat(null === (a = e.role) || void 0 === a ? void 0 : a.id)) }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var j, V, O, R; const P = s.Ay.ul(j || (j = (0, o.A)(["\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  grid-gap: 16px;\n  list-style: none;\n  justify-content: space-around;\n  margin: 0;\n  padding: 20px;\n  li {\n    display: list-item;\n    text-align: -webkit-match-parent;\n  }\n  .items-center {\n    column-gap: 1.5rem;\n    display: flex;\n  }\n  .rounded-full {\n    vertical-align: middle;\n  }\n  .text-base {\n    font-size: 1rem;\n    line-height: 1.75rem;\n    font-weight: 600;\n    letter-spacing: -0.025em;\n    --tw-text-opacity: 1;\n    overflow-wrap: break-word;\n    margin: 0;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(79 70 229 / var(--tw-text-opacity));\n    line-height: 1.5rem;\n    font-weight: 600;\n    font-size: 0.875rem;\n    margin: 0;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    justify-content: flex-start;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n      border-bottom: none;\n    }\n    a {\n      --tw-text-opacity: 1;\n      color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    D = (0, s.Ay)(l.A)(V || (V = (0, o.A)(["\n  display: flex;\n  grid-gap: 16px;\n  ", "\n  padding: 16px 0;\n"])), (e => { let { photoSize: t } = e; return "\n    width: ".concat(3.15 * t, "px;\n  ") })),
                    F = (0, s.Ay)(l.A)(O || (O = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n    width: ".concat(t, "px;\n  ") })),
                    N = (0, s.Ay)(l.A)(R || (R = (0, o.A)(["\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n    width: ".concat(2.15 * t, "px;\n  ") })),
                    _ = e => { var t, n, r; let { items: a, theme: o, themeFieldsWithStyles: i, orgId: l, handleClick: s } = e; const c = null === o || void 0 === o || null === (t = o.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos,
                            d = (null === c || void 0 === c || null === (r = c.standard) || void 0 === r ? void 0 : r.size) || 80; return (0, p.jsx)(P, { name: "layout-1", photoSize: d, children: a.map((e => { var t, n, r, a, u, h; return (0, p.jsx)("li", { onClick: s && s(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), className: "item photoboard-item", children: (0, p.jsxs)(D, { photoSize: d, children: [(0, p.jsx)(F, { className: "rounded-full", photoSize: d, children: (0, p.jsx)(M, { size: d, shape: null === c || void 0 === c ? void 0 : c.shape, person: null === e || void 0 === e ? void 0 : e.member, role: e.role }) }), (0, p.jsxs)(N, { display: "flex", flexDirection: "column", gridGap: 16, photoSize: d, children: [null === i || void 0 === i || null === (r = i.inTheme) || void 0 === r || null === (a = r.mainSection) || void 0 === a ? void 0 : a.map((t => { var n, r, a, i, s, c; return (0, p.jsx)("div", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (c = o.data) || void 0 === c ? void 0 : c.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), (0, p.jsx)("ul", { className: "social-links", children: null === i || void 0 === i || null === (u = i.inTheme) || void 0 === u || null === (h = u.iconSection) || void 0 === h ? void 0 : h.map((t => { var n, r, a, i, s, c, d, u, h; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (i = e[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (c = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === c ? void 0 : c[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }) })) })] })] }) }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var B, W; const U = (0, T.o)("layout-2") || { width: 200 },
                    q = s.Ay.ul(B || (B = (0, o.A)(["\n  grid-template-columns: repeat(auto-fit, minmax(", 'px, 1fr));\n  max-width: none;\n  text-align: center;\n  row-gap: 16px;\n  column-gap: 16px;\n  display: grid;\n  list-style: none;\n  margin: 0;\n  padding: 20px;\n  li {\n    display: list-item;\n    text-align: -webkit-match-parent;\n  }\n  .rounded-full {\n    display: inline-block;\n    vertical-align: middle;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 100%;\n    margin-bottom: 14px;\n    position: relative;\n    overflow: hidden;\n    &:hover {\n      img {\n        transition: all 0.3s ease-out;\n        transform: scale(1.2);\n      }\n      img:after {\n        transition: all 0.3s ease-out;\n        transform: scale(1.2);\n      }\n      .MuiAvatar-root:after {\n        background: #333;\n        content: "";\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        transition: all 0.3s ease-out;\n        transform: scale(1.2);\n        opacity: 0.6;\n      }\n    }\n    img {\n      display: inline-block;\n      transform: scale(1);\n      transition: all 0.3s ease-out;\n    }\n  }\n  .text-base {\n    line-height: 1.75rem;\n    letter-spacing: -0.025em;\n    --tw-text-opacity: 1;\n    overflow-wrap: break-word;\n    margin: 6px;\n    inline-size: inherit;\n    text-align: center;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.5rem;\n    font-size: 0.875rem;\n  }\n  .member_wrap {\n    transition: all 0.3s ease-in-out;\n    position: relative;\n    &:hover {\n      img {\n        transition: all 0.3s ease-out;\n        transform: scale(1.2);\n      }\n      .hidden_fields {\n        transition: all 0.3s ease-out;\n        opacity: 1;\n      }\n    }\n  }\n'])), U.width),
                    G = s.Ay.div(W || (W = (0, o.A)(["\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  transition: all 0.3s ease-in-out;\n  opacity: 0;\n  z-index: 10;\n  width: 100%;\n  ul {\n    list-style: none;\n    padding: 0;\n    transition: all 0.5s ease-in-out;\n    text-align: center;\n    li {\n      padding: 0 10px;\n      display: inline-block;\n      a {\n        color: #fff;\n        opacity: 1;\n      }\n    }\n  }\n"]))),
                    K = e => { var t, n; let { items: r, theme: a, themeFieldsWithStyles: o, orgId: i, handleClick: l } = e; const s = null === a || void 0 === a || null === (t = a.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos; return (0, p.jsx)(q, { name: "layout-2", children: r.map((e => { var t, n, r, c, d, u, h; return (0, p.jsx)("li", { onClick: l && l(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), className: "item photoboard-item", children: (0, p.jsxs)("div", { className: "member_wrap", children: [(0, p.jsxs)("div", { className: "rounded-full", children: [(0, p.jsx)(M, { size: (null === s || void 0 === s || null === (r = s.standard) || void 0 === r ? void 0 : r.size) || 80, shape: null === s || void 0 === s ? void 0 : s.shape, person: null === e || void 0 === e ? void 0 : e.member, role: e.role }), (0, p.jsx)(G, { className: "hidden_fields", children: (0, p.jsx)("ul", { children: null === o || void 0 === o || null === (c = o.inTheme) || void 0 === c || null === (d = c.hoverSection) || void 0 === d ? void 0 : d.map((t => { var n, r, o, l, s, c, d, u, h, m; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: i, objectId: null === (s = e[null === t || void 0 === t || null === (c = t.fieldInfo) || void 0 === c ? void 0 : c.model]) || void 0 === s ? void 0 : s.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (d = e[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.model]) || void 0 === d ? void 0 : d[null === t || void 0 === t || null === (h = t.fieldInfo) || void 0 === h ? void 0 : h.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === a || void 0 === a || null === (m = a.data) || void 0 === m ? void 0 : m.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }, null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.id) }) })) }) })] }), (0, p.jsx)("div", { style: { gridGap: 10, display: "grid" }, children: null === o || void 0 === o || null === (u = o.inTheme) || void 0 === u || null === (h = u.mainSection) || void 0 === h ? void 0 : h.map((t => { var n, r, o, l, s, c, d; return (0, p.jsx)("div", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: i, objectId: null === (r = e[null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.model]) || void 0 === r ? void 0 : r.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (l = e[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.model]) || void 0 === l ? void 0 : l[null === t || void 0 === t || null === (c = t.fieldInfo) || void 0 === c ? void 0 : c.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === a || void 0 === a || null === (d = a.data) || void 0 === d ? void 0 : d.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }, null === t || void 0 === t || null === (n = t.fieldInfo) || void 0 === n ? void 0 : n.id) }) })) })] }) }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var Z, Y, X, $; const Q = s.Ay.ul(Z || (Z = (0, o.A)(["\n  grid-auto-flow: dense;\n  display: grid;\n  justify-content: center;\n  margin-top: 5rem;\n  list-style: none;\n  margin: 0;\n  padding: 20px;\n  margin-left: auto;\n  margin-right: auto;\n  direction: ltr;\n  text-align: left;\n  grid-gap: 16px;\n  li {\n    display: list-item;\n    text-align: left;\n  }\n  .rounded-full {\n    // border-radius: 9999px;\n    display: inline-block;\n    vertical-align: middle;\n    // margin-left: auto;\n    // margin-right: auto;\n    // max-width: 100%;\n  }\n  .text-base {\n    line-height: 1.75rem;\n    letter-spacing: -0.025em;\n    --tw-text-opacity: 1;\n    margin: 0;\n    margin-top: 4px;\n    overflow-wrap: break-word;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    // color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.5rem;\n    // font-size: 0.875rem;\n    margin: 0;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    justify-content: flex-start;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    margin-top: 1rem;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n      border-bottom: none;\n    }\n    a {\n      --tw-text-opacity: 1;\n      // color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    J = (0, s.Ay)(l.A)(Y || (Y = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n  width: 100%;\n  height: ".concat(t, "px;\n  overflow: hidden;\n  padding: 16px 0;\n  background-color: #fff;\n  box-shadow: -2px 2px 4px 0.8px #eeeeee;\n  justify-content: space-between;\n  align-items: center;\n  border: solid 1px #d1d1d1;\n  ") })),
                    ee = (0, s.Ay)(l.A)(X || (X = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n  // background-color: #d8d8d8;\n  width: ".concat(t, "px;\n  display: flex;\n  align-items: center;\n  height: 100%;\n  \n  ") })),
                    te = (0, s.Ay)(l.A)($ || ($ = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n    background-color: #fff;\n    padding: ".concat(t > 200 ? "10px 30px" : "10px 20px", ";\n    width: ").concat(t, "px;\n    height: ").concat(t, "px;\n    overflow: auto;\n    vertical-align: middle;\n    display: flex;\n    flex-direction: column;\n    grid-gap: 8px;\n    justify-content: safe center;\n  ") })),
                    ne = e => { var t, n, r; let { items: a, theme: o, themeFieldsWithStyles: i, orgId: l, handleClick: s, classes: c } = e; const d = null === o || void 0 === o || null === (t = o.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos; return (0, p.jsx)(Q, { className: "".concat(c || "photoboard-items"), name: "layout-3", photoSize: (null === d || void 0 === d || null === (r = d.standard) || void 0 === r ? void 0 : r.size) || 80, children: a.map((e => { var t, n, r, a, c, u, h, m, f, g, y, b; return (0, p.jsx)("li", { className: "photoboard-item item", onClick: s && s(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), children: (0, p.jsxs)(J, { display: "flex", photoSize: (null === d || void 0 === d || null === (r = d.standard) || void 0 === r ? void 0 : r.size) || 80, children: [(0, p.jsx)(ee, { photoSize: (null === d || void 0 === d || null === (a = d.standard) || void 0 === a ? void 0 : a.size) || 80, justifyContent: "flex-start", children: (0, p.jsx)("div", { className: "rounded-full", children: (0, p.jsx)(M, { size: (null === d || void 0 === d || null === (c = d.standard) || void 0 === c ? void 0 : c.size) || 80, shape: "square", person: null === e || void 0 === e ? void 0 : e.member, role: e.role }) }) }), (0, p.jsxs)(te, { photoSize: (null === d || void 0 === d || null === (u = d.standard) || void 0 === u ? void 0 : u.size) || 80, children: [null === i || void 0 === i || null === (h = i.inTheme) || void 0 === h || null === (m = h.mainSection) || void 0 === m ? void 0 : m.map((t => { var n, r, a, i, s, c; return (0, p.jsx)("div", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (c = o.data) || void 0 === c ? void 0 : c.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), (null === i || void 0 === i || null === (f = i.inTheme) || void 0 === f || null === (g = f.iconSection) || void 0 === g ? void 0 : g.length) > 0 && (0, p.jsx)("ul", { className: "social-links", children: null === i || void 0 === i || null === (y = i.inTheme) || void 0 === y || null === (b = y.iconSection) || void 0 === b ? void 0 : b.map((t => { var n, r, a, i, s, c, d, u, h; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (i = e[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (c = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === c ? void 0 : c[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }) })) })] })] }) }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var re, ae, oe, ie; const le = s.Ay.ul(re || (re = (0, o.A)(["\n  ", "\n"])), (e => { var t; let { photoSize: n } = e; return "\n    row-gap: 1rem;\n    column-gap: 1rem;\n    grid-template-columns: repeat(auto-fit, minmax(".concat(null === (t = (0, T.o)("layout-4", n)) || void 0 === t ? void 0 : t.width, "px, 1fr));\n    display: grid;\n    list-style: none;\n    margin: 0;\n    padding: 20px;\n\n    @media (min-width: 1024px) {\n        margin-left: 0px;\n        margin-right: 0px;\n    }\n\n    @media (min-width: 1024px) {\n        max-width: none;\n    }\n\n    li{\n      display: list-item;\n      text-align: center;\n    }\n    .rounded-full{\n      display: inline-block;\n      vertical-align: middle;\n    }\n    .text-base{\n      line-height: 1.75rem;\n      letter-spacing: -0.025em;\n      --tw-text-opacity: 1;\n      margin:0;\n      overflow-wrap: break-word;\n    }\n    .text-sm{\n      --tw-text-opacity: 1;\n      line-height: 1.5rem;\n      margin:0;\n    }\n    .job-desc {\n      --tw-text-opacity: 1;\n      line-height: 1.75rem;\n      margin: 0;\n      margin-top: 8px;\n      text-align: left;\n    }\n    .social-links{\n      column-gap: 1rem;\n      justify-content:flex-start;\n      display: flex;\n      margin: 0;\n      padding: 0;\n      list-style: none;\n      li{\n        margin:0;\n        padding:0;\n        border-bottom: none;\n      }\n      a{\n        --tw-text-opacity: 1;\n        padding:0;\n        margin:0;\n      }\n    }\n  ") })),
                    se = (0, s.Ay)(l.A)(ae || (ae = (0, o.A)(["\n  background-color: #fff;\n  box-shadow: 1px 1px 6px 0px #d1d1d1;\n  justify-content: space-between;\n  padding: ", "px;\n  display: flex;\n  flex-direction: column;\n  grid-gap: 8px;\n"])), 20),
                    ce = (0, s.Ay)(l.A)(oe || (oe = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n  width: ".concat(t, "px;\n  display: flex;\n  align-items: center;\n  height: 100%;\n  ") })),
                    de = (0, s.Ay)(l.A)(ie || (ie = (0, o.A)(["\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n    background-color: #fff;\n    min-width: ".concat(1.2 * t, "px;\n    text-align: start;\n    display: flex;\n    flex-direction: column;\n    grid-gap: 12px;\n  ") })),
                    ue = e => { var t, n, r; let { items: a, theme: o, themeFieldsWithStyles: i, orgId: s, handleClick: c } = e; const d = null === o || void 0 === o || null === (t = o.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos; return (0, p.jsx)(le, { name: "layout-4", photoSize: (null === d || void 0 === d || null === (r = d.standard) || void 0 === r ? void 0 : r.size) || 80, children: a.map((e => { var t, n, r, a, u, h, m, f, g, y, b, w, z; return (0, p.jsx)("li", { onClick: c && c(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), className: "item photoboard-item", children: (0, p.jsxs)(se, { height: "100%", children: [(0, p.jsxs)(l.A, { display: "flex", gridGap: 16, alignItems: "center", children: [(0, p.jsx)(ce, { photoSize: (null === d || void 0 === d || null === (r = d.standard) || void 0 === r ? void 0 : r.size) || 80, justifyContent: "flex-start", children: (0, p.jsx)("div", { className: "rounded-full", children: (0, p.jsx)(M, { size: (null === d || void 0 === d || null === (a = d.standard) || void 0 === a ? void 0 : a.size) || 80, shape: null === d || void 0 === d ? void 0 : d.shape, person: null === e || void 0 === e ? void 0 : e.member, role: e.role }) }) }), (0, p.jsxs)(de, { photoSize: (null === d || void 0 === d || null === (u = d.standard) || void 0 === u ? void 0 : u.size) || 80, children: [null === i || void 0 === i || null === (h = i.inTheme) || void 0 === h || null === (m = h.mainSection) || void 0 === m ? void 0 : m.map((t => { var n, r, a, i, l, c; return (0, p.jsx)("h3", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: s, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (c = o.data) || void 0 === c ? void 0 : c.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), (null === i || void 0 === i || null === (f = i.inTheme) || void 0 === f || null === (g = f.iconSection) || void 0 === g ? void 0 : g.length) > 0 && (0, p.jsx)("ul", { className: "social-links", children: null === i || void 0 === i || null === (y = i.inTheme) || void 0 === y || null === (b = y.iconSection) || void 0 === b ? void 0 : b.map((t => { var n, r, a, i, l, c, d, u, h; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: s, objectId: null === (i = e[null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (c = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === c ? void 0 : c[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }) })) })] })] }), null === i || void 0 === i || null === (w = i.inTheme) || void 0 === w || null === (z = w.subSection) || void 0 === z ? void 0 : z.map((t => { var n, r, a, i, l, c; return (0, p.jsx)("p", { className: "job-desc", children: (0, p.jsx)(v, { item: e, orgId: s, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (l = t.fieldInfo) || void 0 === l ? void 0 : l.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (c = o.data) || void 0 === c ? void 0 : c.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }))] }) }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var he, me, pe, fe, ve = n(3523); const ge = s.Ay.ul(he || (he = (0, o.A)(["\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity));\n  row-gap: 3.5rem;\n  column-gap: 2rem;\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n  max-width: 42rem;\n  display: grid;\n  list-style: none;\n  margin: 0;\n  padding: 20px;\n  @media (min-width: 640px) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  @media (min-width: 1024px) {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n  @media (min-width: 1024px) {\n    max-width: none;\n  }\n  @media (min-width: 1024px) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  @media (min-width: 1280px) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  li {\n    display: list-item;\n  }\n  .rounded-full {\n    object-fit: cover;\n    border-radius: 1rem;\n    width: 10rem;\n    aspect-ratio: 14/13;\n    max-width: 100%;\n    height: 10rem;\n    display: block;\n    vertical-align: middle;\n  }\n  .text-base {\n    --tw-text-opacity: 1;\n    color: rgb(255 255 255 / var(--tw-text-opacity));\n    letter-spacing: -0.025em;\n    line-height: 2rem;\n    font-weight: 600;\n    font-size: 1.125rem;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(209 213 219 / var(--tw-text-opacity));\n    line-height: 1.75rem;\n    font-size: 1rem;\n  }\n  .location {\n    --tw-text-opacity: 1;\n    color: rgb(107 114 128 / var(--tw-text-opacity));\n    line-height: 1.5rem;\n    font-size: 0.875rem;\n    margin: 0;\n  }\n"]))),
                    ye = (0, s.Ay)(l.A)(me || (me = (0, o.A)([""]))),
                    be = (0, s.Ay)(l.A)(pe || (pe = (0, o.A)([""]))),
                    we = (0, s.Ay)(l.A)(fe || (fe = (0, o.A)([""]))),
                    ze = e => { let { items: t, handleClick: n } = e; const r = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.dj.LOCATIONADDRESS && e.model === d.A2.ROLE && e.isDefault))) || void 0 === t ? void 0 : t.value }; return (0, p.jsx)(ge, { name: "layout-5", children: t.map((e => { var t, a, o, i; return (0, p.jsxs)(ye, { display: "flex", flexDirection: "row", gridGap: 16, className: "item photoboard-item", children: [(0, p.jsx)(we, { children: (0, p.jsx)("img", { className: "rounded-full", src: (0, ve.K7)(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.photo), alt: "" }) }), (0, p.jsxs)(be, { onClick: n && n(null === e || void 0 === e || null === (a = e.member) || void 0 === a ? void 0 : a.id, null === e || void 0 === e || null === (o = e.role) || void 0 === o ? void 0 : o.id), display: "flex", flexDirection: "column", gridGap: 16, children: [(0, p.jsx)("p", { className: "text-sm", children: (0, d.mA)(e.role) }), (0, p.jsx)("p", { className: "location", children: r(null === e || void 0 === e || null === (i = e.role) || void 0 === i ? void 0 : i.fields) })] }, null === e || void 0 === e ? void 0 : e.roleId)] }) })) }) }; var xe; const Ae = s.Ay.ul(xe || (xe = (0, o.A)(["\n  list-style: none;\n  margin: 0px;\n  padding: 20px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity));\n  gap: 1.5rem;\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n  max-width: 42rem;\n  display: grid;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (min-width: 640px) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  @media (min-width: 1024px) {\n    max-width: none;\n    margin-left: 0px;\n    margin-right: 0px;\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n    gap: 2rem;\n  }\n\n  li {\n    display: list-item;\n    text-align: center;\n    padding-top: 2.5rem;\n    padding-bottom: 2.5rem;\n    padding-left: 2rem;\n    padding-right: 2rem;\n    --tw-bg-opacity: 1;\n    background-color: rgb(31 41 55 / var(--tw-bg-opacity));\n    border-radius: 1rem;\n  }\n  .rounded-full {\n    width: 14rem;\n    height: 14rem;\n    border-radius: 9999px;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 100%;\n    display: block;\n    vertical-align: middle;\n  }\n  .text-base {\n    --tw-text-opacity: 1;\n    color: rgb(255 255 255 / var(--tw-text-opacity));\n    letter-spacing: -0.025em;\n    line-height: 1.75rem;\n    font-weight: 600;\n    font-size: 1rem;\n    margin: 0;\n    margin-top: 1.5rem;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(156 163 175 / var(--tw-text-opacity));\n    line-height: 1.5rem;\n    font-size: 0.875rem;\n    margin: 0;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    justify-content: center;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    margin-top: 1.5rem;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n    }\n    a {\n      --tw-text-opacity: 1;\n      color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    ke = e => { let { items: t, handleClick: n } = e; const r = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.LINKEDIN && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            a = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.EMAIL && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            o = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.PHONE && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value }; return (0, p.jsx)(Ae, { name: "layout-6", children: t.map((e => { var t, i, l, s, c, h, m, f; return (0, p.jsxs)("li", { onClick: n && n(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (i = e.role) || void 0 === i ? void 0 : i.id), className: "item photoboard-item", children: [(0, p.jsx)("img", { className: "rounded-full", src: (0, ve.K7)(null === e || void 0 === e || null === (l = e.member) || void 0 === l ? void 0 : l.photo), alt: "" }), (0, p.jsx)("h3", { className: "text-base", children: null === e || void 0 === e || null === (s = e.member) || void 0 === s ? void 0 : s.name }), (0, p.jsx)("p", { className: "text-sm", children: (0, d.mA)(e.role) }), r(null === e || void 0 === e || null === (c = e.member) || void 0 === c ? void 0 : c.fields) && (0, p.jsxs)("ul", { className: "social-links", children: [(0, p.jsx)("li", { children: (0, p.jsx)("a", { href: r(null === e || void 0 === e || null === (h = e.member) || void 0 === h ? void 0 : h.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "LinkedInInverse", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: a(null === e || void 0 === e || null === (m = e.member) || void 0 === m ? void 0 : m.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "Email", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: "tel:".concat(o(null === e || void 0 === e || null === (f = e.member) || void 0 === f ? void 0 : f.fields)), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "PhoneSolid", size: "lg", color: "#9E9E9E" }) }) })] })] }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var Se; const Me = s.Ay.ul(Se || (Se = (0, o.A)(["\n  display: grid;\n  list-style: none;\n  margin: 0;\n  padding: 20px;\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 42rem;\n  column-gap: 1.5rem;\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n  row-gap: 5rem;\n\n  @media (min-width: 640px) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  @media (min-width: 1280px) {\n    grid-column: span 2 / span 2;\n  }\n  @media (min-width: 1024px) {\n    -moz-column-gap: 2rem;\n    column-gap: 2rem;\n  }\n  @media (min-width: 1024px) {\n    max-width: none;\n  }\n  @media (min-width: 1024px) {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n\n  li {\n    display: list-item;\n    text-align: -webkit-match-parent;\n  }\n  .rounded-full {\n    object-fit: cover;\n    border-radius: 1rem;\n    width: 100%;\n    aspect-ratio: 5/4;\n    max-width: 100%;\n    height: auto;\n    display: block;\n    vertical-align: middle;\n  }\n  .text-base {\n    font-size: 1.125rem;\n    line-height: 2rem;\n    font-weight: 600;\n    --tw-text-opacity: 1;\n    color: rgb(17 24 39 / var(--tw-text-opacity));\n    margin: 0;\n    margin-top: 1.5rem;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.75rem;\n    font-size: 1rem;\n    margin: 0;\n  }\n  .job-desc {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.75rem;\n    font-size: 1rem;\n    margin: 0;\n    margin-top: 1rem;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    margin-top: 1.5rem;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n    }\n    a {\n      --tw-text-opacity: 1;\n      color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    Ee = e => { let { items: t, handleClick: n } = e; const r = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.LINKEDIN && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            a = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.dj.DESCRIPTION && e.model === d.A2.ROLE && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            o = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.EMAIL && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            i = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.PHONE && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value }; return (0, p.jsx)(Me, { name: "layout-7", children: t.map((e => { var t, l, s, c, h, m, f, v, g; return (0, p.jsxs)("li", { onClick: n && n(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (l = e.role) || void 0 === l ? void 0 : l.id), className: "item photoboard-item", children: [(0, p.jsx)("img", { className: "rounded-full", src: (0, ve.K7)(null === e || void 0 === e || null === (s = e.member) || void 0 === s ? void 0 : s.photo), alt: "" }), (0, p.jsx)("h3", { className: "text-base", children: null === e || void 0 === e || null === (c = e.member) || void 0 === c ? void 0 : c.name }), (0, p.jsx)("p", { className: "text-sm", children: (0, d.mA)(e.role) }), (0, p.jsx)("p", { className: "job-desc", children: a(null === e || void 0 === e || null === (h = e.role) || void 0 === h ? void 0 : h.fields) }), r(null === e || void 0 === e || null === (m = e.member) || void 0 === m ? void 0 : m.fields) && (0, p.jsxs)("ul", { className: "social-links", children: [(0, p.jsx)("li", { children: (0, p.jsx)("a", { href: r(null === e || void 0 === e || null === (f = e.member) || void 0 === f ? void 0 : f.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "LinkedInInverse", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: o(null === e || void 0 === e || null === (v = e.member) || void 0 === v ? void 0 : v.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "Email", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: "tel:".concat(i(null === e || void 0 === e || null === (g = e.member) || void 0 === g ? void 0 : g.fields)), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "PhoneSolid", size: "lg", color: "#9E9E9E" }) }) })] })] }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var Ce, Te, He, Le; const Ie = s.Ay.ul(Ce || (Ce = (0, o.A)(["\n  list-style: none;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  flex-wrap: wrap;\n  padding-left: 16px;\n\n  .rounded-full {\n    object-fit: cover;\n    border-radius: 1rem;\n    flex: none;\n    aspect-ratio: 4/5;\n    display: block;\n    vertical-align: middle;\n  }\n  .text-base {\n    --tw-text-opacity: 1;\n    letter-spacing: -0.025em;\n    line-height: 2rem;\n    margin: 0;\n    overflow-wrap: break-word;\n    width: 100%;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    line-height: 1.75rem;\n    margin: 0;\n  }\n  .job-desc {\n    --tw-text-opacity: 1;\n    line-height: 1.75rem;\n    margin: 0;\n    text-align: justify;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    justify-content: flex-start;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n      border-bottom: none;\n    }\n    a {\n      --tw-text-opacity: 1;\n      color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    je = s.Ay.li(Te || (Te = (0, o.A)(["\n  flex-direction: row;\n  display: flex;\n  text-align: -webkit-match-parent;\n  border-bottom: 1px solid rgb(229 231 235);\n  padding: 16px 0;\n  flex: 1;\n  grid-gap: 16px;\n"]))),
                    Ve = (0, s.Ay)(l.A)(He || (He = (0, o.A)(["\n  display: flex;\n  flex-direction: column;\n  grid-gap: 16px;\n  justify-content: center;\n  padding-right: 16px;\n  ", "\n"])), (e => { let { photoSize: t } = e; return "\n    width: ".concat(3 * t, "px;\n  ") })),
                    Oe = (0, s.Ay)(l.A)(Le || (Le = (0, o.A)([""]))),
                    Re = e => { var t, n, r; let { items: a, theme: o, themeFieldsWithStyles: i, orgId: l, handleClick: s } = e; const c = null === o || void 0 === o || null === (t = o.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.photos,
                            d = (null === c || void 0 === c || null === (r = c.standard) || void 0 === r ? void 0 : r.size) || 100; return (0, p.jsx)(Ie, { name: "layout-8", children: a.map((e => { var t, n, r, a, u, h, m, f; return (0, p.jsxs)(je, { onClick: s && s(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (n = e.role) || void 0 === n ? void 0 : n.id), className: "item photoboard-item", children: [(0, p.jsx)(Oe, { children: (0, p.jsx)(M, { size: d, shape: null === c || void 0 === c ? void 0 : c.shape, person: null === e || void 0 === e ? void 0 : e.member, role: e.role }) }), (0, p.jsxs)(Ve, { photoSize: d, children: [null === i || void 0 === i || null === (r = i.inTheme) || void 0 === r || null === (a = r.mainSection) || void 0 === a ? void 0 : a.map((t => { var n, r, a, i, s, c; return (0, p.jsx)("h3", { className: "text-base", children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (a = e[null === t || void 0 === t || null === (i = t.fieldInfo) || void 0 === i ? void 0 : i.model]) || void 0 === a ? void 0 : a[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (c = o.data) || void 0 === c ? void 0 : c.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), null === i || void 0 === i || null === (u = i.inTheme) || void 0 === u || null === (h = u.subSection) || void 0 === h ? void 0 : h.map((t => { var n, r, a, i, s, c, d, u, h; return (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("p", { className: "job-desc", children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (i = e[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (c = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === c ? void 0 : c[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) })), (0, p.jsx)("ul", { className: "social-links", children: null === i || void 0 === i || null === (m = i.inTheme) || void 0 === m || null === (f = m.iconSection) || void 0 === f ? void 0 : f.map((t => { var n, r, a, i, s, c, d, u, h; return (0, p.jsx)(p.Fragment, { children: (null === (n = e[null === t || void 0 === t || null === (r = t.fieldInfo) || void 0 === r ? void 0 : r.model]) || void 0 === n ? void 0 : n[null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.id]) && (0, p.jsx)("li", { children: (0, p.jsx)(v, { item: e, orgId: l, objectId: null === (i = e[null === t || void 0 === t || null === (s = t.fieldInfo) || void 0 === s ? void 0 : s.model]) || void 0 === i ? void 0 : i.id, field: null === t || void 0 === t ? void 0 : t.fieldInfo, value: null === (c = e[null === t || void 0 === t || null === (d = t.fieldInfo) || void 0 === d ? void 0 : d.model]) || void 0 === c ? void 0 : c[null === t || void 0 === t || null === (u = t.fieldInfo) || void 0 === u ? void 0 : u.id], fontDetails: null === t || void 0 === t ? void 0 : t.fieldStyle, photoboardTheme: null === o || void 0 === o || null === (h = o.data) || void 0 === h ? void 0 : h.photoboard, section: null === t || void 0 === t ? void 0 : t.fieldSection }) }) }) })) })] })] }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var Pe; const De = s.Ay.ul(Pe || (Pe = (0, o.A)(["\n  display: grid;\n  list-style: none;\n  margin: 0;\n  margin-left: auto;\n  margin-right: auto;\n  padding: 20px;\n  row-gap: 5rem;\n  column-gap: 1.5rem;\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n  max-width: 42rem;\n  @media (min-width: 640px) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  @media (min-width: 1024px) {\n    -moz-column-gap: 2rem;\n    column-gap: 2rem;\n    max-width: 56rem;\n  }\n  @media (min-width: 1280px) {\n    max-width: none;\n  }\n\n  li {\n    flex-direction: column;\n    display: flex;\n    gap: 2.5rem;\n    padding-top: 3rem;\n    text-align: -webkit-match-parent;\n    @media (min-width: 640px) {\n      flex-direction: row;\n    }\n  }\n  .rounded-full {\n    object-fit: cover;\n    border-radius: 1rem;\n    flex: none;\n    width: 13rem;\n    aspect-ratio: 4/5;\n    max-width: 100%;\n    height: auto;\n    display: block;\n    vertical-align: middle;\n  }\n  .card {\n    flex: 1 1 auto;\n    max-width: 36rem;\n  }\n  .text-base {\n    font-size: 1.125rem;\n    font-weight: 600;\n    --tw-text-opacity: 1;\n    color: rgb(17 24 39 / var(--tw-text-opacity));\n    letter-spacing: -0.025em;\n    line-height: 2rem;\n    margin: 0;\n    overflow-wrap: break-word;\n  }\n  .text-sm {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.75rem;\n    font-size: 1rem;\n    margin: 0;\n  }\n  .job-desc {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity));\n    line-height: 1.75rem;\n    font-size: 1rem;\n    margin: 0;\n    margin-top: 1rem;\n  }\n  .social-links {\n    column-gap: 1.5rem;\n    display: flex;\n    margin: 0;\n    padding: 0;\n    margin-top: 1.5rem;\n    list-style: none;\n    li {\n      margin: 0;\n      padding: 0;\n    }\n    a {\n      --tw-text-opacity: 1;\n      color: rgb(156 163 175 / var(--tw-text-opacity));\n      padding: 0;\n      margin: 0;\n    }\n  }\n"]))),
                    Fe = e => { let { items: t, handleClick: n } = e; const r = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.LINKEDIN && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            a = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.dj.DESCRIPTION && e.model === d.A2.ROLE && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            o = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.EMAIL && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value },
                            i = e => { var t; return null === e || void 0 === e || null === (t = e.find((e => e.name === d.x2.PHONE && e.model === d.A2.MEMBER && e.isDefault))) || void 0 === t ? void 0 : t.value }; return (0, p.jsx)(De, { name: "layout-9", children: t.map((e => { var t, l, s, c, h, m, f, v, g; return (0, p.jsxs)("li", { onClick: n && n(null === e || void 0 === e || null === (t = e.member) || void 0 === t ? void 0 : t.id, null === e || void 0 === e || null === (l = e.role) || void 0 === l ? void 0 : l.id), className: "item photoboard-item", children: [(0, p.jsx)("img", { className: "rounded-full", src: null === e || void 0 === e || null === (s = e.member) || void 0 === s ? void 0 : s.photo, alt: "" }), (0, p.jsxs)("div", { className: "card", children: [(0, p.jsx)("h3", { className: "text-base", children: null === e || void 0 === e || null === (c = e.member) || void 0 === c ? void 0 : c.name }), (0, p.jsx)("p", { className: "text-sm", children: (0, d.mA)(e.role) }), (0, p.jsx)("p", { className: "job-desc", children: a(null === e || void 0 === e || null === (h = e.role) || void 0 === h ? void 0 : h.fields) }), r(null === e || void 0 === e || null === (m = e.member) || void 0 === m ? void 0 : m.fields) && (0, p.jsxs)("ul", { className: "social-links", children: [(0, p.jsx)("li", { children: (0, p.jsx)("a", { href: r(null === e || void 0 === e || null === (f = e.member) || void 0 === f ? void 0 : f.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "LinkedInInverse", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: o(null === e || void 0 === e || null === (v = e.member) || void 0 === v ? void 0 : v.fields), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "Email", size: "lg", color: "#9E9E9E" }) }) }), (0, p.jsx)("li", { children: (0, p.jsx)("a", { href: "tel:".concat(i(null === e || void 0 === e || null === (g = e.member) || void 0 === g ? void 0 : g.fields)), target: "_blank", rel: "noopener noreferrer", children: (0, p.jsx)(u.Ay, { icon: "PhoneSolid", size: "lg", color: "#9E9E9E" }) }) })] })] })] }, null === e || void 0 === e ? void 0 : e.roleId) })) }) }; var Ne = n(24738),
                    _e = n(81635),
                    Be = n(19367),
                    We = n(9368),
                    Ue = n(57546),
                    qe = n(63580); const Ge = e => { let { layout: t, theme: n, items: r, pureData: o, orgId: i, layoutClasses: s } = e; const c = (0, h.d4)(Ne.ZI),
                        d = "single",
                        u = (0, h.wA)(),
                        { resource: m } = (0, Ue.A)(),
                        f = (0, h.d4)(Be.Jf),
                        v = !(0, We.A)() || (null === f || void 0 === f ? void 0 : f.showRoleDetails),
                        g = qe.A.find((e => e.id === t)),
                        y = (0, a.useMemo)((() => c(d, null === g || void 0 === g ? void 0 : g.fields)), [d, c]),
                        b = (e, t) => () => { u((0, _e.gN)({ activePersonId: e, mode: "view", selectedRoleId: t })) }; return (0, p.jsx)(l.A, { height: "100%", children: (() => { switch (t) {
                                case "layout-1":
                                    return (0, p.jsx)(_, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-2":
                                    return (0, p.jsx)(K, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-3":
                                    return (0, p.jsx)(ne, { classes: s, items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-4":
                                    return (0, p.jsx)(ue, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-5":
                                    return (0, p.jsx)(ze, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-6":
                                    return (0, p.jsx)(ke, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-7":
                                    return (0, p.jsx)(Ee, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-8":
                                    return (0, p.jsx)(Re, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                case "layout-9":
                                    return (0, p.jsx)(Fe, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 });
                                default:
                                    return (0, p.jsx)(I, { items: r, theme: n, themeFieldsWithStyles: y, orgId: i, handleClick: !m && v && !o && b || void 0 }) } })() }) } }, 13020: (e, t, n) => { "use strict";
                n.d(t, { o: () => r });
                n(57528), n(72119); const r = (e, t) => { switch (e) {
                        case "layout-2":
                            return { width: 200 };
                        case "layout-0":
                            return { width: t > 200 ? 250 : 200 };
                        case "layout-4":
                            return { width: 2.2 * t + 40 } } } }, 10075: (e, t, n) => { "use strict";
                n.d(t, { A: () => W }); var r = n(65043),
                    a = n(96364),
                    o = n(70567),
                    i = n(61531),
                    l = n(40454),
                    s = n(9989),
                    c = n(44235),
                    d = n(38325),
                    u = n(86852),
                    h = n(84),
                    m = n(49092),
                    p = n(49157),
                    f = n(5387),
                    v = n(14556),
                    g = n(85725),
                    y = n(85657),
                    b = n(77454),
                    w = n(79043),
                    z = n(91688),
                    x = n(75156),
                    A = n(70318),
                    k = n(82244),
                    S = n(12176),
                    M = n(26225),
                    E = n(7743),
                    C = n(1619),
                    T = n(356),
                    H = n(60038),
                    L = n(64021),
                    I = n(6124),
                    j = n(65025),
                    V = n(94642),
                    O = n(10621),
                    R = n(89656),
                    P = n(70494),
                    D = n(91383),
                    F = n(19924),
                    N = n(80700),
                    _ = n(15813),
                    B = n(70579); const W = e => { var t, n, W; const { defaults: U } = { ...e, defaults: { ...e.defaults || {} } }; let { orgId: q, resource: G, base: K, resourceAction: Z } = (0, z.useParams)();
                    Z = Z || U.resourceAction || "view", G = G || U.resource; const { t: Y } = (0, m.B)(), X = (0, o.A)(), { openDialog: $ } = (0, h.A)("newRole"), [Q, J] = (0, r.useState)(0), [ee, te] = (0, r.useState)(25), ne = "printPreview" === Z, re = "print" === K, ae = re || ne, oe = (0, v.d4)(M.Zx), ie = (0, v.d4)(E.gJ), le = (0, v.d4)(R.ZM), { printSettings: { chartElements: { legend: { visible: se, position: ce = "", ownPage: de } }, pageSetup: { pageFormat: { size: ue, orientation: he } } } } = (0, V.A)(), me = ae && se && !de && (null === oe || void 0 === oe || null === (t = oe.rules) || void 0 === t ? void 0 : t.length) > 0, pe = (null === ue || void 0 === ue ? void 0 : ue.value) && ae && "auto" !== (null === ue || void 0 === ue ? void 0 : ue.value), { updatePageReady: fe } = (0, I.A)(), ve = (0, j.A)(), { groupBy: ge, query: ye, handleGroupByChange: be } = ve, we = null === ve || void 0 === ve ? void 0 : ve.orderBy, { data: ze, printData: xe, selectedGroup: Ae, groups: ke, setSelectedGroup: Se, legendsElemRef: Me, layoutClasses: Ee } = (0, P.A)(); let Ce = ze;
                    ae && (Ce = xe); const Te = pe ? "".concat(("portrait" === he ? (null === ue || void 0 === ue ? void 0 : ue.width) || 8.3 : (null === ue || void 0 === ue ? void 0 : ue.height) || 11.7) * _.ek[null === ue || void 0 === ue ? void 0 : ue.value], "px") : "100%",
                        He = (0, r.useCallback)((e => { var t, n, r; return !ye && "" !== ye || C.A.filterItem(e, ye, [null === (t = ie[O.x2.FIRSTNAME]) || void 0 === t ? void 0 : t.id, null === (n = ie[O.x2.LASTNAME]) || void 0 === n ? void 0 : n.id, null === (r = ie[O.x2.LINKEDIN]) || void 0 === r ? void 0 : r.id]) }), [ye]),
                        [Le, Ie] = (0, r.useMemo)((() => { const e = Ce.filter(He),
                                t = ae ? e.length : Q * ee + ee; return [e.slice(Q * ee, t), e.length] }), [Ce, Q, ee, we, He, K, le]);
                    (0, r.useLayoutEffect)((() => { J(0) }), [ee, ge]); const je = e => () => { Se(e), J(0), G && !ae && T.A.trackEvent({ eventName: "".concat(G.toUpperCase, "_GROUPBY_CLICK"), extraParams: e && e.title ? e.title : "None" }) },
                        Ve = async () => { be(null) }, Oe = () => { $({ allowChangeManager: !0 }) };
                    (0, r.useEffect)((() => { G && T.A.trackEvent({ eventName: "".concat(G.toUpperCase(), "_OPEN") }) }), []), (0, r.useEffect)((() => { re || ne || fe() }), []); const Re = { circle: "Circle", square: "Square", stretch: "Square" },
                        Pe = (0, v.d4)(F.hK),
                        De = (null === Pe || void 0 === Pe || null === (n = Pe.data) || void 0 === n || null === (W = n.photoboard) || void 0 === W ? void 0 : W.layout) || "layout-0",
                        Fe = () => (0, B.jsxs)("div", { style: { flex: 1, containerType: "inline-size", containerName: "photoboard" }, children: [me && ce.includes("top") && (0, B.jsxs)(i.A, { p: 3, py: 1, ref: Me, display: "flex", flexDirection: "row", alignItems: "center", children: [(0, B.jsx)(i.A, { mr: 2, children: (0, B.jsx)(a.A, { weight: "medium", children: null !== oe && void 0 !== oe && oe.title ? oe.title : "Legend" }) }), (0, B.jsx)(i.A, { ml: 1, children: (0, B.jsx)(i.A, { display: "flex", flexDirection: "row", children: (0, B.jsx)(i.A, { display: "flex", flexDirection: "row", children: null === oe || void 0 === oe ? void 0 : oe.rules.map((e => (0, B.jsx)(i.A, { ml: 1, children: (0, B.jsx)(D.A, { rule: e, isPrint: !0, shape: Re[Pe.chartOptions.legend.badgeShape] || "Circle" }, e.id) }))) }) }) })] }), Le.length > 0 && (0, B.jsx)(i.A, { width: ne ? "100%" : Te, className: "photoboard", overflow: "hidden", children: (0, B.jsx)(N._, { layoutClasses: Ee, layout: De, theme: Pe, items: Le, pureData: ae, orgId: q, resource: G }) }), 0 === Le.length && (0, B.jsx)(l.A, { container: !0, justifyContent: "center", alignItems: "center", style: { marginTop: 32 }, children: (0, B.jsx)(L.A, { text: Y("General.Text.NothingHere"), image: H.A, handleClick: Oe, buttonText: "New Role", isButtonVisible: !1 }) }), me && ce.includes("bottom") && (0, B.jsxs)(i.A, { p: 3, ref: Me, display: "flex", flexDirection: "row", alignItems: "center", children: [(0, B.jsx)(i.A, { mr: 2, children: (0, B.jsx)(a.A, { weight: "medium\xcf", children: null !== oe && void 0 !== oe && oe.title ? oe.title : "Legend" }) }), (0, B.jsx)(i.A, { ml: 1, children: (0, B.jsx)(i.A, { display: "flex", flexDirection: "row", children: (0, B.jsx)(i.A, { display: "flex", flexDirection: "row", children: null === oe || void 0 === oe ? void 0 : oe.rules.map((e => (0, B.jsx)(i.A, { ml: 1, children: (0, B.jsx)(D.A, { rule: e, isPrint: !0, shape: Re[Pe.chartOptions.legend.badgeShape] || "Circle" }, e.id) }))) }) }) })] })] }); return (0, B.jsx)(B.Fragment, { children: (0, B.jsxs)(w.A, { container: !0, justifyContent: "flex-start", isPrintScreen: re, isPrintPreview: ne, children: [ge && !ae && (0, B.jsx)(i.A, { border: 1, borderTop: 0, borderLeft: 0, borderBottom: 0, borderColor: "grey.200", style: { backgroundColor: X.palette.grey[100] }, clone: !0, children: (0, B.jsx)(p.A, { item: !0, width: 375, maxOnly: "department" !== (null === ge || void 0 === ge ? void 0 : ge.attr), children: (0, B.jsx)(i.A, { pl: 3, clone: !0, children: (0, B.jsxs)(g.A, { container: !0, justifyContent: "flex-start", overflow: "hidden", direction: "column", children: [(0, B.jsxs)(i.A, { pt: 1, mr: 3, children: [(0, B.jsxs)(l.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, B.jsx)(i.A, { pl: 2, children: (0, B.jsx)(a.A, { variant: "subtitle1", display: "inline", children: (null === ge || void 0 === ge ? void 0 : ge.label) || "" }) }), (0, B.jsx)(i.A, { ml: 2, children: (0, B.jsx)(A.A, { onClick: Ve, children: (0, B.jsx)(x.Ay, { icon: "Close" }) }) })] }), (0, B.jsx)(k.A, { weight: 1, orientation: "hors" })] }), (0, B.jsx)(y.A, { item: !0, xs: !0, children: (0, B.jsx)(i.A, { mr: 3, children: (0, B.jsxs)(s.A, { className: "content", children: [0 === ke.length && (0, B.jsxs)(i.A, { ml: 2, children: [(0, B.jsxs)(a.A, { variant: "body1", color: "inherit", children: ['"Group by ', null === ge || void 0 === ge ? void 0 : ge.title, '"'] }), (0, B.jsx)(a.A, { variant: "body2", color: "inherit", children: "No groups found" }), (0, B.jsx)(a.A, { display: "block", variant: "body2", children: (0, B.jsx)("a", { onClick: Ve, children: "Clear groups" }) })] }), ke.map((e => (0, B.jsx)(f.A, { button: !0, onClick: je(e), selected: (null === Ae || void 0 === Ae ? void 0 : Ae.id) === e.id, children: (0, B.jsxs)(l.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, B.jsx)(S.A, { variant: "body1", color: "inherit", children: e.name }), (0, B.jsx)(u.A, { spacing: { right: 1 }, children: (0, B.jsx)(c.A, { badgeContent: e.peopleCount || 0, color: "primary", overlap: "rectangular" }) })] }) }, e.id)))] }) }) })] }) }) }) }), (0, B.jsxs)(b.A, { item: !0, xs: !0, isPrintScreen: re, style: { display: "flex", flexDirection: "column" }, children: [(0, B.jsx)(Fe, { children: Le }), Ie > 0 && !ae && (0, B.jsx)(i.A, { position: "sticky", left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", borderTop: "solid 1px #ddd", children: (0, B.jsx)(d.A, { rowsPerPageOptions: [25, 50, 100, 250], component: "div", count: Ie, rowsPerPage: ee, page: Q, backIconButtonProps: { "aria-label": Y("Common.Tables.backIconButtonText") }, nextIconButtonProps: { "aria-label": Y("Common.Tables.nextIconButtonText") }, onPageChange: (e, t) => { J(t) }, onRowsPerPageChange: e => { te(parseInt(e.target.value, 10)), J(0) }, labelRowsPerPage: Y("Common.Tables.labelRowsPerPage"), backIconButtonText: Y("Common.Tables.backIconButtonText"), nextIconButtonText: Y("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: t, to: n, count: r } = e; return "".concat(t, "-").concat(n, " ").concat(Y("Common.Tables.of"), " ").concat(r) } }) })] })] }) }) } }, 32281: (e, t, n) => { "use strict";
                n.d(t, { E: () => p }); var r, a = n(57528),
                    o = (n(65043), n(97912)),
                    i = n(55357),
                    l = n(67467),
                    s = n(29829),
                    c = n(59548),
                    d = n(18908),
                    u = n(72119),
                    h = n(70579); const m = (0, u.Ay)(i.A)(r || (r = (0, a.A)(["\n  font-size: 12px;\n"])));
                class p { getCompatibleCell(e) { let t;
                        e.text || (e.text = ""); try { t = (0, o.I_)(e, "selectedValue", e.isMulti ? "object" : "string") } catch (c) { t = void 0 } const n = (0, o.I_)(e, "values", "object"),
                            r = t ? parseFloat(t) : t; let a, i, l = !0; try { l = (0, o.I_)(e, "isDisabled", "boolean") } catch (d) { l = !1 } try { a = (0, o.I_)(e, "inputValue", "string") } catch (u) { a = void 0 } try { i = (0, o.I_)(e, "isMulti", "boolean") } catch (h) { i = !1 } const s = t || ""; return Object.assign(Object.assign({}, e), { selectedValue: t, text: s, value: r, values: n, isDisabled: l, inputValue: a, isMulti: i }) } update(e, t) { return this.getCompatibleCell(Object.assign(Object.assign({}, e), { selectedValue: t.selectedValue, inputValue: t.inputValue, isMulti: t.isMulti, value: t.value })) } getClassName(e) { return "".concat(e.className ? e.className : "") } handleKeyDown(e, t, n, r, a) { if ((t === o.uP.SPACE || t === o.uP.ENTER) && !r) return { cell: this.getCompatibleCell(Object.assign(Object.assign({}, e))), enableEditMode: !1 }; const i = (0, o.ne)(t, r); return n || a || !(0, o.$Z)(t) ? { cell: e, enableEditMode: !1 } : { cell: this.getCompatibleCell(Object.assign(Object.assign({}, e), { inputValue: r ? i : i.toLowerCase() })), enableEditMode: !1 } } render(e, t, n) { return (0, h.jsx)(f, { onCellChanged: e => n(this.getCompatibleCell(e), !0), cell: e }) } } const f = e => { let { onCellChanged: t, cell: n } = e; const r = (n.selectedValue && n.isMulti ? [...n.selectedValue] : n.selectedValue) || []; return (0, h.jsx)("div", { id: "react-select-dropdown-menus-".concat(n.attr), className: "react-select-dropdown-menus", children: (0, h.jsxs)(l.A, { sx: { m: 1, width: 300 }, id: "react-select-".concat(n.attr), children: [(0, h.jsx)(s.A, { id: "react-select-".concat(n.attr), children: "Select" }), (0, h.jsxs)(c.A, { disabled: !(n.values || []).length && !n.selectedValue || n.isDisabled, autoWidth: !0, variant: "standard", labelId: "react-select-".concat(n.attr), name: "react-select-".concat(n.attr), id: "react-select-", multiple: n.isMulti, value: r, onChange: e => { const { target: { value: r } } = e;
                                    t({ ...n, selectedValue: r }) }, input: (0, h.jsx)(d.A, { id: "react-select-".concat(n.attr), label: "Name" }), onBlur: e => (e => { e.target.id = "react-select-", e.preventDefault() })(e), children: [n.selectedValue && !(n.values || []).find((e => e.value === n.selectedValue)) && (0, h.jsx)(m, { id: "react-select-".concat(n.selectedValue), value: n.selectedValue, children: n.selectedValue }), (n.values || []).map((e => (0, h.jsx)(m, { id: "react-select-".concat(e.value), value: e.value, children: e.label }, e.value)))] })] }) }) } }, 43098: (e, t, n) => { "use strict";
                n.d(t, { KS: () => h, Pe: () => c, eY: () => f, gs: () => p, mF: () => m }); var r = n(80192),
                    a = n(6562),
                    o = n(74079),
                    i = n(7743),
                    l = n(23993),
                    s = n(55005); const c = e => { var t, n, r, a, o; return (null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n || null === (r = n.alias) || void 0 === r ? void 0 : r.rootChartId) || (null === (a = e.chart) || void 0 === a || null === (o = a.info) || void 0 === o ? void 0 : o.id) },
                    d = (e, t) => { var n, r, a; return "department" === (null === (n = e) || void 0 === n ? void 0 : n.type) || "location" === (null === (r = e) || void 0 === r ? void 0 : r.type) ? (e = t[null === (a = e) || void 0 === a ? void 0 : a.parent], d(e, t)) : e },
                    u = (0, r.Mz)((e => (0, l.qs)(e.roles)), o.A, a.A, i.gJ, ((e, t, n, r) => { var a; const o = null === r || void 0 === r || null === (a = r.roleName) || void 0 === a ? void 0 : a.id; return e.reduce(((e, r) => { const a = t[r] || {}; let i, l = ""; var s;
                            a.parent && (i = t[null === a || void 0 === a ? void 0 : a.parent], i = d(i, t), null !== (s = i) && void 0 !== s && s.members && i.members.map(((e, t) => { var r, a, o;
                                null !== (r = n[e]) && void 0 !== r && r.name && (l = 0 === t ? null === (a = n[e]) || void 0 === a ? void 0 : a.name : l + "," + (null === (o = n[e]) || void 0 === o ? void 0 : o.name)) }))); if ("department" !== (null === a || void 0 === a ? void 0 : a.type) && "location" !== (null === a || void 0 === a ? void 0 : a.type) && "embedded" !== (null === a || void 0 === a ? void 0 : a.type) && "shared" !== (null === a || void 0 === a ? void 0 : a.type)) { let t = a.members.filter((e => e)); if (0 === (null === t || void 0 === t ? void 0 : t.length)) { var c;
                                    e.push({ roleId: null === a || void 0 === a ? void 0 : a.id, memberId: null, role: a, roleName: a[o], member: null, parents: l, parentRole: (null === (c = i) || void 0 === c ? void 0 : c[o]) || "", parent: l && (i || "") }) } else { const r = null === t || void 0 === t ? void 0 : t.map((e => n[e])).filter((e => e)); for (let t = 0; t < (null === r || void 0 === r ? void 0 : r.length); t++) { var u; const n = r[t];
                                        e.push({ roleId: null === a || void 0 === a ? void 0 : a.id, memberId: null === n || void 0 === n ? void 0 : n.id, role: a, roleName: a[o], member: n, parents: l, parentRole: (null === (u = i) || void 0 === u ? void 0 : u[o]) || "", parent: l && (i || "") }) } } } return e = e.sort(((e, t) => e.roleId > t.roleId ? 1 : t.roleId > e.roleId ? -1 : 0)) }), []) })),
                    h = (0, r.Mz)((e => [...u(e)]), (e => e)),
                    m = (0, r.Mz)((e => e.spreadsheet), (e => e.selectedHiddenColumns || [])),
                    p = (0, r.Mz)((e => e.spreadsheet), s.A, ((e, t) => e.visibleColumns || t)),
                    f = (0, r.Mz)((e => e.spreadsheet), (e => ({ sort: e.spreadsheetSort, order: e.spreadsheetSortOrder }))) }, 12395: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, G5: () => o, d1: () => i, lw: () => l, vv: () => a }); const r = (0, n(80907).Z0)({ name: "spreadsheet", initialState: { selectedHiddenColumns: [], visibleColumns: null, spreadsheetSort: null, spreadsheetSortOrder: "asc" }, reducers: { setSelectedHiddenColumns: (e, t) => { let { payload: n } = t;
                                e.selectedHiddenColumns = n }, setVisibleColumns: (e, t) => { let { payload: n } = t;
                                e.visibleColumns = n }, setSort: (e, t) => { let { payload: n } = t;
                                e.spreadsheetSort = n }, setOrder: (e, t) => { let { payload: n } = t;
                                e.spreadsheetSortOrder = n } } }),
                    { setSelectedHiddenColumns: a, setVisibleColumns: o, setSort: i, setOrder: l } = r.actions,
                    s = r.reducer }, 70494: (e, t, n) => { "use strict";
                n.d(t, { A: () => f }); var r = n(14556),
                    a = n(91688),
                    o = n(65043),
                    i = n(26225),
                    l = n(13888),
                    s = n(89656),
                    c = n(59177),
                    d = n(65025),
                    u = n(19924),
                    h = n(13020),
                    m = n(98433),
                    p = n(15813); const f = () => { var e, t, n, f, v, g, y, b, w, z, x, A, k, S, M, E, C; const T = (0, r.d4)(m.rW),
                        H = (0, r.d4)(m.pl),
                        L = (0, r.d4)(l.id),
                        I = (0, r.d4)(l.lM),
                        j = (0, r.d4)(m.kT),
                        V = (0, r.d4)(m.db),
                        [O, R] = (0, o.useState)(""),
                        [P, D] = (0, o.useState)(),
                        [F, N] = (0, o.useState)({}),
                        [_, B] = (0, o.useState)([]),
                        [W, U] = (0, o.useState)(!1),
                        q = (0, o.useRef)(null),
                        G = (0, o.useRef)(null),
                        { base: K, resourceAction: Z, resource: Y } = (0, a.useParams)(),
                        X = "printPreview" === Z,
                        $ = "print" === K,
                        Q = null === H || void 0 === H || null === (e = H.header) || void 0 === e ? void 0 : e.visible,
                        J = null === H || void 0 === H || null === (t = H.footer) || void 0 === t ? void 0 : t.visible,
                        ee = (null === (n = H.header) || void 0 === n || null === (f = n.text) || void 0 === f ? void 0 : f.length) > 0,
                        te = (null === H || void 0 === H || null === (v = H.header) || void 0 === v || null === (g = v.subtitle) || void 0 === g ? void 0 : g.visible) && (null === H || void 0 === H || null === (y = H.header) || void 0 === y || null === (b = y.subtitle) || void 0 === b || null === (w = b.text) || void 0 === w ? void 0 : w.length) > 0,
                        ne = null === H || void 0 === H || null === (z = H.header) || void 0 === z ? void 0 : z.companyLogo,
                        re = (0, r.d4)(s.P0),
                        ae = (0, r.d4)(u.hK),
                        oe = (null === ae || void 0 === ae || null === (x = ae.data) || void 0 === x || null === (A = x.photoboard) || void 0 === A ? void 0 : A.layout) || "layout-0",
                        ie = (null === ae || void 0 === ae || null === (k = ae.data) || void 0 === k || null === (S = k.photoboard) || void 0 === S || null === (M = S.photos) || void 0 === M || null === (E = M.standard) || void 0 === E ? void 0 : E.size) || 100,
                        le = null === ae || void 0 === ae || null === (C = ae.base) || void 0 === C ? void 0 : C.table,
                        { directory: se } = re.data,
                        ce = (0, d.A)(),
                        { order: de, groupBy: ue } = ce,
                        he = ($ || X) && (null === se || void 0 === se ? void 0 : se.orderBy) || (null === ce || void 0 === ce ? void 0 : ce.orderBy),
                        me = (0, r.d4)((e => (0, i.py)(e, ue))),
                        pe = $ || X ? null : me,
                        fe = (0, r.wA)();
                    (0, o.useLayoutEffect)((() => { null !== pe && void 0 !== pe && pe.length && P && pe.find((e => e.id === P.id)) || (null !== pe && void 0 !== pe && pe.length ? D(pe[0]) : D(null)) }), [pe]); const ve = (e, t) => { var n, r; let a, o;
                            Array.isArray(he) ? [a, o] = he : (a = he.model, o = he.attr); let i = (0, c.Ok)(null === e || void 0 === e || null === (n = e[a]) || void 0 === n ? void 0 : n[o]).toLowerCase(),
                                l = (0, c.Ok)(null === t || void 0 === t || null === (r = t[a]) || void 0 === r ? void 0 : r[o]).toLowerCase(); var s, d; "department" !== o && "location" !== o || (i = null === e || void 0 === e || null === (s = e[o]) || void 0 === s ? void 0 : s.toLowerCase(), l = null === t || void 0 === t || null === (d = t[o]) || void 0 === d ? void 0 : d.toLowerCase()); return i ? l ? i.localeCompare(l) : -1 : 1 },
                        ge = (0, r.d4)((e => X || $ || !ue || null !== P && void 0 !== P && P.id ? "directory" === Y ? (0, i.XZ)(e, { groupId: X || $ ? null : null === P || void 0 === P ? void 0 : P.id, groupBy: X || $ ? null : ue }) : (0, i.Y7)(e, { groupId: X || $ ? null : null === P || void 0 === P ? void 0 : P.id, groupBy: X || $ ? null : ue }) : [])).sort((() => { if (he) { return "desc" !== (($ || X) && (null === he || void 0 === he ? void 0 : he.order) || de) ? (e, t) => ve(e, t) : (e, t) => -ve(e, t) } })()),
                        ye = (0, o.useCallback)((() => { var e, t, n, r; let a = 11.7,
                                o = 8.3; const i = (null === (e = document.getElementsByClassName("printHeader")[0]) || void 0 === e ? void 0 : e.offsetHeight) || 0,
                                l = (null === (t = document.getElementsByClassName("printFooter")[0]) || void 0 === t ? void 0 : t.offsetHeight) || 0;
                            T.pageFormat.orientation === p.t4.LANDSCAPE ? (a = p.q2[T.pageFormat.size].width || 8.3, o = p.q2[T.pageFormat.size].height || 11.7) : (a = p.q2[T.pageFormat.size].height || 11.7, o = p.q2[T.pageFormat.size].width || 8.3); let s = (null === (n = q.current) || void 0 === n ? void 0 : n.offsetHeight) || 0,
                                c = (null === (r = G.current) || void 0 === r ? void 0 : r.offsetHeight) || 0;
                            ($ || X) && (s = 40, c = L.visible && !L.ownPage ? 40 : 0); return { currentPageHeightInPx: a * (p.ek[T.pageFormat.size] || 100) - s - c - i - l, currentPageWidthInPx: o * (p.ek[T.pageFormat.size] || 100) } }), [T, document, H.header, H.footer, L]); return (0, o.useLayoutEffect)((() => { async function e() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0,
                                t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
                                n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                                r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0,
                                a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 0,
                                o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 0,
                                i = arguments.length > 6 && void 0 !== arguments[6] ? arguments[6] : 0;
                            B([]), N({}); let l = {}; if ("auto" === T.pageFormat.size) l = { 1: ge.map((e => ({ memberId: null === e || void 0 === e ? void 0 : e.memberId, roleId: null === e || void 0 === e ? void 0 : e.roleId }))) }, N(l);
                            else { const { currentPageHeightInPx: s, currentPageWidthInPx: c } = ye(); let d = c,
                                    u = s; if (j === p._6.DIRECTORY) { const e = ge.length,
                                        t = null !== le && void 0 !== le && le.densePadding ? 60 : 80,
                                        n = Math.floor(s / t),
                                        r = Math.ceil(e / n); for (let a = 1; a <= r; a++) { l[a] = [];
                                        ge.slice((a - 1) * n, a * n).forEach((e => { e && l[a].push({ memberId: null === e || void 0 === e ? void 0 : e.memberId, roleId: null === e || void 0 === e ? void 0 : e.roleId }) })) } } else if (j === p._6.PHOTOBOARD) { d = d - Math.max(r, n) + a, u = u - r + a; let s = i || Math.floor(d / (t + a + o)),
                                        c = Math.floor(u / (e + a + o));
                                    s = s > 0 ? s : 1, c = c > 0 ? c : 1; const h = s * c,
                                        m = ge.length,
                                        p = Math.ceil(m / h); if (h > 0)
                                        for (let e = 1; e <= p; e++) { l[e] = [];
                                            ge.slice((e - 1) * h, e * h).forEach((t => { t && l[e].push({ memberId: null === t || void 0 === t ? void 0 : t.memberId, roleId: null === t || void 0 === t ? void 0 : t.roleId }) })) } } N(l), fe((0, m.LU)(Object.keys(l).length + (V.visible ? 1 : 0))) } } j === p._6.DIRECTORY && ($ || X) && T.pagination === p.ti.MULTIPLE ? e() : j === p._6.PHOTOBOARD && ($ || X) && T.pagination === p.ti.MULTIPLE ? async function() { const t = (e => { switch (e) {
                                        case "layout-1":
                                        case "layout-3":
                                        case "layout-4":
                                            return 40;
                                        default:
                                            return 0 } })(oe),
                                n = (e => { switch (e) {
                                        case "layout-1":
                                        case "layout-2":
                                            return 16;
                                        default:
                                            return 0 } })(oe),
                                r = (0, h.o)(oe, ie); let a, o, i = 0;
                            U(!0); let l = 0;
                            B([]), N({}), B(ge); const s = document.querySelectorAll("ul[name='".concat(oe, "'] .item")); if ((null === s || void 0 === s ? void 0 : s.length) > 0) { if ("layout-3" === oe) { const e = s[0].offsetWidth; let t;
                                    t = T.pageFormat.orientation === p.t4.LANDSCAPE ? p.q2[T.pageFormat.size].height || 11.7 : p.q2[T.pageFormat.size].width || 8.3; const n = t * (p.ek[T.pageFormat.size] || 100);
                                    i = n <= 960 ? 1 : n <= 1300 ? 2 : n <= 1850 ? 3 : n <= 2340 ? 4 : n <= 2750 ? 5 : 6, R("photoboard-items-".concat(i)), l = n - i * e } a = Math.max(...Array.from(s, (e => e.offsetHeight))), o = null !== r && void 0 !== r && r.width ? r.width : Math.max(...Array.from(s, (e => e.offsetWidth))), e(a || 0, o, l, t, n, 0, i), U(!1) } U(!1) }(): ($ || X) && (B(ge), fe((0, m.LU)(1))) }), [p.q2[T.pageFormat.size].height, j, T.pagination, T.outputType, T.pageFormat.orientation, Q, J, ee, te, ne, L, he, V.visible]), (0, o.useEffect)((() => { if ("auto" !== T.pageFormat.size && T.pagination === p.ti.MULTIPLE)
                            if ($ || X) { if (Object.keys(F).length) { var e, t; let n = 0;
                                    V.visible && n++, L.visible && L.ownPage && n++; const r = (null === (e = F[I - n]) || void 0 === e ? void 0 : e.map((e => e.roleId))) || [],
                                        a = (null === (t = F[I - n]) || void 0 === t ? void 0 : t.map((e => e.memberId))) || [],
                                        o = [],
                                        i = ge.filter((e => { const t = r.includes(e.roleId) && a.includes(e.memberId) && !o.includes("".concat(null === e || void 0 === e ? void 0 : e.roleId, "_").concat(null === e || void 0 === e ? void 0 : e.memberId)); return !(!t || o.length === r.length) && (o.push("".concat(null === e || void 0 === e ? void 0 : e.roleId, "_").concat(null === e || void 0 === e ? void 0 : e.memberId)), t) }));
                                    B(i) } } else B(ge) }), [F, I]), { selectedGroup: P, setSelectedGroup: D, tableHeadersRef: q, legendsElemRef: G, groups: pe, printData: _, data: ge, pageMap: F, isLoading: W, layoutClasses: O } } }, 25492: (e, t, n) => { "use strict";
                n.d(t, { E: () => V }); var r, a = n(65043),
                    o = n(14556),
                    i = n(97912),
                    l = n(57528),
                    s = n(72119),
