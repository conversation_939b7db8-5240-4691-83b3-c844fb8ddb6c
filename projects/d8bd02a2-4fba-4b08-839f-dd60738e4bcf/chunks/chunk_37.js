                            a = ["email", "emailaddress", "email address", "emailaddr", "email_addr", "emailaddy", "workemail", "work email", "work_email", "workemail", "contact_email", "contactemail", "contact email", "member email", "memberemail", "member_email", "member - email", "work contact: work email"]; break;
                        case "phone":
                            a = ["phone", "phone number", "phonenumber", "phoneno", "phone no", "phonenum", "phone_num", "phone num", "phone1", "primary phone", "primary_phone", "phone_primary", "member - phone"]; break;
                        case "locationAddress":
                            a = ["location", "address", "location address", "locationaddress", "role - location address"]; break;
                        case "linkedIn":
                        case "linkedInUrl":
                            a = ["linkedin", "linked in", "linkedin url", "linked in url", "linkedinurl", "member - linkedin"]; break;
                        case "details":
                            a = ["details", "member details", "profile", "about", "description", "desc.", "desc", "descr", "member - description"]; break;
                        case "bgcolor":
                            a = ["role color", "role colour", "rolecolor", "rolecolour", "color", "colour", "bgcolour", "background", "background color", "background colour", "bg", "bgcolor"]; break;
                        case "propagateBg":
                            a = ["propagateBg", "propagate bg", "propagate color"]; break;
                        case "description":
                            a = ["roledescription", "role description", "role desc", "role - job description"]; break;
                        case "department":
                            a = ["department", "dept", "dept.", "department name", "department_name", "departmentname", "department description", "dept name", "dept. name", "dept_name", "dep", "dep."]; break;
                        case "parent":
                            a = ["parentid", "parent", "bossid", "boss", "boss id", "boss_id", "supervisorid", "supervisor id", "supervisor_id", "supervisor", "supervisor's name", "supervisor name", "managerid", "manager id", "manager_id", "manager", "manager unique id", "reportstoid", "reports_to_id", "reports to id", "reports to", "line manager id", "line_manager_id", "linemanagerid", "reports to associate id", "PLANS_SUP", "owner"]; break;
                        case "entityType":
                            a = ["entity type", "org type", "organization type"]; break;
                        case "entityPurpose":
                            a = ["entity purpose", "org purpose", "organization purpose", "purpose"]; break;
                        case "stake":
                            a = ["stake", "stake %", "equity", "ownership", "ownership %"]; break;
                        case "role_type":
                            a = ["roletype", "role type", "role_type", "type"]; break;
                        case "dottedIds":
                            a = ["dottedIds", "dotted ids", "dotted reports", "dottedreports"]; break;
                        case "function":
                            "matrix" === n && (a = ["function", "function name", "functionid", "function id", "matrix function", "matrixfunction"]); break;
                        case "team":
                            "matrix" === n && (a = ["team", "team name", "teamid", "team id", "matrix team", "matrixteam"]); break;
                        default:
                            { const t = "string" === typeof e && e.trim().toLowerCase(); if (t) { a.push(t); const e = t.replace(/\s+/g, "");
                                    e && a.push(e) } break } } return t && t.length > 0 && t.forEach((function(e) { a.forEach((function(t) { try { let n = e.value ? e.value : e.fieldId; const a = "^(person|role|member|employee)?[ s:-]{0,3}(".concat(t.toLowerCase().trim(), ")$");
                                n && new RegExp(a).test(n.toLowerCase().trim()) && (r.value = n.toString(), r.example = e.example ? e.example.toString() : "", r.label = e.label ? e.label.toString() : "") } catch (n) {} })) })), r }

                function mc(e) { for (var t = [], n = 0; n < e.length; n++) { Object.keys(e[n]).forEach((function(r) { for (var a = !1, o = 0; o < t.length; o++)
                                if (t[o].value === r) { a = !0; break } a || t.push({ value: r, label: r, example: e[n][r] }) })) } return t } const pc = { parseCsv: async (e, t) => { const { fileEncoding: n, chartType: r, importType: a, importFields: o, structuredFields: i = { id: "", parent: "", department: "" } } = t || {}, l = {}, s = [], c = []; let d = !1; return new Promise(((u, h) => { ac().parse(e, { worker: !0, delimiter: "", newline: "", quoteChar: '"', header: !0, dynamicTyping: !1, preview: 0, encoding: n || "UTF-8", comments: !1, step: function(e) { if (d || (d = !0, e && e.meta && e.meta.fields && e.meta.fields.length > 0 && e.meta.fields.forEach((function(t) { "" === t || s.push({ value: t, label: t, example: e.data[t] }) })), (o || []).filter((e => "people" !== a || ("member" === e.model || "role" === e.name))).forEach((function(e) { const n = hc(e.name || e.fieldId, s, r);
                                            l[e.fieldId] = null !== t && void 0 !== t && t.flatColumns ? n.value : { selectedValue: n.value, selectedValueExample: n.example }, "undefined" !== typeof i[e.fieldId] && (i[e.fieldId] = n.value) }))), e && e.errors && e.errors.length > 0 && console.log("CSV error"), e && e.data && Object.keys(e.data).length > 0) { let t = !0; for (const n in e.data)
                                            if (e.data[n].length > 0) { t = !1; break } t || c.push(e.data) } }, complete: function() { u({ columnMap: l, results: c, fieldsAvailable: s, structuredFields: i }) }, error: function() { h({ error: "Error Parsing CSV:", message: "There was an error reading your file" }) }, download: !1, skipEmptyLines: !0 }) })) }, parseExcel: (e, t) => { const n = e,
                            { chartType: r, importFields: a, importType: o, structuredFields: i = { id: "", parent: "", department: "" } } = t || {}; return new Promise(((e, l) => { const s = {},
                                c = new FileReader;
                            c.onload = function(n) { let d, u = null;
                                u = n ? n.target.result : c.content, d = Ls(u, { type: "binary" }); let h = function(e) { var t = {}; return e.SheetNames.forEach((function(n) { var r = Ws.sheet_to_row_object_array(e.Sheets[n], { raw: !1 });
                                        r.length > 0 && (t[n] = r) })), t }(d); try { let n = [],
                                        l = h[Object.keys(h)[0]]; if (null !== l && l.length > 0 && (n = mc(l)), a.filter((e => "people" !== o || ("member" === e.model || "role" === e.fieldId))).forEach((function(e) { const a = hc(e.name || e.fieldId, n, r);
                                            s[e.fieldId] = null !== t && void 0 !== t && t.flatColumns ? a.value : { selectedValue: a.value, selectedValueExample: a.example }, "undefined" !== typeof i[e.fieldId] && (i[e.fieldId] = a.value) })), null !== l && l.length > 0) { const t = []; for (let e = 0; e < l.length; e++) t.push(l[e]);
                                        e({ columnMap: s, results: t, fieldsAvailable: n, structuredFields: i }) } } catch (n) { console.log("Failed to parse excel file. e="), console.log(n), l("Failed to parse excel file.") } }, c.readAsBinaryString(n) })) }, getMatchingImportField: hc, initialImportFields: [{ fieldId: "firstName", required: !0, requiredChart: !0, label: "First Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "lastName", label: "Last Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "role", requiredChart: !0, label: "Role Title", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "department", requiredChart: !1, label: "Department", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "email", required: !1, label: "Email", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "phone", label: "Phone", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "linkedIn", label: "LinkedIn Profile URL", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "details", label: "Employee Bio", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "description", label: "Role Description", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "locationAddress", label: "Location", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0 }, { fieldId: "role_type", requiredChart: !1, label: "Role Type", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "bgcolor", requiredChart: !1, label: "Color", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "id", requiredChart: !0, label: "Unique ID", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0 }, { fieldId: "parent", requiredChart: !0, label: "Manager Unique Id", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0 }, { fieldId: "propagateBg", requiredChart: !1, label: "Propagate Color", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1 }, { fieldId: "dropLevel", requiredChart: !1, label: "Drop Level", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1 }, { fieldId: "dottedIds", requiredChart: !1, label: "Dotted Reports", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1 }, { fieldId: "expectedMemberCount", requiredChart: !1, label: "Expected Member Count", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0 }, { fieldId: "photo", required: !1, label: "Member Photo", model: "member", selectedValue: "", selectedValueExample: "", hidden: !1 }, { fieldId: "function", requiredChart: !0, label: "Matrix Top Function", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1, chartType: "matrix", typeSpecific: !0 }, { fieldId: "team", requiredChart: !0, label: "Matrix Left Team", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1, chartType: "matrix", typeSpecific: !0 }], initialOnboardImportFields: [{ fieldId: "firstName", required: !0, requiredChart: !0, label: "First Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "lastName", label: "Last Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "email", required: !1, label: "Email", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "phone", label: "Phone", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "details", label: "Employee Bio", model: "member", selectedValue: "", selectedValueExample: "" }], initialIntegrationFields: [{ fieldId: "firstName", required: !0, requiredChart: !0, label: "First Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "lastName", label: "Last Name", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "role", requiredChart: !0, label: "Role Title", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "email", required: !1, label: "Email", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "phone", label: "Phone", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "linkedIn", label: "LinkedIn Profile URL", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "details", label: "Employee Bio", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "description", label: "Role Description", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "locationAddress", label: "Location", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0 }, { fieldId: "role_type", requiredChart: !1, label: "Role Type", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "bgcolor", requiredChart: !1, label: "Color", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "propagateBg", requiredChart: !1, label: "Propagate Color", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0, importHidden: !0 }, { fieldId: "dropLevel", requiredChart: !1, label: "Drop Level", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0, importHidden: !0 }, { fieldId: "dottedIds", requiredChart: !1, label: "Dotted Reports", model: "role", selectedValue: "", selectedValueExample: "" }, { fieldId: "expectedMemberCount", requiredChart: !1, label: "Expected Member Count", model: "role", selectedValue: "", selectedValueExample: "", hidden: !0, importHidden: !0 }, { fieldId: "photo", required: !1, label: "Member Photo", model: "member", selectedValue: "", selectedValueExample: "" }, { fieldId: "function", requiredChart: !0, label: "Matrix Top Function", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1, chartType: "matrix", typeSpecific: !0, importHidden: !0 }, { fieldId: "team", requiredChart: !0, label: "Matrix Left Team", model: "role", selectedValue: "", selectedValueExample: "", hidden: !1, chartType: "matrix", typeSpecific: !0, importHidden: !0 }], getDefaultFieldInfo: (e, t) => e ? (t || []).find((t => t.name === e)) : null, getHeaderRowFromDataSet: mc } }, 77604: (e, t, n) => { "use strict";
                n.d(t, { S5: () => u, T$: () => c, ZL: () => r, jk: () => a, vS: () => d }); const r = { trial: { 25: { annual: 0, monthly: 0, admins: 1 } }, standard: { 150: { annual: 11, monthly: 20, admins: 3 }, 250: { annual: 18, monthly: 33, admins: 3 }, 500: { annual: 35, monthly: 61, admins: 5 }, 750: { annual: 51, monthly: 89, admins: 5 }, 1e3: { annual: 67, monthly: 116, admins: 10 }, 1250: { annual: 83, monthly: 145, admins: 10 }, 1500: { annual: 97, monthly: 172, admins: 10 }, 1750: { annual: 111, monthly: 200, admins: 10 }, 2e3: { annual: 126, monthly: 220, admins: 15 }, 2250: { annual: 140, monthly: 246, admins: 15 }, 2500: { annual: 150, monthly: 270, admins: 15 }, "2500plus": { admins: "Unlimited" } }, premium: { 150: { annual: 22, monthly: 39, admins: "Unlimited" }, 250: { annual: 36, monthly: 64, admins: "Unlimited" }, 500: { annual: 70, monthly: 122, admins: "Unlimited" }, 750: { annual: 102, monthly: 178, admins: "Unlimited" }, 1e3: { annual: 133, monthly: 235, admins: "Unlimited" }, 1250: { annual: 165, monthly: 289, admins: "Unlimited" }, 1500: { annual: 194, monthly: 340, admins: "Unlimited" }, 1750: { annual: 222, monthly: 394, admins: "Unlimited" }, 2e3: { annual: 250, monthly: 438, admins: "Unlimited" }, 2250: { annual: 280, monthly: 490, admins: "Unlimited" }, 2500: { annual: 302, monthly: 540, admins: "Unlimited" }, "2500plus": { admins: "Unlimited" } } },
                    a = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "standard",
                            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 150; const n = r[e][t].admins; return { standard: ["Email Support", "CSV & Excel Importing", "AWS Hosting - US East", "".concat(n, " Admins")], premium: ["Dedicated Onboarding Assistance", "CSV & Excel Importing", "Organimi Connect (TM) - sFTP, Active Directory, Google Workspace,...", "Matrix Org Charts / Project Charts", "AWS Hosting - US East, EU Ireland, AP - Sydney", "1 Hour Onboarding", "".concat(n, " Admins")] } [e] },
                    o = { enterprise: 9, premium: 8, standard: 7, pro: 6, planner: 5, growth: 4, team: 3, starter: 2, personal: 1, free: 0 },
                    i = { active: 3, requires_action: 2, trialing: 2, expired: 1 };

                function l(e) { const t = e || "free",
                        n = /^(2024_v7|v7)_(standard|premium)_(monthly|annual)_(\d{3,4})$/; if (n.test(t)) { const e = t.match(n),
                            r = e[4],
                            a = e[2]; return { planType: a, frequency: e[3], userTier: parseInt(r || 50), admins: (() => { if ("premium" === a) return 999; switch (!0) {
                                    case r < 500:
                                        return 3;
                                    case r < 1e3:
                                        return 5;
                                    case r < 2e3:
                                        return 10;
                                    default:
                                        return 15 } })() } } return null }

                function s(e) { return [150, 250, 500, 750, 1e3, 1250, 1500, 1750, 2e3, 2250, 2500].find((t => t > e)) || 1e4 }

                function c(e) { var t; const n = (null === e || void 0 === e || null === (t = e.usage) || void 0 === t ? void 0 : t.members) || 0,
                        r = (null === e || void 0 === e ? void 0 : e.plan) || { frequency: "annual", userTier: 150 },
                        a = (null === e || void 0 === e ? void 0 : e.paidPlanType) || "premium",
                        o = "active" === e.status,
                        i = s(n); if (o && ("standard" === a || "premium" === a)) { var c, d; const t = l(null === e || void 0 === e || null === (c = e.plan) || void 0 === c ? void 0 : c.id); return { userTier: (null === e || void 0 === e || null === (d = e.max) || void 0 === d ? void 0 : d.members) || 150, frequency: (null === t || void 0 === t ? void 0 : t.frequency) || (null === r || void 0 === r ? void 0 : r.frequency), planType: (null === t || void 0 === t ? void 0 : t.planType) || a, status: "current" } } return i > 2500 ? { userTier: 2500, planType: "enterprise", frequency: "annual", status: "recommended" } : { userTier: i, planType: "premium", frequency: "annual", status: "recommended" } }

                function d(e) { let t = 0; const n = l(e); if (!n) return null; const { planType: a, frequency: o, userTier: i } = n, s = r[a][i]; return t = "annual" === o ? 12 * s[o] : s[o], { planType: a, frequency: o, userTier: i, cost: t } }

                function u(e, t, n) { const { type: r, status: a, owners: l, usage: c } = e, { type: d, status: u, owners: h, usage: m } = t, p = l.includes(n), f = h.includes(n), v = s((null === c || void 0 === c ? void 0 : c.members) || 0), g = s((null === m || void 0 === m ? void 0 : m.members) || 0), y = i[a] >= i[u], b = o[r] > o[d], w = o[r] === o[d], z = b || w && v >= g; return y && z && p || y && p && !f || y && z && !p && !f || "trialing" === a && "free" === d } }, 12571: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = [{ name: "Alberta", code: "AB" }, { name: "British Columbia", code: "BC" }, { name: "Manitoba", code: "MB" }, { name: "New Brunswick", code: "NB" }, { name: "Newfoundland and Labrador", code: "NL" }, { name: "Northwest Territories", code: "NT" }, { name: "Nova Scotia", code: "NS" }, { name: "Nunavut", code: "NU" }, { name: "Ontario", code: "ON" }, { name: "Prince Edward Island", code: "PE" }, { name: "Quebec", code: "QC" }, { name: "Saskatchewan", code: "SK" }, { name: "Yukon Territory", code: "YT" }] }, 1619: (e, t, n) => { "use strict";
                n.d(t, { A: () => h }); var r = n(99255),
                    a = n(24241),
                    o = n(78396),
                    i = n(10621); const l = ["_id", "id", "__v", "children", "parent", "member", "chart", "type", "is_department", "is_shared", "is_assistant", "embedded_chart", "orientation", "location", "bgcolor", "propagateBg", "users_collapsed", "sample_role", "position", "dropLevel", "expectedMemberCount", "comparators", "dynBgcolor", "dotted", "functionId", "teamId", "fields"],
                    s = ["_id", "id", "__v", "user", "organization", "roleTitle", "photo", "phones", "emails", "linkedIn", "googleId", "relatedRoles", "firstName", "lastName", "fields"],
                    c = { member: { name: 0, firstName: 0, lastName: 0, email: -1, phone: -1, description: -2 }, role: { name: 0, description: -1, locationAddress: -2 } };

                function d(e, t, n) { var o;
                    n = (null === (o = n) || void 0 === o ? void 0 : o.toLowerCase()) || "string"; const i = parseFloat(t),
                        l = parseFloat(String(e || "")); if ("number" === n && !isNaN(i) && !isNaN(l)) return l === i; var s; if (["currency", "location", "number", "string", "url"].includes(n)) return e = String(e || "").toLowerCase(), (null === t || void 0 === t ? void 0 : t.toString) && -1 !== (null === t || void 0 === t || null === (s = t.toString()) || void 0 === s ? void 0 : s.toLowerCase().indexOf(e)); if (["boolean", "switch"].includes(n)) return e === t; if (Array.isArray(e) && Array.isArray(t)) { const n = new Set(e.map((e => e.toLowerCase && e.toLowerCase()))),
                            r = new Set(t.map((e => e.toLowerCase && e.toLowerCase()))); return !!new Set([...n].filter((e => r.has(e)))).size } if ("date" !== n) return !1; { e = String(e); const [n, o] = [r.A.isDate(e), r.A.isDate(t)]; if (n || o) { const r = n ? a.c9.fromISO(e.toUpperCase()) : {},
                                i = o ? a.c9.fromISO(t.toUpperCase()) : {}; return r.day === i.day && r.month === i.month && r.year === i.year } } }

                function u(e, t) { return e = (e || "").toLowerCase(), Array.isArray(t) ? -1 !== t.map((e => (e || "").toString().toLowerCase())).join("").indexOf(e) : !(t instanceof Object) && ("string" === typeof t ? t.toLowerCase && -1 !== t.toLowerCase().indexOf(e) : "number" === typeof t && (t.toString && -1 !== t.toString().indexOf(e))) } const h = { escapeRegExp: function(e) { return e ? "string" !== typeof e && "function" !== typeof e.toString ? "" : ("string" !== typeof e && "function" === typeof e.toString && (e = e.toString()), e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&")) : "" }, queryTree: function(e) { let { roles: t = [], roster: n = {}, query: r = "", searchableFields: a = {}, defaultFieldsMap: l, vacantText: s } = e; const d = function(e) { let t = 0,
                                n = e.length - 1; for (; t < e.length && " " === e[t];) t++; for (; n > t && " " === e[n];) n -= 1; return e.substring(t, n + 1) }(r).toLowerCase(); if (null === t || void 0 === t || !t.length || !d) return []; let h = [],
                            m = !1,
                            { member: p, role: f } = l ? function(e) { var t, n, r, a, o, l, s, d; return { member: { name: 0, [null === (t = e[i.x2.FIRSTNAME]) || void 0 === t ? void 0 : t.id]: c.member.firstName, [null === (n = e[i.x2.LASTNAME]) || void 0 === n ? void 0 : n.id]: c.member.lastName, [null === (r = e[i.x2.EMAIL]) || void 0 === r ? void 0 : r.id]: c.member.email, [null === (a = e[i.x2.PHONE]) || void 0 === a ? void 0 : a.id]: c.member.phone, [null === (o = e[i.x2.DESCRIPTION]) || void 0 === o ? void 0 : o.id]: c.member.description }, role: {
                                        [null === (l = e.roleName) || void 0 === l ? void 0 : l.id]: c.role.name, [null === (s = e.roleDescription) || void 0 === s ? void 0 : s.id]: c.role.description, [null === (d = e.locationAddress) || void 0 === d ? void 0 : d.id]: c.role.locationAddress } } }(l) : c; for (let i of t) { if (i.id === o.Uz || !i || (null === i || void 0 === i ? void 0 : i.type) === o.mv.HIDDEN) continue; let e; for (let t of a.role) { if ("members" === t) { var v; if (!Array.isArray(i[t])) continue; let r = ["single", "assistant", "shared"]; if (0 === (null === (v = i[t]) || void 0 === v ? void 0 : v.length) && (d.includes("vacant") || d === (null === s || void 0 === s ? void 0 : s.toLowerCase())) && r.includes(i.type)) { var g;
                                        e = f[t], e = "number" === typeof e ? e : -99, h.push({ role: Object.assign({}, i), member: null !== (g = i.members) && void 0 !== g && g.length ? n[i.members[0]] : null, match: { key: t, model: "role", query: d }, fieldWeight: e }), m = !0; break } for (let o of i[t]) { let t = n[o]; if (t) { for (let n of a.member)
                                                if (u(d, t[n])) { e = p[n], e = "number" === typeof e ? e : -99, h.push({ role: Object.assign({}, i), member: t, match: { key: n, model: "member", query: d }, fieldWeight: e }), m = !0; break } if (m) break } } } else if ("member" === t) { let t = n[i.member]; if (!t) continue; for (let n of a.member)
                                        if (u(d, t[n])) { e = p[n], e = "number" === typeof e ? e : -99, h.push({ role: Object.assign({}, i), member: t, match: { key: n, model: "member", query: d }, fieldWeight: e }), m = !0; break } } else { var y; if (u(d, i[t])) e = f[t], e = "number" === typeof e ? e : -99, h.push({ role: Object.assign({}, i), member: null !== (y = i.members) && void 0 !== y && y.length ? n[i.members[0]] : null, match: { key: t, model: "role", query: d }, fieldWeight: e }), m = !0 } if (m) break } m = !1 } return h.sort(((e, t) => e.fieldWeight < t.fieldWeight ? 1 : e.fieldWeight > t.fieldWeight ? -1 : 0)), h }, queryTreeAdvanced: function(e) { let { roles: t = [], roster: n = {}, advancedQuery: r = {}, fieldMap: a } = e; const i = (null === r || void 0 === r ? void 0 : r.member) || {},
                            l = (null === r || void 0 === r ? void 0 : r.role) || {},
                            s = Object.entries(i).length,
                            c = Object.entries(l).length; let u, h = []; if (null === t || void 0 === t || !t.length || !s && !c) return h; for (let v of t) { let e = null,
                                t = !0; if (v.id !== o.Uz && (null === v || void 0 === v ? void 0 : v.type) !== o.mv.HIDDEN) { for (let r in l) { var m, p; if (!t) break; if (u = l[r], d(u, v[r], null === (m = a[r]) || void 0 === m ? void 0 : m.type)) e = { role: Object.assign({}, v), member: null !== (p = v.members) && void 0 !== p && p.length ? n[v.members[0]] : null, match: { key: r, model: "role", query: u } };
                                    else t = !1 } if (t) { const r = v.members || (null === v || void 0 === v ? void 0 : v.member) || {}; for (let o of r) { let r = n[o]; if (r) { if (!t) break; for (let n in i) { var f;
                                                u = i[n], d(u, r[n], null === (f = a[n]) || void 0 === f ? void 0 : f.type) ? e = { role: Object.assign({}, v), member: r, match: { key: n, model: "member", query: u } } : t = !1 } } } } t && e && h.push(e) } } return h }, filterItem: function(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : []; if (!e) return !1; for (let r of ["member", "role", "department"])
                            for (let a in e[r]) { if (!([...s, ...l, ...n, "members"].indexOf(a) + 1)) { if ("department" === r && u(t, e[r])) return e.match = { query: t, model: r, key: e[r] }, !0; if (u(t, e[r][a])) return e.match = { query: t, model: r, key: a }, !0 } }
                        return !1 } } }, 65038: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = [{ name: "Alabama", code: "AL" }, { name: "Alaska", code: "AK" }, { name: "American Samoa", code: "AS" }, { name: "Arizona", code: "AZ" }, { name: "Arkansas", code: "AR" }, { name: "California", code: "CA" }, { name: "Colorado", code: "CO" }, { name: "Connecticut", code: "CT" }, { name: "Delaware", code: "DE" }, { name: "District Of Columbia", code: "DC" }, { name: "Federated States Of Micronesia", code: "FM" }, { name: "Florida", code: "FL" }, { name: "Georgia", code: "GA" }, { name: "Guam", code: "GU" }, { name: "Hawaii", code: "HI" }, { name: "Idaho", code: "ID" }, { name: "Illinois", code: "IL" }, { name: "Indiana", code: "IN" }, { name: "Iowa", code: "IA" }, { name: "Kansas", code: "KS" }, { name: "Kentucky", code: "KY" }, { name: "Louisiana", code: "LA" }, { name: "Maine", code: "ME" }, { name: "Marshall Islands", code: "MH" }, { name: "Maryland", code: "MD" }, { name: "Massachusetts", code: "MA" }, { name: "Michigan", code: "MI" }, { name: "Minnesota", code: "MN" }, { name: "Mississippi", code: "MS" }, { name: "Missouri", code: "MO" }, { name: "Montana", code: "MT" }, { name: "Nebraska", code: "NE" }, { name: "Nevada", code: "NV" }, { name: "New Hampshire", code: "NH" }, { name: "New Jersey", code: "NJ" }, { name: "New Mexico", code: "NM" }, { name: "New York", code: "NY" }, { name: "North Carolina", code: "NC" }, { name: "North Dakota", code: "ND" }, { name: "Northern Mariana Islands", code: "MP" }, { name: "Ohio", code: "OH" }, { name: "Oklahoma", code: "OK" }, { name: "Oregon", code: "OR" }, { name: "Palau", code: "PW" }, { name: "Pennsylvania", code: "PA" }, { name: "Puerto Rico", code: "PR" }, { name: "Rhode Island", code: "RI" }, { name: "South Carolina", code: "SC" }, { name: "South Dakota", code: "SD" }, { name: "Tennessee", code: "TN" }, { name: "Texas", code: "TX" }, { name: "Utah", code: "UT" }, { name: "Vermont", code: "VT" }, { name: "Virgin Islands", code: "VI" }, { name: "Virginia", code: "VA" }, { name: "Washington", code: "WA" }, { name: "West Virginia", code: "WV" }, { name: "Wisconsin", code: "WI" }, { name: "Wyoming", code: "WY" }] }, 59177: (e, t, n) => { "use strict";
                n.d(t, { B9: () => m, NY: () => h, Ok: () => d, P8: () => p, RP: () => s, Sn: () => u, St: () => l, ZH: () => c, aG: () => f, xn: () => i }); var r = n(46270),
                    a = n.n(r),
                    o = n(70512); const i = e => { if ("string" === typeof e && "null" !== e && "undefined" !== e) try { return JSON.parse(e) } catch (t) { return e }
                        return "" },
                    l = e => { try { if (!e || !e.length) return { firstName: null, lastName: null }; var t = a().parse(e),
                                n = {}; return n.firstName = t.firstName, n.firstName += t.initials ? " " + t.initials : "", n.lastName = t.lastName, n.lastName += t.suffix ? " " + t.suffix : "", n } catch (r) { return { firstName: "", lastName: "" } } },
                    s = (e, t) => e ? e.length > t ? e.substr(0, t - 1) + "..." : e : "",
                    c = e => e ? e.charAt(0).toUpperCase() + e.slice(1) : "",
                    d = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ""; if ("string" !== typeof e) try { return JSON.stringify(e || "") } catch (t) { return "" }
                        return e },
                    u = e => e.replace(/\w\S*/g, (function(e) { return e.charAt(0).toUpperCase() + e.substr(1).toLowerCase() })),
                    h = e => "s" === e.charAt(e.length - 1) ? e.substr(0, e.length - 1) : e,
                    m = e => { try { return e.match(o.eT) } catch (t) { return console.log(t), !1 } },
                    p = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ""; return "string" !== typeof e ? "" : null === e || void 0 === e ? void 0 : e.trim().toLowerCase() },
                    f = e => "string" === typeof e && e.length > 0 && e || "" }, 59445: (e, t, n) => { "use strict";
                n.d(t, { Sp: () => c, cf: () => d, q$: () => u }); var r = n(10621),
                    a = n(18519),
                    o = n(45956),
                    i = n(37294),
                    l = n(59177); const s = {
                        [o.r_.LOW]: { box: { color: i.Qs.Error[600], thickness: 2 }, line: { color: i.Qs.Error[600], thickness: 2 }, marker: { fill: i.Qs.Error[600], size: 5 } }, [o.r_.MEDIUM]: { box: { color: i.Qs.Warning[200], thickness: 2 }, line: { color: i.Qs.Warning[200], thickness: 2 }, marker: { fill: i.Qs.Warning[200], size: 5 } }, [o.r_.HIGH]: { box: { color: i.Qs.Success[500], thickness: 2 }, line: { color: i.Qs.Success[500], thickness: 2 }, marker: { fill: i.Qs.Success[500], size: 5 } } },
                    c = (e, t, n) => { var a, o, i; return { confidence: e.confidence, status: e.status, suggestionType: e.type, suggestedParentType: null === (a = e.parent) || void 0 === a ? void 0 : a.type, suggestedRoleType: e.role.type, id: e.id, existingRole: t, suggestedParentId: null === (o = e.parent) || void 0 === o ? void 0 : o.id, suggestedRoleId: e.role.id, roleTitle: t ? (0, r.mA)(t) : e.role.title, parentTitle: n ? (0, r.mA)(n) : (null === (i = e.parent) || void 0 === i ? void 0 : i.title) || "" } },
                    d = (e, t) => { var n; const o = [{ ...t.reduce(((e, t) => (e[t.id] = t, e)), {})[(0, r.dP)(t, r.dj.NAME)], value: (0, l.Sn)(e.role.title) }]; return { suggestion: e, suggestionType: e.type, type: a.T.Single, chart: e.chartId, children: [], orientation: "left", propagateBg: "true", stackOverrides: { direction: "grid", minColumns: 3, maxRows: 3 }, sharedStackOverrides: { direction: "grid", minColumns: 3, maxRows: 3 }, parent: null === (n = e.parent) || void 0 === n ? void 0 : n.id, id: e.role.id, dotted: [], dottedReports: [], dropLevel: 0, expectedMemberCount: 0, members: [], fields: o, position: 0, ...(0, r.SB)({ fields: o }) } },
                    u = e => { const t = (n = e.confidence) < o.r_.MEDIUM ? o.r_.LOW : n < o.r_.HIGH ? o.r_.MEDIUM : o.r_.HIGH; var n; return s[t] } }, 39362: (e, t, n) => { "use strict";
                n.d(t, { r: () => p }); var r = n(72952); const a = { name: "Modern", organimiThemeName: "modern", id: "modern", image: "modern", type: "organimi", customizeRoleTypes: !1, base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#b7b7b7", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 200, height: 140, sharedMemberHeight: 50 }, backgroundColor: "#ffffff", colorPosition: "top" }, textAlign: "center", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: .5, color: "#aaaaaa" }, secondary: { visible: !0, style: "dotted", thickness: .5, color: "#aaaaaa" } }, photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !1, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "poppins", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 8, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "default", vacantText: "-vacant-", backgroundColor: "#F8F8F8", legend: { title: "Legend", badgeBarPosition: "top", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-0", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#151515", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    o = { customizeRoleTypes: !1, name: "Modern Dark", organimiThemeName: "moderndark", id: "moderndark", image: "moderndark", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#777777", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 200, height: 140, sharedMemberHeight: 50 }, backgroundColor: "#D8D8D8", colorPosition: "top" }, textAlign: "center", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 1, color: "#dddddd" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#dddddd" } }, photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !1, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "poppins", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#151515", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 60 }, noPhoto: { visible: !0, size: 60, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 60, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#303030", legend: { title: "Legend", badgeBarPosition: "top", badgeShape: "square" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-0", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#151515", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    i = { id: "classic", image: "classic", type: "organimi", name: "Classic", organimiThemeName: "classic", customizeRoleTypes: !1, base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#f2f0f4", shape: "square", size: { auto: !0, autoStrategy: "compact", width: 240, height: 140, sharedMemberHeight: 50 }, backgroundColor: "#f2f0f4", colorPosition: "top" }, textAlign: "center", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#cccccc" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !1, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "poppins", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#FFFFFF", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-0", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    l = { customizeRoleTypes: !1, name: "Classic (Dark)", organimiThemeName: "classicdark", id: "classicdark", image: "classicdark.png", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#1d2129", shape: "square", size: { auto: !0, autoStrategy: "compact", width: 240, height: 140, sharedMemberHeight: 50 }, backgroundColor: "#1d2129", colorPosition: "top" }, textAlign: "center", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#151515" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant4", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !1, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "poppins", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant4", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant4", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#7e868c", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-0", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#ffffff", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    s = { customizeRoleTypes: !1, data: { chart: [{ fontDetails: { size: 15, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000000", align: "center", showLabel: !1, border: "none" }, field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0 } }, { fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000000", align: "center", showLabel: !1, border: "none" }, field: { name: "name", label: "Name", model: "role", type: "string", isDefault: !0 } }], directory: { columns: [{ fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 }, field: { isDefault: !0, label: "First Name", model: "member", name: "firstName", type: "string" } }, { fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 }, field: { isDefault: !0, label: "Last Name", model: "member", name: "lastName", type: "string" } }, { fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 }, field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string" } }, { fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 }, field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string" } }, { fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 }, field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string" } }] }, photoboard: { layout: "layout-0", fields: [], photos: { standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } } }, base: { cards: { cardFrame: { size: { width: 200, height: 140, sharedMemberHeight: 50, auto: !0, autoStrategy: "compact" }, backgroundColor: "#FFFFFF", colorPosition: "top", visible: !0, thickness: 1, color: "#000000", shape: "square" }, textAlign: "center" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#151515" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" }, department: { visible: !1 } }, photos: { standard: { visible: !1, size: 80 }, noPhoto: { visible: !1, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !1, size: 80, imageAvatarId: "vacant1", customImageUrl: "" }, position: "left", shape: "circle", shadow: !1 }, table: { showHeader: !0, showLegend: !0, densePadding: !1, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "poppins", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" }, position: "left", shape: "square", shadow: !1 }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1 } }, chartOptions: { legend: { title: "Legend", badgeShape: "circle", badgeBarPosition: "left" }, stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#FFFFFF" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 }, visible: !0 } }, name: "Printer Friendly", organimiThemeName: "printerfriendly", id: "printerfriendly", image: "printerfriendly", type: "organimi" },
                    c = { name: "Corporate", customizeRoleTypes: !1, organimiThemeName: "corporate", id: "corporate", image: "classic", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#b7b7b7", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 300, height: 130, sharedMemberHeight: 50 }, backgroundColor: "#ffffff", colorPosition: "left" }, textAlign: "left", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 1, color: "#b7b7b7" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !0, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "inter", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { enabled: !0, photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#243458", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#151515", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#f3f3f3", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Hover, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-8", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    d = { name: "Luxe", organimiThemeName: "luxe", customizeRoleTypes: !1, id: "luxe", image: "classic", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#b7b7b7", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 340, height: 140, sharedMemberHeight: 50 }, backgroundColor: "#ffffff", colorPosition: "left" }, textAlign: "right", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#cccccc" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "right", shape: "circle", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !0, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "inter", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "right", shape: "circle", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "right", shape: "circle", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#E2E2E2", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-1", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 600, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    u = { organimiThemeName: "minimalist", name: "Minimalist", id: "minimalist", image: "classic", type: "organimi", customizeRoleTypes: !1, base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#DADADA", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 340, height: 100, sharedMemberHeight: 50 }, backgroundColor: "#ffffff", colorPosition: "left" }, textAlign: "right", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#cccccc" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "right", shape: "fill", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !0, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "Inter", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "right", shape: "fill", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "right", shape: "fill", shadow: !1, standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#f8f8f8", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { visible: !0, expandStyle: r.$.Hover, position: r.d5.insideRight, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-3", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 600, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    h = { name: "Corporate Dark", customizeRoleTypes: !1, organimiThemeName: "corporateDark", id: "corporateDark", image: "classic", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: 1, color: "#19233c", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 300, height: 130, sharedMemberHeight: 50 }, backgroundColor: "#dfe5f2", colorPosition: "left" }, textAlign: "left", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#bdc9e4" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !0, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "inter", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { enabled: !0, photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "aboveLeft", shape: "oval", shadow: !1, standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#243458", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Hover, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-8", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 13, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#26375e", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    m = { name: "Business Card", customizeRoleTypes: !1, organimiThemeName: "businessCard", id: "businessCard", image: "classic", type: "organimi", base: { cards: { cardFrame: { visible: !0, thickness: .5, color: "#DADADA", shape: "oval", size: { auto: !0, autoStrategy: "compact", width: 240, height: 130, sharedMemberHeight: 50 }, backgroundColor: "#ffffff", colorPosition: "top" }, textAlign: "left", fontFamily: "inter" }, lines: { primary: { visible: !0, style: "solid", thickness: 2, color: "#cccccc" }, secondary: { visible: !0, style: "dotted", thickness: 2, color: "#151515" } }, photos: { position: "topLeft", shape: "circle", shadow: !1, standard: { visible: !0, size: 44 }, noPhoto: { visible: !0, size: 44, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 44, imageAvatarId: "vacant1", customImageUrl: "" } }, table: { showHeader: !0, showLegend: !0, densePadding: !0, color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", fontFamily: "inter", align: "left", headerBackgroundColor: "#FFFFFF", cellBackgroundColor: "#FFFFFF", alternateCellBackground: !1, alternateCellBackgroundColor: "#FFFFFF", includeVacantPositions: !1, showPeople: !1 }, directoryPhotos: { position: "left", shape: "square", shadow: !1, standard: { visible: !0, size: 40 }, noPhoto: { visible: !0, size: 40, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 40, imageAvatarId: "vacant4", customImageUrl: "" } }, content: { commonStyle: !1, align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, fontFamily: "poppins" } }, roleTypeOverrides: { shared: { fields: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, case: "none", border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "topLeft", shape: "circle", shadow: !1, standard: { visible: !0, size: 44 }, noPhoto: { visible: !0, size: 44, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 44, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, location: { fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }], photos: { position: "topLeft", shape: "circle", shadow: !1, standard: { visible: !0, size: 44 }, noPhoto: { visible: !0, size: 44, imageAvatarId: "initials", customImageUrl: null }, vacant: { visible: !0, size: 44, imageAvatarId: "vacant1", customImageUrl: "" } }, enabled: !0 }, assistant: { enabled: !1, fields: [] }, department: { enabled: !0, displayType: "card", borderVisible: !1, borderColor: "#ddd", textAlign: "center", fields: [{ field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 14, weight: 700, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } }, chartOptions: { stackDirection: "grid", stackColumns: 4, stackRows: 6, stackDirectionShared: "condensed", stackColumnsShared: 1, stackRowsShared: 4, minifyClusters: !0, compactMode: !0, orderAutomatically: !1, orderBy: "lastName", vacantText: "-vacant-", backgroundColor: "#E2E2E2", legend: { title: "Legend", badgeBarPosition: "left", badgeShape: "circle" }, matrixTopLabel: "Project", matrixSideLabel: "Team" }, orgStructure: { roleCounts: { position: r.d5.below, expandStyle: r.$.Visible, visible: !0, rules: { countByPeople: !1, directReportsOnly: !1, inclDept: !1, inclLocation: !1, inclUnassigned: !0, inclSharedMembers: !0 } } }, data: { directory: { columns: [{ field: { name: "firstName", label: "First Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "lastName", label: "Last Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "phone", label: "Phone", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { name: "email", label: "Email", type: "string", isDefault: !0, model: "member" }, fontDetails: { align: "left", color: "#151515", size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] }, detailsPane: { layout: { name: "layout-1" }, fields: [] }, photoboard: { layout: "layout-0", fields: [], photos: { position: "above", shape: "circle", shadow: !1, standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" } }, showPeople: !1 }, chart: [{ field: { id: "name", name: "name", label: "Name", type: "string", isDefault: !0, model: "member" }, fontDetails: { size: 14, weight: 600, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#000000", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }, { field: { id: "", name: "name", label: "Job Title", type: "string", isDefault: !0, model: "role" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#666666", align: "center", showLabel: !1, showIconLabel: !1, border: "none", dateFormat: { type: "format", value: "yyyy-MM-dd" } } }] } },
                    p = e => { const t = [{ ...a }, { ...o }, { ...c }, { ...h }, { ...d }, { ...m }, { ...u }, { ...i }, { ...l }, { ...s }]; if (null === e || void 0 === e || !e.length) return t; for (let a of t) { var n, r, p, f, v, g, y, b, w; const t = [null === a || void 0 === a || null === (n = a.data) || void 0 === n ? void 0 : n.chart, null === a || void 0 === a || null === (r = a.roleTypeOverrides) || void 0 === r || null === (p = r.shared) || void 0 === p ? void 0 : p.fields, null === a || void 0 === a || null === (f = a.roleTypeOverrides) || void 0 === f || null === (v = f.location) || void 0 === v ? void 0 : v.fields, null === a || void 0 === a || null === (g = a.roleTypeOverrides) || void 0 === g || null === (y = g.assistant) || void 0 === y ? void 0 : y.fields, null === a || void 0 === a || null === (b = a.roleTypeOverrides) || void 0 === b || null === (w = b.department) || void 0 === w ? void 0 : w.fields]; for (let n of t)
                                for (let t of n || []) { if (!t.field.isDefault) continue; const n = (e || []).find((e => { var n, r, a; return e.model === (null === (n = t.field) || void 0 === n ? void 0 : n.model) && e.name === (null === (r = t.field) || void 0 === r ? void 0 : r.name) && e.type === (null === (a = t.field) || void 0 === a ? void 0 : a.type) && e.isDefault }));
                                    n && (t.field.id = n.id) } } return t } }, 279: (e, t, n) => { "use strict";
                n.d(t, { b: () => m }); var r = n(99255),
                    a = n(1619); const o = { ">": (e, t) => e > t, ">=": (e, t) => e >= t, "<": (e, t) => e < t, "<=": (e, t) => e <= t };

                function i(e) { let t, n, r; switch (e) {
                        case "EQ":
                            t = "^", n = "$"; break;
                        case "CONTAINS":
                            t = "", n = ""; break;
                        case "GT":
                            r = ">"; break;
                        case "GE":
                            r = ">="; break;
                        case "LT":
                            r = "<"; break;
                        case "LE":
                            r = "<=" } return { prefix: t, suffix: n, conditionOperator: r } }

                function l(e) { let { data: t = "", target: n = "", operator: r = "CONTAINS" } = e; const { prefix: l, suffix: s, conditionOperator: c } = i(r); return c ? o[c](t, n) : "string" === typeof t && new RegExp(l + a.A.escapeRegExp(n) + s, "i").test((t || "").trim()) }

                function s(e) { let { data: t, target: n = "", operator: r = "CONTAINS" } = e; return t ? Array.isArray(t) ? "NOT_CONTAINS" === r ? 0 !== t.length && !t.includes(n) : t.includes(n) : "string" === typeof t && l({ data: t, target: n, operator: r }) : "" === n }

                function c(e) { let { data: t, target: n = "" } = e; return n = n && n.trim(), /^false|off|no$/.test(n) ? !t : !!/^(true|on|yes)$/.test(n) && ("yes" === t || "on" === t || "true" === t || !0 === t) }

                function d(e) { let { data: t, target: n = "", operator: r = "CONTAINS" } = e; if ("undefined" === typeof t) return !1; const { conditionOperator: a } = i(r); let s, c; if ("number" === typeof t) s = t;
                    else try { s = parseFloat((t || "").trim()) } catch (d) { return !1 }
                    if ("number" === typeof n) c = n;
                    else try { c = parseFloat((n || "").trim()) } catch (d) { return !1 }
                    return isNaN(s) || isNaN(c) || !a ? ("CONTAINS" === r || "EQ" === r) && l({ data: 0 === t ? "0" : (t || "").toString(), target: 0 === n ? "0" : (n || "").toString(), operator: r }) : o[a](s, c) } const u = { url: l, string: l, richtext: s, tags: s, boolean: c, switch: c, number: d, currency: d, date: function(e) { let { data: t, target: n = "", operator: a = "CONTAINS" } = e; const { conditionOperator: o } = i(a); return !(!r.A.isDate(t) || "currentDate" !== n && !r.A.isDate(n)) && (t = r.A.getDateSimple(t), n = "currentDate" === n ? r.A.getDateSimple(new Date) : r.A.getDateSimple(n), o && n ? r.A.compare(t, n, o) : ("EQ" === a || "CONTAINS" === a) && !!r.A.isEqual(t, n, !1)) }, location: function(e) { let { data: t, target: n = "", operator: r = "CONTAINS" } = e; return t ? Array.isArray(t) ? "NOT_CONTAINS" === r ? 0 !== t.length && !t.includes(n) : t.includes(n) : "string" === typeof t && l({ data: t, target: n, operator: r }) : "" === n } },
                    h = (e, t) => { var n; return (null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) && (t[e.field.id] || "unfilled" === e.field.id) },
                    m = e => { let { role: t = {}, ruleTests: n = {}, roster: r = {}, fieldMap: a = {} } = e; const o = {},
                            i = (t.members || []).map((e => r[e])).filter((e => e)); return Object.keys(n).forEach((function(e) { const r = n[e].comparators,
                                l = "OR" === n[e].join,
                                s = "AND" === n[e].join,
                                c = {}; let d, m = !0; for (let n = 0; n < r.length; n++) { var p, f, v, g; const e = r[n]; if (!h(e, a)) { m = !1; continue } const o = (null === (p = r[n]) || void 0 === p ? void 0 : p.type) || (null === (f = r[n]) || void 0 === f || null === (v = f.field) || void 0 === v ? void 0 : v.type) || "string"; if ("role" === (null === e || void 0 === e || null === (g = e.field) || void 0 === g ? void 0 : g.model)) { if (/(string|richtext|tags|date|boolean|number|currency|switch|location|url)/.test(o) && u[o]) { const n = t[e.field.id];
                                        m = u[o]({ data: n, target: e.value, operator: e.operator }) } else "chart" === o ? "unfilled" === e.field.name && (m = /shared|single|assistant/.test(t.type) && !(t.members && t.members.length || t.member)) : m = !1;
                                    void 0 !== d && s ? d = m && d : void 0 !== d && l ? d = m || d : m && (d = m) } else if (i && i.length)
                                    for (const t of i) { let n = !1; /(string|richtext|tags|date|boolean|number|currency|switch|url|location)/.test(o) && u[o] && (n = u[o]({ data: t[e.field.id], target: e.value, operator: e.operator })), "undefined" === typeof c[t.id] ? (c[t.id] = n, m = n) : l ? (c[t.id] = c[t.id] || n, m = m || c[t.id]) : (c[t.id] = c[t.id] && n, m = c[t.id] && n) } else d && s && (d = !1), m = !1; if ("shared" !== t.type) { if (s && !m) break; if (l && m) break } } if ("shared" !== t.type) o[e] = !!m;
                            else { const n = Object.keys(c);
                                o[e] = {}, o[e][t.id] = d; for (let t = 0; t < n.length; t++) o[e][n[t]] = c[n[t]] } })), o } }, 3523: (e, t, n) => { "use strict";
                n.d(t, { Hp: () => a, K7: () => i, cl: () => l }); var r = n(59177); const a = new RegExp("^(https?:\\/\\/)((([a-z\\d*]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$", "i"),
                    o = window.location !== window.parent.location ? document.referrer : document.location.href,
                    i = ((document.location.ancestorOrigins || [])[0], e => { if ("string" === typeof e) { if (e) { if (e.startsWith("/securefiles")) { const t = (e => (e || "").match(/^\/securefiles\/(\w+)\//)[1])(e),
                                        n = (e => (e || "").match(/^\/securefiles\/\w+\/(\w+)\//)[1])(e),
                                        a = function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "photos",
                                                n = null; return e || (e = (document.location.pathname.split("/") || [])[3] || void 0), e ? (n = (0, r.xn)(window.localStorageSafe.getItem("orgCfPolicy.".concat(e, ".").concat(t))), n) : n }(t, n); if (!a) return e; const o = e.replace("/securefiles", "https://".concat(a.distributionDomain)); let i = new URL(o),
                                        l = new URLSearchParams(i.search); return l.append("Policy", a["CloudFront-Policy"]), l.append("Signature", a["CloudFront-Signature"]), l.append("Key-Pair-Id", a["CloudFront-Key-Pair-Id"]), i.search = l.toString(), i.toString() } return e } return e } return "" }),
                    l = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : []; const { origin: n, pathname: r, search: a } = window.location, o = new URLSearchParams(a || ""); return Object.keys(e).forEach((t => o.set(t, e[t]))), t.forEach((e => o.delete(e))), "".concat(n).concat(r, "?").concat(o) } }, 3342: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); const r = e => { if (e) { return /^(?:#|ext\.#?|Ext\.#?|EXT\.#?|extension|Extension|ext|Ext|EXT|x?(?:(?:\(?(?:00|\+)([1-4]\d\d|[1-9]\d?)\)?)?[\-\.\ \\\/]?))(?:(?:\(?(?:00|\+)([1-4]\d\d|[1-9]\d?)\)?)?[\-\.\ \\\/]?)?((?:\(?\d{1,}\)?[\-\.\ \\\/]?){2,})(?:[\-\.\ \\\/]?(?:#|ext\.#?|Ext\.#?|EXT\.#?|extension|Extension|ext|Ext|EXT|x)[\-\.\ \\\/]?(\d+))?$/.test(e) } return !0 },
                    a = e => { try { if ((e = (e || "").trim().toLowerCase()) && e.length) { return new URL(e).host.endsWith("linkedin.com") } return !0 } catch (t) { return !1 } },
                    o = () => "Password must contain at least 10 characters, including uppercase, lowercase letters, special characters (#, ?, !, @, $, %, ^,&, *, -) and numbers.",
                    i = e => e.substr(e.lastIndexOf(".") + 1).toLowerCase(),
                    l = { firstName: e => { var t; return !(null === e || void 0 === e || null === (t = e.trim) || void 0 === t || !t.call(e)) }, linkedIn: e => a(e), phone: e => r(e), email: e => !e || s(e) },
                    s = e => /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e),
                    c = { firstName: "Please enter a name", phone: "Please enter a valid phone number", linkedIn: "Please enter a valid linkedIn URL", email: "Please enter a valid email address", predictable: "Password should not contain your first name or username.", passwordConfirmation: "Password and confirm password did not match.", validatePasswordStrength: o() },
                    d = { checkPasswordMatch: e => { let { password: t = "", passwordConfirmation: n = "", cb: r } = e; return (!t || !n || n === t) && (r && "function" === typeof r && r(), !0) }, isUrlSafe: e => { try { const t = new URL(e); return "http:" === t.protocol || ("https:" === t.protocol || ("ftp:" === t.protocol || "mailto:" === t.protocol)) } catch (t) { return !1 } }, isPasswordPredictable: e => { let { password: t, firstName: n } = e; return !(t && n && t.toLowerCase().includes(null === n || void 0 === n ? void 0 : n.toLowerCase())) }, getUriFromUrl: e => e ? e.replace(/^.*\/\/[^\/]+/, "") : "", getFileExtensionFromFilename: i, getFileExtension: (e, t) => new Promise((n => { if (!0 !== t && (!e || !e.name)) return n(""); let r = "";!0 !== t && (r = i(e.name)), n(r) })), getFileExtensionFromUInt8Array: e => { let t = ""; try { return i(e) } catch (n) { t = "" } return t }, getPasswordStrength: e => { let t = { strength: "weak", notice: o() },
                                n = new RegExp(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{10,}$/),
                                r = new RegExp(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{10,}$/); return n.test(e) ? (t.strength = "strong", t.notice = "") : r.test(e) && (t.strength = "medium", t.notice = ""), t }, getMinPasswordRequirementString: o, isLinkedInUrlValid: a, isPhoneNoValid: r, checkValidDomain: e => { var t = new RegExp(/^((?:(?:(?:\w[\.\-\+]?)*)\w)+)((?:(?:(?:\w[\.\-\+]?){0,62})\w)+)\.(\w{2,6})$/); return e.match(t) }, checkValidEmail: s, isUrlValid: e => { try { if ((e = (e || "").trim().toLowerCase()) && e.length) { const t = new URL(e); return "http:" === t.protocol || "https:" === t.protocol } return !0 } catch (t) { return !1 } }, MemberFieldValidators: l, ERROR_MESSAGES: c } }, 66434: (e, t, n) => { "use strict";
                n.d(t, { D: () => a }); const r = 3;

                function a(e, t) { return t = t || r, new Promise((async (n, r) => { e().then(n).catch((o => { setTimeout((() => { t <= 1 ? r(o) : a(e, t - 1).then(n, r) }), 1500) })) })) } }, 356: (e, t, n) => { "use strict";
                n.d(t, { A: () => y }); var r = n(98960),
                    a = n.n(r);
                a().init("f38e6624f358118c741218a2029dc34a"); const o = e => { try { if (e && e.length && 1 === e.length) { let t = e[0],
                                    n = {}; return "trialing" === t.status ? n.planType = "Free Trial" : n.planType = t.paidPlanType, n.engagementLevel = t.status, n.licenses = t.id, n } return {} } catch (t) { return {} } },
                    i = { identify: e => { let { userId: t } = e; try { a().identify(t) } catch (n) {} }, associateRegisteredUser: e => { let { userId: t } = e; try { a().alias(t) } catch (n) {} }, updateUserProfile: e => { let { firstName: t, lastName: n, email: r, licenses: i } = e; try { let e = o(i),
                                    l = o([i]);
                                a().people.set({ $first_name: t, $last_name: n, $email: r, "Plan Type": l.planType, Status: l.engagementLevel, maxLicense: l }), a().people.append("Licenses", e.licenses) } catch (l) {} } }; var l = n(46987),
                    s = n.n(l),
                    c = n(60194); const d = { init: e => { let { isProd: t, isStaging: n } = e;
                        t ? s().init("organimi/prod", { release: "".concat(c.$U), dom: { textSanitizer: !1, inputSanitizer: !1 } }) : n && s().init("organimi/prod", { release: "".concat(c.$U) }) }, identify: e => { let { userId: t, firstName: n, lastName: r, email: a } = e; try { s().identify(t, { name: "".concat(n, " ").concat(r), email: a }) } catch (o) {} }, captureException: e => { let { exception: t } = e; try { s().captureException(t) } catch (n) {} } }; var u = n(87769),
                    h = n(40854); const m = n.n(h)().create(u.A),
                    p = !1,
                    f = !0,
                    v = () => f,
                    g = () => { const e = window.location.href; return e.indexOf("/embed/") >= 0 || e.indexOf("/public/") >= 0 },
                    y = { init: () => { try { const e = p;!g() && v() && d.init({ isProd: f, isStaging: e }) } catch (e) { console.error(e), console.log("Error::Could not run init") } }, identify: e => { let { userId: t, firstName: n, lastName: r, email: a, licenses: o, maxLicense: l } = e; try { v() && (g() || d.identify({ userId: t, firstName: n, lastName: r, email: a }), i.identify({ userId: t }), i.updateUserProfile({ userId: t, firstName: n, lastName: r, email: a, licenses: o, maxLicense: l })) } catch (s) { console.error(s), console.log("Error::Could not run t identify") } }, associateRegisteredUser: e => { let { userId: t } = e; try { v() && i.associateRegisteredUser({ userId: t }) } catch (n) { console.error(n), console.log("Error::Could not run t associate") } }, trackEvent: async e => { try { v() && await m.put("/trackevent", e) } catch (t) { console.error(t), console.log("Error::Could not run t event") } }, captureException: e => { let { exception: t } = e; try {!g() && v() && d.captureException({ exception: t }) } catch (n) { console.error(n), console.log("Error::Could not run t event") } } } }, 81780: (e, t, n) => { "use strict";
                n.d(t, { Mv: () => v, N_: () => k, Sd: () => C, WC: () => b, YK: () => s, aH: () => d, dL: () => g, eO: () => p, fn: () => c, gD: () => S, l: () => A, pr: () => M, u0: () => x, zi: () => y }); var r = n(43862),
                    a = n(59177),
                    o = n(78396),
                    i = n(10621),
                    l = n(15813); const s = { hors: 24, vers: 16, clusterVers: 12, clusterHors: 12, clusterCols: 16, branch: 32, sharedMemberHeight: 50, assistant: { vers: 32, hors: 8 }, matrixCol: 32 },
                    c = e => "leftright" === e ? { ...s, vers: 18, hors: 36 } : s,
                    d = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const n = t.multi || !1,
                            r = t.breakBy || "level"; let a; if (n) { if (/matrixCells|function|team/i.test(r)) { var o, i;
                                a = m(e, t), a.pageCount = Object.keys((null === (o = a) || void 0 === o || null === (i = o.root) || void 0 === i ? void 0 : i.partitions) || {}).length } else { a = {}; const n = h(e, t),
                                    r = (null === t || void 0 === t ? void 0 : t.orientation) || "landscape"; if (t.showToC) { const e = l.ek[null === t || void 0 === t ? void 0 : t.pageSize] || 96,
                                        a = "auto" !== (null === t || void 0 === t ? void 0 : t.pageSize) && l.q2[null === t || void 0 === t ? void 0 : t.pageSize] || l.q2.letter,
                                        o = "landscape" === r ? null === a || void 0 === a ? void 0 : a.width : null === a || void 0 === a ? void 0 : a.height,
                                        i = o * e - 75 - 40 - ("landscape" === r ? null === a || void 0 === a ? void 0 : a.height : null === a || void 0 === a ? void 0 : a.width) * e * .2 - 56; if (0 !== o) { let e = 50; const r = Math.round(i / e),
                                            a = Object.keys(n || {}).reduce(((e, t) => n[t].pageNumber > e ? n[t].pageNumber : e), 1),
                                            o = Math.ceil(a / r);
                                        Object.keys(n).forEach((e => { "object" === typeof n[e] && (n[e].pageNumber += o, n[e].prevPageNumber += o) })), n.tableOfContents = { visible: t.showToC, totalToCPages: o, maxRowsPerPage: r }, n.pageCount = a + o } } a = n, a.pageCount = Object.keys(a || {}).reduce(((e, t) => { var n, r; return (null === (n = a[t]) || void 0 === n ? void 0 : n.pageNumber) > e ? null === (r = a[t]) || void 0 === r ? void 0 : r.pageNumber : e }), 1) } } else a = u(e, t), a.pageCount = 1; return a },
                    u = (e, t) => { let { parentChildMap: n } = e, { nLevels: r, topRoles: a } = t;

                        function i(e, t) { const r = n[e] || []; if (0 === t && r.length) this.push(e);
                            else if (t > 0)
                                for (let n = 0; n < r.length; n++) i.apply(this, [r[n], t - 1]); return this } const l = {}; if (null !== a && void 0 !== a && a.length)
                            for (let { id: o, name: s } of a) l[o] = { name: s, pageNumber: 1, prevPageNumber: 1, prevTopId: o, nLevels: r, pageBreaks: !r && [] || i.apply([], [o, Math.max(r - 1, 0)]) };
                        else l[o.Uz] = { name: o.Uz, pageNumber: 1, prevPageNumber: 1, prevTopId: o.Uz, nLevels: r, pageBreaks: !r && [] || i.apply([], [o.Uz, r]) }; return l },
                    h = (e, t) => { let { roleMap: n, parentChildMap: r } = e, { nLevels: a, nTopLevels: i, topRoles: l, breakBy: s, pageMapTopIds: c, getPageForRoleFn: d, getRootPathSelector: u } = t; const h = Math.max(parseInt(a), 2),
                            m = Math.max(parseInt(i), 2),
                            p = {}; let f = [],
                            v = 1;
                        f = null !== l && void 0 !== l && l.length ? l.map((e => e.id)) : r[o.Uz]; const g = f.map((e => n[e])).filter((e => e)); for (let M = 0; M < g.length; M++) { const { id: e, name: t } = g[M];
                            p[e] = { pageNumber: 1, name: t, prevTopId: o.Uz, prevPageNumber: 1 }, S.call(p, { roleId: e, curLevel: 2, prevId: e, fromRoot: !0 }) } if ("manual" === s && (null === c || void 0 === c ? void 0 : c.length) >= 1) { for (let t = 0; t < c.length; t++) { let e = c[t]; const { id: r, prevId: a } = e, { name: o } = n[r];
                                p[r] = { pageNumber: t + 2, name: o, prevTopId: a } } let e = null === c || void 0 === c ? void 0 : c.map((e => ({ ...e }))); for (let t = 0; t < (null === e || void 0 === e ? void 0 : e.length); t++) { var y; let n = e[t],
                                    { id: a } = n,
                                    o = r[a]; if (null !== o && void 0 !== o && o.length && null !== (y = Object.keys(p || {})) && void 0 !== y && y.length)
                                    for (let t of o) { var b, w; if (null !== (b = Object.keys(p)) && void 0 !== b && b.includes(t) && (null === (w = p[t]) || void 0 === w ? void 0 : w.prevId) !== a) { let n = null === e || void 0 === e ? void 0 : e.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === t));
                                            e[n].prevId = a } } } for (let t = 0; t < (null === e || void 0 === e ? void 0 : e.length); t++) { let n = e[t],
                                    { id: r, prevId: a } = n; var z; if (p[a]) p[r].prevPageNumber = null === (z = p[a]) || void 0 === z ? void 0 : z.pageNumber;
                                else if ("root" !== a) { let t = d(a, e.map((e => null === e || void 0 === e ? void 0 : e.id)), u); var x, A, k; if (1 === t) a = null === (x = u(a)[0]) || void 0 === x ? void 0 : x.id;
                                    else a = null === (A = e[t - 2]) || void 0 === A ? void 0 : A.id; if (p[a]) p[r].prevPageNumber = null === (k = p[a]) || void 0 === k ? void 0 : k.pageNumber } } } return p;

                        function S(e) { let { roleId: t, curLevel: a = 1, prevId: o, fromRoot: i = !1 } = e;
                            o || (o = t); const l = i ? m : h; for (let c of r[t]) { const e = n[c];
                                e && ("levels" === s ? a >= l && r[c].length ? (v += 1, this[o].pageBreaks = this[o].pageBreaks || [], this[o].pageBreaks.push(c), this[c] = { pageNumber: v, prevPageNumber: this[o].pageNumber, prevTopId: o, name: e.name }, S.call(this, { roleId: c, curLevel: 2, prevId: c, fromRoot: !1 })) : S.call(this, { roleId: c, curLevel: a + 1, prevId: o, fromRoot: i }) : "department" !== s && "location" !== s || (e.type === s && e.children && e.children.length > 0 ? (v += 1, this[o].pageBreaks = this[o].pageBreaks || [], this[o].pageBreaks.push(c), this[c] = { pageNumber: v, prevPageNumber: this[o].pageNumber, prevTopId: o, name: e.name }, S.call(this, { roleId: c, curLevel: 2, prevId: c, fromRoot: !1 })) : S.call(this, { roleId: c, curLevel: a + 1, prevId: o, fromRoot: i }))) } } },
                    m = (e, t) => { let { roles: n } = e; const r = t.breakBy,
                            a = n.filter((e => "function" === e.type)).sort(((e, t) => (null === e || void 0 === e ? void 0 : e.position) || 0 - (null === t || void 0 === t ? void 0 : t.position) || 0)),
                            o = n.filter((e => "team" === e.type)).sort(((e, t) => (null === e || void 0 === e ? void 0 : e.position) || 0 - (null === t || void 0 === t ? void 0 : t.position) || 0)); let i = 1; const l = { root: { partitions: {}, prevPageNumber: null, prevTopId: null, name: "Root" } }; return "function" === r ? a.reduce(((e, t) => (e.root.partitions[i] = [t.id.toString(), ...o.map((e => e.id.toString()))], i++, e)), l) : "team" === r ? o.reduce(((e, t) => (e.root.partitions[i] = [t.id, ...a.map((e => e.id.toString()))], i++, e)), l) : a.reduce(((e, t) => { let n = (t.id || "").toString(); return o.forEach((t => { let r = (t.id || "").toString();
                                e.root.partitions[i] = [n, r], i++ })), e }), l) };

                function p(e, t) { const n = (null === e || void 0 === e ? void 0 : e.tableOfContents) || {},
                        r = (null === n || void 0 === n ? void 0 : n.totalToCPages) || 0; return t > 1 && e ? null !== n && void 0 !== n && n.visible && t <= r + 1 ? null : Object.keys(e || {}).find((n => { var r; const a = e[n]; if (n !== o.Uz && (a.pageNumber === t || null !== (r = a.partitions) && void 0 !== r && r.find((e => e.pageNumber === t)))) return n })) : null } const f = e => (t, n) => ((e, t, n) => { let r = (0, a.Ok)(e[n]).toLowerCase(),
                        o = (0, a.Ok)(t[n]).toLowerCase(); return "" === r ? 1 : "" === o ? -1 : r.localeCompare(o) })(t, n, e);

                function v() { var e, t; let n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
                        r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        { pageMap: a = {}, topRoleId: l, orderBy: s, orderAutomatically: c, numRolesInSubtreeMap: d, curPage: u } = arguments.length > 2 ? arguments[2] : void 0; const h = Object.keys(a || {}); let m;
                    l && l !== o.Uz && (m = [l]); const p = JSON.parse(JSON.stringify(n.map((e => ({ ...e }))))),
                        v = { id: o.Uz, children: [], members: [], type: o.Uz }; let g = {},
                        y = []; for (let o of p) { var b;
                        null !== (b = m) && void 0 !== b && b.length || o.parent && !o.isRoot || (y.push(o.id), o.parent = v.id), g[o.id] = o } return null !== (e = y) && void 0 !== e && e.length || null === (t = m) || void 0 === t || !t.length || (y = m), y.sort(((e, t) => (g[e].position || 0) <= (g[t].position || 0) ? -1 : 1)), y.length ? (v.children = y, function e(t, n) { const o = a && a[n];
                        n = o && n; const l = [...new Set(t.children)]; let m, p = [...l]; return c ? "department" === s ? (p.sort(((e, t) => d[e] - d[t])), m = [...p]) : (p = l.map((e => { let t = { ...g[e] }; return t.firstName = t.members && (0, i.II)(r[null === t || void 0 === t ? void 0 : t.members[0]]) || "", t.lastName = t.members && (0, i.US)(r[null === t || void 0 === t ? void 0 : t.members[0]]) || "", t.name = t && (0, i.mA)(t) || "", t })), p.sort(f(s)), m = p.map((e => null === e || void 0 === e ? void 0 : e.id))) : m = l, t.children = m.map((r => { var a; const i = h.includes(t.id) && n !== (null === t || void 0 === t ? void 0 : t.id),
                                l = o && (o.pageBreaks || []).includes(t.id),
                                s = !(null === o || void 0 === o || null === (a = o.partitions) || void 0 === a || !a.find((e => e.ids.includes(r) && e.pageNumber === u))),
                                c = !(null === o || void 0 === o || !o.partitions); return !(i || l) && (s || !c) && null !== r && void 0 !== r && r.length && g[r] ? e({ ...g[r] }, c ? r : n || r) : r && r.length && !g[r] ? null : void 0 })).filter((e => e)), t }(v, o.Uz)) : v }

                function g(e) { const t = this; return e ? [e] : t.ids.filter((e => !t.entities[e].parent)) }

                function y() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : o.Uz; const t = this; return function n(r, a) { var i; const l = this; let s; var c;
                        r && r !== o.Uz ? s = ((null === (c = t.entities[r]) || void 0 === c ? void 0 : c.children) || []).filter((n => { var a, o, i; return n !== r && ((null === (a = t.entities[n]) || void 0 === a ? void 0 : a.parent) === r || !(null !== (o = t.entities[n]) && void 0 !== o && o.parent) || (null === (i = t.entities[n]) || void 0 === i ? void 0 : i.parent) === e) })) || [] : s = g.call(t); if (null !== (i = s) && void 0 !== i && i.length) { l[a] = l[a] || [], l[a].push(...s); for (let e of s) e && n.apply(l, [e, a + 1]) } return l }.apply([], [e, 1]) }

                function b() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; return function t(n) { let r = this,
                            a = (e[n] || []).length; return r += a, a ? r < 20 ? t.call(r, n + 1) : r - 4 < 20 ? n : Math.max(n - 1, 1) : r }.call(0, 1) }

                function w(e) { const t = this; return function e(n) { const r = t.entities[n]; return r && r.parent ? (this.push(r.parent), e.call(this, r.parent)) : this }.call([], e) }

                function z(e) { let { memberIndexMap: t, roleId: n, memberIds: r } = e; for (let a = 0; a < (null === r || void 0 === r ? void 0 : r.length); a++) { const e = r[a];
                        t[e] = t[e] || []; const o = t[e].indexOf(n); - 1 !== o && t[e].splice(o, 1) } }

                function x(e) { let { memberIndexMap: t, roleId: n, memberIds: r = [] } = e; for (let a = 0; a < (null === r || void 0 === r ? void 0 : r.length); a++) { const e = r[a];
                        t[e] = t[e] || []; - 1 === t[e].indexOf(n) && t[e].push(n) } }

                function A(e) { let { memberIndexMap: t, roleId: n } = e; if (!n || "object" !== typeof t) return; const r = Object.keys(t); for (let a of r) { let e = t[a].indexOf(n); - 1 === e || t[a].splice(e, 1) } }

                function k(e) { r.k9.upsertMany(this, e) }

                function S(e) { const t = this; for (let n of e) { const a = t.entities[n]; if (!a) continue; const { members: o = [] } = a;
                        z({ memberIndexMap: t.memberIndexMap, roleId: n, memberIds: o }), r.k9.removeMany(t, e) } }

                function M(e) { const t = this,
                        n = w.call(t, e),
                        r = (t.entities[n[0]] || {}).children || []; for (let a of [...n, ...r]) t.collapseMap[a] = !1;
                    t.reFocusId = e } const E = e => { let t, { siblings: n, parentRole: r, currentRoleId: a } = e; const o = null === n || void 0 === n ? void 0 : n.indexOf(a); let i = null === n || void 0 === n ? void 0 : n.length; return t = 0 === o && 1 === i ? { id: null === r || void 0 === r ? void 0 : r.id, position: "below" } : 0 === o ? { id: n[1], position: "left" } : o === i - 1 ? { id: n[i - 2], position: "right" } : { id: n[o + 1], position: "left" }, t },
                    C = (e, t, n, r, a) => { let o; if (Object.keys(e || {}).length <= 1) return o = { id: "root", position: "below" }, o; const i = e[t]; let l, s, c = e[null === i || void 0 === i ? void 0 : i.parent],
                            d = null === i || void 0 === i ? void 0 : i.children,
                            u = null === c || void 0 === c ? void 0 : c.children; const h = null === u || void 0 === u ? void 0 : u.length; var m; if (null !== i && void 0 !== i && i.parent || (l = (e => { const t = []; for (let n in e) { let r = e[n];!r || null !== r && void 0 !== r && r.parent || t.push(r) } return t })(e), s = l.filter((e => (null === e || void 0 === e ? void 0 : e.id) !== (null === i || void 0 === i ? void 0 : i.id)))), null !== d && void 0 !== d && d.length) o = (a || r) && h && a ? E({ siblings: u, parentRole: c, currentRoleId: t }) : { id: (null === c || void 0 === c ? void 0 : c.id) || "root", position: "level", children: d };
                        else if ((null === i || void 0 === i || !i.parent) && (null === (m = s) || void 0 === m ? void 0 : m.length) >= 1) { let e = l.map((e => e.id));
                            o = E({ siblings: e, parentRole: { id: "root" }, currentRoleId: t }) } else o = h ? E({ siblings: u, parentRole: c, currentRoleId: t }) : { id: "root", position: "below" }; return o } }, 29794: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(78396),
                    a = n(31286),
                    o = n(14556); const i = () => { const e = (0, o.wA)(),
                        t = (0, o.d4)((e => { const t = (e.notification.notifications || []).filter((e => "banner" === e.displayType && !e.isRead)); return t.length ? t[t.length - 1] : null })),
                        n = (null === t || void 0 === t ? void 0 : t.id) || null,
                        i = !!n; return { appHeaderHeight: i ? r.Ay.toolbars.headerHeight + r.Ay.toolbars.bannerHeight : r.Ay.toolbars.headerHeight, bannerOpen: i, closeBanner: () => { n && (async t => { await e(a.tZ.updateReadStatus({ notificationIdsToMarkAsRead: [t] })) })(n) }, bannerNotification: t } } }, 24251: (e, t, n) => { "use strict";
                n.d(t, { A: () => c }); var r = n(66856),
                    a = n(53109),
                    o = n(14556),
                    i = n(91688),
                    l = n(65043); const s = { print: e => { let { event: t, history: n, params: a, ctrlOrMetaKey: o, which: i } = e;
                            o && 80 === i && (t.preventDefault(), n.push((0, r.Qo)({ ...a, base: "protected" }))) }, zoom: e => { let { event: t, ctrlOrMetaKey: n, which: r, dispatch: o, location: i } = e;
                            n && "wheel" === t.type && t.preventDefault(), !n || 48 !== r && 61 !== r && 107 !== r && 173 !== r && 109 !== r && 187 !== r && 189 !== r || (t.preventDefault(), 61 === r || 107 === r || 187 === r ? i.pathname.endsWith("printPreview") ? o((0, a.ET)()) : o((0, a.nF)()) : 109 === r || 173 === r || 189 === r ? i.pathname.endsWith("printPreview") ? o((0, a.DX)()) : o((0, a.ke)()) : 48 !== r && 96 !== r || (i.pathname.endsWith("printPreview") ? o((0, a.cK)(1)) : o((0, a.pW)(1)))) }, find: e => { let { event: t, ctrlOrMetaKey: n, which: r, fieldRef: a } = e; if (n && 70 === r) { t.preventDefault(); const { current: e } = a;
                                null !== e && e.focus() } } },
                    c = e => { const t = (0, o.wA)(null),
                            n = (0, i.useHistory)(),
                            r = (0, i.useParams)(),
                            a = (0, i.useLocation)(); let c = (0, l.useRef)(null); const d = o => { let { which: i, ctrlKey: l, metaKey: d } = o; const u = { event: o, dispatch: t, history: n, params: r, which: i, ctrlOrMetaKey: !0 === l || !0 === d || 91 === i || 93 === i, location: a, fieldRef: c }; for (let t = 0; t < e.length; t++) { let n = e[t];
                                n = n.toLowerCase(), s[n](u) } }; return (0, l.useEffect)((() => (document.addEventListener("keydown", d), () => { document.removeEventListener("keydown", d) })), []), { fieldRef: c, setFieldRef: e => { c.current = e } } } }, 22264: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(41473); const o = () => (0, r.useContext)(a.B) }, 64418: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(23523);

                function o() { const [, e] = (0, r.useContext)(a.c) || [{}, () => {}]; return { confirmAction: function(t) { let { title: n, message: r, execFunc: a, cancelFunc: o, confirmButtonText: i = "Confirm", cancelButtonText: l = "Cancel" } = t;
                            e({ open: !0, title: n, message: r, execFunc: a, cancelFunc: o, confirmButtonText: i, cancelButtonText: l }) } } } }, 86255: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(65043);

                function a() { const e = (0, r.useRef)(null); return { eventDebounce: (t, n) => function(r) {
                            (null === r || void 0 === r ? void 0 : r.persist) && r.persist(), clearTimeout(e.current), e.current = setTimeout((() => { t.apply(this, arguments) }), n) } } } }, 86597: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(14556),
                    a = n(91688),
                    o = n(94916),
                    i = n(42006),
                    l = n(78226);

                function s() { const { orgId: e } = (0, a.useParams)(), t = (0, r.d4)(l.B), [n] = (0, o.A)("activeLicenseId", null, !0), [s] = (0, o.A)("license.recent.organization.".concat(n)), c = (0, r.d4)(i.zl) || [], d = (0, r.d4)(i.Ok) || [], u = e && (null === t || void 0 === t ? void 0 : t.id) === e && t, h = c.find((t => ":orgId" !== e && t.id === e)), m = d.find((e => e.id === s)), p = c[0], f = d[0]; return h || m || p || f || u } }, 84: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(1523);

                function o(e) { var t, n; const [o, i, l] = (0, r.useContext)(a.MV) || [{}, () => {}, {}]; return { toggleDialog: function() { let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            i((n => ({ ...n, [e]: { open: !n[e].open, ignoreRouteEffects: n[e].ignoreRouteEffects, props: t } }))) }, closeDialog: function() { let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            i((n => ({ ...n, [e]: { open: !1, ignoreRouteEffects: n[e].ignoreRouteEffects, props: t } }))) }, updateDefaultProps: function() { let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            i((n => ({ ...n, [e]: { ...n[e], props: { ...n[e].props, ...t } } }))) }, openDialog: function() { let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            i(arguments.length > 1 && void 0 !== arguments[1] && arguments[1] ? { ...l, [e]: { open: !0, ignoreRouteEffects: l[e].ignoreRouteEffects, props: t } } : n => ({ ...n, [e]: { open: !0, ignoreRouteEffects: n[e].ignoreRouteEffects, props: t } })) }, open: null === (t = o[e]) || void 0 === t ? void 0 : t.open, props: null === (n = o[e]) || void 0 === n ? void 0 : n.props } } }, 43781: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r = n(43331),
                    a = n(14556),
                    o = n(94916),
                    i = n(65043); const l = 3e5,
                    s = 3e6,
                    c = 100;

                function d(e) { let { orgId: t } = e; const n = (0, a.wA)(),
                        [, d] = (0, o.A)("orgCfPolicy.".concat(t, ".photos"), {}),
                        [, u] = (0, o.A)("orgCfPolicy.".concat(t, ".logos"), {}),
                        h = (0, i.useRef)(null),
                        m = (0, i.useRef)(0),
                        [p, f] = (0, i.useState)(!1),
                        v = async e => { let { orgId: t, resourceType: a = "photos" } = e; try { const { payload: e, error: o } = await n(r.no.getOrgCfPolicy({ orgId: t, resourceType: a })); if (o) throw clearInterval(h.current), f(!1), "Cannot get CF Policy"; return null !== e && void 0 !== e && e.cfRequestSignature && "photos" === a ? d(e.cfRequestSignature) : null !== e && void 0 !== e && e.cfRequestSignature && "logos" === a && u(e.cfRequestSignature), { cfRequestSignature: e.cfRequestSignature } } catch (o) { return console.error("Error fetching org cf policy", o), { cfRequestSignature: {} } } }; return (0, i.useEffect)((() => { async function e(e) { let { resourceType: n = "photos" } = e;
                            f(!0); const { cfRequestSignature: r } = await v({ orgId: t, resourceType: n });
                            h.current && clearInterval(h.current), f(!1); const a = 1e3 * (null !== r && void 0 !== r && r.expires ? new Date(null === r || void 0 === r ? void 0 : r.expires) : new Date(Date.now() + l)).getTime() - (new Date).getTime(); let o = Math.max(l, Math.min(Math.floor(.8 * a), s));!isNaN(o) && o || (o = s), h.current = setInterval((() => { m.current = (m.current || 0) + 1, m.current > c ? clearInterval(h.current) : v({ orgId: t }) }), o) }
                        return t && (e({ resourceType: "photos" }), e({ resourceType: "logos" })), () => { h.current && clearInterval(h.current) } }), [t]), p } }, 3643: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(91688),
                    a = n(57546);

                function o() { const { base: e } = (0, r.useParams)(), { jobId: t } = (0, a.A)(); return !("print" !== e || !t) } }, 9368: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(91688);

                function a() { const { base: e } = (0, r.useParams)(); return "public" === e || "embed" === e || "community" === e } }, 94916: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(65043),
                    a = n(14556),
                    o = n(69645),
                    i = n(59269),
                    l = n(49244);

                function s(e, t, n) { const s = (0, a.wA)(),
                        [c, d] = (0, r.useContext)(l.O),
                        u = (0, a.d4)(i._),
                        h = n ? u : c; let m = window.localStorageSafe.getItem(e),
                        p = h[e]; if (m) try { p = JSON.parse(m) } catch (f) { "string" === typeof m ? p = m : window.localStorageSafe.removeItem(e) } p || (p = t); return [p, t => { try { const r = t instanceof Function ? t(p) : t;
                            (t => { n ? s((0, o.h)({ key: e, value: t })) : d((n => ({ ...n, [e]: t }))) })(r), t ? window.localStorageSafe.setItem(e, JSON.stringify(r)) : window.localStorageSafe.removeItem(e) } catch (r) { console.log(r) } }] } }, 47556: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(86597),
                    a = n(91688),
                    o = n(66856);

                function i() { const e = (0, r.A)(),
                        t = (0, a.useHistory)(); return () => { const n = null !== e && void 0 !== e && e.id ? (0, o.r2)({ orgId: null === e || void 0 === e ? void 0 : e.id }) : (0, o.ze)();
                        t.replace(n) } } }, 63560: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(50882);

                function o() { const { activeNotificationForDialog: e, canFetchNotifications: t, fetchedNotifications: n, getNotifications: o, handleNotificationDialogOpen: i, handleNotificationDialogClose: l, notificationsLastUpdated: s, notificationsLoading: c, openDialogCategory: d, restartNotificationCallbackRef: u, pingIntervalState: [h, m], maxAutoFetchAttemptsState: [p, f], notificationAutoFetchCountRef: v } = (0, r.useContext)(a.V2); return { activeNotificationForDialog: e, canFetchNotifications: t, fetchedNotifications: n, getNotifications: o, handleNotificationDialogOpen: i, handleNotificationDialogClose: l, notificationsLastUpdated: s, notificationsLoading: c, openDialogCategory: d, restartNotificationPings: e => { let { source: t, callback: n } = e;
                            v.current < p ? (a.j4[t].pingInterval < h && m(a.j4[t].pingInterval), p - v.current < a.j4[t].maxAutoFetchAttempts && (f(a.j4[t].maxAutoFetchAttempts), v.current = 0)) : (v.current = 0, m(a.j4[t].pingInterval || a.J0), f(a.j4[t].maxAutoFetchAttempts || 10)), n && "function" === typeof n && (u.current[t] = n) } } } }, 36138: (e, t, n) => { "use strict";
                n.d(t, { u: () => a }); var r = n(91688); const a = e => (0, r.useRouteMatch)(e) || { params: {} } }, 6124: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(61342);

                function o() { const [e, t] = (0, r.useContext)(a.w); return { pageReady: e, updatePageReady: () => { t(!0) }, resetPageReady: () => { t(!1) } } } }, 2173: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(65043); const a = (e, t) => { const [n, a] = (0, r.useState)(!1), o = (0, r.useRef)(), i = (0, r.useCallback)((async () => { e && "function" === typeof e && await e.apply(void 0, o.current) }), [e]); return (0, r.useEffect)((() => { let e = !0; return (async () => { n && (await i(), e && a(!1)) })(), () => { e = !1 } }), [n]), [n, function(e) { e && e.preventDefault && e.preventDefault(), o.current = arguments, a(!t || t.apply(null, arguments)) }] } }, 85899: (e, t, n) => { "use strict";
                n.d(t, { A: () => u }); var r = n(65043),
                    a = n(36138),
                    o = n(86255),
                    i = n(66856),
                    l = n(14556),
                    s = n(84091),
                    c = n(10621),
                    d = n(94916); const u = e => { let { searchOrg: t = null, chunkSize: n, defaultSort: u = c.x2.FIRSTNAME } = e; const h = (0, l.wA)(),
                        [m, p] = (0, r.useState)(1),
                        [f, v] = (0, r.useState)(!1),
                        [g, y] = (0, d.A)("mobile.people.sort", u),
                        [b, w] = (0, d.A)("mobile.people.sortOrder", 1),
                        [z, x] = (0, r.useState)([]),
                        [A, k] = (0, r.useState)(""),
                        [S, M] = (0, r.useState)(n || 10),
                        [E, C] = (0, r.useState)(0),
                        { params: { orgId: T } } = (0, a.u)([(0, i.si)(), (0, i.N9)()]),
                        { eventDebounce: H } = (0, o.A)(),
                        L = H(((e, t) => { M(n || 10), k(t) }), 500),
                        I = async e => { let { forceStartPageNumber: n } = e; const { error: r, payload: a } = await h(s.OH.getPeoplePaginated({ orgId: t || T, page: n || m, pageSize: S, search: A, sort: 1 === b ? g : "-".concat(g) })); if (v(!1), !r) { let { totalCount: e, people: t } = a;
                                t = t.map(c.SB), C(e || 0), x(t || []) } }; return (0, r.useEffect)((() => { v(!0), I({ forceStartPageNumber: 1 }) }), [A, S, t, g, b]), { query: A, people: z, loading: f, totalResults: E, sortBy: g, handleInputChange: (e, t) => { var n;
                            x([]), v(!0), L(e, t || (null === e || void 0 === e || null === (n = e.target) || void 0 === n ? void 0 : n.value)) }, handleSortChange: e => {
                            (c.x2[e] || Object.values(c.x2).includes(e)) && y(e) }, handleLoadMore: () => { M(S + 10) }, handleLoadPrevPage: async () => { const e = Math.max(m - 1, 1);
                            v(!0), await I({ forceStartPageNumber: e }), p((() => e)) }, handleLoadNextPage: async () => { const e = m + 1;
                            v(!0), await I({ forceStartPageNumber: e }), p((() => e)) }, curPage: m, sortOrder: b, handleSortOrderChange: e => { w(e) } } } }, 91945: (e, t, n) => { "use strict";
                n.d(t, { A: () => f }); var r = n(65043),
                    a = n(94642),
                    o = n(14556),
                    i = n(69219),
                    l = n(98433),
                    s = n(23993),
                    c = n(66588),
                    d = n(78396),
                    u = n(15813),
                    h = n(10621),
                    m = n(45418),
                    p = n(91688); const f = () => { const [e, t] = (0, r.useState)(null), { setPageNumber: n, pageCount: f } = (0, a.A)(), { chartId: v } = (0, p.useParams)(), g = (0, o.d4)(i.uo), y = (0, o.d4)(l.ez), b = (0, o.d4)(s.KZ), w = (0, o.d4)(s.Xp), z = (0, o.d4)(s.aC), x = (0, o.d4)(s.N1), A = (0, o.d4)(l.xy), k = (0, o.d4)(m.xt), { show: S } = (0, c.A)(), M = (0, o.wA)(), E = g === d.uI.PRINT_PREVIEW && z.breakBy === u.N0.MANUAL && z.pagination === u.ti.MULTIPLE && A.printType === u._6.CHART, C = g === d.uI.PRINT_PREVIEW, T = { showCreatePage: E, showDirectReportDirectionOverrides: C, showPrintContextMenu: E || C }, H = () => { t(null) }; return { createPageAnchorEl: e, handleCreatePagePopoverClose: H, handleCreatePagePopoverOpen: e => { t(e.target) }, addPageBreak: e => { var t; let { role: n } = e; if (b.includes(n.id)) { const e = b.indexOf(null === n || void 0 === n ? void 0 : n.id) + 1; return void S("Page already created from this role. (See page ".concat(e, ")"), "info", 2e3) } const r = x[n.id];
                            M((0, l.lh)({ chartId: v || (null === r || void 0 === r ? void 0 : r.chart), pageBreak: { id: null === r || void 0 === r ? void 0 : r.id, name: (0, h.mA)(r), personName: (0, h.Bx)(n.members[0]) || (0, h.Bx)(k[n.members[0]]), firstRoleMember: null !== r && void 0 !== r && r.members && (null === r || void 0 === r ? void 0 : r.members[0]) || "", firstParentRoleName: null !== (t = r.parent) && void 0 !== t && t.members && (0, h.mA)(null === r || void 0 === r ? void 0 : r.parent) || "" } })), H() }, removePageBreak: e => { let { role: t } = e; const r = x[t.id];
                            M((0, l.lh)({ chartId: v || (null === r || void 0 === r ? void 0 : r.chart), pageBreak: t, remove: !0 })), H(), y >= f && n(f - 1) }, changePageBreakName: (e, t) => { var n; const r = x[e],
                                a = (null === (n = w[e]) || void 0 === n ? void 0 : n.prevTopId) === d.Uz;
                            M(a ? (0, l.o4)({ ...A.tableOfContents, rootLabel: t }) : (0, l.sx)({ chartId: v || (null === r || void 0 === r ? void 0 : r.chart), pageBreakId: e, name: t })) }, displayOptions: T, handleChangeDirectReportDirection: e => t => { M((0, l.LY)({ id: null === e || void 0 === e ? void 0 : e.id, chartId: v || (null === e || void 0 === e ? void 0 : e.chart), directReportDirectionSettings: t })) }, isNewPageCreationAllowed: e => T.showCreatePage && !b.includes(null === e || void 0 === e ? void 0 : e.id) } } }, 94642: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(14556),
                    a = n(98433),
                    o = n(6124),
                    i = n(15813),
