                    h = n(70579); const m = e => { let { confirm: t, inputEl: n, url: m, isLocalUrl: p, extension: f, mode: v, onConfirmDelete: g, onConfirmCancel: y, onDrop: b, onError: w, onCancel: z, onDelete: x, disabled: A, fileType: k, label: S, align: M, progress: E } = e; const { downloadFile: C } = (0, u.A)(), T = !!(E && E < 100), H = 100 === E, L = "pending" === t; return "inline" !== v && (0, h.jsx)("div", { className: "fileUploadZone", children: (0, h.jsx)(a.Ay, { onDrop: b, className: "dropZoneComponent", children: (0, h.jsxs)(c.A, { className: "text-weight-normal bg-gray fullWidth", disabled: "undefined" !== typeof A && A, children: ["Drag and Drop a ", k || "", " or Click to Browse..."] }) }) }) || (0, h.jsx)("div", { className: "fileUploadInline", children: (0, h.jsxs)(i.A, { fullWidth: !0, children: [(0, h.jsx)(d.A, { variant: "body2", align: "left", display: "block", children: S }), (0, h.jsxs)("div", { className: "gutter-bottom ".concat(M || "left", "Align"), children: [(0, h.jsx)("input", { ref: n, id: "".concat(S, "FileUpload"), type: "file", onChange: e => { b(e && e.target && e.target.files) }, style: { display: "none" } }), !m && (0, h.jsxs)("div", { className: "fileSelectionWrapper", children: [(T || H) && (0, h.jsx)("div", { className: "gutter-bottom", children: (0, h.jsx)(l.A, { variant: "determinate", value: E }) }), T && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection", onClick: z, children: "Cancel Upload" }) }), !(T || H) && (0, h.jsx)("label", { className: "fileUploadSelection", htmlFor: "".concat(S, "FileUpload"), children: "Select File" })] }) || (0, h.jsxs)("div", { className: "fileSelectionWrapper", children: [T && (0, h.jsx)("div", { className: "gutter-bottom", children: (0, h.jsx)(l.A, { variant: "determinate", value: E }) }), (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)(s.A, { width: 32, children: (0, h.jsx)(o.o, { extension: f || "docx", ...o.k[f] }) }) }), T && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection", onClick: z, children: "Cancel Upload" }) }), !T && (0, h.jsxs)(r.Fragment, { children: [!L && "preview_unsupported" === m && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection", onClick: () => w("Brower Support Error", "File preview is not supported on current browser. Save and try again."), children: "View" }) }), !(L || "preview_unsupported" == m) && (0, h.jsxs)("span", { className: "inline-middle gutter-right", children: [p && (0, h.jsx)("a", { className: "fileUploadSelection", href: m, target: "_blank", children: "View" }), !p && (0, h.jsx)("a", { className: "fileUploadSelection", onClick: () => C({ url: m }), children: "View" })] }), !(L || !x) && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection", onClick: x, children: "Delete" }) }), !(!g || !L) && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection btn-red", onClick: g, children: "Confirm Delete" }) }), !(!y || !L) && (0, h.jsx)("span", { className: "inline-middle gutter-right", children: (0, h.jsx)("a", { className: "fileUploadSelection", onClick: y, children: "Cancel" }) }), !L && (0, h.jsx)("span", { className: "inline-middle", children: (0, h.jsx)("label", { className: "fileUploadSelection", htmlFor: "".concat(S, "FileUpload"), children: "Replace" }) })] })] })] })] }) }) } }, 99793: (e, t, n) => { "use strict";
                n.d(t, { g: () => v }); var r, a, o, i, l = n(57528),
                    s = n(96446),
                    c = n(72119),
                    d = n(37294),
                    u = n(70579); const h = (0, c.i7)(r || (r = (0, l.A)(["\n  0%, 20% {\n    width: 100%;\n    height: 100%;\n    margin: 0;\n    opacity: 1;\n    transform: rotateY(0deg) rotateX(0);\n  }\n  25% {\n    width: 60%;\n    height: 60%;\n    margin: 20%;\n    opacity: 0.5;\n  }\n  30%, 50%, 70% {\n    width: 100%;\n    height: 100%;\n    margin: 0;\n    opacity: 1;\n  }\n  75% {\n    width: 60%;\n    height: 60%;\n    margin: 20%;\n    opacity: 0.5;\n  }\n  80%, 100% {\n    width: 100%;\n    height: 100%;\n    margin: 0;\n    opacity: 1;\n  }\n"]))),
                    m = (0, c.Ay)(s.A)(a || (a = (0, l.A)(["\n  width: 100%;\n  height: 100%;\n  opacity: 1;\n  background: ", ";\n  animation: ", " ", " linear infinite;\n  animation-direction: alternate;\n  border-radius: 4px;\n  ", "\n"])), d.Qs.Violet[500], h, "4200ms", (e => { let { index: t } = e; return "\n    animation-delay:".concat(350 * (t - 1), "ms;\n  ") })),
                    p = (0, c.Ay)(s.A)(o || (o = (0, l.A)(["\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  background: #ffffff;\n"]))),
                    f = (0, c.Ay)(s.A)(i || (i = (0, l.A)(["\n  transform: rotate(-45deg);\n  transform-origin: 50% 50%;\n"]))),
                    v = e => { let { size: t = 20 } = e; const n = t; return (0, u.jsx)(f, { display: "grid", gridTemplateColumns: "".concat(n, "px ").concat(n, "px ").concat(n, "px"), gridTemplateRows: "".concat(n, "px ").concat(n, "px ").concat(n, "px"), gap: "4px", children: [0, 1, 2, 3, 4, 5, 6, 7, 8].map((e => 2 === e || 6 === e || 7 === e ? (0, u.jsx)(p, {}, "animation_".concat(e)) : (0, u.jsx)(m, { index: e - (e > 7 ? 3 : e > 6 ? 2 : e > 2 ? 1 : 0) }, "animation_".concat(e)))) }) } }, 37869: (e, t, n) => { "use strict";
                n.d(t, { A: () => f });
                n(65043); var r = n(78300),
                    a = n(80539),
                    o = n(75156),
                    i = n(96382),
                    l = n(14556),
                    s = n(89656),
                    c = n(61531),
                    d = n(48853),
                    u = n(78396),
                    h = n(10621),
                    m = n(70579); const p = 90,
                    f = e => { let { size: t = p, person: n, role: f, showEdit: v = !1, handleChangePhotoClick: g, directory: y } = e; const b = y ? (0, l.d4)(s.NL) : (0, l.d4)(s.YY),
                            w = y ? (0, l.d4)(s.bh) : (0, l.d4)(s.Ay),
                            { userHasMinAccess: z } = (0, d.A)(); let x, A, k; if (n) { const e = (0, h.II)(n),
                                r = n.name || (e ? "".concat(e, " ").concat((0, h.US)(n)) : "");
                            x = (0, m.jsx)(a.A, { width: t, height: t, src: b, name: r, overrideColor: n.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") }, onError: e => { e.target.setAttribute("processed", "true"), e.target.setAttribute("href", i.A) } } }), A = (0, m.jsx)(a.A, { width: t, height: t, src: n.photo || b, name: r, overrideColor: n.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") } }, children: x }) } else if (f) { const { type: e } = f || {};
                            A = /single|assistant|shared/i.test(e) ? (0, m.jsx)(a.A, { width: t, height: t, src: w, name: "va", imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") } }, children: x }) : "department" === e ? (0, m.jsx)(r.A, { width: t, height: t, children: (0, m.jsx)(o.Ay, { icon: "Building", size: "x3" }) }) : "location" === e ? (0, m.jsx)(a.A, { width: t, height: t, src: (0, s.pM)((0, h.Un)(f)) }) : "embedded" === e ? (0, m.jsx)(r.A, { width: t, height: t, children: (0, m.jsx)(o.Ay, { icon: "Chart", size: "x3" }) }) : (0, m.jsx)(r.A, { width: t, height: t, name: "ii", imgProps: { onLoad: e => { e.target.setAttribute("processed", "true") } }, children: (0, m.jsx)(o.Ay, { icon: "Building", size: "x3" }) }) } return z(u.td.EDITOR) && n && v && "function" === typeof g && (k = (0, m.jsx)(a.u, { position: "br", handleClick: g })), (0, m.jsxs)(c.A, { position: "relative", children: [A, k] }) } }, 50330: (e, t, n) => { "use strict";
                n.d(t, { A: () => I }); var r = n(57528),
                    a = n(61258),
                    o = n(61531),
                    i = n(66187),
                    l = n(55357),
                    s = n(40454),
                    c = n(43577),
                    d = n(49147),
                    u = n(73083),
                    h = n(16853),
                    m = n(9579),
                    p = n(90349),
                    f = n(84866),
                    v = n(96364),
                    g = n(56579),
                    y = n(85279),
                    b = n(33849),
                    w = (n(65043), n(70579)); const z = e => { let { selected: t, label: n = "Search", name: r = "search", handleSelect: a, options: o, freeSolo: i = !0, multiple: l = !0 } = e; return (0, w.jsx)(p.Ay, { multiple: l, id: "tags-outlined-".concat(r), onChange: a, value: t, options: o, filterSelectedOptions: !0, freeSolo: i, renderOption: e => (0, w.jsx)(v.A, { children: e }), renderInput: e => (0, w.jsx)(f.A, { ...e, name: r, variant: "outlined", label: n }) }) }; var x, A, k = n(54460),
                    S = n(72119),
                    M = n(75156),
                    E = n(24241),
                    C = n(10621),
                    T = n(61360); const H = (0, S.Ay)(o.A)(x || (x = (0, r.A)(["\n  position: absolute;\n  width: 100%;\n  background: rgba(220, 220, 220, 0.5);\n  padding: 8px;\n  z-index: 999;\n  top: 8px;\n  bottom: 8px;\n"]))),
                    L = (0, S.Ay)(i.A)(A || (A = (0, r.A)([""]))),
                    I = e => { var t, n, r; let { syncLocked: i, onFieldChange: x, onFieldError: A, onImmediateFieldChange: S, value: I, name: j, error: V, inputRef: O, label: R, modelId: P, orgId: D, field: F, options: N, autoFocus: _, fullWidth: B, disabled: W } = e;
                        _ = !i && _; const { control: U } = (0, a.xW)(), q = F.displayType || F.type, G = null === F || void 0 === F || null === (t = F.typeMetadata) || void 0 === t ? void 0 : t[F.type], K = (null === G || void 0 === G ? void 0 : G.multiple) || !1, Z = null === G || void 0 === G ? void 0 : G.isRestricted; let Y = ""; "richText" === q && Array.isArray(I) && (Y = (Array.isArray(I) ? I.join("\n") : I || "").toString()); const X = e => function(t, n) { var r; "function" === typeof x && x.call(this, (null === t || void 0 === t || null === (r = t.target) || void 0 === r ? void 0 : r.value) || n); "function" === typeof e && e.apply(this, arguments) }; let $ = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(f.A, { error: V, name: r, inputRef: O, defaultValue: n, fullWidth: !0, label: "".concat(R, " ").concat("computed" === q ? "(computed)" : ""), autoFocus: _ || !1, onChange: X(t), helperText: null === V || void 0 === V ? void 0 : V.message, disabled: W || "computed" === q }) } }),
                            Q = $; const J = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(g.A, { fullWidth: !0, size: "medium", label: R, onChange: X(t), value: n || (null === N || void 0 === N ? void 0 : N.length) && N[0].value, name: r, children: (0, T.Pn)(null === G || void 0 === G ? void 0 : G.iconPackage).map(((e, t) => (0, w.jsx)(l.A, { value: e.value, children: (0, w.jsxs)(o.A, { display: "flex", gridGap: 16, children: [e.value && (0, w.jsx)("img", { src: "".concat("https://assets-organimi.s3.amazonaws.com").concat(e.path), width: 24 }), (0, w.jsx)(o.A, { children: e.label })] }) }, "select_".concat(r, "_").concat(e.value, "_").concat(t)))) }) } }); let ee = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: Y, render: e => { let { onChange: t, name: n } = e; return (0, w.jsx)(f.A, { multiline: !0, error: V, name: n, defaultValue: Y, fullWidth: !0, label: R, autoFocus: _ || !1, onChange: X(t), helperText: null === V || void 0 === V ? void 0 : V.message }) } }),
                            te = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { var t, n; let { onChange: r, value: a, name: o } = e; return (0, w.jsx)(f.A, { error: V, type: "url", name: o, inputRef: O, defaultValue: a, fullWidth: !0, label: R, onChange: X(r), helperText: null === V || void 0 === V ? void 0 : V.message, disabled: null === F || void 0 === F || null === (t = F.typeMetadata) || void 0 === t || null === (n = t.url) || void 0 === n ? void 0 : n.isDynamic }) } }),
                            ne = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(f.A, { error: V, inputProps: { step: "any" }, type: "number", name: r, inputRef: O, defaultValue: n, fullWidth: !0, label: R, onChange: X(t), helperText: null === V || void 0 === V ? void 0 : V.message }) } }),
                            re = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(g.A, { size: "medium", fullWidth: !0, label: R, onChange: X(t), value: n || (null === N || void 0 === N ? void 0 : N.length) && N[0].value, name: r, children: N.map(((e, t) => (0, w.jsx)(l.A, { value: e.value, children: e.label }, "select_".concat(r, "_").concat(e.value, "_").concat(t)))) }) } }),
                            ae = Z ? (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: (0, C.UT)(I || (null === N || void 0 === N ? void 0 : N.length) && N[0].value, F), render: e => { var t, n; let { onChange: r, value: a, name: o } = e; return (0, w.jsxs)(g.A, { onChange: X(r), size: "medium", value: a, fullWidth: !0, label: R, name: o, multiple: K, children: [!K && (0, C.AP)(a, F) && (0, w.jsx)(l.A, { value: a, children: a }), ((null === F || void 0 === F || null === (t = F.typeMetadata) || void 0 === t || null === (n = t.location) || void 0 === n ? void 0 : n.choices) || []).map(((e, t) => e.nickname !== a && (0, w.jsx)(l.A, { value: e.nickname, children: e.nickname }, "select_".concat(o, "_").concat(e.nickname, "_").concat(t)) || (0, w.jsx)(w.Fragment, {})))] }) } }) : (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: (0, C.UT)(I || (null === N || void 0 === N ? void 0 : N.length) && N[0].value, F), render: e => { var t, n; let { onChange: r, value: a, name: o } = e; return (0, w.jsx)(p.Ay, { multiple: K, id: "location-input-".concat(o), onChange: X(((e, t) => { r(K ? t : null === t ? [] : [t]) })), value: (0, C.UT)(a, F), options: ((null === F || void 0 === F || null === (t = F.typeMetadata) || void 0 === t || null === (n = t.location) || void 0 === n ? void 0 : n.choices) || []).map((e => e.nickname)).filter((e => e)), disableCloseOnSelect: K, freeSolo: !0, renderOption: e => (0, w.jsx)(L, { children: e }), size: "medium", fullWidth: !0, limitTags: 2, noOptionsText: "No default locations added to this custom field", renderInput: e => (0, w.jsx)(f.A, { ...e, error: V, InputLabelProps: { shrink: !0 }, name: o, inputRef: K ? null : O, fullWidth: !0, label: R, helperText: null === V || void 0 === V ? void 0 : V.message }) }) } }); const oe = (e, t) => K ? t ? Array.isArray(t) ? t : [t] : [] : t ? Array.isArray(t) ? null === t[0] ? null : t[0] : t : []; let ie = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { var t, n; let { onChange: r, value: a, name: o } = e; return (0, w.jsx)(z, { selected: oe(0, a), label: R, handleSelect: X(((e, t) => { r(K ? t : null === t ? [] : [t]) })), name: o, defaultValue: (0, C.UT)(a, F), options: (null === F || void 0 === F || null === (t = F.typeMetadata) || void 0 === t || null === (n = t.tags) || void 0 === n ? void 0 : n.choices) || [], freeSolo: !Z, multiple: K }) } }),
                            le = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsxs)(w.Fragment, { children: [(0, w.jsx)(v.A, { variant: "body2", align: "left", display: "block", children: R }), (0, w.jsxs)(s.A, { component: "label", container: !0, alignItems: "center", children: [(0, w.jsx)(s.A, { item: !0, children: (0, w.jsx)(v.A, { variant: "body2", children: F.labelLeft }) }), (0, w.jsx)(s.A, { item: !0, children: "role.orientation" === r ? (0, w.jsx)(c.A, { checked: "right" === n, onChange: X((e => t(e.target.checked ? "right" : "left"))), name: r }) : (0, w.jsx)(c.A, { checked: F.toStringFormat ? n === F.toStringFormat(n) : n, onChange: X((e => t(F.toStringFormat ? F.toStringFormat(e.target.checked) : e.target.checked))), name: r }) }), (0, w.jsx)(s.A, { item: !0, children: (0, w.jsx)(v.A, { variant: "body2", children: F.labelRight }) })] })] }) } }),
                            se = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(d.A, { className: "checkboxCtrl", children: (0, w.jsx)(u.A, { control: (0, w.jsx)(h.A, { name: r, checked: /1|true/i.test(n), onChange: X((e => t(e.target.checked))), color: "primary" }), label: F.label.toLowerCase() }) }) } }),
                            ce = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n } = e; return (0, w.jsx)(y.A, { label: R, clearable: !!n, type: "date", onChange: X(t), value: n, field: F, classes: "dateInputFullWidth" }) } }),
                            de = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n } = e; return (0, w.jsx)(b.A, { onImmediateChange: S, onError: A, onChange: X(t), fieldId: F.id, fieldLabel: F.label, model: F.model, modelId: P, orgId: D, value: n }) } }),
                            ue = (0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I, render: e => { let { onChange: t, value: n, name: r } = e; return (0, w.jsx)(f.A, { error: V, type: "number", inputProps: { step: "any" }, name: r, inputRef: O, onChange: X(t), defaultValue: n, fullWidth: !0, label: R }) } }); const he = E.c9.fromISO(I),
                            me = (0, w.jsxs)(w.Fragment, { children: [(0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I }), (0, w.jsx)(m.Ay, { title: (0, w.jsxs)(o.A, { textAlign: "center", p: 1, children: [(0, w.jsx)(v.A, { fontSize: "12px", color: "white", children: "Value computed based on:" }), (0, w.jsx)(o.A, { mt: 1, textAlign: "center", children: (0, w.jsx)(k.A, { tokens: null === F || void 0 === F || null === (n = F.typeMetadata) || void 0 === n || null === (r = n.computed) || void 0 === r ? void 0 : r.tokens, tooltipMode: !0 }) })] }), placement: "bottom", arrow: !0, children: (0, w.jsx)(f.A, { value: null !== he && void 0 !== he && he.isValid ? he.toLocaleString() : I, fullWidth: !0, label: R, disabled: !0 }) })] }),
                            pe = (0, w.jsxs)(w.Fragment, { children: [(0, w.jsx)(a.xI, { control: U, name: j, defaultValue: I }), (0, w.jsx)(f.A, { value: (0, C.$)(I, F), fullWidth: !0, label: R, disabled: !0 })] }); return (0, w.jsxs)(o.A, { position: "relative", width: B ? "100%" : "auto", children: [i && (0, w.jsx)(m.Ay, { title: "Field is currently synced to a third party integration field. Change your integration settings to allow manual changes to this field", placement: "bottom", arrow: !0, children: (0, w.jsx)(H, { children: (0, w.jsx)(o.A, { position: "absolute", right: 8, top: 0, bottom: 0, display: "flex", alignItems: "center", children: (0, w.jsx)(M.Ay, { icon: "Lock", size: "lg" }) }) }) }), { switch: le, boolean: se, string: Q, richtext: ee, text: Q, url: te, number: ne, tags: ie, date: ce, attachment: de, location: ae, select: re, currency: ue, computed: me, rollup: pe, iconpicklist: J } [q.toLowerCase()]] }) } }, 16567: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => i, yD: () => o }); var r = n(80907); const a = "communityBuild",
                    o = (0, n(9787).a)({ slice: a, scope: "communityBuild" }),
                    i = (0, r.Z0)({ name: a, initialState: {}, reducers: { "getCommunityBuild/fulfilled": (e, t) => { let { payload: { communityBuild: n } } = t;
                                e.communityBuild = n || {} }, "createCommunityObject/fulfilled": (e, t) => { let { payload: { communityBuild: n } } = t;
                                e.communityBuild = n || {} }, "getCommunityBuildSafe/fulfilled": (e, t) => { let { payload: { communityBuild: n } } = t;
                                e.communityBuild = n || {} } } }).reducer }, 30208: (e, t, n) => { "use strict";
                n.d(t, { P: () => a, m: () => r }); const r = e => e.communityBuild || {},
                    a = e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.id } }, 72127: (e, t, n) => { "use strict";
                n.d(t, { Ok: () => d, VF: () => i, fg: () => p, kk: () => s, mz: () => u, t0: () => c }); var r = n(80192),
                    a = n(15622),
                    o = n(41);
                n(45418); const i = (0, r.Mz)(a.A, (e => ({ ...e, companyName: (0, o.Zg)(e) }))),
                    l = e => e.dashboard.integrationsAvailable || [],
                    s = e => e.dashboard.charts,
                    c = e => e.profileCard.open,
                    d = e => e.user.organizations,
                    u = (0, r.Mz)(d, (e => e.filter((e => "owner" === (null === e || void 0 === e ? void 0 : e.accessLevel) || "admin" === (null === e || void 0 === e ? void 0 : e.accessLevel))) || [])),
                    h = ((0, r.Mz)((e => e.organization), (e => e.id)), (0, r.Mz)(l, (e => (null === e || void 0 === e ? void 0 : e.reduce(((e, t) => (e[t.id] = t, e)), {})) || {}))),
                    m = (0, r.Mz)((e => { var t, n; return (null === e || void 0 === e || null === (t = e.import) || void 0 === t || null === (n = t.integrations) || void 0 === n ? void 0 : n.active) || [] }), (e => (null === e || void 0 === e ? void 0 : e.reduce(((e, t) => (e[t.type] ? e[t.type] = [...e[t.type], t] : e[t.type] = [t], e)), {})) || {})),
                    p = (0, r.Mz)(m, l, h, ((e, t, n) => { const r = [],
                            a = [];
                        Object.keys(e).forEach((t => { e[t].length > 0 && n[t] && (r.push({ ...n[t], active: !0 }), a.push(t)) })); for (let o = 0; o < t.length; o++) a.includes(t[o].id) || r.push({ ...t[o], active: !1 }); return r })) }, 23986: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => i, qT: () => o }); var r = n(80907); const a = "client",
                    o = (0, n(9787).a)({ slice: a, scope: "client" }),
                    i = (0, r.Z0)({ name: a, initialState: { apiClients: {}, apiIdLicenseMap: {}, apiLogsPaginated: {} }, reducers: { "createAPIKey/fulfilled": (e, t) => { var n; const r = { ...null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.apiConfig },
                                    a = null === r || void 0 === r ? void 0 : r.license;
                                delete r.clientSecret, Array.isArray(e.apiClients[a]) ? e.apiClients[a].push(r) : e.apiClients[a] = [r], e.apiIdLicenseMap[null === r || void 0 === r ? void 0 : r.id] = a }, "getAPIClients/fulfilled": (e, t) => { var n; const r = null === (n = t.payload.apiClients[0]) || void 0 === n ? void 0 : n.license;
                                e.apiClients[r] = t.payload.apiClients; const a = {};
                                t.payload.apiClients.forEach((e => { a[null === e || void 0 === e ? void 0 : e.id] = e.license })), e.apiIdLicenseMap = a }, "deleteApiClient/fulfilled": (e, t) => { if (t.payload.apiConfig) { var n, r; const a = null === (n = t.payload) || void 0 === n || null === (r = n.apiConfig) || void 0 === r ? void 0 : r.id,
                                        o = e.apiIdLicenseMap[a],
                                        i = [...e.apiClients[o]],
                                        l = i.findIndex((e => e.id === a));
                                    i.splice(l, 1), e.apiClients[o] = i, delete e.apiIdLicenseMap[a] } }, "reGenerateClientSecret/fulfilled": (e, t) => { var n, r; const a = null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.apiId,
                                    o = e.apiIdLicenseMap[a],
                                    i = [...e.apiClients[o]],
                                    l = i.findIndex((e => e.id === a)),
                                    s = { ...t.payload.apiConfig };
                                delete s.clientSecret, i[l] = s, e.apiClients[o] = i }, "getLogs/fulfilled": (e, t) => { e.apiLogsPaginated = t.payload } } }).reducer }, 39534: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => i, kk: () => o }); var r = n(80907); const a = "webhook",
                    o = (0, n(9787).a)({ slice: a, scope: "webhook" }),
                    i = (0, r.Z0)({ name: a, initialState: { webhooks: {}, webhookIdLicenseMap: {} }, reducers: { "createNewWebhook/fulfilled": (e, t) => { var n; let r = { ...null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.webhook }; const a = null === r || void 0 === r ? void 0 : r.licenseId;
                                Array.isArray(e.webhooks[a]) ? e.webhooks[a].push(r) : a && (e.webhooks[a] = [r]), e.webhookIdLicenseMap[null === r || void 0 === r ? void 0 : r.id] = a }, "getWebhooks/fulfilled": (e, t) => { var n; const r = null === (n = t.payload.webhooks[0]) || void 0 === n ? void 0 : n.licenseId; if (r) { e.webhooks[r] = t.payload.webhooks; const n = {};
                                    t.payload.webhooks.forEach((e => { n[null === e || void 0 === e ? void 0 : e.id] = e.licenseId })), e.webhookIdLicenseMap = { ...e.webhookIdLicenseMap, ...n } } }, "deleteWebhook/fulfilled": (e, t) => { if (t.payload.webhook) { var n, r; const a = null === (n = t.payload) || void 0 === n || null === (r = n.webhook) || void 0 === r ? void 0 : r.id,
                                        o = e.webhookIdLicenseMap[a],
                                        i = [...e.webhooks[o]],
                                        l = i.findIndex((e => e.id === a));
                                    i.splice(l, 1), e.webhooks[o] = i, delete e.webhookIdLicenseMap[a] } } } }).reducer }, 53841: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, B$: () => o, ZP: () => l }); var r = n(80907); const a = "userlifecycle",
                    o = (0, n(47730).B)({ slice: a, scope: "userlifecycle" }),
                    i = (0, r.Z0)({ name: a, initialState: { user: { lifecycle: null } }, reducers: { "markUserDeactivated/fulfilled": (e, t) => { let { payload: { user: n } } = t;
                                e.user.lifecycle = n.lifecycle }, "markUserActivated/fulfilled": (e, t) => { let { payload: { user: n } } = t;
                                e.user.lifecycle = n.lifecycle } }, extraReducers: {} }),
                    l = e => e.userlifecycle.user.lifecycle,
                    s = i.reducer }, 31297: (e, t, n) => { "use strict";
                n.d(t, { A: () => l, o: () => i }); var r = n(47730),
                    a = n(80907); const o = "templates",
                    i = (0, r.B)({ slice: o, scope: "templates" }),
                    l = (0, a.Z0)({ name: o, initialState: { templates: [], templatesMeta: {} }, reducers: { "getTemplates/fulfilled": (e, t) => { var n, r, a, o;
                                e.templates = t.payload.templates, e.templatesMeta = { env: null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.env, branded: null === t || void 0 === t || null === (a = t.meta) || void 0 === a || null === (o = a.arg) || void 0 === o ? void 0 : o.branded } }, viewTemplate: (e, t) => { const n = t.payload.template;
                                e.templates = e.templates.map((e => e.id === n.id ? { ...e, ...n } : e)) } } }).reducer }, 86674: (e, t, n) => { "use strict";
                n.d(t, { A: () => u }); var r = n(83462),
                    a = n(4219),
                    o = n(35316),
                    i = n(29347),
                    l = n(42518),
                    s = n(61258),
                    c = n(24115),
                    d = n(70579); const u = e => { let { title: t = "Prompt", render: n, handleClose: u, handleSuccess: h, btnText: m = { submit: "Submit", cancel: "Cancel" }, maxWidth: p, pending: f = !1 } = e; const v = (0, s.mN)(),
                        { handleSubmit: g } = v,
                        y = g(h); return (0, d.jsx)(r.A, { open: !0, onClose: u, "aria-labelledby": "prompt-dialog-title", "aria-describedby": "prompt-dialog-description", maxWidth: p, zIndexType: "alert", children: (0, d.jsxs)("form", { children: [(0, d.jsx)(a.A, { align: "left", id: "prompt-dialog-title", children: t }), (0, d.jsx)(o.A, { sx: { marginTop: 2 }, children: n && (0, d.jsx)(c.A, { loading: f, transparent: !0, children: n(v) }) }), (0, d.jsxs)(i.A, { sx: { paddingRight: 3 }, children: [(0, d.jsx)(l.A, { type: "button", onClick: u, color: "primary", variant: "outlined", disabled: f, children: m.cancel || "Cancel" }), (0, d.jsx)(l.A, { type: "submit", onClick: y, color: "primary", variant: "contained", disabled: f, children: m.submit || "Submit" })] })] }) }) } }, 5560: (e, t, n) => { "use strict";
                n.r(t), n.d(t, { MemberPhotoFieldDeletedValue: () => M, default: () => E }); var r = n(65043),
                    a = n(46615),
                    o = n(84),
                    i = n(43867),
                    l = n(61531),
                    s = n(52643),
                    c = n(52907),
                    d = n(72835),
                    u = n(35033),
                    h = n(86852),
                    m = n(78780),
                    p = n(4524),
                    f = n(96364),
                    v = n(70567),
                    g = n(40454),
                    y = n(75156),
                    b = n(91688),
                    w = n(66856),
                    z = n(70579); const x = () => { const e = (0, v.A)(),
                        t = (0, b.useHistory)(); return (0, z.jsxs)(g.A, { container: !0, justifyContent: "flex-start", alignItems: "center", direction: "column", children: [(0, z.jsx)(l.A, { my: 3, children: (0, z.jsx)(y.Ay, { icon: "Upload", size: "x5", color: e.palette.primary.main }) }), (0, z.jsx)(l.A, { my: 4, children: (0, z.jsxs)(f.A, { variant: "h3", color: "primary", children: [(0, z.jsx)(z.Fragment, { children: "You can upload an entire folder of " }), (0, z.jsx)("br", {}), " ", (0, z.jsx)(z.Fragment, { children: "photos all at once to save you time." })] }) }), (0, z.jsx)(d.A, { variant: "contained", color: "primary", onClick: () => { t.push((0, w.N$)()) }, children: "Upload a Batch of Photos" })] }) }; var A = n(5816),
                    k = n(24115); const S = e => { let { value: t, hidden: n, children: r } = e; return (0, z.jsx)("div", { "aria-labelledby": "member-photo-".concat(t), hidden: n, children: r }) },
                    M = "DELETED_PHOTO",
                    E = e => { let { open: t, children: n, type: f, member: v, organization: g, handleChangePhoto: y, handleLinkedInSearch: b, handleSearchResult: w, allowAvatars: M = !0, allowBatchUpload: E = !0, allowLinkedInSearch: C = !0, square: T = !0, mode: H = "upload", photo: L, entity: I = "person", initialAvatarBackgroundColor: j, orgId: V, photoMaxWidth: O = 200, photoMaxHeight: R = 200, useImageWithCfPolicy: P = !0 } = e; const D = [{ label: "Upload Photo", value: "upload" }, { label: "search" === H ? "Search for Person" : "Search for Photos", value: "search" }, { label: "Batch Upload", value: "batch" }],
                            { toggleDialog: F, closeDialog: N } = (0, o.A)("memberPhoto"),
                            [_, B] = (0, r.useState)(H),
                            [W, U] = (0, r.useState)(L || null),
                            [q, G] = (0, r.useState)(j),
                            [K, Z] = (0, r.useState)(!1),
                            Y = () => { F() },
                            X = e => { B("upload"), U(e) },
                            $ = () => { U({ deleted: !0 }) },
                            Q = e => { U(e), y({ photo: e, memberPhotoColor: q }), setTimeout((() => { N() }), 300) },
                            J = (e, t) => { U(e), w(e, t), setTimeout((() => { N() }), 300) },
                            ee = (e, t) => { "function" === typeof b && b(e, t) },
                            te = e => { G(e) },
                            ne = e => ({ upload: (0, z.jsx)(m.A, { updatePhoto: X, securePhoto: W, deletePhoto: $, allowAvatars: M, updateAvatar: Q, updateBackground: te, square: T, type: f, member: v, initialAvatarBackgroundColor: q, orgId: V, photoMaxWidth: O, photoMaxHeight: R, useImageWithCfPolicy: P }), search: (0, z.jsx)(p.A, { member: v, updatePhoto: X, organization: g, allowLinkedInSearch: C, square: T, type: f, mode: H, updatePhotoSearch: J, onLinkedInPhotoSearch: ee, entity: I, photoMaxWidth: O, photoMaxHeight: R }), batch: (0, z.jsx)(x, {}) } [e]); return (0, r.useEffect)((() => { B(H) }), [t]), (0, z.jsxs)(z.Fragment, { children: [n, (0, z.jsxs)(a.A, { open: t, onClose: Y, children: [(0, z.jsx)(A.A, { id: "photo-dialog-title", onClose: Y, children: "Change Photo" }), (0, z.jsx)(i.A, { children: (0, z.jsx)(l.A, { width: 980, children: (0, z.jsxs)(k.A, { loading: K, transparent: !0, children: [(0, z.jsx)(l.A, { pb: 3, children: (0, z.jsx)(h.A, { spacing: { top: 1 }, children: (0, z.jsx)(u.A, { onChange: (e, t) => { B(t) }, value: _, variant: "fullWidth", children: D.filter((e => !!E || "batch" !== e.value)).filter((e => "search" !== H || "batch" !== e.value && "upload" !== e.value)).map((e => (0, z.jsx)(s.A, { value: e.value, label: e.label, id: "member-photo-".concat(e.value) }, "member-photo-tab-".concat(e.value)))) }) }) }), (0, z.jsx)(l.A, { py: 3, children: D.map((e => (0, z.jsx)(S, { hidden: _ !== e.value, active: e.label, value: e.value, children: ne(e.value) }, "orgpanel-settings-tabpanel-".concat(e.value)))) })] }) }) }), (0, z.jsxs)(c.A, { children: [(0, z.jsx)(d.A, { variant: "outlined", color: "primary", onClick: Y, disabled: K, children: "Cancel" }), (0, z.jsx)(d.A, { variant: "contained", color: "primary", disabled: "" === W || K, onClick: async () => { if ("function" === typeof y) { var e; "AsyncFunction" === (null === y || void 0 === y || null === (e = y.constructor) || void 0 === e ? void 0 : e.name) && Z(!0); try { await y({ newPhoto: W, memberPhotoColor: q }) } finally { Z(!1) } } F() }, children: "Save" })] })] })] }) } }, 17785: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => w, GQ: () => h, Hl: () => y, Ih: () => g, Lf: () => s, O1: () => v, PK: () => b, Zt: () => f, ar: () => p, hg: () => u }); var r = n(80907),
                    a = n(9787),
                    o = n(47730),
                    i = n(59177); const l = "shareChart",
                    s = { Invite: "invite", SSO: "saml-login", Public: "publicurl", Iframe: "iframe" },
                    c = "viewer",
                    d = "editor",
                    u = (0, a.a)({ slice: l, scope: "access" }),
                    h = (0, o.B)({ slice: l, scope: "access" }),
                    m = (0, r.Z0)({ name: l, initialState: { invites: [], publicShares: [], iframeLinks: [], genericShare: {}, originalInvites: [] }, reducers: { addUserInvite: (e, t) => { let { payload: n } = t; const r = e.invites,
                                    a = n.user,
                                    o = r.findIndex((e => e.email === a.email));
                                o > -1 ? (e.invites[o].type = a.type, e.invites[o].dirty = !0) : e.invites.unshift({ ...a, type: a.type || "viewer", email: (0, i.P8)(a.email), dirty: !0 }) }, addBulkUserInvites: (e, t) => { let { payload: n } = t; const r = e.invites,
                                    a = n.users || []; for (let o of a) { const t = (0, i.P8)(o.email),
                                        n = r.findIndex((e => e.email === t));
                                    t && (n > -1 ? e.invites[n].type = e.invites[n].type || o.type || "viewer" : e.invites.push({ ...o, type: o.type || "viewer", email: t, dirty: !0 })) } }, removeShareAccess: (e, t) => { let { payload: { email: n } } = t; const r = e.invites.findIndex((e => e.email === n)); if (r > -1) { const t = e.invites[r];
                                    t.isExisting ? t.pendingDelete = !0 : e.invites.splice(r, 1) } }, cancelPendingDeleteAccess: (e, t) => { let { payload: { email: n } } = t; const r = e.invites.findIndex((e => e.email === n)); if (r > -1) { e.invites[r].pendingDelete = !1 } }, resetLocalChanges: e => { e.invites = [...e.originalInvites || []] }, userPermissionsSaved: e => { e.originalInvites = e.invites = [...e.invites].filter((e => !e.pendingDelete)).map((e => (e.isExisting = !0, e.dirty = !1, { ...e }))) }, "getChartPermits/fulfilled": (e, t) => { var n; let { payload: { access: r = [], salesforce: a = {} } } = t; const o = [...r],
                                    i = o.find((e => e.type === s.SSO));
                                e.genericShare[s.SSO] = i ? { ...i, accessLevel: "r" === i.rw ? c : d } : null, e.publicShares = o.filter((e => e.type === s.Public)), e.iframeLinks = o.filter((e => e.type === s.Iframe)), e.originalInvites = e.invites = o.filter((e => ["r", "w", "x"].includes(e.rw) && e.email)).map((e => ({ email: e.email, type: "r" === e.rw ? c : d, isExisting: !0 }))), e.embeddableDomains = (null === (n = e.iframeLinks[0]) || void 0 === n ? void 0 : n.embeddableDomains) || [], e.salesforce = a || {} }, "createGenericPermit/fulfilled": (e, t) => { var n, r, a; let { meta: o } = t; const i = (null === o || void 0 === o || null === (n = o.arg) || void 0 === n || null === (r = n.data) || void 0 === r ? void 0 : r.accessLevel) || c;
                                e.genericShare = { ...e.genericShare, [null === o || void 0 === o || null === (a = o.arg) || void 0 === a ? void 0 : a.policyType]: { accessLevel: i } } }, "updateGenericPermit/fulfilled": (e, t) => { var n, r, a; let { meta: o } = t; const i = (null === o || void 0 === o || null === (n = o.arg) || void 0 === n || null === (r = n.data) || void 0 === r ? void 0 : r.accessLevel) || c;
                                e.genericShare = { ...e.genericShare, [null === o || void 0 === o || null === (a = o.arg) || void 0 === a ? void 0 : a.policyType]: { accessLevel: i } } }, "removeGenericPermit/fulfilled": (e, t) => { var n; let { meta: r } = t;
                                e.genericShare[null === r || void 0 === r || null === (n = r.arg) || void 0 === n ? void 0 : n.policyType] = null }, "updateLinkSecurity/fulfilled": (e, t) => { var n; let { meta: r, payload: a } = t; const o = null === r || void 0 === r || null === (n = r.arg) || void 0 === n ? void 0 : n.publicId; if (!o || null === a || void 0 === a || !a.security) return e; const i = e.publicShares.findIndex((e => e.publicId === o));
                                i > -1 && (e.publicShares[i] = { ...e.publicShares[i], security: null === a || void 0 === a ? void 0 : a.security }) }, "disableLinkSecurity/fulfilled": (e, t) => { var n; let { meta: r, payload: a } = t; const o = null === r || void 0 === r || null === (n = r.arg) || void 0 === n ? void 0 : n.publicId; if (!o || null === a || void 0 === a || !a.security) return e; const i = e.publicShares.findIndex((e => e.publicId === o));
                                i > -1 && (e.publicShares[i] = { ...e.publicShares[i], security: null === a || void 0 === a ? void 0 : a.security }) }, "deletePublicLink/fulfilled": (e, t) => { var n; let { meta: r } = t; const a = null === r || void 0 === r || null === (n = r.arg) || void 0 === n ? void 0 : n.publicId,
                                    o = e.publicShares.findIndex((e => e.publicId === a));
                                o > -1 && e.publicShares.splice(o, 1); const i = e.iframeLinks.findIndex((e => e.publicId === a));
                                i > -1 && e.iframeLinks.splice(i, 1) } }, extraReducers: { "chart/sharePublic/fulfilled": (e, t) => { let { payload: n } = t; const r = n.publicShareSettings;
                                r.type === s.Public ? e.publicShares.push(r) : r.type === s.Iframe && e.iframeLinks.push(r) }, "chart/shareIFrame/fulfilled": (e, t) => { var n; let { meta: r } = t; const a = null === r || void 0 === r || null === (n = r.arg) || void 0 === n ? void 0 : n.domains;
                                e.embeddableDomains = a || [] }, "chart/updatePublicShareLink/fulfilled": (e, t) => { var n; let { payload: r } = t; const a = r.publicShareSettings,
                                    o = null === a || void 0 === a ? void 0 : a.publicId; if (!o) return e; if (!(null === a || void 0 === a || null === (n = a.salesforce) || void 0 === n || !n.accountId)) return void(e.salesforce = { ...e.salesforce, chartShareSettings: a }); const i = e.publicShares.findIndex((e => e.publicId === o));
                                i > -1 && (e.publicShares[i] = { ...e.publicShares[i], ...a }); const l = e.iframeLinks.findIndex((e => e.publicId === o));
                                l > -1 && (e.iframeLinks[l] = { ...e.iframeLinks[l], ...a }) }, "chart/linkChartToSalesforce/fulfilled": (e, t) => { let { payload: n } = t;
                                e.salesforce = { ...e.salesforce, ...n } }, "chart/unlinkChartFromSalesforce/fulfilled": (e, t) => { let { payload: n } = t;
                                e.salesforce = { ...e.salesforce, ...n } }, "organization/deleteThirdPartyCredentials/fulfilled": (e, t) => { let { payload: n } = t;
                                e.salesforce = { ...e.salesforce, ...n } } } }),
                    { addUserInvite: p, addBulkUserInvites: f, removeShareAccess: v, resetLocalChanges: g, userPermissionsSaved: y, cancelPendingDeleteAccess: b } = m.actions,
                    w = m.reducer }, 54762: (e, t, n) => { "use strict";
                n.d(t, { $u: () => d, KJ: () => c, ND: () => i, Pe: () => l, Pq: () => s, VF: () => o, mn: () => u }); var r = n(80192),
                    a = n(15622); const o = (0, r.Mz)(a.A, (e => { var t; return { ...e, isAnnualPlan: "annual" === (null === e || void 0 === e || null === (t = e.plan) || void 0 === t ? void 0 : t.frequency) } })),
                    i = (0, r.Mz)(a.A, (e => null === e || void 0 === e ? void 0 : e.isSamlConfigured)),
                    l = e => { var t, n, r, a, o; return (null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n || null === (r = n.alias) || void 0 === r ? void 0 : r.rootChartId) || (null === (a = e.chart) || void 0 === a || null === (o = a.info) || void 0 === o ? void 0 : o.id) },
                    s = e => { const { info: t } = e.chart || {}; return null === t || void 0 === t ? void 0 : t.id },
                    c = (0, r.Mz)(a.A, (e => { var t; return null === e || void 0 === e || null === (t = e.featureSet) || void 0 === t ? void 0 : t.reports })),
                    d = e => e.user,
                    u = e => { var t; return null === (t = e.organization) || void 0 === t ? void 0 : t.id } }, 91357: (e, t, n) => { "use strict";
                n.d(t, { j: () => b }); var r, a, o, i = n(48853),
                    l = n(57528),
                    s = n(61531),
                    c = n(48655),
                    d = n(72119),
                    u = n(70579); const h = (0, d.Ay)(s.A)(r || (r = (0, l.A)(["\n  background: rgba(200, 200, 200, 0.2);\n  position: relative;\n  padding: 4px;\n  border-radius: 4px;\n  margin: 4px 0;\n"]))),
                    m = d.Ay.div(a || (a = (0, l.A)(["\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"]))),
                    p = (0, d.Ay)(c.A)(o || (o = (0, l.A)(["\n  .MuiOutlinedInput-input {\n    padding: 14.5px 14px;\n  }\n"]))),
                    f = e => { let { field: t, handleEditCustomField: n } = e; return (0, u.jsxs)(h, { onClick: n, children: [(0, u.jsx)(p, { variant: "outlined", fullWidth: !0, disabled: !0, label: t.label, value: " " }), (0, u.jsx)(m, { children: (0, u.jsx)("a", { children: "Complete field setup" }) })] }) }; var v = n(78396),
                    g = n(10621),
                    y = n(84); const b = e => { var t; let { field: n, children: r } = e; const { userHasMinAccess: a } = (0, i.A)(), o = a(v.td.ADMIN), { openDialog: l } = (0, y.A)("customFields"), s = null === n || void 0 === n ? void 0 : n.type, c = (null === n || void 0 === n || null === (t = n.typeMetadata) || void 0 === t ? void 0 : t[s]) || {}, d = (null === c || void 0 === c ? void 0 : c.isRestricted) || !1, h = () => { l({ mode: "edit", field: { ...n } }) }; switch (s) {
                        case g.ZE.LOCATION:
                            var m; if (0 === (null === c || void 0 === c || null === (m = c.choices) || void 0 === m ? void 0 : m.length) && o && d) return (0, u.jsx)(f, { field: n, handleEditCustomField: h }); break;
                        case g.ZE.TAGS:
                            var p; if (0 === (null === c || void 0 === c || null === (p = c.choices) || void 0 === p ? void 0 : p.length) && d) return (0, u.jsx)(f, { field: n, handleEditCustomField: h }) } return r } }, 54460: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(40454),
                    a = n(85865),
                    o = n(32115),
                    i = n(65043),
                    l = n(70579); const s = e => { let { tokens: t = [], tooltipMode: n = !1 } = e; return (0, l.jsx)(r.A, { container: !0, alignItems: "center", justifyContent: n ? "center" : "start", style: { gap: "10px" }, children: null === t || void 0 === t ? void 0 : t.map(((e, t) => (null === e || void 0 === e ? void 0 : e.show) && (0, i.createElement)(a.A, { ...n ? { fontSize: "10px" } : {}, variant: o.Eq.bodySM, component: "span", key: "".concat(null === e || void 0 === e ? void 0 : e.type, "-").concat(null === e || void 0 === e ? void 0 : e.subType, "-").concat(t), color: n ? "white" : "field" === (null === e || void 0 === e ? void 0 : e.subType) ? "primary" : "operand" === (null === e || void 0 === e ? void 0 : e.type) ? "secondary" : "textPrimary" }, "field" === (null === e || void 0 === e ? void 0 : e.subType) && "@", null === e || void 0 === e ? void 0 : e.label, "day" === (null === e || void 0 === e ? void 0 : e.subType) && " day(s)", "month" === (null === e || void 0 === e ? void 0 : e.subType) && " month(s)", "year" === (null === e || void 0 === e ? void 0 : e.subType) && " year(s)"))) }) } }, 25747: (e, t, n) => { "use strict";
                n.d(t, { A: () => i, g: () => o }); var r = n(80907); const a = "fields",
                    o = (0, n(9787).a)({ slice: a, scope: "fields" }),
                    i = (0, r.Z0)({ name: a, initialState: { custom: [], available: [] }, reducers: {}, extraReducers: {} }).reducer }, 72220: (e, t, n) => { "use strict";
                n.d(t, { ez: () => c, gn: () => l, sh: () => s }); var r = n(80192),
                    a = n(10621),
                    o = n(7743); const i = e => ({ ...e, isCustom: !e.isDefault, display: "".concat(e.name[0]).concat(e.name.slice(1).toLowerCase()), model: e.model.toLowerCase(), viewOptions: (0, a.to)(e), isDefault: e.isDefault, createdDateOrDefault: e.isDefault ? "Standard Field" : "Custom Field" }),
                    l = (0, r.Mz)((e => e.organization.fields), (e => (e || []).map(i))),
                    s = (0, r.Mz)(o.kA, (e => e.some(a.CR))),
                    c = (0, r.Mz)(o.kA, (e => ({ rollup: null === e || void 0 === e ? void 0 : e.filter((e => a.Sg.includes(e.type))), url: null === e || void 0 === e ? void 0 : e.filter((e => a.zn.includes(e.type))), computed: null === e || void 0 === e ? void 0 : e.filter((e => ![a.ZE.ATTACHMENT, a.ZE.COMPUTED].includes(e.type))) }))) }, 18073: (e, t, n) => { "use strict";
                n.d(t, { A: () => lt }); var r = n(57528),
                    a = n(65043),
                    o = n(51136),
                    i = n(98587),
                    l = n(58168),
                    s = n(69292),
                    c = n(68606),
                    d = n(34535),
                    u = n(72876),
                    h = n(6803),
                    m = n(63336),
                    p = n(57056),
                    f = n(32400);

                function v(e) { return (0, f.Ay)("MuiAppBar", e) }(0, p.A)("MuiAppBar", ["root", "positionFixed", "positionAbsolute", "positionSticky", "positionStatic", "positionRelative", "colorDefault", "colorPrimary", "colorSecondary", "colorInherit", "colorTransparent", "colorError", "colorInfo", "colorSuccess", "colorWarning"]); var g = n(70579); const y = ["className", "color", "enableColorOnDark", "position"],
                    b = (e, t) => e ? "".concat(null == e ? void 0 : e.replace(")", ""), ", ").concat(t, ")") : t,
                    w = (0, d.Ay)(m.A, { name: "MuiAppBar", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, t["position".concat((0, h.A)(n.position))], t["color".concat((0, h.A)(n.color))]] } })((e => { let { theme: t, ownerState: n } = e; const r = "light" === t.palette.mode ? t.palette.grey[100] : t.palette.grey[900]; return (0, l.default)({ display: "flex", flexDirection: "column", width: "100%", boxSizing: "border-box", flexShrink: 0 }, "fixed" === n.position && { position: "fixed", zIndex: (t.vars || t).zIndex.appBar, top: 0, left: "auto", right: 0, "@media print": { position: "absolute" } }, "absolute" === n.position && { position: "absolute", zIndex: (t.vars || t).zIndex.appBar, top: 0, left: "auto", right: 0 }, "sticky" === n.position && { position: "sticky", zIndex: (t.vars || t).zIndex.appBar, top: 0, left: "auto", right: 0 }, "static" === n.position && { position: "static" }, "relative" === n.position && { position: "relative" }, !t.vars && (0, l.default)({}, "default" === n.color && { backgroundColor: r, color: t.palette.getContrastText(r) }, n.color && "default" !== n.color && "inherit" !== n.color && "transparent" !== n.color && { backgroundColor: t.palette[n.color].main, color: t.palette[n.color].contrastText }, "inherit" === n.color && { color: "inherit" }, "dark" === t.palette.mode && !n.enableColorOnDark && { backgroundColor: null, color: null }, "transparent" === n.color && (0, l.default)({ backgroundColor: "transparent", color: "inherit" }, "dark" === t.palette.mode && { backgroundImage: "none" })), t.vars && (0, l.default)({}, "default" === n.color && { "--AppBar-background": n.enableColorOnDark ? t.vars.palette.AppBar.defaultBg : b(t.vars.palette.AppBar.darkBg, t.vars.palette.AppBar.defaultBg), "--AppBar-color": n.enableColorOnDark ? t.vars.palette.text.primary : b(t.vars.palette.AppBar.darkColor, t.vars.palette.text.primary) }, n.color && !n.color.match(/^(default|inherit|transparent)$/) && { "--AppBar-background": n.enableColorOnDark ? t.vars.palette[n.color].main : b(t.vars.palette.AppBar.darkBg, t.vars.palette[n.color].main), "--AppBar-color": n.enableColorOnDark ? t.vars.palette[n.color].contrastText : b(t.vars.palette.AppBar.darkColor, t.vars.palette[n.color].contrastText) }, { backgroundColor: "var(--AppBar-background)", color: "inherit" === n.color ? "inherit" : "var(--AppBar-color)" }, "transparent" === n.color && { backgroundImage: "none", backgroundColor: "transparent", color: "inherit" })) })),
                    z = a.forwardRef((function(e, t) { const n = (0, u.A)({ props: e, name: "MuiAppBar" }),
                            { className: r, color: a = "primary", enableColorOnDark: o = !1, position: d = "fixed" } = n,
                            m = (0, i.default)(n, y),
                            p = (0, l.default)({}, n, { color: a, position: d, enableColorOnDark: o }),
                            f = (e => { const { color: t, position: n, classes: r } = e, a = { root: ["root", "color".concat((0, h.A)(t)), "position".concat((0, h.A)(n))] }; return (0, c.A)(a, v, r) })(p); return (0, g.jsx)(w, (0, l.default)({ square: !0, component: "header", ownerState: p, elevation: 4, className: (0, s.A)(f.root, r, "fixed" === d && "mui-fixed"), ref: t }, m)) })); var x = n(96446),
                    A = n(68903),
                    k = n(64775),
                    S = n(26240),
                    M = n(55013),
                    E = n(30344); const C = ["initialWidth", "width"],
                    T = ["xs", "sm", "md", "lg", "xl"],
                    H = function(e, t) { return !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2] ? T.indexOf(e) <= T.indexOf(t) : T.indexOf(e) < T.indexOf(t) },
                    L = function(e, t) { return arguments.length > 2 && void 0 !== arguments[2] && arguments[2] ? T.indexOf(t) <= T.indexOf(e) : T.indexOf(t) < T.indexOf(e) }; const I = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return t => { const { withTheme: n = !1, noSSR: r = !1, initialWidth: o } = e; return function(e) { const s = (0, S.A)(),
                                c = e.theme || s,
                                d = (0, k.A)({ theme: c, name: "MuiWithWidth", props: e }),
                                { initialWidth: u, width: h } = d,
                                m = (0, i.default)(d, C),
                                [p, f] = a.useState(!1);
                            (0, M.A)((() => { f(!0) }), []); const v = c.breakpoints.keys.slice().reverse().reduce(((e, t) => { const n = (0, E.A)(c.breakpoints.up(t)); return !e && n ? t : e }), null),
                                y = (0, l.default)({ width: h || (p || r ? v : void 0) || u || o }, n ? { theme: c } : {}, m); return void 0 === y.width ? null : (0, g.jsx)(t, (0, l.default)({}, y)) } } }()((function(e) { const { children: t, only: n, width: r } = e, o = (0, S.A)(); let i = !0; if (n)
                        if (Array.isArray(n))
                            for (let a = 0; a < n.length; a += 1) { if (r === n[a]) { i = !1; break } } else n && r === n && (i = !1); if (i)
                        for (let a = 0; a < o.breakpoints.keys.length; a += 1) { const t = o.breakpoints.keys[a],
                                n = e["".concat(t, "Up")],
                                l = e["".concat(t, "Down")]; if (n && H(t, r) || l && L(t, r)) { i = !1; break } }
                    return i ? (0, g.jsx)(a.Fragment, { children: t }) : null }));

                function j(e) { return (0, f.Ay)("PrivateHiddenCss", e) }(0, p.A)("PrivateHiddenCss", ["root", "xlDown", "xlUp", "onlyXl", "lgDown", "lgUp", "onlyLg", "mdDown", "mdUp", "onlyMd", "smDown", "smUp", "onlySm", "xsDown", "xsUp", "onlyXs"]); const V = ["children", "className", "only"],
                    O = (0, d.Ay)("div", { name: "PrivateHiddenCss", slot: "Root" })((e => { let { theme: t, ownerState: n } = e; const r = { display: "none" }; return (0, l.default)({}, n.breakpoints.map((e => { let { breakpoint: n, dir: a } = e; return "only" === a ? {
                                [t.breakpoints.only(n)]: r } : "up" === a ? {
                                [t.breakpoints.up(n)]: r } : {
                                [t.breakpoints.down(n)]: r } })).reduce(((e, t) => (Object.keys(t).forEach((n => { e[n] = t[n] })), e)), {})) })); const R = function(e) { const { children: t, className: n, only: r } = e, a = (0, i.default)(e, V), o = (0, S.A)(), d = []; for (let i = 0; i < o.breakpoints.keys.length; i += 1) { const e = o.breakpoints.keys[i],
                                t = a["".concat(e, "Up")],
                                n = a["".concat(e, "Down")];
                            t && d.push({ breakpoint: e, dir: "up" }), n && d.push({ breakpoint: e, dir: "down" }) } if (r) {
                            (Array.isArray(r) ? r : [r]).forEach((e => { d.push({ breakpoint: e, dir: "only" }) })) } const u = (0, l.default)({}, e, { breakpoints: d }),
                            m = (e => { const { classes: t, breakpoints: n } = e, r = { root: ["root", ...n.map((e => { let { breakpoint: t, dir: n } = e; return "only" === n ? "".concat(n).concat((0, h.A)(t)) : "".concat(t).concat((0, h.A)(n)) }))] }; return (0, c.A)(r, j, t) })(u); return (0, g.jsx)(O, { className: (0, s.A)(m.root, n), ownerState: u, children: t }) },
                    P = ["implementation", "lgDown", "lgUp", "mdDown", "mdUp", "smDown", "smUp", "xlDown", "xlUp", "xsDown", "xsUp"]; const D = function(e) { const { implementation: t = "js", lgDown: n = !1, lgUp: r = !1, mdDown: a = !1, mdUp: o = !1, smDown: s = !1, smUp: c = !1, xlDown: d = !1, xlUp: u = !1, xsDown: h = !1, xsUp: m = !1 } = e, p = (0, i.default)(e, P); return "js" === t ? (0, g.jsx)(I, (0, l.default)({ lgDown: n, lgUp: r, mdDown: a, mdUp: o, smDown: s, smUp: c, xlDown: d, xlUp: u, xsDown: h, xsUp: m }, p)) : (0, g.jsx)(R, (0, l.default)({ lgDown: n, lgUp: r, mdDown: a, mdUp: o, smDown: s, smUp: c, xlDown: d, xlUp: u, xsDown: h, xsUp: m }, p)) }; var F, N, _ = n(17392),
                    B = n(42518),
                    W = n(32143),
                    U = n(72119),
                    q = n(86852),
                    G = n(53492),
                    K = n(28623),
                    Z = n(75995),
                    Y = n(49092),
                    X = n(82244),
                    $ = n(96364),
                    Q = n(49015),
                    J = n(42006),
                    ee = n(14556),
                    te = n(44676),
                    ne = n(91688),
                    re = n(356),
                    ae = n(66856),
                    oe = n(84),
                    ie = n(75156),
                    le = n(80539),
                    se = n(61531); const ce = (0, U.Ay)(se.A)(F || (F = (0, r.A)(["\n  @media (max-width: 662px) {\n    visibility: hidden;\n    width: 0;\n    height: 0;\n  }\n"]))),
                    de = (0, U.Ay)(se.A)(N || (N = (0, r.A)(["\n  display: flex;\n  justify-content: center;\n  flex-direction: column;\n  @media (max-width: 662px) {\n    visibility: visible;\n    width: auto;\n    height: 100%;\n  }\n  @media (min-width: 663px) {\n    visibility: hidden;\n    width: 0;\n    height: 0;\n  }\n"]))); var ue = n(80192),
                    he = n(59269),
                    me = n(15622); const pe = e => e.organization.id,
                    fe = (0, ue.Mz)(he._, (e => null === e || void 0 === e ? void 0 : e.activeLicenseId)); var ve = n(37556),
                    ge = n(48853),
                    ye = n(47556),
                    be = n(78396); const we = [{ name: "Building Your Chart", action: "https://organimi.zendesk.com/hc/en-us/articles/360059109971-V7-Manually-build-your-org-chart-" }, { name: "Importing Your Data", action: "https://organimi.zendesk.com/hc/en-us/articles/4407715926292-Create-a-chart-by-importing-a-CSV-file-into-Organimi-" }, { name: "Adding Custom Fields", action: "https://organimi.zendesk.com/hc/en-us/articles/4407345997204-V7-Add-custom-fields-" }, { name: "Themes & Styling", action: "https://organimi.zendesk.com/hc/en-us/articles/************-Edit-themes-" }, { name: "Printing & Exporting", action: "https://organimi.zendesk.com/hc/en-us/articles/4411393919892-Organimi-s-printing-guide-" }, { name: "Sharing Charts", action: "https://organimi.zendesk.com/hc/en-us/articles/10276241612564-Share-charts-using-share-with-people-and-groups-" }, { name: "Embed on Website", action: "https://organimi.zendesk.com/hc/en-us/articles/4403014694548-V7-Share-your-chart-on-WordPress-" }, { name: "Board of Directors Chart", action: "https://organimi.zendesk.com/hc/en-us/articles/9681509816468-V7-Charts-for-governance-or-board-of-directors-" }]; var ze, xe = n(55357),
                    Ae = n(17339); const ke = (0, U.Ay)(xe.A)(ze || (ze = (0, r.A)(["\n  display: flex;\n  grid-gap: 16px;\n  align-items: center;\n  justify-content: flex-start;\n"]))),
                    Se = e => { var t; let { isMobileMenuOpen: n, handleMobileMenuClose: r, handleActionClick: a, handleQuickLinksClick: o, handleHelpLinkClick: i, handleUserClick: l, mobileMoreAnchorEl: s } = e; const { firstName: c, lastName: d } = (0, ee.d4)(J.VW), u = (0, ee.d4)(J.VF), h = (0, ee.d4)(fe), { userHasMinAccess: m } = (0, ge.A)(), p = m(be.td.ADMIN), { t: f } = (0, Y.B)(); return (0, g.jsxs)(te.A, { anchorEl: s, anchorOrigin: { vertical: "bottom", horizontal: "left" }, id: "toolbarTopMenu", keepMounted: !0, transformOrigin: { vertical: "top", horizontal: "left" }, open: n, onClose: r, children: [!!u && "trialing" === u.status && (0, g.jsx)(xe.A, { onClick: a("upgradeNow"), children: (0, g.jsxs)($.A, { variant: "caption", color: "textSecondary", display: "inline", children: [f("Header.Text.Trial"), " ", null === (t = u.trialInfo) || void 0 === t ? void 0 : t.daysRemaining, " ", f("Header.Text.DaysRemaining")] }) }), (0, g.jsxs)(ke, { onClick: a("setMobileView"), children: [(0, g.jsx)(se.A, { width: 36, justifyContent: "center", display: "flex", children: (0, g.jsx)(Ae.A, { children: (0, g.jsx)(ie.Ay, { icon: "Mobile", size: "xs" }) }) }), (0, g.jsx)($.A, { children: "Use Mobile View" })] }), p && h && (0, g.jsxs)(ke, { onClick: o, children: [(0, g.jsx)(se.A, { width: 36, justifyContent: "center", display: "flex", children: (0, g.jsx)(Ae.A, { children: (0, g.jsx)(ie.Ay, { icon: "ArrowLeft", size: "xs" }) }) }), (0, g.jsx)($.A, { children: f("Header.Buttons.QuickLinks") })] }), (0, g.jsxs)(ke, { onClick: i, children: [(0, g.jsx)(se.A, { width: 36, justifyContent: "center", display: "flex", children: (0, g.jsx)(Ae.A, { children: (0, g.jsx)(ie.Ay, { icon: "Help" }) }) }), (0, g.jsx)($.A, { children: "Help" })] }), (0, g.jsxs)(ke, { onClick: l, children: [(0, g.jsx)(se.A, { width: 36, justifyContent: "center", display: "flex", children: (0, g.jsx)(Ae.A, { children: (0, g.jsx)(le.A, { name: "".concat(c, " ").concat(d), width: 30, height: 30 }) }) }), (0, g.jsx)($.A, { children: "My Account" })] })] }) }; var Me = n(90318),
                    Ee = n(40454),
                    Ce = n(5571),
                    Te = n(9989),
                    He = n(72835),
                    Le = n(27017),
                    Ie = n(79718); const je = e => { let { anchorElUser: t, handleUserClose: n, handleActionClick: r, handleLogoutClick: a } = e; const { username: o, firstName: i, lastName: l, photo: s } = (0, ee.d4)(J.VW), c = (0, ee.d4)(J.VF), d = (0, ee.d4)(fe), { userType: u } = (0, ge.A)(), { t: h } = (0, Y.B)(); return (0, g.jsxs)(Ie.A, { id: "user-menu", anchorEl: t, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "center" }, keepMounted: !0, open: Boolean(t), onClose: n, children: [(0, g.jsx)(Me.A, { spacing: { all: 2 }, children: (0, g.jsxs)(Ee.A, { container: !0, wrap: "nowrap", children: [(0, g.jsx)(Ee.A, { item: !0, children: (0, g.jsx)(se.A, { mr: 2, children: (0, g.jsx)(le.A, { variant: "square", width: 80, height: 80, name: "".concat(i, " ").concat(l), src: s, useImageWithCfPolicy: !1 }) }) }), (0, g.jsxs)(Ee.A, { item: !0, xs: !0, children: [(0, g.jsxs)($.A, { variant: "h3", style: { textTransform: "capitalize" }, children: [i, " ", l] }), (0, g.jsx)(q.A, { spacing: { bottom: 2 }, children: (0, g.jsx)($.A, { variant: "body1", children: o }) }), "owner" === u && c && d && (0, g.jsx)(He.A, { color: "primary", variant: "outlined", onClick: r("upgradeNow"), children: h("Header.Menu.UpgradeNow") })] })] }) }), (0, g.jsx)(Ce.A, {}), (0, g.jsxs)(Te.A, { children: [d && (0, g.jsxs)(xe.A, { onClick: r("myAccount"), children: [(0, g.jsx)(Le.A, { children: (0, g.jsx)(ie.Ay, { icon: "User", size: "lg" }) }), (0, g.jsx)($.A, { children: h("Header.Menu.MyAccount") })] }), (0, g.jsxs)(xe.A, { onClick: a, children: [(0, g.jsx)(Le.A, { children: (0, g.jsx)(ie.Ay, { icon: "Power", size: "lg" }) }), (0, g.jsx)($.A, { children: h("Header.Menu.Logout") })] })] })] }) },
                    Ve = e => { let { anchorElAction: t, handleActionClose: n, handleActionClick: r } = e; const { t: a } = (0, Y.B)(), o = (0, ee.d4)(fe), { userHasMinAccess: i } = (0, ge.A)(), l = i(be.td.ADMIN), s = i(be.td.OWNER); return (0, g.jsxs)(te.A, { id: "actionsr-menu", anchorEl: t, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, open: Boolean(t), onClose: n, children: [l && (0, g.jsx)(xe.A, { onClick: r("newChart"), children: a("Header.QuickLinks.NewChart") }), s && (0, g.jsx)(xe.A, { onClick: r("newOrganization"), children: a("Header.QuickLinks.NewOrganization") }), l && (0, g.jsx)(Ce.A, {}), l && (0, g.jsx)(xe.A, { onClick: r("importExcel"), children: a("Header.QuickLinks.ImportExcelCsv") }), l && (0, g.jsx)(xe.A, { onClick: r("connect"), children: a("Header.QuickLinks.OrganimiConnect") }), l && (0, g.jsx)(xe.A, { onClick: r("organimiExplore"), children: "Organimi Explore" }), l && (0, g.jsx)(Ce.A, {}), l && o && (0, g.jsx)(xe.A, { onClick: r("upgradeNow"), children: (0, g.jsx)($.A, { color: "primary", weight: "bold", children: a("Header.Menu.UpgradeNow") }) })] }) }; const Oe = function(e) { let { anchorEl: t, onClose: n, changeLanguage: r } = e; return (0, g.jsxs)(te.A, { id: "actionsr-menu-lang", anchorEl: t, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, onClose: n, open: Boolean(t), children: [(0, g.jsx)(xe.A, { onClick: () => r("en"), children: "En" }), (0, g.jsx)(xe.A, { onClick: () => r("fr"), children: "Fr" })] }) }; var Re; const Pe = (0, U.Ay)(ie.Ay)(Re || (Re = (0, r.A)(["\n  margin-right: 5px;\n  font-size: 15px;\n  top: 4px;\n  position: relative;\n"]))),
                    De = e => { let { anchorElHelp: t, handleActionClick: n, handleMenuClose: r, handleHelpMenuMixpanelEvents: a, handleGettingStartedMenu: o } = e; const { t: i } = (0, Y.B)(); return (0, g.jsxs)(te.A, { id: "actionsr-menu-help", anchorEl: t, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, open: Boolean(t), onClose: r, children: [(0, g.jsx)(xe.A, { onClick: () => a("help_menu_search_help_center"), children: (0, g.jsxs)($.A, { variant: "body1", component: "a", href: "https://organimi.zendesk.com/hc/en-us", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: [(0, g.jsx)(Pe, { icon: "Search" }), "Search Help Center"] }) }), (0, g.jsxs)(xe.A, { onClick: o, style: { justifyContent: "space-between" }, children: ["Getting Started", (0, g.jsx)(ie.Ay, { icon: "ArrowRight", size: "xs" })] }), (0, g.jsx)(xe.A, { onClick: n("tours"), children: (0, g.jsx)($.A, { variant: "body1", component: "a", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: "Interactive Tutorials" }) }), (0, g.jsx)(xe.A, { onClick: () => a("help_menu_video_tutorials"), children: (0, g.jsx)($.A, { variant: "body1", component: "a", href: "https://www.youtube.com/c/Organimi/videos", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: "Video Tutorials" }) }), (0, g.jsx)(xe.A, { onClick: () => a("help_menu_training_link_click"), children: (0, g.jsx)($.A, { variant: "body1", component: "a", href: "https://www.organimi.com/training/", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: "Organimi U Training" }) }), (0, g.jsx)(xe.A, { onClick: () => a("help_menu_faq"), children: (0, g.jsx)($.A, { variant: "body1", component: "a", href: "https://www.organimi.com/questions/", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: "FAQ" }) }), (0, g.jsx)(xe.A, { onClick: n("whatsNew"), children: (0, g.jsx)($.A, { variant: "body1", color: "primary", style: { textDecoration: "none" }, children: i("Header.QuickLinks.WhatsNew") }) }), (0, g.jsx)(Ce.A, {}), (0, g.jsx)(xe.A, { onClick: () => a("help_menu_email_support"), children: (0, g.jsxs)($.A, { variant: "body1", component: "a", href: "mailto:<EMAIL>?subject=Contact Organimi Expert", target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: [(0, g.jsx)(Pe, { icon: "Email", size: "sm", style: { marginRight: 10 } }), "Email Support"] }) })] }) }; var Fe = n(85571),
                    Ne = n(24115),
                    _e = n(7091),
                    Be = n(53629),
                    We = n(8606),
                    Ue = n(63560); const qe = e => { let { anchorElNotifications: t, handleNotificationsClose: n } = e; const r = (0, ee.d4)(Fe.yV),
                        { canFetchNotifications: a, fetchedNotifications: o, notificationsLoading: i, notificationsLastUpdated: l, handleNotificationDialogOpen: s, handleNotificationDialogClose: c, openDialogCategory: d, activeNotificationForDialog: u } = (0, Ue.A)(); return (0, g.jsxs)(g.Fragment, { children: [(0, g.jsx)(Ie.A, { id: "user-menu", anchorEl: t, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "center" }, transformOrigin: { vertical: "top", horizontal: "right" }, keepMounted: !0, open: Boolean(t), onClose: n, children: (0, g.jsx)(se.A, { py: 2, children: (0, g.jsxs)(se.A, { width: "500px", minWidth: "500px", maxWidth: "500px", children: [(0, g.jsx)(se.A, { display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "center", gridGap: 8, px: 2, children: (0, g.jsx)($.A, { variant: "h6", weight: "medium", children: "Notifications" }) }), (0, g.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, g.jsx)(se.A, { maxHeight: "600px", overflow: "auto", px: 2, py: 2, children: a ? (0, g.jsx)(Ne.A, { loading: i && !o, spinnerSize: 40, title: "Loading notifications...", children: 0 === (null === r || void 0 === r ? void 0 : r.length) ? (0, g.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", height: "100%", gridGap: 8, children: [(0, g.jsx)(ie.Ay, { icon: "Flag", size: "x2" }), (0, g.jsx)($.A, { variant: "caption", children: "No notifications" })] }, "no-notifications") : (0, g.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: .4, children: null === r || void 0 === r ? void 0 : r.map((e => (0, g.jsx)(Be.A, { notification: e, handleDialogOpen: () => s(e), closeNotificationPanel: n }, "notification-" + (null === e || void 0 === e ? void 0 : e.id)))) }, "notifications-container") }) : (0, g.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", height: "100%", gridGap: 8, children: "Please select account to view notifications" }, "no-notifications") }), (0, g.jsx)(se.A, { my: 1, children: (0, g.jsx)(Ce.A, {}) }), (0, g.jsx)(se.A, { display: "flex", justifyContent: "space-between", alignItems: "center", flexDirection: "row-reverse", my: 1, px: 2, children: (0, g.jsx)(Ne.A, { loading: a && i, spinnerSize: 20, titleVariant: "caption", transparent: !0, children: (0, g.jsxs)($.A, { variant: "caption", children: ["Last Updated:", " ", null !== l && void 0 !== l && l.getTime() ? (0, _e.g)(l) : "Never"] }) }) })] })] }) }) }), (0, g.jsx)(We.A, { open: "system" === d, notification: u, handleClose: c })] }) }; var Ge; const Ke = (0, U.Ay)(se.A)(Ge || (Ge = (0, r.A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translateY(-50%) translateX(-50%);\n  background: #ffb74d;\n  border-radius: 24px;\n  z-index: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px 8px 16px;\n  cursor: pointer;\n  color: #1f1f1f;\n"]))),
                    Ze = () => { const e = (0, ee.d4)(me.A),
                            { openDialog: t } = (0, oe.A)("upgradeRequiredDialog"); return e && e.isOverLimit && e.softBlock ? (0, g.jsxs)(Ke, { onClick: () => { var n;
                                t({ license: e, resources: e.resourcesOverLimit || [], isLicenseOverLimit: e.isOverLimit || !1, isOwnerOrAdmin: ["owner", "admin"].includes(null === e || void 0 === e ? void 0 : e.accessLevel), showAccountSwitcher: !1, accountOwnerEmail: null === e || void 0 === e || null === (n = e.user) || void 0 === n ? void 0 : n.username, closable: !0 }) }, children: [(0, g.jsx)(ie.Ay, { size: "x2", icon: "ExclamationTriangle" }), (0, g.jsx)($.A, { children: "You account has gone over the limits. Click here for more details" })] }) : (0, g.jsx)(g.Fragment, {}) }; var Ye = n(94916),
                    Xe = n(29794),
                    $e = n(85865),
                    Qe = n(43845),
                    Je = n(37294),
                    et = n(10846),
                    tt = n(59177); const nt = () => { var e, t; const { bannerOpen: n, closeBanner: r, bannerNotification: o } = (0, Xe.A)(), i = (0, a.useMemo)((() => { if (!o || null === o || void 0 === o || !o.id) return null; return new((0, et.B)(o.notificationTopic))({ ...o }) }), [o]), l = (0, a.useMemo)((() => { var e, t; return (null === Je.Qs || void 0 === Je.Qs || null === (e = Je.Qs[(0, tt.Sn)((null === i || void 0 === i || null === (t = i.content) || void 0 === t ? void 0 : t.color) || "Primary")]) || void 0 === e ? void 0 : e[100]) || Je.Qs.Info[100] }), [i]); return n ? (0, g.jsxs)(x.A, { display: "flex", justifyContent: "center", width: "100%", bgcolor: l, px: 2, alignItems: "center", gap: 2, py: 1, position: "relative", children: [(0, g.jsx)($e.A, { variant: "bodyMD", component: "span", style: { textAlign: "center" }, fontWeight: 500, color: Je.Qs.Neutrals[900], children: null === i || void 0 === i ? void 0 : i.getNotificationTitle() }), (0, g.jsx)($e.A, { variant: "bodyMD", component: "span", style: { textAlign: "center" }, color: Je.Qs.Neutrals[900], children: null === i || void 0 === i ? void 0 : i.getNotificationSubtitle() }), (0, g.jsx)("a", { target: "_blank", href: (null === i || void 0 === i || null === (e = i.content) || void 0 === e ? void 0 : e.externalLink) || "#", children: (0, g.jsx)(Qe.A, { label: "Register Now", color: (null === i || void 0 === i || null === (t = i.content) || void 0 === t ? void 0 : t.color) || "primary", size: "large" }) }), (0, g.jsx)(x.A, { position: "absolute", right: 10, top: 0, children: (0, g.jsx)(_.A, { onClick: r, children: (0, g.jsx)(ie.me, { name: "remove", fontSize: 14, color: "#000000", variant: "light" }) }) })] }) : null }; var rt, at; const ot = (0, U.Ay)(ie.gF)(rt || (rt = (0, r.A)(["\n  @keyframes fadeIn {\n    0% {\n      opacity: 0.1;\n    }\n    100% {\n      opacity: 1;\n    }\n  }\n"]))),
                    it = (0, U.Ay)(o.A)(at || (at = (0, r.A)(["\n  .MuiBadge-badge {\n    height: 15px;\n    min-width: 12px;\n    font-size: 0.6rem;\n  }\n"]))),
                    lt = () => { var e; const t = (0, ee.wA)(),
                            n = (0, ne.useHistory)(),
                            { t: r, i18n: o } = (0, Y.B)(),
                            { openDialog: i } = (0, oe.A)("account"),
                            { toggleDialog: l } = (0, oe.A)("organizationMeta"),
                            { openDialog: s } = (0, oe.A)("trialExpiredDialog"),
                            { openDialog: c } = (0, oe.A)("accountPastDueDialog"),
                            { toggleDialog: d } = (0, oe.A)("upgradeDialog"),
                            { toggleDialog: u } = (0, oe.A)("whatsNewDialog"),
                            { toggleDialog: h } = (0, oe.A)("selectOrganization"),
                            { toggleDialog: m } = (0, oe.A)("backToV6Dialog"),
                            { toggleDialog: p } = (0, oe.A)("newChart"),
                            { openDialog: f } = (0, oe.A)("demoSpace"),
                            { openDialog: v } = (0, oe.A)("toursCatalog"),
                            { openDialog: y } = (0, oe.A)("importFile"),
                            b = (0, ye.A)(),
                            w = (0, ee.d4)(fe),
                            { userHasMinAccess: k } = (0, ge.A)(),
                            S = k(be.td.ADMIN),
                            [, M] = (0, Ye.A)("isDesktopMode", !1, !0),
                            E = (0, ee.d4)(pe),
                            [C, T] = (0, a.useState)(null),
                            [H, L] = (0, a.useState)(null),
                            [I, j] = (0, a.useState)(null),
                            [V, O] = (0, a.useState)(null),
                            [R, P] = (0, a.useState)(null),
                            [F, N] = (0, a.useState)(null),
                            [U, se] = (0, a.useState)(null),
                            ue = Boolean(C),
                            { firstName: he, lastName: me, photo: ze } = (0, ee.d4)(J.VW),
                            xe = (0, ee.d4)(J.VF),
                            Ae = (0, ee.d4)(Fe.E_),
                            ke = (0, ee.d4)(Fe.BT),
                            Me = (0, ee.d4)(Fe.yV),
                            Ee = (0, a.useRef)(),
                            Ce = (0, a.useRef)(0),
                            { userType: Te } = (0, ge.A)(),
                            [He, Le] = (0, a.useState)(!0),
                            { onTourEventComplete: Ie } = (0, ve.M)({}),
                            { getNotifications: Re, handleNotificationDialogOpen: Pe } = (0, Ue.A)(),
                            Ne = e => { L(e.currentTarget) },
                            _e = () => { L(null) },
                            Be = e => { j(e.currentTarget) },
                            We = e => { P(e.currentTarget), Le(!1), tt("help_menu_open"), Ie({ event: "app-top-bar-help--click" }) },
                            Ge = () => { P(null), tt("help_menu_close") },
                            Ke = e => () => { switch (e) {
                                    case "setMobileView":
                                        M(!1); break;
                                    case "newChart":
                                        p(); break;
                                    case "newOrganization":
                                        l({ redirectToOrganization: !0 }); break;
                                    case "tryDemoChart":
                                        l(); break;
                                    case "whatsNew":
                                        tt("help_menu_whats_new"), u(); break;
                                    case "upgradeNow":
                                        !Te || "owner" !== Te && "admin" !== Te || d(); break;
                                    case "importExcel":
                                        y(); break;
                                    case "importActiveDirectory":
                                        Xe("activedirectory"); break;
                                    case "importGsuite":
                                        Xe("gsuite"); break;
                                    case "importSalesforce":
                                        Xe("salesforce"); break;
                                    case "openHelpCenter":
                                        window.open("https://organimi.zendesk.com/hc/en-us/categories/************-Version-7-Help-Center", "_blank"); break;
                                    case "myAccount":
                                        i(); break;
                                    case "backToV6":
                                        m(); break;
                                    case "connect":
                                        n.push((0, ae.Oq)()); break;
                                    case "tours":
                                        v(), Ge(); break;
                                    case "organimiExplore":
                                        f({ source: "help-link" }); break;
                                    default:
                                        console.log("QuickActions via Header Click called with invalid type", e) } Qe(), _e() },
                            Xe = e => { E ? $e(e, E) : h({ title: "Select an organization to begin", onSelect: $e(e) }) },
                            $e = e => t => { e ? n.push((0, ae.ve)({ importType: e, orgId: t }), { appRoute: !0 }) : n.push((0, ae.xv)({ orgId: t }), { appRoute: !0 }) },
                            Qe = () => { j(null) },
                            Je = () => { O(null) },
                            et = () => { b() };
                        (0, a.useEffect)((() => { var e;
                            xe && "trialing" === xe.status && (null === (e = xe.trialInfo) || void 0 === e ? void 0 : e.daysRemaining) < 1 && s(), xe && "past_due" === xe.status && c() }), [xe]), (0, a.useEffect)((() => { document.title = "Organimi " }), []); const tt = e => { re.A.trackEvent({ eventName: e.toUpperCase() }) };
                        (0, a.useEffect)((() => { if (ke > 0) { const e = Me.filter((e => !1 === e.isRead && !0 === (null === e || void 0 === e ? void 0 : e.autoOpen) && "dialog" === e.displayType)),
                                    t = e[e.length - 1];
                                Ee.current = setTimeout((() => { Pe(t) }), 3e3) } return () => { Ee.current && clearTimeout(Ee.current) } }), [ke, Me]), (0, a.useEffect)((() => { U && Ce.current >= 10 && Re() }), [U]), (0, a.useEffect)((() => { U && at({ lastSeenNotificationId: Ae, lastSeenAt: new Date }) }), [U, Ae]); const [rt, at] = (0, Ye.A)("license.".concat(null === xe || void 0 === xe ? void 0 : xe.id, ".lastSeenNotification"), {}, !0), lt = (0, a.useMemo)((() => (null === rt || void 0 === rt ? void 0 : rt.lastSeenNotificationId) === Ae ? 0 : ke), [ke, Ae, rt]); return (0, g.jsxs)(z, { position: "fixed", "data-tour-anchor": "app-top-bar", color: "white", style: { boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.3)" }, children: [(0, g.jsxs)(x.A, { children: [(0, g.jsx)(nt, {}), (0, g.jsx)(x.A, { px: 1, pt: 1, children: (0, g.jsxs)(A.Ay, { container: !0, justifyContent: "space-between", style: { alignItems: "center" }, children: [(0, g.jsxs)(A.Ay, { item: !0, children: [(0, g.jsx)(D, { smDown: !0, children: (0, g.jsx)(G.A, { height: 50, src: K, alt: "Organimi", onClick: et }) }), (0, g.jsx)(D, { mdUp: !0, children: (0, g.jsx)(G.A, { height: 50, src: Z.A, alt: "organimi", onClick: et }) })] }), (0, g.jsx)(Ze, {}), (0, g.jsxs)(A.Ay, { item: !0, children: [(0, g.jsx)(de, { children: (0, g.jsx)(_.A, { onClick: e => { T(e.currentTarget) }, children: (0, g.jsx)(ie.gF, { icon: "HamburgerMenu", size: "xl", color: "#000000" }) }) }), (0, g.jsxs)(ce, { children: [(0, g.jsx)(q.A, { spacing: { right: 2 }, variant: "span", children: "viewer" === Te ? (0, g.jsx)(B.A, { flash: !0, color: "primary", variant: "outlined", onClick: Ke("whatsNew"), children: r("Header.QuickLinks.WhatsNew") }) : (0, g.jsx)(g.Fragment, { children: w && S && (0, g.jsx)(B.A, { flash: !0, color: "primary", variant: "outlined", endIcon: (0, g.jsx)(ie.Ay, { icon: "ExpandDown" }), onClick: Be, children: r("Header.Buttons.QuickLinks") }) }) }), !!xe && "trialing" === xe.status && (0, g.jsxs)(g.Fragment, { children: [(0, g.jsx)(q.A, { spacing: { horizontal: 2 }, variant: "span", children: (0, g.jsx)(X.A, { weight: 1, height: 20, orientation: "vers" }) }), (0, g.jsxs)($.A, { variant: "caption", color: "textSecondary", display: "inline", component: "a", onClick: Ke("upgradeNow"), children: [r("Header.Text.Trial"), " ", null === (e = xe.trialInfo) || void 0 === e ? void 0 : e.daysRemaining, " ", r("Header.Text.DaysRemaining")] })] }), (0, g.jsx)(q.A, { spacing: { horizontal: 2 }, variant: "span", children: (0, g.jsx)(X.A, { weight: 1, height: 20, orientation: "vers" }) }), (0, g.jsx)(q.A, { spacing: { horizontal: 1 }, variant: "span", children: (0, g.jsx)(_.A, { onClick: e => { se(e.currentTarget) }, color: "primary", children: (0, g.jsx)(it, { badgeContent: lt, color: "primary", children: (0, g.jsx)(ie.gF, { icon: "Bell", size: "xl" }) }) }) }), (0, g.jsx)(_.A, { onClick: e => We(e), "aria-describedby": "dashboard_tooltip_9", color: He ? "primary" : void 0, style: He ? { animation: "fadeIn 2s infinite linear" } : {}, "data-tour-anchor": "app-top-bar-help", children: (0, g.jsx)(ot, { icon: "Help", size: "xl" }) }, "dashboard-header-help-button"), (0, g.jsx)(x.A, { display: "inline-block", children: (0, g.jsx)(X.A, { weight: 1, height: 20 }) }), (0, g.jsx)(q.A, { spacing: { left: 1 }, variant: "span", children: (0, g.jsx)(_.A, { onClick: Ne, children: (0, g.jsx)(le.A, { name: "".concat(he, " ").concat(me), width: 30, height: 30, src: ze, useImageWithCfPolicy: !1 }) }) })] })] })] }) })] }), (0, g.jsx)(Se, { isMobileMenuOpen: ue, handleActionClick: Ke, handleMobileMenuClose: () => { T(null) }, handleQuickLinksClick: Be, mobileMoreAnchorEl: C, handleHelpLinkClick: We, handleUserClick: Ne }), (0, g.jsx)(je, { handleActionClick: Ke, anchorElUser: H, handleLogoutClick: async () => { const { error: e } = await t(Q.ip.logout());
                                    e || (_e(), window.location.assign((0, ae.iD)())) }, handleUserClose: _e }), (0, g.jsx)(Ve, { anchorElAction: I, handleActionClose: Qe, handleActionClick: Ke }), (0, g.jsx)(Oe, { anchorEl: V, onClose: Je, changeLanguage: e => { o.changeLanguage(e), Je() } }), (0, g.jsx)(De, { anchorElHelp: R, handleActionClick: Ke, handleHelpMenuMixpanelEvents: tt, handleMenuClose: Ge, handleGettingStartedMenu: e => { N(e.currentTarget), tt("help_menu_getting_started") } }), (0, g.jsx)(qe, { anchorElNotifications: U, handleNotificationsClose: () => { se(null) } }), (0, g.jsx)(te.A, { id: "actionsr-menu-getting-started", anchorEl: F, getContentAnchorEl: null, anchorOrigin: { vertical: "top", horizontal: 0 }, transformOrigin: { vertical: "top", horizontal: 205 }, open: Boolean(F), MenuListProps: { onMouseLeave: () => N(null) }, onClose: () => N(null), children: we.map((e => (0, g.jsx)(W.A, { onClick: () => { return t = e.name, void re.A.trackEvent({ eventName: "HELP_MENU_GETTING_STARTED", extraParams: { component: t.toLowerCase() } }); var t }, children: (0, g.jsx)($.A, { variant: "body1", component: "a", href: e.action, target: "_blank", color: "textPrimary", style: { textDecoration: "none" }, children: e.name }) }, e.name))) })] }) } }, 53629: (e, t, n) => { "use strict";
                n.d(t, { A: () => z }); var r = n(65043),
                    a = n(61531),
                    o = n(9579),
                    i = n(17339),
                    l = n(59177),
                    s = n(61938),
                    c = n(10846),
                    d = n(14556),
                    u = n(31286),
                    h = n(75156),
                    m = n(91688),
                    p = n(83340),
                    f = n(47088),
                    v = n(24115),
                    g = n(66588),
                    y = n(64418),
                    b = n(49015),
                    w = n(70579); const z = e => { let { notification: t, handleDialogOpen: n, closeNotificationPanel: z } = e; const x = (0, d.wA)(),
                        [A, k] = (0, r.useState)(!1),
                        { show: S } = (0, g.A)(),
                        { confirmAction: M } = (0, y.A)(),
                        E = {
                            [f.ix.InvitePendingOwner]: { primary: async function() { var e, n; if (null === this || void 0 === this || null === (e = this.meta) || void 0 === e || !e.token || null === this || void 0 === this || null === (n = this.meta) || void 0 === n || !n.licenseId) return;
                                    k(!0); const { payload: r, error: a } = await x(p.hW.acceptAccess({ notificationId: t.id, token: this.meta.token, licenseId: this.meta.licenseId, ignoreErrorHandler: !0 }));!a && null !== r && void 0 !== r && r.success && (await x(b.lf.getLicenses({ ignoreErrorHandler: !0 })), S("Account Access Updated")), k(!1) }, secondary: async function() { var e, n;
                                    null !== this && void 0 !== this && null !== (e = this.meta) && void 0 !== e && e.token && null !== this && void 0 !== this && null !== (n = this.meta) && void 0 !== n && n.licenseId && (k(!0), M({ title: "Reject Account Owner Invitation", message: "Are you sure you do not want owner access into this account? A new invite will have to be sent from the existing account if you change your mind", cancelButtonText: "Cancel", confirmButtonText: "Reject Invitation", cancelFunc: () => { k(!1) }, execFunc: async () => { const { payload: e, error: n } = await x(p.hW.rejectAccess({ notificationId: t.id, token: this.meta.token, licenseId: this.meta.licenseId, ignoreErrorHandler: !0 }));
                                            k(!1), n && S("Error, No changes could be made", "info"), !n && null !== e && void 0 !== e && e.success && S("Account Access Updated") } })) } } },
                        C = (0, m.useHistory)(),
                        T = new((0, c.B)(t.notificationTopic))(t, E); return (0, w.jsx)(s.SI, { children: (0, w.jsx)(v.A, { loading: A, transparent: !0, children: (0, w.jsxs)(a.A, { display: "flex", flexDirection: "row", alignItems: "flex-start", gridGap: 16, children: [(0, w.jsx)(a.A, { style: { borderRadius: "50%", padding: "2px", height: "48px", width: "48px", border: "1px solid #dad7d7", display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }, flexShrink: 0, children: T.getNotificationIcon() }, "notification-icon" + (null === t || void 0 === t ? void 0 : t.id)), (0, w.jsx)(a.A, { flex: 1, children: (0, w.jsxs)(a.A, { display: "flex", flexDirection: "column", children: [(0, w.jsxs)(a.A, { justifyContent: "space-between", alignItems: "center", display: "flex", flexDirection: "row", children: [(0, w.jsx)(s.Kk, { children: T.getNotificationTitle() }), (0, w.jsxs)(a.A, { display: "flex", alignItems: "center", gridGap: 8, children: [(0, w.jsx)(s.Rl, { children: T.getNotificationTime() }), !(t.isRead || T.hideRead) && (0, w.jsx)(o.Ay, { title: "Mark as read", placement: "right", arrow: !0, children: (0, w.jsx)(i.A, { style: { fontSize: "12px", padding: "6px" }, color: "primary", onClick: async () => { var e, n, r; let a = [t.id]; var o;
                                                            null !== t && void 0 !== t && null !== (e = t.meta) && void 0 !== e && e.isGrouped && null !== t && void 0 !== t && null !== (n = t.meta) && void 0 !== n && null !== (r = n.allNotificationLogIds) && void 0 !== r && r.length && (a = null === t || void 0 === t || null === (o = t.meta) || void 0 === o ? void 0 : o.allNotificationLogIds);
                                                            await x(u.tZ.updateReadStatus({ notificationIdsToMarkAsRead: a })) }, children: (0, w.jsx)(h.Ay, { icon: "FavouriteCircle", size: "xs" }) }) })] })] }), (0, w.jsx)(s.a5, { children: T.getNotificationSubtitle() }), (0, w.jsx)(s.Kx, { children: (0, l.RP)(T.getNotificationBody(), 120) }), (0, w.jsx)(a.A, { my: 1, display: "flex", flexDirection: "row", gridGap: "8", justifyContent: "space-between", children: T.getNotificationActionMenu({ primaryAction: n, navigateTo: e => { e && (C.push(e), z()) } }) })] }) })] }) }) }, null === t || void 0 === t ? void 0 : t.id) } }, 61938: (e, t, n) => { "use strict";
                n.d(t, { Kk: () => h, Kx: () => f, Rl: () => m, SI: () => v, a5: () => p }); var r, a, o, i, l, s = n(57528),
                    c = n(72119),
                    d = n(96364),
                    u = n(61531); const h = (0, c.Ay)(d.A)(r || (r = (0, s.A)(["\n  font-size: 0.9rem;\n  font-weight: 500;\n  //line-height: 1.6;\n  letter-spacing: 0.0075em;\n  color: rgba(0, 0, 0, 0.87);\n"]))),
                    m = (0, c.Ay)(d.A)(a || (a = (0, s.A)(["\n  font-size: 0.8rem;\n  font-weight: 400;\n  font-style: italic;\n  letter-spacing: 0.01071em;\n  color: rgba(0, 0, 0, 0.6);\n"]))),
                    p = (0, c.Ay)(d.A)(o || (o = (0, s.A)(["\n  font-size: 0.8rem;\n  font-weight: 500;\n  font-style: italic;\n  //line-height: 1.43;\n  letter-spacing: 0.01071em;\n  color: rgba(0, 0, 0, 0.6);\n"]))),
                    f = (0, c.Ay)(d.A)(i || (i = (0, s.A)(["\n  font-size: 0.8rem;\n  font-weight: 400;\n  line-height: 1.43;\n  letter-spacing: 0.01071em;\n  color: rgba(0, 0, 0, 0.6);\n"]))),
                    v = (0, c.Ay)(u.A)(l || (l = (0, s.A)(["\n  padding: 4px;\n  &:hover {\n    background-color: #f5f5f5;\n  }\n"]))) }, 56012: (e, t, n) => { "use strict";
                n.d(t, { $F: () => p, Av: () => v, Ep: () => m, iO: () => g, zJ: () => f }); var r, a, o, i, l, s = n(57528),
                    c = n(72119),
                    d = n(35801),
                    u = n(61531),
                    h = n(96364); const m = (0, c.Ay)(d.A)(r || (r = (0, s.A)(["\n  padding: 0px;\n"]))),
                    p = (0, c.Ay)(u.A)(a || (a = (0, s.A)(["\n  padding: 16px 32px;\n  display: flex;\n  flex-direction: column;\n  background-color: ", ";\n  color: ", ";\n  //width: 45%;\n  position: relative;\n"])), (e => e.theme.palette.primary.main), (e => e.theme.palette.primary.contrastText)),
                    f = (0, c.Ay)(u.A)(o || (o = (0, s.A)(["\n  display: flex;\n  flex-direction: column;\n"]))),
                    v = (0, c.Ay)(h.A)(i || (i = (0, s.A)(["\n  font-size: 1rem;\n  font-weight: 400;\n  color: inherit;\n  margin-bottom: 8px;\n"]))),
                    g = (0, c.Ay)(h.A)(l || (l = (0, s.A)(["\n  font-size: 0.9rem;\n  font-weight: 300;\n  line-height: 1.43;\n  color: inherit;\n"]))) }, 8606: (e, t, n) => { "use strict";
                n.d(t, { A: () => f }); var r = n(65043),
                    a = n(61531),
                    o = n(96364),
                    i = n(31286),
                    l = n(14556),
                    s = n(24115),
                    c = n(10846),
                    d = n(56012),
                    u = n(2173),
                    h = n(84),
                    m = n(356),
                    p = n(70579); const f = e => { let { open: t, notification: n, handleClose: f } = e; const [v, g] = r.useState((null === n || void 0 === n ? void 0 : n.content) || {}), [y, b] = r.useState(!0), w = (0, l.wA)();
                    (0, r.useEffect)((() => { null !== n && void 0 !== n && n.id && null !== n && void 0 !== n && n.hasDialogContent && "dialog" === n.displayType ? (b(!0), (async () => { const { payload: e, error: t } = await w(i.tZ.getDialogContent({ notificationId: null === n || void 0 === n ? void 0 : n.id })); if (e) { const t = e.notification;
                                g(null === t || void 0 === t ? void 0 : t.content) } else console.log({ error: t });
                            setTimeout((() => { b(!1) }), 300) })()) : b(!1) }), [n]); const [z, x] = (0, u.A)((async () => { await (async () => { null !== n && void 0 !== n && n.isRead || await w(i.tZ.updateReadStatus({ notificationIdsToMarkAsRead: [null === n || void 0 === n ? void 0 : n.id] })) })(), g({}), f() })), A = (0, r.useMemo)((() => { if (!n || null === n || void 0 === n || !n.id) return null; return new((0, c.B)(n.notificationTopic))({ ...n, content: { ...(null === n || void 0 === n ? void 0 : n.content) || {}, dialogContent: null === v || void 0 === v ? void 0 : v.dialogContent } }) }), [v, n]), { openDialog: k } = (0, h.A)("upgradeDialog"), S = e => () => { m.A.trackEvent({ eventName: e }), k(), x() }; return (0, p.jsx)(d.Ep, { open: t, onClose: x, children: null !== n && void 0 !== n && n.hasDialogContent ? (0, p.jsx)(a.A, { p: y ? 2 : 0, children: (0, p.jsx)(s.A, { loading: y || z, title: z ? "" : "Loading notification content", transparent: z, children: (0, p.jsxs)(d.zJ, { children: [(0, p.jsxs)(d.$F, { gridGap: "8px", children: [(0, p.jsx)(o.A, { variant: "h6", color: "inherit", children: "Announcement" }), (0, p.jsx)(o.A, { variant: "h3", color: "inherit", children: null === A || void 0 === A ? void 0 : A.getNotificationTitle() })] }), (0, p.jsxs)(a.A, { px: 4, py: 2, children: [(0, p.jsx)(d.Av, { children: null === A || void 0 === A ? void 0 : A.getNotificationSubtitle() }), (0, p.jsx)(d.iO, { variant: "body1", color: "inherit", children: null === A || void 0 === A ? void 0 : A.getNotificationDialogContent() }), null === A || void 0 === A ? void 0 : A.getNotificationDialogActionButtons({ handleClose: x, actions: { upgrade: S } })] })] }) }) }) : (0, p.jsxs)(d.zJ, { children: [(0, p.jsxs)(d.$F, { gridGap: "8px", children: [(0, p.jsx)(o.A, { variant: "h6", color: "inherit", children: "Announcement" }), (0, p.jsx)(o.A, { variant: "h3", color: "inherit", children: null === A || void 0 === A ? void 0 : A.getNotificationTitle() })] }), (0, p.jsxs)(a.A, { p: 2, children: [(0, p.jsx)(d.Av, { children: null === A || void 0 === A ? void 0 : A.getNotificationSubtitle() }), (0, p.jsx)(d.iO, { variant: "body1", color: "inherit", children: null === A || void 0 === A ? void 0 : A.getNotificationBody() }), null === A || void 0 === A ? void 0 : A.getNotificationDialogActionButtons({ handleClose: x, actions: { upgrade: S } })] })] }) }) } }, 10846: (e, t, n) => { "use strict";
                n.d(t, { B: () => I });
                n(65043); var r = n(7091),
                    a = n(75995),
                    o = n(72835),
                    i = n(61531),
                    l = n(70579); const s = class { constructor(e) { this.id = void 0, this.notificationTopic = void 0, this.content = void 0, this.hasDialogContent = void 0, this.createdAt = void 0, this.isRead = void 0, this.meta = void 0, this.displayType = void 0, this.id = e.id, this.notificationTopic = e.notificationTopic, this.content = e.content, this.hasDialogContent = e.hasDialogContent, this.createdAt = e.createdAt, this.isRead = e.isRead, this.meta = e.meta, this.displayType = e.displayType } getNotificationIcon() { return (0, l.jsx)("img", { src: a.A, width: 40, height: 40, alt: "" }) } getNotificationTitle() { return "getNotificationTitle for ".concat(this.notificationTopic, " not implemented") } getNotificationSubtitle() { return "getNotificationSubtitle for ".concat(this.notificationTopic, " not implemented") } getNotificationBody() { throw new Error("getNotificationBody() must be implemented") } getNotificationTime() { return (0, r.g)(new Date(this.createdAt)) } getNotificationDialogContent() { throw new Error("getNotificationDialogContent() must be implemented") } getNotificationDialogActionButtons(e) { let { handleClose: t } = e; return (0, l.jsx)(i.A, { mt: 1, display: "flex", flexDirection: "row", gridGap: "8", justifyContent: "flex-end", children: (0, l.jsx)(o.A, { color: "secondary", onClick: t, variant: "contained", size: "small", children: "Close" }) }) } getNotificationActionMenu(e) { throw new Error("getNotificationActionMenu() must be implemented ".concat(e)) } getNotificationReadStatus() { return this.isRead } }; var c = n(75156),
                    d = n(96364),
                    u = n(61938);
                class h extends s { constructor(e) { super(e) } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "IT", size: "lg" }) } getNotificationTitle() { switch (this.notificationTopic) {
                            case "inviteAcceptedOwner":
                                return "Owner Invitation Accepted";
                            case "inviteAcceptedAdmin":
                                return "Admin Invitation Accepted";
                            case "inviteAcceptedEditor":
                                return "Editor Invitation Accepted";
                            case "inviteAcceptedViewer":
                                return "Viewer Invitation Accepted";
                            default:
                                return "Invitation Accepted" } } getNotificationSubtitle() { var e, t, n, r, a, o, i, l; switch (this.notificationTopic) {
                            case "inviteAcceptedOwner":
                                return "".concat(null === this || void 0 === this || null === (e = this.meta) || void 0 === e || null === (t = e.invitedUser) || void 0 === t ? void 0 : t.username, " has accepted your invitation");
                            case "inviteAcceptedAdmin":
                                return "".concat(null === this || void 0 === this || null === (n = this.meta) || void 0 === n || null === (r = n.invitedUser) || void 0 === r ? void 0 : r.username, " has accepted your invitation");
                            case "inviteAcceptedEditor":
                            case "inviteAcceptedViewer":
                                var s, c; if (null !== this && void 0 !== this && null !== (a = this.meta) && void 0 !== a && null !== (o = a.invitedUser) && void 0 !== o && o.username) return "".concat(null === this || void 0 === this || null === (s = this.meta) || void 0 === s || null === (c = s.invitedUser) || void 0 === c ? void 0 : c.username, " has accepted your invitation"); { var d; const e = null === this || void 0 === this || null === (d = this.meta) || void 0 === d ? void 0 : d.invitedUsers,
                                        t = null === e || void 0 === e ? void 0 : e.length; return "".concat(t, " users have accepted your invitation") } } return "".concat(null === this || void 0 === this || null === (i = this.meta) || void 0 === i || null === (l = i.invitedUser) || void 0 === l ? void 0 : l.username, " has accepted your invitation") } getNotificationBody() { var e, t, n, r, a, o; switch (this.notificationTopic) {
                            case "inviteAcceptedOwner":
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", null === this || void 0 === this || null === (e = this.meta) || void 0 === e || null === (t = e.invitedUser) || void 0 === t ? void 0 : t.username, " "] }), (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [" ", "now has the full access to your account"] })] });
                            case "inviteAcceptedAdmin":
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", null === this || void 0 === this || null === (n = this.meta) || void 0 === n || null === (r = n.invitedUser) || void 0 === r ? void 0 : r.username, " "] }), (0, l.jsxs)(u.Kx, { display: "inline", children: [" ", "now has the admin access to the organization", " "] }), (0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", null === this || void 0 === this || null === (a = this.meta) || void 0 === a || null === (o = a.organization) || void 0 === o ? void 0 : o.name] })] });
                            case "inviteAcceptedEditor":
                                { var i, s, c, h; let e = ""; if (null !== this && void 0 !== this && null !== (i = this.meta) && void 0 !== i && null !== (s = i.invitedUser) && void 0 !== s && s.username) { var m, p;
                                        e = "".concat(null === this || void 0 === this || null === (m = this.meta) || void 0 === m || null === (p = m.invitedUser) || void 0 === p ? void 0 : p.username) } else { var f; const t = null === this || void 0 === this || null === (f = this.meta) || void 0 === f ? void 0 : f.invitedUsers,
                                            n = null === t || void 0 === t ? void 0 : t.length; var v; if (1 === n) e = "".concat(null === (v = t[0]) || void 0 === v ? void 0 : v.username);
                                        else if (2 === n) { var g, y;
                                            e = "".concat(null === (g = t[0]) || void 0 === g ? void 0 : g.username, " and ").concat(null === (y = t[1]) || void 0 === y ? void 0 : y.username) } else { var b;
                                            e = "".concat(null === (b = t[0]) || void 0 === b ? void 0 : b.username, " and ").concat(n - 1, " others") } } return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: e }), " "] }), (0, l.jsxs)(u.Kx, { display: "inline", children: [" ", "now has the editor access to the chart:"] }), (0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", null === this || void 0 === this || null === (c = this.meta) || void 0 === c || null === (h = c.chart) || void 0 === h ? void 0 : h.name, " "] })] }) }
                            case "inviteAcceptedViewer":
                                { var w, z, x, A; let e = ""; if (null !== this && void 0 !== this && null !== (w = this.meta) && void 0 !== w && null !== (z = w.invitedUser) && void 0 !== z && z.username) { var k, S;
                                        e = "".concat(null === this || void 0 === this || null === (k = this.meta) || void 0 === k || null === (S = k.invitedUser) || void 0 === S ? void 0 : S.username) } else { var M; const t = null === this || void 0 === this || null === (M = this.meta) || void 0 === M ? void 0 : M.invitedUsers,
                                            n = null === t || void 0 === t ? void 0 : t.length; var E; if (1 === n) e = "".concat(null === (E = t[0]) || void 0 === E ? void 0 : E.username);
                                        else if (2 === n) { var C, T;
                                            e = "".concat(null === (C = t[0]) || void 0 === C ? void 0 : C.username, " and ").concat(null === (T = t[1]) || void 0 === T ? void 0 : T.username) } else { var H;
                                            e = "".concat(null === (H = t[0]) || void 0 === H ? void 0 : H.username, " and ").concat(n - 1, " others") } } return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: e }), " "] }), (0, l.jsxs)(u.Kx, { display: "inline", children: [" ", "now has the viewer access to the chart:"] }), (0, l.jsxs)(u.Kx, { display: "inline", weight: "bold", children: [" ", null === this || void 0 === this || null === (x = this.meta) || void 0 === x || null === (A = x.chart) || void 0 === A ? void 0 : A.name, " "] })] }) } } } getNotificationActionMenu() { return (0, l.jsx)(l.Fragment, {}) } } var m, p = n(57528),
                    f = n(72119),
                    v = n(30105); const g = (0, f.Ay)(v.A)(m || (m = (0, p.A)(["\n  padding: 4px 11px;\n  font-size: 0.7rem;\n"]))); var y = n(8289),
                    b = n(56012);
                class w extends s { constructor(e) { super(e) } getNotificationIcon() { return super.getNotificationIcon() } getNotificationTitle() { var e; return null === this || void 0 === this || null === (e = this.content) || void 0 === e ? void 0 : e.title } getNotificationSubtitle() { var e; return null === this || void 0 === this || null === (e = this.content) || void 0 === e ? void 0 : e.subtitle } getNotificationBody() { var e; return null === this || void 0 === this || null === (e = this.content) || void 0 === e ? void 0 : e.body } getNotificationDialogContent() { var e; const t = null === this || void 0 === this ? void 0 : this.content; return null === t || void 0 === t || null === (e = t.dialogContent) || void 0 === e ? void 0 : e.map(((e, t) => { switch (e.type) {
                                case "h1":
                                case "h2":
                                case "h3":
                                case "h4":
                                case "h5":
                                case "h6":
                                    return (0, l.jsx)(d.A, { variant: e.type, color: "inherit", weight: "medium", children: e.text }, t);
                                case "p":
                                    return (0, l.jsx)(b.iO, { variant: "body2", color: "inherit", children: e.text }, t);
                                case "br":
                                    return (0, l.jsx)(i.A, { mt: "12px" });
                                case "a":
                                    return (0, l.jsx)(y.A, { href: e.link, target: "_blank", children: (0, l.jsxs)(i.A, { display: "flex", flexDirection: "row", gridGap: "4px", alignItems: "center", children: [(0, l.jsx)(d.A, { weight: "medium", color: "inherit", children: e.text }), (0, l.jsx)(c.Ay, { icon: "ExternalLink", size: "md" })] }) }, t);
                                case "li":
                                    return (0, l.jsx)("li", { children: e.text }, t);
                                case "actionButton":
                                    return (0, l.jsx)(l.Fragment, {});
                                default:
                                    return (0, l.jsx)(b.iO, { variant: "body2", color: "inherit", children: e.text || "" }, t) } })) } getNotificationDialogActionButtons(e) { var t, n; let { handleClose: r, actions: a } = e; const s = null === this || void 0 === this || null === (t = this.content) || void 0 === t || null === (n = t.dialogContent) || void 0 === n ? void 0 : n.filter((e => "actionButton" === e.type)); return 0 === (null === s || void 0 === s ? void 0 : s.length) ? super.getNotificationDialogActionButtons({ handleClose: r, actions: a }) : (0, l.jsxs)(i.A, { mt: 1, display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "flex-end", children: [(0, l.jsx)(o.A, { color: "primary", onClick: r, variant: "outlined", size: "small", children: "Close" }), null === s || void 0 === s ? void 0 : s.map(((e, t) => { var n; return (0, l.jsx)(o.A, { color: (null === e || void 0 === e ? void 0 : e.actionButtonColor) || "primary", onClick: a[null === e || void 0 === e ? void 0 : e.actionId](null === e || void 0 === e || null === (n = e.trackParams) || void 0 === n ? void 0 : n.eventName) || r, variant: (null === e || void 0 === e ? void 0 : e.actionButtonVariant) || "contained", size: "small", children: e.text }, t) }))] }) } getNotificationActionMenu(e) { let { primaryAction: t } = e; return (0, l.jsx)(l.Fragment, { children: (0, l.jsx)(g, { color: "primary", variant: "contained", onClick: "banner" === this.displayType ? void 0 : t, size: "large", href: "banner" === this.displayType ? this.content.externalLink : void 0, target: "banner" === this.displayType ? "_blank" : void 0, children: this.content.externalLinkLabel || "View More" }) }) } } var z = n(47088),
                    x = n(9579),
                    A = n(66856);
                class k extends s { constructor(e) { var t, n, r, a, o;
                        super(e), this.chart = void 0, this.performingUser = void 0, this.isMove = void 0, this.organization = void 0, this.sourceChart = void 0, this.chart = (null === this || void 0 === this || null === (t = this.meta) || void 0 === t ? void 0 : t.chart) || {}, this.performingUser = (null === this || void 0 === this || null === (n = this.meta) || void 0 === n ? void 0 : n.performingUser) || {}, this.isMove = (null === this || void 0 === this || null === (r = this.meta) || void 0 === r ? void 0 : r.isMove) || !1, this.organization = (null === this || void 0 === this || null === (a = this.meta) || void 0 === a ? void 0 : a.organization) || {}, this.sourceChart = (null === this || void 0 === this || null === (o = this.meta) || void 0 === o ? void 0 : o.sourceChart) || {} } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "Chart", size: "lg" }) } getNotificationTitle() { switch (this.notificationTopic) {
                            case z.ix.NewChart:
                                return "Chart Created";
                            case z.ix.DeletedChart:
                                return "Chart Deleted";
                            case z.ix.ReplicateChartSuccess:
                                return "Chart ".concat(this.isMove ? "Moved" : "Duplicated");
                            default:
                                return "" } } getNotificationSubtitle() { switch (this.notificationTopic) {
                            case z.ix.NewChart:
                                return "".concat(this.chart.name, " has been created");
                            case z.ix.DeletedChart:
                                return "".concat(this.chart.name, " has been deleted");
                            default:
                                return "" } } getNotificationBody() { switch (this.notificationTopic) {
                            case z.ix.NewChart:
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.performingUser.username, " "] }), (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [" ", "created a new chart", " ", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: ["$", this.chart.name] }), ", under", " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: this.organization.name }), "."] })] });
                            case z.ix.DeletedChart:
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.performingUser.username, " "] }), (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [" ", "deleted the chart", " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: this.chart.name }), " ", ", under", " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: this.organization.name }), "."] })] });
                            case z.ix.ReplicateChartSuccess:
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: ["The chart", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.sourceChart.name, " "] }), "has been ", this.isMove ? "moved" : "duplicated", " to a new chart named", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.chart.name, " "] }), "in organization", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.organization.name] }), "."] }) }) } } getNotificationActionMenu(e) { let { navigateTo: t } = e; switch (this.notificationTopic) {
                            case z.ix.NewChart:
                            case z.ix.ReplicateChartSuccess:
                                return (0, l.jsx)(x.Ay, { title: "", placement: "bottom", arrow: !0, children: (0, l.jsx)(i.A, { my: 1, width: "max-content", children: (0, l.jsx)(g, { color: "primary", variant: "contained", size: "large", onClick: () => { t((0, A.Wx)({ base: "protected", chartId: this.chart.id, orgId: this.organization.id })) }, children: "View Chart" }) }) });
                            default:
                                return (0, l.jsx)(l.Fragment, {}) } } } class S extends s { constructor(e) { super(e) } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "Building", size: "lg" }) } getNotificationTitle() { switch (this.notificationTopic) {
                            case "newOrg":
                                return "Organization Created";
                            case "deletedOrg":
                                return "Organization Deleted" } } getNotificationSubtitle() { var e, t, n, r; switch (this.notificationTopic) {
                            case "newOrg":
                                return "".concat(null === this || void 0 === this || null === (e = this.meta) || void 0 === e || null === (t = e.organization) || void 0 === t ? void 0 : t.name, " has been created");
                            case "deletedOrg":
                                return "".concat(null === this || void 0 === this || null === (n = this.meta) || void 0 === n || null === (r = n.organization) || void 0 === r ? void 0 : r.name, " has been deleted") } } getNotificationBody() { var e, t, n, r, a, o, i, s; switch (this.notificationTopic) {
                            case "newOrg":
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", null === this || void 0 === this || null === (e = this.meta) || void 0 === e || null === (t = e.performingUser) || void 0 === t ? void 0 : t.username, " "] }), (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [" ", "created a new Organization", " ", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: ["$", null === this || void 0 === this || null === (n = this.meta) || void 0 === n || null === (r = n.organization) || void 0 === r ? void 0 : r.name] }), "."] })] });
                            case "deletedOrg":
                                return (0, l.jsxs)(l.Fragment, { children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", null === this || void 0 === this || null === (a = this.meta) || void 0 === a || null === (o = a.performingUser) || void 0 === o ? void 0 : o.username, " "] }), (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [" ", "deleted the Organization", " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: null === this || void 0 === this || null === (i = this.meta) || void 0 === i || null === (s = i.organization) || void 0 === s ? void 0 : s.name }), "."] })] }) } } getNotificationActionMenu() { return (0, l.jsx)(l.Fragment, {}) } } var M = n(14556),
                    E = n(85571);
                class C extends s { constructor(e) { var t, n, r, a, o, i, l, s;
                        super(e), this.orgName = (null === this || void 0 === this || null === (t = this.meta) || void 0 === t || null === (n = t.organization) || void 0 === n ? void 0 : n.name) || "", this.snapshotDate = (null === (r = new Date(null === this || void 0 === this || null === (a = this.meta) || void 0 === a ? void 0 : a.snapshotDate)) || void 0 === r ? void 0 : r.toDateString()) || "", this.displayType = "automated" === (null === this || void 0 === this || null === (o = this.meta) || void 0 === o ? void 0 : o.type) ? "Automated" : "On-Demand", this.newOrgName = (null === this || void 0 === this || null === (i = this.meta) || void 0 === i ? void 0 : i.restoredOrgName) || "", this.newOrgId = null === this || void 0 === this || null === (l = this.meta) || void 0 === l ? void 0 : l.restoredOrgId, this.deletedCount = (null === this || void 0 === this || null === (s = this.meta) || void 0 === s ? void 0 : s.deletedCount) || "" } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "DatabaseLight", size: "lg" }) } getNotificationTitle() { switch (this.notificationTopic) {
                            case "snapshotCreated":
                                return "Backup Created";
                            case "snapshotDeleted":
                                return "Backup Deleted";
                            case "snapshotRestored":
                                return "Backup Restored";
                            case "snapshotRestoreFailed":
                                return "Backup Restore Failed";
                            case "snapshotLimitReached":
                                return "Backups Limit Has Reached";
                            default:
                                return "" } } getNotificationSubtitle() { return this.notificationTopic, "" } getNotificationBody() { switch (this.notificationTopic) {
                            case "snapshotCreated":
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [this.displayType, " "] }), "backup created for", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.orgName, " "] }), "organization on", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.snapshotDate] })] }) });
                            case "snapshotDeleted":
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [(0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [this.deletedCount, " "] }), "Backup snapshot", this.deletedCount > 1 ? "s" : "", " of organization", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.orgName, " "] }), "has been deleted."] }) });
                            case "snapshotRestored":
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: ["Backup snapshot of organization", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.orgName, " "] }), "from", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.snapshotDate, " "] }), "has been restored to a new organization named", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.newOrgName, " "] })] }) });
                            case "snapshotRestoreFailed":
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: ["Backup snapshot of organization", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.orgName, " "] }), "from", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.snapshotDate, " "] }), "could not be restored. Contact Organimi support for assistance."] }) });
                            case "snapshotLimitReached":
                                return (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: ["Could not create a backup of your", (0, l.jsxs)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: [" ", this.orgName, " "] }), "organization because you have reached the max limit of backup snapshots available in your plan. In order to create more snapshots, delete older snapshots from any of your organizations."] }) });
                            default:
                                return "" } } getNotificationActionMenu(e) { let { navigateTo: t } = e; return "snapshotRestored" === this.notificationTopic ? () => { const e = !!(0, M.d4)(E.Ok).find((e => e.id === this.newOrgId)),
                                n = e ? "" : "Restored backup not available. You can restore the same backup again."; return (0, l.jsx)(x.Ay, { title: n, placement: "bottom", arrow: !0, children: (0, l.jsx)(i.A, { my: 1, width: "max-content", children: (0, l.jsx)(g, { color: "primary", variant: "contained", size: "large", disabled: !e, onClick: () => { t((0, A.r2)({ orgId: this.newOrgId })) }, children: "View Restored Backup" }) }) }) } : (0, l.jsx)(l.Fragment, {}) } } class T extends s { constructor(e, t) { super(e), this.hideRead = !0, this.actions = null === t || void 0 === t ? void 0 : t[z.ix.InvitePendingOwner] } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "IT", size: "lg" }) } getNotificationTitle() { return "New Pending Account Invitation" } getNotificationSubtitle() { var e, t, n, r; return null !== this && void 0 !== this && null !== (e = this.meta) && void 0 !== e && e.senderName ? "".concat(null === this || void 0 === this || null === (n = this.meta) || void 0 === n ? void 0 : n.senderName, " has invited you to become an owner on their account") : null !== this && void 0 !== this && null !== (t = this.meta) && void 0 !== t && t.senderEmail ? "".concat(null === this || void 0 === this || null === (r = this.meta) || void 0 === r ? void 0 : r.senderEmail, " has invited you to become an owner on their account") : "You've been invited! " } getNotificationBody() { return (0, l.jsx)(l.Fragment, { children: (0, l.jsx)(u.Kx, { display: "inline", children: " Please submit your response below" }) }) } getNotificationActionMenu() { var e, t, n, r; return (0, l.jsxs)(i.A, { display: "flex", gridGap: 16, children: [(0, l.jsx)(g, { color: "primary", variant: "outlined", onClick: null === (e = this.actions) || void 0 === e || null === (t = e.secondary) || void 0 === t ? void 0 : t.bind(this), size: "large", children: "Delete Request" }), (0, l.jsx)(g, { color: "primary", variant: "contained", onClick: null === (n = this.actions) || void 0 === n || null === (r = n.primary) || void 0 === r ? void 0 : r.bind(this), size: "large", children: "Accept Invite" })] }) } } var H = n(59177);
                class L extends s { constructor(e) { var t, n, r, a, o, i, l, s, c, d, u, h, m, p, f, v;
                        super(e), this.orgName = void 0, this.chartName = void 0, this.orgId = void 0, this.chartId = void 0, this.integrationType = void 0, this.uploadType = void 0, this.resources = void 0, this.orgName = (null === this || void 0 === this || null === (t = this.meta) || void 0 === t || null === (n = t.organization) || void 0 === n ? void 0 : n.name) || "", this.chartName = (null === this || void 0 === this || null === (r = this.meta) || void 0 === r || null === (a = r.chart) || void 0 === a ? void 0 : a.name) || "", this.orgId = (null === this || void 0 === this || null === (o = this.meta) || void 0 === o || null === (i = o.organization) || void 0 === i ? void 0 : i.id) || "", this.chartId = (null === this || void 0 === this || null === (l = this.meta) || void 0 === l || null === (s = l.chart) || void 0 === s ? void 0 : s.id) || "", this.integrationType = (null === this || void 0 === this || null === (c = this.meta) || void 0 === c || null === (d = c.integration) || void 0 === d ? void 0 : d.type) || "", this.uploadType = (null === this || void 0 === this || null === (u = this.meta) || void 0 === u || null === (h = u.chartIntegration) || void 0 === h || null === (m = h.params) || void 0 === m || null === (p = m.options) || void 0 === p ? void 0 : p.uploadType) || ""; const g = [];
                        null !== this && void 0 !== this && null !== (f = this.meta) && void 0 !== f && f.isPhotoFieldMatched && g.push("photo"), null !== this && void 0 !== this && null !== (v = this.meta) && void 0 !== v && v.isAttachmentFieldMatched && g.push("attachment"), 0 === g.length && g.push("attachment"), this.resources = g.map((e => (0, H.Sn)(e) + "s")).join(" & ") } getNotificationIcon() { return (0, l.jsx)(c.Ay, { icon: "Integration", size: "lg" }) } getNotificationTitle() { return this.notificationTopic === z.ix.IntegrationFileSyncSuccess ? "".concat(this.resources, " sync completed") : "" } getNotificationSubtitle() { return this.notificationTopic === z.ix.IntegrationFileSyncSuccess ? "".concat(this.resources, " sync completed for ").concat(this.orgName, " organization") : "" } getNotificationBody() { return this.notificationTopic === z.ix.IntegrationFileSyncSuccess ? (0, l.jsx)(l.Fragment, { children: (0, l.jsxs)(d.A, { display: "inline", fontSize: "inherit", children: [this.resources, " sync completed for", "members" === this.uploadType ? "all the members in the " : " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: this.orgName }), "chart" === this.uploadType ? (0, l.jsxs)(l.Fragment, { children: [", chart", " ", (0, l.jsx)(d.A, { display: "inline", weight: "bold", fontSize: "inherit", children: this.chartName }), "."] }) : ""] }) }) : "" } getNotificationActionMenu(e) { let { navigateTo: t } = e; if (this.notificationTopic === z.ix.IntegrationFileSyncSuccess) { return () => (0, l.jsx)(x.Ay, { title: this.chartName, placement: "bottom", arrow: !0, children: (0, l.jsx)(i.A, { my: 1, width: "max-content", children: (0, l.jsx)(g, { color: "primary", variant: "contained", size: "large", onClick: () => { const e = (0, A.Wx)({ orgId: this.orgId, chartId: this.chartId, base: "protected" }),
                                                n = (0, A.Zm)({ orgId: this.orgId });
                                            t("members" === this.uploadType ? n : e) }, children: "members" === this.uploadType ? "View Members" : "View Chart" }) }) }) } return (0, l.jsx)("span", {}) } } const I = e => { var t; switch (null === (t = z.Vc[e]) || void 0 === t ? void 0 : t.category) {
                        case z.at.PendingInvite:
                            return T;
                        case z.at.Invite:
                            return h;
                        case z.at.System:
                            return w;
                        case z.at.Chart:
                            return k;
                        case z.at.Org:
                            return S;
                        case z.at.Snapshot:
                            return C;
                        case z.at.Integration:
                            return L;
                        default:
                            return s } } }, 7091: (e, t, n) => { "use strict";
                n.d(t, { g: () => r }); const r = e => { const t = (new Date).getTime() - (null === e || void 0 === e ? void 0 : e.getTime()) || 0,
                        n = Math.floor(t / 1e3),
                        r = Math.floor(n / 60),
                        a = Math.floor(r / 60); return a > 24 ? "".concat(Math.floor(a / 24), " days ago") : a > 0 ? "".concat(a, " hours ago") : r > 0 ? "".concat(r, " minutes ago") : n > 0 ? "".concat(n, " seconds ago") : "Just now" } }, 31286: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, tZ: () => o }); var r = n(80907); const a = "notification",
                    o = (0, n(47730).B)({ slice: a, scope: "notification" }),
                    i = (0, r.Z0)({ name: a, initialState: { notifications: [], unreadNotifications: 0, loading: !1 }, reducers: { setNotificationsLoading: (e, t) => { let { payload: n } = t;
                                e.loading = n }, "all/fulfilled": (e, t) => { const { notifications: n = [], unreadNotifications: r } = t.payload || {};
                                n.length && (e.notifications = [...n, ...e.notifications], e.unreadNotifications = (e.unreadNotifications || 0) + (r || 0)) }, "updateReadStatus/fulfilled": (e, t) => { var n; const { notificationIdsToMarkAsRead: r } = (null === t || void 0 === t || null === (n = t.meta) || void 0 === n ? void 0 : n.arg) || {};
                                r.length && (e.notifications = e.notifications.map((e => (r.includes(e.id) && (e.isRead = !0), e))), e.unreadNotifications = Math.max(e.unreadNotifications - r.length, 0)) } }, extraReducers: { "license/acceptAccess/fulfilled": (e, t) => { let { meta: { arg: n } } = t; const r = null === n || void 0 === n ? void 0 : n.notificationId,
                                    a = [...e.notifications || []],
                                    o = a.findIndex((e => e.id === r)); if (o > -1) { a[o].isRead || (e.unreadNotifications = Math.max(e.unreadNotifications - 1, 0)), a.splice(o, 1), e.notifications = a } }, "license/acceptAccess/rejected": (e, t) => { let { meta: { arg: n } } = t; const r = null === n || void 0 === n ? void 0 : n.notificationId,
                                    a = [...e.notifications || []],
                                    o = a.findIndex((e => e.id === r)); if (o > -1) { a[o].isRead || (e.unreadNotifications = Math.max(e.unreadNotifications - 1, 0)), a.splice(o, 1), e.notifications = a } } } }),
                    { setNotificationsLoading: l } = i.actions,
                    s = i.reducer }, 85571: (e, t, n) => { "use strict";
                n.d(t, { BT: () => o, E_: () => r, Ok: () => i, yV: () => a }); const r = e => { var t, n; return (null === (t = ((null === (n = e.notification) || void 0 === n ? void 0 : n.notifications) || [])[0]) || void 0 === t ? void 0 : t.id) || "" },
                    a = e => { var t; return (null === (t = e.notification) || void 0 === t ? void 0 : t.notifications) || [] },
                    o = e => { var t; return (null === (t = e.notification) || void 0 === t ? void 0 : t.unreadNotifications) || 0 },
                    i = e => { var t; return (null === e || void 0 === e || null === (t = e.user) || void 0 === t ? void 0 : t.organizations) || [] } }, 73179: (e, t, n) => { "use strict";
                n.d(t, { A2: () => l, Ay: () => a, CN: () => o, UO: () => s, Wh: () => c, g4: () => d, qi: () => i }); const r = (0, n(80907).Z0)({ name: "history", initialState: { undo: [], redo: [], isTrackingEnabled: !1 }, reducers: { addUndo: (e, t) => { let { payload: n } = t; const { eventType: r, arg: a } = n;
                                delete a.isUndo, delete a.isRedo, e.undo.push({ eventType: r, arg: a }) }, performUndo: (e, t) => { let { payload: n } = t; const { eventType: r, arg: a, pushRedo: o = !0 } = n;
                                delete a.isUndo, delete a.isRedo, o && e.redo.push({ eventType: r, arg: a }), e.undo.pop() }, performRedo: (e, t) => { let { payload: n } = t; const { eventType: r, arg: a } = n;
                                delete a.isUndo, delete a.isRedo, e.redo.pop(), e.undo.push({ eventType: r, arg: a }) }, clearRedo: e => { e.redo = [] }, setTracking: (e, t) => { let { payload: n } = t;
                                e.isTrackingEnabled = n.isTrackingEnabled }, clearHistory: e => { e.redo = [], e.undo = [] } } }),
                    a = r.reducer,
                    { addUndo: o, performUndo: i, performRedo: l, clearRedo: s, setTracking: c, clearHistory: d } = r.actions }, 20913: (e, t, n) => { "use strict";
                n.d(t, { K: () => r, z: () => a }); const r = e => e.importPhotos.people,
                    a = e => [...[...e.user.organizations].filter((e => "owner" === e.accessLevel || "admin" === e.accessLevel))] }, 17323: (e, t, n) => { "use strict";
                n.d(t, { Ah: () => s, Ay: () => f }); var r = n(80907),
                    a = n(9922),
                    o = n(9787); const i = "import",
                    l = { currentImport: null, customFieldsMap: null, columnMap: a.Ay.initialImportFields.reduce(((e, t) => (e[t.fieldId] = { selectedValue: "", selectedValueExample: "" }, e)), {}), integrations: { active: [], available: [] }, integration: {}, logs: [] },
                    s = (0, o.a)({ slice: i, scope: "import" }),
                    c = (0, r.Z0)({ name: i, initialState: l, reducers: { setImport: (e, t) => { e.currentImport = t.payload }, setColumnMap: (e, t) => { let { payload: n } = t;
                                e.columnMap = n }, setCustomFieldsMap: (e, t) => { let { payload: n } = t;
                                e.customFieldsMap = n }, "getIntegrations/fulfilled": (e, t) => { let { payload: { active: n, available: r } } = t; const a = r.map((e => { const t = n.find((t => t.type === e.id)); if (t) { const { id: n } = t; return { ...e, activeIntegrationId: n } } return e }));
                                e.integrations = { active: n, available: a } }, "updateIntegrationOptions/fulfilled": (e, t) => { let { payload: { integration: n } } = t;
                                e.integration = { ...n } }, connectNewChartIntegration: e => { e.currentImport = { ...e.currentImport, chartId: "none", importType: "new" } }, editCurrentChartIntegration: (e, t) => { let { payload: { chartId: n, chartName: r } } = t;
                                e.currentImport = { ...e.currentImport, chartId: n, importType: "existing", options: { ...e.currentImport.options, name: r } } }, "updateChartIntegration/fulfilled": (e, t) => { let { payload: { integration: n } } = t;
                                e.integration = { ...n } }, updateColumnMapField: (e, t) => { let { payload: { fieldId: n, match: r } } = t;
                                n && (e.columnMap[n] = r) } }, extraReducers: { "import/getAvailableIntegrations/fulfilled": (e, t) => { let { payload: { available: n } } = t;
                                e.integrations.available = n }, "integration/getIntegrations/fulfilled": (e, t) => { let { payload: { available: n, active: r } } = t;
                                e.integrations.available = n, e.integrations.active = r } } }),
                    { setImport: d, setColumnMap: u, setCustomFieldsMap: h, editCurrentChartIntegration: m, connectNewChartIntegration: p } = c.actions,
                    f = c.reducer }, 54707: (e, t, n) => { "use strict";
                n.d(t, { r: () => r }); const r = function() { let e = (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "").match(/[-\w]{25,}/); return e && e[0] } }, 71887: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => C, Cr: () => x, GF: () => v, HO: () => E, L8: () => c, Nn: () => M, QN: () => A, S1: () => f, U2: () => d, dD: () => m, h_: () => b, hb: () => p, i7: () => h, kJ: () => w, o2: () => z }); var r = n(80907),
                    a = n(4981),
                    o = n(47730),
                    i = n(9787); const l = "integration",
                    s = { sourceOverview: {}, integrations: { available: [], active: [] }, active: null, setup: { mode: a.fp.NEW, columns: [], meta: { uploadType: "chart", chartName: "", overwriteExisting: !1, overwriteId: "", syncEnabled: !1, includePhotos: !1 }, structureFields: { id: "", parent: "", department: "", company: "", orgUnit: "", team: "" }, autoBuild: { departments: !0, assistants: !1 }, exclusionRules: { no_roleManagerOrReports: !1, empty_memberFirstName: !1, empty_memberLastName: !1, empty_roleTitle: !1 }, inclusions: { includeAll: { departments: !0, companies: !0, orgUnits: !0, teams: !0, officeLocations: !0, employeeTypes: !0, groups: !0 }, departments: [], companies: [], officeLocations: [], employeeTypes: [], orgUnits: [], teams: [], groups: [] }, filtersAvailable: { groups: null } }, logs: [] },
                    c = (0, i.a)({ slice: l, scope: "integration" }),
                    d = (0, o.B)({ slice: l, scope: "integration" }),
                    u = (0, r.Z0)({ name: l, initialState: s, reducers: { setIntegrationMeta: (e, t) => { let { payload: { uploadType: n, syncEnabled: r, chartName: a, overwriteExisting: o, overwriteId: i, fileLocation: l, selectedFileId: s } } = t;
                                e.setup.meta.overwriteExisting = o, e.setup.meta.overwriteId = i, e.setup.meta.chartName = a, e.setup.meta.syncEnabled = r, l && (e.setup.meta.fileLocation = l), s && (e.setup.meta.selectedFileId = s), n && (e.setup.meta.uploadType = n) }, setFieldMatch: (e, t) => { let { payload: { organizationFieldId: n, selectedField: r } } = t; const { label: a, model: o, selectedValue: i } = r || {}, l = e.setup.columns.findIndex((e => e.fieldId === n)); if (-1 !== l) { e.setup.columns[l].selectedValue = i } else e.setup.columns.push({ fieldId: n, selectedValue: i, label: a, model: o }) }, setInclusionsOfType: (e, t) => { const { payload: { resource: n, items: r = [], includeAll: a } } = t; "groups" === n ? (console.log(e.setup.inclusions[n]), e.setup.inclusions[n] = e.setup.inclusions[n].reduce(((e, t) => (r.includes(t.id) && e.push(t), e)), []), console.log(e.setup.inclusions[n])) : e.setup.inclusions[n] = r, e.setup.inclusions.includeAll[n] = a || !1 }, setExclusionRules: (e, t) => { e.setup.exclusionRules = t.payload }, setSetupColumns: (e, t) => { let { payload: n } = t;
                                e.setup.columns = n }, setSetupStructuredFields: (e, t) => { let { payload: n } = t;
                                e.setup.structureFields = n }, setStructureField: (e, t) => { let { payload: { name: n, value: r } } = t;
                                e.setup.structureFields[n] = r }, setAutoBuildRule: (e, t) => { let { payload: { name: n, value: r } } = t;
                                e.setup.autoBuild[n] = r }, setMetaProperty: (e, t) => { let { payload: { name: n, value: r } } = t;
                                e.setup.meta[n] = r }, setCurrentOptions: (e, t) => { e.setup.options = t.payload }, updateExclusionRule: (e, t) => { const { payload: { name: n, value: r } } = t;
                                e.setup.exclusionRules[n] = r }, updateMetaSetting: (e, t) => { const { payload: { name: n, value: r } } = t;
                                e.setup.meta[n] = r }, resetActiveIntegration: e => { e.setup = { ...s.setup } }, editChartIntegration: (e, t) => { var n, r, o, i; let { payload: { chartIntegration: l } } = t; const { params: c } = l || {}, { options: d } = c || {}, { integrationOptions: u } = d || {}; let h = c.columnMap || [],
                                    m = null === l || void 0 === l || null === (n = l.params) || void 0 === n || null === (r = n.options) || void 0 === r ? void 0 : r.uploadType,
                                    p = null !== l && void 0 !== l && l.chart ? "chart" : m;
                                e.setup = { ...s.setup, columns: h, mode: h.length ? a.fp.EDIT : a.fp.NEW, meta: { uploadType: p, chartName: null === l || void 0 === l || null === (o = l.chart) || void 0 === o ? void 0 : o.name, overwriteExisting: !0, overwriteId: null === (i = l.chart) || void 0 === i ? void 0 : i.id, syncEnabled: l.syncEnabled, fileLocation: u.fileLocation, selectedFileId: u.selectedFileId }, structureFields: u.structuredFields, autoBuild: u.autoBuild, exclusionRules: u.exclusionRules, inclusions: u.inclusions, externalDataSource: u.externalDataSource || "" } }, "getGroupsAvailable/fulfilled": (e, t) => { let { payload: { data: n } } = t;
                                e.setup.filtersAvailable.groups = (n || []).sort(((e, t) => { var n; return null === (n = e.displayName) || void 0 === n ? void 0 : n.localeCompare(null === t || void 0 === t ? void 0 : t.displayName) })) }, "createInitIntegration/fulfilled": (e, t) => { let { payload: n } = t; const r = e.integrations.active,
                                    a = r.findIndex((e => e.type === n.type));
                                a > -1 ? r[a] = n : r.push(n), e.active = n }, "getIntegrations/fulfilled": (e, t) => { let { payload: { active: n, available: r } } = t; const a = r.map((e => { const t = n.find((t => t.type === e.id)); if (t) { const { id: n } = t; return { ...e, activeIntegrationId: n } } return e }));
                                e.integrations = { active: n, available: a } }, "getIntegration/fulfilled": (e, t) => { let { payload: { integration: n } } = t;
                                e.active = n }, "updateChartIntegration/fulfilled": (e, t) => { let { meta: { arg: { name: n, value: r, chartIntegrationId: a } } } = t; switch (n) {
                                    case "integrationOptions":
                                        { var o; let t; const n = ((null === e || void 0 === e || null === (o = e.active) || void 0 === o ? void 0 : o.charts) || []).find((e => (null === e || void 0 === e ? void 0 : e.id) === a)); if (n) { var i, l;
                                                t = (null === n || void 0 === n || null === (i = n.params) || void 0 === i || null === (l = i.options) || void 0 === l ? void 0 : l.integrationOptions) || {} } else { var s, c, d, u, h, m, p, f, v, g; if ((null === (s = e.active) || void 0 === s || null === (c = s.photo) || void 0 === c ? void 0 : c.id) === a) t = (null === (h = e.active.photo) || void 0 === h || null === (m = h.params) || void 0 === m || null === (p = m.options) || void 0 === p ? void 0 : p.integrationOptions) || {}; if ((null === (d = e.active) || void 0 === d || null === (u = d.roster) || void 0 === u ? void 0 : u.id) === a) t = (null === (f = e.active.roster) || void 0 === f || null === (v = f.params) || void 0 === v || null === (g = v.options) || void 0 === g ? void 0 : g.integrationOptions) || {} } const y = ["fileLocation"]; for (let e of Object.keys(r)) y.includes(e) && (t[e] = r[e]); break }
                                    case "syncEnabled":
                                        { var y, b, w, z, x; const t = ((null === e || void 0 === e || null === (y = e.active) || void 0 === y ? void 0 : y.charts) || []).find((e => (null === e || void 0 === e ? void 0 : e.id) === a));t && (t.syncEnabled = r), (null === (b = e.active) || void 0 === b || null === (w = b.photo) || void 0 === w ? void 0 : w.id) === a && (e.active.photo.syncEnabled = r), (null === (z = e.active) || void 0 === z || null === (x = z.roster) || void 0 === x ? void 0 : x.id) === a && (e.active.roster.syncEnabled = r); break }
                                    default:
                                        return e } }, "updateIntegration/fulfilled": (e, t) => { let { meta: { arg: { name: n, value: r } } } = t; switch (n) {
                                    case "notificationSuccessOn":
                                    case "notificationFailureOn":
                                    case "notificationFailureEmails":
                                    case "notificationSuccessEmails":
                                        e.active[n] = r; break;
                                    default:
                                        return e } }, "updateIntegrationOptions/fulfilled": (e, t) => { let { payload: { integration: n } } = t;
                                e.active = { ...n } }, "removeIntegration/fulfilled": (e, t) => { let { meta: { arg: { integrationType: n } } } = t;
                                e.active = null; const r = e.integrations.active.findIndex((e => e.type === n));
                                r > -1 && e.integrations.active.splice(r, 1) }, "removeChartIntegration/fulfilled": (e, t) => { let { meta: { arg: { chartIntegrationId: n } } } = t; if (e.active) { var r, a; const t = e.active.charts.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === n));
                                    t > -1 && e.active.charts.splice(t, 1), (null === (r = e.active.photo) || void 0 === r ? void 0 : r.id) === n && (e.active.photo = null), (null === (a = e.active.roster) || void 0 === a ? void 0 : a.id) === n && (e.active.roster = null) } }, integrationJobComplete: (e, t) => { let { payload: n } = t; if ((null === n || void 0 === n ? void 0 : n.jobType) === a.v_.GET_OVERVIEW) { var r; const { overview: t, fieldsAvailable: a, userGroups: l, externalDataSource: s = "", inclusions: c } = (null === n || void 0 === n || null === (r = n.result) || void 0 === r ? void 0 : r.integrationDetails) || {};
                                    e.setup.inclusions = { ...e.setup.inclusions, ...c }; try { if ("object" === typeof c)
                                            for (const t of Object.keys(c)) { var o;
                                                (null === (o = c[t]) || void 0 === o ? void 0 : o.length) > 0 && (e.setup.inclusions.includeAll[t] = !1) } } catch (i) { console.error(i) } e.setup.sourceOverview = { overview: t, fieldsAvailable: a, userGroups: l, externalDataSource: s } } }, "getIntegrationDataOverview/pending": e => { e.setup.sourceOverview = {} }, "updateIntegrationConnection/fulfilled": () => {}, "getIntegrationLogs/fulfilled": (e, t) => { let { payload: { logs: n } } = t;
                                e.logs = n }, "getIntegrationLogs/pending": () => {} }, extraReducers: {} }),
                    { setIntegrationMeta: h, setMetaProperty: m, setSetupColumns: p, setSetupStructuredFields: f, setFieldMatch: v, setCurrentOptions: g, setExclusionRules: y, setInclusionsOfType: b, setStructureField: w, setAutoBuildRule: z, updateExclusionRule: x, resetActiveIntegration: A, updateInclusions: k, updateMetaSetting: S, editChartIntegration: M, integrationJobComplete: E } = u.actions,
                    C = u.reducer }, 4981: (e, t, n) => { "use strict";
                n.d(t, { CF: () => i, RZ: () => o, SV: () => c, _F: () => d, an: () => m, fp: () => r, lo: () => u, n$: () => s, n4: () => l, pL: () => h, v_: () => a }); const r = { EDIT: "edit", NEW: "new" },
                    a = { GET_OVERVIEW: "getOverview", TEST_CONNECTION: "testConnection", CREATE_CHART_INTEGRATION: "createChartIntegration" },
                    o = { SFTP: "sftp", AD: "activedirectory", GOOGLE_DIR: "gsuite", GSHEETS: "gsheets", GDRIVE: "gdrive", SALESFORCE: "salesforce", MSTEAMS: "msteams", OFFICE365: "office365", PEOPLEHR: "peoplehr", OKTA: "okta", JUMPCLOUD: "jumpcloud", DROPBOX: "dropbox", PAYCOR: "paycor", BAMBOOHR: "bamboohr", PAYLOCITY: "paylocity", FIFTEEN_FIVE: "15five", PERSONIO: "personio" },
                    i = {
                        [o.SFTP]: "SFTP", [o.AD]: "Microsoft Entra ID", [o.GOOGLE_DIR]: "Google Workspace", [o.GSHEETS]: "Google Sheets", [o.GDRIVE]: "Google Drive", [o.SALESFORCE]: "Salesforce", [o.MSTEAMS]: "Microsoft Teams", [o.OFFICE365]: "Office 365", [o.PEOPLEHR]: "PeopleHR", [o.OKTA]: "Okta", [o.JUMPCLOUD]: "JumpCloud", [o.DROPBOX]: "Dropbox", [o.PAYCOR]: "Paycor", [o.BAMBOOHR]: "BambooHR", [o.PAYLOCITY]: "Paylocity", [o.FIFTEEN_FIVE]: "15Five", [o.PERSONIO]: "Personio" },
                    l = { INFO: "info", START: "start", START_ROSTER: "start_roster", START_PHOTOS: "start_photos", DELETE: "delete", START_FTP: "start_ftp", DISCONNECT: "disconnect", INVOKE: "invoke", SUBMIT: "submit", LOADING: "loading", DATA_ERROR: "data_error", REQUIRED_COLUMNS: "required_columns", AUTH_ERROR: "auth_error", DETAILED_ERRORS: "detailed_errors", EDIT_META: "edit_meta", START_PEOPLEHR: "start_peoplehr", START_OKTA: "start_okta", START_JUMPCLOUD: "start_jumpcloud", START_PAYCOR: "start_paycor", START_BAMBOOHR: "start_bamboohr", START_PERSONIO: "start_personio" },
                    s = { INVOKE: "invoke", DELETE: "delete", INFO: "info", EDIT: "edit", EDIT_META: "edit_meta" },
