                    c = { DEPARTMENT: "department", ID: "id", PARENT: "parent", COMPANY: "company", TEAM: "team", ROLE_TYPE: "role_type", COLOR: "bgcolor" },
                    d = { FILTERS: "filters", FILTERS_ORGUNITS: "filters.orgunits", FILTERS_GROUPS: "filters.groups", FILTERS_TEAMS: "filters.teams", FILTERS_EMPLOYEE_TYPES: "filters.employeeTypes", FILTERS_OFFICE_LOCATIONS: "filters.officeLocations", EXCLUSIONS: "exclusionRules", AUTO_BUILD: "autoBuild", STRUCTURE_FIELDS: "structuredFields", COLUMNS: "columns", PHOTOS: "photos" },
                    u = {
                        [d.FILTERS]: { next: d.EXCLUSIONS }, [d.EXCLUSIONS]: { next: d.AUTO_BUILD }, [d.AUTO_BUILD]: { next: d.STRUCTURE_FIELDS }, [d.STRUCTURE_FIELDS]: { next: d.COLUMNS }, [d.COLUMNS]: { next: null }, [d.PHOTOS]: { next: null } },
                    h = {
                        [o.AD]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !0, [d.FILTERS_OFFICE_LOCATIONS]: !0, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.FILTERS_GROUPS]: !0, [d.PHOTOS]: !1 }, [o.SFTP]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !0 }, [o.PEOPLEHR]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.JUMPCLOUD]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.GOOGLE_DIR]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !0, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.GSHEETS]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.GDRIVE]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.SALESFORCE]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.MSTEAMS]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !0, [d.PHOTOS]: !1 }, [o.OFFICE365]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !1, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.OKTA]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.DROPBOX]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !1 }, [o.PAYCOR]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.PAYLOCITY]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.BAMBOOHR]: {
                            [d.FILTERS]: !0, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.FIFTEEN_FIVE]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1 }, [o.PERSONIO]: {
                            [d.FILTERS]: !1, [d.STRUCTURE_FIELDS]: !0, [d.COLUMNS]: !0, [d.EXCLUSIONS]: !0, [d.AUTO_BUILD]: !0, [d.FILTERS_EMPLOYEE_TYPES]: !1, [d.FILTERS_OFFICE_LOCATIONS]: !1, [d.FILTERS_ORGUNITS]: !1, [d.FILTERS_TEAMS]: !1, [d.PHOTOS]: !0 } },
                    m = {
                        [o.AD]: ["chart", "photos", "members"], [o.SFTP]: ["chart", "photos", "members"], [o.PEOPLEHR]: ["chart", "photos", "members"], [o.GOOGLE_DIR]: ["chart", "photos", "members"], [o.BAMBOOHR]: ["chart", "photos", "members"], [o.PERSONIO]: ["chart", "photos", "photos"], [o.JUMPCLOUD]: ["chart", "members"], [o.OKTA]: ["chart", "members"], [o.DROPBOX]: ["chart", "members"], [o.PAYCOR]: ["chart", "members"], [o.GSHEETS]: ["chart", "members"], [o.GDRIVE]: ["chart", "members"], [o.SALESFORCE]: ["chart", "members"], [o.MSTEAMS]: ["chart", "members"], [o.OFFICE365]: ["chart", "members"], [o.PAYLOCITY]: ["chart", "members"], [o.FIFTEEN_FIVE]: ["chart", "members"] } }, 3259: (e, t, n) => { "use strict";
                n.d(t, { gP: () => v, p9: () => D, YG: () => b, a4: () => f, Qt: () => p, jA: () => g, kr: () => y, YY: () => q, vv: () => P, tw: () => z, Wq: () => V, fy: () => T, Zy: () => E, aO: () => R, Kq: () => C, dP: () => H, wk: () => L, x7: () => k, n9: () => M, V3: () => F, ch: () => u, D1: () => N, RU: () => A, io: () => m, T3: () => h, OS: () => x, Hd: () => B, LM: () => w, zh: () => U, ao: () => W, E2: () => j, vi: () => I, o: () => _, fg: () => X, OO: () => O, ZA: () => d, yH: () => G }); var r = n(80192),
                    a = n(9922),
                    o = n(59177),
                    i = n(54707);

                function l(e) { return "string" !== typeof e ? "" : e.toLowerCase().trim() } const s = e => { try { if (!e) return []; const t = "string" === typeof e ? JSON.parse(e) : e; return Object.keys(t || {}).filter((e => e && (t || {})[e])).map(l) } catch (t) { return [] } }; var c = n(4981); const d = e => { var t; const n = (null === (t = e.organization) || void 0 === t ? void 0 : t.name) || ""; return (0, o.RP)(n, 30) },
                    u = e => e.integration.setup.filtersAvailable,
                    h = e => e.integration.setup,
                    m = e => e.integration.setup.meta,
                    p = e => t => { const n = e.integration.integrations.active.find((e => e.type === t)); return n || null },
                    f = e => e.integration.active,
                    v = e => { var t, n; return ((null === (t = e.integration) || void 0 === t || null === (n = t.active) || void 0 === n ? void 0 : n.charts) || []).map((t => { const n = e.organization.fields; return 7.1 !== (null === t || void 0 === t ? void 0 : t.createdInVersion) && n ? ((e, t) => { var n; const r = e.params || {},
                                    a = r.options || {},
                                    o = [...r.columnMap] || [],
                                    l = a.integrationOptions || {},
                                    c = l.exclusionRules || {}; let d, u, h; try { d = "string" === typeof c.departments ? JSON.parse(c.departments) : c.departments, u = "string" === typeof c.companies ? JSON.parse(c.companies) : c.companies, h = "string" === typeof c.teams ? JSON.parse(c.teams) : c.teams } catch (M) { d = null, u = null, h = null } const m = 0 === s(d).length,
                                    p = 0 === s(u).length,
                                    f = 0 === s(h).length,
                                    v = function(e) { const t = { id: "", parent: "", department: "", company: "", team: "", orgUnit: "", matrixFunction: "", matrixTeam: "" },
                                            n = ["id", "parent", "department", "company", "team", "orgUnit", "matrixFunction", "matrixTeam"]; for (const r of n) { const n = e.find((e => e.fieldId === r));
                                            n && (t[r] = n.selectedValue || "") } return t }(o),
                                    g = (y = t, o.map((e => { const t = y.find((t => { var n, r, a, o, i, l; const s = (null === e || void 0 === e || null === (n = e.model) || void 0 === n ? void 0 : n.toLowerCase()) || "role",
                                                c = (null === t || void 0 === t || null === (r = t.model) || void 0 === r ? void 0 : r.toLowerCase()) || "",
                                                d = (null === t || void 0 === t || null === (a = t.label) || void 0 === a ? void 0 : a.toLowerCase().replace(/\s/g, "")) || "",
                                                u = (null === t || void 0 === t || null === (o = t.name) || void 0 === o ? void 0 : o.toLowerCase()) || "",
                                                h = null === t || void 0 === t ? void 0 : t.id,
                                                m = (null === e || void 0 === e || null === (i = e.fieldId) || void 0 === i ? void 0 : i.toLowerCase().replace(/\s/g, "")) || "",
                                                p = (null === e || void 0 === e || null === (l = e.label) || void 0 === l ? void 0 : l.toLowerCase().replace(/\s/g, "")) || ""; return "role" === m ? c === s && "name" === u : "description" === m || "details" === m ? c === s && "description" === u : "firstname" === m ? c === s && "firstname" === u : "lastname" === m ? c === s && "lastname" === u : "location" === m ? c === s && "locationaddress" === u : h && h === m || c === s && d === p })); return t ? { ...e, fieldId: t.id } : "role_type" === e.fieldId ? { ...e } : void 0 })).filter((e => e))); var y; let b;
                                v && v.department || !a.departmentColumnName || (v.department = a.departmentColumnName); const w = (null === e || void 0 === e || null === (n = e.connection) || void 0 === n ? void 0 : n.path) || l.requestedURL || ""; var z, x; "gsheets" === e.params.options.integrationType || "gdrive" === e.params.options.integrationType ? (b = (null === (z = l.selectedFile) || void 0 === z ? void 0 : z.id) || "", l.requestedURL && !b && (b = (0, i.r)(l.requestedURL))) : b = (null === (x = l.selectedFile) || void 0 === x ? void 0 : x.id) || ""; const A = { autoBuild: { departments: a.tryAutoBuildDepartmentRoles, assistants: a.autoDetectRoleTypes }, structuredFields: v, inclusions: { includeAll: { departments: m, companies: p, teams: f, orgUnits: !1 }, departments: [], teams: [], companies: [], orgUnits: [] }, exclusionRules: { ...c }, fileLocation: w, selectedFileId: b },
                                    k = { ...a, integrationOptions: A },
                                    S = { ...r, columnMap: g, options: k }; return { ...e, params: S } })({ ...t }, n) : t })) },
                    g = e => { var t, n; return null === (t = e.integration) || void 0 === t || null === (n = t.active) || void 0 === n ? void 0 : n.photo },
                    y = e => { var t, n; return null === (t = e.integration) || void 0 === t || null === (n = t.active) || void 0 === n ? void 0 : n.roster },
                    b = e => e.integration.active,
                    w = (0, r.Mz)(p, (e => t => !!e(t))),
                    z = e => t => { const n = e.integration.integrations.available.find((e => e.id === t)); return n || null },
                    x = e => { var t; return null === (t = e.integration.setup) || void 0 === t ? void 0 : t.sourceOverview },
                    A = e => { var t, n; return !(null === (t = e.integration.setup) || void 0 === t || null === (n = t.sourceOverview) || void 0 === n || !n.overview) },
                    k = e => e.integration.setup.inclusions.includeAll;

                function S(e, t, n) { return "groups" === e ? !!t.find((e => e.id === n)) : t.includes(n) } const M = e => { const t = e.integration.setup || {},
                            n = t.sourceOverview || {},
                            r = t.inclusions || {},
                            a = n.userGroups || {}; return e => { const t = r[e] || {},
                                n = a[e]; return n ? n.map((n => ({ enabled: S(e, t, n.id || n), name: n.displayName || n, value: n.id || n }))) : [] } },
                    E = e => (e.integration.setup || {}).exclusionRules || {},
                    C = e => { var t, n, r; return null === (t = e.integration) || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.sourceOverview) || void 0 === r ? void 0 : r.fieldsAvailable },
                    T = e => e.integration.setup.columns,
                    H = (0, r.Mz)(C, T, (function() { let e = arguments.length > 1 ? arguments[1] : void 0; return (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []).filter((t => e.findIndex((e => e.selectedValue === t.value)) > -1)) })),
                    L = (0, r.Mz)(C, T, ((e, t) => e.filter((e => -1 === t.findIndex((t => t.selectedValue === e.value)))))),
                    I = e => { var t; return null === (t = e.organization) || void 0 === t ? void 0 : t.fields },
                    j = (0, r.Mz)(I, (e => t => (e || []).filter((e => e.type === t)))),
                    V = (0, r.Mz)(T, (e => (e || []).reduce(((e, t) => (e[t.fieldId] = { selectedValue: (null === t || void 0 === t ? void 0 : t.selectedValue) || "", selectedExample: (null === t || void 0 === t ? void 0 : t.selectedExample) || "" }, e)), {}))),
                    O = e => e.integration.setup.structureFields,
                    R = e => { var t, n, r; return (null === (t = e.integration) || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.sourceOverview) || void 0 === r ? void 0 : r.externalDataSource) || "" },
                    P = e => e.integration.setup.autoBuild || { assistants: !1, departments: !0 },
                    D = e => { var t, n; return (null === (t = e.integration.active) || void 0 === t || null === (n = t.details) || void 0 === n ? void 0 : n.name) || "active" },
                    F = e => { const t = e.integration.active || {}; return { notificationSuccessOn: null === t || void 0 === t ? void 0 : t.notificationSuccessOn, notificationFailureOn: null === t || void 0 === t ? void 0 : t.notificationFailureOn, notificationFailureEmails: null === t || void 0 === t ? void 0 : t.notificationFailureEmails, notificationSuccessEmails: null === t || void 0 === t ? void 0 : t.notificationSuccessEmails } },
                    N = e => e.integration.logs || [],
                    _ = e => e.integration.setup.mode,
                    B = e => { var t, n, r; return "chart" === (null === (t = e.integration) || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.meta) || void 0 === r ? void 0 : r.uploadType) },
                    W = e => { var t, n, r; return "members" === (null === (t = e.integration) || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.meta) || void 0 === r ? void 0 : r.uploadType) },
                    U = e => { var t, n, r; return "photos" === (null === (t = e.integration) || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.meta) || void 0 === r ? void 0 : r.uploadType) },
                    q = (0, r.Mz)((e => e.organization.fields), W, f, (function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
                            t = arguments.length > 1 ? arguments[1] : void 0,
                            n = arguments.length > 2 ? arguments[2] : void 0; const r = a.Ay.initialIntegrationFields; return [...r, ...e].filter((e => (!t || "member" === e.model) && (!e.importHidden || "attachment" === e.type))).filter((e => (null === n || void 0 === n ? void 0 : n.type) === c.RZ.SFTP || "photo" !== e.fieldId && "attachment" !== e.type)).reduce(((e, t) => { if (t.id) { const n = (() => { let e = t.name; return "role" === t.model && "name" === t.name ? "role" : "member" === t.model && "description" === t.name ? "details" : e })(); if (r.find((e => e.fieldId === n))) { const r = e.find((e => e.id === n));
                                    r && (r.id = t.id) } else e.push({ isDefault: t.isDefault || !1, name: "role" === t.model && "name" === t.name ? "role" : t.id || t.name, type: (t.type || "string").toLowerCase(), id: t.id || t.fieldId, required: !1, requiredChart: !1, label: t.label || t.name[0] + t.name.toLowerCase().substring(1), model: t.model.toLowerCase(), typeSpecific: !1, chartType: t.chartType || null }) } else e.push({ isDefault: t.isDefault || !1, name: "role" === t.model && "name" === t.name ? "role" : t.fieldId, type: (t.type || "string").toLowerCase(), id: t.id || t.fieldId, required: t.required || !1, requiredChart: t.requiredChart || !1, label: t.label || t.name[0] + t.name.toLowerCase().substring(1), model: t.model.toLowerCase(), typeSpecific: t.typeSpecific || !1, chartType: t.chartType || null }); return e }), []) })),
                    G = (0, r.Mz)(q, W, V, ((e, t, n) => { if (t) { const t = ["firstName", "lastName", "email"].map((t => e.find((e => e.name === t)))); for (let e of t) { var r; if (null !== (r = n[null === e || void 0 === e ? void 0 : e.id]) && void 0 !== r && r.selectedValue) return [] } return t } return e.filter((e => null === e || void 0 === e ? void 0 : e.requiredChart)).filter((e => { var t; return !(null !== (t = n[null === e || void 0 === e ? void 0 : e.id]) && void 0 !== t && t.selectedValue) })) })),
                    K = e => { var t, n; return (null === e || void 0 === e || null === (t = e.integration) || void 0 === t || null === (n = t.integrations) || void 0 === n ? void 0 : n.available) || [] },
                    Z = (0, r.Mz)(K, (e => (null === e || void 0 === e ? void 0 : e.reduce(((e, t) => (e[t.id] = t, e)), {})) || {})),
                    Y = (0, r.Mz)((e => { var t, n; return (null === e || void 0 === e || null === (t = e.integration) || void 0 === t || null === (n = t.integrations) || void 0 === n ? void 0 : n.active) || [] }), (e => (null === e || void 0 === e ? void 0 : e.reduce(((e, t) => (e[t.type] ? e[t.type] = [...e[t.type], t] : e[t.type] = [t], e)), {})) || {})),
                    X = (0, r.Mz)(Y, K, Z, ((e, t, n) => { const r = [],
                            a = [];
                        Object.keys(e).forEach((t => { e[t].length > 0 && n[t] && (r.push({ ...n[t], active: !0 }), a.push(t)) })); for (let o = 0; o < t.length; o++) a.includes(t[o].id) || r.push({ ...t[o], active: !1 }); return r })) }, 30458: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => d, CX: () => c, Gs: () => s, zU: () => l }); var r = n(80907),
                    a = n(47730),
                    o = n(9787); const i = "lambda",
                    l = (0, a.B)({ slice: i, scope: "print" }),
                    s = (0, a.B)({ slice: i, scope: "integration" }),
                    c = (0, o.a)({ slice: i, scope: "token" }),
                    d = (0, r.Z0)({ name: i, initialState: {} }).reducer }, 91383: (e, t, n) => { "use strict";
                n.d(t, { A: () => v }); var r = n(75156),
                    a = n(70318),
                    o = n(96364),
                    i = n(82945),
                    l = n(61531),
                    s = n(40454),
                    c = n(65043),
                    d = n(93865),
                    u = n(18858),
                    h = n(78396),
                    m = n(48853),
                    p = n(70579); const f = "legend",
                    v = e => { let { rule: t, shape: n, onDelete: v, onEdit: g, id: y, index: b, moveItem: w, isPrint: z = !1 } = e; const { userHasMinAccess: x } = (0, m.A)(), A = x(h.td.ADMIN), k = w ? { border: "1px dashed gray", padding: "0.5rem 1rem", marginBottom: ".5rem", backgroundColor: "white", cursor: "move" } : {}, [S, M] = (0, c.useState)(!1), E = (0, c.useRef)(null), [, C] = (0, d.H)({ accept: f, drop(e, t) { var n; if (!E.current) return; const r = e.index,
                                    a = b; if (r === a) return; const o = null === (n = E.current) || void 0 === n ? void 0 : n.getBoundingClientRect(),
                                    i = (o.bottom - o.top) / 2,
                                    l = t.getClientOffset().y - o.top;
                                r < a && l < i || r > a && l > i || (w(r, a), e.index = a) }, collect: e => ({ isOver: e.isOver() }) }), [{ isDragging: T }, H] = (0, u.i)({ item: { type: f, id: y, index: b }, collect: e => ({ isDragging: e.isDragging() }) }), L = T ? 0 : 1; return A && "function" === typeof w && H(C(E)), (0, p.jsx)(l.A, { my: z ? .5 : 1, ref: E, onMouseEnter: () => { M(!0) }, onMouseLeave: () => { M(!1) }, style: { ...k, opacity: L }, children: (0, p.jsxs)(s.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, p.jsx)(l.A, { mr: 1, children: (0, p.jsx)(i.A, { icon: t.icon, color: t.color, variant: t.theme, shape: n, label: t.label, size: z ? 18 : 24 }) }), (0, p.jsx)(s.A, { item: !0, xs: !0, children: (0, p.jsx)(o.A, { variant: z ? "body2" : "h6", children: t.label }) }), S && "function" === typeof v && "function" === typeof g && A && (0, p.jsxs)(l.A, { ml: 1, children: [(0, p.jsx)(a.A, { size: "small", onClick: g(t.id), children: (0, p.jsx)(r.Ay, { icon: "Edit" }) }), (0, p.jsx)(a.A, { size: "small", onClick: v(t.id), children: (0, p.jsx)(r.Ay, { icon: "Delete" }) })] })] }) }, t.id) } }, 19924: (e, t, n) => { "use strict";
                n.d(t, { Co: () => d, Zx: () => o.A, hK: () => i.A, sm: () => u }); var r = n(80192),
                    a = n(7743),
                    o = n(84726),
                    i = n(30752),
                    l = n(10621),
                    s = n(59177); const c = (e, t) => e.model === l.A2.ROLE && t.model === l.A2.MEMBER ? -1 : e.model === l.A2.MEMBER && t.model === l.A2.ROLE ? 1 : 0,
                    d = (0, r.Mz)(a.dS, (e => e.filter((e => ![l.ZE.ATTACHMENT].includes(e.type))).sort(c).map((e => ({ ...e, label: "".concat(e.model === l.A2.MEMBER ? "Person" : (0, s.ZH)(l.A2.ROLE), " ").concat(e.label), value: e }))))),
                    u = e => { var t, n; return null === e || void 0 === e || null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.alias } }, 68586: (e, t, n) => { "use strict";
                n.d(t, { A: () => c });
                n(65043); var r = n(20965),
                    a = n(37294),
                    o = n(96446),
                    i = n(41020),
                    l = n(75156),
                    s = n(70579); const c = e => { let { swatchElement: t, popperElement: n, swatchElementWrapperProps: c = { width: (0, r.A)(35), height: (0, r.A)(35), maxWidth: (0, r.A)(35), maxHeight: (0, r.A)(35), borderRadius: (0, r.A)(2), border: "1px solid ".concat(a.Qs.Neutrals[400]), padding: (0, r.A)(4) }, handleToggleSwatch: d, popperOpen: u = !1, popperAnchorEl: h } = e; return (0, s.jsxs)(s.Fragment, { children: [(0, s.jsx)(o.A, { display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", onClick: d, ...c, children: (0, s.jsxs)(o.A, { width: (0, r.A)(26), height: (0, r.A)(26), borderRadius: (0, r.A)(2), border: "1px solid ".concat(a.Qs.Neutrals[500]), sx: { cursor: "pointer" }, position: "relative", children: [t, (0, s.jsx)(o.A, { position: "absolute", bottom: "-1px", right: "-1px", display: "flex", alignItems: "flex-end", justifyContent: "flex-end", children: (0, s.jsx)(o.A, { borderLeft: "1px solid ".concat(a.Qs.Neutrals[500]), borderTop: "1px solid ".concat(a.Qs.Neutrals[500]), bgcolor: a.Qs.Neutrals[0], borderRadius: "".concat((0, r.A)(2), " 0 0 0"), width: (0, r.A)(8), height: (0, r.A)(8), display: "flex", alignItems: "flex-start", justifyContent: "flex-start", paddingLeft: (0, r.A)(2), children: (0, s.jsx)(l.gF, { icon: u ? "CaretUp" : "CaretDown", size: "xxs" }) }, "color-picker-caret-".concat(u)) })] }) }), (0, s.jsx)(i.Ay, { open: u, onClose: d, anchorEl: h, anchorOrigin: { vertical: "bottom", horizontal: "left" }, children: n })] }) } }, 79091: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => p, Qs: () => c, cl: () => m, l: () => h, tO: () => l, wL: () => o, wt: () => s }); var r = n(80907); const a = "legend",
                    o = (0, n(9787).a)({ slice: a, scope: "legends" }),
                    i = (0, r.Z0)({ name: a, initialState: { open: !1, props: {}, filterBadges: [] }, reducers: { openLegend: (e, t) => { let { payload: n } = t;
                                e.open = !0, e.props = n || {} }, closeLegend: e => { e.open = !1, e.props = {} }, addFilterBadge: (e, t) => { let { payload: n } = t;
                                e.filterBadges.push(n) }, removeFilterBadge: (e, t) => { let { payload: n } = t;
                                e.filterBadges = e.filterBadges.filter((e => e !== n)) }, setFilterBadges: (e, t) => { let { payload: n } = t;
                                e.filterBadges = n || [] }, clearFilterBadges: e => { e.filterBadges = [] } }, extraReducers: {} }),
                    l = e => e.legend.open,
                    { openLegend: s, closeLegend: c, addFilterBadge: d, removeFilterBadge: u, setFilterBadges: h, clearFilterBadges: m } = i.actions,
                    p = i.reducer }, 83340: (e, t, n) => { "use strict";
                n.d(t, { $j: () => u, AV: () => i, Ay: () => m, Hj: () => h, T7: () => d, TL: () => c, hW: () => l }); var r = n(80907),
                    a = n(47730); const o = "license",
                    i = (0, n(9787).a)({ slice: o, scope: "license" }),
                    l = (0, a.B)({ slice: o, scope: "license" }),
                    s = (0, r.Z0)({ name: o, initialState: {}, reducers: { "getBilling/fulfilled": (e, t) => ({ ...e, ...t.payload }), "getCustomer/fulfilled": (e, t) => ({ ...e, ...t.payload }), "updateCustomer/fulfilled": (e, t) => ({ ...e, ...t.payload }), "cancelPlan/fulfilled": (e, t) => ({ ...e, ...t.payload }), clearBilling: e => (({}, {})) } }),
                    c = e => e.license.billingHistory ? e.license.billingHistory : {},
                    d = e => e.license.cards,
                    u = e => e.license.stripe_customer,
                    { clearBilling: h } = s.actions,
                    m = s.reducer }, 69645: (e, t, n) => { "use strict";
                n.d(t, { A: () => o, h: () => a }); const r = (0, n(80907).Z0)({ name: "localStorageStore", initialState: {}, reducers: { setLocalStorageItem: (e, t) => { let { payload: { key: n, value: r } } = t; return { ...e, [n]: r } } } }),
                    { setLocalStorageItem: a } = r.actions,
                    o = r.reducer }, 59269: (e, t, n) => { "use strict";
                n.d(t, { _: () => r }); const r = e => e.localStorageReducer }, 4524: (e, t, n) => { "use strict";
                n.d(t, { A: () => C }); var r = n(65043),
                    a = n(96364),
                    o = n(72835),
                    i = n(40454),
                    l = n(99229),
                    s = n(61531),
                    c = n(86255),
                    d = n(87958),
                    u = n(51481),
                    h = n(73083),
                    m = n(61),
                    p = n(14556),
                    f = n(3879),
                    v = n(38872),
                    g = n(80539),
                    y = n(84),
                    b = n(24115); const w = e => e.organization.name; var z = n(54229),
                    x = n(75156),
                    A = n(34859),
                    k = n(492),
                    S = n(20495),
                    M = n(70579); const E = (0, A.A)((e => ({ root: { flexGrow: 1 }, paper: { height: 160, display: "flex", justifyContent: "center", alignItems: "center", color: e.palette.text.secondary, position: "relative", margin: e.spacing(1) } }))),
                    C = e => { let { member: t, organization: n, updatePhoto: A, updatePhotoSearch: C, onLinkedInPhotoSearch: T, type: H, allowLinkedInSearch: L = !0, square: I = !0, mode: j, entity: V = "person", photoMaxWidth: O = 200, photoMaxHeight: R = 200 } = e; const { eventDebounce: P } = (0, c.A)(), [D, F] = (0, r.useState)("upload" === j ? "google" : "linkedin"), [N, _] = (0, r.useState)(t || "".concat(n, " logo")), [B, W] = (0, r.useState)([]), [U, q] = (0, r.useState)(!1), G = (0, r.useRef)(), K = (0, p.wA)(), Z = E(), Y = (0, p.d4)(w), { openDialog: X } = (0, y.A)("cropper"), $ = async () => { q(!0); const { error: e, payload: t } = await K(m.JP.findPeoplePublic({ query: N, type: D, entity: V }));
                            q(!1); let n = [];
                            e || (t.forEach((e => { let t = "linkedin" === D ? { person: { ...e.person, lastName: e.person.lastName.split(" - ")[0], title: e.person.lastName.split(" - ")[1], position: e.person.lastName.split(" - ")[2], linkedIn: e.person.linkedIn } } : e;
                                n.push(t) })), W(n)) }, Q = P((async e => { "" !== e.target.value.trim() ? _(e.target.value.trim()) : (_(""), W([])) }), 1e3), J = e => () => { var t;
                            X({ image: e.person.photo, onUpload: "search" === j ? (t = e.person.linkedIn, e => { C(e, t) }) : A, type: H, square: I, imageData: e, onLinkedInPhotoSearch: T, maxWidth: O, maxHeight: R }) }; return (0, r.useEffect)((() => { "" !== N && $() }), [D, N]), (0, M.jsxs)(i.A, { container: !0, justifyContent: "center", alignItems: "flex-start", spacing: 2, children: [L && (0, M.jsx)(i.A, { item: !0, xs: 2, children: (0, M.jsxs)(u.A, { "aria-label": "type", name: "type", value: D, onChange: e => { F(e.target.value) }, children: ["upload" === j && (0, M.jsx)(h.A, { value: "google", control: (0, M.jsx)(d.A, {}), label: "Google" }), (0, M.jsx)(h.A, { value: "linkedin", control: (0, M.jsx)(d.A, {}), label: "LinkedIn" })] }) }), (0, M.jsx)(i.A, { item: !0, xs: L ? 7 : 9, children: (0, M.jsx)(z.A, { size: "small", fullWidth: !0, defaultValue: N, inputRef: G, placeholder: "Search Photo", onChange: Q, InputProps: { endAdornment: (0, M.jsx)(l.A, { position: "end", children: (0, M.jsx)(x.Ay, { icon: "Search" }) }) } }) }), (0, M.jsx)(i.A, { item: !0, xs: 3, children: (0, M.jsx)(o.A, { variant: "contained", color: "primary", fullWidth: !0, onClick: $, children: "Search" }) }), t && (0, M.jsxs)(a.A, { variant: "body1", gutterBottom: !0, component: "a", onClick: () => { G.current.value = "".concat(t, " ").concat(Y), _("".concat(t, " ").concat(Y)) }, children: ["Search instead for ".concat(t, " ").concat(Y), "\xa0"] }), "linkedin" === D && (0, M.jsx)(a.A, { variant: "body1", gutterBottom: !0, children: "Note: Only public LinkedIn photos will show" }), (0, M.jsx)(f.A, { children: (0, M.jsx)(b.A, { loading: U, children: (0, M.jsx)(s.A, { clone: !0, style: { overflow: "auto" }, children: (0, M.jsx)(i.A, { container: !0, spacing: 2, justifyContent: "center", children: B.map(((e, t) => (0, M.jsxs)(v.A, { onClick: J(e), linkedin: "linkedin" === D, children: ["person" !== V ? (0, M.jsx)(S.A, { className: Z.paper, elevation: 0, children: (0, M.jsx)("img", { className: k.A.searchImage, src: e.person.photo, alt: e.title }) }) : (0, M.jsx)(g.A, { width: 100, height: 100, src: e.person.photo }), "linkedin" === D ? (0, M.jsxs)(M.Fragment, { children: [(0, M.jsxs)(a.A, { variant: "body1", children: [e.person.firstName, " ", e.person.lastName] }), (0, M.jsx)(a.A, { variant: "body2", children: e.person.title }), (0, M.jsx)(a.A, { variant: "body2", children: e.person.position })] }) : (0, M.jsx)(M.Fragment, { children: (0, M.jsx)(a.A, { variant: "body1", children: N }) })] }, t))) }) }) }) })] }) } }, 78780: (e, t, n) => { "use strict";
                n.d(t, { A: () => S }); var r = n(65043),
                    a = n(96364),
                    o = n(1997),
                    i = n(80539),
                    l = n(40454),
                    s = n(61531),
                    c = n(66795),
                    d = n(72835),
                    u = n(84),
                    h = n(84866),
                    m = n(91693),
                    p = n(70318),
                    f = n(75156),
                    v = n(34725),
                    g = n(72416),
                    y = n(46662),
                    b = n(27266),
                    w = n(14106),
                    z = n(96942),
                    x = n(53492),
                    A = n(3523),
                    k = n(70579); const S = e => { let { updatePhoto: t, securePhoto: n, deletePhoto: S, initialAvatarBackgroundColor: M, updateBackground: E, type: C, updateAvatar: T, allowAvatars: H = !0, square: L = !0, member: I, photoMaxWidth: j = 200, photoMaxHeight: V = 200, useImageWithCfPolicy: O = !0 } = e; const R = (0, r.useRef)(),
                        { openDialog: P } = (0, u.A)("cropper"),
                        { openDialog: D } = (0, u.A)("changeAvatar"),
                        { openDialog: F } = (0, u.A)("errorDialog"),
                        N = [{ id: "silhouettes", image: w.A, label: "People" }, { id: "lego", image: b.A, label: "Characters" }, { id: "animals", image: v.A, label: "Animals" }, { id: "flags", image: y.A, label: "Flags" }, { id: "emoticons", image: g.A, label: "Emojis" }, { id: "search", image: "search", isSearch: !0 }],
                        _ = e => () => { D({ onSelect: T, category: e }) },
                        B = () => {},
                        W = () => { D({ onSelect: T }) }; return (0, k.jsxs)(k.Fragment, { children: [(0, k.jsxs)(l.A, { container: !0, justifyContent: "space-between", spacing: 2, children: [(0, k.jsx)(l.A, { item: !0, xs: H ? 6 : 12, children: (0, k.jsxs)(o.A, { children: [(0, k.jsx)("input", { type: "file", name: "fileUploader", ref: R, id: "fileUploader", style: { display: "none" }, accept: "image/*", onChange: e => { if (e.target.files && e.target.files.length > 0) { if (0 !== e.target.files[0].type.indexOf("image/")) return void F({ title: "Invalid File Type", message: "Only image files are supported." }); const n = new FileReader;
                                                n.addEventListener("load", (() => { n.result ? P({ image: n.result, onUpload: t, type: C, square: L, maxWidth: j }) : F({ title: "File Upload Error", message: "Something went wrong." }) })), n.readAsDataURL(e.target.files[0]) } } }), (0, k.jsxs)(l.A, { container: !0, direction: "column", alignItems: "center", justifyContent: "space-between", className: "uploadGrid", children: [(0, k.jsxs)(l.A, { container: !0, direction: "row", alignItems: "center", justifyContent: "center", className: "uploadGrid", spacing: 2, children: [(0, k.jsx)(l.A, { item: !0, children: "photo" !== C && n ? (0, k.jsx)(x.A, { width: 100, src: O ? (0, A.K7)(n) : n }) : (0, k.jsx)(i.A, { width: 100, height: 100, src: (0, A.K7)(n), name: I, overrideColor: M }) }), (0, k.jsx)(l.A, { children: !n && "photo" === C && (0, k.jsx)(z.Ay, { handleChangeAvatarBackground: e => { E(e) }, initialColor: M, src: n }) })] }), (0, k.jsx)(s.A, { my: 2, children: (0, k.jsxs)(c.A, { maxWidth: "sm", children: [(0, k.jsx)(d.A, { variant: "outlined", color: "primary", onClick: () => { R.current.click() }, children: "Upload Photo" }), (0, k.jsx)(p.A, { color: "primary", onClick: S, disabled: "" === n, children: (0, k.jsx)(f.Ay, { icon: "Delete" }) })] }) }), (0, k.jsx)(a.A, { variant: "body1", children: "Max file size : 2MB" })] })] }) }), H && (0, k.jsx)(l.A, { item: !0, xs: 6, children: (0, k.jsx)(o.A, { children: (0, k.jsx)(l.A, { container: !0, spacing: 2, children: N.map((e => (0, k.jsx)(l.A, { item: !0, xs: 4, children: (0, k.jsx)(m.A, { id: e.id, image: e.image, handleClick: _, handleEdit: B, handleSearch: W, customImageUrl: null, isNew: e.isNew, isSearch: e.isSearch, label: e.label }) }, e.id))) }) }) })] }), (0, k.jsxs)(l.A, { container: !0, direction: "column", alignItems: "center", justifyContent: "space-between", className: "uploadGrid", children: [(0, k.jsx)(s.A, { my: 3, children: (0, k.jsx)(a.A, { variant: "h5", children: " - OR -" }) }), (0, k.jsx)(h.A, { fullWidth: !0, placeholder: "https://", label: "Type in image URL", onBlur: e => { "" !== e.target.value.trim() && P({ image: e.target.value.trim(), onUpload: t, square: L, maxWidth: j, maxHeight: V }) } })] })] }) } }, 55345: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r = n(68625),
                    a = n(71745),
                    o = (n(65043), n(70579)); const i = (0, a.A)({ root: { color: "#5C2DBF", height: 16, margin: 0 }, thumb: { height: 24, width: 24, border: "1px solid #ffffff", background: "#5C2DBF", borderRadius: 20, marginTop: -5, marginLeft: -12, "&:focus, &:hover, &$active": { boxShadow: "inherit" }, cursor: "grab" }, active: { borderRadius: 50, background: "#e6def7", border: "2px solid #ffffff" }, valueLabel: { left: "calc(-50% + 4px)" }, markLabel: { display: "none" }, mark: { borderRadius: 0, display: "none" }, track: { height: 16, border: "1px solid #ffffff", background: "#5C2DBF", borderRadius: 16 }, rail: { height: 16, borderRadius: 16 } })(r.A); const l = function(e) { let { marks: t, defaultValue: n, handleChange: r } = e; return (0, o.jsx)(i, { defaultValue: n, onChange: r, step: null, valueLabelDisplay: "off", marks: t }) } }, 79825: (e, t, n) => { "use strict";
                n.d(t, { CQ: () => y, rP: () => P, hJ: () => N, dN: () => f, n4: () => W }); var r, a, o, i, l, s, c, d, u = n(57528),
                    h = n(96364),
                    m = n(29829),
                    p = n(72119);
                (0, p.Ay)(h.A)(r || (r = (0, u.A)(["\n  font-size: 30px;\n  font-weight: 600;\n  color: #000000;\n"]))), (0, p.Ay)(h.A)(a || (a = (0, u.A)(["\n  font-size: 24px;\n  font-weight: 500;\n  color: #000000;\n"]))), (0, p.Ay)(m.A)(o || (o = (0, u.A)(["\n  color: #666666;\n  font-size: 16px;\n"]))), (0, p.Ay)(h.A).attrs({ align: "center" })(i || (i = (0, u.A)(["\n  font-weight: 500;\n  font-size: 26px;\n"]))), (0, p.Ay)(h.A)(l || (l = (0, u.A)(["\n  font-size: 18px;\n  margin-top: 16px;\n  margin-bottom: 16px;\n"]))), (0, p.Ay)(h.A)(s || (s = (0, u.A)(["\n  font-size: 16px;\n  color: #444444;\n  font-weight: 600;\n"]))), (0, p.Ay)(h.A)(c || (c = (0, u.A)(["\n  font-size: 12px;\n  color: #999999;\n  font-weight: 400;\n"]))); const f = (0, p.Ay)(h.A)(d || (d = (0, u.A)(["\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 24px;\n"]))); var v, g = n(61531); const y = (0, p.Ay)(g.A).attrs({ display: "flex", justifyContent: "center", p: 2, flexDirection: "column", alignItems: "center" })(v || (v = (0, u.A)([""]))); var b, w, z = n(75156),
                    x = n(749),
                    A = (n(65043), n(70579)); const k = (0, p.Ay)(h.A)(b || (b = (0, u.A)(["\n  font-size: 22px;\n  color: #444;\n"])));
                (0, p.Ay)(x.A).attrs({ elevation: 2 })(w || (w = (0, u.A)(["\n  ", "\n"])), (e => { let { theme: t, width: n } = e; return "\n    padding: 12px 18px;\n    grid-gap: 24px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: flex-start;\n    position:relative;\n    cursor:pointer;\n    width: ".concat(n || 300, "px;\n    color:").concat(t.palette.primary.main, ";\n    &:hover{\n      color:").concat(t.palette.secondary.main, ";\n    }\n    ").concat(z.Ay, " {\n      width: 30px; \n    }\n    &:hover {\n      box-shadow: 0px 3px 4px -2px rgb(0 0 0 / 20%), 0px 2px 5px 0px rgb(0 0 0 / 14%), 0px 1px 8px 0px rgb(0 0 0 / 12%);\n    }\n    &:hover ").concat(k, " {\n      font-weight: 500;\n      color: #000;\n    }\n") })); var S, M, E, C, T, H, L, I, j, V;
                p.Ay.a(S || (S = (0, u.A)(["\n  text-decoration: underline;\n"]))), (0, p.Ay)(g.A)(M || (M = (0, u.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    border-top: solid 8px ".concat(t.palette.primary.light, ";\n  ") })), (0, p.Ay)(g.A)(E || (E = (0, u.A)(["\n  width: 12px;\n  height: 12px;\n  border-radius: 6px;\n  cursor: pointer;\n  ", "\n"])), (e => { let { theme: t, active: n } = e; return "\n    background: ".concat(n ? "".concat(t.palette.primary.light) : "rgba(0, 0, 0, 0.3)", ";\n  ") })), (0, p.Ay)(g.A)(C || (C = (0, u.A)(["\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  flex: 1;\n  grid-gap: 24px;\n"]))), (0, p.Ay)(h.A)(T || (T = (0, u.A)(["\n  font-size: 20px;\n  color: #000000;\n"]))), (0, p.Ay)(h.A)(H || (H = (0, u.A)(["\n  font-size: 16px;\n  color: #444444;\n  font-weight: 300;\n  line-height: 1.6em;\n"]))), (0, p.Ay)(g.A)(L || (L = (0, u.A)(["\n  position: absolute;\n  bottom: 0;\n  margin: 32px;\n  left: 0;\n  z-index: 1;\n  background: #ffffff;\n  border: solid 2px #666666;\n  border-radius: 4px;\n"]))), (0, p.Ay)(g.A)(I || (I = (0, u.A)(["\n  position: absolute;\n  bottom: 0;\n  margin: 32px;\n  right: 0;\n  z-index: 1;\n  background: #ffffff;\n  border: solid 2px #666666;\n  border-radius: 4px;\n"]))), (0, p.Ay)(g.A)(j || (j = (0, u.A)(["\n  position: fixed;\n  display: flex;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  z-index: 1;\n"]))), (0, p.Ay)(g.A)(V || (V = (0, u.A)(["\n  width: 100%;\n  color: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.4);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  &:hover {\n    background: rgba(0, 0, 0, 0.1);\n  }\n"]))); var O = n(53492),
                    R = n(28623); const P = () => (0, A.jsx)(g.A, { pt: 2, pb: 2, display: "flex", justifyContent: "center", bgcolor: "#ffffff", children: (0, A.jsx)(O.A, { height: 64, src: R }) }); var D;
                (0, p.Ay)(g.A)(D || (D = (0, u.A)([""]))); var F; const N = (0, p.Ay)(g.A)(F || (F = (0, u.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    background: ".concat(t.palette.primary.main, ";\n  ") })); var _ = n(30105); const B = n.p + "static/media/welcomeTour.e3b53881e7aedae93e839bdacbfdf064.svg",
                    W = e => { let { handleComplete: t } = e; return (0, A.jsxs)(g.A, { width: 600, borderRadius: 8, my: 4, display: "flex", flexDirection: "column", alignItems: "center", children: [(0, A.jsx)(g.A, { mt: 3, children: (0, A.jsx)(h.A, { variant: "h1", align: "center", children: "You're all set!" }) }), (0, A.jsx)(O.A, { src: B, width: 500 }), (0, A.jsx)(g.A, { mt: 4, mb: 3, children: (0, A.jsx)(h.A, { variant: "h3", align: "center", children: "It's time to start building your chart" }) }), (0, A.jsxs)(g.A, { sx: { width: 435 }, display: "flex", justifyContent: "space-around", mb: 4, mt: 2, children: [(0, A.jsx)(_.A, { variant: "outlined", color: "primary", onClick: () => { window.open("https://www.organimi.com/the-huddle-tv/", "_blank") }, children: "View Video Tutorials >" }), (0, A.jsx)(_.A, { variant: "contained", color: "primary", onClick: t, children: "Go to Organimi >" })] })] }) } }, 75308: (e, t, n) => { "use strict";
                n.d(t, { $: () => r }); const r = e => e.user }, 53462: (e, t, n) => { "use strict";
                n.d(t, { A: () => l });
                n(65043); var r = n(54229),
                    a = n(99229),
                    o = n(75156),
                    i = n(70579); const l = e => { let { handleSearch: t, placeholder: n, defaultValue: l, handleClear: s, query: c } = e; const d = { startAdornment: (0, i.jsx)(a.A, { position: "start", children: (0, i.jsx)(o.Ay, { icon: "Search" }) }) }; return c && (d.endAdornment = (0, i.jsx)(a.A, { position: "end", onClick: s || void 0, children: (0, i.jsx)(o.Ay, { icon: "Close", style: { cursor: "pointer" } }) })), (0, i.jsx)(r.A, { size: "small", fullWidth: !0, value: c, defaultValue: l, placeholder: n, onChange: t, InputProps: d }) } }, 43005: (e, t, n) => { "use strict";
                n.d(t, { A: () => K }); var r, a = n(57528),
                    o = n(65043),
                    i = n(59691),
                    l = n(40454),
                    s = n(55357),
                    c = n(49768),
                    d = n(61531),
                    u = n(9579),
                    h = n(38325),
                    m = n(14370),
                    p = n(16853),
                    f = n(64759),
                    v = n(94363),
                    g = n(77887),
                    y = n(695),
                    b = n(72835),
                    w = n(78300),
                    z = n(86852),
                    x = n(53462),
                    A = n(97105),
                    k = n(49092),
                    S = n(96364),
                    M = n(84),
                    E = n(64418),
                    C = n(14556),
                    T = n(31777),
                    H = n(67264),
                    L = n(84866),
                    I = n(43331),
                    j = n(24115),
                    V = n(67479),
                    O = n(42006),
                    R = n(75156),
                    P = n(66588),
                    D = n(72119),
                    F = n(86597),
                    N = n(70579); const _ = (0, D.Ay)(L.A)(r || (r = (0, a.A)(["\n  margin-left: 16px;\n  .MuiSelect-root {\n    padding-right: 32px;\n    padding-top: 8px;\n    padding-bottom: 8px;\n  }\n"]))),
                    B = [{ name: "name", label: "Name", align: "left", sortable: !0 }, { name: "email", label: "Email", align: "left", sortable: !0 }, { name: "typeDescription", label: "Role", align: "left", sortable: !0 }, { name: "charts", label: "Charts", align: "left", sortable: !0, getLabel: function(e) { return e.permissionLevel <= 1 ? "All" : e[this.name].length } }];

                function W(e, t, n) { return t[n] < e[n] ? -1 : t[n] > e[n] ? 1 : 0 }

                function U(e, t) { return "desc" === e ? (e, n) => W(e, n, t) : (e, n) => -W(e, n, t) }

                function q(e, t) { const n = e.map(((e, t) => [e, t])); return n.sort(((e, n) => { const r = t(e[0], n[0]); return 0 !== r ? r : e[1] - n[1] })), n.map((e => e[0])) }

                function G(e) { const { onSelectAllClick: t, order: n, orderBy: r, numSelected: a, rowCount: o, onRequestSort: i } = e; return (0, N.jsx)(f.A, { children: (0, N.jsxs)(y.A, { children: [(0, N.jsx)(g.A, { padding: "checkbox", children: (0, N.jsx)(p.A, { indeterminate: a > 0 && a < o, checked: o > 0 && a === o, onChange: t, inputProps: { "aria-label": "select all desserts" } }) }), B.map((e => { return (0, N.jsx)(g.A, { align: e.numeric ? "right" : "left", sortDirection: r === e.name && n, children: (0, N.jsx)(m.A, { active: r === e.name, direction: r === e.name ? n : "asc", onClick: (t = e.name, e => { i(e, t) }), children: e.label }) }, e.name); var t }))] }) }) } const K = e => { let { chartId: t, toolbarOverrideGroup: n } = e; const { t: r } = (0, k.B)(), [a, m] = (0, o.useState)(!0), { confirmAction: f } = (0, E.A)(), { openDialog: L } = (0, M.A)("userCard"), { openDialog: D } = (0, M.A)("alert"), W = (0, C.wA)(), [K, Z] = (0, o.useState)(null), [Y, X] = (0, o.useState)(""), [$, Q] = (0, o.useState)(null), [J, ee] = (0, o.useState)(t || "All"), [te, ne] = (0, o.useState)("asc"), [re, ae] = (0, o.useState)("calories"), [oe, ie] = (0, o.useState)([]), [le, se] = (0, o.useState)(0), [ce, de] = (0, o.useState)(25), ue = (0, C.d4)(I.Ot), he = (0, C.d4)(V.AZ), me = (0, C.d4)(O.VW), pe = (0, F.A)(), fe = null === pe || void 0 === pe ? void 0 : pe.id, ve = "email", { show: ge } = (0, P.A)();
                    (0, o.useEffect)((() => { let e = !0; return (async () => { m(!0), await W(A.Bf.getOrganizationUsers({ orgId: fe })), m(!1) })(), () => { e = !1 } }), []); const ye = (0, o.useMemo)((() => { let e = []; var t; return K ? (se(0), e = he.filter((e => e.type === K))) : e = he, "All" !== J && (se(0), e = e.filter((e => { const t = e.charts.map((e => null === e || void 0 === e ? void 0 : e.id)).includes(J),
                                    n = e.organizations.map((e => null === e || void 0 === e ? void 0 : e.id)).includes(fe); return t || n }))), "" !== Y && (se(0), t = Y, e = e.filter((e => Object.keys(e).some((n => "string" === typeof e[n] && e[n].toLowerCase().includes(t.toLowerCase())))))), e }), [K, he, Y, J]),
                        be = ye.length,
                        we = () => { Q(null) },
                        ze = (e, t) => { se(t) },
                        xe = () => { ie([]) },
                        Ae = () => { let e = Math.floor(he.length / ce);
                            se(e) },
                        ke = e => async t => { if (t && t.stopPropagation(), e === me.username) D({ title: "Failed to edit", message: "Cannot edit yourself (".concat(me.firstName, " ").concat(me.lastName, ") from the user management.") });
                            else { const { error: t } = await W(A.Bf.getUserWithPerms({ orgId: fe, email: e }));
                                t || L({ mode: "edit", email: e, callback: () => {} }) } }, { handleButtonActionClick: Se } = (0, H.A)({ delete: () => { oe.includes(me.username) ? D({ title: "Failed to delete", message: "Cannot delete yourself (".concat(me.firstName, " ").concat(me.lastName, ") from the user management.") }) : f({ execFunc: async () => { const { payload: e, error: t } = await W(A.Bf.deleteUsers({ orgId: fe, users: oe.map((e => ({
                                                [ve]: e }))) })); if (!t) {
                                            (oe || []).length !== ((null === e || void 0 === e ? void 0 : e.users) || []).filter((e => e)).length && ge("Could not delete all users requested", "warning"), xe() } }, title: "Confirm delete of ".concat(oe.length, " users(s)"), message: "This action is irreversible" }) }, edit: () => { let e = oe[0];
                                e === me.username ? D({ title: "Failed to edit", message: "Cannot edit yourself (".concat(me.firstName, " ").concat(me.lastName, ") from the user management.") }) : (ke(e).call(), xe()) }, filter: e => { Q(e.currentTarget) } }), Me = e => { Z(e), we() }; return (0, o.useEffect)((() => { ze(0, 0) }), []), (0, N.jsxs)(j.A, { loading: a, children: [(0, N.jsx)("div", { style: { borderBottom: "solid 1px #ddd", padding: 16 }, children: (0, N.jsxs)(l.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, N.jsx)(l.A, { item: !0, children: (0, N.jsxs)(l.A, { container: !0, justifyContent: "flex-start", alignItems: "center", children: [(0, N.jsx)(T.A, { buttonGroup: n || "organization_userlist", selectedCount: oe.length, handleActionClick: Se }), (0, N.jsxs)(_, { select: !0, size: "small", placeholder: "Filter Chart", value: J, onChange: e => { ee(e.target.value) }, disabled: !!t, children: [(0, N.jsx)(s.A, { value: "All", children: "Filter by Chart" }), ue.map((e => (0, N.jsx)(s.A, { value: e.id, children: e.name }, e.id)))] }), (0, N.jsxs)(c.A, { id: "actionsr-menu-users", anchorEl: $, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, open: Boolean($), onClose: we, children: [(0, N.jsx)(s.A, { value: null, onClick: () => Me(null), children: r("Text.Permissions.All") }), (0, N.jsx)(s.A, { value: "owners", onClick: () => Me("owner"), children: r("Text.Permissions.Owners") }), (0, N.jsx)(s.A, { value: "admins", onClick: () => Me("admin"), children: r("Text.Permissions.Administrators") }), (0, N.jsx)(s.A, { value: "editors", onClick: () => Me("editor"), children: r("Text.Permissions.Editors") }), (0, N.jsx)(s.A, { value: "viewers", onClick: () => Me("viewer"), children: r("Text.Permissions.Viewers") })] })] }) }), (0, N.jsx)(l.A, { item: !0, children: (0, N.jsxs)(l.A, { container: !0, alignItems: "center", children: ["chart_userlist" !== n && (0, N.jsx)(z.A, { spacing: { right: 2 }, children: (0, N.jsx)(x.A, { handleSearch: e => { var t; let n = null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value;
                                                    X(n) }, handleClear: () => { X("") }, query: Y, placeholder: r("Common.Labels.SearchUsers") }) }), (0, N.jsx)(b.A, { variant: "contained", color: "primary", onClick: () => { L({ mode: "add", callback: Ae }) }, children: r("Common.Buttons.NewAdmin") })] }) })] }) }), (0, N.jsx)(d.A, { flex: 1, overflow: "auto", height: "100%", children: (0, N.jsxs)(v.A, { "aria-label": "dashboard", stickyHeader: !0, children: [(0, N.jsx)(G, { numSelected: oe.length, order: te, orderBy: re, onSelectAllClick: e => { if (e.target.checked) { const e = he.map((e => e.email));
                                            ie(e) } else ie([]) }, onRequestSort: (e, t) => { ne(re === t && "asc" === te ? "desc" : "asc"), ae(t) }, rowCount: he.length }), (0, N.jsx)(i.A, { children: q(ye, U(te, re)).slice(le * ce, le * ce + ce).map((e => { const t = (n = e.email, -1 !== oe.indexOf(n)); var n; return (0, N.jsxs)(y.A, { hover: !0, onClick: ke(e.email), role: "checkbox", "aria-checked": t, tabIndex: -1, selected: t, children: [(0, N.jsx)(g.A, { padding: "checkbox", children: (0, N.jsx)(p.A, { checked: t, onClick: t => ((e, t) => { e && e.stopPropagation(); const n = oe.indexOf(t); let r = []; - 1 === n ? r = r.concat(oe, t) : 0 === n ? r = r.concat(oe.slice(1)) : n === oe.length - 1 ? r = r.concat(oe.slice(0, -1)) : n > 0 && (r = r.concat(oe.slice(0, n), oe.slice(n + 1))), ie(r) })(t, e.email), inputProps: { "aria-label": "Select user ".concat(e.email) } }) }), B.map((t => (0, N.jsx)(g.A, { scope: "row", children: (0, N.jsxs)(d.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: ["function" === typeof t.getLabel ? t.getLabel(e) : e[t.name], e.pending && "email" === t.name && (0, N.jsx)(u.Ay, { title: "Email Invitation Sent", placement: "top", arrow: !0, children: (0, N.jsx)("span", { children: (0, N.jsx)(R.Ay, { icon: "Pending", size: "x2" }) }) })] }) }, "organization-userlist-bodyrow-".concat(e.email, "-cell-").concat(t.name))))] }, e.email) })) })] }) }), be > ce && (0, N.jsx)(d.A, { position: "sticky", left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", borderTop: "solid 1px #ddd", children: (0, N.jsx)(h.A, { rowsPerPageOptions: [25, 50, 100, 250], component: "div", count: be, rowsPerPage: ce, page: le, backIconButtonProps: { "aria-label": r("Common.Tables.backIconButtonText") }, nextIconButtonProps: { "aria-label": r("Common.Tables.nextIconButtonText") }, onPageChange: ze, onRowsPerPageChange: e => { de(parseInt(e.target.value, 10)), se(0) }, labelRowsPerPage: r("Common.Tables.labelRowsPerPage"), backIconButtonText: r("Common.Tables.backIconButtonText"), nextIconButtonText: r("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: t, to: n, count: a } = e; return "".concat(t, "-").concat(n, " ").concat(r("Common.Tables.of"), " ").concat(a) } }) }), !be && (0, N.jsxs)(l.A, { container: !0, justifyContent: "center", direction: "column", alignItems: "center", children: [(0, N.jsx)(z.A, { variant: "div", spacing: { vertical: 2 }, children: (0, N.jsx)(w.A, { width: 200, height: 200 }) }), (0, N.jsx)(S.A, { variant: "h4", color: "primary", weight: "light", children: Y.length || K ? r("General.Text.NoSearchResults") : r("General.Text.NothingFound") })] })] }) } }, 26019: (e, t, n) => { "use strict";
                n.d(t, { FF: () => A, iU: () => w, Jv: () => g }); var r, a, o = n(57528),
                    i = n(75156),
                    l = n(96364),
                    s = n(96795),
                    c = n(96962),
                    d = n(30105),
                    u = n(61531),
                    h = n(72119),
                    m = n(3523),
                    p = n(70579); const f = (0, h.Ay)(l.A)(r || (r = (0, o.A)(["\n  font-size: 14px;\n  font-weight: 300;\n  color: #444;\n"]))),
                    v = (0, h.Ay)(l.A)(a || (a = (0, o.A)(["\n  font-weight: 500;\n  color: #000;\n"]))),
                    g = e => { let { name: t, handleEditOrg: n, logo: r, isAdmin: a, website: o, description: l } = e; return (0, p.jsxs)(p.Fragment, { children: [(0, p.jsx)(s.A, { avatar: r ? (0, p.jsx)("img", { src: (0, m.K7)(r), height: 80, style: { border: "solid 2px #dddddd", borderRadius: "8px" } }) : (0, p.jsx)(c.A, { "aria-label": "recipe", children: (0, p.jsx)(i.Ay, { icon: "Building" }) }), title: (0, p.jsx)(v, { children: t }), subheader: o ? (0, p.jsx)("a", { target: "_blank", href: o, children: o }) : l ? (0, p.jsx)(f, { color: "textSecondary", children: l }) : null, action: a ? (0, p.jsx)(d.A, { onClick: n, size: "small", color: "primary", children: "Edit Organization Info" }) : null }), l && o && (0, p.jsx)(u.A, { ml: 2, mb: 2, children: (0, p.jsx)(f, { color: "textSecondary", children: l }) })] }) }; var y, b; const w = (0, h.Ay)(l.A)(y || (y = (0, o.A)(["\n  font-size: 12px;\n  color: #666;\n"])));
                (0, h.Ay)(l.A)(b || (b = (0, o.A)(["\n  font-size: 14px;\n  color: #666;\n"]))); var z, x = n(695); const A = (0, h.Ay)(x.A)(z || (z = (0, o.A)(["\n  > .MuiTableCell-root {\n    line-height: 2;\n    padding: 12px 24px 12px 16px;\n  }\n"]))) }, 43331: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => M, BF: () => g, II: () => A, M3: () => S, OX: () => w, Ot: () => z, UY: () => h, hb: () => v, lz: () => b, mn: () => k, no: () => m, qi: () => x, wO: () => y }); var r = n(80907),
                    a = n(80192),
                    o = n(9787),
                    i = n(47730),
                    l = n(47088),
                    s = n(94234),
                    c = n(10621); const d = "organization",
                    u = { charts: [], theme: 0, initialLoaded: !1 },
                    h = (0, o.a)({ slice: d, scope: "organizations" }),
                    m = (0, i.B)({ slice: d, scope: "organizations" }),
                    p = (e, t) => { let { payload: n } = t; const { changedCharts: r, theme: a } = n; for (let o of r || []) { const { id: t } = o || {}, n = e.charts.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === t)); if (-1 !== n) { const t = e.charts[n];
                                e.charts[n] = { ...t, theme: (null === a || void 0 === a ? void 0 : a.id) || t.theme, v6FormatMigrated: !0 } } } },
                    f = (0, r.Z0)({ name: d, initialState: u, reducers: { resetOrgResources: () => u, "get/pending": () => ({ ...u }), "get/fulfilled": (e, t) => ({ ...t.payload.organization, initialLoaded: !0, charts: e.charts }), "getSimple/fulfilled": (e, t) => ({ ...t.payload.organization }), "update/fulfilled": (e, t) => ({ ...t.payload.organization, charts: e.charts }), "create/fulfilled": (e, t) => ({ ...t.payload.organization, charts: e.charts }), "getCharts/fulfilled": (e, t) => { e.charts = t.payload.charts }, "remove/fulfilled": e => {
                                ({ id: null, charts: [] }) }, "copyCustomFields/fulfilled": (e, t) => { let { payload: n } = t; const r = null === n || void 0 === n ? void 0 : n.fields;
                                e.fields = r || e.fields } }, extraReducers: { "roles/mergeChartLink/fulfilled": (e, t) => { let { meta: { arg: { embeddedChartId: n } } } = t;
                                n && (e.charts = e.charts.filter((e => e.id !== n))) }, "chart/createAliasLink/fulfilled": (e, t) => { const { chart: n } = t.payload, { organization: r } = n;
                                r === e.id && e.charts.push(t.payload.chart) }, "chart/create/fulfilled": (e, t) => { const { chart: n } = t.payload, { organization: r } = n;
                                r === e.id && e.charts.push(t.payload.chart) }, "chart/delete/fulfilled": (e, t) => { var n; const r = [...((null === (n = t.payload) || void 0 === n ? void 0 : n.charts) || []).map((e => e.id)).filter((e => e))]; for (const i of e.charts) { var a, o;
                                    null !== i && void 0 !== i && null !== (a = i.alias) && void 0 !== a && a.rootChartId && (r.includes(null === i || void 0 === i || null === (o = i.alias) || void 0 === o ? void 0 : o.rootChartId) && r.push(i.id)) } r.length && (e.charts = e.charts.filter((e => !r.includes(e.id)))) }, "chart/update/fulfilled": (e, t) => { let { payload: { chart: n } } = t; const r = e.charts.findIndex((e => e.id === n.id)); - 1 !== r && (e.charts[r] = n) }, "themes/setActive/fulfilled": (e, t) => {
                                ((e, t) => { let { payload: n } = t; const { mode: r, theme: { id: a }, chartId: o } = n;
                                    r === s.cD.makeDefault && a && (e.theme = a, e.charts = e.charts.map((e => e.theme === a ? { ...e, theme: null } : e))), r === s.cD.applyDefaultToChart && (e.charts = e.charts.map((e => e.id === o ? { ...e, theme: null } : e))), r === s.cD.resetAllToDefault && a && (e.charts = e.charts.map((e => ({ ...e, theme: null })))), r === s.cD.applyToChart && a && (e.charts = e.charts.map((e => e.id === o ? { ...e, theme: a } : e))), r === s.cD.applyToChartAndMakeDefault && a && o && (e.theme = a, e.charts = e.charts.map((e => e.theme === a || e.theme === o ? { ...e, theme: null } : e))) })(e, t) }, "themes/delete/fulfilled": (e, t) => { var n; const r = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.themeId) || "";
                                e.theme === r && (e.theme = "modern"), e.charts = e.charts.map((e => ({ ...e, theme: (null === e || void 0 === e ? void 0 : e.theme) === r ? null : null === e || void 0 === e ? void 0 : e.theme }))) }, "chart/createDemo/fulfilled": (e, t) => { var n; const r = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.chart;
                                r && e.charts.unshift(r) }, "chart/migrateFormat/fulfilled": p, "chart/get/fulfilled": (e, t) => { let { payload: n } = t; const { chart: r } = n; return r.organization !== e.id || e.charts.find((e => e.id === r.id)) || e.charts.push(r), p(e, { payload: n }) }, "chart/getLinkedCharts/fulfilled": (e, t) => { let { payload: n } = t; const r = n.charts || []; for (let a of r || []) { const { id: t } = a || {}; - 1 === e.charts.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === t)) && e.charts.push(a) } }, "userManagement/removeChartAccess/fulfilled": (e, t) => { let { payload: n } = t;
                                null !== n && void 0 !== n && n.chartId && (e.charts = e.charts.filter((e => e.id !== n.chartId))) }, "fields/createCustomField/fulfilled": (e, t) => { let { payload: n } = t;
                                e.fields || (e.fields = []), null !== n && void 0 !== n && n.field && e.fields.push(n.field) }, "fields/updateCustomField/fulfilled": (e, t) => { let { payload: n } = t; if (e.fields || (e.fields = []), null !== n && void 0 !== n && n.field) { const t = e.fields.findIndex((e => { var t; return e.id === (null === n || void 0 === n || null === (t = n.field) || void 0 === t ? void 0 : t.id) })); if (-1 !== t) { const r = [...e.fields];
                                        r.splice(t, 1, n.field), e.fields = r } } }, "fields/deleteCustomField/fulfilled": (e, t) => { let { payload: n } = t;
                                e.fields || (e.fields = []); const r = null === n || void 0 === n ? void 0 : n.fields;
                                e.fields = e.fields.filter((e => !r.includes(e.id))) }, "templates/restoreTemplate/fulfilled": (e, t) => { var n; let { payload: r } = t;
                                null !== r && void 0 !== r && null !== (n = r.charts) && void 0 !== n && n.length && e.charts.push(...r.charts) } } }),
                    { resetOrgResources: v } = f.actions,
                    g = (0, a.Mz)((e => e.organization), (e => (0, c.LS)({ id: e.id, name: e.name, license: e.license, logo: e.logo, website: e.website, description: e.description, ownershipDetails: e.ownershipDetails, updated_at: e.updated_at, created_at: e.created_at }, "logo", "updated_at"))),
                    y = (0, a.Mz)((e => e.organization.charts), (e => (e || []).filter((e => !e.alias)))),
                    b = (0, a.Mz)((e => e.organization.charts), (e => (e || []).filter((e => !e.alias && e.status !== l.N4.Draft)))),
                    w = (0, a.Mz)((e => e.organization.charts), (e => (e || []).filter((e => e.status !== l.N4.Draft)))),
                    z = e => e.organization.charts,
                    x = (0, a.Mz)((e => e.organization.charts), (e => e.reduce(((t, n) => { if (n.alias) { const r = n.alias.rootChartId,
                                a = e.find((e => e.id === r));
                            a ? (t[r] = t[r] || [], t[r].push(n)) : (t.shared = t.shared || [], t.shared.push(n)) } return t }), {}))),
                    A = (0, a.Mz)((e => e.organization.charts), (e => e.reduce(((e, t) => { if (t.alias && t.alias.rootChartId && t && t.id) { e[t.alias.roleId] = t } return e }), {}))),
                    k = e => e.organization.id,
                    S = e => { let t = e.user.organizations.find((t => t.id === e.organization.id)); return t ? t.accessLevel : null },
                    M = f.reducer }, 90930: (e, t, n) => { "use strict";
                n.d(t, { BF: () => o, Qn: () => i }); var r = n(80192),
                    a = n(10621);
                n(15622), n(42006); const o = (0, r.Mz)((e => e.organization), (e => (0, a.LS)({ id: e.id, name: e.name, license: e.license, logo: e.logo, website: e.website, description: e.description, ownershipDetails: e.ownershipDetails, updated_at: e.updated_at, created_at: e.created_at }, "logo", "updated_at"))),
                    i = (0, r.Mz)((e => e.organization), (e => t => (e.charts || []).find((e => e.id === t)))) }, 1443: (e, t, n) => { "use strict";
                n.d(t, { A: () => X }); var r = n(65043),
                    a = n(96446),
                    o = n(85865),
                    i = n(39336),
                    l = n(67784); let s = function(e) { return e.Parent = "parent", e.Child = "child", e.Main = "main", e }({}); var c = n(6803),
                    d = n(42518),
                    u = n(49248),
                    h = n(17392),
                    m = n(98022),
                    p = n(70579); const f = e => { let { children: t, elevation: n = m.vR.lowest, ...r } = e; return (0, p.jsx)(a.A, { id: "navigation-icon-button", borderRadius: "50%", sx: { ...m.Qi[n], backgroundColor: "white !important" }, children: (0, p.jsx)(h.A, { ...r, children: t }) }) }; var v = n(75156),
                    g = n(32115),
                    y = n(69384); const b = e => { let { rowType: t, entityLength: n, children: i, handleNextClick: l, handleAddEntityClick: h, handlePrevClick: m, isFilterApplied: b, secView: w, isMinOwner: z } = e; const x = t === s.Parent,
                        A = t === s.Child,
                        { alignItems: k, rowHeight: S } = (0, r.useMemo)((() => t === s.Main ? { alignItems: "center", rowHeight: 2 * u.ZM + u.E7 } : x ? { alignItems: "flex-start", rowHeight: 2 * u.ZM + u.E7 } : A ? { alignItems: "center", rowHeight: 4 * u.ZM + u.E7 } : { alignItems: "center", rowHeight: 2 * u.ZM + u.E7 }), [t]),
                        M = A ? 2 * u.ZM : 0,
                        E = (0, c.A)(x ? b ? y.Z_ : y.GX : b ? y.a1 : y.su),
                        C = (0, r.useMemo)((() => { if (b) return "No ".concat(E, " found for the selected filters. Please try another filter or clear the filter."); if (w) return "No ".concat(E, " found."); const e = "Please add ".concat(x ? "an" : "a", " ").concat(E); return "No ".concat(E, " found. ").concat(z ? e : "") }), [b, E, x, w]); return (0, p.jsxs)(a.A, { display: "flex", alignItems: k, zIndex: 2, height: S, children: [0 === n && (0, p.jsxs)(a.A, { height: "100%", width: "100%", display: "flex", alignItems: "center", justifyContent: "center", flexDirection: "column", gap: 2, children: [(0, p.jsx)(o.A, { variant: g.Eq.subheadingXL, children: C }), !w && z && (0, p.jsx)(d.A, { startIcon: (0, p.jsx)(v.gF, { icon: "Add", size: "sm" }), variant: "outlined", color: "primary", size: "small", onClick: h, children: (0, c.A)(x ? y.GX : y.su) })] }), n > 1 && (0, p.jsx)(a.A, { height: u.E7 + M, maxHeight: u.E7 + M, minHeight: u.E7 + M, display: "flex", alignItems: "center", children: (0, p.jsx)(f, { onClick: m, size: "large", color: "black", children: (0, p.jsx)(v.gF, { icon: "ArrowLeft", size: "lg" }) }) }), n > 0 && (0, p.jsx)("div", { style: { flexGrow: 1, width: "calc(70vw - 100px)" }, children: (0, p.jsx)("div", { style: { display: "flex", overflowX: "hidden", overflowY: "hidden", justifyContent: 1 === n ? "center" : "space-around", alignItems: "center", paddingLeft: "".concat(u.Fy, "px"), paddingRight: "".concat(u.Fy, "px"), borderRadius: "4px", paddingBottom: "".concat(x ? 2 * u.ZM : A ? 100 : 0, "px"), paddingTop: "".concat(A ? 2 * u.ZM : 0, "px") }, children: i }) }), (0, p.jsx)(a.A, { position: "relative", height: u.E7 + M, maxHeight: u.E7 + M, minHeight: u.E7 + M, minWidth: 80, children: n >= 1 && (0, p.jsxs)(p.Fragment, { children: [!w && z && (0, p.jsx)(a.A, { position: "relative", zIndex: 2, children: (0, p.jsx)(d.A, { startIcon: (0, p.jsx)(v.gF, { icon: "Add", size: "sm" }), variant: "outlined", color: "primary", size: "small", onClick: h, children: (0, c.A)(x ? y.GX : y.su) }) }), n > 1 && (0, p.jsx)(a.A, { position: "absolute", height: u.E7 + M, maxHeight: u.E7 + M, minHeight: u.E7 + M, display: "flex", alignItems: "center", top: 0, justifyContent: "center", width: "100%", children: (0, p.jsx)(f, { onClick: l, size: "large", color: "black", children: (0, p.jsx)(v.gF, { icon: "ArrowRight", size: "lg" }) }) })] }) })] }) }; var w = n(14556),
                    z = n(66779),
                    x = n(37294),
                    A = n(60587),
                    k = n(77739); let S = function(e) { return e.Focus = "focus", e.Initial = "initial", e.Left = "left", e.Right = "right", e }({}); const M = { cardContainer: {
                        [S.Focus]: e => { let { cardType: t, actualWidth: n, cardContentHeight: r, borderColorHsl: a, zIndex: o } = e; return { scale: 1, zIndex: o, width: n, maxWidth: n, height: r, maxHeight: r, marginRight: "", marginLeft: "", backgroundColor: "transparent", boxShadow: "0px 8px 7px 0px rgba(0, 0, 0, 0),\n              0px 7px 3px 0px rgba(0, 0, 0, 0.01),\n              0px 0px 6px 0px rgba(0, 0, 0, 0.05), 0px 7px 7px 0px rgba(0, 0, 0, 0.09),\n              0px 7px 5px 0px rgba(0, 0, 0, 0.1)", border: t === s.Main ? "6px solid ".concat(x.Qs.Violet[500]) : "1px solid ".concat(a), transition: { ease: [0, .71, .8, 1.01], duration: .2 } } }, [S.Initial]: e => { let { cardType: t, actualWidth: n, cardContentHeight: r, borderColorHsl: a, zIndex: o } = e; return { scale: 1, zIndex: o, width: n, maxWidth: n, height: r, maxHeight: r, marginRight: "", marginLeft: "", backgroundColor: "transparent", boxShadow: "0px 8px 7px 0px rgba(0, 0, 0, 0),\n        0px 7px 3px 0px rgba(0, 0, 0, 0.01),\n        0px 0px 6px 0px rgba(0, 0, 0, 0.05), 0px 7px 7px 0px rgba(0, 0, 0, 0.09),\n        0px 7px 5px 0px rgba(0, 0, 0, 0.1)", border: t === s.Main ? "8px solid ".concat(x.Qs.Violet[500]) : "1px solid ".concat(a), transition: { ease: [0, .71, .8, 1.01], duration: .2 } } }, [S.Left]: e => { let { actualWidth: t, borderColorHsl: n, cardContentHeight: r, direction: a, hideContent: o, zIndex: i } = e; return { zIndex: i, perspective: "1000px", width: t, maxWidth: t, height: r, maxHeight: r, border: "1px solid ".concat(n), backgroundColor: "white", marginRight: "-".concat((o ? .85 : .7) * t, "px"), boxShadow: "", transition: { ease: [0, .71, .8, 1.01], duration: -1 === a ? .5 : 0 } } }, [S.Right]: e => { let { actualWidth: t, borderColorHsl: n, cardContentHeight: r, direction: a, hideContent: o, zIndex: i } = e; return { zIndex: i, width: t, maxWidth: t, height: r, maxHeight: r, border: "1px solid ".concat(n), backgroundColor: "white", marginLeft: "-".concat((o ? .85 : .7) * t, "px"), boxShadow: "", transition: { ease: [0, .71, .8, 1.01], duration: 1 === a ? .5 : 0 } } } }, cardContentWrapper: {
                        [S.Focus]: () => ({ opacity: [.5, 1], transition: { ease: [0, .71, .8, 1.01], duration: 1 } }), [S.Left]: e => { let { hideContent: t } = e; return { opacity: t ? 0 : [.4, 1], display: t ? "none" : "", transition: { ease: [0, .71, .8, 1.01], duration: 1 } } }, [S.Right]: e => { let { hideContent: t } = e; return { opacity: t ? 0 : [.4, 1], display: t ? "none" : "", transition: { ease: [0, .71, .8, 1.01], duration: 1 } } } }, cardHeader: {
                        [S.Focus]: { flexDirection: "row-reverse", transition: { ease: [0, .71, .8, 1.01], duration: 1 } }, [S.Left]: { flexDirection: "row", transition: { ease: [0, .71, .8, 1.01], duration: 1 } }, [S.Right]: { flexDirection: "row-reverse", transition: { ease: [0, .71, .8, 1.01], duration: 1 } } } }; var E = n(60350),
                    C = n(70318); const T = e => { var t; let { entity: n, idx: i, activeIdx: l, direction: c, onClick: d, onTransitionEnd: h, cardType: m = s.Parent, handleInfoClick: f, handleChangeActiveOrgClick: y, secView: b } = e; const w = Math.abs(l - i) > 3,
                        T = Math.max(.01, 1 - .05 * (l - i)),
                        H = Math.max(.01, 1 - .05 * -(l - i)),
                        L = i < l,
                        I = i > l,
                        j = (0, r.useMemo)((() => L ? T : I ? H : 1), [i, l, T, H]),
                        V = (0, r.useMemo)((() => u.zh * j), [u.zh, j]),
                        O = (0, r.useMemo)((() => { const e = Math.abs(i - l); let t = 83; return e > 1 && (t = Math.min(98, 83 + .8 * e)), i === l && (t = 34), "hsl(0,0%, ".concat(t, "%)") }), [i, l]),
                        R = (0, r.useMemo)((() => 100 - Math.abs(i - l)), [i, l]),
                        { cardContentHeight: P } = (0, r.useMemo)((() => { const e = u.E7 * j; return w ? { cardContentHeight: e, cardContainerHeight: e } : { cardContentHeight: e, cardContainerHeight: e + 2 * u.ZM } }), [u.E7, j]),
                        D = (0, r.useRef)(null),
                        F = () => { h && h(m, D) }; if ((0, r.useEffect)((() => { m === s.Main && F() }), [m]), Math.abs(l - i) > 20) return null; const N = { actualWidth: V, borderColorHsl: O, cardContentHeight: P, cardType: m, zIndex: R, hideContent: w, direction: c }; return (0, p.jsx)(z.P.div, { id: "card-container-".concat(i), onClick: d, style: { overflow: "hidden", display: "flex", alignItems: "center", justifyContent: "center", borderRadius: "4px", position: "relative" }, onAnimationComplete: () => { l === i && F() }, ref: D, variants: {
                            [S.Focus]: M.cardContainer[S.Focus](N), [S.Initial]: M.cardContainer[S.Initial](N), [S.Left]: M.cardContainer[S.Left](N), [S.Right]: M.cardContainer[S.Right](N) }, animate: l === i ? S.Focus : l > i ? S.Left : S.Right, initial: S.Initial, children: (0, p.jsx)(a.A, { height: "100%", width: "100%", bgcolor: x.Qs.Neutrals[0], display: "flex", borderRadius: "4px", children: (0, p.jsx)(z.P.div, { style: { padding: "15px 17px", width: "100%", height: "100%" }, variants: {
                                    [S.Focus]: M.cardContentWrapper[S.Focus]({}), [S.Left]: M.cardContentWrapper[S.Left]({ hideContent: w }), [S.Right]: M.cardContentWrapper[S.Right]({ hideContent: w }) }, animate: l === i ? S.Focus : l > i ? S.Left : S.Right, children: (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", justifyContent: "space-between", height: "100%", width: "100%", children: [(0, p.jsxs)(z.P.div, { style: { width: "100%", display: "flex", gap: "12px", height: "100%", backgroundColor: "white", opacity: 1 }, animate: l === i ? "focus" : l > i ? "left" : "right", variants: M.cardHeader, children: [(0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", gap: "6px", alignItems: "center", children: [null !== n && void 0 !== n && n.logo ? (0, p.jsx)(A.A, { src: null === n || void 0 === n ? void 0 : n.logo, sx: { width: 25, height: 25 }, slotProps: { img: { style: { objectFit: "scale-down" } } } }, "card-entity-logo-".concat(n.id || n.cik, "-").concat(n.logo)) : (0, p.jsx)(A.A, { sx: { width: 25, height: 25 }, "aria-label": "recipe", children: (0, p.jsx)(v.gF, { icon: "Building" }) }, "card-entity-logo-".concat(n.id || n.cik, "-").concat(n.logo)), (0, p.jsx)("span", { onClick: f, children: (0, p.jsx)(v.gF, { icon: "Info", size: "sm", onClick: f }) })] }, "card-entity-".concat(n.id || n.cik)), (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", children: [(0, p.jsx)(o.A, { variant: g.Eq.subheadingSM, color: x.Qs.Neutrals[900], children: null === n || void 0 === n ? void 0 : n.name }), (0, p.jsx)(o.A, { variant: g.Eq.caption, color: x.Qs.Neutrals[800], children: null === n || void 0 === n ? void 0 : n.locationString })] })] }), (0, p.jsxs)(a.A, { display: "flex", flexDirection: i < l ? "row-reverse" : "row", justifyContent: "space-between", alignItems: "center", width: "100%", children: [!b && (0, p.jsxs)(a.A, { display: "flex", gap: "8px", alignItems: "center", children: [(0, p.jsx)(E.K, { org: n }), y && (0, p.jsx)(k.A, { title: "See ownership chart", children: (0, p.jsx)(C.A, { size: "small", onClick: () => y(n), children: (0, p.jsx)(a.A, { mt: 1, children: (0, p.jsx)(v.gF, { icon: "DrillDown", size: "sm", color: x.Qs.Blue[500], onClick: () => y(n) }) }) }) })] }), (0, p.jsxs)(a.A, { display: "flex", gap: .5, flexDirection: i < l ? "row-reverse" : "row", alignItems: "center", id: "card-entity-type-".concat(i, "-").concat(n.entityType), children: [(0, p.jsx)(o.A, { variant: g.Eq.caption, color: x.Qs.Neutrals[800], children: (null === n || void 0 === n ? void 0 : n.incorporatedYear) && (0, p.jsxs)(p.Fragment, { children: ["Incorporated ", n.incorporatedYear] }) }), null === n || void 0 === n || null === (t = n.entityType) || void 0 === t ? void 0 : t.map((e => (0, p.jsx)(k.A, { title: e, arrow: !0, children: (0, p.jsx)(a.A, { display: "flex", children: (0, p.jsx)(v.gF, { icon: "Circle", size: "xxs", color: u.HF[e] }, "card-entity-type-icon-".concat(i, "-").concat(e)) }, "card-entity-type-icon-wrapper-".concat(i, "-").concat(e)) }, "card-entity-type-icon-wrapper-tooltip-".concat(i, "-").concat(e))))] }, "card-entity-type-".concat(i, "-").concat(n.entityType))] })] }) }) }) }) }; var H = n(44938),
                    L = n(23546),
                    I = function(e) { return e.Above = "above", e.Below = "below", e }(I || {}); const j = (e, t, n, r, a) => { if (e.y === t.y && e.x === t.x) return ""; const o = 10,
                            i = a === I.Above ? 20 : -10,
                            l = (n.y + r.y) / 2,
                            s = Math.abs(e.x - t.x); return s < 15 ? e.x > t.x ? "M".concat(e.x, ",").concat(e.y, " V").concat(l + i - o, " Q").concat(e.x, ",").concat(l + i, " ").concat(e.x - s / 2.5, ",").concat(l + i, " H").concat(t.x + s / 2.5, " Q").concat(t.x, ",").concat(l + i, " ").concat(t.x, ",").concat(l + o + i, " V").concat(t.y) : "M".concat(e.x, ",").concat(e.y, " V").concat(l + i - o, " Q").concat(e.x, ",").concat(l + i, " ").concat(e.x + s / 2.5, ",").concat(l + i, " H").concat(t.x - s / 2.5, " Q").concat(t.x, ",").concat(l + i, " ").concat(t.x, ",").concat(l + i + o, " V").concat(t.y) : e.x > t.x ? "M".concat(e.x, ",").concat(e.y, " V").concat(l + i - o, " Q").concat(e.x, ",").concat(l + i, " ").concat(e.x - o, ",").concat(l + i, " H").concat(t.x + o, " Q").concat(t.x, ",").concat(l + i, " ").concat(t.x, ",").concat(l + i + o, " V").concat(t.y) : "M".concat(e.x, ",").concat(e.y, " V").concat(l + i - o, " Q").concat(e.x, ",").concat(l + i, " ").concat(e.x + o, ",").concat(l + i, " H").concat(t.x - o, " Q").concat(t.x, ",").concat(l + i, " ").concat(t.x, ",").concat(l + i + o, " V").concat(t.y) },
                    V = e => { let { lines: t, childrenLines: n, activeLineOrigin: r, activeLineDestination: a, activeLineChildrenOrigin: i, activeLineChildrenDestination: l } = e; return (0, p.jsxs)("svg", { width: "100%", height: "99%", children: [t.map((e => { let { idx: t, origin: n, destination: o, isActive: i, connectionTo: l } = e; return (0, p.jsx)(H.A, { data: { source: { x: 0, y: 0 }, target: { x: 1e3, y: 1e3 } }, children: () => (0, p.jsx)(p.Fragment, { children: (0, p.jsx)(z.P.path, { d: j({ x: n.x, y: n.y }, { x: o.x, y: o.y }, r, a, I.Above), fill: "none", stroke: i ? x.Qs.Violet[500] : x.Qs.Neutrals[400], strokeWidth: u.Zz, animate: { d: j({ x: n.x, y: n.y }, { x: o.x, y: o.y }, r, a, I.Above), transition: { ease: "easeIn", duration: .2 }, stroke: i ? x.Qs.Violet[500] : x.Qs.Neutrals[400], opacity: [0, 1] } }, "line-path-".concat(l, "-").concat(t)) }) }, "line-".concat(t)) })), (0, p.jsx)(L.N, { children: t.map((e => { let { idx: t, origin: n, isActive: r, connectionTo: a, percentage: i } = e; return (0, p.jsx)(z.P.foreignObject, { animate: { x: n.x - 30, y: n.y, width: 60, height: u.ZM + 20, opacity: [0, 1], transition: { ease: "easeIn", duration: .2 } }, initial: { x: n.x - 30, y: n.y - 100, width: 60, height: u.ZM + 20 }, exit: { x: n.x - 30, y: n.y - 100, opacity: [1, 0], width: 60, height: u.ZM + 20 }, children: (0, p.jsx)(z.P.div, { style: { display: "flex", alignItems: "center", justifyContent: "center", height: "100%", width: "100%" }, children: (0, p.jsx)(z.P.div, { variants: { focus: { backgroundColor: x.Qs.Violet[500], color: x.Qs.Neutrals[0], border: "1px solid ".concat(x.Qs.Violet[500]), transition: { duration: .2, ease: [.17, .67, .83, .67] } }, inactive: { backgroundColor: x.Qs.Neutrals[0], color: x.Qs.Neutrals[500], border: "1px solid ".concat(x.Qs.Neutrals[500]), transition: { duration: .2, ease: [.17, .67, .83, .67] } }, initial: { backgroundColor: x.Qs.Neutrals[0], color: x.Qs.Neutrals[500], border: "1px solid ".concat(x.Qs.Neutrals[500]) } }, style: { padding: "3px 5px", borderRadius: "99px" }, animate: r ? "focus" : "inactive", initial: "initial", children: (0, p.jsxs)(o.A, { variant: g.Eq.subheadingSM, children: [i, "%"] }) }, "badge-".concat(a, "-").concat(t)) }, "badge-container-".concat(a, "-").concat(t)) }, "fe-".concat(a, "-").concat(t, "-").concat(i)) })) }), n.map((e => { let { idx: t, origin: n, destination: r, isActive: a, connectionTo: o } = e; return (0, p.jsx)(H.A, { data: { source: { x: 0, y: 0 }, target: { x: 1e3, y: 1e3 } }, children: () => (0, p.jsx)(z.P.path, { d: j({ x: n.x, y: n.y }, { x: r.x, y: r.y }, i, l, I.Below), fill: "none", stroke: a ? x.Qs.Violet[500] : x.Qs.Neutrals[400], strokeWidth: u.Zz, animate: { d: j({ x: n.x, y: n.y }, { x: r.x, y: r.y }, i, l, I.Below), transition: { ease: "easeIn", duration: .2 }, stroke: a ? x.Qs.Violet[500] : x.Qs.Neutrals[400], opacity: [0, 1] } }, "line-path-".concat(o, "-").concat(t)) }, "line-".concat(t)) })), (0, p.jsx)(L.N, { children: n.map((e => { let { idx: t, destination: n, isActive: r, connectionTo: a, percentage: i } = e; return (0, p.jsx)(z.P.foreignObject, { animate: { x: n.x - 30, y: n.y - u.ZM, width: 60, opacity: [0, 1], height: u.ZM, transition: { ease: "easeIn", duration: .2 } }, initial: { x: n.x - 30, y: n.y, width: 60, height: u.ZM }, exit: { x: n.x - 30, y: n.y, opacity: [1, 0], width: 60, height: u.ZM }, children: (0, p.jsx)(z.P.div, { style: { display: "flex", alignItems: "center", justifyContent: "center", height: "100%", width: "100%" }, children: (0, p.jsx)(z.P.div, { variants: { focus: { backgroundColor: x.Qs.Violet[500], color: x.Qs.Neutrals[0], border: "1px solid ".concat(x.Qs.Violet[500]), transition: { duration: .2, ease: [.17, .67, .83, .67] } }, inactive: { backgroundColor: x.Qs.Neutrals[0], color: x.Qs.Neutrals[500], border: "1px solid ".concat(x.Qs.Neutrals[500]), transition: { duration: .2, ease: [.17, .67, .83, .67] } }, initial: { backgroundColor: x.Qs.Neutrals[0], color: x.Qs.Neutrals[500], border: "1px solid ".concat(x.Qs.Neutrals[500]) } }, style: { padding: "3px 5px", borderRadius: "99px" }, animate: r ? "focus" : "inactive", initial: "initial", children: (0, p.jsxs)(o.A, { variant: g.Eq.subheadingSM, children: [i, "%"] }) }, "badge-".concat(a, "-").concat(t)) }, "badge-container-".concat(a, "-").concat(t)) }, "fe-".concat(a, "-").concat(t, "-").concat(i)) })) })] }) }; var O = n(50543),
                    R = n(20965),
                    P = n(31056),
                    D = n(25197),
                    F = n(38355),
                    N = n(14429),
                    _ = n(356),
                    B = n(72952),
                    W = n(86255),
                    U = n(66856),
                    q = n(91688),
                    G = n(63296),
                    K = n(97184); const Z = { x: 0, y: 0 },
                    Y = () => Object.keys(B.ck).reduce(((e, t) => (e[B.ck[t]] = !1, e)), {}),
                    X = e => { var t, n, c, d; let { secView: h = !1 } = e; const m = (0, q.useHistory)(),
                            [f, v] = (0, r.useState)(0),
                            [y, z] = (0, r.useState)(0),
                            [A, k] = (0, r.useState)(0),
                            [S, M] = (0, r.useState)(0),
                            E = (0, r.useRef)(null),
                            C = (0, r.useRef)(null),
                            H = (0, r.useRef)(null),
                            [L, I] = (0, r.useState)(Z),
                            [j, B] = (0, r.useState)(Z),
                            [X, $] = (0, r.useState)(Z),
                            [Q, J] = (0, r.useState)(Z),
                            [ee, te] = (0, r.useState)([]),
                            [ne, re] = (0, r.useState)([]),
                            [ae, oe] = (0, r.useState)(Y),
                            { orgId: ie } = (0, q.useParams)(),
                            le = (0, w.d4)((0, O.hm)(ie, h)),
                            se = (0, w.d4)(h ? O.hj : (0, O.a2)(ie)),
                            ce = (0, w.d4)(O.U3),
                            { isMinOwner: de, isMinAdmin: ue } = (0, G.A)(),
                            { openOrgEntityInfoDialog: he, openRelationsDialog: me } = (0, K.A)(),
                            [pe, fe] = (0, r.useState)("ownershipDescending"),
                            ve = (0, r.useMemo)((() => { var e, t; const n = Object.keys(ae).filter((e => ae[e])); let r = [...null === se || void 0 === se ? void 0 : se.owners],
                                    a = [...null === se || void 0 === se ? void 0 : se.subsidiaries]; return n.length > 0 && (r = [...null === se || void 0 === se ? void 0 : se.owners].filter((e => { var t; return e.entityType && (null === (t = e.entityType) || void 0 === t ? void 0 : t.some((e => n.includes(e)))) })), a = [...null === se || void 0 === se ? void 0 : se.subsidiaries].filter((e => { var t; return e.entityType && (null === (t = e.entityType) || void 0 === t ? void 0 : t.some((e => n.includes(e)))) }))), null === (e = r) || void 0 === e || e.sort(((e, t) => "ownershipDescending" === pe ? (t.stake || 0) - (e.stake || 0) : (e.stake || 0) - (t.stake || 0))), null === (t = a) || void 0 === t || t.sort(((e, t) => "ownershipDescending" === pe ? (t.stake || 0) - (e.stake || 0) : (e.stake || 0) - (t.stake || 0))), { owners: r, subsidiaries: a } }), [se, pe, ae]),
                            ge = (null === ve || void 0 === ve || null === (t = ve.owners) || void 0 === t ? void 0 : t.length) || 0,
                            ye = (null === ve || void 0 === ve || null === (n = ve.subsidiaries) || void 0 === n ? void 0 : n.length) || 0,
                            { eventDebounce: be } = (0, W.A)(),
                            we = e => { m.push((0, U.wi)({ orgId: null === e || void 0 === e ? void 0 : e.id, view: "chart", base: "protected" })) },
                            ze = (e, t) => { if (t.current)
                                    if (e === s.Parent) { const e = t.current;
                                        E.current = e; const n = ((null === e || void 0 === e ? void 0 : e.offsetLeft) || 0) + ((null === e || void 0 === e ? void 0 : e.offsetWidth) || 0) / 2,
                                            r = (null === e || void 0 === e ? void 0 : e.offsetHeight) || 0;
                                        I({ x: n, y: r }) } else if (e === s.Main) { const e = t.current;
                                    C.current = e; const n = ((null === e || void 0 === e ? void 0 : e.offsetLeft) || 0) + ((null === e || void 0 === e ? void 0 : e.offsetWidth) || 0) / 2,
                                        r = (null === e || void 0 === e ? void 0 : e.offsetTop) || 0,
                                        a = r + ((null === e || void 0 === e ? void 0 : e.offsetHeight) || 0);
                                    B({ x: n, y: r }), $({ x: n, y: a }) } else { const e = t.current;
                                    H.current = e; const n = ((null === e || void 0 === e ? void 0 : e.offsetLeft) || 0) + ((null === e || void 0 === e ? void 0 : e.offsetWidth) || 0) / 2,
                                        r = (null === e || void 0 === e ? void 0 : e.offsetTop) || 0;
                                    J({ x: n, y: r }) } },
                            xe = e => { let { card: t, position: n, row: r, idx: a } = e; const o = ke(t, null, n),
                                    i = "left" === n,
                                    l = "right" === n,
                                    c = l && (i || l) ? t.offsetWidth - o / 2 : o / 2; if (r === s.Parent) { var d; const e = (null === t || void 0 === t ? void 0 : t.offsetLeft) + c,
                                        n = null === t || void 0 === t ? void 0 : t.offsetHeight,
                                        r = i ? f - a - 1 : f + a + 1,
                                        o = Math.round(10 * ((null === (d = ve.owners[r]) || void 0 === d ? void 0 : d.stake) || 0)) / 10; return { origin: { x: e, y: n }, destination: j, idx: r, isActive: !1, percentage: o, connectionTo: s.Parent } } if (r === s.Child) { var u; const e = (null === t || void 0 === t ? void 0 : t.offsetLeft) + c,
                                        n = null === t || void 0 === t ? void 0 : t.offsetTop,
                                        r = i ? y - a - 1 : y + a + 1,
                                        o = Math.round(10 * ((null === (u = ve.subsidiaries[r]) || void 0 === u ? void 0 : u.stake) || 0)) / 10; return { origin: X, destination: { x: e, y: n }, idx: r, isActive: !1, percentage: o, connectionTo: s.Child } } return { origin: { x: 0, y: 0 }, destination: { x: 0, y: 0 }, idx: 0, isActive: !1, percentage: 0, connectionTo: s.Parent } };
                        (0, r.useEffect)((() => { var e; if (0 === L.x || 0 === L.y || 0 === j.x || 0 === j.y) return; if (0 === ge) return void te([]); const t = E.current,
                                n = [],
                                r = []; for (let s = 0; s <= 2; s++) { var a, o; const e = (null === (a = null !== n && void 0 !== n && n.length ? n[s - 1] : t) || void 0 === a ? void 0 : a.previousSibling) || null,
                                    i = (null === (o = null !== r && void 0 !== r && r.length ? r[s - 1] : t) || void 0 === o ? void 0 : o.nextSibling) || null;
                                n.push(e), r.push(i) } const i = n.filter((e => e)).map(((e, t) => xe({ card: e, position: "left", row: s.Parent, idx: t }))),
                                l = r.filter((e => e)).map(((e, t) => xe({ card: e, position: "right", row: s.Parent, idx: t }))),
                                c = Math.round(10 * ((null === (e = ve.owners[f]) || void 0 === e ? void 0 : e.stake) || 0)) / 10,
                                d = [...i, ...l, { origin: L, destination: j, idx: f, isActive: !0, percentage: c, connectionTo: s.Parent }];
                            te(d) }), [L, j, ge]), (0, r.useEffect)((() => { var e; if (0 === X.x || 0 === X.y || 0 === Q.x || 0 === Q.y) return; if (0 === ye) return void re([]); const t = H.current,
                                n = [],
                                r = []; for (let s = 0; s <= 2; s++) { var a, o; const e = (null === (a = null !== n && void 0 !== n && n.length ? n[s - 1] : t) || void 0 === a ? void 0 : a.previousSibling) || null,
                                    i = (null === (o = null !== r && void 0 !== r && r.length ? r[s - 1] : t) || void 0 === o ? void 0 : o.nextSibling) || null;
                                n.push(e), r.push(i) } const i = n.filter((e => e)).map(((e, t) => xe({ card: e, position: "left", row: s.Child, idx: t }))),
                                l = r.filter((e => e)).map(((e, t) => xe({ card: e, position: "right", row: s.Child, idx: t }))),
                                c = Math.round(10 * ((null === (e = ve.subsidiaries[y]) || void 0 === e ? void 0 : e.stake) || 0)) / 10,
                                d = [...i, ...l, { origin: X, destination: Q, idx: y, isActive: !0, percentage: c, connectionTo: s.Child }];
                            re(d) }), [Q, X, ye]); const Ae = be((() => { ze(s.Main, C), ze(s.Parent, E), ze(s.Child, H) }), 500);
                        (0, r.useEffect)((() => (window.addEventListener("resize", Ae), () => { window.removeEventListener("resize", Ae) })), []), (0, r.useEffect)((() => { v(0), z(0) }), [ae, pe]), (0, r.useEffect)((() => { be((() => { ze(s.Child, H) }), 50)() }), [ye, null === ve || void 0 === ve ? void 0 : ve.subsidiaries]), (0, r.useEffect)((() => { be((() => { ze(s.Parent, E), ze(s.Child, H) }), 50)() }), [ge, null === ve || void 0 === ve ? void 0 : ve.owners]), (0, r.useEffect)((() => { f > ge - 1 && ge > 0 && (v(ge - 1), be((() => { ze(s.Parent, E) }), 50)()) }), [f, ge]), (0, r.useEffect)((() => { y > ye - 1 && ye > 0 && (z(ye - 1), be((() => { ze(s.Child, H) }), 50)()) }), [y, ye]); const ke = (e, t, n) => { if (!e) return 0; if ("left" === n) { const n = (t = t || e.nextSibling).offsetLeft - e.offsetLeft; return Math.min(n, e.offsetWidth) } { t = t || e.previousSibling; const n = e.offsetLeft + e.offsetWidth - (t.offsetLeft + t.offsetWidth); return Math.min(n, e.offsetWidth) } },
                            Se = (0, w.d4)((0, O.Jb)(ie)),
                            Me = (0, r.useMemo)((() => Object.values(ae).some((e => e))), [ae]); if (!le) return (0, p.jsx)(p.Fragment, {}); const Ee = e => () => { _.A.trackEvent({ eventName: "OWNERSHIP_CHART_ORG_ADDED", extraParams: { type: e } }), me({ relationType: e, orgId: le.id, orgs: ce }) },
                            Ce = (null === ve || void 0 === ve || null === (c = ve.owners) || void 0 === c ? void 0 : c.length) + (null === ve || void 0 === ve || null === (d = ve.subsidiaries) || void 0 === d ? void 0 : d.length) + 1; return (0, p.jsxs)(N.A, { children: [(0, p.jsxs)(a.A, { display: "flex", alignItems: "center", justifyContent: "space-between", gap: "12px", children: [(0, p.jsxs)(a.A, { display: "flex", gap: (0, R.A)(18), alignItems: "center", height: "100%", children: [(0, p.jsxs)(o.A, { variant: g.Eq.h3, children: [Ce, " total"] }), (0, p.jsx)(i.A, { orientation: "vertical", color: x.Qs.Neutrals[300], flexItem: !0 }), (0, p.jsxs)(l.A, { select: !0, label: "Sort", variant: "outlined", labelPlacement: "start", required: !0, size: "small", value: pe, onChange: e => { fe(e.target.value) }, onClick: () => { _.A.trackEvent({ eventName: "OWNERSHIP_CHART_FILTERS_CLICKED", extraParams: {} }) }, children: [(0, p.jsx)(P.Dr, { value: "ownershipDescending", divider: !0, children: "Ownership % (Descending)" }), (0, p.jsx)(P.Dr, { value: "ownershipAscending", divider: !0, children: "Ownership % (Ascending)" })] })] }), Se.length > 0 && (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", gap: "6px", maxWidth: "45vw", children: [(0, p.jsxs)(a.A, { display: "flex", gap: "6px", alignItems: "center", height: "100%", style: { overflowX: "auto" }, children: [(0, p.jsx)(o.A, { variant: g.Eq.subheadingXS, children: "Filter by ownership type:" }), (0, p.jsx)(a.A, { display: "flex", gap: "6px", alignItems: "center", height: "100%", flexGrow: 1, flex: 1, style: { boxSizing: "border-box", overflowX: "auto" }, children: Se.map((e => { const t = e,
                                                    n = u.lI[t],
                                                    r = u.HF[t]; return n ? (0, p.jsx)(D.A, { selected: ae[t], variant: "outlined", color: r, size: "large", label: n, onClick: (a = t, () => { oe((e => ({ ...e, [a]: !e[a] }))) }) }, t) : null; var a })) })] }), Me && (0, p.jsx)(a.A, { display: "flex", justifyContent: "flex-start", children: (0, p.jsx)(o.A, { variant: g.Eq.caption, color: x.Qs.Neutrals[800], sx: { textDecoration: "underline" }, onClick: () => { oe(Y) }, children: "Clear all filters" }) })] })] }), (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", position: "relative", mt: (0, R.A)(58), style: { display: "flex", flexDirection: "column", position: "relative" }, children: [(0, p.jsx)(b, { rowType: s.Parent, handleNextClick: () => { k(1), v(Math.min(f + 1, ge - 1)) }, handlePrevClick: () => { k(-1), v(Math.max(f - 1, 0)) }, entityLength: ge, handleAddEntityClick: Ee(F.zZ.Owner), secView: h, isFilterApplied: Me, isMinOwner: de, children: ve.owners.map(((e, t) => { return (0, p.jsx)(T, { secView: h, entity: e, idx: t, activeIdx: f, direction: A, onClick: (n = t, () => { n !== f && (k(n < f ? -1 : 1), v(n)) }), onTransitionEnd: ze, cardType: s.Parent, handleChangeActiveOrgClick: we, handleInfoClick: t => { t.preventDefault(), t.stopPropagation(), he({ org: e, showEdit: !h && ue, editOrgOnly: !de, showDelete: !h && de, relationType: F.zZ.Owner, primaryOrgId: le.id }) } }, "parent-card-".concat(t, "-").concat(pe)); var n })) }), (0, p.jsx)(a.A, { display: "flex", justifyContent: "center", children: (0, p.jsx)(T, { entity: le, secView: h, idx: 0, activeIdx: 0, direction: 1, onClick: () => {}, cardType: s.Main, onTransitionEnd: ze, handleInfoClick: e => { e.preventDefault(), e.stopPropagation(), he({ org: le, showEdit: !h && ue, showDelete: !1 }) } }, null === le || void 0 === le ? void 0 : le.id) }), (0, p.jsx)(b, { rowType: s.Child, handlePrevClick: () => { _.A.trackEvent({ eventName: "OWNERSHIP_CHART_NAVIGATE_BUTTON_CLICKED", extraParams: { action: "back" } }), M(-1), z(Math.max(y - 1, 0)) }, handleNextClick: () => { _.A.trackEvent({ eventName: "OWNERSHIP_CHART_NAVIGATE_BUTTON_CLICKED", extraParams: { action: "next" } }), M(1), z(Math.min(y + 1, ye - 1)) }, entityLength: ye, handleAddEntityClick: Ee(F.zZ.Subsidiary), secView: h, isFilterApplied: Me, isMinOwner: de, children: ve.subsidiaries.map(((e, t) => { return (0, p.jsx)(T, { entity: e, secView: h, idx: t, activeIdx: y, direction: S, onClick: (n = t, () => { n !== y && (M(n < y ? -1 : 1), z(n)) }), onTransitionEnd: ze, cardType: s.Child, handleChangeActiveOrgClick: we, handleInfoClick: t => { t.preventDefault(), t.stopPropagation(), he({ org: e, showEdit: !h && ue, editOrgOnly: !de, showDelete: !h && de, relationType: F.zZ.Subsidiary, primaryOrgId: le.id }) } }, "child-card-".concat(t, "-").concat(pe)); var n })) }), (0, p.jsx)(a.A, { style: { position: "absolute" }, width: "100%", height: "100%", children: (0, p.jsx)(V, { lines: ee, childrenLines: ne, activeLineOrigin: L, activeLineDestination: j, activeLineChildrenOrigin: X, activeLineChildrenDestination: Q }) })] })] }) } }, 49248: (e, t, n) => { "use strict";
                n.d(t, { E7: () => i, Fy: () => c, HF: () => u, ZM: () => s, Zz: () => l, lI: () => d, zh: () => o }); var r = n(37294),
                    a = n(72952); const o = 329,
                    i = 142,
                    l = 3,
                    s = 50,
                    c = 24,
                    d = {
                        [a.ck.IN]: "Individual", [a.ck.CO]: "Corporation", [a.ck.LLC]: "Limited Liability Company", [a.ck.PN]: "Partnership", [a.ck.LP]: "Limited Partnership", [a.ck.LLP]: "Limited Liability Partnership", [a.ck.TR]: "Trust", [a.ck.OO]: "Other" },
                    u = {
                        [a.ck.CO]: r.o3.Teal, [a.ck.IN]: r.o3.GrassGreen, [a.ck.PN]: r.o3.Black, [a.ck.LLP]: r.o3.DarkPink, [a.ck.LLC]: r.o3.DarkViolet, [a.ck.OO]: r.o3.DarkPurple, [a.ck.LP]: r.o3.BrightRed, [a.ck.TR]: r.o3.DarkPurple } }, 60350: (e, t, n) => { "use strict";
                n.d(t, { K: () => g }); var r, a = n(57528),
                    o = n(34535),
                    i = n(17392),
                    l = n(77739),
                    s = n(96446),
                    c = n(85865),
                    d = n(37294),
                    u = n(32115),
                    h = n(69384),
                    m = n(20965),
                    p = n(63296),
                    f = n(70579); const v = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  border-radius: ", ";\n"])), (0, m.A)(4)),
                    g = e => { let { org: t } = e; const { gotoCharts: n } = (0, p.A)(); return (0, f.jsx)(l.A, { title: "Go to Charts", children: (0, f.jsx)(v, { onClick: n(t), children: (0, f.jsx)(s.A, { display: "flex", gap: 2, children: h.XU.map((e => { var n; return (0, f.jsxs)(s.A, { display: "flex", gap: 1, alignItems: "center", children: [(0, f.jsx)("img", { src: e.icon, alt: "".concat(e.name, " charts") }), (0, f.jsx)(c.A, { color: d.Qs.Neutrals[600], variant: u.Eq.labelLG, children: (null === t || void 0 === t || null === (n = t[null === e || void 0 === e ? void 0 : e.name]) || void 0 === n ? void 0 : n.toString()) || "0" })] }, "".concat(e.name, "-").concat(e.icon)) })) }) }) }) } }, 99713: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r = n(14556),
                    a = n(66588),
                    o = n(43331),
                    i = n(38355),
                    l = n(65043),
                    s = n(40245),
                    c = n(66856);

                function d() { const e = (0, r.wA)(),
                        { show: t } = (0, a.A)(),
                        [n, d] = (0, l.useState)(!1),
                        u = async () => { d(!0); const t = await e(s.VL.getAllRelations({ ignoreErrorHandler: !0 })); return d(!1), t }; return { loading: n, setLoading: d, getAllOrgs: async () => { d(!0); const t = await e(s.VL.getOrgsStats({})); return d(!1), t }, getOrgRelations: async t => { d(!0); const n = await e(s.VL.getOrgRelations({ orgId: t, ignoreErrorHandler: !0 })); return d(!1), n }, getAllRelations: u, createEntity: async (n, r) => { var a, i, l, s, c, u, h, m, p, f, v, g; const { name: y, logo: b, website: w, description: z, entityType: x, hierarchyLevel: A, entityPurpose: k, ...S } = r || {}, M = { name: y.trim(), logo: b, website: w, description: z, ownershipDetails: { ...S, hierarchyLevel: (null === (a = Array.isArray(A) ? A[0] : A) || void 0 === a ? void 0 : a.toLowerCase()) || null, entityPurpose: (null === (i = Array.isArray(k) ? k[0] : k) || void 0 === i ? void 0 : i.toLowerCase()) || null, entityType: "string" === typeof x ? null === x || void 0 === x ? void 0 : x.split(",") : x, location: { nickname: null === r || void 0 === r || null === (l = r.location) || void 0 === l ? void 0 : l.nickname, address: { line1: null === r || void 0 === r || null === (s = r.location) || void 0 === s ? void 0 : s.line1, line2: null === r || void 0 === r || null === (c = r.location) || void 0 === c ? void 0 : c.line2, city: null === r || void 0 === r || null === (u = r.location) || void 0 === u ? void 0 : u.city, country: null === r || void 0 === r || null === (h = r.location) || void 0 === h ? void 0 : h.country, state: null === r || void 0 === r || null === (m = r.location) || void 0 === m ? void 0 : m.state, zip: null === r || void 0 === r || null === (p = r.location) || void 0 === p ? void 0 : p.zip }, geo: null === r || void 0 === r || null === (f = r.location) || void 0 === f ? void 0 : f.geo, placeId: null === r || void 0 === r || null === (v = r.location) || void 0 === v ? void 0 : v.placeId, mapSearch: null === r || void 0 === r || null === (g = r.location) || void 0 === g ? void 0 : g.mapSearch } } };
                            d(!0); const { error: E, payload: C } = await e(o.no.create({ data: M, licenseId: n })); return d(!1), E ? (t("Could not create organization. Contact Support for more details", "error"), {}) : { error: E, payload: C } }, updateEntity: async (n, r) => { var a, i, l, s, c, u, h, m, p, f, v, g; if (!n) return {}; const { name: y, logo: b, website: w, description: z, entityType: x, hierarchyLevel: A, entityPurpose: k, ...S } = r || {}, M = { name: y.trim(), logo: b, website: w, description: z, ownershipDetails: { ...S, hierarchyLevel: (null === (a = Array.isArray(A) ? A[0] : A) || void 0 === a ? void 0 : a.toLowerCase()) || null, entityPurpose: (null === (i = Array.isArray(k) ? k[0] : k) || void 0 === i ? void 0 : i.toLowerCase()) || null, entityType: "string" === typeof x ? null === x || void 0 === x ? void 0 : x.split(",") : x, location: { nickname: null === r || void 0 === r || null === (l = r.location) || void 0 === l ? void 0 : l.nickname, address: { line1: null === r || void 0 === r || null === (s = r.location) || void 0 === s ? void 0 : s.line1, line2: null === r || void 0 === r || null === (c = r.location) || void 0 === c ? void 0 : c.line2, city: null === r || void 0 === r || null === (u = r.location) || void 0 === u ? void 0 : u.city, country: null === r || void 0 === r || null === (h = r.location) || void 0 === h ? void 0 : h.country, state: null === r || void 0 === r || null === (m = r.location) || void 0 === m ? void 0 : m.state, zip: null === r || void 0 === r || null === (p = r.location) || void 0 === p ? void 0 : p.zip }, geo: null === r || void 0 === r || null === (f = r.location) || void 0 === f ? void 0 : f.geo, placeId: null === r || void 0 === r || null === (v = r.location) || void 0 === v ? void 0 : v.placeId, mapSearch: null === r || void 0 === r || null === (g = r.location) || void 0 === g ? void 0 : g.mapSearch } } };
                            d(!0); const { error: E, payload: C } = await e(o.no.update({ data: M, orgId: n })); return d(!1), E ? (t("Could not edit the organization. Contact Support for more details", "error"), {}) : { error: E, payload: C } }, upsertRelation: async n => { d(!0); const { payload: r, error: a } = await e(s.VL.upsertOrgRelation({ orgId: n.orgId || "", data: n, ignoreErrorHandler: !0 }));
                            d(!1); const o = n.type === i.zZ.Owner ? "Owner" : "Subsidiary"; if (!a) return t("".concat(o, " added successfully."), "success"), void await u(); "error" in r && null !== r && void 0 !== r && r.error && "string" === typeof(null === r || void 0 === r ? void 0 : r.error) ? t(r.error, "error") : t("Something went wrong", "error") }, deleteRelation: async (n, r, a) => { d(!0); const { error: o, payload: i } = await e(s.VL.deleteOrgRelation({ orgId: n, relatedOrgId: r, type: a })); return d(!1), o ? (t("Could not delete. Contact Support for more details", "error"), {}) : (await u(), { error: o, payload: i }) }, importSECOwnership: async n => { var r;
                            d(!0); const a = await e(s.VL.importSECOwnership({ entity: n, ignoreErrorHandler: !0 }));
                            d(!1), null !== a && void 0 !== a && a.error ? t("Something went wrong.", "error") : t("Chart imported successfully.", "success"); const o = null === (r = a.payload) || void 0 === r ? void 0 : r.orgId; return o && (window.location.href = (0, c.wi)({ orgId: o, base: "protected", view: "chart" })), a }, importOwnership: async n => { d(!0); const r = await e(s.VL.importOwnership({ data: n, ignoreErrorHandler: !0 })); return d(!1), null !== r && void 0 !== r && r.error ? t("Something went wrong.", "error") : (t("Chart imported successfully.", "success"), window.location.href = (0, c.YQ)()), r }, searchExternalOrg: async n => { d(!0); const r = await e(s.VL.searchExternalOrg({ search: n, ignoreErrorHandler: !0 })); return d(!1), null !== r && void 0 !== r && r.error && t("Something went wrong.", "error"), r }, searchExternalRelations: async n => { d(!0), e((0, s.HO)(n)); const r = await e(s.VL.searchExternalRelations({ cik: n.cik, ignoreErrorHandler: !0 })); return d(!1), null !== r && void 0 !== r && r.error && t("Something went wrong.", "error"), r } } } }, 97184: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(84);

                function a() { const { openDialog: e, closeDialog: t } = (0, r.A)("organizationMeta"), { openDialog: n, closeDialog: a } = (0, r.A)("orgRelationDialog"), { closeDialog: o, openDialog: i } = (0, r.A)("orgEntity"), { closeDialog: l, openDialog: s } = (0, r.A)("orgEntityInfo"), { openDialog: c } = (0, r.A)("deleteOrganization"), { openDialog: d, closeDialog: u } = (0, r.A)("newOwnershipChart"), { openDialog: h, closeDialog: m } = (0, r.A)("secImport"), { openDialog: p, closeDialog: f } = (0, r.A)("ownershipImportFile"), { openDialog: v, closeDialog: g } = (0, r.A)("ownershipManualEntry"); return { openOrgMetaDialog: e, openRelationsDialog: n, openOrgEntityDialog: i, openOrgEntityInfoDialog: s, openDeleteOrgDialog: c, openCreateOwnershipChartDialog: d, openSECImportDialog: h, openImportsDialog: p, openManualEntryDialog: v, closeOrgMetaDialog: t, closeRelationsDialog: a, closeOrgEntityDialog: o, closeOrgEntityInfoDialog: l, closeCreateOwnershipDialog: u, closeSECImportDialog: m, closeImportsDialog: f, closeManualEntryDialog: g } } }, 63296: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r = n(14556),
                    a = n(42006),
                    o = n(66856),
                    i = n(48853),
                    l = n(78396),
                    s = n(356),
                    c = n(91688);

                function d() { var e; const t = (0, c.useHistory)(),
                        { userHasMinAccess: n } = (0, i.A)(),
                        d = n(l.td.OWNER),
                        u = n(l.td.ADMIN),
                        h = (0, r.d4)(a.VF),
                        m = null === h || void 0 === h || null === (e = h.featureSet) || void 0 === e ? void 0 : e.ownershipCharts,
                        p = e => n => { n && (null === n || void 0 === n || n.stopPropagation()), n && (null === n || void 0 === n || n.preventDefault()), t.push((0, o.r2)({ orgId: e.id })) },
                        f = e => n => { n && (null === n || void 0 === n || n.stopPropagation()), n && (null === n || void 0 === n || n.preventDefault()), t.push((0, o.wi)({ orgId: e.id, view: "builder", base: "protected" })) }; return { isMinOwner: d, isMinAdmin: u, isOwnershipEnabled: m, onOrgSelect: e => t => { t && (null === t || void 0 === t || t.stopPropagation()), t && (null === t || void 0 === t || t.preventDefault()), s.A.trackEvent({ eventName: "ORGS_DASHBOARD_ORG_SELECTED", extraParams: { isOwnershipEnabled: m, orgId: e.id } }), m ? f(e)(null) : p(e)(null) }, gotoTalentPool: e => n => { n && (null === n || void 0 === n || n.stopPropagation()), n && (null === n || void 0 === n || n.preventDefault()), t.push((0, o.K7)({ orgId: e.id, resource: "people" })) }, gotoCharts: p, gotoOwnershipChart: f } } }, 25197: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r = n(65043),
                    a = n(43845),
                    o = n(85865),
                    i = n(37294),
                    l = n(75156),
                    s = n(32115),
                    c = n(70579); const d = e => { let { selected: t, color: n, label: d, ...u } = e; const h = "".concat(t ? n : "transparent", " !important"),
                        m = "".concat(t ? i.Qs.Neutrals[0] : i.Qs.Neutrals[800], " !important"),
                        p = t ? i.Qs.Neutrals[0] : n; return (0, r.createElement)(a.A, { ...u, sx: { backgroundColor: h, color: m, "&:hover": { backgroundColor: n, color: m }, alignItems: "baseline" }, icon: (0, c.jsx)(l.gF, { icon: "Circle", size: "xxs", color: p }), label: (0, c.jsx)(o.A, { color: "inherit", variant: s.Eq.caption, children: d }), key: "chip-".concat(p, "-").concat(m, "-").concat(h) }) } }, 14429: (e, t, n) => { "use strict";
                n.d(t, { A: () => l });
                n(65043); var r = n(20965),
                    a = n(23546),
                    o = n(66779),
                    i = n(70579); const l = e => { let { children: t } = e; return (0, i.jsx)(a.N, { children: (0, i.jsx)(o.P.div, { initial: { opacity: 0 }, animate: { opacity: 1, transition: { duration: .5 } }, exit: { opacity: 0, transition: { duration: .5 } }, style: { marginTop: (0, r.A)(38), display: "flex", flexDirection: "column", height: "100%", overflowY: "auto", paddingLeft: (0, r.A)(20), paddingRight: (0, r.A)(30) }, children: t }) }) } }, 69384: (e, t, n) => { "use strict";
                n.d(t, { XU: () => h, SY: () => f, WM: () => g, Ou: () => p, Fg: () => v, fc: () => m, Z_: () => b, GX: () => y, h2: () => S, a1: () => z, su: () => w, iW: () => F, ly: () => H, s: () => L, NL: () => P, Pi: () => O, Hv: () => V, $6: () => R, jb: () => N, JK: () => E, o5: () => k, XQ: () => _, Wd: () => A, ex: () => C, jS: () => M, kE: () => T, US: () => D, le: () => I, AU: () => j });
                n(65043); const r = n.p + "static/media/traditional.3def2d0d141522ee65226ba42da48177.svg"; const a = n.p + "static/media/matrix.545841f3f33b0a28dee9d66327e47eea.svg"; const o = n.p + "static/media/bod.caf1c1bd3a151fbfb0c12b90087f4bb3.svg"; var i, l = n(82715),
                    s = n(72952),
                    c = n(32115),
                    d = n(47088),
                    u = n(37294); const h = [{ name: "traditional", icon: r }, { name: "matrix", icon: a }, { name: "bod", icon: o }],
                    m = null === (i = Object.values(l.PC)) || void 0 === i ? void 0 : i.filter((e => isNaN(Number(e)))),
                    p = 330,
                    f = 143,
                    v = 2560,
                    g = 35,
                    y = "owner",
                    b = "owners",
                    w = "owned entity",
                    z = "owned entities",
                    x = { logo: l.PC.Logo, stake: l.PC.Stake, name: l.PC.Basics, [l.uv.LOCATION]: l.PC.HQ, [l.uv.INCORPORATED]: l.PC.Basics, [l.uv.JURISDICTION]: l.PC.Basics, [l.uv.DESCRIPTION]: l.PC.Basics, [l.uv.WEBSITE]: l.PC.Basics, [l.uv.INDUSTRY]: l.PC.Basics, [l.uv.CATEGORY]: l.PC.Basics, [l.uv.SECTOR]: l.PC.Basics, [l.uv.ENTITYTYPE]: l.PC.Basics, [l.uv.EXCHANGE]: l.PC.Financial, [l.uv.CURRENCY]: l.PC.Financial, [l.uv.TICKER]: l.PC.Financial, [l.uv.CIK]: l.PC.OrgStructure, [l.uv.CUSIP]: l.PC.OrgStructure, [l.uv.SIC]: l.PC.OrgStructure, [l.uv.SICSECTOR]: l.PC.OrgStructure, [l.uv.SICINDUSTRY]: l.PC.OrgStructure, [l.uv.FAMASECTOR]: l.PC.OrgStructure, [l.uv.FAMAINDUSTRY]: l.PC.OrgStructure, [l.uv.DUNS]: l.PC.OrgStructure, [l.uv.HIERARCHYLEVEL]: l.PC.Basics, [l.uv.ENTITYPURPOSE]: l.PC.Basics, [l.uv.CARDSIZE]: l.PC.Basics, [l.uv.CARDBGCOLOR]: l.PC.Basics },
                    A = ["logo", "id", "orgId", "ownershipDetails", "locationString", "secId", "fields", "__v", "license", "user", "accessLevel", "highestAccessLevel", "shareAccess", "theme", "hasRelations"],
                    k = ["incorporated", "createdAt", "updatedAt", "created_at", "updated_at"]; let S = function(e) { return e.ID = "Id", e.LOGO = "Logo", e.NAME = "Name", e.LOCATION = "Location", e.LOCATIONCOUNTRY = "Country", e.TOTALCHARTS = "Total Org Charts", e.ENTITYTYPE = "Entity Type", e.INCORPORATEDYEAR = "Incorporated Year", e.WEBSITE = "Website", e.DESCRIPTION = "Description", e.INCORPORATED = "Incorporated in", e.JURISDICTION = "Jurisdiction", e.EXCHANGE = "Stock Exchange", e.TICKER = "Ticker symbol", e.CIK = "CIK", e.CUSIP = "CUSIP", e.ISDELISTED = "Is delisted from SEC", e.CATEGORY = "Category", e.SECTOR = "Sector", e.INDUSTRY = "Industry", e.SIC = "SIC", e.SICSECTOR = "SIC Sector", e.SICINDUSTRY = "SIC Industry", e.FAMASECTOR = "FAMA Sector", e.FAMAINDUSTRY = "FAMA Industry", e.CURRENCY = "Currency", e.DUNS = "DUNS Number", e.STAKE = "Stake", e.TRADITIONAL = "Traditional Charts", e.MATRIX = "Matrix Charts", e.BOD = "Board of Directors Charts", e.PEOPLECOUNT = "People Count", e.OWNERS = "owners Count", e.SUBSIDIARIES = "owned entities Count", e.STOCK = "Stock", e.UPDATEDAT = "Updated At", e.CREATEDAT = "Created At", e.HIERARCHYLEVEL = "Hierarchy Level", e.ENTITYPURPOSE = "Entity Purpose", e.CARDSIZE = "Card Size", e.CARDBGCOLOR = "Card Color", e.SOLEVOTINGPOWER = "Sole Voting Power", e.SHAREDVOTINGPOWER = "Shared Voting Power", e.SOLEDISPOSITIVEPOWER = "Sole Dispositive Power", e.SHAREDDISPOSITIVEPOWER = "Shared Dispositive Power", e.AGGREGATEAMOUNTOWNED = "Aggregate Amount Owned", e.AMOUNTEXCLUDESCERTAINSHARES = "Amount Excludes Certain Shares", e }({}); const M = [{ id: l.uv.JURISDICTION, name: l.uv.JURISDICTION, label: S.JURISDICTION, type: d.yw.String, isDefault: !0 }, { id: l.uv.LOCATION, name: l.uv.LOCATION, label: S.LOCATION, type: d.yw.Location, isDefault: !0 }, { id: l.uv.EXCHANGE, name: l.uv.EXCHANGE, label: S.EXCHANGE, type: d.yw.String, isDefault: !0 }, { id: l.uv.TICKER, name: l.uv.TICKER, label: S.TICKER, type: d.yw.String, isDefault: !0 }, { id: l.uv.ENTITYTYPE, name: l.uv.ENTITYTYPE, label: S.ENTITYTYPE, type: d.yw.Tags, isDefault: !0, typeMetadata: { tags: { choices: Object.values(s.ck), multiple: !0, isRestricted: !0 } } }, { id: l.uv.INCORPORATED, name: l.uv.INCORPORATED, label: S.INCORPORATED, type: d.yw.Date, isDefault: !0 }, { id: l.uv.CIK, name: l.uv.CIK, label: S.CIK, type: d.yw.String, isDefault: !0 }, { id: l.uv.CUSIP, name: l.uv.CUSIP, label: S.CUSIP, type: d.yw.String, isDefault: !0 }, { id: l.uv.ISDELISTED, name: l.uv.ISDELISTED, label: S.ISDELISTED, type: d.yw.Boolean, isDefault: !0 }, { id: l.uv.CATEGORY, name: l.uv.CATEGORY, label: S.CATEGORY, type: d.yw.String, isDefault: !0 }, { id: l.uv.SECTOR, name: l.uv.SECTOR, label: S.SECTOR, type: d.yw.String, isDefault: !0 }, { id: l.uv.INDUSTRY, name: l.uv.INDUSTRY, label: S.INDUSTRY, type: d.yw.String, isDefault: !0 }, { id: l.uv.SIC, name: l.uv.SIC, label: S.SIC, type: d.yw.String, isDefault: !0 }, { id: l.uv.SICSECTOR, name: l.uv.SICSECTOR, label: S.SICSECTOR, type: d.yw.String, isDefault: !0 }, { id: l.uv.SICINDUSTRY, name: l.uv.SICINDUSTRY, label: S.SICINDUSTRY, type: d.yw.String, isDefault: !0 }, { id: l.uv.FAMASECTOR, name: l.uv.FAMASECTOR, label: S.FAMASECTOR, type: d.yw.String, isDefault: !0 }, { id: l.uv.FAMAINDUSTRY, name: l.uv.FAMAINDUSTRY, label: S.FAMAINDUSTRY, type: d.yw.String, isDefault: !0 }, { id: l.uv.DUNS, name: l.uv.DUNS, label: S.DUNS, type: d.yw.String, isDefault: !0 }, { id: l.uv.CURRENCY, name: l.uv.CURRENCY, label: S.CURRENCY, type: d.yw.String, isDefault: !0 }, { id: l.uv.HIERARCHYLEVEL, name: l.uv.HIERARCHYLEVEL, label: S.HIERARCHYLEVEL, type: d.yw.Tags, isDefault: !0, typeMetadata: { tags: { choices: Object.values(s.bV), multiple: !1, isRestricted: !0 } } }, { id: l.uv.ENTITYPURPOSE, name: l.uv.ENTITYPURPOSE, label: S.ENTITYPURPOSE, type: d.yw.Tags, isDefault: !0, typeMetadata: { tags: { choices: Object.values(s.UF), multiple: !1, isRestricted: !0 } } }, { id: l.uv.CARDSIZE, name: l.uv.CARDSIZE, label: S.CARDSIZE, type: d.yw.String, isDefault: !0, typeMetadata: { tags: { choices: Object.values(s.zb).filter((e => "dummyNodeSize" !== e)), multiple: !1, isRestricted: !0 } } }, { id: l.uv.CARDBGCOLOR, name: l.uv.CARDBGCOLOR, label: S.CARDBGCOLOR, type: d.yw.String, isDefault: !0 }],
                    E = [{ name: "logo", label: "" }, { name: "stake", label: "Ownership Stake" }, { name: "name", label: "Name" }, ...M].reduce(((e, t) => { const n = x[t.name]; return e[n] = e[n] || [], e[n].push(t), e }), {}),
                    C = [{ id: "id", name: "id", label: "ID", type: d.yw.String, isStructuredField: !0 }, { id: "parent", name: "parent", label: "Owner", type: d.yw.String, isStructuredField: !0 }, { id: "stake", name: "stake", label: "Stake %", type: d.yw.String, shrink: !0 }, { id: "name", name: "name", label: "Name", type: d.yw.String, canDisable: !0 }, ...M.filter((e => [l.uv.JURISDICTION, l.uv.ENTITYTYPE, l.uv.ENTITYPURPOSE].includes(e.name))).map((e => ({ ...e, canDisable: !0 })))],
                    T = C.map((e => ({ ...e, fieldId: e.name, model: "organization", selectedValue: "", selectedValueExample: "", hidden: e.hidden || !1, name: e.name, fieldType: (e.type || "string").toLowerCase(), id: e.id || e.name, required: !1, label: e.label || e.name[0] + e.name.toLowerCase().substring(1), chartType: "ownership" }))),
                    H = 30,
                    L = 80,
                    I = 25,
                    j = 40,
                    V = s.zb.Small,
                    O = "#ffffff",
                    R = "#919191",
                    P = 10,
                    D = { dummyNodeSize: { width: 10, height: 120, chipSize: "medium", logoHeight: 14, logoWidth: 20, fontVariant: c.Eq.caption, headingVariant: c.Eq.subheadingXS, addButtonSize: "lg" }, s: { height: 120, width: 240 + P, chipSize: "medium", logoHeight: 14, logoWidth: 20, fontVariant: c.Eq.caption, headingVariant: c.Eq.subheadingXS, addButtonSize: "lg" }, m: { height: 160, width: 330 + P, chipSize: "large", logoHeight: 17.5, logoWidth: 25, fontVariant: c.Eq.bodySM, headingVariant: c.Eq.subheadingSM, addButtonSize: "lg" }, l: { height: 190, width: 400 + P, chipSize: "large", logoHeight: 25, logoWidth: 35, fontVariant: c.Eq.bodyMD, headingVariant: c.Eq.subheadingXL, addButtonSize: "lg" }, xl: { height: 240, width: 400 + P, chipSize: "large", logoHeight: 30, logoWidth: 40, fontVariant: c.Eq.bodyLG, headingVariant: c.Eq.h1, addButtonSize: "lg" } },
                    F = { invertStroke: u.Qs.Neutrals[0], stroke: u.Qs.Info[500], strokeWidth: 2, fill: u.Qs.Neutrals[0], invertFill: u.Qs.Info[600], arrowHeight: 10, arrowWidth: 10 },
                    N = { stroke: R, strokeWidth: 1, fill: u.Qs.Neutrals[0], invertFill: R, arrowHeight: 10, arrowWidth: 10 },
                    _ = ["name", "entityType", "entityPurpose", "jurisdiction", "incorporatedYear", "createdAt", "updatedAt", "hierarchyLevel", "owners", "subsidiaries", "hasRelations", "peopleCount", "totalCharts", "traditional", "bod", "matrix", "cardSize", "cardBgColor"] }, 82715: (e, t, n) => { "use strict";
                n.d(t, { By: () => c, D2: () => a, JL: () => i, PC: () => l, Z6: () => s, aL: () => d, uv: () => o }); var r = n(6803); const a = e => (0, r.A)(e.replace(/([a-z])([A-Z])/g, "$1 $2")); let o = function(e) { return e.WEBSITE = "website", e.DESCRIPTION = "description", e.ENTITYTYPE = "entityType", e.INCORPORATED = "incorporated", e.JURISDICTION = "jurisdiction", e.EXCHANGE = "exchange", e.TICKER = "ticker", e.LOCATION = "location", e.CIK = "cik", e.CUSIP = "cusip", e.ISDELISTED = "isDelisted", e.CATEGORY = "category", e.SECTOR = "sector", e.INDUSTRY = "industry", e.SIC = "sic", e.SICSECTOR = "sicSector", e.SICINDUSTRY = "sicIndustry", e.FAMASECTOR = "famaSector", e.FAMAINDUSTRY = "famaIndustry", e.DUNS = "duns", e.CURRENCY = "currency", e.HIERARCHYLEVEL = "hierarchyLevel", e.ENTITYPURPOSE = "entityPurpose", e.CARDSIZE = "cardSize", e.CARDBGCOLOR = "cardBgColor", e }({}),
                    i = function(e) { return e.Owners = "owners", e.Subsidiaries = "subsidiaries", e }({}),
                    l = function(e) { return e.Logo = "Logo", e.Stake = "Ownership Stake", e.Basics = "Basics", e.HQ = "Headquarter Location", e.OrgStructure = "Organization Structure", e.Financial = "Financial", e }({}),
                    s = function(e) { return e.Card = "card", e.Connection = "connection", e.HiddenLeaf = "hiddenLeaf", e }({}),
                    c = function(e) { return e.DuplicateId = "duplicate unique id found", e.DuplicateName = "duplicate organization name found", e.MissingField = "some fields are required", e.MissingRoot = "missing a top role in chart", e.Loop = "looping reporting structure found", e.InvalidParent = "invalid owner id found", e }({}); const d = Object.keys(c) }, 40245: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => d, HO: () => l, VL: () => o, ke: () => c, nF: () => s }); var r = n(80907); const a = "ownership",
                    o = (0, n(47730).B)({ slice: a, scope: "ownership" }),
                    i = (0, r.Z0)({ name: a, initialState: { orgEntities: [], allRelations: [], externalEntity: null, externalEntityRelations: null, screen: { zoom: 1 } }, reducers: { zoomIn: e => { e.screen.zoom = Math.min(e.screen.zoom + .1, 2) }, zoomOut: e => { e.screen.zoom = Math.max(e.screen.zoom - .1, .5) }, "getOrgsStats/fulfilled": (e, t) => { var n;
                                e.orgEntities = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.organizations) || [] }, "searchExternalRelations/fulfilled": (e, t) => { e.externalEntityRelations = (null === t || void 0 === t ? void 0 : t.payload) || null }, "getAllRelations/fulfilled": (e, t) => { var n;
                                e.allRelations = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.relations) || [] }, setExternalEntity: (e, t) => { e.externalEntity = null === t || void 0 === t ? void 0 : t.payload } }, extraReducers: { "organization/update/fulfilled": (e, t) => { let { payload: n } = t; const r = n.organization; if (null === r || void 0 === r || !r.id) return; const a = e.orgEntities.findIndex((e => e.id === r.id)); if (a > -1) { const t = { name: r.name, ...(null === r || void 0 === r ? void 0 : r.ownershipDetails) || {} };
                                    e.orgEntities[a] = { ...e.orgEntities[a], ...t } } else; }, "organization/create/fulfilled": (e, t) => { let { payload: n } = t; const r = n.organization,
                                    a = { ...r, ...(null === r || void 0 === r ? void 0 : r.ownershipDetails) || {} };
                                a && e.orgEntities.push(a) }, "organization/delete/fulfilled": (e, t) => { var n, r; const a = (null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.orgId) || null;
                                a && (e.orgEntities = e.orgEntities.filter((e => e.id !== a))) }, "organization/remove/fulfilled": (e, t) => { var n, r; const a = (null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.orgId) || null;
                                a && (e.orgEntities = e.orgEntities.filter((e => e.id !== a)), e.allRelations = e.allRelations.filter((e => e.organization !== a && e.parentOrganization !== a))) } } }),
                    { setExternalEntity: l, zoomIn: s, zoomOut: c } = i.actions,
                    d = i.reducer }, 50543: (e, t, n) => { "use strict";
                n.d(t, { VF: () => d.A, mI: () => m, Jb: () => x, zQ: () => w, SO: () => A, OP: () => z, Q9: () => M, hj: () => S, Z_: () => E, a2: () => b, hm: () => y, Xy: () => g, $q: () => f, U3: () => h, KS: () => H, s1: () => v, SN: () => k, ng: () => p, EU: () => T, N8: () => C }); var r = n(82715),
                    a = n(80192),
                    o = n(24241),
                    i = n(72952),
                    l = n(69384); var s = n(78226),
                    c = n(6803),
                    d = n(15622); const u = (e, t) => { const n = { ...e },
                            r = { ...(null === t || void 0 === t ? void 0 : t.sec) || {} };
                        delete r.name; const a = { ...t, ...r }; let l = null; if ("incorporated" in n && n.incorporated) { const e = o.c9.fromISO(n.incorporated);
                            l = e && e.isValid ? e.year : null } let s = "",
                            d = ""; var u, h, m, p; "location" in n && ("string" === typeof n.location ? s = n.location : (d = (null === n || void 0 === n || null === (u = n.location) || void 0 === u ? void 0 : u.country) || "", s = (null === n || void 0 === n || null === (h = n.location) || void 0 === h ? void 0 : h.nickname) || d || (null === n || void 0 === n || null === (m = n.location) || void 0 === m ? void 0 : m.city) || (null === n || void 0 === n || null === (p = n.location) || void 0 === p ? void 0 : p.line1) || "")); let f = 0; "traditional" in n && (f = ((null === n || void 0 === n ? void 0 : n.traditional) || 0) + ((null === n || void 0 === n ? void 0 : n.matrix) || 0) + ((null === n || void 0 === n ? void 0 : n.bod) || 0)); let v = []; "entityType" in n && (v = (null === n || void 0 === n ? void 0 : n.entityType) || []); let g = null !== t && void 0 !== t && t.stake ? null === t || void 0 === t ? void 0 : t.stake : "stake" in n ? n.stake : "amountAsPercent" in n ? n.amountAsPercent : void 0;
                        g && (g = Math.round(100 * g) / 100); const y = { ...a, ...n, stake: g, totalCharts: f, entityType: v, incorporatedYear: l, locationString: s, locationCountry: d }; return y.hierarchyLevel = y.hierarchyLevel ? (0, c.A)(y.hierarchyLevel) : i.bV.Primary, y.hasRelations = !(!y.subsidiaries && !y.owners), y },
                    h = e => { var t; return ((null === e || void 0 === e || null === (t = e.ownership) || void 0 === t ? void 0 : t.orgEntities) || []).map((e => u(e))) },
                    m = e => e.ownership.allRelations,
                    p = (0, a.Mz)(h, (e => e.reduce(((e, t, n) => (t.id && (e[t.id] = (n + 1).toString()), e)), {}))),
                    f = (0, a.Mz)(h, (e => e.reduce(((e, t) => (null !== t && void 0 !== t && t.id && (e[t.id] = t), e)), {}))),
                    v = (0, a.Mz)(f, m, ((e, t) => ((e, t) => { const n = {}; for (const r of Object.values(e)) { if (null === r || void 0 === r || !r.id) continue;
                            n[r.id] = { children: [], parents: [], primaryParent: null }, n[r.id].children = t.filter((e => e.parentOrganization === r.id)).map((e => ({ id: e.organization, stake: e.stake, sec: e.sec }))), n[r.id].parents = t.filter((e => e.organization === r.id)).map((e => ({ id: e.parentOrganization, stake: e.stake, sec: e.sec }))); const e = n[r.id].parents.length;
                            1 === e && (n[r.id].primaryParent = n[r.id].parents[0]), e > 1 && (n[r.id].primaryParent = n[r.id].parents.reduce(((e, t) => t.stake > e.stake ? t : e), { id: "", stake: 0 })) } return n })(e, t))),
                    g = (0, a.Mz)(f, v, s.B, ((e, t, n) => { const a = n.id,
                            o = structuredClone(t),
                            i = [],
                            s = {},
                            c = {}; let d = -1,
                            u = a; const h = [];! function e(t, n) { const r = null === (n = o[t]) || void 0 === n ? void 0 : n.primaryParent; return !r || h.includes(r.id) ? null : (h.push(null === r || void 0 === r ? void 0 : r.id), u = r.id, e(u)) }(a); const m = [{ id: u }],
                            p = [...m]; for (; p.length > 0;) { const t = p.length,
                                n = [];
                            d++, c[d] = c[d] || {}; for (let a = 0; a < t; a++) { var f, v; const t = p.shift(); if (null === t || void 0 === t || !t.id) continue; const a = e[t.id];
                                n.push({ name: null === (f = e[t.id]) || void 0 === f ? void 0 : f.name, id: t.id }), s[t.id] = { id: t.id, level: d, position: n.length - 1, x: 0, y: 0, width: 0, height: 0, recursiveWidth: 0, nodeType: r.Z6.Card }, c[d].maxHeight = Math.max(c[d].maxHeight || 0, l.US[(null === a || void 0 === a ? void 0 : a.cardSize) || "s"].height || 0); const i = ((null === (v = o[t.id]) || void 0 === v ? void 0 : v.children) || []).filter((e => { var n, r; return (null === (n = o[e.id]) || void 0 === n || null === (r = n.primaryParent) || void 0 === r ? void 0 : r.id) === t.id }));
                                p.push(...i) } i.push(n) } return ((e, t, n, r, a) => { let o = l.ly; const i = (n, r, a, o, s, c) => { var d, u; const h = e[n.id],
                                    m = l.US[(null === h || void 0 === h ? void 0 : h.cardSize) || l.Hv].width,
                                    p = l.US[(null === h || void 0 === h ? void 0 : h.cardSize) || l.Hv].height;
                                s[o] = s[o] || {}; const f = s[o].maxHeight || p;
                                c[n.id] = { ...c[n.id], id: n.id, x: r, y: a, width: m, height: p, recursiveWidth: 0 }; let v = 0; const g = (null === t || void 0 === t || null === (d = t[n.id]) || void 0 === d || null === (u = d.children) || void 0 === u ? void 0 : u.sort(((e, t) => { var n, r; return ((null === (n = c[e.id]) || void 0 === n ? void 0 : n.position) || 0) - ((null === (r = c[t.id]) || void 0 === r ? void 0 : r.position) || 0) }))) || []; for (const e of g) { var y, b; if ((null === (y = t[e.id]) || void 0 === y || null === (b = y.primaryParent) || void 0 === b ? void 0 : b.id) !== n.id) continue; const a = c[n.id].y + f + 2 * l.AU,
                                        { recursiveWidth: d } = i(e, v + r, a, o + 1, s, c);
                                    v += d } return c[n.id].recursiveWidth += Math.max(v, m + 2 * l.le), c[n.id].x = r + c[n.id].recursiveWidth / 2 - (c[n.id].width + 2 * l.le) / 2, s[o].maxHeight = Math.max(f, p), { recursiveWidth: c[n.id].recursiveWidth } }; for (const s of n) { const { recursiveWidth: e } = i(s, o, l.s, 0, r, a);
                                o += e } })(e, o, m, c, s), ((e, t, n) => { const r = (t, n, a) => { var o, i; const l = a[t.id].recursiveWidth,
                                    s = (null === e || void 0 === e || null === (o = e[t.id]) || void 0 === o || null === (i = o.children) || void 0 === i ? void 0 : i.sort(((e, t) => { var n, r; return ((null === (n = a[e.id]) || void 0 === n ? void 0 : n.position) || 0) - ((null === (r = a[t.id]) || void 0 === r ? void 0 : r.position) || 0) }))) || [],
                                    c = null === s || void 0 === s ? void 0 : s.reduce(((n, r) => { var o, i; return n + ((null === (o = e[r.id]) || void 0 === o || null === (i = o.primaryParent) || void 0 === i ? void 0 : i.id) === t.id ? a[r.id].recursiveWidth : 0) }), 0),
                                    d = Math.max(l - c, 0); for (const m of s) { var u, h;
                                    (null === (u = e[m.id]) || void 0 === u || null === (h = u.primaryParent) || void 0 === h ? void 0 : h.id) === t.id && (a[m.id].x += n + d / 2, r(m, n + d / 2, a)) } }; for (const a of t) r(a, 0, n) })(o, m, s), ((e, t, n) => { const r = (e, n, r) => { var a, o; const i = (null === (a = t[e.id]) || void 0 === a ? void 0 : a[r]) || []; return 0 === i.length ? (null === (o = n[e.id]) || void 0 === o ? void 0 : o.position) || 0 : i.reduce(((e, t) => { var r; return e + ((null === (r = n[t.id]) || void 0 === r ? void 0 : r.position) || 0) }), 0) / i.length }; for (let a = 0; a < 5; a++) { for (let t = 1; t < e.length; t++) e[t].forEach((e => { e.barycenter = r(e, n, "parents") })), e[t].sort(((e, t) => (e.barycenter || 0) - (t.barycenter || 0))); for (let t = e.length - 2; t >= 0; t--) e[t].forEach((e => { e.barycenter = r(e, n, "children") })), e[t].sort(((e, t) => (e.barycenter || 0) - (t.barycenter || 0))) } for (const a of e)
                                for (const [e, t] of a.entries()) n[t.id].position = e })(i, o, s), { levels: i, levelMap: c, orgIdMap: e, nodeDims: s, leveledParentChildMap: o, roots: m } })),
                    y = (e, t) => (0, a.Mz)((e => e.ownership.externalEntity), f, ((n, r) => t ? n : r[e])),
                    b = e => (0, a.Mz)(f, v, ((t, n) => { var r, a; return { owners: ((null === (r = n[e]) || void 0 === r ? void 0 : r.parents) || []).map((e => ({ ...t[e.id], ...(null === e || void 0 === e ? void 0 : e.sec) || {}, stake: null === e || void 0 === e ? void 0 : e.stake }))).filter((e => !!e)), subsidiaries: ((null === (a = n[e]) || void 0 === a ? void 0 : a.children) || []).map((e => ({ ...t[e.id], ...(null === e || void 0 === e ? void 0 : e.sec) || {}, stake: null === e || void 0 === e ? void 0 : e.stake }))).filter((e => !!e)) } })),
                    w = e => (0, a.Mz)(b(e), (e => (null === e || void 0 === e ? void 0 : e.owners) || [])),
                    z = e => (0, a.Mz)(b(e), (e => (null === e || void 0 === e ? void 0 : e.subsidiaries) || [])),
                    x = e => (0, a.Mz)(w(e), z(e), ((e, t) => { const n = [...e, ...t].filter((e => !!e)); return Array.from(new Set(n.flatMap((e => e.entityType)).filter((e => !!e)))) })),
                    A = e => { let { pageNumber: t, orgId: n } = e; return (0, a.Mz)(b(n), k(n), ((e, n) => { let { pageMap: r } = n; const a = r[t]; return a && "orgInfo" !== (null === a || void 0 === a ? void 0 : a.display) ? e[a.display].slice(a.startIdx, a.endIdx) : [] })) },
                    k = e => (0, a.Mz)(b(e), (e => { const t = Math.ceil(e.owners.length / 7),
                            n = Math.ceil(e.subsidiaries.length / 7),
                            a = { 1: { display: "orgInfo", startIdx: 1, endIdx: 1 } }; for (let o = 1; o <= t; o++) a[o + 1] = { display: r.JL.Owners, startIdx: 7 * (o - 1), endIdx: Math.min(7 * o, e.owners.length) }; for (let o = 1; o <= n; o++) a[o + 1 + t] = { display: r.JL.Subsidiaries, startIdx: 7 * (o - 1), endIdx: Math.min(7 * o, e.subsidiaries.length) }; return { pageMap: a, lastPage: Object.keys(a).length } })),
                    S = (0, a.Mz)((e => e.ownership.externalEntityRelations), (e => ({ owners: ((null === e || void 0 === e ? void 0 : e.owners) || []).map((e => u(e))), subsidiaries: ((null === e || void 0 === e ? void 0 : e.subsidiaries) || []).map((e => u(e))) }))),
                    M = (0, a.Mz)(S, (e => (null === e || void 0 === e ? void 0 : e.owners) || [])),
                    E = (0, a.Mz)(S, (e => (null === e || void 0 === e ? void 0 : e.subsidiaries) || [])),
                    C = (0, a.Mz)(v, f, ((e, t) => { const n = Object.keys(t),
                            r = {}; for (const o of n) { var a; const t = (null === (a = e[o]) || void 0 === a ? void 0 : a.parents) || []; for (const e of t) { r["".concat(e.id, "-").concat(o)] = e.stake } } return r })),
                    T = (0, a.Mz)(g, v, ((e, t) => { let { nodeDims: n } = e; const r = {}; for (const o of Object.keys(n)) { var a; const e = n[o],
                                i = (null === (a = t[o]) || void 0 === a ? void 0 : a.parents) || [],
                                l = i.filter((e => !!n[e.id])).length,
                                s = Math.min(l, 3); for (const [t, n] of (i || []).entries()) { if (t > 2) continue; const a = n.id,
                                    i = "".concat(a, "-").concat(o),
                                    l = e.width / s,
                                    c = l * t + (e.width - (s - 1) * l) / 2;
                                r[i] = c } } return r })),
                    H = e => e.ownership.screen.zoom }, 84091: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => f, FK: () => s, OH: () => c, qu: () => m, rk: () => d }); var r = n(80907),
                    a = n(9787),
                    o = n(47730),
                    i = n(10621); const l = "people",
                    s = (0, r.pU)({ sortComparer: (e, t) => (0, i.US)(e).localeCompare((0, i.US)(t)) }),
                    c = (0, a.a)({ slice: l, scope: "people" }),
                    d = (0, o.B)({ slice: l, scope: "people" });

                function u(e) { return { ...(0, i.LS)((0, i.SB)(e)), name: e.name || (0, i.Bx)(e) } } const h = (0, r.Z0)({ name: l, initialState: s.getInitialState({ visiblePeople: [] }), reducers: { "getPeople/fulfilled": (e, t) => { let { payload: { people: n = [] } } = t;
                                s.setAll(e, n.map(u)) }, "getPeoplePaginated/fulfilled": (e, t) => { let { payload: { people: n = [] } } = t;
                                e.visiblePeople = n.map((e => e.id)), s.upsertMany(e, n.map(u)) }, "deletePeople/fulfilled": (e, t) => { let { payload: { people: n } } = t;
                                null !== n && void 0 !== n && n.length && (e.visiblePeople = e.visiblePeople.filter((e => -1 === n.findIndex((t => t.id === e)))), s.removeMany(e, n.map((e => e.id)))) }, "addPerson/fulfilled": (e, t) => { let { payload: { duplicatesFound: n, person: r } } = t;
                                n || (e.visiblePeople.unshift(r.id), s.addOne(e, u(r))) }, "updatePerson/fulfilled": (e, t) => { let { payload: { person: n } } = t;
                                n && (-1 === e.visiblePeople.indexOf(n.id) && e.visiblePeople.unshift(n.id), s.upsertOne(e, u(n))) }, loadSamplePeople: (e, t) => { let { payload: { people: n = [] } } = t;
                                s.upsertMany(e, n.map(u)) }, unloadSamplePeople: () => {}, "bulkUpdate/fulfilled": (e, t) => { let { payload: { members: n = [] } } = t;
                                s.upsertMany(e, n.map(u)) } }, extraReducers: { "search/selectSearchResult": (e, t) => { var n; let { payload: r } = t;
                                (null === r || void 0 === r || null === (n = r.people) || void 0 === n ? void 0 : n.length) > 0 && s.upsertMany(e, (r.people || []).map(u)) }, "chart/getChildrenRoles/fulfilled": (e, t) => { let { payload: { roles: n } } = t; const r = n.reduce(((e, t) => (e.push(...t.members || []), e)), []);
                                s.upsertMany(e, r.map(u)) }, "roles/update/fulfilled": (e, t) => { var n; let { payload: { members: r = [] }, meta: { arg: a } } = t; const o = (null === a || void 0 === a || null === (n = a.data) || void 0 === n ? void 0 : n.members) || [],
                                    i = r.map((e => { const t = o.find((t => (null === t || void 0 === t ? void 0 : t.id) === (null === e || void 0 === e ? void 0 : e.id))); return t ? { ...e, name: (null === t || void 0 === t ? void 0 : t.name) || "" } : e }));
                                s.upsertMany(e, i.map(u)) }, "roles/create/fulfilled": (e, t) => { var n; let { payload: { members: r = [] }, meta: { arg: a } } = t; const o = (null === a || void 0 === a || null === (n = a.data) || void 0 === n ? void 0 : n.members) || [],
                                    i = r.map((e => { const t = o.find((t => (null === t || void 0 === t ? void 0 : t.id) === (null === e || void 0 === e ? void 0 : e.id))); return t ? { ...e, name: (null === t || void 0 === t ? void 0 : t.name) || "" } : e }));
                                s.upsertMany(e, i.map(u)) }, "fields/deleteCustomField/fulfilled": (e, t) => { let { payload: n } = t; const r = null === n || void 0 === n ? void 0 : n.fields; if (!r.length) return; const a = s.getSelectors().selectAll(e).map((e => ({ ...e, fields: [...e.fields] })));
                                null !== a && void 0 !== a && a.length && (a.forEach((e => e.fields = e.fields.filter((e => !r.includes(e.id))))), s.setAll(e, a)) }, "cleanup/deleteUnassignedPeople/fulfilled": (e, t) => { let { payload: { memberIds: n } } = t;
                                null !== n && void 0 !== n && n.length && s.removeMany(e, n) } } }),
                    { loadSamplePeople: m, unloadSamplePeople: p } = h.actions,
                    f = h.reducer }, 15813: (e, t, n) => { "use strict";
                n.d(t, { A8: () => z, Bd: () => k, IT: () => v, J_: () => l, KO: () => p, N0: () => c, Nn: () => S, PA: () => m, Xp: () => x, _6: () => i, ek: () => y, iP: () => a, jy: () => A, lL: () => r, nH: () => o, or: () => u, q2: () => g, t4: () => b, ti: () => s, xq: () => f, yQ: () => d, yX: () => h, z7: () => w }); const r = 36,
                    a = 310,
                    o = 50; let i = function(e) { return e.CHART = "chart", e.DIRECTORY = "directory", e.PHOTOBOARD = "photoboard", e }({}),
                    l = function(e) { return e.FULL = "full", e.ROLES = "roles", e }({}),
                    s = function(e) { return e.SINGLE = "single", e.MULTIPLE = "multi", e }({}),
                    c = function(e) { return e.DEPARTMENT = "department", e.LEVEL = "levels", e.LOCATION = "location", e.MANUAL = "manual", e.TEAM = "team", e.FUNCTION = "function", e.FUNCTIONTEAM = "matrixCells", e.BESTFIT = "bestfit", e }({}),
                    d = function(e) { return e.SCREEN = "screen", e.STANDARD = "standard", e.LARGE = "poster", e.POWERPOINT = "powerpoint", e }({}),
                    u = function(e) { return e.SMALL = "small", e.MEDIUM = "medium", e.LARGE = "large", e }({}),
                    h = function(e) { return e.LEFT = "left", e.CENTER = "center", e.RIGHT = "right", e }({}),
                    m = function(e) { return e.TOP_LEFT = "topLeft", e.TOP_RIGHT = "topRight", e.BOTTOM_LEFT = "bottomLeft", e.BOTTOM_RIGHT = "bottomRight", e.TOP = "top", e.BOTTOM = "bottom", e }({}); const p = { chart: [{ value: m.TOP_LEFT, label: "Top left" }, { value: m.TOP_RIGHT, label: "Top right" }, { value: m.BOTTOM_LEFT, label: "Bottom left" }, { value: m.BOTTOM_RIGHT, label: "Bottom right" }], directory: [{ value: m.TOP, label: "Top" }, { value: m.BOTTOM, label: "Bottom" }], photoboard: [{ value: m.TOP_LEFT, label: "Top" }, { value: m.BOTTOM_LEFT, label: "Bottom" }] }; let f = function(e) { return e.MANAGER_PATH = "managerPath", e.PARENT_PAGE_LINK = "parentPageLink", e.NO_PARENT_LINK = "noParentLink", e }({}),
                    v = function(e) { return e.THIN = "thin", e.NORMAL = "normal", e.THICK = "thick", e.THICKER = "thicker", e }({}); const g = { auto: { width: 33.1, height: 46.8, labels: { landscape: "Auto" } }, letter: { width: 8.5, height: 11, labels: { landscape: 'Letter Landscape (11" x 8.5")', portrait: 'Letter Portrait (8.5" x 11")' } }, legal: { width: 8.5, height: 14, labels: { landscape: 'Legal Landscape (14" x 8.5")', portrait: 'Legal Portrait (8.5" x 14")' } }, tabloid: { width: 11, height: 17, labels: { landscape: 'Ledger (17" x 11")', portrait: 'Tabloid (11" x 17")' } }, superB: { width: 13, height: 19, labels: { landscape: 'Super B Landscape (19" x 13")', portrait: 'Super B Portrait (13" x 19")' } }, A4: { width: 8.3, height: 11.7, labels: { landscape: "A4 Landscape (297 mm x 210 mm)", portrait: "A4 Portrait (210 mm x 297 mm)" } }, A3: { width: 11.7, height: 16.5, labels: { landscape: "A3 Landscape (420 mm x 297 mm)", portrait: "A3 Portrait (297 mm x 420 mm)" } }, A5: { width: 5.8, height: 8.3, labels: { landscape: "A5 Landscape (210 mm x 148 mm)", portrait: "A5 Portrait (148 mm x 210 mm)" } }, A2: { width: 16.5, height: 23.4, labels: { landscape: "A2 Landscape (594 mm x 420 mm)", portrait: "A2 Portrait (420 mm x 594 mm)" } }, roll11: { width: 11, height: 100, labels: { landscape: 'Roll (11")' } }, roll17: { width: 17, height: 100, labels: { landscape: 'Roll (17")' } }, roll18: { width: 18, height: 100, labels: { landscape: 'Roll (18")' } }, roll22: { width: 22, height: 100, labels: { landscape: 'Roll (22")' } }, roll24: { width: 24, height: 100, labels: { landscape: 'Roll (24")' } }, roll30: { width: 30, height: 100, labels: { landscape: 'Roll (30")' } }, roll34: { width: 34, height: 100, labels: { landscape: 'Roll (34")' } }, roll36: { width: 36, height: 100, labels: { landscape: 'Roll (36")' } }, roll42: { width: 42, height: 100, labels: { landscape: 'Roll (42")' } }, roll44: { width: 44, height: 100, labels: { landscape: 'Roll (44")' } } },
                    y = { A5: 96, A4: 96, A3: 96, A2: 96, A1: 96, A0: 96, ansie: 90, ansid: 100, ansic: 102, ansib: 140, ansia: 140, letter: 96, legal: 96, tabloid: 96, superB: 96, roll11: 90, roll17: 100, roll18: 100, roll22: 100, roll24: 102, roll30: 140, roll34: 140, roll36: 140, roll42: 140, roll44: 140 }; let b = function(e) { return e.PORTRAIT = "portrait", e.LANDSCAPE = "landscape", e }({}); const w = { printRange: "Select the part of the chart to print. Use 'Specific sections' to print only a subset.", pagination: "Single page limits other settings. Multiple pages allow more customization options.", breakBy: "Define how the chart splits across multiple pages. When creating page breaks manually, you can search for a role or click on a role in the chart to add a page break.", outputType: "Choose between digital formats and print-ready options. Output type affects other settings.", pageFormat: "Choose the paper size and orientation for printed charts.", nLevels: "Specify how many levels of the chart hierarchy to include. Useful for simplifying large charts.", roleColors: "Choose to display role colors in your printout.", roleCounts: "Choose to display the number of roles reporting to each role.", chartNavigation: "Select how the top role on each page should link back to its parent.", coverPage: "Add a cover page to the front of your printout. Customize its content and style.", tocShowInPrint: "Choose whether to include pages for the table of contents in the printout.", ignoreHeaderMargin: "Ignore the header margin and make it transparent when printing. This lets the chart extend to the top of the page. Warning: This may cause the header to overlap chart elements." }; let z = function(e) { return e.QUEUED = "queued", e.DEQUEUED = "dequeued", e.COMPLETE = "complete", e.FAILED = "failed", e.CANCELLED = "cancelled", e }({}),
                    x = function(e) { return e.x25 = "0.25", e.x33 = "0.33", e.x50 = "0.5", e.x67 = "0.67", e.x75 = "0.75", e.x80 = "0.8", e.x90 = "0.9", e.x100 = "1", e.x110 = "1.1", e.x125 = "1.25", e.x150 = "1.5", e.x175 = "1.75", e.x200 = "2", e.x250 = "2.5", e.x300 = "3", e.x400 = "4", e.x500 = "5", e }({}),
