                    A = function(e) { return e.INHERIT = "inherit", e.HORIZONTAL = "horizontal", e.VERTICAL = "vertical", e.GRID = "grid", e.CLUSTER = "cluster", e }({}); const k = { "YYYY-MM-DD": "".concat((new Date).getFullYear(), "-").concat(String((new Date).getMonth() + 1).padStart(2, "0"), "-").concat(String((new Date).getDate()).padStart(2, "0")), "MM/DD/YYYY": "".concat(String((new Date).getMonth() + 1).padStart(2, "0"), "/").concat(String((new Date).getDate()).padStart(2, "0"), "/").concat((new Date).getFullYear()), "DD/MM/YYYY": "".concat(String((new Date).getDate()).padStart(2, "0"), "/").concat(String((new Date).getMonth() + 1).padStart(2, "0"), "/").concat((new Date).getFullYear()), "Mon DD, YYYY": "".concat((new Date).toLocaleString("en-US", { month: "short", day: "2-digit", year: "numeric" })), "DD Mon YYYY": "".concat((new Date).toLocaleString("en-UK", { month: "short", year: "numeric", day: "2-digit" })), "Month D, YYYY": "".concat((new Date).toLocaleString("en-US", { month: "long", day: "numeric", year: "numeric" })), "D Month YYYY": "".concat((new Date).toLocaleString("en-UK", { month: "long", day: "numeric", year: "numeric" })) },
                    S = {
                        [d.SCREEN]: ["auto"], [d.STANDARD]: ["letter", "legal", "tabloid", "superB", "A4", "A3", "A5", "A2"], [d.LARGE]: ["roll11", "roll17", "roll18", "roll22", "roll24", "roll30", "roll34", "roll36", "roll42", "roll44"], [d.POWERPOINT]: ["letter", "legal", "tabloid", "superB", "A4", "A3", "A5", "A2"] } }, 92517: (e, t, n) => { "use strict";
                n.d(t, { CQ: () => s, U3: () => i, fx: () => o, sJ: () => a }); var r = n(15813); const a = e => { switch (e) {
                            case r.IT.THIN:
                                return "300";
                            case r.IT.NORMAL:
                                return "400";
                            case r.IT.THICK:
                                return "700";
                            case r.IT.THICKER:
                                return "900";
                            default:
                                return "400" } },
                    o = e => { switch (e) {
                            case r.or.SMALL:
                                return 14;
                            case r.or.MEDIUM:
                                return 18;
                            case r.or.LARGE:
                                return 24;
                            default:
                                return 14 } },
                    i = e => { var t, n, a, o, i, l, s, c, d, u, h, m, p, f, v, g, y, b, w, z, x, A, k, S, M, E, C, T, H, L, I, j, V, O, R, P, D, F, N, _, B; const { setup: W, layout: U } = e, { meta: q, chart: G, legend: K } = U; return { printType: null === W || void 0 === W ? void 0 : W.resource, themeId: null === G || void 0 === G ? void 0 : G.themeId, pageSetup: { range: null === W || void 0 === W || null === (t = W.range) || void 0 === t ? void 0 : t.type, topRoles: null === W || void 0 === W ? void 0 : W.topRoles, pagination: null === W || void 0 === W || null === (n = W.page) || void 0 === n ? void 0 : n.paginationType, breakBy: null === W || void 0 === W || null === (a = W.page) || void 0 === a || null === (o = a.multiOptions) || void 0 === o ? void 0 : o.breakBy, bestFitScale: (null === W || void 0 === W || null === (i = W.page) || void 0 === i || null === (l = i.multiOptions) || void 0 === l ? void 0 : l.bestFitScale) || .9, outputType: null === W || void 0 === W ? void 0 : W.optimizeFor, pageFormat: { size: (null === W || void 0 === W || null === (s = W.page) || void 0 === s || null === (c = s.size) || void 0 === c ? void 0 : c.value) || "letter", orientation: null === W || void 0 === W || null === (d = W.page) || void 0 === d ? void 0 : d.orientation }, nLevels: null === W || void 0 === W || null === (u = W.range) || void 0 === u ? void 0 : u.nLevels, pageBreaks: (null === W || void 0 === W ? void 0 : W.pageBreaks) || {} }, headerFooter: { header: { visible: null === q || void 0 === q || null === (h = q.header) || void 0 === h ? void 0 : h.visible, text: null === q || void 0 === q || null === (m = q.header) || void 0 === m ? void 0 : m.title, size: null === q || void 0 === q || null === (p = q.header) || void 0 === p ? void 0 : p.size, position: null === q || void 0 === q || null === (f = q.header) || void 0 === f ? void 0 : f.placement, fontWeight: null === q || void 0 === q || null === (v = q.header) || void 0 === v ? void 0 : v.thickness, textColor: /^#(?:[0-9a-f]{3}|[0-9a-f]{6})$/i.test(null === q || void 0 === q || null === (g = q.header) || void 0 === g ? void 0 : g.color) ? null === q || void 0 === q || null === (y = q.header) || void 0 === y ? void 0 : y.color : "#000000", subtitle: { visible: null === q || void 0 === q || null === (b = q.header) || void 0 === b ? void 0 : b.showSubtitle, text: null === q || void 0 === q || null === (w = q.header) || void 0 === w ? void 0 : w.subtitle, useToC: (null === q || void 0 === q || null === (z = q.header) || void 0 === z ? void 0 : z.useToCAsSubtitle) || !1 }, companyLogo: null === G || void 0 === G ? void 0 : G.logo, ignoreMargin: (null === q || void 0 === q || null === (x = q.header) || void 0 === x ? void 0 : x.ignoreMargin) || !1 }, footer: { visible: null === q || void 0 === q || null === (A = q.footer) || void 0 === A ? void 0 : A.visible, text: null === q || void 0 === q || null === (k = q.footer) || void 0 === k ? void 0 : k.text, size: (null === q || void 0 === q || null === (S = q.footer) || void 0 === S ? void 0 : S.size) || r.or.MEDIUM, position: (null === q || void 0 === q || null === (M = q.footer) || void 0 === M ? void 0 : M.placement) || r.yX.LEFT, organimiLogo: null === q || void 0 === q || null === (E = q.footer) || void 0 === E ? void 0 : E.showOrganimiLogo, useTimestamp: (null === q || void 0 === q || null === (C = q.footer) || void 0 === C ? void 0 : C.useTimestamp) || !1 } }, chartElements: { legend: { visible: null === K || void 0 === K ? void 0 : K.visible, position: null === K || void 0 === K ? void 0 : K.position, ownPage: (null === K || void 0 === K ? void 0 : K.ownPage) || !1 }, pageNumbers: null === G || void 0 === G ? void 0 : G.pageNumberFooter, pageIndicators: null === W || void 0 === W || null === (T = W.page) || void 0 === T || null === (H = T.multiOptions) || void 0 === H ? void 0 : H.showPageIndicators, linkedInIcons: null === G || void 0 === G ? void 0 : G.linkedIn, roleColors: null === G || void 0 === G ? void 0 : G.roleColors, roleCounts: null === G || void 0 === G ? void 0 : G.roleCounts, chartNavigation: null === G || void 0 === G ? void 0 : G.chartNavigation, directReportDirectionOverrides: null === G || void 0 === G ? void 0 : G.directReportDirectionOverrides }, tableOfContents: { visible: null === W || void 0 === W ? void 0 : W.showToC, rootLabel: null === W || void 0 === W ? void 0 : W.tocRootLabel }, coverPage: { visible: (null === W || void 0 === W || null === (L = W.coverPage) || void 0 === L ? void 0 : L.visible) || !1, title: (null === W || void 0 === W || null === (I = W.coverPage) || void 0 === I ? void 0 : I.title) || "", subtitle: (null === W || void 0 === W || null === (j = W.coverPage) || void 0 === j ? void 0 : j.subtitle) || "", size: (null === W || void 0 === W || null === (V = W.coverPage) || void 0 === V ? void 0 : V.size) || r.or.MEDIUM, position: (null === W || void 0 === W || null === (O = W.coverPage) || void 0 === O ? void 0 : O.placement) || r.yX.LEFT, fontWeight: (null === W || void 0 === W || null === (R = W.coverPage) || void 0 === R ? void 0 : R.thickness) || r.IT.NORMAL, textColor: (null === W || void 0 === W || null === (P = W.coverPage) || void 0 === P ? void 0 : P.color) || "#000000", date: { visible: (null === W || void 0 === W || null === (D = W.coverPage) || void 0 === D || null === (F = D.date) || void 0 === F ? void 0 : F.visible) || !0, format: (null === W || void 0 === W || null === (N = W.coverPage) || void 0 === N || null === (_ = N.date) || void 0 === _ ? void 0 : _.format) || "YYYY-MM-DD" }, showHeaderFooter: (null === W || void 0 === W || null === (B = W.coverPage) || void 0 === B ? void 0 : B.showHeaderFooter) || !1 } } },
                    l = e => { switch (e) {
                            case r.yQ.SCREEN:
                            case r.yQ.STANDARD:
                            case r.yQ.LARGE:
                                return "pdf";
                            case r.yQ.POWERPOINT:
                                return "ppt";
                            default:
                                return "pdf" } },
                    s = e => { const { printType: t, themeId: n, pageSetup: a, headerFooter: o, chartElements: i, tableOfContents: s } = e; return { fileType: l(a.outputType), setup: { resource: t, optimizeFor: a.outputType, range: { type: a.range, nLevels: a.nLevels, departments: [], roles: [], nTopLevels: a.nLevels }, page: { paginationType: a.pagination, size: { auto: a.outputType === r.yQ.SCREEN, value: a.pageFormat.size, label: r.q2[a.pageFormat.size].labels[a.pageFormat.orientation], width: r.q2[a.pageFormat.size].width, height: r.q2[a.pageFormat.size].height }, orientation: a.pageFormat.orientation, multiOptions: { showPageIndicators: i.pageIndicators, breakBy: a.breakBy, bestFitScale: a.bestFitScale, breakByCustom: null }, margins: { top: 1, right: 1, bottom: 1, left: 1 } }, showToC: s.visible, tocRootLabel: s.rootLabel, topRoles: a.topRoles, pageBreaks: { ...a.pageBreaks }, coverPage: { visible: e.coverPage.visible, title: e.coverPage.title, subtitle: e.coverPage.subtitle, size: e.coverPage.size, placement: e.coverPage.position, thickness: e.coverPage.fontWeight, color: e.coverPage.textColor, date: e.coverPage.date, showHeaderFooter: e.coverPage.showHeaderFooter } }, layout: { meta: { header: { visible: o.header.visible, title: o.header.text, size: o.header.size, placement: o.header.position, thickness: o.header.fontWeight, color: o.header.textColor, showSubtitle: o.header.subtitle.visible, subtitle: o.header.subtitle.text, useToCAsSubtitle: o.header.subtitle.useToC, ignoreMargin: o.header.ignoreMargin, margins: { top: 1, right: 1, bottom: 1, left: 1 }, showTitle: !0 }, footer: { visible: o.footer.visible, text: o.footer.text || "", size: o.footer.size, placement: o.footer.position, showOrganimiLogo: o.footer.organimiLogo, useTimestamp: o.footer.useTimestamp, margins: { top: 1, right: 1, bottom: 1, left: 1 } } }, chart: { themeId: n, logo: o.header.companyLogo, linkedIn: i.linkedInIcons, roleColors: i.roleColors, roleCounts: i.roleCounts, chartNavigation: i.chartNavigation, pageNumberFooter: i.pageNumbers, directReportDirectionOverrides: i.directReportDirectionOverrides, directory: { columnsToExclude: [] }, direction: "top", dottedReports: !0, stackedRoles: 2, sharedRoles: 2, compact: !0, departmentBorder: !0, dotStyle: "dashed", dotColor: "blue" }, legend: { visible: i.legend.visible, position: i.legend.position, ownPage: i.legend.ownPage, title: { text: "Legend", size: "medium", color: "black", shape: "rectangle" } } } } } }, 98433: (e, t, n) => { "use strict";
                n.d(t, { $o: () => q, Ay: () => oe, DD: () => M, GE: () => J, IR: () => P, IU: () => S, Jt: () => d, L$: () => N, LU: () => F, LY: () => te, OW: () => ne, Qp: () => Z, R0: () => m, T0: () => D, TK: () => I, Ut: () => y, Vq: () => x, W8: () => E, WH: () => B, X5: () => z, b0: () => $, dY: () => ae, db: () => T, eE: () => W, eI: () => C, ez: () => w, gb: () => U, iZ: () => R, io: () => X, j$: () => V, jz: () => _, k1: () => Y, kT: () => v, kU: () => H, kv: () => h, l8: () => K, lh: () => Q, mU: () => L, mh: () => j, o4: () => G, pl: () => k, qG: () => b, r2: () => g, rW: () => A, sM: () => re, sx: () => ee, xy: () => f }); var r = n(80907),
                    a = n(80192),
                    o = n(9787),
                    i = n(47730),
                    l = n(15813),
                    s = n(92517); const c = "print",
                    d = { printType: l._6.CHART, themeId: "", pageSetup: { range: l.J_.FULL, topRoles: [], pagination: l.ti.SINGLE, breakBy: l.N0.LEVEL, bestFitScale: .9, outputType: l.yQ.SCREEN, pageFormat: { size: "auto", orientation: l.t4.LANDSCAPE }, nLevels: 0, pageBreaks: {} }, headerFooter: { header: { visible: !0, text: "", size: l.or.MEDIUM, position: l.yX.LEFT, fontWeight: l.IT.NORMAL, textColor: "#000000", subtitle: { visible: !1, text: "", useToC: !1 }, companyLogo: !0, ignoreMargin: !1 }, footer: { visible: !0, text: null, size: l.or.MEDIUM, position: l.yX.CENTER, organimiLogo: !0, useTimestamp: !1 } }, chartElements: { legend: { visible: !0, position: l.PA.TOP_LEFT, ownPage: !1 }, pageNumbers: !0, pageIndicators: !0, linkedInIcons: !0, roleColors: !0, roleCounts: !0, chartNavigation: l.xq.PARENT_PAGE_LINK, directReportDirectionOverrides: {} }, tableOfContents: { visible: !1, rootLabel: "" }, coverPage: { visible: !1, title: "", subtitle: "", textColor: "#000000", size: l.or.MEDIUM, position: l.yX.CENTER, fontWeight: l.IT.NORMAL, date: { visible: !1, format: "YYYY-MM-DD" }, showHeaderFooter: !1 } },
                    u = { settings: d, templates: [], activeTemplateId: null, printHistory: { completed: [], inProgress: [], failed: [] }, page: { current: 1, count: 1, flatChartCount: 1 }, zoom: Number(l.Xp.x100), fitScale: Number(l.Xp.x100), bestFitMap: void 0 },
                    h = (0, o.a)({ slice: c, scope: "print" }),
                    m = (0, i.B)({ slice: c, scope: "print" }),
                    p = (0, r.Z0)({ name: c, initialState: u, reducers: { resetPrintSlice: () => u, setPrintType: (e, t) => { e.settings.printType = t.payload }, setThemeId: (e, t) => { e.settings.themeId = t.payload }, setTemplates: (e, t) => { e.templates = t.payload }, setActiveTemplateId: (e, t) => { const n = t.payload; if (!n) return e.settings = u.settings, void(e.activeTemplateId = null);
                                e.activeTemplateId = n; const r = e.templates.find((e => e.id === n));
                                r && (e.settings = (0, s.U3)(r)) }, setCurPage: (e, t) => { e.page.current = t.payload }, setPageCount: (e, t) => { e.page.count = t.payload }, setFlatChartCount: (e, t) => { e.page.flatChartCount = t.payload }, setPageSetup: (e, t) => { e.settings.pageSetup = { ...t.payload, pageBreaks: e.settings.pageSetup.pageBreaks } }, updatePrintSettingsTopRole: (e, t) => { var n; let { payload: r } = t;
                                r && null !== e && void 0 !== e && null !== (n = e.settings) && void 0 !== n && n.pageSetup && (e.settings.pageSetup.range = l.J_.ROLES, e.settings.pageSetup.topRoles = [r]) }, setHeaderFooter: (e, t) => { e.settings.headerFooter = t.payload }, setHeader: (e, t) => { e.settings.headerFooter.header = t.payload }, setFooter: (e, t) => { e.settings.headerFooter.footer = t.payload }, setChartElements: (e, t) => { e.settings.chartElements = t.payload }, setLegend: (e, t) => { e.settings.chartElements.legend = t.payload }, "getInProgressPrintJobs/fulfilled": (e, t) => { var n;
                                e.printHistory.inProgress = (null === (n = t.payload) || void 0 === n ? void 0 : n.inProgressJobs) || [] }, "cancelPrintJob/fulfilled": (e, t) => { e.printHistory.inProgress = e.printHistory.inProgress.filter((e => { var n, r; return e.id !== (null === (n = t.payload) || void 0 === n || null === (r = n.job) || void 0 === r ? void 0 : r.id) })) || [] }, "getPrintHistory/fulfilled": (e, t) => { var n, r; const a = e.printHistory.completed.reduce(((e, t) => (e[t.id] = t, e)), {}),
                                    o = [];
                                e.printHistory.completed = null === (n = (null === (r = t.payload) || void 0 === r ? void 0 : r.printHistory) || []) || void 0 === n ? void 0 : n.map((e => { var t; return o.push(e.fileInfo.code), { ...e, new: null === (t = a[e.id]) || void 0 === t ? void 0 : t.new } })), e.printHistory.inProgress = e.printHistory.inProgress.filter((e => !o.includes(e.fileCode))) }, "updateSaveStatusOfFileInPrintHistory/fulfilled": (e, t) => { const { printHistory: n } = t.payload;
                                e.printHistory.completed = e.printHistory.completed.map((e => e.id === (null === n || void 0 === n ? void 0 : n.id) ? { ...e, ...n } : e)) }, "deletePrintHistory/fulfilled": (e, t) => { e.printHistory.completed = [...e.printHistory.completed || []].filter((e => e.id !== t.payload.id)) }, "updateFileNameInPrintHistory/fulfilled": (e, t) => { const { printHistory: n } = t.payload;
                                e.printHistory.completed = e.printHistory.completed.map((e => e.id === (null === n || void 0 === n ? void 0 : n.id) ? { ...e, ...n } : e)) }, setPrintHistoryFailed: (e, t) => { e.printHistory.failed = t.payload }, setTableOfContents: (e, t) => { e.settings.tableOfContents = t.payload }, setCoverPage: (e, t) => { e.settings.coverPage = t.payload }, setZoom: (e, t) => { e.zoom = t.payload }, setFitScale: (e, t) => { e.fitScale = t.payload }, "getPrintTemplates/fulfilled": (e, t) => { var n; const { printTemplates: r } = t.payload; if (e.templates = r, e.activeTemplateId = (null === (n = r[0]) || void 0 === n ? void 0 : n.id) || null, e.activeTemplateId) { const t = r.find((t => t.id === e.activeTemplateId));
                                    t && (e.settings = (0, s.U3)(t)) } }, "create/fulfilled": (e, t) => { const { template: n } = t.payload;
                                e.templates.push(n), e.activeTemplateId = n.id }, "update/fulfilled": (e, t) => { const { template: n } = t.payload;
                                e.templates = e.templates.map((e => e.id === n.id ? n : e)) }, "delete/fulfilled": (e, t) => { var n, r; const { templateId: a } = t.payload;
                                e.templates = e.templates.filter((e => e.id !== a)), e.activeTemplateId = (null === (n = e.templates[0]) || void 0 === n ? void 0 : n.id) || null, p.caseReducers.setActiveTemplateId(e, { payload: (null === (r = e.templates[0]) || void 0 === r ? void 0 : r.id) || null, type: "" }) }, "getPrintJobSettings/fulfilled": (e, t) => { const { params: n } = t.payload;
                                e.settings = (0, s.U3)(n), n && (e.page.count = n.pageCount || 1) }, markPrintJobAsNew: (e, t) => { const { fileCode: n } = t.payload;
                                e.printHistory.completed = e.printHistory.completed.map((e => (e.fileInfo.code === n && (e.new = !0), e))) }, setPageBreak: (e, t) => { var n; const { chartId: r, pageBreak: a, remove: o } = t.payload; var i; if ((e.settings.pageSetup.pageBreaks = { ...e.settings.pageSetup.pageBreaks || {} }, o || null !== (n = e.settings.pageSetup.pageBreaks[r]) && void 0 !== n && n.find((e => e.id === a.id))) && (e.settings.pageSetup.pageBreaks[r] = null === (i = e.settings.pageSetup.pageBreaks[r]) || void 0 === i ? void 0 : i.filter((e => e.id !== a.id)), o)) return;
                                e.settings.pageSetup.pageBreaks[r] || (e.settings.pageSetup.pageBreaks[r] = []), e.settings.pageSetup.pageBreaks[r].push({ ...a }) }, setPageBreaks: (e, t) => { const { chartId: n, pageBreaks: r } = t.payload;
                                e.settings.pageSetup.pageBreaks[n] = r }, setPageBreakName: (e, t) => { const { chartId: n, pageBreakId: r, name: a } = t.payload;
                                e.settings.pageSetup.pageBreaks[n] = e.settings.pageSetup.pageBreaks[n].map((e => e.id === r ? { ...e, name: a } : e)) }, overrideDirectReportDirection: (e, t) => { const { id: n, chartId: r, directReportDirectionSettings: a } = t.payload, o = { ...e.settings.chartElements.directReportDirectionOverrides };
                                Object.keys(o).includes(r) ? o[r] = { ...o[r], [n]: a } : o[r] = {
                                    [n]: a }, e.settings.chartElements.directReportDirectionOverrides = o }, resetDirectReportDirection: (e, t) => { const { chartId: n } = t.payload, r = { ...e.settings.chartElements.directReportDirectionOverrides };
                                Object.keys(r).includes(n) && delete r[n], e.settings.chartElements.directReportDirectionOverrides = r }, setBestFitMap: (e, t) => { e.bestFitMap = t.payload } } }),
                    f = e => e.print.settings,
                    v = e => e.print.settings.printType,
                    g = e => e.print.settings.themeId,
                    y = e => e.print.templates,
                    b = e => e.print.activeTemplateId,
                    w = ((0, a.Mz)(y, b, ((e, t) => e.find((e => e.id === t)))), e => e.print.page.current),
                    z = e => e.print.page.count,
                    x = e => e.print.page.flatChartCount,
                    A = e => e.print.settings.pageSetup,
                    k = e => e.print.settings.headerFooter,
                    S = e => e.print.settings.chartElements,
                    M = (0, a.Mz)((e => { var t, n, r; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.chartElements) || void 0 === r ? void 0 : r.legend }), (e => { var t; return null === (t = e.chart) || void 0 === t ? void 0 : t.info }), ((e, t) => { var n, r, a, o; return { ...e, visible: (null === t || void 0 === t || null === (n = t.legend) || void 0 === n || null === (r = n.rules) || void 0 === r ? void 0 : r.length) > 0 && e.visible, ownPage: (null === t || void 0 === t || null === (a = t.legend) || void 0 === a || null === (o = a.rules) || void 0 === o ? void 0 : o.length) > 0 && e.ownPage } })),
                    E = e => e.print.printHistory,
                    C = e => e.print.settings.tableOfContents,
                    T = e => e.print.settings.coverPage,
                    H = e => e.print.zoom,
                    L = e => e.print.bestFitMap,
                    { resetPrintSlice: I, setPrintType: j, setThemeId: V, setTemplates: O, setActiveTemplateId: R, setCurPage: P, setPageCount: D, setFlatChartCount: F, setPageSetup: N, setHeaderFooter: _, setHeader: B, setFooter: W, setChartElements: U, setLegend: q, setTableOfContents: G, setCoverPage: K, setZoom: Z, setFitScale: Y, markPrintJobAsNew: X, setPrintHistoryFailed: $, setPageBreak: Q, setPageBreaks: J, setPageBreakName: ee, overrideDirectReportDirection: te, resetDirectReportDirection: ne, setBestFitMap: re, updatePrintSettingsTopRole: ae } = p.actions,
                    oe = p.reducer }, 37556: (e, t, n) => { "use strict";
                n.d(t, { M: () => d }); var r = n(65043),
                    a = n(14556),
                    o = n(83972),
                    i = n(49015),
                    l = n(33070),
                    s = n(356),
                    c = n(86597);

                function d(e) { let { featureTours: t } = e; const n = (0, a.wA)(),
                        { activeTour: d, activeTourDef: u, activeTourStep: h, activeTourStepIndex: m, activeTourUserInput: p, activeTourStatus: f } = (0, a.d4)(o.AK),
                        v = (0, a.d4)(o.VW),
                        g = (0, c.A)(),
                        y = null === g || void 0 === g ? void 0 : g.id,
                        b = { orgId: null === g || void 0 === g ? void 0 : g.id, tourName: d, stepName: null === h || void 0 === h ? void 0 : h.id, stepNumber: m, tourInput: p };
                    (0, r.useEffect)((() => (null !== t && void 0 !== t && t.length && n((0, o.j9)(t)), () => { null !== t && void 0 !== t && t.length && n((0, o.oR)(t)) })), []); const w = e => n((0, o._c)(e)),
                        z = function() { let { increment: e, userInput: t = null } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const r = e || 1;
                            t && x({ userInput: t }), n((0, o.zE)(m + r)) },
                        x = async e => { let { userInput: t, ignoreErrorHandler: r = !0 } = e;
                            n(i.lf.updateUserProgress({ orgId: y, data: { name: d, currentStepId: (null === h || void 0 === h ? void 0 : h.id) || m, userInput: t, ignoreErrorHandler: r } })) }, A = async function() { let { userInput: e, resetActiveTour: t = !0 } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            t && n((0, o._c)({ tour: null })), n(i.lf.updateUserProgress({ orgId: y, data: { name: d, type: "complete", currentStepId: (null === h || void 0 === h ? void 0 : h.id) || m, userInput: e } })), s.A.trackEvent({ eventName: "TOUR_COMPLETED", extraParams: b }) }, k = { MACRO_TOURS: { ...l.B }, TOURS_DEFS: { ...l.U }, trackingData: b, userDetails: v, activeOrg: g, activeTour: d, activeTourDef: u, activeTourStep: h, activeTourStepIndex: m, activeTourUserInput: p, activeTourStatus: f, goBack: () => { var e; const t = null === h || void 0 === h || null === (e = h.actions) || void 0 === e ? void 0 : e.find((e => "back" === e.type)),
                                    r = null === t || void 0 === t ? void 0 : t.event,
                                    a = r ? o.s_[r] : void 0;
                                a && n(a()); const i = null === t || void 0 === t ? void 0 : t.backToStepId,
                                    l = u.steps.findIndex((e => e.id === i)),
                                    c = l > -1 ? l : 0 === m ? 0 : m - 1;
                                n((0, o.zE)(c)), s.A.trackEvent({ eventName: "TOUR_BACK_CLICKED", extraParams: b }) }, goNext: z, skipTour: async function() { let { userInput: e, complete: t = !1, errored: r = !1, resetActiveTour: a = !0 } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                                a && n((0, o._c)({ tour: null })), null !== u && void 0 !== u && u.isMacroTour || ("completed" !== f && n(i.lf.updateUserProgress({ orgId: y, data: { name: d, type: r ? "skipOnError" : t ? "skipAndComplete" : "skip", currentStepId: (null === h || void 0 === h ? void 0 : h.id) || m, userInput: e } })), s.A.trackEvent({ eventName: "TOUR_SKIPPED", extraParams: b })) }, addSteps: e => { let { steps: t = [], index: r = null } = e; return n((0, o.u9)({ steps: t, index: r })) }, removeStep: e => { let { stepIndex: t } = e; return n((0, o.Uh)({ stepIndex: t })) }, postTourData: x, completeTour: A, setActiveTour: w, onTourEventComplete: function() { var e, t, r; let { event: a = "", data: i = null } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; if (!a) return; if (!a || !h) return; const l = null === u || void 0 === u ? void 0 : u.gotoStepOnEvent,
                                    s = null === h || void 0 === h || null === (e = h.actions) || void 0 === e ? void 0 : e.find((e => "nextOnEvent" === (null === e || void 0 === e ? void 0 : e.type))),
                                    c = null === h || void 0 === h || null === (t = h.actions) || void 0 === t ? void 0 : t.find((e => "closeOnEvent" === (null === e || void 0 === e ? void 0 : e.type))),
                                    d = null === h || void 0 === h || null === (r = h.actions) || void 0 === r ? void 0 : r.find((e => "completeOnEvent" === (null === e || void 0 === e ? void 0 : e.type))); if (a === (null === l || void 0 === l ? void 0 : l.event)) { var m; const e = null === u || void 0 === u || null === (m = u.steps) || void 0 === m ? void 0 : m.findIndex((e => e.id === (null === l || void 0 === l ? void 0 : l.stepId))); return e > -1 && n((0, o.zE)(e)) } return a === (null === s || void 0 === s ? void 0 : s.event) ? z({ userInput: i }) : a === (null === c || void 0 === c ? void 0 : c.event) ? w({ tour: (null === c || void 0 === c ? void 0 : c.nextTourId) || null }) : a === (null === d || void 0 === d ? void 0 : d.event) ? A({ userInput: i || {} }) : void 0 } }; return k } }, 43940: (e, t, n) => { "use strict";
                n.d(t, { M: () => r.M }); var r = n(37556) }, 83972: (e, t, n) => { "use strict";
                n.d(t, { AK: () => s, Ay: () => y, Uh: () => v, VW: () => c, _c: () => p, _l: () => d, j9: () => u, oR: () => h, s_: () => g, u9: () => f, zE: () => m }); var r = n(80907),
                    a = n(33070),
                    o = n(70669); const i = (e, t) => { var n, r; const { activeTour: o, readyTours: i, pendingTours: l, completedTours: s, skippedTours: c } = e; if ((o || !(null !== i && void 0 !== i && i.length)) && !t) { var d; if (o) e.activeTourUserInput = { ...(null === e || void 0 === e ? void 0 : e.activeTourUserInput) || {}, ...(null === (d = [...l, ...s, ...c].find((e => e.name === o))) || void 0 === d ? void 0 : d.userInput) || {} }; return } if (t && (null === t || void 0 === t || !t.tour)) return e.activeTour = null, e.activeTourDef = null, e.activeTourStep = null, e.activeTourStepIndex = null, e.activeTourUserInput = null, void(e.activeTourStatus = null); const u = (null === t || void 0 === t ? void 0 : t.tour) || (null === (n = l.find((e => i.includes(e.name)))) || void 0 === n ? void 0 : n.name),
                            h = (null === a.U || void 0 === a.U ? void 0 : a.U[u]) || null,
                            m = [...l, ...s, ...c].find((e => e.name === u)) || null,
                            p = (null === m || void 0 === m ? void 0 : m.userInput) || null,
                            f = [...(null === h || void 0 === h ? void 0 : h.steps) || []]; if (null !== t && void 0 !== t && null !== (r = t.stepsToAdd) && void 0 !== r && r.length) { const e = null === (null === t || void 0 === t ? void 0 : t.indexToAddStep) ? (null === f || void 0 === f ? void 0 : f.length) - 1 : null === t || void 0 === t ? void 0 : t.indexToAddStep;
                            f.splice(e, 0, ...t.stepsToAdd) } const v = (null === t || void 0 === t ? void 0 : t.startingStepIndex) || 0;
                        e.activeTour = u, e.activeTourDef = { ...h, steps: f }, e.activeTourStep = f[v] || null, e.activeTourStepIndex = f[v] ? v : null, e.activeTourUserInput = { ...p, ...(null === t || void 0 === t ? void 0 : t.userInput) || {} }, e.activeTourStatus = (null === m || void 0 === m ? void 0 : m.status) || null },
                    l = (0, r.Z0)({ name: "tour", initialState: { pendingTours: [], completedTours: [], skippedTours: [], readyTours: [], activeTour: null, activeTourDef: null, activeTourStep: null, activeTourStepIndex: null, activeTourUserInput: null, activeTourStatus: null }, reducers: { registerForTour: (e, t) => { let { payload: n } = t; const r = n || [];
                                e.readyTours = Array.from(new Set([...e.readyTours, ...r])), i(e) }, deregisterForTour: (e, t) => { var n; let { payload: r } = t; const a = r || [];
                                e.readyTours = e.readyTours.filter((e => !a.includes(e))), (null !== r && void 0 !== r && r.includes(e.activeTour) || null !== e && void 0 !== e && null !== (n = e.activeTourStep) && void 0 !== n && n.isMacroStep) && (e.activeTour = null, e.activeTourDef = null, e.activeTourStep = null, e.activeTourStepIndex = null, e.activeTourUserInput = null, e.activeTourStatus = null), i(e) }, forceSetActiveTour: (e, t) => { let { payload: n } = t;
                                i(e, { ...n }) }, addStepsToActiveTour: (e, t) => { var n, r, a, o; let { payload: i } = t; if (null === i || void 0 === i || null === (n = i.steps) || void 0 === n || !n.length) return e; const l = [...(null === e || void 0 === e || null === (r = e.activeTourDef) || void 0 === r ? void 0 : r.steps) || []],
                                    s = null === (null === i || void 0 === i ? void 0 : i.index) ? null === e || void 0 === e || null === (a = e.activeTourDef) || void 0 === a || null === (o = a.steps) || void 0 === o ? void 0 : o.length : null === i || void 0 === i ? void 0 : i.index;
                                l.splice(s, 0, ...i.steps), e.activeTourDef.steps = l }, removeStepsFromActiveTour: (e, t) => { var n; let { payload: r } = t; if (null === (null === r || void 0 === r ? void 0 : r.stepIndex) || void 0 === (null === r || void 0 === r ? void 0 : r.stepIndex)) return e; const a = [...(null === e || void 0 === e || null === (n = e.activeTourDef) || void 0 === n ? void 0 : n.steps) || []];
                                a.splice(null === r || void 0 === r ? void 0 : r.stepIndex, 1), e.activeTourDef.steps = a }, setActiveTourStepIndex: (e, t) => { let { payload: n } = t; if (e.activeTourDef) { var r; const t = (null === (r = e.activeTourDef) || void 0 === r ? void 0 : r.steps[n]) || null;
                                    e.activeTourStep = t, e.activeTourStepIndex = [null, void 0].includes(n) ? null : n } } }, extraReducers: { "user/getUserTours/fulfilled": (e, t) => { var n, r, a; let { payload: o } = t;
                                e.pendingTours = (null === o || void 0 === o || null === (n = o.userTours) || void 0 === n ? void 0 : n.pending) || [], e.completedTours = (null === o || void 0 === o || null === (r = o.userTours) || void 0 === r ? void 0 : r.completed) || [], e.skippedTours = (null === o || void 0 === o || null === (a = o.userTours) || void 0 === a ? void 0 : a.skipped) || [], i(e) }, "user/updateUserProgress/pending": (e, t) => { var n; let { meta: { arg: r } } = t; const a = null === r || void 0 === r || null === (n = r.data) || void 0 === n ? void 0 : n.userInput;
                                a && null !== e && void 0 !== e && e.activeTour && (e.activeTourUserInput = { ...e.activeTourUserInput || {}, ...a }) }, "user/updateUserProgress/fulfilled": (e, t) => { var n, r, a; let { payload: o } = t;
                                e.pendingTours = (null === o || void 0 === o || null === (n = o.userTours) || void 0 === n ? void 0 : n.pending) || e.pendingTours, e.completedTours = (null === o || void 0 === o || null === (r = o.userTours) || void 0 === r ? void 0 : r.completed) || e.completedTours, e.skippedTours = (null === o || void 0 === o || null === (a = o.userTours) || void 0 === a ? void 0 : a.skipped) || e.skippedTours, i(e) } } }),
                    s = e => { const { activeTour: t, activeTourDef: n, activeTourStep: r, activeTourStepIndex: a, activeTourUserInput: o, activeTourStatus: i } = e.tour; return { activeTour: t, activeTourDef: n, activeTourStep: r, activeTourStepIndex: a, activeTourUserInput: o, activeTourStatus: i } },
                    c = e => e.user,
                    d = e => e.organization,
                    { registerForTour: u, deregisterForTour: h, setActiveTourStepIndex: m, forceSetActiveTour: p, addStepsToActiveTour: f, removeStepsFromActiveTour: v } = l.actions,
                    g = { "talent-pool-btn-toolbar--click": o.zi },
                    y = l.reducer }, 33070: (e, t, n) => { "use strict";
                n.d(t, { B: () => a, U: () => o }); const r = { anchorDataAttribute: "app-top-bar", position: "bottom", backdrop: !1, arrow: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0 },
                    a = { macro_chartThemesBtn: { isMacroStep: !0, id: "macro_chartThemesBtn", title: "Click on Themes Button", text: "You can style your chart and role card's look and feel. Add or remove what is shown on the role cards. Play around with role cards positioning and more..", anchorDataAttribute: "themes-btn-toolbar", anchorClass: "tour-highlight-anchor-white-purple-background", backdrop: !1, actions: [{ type: "nextOnEvent", event: "themes-btn-toolbar--click" }] }, macro_chartShareBtn: { isMacroStep: !0, id: "macro_chartShareBtn", title: "Click on Share Button", text: "You can invite people to the chart, create public links, embed the chart in your website and much more..", anchorDataAttribute: "share-btn-toolbar", backdrop: !1, anchorOffset: "30px", actions: [{ type: "nextOnEvent", event: "share-btn-toolbar--click" }] }, macro_helpBtn: { isMacroStep: !0, id: "macro_helpBtn", title: "Help is Always Here.", text: "You can always start the tour again from the help section where you will also find other helpful resources.", anchorDataAttribute: "app-top-bar-help", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, anchorOffset: "30px", actions: [{ type: "closeOnEvent", event: "app-top-bar-help--click" }] }, macro_integrationRosterOnly: { isMacroStep: !0, id: "macro_integrationRosterOnly", title: "Roster Integration", text: "We already have setup roles. So we will import people only from your system. Note that you can import both roles and people together from your system at any time. For now let's get your people imported.", anchorDataAttribute: "new-roster-integration-btn", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "nextOnEvent", event: "integration-setup-loaded" }] }, macro_onboardingTemplates: { id: "SelectTemplate", customComponentName: "selectOnboardingTemplate" }, macro_onboardingAddPeople: { id: "AddPeople", customComponentName: "addPeople" }, macro_onboardingImportPeople: { id: "importPeople", customComponentName: "importPeople" } },
                    o = { utilizeTemplateTour: { id: "utilizeTemplateTour", title: "The Basics", steps: [{ id: "step1", customComponentName: "utilizeTemplate" }] }, onboarding: { id: "onboarding", title: "The Basics", steps: [{ id: "LetsGetStarted", customComponentName: "onboardingLetsStart" }, { id: "UserDetailsForm", customComponentName: "onboardingUserDetailsForm" }, { id: "RoleVsPeople", customComponentName: "onboardingRoleVsPeople" }, { id: "RolesOption", customComponentName: "onboardingRolesOption" }, { id: "PeopleOption", customComponentName: "onboardingPeopleOption" }] }, chartTour: { id: "chartTour", title: "The Basics", steps: [{ id: "1", title: "Charting Area", text: "The charting area is where all of the action happens. Based on your previous choices, we've set up some open roles. Now, let's match them with the right talents from your pool.", ...r, actions: [{ type: "next" }] }, { id: "routeToTalentPool", title: "Talent Pool", text: "Your Talent Pool displays all members of your organization, whether added manually or imported. To view, simply click the highlighted 'Open Talent Pool' button, located here.", anchorDataAttribute: "talent-pool-btn-toolbar", backdrop: !1, anchorOffset: "15px", actions: [{ type: "back" }, { type: "next" }] }, { id: "personAddedDND2", title: "Assign People to Roles", text: "Ready to assign your talent pool to roles? Simply drag a person from the Talent Pool and drop them onto a role card to set their position.", imageLink: "/product-tour/dnd.gif", ...r, position: "bottom-start", actions: [{ type: "back" }, { type: "nextOnEvent", event: "role-card-person-drag-and-dropped" }, { type: "next" }] }, { id: "createRole", title: "Adding Roles", text: "Great job, you've assigned a person to a role! To expand your chart, add more roles for colleagues. Just hover over a role card and click the '+' button.", imageLink: "/product-tour/addRole.gif", ...r, anchorOffset: "100px", position: "bottom-start", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, backdropAboveModal: !1, actions: [{ type: "back" }, { type: "next" }] }, { id: "happyCharting", customComponentName: "chartOnboardingNextSteps" }] }, chartViewTour: { id: "chartViewTour", title: "The Basics", steps: [{ id: "1", title: "Navigating a Chart", text: "Learn how to navigate around the chart", ...r, actions: [{ type: "back" }, { type: "next" }] }, { id: "2", title: "Role Details", text: "Click on a role card to see its details. The side panel will show the details of the Role and the Person if someone is assigned to that role.", imageLink: "/product-tour/detailsPane.gif", ...r, position: "bottom-start", actions: [{ type: "back" }, { type: "next" }] }, { id: "3", title: "Navigating The Hierarchy", text: "Underneath each manager's role card, we can see the counts for direct and overall reports. You can either expand the direct reports only and keep expanding the heirarchy, or you can expand them all.", imageLink: "/product-tour/expandCollapse.gif", ...r, anchorOffset: "100px", position: "bottom-start", actions: [{ type: "back" }, { type: "next" }] }, { id: "4", title: "Adjust Levels", text: "You can select how many levels you want to see in the chart.", imageLink: "/product-tour/chartLevels.gif", anchorDataAttribute: "levels-btn-toolbar", highlightAnchor: !1, anchorOffset: "25px", backdrop: !1, position: "left", arrow: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "5", title: "Zoom Controls", text: "You can find the zoom-in and zoom-out controls right here. You can also click 'fit-all' button to see all your roles in a single glance.", ...r, anchorDataAttribute: "zoom-in-btn-toolbar", highlightAnchor: !1, backdrop: !1, position: "bottom", arrow: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "6", title: "Searching Your Chart", text: "You can type in your search query and matching find people and roles. You can also use advanced search option to find results using specific fields.", imageLink: "/product-tour/searchChart.gif", zoomedImageOnRight: !0, ...r, anchorDataAttribute: "search-chart-toolbar", highlightAnchor: !1, backdrop: !1, position: "bottom", arrow: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "7", title: "Filtering Your Chart", text: "You can also use the search results to filter the chart.", imageLink: "/product-tour/filterChart.gif", zoomedImageOnRight: !0, ...r, anchorDataAttribute: "search-chart-toolbar", highlightAnchor: !1, backdrop: !1, position: "bottom", arrow: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "8", title: "Different Chart Views", text: "Here you have different views of the chart. You can view your chart as a directory instead or view the photoboard version of your chart which can also be embedded on your website.", ...r, anchorDataAttribute: "directory-view-toolbar", highlightAnchor: !1, backdrop: !1, position: "right", arrow: !0, actions: [{ type: "back" }, { type: "next" }] }, { ...a.macro_helpBtn, actions: [{ type: "back" }, { type: "complete" }] }] }, themesTour: { id: "themesTour", title: "The Basics", steps: [{ id: "1", title: "Themes", text: "With themes, you can style your chart and role cards. Change colors, style of photos, hide / show data on role cards, play around with role positioning and much more.", anchorDataAttribute: "themes-sidebar", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "right", actions: [{ type: "next" }] }, { id: "2", title: "Organimi Themes", text: "Here are a few preset themes. Click on any one of them to apply that theme to the chart.", anchorDataAttribute: "themes-sidebar", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "right", actions: [{ type: "back" }, { type: "next" }] }, { id: "3", title: "Custom Themes", text: "You can always create your own custom theme using a preset theme as a template. Click on the New button.", anchorDataAttribute: "new-theme-btn", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, zIndex: 1, position: "right-start", actions: [{ type: "back" }, { type: "nextOnEvent", event: "theme-created" }] }, { id: "4", title: "Cards", text: "From this cards section of the themes, you can customize the colors background etc..", anchorDataAttribute: "themes-bar-cards", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "5", title: "Fields", text: "You can show or hide specific fields shown on role cards and can style them individually.", anchorDataAttribute: "themes-bar-fields", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "6", title: "Photos", text: "You can adjust the size and position of the photos appearing on role cards.", anchorDataAttribute: "themes-bar-photos", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "7", title: "Role Specific", text: "You can style specific type of roles from here.", anchorDataAttribute: "themes-bar-shared-roles", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next" }] }, { ...a.macro_helpBtn, actions: [{ type: "back" }, { type: "complete" }] }] }, shareOptionsTour: { id: "shareOptionsTour", title: "The Basics", steps: [{ id: "0", title: "Sharing Options", text: "You have multiple ways to share your chart with others.", anchorDataAttribute: "share-option-sso", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, arrow: !1, position: "left", actions: [{ type: "back" }, { type: "next" }] }, { id: "1", title: "Share by SSO", text: "If you have SSO setup, you can give automatic view/edit access to anyone who uses your SSO to login.", anchorDataAttribute: "share-option-sso", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", anchorOffset: "30px", actions: [{ type: "back" }, { type: "next" }] }, { id: "2", title: "Share by Invite", text: "Invite people by private sharing. You can invite specific people or bulk invite people in your chart or talent pool.", anchorDataAttribute: "share-option-invite", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", anchorOffset: "30px", actions: [{ type: "back" }, { type: "next" }] }, { id: "3", title: "Share by Link", text: "You can share your chart with a link. This way people viewing / editing the chart wont have to login to Organimi. And you can decide if you want to keep the link publicly accessible to anyone, or restrict it with a password or IP protection.", anchorDataAttribute: "share-option-link", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", anchorOffset: "30px", actions: [{ type: "back" }, { type: "next" }] }, { id: "4", title: "Share by Embed", text: "You can securely embed this chart on your website", anchorDataAttribute: "share-option-embed", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", anchorOffset: "30px", actions: [{ type: "back" }, { type: "next" }] }, { ...a.macro_helpBtn, actions: [{ type: "back" }, { type: "complete" }] }] }, onboardingImportsTour: { id: "onboardingImportsTour", title: "Import Basics", steps: [{ id: "imports-start", title: "Data Import", text: "Import your data with a few easy steps. Select your file, Apply any filters to your data before importing, Match your data columns with Organimi fields and finally complete your import.", ignoreAnchorZIndex: !0, anchorDataAttribute: "app-top-bar", arrow: !1, anchorOffset: "10px", position: "bottom", backdrop: !0, backdropAboveModal: !0, highlightAnchor: !1, actions: [{ type: "next" }] }, { isMacroStep: !0, id: "macro_importRosterOnly", title: "Import People Only", text: "We already have setup roles. So we will import people only using a file. Note that you can import both roles and people together with a single file at any time. For now let's get your people imported.", anchorDataAttribute: "imports-type", backdrop: !0, backdropAboveModal: !0, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next", text: "Ok" }] }, { id: "hidden", hiddenStep: !0, actions: [{ type: "nextOnEvent", event: "imports-columns-matched" }] }, { id: "last", title: "Complete Import", text: "Click on complete import. We will take you to your chart after the import is complete.", customComponentName: "onboardingCompleteImport", anchorDataAttribute: "imports-complete-btn", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", actions: [{ type: "closeOnEvent", event: "imports-completed" }] }] }, importsTour: { id: "importsTour", title: "Import Basics", gotoStepOnEvent: { event: "imports-select-data-loaded", stepId: "select-data" }, steps: [{ id: "1", hiddenStep: !0 }, { id: "select-data", title: "Select Data", text: "Select the columns you want to import into Organimi and match it with Organimi's fields.", anchorDataAttribute: "app-top-bar", arrow: !1, anchorOffset: "10px", position: "bottom-end", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "nextOnEvent", event: "imports-columns-matched" }] }, { id: "4", title: "Complete Import", text: "Click on complete import. We will take you to your chart after this.", anchorDataAttribute: "imports-complete-btn", backdrop: !1, highlightAnchor: !1, ignoreAnchorZIndex: !0, position: "bottom", actions: [{ type: "completeOnEvent", event: "imports-completed" }] }] }, integrationTour: { id: "integrationTour", title: "Integrations Basics", steps: [{ id: "1", title: "Roster Integration", text: "You can import people from your system by creating a Roster Integration", anchorDataAttribute: "new-roster-integration-btn", backdrop: !0, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "next" }] }, { id: "2", title: "Chart Integration", text: "Or you can import both your org's role structure and people at the same time by creating a Chart Integration", anchorDataAttribute: "new-chart-integration-btn", backdrop: !0, highlightAnchor: !1, ignoreAnchorZIndex: !0, actions: [{ type: "back" }, { type: "next" }] }, { id: "2", title: "Create an Integration", text: "Click on either new roster integration or new chart integration button to continue.", anchorDataAttribute: "new-chart-integration-btn", ...r, actions: [{ type: "back" }, { type: "nextOnEvent", event: "integration-setup-loaded" }] }, { id: "setup", title: "Select & Filter Data", text: "You can add a filter data before importing, and select the inidividual fields to be imported by matching your source fields with Organimi fields.", ...r, backdrop: !0, actions: [{ type: "back" }, { type: "next" }] }, { ...a.macro_helpBtn, actions: [{ type: "back" }, { type: "complete" }] }] } } }, 42543: (e, t, n) => { "use strict";
                n.d(t, { A: () => ht }); var r = n(57528),
                    a = n(65043),
                    o = n(14556),
                    i = n(79091),
                    l = n(81635),
                    s = n(393),
                    c = n(23993),
                    d = n(78396),
                    u = n(45418),
                    h = n(70725),
                    m = n(356),
                    p = n(84091),
                    f = n(43331),
                    v = n(19367),
                    g = n(24115),
                    y = n(43862),
                    b = n(66856),
                    w = n(36138),
                    z = n(59177),
                    x = n(22264),
                    A = n(19924),
                    k = n(77820),
                    S = n(88159),
                    M = n(61531),
                    E = n(24738),
                    C = n(74772),
                    T = n(10621),
                    H = n(16091),
                    L = n(7743),
                    I = n(48853),
                    j = n(84),
                    V = n(96446),
                    O = n(24056),
                    R = n(42518),
                    P = n(85865),
                    D = n(37294),
                    F = n(66486),
                    N = n(72119),
                    _ = n(70318),
                    B = n(75156),
                    W = n(42517),
                    U = n(24241),
                    q = n(80286),
                    G = n(70579); const K = e => { let { field: t, value: n, objectId: r, orgId: o, fontDetails: i } = e; const l = ((null === t || void 0 === t ? void 0 : t.displayType) || (null === t || void 0 === t ? void 0 : t.type) || "").toLowerCase(),
                        [s, c] = (0, a.useState)({ fieldId: null === t || void 0 === t ? void 0 : t.id, isReadMore: !0 }),
                        d = e => { e !== s.fieldId && c({ fieldId: e, isReadMore: !1 }), e === s.fieldId && c((t => ({ fieldId: e, isReadMore: !t.isReadMore }))) },
                        u = e => { null === e || void 0 === e || e.stopPropagation() }; if ("string" === l) { if ((null === t || void 0 === t ? void 0 : t.name) === T.x2.PHONE) return (0, G.jsx)("span", { children: n ? (0, G.jsx)("a", { href: "tel:".concat(n), target: "_blank", rel: "noopener noreferrer", children: n }) : "------" }); if ((null === t || void 0 === t ? void 0 : t.name) === T.x2.EMAIL) return (0, G.jsx)("span", { children: n ? (0, G.jsx)("a", { href: "mailto:".concat(n), target: "_blank", rel: "noopener noreferrer", children: (0, z.RP)(n, 30) }) : "------" }); if (n) return (0, T.vK)(n) && (0, G.jsxs)("span", { children: [s.isReadMore && s.fieldId === (null === t || void 0 === t ? void 0 : t.id) && "string" === typeof n ? n.slice(0, 75) : n, "string" === typeof n && n.length > 75 && (0, G.jsxs)("a", { onClick: () => d(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em", color: D.Qs.Violet[500] }, children: ["\xa0", s.isReadMore && s.fieldId === (null === t || void 0 === t ? void 0 : t.id) ? "...more" : " ...less"] })] }) } if ("number" === l) return (0, G.jsx)("span", { children: (0, T.vK)(n) ? n : "------" }); if ("iconpicklist" === l && n && q.Md[n]) return (0, G.jsxs)(M.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, G.jsx)("img", { src: "".concat("https://assets-organimi.s3.amazonaws.com").concat(q.Md[n].path), width: 24 }), (0, G.jsx)("span", { children: q.Md[n].label })] }); if ("url" === l) return (0, G.jsx)("span", { children: n ? (0, G.jsxs)("a", { onClick: u, href: n, target: "_blank", rel: "noopener noreferrer", children: [(0, z.RP)(n, 40), " "] }) : "------" }); if ("attachment" === l) return n ? (0, G.jsx)(W.A, { orgId: o, model: null === t || void 0 === t ? void 0 : t.model, name: null === t || void 0 === t ? void 0 : t.id, objectId: r, extension: null === n || void 0 === n ? void 0 : n.extension, label: null === t || void 0 === t ? void 0 : t.label }) : "------"; if ("boolean" === l) return (0, G.jsx)(M.A, { children: (0, G.jsx)(B.Ay, { icon: "Checkbox", size: "lg" }) }); if ("switch" === l) return (0, G.jsx)(M.A, { children: (0, G.jsx)(B.Ay, { icon: "Toggle", size: "lg", color: "#eeeeee" }) }); if ("tags" === l) return (0, G.jsx)("span", { children: n ? (0, T.Hc)(n) : "------" }); if ("date" === l) return (0, G.jsx)("span", { children: n && "Invalid Date" !== n.toString() ? (0, T.Os)(n, null === i || void 0 === i ? void 0 : i.dateFormat) : "------" }); if ("computed" === l) { const e = U.c9.fromISO(n); return (0, G.jsx)("span", { children: null !== e && void 0 !== e && e.isValid ? (0, T.Os)(n, null === i || void 0 === i ? void 0 : i.dateFormat) : n }) } if ("location" === l) return (0, G.jsx)("span", { children: n ? (0, T.rS)(n, t) : "------" }); if ("richtext" === l) { if ("Sample value" === n) return (0, G.jsx)("span", { children: n }); if (!Array.isArray(n)) return (0, G.jsx)("span", { children: "------" }); if (Array.isArray(n)) { const e = !s.isReadMore && s.fieldId === (null === t || void 0 === t ? void 0 : t.id),
                                r = n.filter((e => null !== e && void 0 !== e)),
                                o = r.length,
                                i = o > 1; return r.map(((n, r) => e || r < 1 ? (0, G.jsxs)(a.Fragment, { children: ["" === n ? (0, G.jsx)("br", {}) : (0, G.jsx)("span", { children: (0, T.vK)(n) && n }), !e && 0 === r && i && (0, G.jsx)("a", { onClick: () => d(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em" }, children: "\xa0...more" }), e && r === o - 1 && i && (0, G.jsx)("a", { onClick: () => d(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em" }, children: "\xa0...less" })] }, r) : null)) } } var h; return "currency" === l ? "Sample value" === n ? (0, G.jsx)("span", { children: n }) : (0, G.jsx)("span", { children: n ? (0, T.Jw)(n, null === t || void 0 === t || null === (h = t.typeMetadata) || void 0 === h ? void 0 : h.currency) : "------" }) : "rollup" === l ? (0, G.jsx)("span", { children: void 0 !== n && null !== n ? (0, T.$)(n, t) : "------" }) : n ? (0, G.jsx)("span", { children: n }) : (0, G.jsx)(G.Fragment, {}) }; var Z, Y, X, $, Q, J, ee, te, ne, re, ae, oe, ie, le = n(35033),
                    se = n(82513),
                    ce = n(96364),
                    de = n(9368),
                    ue = n(55340),
                    he = n(80313),
                    me = n(41450),
                    pe = n(32115); const fe = (0, N.Ay)(V.A)(Z || (Z = (0, r.A)(["\n  width: 176px;\n  max-height: 315px;\n  display: grid;\n  justify-content: center;\n  flex-direction: column;\n  align-items: center;\n  margin: 46px 65px;\n  overflow-y: auto;\n"]))),
                    ve = N.Ay.div(Y || (Y = (0, r.A)(["\n  width: 153px;\n  display: grid;\n  grid-gap: 3px;\n  opacity: 0px;\n"]))),
                    ge = N.Ay.div(X || (X = (0, r.A)(["\n  font-family: Inter;\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 24.2px;\n  letter-spacing: -0.01em;\n  text-align: center;\n  text-underline-position: from-font;\n  text-decoration-skip-ink: none;\n  color: rgba(0, 0, 0, 1);\n"]))),
                    ye = N.Ay.div($ || ($ = (0, r.A)(["\n  font-family: Inter;\n  font-size: 16px;\n  font-weight: 400;\n  line-height: 19.36px;\n  letter-spacing: -0.01em;\n  text-align: center;\n  text-underline-position: from-font;\n  text-decoration-skip-ink: none;\n  color: rgba(153, 153, 153, 1);\n  text-wrap: wrap;\n"]))),
                    be = N.Ay.div(Q || (Q = (0, r.A)(["\n  width: 563px;\n"]))),
                    we = (0, N.Ay)(V.A)(J || (J = (0, r.A)(["\n  display: flex;\n  justify-content: right;\n  padding: 18px 36px;\n"]))),
                    ze = (0, N.Ay)(_.A)(ee || (ee = (0, r.A)(["\n  padding: 5px 8px;\n  font-size: 24px;\n"]))),
                    xe = (0, N.Ay)(V.A)(te || (te = (0, r.A)(["\n  display: grid;\n  margin: 18px 13px;\n  margin-bottom: 24px;\n  overflow-y: auto;\n  max-height: 245px;\n"]))),
                    Ae = N.Ay.div(ne || (ne = (0, r.A)(["\n  display: flex;\n  justify-content: space-between;\n  padding: 6px 23px;\n  gap: 16px;\n  &:nth-child(even) {\n    background: #f9f9fa;\n  }\n"]))),
                    ke = (0, N.Ay)(V.A)(re || (re = (0, r.A)(["\n  flex: 1 1 auto;\n  position: relative;\n  overflow-y: hidden;\n"]))),
                    Se = (0, N.Ay)(V.A)(ae || (ae = (0, r.A)(["\n  ", "\n"])), (e => { let { bannerColor: t, indicatorWidth: n } = e; return "\n  .MuiTabs-indicator{\n    background-color: ".concat(t, ";\n    min-width: ").concat(n, ";\n    max-width:100%;\n  }\n  .Mui-selected{\n    color: #000000 !important;\n  }\n") })),
                    Me = (0, N.Ay)(O.A)(oe || (oe = (0, r.A)(["\n  font-size: 10px !important;\n  font-weight: 600 !important;\n  color: #828282 !important;\n"]))),
                    Ee = (0, N.Ay)(V.A)(ie || (ie = (0, r.A)(['\n  position: relative;\n  display: inline-block;\n  &::after {\n    content: "";\n    position: absolute;\n    bottom: -15px;\n    left: 50%;\n    transform: translateX(-50%);\n    width: 70%;\n    height: 3px;\n    background: #424242;\n    border-radius: 50%;\n    filter: blur(7px);\n  }\n']))),
                    Ce = e => { var t, n; let { themeFieldsWithStyles: r, member: a, role: o, isSample: i, orgId: l } = e; return (0, G.jsx)(G.Fragment, { children: null === r || void 0 === r || null === (t = r.inTheme) || void 0 === t || null === (n = t.filter((e => !(null !== e && void 0 !== e && e.hidden || !i && (e => { var t, n; return void 0 === (null === e || void 0 === e || null === (t = e.fieldStyle) || void 0 === t ? void 0 : t.hideEmptyField) || (null === e || void 0 === e || null === (n = e.fieldStyle) || void 0 === n ? void 0 : n.hideEmptyField) })(e) && (0, T.i6)(e, a, o))))) || void 0 === n ? void 0 : n.map(((e, t) => { var n, r, s, c, d; const u = "member" === (null === e || void 0 === e || null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.model) ? a : o; return (0, G.jsxs)(Ae, { children: [(0, G.jsxs)(P.A, { fontSize: 14, color: D.Qs.Neutrals[800], fontWeight: 600, children: [null === e || void 0 === e || null === (r = e.fieldInfo) || void 0 === r ? void 0 : r.label, ":"] }), (0, G.jsx)(P.A, { fontSize: 14, color: D.Qs.Neutrals[600], fontWeight: 400, textAlign: "right", children: (0, G.jsx)(K, { objectId: null === u || void 0 === u ? void 0 : u.id, field: null === e || void 0 === e ? void 0 : e.fieldInfo, value: i ? "Sample value" : "member" === (null === e || void 0 === e || null === (s = e.fieldInfo) || void 0 === s ? void 0 : s.model) ? null === a || void 0 === a ? void 0 : a[null === e || void 0 === e || null === (c = e.fieldInfo) || void 0 === c ? void 0 : c.id] : null === o || void 0 === o ? void 0 : o[null === e || void 0 === e || null === (d = e.fieldInfo) || void 0 === d ? void 0 : d.id], fontDetails: null === e || void 0 === e ? void 0 : e.fieldStyle, orgId: l }, t) })] }) })) }) },
                    Te = e => { let { role: t, personRoles: n, roleNameFieldId: r, isSample: a, publicOrEmbedorCommunity: o } = e; const i = e => { const t = e.find((e => e.id === r)); return t ? t.value : "" }; return (0, G.jsx)(G.Fragment, { children: a ? (0, G.jsx)(ce.A, { children: " Role 1" }) : (0, G.jsx)(G.Fragment, { children: (n || []).length > 0 && (0, G.jsxs)(G.Fragment, { children: [o && (0, G.jsx)(V.A, { mt: 1, children: (0, G.jsx)(ue.A, { active: t.id, name: t[r], chartName: t.chartName, id: t.id, chartId: t.chartId, orgId: t.orgId, isClickable: !1 }) }), null === n || void 0 === n ? void 0 : n.map((e => (0, G.jsx)(ue.A, { active: (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id), name: i(e.fields), chartName: null === e || void 0 === e ? void 0 : e.chartName, id: null === e || void 0 === e ? void 0 : e.id, chartId: null === e || void 0 === e ? void 0 : e.chartId, orgId: null === e || void 0 === e ? void 0 : e.orgId, isClickable: !0 }, null === e || void 0 === e ? void 0 : e.id)))] }) }) }) },
                    He = e => { let { isMobile: t, role: n, connections: r, isSample: a } = e; return a ? (0, G.jsx)(ce.A, { children: " Connection 1" }) : (0, G.jsx)(se.Ay, { isMobile: t, role: n, connections: r }) },
                    Le = e => { var t, n, r, i, l, s, d; let { role: u, member: h, theme: m, themeFieldsWithStyles: p, themeBannerFieldsWithStyles: f, personRoles: v, showMoreThanOnePerson: g, handlePrevSharedPerson: y, handleNextSharedPerson: b, connections: w, roleNameFieldId: z, isSample: x, isMobile: A, editAccess: k, onClick: S, onClose: M, orgId: E } = e; const C = null === m || void 0 === m || null === (t = m.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n ? void 0 : n.photos,
                            T = null === m || void 0 === m || null === (r = m.data) || void 0 === r || null === (i = r.detailsPane) || void 0 === i || null === (l = i.layout) || void 0 === l ? void 0 : l.banner,
                            [H, L] = (0, a.useState)("details"),
                            I = (0, o.d4)(c.LH),
                            j = (0, de.A)(),
                            O = null === (s = I[null === u || void 0 === u ? void 0 : u.id]) || void 0 === s ? void 0 : s.bgcolor,
                            N = e => { var t, n, r, a, o, i, l, s, c, d, m, p, v; return "member" === (null === f || void 0 === f || null === (t = f.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === h || void 0 === h ? void 0 : h[null === f || void 0 === f || null === (o = f.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === u || void 0 === u ? void 0 : u[null !== (c = null === f || void 0 === f || null === (d = f.inTheme) || void 0 === d || null === (m = d.mainSection) || void 0 === m || null === (p = m[e]) || void 0 === p || null === (v = p.fieldInfo) || void 0 === v ? void 0 : v.id) && void 0 !== c ? c : ""] },
                            _ = e => { var t, n, r, a, o, i, l, s, c, d; let { index: m } = e; const p = "member" === (null === f || void 0 === f || null === (t = f.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[m]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? h : u; return (0, G.jsx)(K, { objectId: null === p || void 0 === p ? void 0 : p.id, field: null === f || void 0 === f || null === (o = f.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[m]) || void 0 === l ? void 0 : l.fieldInfo, value: N(m), fontDetails: null === f || void 0 === f || null === (s = f.inTheme) || void 0 === s || null === (c = s.mainSection) || void 0 === c || null === (d = c[m]) || void 0 === d ? void 0 : d.fieldStyle, orgId: E }) },
                            W = [{ value: "details", label: "Details" }];!x && !h || j || W.push({ value: "roles", label: "Roles" }), W.push({ value: "connections", label: "Connections" }); const U = e => { let { value: t, hidden: n, children: r } = e; return (0, G.jsx)("div", { "aria-labelledby": "details-pane-layout3-".concat(t), hidden: n, style: { overflow: "auto", position: "relative", height: "100%" }, children: r }) },
                            q = e => ({ details: (0, G.jsx)(Ce, { themeFieldsWithStyles: p, member: h, role: u, theme: m, isSample: x, orgId: E }), roles: (0, G.jsx)(Te, { role: u, personRoles: v, roleNameFieldId: z, isSample: x, publicOrEmbedorCommunity: j }), connections: (0, G.jsx)(He, { isMobile: A, member: h, role: u, connections: w, isSample: x }) } [e]),
                            Z = null !== T && void 0 !== T && T.showRoleColorAsBannerColor && O && "#ffffff" !== O && "#FFFFFF" !== O ? O : (null === T || void 0 === T ? void 0 : T.color) || "#00ACC0"; return (0, G.jsxs)(V.A, { display: "flex", children: [(0, G.jsxs)(V.A, { width: 307, height: 415, bgcolor: D.Qs.Neutrals[200], display: "flex", justifyContent: "center", position: "relative", children: [g && (0, G.jsx)(me.A, { handleLeftClick: y, handleRightClick: b }), (0, G.jsxs)(fe, { children: [(0, G.jsx)(Ee, { display: "flex", justifyContent: "center", flexDirection: "column", children: (0, G.jsx)(F.A, { size: (null === C || void 0 === C || null === (d = C.standard) || void 0 === d ? void 0 : d.size) || 100, shape: null === C || void 0 === C ? void 0 : C.shape, person: h, role: u }) }), (0, G.jsxs)(ve, { children: [(0, G.jsx)(ge, { children: (0, G.jsx)(_, { index: 0 }) }), (0, G.jsx)(ye, { children: (0, G.jsx)(_, { index: 1 }) })] }), (null === T || void 0 === T ? void 0 : T.showLegends) && !x && (0, G.jsx)(V.A, { display: "flex", justifyContent: "center", children: (0, G.jsx)(he.n, { role: u, person: h }) }), !x && k && (0, G.jsx)(R.A, { variant: "contained", color: "primary", onClick: S, children: (0, G.jsx)(P.A, { variant: pe.Eq.bodyMD, color: D.Qs.Neutrals[0], children: "Edit" }) })] })] }), (0, G.jsxs)(be, { children: [(0, G.jsx)(we, { children: !x && (0, G.jsx)(ze, { size: "medium", onClick: M, children: (0, G.jsx)(B.Ay, { icon: "Close" }) }) }), (0, G.jsxs)(ke, { children: [(0, G.jsx)(Se, { bannerColor: Z, indicatorWidth: 3 === W.length ? "187px" : "281px", children: (0, G.jsx)(le.A, { onChange: (e, t) => (e => { L(e) })(t), value: H, variant: "fullWidth", children: W.map((e => (0, G.jsx)(Me, { value: e.value, label: e.label, id: "details-pane-layout3-".concat(e.value) }, "details-pane-layout3-".concat(e.value)))) }) }), W.map((e => (0, G.jsx)(U, { hidden: H !== e.value, value: e.value, children: (0, G.jsx)(xe, { children: q(e.value) }) }, "details-pane-layout3-".concat(e.value))))] })] })] }) }; var Ie, je, Ve, Oe, Re, Pe, De, Fe, Ne, _e, Be; const We = (0, N.Ay)(V.A)(Ie || (Ie = (0, r.A)(["\n  display: flex;\n  flex-direction: row;\n  width: 512px;\n  min-height: 90px;\n  border-bottom: 1px solid #DADADA\n  border-radius: 4px 4px 0 0;\n  background-color: #F8F8F8;\n  padding: 13px 23px;\n  justify-content: space-between;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n"]))),
                    Ue = N.Ay.div(je || (je = (0, r.A)(["\n  font-family: Inter;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 19px;\n  letter-spacing: -0.01em;\n  text-underline-position: from-font;\n  text-decoration-skip-ink: none;\n  color: rgba(0, 0, 0, 1);\n"]))),
                    qe = N.Ay.div(Ve || (Ve = (0, r.A)(["\n  font-family: Inter;\n  font-size: 16px;\n  font-weight: 400;\n  line-height: 19.36px;\n  letter-spacing: -0.01em;\n  text-underline-position: from-font;\n  text-decoration-skip-ink: none;\n  color: rgba(153, 153, 153, 1);\n  text-wrap: wrap;\n"]))),
                    Ge = N.Ay.div(Oe || (Oe = (0, r.A)(["\n  width: 512px;\n"]))),
                    Ke = (0, N.Ay)(_.A)(Re || (Re = (0, r.A)(["\n  padding: 5px 8px;\n  font-size: 20px;\n  position: absolute;\n"]))),
                    Ze = (0, N.Ay)(V.A)(Pe || (Pe = (0, r.A)(["\n  display: grid;\n  margin: 18px 13px;\n  margin-bottom: 24px;\n  overflow-y: auto;\n  height: auto;\n  max-height: 210px;\n"]))),
                    Ye = N.Ay.div(De || (De = (0, r.A)(["\n  display: flex;\n  justify-content: space-between;\n  padding: 6px 23px;\n  gap: 16px;\n  &:nth-child(even) {\n    background: #f9f9fa;\n  }\n"]))),
                    Xe = (0, N.Ay)(V.A)(Fe || (Fe = (0, r.A)(["\n  flex: 1 1 auto;\n  position: relative;\n  overflow-y: hidden;\n"]))),
                    $e = (0, N.Ay)(V.A)(Ne || (Ne = (0, r.A)(["\n  ", "\n"])), (e => { let { bannerColor: t, indicatorWidth: n } = e; return "\n  .MuiTabs-indicator{\n    background-color: ".concat(t, ";\n    min-width: ").concat(n, ";\n    max-width:100%;\n  }\n  .Mui-selected{\n    color: #000000 !important;\n  }\n") })),
                    Qe = (0, N.Ay)(O.A)(_e || (_e = (0, r.A)(["\n  font-size: 10px !important;\n  font-weight: 600 !important;\n  color: #828282 !important;\n"]))),
                    Je = (0, N.Ay)(R.A)(Be || (Be = (0, r.A)(["\n  border: 1px solid ", ";\n  border-radius: 4px;\n  padding: 6px 12px;\n  align-items: center;\n  i {\n    font-size: 16px;\n  }\n  .fa-xs {\n    font-size: 16px;\n    padding-right: 5px;\n  }\n"])), D.Qs.Violet[500]),
                    et = e => { var t, n; let { themeFieldsWithStyles: r, member: a, role: o, isSample: i, orgId: l } = e; return (0, G.jsx)(G.Fragment, { children: null === r || void 0 === r || null === (t = r.inTheme) || void 0 === t || null === (n = t.filter((e => !(null !== e && void 0 !== e && e.hidden || !i && (e => { var t, n; return void 0 === (null === e || void 0 === e || null === (t = e.fieldStyle) || void 0 === t ? void 0 : t.hideEmptyField) || (null === e || void 0 === e || null === (n = e.fieldStyle) || void 0 === n ? void 0 : n.hideEmptyField) })(e) && (0, T.i6)(e, a, o))))) || void 0 === n ? void 0 : n.map(((e, t) => { var n, r, s, c, d; const u = "member" === (null === e || void 0 === e || null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.model) ? a : o; return (0, G.jsxs)(Ye, { children: [(0, G.jsxs)(P.A, { fontSize: 14, color: D.Qs.Neutrals[800], fontWeight: 600, children: [null === e || void 0 === e || null === (r = e.fieldInfo) || void 0 === r ? void 0 : r.label, ":"] }), (0, G.jsx)(P.A, { fontSize: 14, color: D.Qs.Neutrals[600], fontWeight: 400, textAlign: "right", children: (0, G.jsx)(K, { objectId: null === u || void 0 === u ? void 0 : u.id, field: null === e || void 0 === e ? void 0 : e.fieldInfo, value: i ? "Sample value" : "member" === (null === e || void 0 === e || null === (s = e.fieldInfo) || void 0 === s ? void 0 : s.model) ? null === a || void 0 === a ? void 0 : a[null === e || void 0 === e || null === (c = e.fieldInfo) || void 0 === c ? void 0 : c.id] : null === o || void 0 === o ? void 0 : o[null === e || void 0 === e || null === (d = e.fieldInfo) || void 0 === d ? void 0 : d.id], fontDetails: null === e || void 0 === e ? void 0 : e.fieldStyle, orgId: l }, t) })] }) })) }) },
                    tt = e => { let { role: t, personRoles: n, roleNameFieldId: r, isSample: a, publicOrEmbedorCommunity: o } = e; const i = e => { const t = e.find((e => e.id === r)); return t ? t.value : "" }; return (0, G.jsx)(G.Fragment, { children: a ? (0, G.jsx)(ce.A, { children: " Role 1" }) : (0, G.jsx)(G.Fragment, { children: (n || []).length > 0 && (0, G.jsxs)(G.Fragment, { children: [o && (0, G.jsx)(V.A, { mt: 1, children: (0, G.jsx)(ue.A, { active: t.id, name: t[r], chartName: t.chartName, id: t.id, chartId: t.chartId, orgId: t.orgId, isClickable: !1 }) }), null === n || void 0 === n ? void 0 : n.map((e => (0, G.jsx)(ue.A, { active: (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id), name: i(e.fields), chartName: null === e || void 0 === e ? void 0 : e.chartName, id: null === e || void 0 === e ? void 0 : e.id, chartId: null === e || void 0 === e ? void 0 : e.chartId, orgId: null === e || void 0 === e ? void 0 : e.orgId, isClickable: !0 }, null === e || void 0 === e ? void 0 : e.id)))] }) }) }) },
                    nt = e => { let { isMobile: t, role: n, connections: r, isSample: a } = e; return a ? (0, G.jsx)(ce.A, { children: " Connection 1" }) : (0, G.jsx)(se.Ay, { isMobile: t, role: n, connections: r }) },
                    rt = e => { var t, n, r, i, l, s, d; let { role: u, member: h, theme: m, themeFieldsWithStyles: p, themeBannerFieldsWithStyles: f, personRoles: v, showMoreThanOnePerson: g, handlePrevSharedPerson: y, handleNextSharedPerson: b, connections: w, roleNameFieldId: z, isSample: x, isMobile: A, editAccess: k, onClick: S, onClose: M, orgId: E } = e; const C = null === m || void 0 === m || null === (t = m.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n ? void 0 : n.photos,
                            T = null === m || void 0 === m || null === (r = m.data) || void 0 === r || null === (i = r.detailsPane) || void 0 === i || null === (l = i.layout) || void 0 === l ? void 0 : l.banner,
                            [H, L] = (0, a.useState)("details"),
                            I = (0, o.d4)(c.LH),
                            j = (0, de.A)(),
                            O = null === (s = I[null === u || void 0 === u ? void 0 : u.id]) || void 0 === s ? void 0 : s.bgcolor,
                            R = e => { var t, n, r, a, o, i, l, s, c, d, m, p, v; return "member" === (null === f || void 0 === f || null === (t = f.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[e]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? null === h || void 0 === h ? void 0 : h[null === f || void 0 === f || null === (o = f.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[e]) || void 0 === l || null === (s = l.fieldInfo) || void 0 === s ? void 0 : s.id] : null === u || void 0 === u ? void 0 : u[null !== (c = null === f || void 0 === f || null === (d = f.inTheme) || void 0 === d || null === (m = d.mainSection) || void 0 === m || null === (p = m[e]) || void 0 === p || null === (v = p.fieldInfo) || void 0 === v ? void 0 : v.id) && void 0 !== c ? c : ""] },
                            N = e => { var t, n, r, a, o, i, l, s, c, d; let { index: m } = e; const p = "member" === (null === f || void 0 === f || null === (t = f.inTheme) || void 0 === t || null === (n = t.mainSection) || void 0 === n || null === (r = n[m]) || void 0 === r || null === (a = r.fieldInfo) || void 0 === a ? void 0 : a.model) ? h : u; return (0, G.jsx)(K, { objectId: null === p || void 0 === p ? void 0 : p.id, field: null === f || void 0 === f || null === (o = f.inTheme) || void 0 === o || null === (i = o.mainSection) || void 0 === i || null === (l = i[m]) || void 0 === l ? void 0 : l.fieldInfo, value: R(m), fontDetails: null === f || void 0 === f || null === (s = f.inTheme) || void 0 === s || null === (c = s.mainSection) || void 0 === c || null === (d = c[m]) || void 0 === d ? void 0 : d.fieldStyle, orgId: E }) },
                            _ = [{ value: "details", label: "Details" }];!x && !h || j || _.push({ value: "roles", label: "Roles" }), _.push({ value: "connections", label: "Connections" }); const W = e => { let { value: t, hidden: n, children: r } = e; return (0, G.jsx)("div", { "aria-labelledby": "details-pane-layout4-".concat(t), hidden: n, style: { overflow: "auto", position: "relative", height: "100%" }, children: r }) },
                            U = e => ({ details: (0, G.jsx)(et, { themeFieldsWithStyles: p, member: h, role: u, theme: m, isSample: x, orgId: E }), roles: (0, G.jsx)(tt, { role: u, personRoles: v, roleNameFieldId: z, isSample: x, publicOrEmbedorCommunity: j }), connections: (0, G.jsx)(nt, { isMobile: A, member: h, role: u, connections: w, isSample: x }) } [e]),
                            q = null !== T && void 0 !== T && T.showRoleColorAsBannerColor && O && "#ffffff" !== O && "#FFFFFF" !== O ? O : (null === T || void 0 === T ? void 0 : T.color) || "#00ACC0"; return (0, G.jsxs)(V.A, { width: 512, overflow: "hidden", justifyContent: "space-between", children: [(0, G.jsxs)(We, { style: g ? { paddingLeft: 45, paddingRight: 45 } : {}, display: "flex", justifyContent: "space-between", children: [g && (0, G.jsx)(me.A, { handleLeftClick: y, handleRightClick: b }), (0, G.jsxs)(V.A, { display: "flex", alignItems: "center", gap: 2, children: [(0, G.jsx)(F.A, { size: (null === C || void 0 === C || null === (d = C.standard) || void 0 === d ? void 0 : d.size) || 100, shape: null === C || void 0 === C ? void 0 : C.shape, person: h, role: u }), (0, G.jsxs)(V.A, { display: "flex", gap: "2px", flexDirection: "column", alignItems: "self-start", justifyContent: "start", children: [(0, G.jsx)(Ue, { children: (0, G.jsx)(N, { index: 0 }) }), (0, G.jsx)(qe, { children: (0, G.jsx)(N, { index: 1 }) }), (null === T || void 0 === T ? void 0 : T.showLegends) && !x && (0, G.jsx)(V.A, { display: "flex", paddingTop: "6px", justifyContent: "flex-start", children: (0, G.jsx)(he.n, { role: u, person: h }) })] })] }), (0, G.jsxs)(V.A, { display: "flex", gap: 1, flexDirection: "column", justifyContent: "end", alignItems: "end", children: [!x && (0, G.jsx)(Ke, { onClick: M, children: (0, G.jsx)(B.gF, { size: "sm", icon: "Close" }) }), !x && k && (0, G.jsxs)(Je, { color: "primary", variant: "outlined", onClick: S, size: "small", children: [(0, G.jsx)(B.gF, { size: "xs", icon: "UserEdit" }), (0, G.jsx)(P.A, { variant: pe.Eq.bodySM, color: D.Qs.Violet[500], children: "Edit" })] })] })] }), (0, G.jsx)(Ge, { children: (0, G.jsxs)(Xe, { children: [(0, G.jsx)($e, { bannerColor: q, indicatorWidth: 3 === _.length ? "187px" : "281px", children: (0, G.jsx)(le.A, { onChange: (e, t) => (e => { L(e) })(t), value: H, variant: "fullWidth", children: _.map((e => (0, G.jsx)(Qe, { value: e.value, label: e.label, id: "details-pane-layout4-".concat(e.value) }, "details-pane-layout4-".concat(e.value)))) }) }), _.map((e => (0, G.jsx)(W, { hidden: H !== e.value, value: e.value, children: (0, G.jsx)(Ze, { children: U(e.value) }) }, "details-pane-layout4-".concat(e.value))))] }) })] }) },
                    at = e => { var t; let { person: n, role: r, allPersonRoles: i, connections: s, layout: c, theme: h, orgId: m, isSample: p, onClose: f } = e; const g = C.A.find((e => e.id === c)),
                            y = "single",
                            b = (0, o.wA)(),
                            w = (0, o.d4)(l.FM),
                            z = (0, o.d4)((e => (0, u.Yk)(e, { ids: (null === r || void 0 === r ? void 0 : r.members) || [] }))),
                            x = (0, o.d4)(E.Dy),
                            A = (0, a.useMemo)((() => x(y, null === g || void 0 === g ? void 0 : g.fields)), [y, x]),
                            V = (0, o.d4)(E.wB),
                            O = (0, a.useMemo)((() => V(y, null === g || void 0 === g ? void 0 : g.fields)), [y, V]),
                            [R, P] = (0, a.useState)(O),
                            [D, F] = (0, a.useState)(A),
                            N = (0, o.d4)(L.gP),
                            _ = (0, o.d4)(L.KN),
                            B = (0, o.d4)(L.gJ),
                            W = null === B || void 0 === B || null === (t = B.roleName) || void 0 === t ? void 0 : t.id; let U, q; if (p) { var K, Z, Y, X; const e = null === (K = B[T.x2.EMAIL]) || void 0 === K ? void 0 : K.id,
                                t = null === (Z = B[T.x2.PHONE]) || void 0 === Z ? void 0 : Z.id,
                                n = null === (Y = B[T.x2.LINKEDIN]) || void 0 === Y ? void 0 : Y.id,
                                r = null === (X = B[T.dj.LOCATIONADDRESS]) || void 0 === X ? void 0 : X.id; let { role: a, member: o } = (0, H.A)("detailsPaneThemeData");
                            a[W] = a.name, a[r] = a.locationAddress, o[e] = o.email, o[t] = o.phone, o[n] = o.linkedIn, U = (0, T.YW)(a, _), q = (0, T.YW)(o, N) } else U = r, q = n; const $ = (0, o.d4)(v.Mk),
                            { userHasMinAccess: Q, userType: J } = (0, I.A)(),
                            ee = (0, o.d4)(v.kw),
                            { syncEnabled: te } = ee || {},
                            ne = (null === $ || void 0 === $ ? void 0 : $.type) !== d.XD.BOARD_OF_DIRECTORS && Q(d.td.EDITOR) && !te && !(J === d.td.EDITOR && !r),
                            { openDialog: re } = (0, j.A)("newRole");
                        (0, a.useEffect)((() => { F(A), P(O) }), [A, O]); const ae = (e, t) => () => { b((0, l.gN)({ activePersonId: e, mode: "view", selectedRoleId: t })) },
                            oe = () => { const e = z.findIndex((e => e.id === (null === n || void 0 === n ? void 0 : n.id))),
                                    t = z[(e - 1 + z.length) % z.length];
                                null !== t && void 0 !== t && t.id && b((0, l.gN)({ ...w, activePersonId: null === t || void 0 === t ? void 0 : t.id })) },
                            ie = () => { const e = z.findIndex((e => e.id === (null === n || void 0 === n ? void 0 : n.id))),
                                    t = z[(e + 1) % z.length];
                                null !== t && void 0 !== t && t.id && b((0, l.gN)({ ...w, activePersonId: null === t || void 0 === t ? void 0 : t.id })) },
                            le = () => { r ? re({ role: r, members: z }) : (b((0, l.Ch)()), b((0, l.gN)({ ...w, mode: "edit" }))) }; return (0, G.jsx)(M.A, { height: "100%", children: (() => { switch (c) {
                                    case "layout-1":
                                        return (0, G.jsx)(k.A, { role: U, member: q, personRoles: i, showMoreThanOnePerson: (null === z || void 0 === z ? void 0 : z.length) > 1, handlePrevSharedPerson: oe, handleNextSharedPerson: ie, connections: s, roleNameFieldId: W, theme: h, themeFieldsWithStyles: D, themeBannerFieldsWithStyles: R, orgId: m, handleClick: ae, isSample: p, editAccess: ne, onClick: le, onClose: f });
                                    case "layout-2":
                                        return (0, G.jsx)(S.A, { role: U, member: q, personRoles: i, showMoreThanOnePerson: (null === z || void 0 === z ? void 0 : z.length) > 1, handlePrevSharedPerson: oe, handleNextSharedPerson: ie, connections: s, roleNameFieldId: W, theme: h, themeFieldsWithStyles: D, themeBannerFieldsWithStyles: R, orgId: m, handleClick: ae, isSample: p, editAccess: ne, onClick: le, onClose: f });
                                    case "layout-3":
                                        return (0, G.jsx)(Le, { role: U, member: q, personRoles: i, showMoreThanOnePerson: (null === z || void 0 === z ? void 0 : z.length) > 1, handlePrevSharedPerson: oe, handleNextSharedPerson: ie, connections: s, roleNameFieldId: W, theme: h, themeFieldsWithStyles: D, themeBannerFieldsWithStyles: R, orgId: m, handleClick: ae, isSample: p, editAccess: ne, onClick: le, onClose: f });
                                    case "layout-4":
                                        return (0, G.jsx)(rt, { role: U, member: q, personRoles: i, showMoreThanOnePerson: (null === z || void 0 === z ? void 0 : z.length) > 1, handlePrevSharedPerson: oe, handleNextSharedPerson: ie, connections: s, roleNameFieldId: W, theme: h, themeFieldsWithStyles: D, themeBannerFieldsWithStyles: R, orgId: m, handleClick: ae, isSample: p, editAccess: ne, onClick: le, onClose: f });
                                    default:
                                        return null } })() }) }; var ot, it, lt = n(35801); const st = (0, N.Ay)(lt.A)(ot || (ot = (0, r.A)(["\n  ", "\n"])), (e => { let { isSample: t } = e; return "\n    &.MuiDialog-root {\n      z-index: ".concat(t && "0 !important", ";\n    }\n    .MuiDialog-paperWidthSm {\n      // max-width: 870px;\n      // max-height: 415px;\n      min-width: 870px;\n      min-height: 415px;\n      left: ").concat(t ? 230 : 0, "px;\n    }\n    .MuiButton-label {\n      text-transform: none;\n    }\n    .MuiBackdrop-root {\n      background-color: rgba(222, 222, 222, 0);\n    }\n  ") })),
                    ct = (0, N.Ay)(lt.A)(it || (it = (0, r.A)(["\n  ", "\n"])), (e => { let { isSample: t } = e; return "\n    &.MuiDialog-root {\n      z-index: ".concat(t && "0 !important", ";\n    }\n    .MuiDialog-paperWidthSm {\n      min-width: 512px;\n      min-height: 395px;\n      // max-width: 512px;\n      // max-height: 395px;\n      left: ").concat(t ? 230 : 0, "px;\n    }\n    .MuiButton-label {\n      text-transform: none;\n    }\n    .MuiBackdrop-root {\n      background-color: rgba(222, 222, 222, 0);\n    }\n  ") })),
                    dt = { direct: [], indirect: [], manager: null },
                    ut = ["public", "embed"],
                    ht = () => { var e, t, n; const r = (0, o.wA)(),
                            [k, S] = (0, a.useState)(dt),
                            [M, E] = (0, a.useState)([]),
                            [C, H] = (0, a.useState)(!0),
                            [L, I] = (0, a.useState)(!0),
                            j = (0, x.A)(),
                            { newActionCardState: V } = j || {},
                            [, O] = V || [],
                            R = (0, o.d4)(l.t0),
                            P = (0, o.d4)(l.FM),
                            D = (0, o.d4)(f.Ot),
                            F = (0, w.u)([(0, b.si)(), (0, b.Zm)(), (0, b.N9)()]),
                            { base: N } = (null === F || void 0 === F ? void 0 : F.params) || {},
                            _ = ut.includes(N); let { mode: B, activePersonId: W, selectedRoleId: U, defaultRole: q, isSample: K } = P || {}; const Z = (0, o.d4)((e => (0, c.gn)(e.roles, U))),
                            Y = (0, o.d4)(v.Pq),
                            X = (0, o.d4)(f.mn),
                            [$, Q] = (0, a.useState)(!1),
                            J = (0, o.d4)(A.hK),
                            ee = null === J || void 0 === J || null === (e = J.data) || void 0 === e || null === (t = e.detailsPane) || void 0 === t || null === (n = t.layout) || void 0 === n ? void 0 : n.name,
                            te = (0, a.useMemo)((() => { let e = Z; if (!Z && q && (e = q), e) { const t = D.find((t => { let { id: n } = t; return n === e.chart })); return { ...e, chartName: (0, z.RP)(null === t || void 0 === t ? void 0 : t.name, 25) || "" } } return null }), [Z, q]); var ne, re; if (!K)
                            if (!W && null !== te && void 0 !== te && null !== (ne = te.members) && void 0 !== ne && ne.length) W = null === te || void 0 === te ? void 0 : te.members[0], W = (null === (re = W) || void 0 === re ? void 0 : re.id) || W;
                            else if (null !== te && void 0 !== te && te.members && -1 === (null === te || void 0 === te ? void 0 : te.members.indexOf(W))) { var ae;
                            W = null === te || void 0 === te ? void 0 : te.members[0], W = (null === (ae = W) || void 0 === ae ? void 0 : ae.id) || W } const oe = (0, o.d4)((e => (0, u.Ar)(e.people, W)));
                        (0, a.useEffect)((() => { R && r((0, i.Qs)()) }), []); const ie = () => { Q(!0);
                            document.querySelector(".dialog-paper") ? setTimeout((() => { r((0, l.Ch)()), Q(!1) }), 900) : r((0, l.Ch)()), r((0, y.Ic)()) };
                        (0, a.useEffect)((() => { "view" === (null === P || void 0 === P ? void 0 : P.mode) && m.A.trackEvent({ eventName: "PREVIEW_CARD" }) }), []), (0, a.useEffect)((() => { W && !_ ? async function() { var e, t;
                                I(!0); const { error: n, payload: a } = await r(p.OH.getRolesByPerson({ orgId: X || (null === F || void 0 === F || null === (e = F.params) || void 0 === e ? void 0 : e.orgId), personId: "object" === typeof W ? null === (t = W) || void 0 === t ? void 0 : t.id : W }));
                                I(!1), n || (null === a || void 0 === a ? void 0 : a.personId) === W && E(a.roles) }(): (I(!1), E([])) }), [W]); const le = (0, a.useMemo)((() => null !== M && void 0 !== M && M.length ? M.map((e => { const t = D.find((t => { let { id: n } = t; return e.chart === n })); return t ? { ...e, active: U === e.id, chartName: t.name, chartId: t.id, orgId: t.organization } : null })).filter((e => e)).sort((e => e.chart === Y ? -1 : 0)) : []), [M, D]),
                            se = e => e ? { ...e, members: (e.members || []).map((e => (0, T.LS)(e))) } : null;
                        (0, a.useEffect)((() => { te ? async function() { var e;
                                H(!0); const { error: t, payload: { directReports: n, indirectReports: a, reportsTo: o } } = await r(y.h7.getRoleConnections({ orgId: X, chartId: Y, roleId: U }));
                                H(!1); const i = (n || []).map(se),
                                    l = null === a || void 0 === a || null === (e = a.map(se)) || void 0 === e ? void 0 : e.filter((e => e)),
                                    s = se(o);
                                t || S({ indirect: l, direct: i, manager: s }) }(): (H(!1), S(dt)), "function" === typeof O && O(null) }), [te]), (0, a.useEffect)((() => () => { r((0, l.Ch)()) }), [Y]); const ce = L || C,
                            de = "view" === B,
                            ue = "edit" === B,
                            he = "add" === B,
                            me = (M || []).find((e => e.chart === Y)),
                            pe = !(!(X && ee && de) || K || !oe && !te) || !!(de && K || ue || he),
                            fe = e => R && (0, G.jsxs)(e, { open: R, isSample: K, "aria-labelledby": "flip-dialog-title", classes: { paper: "dialog-paper ".concat($ ? "dialog-paper-exit" : "") }, children: [de && !K && (0, G.jsx)(at, { person: oe, role: te, allPersonRoles: le, connections: k, layout: ee, theme: J, orgId: X, isSample: !1, onClose: ie }), de && K && (0, G.jsx)(at, { layout: ee, theme: J, orgId: X, isSample: !0 }), (ue || he) && (0, G.jsx)(h.A, { mode: B, person: oe || {}, activeChartRole: me })] }); return (0, G.jsx)(G.Fragment, { children: pe && (0, G.jsxs)(G.Fragment, { children: ["layout-3" === ee && R && fe(st), "layout-4" === ee && R && fe(ct), ee && "layout-3" !== ee && "layout-4" !== ee && R && (0, G.jsx)(s.Ay, { width: d.Ay.dialogs.profileCardWidth, "data-tour-anchor": "profile-container", children: (0, G.jsx)(g.A, { loading: ce, children: (0, G.jsxs)(G.Fragment, { children: [de && !K && (0, G.jsx)(at, { person: oe, role: te, allPersonRoles: le, connections: k, layout: ee, theme: J, orgId: X, isSample: !1, onClose: ie }), de && K && (0, G.jsx)(at, { layout: ee, theme: J, orgId: X, isSample: !0 }), (ue || he) && (0, G.jsx)(h.A, { mode: B, person: oe || {}, activeChartRole: me })] }) }) })] }) }) } }, 70725: (e, t, n) => { "use strict";
                n.d(t, { A: () => T }); var r = n(65043),
                    a = n(96364),
                    o = n(14556),
                    i = n(36138),
                    l = n(96446),
                    s = n(42518),
                    c = n(84),
                    d = n(61258),
                    u = n(7743),
                    h = n(66856),
                    m = n(50330),
                    p = n(43331),
                    f = n(81635),
                    v = n(2173),
                    g = n(84091),
                    y = n(37869),
                    b = n(3342),
                    w = n(10621),
                    z = n(24115),
                    x = n(14308),
                    A = n(43940),
                    k = n(70318),
                    S = n(75156),
                    M = n(5560),
                    E = n(91357),
                    C = n(70579); const T = e => { var t, n, T; let { mode: H, person: L, activeChartRole: I, onSaveCB: j, onCancelCB: V } = e; const O = (0, o.wA)(),
                        { onTourEventComplete: R } = (0, A.M)({}),
                        [P, D] = (0, r.useState)({ photo: L.photo, memberPhotoColor: L.memberPhotoColor }),
                        F = (0, o.d4)(p.mn),
                        N = (0, o.d4)(p.BF),
                        _ = (0, o.d4)(x.Fi),
                        B = (0, o.d4)(u.gJ),
                        W = (0, o.d4)(u.gP),
                        U = (0, o.d4)(f.FM),
                        q = (0, o.d4)(u.rq),
                        G = (0, i.u)([(0, h.N9)(), (0, h.si)()]),
                        K = null === G || void 0 === G || null === (t = G.params) || void 0 === t ? void 0 : t.chartId,
                        { openDialog: Z } = (0, c.A)("customFields"),
                        { openDialog: Y } = (0, c.A)("memberPhoto"),
                        { openDialog: X } = (0, c.A)("duplicatePersonFoundDialog"),
                        $ = null === W || void 0 === W ? void 0 : W.reduce(((e, t) => (e[t.id] || (e[t.id] = L[t.id]), e)), {}),
                        Q = (0, d.mN)({ defaultValues: { ...$ } }),
                        { register: J, watch: ee, setValue: te, handleSubmit: ne, setError: re, clearErrors: ae, errors: oe, reset: ie } = Q,
                        le = null === (n = B[w.x2.FIRSTNAME]) || void 0 === n ? void 0 : n.id,
                        se = null === (T = B[w.x2.LASTNAME]) || void 0 === T ? void 0 : T.id,
                        ce = ee(le),
                        de = ee(se),
                        ue = "".concat(ce || "", " ").concat(de || ""),
                        he = e => { let { id: t, name: n } = e; return e => {!b.A.MemberFieldValidators[n] || b.A.MemberFieldValidators[n](e) ? ae(t) : re(t, { message: b.A.ERROR_MESSAGES[n], shouldFocus: !0 }) } },
                        me = e => { var t; const n = null !== e && void 0 !== e && null !== (t = e.newPhoto) && void 0 !== t && t.deleted ? M.MemberPhotoFieldDeletedValue : null === e || void 0 === e ? void 0 : e.newPhoto;
                            delete e.newPhoto, D({ ...e || {}, newMemberPhoto: n }) },
                        pe = (e, t) => { ce || de || (te(le, e), te(se, t)) },
                        fe = () => V ? V() : "function" === typeof U.onCancelCB ? U.onCancelCB() : O((0, f.gN)({ ...U, mode: "view" })),
                        ve = e => { var t, n, r;
                            re(null === (t = Object.keys(e)) || void 0 === t ? void 0 : t[0], { message: b.A.ERROR_MESSAGES[null === (n = Object.keys(e)) || void 0 === n || null === (r = n[0]) || void 0 === r ? void 0 : r.message], shouldFocus: !0 }) },
                        ge = async function(e, t) { var n, r, a; let o = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (null !== (n = Object.keys(oe)) && void 0 !== n && n.length) return ve(oe); const i = "saveAddMore" === (null === t || void 0 === t || null === (r = t.nativeEvent) || void 0 === r || null === (a = r.submitter) || void 0 === a ? void 0 : a.name),
                                l = (0, w.YW)(e, W); if ("edit" === H) { const { error: e } = await O(g.OH.updatePerson({ orgId: F, personId: L.id, data: { ...P, ...l }, chartId: K })); if (!e && i) return ie({ defaultValues: { ...$ } });
                                e || ye(L.id) } if ("add" === H) { var s; const { error: t, payload: n } = await O(g.OH.addPerson({ orgId: F, data: { ...P, ...l }, chartId: K, forceDuplicate: o })); if (null !== n && void 0 !== n && n.duplicatesFound && null !== n && void 0 !== n && null !== (s = n.duplicates) && void 0 !== s && s.length) return void X({ fields: q, duplicatePeople: n.duplicates.map(w.SB), action: ge.bind(null, e, null, !0) }); var c; if (!t && i && ie({ defaultValues: { ...$ } }), !t && !i) ye(null === (c = n.person) || void 0 === c ? void 0 : c.id);
                                R({ event: "talent-pool-person-added" }) } }, ye = e => j ? j() : "function" === typeof U.onSaveCB ? U.onSaveCB() : O((0, f.gN)({ ...U, activePersonId: e, mode: "view" })), [be, we] = (0, v.A)(ne(ge, ve)), ze = "add" === H, xe = P.newMemberPhoto, Ae = L.photo, ke = (0, r.useMemo)((() => xe === M.MemberPhotoFieldDeletedValue ? null : "string" === typeof xe && xe ? xe : Ae), [L.id, xe, Ae]), Se = (0, C.jsx)(y.A, { handleChangePhotoClick: () => { Y({ member: null === L || void 0 === L ? void 0 : L.name, handleChangePhoto: me, handleLinkedInSearch: pe, type: "photo", photo: ke, organization: null === N || void 0 === N ? void 0 : N.name, initialAvatarBackgroundColor: null === L || void 0 === L ? void 0 : L.memberPhotoColor }) }, person: { ...L, name: ue, ...P, photo: ke }, size: 90, handle: !0, showEdit: !0 }); return (0, C.jsxs)(l.A, { display: "flex", flexDirection: "column", justifyContent: "space-between", wrap: "nowrap", gap: 2, p: 3, overflow: "auto", children: [(0, C.jsx)(l.A, { position: "absolute", right: 16, zIndex: 1, children: (0, C.jsx)(k.A, { disabled: be, onClick: fe, children: (0, C.jsx)(S.Ay, { icon: "Close" }) }) }), (0, C.jsxs)(z.A, { loading: be, transparent: !0, zIndex: 3, children: [(0, C.jsx)(l.A, { m: 2, display: "flex", justifyContent: "center", children: Se }), (0, C.jsx)(l.A, { m: 2, children: (0, C.jsx)(d.Op, { ...Q, children: (0, C.jsxs)("form", { id: "personForm", onSubmit: we, children: [W.map(((e, t) => (0, C.jsx)(E.j, { field: e, children: (0, C.jsx)(m.A, { syncLocked: I && _["member.".concat(e.id)], field: e, label: e.label, name: e.id, modelId: null === L || void 0 === L ? void 0 : L.id, inputRef: J(e.inputRefParams), orgId: F, value: L[e.id], onFieldChange: he(e), error: oe[e.id], autoFocus: 0 === t }, e.id) }))), (0, C.jsx)(l.A, { mt: 1, mb: 1, children: (0, C.jsx)(a.A, { variant: "body2", component: "a", onClick: () => { Z({ field: { model: "member" } }) }, children: "Add More Fields\xa0>>" }) }), (0, C.jsxs)(l.A, { mt: 1, mb: 5, display: "flex", justifyContent: "space-between", gap: 1, children: [!ze && (0, C.jsx)(s.A, { style: { flex: 1 }, disabled: be, onClick: fe, variant: "outlined", color: "primary", children: "Cancel" }), ze && (0, C.jsx)(s.A, { style: { flex: 2 }, name: "saveAddMore", disabled: be, variant: "outlined", color: "primary", type: "submit", form: "personForm", children: "Save & Add More" }), (0, C.jsx)(s.A, { style: { flex: 1 }, disabled: be, variant: "contained", color: "primary", onClick: we, children: "Save" })] })] }) }) })] })] }) } }, 81635: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, Ch: () => l, FM: () => o, gN: () => i, t0: () => a }); const r = (0, n(80907).Z0)({ name: "profileCard", initialState: { open: !1, props: {} }, reducers: { openProfileCard: (e, t) => { let { payload: n } = t;
                                e.open = !0, e.props = n || {} }, closeProfileCard: e => { e.open = !1, e.props = {} } } }),
                    a = e => e.profileCard.open,
                    o = e => e.profileCard.props,
                    { openProfileCard: i, closeProfileCard: l } = r.actions,
                    s = r.reducer }, 14308: (e, t, n) => { "use strict";
                n.d(t, { Dy: () => d, Fi: () => l, Pq: () => o.Pq, hK: () => a.A, wB: () => c, zN: () => s }); var r = n(80192),
                    a = n(30752),
                    o = n(19367),
                    i = n(7743); const l = (0, r.Mz)((e => { var t; return null === (t = e.chart) || void 0 === t ? void 0 : t.integration }), (e => { var t; if (!(null === e || void 0 === e ? void 0 : e.syncEnabled)) return {}; return ((null === e || void 0 === e || null === (t = e.params) || void 0 === t ? void 0 : t.columnMap) || []).reduce(((e, t) => { if (null === t || void 0 === t || !t.selectedValue) return e; return e["".concat(t.model, ".").concat(t.fieldId)] = !0, e }), {}) })),
                    s = (0, r.Mz)(a.A, (e => { var t; return null === e || void 0 === e || null === (t = e.data) || void 0 === t ? void 0 : t.detailsPane })),
                    c = (0, r.Mz)(i.bH, a.A, ((e, t) => (n, r) => { var a, o, i, l, s, c, d, u, h, m, p, f; let v = "single" !== n ? null === t || void 0 === t || null === (a = t.roleTypeOverrides) || void 0 === a || null === (o = a[n]) || void 0 === o ? void 0 : o.fields : null === t || void 0 === t || null === (i = t.data) || void 0 === i || null === (l = i.detailsPane) || void 0 === l || null === (s = l.layout) || void 0 === s || null === (c = s.banner) || void 0 === c ? void 0 : c.fields;
                        v = (!v || 0 === (null === (d = v) || void 0 === d ? void 0 : d.length)) && r || v; const g = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]) && (null === e || void 0 === e ? void 0 : e.isDefault))).reduce(((e, t) => { var n; const r = t.id; if (v = null === (n = v) || void 0 === n ? void 0 : n.map((e => { var n, r; return (null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.name) === (null === t || void 0 === t ? void 0 : t.name) && (null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.model) === (null === t || void 0 === t ? void 0 : t.model) && (e.field.id = null === t || void 0 === t ? void 0 : t.id), e })), v) { const n = [...v.keys()].filter((e => { var t, n; return null !== v[e] && (null === (t = v[e]) || void 0 === t || null === (n = t.field) || void 0 === n ? void 0 : n.id) === r }));
                                    n.length > 0 ? null === n || void 0 === n || n.forEach((n => { e.inTheme[n] = { fieldKey: r, fieldInfo: t, fieldSection: v[n].field.section, fieldStyle: v[n].fontDetails } })) : e.notInTheme.push({ fieldKey: r, fieldInfo: t }) } else e.notInTheme.push({ fieldKey: r, fieldInfo: t }); return e }), { inTheme: Array.from(Array(null === (u = v) || void 0 === u ? void 0 : u.length)), notInTheme: [], otherOrgFields: [] }),
                            y = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (h = v) || void 0 === h || h.forEach(((e, t) => { var n, r;
                            y.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || g.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), g.inTheme = null === (m = g.inTheme) || void 0 === m ? void 0 : m.filter((e => e)), g.inTheme.mainSection = null === (p = g.inTheme) || void 0 === p ? void 0 : p.filter((e => "main-section" === e.fieldSection)), g.inTheme.iconSection = null === (f = g.inTheme) || void 0 === f ? void 0 : f.filter((e => "icon-section" === e.fieldSection)), g })),
                    d = (0, r.Mz)(i.Z7, a.A, ((e, t) => (n, r) => { var a, o, i, l, s, c, d, u, h, m, p, f; let v = "single" !== n ? null === t || void 0 === t || null === (a = t.roleTypeOverrides) || void 0 === a || null === (o = a[n]) || void 0 === o ? void 0 : o.fields : null === t || void 0 === t || null === (i = t.data) || void 0 === i || null === (l = i.detailsPane) || void 0 === l ? void 0 : l.fields,
                            g = null === t || void 0 === t || null === (s = t.data) || void 0 === s || null === (c = s.detailsPane) || void 0 === c || null === (d = c.layout) || void 0 === d || null === (u = d.banner) || void 0 === u ? void 0 : u.fields;
                        g && 0 !== (null === (h = g) || void 0 === h ? void 0 : h.length) || (g = r), v && 0 !== (null === (m = v) || void 0 === m ? void 0 : m.length) || (v = null === e || void 0 === e ? void 0 : e.map((e => ({ field: e, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#151515", hideEmptyField: !0 } })))); const y = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]))).reduce(((e, n) => { var r, a; const o = n.id;
                                v = v.map((e => { var t, r; return (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.name) === (null === n || void 0 === n ? void 0 : n.name) && (null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.model) === (null === n || void 0 === n ? void 0 : n.model) && (e.field.id = n.id), e })); const i = v.findIndex((e => { var t; return null !== e && (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.id) === o })); return i > -1 ? e.inTheme[i] = { fieldKey: o, fieldInfo: n, fieldStyle: v[i].fontDetails } : 0 !== (null === t || void 0 === t || null === (r = t.data) || void 0 === r || null === (a = r.detailsPane) || void 0 === a ? void 0 : a.fields.length) && e.notInTheme.push({ fieldKey: o, fieldInfo: n }), e }), { inTheme: Array.from(Array(v.length)), notInTheme: [], otherOrgFields: [] }),
                            b = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (p = v) || void 0 === p || p.forEach(((e, t) => { var n, r;
                            b.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || y.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), y.inTheme = null === (f = y.inTheme) || void 0 === f ? void 0 : f.filter((e => e)), y })) }, 88773: (e, t, n) => { "use strict";
                n.d(t, { A: () => p }); var r = n(14556),
                    a = n(61531),
                    o = n(40454),
                    i = n(84),
                    l = n(356),
                    s = n(72835),
                    c = n(96364),
                    d = n(75156),
                    u = n(70128),
                    h = n(42887),
                    m = n(70579); const p = () => { const { toggleDialog: e } = (0, i.A)("upgradeDialog"), t = (0, r.d4)(h.vm); return (0, m.jsx)(a.A, { textAlign: "center", children: (0, m.jsxs)(o.A, { container: !0, direction: "column", alignItems: "center", children: [(0, m.jsx)(a.A, { mb: 2, children: (0, m.jsx)(d.Ay, { icon: "Lock", size: "x4", color: "#BCBCBC" }) }), (0, m.jsx)(a.A, { mb: 3, children: (0, m.jsx)(c.A, { variant: "h2", color: "textSecondary", children: "Premium-Only Feature" }) }), (0, m.jsx)(a.A, { mb: 3, children: (0, m.jsx)(c.A, { color: "textSecondary", children: "Reporting in Organimi is a feature only available to Premium users. Upgrade today to generate detailed organization and chart-level reports. Download, email, and share detailed reports with your team!" }) }), (0, m.jsx)(a.A, { children: (0, m.jsx)(s.A, { color: "primary", variant: "contained", onClick: () => { e(), l.A.trackEvent({ eventName: u.hK.upgrade_clicked, extraParams: t }) }, children: "Upgrade Now" }) })] }) }) } }, 86913: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => p, HL: () => h, fz: () => s, jt: () => u, oB: () => d, uO: () => m }); var r = n(80907),
                    a = n(9787),
                    o = n(70128); const i = "report",
                    l = { metrics: [], chartReports: [], orgReports: [], config: {}, filters: { loadingCounter: 0, reportLevel: "org", chartId: "", orgId: "", orgName: "", chartName: "", startDate: null, endDate: null, metrics: [] } },
                    s = (0, a.a)({ slice: i, scope: "reports" }),
                    c = (0, r.Z0)({ name: i, initialState: l, reducers: { updateReportFilter: (e, t) => { var n; let r = null === t || void 0 === t ? void 0 : t.payload; return Array.isArray(r) || (r = [r]), null === (n = r) || void 0 === n || n.forEach((t => { const n = null === t || void 0 === t ? void 0 : t.which,
                                        r = null === t || void 0 === t ? void 0 : t.value; if (Object.keys(e.filters).includes(n) && (e.filters[n] = r), "chartId" === n && (e.chartReports = l.chartReports), "orgId" === n && (e.orgReports = l.orgReports, e.chartReports = l.chartReports), "reportLevel" === n) { var a, o, i, s, c, d, u, h; if ("org" === r) e.filters.startDate = (null === (a = e.orgReports) || void 0 === a || null === (o = a[1]) || void 0 === o ? void 0 : o.createdAt) || null, e.filters.endDate = (null === (i = e.orgReports) || void 0 === i || null === (s = i[0]) || void 0 === s ? void 0 : s.createdAt) || null; if ("chart" === r) e.filters.startDate = (null === (c = e.chartReports) || void 0 === c || null === (d = c[1]) || void 0 === d ? void 0 : d.createdAt) || null, e.filters.endDate = (null === (u = e.chartReports) || void 0 === u || null === (h = u[0]) || void 0 === h ? void 0 : h.createdAt) || null } })), e }, incLoading: e => { e.filters.loadingCounter++ }, decLoading: e => { e.filters.loadingCounter-- }, resetLoading: e => { e.filters.loadingCounter = 0 }, "getReportMetrics/fulfilled": (e, t) => { var n;
                                e.metrics = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.metrics }, "getChartReports/pending": e => { e.chartReports = l.chartReports, "chart" === e.filters.reportLevel && (e.filters.startDate = null, e.filters.endDate = null) }, "getChartReports/fulfilled": (e, t) => { var n, r, a, i, l;
                                (e.chartReports = ((null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.chartReports) || []).map(o.vP), "chart" === e.filters.reportLevel) && (e.filters.startDate = (null === (r = e.chartReports) || void 0 === r || null === (a = r[1]) || void 0 === a ? void 0 : a.createdAt) || null, e.filters.endDate = (null === (i = e.chartReports) || void 0 === i || null === (l = i[0]) || void 0 === l ? void 0 : l.createdAt) || null) }, "getOrgReports/pending": e => { e.orgReports = l.orgReports, "org" === e.filters.reportLevel && (e.filters.startDate = null, e.filters.endDate = null) }, "getOrgReports/fulfilled": (e, t) => { var n, r, a, i, l;
                                (e.orgReports = ((null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.orgReports) || []).map(o.vP), "org" === e.filters.reportLevel) && (e.filters.startDate = (null === (r = e.orgReports) || void 0 === r || null === (a = r[1]) || void 0 === a ? void 0 : a.createdAt) || null, e.filters.endDate = (null === (i = e.orgReports) || void 0 === i || null === (l = i[0]) || void 0 === l ? void 0 : l.createdAt) || null) }, "getReportConfig/fulfilled": (e, t) => { var n;
                                e.config = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.reportConfig }, "updateReportConfig/fulfilled": (e, t) => { var n;
                                e.config = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.reportConfig } }, extraReducers: {} }),
                    { updateReportFilter: d, incLoading: u, decLoading: h, resetLoading: m } = c.actions,
                    p = c.reducer }, 42887: (e, t, n) => { "use strict";
                n.d(t, { BF: () => m, KE: () => p, PW: () => f, Su: () => d, TF: () => s, UY: () => y, YH: () => l, Zl: () => i, cJ: () => h, fc: () => v, jO: () => c, tU: () => g, v3: () => u, vm: () => b }); var r = n(80192),
                    a = n(70128),
                    o = n(42006);
                (0, r.Mz)((e => e.organization.charts), (e => (e || []).filter((e => !e.alias)))); const i = (0, r.Mz)((e => e.report), (e => null === e || void 0 === e ? void 0 : e.filters)),
                    l = (0, r.Mz)((e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.config }), (e => e)),
                    s = ((0, r.Mz)((e => e.report), (e => { var t; return null === e || void 0 === e || null === (t = e.metrics) || void 0 === t ? void 0 : t.filter((e => "chart" === (null === e || void 0 === e ? void 0 : e.model))) })), (0, r.Mz)((e => e.report), (e => null === e || void 0 === e ? void 0 : e.orgReports)), (0, r.Mz)((e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.orgReports }), (e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.chartReports }), (e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.filters }), ((e, t, n) => "org" === (null === n || void 0 === n ? void 0 : n.reportLevel) ? e : t))),
                    c = (0, r.Mz)((e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.metrics }), (e => { var t, n; return null === (t = e.report) || void 0 === t || null === (n = t.filters) || void 0 === n ? void 0 : n.reportLevel }), ((e, t) => e.filter((e => (null === e || void 0 === e ? void 0 : e.model) === t)))),
                    d = (0, r.Mz)(c, (e => null === e || void 0 === e ? void 0 : e.sort(((e, t) => { var n; return null === e || void 0 === e || null === (n = e.groupLabel) || void 0 === n ? void 0 : n.localeCompare(null === t || void 0 === t ? void 0 : t.groupLabel) })))),
                    u = (0, r.Mz)(s, (e => null === e || void 0 === e ? void 0 : e.map((e => { var t; return null === e || void 0 === e || null === (t = e.metrics) || void 0 === t ? void 0 : t.reduce(((t, n) => (isNaN(null === n || void 0 === n ? void 0 : n.value) || (t[null === n || void 0 === n ? void 0 : n.id] = null === n || void 0 === n ? void 0 : n.value, t.createdAt = null === e || void 0 === e ? void 0 : e.createdAt, t.id = null === e || void 0 === e ? void 0 : e.id), t)), {}) })))),
                    h = (0, r.Mz)((e => e.organization.charts), (e => e.filter((e => !e.alias && "traditional" === e.type && "organization" === e.shareAccess)))),
                    m = e => e.organization,
                    p = (0, r.Mz)(l, h, ((e, t) => { var n; return null !== e && void 0 !== e && null !== (n = e.chartReport) && void 0 !== n && n.includeAll ? t : null === t || void 0 === t ? void 0 : t.filter((t => { var n, r; return null === e || void 0 === e || null === (n = e.chartReport) || void 0 === n || null === (r = n.charts) || void 0 === r ? void 0 : r.includes(null === t || void 0 === t ? void 0 : t.id) })) })),
                    f = (0, r.Mz)(s, c, (e => { var t; return null === (t = e.report) || void 0 === t ? void 0 : t.filters }), ((e, t, n) => { const r = e.find((e => (null === e || void 0 === e ? void 0 : e.createdAt) === (null === n || void 0 === n ? void 0 : n.startDate))) || {},
                            o = e.find((e => (null === e || void 0 === e ? void 0 : e.createdAt) === (null === n || void 0 === n ? void 0 : n.endDate))) || {}; return (null === t || void 0 === t ? void 0 : t.reduce(((e, t) => { var n, i, l, s, c; const d = null === r || void 0 === r || null === (n = r.metrics) || void 0 === n || null === (i = n.find((e => (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id)))) || void 0 === i ? void 0 : i.value,
                                u = null === o || void 0 === o || null === (l = o.metrics) || void 0 === l || null === (s = l.find((e => (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id)))) || void 0 === s ? void 0 : s.value,
                                h = (null === (c = a.Fk[null === t || void 0 === t ? void 0 : t.metricType]) || void 0 === c ? void 0 : c.call(a.Fk, t, d, u)) || []; return e.push(...h), e }), [])).map(((e, t) => ({ ...e, id: t }))) })),
                    v = (0, r.Mz)(f, (e => { const t = [],
                            n = (0, a.hW)(null === e || void 0 === e ? void 0 : e.map((e => null === e || void 0 === e ? void 0 : e.group))); return n.forEach((n => { t.push({ label: n, data: null === e || void 0 === e ? void 0 : e.filter((e => (null === e || void 0 === e ? void 0 : e.group) === n)).map((e => ({ ...e, indent: ((null === e || void 0 === e ? void 0 : e.indent) || 0) + 1 }))) }) })), t.push(...e.filter((e => !(null !== e && void 0 !== e && e.group)))), { data: t, groups: n } })),
                    g = (0, r.Mz)((e => e.user.organizations), (e => e.filter((e => "owner" === (null === e || void 0 === e ? void 0 : e.accessLevel) || "admin" === (null === e || void 0 === e ? void 0 : e.accessLevel))) || [])),
                    y = (0, r.Mz)(o.VF, (e => { var t; return (null === e || void 0 === e || null === (t = e.featureSet) || void 0 === t ? void 0 : t.reports) || !1 })),
                    b = (0, r.Mz)(o.VF, o.eW, ((e, t) => ({ planId: e.planId, planExpiry: e.expiry, userAccess: t }))) }, 70128: (e, t, n) => { "use strict";
                n.d(t, { Fk: () => h, Wo: () => c, hK: () => o, hW: () => l, sq: () => i, vP: () => s }); var r = n(24241),
                    a = n(99255); const o = { visited: "REPORTS_VISIT", settings_visited: "REPORTS_SETTINGS_VISIT", org_enabled_true: "REPORTS_ORG_ENABLE", org_enabled_false: "REPORTS_ORG_DISABLE", chart_enabled_true: "REPORTS_CHART_ENABLE", chart_enabled_false: "REPORTS_CHART_DISABLE", email_enabled_true: "REPORTS_EMAIL_ENABLE", email_enabled_false: "REPORTS_EMAIL_DISABLE", config_updated: "REPORTS_CONFIG_UPDATE", upgrade_clicked: "REPORTS_PLAN_UPGRADE_CLICK", download: "REPORTS_DOWNLOAD", visited_from_email: "REPORTS_VISIT_FROM_EMAIL" },
                    i = e => { var t; return "-" === e ? e : ("string" === typeof e && (e = new Date(e)), e = new Date(null === (t = e) || void 0 === t ? void 0 : t.setHours(0, 0, 0, 0)), null === a.A || void 0 === a.A ? void 0 : a.A.getDateStr(e, r.c9.DATE_MED)) },
                    l = function() { var e; let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; return (null === (e = Array.from(new Set(t))) || void 0 === e ? void 0 : e.filter((e => e))) || [] },
                    s = e => ({ ...e, createdAt: i(null === e || void 0 === e ? void 0 : e.createdAt), updatedAt: i(null === e || void 0 === e ? void 0 : e.updatedAt) }),
                    c = e => null !== e && void 0 !== e,
                    d = (e, t) => c(e) && c(t) ? (t || 0) - (e || 0) : "-",
                    u = (e, t) => e || t ? !e && t ? t : e && !t || e === t ? e : e !== t ? "".concat(e, " -> ").concat(t, " (renamed)") : void 0 : null,
                    h = { total: (e, t, n) => e ? [{ label: (null === e || void 0 === e ? void 0 : e.description) || (null === e || void 0 === e ? void 0 : e.label) || (null === e || void 0 === e ? void 0 : e.name), startValue: t, endValue: n, difference: d(t, n), group: null === e || void 0 === e ? void 0 : e.groupLabel, metricId: null === e || void 0 === e ? void 0 : e.id }] : [], "dynamic-total": function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [],
                                n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : []; if (!e) return []; const r = [{ label: (null === e || void 0 === e ? void 0 : e.description) || (null === e || void 0 === e ? void 0 : e.label) || (null === e || void 0 === e ? void 0 : e.name), startValue: "", endValue: "", difference: "", group: null === e || void 0 === e ? void 0 : e.groupLabel, metricId: null === e || void 0 === e ? void 0 : e.id }]; if (!Array.isArray(t) || !Array.isArray(n)) return r; const a = Array.from(new Set([...null === t || void 0 === t ? void 0 : t.map((e => { var t; return null === e || void 0 === e || null === (t = e[0]) || void 0 === t ? void 0 : t.id })), ...null === n || void 0 === n ? void 0 : n.map((e => { var t; return null === e || void 0 === e || null === (t = e[0]) || void 0 === t ? void 0 : t.id }))])).map((r => { var a, o; const i = null === t || void 0 === t ? void 0 : t.find((e => { var t; return (null === e || void 0 === e || null === (t = e[0]) || void 0 === t ? void 0 : t.id) === r })),
                                    l = null === n || void 0 === n ? void 0 : n.find((e => { var t; return (null === e || void 0 === e || null === (t = e[0]) || void 0 === t ? void 0 : t.id) === r })),
                                    s = null === i || void 0 === i ? void 0 : i[1],
                                    c = null === l || void 0 === l ? void 0 : l[1],
                                    h = null === i || void 0 === i || null === (a = i[0]) || void 0 === a ? void 0 : a.label,
                                    m = null === l || void 0 === l || null === (o = l[0]) || void 0 === o ? void 0 : o.label; return { label: u(h, m), startValue: s, endValue: c, difference: d(s, c), indent: 1, group: null === e || void 0 === e ? void 0 : e.groupLabel, metricId: null === e || void 0 === e ? void 0 : e.id } })); return [...r, ...a] } } }, 43862: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => E, CK: () => S, Fc: () => y, Ic: () => z, Qk: () => b, Sq: () => u, h7: () => d, k9: () => s, mO: () => w, tE: () => g, wu: () => k }); var r = n(80907),
                    a = n(9787),
                    o = n(81780),
                    i = n(10621),
                    l = n(47730); const s = (0, r.pU)({ sortComparer: (e, t) => { var n; return null === (n = (0, i.mA)(e)) || void 0 === n ? void 0 : n.localeCompare((0, i.mA)(t)) } }),
                    c = "roles",
                    d = (0, a.a)({ slice: c, scope: "roles" }),
                    u = (0, l.B)({ slice: c, scope: "roles" }),
                    h = e => (!0 === e.propagateBg || "true" === e.propagateBg ? e.propagateBg = "on" : !1 !== e.propagateBg && "false" !== e.propagateBg || (e.propagateBg = "off"), { ...(0, i.SB)(e) }),
                    m = (e, t) => { let { payload: { roles: n = [], changed: r = [], deleted: a = [] } } = t;
                        s.upsertMany(e, (n || []).map(h)), o.N_.call(e, (r || []).map(h)), o.gD.call(e, a); for (let i = 0; i < n.length; i++) { const t = n[i];
                            (0, o.u0)({ memberIndexMap: e.memberIndexMap, roleId: null === t || void 0 === t ? void 0 : t.id, memberIds: (null === t || void 0 === t ? void 0 : t.members) || [] }) } },
                    p = (e, t) => { var n, r; let { payload: { roles: a = [], topRoleId: i } } = t;
                        s.setAll(e, (a || []).map(h)), i = (null === e || void 0 === e || null === (n = e.initialDisplay) || void 0 === n ? void 0 : n.topRole) || i || "root"; const l = o.zi.call(e, i); let c = 1; var d; "levels" === (null === e || void 0 === e || null === (r = e.initialDisplay) || void 0 === r ? void 0 : r.collapseToMode) ? c = Math.max((null === e || void 0 === e || null === (d = e.initialDisplay) || void 0 === d ? void 0 : d.collapseToLevels) + ("root" !== i ? -1 : 0), 1 + ("root" !== i ? -1 : 0)): c = (0, o.WC)(l); const u = {},
                            m = {}; for (let s = 0; s < a.length; s++) { const e = a[s];
                            m[e.id] = !!e.parent && (e.children || []).length > 0, (0, o.u0)({ memberIndexMap: u, roleId: null === e || void 0 === e ? void 0 : e.id, memberIds: (null === e || void 0 === e ? void 0 : e.members) || [] }) } i && (m[i] = !(c >= 1)); for (let o = 1; o <= c; o++)
                            for (let t of l[o] || []) { var p; const n = e.entities[t] || {};
                                o === c && (null === n || void 0 === n || null === (p = n.children) || void 0 === p ? void 0 : p.length) > 0 || "hidden" === (null === n || void 0 === n ? void 0 : n.type) ? m[t] = !0 : m[t] = !1 } e.collapseMap = m, e.memberIndexMap = u },
                    f = (e, t) => { let { payload: { roles: n = [], changed: r = [] } } = t; const a = n[0]; if (a) { const { id: t, members: n } = a;
                            s.upsertOne(e, h(a)), (0, o.u0)({ memberIndexMap: e.memberIndexMap, roleId: t, memberIds: n }), o.N_.call(e, (r || []).map(h)) } },
                    v = (0, r.Z0)({ name: c, initialState: s.getInitialState({ memberIndexMap: {}, collapseMap: {}, clusterCollapseMap: {}, autoSizes: {}, couldTransition: [], lastExpanded: [], reFocusId: null, initialDisplay: {} }), reducers: { updateAutoSize: (e, t) => { let { payload: { roleId: n, height: r } } = t;
                                e.autoSizes[n] = r }, updateBatchAutoSize: (e, t) => { let { payload: n } = t; const r = { ...e.autoSizes },
                                    a = Object.keys(n).reduce(((e, t) => { const r = n[t] || 0; return e = Math.max(r, e) }), 0); for (const o of Object.keys(n)) null !== n && void 0 !== n && n[o] || (r[o] = Math.min(a, r[o]));
                                e.autoSizes = { ...r, ...(null === n || void 0 === n ? void 0 : n.sizes) || {} } }, removeLastTransitioned: e => { e.couldTransition = [], e.lastExpanded = [] }, focusRoleInChart: (e, t) => { let { payload: { roleId: n } } = t;
                                e.reFocusId = null, o.pr.call(e, n) }, resetRefocusId: e => { e.lastExpanded = [], e.couldTransition = [], e.reFocusId = null }, expandRole: (e, t) => { let { payload: { roleId: n, clusterNodes: r = [], forceExpand: a } } = t; const o = [...r],
                                    i = e.collapseMap[n],
                                    l = e.entities[n];
                                i && function(t, n, r) { if (e.entities[t] && null !== (n = e.entities[t]) && void 0 !== n && null !== (r = n.children) && void 0 !== r && r.length) { const n = e.entities[t].children || []; for (let t = 0; t < (null === n || void 0 === n ? void 0 : n.length); t++) o.push(n[t]), e.collapseMap[n[t]] = !0 } }(n); for (let c of r) { var s;
                                    null !== l && void 0 !== l && null !== (s = l.children) && void 0 !== s && s.length ? e.collapseMap[c] = !0 : e.collapseMap[c] = !1 } e.lastExpanded = [n, ...r], e.couldTransition = o, e.collapseMap[n] = !a && !i, e.reFocusId = n }, expandVisibleLevels: (e, t) => { let { payload: { numLevels: n, topRoleId: r } } = t;

                                function a() { let t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; for (let r of t) { var n;
                                        e.collapseMap[r] = !0;
                                        a((null === (n = e.entities[r]) || void 0 === n ? void 0 : n.children) || []) } }! function t(r) { let o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 1; for (let l of r) { var i; const r = (null === (i = e.entities[l]) || void 0 === i ? void 0 : i.children) || [];
                                        0 !== r.length ? o < n ? (e.collapseMap[l] = !1, t(r, o + 1)) : (e.collapseMap[l] = !0, a(r)) : e.collapseMap[l] = !1 } }(o.dL.call(e, r)) }, expandSubTree: (e, t) => { let { payload: { roleId: n, collapsed: r, topRoleId: a } } = t;
                                r ? e.collapseMap[n] = !0 : function t(n) { let r; var i;
                                    (e.collapseMap[n] = !1, "root" === n) ? r = o.dL.call(e, a): r = (null === (i = e.entities[n]) || void 0 === i ? void 0 : i.children) || []; for (let e = 0; e < r.length; e++) t(r[e]) }(n), e.reFocusId = n }, "removeRole/fulfilled": (e, t) => { let { payload: { deleted: n, changed: r = [] } } = t;
                                o.gD.call(e, n), o.N_.call(e, (r || []).map(h)) }, "removeRoleRecursive/fulfilled": (e, t) => { let { payload: { changed: n = [], deleted: r } } = t;
                                o.gD.call(e, r), o.N_.call(e, (n || []).map(h)) }, "dropPeople/fulfilled": (e, t) => { let { payload: { role: n } } = t;
                                (0, o.l)({ memberIndexMap: e.memberIndexMap, roleId: null === n || void 0 === n ? void 0 : n.id }), s.upsertOne(e, n) }, "assignPeople/fulfilled": (e, t) => { let { payload: { role: n } } = t;
                                s.upsertOne(e, h(n)) }, "update/fulfilled": f, "bulkUpdate/fulfilled": (e, t) => { let { payload: { roles: n = [], changed: r = [] } } = t;
                                n.forEach((t => { const n = t; if (n) { const { id: t, members: a } = n;
                                        s.upsertOne(e, h(n)), (0, o.u0)({ memberIndexMap: e.memberIndexMap, roleId: t, memberIds: a }), o.N_.call(e, (r || []).map(h)) } })) }, "moveRoleInChart/fulfilled": f, "create/fulfilled": (e, t) => { let { payload: { roles: n = [], changed: r = [] } } = t;
                                s.addMany(e, (n || []).map(h)), o.N_.call(e, (r || []).map(h)); for (let s = 0; s < n.length; s++) { let { id: t, parent: r, members: c, children: d } = n[s]; if (0 === s && (e.reFocusId = t), e.collapseMap[t] = !1, r && 0 === (null === d || void 0 === d ? void 0 : d.length)) { var a; const n = e.entities[r]; if ((null === n || void 0 === n || null === (a = n.children) || void 0 === a ? void 0 : a.length) > 1) { const r = n.children.map((t => e.entities[t])); let a; for (let e of r) { var i; if ((null === e || void 0 === e ? void 0 : e.id) === t) break; const n = 0 === (null === e || void 0 === e || null === (i = e.children) || void 0 === i ? void 0 : i.length) && "single" === e.type || !1;
                                                n && !a ? a = e : n || (a = null) } var l; if (a) e.collapseMap[null === (l = a) || void 0 === l ? void 0 : l.id] = !1 } } r && (e.collapseMap[r] = !1), (0, o.u0)({ memberIndexMap: e.memberIndexMap, roleId: t, memberIds: c }) } }, "duplicateMatrixStructure/fulfilled": (e, t) => { let { payload: { roles: n = [], changed: r = [] } } = t;
                                s.addMany(e, (n || []).map(h)), o.N_.call(e, (r || []).map(h)); for (let o = 0; o < n.length; o++) { let { id: t, parent: r, members: s, children: c } = n[o]; if (0 === o && (e.reFocusId = t), e.collapseMap[t] = !1, r && 0 === (null === c || void 0 === c ? void 0 : c.length)) { var a; const n = e.entities[r]; if ((null === n || void 0 === n || null === (a = n.children) || void 0 === a ? void 0 : a.length) > 1) { const r = n.children.map((t => e.entities[t])); let a; for (let e of r) { var i; if ((null === e || void 0 === e ? void 0 : e.id) === t) break; const n = 0 === (null === e || void 0 === e || null === (i = e.children) || void 0 === i ? void 0 : i.length) && "single" === e.type || !1;
                                                n && !a ? a = e : n || (a = null) } var l; if (a) e.collapseMap[null === (l = a) || void 0 === l ? void 0 : l.id] = !1 } } r && (e.collapseMap[r] = !1) } }, loadSampleRoles: p, unloadSampleRoles: () => {}, "mergeChartLink/fulfilled": m }, extraReducers: { "search/selectSearchResult": (e, t) => { let { payload: n } = t; if (null === n || void 0 === n || !n.role) return e; const { role: { id: r } = { id: "" } } = n || {};
                                e.collapseMap[r] = !1, m(e, { payload: { roles: [n.role] } }) }, "chart/getRoles/pending": e => { s.setAll(e, []) }, "chart/getRoles/fulfilled": p, "chart/getRolesSilent/fulfilled": p, "chart/refreshRoles/fulfilled": (e, t) => { let { payload: { roles: n = [] } } = t;
                                o.N_.call(e, n.map(h)) }, "chart/getChildrenRoles/fulfilled": (e, t) => { let { payload: { roles: n = [] }, meta: { arg: r = {} } } = t; "root" === r.roleId && s.removeAll(e), s.upsertMany(e, n.map((e => h({ ...e, members: e.members.map((e => e.id)) })))); for (let a of n) { const { id: t, members: n } = a, r = n.map((e => e.id));
                                    (0, o.u0)({ memberIndexMap: e.memberIndexMap, roleId: t, memberIds: r }) } }, "chart/get/fulfilled": (e, t) => { let { payload: n } = t;
                                n.chart.sharePermission && (e.initialDisplay = { collapseToMode: n.chart.sharePermission.collapseToMode, collapseToLevels: n.chart.sharePermission.collapseToLevels, topRole: n.chart.sharePermission.topRole }) }, "fields/deleteCustomField/fulfilled": (e, t) => { let { payload: n } = t; const r = null === n || void 0 === n ? void 0 : n.fields; if (!r.length) return; const a = s.getSelectors().selectAll(e).map((e => ({ ...e, fields: [...e.fields] })));
                                null !== a && void 0 !== a && a.length && (a.forEach((e => e.fields = e.fields.filter((e => !r.includes(e.id))))), s.setAll(e, a)) } } }),
                    { expandRole: g, expandSubTree: y, expandVisibleLevels: b, focusRoleInChart: w, resetRefocusId: z, removeLastTransitioned: x, updateAutoSize: A, updateBatchAutoSize: k, loadSampleRoles: S, unloadSampleRoles: M } = v.actions,
                    E = v.reducer }, 61: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => h, EJ: () => i, F8: () => u, JP: () => a, Lt: () => s, N$: () => d, nZ: () => l, w9: () => c }); var r = n(80907); const a = (0, n(9787).a)({ slice: "search", scope: "search" }),
                    o = (0, r.Z0)({ name: "search", initialState: { selectedResult: null, filterChart: !1, searchResults: [], showDepartmentFilter: !1 }, reducers: { selectSearchResult: (e, t) => { let { payload: n } = t;
                                e.selectedResult = n }, resetSearchResult: e => { e.selectedResult = null }, saveSearchResult: (e, t) => { let { payload: n } = t;
                                e.searchResults = (null === n || void 0 === n ? void 0 : n.searchResults) || [], e.searchResultWithDepartment = (null === n || void 0 === n ? void 0 : n.searchResultWithDepartment) || [], e.searchResultWithoutDepartment = (null === n || void 0 === n ? void 0 : n.searchResultWithoutDepartment) || [], e.filterChart = (null === n || void 0 === n ? void 0 : n.filterChart) || !1 }, expandSearchResult: (e, t) => { let { payload: { role: n } } = t;
                                e.searchResults.push({ role: n }) }, setFilterWithDepartment: (e, t) => { let { payload: n } = t;
                                e.showDepartmentFilter = n }, closeFilter: e => { e.filterChart = !1, e.searchResults = [], e.searchResultWithDepartment = [], e.searchResultWithoutDepartment = [] } } }),
                    { selectSearchResult: i, resetSearchResult: l, saveSearchResult: s, expandSearchResult: c, setFilterWithDepartment: d, closeFilter: u } = o.actions,
                    h = o.reducer }, 14241: (e, t, n) => { "use strict";
                n.d(t, { ZH: () => c, a4: () => o, mE: () => s, s1: () => i, zk: () => d }); var r = n(80192),
                    a = n(59770); const o = e => e.search.selectedResult,
                    i = (0, r.Mz)(a.Qo, (e => e.search.filterChart), ((e, t) => t || !!e.length));

                function l(e, t) { const n = new Map; return (e || []).concat(t || []).forEach((e => { var t; const r = null === e || void 0 === e || null === (t = e.role) || void 0 === t ? void 0 : t.id;
                        r && n.set(r, e) })), Array.from((null === n || void 0 === n ? void 0 : n.values()) || []) } const s = (0, r.Mz)((e => e.search.searchResults), a.Qo, a.Y$, (e => e.search.showDepartmentFilter), ((e, t, n, r) => l(e, r ? n : t))),
                    c = (0, r.Mz)((e => e.search.searchResultWithDepartment), a.Y$, ((e, t) => l(e, t))),
                    d = (0, r.Mz)((e => e.search.searchResultWithoutDepartment), a.Qo, i, ((e, t, n) => n ? l(e, t) : [])) }, 94251: (e, t, n) => { "use strict";
                n.r(t), n.d(t, { default: () => E }); var r = n(59691),
                    a = n(61531),
                    o = n(38325),
                    i = n(14370),
                    l = n(16853),
                    s = n(64759),
                    c = n(67503),
                    d = n(77887),
                    u = n(695),
                    h = n(72835),
                    m = n(96364),
                    p = n(79882),
                    f = n(75687),
                    v = n(49092),
                    g = n(8064),
                    y = n(35801),
                    b = n(43867),
                    w = n(52907),
                    z = n(5816),
                    x = n(51836),
                    A = n(43331),
                    k = n(14556),
                    S = n(70579); const M = [{ name: "name", label: "Chart", align: "left", sortable: !0 }, { name: "role", label: "Roles", align: "left", sortable: !0 }, { name: "people", label: "People", align: "left", sortable: !0 }, { name: "department", label: "Departments", align: "left", sortable: !0 }, { name: "location", label: "Locations", align: "left", sortable: !0 }, { name: "vacantRole", label: "Vacant Roles", align: "left", sortable: !0 }],
                    E = e => { let { closeDialog: t, restoreSnapshot: n, snapshotId: E } = e; const { t: C } = (0, v.B)(), T = () => { t() }, { rows: H, page: L, order: I, createSortHandler: j, totalCount: V, rowsPerPage: O, handleChangePage: R, handleChangeRowsPerPage: P } = (0, f.s)({ dataSelector: p.yo, defaultValues: { order: "asc", orderBy: "createdAt", rowsPerPage: 25 } }), D = (0, k.d4)(A.OX), F = H.filter((e => D.some((t => t.id === e.chart)))), { isItemSelected: N, handleSelectAll: _, handleSelectItem: B, selected: W } = (0, f.U)({ selectField: "chart" }); return (0, S.jsxs)(y.A, { open: !0, onClose: T, fullWidth: !0, maxWidth: "md", children: [(0, S.jsx)(z.A, { onClose: T, children: "Restore Organization" }), (0, S.jsxs)(b.A, { children: [(0, S.jsx)(a.A, { pb: 3, display: "flex", justifyContent: "center", textAlign: "center", children: (0, S.jsx)(a.A, { width: "75%", children: (0, S.jsx)(m.A, { children: "This backup includes the following charts. Select which charts should be included in the restored organization." }) }) }), (0, S.jsxs)(S.Fragment, { children: [V > 0 && (0, S.jsx)(a.A, { flex: 1, overflow: "auto", height: "100%", children: (0, S.jsxs)(c.A, { "aria-label": "dashboard", size: "small", stickyHeader: !0, children: [(0, S.jsx)(s.A, { children: (0, S.jsxs)(u.A, { children: [(0, S.jsx)(d.A, { padding: "checkbox", children: (0, S.jsx)(l.A, { indeterminate: W.length > 0 && W.length < H.length, checked: W.length === H.length, onClick: _(H), inputProps: { "aria-label": "Select all charts" }, size: "small" }) }), M.map((e => e.sortable ? (0, S.jsx)(d.A, { align: e.align || "left", children: (0, S.jsx)(i.A, { onClick: j(e.name), direction: I, children: e.label }) }, "snapshot-detail-header-".concat(e.name)) : (0, S.jsx)(d.A, { align: e.align || "left", children: e.label }, "snapshot-detail-header-".concat(e.name))))] }) }), (0, S.jsx)(r.A, { children: F.map((e => { var t, n; return (0, S.jsx)(g.f, { hover: !0, style: { position: "relative" }, children: (0, S.jsxs)(S.Fragment, { children: [(0, S.jsx)(d.A, { padding: "checkbox", style: { paddingLeft: null !== (t = e.alias) && void 0 !== t && t.roleId ? 28 : 18, borderLeft: null !== (n = e.alias) && void 0 !== n && n.roleId ? "solid 4px #3CD3C2" : "none" }, children: (0, S.jsx)(l.A, { checked: N(e), onClick: B(e), inputProps: { "aria-label": "Select chart ".concat(e.name) }, size: "small" }) }), M.map((t => (0, S.jsx)(d.A, { scope: "row", children: e[t.name] }, "snapshot-detail-data-".concat(e.chart, "-cell-").concat(t.name))))] }) }, "snapshot-detail-data-".concat(e.chart)) })) })] }) }), V > O && (0, S.jsx)(a.A, { position: "sticky", left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", borderTop: "solid 1px #ddd", children: (0, S.jsx)(o.A, { rowsPerPageOptions: [25, 50, 100, 250], component: "div", count: V, rowsPerPage: O, page: L, backIconButtonProps: { "aria-label": C("Common.Tables.backIconButtonText") }, nextIconButtonProps: { "aria-label": C("Common.Tables.nextIconButtonText") }, onPageChange: R, onRowsPerPageChange: P, labelRowsPerPage: C("Common.Tables.labelRowsPerPage"), backIconButtonText: C("Common.Tables.backIconButtonText"), nextIconButtonText: C("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: t, to: n, count: r } = e; return "".concat(t, "-").concat(n, " ").concat(C("Common.Tables.of"), " ").concat(r) } }) }), !V && (0, S.jsx)(g.X, { headCells: M, text: "No charts available in this backup snapshot", image: x.A })] })] }), (0, S.jsx)(w.A, { children: (0, S.jsxs)(a.A, { my: 1, display: "flex", justifyContent: "center", gridGap: 16, width: "100%", children: [(0, S.jsx)(h.A, { variant: "outlined", onClick: t, color: "primary", children: "Cancel" }), (0, S.jsx)(h.A, { disabled: !W.length, variant: "contained", color: "primary", onClick: () => { n(E, W), t() }, children: W.length && 1 === W.length ? "Restore Chart" : W.length > 1 ? "Restore (".concat(W.length, ") Chart") : "Restore Charts" })] }) })] }) } }, 8064: (e, t, n) => { "use strict";
                n.d(t, { X: () => p, f: () => i }); var r, a = n(57528),
                    o = n(695); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)(["\n  > .MuiTableCell-root {\n    line-height: 2;\n    padding: 12px 24px 12px 16px;\n  }\n"]))); var l = n(61531),
                    s = n(16853),
                    c = n(64759),
                    d = n(67503),
                    u = n(77887),
                    h = n(64021),
                    m = n(70579); const p = e => { let { headCells: t = [], text: n, image: r, handleClick: a, buttonText: i, isButtonVisible: p, smallText: f } = e; return (0, m.jsxs)(m.Fragment, { children: [(0, m.jsx)(d.A, { "aria-label": "dashboard", size: "small", stickyHeader: !0, children: (0, m.jsx)(c.A, { children: (0, m.jsxs)(o.A, { children: [(0, m.jsx)(u.A, { padding: "checkbox", children: (0, m.jsx)(s.A, { disabled: !0 }) }), t.map((e => (0, m.jsx)(u.A, { align: e.align || "left", children: e.label }, "organization-chartlist-headcell-".concat(e.name))))] }) }) }), (0, m.jsx)(l.A, { flexGrow: 1, display: "flex", justifyContent: "flex-start", mt: 4, flexDirection: "column", children: (0, m.jsx)(h.A, { text: n, smallText: f, image: r, handleClick: a, buttonText: i, isButtonVisible: p }) })] }) };
                n(94251) }, 79882: (e, t, n) => { "use strict";
                n.d(t, { Ar: () => u, Ay: () => y, Nb: () => f, VF: () => v, aK: () => m, al: () => h, cm: () => c, xd: () => g, yo: () => p }); var r = n(80907),
                    a = n(80192),
                    o = n(42006),
                    i = n(47730); const l = "snapshots",
                    s = { config: {}, list: [], activeSnapshotDetails: [] },
                    c = (0, i.B)({ slice: l, scope: "snapshots" }),
                    d = (0, r.Z0)({ name: l, initialState: s, reducers: { setSnapshotDetails: (e, t) => { let { payload: n } = t; const r = null === n || void 0 === n ? void 0 : n.snapshotId,
                                    a = e.list.find((e => e.id === r));
                                e.activeSnapshotDetails = ((null === a || void 0 === a ? void 0 : a.charts) || []).map((e => ({ chart: e.chart, name: e.name, ...e.counts }))) }, "getSnapshotsList/fulfilled": (e, t) => { let { payload: n } = t;
                                e.list = (null === n || void 0 === n ? void 0 : n.snapshots) || s.list }, "deleteSnapshot/fulfilled": (e, t) => { var n; let { meta: r } = t;
                                null !== r && void 0 !== r && null !== (n = r.arg) && void 0 !== n && n.snapshotIds && (e.list = e.list.filter((e => { var t; return !(null !== r && void 0 !== r && null !== (t = r.arg) && void 0 !== t && t.snapshotIds.includes(e.id)) }))) }, "enableAutoSnapshots/fulfilled": (e, t) => { let { payload: n } = t;
                                e.config = (null === n || void 0 === n ? void 0 : n.snapshotConfig) || s.config }, "disableAutoSnapshots/fulfilled": (e, t) => { let { payload: n } = t;
                                e.config = (null === n || void 0 === n ? void 0 : n.snapshotConfig) || s.config } }, extraReducers: { "organization/get/fulfilled": (e, t) => { var n; let { payload: r } = t;
                                e.config = (null === r || void 0 === r || null === (n = r.organization) || void 0 === n ? void 0 : n.snapshotConfig) || s.config } } }),
                    u = (0, a.Mz)((e => e.organization), (e => e.id)),
                    h = (0, a.Mz)((e => e.snapshots), (e => e.config)),
                    m = (0, a.Mz)((e => e.snapshots), (e => e.list)),
                    p = (0, a.Mz)((e => e.snapshots), (e => e.activeSnapshotDetails)),
                    f = (0, a.Mz)(o.VF, (e => { var t; return (null === e || void 0 === e || null === (t = e.featureSet) || void 0 === t ? void 0 : t.snapshots) || !1 })),
                    v = o.VF,
                    { setSnapshotDetails: g } = d.actions,
                    y = d.reducer }, 97626: (e, t, n) => { "use strict";
                n.d(t, { b: () => Fe, A: () => Ne }); var r = n(57528),
                    a = n(65043),
                    o = n(393),
                    i = n(78396),
                    l = n(61531),
                    s = n(9579),
                    c = n(40454),
                    d = n(58168),
                    u = n(80045),
                    h = n(97950),
                    m = n(27355),
                    p = n(88692),
                    f = n(60768),
                    v = n(70567),
                    g = n(12899),
                    y = n(40830);

                function b(e, t) { var n = function(e, t) { var n, r = t.getBoundingClientRect(); if (t.fakeTransform) n = t.fakeTransform;
                        else { var a = window.getComputedStyle(t);
                            n = a.getPropertyValue("-webkit-transform") || a.getPropertyValue("transform") } var o = 0,
                            i = 0; if (n && "none" !== n && "string" === typeof n) { var l = n.split("(")[1].split(")")[0].split(",");
                            o = parseInt(l[4], 10), i = parseInt(l[5], 10) } return "left" === e ? "translateX(".concat(window.innerWidth, "px) translateX(").concat(o - r.left, "px)") : "right" === e ? "translateX(-".concat(r.left + r.width - o, "px)") : "up" === e ? "translateY(".concat(window.innerHeight, "px) translateY(").concat(i - r.top, "px)") : "translateY(-".concat(r.top + r.height - i, "px)") }(e, t);
                    n && (t.style.webkitTransform = n, t.style.transform = n) } var w = { enter: g.p0.enteringScreen, exit: g.p0.leavingScreen }; const z = a.forwardRef((function(e, t) { var n = e.children,
                        r = e.direction,
                        o = void 0 === r ? "down" : r,
                        i = e.in,
                        l = e.onEnter,
                        s = e.onEntered,
                        c = e.onEntering,
                        g = e.onExit,
                        z = e.onExited,
                        x = e.onExiting,
                        A = e.style,
                        k = e.timeout,
                        S = void 0 === k ? w : k,
                        M = e.TransitionComponent,
                        E = void 0 === M ? p.Ay : M,
                        C = (0, u.A)(e, ["children", "direction", "in", "onEnter", "onEntered", "onEntering", "onExit", "onExited", "onExiting", "style", "timeout", "TransitionComponent"]),
                        T = (0, v.A)(),
                        H = a.useRef(null),
                        L = a.useCallback((function(e) { H.current = h.findDOMNode(e) }), []),
                        I = (0, f.A)(n.ref, L),
                        j = (0, f.A)(I, t),
                        V = function(e) { return function(t) { e && (void 0 === t ? e(H.current) : e(H.current, t)) } },
                        O = V((function(e, t) { b(o, e), (0, y.q)(e), l && l(e, t) })),
                        R = V((function(e, t) { var n = (0, y.c)({ timeout: S, style: A }, { mode: "enter" });
                            e.style.webkitTransition = T.transitions.create("-webkit-transform", (0, d.default)({}, n, { easing: T.transitions.easing.easeOut })), e.style.transition = T.transitions.create("transform", (0, d.default)({}, n, { easing: T.transitions.easing.easeOut })), e.style.webkitTransform = "none", e.style.transform = "none", c && c(e, t) })),
                        P = V(s),
                        D = V(x),
                        F = V((function(e) { var t = (0, y.c)({ timeout: S, style: A }, { mode: "exit" });
                            e.style.webkitTransition = T.transitions.create("-webkit-transform", (0, d.default)({}, t, { easing: T.transitions.easing.sharp })), e.style.transition = T.transitions.create("transform", (0, d.default)({}, t, { easing: T.transitions.easing.sharp })), b(o, e), g && g(e) })),
                        N = V((function(e) { e.style.webkitTransition = "", e.style.transition = "", z && z(e) })),
                        _ = a.useCallback((function() { H.current && b(o, H.current) }), [o]); return a.useEffect((function() { if (!i && "down" !== o && "right" !== o) { var e = (0, m.A)((function() { H.current && b(o, H.current) })); return window.addEventListener("resize", e),
                                function() { e.clear(), window.removeEventListener("resize", e) } } }), [o, i]), a.useEffect((function() { i || _() }), [i, _]), a.createElement(E, (0, d.default)({ nodeRef: H, onEnter: O, onEntered: P, onEntering: R, onExit: F, onExited: N, onExiting: D, appear: !0, in: i, timeout: S }, C), (function(e, t) { return a.cloneElement(n, (0, d.default)({ ref: j, style: (0, d.default)({ visibility: "exited" !== e || i ? void 0 : "hidden" }, A, n.props.style) }, t)) })) })); var x = n(48655),
                    A = n(41583),
                    k = n(96364),
                    S = n(70318),
                    M = n(75156),
                    E = n(14556),
                    C = n(70669),
                    T = n(6904),
                    H = n(85725),
                    L = n(20447),
                    I = n(176),
                    j = n(45418),
                    V = n(23993); var O, R, P, D, F = n(8713),
                    N = n(28269),
                    _ = n(17339),
                    B = n(80539),
                    W = n(61071),
