                function Vo(e, t) { if (t && t.themeXLSX) return t.themeXLSX; if (e && "string" == typeof e.raw) return e.raw; var n = [at]; return n[n.length] = '<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">', n[n.length] = "<a:themeElements>", n[n.length] = '<a:clrScheme name="Office">', n[n.length] = '<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>', n[n.length] = '<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>', n[n.length] = '<a:dk2><a:srgbClr val="1F497D"/></a:dk2>', n[n.length] = '<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>', n[n.length] = '<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>', n[n.length] = '<a:accent2><a:srgbClr val="C0504D"/></a:accent2>', n[n.length] = '<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>', n[n.length] = '<a:accent4><a:srgbClr val="8064A2"/></a:accent4>', n[n.length] = '<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>', n[n.length] = '<a:accent6><a:srgbClr val="F79646"/></a:accent6>', n[n.length] = '<a:hlink><a:srgbClr val="0000FF"/></a:hlink>', n[n.length] = '<a:folHlink><a:srgbClr val="800080"/></a:folHlink>', n[n.length] = "</a:clrScheme>", n[n.length] = '<a:fontScheme name="Office">', n[n.length] = "<a:majorFont>", n[n.length] = '<a:latin typeface="Cambria"/>', n[n.length] = '<a:ea typeface=""/>', n[n.length] = '<a:cs typeface=""/>', n[n.length] = '<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>', n[n.length] = '<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>', n[n.length] = '<a:font script="Hans" typeface="\u5b8b\u4f53"/>', n[n.length] = '<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>', n[n.length] = '<a:font script="Arab" typeface="Times New Roman"/>', n[n.length] = '<a:font script="Hebr" typeface="Times New Roman"/>', n[n.length] = '<a:font script="Thai" typeface="Tahoma"/>', n[n.length] = '<a:font script="Ethi" typeface="Nyala"/>', n[n.length] = '<a:font script="Beng" typeface="Vrinda"/>', n[n.length] = '<a:font script="Gujr" typeface="Shruti"/>', n[n.length] = '<a:font script="Khmr" typeface="MoolBoran"/>', n[n.length] = '<a:font script="Knda" typeface="Tunga"/>', n[n.length] = '<a:font script="Guru" typeface="Raavi"/>', n[n.length] = '<a:font script="Cans" typeface="Euphemia"/>', n[n.length] = '<a:font script="Cher" typeface="Plantagenet Cherokee"/>', n[n.length] = '<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>', n[n.length] = '<a:font script="Tibt" typeface="Microsoft Himalaya"/>', n[n.length] = '<a:font script="Thaa" typeface="MV Boli"/>', n[n.length] = '<a:font script="Deva" typeface="Mangal"/>', n[n.length] = '<a:font script="Telu" typeface="Gautami"/>', n[n.length] = '<a:font script="Taml" typeface="Latha"/>', n[n.length] = '<a:font script="Syrc" typeface="Estrangelo Edessa"/>', n[n.length] = '<a:font script="Orya" typeface="Kalinga"/>', n[n.length] = '<a:font script="Mlym" typeface="Kartika"/>', n[n.length] = '<a:font script="Laoo" typeface="DokChampa"/>', n[n.length] = '<a:font script="Sinh" typeface="Iskoola Pota"/>', n[n.length] = '<a:font script="Mong" typeface="Mongolian Baiti"/>', n[n.length] = '<a:font script="Viet" typeface="Times New Roman"/>', n[n.length] = '<a:font script="Uigh" typeface="Microsoft Uighur"/>', n[n.length] = '<a:font script="Geor" typeface="Sylfaen"/>', n[n.length] = "</a:majorFont>", n[n.length] = "<a:minorFont>", n[n.length] = '<a:latin typeface="Calibri"/>', n[n.length] = '<a:ea typeface=""/>', n[n.length] = '<a:cs typeface=""/>', n[n.length] = '<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>', n[n.length] = '<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>', n[n.length] = '<a:font script="Hans" typeface="\u5b8b\u4f53"/>', n[n.length] = '<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>', n[n.length] = '<a:font script="Arab" typeface="Arial"/>', n[n.length] = '<a:font script="Hebr" typeface="Arial"/>', n[n.length] = '<a:font script="Thai" typeface="Tahoma"/>', n[n.length] = '<a:font script="Ethi" typeface="Nyala"/>', n[n.length] = '<a:font script="Beng" typeface="Vrinda"/>', n[n.length] = '<a:font script="Gujr" typeface="Shruti"/>', n[n.length] = '<a:font script="Khmr" typeface="DaunPenh"/>', n[n.length] = '<a:font script="Knda" typeface="Tunga"/>', n[n.length] = '<a:font script="Guru" typeface="Raavi"/>', n[n.length] = '<a:font script="Cans" typeface="Euphemia"/>', n[n.length] = '<a:font script="Cher" typeface="Plantagenet Cherokee"/>', n[n.length] = '<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>', n[n.length] = '<a:font script="Tibt" typeface="Microsoft Himalaya"/>', n[n.length] = '<a:font script="Thaa" typeface="MV Boli"/>', n[n.length] = '<a:font script="Deva" typeface="Mangal"/>', n[n.length] = '<a:font script="Telu" typeface="Gautami"/>', n[n.length] = '<a:font script="Taml" typeface="Latha"/>', n[n.length] = '<a:font script="Syrc" typeface="Estrangelo Edessa"/>', n[n.length] = '<a:font script="Orya" typeface="Kalinga"/>', n[n.length] = '<a:font script="Mlym" typeface="Kartika"/>', n[n.length] = '<a:font script="Laoo" typeface="DokChampa"/>', n[n.length] = '<a:font script="Sinh" typeface="Iskoola Pota"/>', n[n.length] = '<a:font script="Mong" typeface="Mongolian Baiti"/>', n[n.length] = '<a:font script="Viet" typeface="Arial"/>', n[n.length] = '<a:font script="Uigh" typeface="Microsoft Uighur"/>', n[n.length] = '<a:font script="Geor" typeface="Sylfaen"/>', n[n.length] = "</a:minorFont>", n[n.length] = "</a:fontScheme>", n[n.length] = '<a:fmtScheme name="Office">', n[n.length] = "<a:fillStyleLst>", n[n.length] = '<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>', n[n.length] = '<a:gradFill rotWithShape="1">', n[n.length] = "<a:gsLst>", n[n.length] = '<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>', n[n.length] = "</a:gsLst>", n[n.length] = '<a:lin ang="16200000" scaled="1"/>', n[n.length] = "</a:gradFill>", n[n.length] = '<a:gradFill rotWithShape="1">', n[n.length] = "<a:gsLst>", n[n.length] = '<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>', n[n.length] = "</a:gsLst>", n[n.length] = '<a:lin ang="16200000" scaled="0"/>', n[n.length] = "</a:gradFill>", n[n.length] = "</a:fillStyleLst>", n[n.length] = "<a:lnStyleLst>", n[n.length] = '<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>', n[n.length] = '<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>', n[n.length] = '<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>', n[n.length] = "</a:lnStyleLst>", n[n.length] = "<a:effectStyleLst>", n[n.length] = "<a:effectStyle>", n[n.length] = "<a:effectLst>", n[n.length] = '<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>', n[n.length] = "</a:effectLst>", n[n.length] = "</a:effectStyle>", n[n.length] = "<a:effectStyle>", n[n.length] = "<a:effectLst>", n[n.length] = '<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>', n[n.length] = "</a:effectLst>", n[n.length] = "</a:effectStyle>", n[n.length] = "<a:effectStyle>", n[n.length] = "<a:effectLst>", n[n.length] = '<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>', n[n.length] = "</a:effectLst>", n[n.length] = '<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>', n[n.length] = '<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>', n[n.length] = "</a:effectStyle>", n[n.length] = "</a:effectStyleLst>", n[n.length] = "<a:bgFillStyleLst>", n[n.length] = '<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>', n[n.length] = '<a:gradFill rotWithShape="1">', n[n.length] = "<a:gsLst>", n[n.length] = '<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>', n[n.length] = "</a:gsLst>", n[n.length] = '<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>', n[n.length] = "</a:gradFill>", n[n.length] = '<a:gradFill rotWithShape="1">', n[n.length] = "<a:gsLst>", n[n.length] = '<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>', n[n.length] = '<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>', n[n.length] = "</a:gsLst>", n[n.length] = '<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>', n[n.length] = "</a:gradFill>", n[n.length] = "</a:bgFillStyleLst>", n[n.length] = "</a:fmtScheme>", n[n.length] = "</a:themeElements>", n[n.length] = "<a:objectDefaults>", n[n.length] = "<a:spDef>", n[n.length] = '<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>', n[n.length] = "</a:spDef>", n[n.length] = "<a:lnDef>", n[n.length] = '<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>', n[n.length] = "</a:lnDef>", n[n.length] = "</a:objectDefaults>", n[n.length] = "<a:extraClrSchemeLst/>", n[n.length] = "</a:theme>", n.join("") }

                function Oo(e) { var t = {}; switch (t.xclrType = e.read_shift(2), t.nTintShade = e.read_shift(2), t.xclrType) {
                        case 0:
                        case 4:
                            e.l += 4; break;
                        case 1:
                            t.xclrValue = function(e, t) { return zn(e, t) }(e, 4); break;
                        case 2:
                            t.xclrValue = ea(e); break;
                        case 3:
                            t.xclrValue = function(e) { return e.read_shift(4) }(e) } return e.l += 8, t }

                function Ro(e) { var t = e.read_shift(2),
                        n = e.read_shift(2) - 4,
                        r = [t]; switch (t) {
                        case 4:
                        case 5:
                        case 7:
                        case 8:
                        case 9:
                        case 10:
                        case 11:
                        case 13:
                            r[1] = Oo(e); break;
                        case 6:
                            r[1] = function(e, t) { return zn(e, t) }(e, n); break;
                        case 14:
                        case 15:
                            r[1] = e.read_shift(1 === n ? 1 : 2); break;
                        default:
                            throw new Error("Unrecognized ExtProp type: " + t + " " + n) } return r }

                function Po(e, t, n, r) { var a, o = Array.isArray(e);
                    t.forEach((function(t) { var i = jn(t.ref); if (o ? (e[i.r] || (e[i.r] = []), a = e[i.r][i.c]) : a = e[t.ref], !a) { a = { t: "z" }, o ? e[i.r][i.c] = a : e[t.ref] = a; var l = Pn(e["!ref"] || "BDWGO1000001:A1");
                            l.s.r > i.r && (l.s.r = i.r), l.e.r < i.r && (l.e.r = i.r), l.s.c > i.c && (l.s.c = i.c), l.e.c < i.c && (l.e.c = i.c); var s = Rn(l);
                            s !== e["!ref"] && (e["!ref"] = s) } a.c || (a.c = []); var c = { a: t.author, t: t.t, r: t.r, T: n };
                        t.h && (c.h = t.h); for (var d = a.c.length - 1; d >= 0; --d) { if (!n && a.c[d].T) return;
                            n && !a.c[d].T && a.c.splice(d, 1) } if (n && r)
                            for (d = 0; d < r.length; ++d)
                                if (c.a == r[d].id) { c.a = r[d].name || c.a; break } a.c.push(c) })) } var Do = Wn; var Fo = "application/vnd.ms-office.vbaProject"; var No = function() { var e = /(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,
                            t = { r: 0, c: 0 };

                        function n(e, n, r, a) { var o = !1,
                                i = !1;
                            0 == r.length ? i = !0 : "[" == r.charAt(0) && (i = !0, r = r.slice(1, -1)), 0 == a.length ? o = !0 : "[" == a.charAt(0) && (o = !0, a = a.slice(1, -1)); var l = r.length > 0 ? 0 | parseInt(r, 10) : 0,
                                s = a.length > 0 ? 0 | parseInt(a, 10) : 0; return o ? s += t.c : --s, i ? l += t.r : --l, n + (o ? "" : "$") + In(s) + (i ? "" : "$") + Hn(l) } return function(r, a) { return t = a, r.replace(e, n) } }(),
                    _o = /(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,
                    Bo = function() { return function(e, t) { return e.replace(_o, (function(e, n, r, a, o, i) { var l = Ln(a) - (r ? 0 : t.c),
                                    s = Tn(i) - (o ? 0 : t.r); return n + "R" + (0 == s ? "" : o ? s + 1 : "[" + s + "]") + "C" + (0 == l ? "" : r ? l + 1 : "[" + l + "]") })) } }();

                function Wo(e, t) { return e.replace(_o, (function(e, n, r, a, o, i) { return n + ("$" == r ? r + a : In(Ln(a) + t.c)) + ("$" == o ? o + i : Hn(Tn(i) + t.r)) })) }

                function Uo(e, t, n) { var r = On(t).s,
                        a = jn(n); return Wo(e, { r: a.r - r.r, c: a.c - r.c }) }

                function qo(e) { return e.replace(/_xlfn\./g, "") }

                function Go(e) { e.l += 1 }

                function Ko(e, t) { var n = e.read_shift(1 == t ? 1 : 2); return [16383 & n, n >> 14 & 1, n >> 15 & 1] }

                function Zo(e, t, n) { var r = 2; if (n) { if (n.biff >= 2 && n.biff <= 5) return Yo(e);
                        12 == n.biff && (r = 4) } var a = e.read_shift(r),
                        o = e.read_shift(r),
                        i = Ko(e, 2),
                        l = Ko(e, 2); return { s: { r: a, c: i[0], cRel: i[1], rRel: i[2] }, e: { r: o, c: l[0], cRel: l[1], rRel: l[2] } } }

                function Yo(e) { var t = Ko(e, 2),
                        n = Ko(e, 2),
                        r = e.read_shift(1),
                        a = e.read_shift(1); return { s: { r: t[0], c: r, cRel: t[1], rRel: t[2] }, e: { r: n[0], c: a, cRel: n[1], rRel: n[2] } } }

                function Xo(e, t, n) { if (n && n.biff >= 2 && n.biff <= 5) return function(e) { var t = Ko(e, 2),
                            n = e.read_shift(1); return { r: t[0], c: n, cRel: t[1], rRel: t[2] } }(e); var r = e.read_shift(n && 12 == n.biff ? 4 : 2),
                        a = Ko(e, 2); return { r: r, c: a[0], cRel: a[1], rRel: a[2] } }

                function $o(e) { var t = e.read_shift(2),
                        n = e.read_shift(2); return { r: t, c: 255 & n, fQuoted: !!(16384 & n), cRel: n >> 15, rRel: n >> 15 } }

                function Qo(e) { var t = 1 & e[e.l + 1]; return e.l += 4, [t, 1] }

                function Jo(e) { return [e.read_shift(1), e.read_shift(1)] }

                function ei(e, t) { var n = [e.read_shift(1)]; if (12 == t) switch (n[0]) {
                        case 2:
                            n[0] = 4; break;
                        case 4:
                            n[0] = 16; break;
                        case 0:
                            n[0] = 1; break;
                        case 1:
                            n[0] = 2 }
                    switch (n[0]) {
                        case 4:
                            n[1] = Ur(e, 1) ? "TRUE" : "FALSE", 12 != t && (e.l += 7); break;
                        case 37:
                        case 16:
                            n[1] = vr[e[e.l]], e.l += 12 == t ? 4 : 8; break;
                        case 0:
                            e.l += 8; break;
                        case 1:
                            n[1] = nr(e); break;
                        case 2:
                            n[1] = $r(e, 0, { biff: t > 0 && t < 8 ? 2 : t }); break;
                        default:
                            throw new Error("Bad SerAr: " + n[0]) } return n }

                function ti(e, t, n) { for (var r = e.read_shift(12 == n.biff ? 4 : 2), a = [], o = 0; o != r; ++o) a.push((12 == n.biff ? tr : oa)(e, 8)); return a }

                function ni(e, t, n) { var r = 0,
                        a = 0;
                    12 == n.biff ? (r = e.read_shift(4), a = e.read_shift(4)) : (a = 1 + e.read_shift(1), r = 1 + e.read_shift(2)), n.biff >= 2 && n.biff < 8 && (--r, 0 == --a && (a = 256)); for (var o = 0, i = []; o != r && (i[o] = []); ++o)
                        for (var l = 0; l != a; ++l) i[o][l] = ei(e, n.biff); return i }

                function ri(e, t, n) { return e.l += 2, [$o(e)] }

                function ai(e) { return e.l += 6, [] }

                function oi(e) { return e.l += 2, [qr(e), 1 & e.read_shift(2)] } var ii = ["Data", "All", "Headers", "??", "?Data2", "??", "?DataHeaders", "??", "Totals", "??", "??", "??", "?DataTotals", "??", "??", "??", "?Current"]; var li = { 1: { n: "PtgExp", f: function(e, t, n) { return e.l++, n && 12 == n.biff ? [e.read_shift(4, "i"), 0] : [e.read_shift(2), e.read_shift(n && 2 == n.biff ? 1 : 2)] } }, 2: { n: "PtgTbl", f: zn }, 3: { n: "PtgAdd", f: Go }, 4: { n: "PtgSub", f: Go }, 5: { n: "PtgMul", f: Go }, 6: { n: "PtgDiv", f: Go }, 7: { n: "PtgPower", f: Go }, 8: { n: "PtgConcat", f: Go }, 9: { n: "PtgLt", f: Go }, 10: { n: "PtgLe", f: Go }, 11: { n: "PtgEq", f: Go }, 12: { n: "PtgGe", f: Go }, 13: { n: "PtgGt", f: Go }, 14: { n: "PtgNe", f: Go }, 15: { n: "PtgIsect", f: Go }, 16: { n: "PtgUnion", f: Go }, 17: { n: "PtgRange", f: Go }, 18: { n: "PtgUplus", f: Go }, 19: { n: "PtgUminus", f: Go }, 20: { n: "PtgPercent", f: Go }, 21: { n: "PtgParen", f: Go }, 22: { n: "PtgMissArg", f: Go }, 23: { n: "PtgStr", f: function(e, t, n) { return e.l++, Kr(e, 0, n) } }, 26: { n: "PtgSheet", f: function(e, t, n) { return e.l += 5, e.l += 2, e.l += 2 == n.biff ? 1 : 4, ["PTGSHEET"] } }, 27: { n: "PtgEndSheet", f: function(e, t, n) { return e.l += 2 == n.biff ? 4 : 5, ["PTGENDSHEET"] } }, 28: { n: "PtgErr", f: function(e) { return e.l++, vr[e.read_shift(1)] } }, 29: { n: "PtgBool", f: function(e) { return e.l++, 0 !== e.read_shift(1) } }, 30: { n: "PtgInt", f: function(e) { return e.l++, e.read_shift(2) } }, 31: { n: "PtgNum", f: function(e) { return e.l++, nr(e) } }, 32: { n: "PtgArray", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5; return e.l += 2 == n.biff ? 6 : 12 == n.biff ? 14 : 7, [r] } }, 33: { n: "PtgFunc", f: function(e, t, n) { var r = (96 & e[e.l]) >> 5;
                                e.l += 1; var a = e.read_shift(n && n.biff <= 3 ? 1 : 2); return [Ci[a], Ei[a], r] } }, 34: { n: "PtgFuncVar", f: function(e, t, n) { var r = e[e.l++],
                                    a = e.read_shift(1),
                                    o = n && n.biff <= 3 ? [88 == r ? -1 : 0, e.read_shift(1)] : function(e) { return [e[e.l + 1] >> 7, 32767 & e.read_shift(2)] }(e); return [a, (0 === o[0] ? Ei : Mi)[o[1]]] } }, 35: { n: "PtgName", f: function(e, t, n) { var r = e.read_shift(1) >>> 5 & 3,
                                    a = !n || n.biff >= 8 ? 4 : 2,
                                    o = e.read_shift(a); switch (n.biff) {
                                    case 2:
                                        e.l += 5; break;
                                    case 3:
                                    case 4:
                                        e.l += 8; break;
                                    case 5:
                                        e.l += 12 } return [r, 0, o] } }, 36: { n: "PtgRef", f: function(e, t, n) { var r = (96 & e[e.l]) >> 5; return e.l += 1, [r, Xo(e, 0, n)] } }, 37: { n: "PtgArea", f: function(e, t, n) { return [(96 & e[e.l++]) >> 5, Zo(e, n.biff >= 2 && n.biff, n)] } }, 38: { n: "PtgMemArea", f: function(e, t, n) { var r = e.read_shift(1) >>> 5 & 3; return e.l += n && 2 == n.biff ? 3 : 4, [r, e.read_shift(n && 2 == n.biff ? 1 : 2)] } }, 39: { n: "PtgMemErr", f: zn }, 40: { n: "PtgMemNoMem", f: zn }, 41: { n: "PtgMemFunc", f: function(e, t, n) { return [e.read_shift(1) >>> 5 & 3, e.read_shift(n && 2 == n.biff ? 1 : 2)] } }, 42: { n: "PtgRefErr", f: function(e, t, n) { var r = e.read_shift(1) >>> 5 & 3; return e.l += 4, n.biff < 8 && e.l--, 12 == n.biff && (e.l += 2), [r] } }, 43: { n: "PtgAreaErr", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5; return e.l += n && n.biff > 8 ? 12 : n.biff < 8 ? 6 : 8, [r] } }, 44: { n: "PtgRefN", f: function(e, t, n) { var r = (96 & e[e.l]) >> 5;
                                e.l += 1; var a = function(e, t, n) { var r = n && n.biff ? n.biff : 8; if (r >= 2 && r <= 5) return function(e) { var t = e.read_shift(2),
                                            n = e.read_shift(1),
                                            r = (32768 & t) >> 15,
                                            a = (16384 & t) >> 14; return t &= 16383, 1 == r && t >= 8192 && (t -= 16384), 1 == a && n >= 128 && (n -= 256), { r: t, c: n, cRel: a, rRel: r } }(e); var a = e.read_shift(r >= 12 ? 4 : 2),
                                        o = e.read_shift(2),
                                        i = (16384 & o) >> 14,
                                        l = (32768 & o) >> 15; if (o &= 16383, 1 == l)
                                        for (; a > 524287;) a -= 1048576; if (1 == i)
                                        for (; o > 8191;) o -= 16384; return { r: a, c: o, cRel: i, rRel: l } }(e, 0, n); return [r, a] } }, 45: { n: "PtgAreaN", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5,
                                    a = function(e, t, n) { if (n.biff < 8) return Yo(e); var r = e.read_shift(12 == n.biff ? 4 : 2),
                                            a = e.read_shift(12 == n.biff ? 4 : 2),
                                            o = Ko(e, 2),
                                            i = Ko(e, 2); return { s: { r: r, c: o[0], cRel: o[1], rRel: o[2] }, e: { r: a, c: i[0], cRel: i[1], rRel: i[2] } } }(e, 0, n); return [r, a] } }, 46: { n: "PtgMemAreaN", f: function(e) { return [e.read_shift(1) >>> 5 & 3, e.read_shift(2)] } }, 47: { n: "PtgMemNoMemN", f: function(e) { return [e.read_shift(1) >>> 5 & 3, e.read_shift(2)] } }, 57: { n: "PtgNameX", f: function(e, t, n) { return 5 == n.biff ? function(e) { var t = e.read_shift(1) >>> 5 & 3,
                                        n = e.read_shift(2, "i");
                                    e.l += 8; var r = e.read_shift(2); return e.l += 12, [t, n, r] }(e) : [e.read_shift(1) >>> 5 & 3, e.read_shift(2), e.read_shift(4)] } }, 58: { n: "PtgRef3d", f: function(e, t, n) { var r = (96 & e[e.l]) >> 5;
                                e.l += 1; var a = e.read_shift(2); return n && 5 == n.biff && (e.l += 12), [r, a, Xo(e, 0, n)] } }, 59: { n: "PtgArea3d", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5,
                                    a = e.read_shift(2, "i"); if (n) switch (n.biff) {
                                    case 5:
                                        e.l += 12, 6; break;
                                    case 12:
                                        12 }
                                return [r, a, Zo(e, 0, n)] } }, 60: { n: "PtgRefErr3d", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5,
                                    a = e.read_shift(2),
                                    o = 4; if (n) switch (n.biff) {
                                    case 5:
                                        o = 15; break;
                                    case 12:
                                        o = 6 }
                                return e.l += o, [r, a] } }, 61: { n: "PtgAreaErr3d", f: function(e, t, n) { var r = (96 & e[e.l++]) >> 5,
                                    a = e.read_shift(2),
                                    o = 8; if (n) switch (n.biff) {
                                    case 5:
                                        e.l += 12, o = 6; break;
                                    case 12:
                                        o = 12 }
                                return e.l += o, [r, a] } }, 255: {} },
                    si = { 64: 32, 96: 32, 65: 33, 97: 33, 66: 34, 98: 34, 67: 35, 99: 35, 68: 36, 100: 36, 69: 37, 101: 37, 70: 38, 102: 38, 71: 39, 103: 39, 72: 40, 104: 40, 73: 41, 105: 41, 74: 42, 106: 42, 75: 43, 107: 43, 76: 44, 108: 44, 77: 45, 109: 45, 78: 46, 110: 46, 79: 47, 111: 47, 88: 34, 120: 34, 89: 57, 121: 57, 90: 58, 122: 58, 91: 59, 123: 59, 92: 60, 124: 60, 93: 61, 125: 61 },
                    ci = { 1: { n: "PtgElfLel", f: oi }, 2: { n: "PtgElfRw", f: ri }, 3: { n: "PtgElfCol", f: ri }, 6: { n: "PtgElfRwV", f: ri }, 7: { n: "PtgElfColV", f: ri }, 10: { n: "PtgElfRadical", f: ri }, 11: { n: "PtgElfRadicalS", f: ai }, 13: { n: "PtgElfColS", f: ai }, 15: { n: "PtgElfColSV", f: ai }, 16: { n: "PtgElfRadicalLel", f: oi }, 25: { n: "PtgList", f: function(e) { e.l += 2; var t = e.read_shift(2),
                                    n = e.read_shift(2),
                                    r = e.read_shift(4),
                                    a = e.read_shift(2),
                                    o = e.read_shift(2); return { ixti: t, coltype: 3 & n, rt: ii[n >> 2 & 31], idx: r, c: a, C: o } } }, 29: { n: "PtgSxName", f: function(e) { return e.l += 2, [e.read_shift(4)] } }, 255: {} },
                    di = { 0: { n: "PtgAttrNoop", f: function(e) { return e.l += 4, [0, 0] } }, 1: { n: "PtgAttrSemi", f: function(e, t, n) { var r = 255 & e[e.l + 1] ? 1 : 0; return e.l += n && 2 == n.biff ? 3 : 4, [r] } }, 2: { n: "PtgAttrIf", f: function(e, t, n) { var r = 255 & e[e.l + 1] ? 1 : 0; return e.l += 2, [r, e.read_shift(n && 2 == n.biff ? 1 : 2)] } }, 4: { n: "PtgAttrChoose", f: function(e, t, n) { e.l += 2; for (var r = e.read_shift(n && 2 == n.biff ? 1 : 2), a = [], o = 0; o <= r; ++o) a.push(e.read_shift(n && 2 == n.biff ? 1 : 2)); return a } }, 8: { n: "PtgAttrGoto", f: function(e, t, n) { var r = 255 & e[e.l + 1] ? 1 : 0; return e.l += 2, [r, e.read_shift(n && 2 == n.biff ? 1 : 2)] } }, 16: { n: "PtgAttrSum", f: function(e, t, n) { e.l += n && 2 == n.biff ? 3 : 4 } }, 32: { n: "PtgAttrBaxcel", f: Qo }, 33: { n: "PtgAttrBaxcel", f: Qo }, 64: { n: "PtgAttrSpace", f: function(e) { return e.read_shift(2), Jo(e) } }, 65: { n: "PtgAttrSpaceSemi", f: function(e) { return e.read_shift(2), Jo(e) } }, 128: { n: "PtgAttrIfError", f: function(e) { var t = 255 & e[e.l + 1] ? 1 : 0; return e.l += 2, [t, e.read_shift(2)] } }, 255: {} };

                function ui(e, t, n, r) { if (r.biff < 8) return zn(e, t); for (var a = e.l + t, o = [], i = 0; i !== n.length; ++i) switch (n[i][0]) {
                        case "PtgArray":
                            n[i][1] = ni(e, 0, r), o.push(n[i][1]); break;
                        case "PtgMemArea":
                            n[i][2] = ti(e, n[i][1], r), o.push(n[i][2]); break;
                        case "PtgExp":
                            r && 12 == r.biff && (n[i][1][1] = e.read_shift(4), o.push(n[i][1])); break;
                        case "PtgList":
                        case "PtgElfRadicalS":
                        case "PtgElfColS":
                        case "PtgElfColSV":
                            throw "Unsupported " + n[i][0] }
                    return 0 !== (t = a - e.l) && o.push(zn(e, t)), o }

                function hi(e, t, n) { for (var r, a, o = e.l + t, i = []; o != e.l;) t = o - e.l, a = e[e.l], r = li[a] || li[si[a]], 24 !== a && 25 !== a || (r = (24 === a ? ci : di)[e[e.l + 1]]), r && r.f ? i.push([r.n, r.f(e, t, n)]) : zn(e, t); return i }

                function mi(e) { for (var t = [], n = 0; n < e.length; ++n) { for (var r = e[n], a = [], o = 0; o < r.length; ++o) { var i = r[o]; if (i)
                                if (2 === i[0]) a.push('"' + i[1].replace(/"/g, '""') + '"');
                                else a.push(i[1]);
                            else a.push("") } t.push(a.join(",")) } return t.join(";") } var pi = { PtgAdd: "+", PtgConcat: "&", PtgDiv: "/", PtgEq: "=", PtgGe: ">=", PtgGt: ">", PtgLe: "<=", PtgLt: "<", PtgMul: "*", PtgNe: "<>", PtgPower: "^", PtgSub: "-" };

                function fi(e, t, n) { if (!e) return "SH33TJSERR0"; if (n.biff > 8 && (!e.XTI || !e.XTI[t])) return e.SheetNames[t]; if (!e.XTI) return "SH33TJSERR6"; var r = e.XTI[t]; if (n.biff < 8) return t > 1e4 && (t -= 65536), t < 0 && (t = -t), 0 == t ? "" : e.XTI[t - 1]; if (!r) return "SH33TJSERR1"; var a = ""; if (n.biff > 8) switch (e[r[0]][0]) {
                        case 357:
                            return a = -1 == r[1] ? "#REF" : e.SheetNames[r[1]], r[1] == r[2] ? a : a + ":" + e.SheetNames[r[2]];
                        case 358:
                            return null != n.SID ? e.SheetNames[n.SID] : "SH33TJSSAME" + e[r[0]][0];
                        default:
                            return "SH33TJSSRC" + e[r[0]][0] }
                    switch (e[r[0]][0][0]) {
                        case 1025:
                            return a = -1 == r[1] ? "#REF" : e.SheetNames[r[1]] || "SH33TJSERR3", r[1] == r[2] ? a : a + ":" + e.SheetNames[r[2]];
                        case 14849:
                            return e[r[0]].slice(1).map((function(e) { return e.Name })).join(";;");
                        default:
                            return e[r[0]][0][3] ? (a = -1 == r[1] ? "#REF" : e[r[0]][0][3][r[1]] || "SH33TJSERR4", r[1] == r[2] ? a : a + ":" + e[r[0]][0][3][r[2]]) : "SH33TJSERR2" } }

                function vi(e, t, n) { var r = fi(e, t, n); return "#REF" == r ? r : function(e, t) { if (!e && !(t && t.biff <= 5 && t.biff >= 2)) throw new Error("empty sheet name"); return /[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e) ? "'" + e + "'" : e }(r, n) }

                function gi(e, t, n, r, a) { var o, i, l, s, c = a && a.biff || 8,
                        d = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } },
                        u = [],
                        h = 0,
                        m = 0,
                        p = ""; if (!e[0] || !e[0][0]) return ""; for (var f = -1, v = "", g = 0, y = e[0].length; g < y; ++g) { var b = e[0][g]; switch (b[0]) {
                            case "PtgUminus":
                                u.push("-" + u.pop()); break;
                            case "PtgUplus":
                                u.push("+" + u.pop()); break;
                            case "PtgPercent":
                                u.push(u.pop() + "%"); break;
                            case "PtgAdd":
                            case "PtgConcat":
                            case "PtgDiv":
                            case "PtgEq":
                            case "PtgGe":
                            case "PtgGt":
                            case "PtgLe":
                            case "PtgLt":
                            case "PtgMul":
                            case "PtgNe":
                            case "PtgPower":
                            case "PtgSub":
                                if (o = u.pop(), i = u.pop(), f >= 0) { switch (e[0][f][1][0]) {
                                        case 0:
                                            v = Be(" ", e[0][f][1][1]); break;
                                        case 1:
                                            v = Be("\r", e[0][f][1][1]); break;
                                        default:
                                            if (v = "", a.WTF) throw new Error("Unexpected PtgAttrSpaceType " + e[0][f][1][0]) } i += v, f = -1 } u.push(i + pi[b[0]] + o); break;
                            case "PtgIsect":
                                o = u.pop(), i = u.pop(), u.push(i + " " + o); break;
                            case "PtgUnion":
                                o = u.pop(), i = u.pop(), u.push(i + "," + o); break;
                            case "PtgRange":
                                o = u.pop(), i = u.pop(), u.push(i + ":" + o); break;
                            case "PtgAttrChoose":
                            case "PtgAttrGoto":
                            case "PtgAttrIf":
                            case "PtgAttrIfError":
                            case "PtgAttrBaxcel":
                            case "PtgAttrSemi":
                            case "PtgMemArea":
                            case "PtgTbl":
                            case "PtgMemErr":
                            case "PtgMemAreaN":
                            case "PtgMemNoMemN":
                            case "PtgAttrNoop":
                            case "PtgSheet":
                            case "PtgEndSheet":
                            case "PtgMemFunc":
                            case "PtgMemNoMem":
                                break;
                            case "PtgRef":
                                l = Sn(b[1][1], d, a), u.push(En(l, c)); break;
                            case "PtgRefN":
                                l = n ? Sn(b[1][1], n, a) : b[1][1], u.push(En(l, c)); break;
                            case "PtgRef3d":
                                h = b[1][1], l = Sn(b[1][2], d, a);
                                p = vi(r, h, a);
                                u.push(p + "!" + En(l, c)); break;
                            case "PtgFunc":
                            case "PtgFuncVar":
                                var w = b[1][0],
                                    z = b[1][1];
                                w || (w = 0); var x = 0 == (w &= 127) ? [] : u.slice(-w);
                                u.length -= w, "User" === z && (z = x.shift()), u.push(z + "(" + x.join(",") + ")"); break;
                            case "PtgBool":
                                u.push(b[1] ? "TRUE" : "FALSE"); break;
                            case "PtgInt":
                            case "PtgErr":
                                u.push(b[1]); break;
                            case "PtgNum":
                                u.push(String(b[1])); break;
                            case "PtgStr":
                                u.push('"' + b[1].replace(/"/g, '""') + '"'); break;
                            case "PtgAreaN":
                                s = Mn(b[1][1], n ? { s: n } : d, a), u.push(Cn(s, a)); break;
                            case "PtgArea":
                                s = Mn(b[1][1], d, a), u.push(Cn(s, a)); break;
                            case "PtgArea3d":
                                h = b[1][1], s = b[1][2], p = vi(r, h, a), u.push(p + "!" + Cn(s, a)); break;
                            case "PtgAttrSum":
                                u.push("SUM(" + u.pop() + ")"); break;
                            case "PtgName":
                                m = b[1][2]; var A = (r.names || [])[m - 1] || (r[0] || [])[m],
                                    k = A ? A.Name : "SH33TJSNAME" + String(m);
                                k && "_xlfn." == k.slice(0, 6) && !a.xlfn && (k = k.slice(6)), u.push(k); break;
                            case "PtgNameX":
                                var S, M = b[1][1]; if (m = b[1][2], !(a.biff <= 5)) { var E = ""; if (14849 == ((r[M] || [])[0] || [])[0] || (1025 == ((r[M] || [])[0] || [])[0] ? r[M][m] && r[M][m].itab > 0 && (E = r.SheetNames[r[M][m].itab - 1] + "!") : E = r.SheetNames[m - 1] + "!"), r[M] && r[M][m]) E += r[M][m].Name;
                                    else if (r[0] && r[0][m]) E += r[0][m].Name;
                                    else { var C = (fi(r, M, a) || "").split(";;");
                                        C[m - 1] ? E = C[m - 1] : E += "SH33TJSERRX" } u.push(E); break } M < 0 && (M = -M), r[M] && (S = r[M][m]), S || (S = { Name: "SH33TJSERRY" }), u.push(S.Name); break;
                            case "PtgParen":
                                var T = "(",
                                    H = ")"; if (f >= 0) { switch (v = "", e[0][f][1][0]) {
                                        case 2:
                                            T = Be(" ", e[0][f][1][1]) + T; break;
                                        case 3:
                                            T = Be("\r", e[0][f][1][1]) + T; break;
                                        case 4:
                                            H = Be(" ", e[0][f][1][1]) + H; break;
                                        case 5:
                                            H = Be("\r", e[0][f][1][1]) + H; break;
                                        default:
                                            if (a.WTF) throw new Error("Unexpected PtgAttrSpaceType " + e[0][f][1][0]) } f = -1 } u.push(T + u.pop() + H); break;
                            case "PtgRefErr":
                            case "PtgRefErr3d":
                            case "PtgAreaErr":
                            case "PtgAreaErr3d":
                                u.push("#REF!"); break;
                            case "PtgExp":
                                l = { c: b[1][1], r: b[1][0] }; var L = { c: n.c, r: n.r }; if (r.sharedf[Vn(l)]) { var I = r.sharedf[Vn(l)];
                                    u.push(gi(I, d, L, r, a)) } else { var j = !1; for (o = 0; o != r.arrayf.length; ++o)
                                        if (i = r.arrayf[o], !(l.c < i[0].s.c || l.c > i[0].e.c) && !(l.r < i[0].s.r || l.r > i[0].e.r)) { u.push(gi(i[1], d, L, r, a)), j = !0; break } j || u.push(b[1]) } break;
                            case "PtgArray":
                                u.push("{" + mi(b[1]) + "}"); break;
                            case "PtgAttrSpace":
                            case "PtgAttrSpaceSemi":
                                f = g; break;
                            case "PtgMissArg":
                                u.push(""); break;
                            case "PtgList":
                                u.push("Table" + b[1].idx + "[#" + b[1].rt + "]"); break;
                            case "PtgElfCol":
                            case "PtgElfColS":
                            case "PtgElfColSV":
                            case "PtgElfColV":
                            case "PtgElfLel":
                            case "PtgElfRadical":
                            case "PtgElfRadicalLel":
                            case "PtgElfRadicalS":
                            case "PtgElfRw":
                            case "PtgElfRwV":
                                throw new Error("Unsupported ELFs");
                            default:
                                throw new Error("Unrecognized Formula Token: " + String(b)) } if (3 != a.biff && f >= 0 && -1 == ["PtgAttrSpace", "PtgAttrSpaceSemi", "PtgAttrGoto"].indexOf(e[0][g][0])) { var V = !0; switch ((b = e[0][f])[1][0]) {
                                case 4:
                                    V = !1;
                                case 0:
                                    v = Be(" ", b[1][1]); break;
                                case 5:
                                    V = !1;
                                case 1:
                                    v = Be("\r", b[1][1]); break;
                                default:
                                    if (v = "", a.WTF) throw new Error("Unexpected PtgAttrSpaceType " + b[1][0]) } u.push((V ? v : "") + u.pop() + (V ? "" : v)), f = -1 } } if (u.length > 1 && a.WTF) throw new Error("bad formula stack"); return u[0] }

                function yi(e, t, n) { var r, a = e.l + t,
                        o = 2 == n.biff ? 1 : 2,
                        i = e.read_shift(o); if (65535 == i) return [
                        [], zn(e, t - 2)
                    ]; var l = hi(e, i, n); return t !== i + o && (r = ui(e, t - i - o, l, n)), e.l = a, [l, r] }

                function bi(e, t, n) { var r, a = e.l + t,
                        o = e.read_shift(2),
                        i = hi(e, o, n); return 65535 == o ? [
                        [], zn(e, t - 2)
                    ] : (t !== o + 2 && (r = ui(e, a - o - 2, i, n)), [i, r]) }

                function wi(e, t, n) { var r = e.l + t,
                        a = na(e);
                    2 == n.biff && ++e.l; var o = function(e) { var t; if (65535 !== cn(e, e.l + 6)) return [nr(e), "n"]; switch (e[e.l]) {
                                case 0:
                                    return e.l += 8, ["String", "s"];
                                case 1:
                                    return t = 1 === e[e.l + 2], e.l += 8, [t, "b"];
                                case 2:
                                    return t = e[e.l + 2], e.l += 8, [t, "e"];
                                case 3:
                                    return e.l += 8, ["", "s"] } return [] }(e),
                        i = e.read_shift(1);
                    2 != n.biff && (e.read_shift(1), n.biff >= 5 && e.read_shift(4)); var l = function(e, t, n) { var r, a = e.l + t,
                            o = 2 == n.biff ? 1 : 2,
                            i = e.read_shift(o); if (65535 == i) return [
                            [], zn(e, t - 2)
                        ]; var l = hi(e, i, n); return t !== i + o && (r = ui(e, t - i - o, l, n)), e.l = a, [l, r] }(e, r - e.l, n); return { cell: a, val: o[0], formula: l, shared: i >> 3 & 1, tt: o[1] } }

                function zi(e, t, n) { var r = e.read_shift(4),
                        a = hi(e, r, n),
                        o = e.read_shift(4); return [a, o > 0 ? ui(e, o, a, n) : null] } var xi = zi,
                    Ai = zi,
                    ki = zi,
                    Si = zi,
                    Mi = { 0: "BEEP", 1: "OPEN", 2: "OPEN.LINKS", 3: "CLOSE.ALL", 4: "SAVE", 5: "SAVE.AS", 6: "FILE.DELETE", 7: "PAGE.SETUP", 8: "PRINT", 9: "PRINTER.SETUP", 10: "QUIT", 11: "NEW.WINDOW", 12: "ARRANGE.ALL", 13: "WINDOW.SIZE", 14: "WINDOW.MOVE", 15: "FULL", 16: "CLOSE", 17: "RUN", 22: "SET.PRINT.AREA", 23: "SET.PRINT.TITLES", 24: "SET.PAGE.BREAK", 25: "REMOVE.PAGE.BREAK", 26: "FONT", 27: "DISPLAY", 28: "PROTECT.DOCUMENT", 29: "PRECISION", 30: "A1.R1C1", 31: "CALCULATE.NOW", 32: "CALCULATION", 34: "DATA.FIND", 35: "EXTRACT", 36: "DATA.DELETE", 37: "SET.DATABASE", 38: "SET.CRITERIA", 39: "SORT", 40: "DATA.SERIES", 41: "TABLE", 42: "FORMAT.NUMBER", 43: "ALIGNMENT", 44: "STYLE", 45: "BORDER", 46: "CELL.PROTECTION", 47: "COLUMN.WIDTH", 48: "UNDO", 49: "CUT", 50: "COPY", 51: "PASTE", 52: "CLEAR", 53: "PASTE.SPECIAL", 54: "EDIT.DELETE", 55: "INSERT", 56: "FILL.RIGHT", 57: "FILL.DOWN", 61: "DEFINE.NAME", 62: "CREATE.NAMES", 63: "FORMULA.GOTO", 64: "FORMULA.FIND", 65: "SELECT.LAST.CELL", 66: "SHOW.ACTIVE.CELL", 67: "GALLERY.AREA", 68: "GALLERY.BAR", 69: "GALLERY.COLUMN", 70: "GALLERY.LINE", 71: "GALLERY.PIE", 72: "GALLERY.SCATTER", 73: "COMBINATION", 74: "PREFERRED", 75: "ADD.OVERLAY", 76: "GRIDLINES", 77: "SET.PREFERRED", 78: "AXES", 79: "LEGEND", 80: "ATTACH.TEXT", 81: "ADD.ARROW", 82: "SELECT.CHART", 83: "SELECT.PLOT.AREA", 84: "PATTERNS", 85: "MAIN.CHART", 86: "OVERLAY", 87: "SCALE", 88: "FORMAT.LEGEND", 89: "FORMAT.TEXT", 90: "EDIT.REPEAT", 91: "PARSE", 92: "JUSTIFY", 93: "HIDE", 94: "UNHIDE", 95: "WORKSPACE", 96: "FORMULA", 97: "FORMULA.FILL", 98: "FORMULA.ARRAY", 99: "DATA.FIND.NEXT", 100: "DATA.FIND.PREV", 101: "FORMULA.FIND.NEXT", 102: "FORMULA.FIND.PREV", 103: "ACTIVATE", 104: "ACTIVATE.NEXT", 105: "ACTIVATE.PREV", 106: "UNLOCKED.NEXT", 107: "UNLOCKED.PREV", 108: "COPY.PICTURE", 109: "SELECT", 110: "DELETE.NAME", 111: "DELETE.FORMAT", 112: "VLINE", 113: "HLINE", 114: "VPAGE", 115: "HPAGE", 116: "VSCROLL", 117: "HSCROLL", 118: "ALERT", 119: "NEW", 120: "CANCEL.COPY", 121: "SHOW.CLIPBOARD", 122: "MESSAGE", 124: "PASTE.LINK", 125: "APP.ACTIVATE", 126: "DELETE.ARROW", 127: "ROW.HEIGHT", 128: "FORMAT.MOVE", 129: "FORMAT.SIZE", 130: "FORMULA.REPLACE", 131: "SEND.KEYS", 132: "SELECT.SPECIAL", 133: "APPLY.NAMES", 134: "REPLACE.FONT", 135: "FREEZE.PANES", 136: "SHOW.INFO", 137: "SPLIT", 138: "ON.WINDOW", 139: "ON.DATA", 140: "DISABLE.INPUT", 142: "OUTLINE", 143: "LIST.NAMES", 144: "FILE.CLOSE", 145: "SAVE.WORKBOOK", 146: "DATA.FORM", 147: "COPY.CHART", 148: "ON.TIME", 149: "WAIT", 150: "FORMAT.FONT", 151: "FILL.UP", 152: "FILL.LEFT", 153: "DELETE.OVERLAY", 155: "SHORT.MENUS", 159: "SET.UPDATE.STATUS", 161: "COLOR.PALETTE", 162: "DELETE.STYLE", 163: "WINDOW.RESTORE", 164: "WINDOW.MAXIMIZE", 166: "CHANGE.LINK", 167: "CALCULATE.DOCUMENT", 168: "ON.KEY", 169: "APP.RESTORE", 170: "APP.MOVE", 171: "APP.SIZE", 172: "APP.MINIMIZE", 173: "APP.MAXIMIZE", 174: "BRING.TO.FRONT", 175: "SEND.TO.BACK", 185: "MAIN.CHART.TYPE", 186: "OVERLAY.CHART.TYPE", 187: "SELECT.END", 188: "OPEN.MAIL", 189: "SEND.MAIL", 190: "STANDARD.FONT", 191: "CONSOLIDATE", 192: "SORT.SPECIAL", 193: "GALLERY.3D.AREA", 194: "GALLERY.3D.COLUMN", 195: "GALLERY.3D.LINE", 196: "GALLERY.3D.PIE", 197: "VIEW.3D", 198: "GOAL.SEEK", 199: "WORKGROUP", 200: "FILL.GROUP", 201: "UPDATE.LINK", 202: "PROMOTE", 203: "DEMOTE", 204: "SHOW.DETAIL", 206: "UNGROUP", 207: "OBJECT.PROPERTIES", 208: "SAVE.NEW.OBJECT", 209: "SHARE", 210: "SHARE.NAME", 211: "DUPLICATE", 212: "APPLY.STYLE", 213: "ASSIGN.TO.OBJECT", 214: "OBJECT.PROTECTION", 215: "HIDE.OBJECT", 216: "SET.EXTRACT", 217: "CREATE.PUBLISHER", 218: "SUBSCRIBE.TO", 219: "ATTRIBUTES", 220: "SHOW.TOOLBAR", 222: "PRINT.PREVIEW", 223: "EDIT.COLOR", 224: "SHOW.LEVELS", 225: "FORMAT.MAIN", 226: "FORMAT.OVERLAY", 227: "ON.RECALC", 228: "EDIT.SERIES", 229: "DEFINE.STYLE", 240: "LINE.PRINT", 243: "ENTER.DATA", 249: "GALLERY.RADAR", 250: "MERGE.STYLES", 251: "EDITION.OPTIONS", 252: "PASTE.PICTURE", 253: "PASTE.PICTURE.LINK", 254: "SPELLING", 256: "ZOOM", 259: "INSERT.OBJECT", 260: "WINDOW.MINIMIZE", 265: "SOUND.NOTE", 266: "SOUND.PLAY", 267: "FORMAT.SHAPE", 268: "EXTEND.POLYGON", 269: "FORMAT.AUTO", 272: "GALLERY.3D.BAR", 273: "GALLERY.3D.SURFACE", 274: "FILL.AUTO", 276: "CUSTOMIZE.TOOLBAR", 277: "ADD.TOOL", 278: "EDIT.OBJECT", 279: "ON.DOUBLECLICK", 280: "ON.ENTRY", 281: "WORKBOOK.ADD", 282: "WORKBOOK.MOVE", 283: "WORKBOOK.COPY", 284: "WORKBOOK.OPTIONS", 285: "SAVE.WORKSPACE", 288: "CHART.WIZARD", 289: "DELETE.TOOL", 290: "MOVE.TOOL", 291: "WORKBOOK.SELECT", 292: "WORKBOOK.ACTIVATE", 293: "ASSIGN.TO.TOOL", 295: "COPY.TOOL", 296: "RESET.TOOL", 297: "CONSTRAIN.NUMERIC", 298: "PASTE.TOOL", 302: "WORKBOOK.NEW", 305: "SCENARIO.CELLS", 306: "SCENARIO.DELETE", 307: "SCENARIO.ADD", 308: "SCENARIO.EDIT", 309: "SCENARIO.SHOW", 310: "SCENARIO.SHOW.NEXT", 311: "SCENARIO.SUMMARY", 312: "PIVOT.TABLE.WIZARD", 313: "PIVOT.FIELD.PROPERTIES", 314: "PIVOT.FIELD", 315: "PIVOT.ITEM", 316: "PIVOT.ADD.FIELDS", 318: "OPTIONS.CALCULATION", 319: "OPTIONS.EDIT", 320: "OPTIONS.VIEW", 321: "ADDIN.MANAGER", 322: "MENU.EDITOR", 323: "ATTACH.TOOLBARS", 324: "VBAActivate", 325: "OPTIONS.CHART", 328: "VBA.INSERT.FILE", 330: "VBA.PROCEDURE.DEFINITION", 336: "ROUTING.SLIP", 338: "ROUTE.DOCUMENT", 339: "MAIL.LOGON", 342: "INSERT.PICTURE", 343: "EDIT.TOOL", 344: "GALLERY.DOUGHNUT", 350: "CHART.TREND", 352: "PIVOT.ITEM.PROPERTIES", 354: "WORKBOOK.INSERT", 355: "OPTIONS.TRANSITION", 356: "OPTIONS.GENERAL", 370: "FILTER.ADVANCED", 373: "MAIL.ADD.MAILER", 374: "MAIL.DELETE.MAILER", 375: "MAIL.REPLY", 376: "MAIL.REPLY.ALL", 377: "MAIL.FORWARD", 378: "MAIL.NEXT.LETTER", 379: "DATA.LABEL", 380: "INSERT.TITLE", 381: "FONT.PROPERTIES", 382: "MACRO.OPTIONS", 383: "WORKBOOK.HIDE", 384: "WORKBOOK.UNHIDE", 385: "WORKBOOK.DELETE", 386: "WORKBOOK.NAME", 388: "GALLERY.CUSTOM", 390: "ADD.CHART.AUTOFORMAT", 391: "DELETE.CHART.AUTOFORMAT", 392: "CHART.ADD.DATA", 393: "AUTO.OUTLINE", 394: "TAB.ORDER", 395: "SHOW.DIALOG", 396: "SELECT.ALL", 397: "UNGROUP.SHEETS", 398: "SUBTOTAL.CREATE", 399: "SUBTOTAL.REMOVE", 400: "RENAME.OBJECT", 412: "WORKBOOK.SCROLL", 413: "WORKBOOK.NEXT", 414: "WORKBOOK.PREV", 415: "WORKBOOK.TAB.SPLIT", 416: "FULL.SCREEN", 417: "WORKBOOK.PROTECT", 420: "SCROLLBAR.PROPERTIES", 421: "PIVOT.SHOW.PAGES", 422: "TEXT.TO.COLUMNS", 423: "FORMAT.CHARTTYPE", 424: "LINK.FORMAT", 425: "TRACER.DISPLAY", 430: "TRACER.NAVIGATE", 431: "TRACER.CLEAR", 432: "TRACER.ERROR", 433: "PIVOT.FIELD.GROUP", 434: "PIVOT.FIELD.UNGROUP", 435: "CHECKBOX.PROPERTIES", 436: "LABEL.PROPERTIES", 437: "LISTBOX.PROPERTIES", 438: "EDITBOX.PROPERTIES", 439: "PIVOT.REFRESH", 440: "LINK.COMBO", 441: "OPEN.TEXT", 442: "HIDE.DIALOG", 443: "SET.DIALOG.FOCUS", 444: "ENABLE.OBJECT", 445: "PUSHBUTTON.PROPERTIES", 446: "SET.DIALOG.DEFAULT", 447: "FILTER", 448: "FILTER.SHOW.ALL", 449: "CLEAR.OUTLINE", 450: "FUNCTION.WIZARD", 451: "ADD.LIST.ITEM", 452: "SET.LIST.ITEM", 453: "REMOVE.LIST.ITEM", 454: "SELECT.LIST.ITEM", 455: "SET.CONTROL.VALUE", 456: "SAVE.COPY.AS", 458: "OPTIONS.LISTS.ADD", 459: "OPTIONS.LISTS.DELETE", 460: "SERIES.AXES", 461: "SERIES.X", 462: "SERIES.Y", 463: "ERRORBAR.X", 464: "ERRORBAR.Y", 465: "FORMAT.CHART", 466: "SERIES.ORDER", 467: "MAIL.LOGOFF", 468: "CLEAR.ROUTING.SLIP", 469: "APP.ACTIVATE.MICROSOFT", 470: "MAIL.EDIT.MAILER", 471: "ON.SHEET", 472: "STANDARD.WIDTH", 473: "SCENARIO.MERGE", 474: "SUMMARY.INFO", 475: "FIND.FILE", 476: "ACTIVE.CELL.FONT", 477: "ENABLE.TIPWIZARD", 478: "VBA.MAKE.ADDIN", 480: "INSERTDATATABLE", 481: "WORKGROUP.OPTIONS", 482: "MAIL.SEND.MAILER", 485: "AUTOCORRECT", 489: "POST.DOCUMENT", 491: "PICKLIST", 493: "VIEW.SHOW", 494: "VIEW.DEFINE", 495: "VIEW.DELETE", 509: "SHEET.BACKGROUND", 510: "INSERT.MAP.OBJECT", 511: "OPTIONS.MENONO", 517: "MSOCHECKS", 518: "NORMAL", 519: "LAYOUT", 520: "RM.PRINT.AREA", 521: "CLEAR.PRINT.AREA", 522: "ADD.PRINT.AREA", 523: "MOVE.BRK", 545: "HIDECURR.NOTE", 546: "HIDEALL.NOTES", 547: "DELETE.NOTE", 548: "TRAVERSE.NOTES", 549: "ACTIVATE.NOTES", 620: "PROTECT.REVISIONS", 621: "UNPROTECT.REVISIONS", 647: "OPTIONS.ME", 653: "WEB.PUBLISH", 667: "NEWWEBQUERY", 673: "PIVOT.TABLE.CHART", 753: "OPTIONS.SAVE", 755: "OPTIONS.SPELL", 808: "HIDEALL.INKANNOTS" },
                    Ei = { 0: "COUNT", 1: "IF", 2: "ISNA", 3: "ISERROR", 4: "SUM", 5: "AVERAGE", 6: "MIN", 7: "MAX", 8: "ROW", 9: "COLUMN", 10: "NA", 11: "NPV", 12: "STDEV", 13: "DOLLAR", 14: "FIXED", 15: "SIN", 16: "COS", 17: "TAN", 18: "ATAN", 19: "PI", 20: "SQRT", 21: "EXP", 22: "LN", 23: "LOG10", 24: "ABS", 25: "INT", 26: "SIGN", 27: "ROUND", 28: "LOOKUP", 29: "INDEX", 30: "REPT", 31: "MID", 32: "LEN", 33: "VALUE", 34: "TRUE", 35: "FALSE", 36: "AND", 37: "OR", 38: "NOT", 39: "MOD", 40: "DCOUNT", 41: "DSUM", 42: "DAVERAGE", 43: "DMIN", 44: "DMAX", 45: "DSTDEV", 46: "VAR", 47: "DVAR", 48: "TEXT", 49: "LINEST", 50: "TREND", 51: "LOGEST", 52: "GROWTH", 53: "GOTO", 54: "HALT", 55: "RETURN", 56: "PV", 57: "FV", 58: "NPER", 59: "PMT", 60: "RATE", 61: "MIRR", 62: "IRR", 63: "RAND", 64: "MATCH", 65: "DATE", 66: "TIME", 67: "DAY", 68: "MONTH", 69: "YEAR", 70: "WEEKDAY", 71: "HOUR", 72: "MINUTE", 73: "SECOND", 74: "NOW", 75: "AREAS", 76: "ROWS", 77: "COLUMNS", 78: "OFFSET", 79: "ABSREF", 80: "RELREF", 81: "ARGUMENT", 82: "SEARCH", 83: "TRANSPOSE", 84: "ERROR", 85: "STEP", 86: "TYPE", 87: "ECHO", 88: "SET.NAME", 89: "CALLER", 90: "DEREF", 91: "WINDOWS", 92: "SERIES", 93: "DOCUMENTS", 94: "ACTIVE.CELL", 95: "SELECTION", 96: "RESULT", 97: "ATAN2", 98: "ASIN", 99: "ACOS", 100: "CHOOSE", 101: "HLOOKUP", 102: "VLOOKUP", 103: "LINKS", 104: "INPUT", 105: "ISREF", 106: "GET.FORMULA", 107: "GET.NAME", 108: "SET.VALUE", 109: "LOG", 110: "EXEC", 111: "CHAR", 112: "LOWER", 113: "UPPER", 114: "PROPER", 115: "LEFT", 116: "RIGHT", 117: "EXACT", 118: "TRIM", 119: "REPLACE", 120: "SUBSTITUTE", 121: "CODE", 122: "NAMES", 123: "DIRECTORY", 124: "FIND", 125: "CELL", 126: "ISERR", 127: "ISTEXT", 128: "ISNUMBER", 129: "ISBLANK", 130: "T", 131: "N", 132: "FOPEN", 133: "FCLOSE", 134: "FSIZE", 135: "FREADLN", 136: "FREAD", 137: "FWRITELN", 138: "FWRITE", 139: "FPOS", 140: "DATEVALUE", 141: "TIMEVALUE", 142: "SLN", 143: "SYD", 144: "DDB", 145: "GET.DEF", 146: "REFTEXT", 147: "TEXTREF", 148: "INDIRECT", 149: "REGISTER", 150: "CALL", 151: "ADD.BAR", 152: "ADD.MENU", 153: "ADD.COMMAND", 154: "ENABLE.COMMAND", 155: "CHECK.COMMAND", 156: "RENAME.COMMAND", 157: "SHOW.BAR", 158: "DELETE.MENU", 159: "DELETE.COMMAND", 160: "GET.CHART.ITEM", 161: "DIALOG.BOX", 162: "CLEAN", 163: "MDETERM", 164: "MINVERSE", 165: "MMULT", 166: "FILES", 167: "IPMT", 168: "PPMT", 169: "COUNTA", 170: "CANCEL.KEY", 171: "FOR", 172: "WHILE", 173: "BREAK", 174: "NEXT", 175: "INITIATE", 176: "REQUEST", 177: "POKE", 178: "EXECUTE", 179: "TERMINATE", 180: "RESTART", 181: "HELP", 182: "GET.BAR", 183: "PRODUCT", 184: "FACT", 185: "GET.CELL", 186: "GET.WORKSPACE", 187: "GET.WINDOW", 188: "GET.DOCUMENT", 189: "DPRODUCT", 190: "ISNONTEXT", 191: "GET.NOTE", 192: "NOTE", 193: "STDEVP", 194: "VARP", 195: "DSTDEVP", 196: "DVARP", 197: "TRUNC", 198: "ISLOGICAL", 199: "DCOUNTA", 200: "DELETE.BAR", 201: "UNREGISTER", 204: "USDOLLAR", 205: "FINDB", 206: "SEARCHB", 207: "REPLACEB", 208: "LEFTB", 209: "RIGHTB", 210: "MIDB", 211: "LENB", 212: "ROUNDUP", 213: "ROUNDDOWN", 214: "ASC", 215: "DBCS", 216: "RANK", 219: "ADDRESS", 220: "DAYS360", 221: "TODAY", 222: "VDB", 223: "ELSE", 224: "ELSE.IF", 225: "END.IF", 226: "FOR.CELL", 227: "MEDIAN", 228: "SUMPRODUCT", 229: "SINH", 230: "COSH", 231: "TANH", 232: "ASINH", 233: "ACOSH", 234: "ATANH", 235: "DGET", 236: "CREATE.OBJECT", 237: "VOLATILE", 238: "LAST.ERROR", 239: "CUSTOM.UNDO", 240: "CUSTOM.REPEAT", 241: "FORMULA.CONVERT", 242: "GET.LINK.INFO", 243: "TEXT.BOX", 244: "INFO", 245: "GROUP", 246: "GET.OBJECT", 247: "DB", 248: "PAUSE", 251: "RESUME", 252: "FREQUENCY", 253: "ADD.TOOLBAR", 254: "DELETE.TOOLBAR", 255: "User", 256: "RESET.TOOLBAR", 257: "EVALUATE", 258: "GET.TOOLBAR", 259: "GET.TOOL", 260: "SPELLING.CHECK", 261: "ERROR.TYPE", 262: "APP.TITLE", 263: "WINDOW.TITLE", 264: "SAVE.TOOLBAR", 265: "ENABLE.TOOL", 266: "PRESS.TOOL", 267: "REGISTER.ID", 268: "GET.WORKBOOK", 269: "AVEDEV", 270: "BETADIST", 271: "GAMMALN", 272: "BETAINV", 273: "BINOMDIST", 274: "CHIDIST", 275: "CHIINV", 276: "COMBIN", 277: "CONFIDENCE", 278: "CRITBINOM", 279: "EVEN", 280: "EXPONDIST", 281: "FDIST", 282: "FINV", 283: "FISHER", 284: "FISHERINV", 285: "FLOOR", 286: "GAMMADIST", 287: "GAMMAINV", 288: "CEILING", 289: "HYPGEOMDIST", 290: "LOGNORMDIST", 291: "LOGINV", 292: "NEGBINOMDIST", 293: "NORMDIST", 294: "NORMSDIST", 295: "NORMINV", 296: "NORMSINV", 297: "STANDARDIZE", 298: "ODD", 299: "PERMUT", 300: "POISSON", 301: "TDIST", 302: "WEIBULL", 303: "SUMXMY2", 304: "SUMX2MY2", 305: "SUMX2PY2", 306: "CHITEST", 307: "CORREL", 308: "COVAR", 309: "FORECAST", 310: "FTEST", 311: "INTERCEPT", 312: "PEARSON", 313: "RSQ", 314: "STEYX", 315: "SLOPE", 316: "TTEST", 317: "PROB", 318: "DEVSQ", 319: "GEOMEAN", 320: "HARMEAN", 321: "SUMSQ", 322: "KURT", 323: "SKEW", 324: "ZTEST", 325: "LARGE", 326: "SMALL", 327: "QUARTILE", 328: "PERCENTILE", 329: "PERCENTRANK", 330: "MODE", 331: "TRIMMEAN", 332: "TINV", 334: "MOVIE.COMMAND", 335: "GET.MOVIE", 336: "CONCATENATE", 337: "POWER", 338: "PIVOT.ADD.DATA", 339: "GET.PIVOT.TABLE", 340: "GET.PIVOT.FIELD", 341: "GET.PIVOT.ITEM", 342: "RADIANS", 343: "DEGREES", 344: "SUBTOTAL", 345: "SUMIF", 346: "COUNTIF", 347: "COUNTBLANK", 348: "SCENARIO.GET", 349: "OPTIONS.LISTS.GET", 350: "ISPMT", 351: "DATEDIF", 352: "DATESTRING", 353: "NUMBERSTRING", 354: "ROMAN", 355: "OPEN.DIALOG", 356: "SAVE.DIALOG", 357: "VIEW.GET", 358: "GETPIVOTDATA", 359: "HYPERLINK", 360: "PHONETIC", 361: "AVERAGEA", 362: "MAXA", 363: "MINA", 364: "STDEVPA", 365: "VARPA", 366: "STDEVA", 367: "VARA", 368: "BAHTTEXT", 369: "THAIDAYOFWEEK", 370: "THAIDIGIT", 371: "THAIMONTHOFYEAR", 372: "THAINUMSOUND", 373: "THAINUMSTRING", 374: "THAISTRINGLENGTH", 375: "ISTHAIDIGIT", 376: "ROUNDBAHTDOWN", 377: "ROUNDBAHTUP", 378: "THAIYEAR", 379: "RTD", 380: "CUBEVALUE", 381: "CUBEMEMBER", 382: "CUBEMEMBERPROPERTY", 383: "CUBERANKEDMEMBER", 384: "HEX2BIN", 385: "HEX2DEC", 386: "HEX2OCT", 387: "DEC2BIN", 388: "DEC2HEX", 389: "DEC2OCT", 390: "OCT2BIN", 391: "OCT2HEX", 392: "OCT2DEC", 393: "BIN2DEC", 394: "BIN2OCT", 395: "BIN2HEX", 396: "IMSUB", 397: "IMDIV", 398: "IMPOWER", 399: "IMABS", 400: "IMSQRT", 401: "IMLN", 402: "IMLOG2", 403: "IMLOG10", 404: "IMSIN", 405: "IMCOS", 406: "IMEXP", 407: "IMARGUMENT", 408: "IMCONJUGATE", 409: "IMAGINARY", 410: "IMREAL", 411: "COMPLEX", 412: "IMSUM", 413: "IMPRODUCT", 414: "SERIESSUM", 415: "FACTDOUBLE", 416: "SQRTPI", 417: "QUOTIENT", 418: "DELTA", 419: "GESTEP", 420: "ISEVEN", 421: "ISODD", 422: "MROUND", 423: "ERF", 424: "ERFC", 425: "BESSELJ", 426: "BESSELK", 427: "BESSELY", 428: "BESSELI", 429: "XIRR", 430: "XNPV", 431: "PRICEMAT", 432: "YIELDMAT", 433: "INTRATE", 434: "RECEIVED", 435: "DISC", 436: "PRICEDISC", 437: "YIELDDISC", 438: "TBILLEQ", 439: "TBILLPRICE", 440: "TBILLYIELD", 441: "PRICE", 442: "YIELD", 443: "DOLLARDE", 444: "DOLLARFR", 445: "NOMINAL", 446: "EFFECT", 447: "CUMPRINC", 448: "CUMIPMT", 449: "EDATE", 450: "EOMONTH", 451: "YEARFRAC", 452: "COUPDAYBS", 453: "COUPDAYS", 454: "COUPDAYSNC", 455: "COUPNCD", 456: "COUPNUM", 457: "COUPPCD", 458: "DURATION", 459: "MDURATION", 460: "ODDLPRICE", 461: "ODDLYIELD", 462: "ODDFPRICE", 463: "ODDFYIELD", 464: "RANDBETWEEN", 465: "WEEKNUM", 466: "AMORDEGRC", 467: "AMORLINC", 468: "CONVERT", 724: "SHEETJS", 469: "ACCRINT", 470: "ACCRINTM", 471: "WORKDAY", 472: "NETWORKDAYS", 473: "GCD", 474: "MULTINOMIAL", 475: "LCM", 476: "FVSCHEDULE", 477: "CUBEKPIMEMBER", 478: "CUBESET", 479: "CUBESETCOUNT", 480: "IFERROR", 481: "COUNTIFS", 482: "SUMIFS", 483: "AVERAGEIF", 484: "AVERAGEIFS" },
                    Ci = { 2: 1, 3: 1, 10: 0, 15: 1, 16: 1, 17: 1, 18: 1, 19: 0, 20: 1, 21: 1, 22: 1, 23: 1, 24: 1, 25: 1, 26: 1, 27: 2, 30: 2, 31: 3, 32: 1, 33: 1, 34: 0, 35: 0, 38: 1, 39: 2, 40: 3, 41: 3, 42: 3, 43: 3, 44: 3, 45: 3, 47: 3, 48: 2, 53: 1, 61: 3, 63: 0, 65: 3, 66: 3, 67: 1, 68: 1, 69: 1, 70: 1, 71: 1, 72: 1, 73: 1, 74: 0, 75: 1, 76: 1, 77: 1, 79: 2, 80: 2, 83: 1, 85: 0, 86: 1, 89: 0, 90: 1, 94: 0, 95: 0, 97: 2, 98: 1, 99: 1, 101: 3, 102: 3, 105: 1, 106: 1, 108: 2, 111: 1, 112: 1, 113: 1, 114: 1, 117: 2, 118: 1, 119: 4, 121: 1, 126: 1, 127: 1, 128: 1, 129: 1, 130: 1, 131: 1, 133: 1, 134: 1, 135: 1, 136: 2, 137: 2, 138: 2, 140: 1, 141: 1, 142: 3, 143: 4, 144: 4, 161: 1, 162: 1, 163: 1, 164: 1, 165: 2, 172: 1, 175: 2, 176: 2, 177: 3, 178: 2, 179: 1, 184: 1, 186: 1, 189: 3, 190: 1, 195: 3, 196: 3, 197: 1, 198: 1, 199: 3, 201: 1, 207: 4, 210: 3, 211: 1, 212: 2, 213: 2, 214: 1, 215: 1, 225: 0, 229: 1, 230: 1, 231: 1, 232: 1, 233: 1, 234: 1, 235: 3, 244: 1, 247: 4, 252: 2, 257: 1, 261: 1, 271: 1, 273: 4, 274: 2, 275: 2, 276: 2, 277: 3, 278: 3, 279: 1, 280: 3, 281: 3, 282: 3, 283: 1, 284: 1, 285: 2, 286: 4, 287: 3, 288: 2, 289: 4, 290: 3, 291: 3, 292: 3, 293: 4, 294: 1, 295: 3, 296: 1, 297: 3, 298: 1, 299: 2, 300: 3, 301: 3, 302: 4, 303: 2, 304: 2, 305: 2, 306: 2, 307: 2, 308: 2, 309: 3, 310: 2, 311: 2, 312: 2, 313: 2, 314: 2, 315: 2, 316: 4, 325: 2, 326: 2, 327: 2, 328: 2, 331: 2, 332: 2, 337: 2, 342: 1, 343: 1, 346: 2, 347: 1, 350: 4, 351: 3, 352: 1, 353: 2, 360: 1, 368: 1, 369: 1, 370: 1, 371: 1, 372: 1, 373: 1, 374: 1, 375: 1, 376: 1, 377: 1, 378: 1, 382: 3, 385: 1, 392: 1, 393: 1, 396: 2, 397: 2, 398: 2, 399: 1, 400: 1, 401: 1, 402: 1, 403: 1, 404: 1, 405: 1, 406: 1, 407: 1, 408: 1, 409: 1, 410: 1, 414: 4, 415: 1, 416: 1, 417: 2, 420: 1, 421: 1, 422: 2, 424: 1, 425: 2, 426: 2, 427: 2, 428: 2, 430: 3, 438: 3, 439: 3, 440: 3, 443: 2, 444: 2, 445: 2, 446: 2, 447: 6, 448: 6, 449: 2, 450: 2, 464: 2, 468: 3, 476: 2, 479: 1, 480: 2, 65535: 0 };

                function Ti(e) { return "of:" == e.slice(0, 3) && (e = e.slice(3)), 61 == e.charCodeAt(0) && 61 == (e = e.slice(1)).charCodeAt(0) && (e = e.slice(1)), (e = (e = (e = e.replace(/COM\.MICROSOFT\./g, "")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g, (function(e, t) { return t.replace(/\./g, "") }))).replace(/\[.(#[A-Z]*[?!])\]/g, "$1")).replace(/[;~]/g, ",").replace(/\|/g, ";") }

                function Hi(e) { var t = e.split(":"); return [t[0].split(".")[0], t[0].split(".")[1] + (t.length > 1 ? ":" + (t[1].split(".")[1] || t[1].split(".")[0]) : "")] } var Li = {},
                    Ii = {};

                function ji(e, t) { if (e) { var n = [.7, .7, .75, .75, .3, .3]; "xlml" == t && (n = [1, 1, 1, 1, .5, .5]), null == e.left && (e.left = n[0]), null == e.right && (e.right = n[1]), null == e.top && (e.top = n[2]), null == e.bottom && (e.bottom = n[3]), null == e.header && (e.header = n[4]), null == e.footer && (e.footer = n[5]) } }

                function Vi(e, t, n, r, a, o) { try { r.cellNF && (e.z = N[t]) } catch (l) { if (r.WTF) throw l } if ("z" !== e.t || r.cellStyles) { if ("d" === e.t && "string" === typeof e.v && (e.v = Fe(e.v)), (!r || !1 !== r.cellText) && "z" !== e.t) try { if (null == N[t] && we(xe[t] || "General", t), "e" === e.t) e.w = e.w || vr[e.v];
                            else if (0 === t)
                                if ("n" === e.t)(0 | e.v) === e.v ? e.w = e.v.toString(10) : e.w = X(e.v);
                                else if ("d" === e.t) { var i = He(e.v);
                                e.w = (0 | i) === i ? i.toString(10) : X(i) } else { if (void 0 === e.v) return "";
                                e.w = Q(e.v, Ii) } else "d" === e.t ? e.w = be(t, He(e.v), Ii) : e.w = be(t, e.v, Ii) } catch (l) { if (r.WTF) throw l }
                        if (r.cellStyles && null != n) try { e.s = o.Fills[n], e.s.fgColor && e.s.fgColor.theme && !e.s.fgColor.rgb && (e.s.fgColor.rgb = ao(a.themeElements.clrScheme[e.s.fgColor.theme].rgb, e.s.fgColor.tint || 0), r.WTF && (e.s.fgColor.raw_rgb = a.themeElements.clrScheme[e.s.fgColor.theme].rgb)), e.s.bgColor && e.s.bgColor.theme && (e.s.bgColor.rgb = ao(a.themeElements.clrScheme[e.s.bgColor.theme].rgb, e.s.bgColor.tint || 0), r.WTF && (e.s.bgColor.raw_rgb = a.themeElements.clrScheme[e.s.bgColor.theme].rgb)) } catch (l) { if (r.WTF && o.Fills) throw l } } } var Oi = /<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,
                    Ri = /<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,
                    Pi = /<(?:\w:)?hyperlink [^>]*>/gm,
                    Di = /"(\w*:\w*)"/,
                    Fi = /<(?:\w:)?col\b[^>]*[\/]?>/g,
                    Ni = /<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,
                    _i = /<(?:\w:)?pageMargins[^>]*\/>/g,
                    Bi = /<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,
                    Wi = /<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,
                    Ui = /<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;

                function qi(e, t, n, r, a, o, i) { if (!e) return e;
                    r || (r = { "!id": {} }), null != g && null == t.dense && (t.dense = g); var l = t.dense ? [] : {},
                        s = { s: { r: 2e6, c: 2e6 }, e: { r: 0, c: 0 } },
                        c = "",
                        d = "",
                        u = e.match(Ri);
                    u ? (c = e.slice(0, u.index), d = e.slice(u.index + u[0].length)) : c = d = e; var h = c.match(Bi);
                    h ? Gi(h[0], l, a, n) : (h = c.match(Wi)) && function(e, t, n, r, a) { Gi(e.slice(0, e.indexOf(">")), n, r, a) }(h[0], h[1], l, a, n); var m = (c.match(/<(?:\w*:)?dimension/) || { index: -1 }).index; if (m > 0) { var p = c.slice(m, m + 50).match(Di);
                        p && function(e, t) { var n = Pn(t);
                            n.s.r <= n.e.r && n.s.c <= n.e.c && n.s.r >= 0 && n.s.c >= 0 && (e["!ref"] = Rn(n)) }(l, p[1]) } var f = c.match(Ui);
                    f && f[1] && function(e, t) { t.Views || (t.Views = [{}]);
                        (e.match(Ki) || []).forEach((function(e, n) { var r = dt(e);
                            t.Views[n] || (t.Views[n] = {}), +r.zoomScale && (t.Views[n].zoom = +r.zoomScale), bt(r.rightToLeft) && (t.Views[n].RTL = !0) })) }(f[1], a); var v = []; if (t.cellStyles) { var y = c.match(Fi);
                        y && function(e, t) { for (var n = !1, r = 0; r != t.length; ++r) { var a = dt(t[r], !0);
                                a.hidden && (a.hidden = bt(a.hidden)); var o = parseInt(a.min, 10) - 1,
                                    i = parseInt(a.max, 10) - 1; for (a.outlineLevel && (a.level = +a.outlineLevel || 0), delete a.min, delete a.max, a.width = +a.width, !n && a.width && (n = !0, po(a.width)), fo(a); o <= i;) e[o++] = _e(a) } }(v, y) } u && Zi(u[1], l, t, s, o, i); var b = d.match(Ni);
                    b && (l["!autofilter"] = function(e) { var t = { ref: (e.match(/ref="([^"]*)"/) || [])[1] }; return t }(b[0])); var w = [],
                        z = d.match(Oi); if (z)
                        for (m = 0; m != z.length; ++m) w[m] = Pn(z[m].slice(z[m].indexOf('"') + 1)); var x = d.match(Pi);
                    x && function(e, t, n) { for (var r = Array.isArray(e), a = 0; a != t.length; ++a) { var o = dt(kt(t[a]), !0); if (!o.ref) return; var i = ((n || {})["!id"] || [])[o.id];
                            i ? (o.Target = i.Target, o.location && (o.Target += "#" + pt(o.location))) : (o.Target = "#" + pt(o.location), i = { Target: o.Target, TargetMode: "Internal" }), o.Rel = i, o.tooltip && (o.Tooltip = o.tooltip, delete o.tooltip); for (var l = Pn(o.ref), s = l.s.r; s <= l.e.r; ++s)
                                for (var c = l.s.c; c <= l.e.c; ++c) { var d = Vn({ c: c, r: s });
                                    r ? (e[s] || (e[s] = []), e[s][c] || (e[s][c] = { t: "z", v: void 0 }), e[s][c].l = o) : (e[d] || (e[d] = { t: "z", v: void 0 }), e[d].l = o) } } }(l, x, r); var A = d.match(_i); if (A && (l["!margins"] = function(e) { var t = {}; return ["left", "right", "top", "bottom", "header", "footer"].forEach((function(n) { e[n] && (t[n] = parseFloat(e[n])) })), t }(dt(A[0]))), !l["!ref"] && s.e.c >= s.s.c && s.e.r >= s.s.r && (l["!ref"] = Rn(s)), t.sheetRows > 0 && l["!ref"]) { var k = Pn(l["!ref"]);
                        t.sheetRows <= +k.e.r && (k.e.r = t.sheetRows - 1, k.e.r > s.e.r && (k.e.r = s.e.r), k.e.r < k.s.r && (k.s.r = k.e.r), k.e.c > s.e.c && (k.e.c = s.e.c), k.e.c < k.s.c && (k.s.c = k.e.c), l["!fullref"] = l["!ref"], l["!ref"] = Rn(k)) } return v.length > 0 && (l["!cols"] = v), w.length > 0 && (l["!merges"] = w), l }

                function Gi(e, t, n, r) { var a = dt(e);
                    n.Sheets[r] || (n.Sheets[r] = {}), a.codeName && (n.Sheets[r].CodeName = pt(kt(a.codeName))) } var Ki = /<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/; var Zi = function() { var e = /<(?:\w+:)?c[ \/>]/,
                        t = /<\/(?:\w+:)?row>/,
                        n = /r=["']([^"']*)["']/,
                        r = /<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,
                        a = /ref=["']([^"']*)["']/,
                        o = Mt("v"),
                        i = Mt("f"); return function(l, s, c, d, u, h) { for (var m, p, f, v, g, y = 0, b = "", w = [], z = [], x = 0, A = 0, k = 0, S = "", M = 0, E = 0, C = 0, T = 0, H = Array.isArray(h.CellXf), L = [], I = [], j = Array.isArray(s), V = [], O = {}, R = !1, P = !!c.sheetStubs, D = l.split(t), F = 0, _ = D.length; F != _; ++F) { var B = (b = D[F].trim()).length; if (0 !== B) { var W = 0;
                                e: for (y = 0; y < B; ++y) switch (b[y]) {
                                    case ">":
                                        if ("/" != b[y - 1]) {++y; break e } if (c && c.cellStyles) { if (M = null != (p = dt(b.slice(W, y), !0)).r ? parseInt(p.r, 10) : M + 1, E = -1, c.sheetRows && c.sheetRows < M) continue;
                                            O = {}, R = !1, p.ht && (R = !0, O.hpt = parseFloat(p.ht), O.hpx = yo(O.hpt)), "1" == p.hidden && (R = !0, O.hidden = !0), null != p.outlineLevel && (R = !0, O.level = +p.outlineLevel), R && (V[M - 1] = O) } break;
                                    case "<":
                                        W = y }
                                if (W >= y) break; if (M = null != (p = dt(b.slice(W, y), !0)).r ? parseInt(p.r, 10) : M + 1, E = -1, !(c.sheetRows && c.sheetRows < M)) { d.s.r > M - 1 && (d.s.r = M - 1), d.e.r < M - 1 && (d.e.r = M - 1), c && c.cellStyles && (O = {}, R = !1, p.ht && (R = !0, O.hpt = parseFloat(p.ht), O.hpx = yo(O.hpt)), "1" == p.hidden && (R = !0, O.hidden = !0), null != p.outlineLevel && (R = !0, O.level = +p.outlineLevel), R && (V[M - 1] = O)), w = b.slice(y).split(e); for (var U = 0; U != w.length && "<" == w[U].trim().charAt(0); ++U); for (w = w.slice(U), y = 0; y != w.length; ++y)
                                        if (0 !== (b = w[y].trim()).length) { if (z = b.match(n), x = y, A = 0, k = 0, b = "<c " + ("<" == b.slice(0, 1) ? ">" : "") + b, null != z && 2 === z.length) { for (x = 0, S = z[1], A = 0; A != S.length && !((k = S.charCodeAt(A) - 64) < 1 || k > 26); ++A) x = 26 * x + k;
                                                E = --x } else ++E; for (A = 0; A != b.length && 62 !== b.charCodeAt(A); ++A); if (++A, (p = dt(b.slice(0, A), !0)).r || (p.r = Vn({ r: M - 1, c: E })), m = { t: "" }, null != (z = (S = b.slice(A)).match(o)) && "" !== z[1] && (m.v = pt(z[1])), c.cellFormula) { if (null != (z = S.match(i)) && "" !== z[1]) { if (m.f = pt(kt(z[1])).replace(/\r\n/g, "\n"), c.xlfn || (m.f = qo(m.f)), z[0].indexOf('t="array"') > -1) m.F = (S.match(a) || [])[1], m.F.indexOf(":") > -1 && L.push([Pn(m.F), m.F]);
                                                    else if (z[0].indexOf('t="shared"') > -1) { v = dt(z[0]); var q = pt(kt(z[1]));
                                                        c.xlfn || (q = qo(q)), I[parseInt(v.si, 10)] = [v, q, p.r] } } else(z = S.match(/<f[^>]*\/>/)) && I[(v = dt(z[0])).si] && (m.f = Uo(I[v.si][1], I[v.si][2], p.r)); var G = jn(p.r); for (A = 0; A < L.length; ++A) G.r >= L[A][0].s.r && G.r <= L[A][0].e.r && G.c >= L[A][0].s.c && G.c <= L[A][0].e.c && (m.F = L[A][1]) } if (null == p.t && void 0 === m.v)
                                                if (m.f || m.F) m.v = 0, m.t = "n";
                                                else { if (!P) continue;
                                                    m.t = "z" } else m.t = p.t || "n"; switch (d.s.c > E && (d.s.c = E), d.e.c < E && (d.e.c = E), m.t) {
                                                case "n":
                                                    if ("" == m.v || null == m.v) { if (!P) continue;
                                                        m.t = "z" } else m.v = parseFloat(m.v); break;
                                                case "s":
                                                    if ("undefined" == typeof m.v) { if (!P) continue;
                                                        m.t = "z" } else f = Li[parseInt(m.v, 10)], m.v = f.t, m.r = f.r, c.cellHTML && (m.h = f.h); break;
                                                case "str":
                                                    m.t = "s", m.v = null != m.v ? kt(m.v) : "", c.cellHTML && (m.h = gt(m.v)); break;
                                                case "inlineStr":
                                                    z = S.match(r), m.t = "s", null != z && (f = Fa(z[1])) ? (m.v = f.t, c.cellHTML && (m.h = f.h)) : m.v = ""; break;
                                                case "b":
                                                    m.v = bt(m.v); break;
                                                case "d":
                                                    c.cellDates ? m.v = Fe(m.v, 1) : (m.v = He(Fe(m.v, 1)), m.t = "n"); break;
                                                case "e":
                                                    c && !1 === c.cellText || (m.w = m.v), m.v = gr[m.v] } if (C = T = 0, g = null, H && void 0 !== p.s && null != (g = h.CellXf[p.s]) && (null != g.numFmtId && (C = g.numFmtId), c.cellStyles && null != g.fillId && (T = g.fillId)), Vi(m, C, T, c, u, h), c.cellDates && H && "n" == m.t && fe(N[C]) && (m.t = "d", m.v = Ve(m.v)), p.cm && c.xlmeta) { var K = (c.xlmeta.Cell || [])[+p.cm - 1];
                                                K && "XLDAPR" == K.type && (m.D = !0) } if (j) { var Z = jn(p.r);
                                                s[Z.r] || (s[Z.r] = []), s[Z.r][Z.c] = m } else s[p.r] = m } } } } V.length > 0 && (s["!rows"] = V) } }(); var Yi = tr;

                function Xi(e) { return [Zn(e), nr(e), "n"] } var $i = tr; var Qi = ["left", "right", "top", "bottom", "header", "footer"];

                function Ji(e, t, n, r, a, o) { var i = o || { "!type": "chart" }; if (!e) return o; var l = 0,
                        s = 0,
                        c = "A",
                        d = { s: { r: 2e6, c: 2e6 }, e: { r: 0, c: 0 } }; return (e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm) || []).forEach((function(e) { var t = function(e) { var t, n = [],
                                r = e.match(/^<c:numCache>/);
                            (e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm) || []).forEach((function(e) { var t = e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);
                                t && (n[+t[1]] = r ? +t[2] : t[2]) })); var a = pt((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/) || ["", "General"])[1]); return (e.match(/<c:f>(.*?)<\/c:f>/gm) || []).forEach((function(e) { t = e.replace(/<.*?>/g, "") })), [n, a, t] }(e);
                        d.s.r = d.s.c = 0, d.e.c = l, c = In(l), t[0].forEach((function(e, n) { i[c + Hn(n)] = { t: "n", v: e, z: t[1] }, s = n })), d.e.r < s && (d.e.r = s), ++l })), l > 0 && (i["!ref"] = Rn(d)), i } var el = [
                        ["allowRefreshQuery", !1, "bool"],
                        ["autoCompressPictures", !0, "bool"],
                        ["backupFile", !1, "bool"],
                        ["checkCompatibility", !1, "bool"],
                        ["CodeName", ""],
                        ["date1904", !1, "bool"],
                        ["defaultThemeVersion", 0, "int"],
                        ["filterPrivacy", !1, "bool"],
                        ["hidePivotFieldList", !1, "bool"],
                        ["promptedSolutions", !1, "bool"],
                        ["publishItems", !1, "bool"],
                        ["refreshAllConnections", !1, "bool"],
                        ["saveExternalLinkValues", !0, "bool"],
                        ["showBorderUnselectedTables", !0, "bool"],
                        ["showInkAnnotation", !0, "bool"],
                        ["showObjects", "all"],
                        ["showPivotChartFilter", !1, "bool"],
                        ["updateLinks", "userSet"]
                    ],
                    tl = [
                        ["activeTab", 0, "int"],
                        ["autoFilterDateGrouping", !0, "bool"],
                        ["firstSheet", 0, "int"],
                        ["minimized", !1, "bool"],
                        ["showHorizontalScroll", !0, "bool"],
                        ["showSheetTabs", !0, "bool"],
                        ["showVerticalScroll", !0, "bool"],
                        ["tabRatio", 600, "int"],
                        ["visibility", "visible"]
                    ],
                    nl = [],
                    rl = [
                        ["calcCompleted", "true"],
                        ["calcMode", "auto"],
                        ["calcOnSave", "true"],
                        ["concurrentCalc", "true"],
                        ["fullCalcOnLoad", "false"],
                        ["fullPrecision", "true"],
                        ["iterate", "false"],
                        ["iterateCount", "100"],
                        ["iterateDelta", "0.001"],
                        ["refMode", "A1"]
                    ];

                function al(e, t) { for (var n = 0; n != e.length; ++n)
                        for (var r = e[n], a = 0; a != t.length; ++a) { var o = t[a]; if (null == r[o[0]]) r[o[0]] = o[1];
                            else switch (o[2]) {
                                case "bool":
                                    "string" == typeof r[o[0]] && (r[o[0]] = bt(r[o[0]])); break;
                                case "int":
                                    "string" == typeof r[o[0]] && (r[o[0]] = parseInt(r[o[0]], 10)) } } }

                function ol(e, t) { for (var n = 0; n != t.length; ++n) { var r = t[n]; if (null == e[r[0]]) e[r[0]] = r[1];
                        else switch (r[2]) {
                            case "bool":
                                "string" == typeof e[r[0]] && (e[r[0]] = bt(e[r[0]])); break;
                            case "int":
                                "string" == typeof e[r[0]] && (e[r[0]] = parseInt(e[r[0]], 10)) } } }

                function il(e) { ol(e.WBProps, el), ol(e.CalcPr, rl), al(e.WBView, tl), al(e.Sheets, nl), Ii.date1904 = bt(e.WBProps.date1904) } var ll = "][*?/\\".split("");

                function sl(e, t) { if (e.length > 31) { if (t) return !1; throw new Error("Sheet names cannot exceed 31 chars") } var n = !0; return ll.forEach((function(r) { if (-1 != e.indexOf(r)) { if (!t) throw new Error("Sheet name cannot contain : \\ / ? * [ ]");
                            n = !1 } })), n } var cl = /<\w+:workbook/;

                function dl(e, t) { var n = {}; return e.read_shift(4), n.ArchID = e.read_shift(4), e.l += t - 8, n }

                function ul(e, t, n) { return ".bin" === t.slice(-4) ? function(e, t) { var n = { AppVersion: {}, WBProps: {}, WBView: [], Sheets: [], CalcPr: {}, xmlns: "" },
                            r = [],
                            a = !1;
                        t || (t = {}), t.biff = 12; var o = [],
                            i = [
                                []
                            ]; return i.SheetNames = [], i.XTI = [], Nl[16] = { n: "BrtFRTArchID$", f: dl }, An(e, (function(e, l, s) { switch (s) {
                                case 156:
                                    i.SheetNames.push(e.name), n.Sheets.push(e); break;
                                case 153:
                                    n.WBProps = e; break;
                                case 39:
                                    null != e.Sheet && (t.SID = e.Sheet), e.Ref = gi(e.Ptg, 0, null, i, t), delete t.SID, delete e.Ptg, o.push(e); break;
                                case 1036:
                                case 361:
                                case 2071:
                                case 158:
                                case 143:
                                case 664:
                                case 353:
                                case 3072:
                                case 3073:
                                case 534:
                                case 677:
                                case 157:
                                case 610:
                                case 2050:
                                case 155:
                                case 548:
                                case 676:
                                case 128:
                                case 665:
                                case 2128:
                                case 2125:
                                case 549:
                                case 2053:
                                case 596:
                                case 2076:
                                case 2075:
                                case 2082:
                                case 397:
                                case 154:
                                case 1117:
                                case 553:
                                case 2091:
                                case 16:
                                    break;
                                case 357:
                                case 358:
                                case 355:
                                case 667:
                                    i[0].length ? i.push([s, e]) : i[0] = [s, e], i[i.length - 1].XTI = []; break;
                                case 362:
                                    0 === i.length && (i[0] = [], i[0].XTI = []), i[i.length - 1].XTI = i[i.length - 1].XTI.concat(e), i.XTI = i.XTI.concat(e); break;
                                case 35:
                                case 37:
                                    r.push(s), a = !0; break;
                                case 36:
                                case 38:
                                    r.pop(), a = !1; break;
                                default:
                                    if (l.T);
                                    else if (!a || t.WTF && 37 != r[r.length - 1] && 35 != r[r.length - 1]) throw new Error("Unexpected record 0x" + s.toString(16)) } }), t), il(n), n.Names = o, n.supbooks = i, n }(e, n) : function(e, t) { if (!e) throw new Error("Could not find file"); var n = { AppVersion: {}, WBProps: {}, WBView: [], Sheets: [], CalcPr: {}, Names: [], xmlns: "" },
                            r = !1,
                            a = "xmlns",
                            o = {},
                            i = 0; if (e.replace(lt, (function(l, s) { var c = dt(l); switch (ut(c[0])) {
                                    case "<?xml":
                                    case "</workbook>":
                                    case "<fileVersion/>":
                                    case "</fileVersion>":
                                    case "<fileSharing":
                                    case "<fileSharing/>":
                                    case "</workbookPr>":
                                    case "<workbookProtection":
                                    case "<workbookProtection/>":
                                    case "<bookViews":
                                    case "<bookViews>":
                                    case "</bookViews>":
                                    case "</workbookView>":
                                    case "<sheets":
                                    case "<sheets>":
                                    case "</sheets>":
                                    case "</sheet>":
                                    case "<functionGroups":
                                    case "<functionGroups/>":
                                    case "<functionGroup":
                                    case "<externalReferences":
                                    case "</externalReferences>":
                                    case "<externalReferences>":
                                    case "<externalReference":
                                    case "<definedNames/>":
                                    case "<definedName/>":
                                    case "</calcPr>":
                                    case "<oleSize":
                                    case "<customWorkbookViews>":
                                    case "</customWorkbookViews>":
                                    case "<customWorkbookViews":
                                    case "<customWorkbookView":
                                    case "</customWorkbookView>":
                                    case "<pivotCaches>":
                                    case "</pivotCaches>":
                                    case "<pivotCaches":
                                    case "<pivotCache":
                                    case "<smartTagPr":
                                    case "<smartTagPr/>":
                                    case "<smartTagTypes":
                                    case "<smartTagTypes>":
                                    case "</smartTagTypes>":
                                    case "<smartTagType":
                                    case "<webPublishing":
                                    case "<webPublishing/>":
                                    case "<fileRecoveryPr":
                                    case "<fileRecoveryPr/>":
                                    case "<webPublishObjects>":
                                    case "<webPublishObjects":
                                    case "</webPublishObjects>":
                                    case "<webPublishObject":
                                    case "<extLst":
                                    case "<extLst>":
                                    case "</extLst>":
                                    case "<extLst/>":
                                    case "<ArchID":
                                    case "<revisionPtr":
                                        break;
                                    case "<workbook":
                                        l.match(cl) && (a = "xmlns" + l.match(/<(\w+):/)[1]), n.xmlns = c[a]; break;
                                    case "<fileVersion":
                                        delete c[0], n.AppVersion = c; break;
                                    case "<workbookPr":
                                    case "<workbookPr/>":
                                        el.forEach((function(e) { if (null != c[e[0]]) switch (e[2]) {
                                                case "bool":
                                                    n.WBProps[e[0]] = bt(c[e[0]]); break;
                                                case "int":
                                                    n.WBProps[e[0]] = parseInt(c[e[0]], 10); break;
                                                default:
                                                    n.WBProps[e[0]] = c[e[0]] } })), c.codeName && (n.WBProps.CodeName = kt(c.codeName)); break;
                                    case "<workbookView":
                                    case "<workbookView/>":
                                        delete c[0], n.WBView.push(c); break;
                                    case "<sheet":
                                        switch (c.state) {
                                            case "hidden":
                                                c.Hidden = 1; break;
                                            case "veryHidden":
                                                c.Hidden = 2; break;
                                            default:
                                                c.Hidden = 0 } delete c.state, c.name = pt(kt(c.name)), delete c[0], n.Sheets.push(c); break;
                                    case "<definedNames>":
                                    case "<definedNames":
                                    case "<ext":
                                    case "<AlternateContent":
                                    case "<AlternateContent>":
                                        r = !0; break;
                                    case "</definedNames>":
                                    case "</ext>":
                                    case "</AlternateContent>":
                                        r = !1; break;
                                    case "<definedName":
                                        (o = {}).Name = kt(c.name), c.comment && (o.Comment = c.comment), c.localSheetId && (o.Sheet = +c.localSheetId), bt(c.hidden || "0") && (o.Hidden = !0), i = s + l.length; break;
                                    case "</definedName>":
                                        o.Ref = pt(kt(e.slice(i, s))), n.Names.push(o); break;
                                    case "<calcPr":
                                    case "<calcPr/>":
                                        delete c[0], n.CalcPr = c; break;
                                    default:
                                        if (!r && t.WTF) throw new Error("unrecognized " + c[0] + " in workbook") } return l })), -1 === Dt.indexOf(n.xmlns)) throw new Error("Unknown Namespace: " + n.xmlns); return il(n), n }(e, n) }

                function hl(e, t, n, r, a, o, i, l) { return ".bin" === t.slice(-4) ? function(e, t, n, r, a, o, i) { if (!e) return e; var l = t || {};
                        r || (r = { "!id": {} }), null != g && null == l.dense && (l.dense = g); var s, c, d, u, h, m, p, f, v, y, b = l.dense ? [] : {},
                            w = { s: { r: 2e6, c: 2e6 }, e: { r: 0, c: 0 } },
                            z = [],
                            x = !1,
                            A = !1,
                            k = [];
                        l.biff = 12, l["!row"] = 0; var S = 0,
                            M = !1,
                            E = [],
                            C = {},
                            T = l.supbooks || a.supbooks || [
                                []
                            ]; if (T.sharedf = C, T.arrayf = E, T.SheetNames = a.SheetNames || a.Sheets.map((function(e) { return e.name })), !l.supbooks && (l.supbooks = T, a.Names))
                            for (var H = 0; H < a.Names.length; ++H) T[0][H + 1] = a.Names[H]; var L, I, j = [],
                            V = [],
                            O = !1; if (Nl[16] = { n: "BrtShortReal", f: Xi }, An(e, (function(e, t, g) { if (!A) switch (g) {
                                    case 148:
                                        s = e; break;
                                    case 0:
                                        c = e, l.sheetRows && l.sheetRows <= c.r && (A = !0), v = Hn(h = c.r), l["!row"] = c.r, (e.hidden || e.hpt || null != e.level) && (e.hpt && (e.hpx = yo(e.hpt)), V[e.r] = e); break;
                                    case 2:
                                    case 3:
                                    case 4:
                                    case 5:
                                    case 6:
                                    case 7:
                                    case 8:
                                    case 9:
                                    case 10:
                                    case 11:
                                    case 13:
                                    case 14:
                                    case 15:
                                    case 16:
                                    case 17:
                                    case 18:
                                    case 62:
                                        switch (d = { t: e[2] }, e[2]) {
                                            case "n":
                                                d.v = e[1]; break;
                                            case "s":
                                                f = Li[e[1]], d.v = f.t, d.r = f.r; break;
                                            case "b":
                                                d.v = !!e[1]; break;
                                            case "e":
                                                d.v = e[1], !1 !== l.cellText && (d.w = vr[d.v]); break;
                                            case "str":
                                                d.t = "s", d.v = e[1]; break;
                                            case "is":
                                                d.t = "s", d.v = e[1].t } if ((u = i.CellXf[e[0].iStyleRef]) && Vi(d, u.numFmtId, null, l, o, i), m = -1 == e[0].c ? m + 1 : e[0].c, l.dense ? (b[h] || (b[h] = []), b[h][m] = d) : b[In(m) + v] = d, l.cellFormula) { for (M = !1, S = 0; S < E.length; ++S) { var H = E[S];
                                                c.r >= H[0].s.r && c.r <= H[0].e.r && m >= H[0].s.c && m <= H[0].e.c && (d.F = Rn(H[0]), M = !0) }!M && e.length > 3 && (d.f = e[3]) } if (w.s.r > c.r && (w.s.r = c.r), w.s.c > m && (w.s.c = m), w.e.r < c.r && (w.e.r = c.r), w.e.c < m && (w.e.c = m), l.cellDates && u && "n" == d.t && fe(N[u.numFmtId])) { var R = U(d.v);
                                            R && (d.t = "d", d.v = new Date(R.y, R.m - 1, R.d, R.H, R.M, R.S, R.u)) } L && ("XLDAPR" == L.type && (d.D = !0), L = void 0), I && (I = void 0); break;
                                    case 1:
                                    case 12:
                                        if (!l.sheetStubs || x) break;
                                        d = { t: "z", v: void 0 }, m = -1 == e[0].c ? m + 1 : e[0].c, l.dense ? (b[h] || (b[h] = []), b[h][m] = d) : b[In(m) + v] = d, w.s.r > c.r && (w.s.r = c.r), w.s.c > m && (w.s.c = m), w.e.r < c.r && (w.e.r = c.r), w.e.c < m && (w.e.c = m), L && ("XLDAPR" == L.type && (d.D = !0), L = void 0), I && (I = void 0); break;
                                    case 176:
                                        k.push(e); break;
                                    case 49:
                                        L = ((l.xlmeta || {}).Cell || [])[e - 1]; break;
                                    case 494:
                                        var P = r["!id"][e.relId]; for (P ? (e.Target = P.Target, e.loc && (e.Target += "#" + e.loc), e.Rel = P) : "" == e.relId && (e.Target = "#" + e.loc), h = e.rfx.s.r; h <= e.rfx.e.r; ++h)
                                            for (m = e.rfx.s.c; m <= e.rfx.e.c; ++m) l.dense ? (b[h] || (b[h] = []), b[h][m] || (b[h][m] = { t: "z", v: void 0 }), b[h][m].l = e) : (p = Vn({ c: m, r: h }), b[p] || (b[p] = { t: "z", v: void 0 }), b[p].l = e); break;
                                    case 426:
                                        if (!l.cellFormula) break;
                                        E.push(e), (y = l.dense ? b[h][m] : b[In(m) + v]).f = gi(e[1], 0, { r: c.r, c: m }, T, l), y.F = Rn(e[0]); break;
                                    case 427:
                                        if (!l.cellFormula) break;
                                        C[Vn(e[0].s)] = e[1], (y = l.dense ? b[h][m] : b[In(m) + v]).f = gi(e[1], 0, { r: c.r, c: m }, T, l); break;
                                    case 60:
                                        if (!l.cellStyles) break; for (; e.e >= e.s;) j[e.e--] = { width: e.w / 256, hidden: !!(1 & e.flags), level: e.level }, O || (O = !0, po(e.w / 256)), fo(j[e.e + 1]); break;
                                    case 161:
                                        b["!autofilter"] = { ref: Rn(e) }; break;
                                    case 476:
                                        b["!margins"] = e; break;
                                    case 147:
                                        a.Sheets[n] || (a.Sheets[n] = {}), e.name && (a.Sheets[n].CodeName = e.name), (e.above || e.left) && (b["!outline"] = { above: e.above, left: e.left }); break;
                                    case 137:
                                        a.Views || (a.Views = [{}]), a.Views[0] || (a.Views[0] = {}), e.RTL && (a.Views[0].RTL = !0); break;
                                    case 485:
                                    case 64:
                                    case 1053:
                                    case 151:
                                    case 152:
                                    case 175:
                                    case 644:
                                    case 625:
                                    case 562:
                                    case 396:
                                    case 1112:
                                    case 1146:
                                    case 471:
                                    case 1050:
                                    case 649:
                                    case 1105:
                                    case 589:
                                    case 607:
                                    case 564:
                                    case 1055:
                                    case 168:
                                    case 174:
                                    case 1180:
                                    case 499:
                                    case 507:
                                    case 550:
                                    case 171:
                                    case 167:
                                    case 1177:
                                    case 169:
                                    case 1181:
                                    case 551:
                                    case 552:
                                    case 661:
                                    case 639:
                                    case 478:
                                    case 537:
                                    case 477:
                                    case 536:
                                    case 1103:
                                    case 680:
                                    case 1104:
                                    case 1024:
                                    case 663:
                                    case 535:
                                    case 678:
                                    case 504:
                                    case 1043:
                                    case 428:
                                    case 170:
                                    case 3072:
                                    case 50:
                                    case 2070:
                                    case 1045:
                                        break;
                                    case 35:
                                        x = !0; break;
                                    case 36:
                                        x = !1; break;
                                    case 37:
                                        z.push(g), x = !0; break;
                                    case 38:
                                        z.pop(), x = !1; break;
                                    default:
                                        if (t.T);
                                        else if (!x || l.WTF) throw new Error("Unexpected record 0x" + g.toString(16)) } }), l), delete l.supbooks, delete l["!row"], !b["!ref"] && (w.s.r < 2e6 || s && (s.e.r > 0 || s.e.c > 0 || s.s.r > 0 || s.s.c > 0)) && (b["!ref"] = Rn(s || w)), l.sheetRows && b["!ref"]) { var R = Pn(b["!ref"]);
                            l.sheetRows <= +R.e.r && (R.e.r = l.sheetRows - 1, R.e.r > w.e.r && (R.e.r = w.e.r), R.e.r < R.s.r && (R.s.r = R.e.r), R.e.c > w.e.c && (R.e.c = w.e.c), R.e.c < R.s.c && (R.s.c = R.e.c), b["!fullref"] = b["!ref"], b["!ref"] = Rn(R)) } return k.length > 0 && (b["!merges"] = k), j.length > 0 && (b["!cols"] = j), V.length > 0 && (b["!rows"] = V), b }(e, r, n, a, o, i, l) : qi(e, r, n, a, o, i, l) }

                function ml(e, t, n, r, a, o, i, l) { return ".bin" === t.slice(-4) ? function(e, t, n, r, a) { if (!e) return e;
                        r || (r = { "!id": {} }); var o = { "!type": "chart", "!drawel": null, "!rel": "" },
                            i = [],
                            l = !1; return An(e, (function(e, r, s) { switch (s) {
                                case 550:
                                    o["!rel"] = e; break;
                                case 651:
                                    a.Sheets[n] || (a.Sheets[n] = {}), e.name && (a.Sheets[n].CodeName = e.name); break;
                                case 562:
                                case 652:
                                case 669:
                                case 679:
                                case 551:
                                case 552:
                                case 476:
                                case 3072:
                                    break;
                                case 35:
                                    l = !0; break;
                                case 36:
                                    l = !1; break;
                                case 37:
                                    i.push(s); break;
                                case 38:
                                    i.pop(); break;
                                default:
                                    if (r.T > 0) i.push(s);
                                    else if (r.T < 0) i.pop();
                                    else if (!l || t.WTF) throw new Error("Unexpected record 0x" + s.toString(16)) } }), t), r["!id"][o["!rel"]] && (o["!drawel"] = r["!id"][o["!rel"]]), o }(e, r, n, a, o) : function(e, t, n, r, a) { if (!e) return e;
                        r || (r = { "!id": {} }); var o, i = { "!type": "chart", "!drawel": null, "!rel": "" },
                            l = e.match(Bi); return l && Gi(l[0], 0, a, n), (o = e.match(/drawing r:id="(.*?)"/)) && (i["!rel"] = o[1]), r["!id"][i["!rel"]] && (i["!drawel"] = r["!id"][i["!rel"]]), i }(e, 0, n, a, o) }

                function pl(e, t, n, r) { return ".bin" === t.slice(-4) ? function(e, t, n) { var r = { NumberFmt: [] }; for (var a in N) r.NumberFmt[a] = N[a];
                        r.CellXf = [], r.Fonts = []; var o = [],
                            i = !1; return An(e, (function(e, a, l) { switch (l) {
                                case 44:
                                    r.NumberFmt[e[0]] = e[1], we(e[1], e[0]); break;
                                case 43:
                                    r.Fonts.push(e), null != e.color.theme && t && t.themeElements && t.themeElements.clrScheme && (e.color.rgb = ao(t.themeElements.clrScheme[e.color.theme].rgb, e.color.tint || 0)); break;
                                case 1025:
                                case 45:
                                case 46:
                                case 48:
                                case 507:
                                case 572:
                                case 475:
                                case 1171:
                                case 2102:
                                case 1130:
                                case 512:
                                case 2095:
                                case 3072:
                                    break;
                                case 47:
                                    617 == o[o.length - 1] && r.CellXf.push(e); break;
                                case 35:
                                    i = !0; break;
                                case 36:
                                    i = !1; break;
                                case 37:
                                    o.push(l), i = !0; break;
                                case 38:
                                    o.pop(), i = !1; break;
                                default:
                                    if (a.T > 0) o.push(l);
                                    else if (a.T < 0) o.pop();
                                    else if (!i || n.WTF && 37 != o[o.length - 1]) throw new Error("Unexpected record 0x" + l.toString(16)) } })), r }(e, n, r) : xo(e, n, r) }

                function fl(e, t, n) { return ".bin" === t.slice(-4) ? function(e, t) { var n = [],
                            r = !1; return An(e, (function(e, a, o) { switch (o) {
                                case 159:
                                    n.Count = e[0], n.Unique = e[1]; break;
                                case 19:
                                    n.push(e); break;
                                case 160:
                                    return !0;
                                case 35:
                                    r = !0; break;
                                case 36:
                                    r = !1; break;
                                default:
                                    if (a.T, !r || t.WTF) throw new Error("Unexpected record 0x" + o.toString(16)) } })), n }(e, n) : function(e, t) { var n = [],
                            r = ""; if (!e) return n; var a = e.match(Na); if (a) { r = a[2].replace(_a, "").split(Ba); for (var o = 0; o != r.length; ++o) { var i = Fa(r[o].trim(), t);
                                null != i && (n[n.length] = i) } a = dt(a[1]), n.Count = a.count, n.Unique = a.uniqueCount } return n }(e, n) }

                function vl(e, t, n) { return ".bin" === t.slice(-4) ? function(e, t) { var n = [],
                            r = [],
                            a = {},
                            o = !1; return An(e, (function(e, i, l) { switch (l) {
                                case 632:
                                    r.push(e); break;
                                case 635:
                                    a = e; break;
                                case 637:
                                    a.t = e.t, a.h = e.h, a.r = e.r; break;
                                case 636:
                                    if (a.author = r[a.iauthor], delete a.iauthor, t.sheetRows && a.rfx && t.sheetRows <= a.rfx.r) break;
                                    a.t || (a.t = ""), delete a.rfx, n.push(a); break;
                                case 3072:
                                case 37:
                                case 38:
                                    break;
                                case 35:
                                    o = !0; break;
                                case 36:
                                    o = !1; break;
                                default:
                                    if (i.T);
                                    else if (!o || t.WTF) throw new Error("Unexpected record 0x" + l.toString(16)) } })), n }(e, n) : function(e, t) { if (e.match(/<(?:\w+:)?comments *\/>/)) return []; var n = [],
                            r = [],
                            a = e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);
                        a && a[1] && a[1].split(/<\/\w*:?author>/).forEach((function(e) { if ("" !== e && "" !== e.trim()) { var t = e.match(/<(?:\w+:)?author[^>]*>(.*)/);
                                t && n.push(t[1]) } })); var o = e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/); return o && o[1] && o[1].split(/<\/\w*:?comment>/).forEach((function(e) { if ("" !== e && "" !== e.trim()) { var a = e.match(/<(?:\w+:)?comment[^>]*>/); if (a) { var o = dt(a[0]),
                                        i = { author: o.authorId && n[o.authorId] || "sheetjsghost", ref: o.ref, guid: o.guid },
                                        l = jn(o.ref); if (!(t.sheetRows && t.sheetRows <= l.r)) { var s = e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),
                                            c = !!s && !!s[1] && Fa(s[1]) || { r: "", t: "", h: "" };
                                        i.r = c.r, "<t></t>" == c.r && (c.t = c.h = ""), i.t = (c.t || "").replace(/\r\n/g, "\n").replace(/\r/g, "\n"), t.cellHTML && (i.h = c.h), r.push(i) } } } })), r }(e, n) }

                function gl(e, t, n) { return ".bin" === t.slice(-4) ? function(e, t, n) { var r = []; return An(e, (function(e, t, n) { if (63 === n) r.push(e);
                            else if (!t.T) throw new Error("Unexpected record 0x" + n.toString(16)) })), r }(e) : function(e) { var t = []; if (!e) return t; var n = 1; return (e.match(lt) || []).forEach((function(e) { var r = dt(e); switch (r[0]) {
                                case "<?xml":
                                case "<calcChain":
                                case "<calcChain>":
                                case "</calcChain>":
                                    break;
                                case "<c":
                                    delete r[0], r.i ? n = r.i : r.i = n, t.push(r) } })), t }(e) }

                function yl(e, t, n, r) { if (".bin" === n.slice(-4)) return function(e, t, n, r) { if (!e) return e; var a = r || {},
                            o = !1;
                        An(e, (function(e, t, n) { switch (n) {
                                case 359:
                                case 363:
                                case 364:
                                case 366:
                                case 367:
                                case 368:
                                case 369:
                                case 370:
                                case 371:
                                case 472:
                                case 577:
                                case 578:
                                case 579:
                                case 580:
                                case 581:
                                case 582:
                                case 583:
                                case 584:
                                case 585:
                                case 586:
                                case 587:
                                    break;
                                case 35:
                                    o = !0; break;
                                case 36:
                                    o = !1; break;
                                default:
                                    if (t.T);
                                    else if (!o || a.WTF) throw new Error("Unexpected record 0x" + n.toString(16)) } }), a) }(e, 0, 0, r) }

                function bl(e, t, n) { return ".bin" === t.slice(-4) ? function(e, t, n) { var r = { Types: [], Cell: [], Value: [] },
                            a = n || {},
                            o = [],
                            i = !1,
                            l = 2; return An(e, (function(e, t, n) { switch (n) {
                                case 335:
                                    r.Types.push({ name: e.name }); break;
                                case 51:
                                    e.forEach((function(e) { 1 == l ? r.Cell.push({ type: r.Types[e[0] - 1].name, index: e[1] }) : 0 == l && r.Value.push({ type: r.Types[e[0] - 1].name, index: e[1] }) })); break;
                                case 337:
                                    l = e ? 1 : 0; break;
                                case 338:
                                    l = 2; break;
                                case 35:
                                    o.push(n), i = !0; break;
                                case 36:
                                    o.pop(), i = !1; break;
                                default:
                                    if (t.T);
                                    else if (!i || a.WTF && 35 != o[o.length - 1]) throw new Error("Unexpected record 0x" + n.toString(16)) } })), r }(e, 0, n) : function(e, t, n) { var r = { Types: [], Cell: [], Value: [] }; if (!e) return r; var a, o = !1,
                            i = 2; return e.replace(lt, (function(e) { var t = dt(e); switch (ut(t[0])) {
                                case "<?xml":
                                case "<metadata":
                                case "</metadata>":
                                case "<metadataTypes":
                                case "</metadataTypes>":
                                case "</metadataType>":
                                case "</futureMetadata>":
                                case "<bk>":
                                case "</bk>":
                                case "</rc>":
                                case "<extLst":
                                case "<extLst>":
                                case "</extLst>":
                                case "<extLst/>":
                                    break;
                                case "<metadataType":
                                    r.Types.push({ name: t.name }); break;
                                case "<futureMetadata":
                                    for (var l = 0; l < r.Types.length; ++l) r.Types[l].name == t.name && (a = r.Types[l]); break;
                                case "<rc":
                                    1 == i ? r.Cell.push({ type: r.Types[t.t - 1].name, index: +t.v }) : 0 == i && r.Value.push({ type: r.Types[t.t - 1].name, index: +t.v }); break;
                                case "<cellMetadata":
                                    i = 1; break;
                                case "</cellMetadata>":
                                case "</valueMetadata>":
                                    i = 2; break;
                                case "<valueMetadata":
                                    i = 0; break;
                                case "<ext":
                                    o = !0; break;
                                case "</ext>":
                                    o = !1; break;
                                case "<rvb":
                                    if (!a) break;
                                    a.offsets || (a.offsets = []), a.offsets.push(+t.i); break;
                                default:
                                    if (!o && n.WTF) throw new Error("unrecognized " + t[0] + " in metadata") } return e })), r }(e, 0, n) } var wl, zl = /([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,
                    xl = /([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;

                function Al(e, t) { var n = e.split(/\s+/),
                        r = []; if (t || (r[0] = n[0]), 1 === n.length) return r; var a, o, i, l = e.match(zl); if (l)
                        for (i = 0; i != l.length; ++i) - 1 === (o = (a = l[i].match(xl))[1].indexOf(":")) ? r[a[1]] = a[2].slice(1, a[2].length - 1) : r["xmlns:" === a[1].slice(0, 6) ? "xmlns" + a[1].slice(6) : a[1].slice(o + 1)] = a[2].slice(1, a[2].length - 1); return r }

                function kl(e) { var t = {}; if (1 === e.split(/\s+/).length) return t; var n, r, a, o = e.match(zl); if (o)
                        for (a = 0; a != o.length; ++a) - 1 === (r = (n = o[a].match(xl))[1].indexOf(":")) ? t[n[1]] = n[2].slice(1, n[2].length - 1) : t["xmlns:" === n[1].slice(0, 6) ? "xmlns" + n[1].slice(6) : n[1].slice(r + 1)] = n[2].slice(1, n[2].length - 1); return t }

                function Sl(e, t, n, r) { var a = r; switch ((n[0].match(/dt:dt="([\w.]+)"/) || ["", ""])[1]) {
                        case "boolean":
                            a = bt(r); break;
                        case "i2":
                        case "int":
                            a = parseInt(r, 10); break;
                        case "r4":
                        case "float":
                            a = parseFloat(r); break;
                        case "date":
                        case "dateTime.tz":
                            a = Fe(r); break;
                        case "i8":
                        case "string":
                        case "fixed":
                        case "uuid":
                        case "bin.base64":
                            break;
                        default:
                            throw new Error("bad custprop:" + n[0]) } e[pt(t)] = a }

                function Ml(e, t, n) { if ("z" !== e.t) { if (!n || !1 !== n.cellText) try { "e" === e.t ? e.w = e.w || vr[e.v] : "General" === t ? "n" === e.t ? (0 | e.v) === e.v ? e.w = e.v.toString(10) : e.w = X(e.v) : e.w = Q(e.v) : e.w = function(e, t) { var n = wl[e] || pt(e); return "General" === n ? Q(t) : be(n, t) }(t || "General", e.v) } catch (o) { if (n.WTF) throw o }
                        try { var r = wl[t] || t || "General"; if (n.cellNF && (e.z = r), n.cellDates && "n" == e.t && fe(r)) { var a = U(e.v);
                                a && (e.t = "d", e.v = new Date(a.y, a.m - 1, a.d, a.H, a.M, a.S, a.u)) } } catch (o) { if (n.WTF) throw o } } }

                function El(e, t, n) { if (n.cellStyles && t.Interior) { var r = t.Interior;
                        r.Pattern && (r.patternType = bo[r.Pattern] || r.Pattern) } e[t.ID] = t }

                function Cl(e, t, n, r, a, o, i, l, s, c) { var d = "General",
                        u = r.StyleID,
                        h = {};
                    c = c || {}; var m = [],
                        p = 0; for (void 0 === u && l && (u = l.StyleID), void 0 === u && i && (u = i.StyleID); void 0 !== o[u] && (o[u].nf && (d = o[u].nf), o[u].Interior && m.push(o[u].Interior), o[u].Parent);) u = o[u].Parent; switch (n.Type) {
                        case "Boolean":
                            r.t = "b", r.v = bt(e); break;
                        case "String":
                            r.t = "s", r.r = yt(pt(e)), r.v = e.indexOf("<") > -1 ? pt(t || e).replace(/<.*?>/g, "") : r.r; break;
                        case "DateTime":
                            "Z" != e.slice(-1) && (e += "Z"), r.v = (Fe(e) - new Date(Date.UTC(1899, 11, 30))) / 864e5, r.v !== r.v ? r.v = pt(e) : r.v < 60 && (r.v = r.v - 1), d && "General" != d || (d = "yyyy-mm-dd");
                        case "Number":
                            void 0 === r.v && (r.v = +e), r.t || (r.t = "n"); break;
                        case "Error":
                            r.t = "e", r.v = gr[e], !1 !== c.cellText && (r.w = e); break;
                        default:
                            "" == e && "" == t ? r.t = "z" : (r.t = "s", r.v = yt(t || e)) } if (Ml(r, d, c), !1 !== c.cellFormula)
                        if (r.Formula) { var f = pt(r.Formula);
                            61 == f.charCodeAt(0) && (f = f.slice(1)), r.f = No(f, a), delete r.Formula, "RC" == r.ArrayRange ? r.F = No("RC:RC", a) : r.ArrayRange && (r.F = No(r.ArrayRange, a), s.push([Pn(r.F), r.F])) } else
                            for (p = 0; p < s.length; ++p) a.r >= s[p][0].s.r && a.r <= s[p][0].e.r && a.c >= s[p][0].s.c && a.c <= s[p][0].e.c && (r.F = s[p][1]);
                    c.cellStyles && (m.forEach((function(e) {!h.patternType && e.patternType && (h.patternType = e.patternType) })), r.s = h), void 0 !== r.StyleID && (r.ixfe = r.StyleID) }

                function Tl(e) { e.t = e.v || "", e.t = e.t.replace(/\r\n/g, "\n").replace(/\r/g, "\n"), e.v = e.w = e.ixfe = void 0 }

                function Hl(e, t) { var n = t || {};
                    ze(); var r = p(Ot(e)); "binary" != n.type && "array" != n.type && "base64" != n.type || (r = "undefined" !== typeof m ? m.utils.decode(65001, u(r)) : kt(r)); var a, o = r.slice(0, 1024).toLowerCase(),
                        i = !1; if ((1023 & (o = o.replace(/".*?"/g, "")).indexOf(">")) > Math.min(1023 & o.indexOf(","), 1023 & o.indexOf(";"))) { var l = _e(n); return l.type = "string", Ia.to_workbook(r, l) } if (-1 == o.indexOf("<?xml") && ["html", "table", "head", "meta", "script", "style", "div"].forEach((function(e) { o.indexOf("<" + e) >= 0 && (i = !0) })), i) return function(e, t) { var n = e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi); if (!n || 0 == n.length) throw new Error("Invalid HTML: could not find <table>"); if (1 == n.length) return Nn(Wl(n[0], t), t); var r = { SheetNames: [], Sheets: {} }; return n.forEach((function(e, n) { _s(r, Wl(e, t), "Sheet" + (n + 1)) })), r }(r, n);
                    wl = { "General Number": "General", "General Date": N[22], "Long Date": "dddd, mmmm dd, yyyy", "Medium Date": N[15], "Short Date": N[14], "Long Time": N[19], "Medium Time": N[18], "Short Time": N[20], Currency: '"$"#,##0.00_);[Red]\\("$"#,##0.00\\)', Fixed: N[2], Standard: N[4], Percent: N[10], Scientific: N[11], "Yes/No": '"Yes";"Yes";"No";@', "True/False": '"True";"True";"False";@', "On/Off": '"Yes";"Yes";"No";@' }; var s, c = [];
                    null != g && null == n.dense && (n.dense = g); var d, h = {},
                        f = [],
                        v = n.dense ? [] : {},
                        y = "",
                        b = {},
                        w = {},
                        z = Al('<Data ss:Type="String">'),
                        x = 0,
                        A = 0,
                        k = 0,
                        S = { s: { r: 2e6, c: 2e6 }, e: { r: 0, c: 0 } },
                        M = {},
                        E = {},
                        C = "",
                        T = 0,
                        H = [],
                        L = {},
                        I = {},
                        j = 0,
                        V = [],
                        O = [],
                        R = {},
                        P = [],
                        D = !1,
                        F = [],
                        _ = [],
                        B = {},
                        W = 0,
                        U = 0,
                        q = { Sheets: [], WBProps: { date1904: !1 } },
                        G = {};
                    Rt.lastIndex = 0, r = r.replace(/<!--([\s\S]*?)-->/gm, ""); for (var K = ""; a = Rt.exec(r);) switch (a[3] = (K = a[3]).toLowerCase()) {
                        case "data":
                            if ("data" == K) { if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else "/" !== a[0].charAt(a[0].length - 2) && c.push([a[3], !0]); break } if (c[c.length - 1][1]) break; "/" === a[1] ? Cl(r.slice(x, a.index), C, z, "comment" == c[c.length - 1][0] ? R : b, { c: A, r: k }, M, P[A], w, F, n) : (C = "", z = Al(a[0]), x = a.index + a[0].length); break;
                        case "cell":
                            if ("/" === a[1])
                                if (O.length > 0 && (b.c = O), (!n.sheetRows || n.sheetRows > k) && void 0 !== b.v && (n.dense ? (v[k] || (v[k] = []), v[k][A] = b) : v[In(A) + Hn(k)] = b), b.HRef && (b.l = { Target: pt(b.HRef) }, b.HRefScreenTip && (b.l.Tooltip = b.HRefScreenTip), delete b.HRef, delete b.HRefScreenTip), (b.MergeAcross || b.MergeDown) && (W = A + (0 | parseInt(b.MergeAcross, 10)), U = k + (0 | parseInt(b.MergeDown, 10)), H.push({ s: { c: A, r: k }, e: { c: W, r: U } })), n.sheetStubs)
                                    if (b.MergeAcross || b.MergeDown) { for (var Z = A; Z <= W; ++Z)
                                            for (var Y = k; Y <= U; ++Y)(Z > A || Y > k) && (n.dense ? (v[Y] || (v[Y] = []), v[Y][Z] = { t: "z" }) : v[In(Z) + Hn(Y)] = { t: "z" });
                                        A = W + 1 } else ++A;
                            else b.MergeAcross ? A = W + 1 : ++A;
                            else(b = kl(a[0])).Index && (A = +b.Index - 1), A < S.s.c && (S.s.c = A), A > S.e.c && (S.e.c = A), "/>" === a[0].slice(-2) && ++A, O = []; break;
                        case "row":
                            "/" === a[1] || "/>" === a[0].slice(-2) ? (k < S.s.r && (S.s.r = k), k > S.e.r && (S.e.r = k), "/>" === a[0].slice(-2) && (w = Al(a[0])).Index && (k = +w.Index - 1), A = 0, ++k) : ((w = Al(a[0])).Index && (k = +w.Index - 1), B = {}, ("0" == w.AutoFitHeight || w.Height) && (B.hpx = parseInt(w.Height, 10), B.hpt = go(B.hpx), _[k] = B), "1" == w.Hidden && (B.hidden = !0, _[k] = B)); break;
                        case "worksheet":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|"));
                                f.push(y), S.s.r <= S.e.r && S.s.c <= S.e.c && (v["!ref"] = Rn(S), n.sheetRows && n.sheetRows <= S.e.r && (v["!fullref"] = v["!ref"], S.e.r = n.sheetRows - 1, v["!ref"] = Rn(S))), H.length && (v["!merges"] = H), P.length > 0 && (v["!cols"] = P), _.length > 0 && (v["!rows"] = _), h[y] = v } else S = { s: { r: 2e6, c: 2e6 }, e: { r: 0, c: 0 } }, k = A = 0, c.push([a[3], !1]), s = Al(a[0]), y = pt(s.Name), v = n.dense ? [] : {}, H = [], F = [], _ = [], G = { name: y, Hidden: 0 }, q.Sheets.push(G); break;
                        case "table":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else { if ("/>" == a[0].slice(-2)) break;
                                c.push([a[3], !1]), P = [], D = !1 } break;
                        case "style":
                            "/" === a[1] ? El(M, E, n) : E = Al(a[0]); break;
                        case "numberformat":
                            E.nf = pt(Al(a[0]).Format || "General"), wl[E.nf] && (E.nf = wl[E.nf]); for (var X = 0; 392 != X && N[X] != E.nf; ++X); if (392 == X)
                                for (X = 57; 392 != X; ++X)
                                    if (null == N[X]) { we(E.nf, X); break } break;
                        case "column":
                            if ("table" !== c[c.length - 1][0]) break; if ((d = Al(a[0])).Hidden && (d.hidden = !0, delete d.Hidden), d.Width && (d.wpx = parseInt(d.Width, 10)), !D && d.wpx > 10) { D = !0, so = oo; for (var $ = 0; $ < P.length; ++$) P[$] && fo(P[$]) } D && fo(d), P[d.Index - 1 || P.length] = d; for (var Q = 0; Q < +d.Span; ++Q) P[P.length] = _e(d); break;
                        case "namedrange":
                            if ("/" === a[1]) break;
                            q.Names || (q.Names = []); var J = dt(a[0]),
                                ee = { Name: J.Name, Ref: No(J.RefersTo.slice(1), { r: 0, c: 0 }) };
                            q.Sheets.length > 0 && (ee.Sheet = q.Sheets.length - 1), q.Names.push(ee); break;
                        case "namedcell":
                        case "b":
                        case "i":
                        case "u":
                        case "s":
                        case "em":
                        case "h2":
                        case "h3":
                        case "sub":
                        case "sup":
                        case "span":
                        case "alignment":
                        case "borders":
                        case "border":
                        case "protection":
                        case "paragraphs":
                        case "name":
                        case "pixelsperinch":
                        case "null":
                            break;
                        case "font":
                            if ("/>" === a[0].slice(-2)) break; "/" === a[1] ? C += r.slice(T, a.index) : T = a.index + a[0].length; break;
                        case "interior":
                            if (!n.cellStyles) break;
                            E.Interior = Al(a[0]); break;
                        case "author":
                        case "title":
                        case "description":
                        case "created":
                        case "keywords":
                        case "subject":
                        case "category":
                        case "company":
                        case "lastauthor":
                        case "lastsaved":
                        case "lastprinted":
                        case "version":
                        case "revision":
                        case "totaltime":
                        case "hyperlinkbase":
                        case "manager":
                        case "contentstatus":
                        case "identifier":
                        case "language":
                        case "appname":
                            if ("/>" === a[0].slice(-2)) break; "/" === a[1] ? Lr(L, K, r.slice(j, a.index)) : j = a.index + a[0].length; break;
                        case "styles":
                        case "workbook":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else c.push([a[3], !1]); break;
                        case "comment":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|"));
                                Tl(R), O.push(R) } else c.push([a[3], !1]), R = { a: (s = Al(a[0])).Author }; break;
                        case "autofilter":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else if ("/" !== a[0].charAt(a[0].length - 2)) { var te = Al(a[0]);
                                v["!autofilter"] = { ref: No(te.Range).replace(/\$/g, "") }, c.push([a[3], !0]) } break;
                        case "datavalidation":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else "/" !== a[0].charAt(a[0].length - 2) && c.push([a[3], !0]); break;
                        case "componentoptions":
                        case "documentproperties":
                        case "customdocumentproperties":
                        case "officedocumentsettings":
                        case "pivottable":
                        case "pivotcache":
                        case "names":
                        case "mapinfo":
                        case "pagebreaks":
                        case "querytable":
                        case "sorting":
                        case "schema":
                        case "conditionalformatting":
                        case "smarttagtype":
                        case "smarttags":
                        case "excelworkbook":
                        case "workbookoptions":
                        case "worksheetoptions":
                            if ("/" === a[1]) { if ((s = c.pop())[0] !== a[3]) throw new Error("Bad state: " + s.join("|")) } else "/" !== a[0].charAt(a[0].length - 2) && c.push([a[3], !0]); break;
                        default:
                            if (0 == c.length && "document" == a[3]) return ns(r, n); if (0 == c.length && "uof" == a[3]) return ns(r, n); var ne = !0; switch (c[c.length - 1][0]) {
                                case "officedocumentsettings":
                                    switch (a[3]) {
                                        case "allowpng":
                                        case "removepersonalinformation":
                                        case "downloadcomponents":
                                        case "locationofcomponents":
                                        case "colors":
                                        case "color":
                                        case "index":
                                        case "rgb":
                                        case "targetscreensize":
                                        case "readonlyrecommended":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "componentoptions":
                                    switch (a[3]) {
                                        case "toolbar":
                                        case "hideofficelogo":
                                        case "spreadsheetautofit":
                                        case "label":
                                        case "caption":
                                        case "maxheight":
                                        case "maxwidth":
                                        case "nextsheetnumber":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "excelworkbook":
                                    switch (a[3]) {
                                        case "date1904":
                                            q.WBProps.date1904 = !0; break;
                                        case "windowheight":
                                        case "windowwidth":
                                        case "windowtopx":
                                        case "windowtopy":
                                        case "tabratio":
                                        case "protectstructure":
                                        case "protectwindow":
                                        case "protectwindows":
                                        case "activesheet":
                                        case "displayinknotes":
                                        case "firstvisiblesheet":
                                        case "supbook":
                                        case "sheetname":
                                        case "sheetindex":
                                        case "sheetindexfirst":
                                        case "sheetindexlast":
                                        case "dll":
                                        case "acceptlabelsinformulas":
                                        case "donotsavelinkvalues":
                                        case "iteration":
                                        case "maxiterations":
                                        case "maxchange":
                                        case "path":
                                        case "xct":
                                        case "count":
                                        case "selectedsheets":
                                        case "calculation":
                                        case "uncalced":
                                        case "startupprompt":
                                        case "crn":
                                        case "externname":
                                        case "formula":
                                        case "colfirst":
                                        case "collast":
                                        case "wantadvise":
                                        case "boolean":
                                        case "error":
                                        case "text":
                                        case "ole":
                                        case "noautorecover":
                                        case "publishobjects":
                                        case "donotcalculatebeforesave":
                                        case "number":
                                        case "refmoder1c1":
                                        case "embedsavesmarttags":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "workbookoptions":
                                    switch (a[3]) {
                                        case "owcversion":
                                        case "height":
                                        case "width":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "worksheetoptions":
                                    switch (a[3]) {
                                        case "visible":
                                            if ("/>" === a[0].slice(-2));
                                            else if ("/" === a[1]) switch (r.slice(j, a.index)) {
                                                case "SheetHidden":
                                                    G.Hidden = 1; break;
                                                case "SheetVeryHidden":
                                                    G.Hidden = 2 } else j = a.index + a[0].length; break;
                                        case "header":
                                            v["!margins"] || ji(v["!margins"] = {}, "xlml"), isNaN(+dt(a[0]).Margin) || (v["!margins"].header = +dt(a[0]).Margin); break;
                                        case "footer":
                                            v["!margins"] || ji(v["!margins"] = {}, "xlml"), isNaN(+dt(a[0]).Margin) || (v["!margins"].footer = +dt(a[0]).Margin); break;
                                        case "pagemargins":
                                            var re = dt(a[0]);
                                            v["!margins"] || ji(v["!margins"] = {}, "xlml"), isNaN(+re.Top) || (v["!margins"].top = +re.Top), isNaN(+re.Left) || (v["!margins"].left = +re.Left), isNaN(+re.Right) || (v["!margins"].right = +re.Right), isNaN(+re.Bottom) || (v["!margins"].bottom = +re.Bottom); break;
                                        case "displayrighttoleft":
                                            q.Views || (q.Views = []), q.Views[0] || (q.Views[0] = {}), q.Views[0].RTL = !0; break;
                                        case "freezepanes":
                                        case "frozennosplit":
                                        case "splithorizontal":
                                        case "splitvertical":
                                        case "donotdisplaygridlines":
                                        case "activerow":
                                        case "activecol":
                                        case "toprowbottompane":
                                        case "leftcolumnrightpane":
                                        case "unsynced":
                                        case "print":
                                        case "printerrors":
                                        case "panes":
                                        case "scale":
                                        case "pane":
                                        case "number":
                                        case "layout":
                                        case "pagesetup":
                                        case "selected":
                                        case "protectobjects":
                                        case "enableselection":
                                        case "protectscenarios":
                                        case "validprinterinfo":
                                        case "horizontalresolution":
                                        case "verticalresolution":
                                        case "numberofcopies":
                                        case "activepane":
                                        case "toprowvisible":
                                        case "leftcolumnvisible":
                                        case "fittopage":
                                        case "rangeselection":
                                        case "papersizeindex":
                                        case "pagelayoutzoom":
                                        case "pagebreakzoom":
                                        case "filteron":
                                        case "fitwidth":
                                        case "fitheight":
                                        case "commentslayout":
                                        case "zoom":
                                        case "lefttoright":
                                        case "gridlines":
                                        case "allowsort":
                                        case "allowfilter":
                                        case "allowinsertrows":
                                        case "allowdeleterows":
                                        case "allowinsertcols":
                                        case "allowdeletecols":
                                        case "allowinserthyperlinks":
                                        case "allowformatcells":
                                        case "allowsizecols":
                                        case "allowsizerows":
                                        case "tabcolorindex":
                                        case "donotdisplayheadings":
                                        case "showpagelayoutzoom":
                                        case "blackandwhite":
                                        case "donotdisplayzeros":
                                        case "displaypagebreak":
                                        case "rowcolheadings":
                                        case "donotdisplayoutline":
                                        case "noorientation":
                                        case "allowusepivottables":
                                        case "zeroheight":
                                        case "viewablerange":
                                        case "selection":
                                        case "protectcontents":
                                            break;
                                        case "nosummaryrowsbelowdetail":
                                            v["!outline"] || (v["!outline"] = {}), v["!outline"].above = !0; break;
                                        case "nosummarycolumnsrightdetail":
                                            v["!outline"] || (v["!outline"] = {}), v["!outline"].left = !0; break;
                                        default:
                                            ne = !1 } break;
                                case "pivottable":
                                case "pivotcache":
                                    switch (a[3]) {
                                        case "immediateitemsondrop":
                                        case "showpagemultipleitemlabel":
                                        case "compactrowindent":
                                        case "location":
                                        case "pivotfield":
                                        case "orientation":
                                        case "layoutform":
                                        case "layoutsubtotallocation":
                                        case "layoutcompactrow":
                                        case "position":
                                        case "pivotitem":
                                        case "datatype":
                                        case "datafield":
                                        case "sourcename":
                                        case "parentfield":
                                        case "ptlineitems":
                                        case "ptlineitem":
                                        case "countofsameitems":
                                        case "item":
                                        case "itemtype":
                                        case "ptsource":
                                        case "cacheindex":
                                        case "consolidationreference":
                                        case "filename":
                                        case "reference":
                                        case "nocolumngrand":
                                        case "norowgrand":
                                        case "blanklineafteritems":
                                        case "hidden":
                                        case "subtotal":
                                        case "basefield":
                                        case "mapchilditems":
                                        case "function":
                                        case "refreshonfileopen":
                                        case "printsettitles":
                                        case "mergelabels":
                                        case "defaultversion":
                                        case "refreshname":
                                        case "refreshdate":
                                        case "refreshdatecopy":
                                        case "versionlastrefresh":
                                        case "versionlastupdate":
                                        case "versionupdateablemin":
                                        case "versionrefreshablemin":
                                        case "calculation":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "pagebreaks":
                                    switch (a[3]) {
                                        case "colbreaks":
                                        case "colbreak":
                                        case "rowbreaks":
                                        case "rowbreak":
                                        case "colstart":
                                        case "colend":
                                        case "rowend":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "autofilter":
                                    switch (a[3]) {
                                        case "autofiltercolumn":
                                        case "autofiltercondition":
                                        case "autofilterand":
                                        case "autofilteror":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "querytable":
                                    switch (a[3]) {
                                        case "id":
                                        case "autoformatfont":
                                        case "autoformatpattern":
                                        case "querysource":
                                        case "querytype":
                                        case "enableredirections":
                                        case "refreshedinxl9":
                                        case "urlstring":
                                        case "htmltables":
                                        case "connection":
                                        case "commandtext":
                                        case "refreshinfo":
                                        case "notitles":
                                        case "nextid":
                                        case "columninfo":
                                        case "overwritecells":
                                        case "donotpromptforfile":
                                        case "textwizardsettings":
                                        case "source":
                                        case "number":
                                        case "decimal":
                                        case "thousandseparator":
                                        case "trailingminusnumbers":
                                        case "formatsettings":
                                        case "fieldtype":
                                        case "delimiters":
                                        case "tab":
                                        case "comma":
                                        case "autoformatname":
                                        case "versionlastedit":
                                        case "versionlastrefresh":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "datavalidation":
                                    switch (a[3]) {
                                        case "range":
                                        case "type":
                                        case "min":
                                        case "max":
                                        case "sort":
                                        case "descending":
                                        case "order":
                                        case "casesensitive":
                                        case "value":
                                        case "errorstyle":
                                        case "errormessage":
                                        case "errortitle":
                                        case "inputmessage":
                                        case "inputtitle":
                                        case "combohide":
                                        case "inputhide":
                                        case "condition":
                                        case "qualifier":
                                        case "useblank":
                                        case "value1":
                                        case "value2":
                                        case "format":
                                        case "cellrangelist":
