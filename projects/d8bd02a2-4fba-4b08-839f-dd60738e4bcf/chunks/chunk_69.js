            (0, a.useEffect)((() => { ve.A.init(), r && (ve.A.identify({ userId: r, firstName: i, lastName: l, email: o, licenses: s, maxLicense: n }), window.dataLayer = window.dataLayer || [], window.dataLayer.push({ event: "login", user_id: r }), function(t) { if (t && !t.createAccount && window.dataLayer) try { window.dataLayer = window.dataLayer || [], window.dataLayer.push({ event: "sign_up" }), window.dataLayer.push({ event: "create_register", category: "Account", action: "create_register", label: "New Free Account" }), e(me.ip.trackGoal({ goalId: "createAccount" })) } catch (n) { console.log("Goal Tracking error"), console.log(n) } }(c)) }), [r]) } var YI = n(3643),
            XI = n(94234);

        function $I() { var e; const [, t] = (0, un.A)("activeLicenseId", null, !0), { base: n } = (0, $.useParams)(), r = (0, ae.d4)(Fr.Sj), o = (0, ae.wA)(), [i, l] = (0, a.useState)(!1), [s, c] = (0, a.useState)(!1), { jobId: d, pId: u, cbId: h } = (0, Q.A)(), m = (0, YI.A)(), [, p] = (0, un.A)("browserMode"), [f, v] = (0, un.A)("token"), g = (0, ae.d4)(IH.A), y = (0, mn.A)(), [b, w] = (0, a.useState)(!1), z = r, x = null === g || void 0 === g || null === (e = g.featureSet) || void 0 === e ? void 0 : e.ownershipCharts;
            async function A() { const { error: e } = await async function() { const { payload: e, error: t } = await o(Gn.CX.getPublicToken({ jobId: d, pId: u, cbId: h })); return t ? v(null) : null !== e && void 0 !== e && e.token && v({ token: null === e || void 0 === e ? void 0 : e.token, savedAt: new Date, type: Wt.em.PUBLIC }), { payload: e, error: t } }();
                e || await async function() { await o(me.lf.getPublicLicense({ jobId: d, pId: u, cbId: h })) }(), p(!m), c(!0) } async function k() { const { payload: e, error: n } = await o(m ? me.ip.getMeLite({ ignoreErrorHandler: !0 }) : me.ip.getMe({ ignoreErrorHandler: !0 })); if (!n) { const { error: e } = await async function() { const { payload: e, error: t } = await o(Gn.CX.getToken({ isPrintRoute: m })); return t ? v(null) : null !== e && void 0 !== e && e.token && v({ token: null === e || void 0 === e ? void 0 : e.token, savedAt: new Date, type: Wt.em.SESSION }), { payload: e, error: t } }();
                    e || await async function() { const { error: e } = await o(me.lf.getLicenses({ ignoreErrorHandler: !0 }));
                        e && t(null) }() } n && 401 === (null === e || void 0 === e ? void 0 : e.responseCode) && m ? await A() : (n && 406 === (null === e || void 0 === e ? void 0 : e.responseCode) && l(!0), p(!m), c(!0)) }
            return (0, a.useEffect)((() => { "community" === n && h || ("public" === n || "embed" === n) && (d || u) ? A() : k() }), []), (0, a.useEffect)((() => {!async function() { if (!y && s && z && g && null !== f && void 0 !== f && f.token) { w(!0); const e = [o(me.lf.getOrganizations()), o(XI.Ww.getLicenseThemes({ licenseId: null === g || void 0 === g ? void 0 : g.id }))];
                        x && (e.push(o(NT.VL.getOrgsStats({ ignoreErrorHandler: !0 }))), e.push(o(NT.VL.getAllRelations({ ignoreErrorHandler: !0 })))), await Promise.all(e), w(!1) } }() }), [y, s, null === g || void 0 === g ? void 0 : g.id, z]), { isReady: s, isUnVerified: i, isAuthorized: z, orgResourcesLoading: b } }

        function QI() { const { pId: e } = (0, Q.A)(), [t, n] = (0, un.A)("publicId"), [r, o] = (0, un.A)("activeLicenseId", null, !0), i = (0, ae.d4)(MI);
            (0, a.useEffect)((() => { e && e !== t && n(e) }), [e]), (0, a.useEffect)((() => { r && r !== i && o(r) }), [r]) } var JI = n(53222);

        function ej(e) { var t; let { isReady: n, isAuthorized: r } = e; const { autoselect: o } = (0, Q.A)(), [i, l] = (0, a.useState)(!1), [s, c] = (0, un.A)("activeLicenseId", null, !0), d = (0, re.u)([(0, ne.si)(), (0, ne.K7)(), (0, ne.wi)()]), u = null === d || void 0 === d || null === (t = d.params) || void 0 === t ? void 0 : t.orgId, h = (0, ae.d4)(JI.H), m = null === h || void 0 === h ? void 0 : h.length; return (0, a.useLayoutEffect)((() => {! function() { let e = s; if (n && "false" !== o && !i) { const t = h.find((e => e.isSamlLoginLicense)); if (t) return c(null === t || void 0 === t ? void 0 : t.id), void l(!0); if (u) { const e = h.find((e => e.organizations.find((e => e === u)))); return null !== e && void 0 !== e && e.id ? (c(e.id), void l(!0)) : void l(!0) } if (e) { const t = (h || []).find((t => t.id === e)); if (t) return c(t.id), void l(!0);
                            e = null } if (1 === h.length) { const e = (h || [])[0]; return c(e.id), void l(!0) } l(!0), r && c(e) } else n && "false" === o && (c(null), l(!0)) }() }), [n, m]), i } var tj = n(37556);

        function nj() { const { isReady: e, isUnVerified: t, isAuthorized: n, orgResourcesLoading: r } = $I(), o = ej({ isReady: e, isAuthorized: n }), l = (0, Q.A)(), s = (0, $.useHistory)();
            QI(), ZI(),
                function() { const e = (0, tj.M)({}),
                        { activeTour: t, activeTourStep: n, trackingData: r } = e,
                        o = (null === n || void 0 === n ? void 0 : n.customComponentName) || "tourTooltip",
                        i = (0, a.useRef)(null),
                        { openDialog: l, closeDialog: s } = (0, Ft.A)(o);
                    i.current = i.current || {}, i.current[o] = s, (0, a.useEffect)((() => { const a = Object.keys(i.current || {}).filter((e => e !== o)); if (t && n) return a.length && a.forEach((e => { var t, n, r;
                            null === (t = i.current) || void 0 === t || null === (n = t[e]) || void 0 === n || n.call(t), null === (r = i.current) || void 0 === r || delete r[e] })), ve.A.trackEvent({ eventName: "TOUR_STEP_LOADED", extraParams: r }), l(e);!t && i.current && (Object.values(i.current).forEach((e => null === e || void 0 === e ? void 0 : e())), i.current = null) }), [t, n]) }(),
                function(e) { const { openDialog: t } = (0, Q.A)(), n = (0, re.u)([(0, ne.si)()]), { params: r } = n || { params: {} }, [, o] = (0, a.useContext)(FI.MV);
                    (0, a.useEffect)((() => { if (e && t) switch (t) {
                            case "shareChartDialog":
                                { const { orgId: e, chartId: t, resource: n } = r || {};e && t && n && o((n => ({ ...n, shareChartDialog: { open: !0, props: { orgId: e, chartId: t, params: r } } }))); break }
                            case "account":
                                o((e => ({ ...e, account: { open: !0, ignoreRouteEffects: !0 } }))) } }), [t, e]) }(e); const c = !(null === l || void 0 === l || !l._gl); return (0, a.useLayoutEffect)((() => { try { if (null !== l && void 0 !== l && l._gl) { const e = { ...l };
                        delete e._gl, s.replace({ search: i().stringify(e) }) } } catch (e) { s.push((0, ne.ze)()), console.log("could not update qs from gl"), console.log(e) } }), [c]), (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(u, {}), e && o && (0, we.jsx)(PI, { isReady: e && o, isAuthorized: n, isUnVerified: t, orgResourcesLoading: r }), (0, we.jsx)("div", { id: "dpi" })] }) } const rj = function() { return (0, we.jsx)(a.Suspense, { fallback: (0, we.jsx)("div", { children: "loading..." }), children: (0, we.jsx)(GI, { children: (0, we.jsx)(X.Kd, { children: (0, we.jsx)(DI.A, { children: (0, we.jsx)(NI.A, { children: (0, we.jsx)(g, { backend: Y, children: (0, we.jsx)(FI.Ay, { children: (0, we.jsx)(KI.A, { children: (0, we.jsx)($.Route, { path: "/:base?", component: nj }) }) }) }) }) }) }) }) }) }; var aj; const oj = e => { ve.A.trackEvent({ eventName: "LOGINCLICK_".concat(e) }) },
            ij = e => { let { setCurrentStep: t } = e; const n = "https://app.organimi.com/api/v7"; return (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(se.A, { display: "flex", flexWrap: "wrap", gridGap: 16, mx: 2, children: [(0, we.jsxs)(lj, { className: "iconBtnLink", href: "".concat(n, "/auth/login/google").concat(window.location.search || ""), onClick: () => oj("GOOGLE"), children: [(0, we.jsx)("img", { src: Ae, alt: "Google Login" }), (0, we.jsx)("span", { children: "Sign In with Google" })] }), (0, we.jsxs)(lj, { className: "iconBtnLink", href: "".concat(n, "/auth/login/linkedin").concat(window.location.search || ""), onClick: () => oj("LINKEDIN"), children: [(0, we.jsx)("img", { src: ke, alt: "LinkedIn Login" }), (0, we.jsx)("span", { children: "Sign In with LinkedIn" })] }), (0, we.jsxs)(lj, { className: "iconBtnLink", href: "".concat(n, "/auth/login/microsoft").concat(window.location.search || ""), onClick: () => oj("MICROSOFT"), children: [(0, we.jsx)("img", { src: Se, alt: "Microsoft Login" }), (0, we.jsx)("span", { children: "Sign In with Microsoft" })] }), (0, we.jsxs)(lj, { className: "iconBtnLink", onClick: () => t(2), children: [(0, we.jsx)(Ee.Ay, { size: "lg", icon: "SSO" }), (0, we.jsx)("span", { children: "Sign In with SSO" })] })] }) }) },
            lj = ee.Ay.a(aj || (aj = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\ndisplay: block;\nborder: 1px solid ".concat(t.palette.grey[200], ";\nwidth: 100%;\nheight: 60px;\nbackground: white;\ndisplay: flex;\nalign-items: center;\npadding: 0 10px;\njustify: flex-start;\ncolor: ").concat(t.palette.grey[700], ";\nfont-weight: 400;\nfont-size: 16px;\n&:hover {\n  text-decoration: none;\n}\nsvg {\n  margin-right: 18px;\n  height: 28px;\n  width: 28px !important;\n}\nimg {\n  margin-right: 18px;\n  height: 28px;\n  width: 28px\n}") })),
            { ERROR_MESSAGES: sj } = ge.A,
            cj = () => { var e, t, n, r; const o = (0, le.A)(),
                    i = "".concat(pe.Yr, ".").concat(pe.ue, ".").concat(pe.cM),
                    [l, s] = (0, a.useState)(""),
                    [c, d] = (0, a.useState)(""),
                    [u, h] = (0, a.useState)(),
                    m = (0, oe.mN)({ mode: "onChange" }),
                    { register: p, handleSubmit: f, errors: v, watch: g, clearErrors: y } = m,
                    b = (0, ae.wA)(),
                    w = de(),
                    { t: z } = (0, ie.B)(),
                    x = (0, Q.A)(),
                    A = x.username || "",
                    k = x.token,
                    S = A && k,
                    M = (0, $.useHistory)(),
                    E = e => { s(e.message || "There was a problem creating an account with that email") },
                    [C, T] = (0, he.A)(f((async e => { const { error: t, payload: n } = await b(me.ip.register({ ...e, onError: E, queryParams: x })); if (!t) { if (null === n || void 0 === n || !n.redirect) return M.push((0, ne.A$)()); let e = n.redirect,
                                t = ""; const r = n.redirect.indexOf("?");
                            r >= 0 && (t = n.redirect.substring(r), e = e.slice(0, r)), window.location.assign("".concat(e).concat(t)) } }), (() => {})));
                (0, a.useEffect)((() => { document.title = "Organimi | " + (S ? "Accept Invitation" : "Sign Up"), ve.A.trackEvent({ eventName: "REGISTER_OPEN" }), h((null === x || void 0 === x ? void 0 : x.step) || 1); try { window.dataLayer ? S ? window.dataLayer.push({ event: "register_open_fromInvite", category: "Account", action: "register_open_fromInvite", label: "Register Open From Invite" }) : window.dataLayer.push({ event: "register_open", category: "Account", action: "register_open", label: "Register Open", testVariableText: "123", testVariableNumber: 123 }) : console.log("DataLayer is not defined") } catch (e) { console.log("Problem logging register_open event in GA"), console.log(e) } }), []); const H = () => { var e, t;
                        v.password || y("password"), v.passwordConfirmation || y("passwordConfirmation"), "passwordConfirmation" === (null === v || void 0 === v || null === (e = v.password) || void 0 === e ? void 0 : e.type) && y("password"), "passwordConfirmation" === (null === v || void 0 === v || null === (t = v.passwordConfirmation) || void 0 === t ? void 0 : t.type) && y("passwordConfirmation") },
                    L = { firstName: { required: "Please enter a First Name" }, lastName: { required: "Please enter a Last Name" }, username: { required: "Please enter the email.", pattern: { value: fe.eT, message: "Invalid Email format." } }, password: { required: "Please enter password", validate: { predictable: e => ge.A.isPasswordPredictable({ password: e, firstName: g("firstName") }), passwordConfirmation: e => ge.A.checkPasswordMatch({ password: e, passwordConfirmation: g("passwordConfirmation"), cb: H }) } }, passwordConfirmation: { required: "Please confirm password", validate: { passwordConfirmation: e => ge.A.checkPasswordMatch({ password: g("password"), passwordConfirmation: e, cb: H }) } } },
                    I = { 1: (0, we.jsx)(we.Fragment, { children: (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "block", height: "100%", flexWrap: "wrap", justifyContent: "center", children: (0, we.jsx)(se.A, { display: "flex", justifyContent: "center", bgcolor: "#ffffff", children: (0, we.jsxs)(ze, { version: "".concat(i), children: [(0, we.jsxs)(se.A, { children: [(0, we.jsx)(se.A, { mt: 2, children: (0, we.jsx)(je.A, { align: "center", color: "textPrimary", children: "CREATE ACCOUNT" }) }), (0, we.jsx)(se.A, { m: 4, children: (0, we.jsx)(oe.Op, { ...m, children: (0, we.jsxs)("form", { id: "registrationForm", onSubmit: T, children: [(0, we.jsx)(Ve.A, { label: z("Auth.Label.FirstName"), id: "registerFirstName", type: "text", inputRef: p(L.firstName), error: !!v.firstName, name: "firstName", autoFocus: !0, fullWidth: !0 }), v.firstName && (0, we.jsx)(Oe.A, { children: v.firstName.message }), (0, we.jsx)(Ve.A, { label: z("Auth.Label.LastName"), id: "registerLastName", type: "text", name: "lastName", error: !!v.lastName, inputRef: p(L.lastName), fullWidth: !0 }), v.lastName && (0, we.jsx)(Oe.A, { children: v.lastName.message }), (0, we.jsx)(Ve.A, { label: z("Auth.Register.EmailLabel"), id: "registerEmail", type: "email", name: "username", defaultValue: A, inputRef: p(L.username), error: !!v.username, fullWidth: !0 }), v.username && (0, we.jsx)(Oe.A, { children: v.username.message }), (0, we.jsx)(ye.A, { id: "registerPassword", name: "password", fullWidth: !0, label: z("Auth.Label.Password"), doValidation: !0, fieldRefParams: L.password, error: !!v.password }), v.password && (0, we.jsx)(Oe.A, { children: (null === (e = v.password) || void 0 === e ? void 0 : e.message) || sj[null === (t = v.password) || void 0 === t ? void 0 : t.type] }), (0, we.jsx)(ye.A, { label: z("Auth.Label.PasswordConfirmation"), id: "registerPasswordConfirmation", name: "passwordConfirmation", fullWidth: !0, fieldRefParams: L.passwordConfirmation, error: !!v.passwordConfirmation }), v.passwordConfirmation && (0, we.jsx)(Oe.A, { children: (null === (n = v.passwordConfirmation) || void 0 === n ? void 0 : n.message) || sj[null === (r = v.passwordConfirmation) || void 0 === r ? void 0 : r.type] }), (0, we.jsx)(je.A, { variant: "caption", color: "error", children: l }), (0, we.jsxs)(se.A, { my: 1, display: "flex", alignItems: "center", flexDirection: "column", gridGap: 32, children: [(0, we.jsx)($e.A, { fullWidth: !0, disabled: C, variant: "contained", color: "primary", type: "submit", form: "registrationForm", children: z(S ? "Auth.Button.AcceptInvitation" : "Auth.Button.Register") }), (0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: (0, ne.iD)(), children: z("Auth.Link.Login") }) })] }), (0, we.jsx)(je.A, { weight: "bold", color: o.palette.success.main, children: c })] }) }) }), (0, we.jsx)(ij, { setCurrentStep: h })] }), (0, we.jsxs)(se.A, { mt: 6, display: "flex", gridGap: 16, alignItems: "center", justifyContent: "space-around", children: [(0, we.jsxs)(Be, { variant: "body1", display: "inline", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", z("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "inline", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: z("Auth.Link.Privacy") }) })] })] }) }) }) }), 2: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: w }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsxs)(ze, { version: "".concat(i), children: [(0, we.jsx)(se.A, { my: 2, children: (0, we.jsx)(je.A, { color: "textPrimary", children: z("Auth.Login.Heading") }) }), (0, we.jsx)(Xe, { altLinkText: "Register without SSO", setCurrentStep: h, btnText: z(S ? "Auth.Button.AcceptInvitation" : "Auth.Button.Login") }), (0, we.jsxs)(se.A, { mt: 6, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", z("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: z("Auth.Link.Privacy") }) })] })] }) })] }) }; return (0, we.jsxs)(we.Fragment, { children: [" ", I[u], " "] }) },
            dj = () => { const [e, t] = (0, a.useState)(""), n = (0, ae.wA)(), { t: r } = (0, ie.B)(), o = (0, oe.mN)({ mode: "onChange" }), { register: i, handleSubmit: l, errors: s, setError: c } = o, d = (0, Q.A)(), u = "".concat(pe.Yr, ".").concat(pe.ue, ".").concat(pe.cM), [h, m] = (0, a.useState)();
                (0, a.useEffect)((() => { document.title = "Organimi | Login", m((null === d || void 0 === d ? void 0 : d.step) || 1) }), []); const p = e => { 401 === e.responseCode ? c("password", { message: "Incorrect email or password" }) : t(e.message) },
                    [f, v] = (0, he.A)(l((async e => { ve.A.trackEvent({ eventName: "LOGINCLICK_LOCAL" }); const { error: t, payload: r } = await n(me.ip.login({ ...e, onError: p, queryParams: d }));
                        t || window.location.assign(r.redirect || (0, ne.ze)()) }))),
                    g = { username: { required: "Please enter the email.", pattern: { value: fe.eT, message: "Invalid Email format." } }, password: { required: "Please enter password" } },
                    y = { 1: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(oe.Op, { ...o, children: (0, we.jsxs)("form", { onSubmit: v, children: [(0, we.jsx)(Ve.A, { label: r("Auth.Label.Email"), id: "loginEmail", type: "text", autoFocus: !0, name: "username", fullWidth: !0, defaultValue: (null === d || void 0 === d ? void 0 : d.username) || "", inputRef: i(g.username), error: !!s.username }), (0, we.jsx)(ye.A, { id: "loginPassword", name: "password", label: r("Auth.Label.Password"), fullWidth: !0, fieldRefParams: g.password, error: !!s.password }), (0, we.jsx)(je.A, { variant: "caption", color: "error", children: e }), (0, we.jsx)(Oe.A, { children: s.username && s.username.message }), (0, we.jsx)(Oe.A, { children: s.password && s.password.message }), (0, we.jsx)(se.A, { my: 1, children: (0, we.jsx)(Ge.A, { type: "submit", variant: "contained", color: "primary", fullWidth: !0, disabled: f, "data-testid": "login-button", children: r("Auth.Button.Login") }) })] }) }), (0, we.jsxs)(se.A, { my: 3, display: "flex", flexDirection: "column", gridGap: 16, children: [(0, we.jsx)(se.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: (0, ne.yZ)(), children: r("Auth.Link.Forgot") }) }) }), (0, we.jsx)(se.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: (0, ne.kz)(), children: r("Auth.Link.Register") }) }) })] }), (0, we.jsx)(ij, { setCurrentStep: m })] }), 2: (0, we.jsx)(Xe, { setCurrentStep: m }) }; return (0, we.jsx)(we.Fragment, { children: (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsxs)(ze, { version: "".concat(u), children: [(0, we.jsx)(se.A, { my: 2, children: (0, we.jsx)(je.A, { color: "textPrimary", children: r("Auth.Login.Heading") }) }), y[h], (0, we.jsxs)(se.A, { mt: 6, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", r("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: r("Auth.Link.Privacy") }) })] })] }) }) }) }; var uj = n(46105); const hj = 100; var mj; const pj = (0, ee.Ay)(je.A)(mj || (mj = (0, J.A)(["\n  color: #666666;\n  font-size: 16px;\n  font-weight: 400;\n"]))),
            fj = e => { let { message: t, loadCountPercent: n } = e; return (0, we.jsxs)(se.A, { bgcolor: "#fff", width: "100%", height: "100%", display: "flex", flexDirection: "column", gridGap: 32, justifyContent: "center", px: 3, children: [(0, we.jsx)(se.A, { px: 3, children: (0, we.jsx)(oM.A, { variant: "determinate", value: n }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, alignItems: "center", justifyContent: "center", border: "solid 1px #eee", p: 3, boxShadow: 1, children: [(0, we.jsx)(zn.A, { label: "Tip", color: "primary" }), (0, we.jsx)(pj, { align: "center", children: t })] })] }) }; var vj, gj = n(74732); const yj = ["Try the read only mobile view once you have your charts built"],
            bj = (0, gj.A)(je.A)(vj || (vj = (0, J.A)(["\n  font-size: 18px;\n  color: #666666;\n  font-weight: 400;\n"]))),
            wj = () => { const [, e] = (0, un.A)("isDesktopMode", !1, !0), t = function(e) { let { maxTime: t = 5e3, maxIntervalIncrease: n = 15, intervalRate: r = 150 } = e; const o = (0, a.useRef)(),
                        [i, l] = (0, a.useState)(0),
                        s = (0, a.useRef)(Date.now()),
                        c = Date.now(); return (0, a.useEffect)((() => (o.current = setInterval((() => { i >= 100 && clearInterval(o.current), l((e => { const t = parseInt(n * Math.random()); return Math.min(e + t, hj) })) }), r), () => { clearInterval(o.current) })), []), c - s > t || i >= hj ? hj : i }({ maxTime: 2e3, maxIntervalIncrease: 35, intervalRate: 500 }), n = 100 === t; return (0, a.useEffect)((() => { n && e(!0) }), [n]), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "space-around", gridGap: 32, p: 4, children: [(0, we.jsxs)(se.A, { children: [(0, we.jsx)("img", { width: 300, src: be, alt: "Organimi Logo" }), (0, we.jsx)(bj, { align: "center", children: "Welcome to Organimi" })] }), (0, we.jsx)(fj, { message: yj[0], loadCountPercent: t })] }) };

        function zj(e) { let { handleClose: t } = e; const [n, r] = (0, un.A)("activeLicenseId", null, !0), a = (0, ae.d4)(JI.H), o = (0, ae.d4)(JI.n), { show: i } = (0, Un.A)(), l = (0, $.useHistory)(), s = (0, ae.wA)(); return 0 === a.length ? (0, we.jsx)(wj, {}) : (0, we.jsx)(we.Fragment, { children: (0, we.jsx)(uj.K, { fullScreen: !0, licenses: a, handleCreateNew: () => { i("create new account") }, handleAccountSelect: async e => { if (null !== e && void 0 !== e && e.requiresReLogin) { await s(me.ip.logout()); const t = "samlLogoutRequired" === (null === e || void 0 === e ? void 0 : e.reLoginReason) ? (0, ne.iD)() : "".concat((0, ne.iD)(), "?step=2");
                            window.location.assign(t) } else r(e.id), l.push("/"); "function" === typeof t && t() }, suggestedName: o, disableForceSSO: async e => { const { error: t } = await s(qH.hW.allowAllLoginMethods({ licenseId: null === e || void 0 === e ? void 0 : e.id })); var n;
                        t || ve.A.trackEvent({ eventName: "SSO_FORCE_DISABLED_OVERRIDE", extraParams: { licenseId: null === e || void 0 === e ? void 0 : e.id, plan: (null === e || void 0 === e || null === (n = e.plan) || void 0 === n ? void 0 : n.id) || "" } });
                        window.location.assign("/") }, activeLicenseId: n, handleClose: t }) }) } var xj, Aj = n(49768),
            kj = n(51836),
            Sj = n(35323),
            Mj = n(7091); const Ej = [{ value: "default", label: "Default" }, { value: "chartNameAsc", label: "Chart Name (A-Z)" }, { value: "chartNameDesc", label: "Chart Name (Z-A)" }, { value: "lastUpdatedDesc", label: "Last Updated (Newest)" }, { value: "lastUpdatedAsc", label: "Last Updated (Oldest)" }, { value: "peopleMost", label: "People (Most)" }, { value: "peopleLeast", label: "People (Least)" }, { value: "departmentsMost", label: "Departments (Most)" }, { value: "departmentsLeast", label: "Departments (Least)" }, { value: "vacantRolesMost", label: "Vacant Roles (Most)" }, { value: "vacantRolesLeast", label: "Vacant Roles (Least)" }],
            Cj = () => { const [e, t] = (0, a.useState)(1), [n, r] = (0, a.useState)(""), o = (0, ae.wA)(), { orgId: i } = (0, Sj.g)(), l = (0, ae.d4)(nn.Ot), s = (0, ae.d4)(Lt.VF), c = (0, $.useHistory)(), [d, u] = (0, a.useState)(null), [h, m] = (0, un.A)("mobile.charts.sortBy", "default"), [, p] = (0, un.A)("isDesktopMode", !1, !0), f = Boolean(d), v = (0, a.useMemo)((() => { const e = l.map((e => ({ ...e }))); switch (h) {
                        case "chartNameAsc":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.name.toLowerCase() > t.name.toLowerCase() ? 1 : -1));
                        case "chartNameDesc":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.name.toLowerCase() > t.name.toLowerCase() ? -1 : 1));
                        case "lastUpdatedDesc":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.updated_at > t.updated_at ? -1 : 1));
                        case "lastUpdatedAsc":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.updated_at > t.updated_at ? 1 : -1));
                        case "peopleMost":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.people > t.counts.people ? -1 : 1));
                        case "peopleLeast":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.people > t.counts.people ? 1 : -1));
                        case "departmentsMost":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.department > t.counts.department ? -1 : 1));
                        case "departmentsLeast":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.department > t.counts.department ? 1 : -1));
                        case "vacantRolesMost":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.vacantRole > t.counts.vacantRole ? -1 : 1));
                        case "vacantRolesLeast":
                            return null === e || void 0 === e ? void 0 : e.sort(((e, t) => e.counts.vacantRole > t.counts.vacantRole ? 1 : -1));
                        default:
                            return e.sort(((e, t) => (null === e || void 0 === e ? void 0 : e.updated_at) > t.updated_at ? -1 : 1)) } }), [l, null === s || void 0 === s ? void 0 : s.id, h]), g = Math.ceil(((null === v || void 0 === v ? void 0 : v.length) || 0) / 10), [y, b] = (0, he.A)((async () => { await o(nn.UY.getCharts({ orgId: i })) }));
                (0, a.useEffect)((() => { b() }), [i]); const w = (0, ee.Ay)(je.A)(xj || (xj = (0, J.A)(["\n    color: #aaaaaa;\n    font-size: 12px;\n    font-weight: 400;\n  "]))),
                    z = (0, a.useMemo)((() => v.slice(10 * (e - 1), Math.min(10 * e, v.length)).filter((e => !(null !== n && void 0 !== n && n.length) || e.name.includes(n)))), [e, n, v]),
                    x = (z.length || 0) < (v.length || 0),
                    A = e => () => { m(e), u(null) },
                    k = () => (0, we.jsx)(Aj.A, { open: f, onClose: () => u(null), variant: "selectedMenu", anchorEl: d, anchorOrigin: { vertical: "bottom", horizontal: "center" }, children: Ej.map((e => (0, we.jsx)(Ze.A, { value: e.value, selected: h === e.value, onClick: A(e.value), children: e.label }))) }); return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", overflow: "auto", flex: 1, children: [(0, we.jsxs)(se.A, { bgcolor: "#ffffff", pt: 2, pb: 1, px: 2, display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsxs)(se.A, { flexGrow: 1, children: [(0, we.jsx)(bI.A, { placeholder: "Chart name", fullWidth: !0, size: "small", label: "Search", autoFocus: !0, variant: "outlined", onChange: e => { var n;
                                    t(1), r(null === e || void 0 === e || null === (n = e.target) || void 0 === n ? void 0 : n.value) }, InputProps: { startAdornment: (0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(Ee.Ay, { icon: "Search" }) }) } }), n && !y && (0, we.jsx)(se.A, { my: 1, children: (0, we.jsxs)(w, { children: ["Showing ", (null === z || void 0 === z ? void 0 : z.length) || 0, " of ", (null === v || void 0 === v ? void 0 : v.length) || 0] }) })] }), (0, we.jsx)(Tr.A, { onClick: e => { u(e.currentTarget) }, children: (0, we.jsx)(Ee.Ay, { icon: "Sort", size: "sm" }) })] }), (0, we.jsxs)(se.A, { flex: 1, display: "flex", flexDirection: "column", overflow: "auto", bgcolor: "white", children: [y && Array(6).fill(0).map(((e, t) => (0, we.jsxs)(se.A, { bgcolor: "white", p: 2, display: "flex", flexDirection: "row", gridGap: 8, alignItems: "center", borderRadius: "5px", children: [(0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(tr.A, { variant: "circle", width: 40, height: 40 }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, we.jsx)(tr.A, { variant: "text", width: 200 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 })] })] }, "chart-skeleton-".concat(t)))), !y && !!v.length && z.map(((e, t) => { var n, r, a, o; const l = new Date(e.updated).toDateString(); return (0, we.jsxs)(se.A, { button: !0, bgcolor: "white", p: 2, display: "flex", flexDirection: "row", gridGap: 8, alignItems: "center", borderRadius: "5px", justifyContent: "space-between", component: Ge.A, onClick: () => { c.push((0, ne.Po)({ orgId: i, chartId: e.id, resource: "chart", resourceAction: "view" })) }, children: [(0, we.jsxs)(se.A, { display: "flex", gridGap: 16, alignItems: "center", textAlign: "left", children: [(0, we.jsx)(se.A, { width: 36, height: 36, display: "flex", justifyContent: "center", alignItems: "center", border: "solid 1px #e1e1e1", borderRadius: "50%", children: (0, we.jsx)(Ee.Ay, { icon: "Chart" }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, we.jsx)(se.A, { display: "flex", flexDirection: "row", alignItems: "center", gridGap: 8, children: (0, we.jsx)(je.A, { variant: "body1", weight: "medium", color: "primary", children: (0, Tn.RP)(e.name, 25) }) }), e.updated && (0, we.jsx)(wn.Ay, { title: l, arrow: !0, placement: "right", children: (0, we.jsxs)(je.A, { variant: "caption", children: ["Last updated: ", (0, Mj.g)(new Date(e.updated))] }) })] })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 8, alignItems: "center", children: [(null === e || void 0 === e || null === (n = e.counts) || void 0 === n ? void 0 : n.people) > 0 && (0, we.jsxs)(se.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "UserGroup", size: "sm" }), (0, we.jsx)(je.A, { weight: "light", color: "#000000", children: (null === e || void 0 === e || null === (r = e.counts) || void 0 === r ? void 0 : r.people) || "N/A" })] }), (null === e || void 0 === e || null === (a = e.counts) || void 0 === a ? void 0 : a.vacantRole) > 0 && (0, we.jsxs)(se.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "UserVacant", size: "sm" }), (0, we.jsx)(je.A, { weight: "light", color: "#000000", children: (null === e || void 0 === e || null === (o = e.counts) || void 0 === o ? void 0 : o.vacantRole) || "N/A" })] })] })] }, "chart-visible-".concat(t)) })), !y && 0 === v.length && (0, we.jsx)(se.A, { p: 2, display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", children: (0, we.jsx)(jn.A, { image: kj.A, text: "Cannot find any charts under this Organization.  Please create a chart from Desktop view to get started.", textVariant: "body1", handleClick: () => { p(!0) }, buttonText: "Use Desktop App", isButtonVisible: !0 }) })] }), x && (0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", mx: 2, alignItems: "center", py: 1, children: [(0, we.jsx)(Ge.A, { onClick: () => { t((e => Math.max(e - 1, 1))) }, size: "small", disabled: y || e <= 1, children: "Prev Page" }), (0, we.jsx)(Ge.A, { onClick: () => { t((e => Math.min(e + 1, g))) }, size: "small", disabled: y || e >= g, children: "Next Page" })] }), (0, we.jsx)(k, {})] }) }; var Tj, Hj, Lj = n(60038); const Ij = (0, ee.Ay)(je.A)(Tj || (Tj = (0, J.A)(["\n  color: #000000;\n  font-size: 14px;\n  font-weight: 400;\n"]))),
            jj = (0, ee.Ay)(je.A)(Hj || (Hj = (0, J.A)(["\n  color: #666666;\n  font-size: 13px;\n  font-weight: 400;\n"]))); var Vj = n(71233),
            Oj = n(80045),
            Rj = (n(2086), n(43024)),
            Pj = n(12899),
            Dj = n(80296),
            Fj = n(40830),
            Nj = n(60768),
            _j = { entering: { transform: "none" }, entered: { transform: "none" } },
            Bj = { enter: Pj.p0.enteringScreen, exit: Pj.p0.leavingScreen },
            Wj = a.forwardRef((function(e, t) { var n = e.children,
                    r = e.disableStrictModeCompat,
                    o = void 0 !== r && r,
                    i = e.in,
                    s = e.onEnter,
                    c = e.onEntered,
                    d = e.onEntering,
                    u = e.onExit,
                    h = e.onExited,
                    m = e.onExiting,
                    p = e.style,
                    f = e.timeout,
                    v = void 0 === f ? Bj : f,
                    g = e.TransitionComponent,
                    y = void 0 === g ? Fc.Ay : g,
                    b = (0, Oj.A)(e, ["children", "disableStrictModeCompat", "in", "onEnter", "onEntered", "onEntering", "onExit", "onExited", "onExiting", "style", "timeout", "TransitionComponent"]),
                    w = (0, le.A)(),
                    z = w.unstable_strictMode && !o,
                    x = a.useRef(null),
                    A = (0, Nj.A)(n.ref, t),
                    k = (0, Nj.A)(z ? x : void 0, A),
                    S = function(e) { return function(t, n) { if (e) { var r = z ? [x.current, t] : [t, n],
                                    a = (0, Dj.A)(r, 2),
                                    o = a[0],
                                    i = a[1];
                                void 0 === i ? e(o) : e(o, i) } } },
                    M = S(d),
                    E = S((function(e, t) {
                        (0, Fj.q)(e); var n = (0, Fj.c)({ style: p, timeout: v }, { mode: "enter" });
                        e.style.webkitTransition = w.transitions.create("transform", n), e.style.transition = w.transitions.create("transform", n), s && s(e, t) })),
                    C = S(c),
                    T = S(m),
                    H = S((function(e) { var t = (0, Fj.c)({ style: p, timeout: v }, { mode: "exit" });
                        e.style.webkitTransition = w.transitions.create("transform", t), e.style.transition = w.transitions.create("transform", t), u && u(e) })),
                    L = S(h); return a.createElement(y, (0, l.default)({ appear: !0, in: i, nodeRef: z ? x : void 0, onEnter: E, onEntered: C, onEntering: M, onExit: H, onExited: L, onExiting: T, timeout: v }, b), (function(e, t) { return a.cloneElement(n, (0, l.default)({ style: (0, l.default)({ transform: "scale(0)", visibility: "exited" !== e || i ? void 0 : "hidden" }, _j[e], p, n.props.style), ref: k }, t)) })) })); const Uj = Wj; var qj = n(75992),
            Gj = a.forwardRef((function(e, t) { var n = e.children,
                    r = e.classes,
                    o = e.className,
                    i = e.color,
                    s = void 0 === i ? "default" : i,
                    c = e.component,
                    d = void 0 === c ? "button" : c,
                    u = e.disabled,
                    h = void 0 !== u && u,
                    m = e.disableFocusRipple,
                    p = void 0 !== m && m,
                    f = e.focusVisibleClassName,
                    v = e.size,
                    g = void 0 === v ? "large" : v,
                    y = e.variant,
                    b = void 0 === y ? "circular" : y,
                    w = (0, Oj.A)(e, ["children", "classes", "className", "color", "component", "disabled", "disableFocusRipple", "focusVisibleClassName", "size", "variant"]); return a.createElement(qj.A, (0, l.default)({ className: (0, Rj.A)(r.root, o, "large" !== g && r["size".concat((0, CM.A)(g))], h && r.disabled, "extended" === b && r.extended, { primary: r.primary, secondary: r.secondary, inherit: r.colorInherit } [s]), component: d, disabled: h, focusRipple: !p, focusVisibleClassName: (0, Rj.A)(r.focusVisible, f), ref: t }, w), a.createElement("span", { className: r.label }, n)) })); const Kj = (0, s.A)((function(e) { return { root: (0, l.default)({}, e.typography.button, { boxSizing: "border-box", minHeight: 36, transition: e.transitions.create(["background-color", "box-shadow", "border"], { duration: e.transitions.duration.short }), borderRadius: "50%", padding: 0, minWidth: 0, width: 56, height: 56, boxShadow: e.shadows[6], "&:active": { boxShadow: e.shadows[12] }, color: e.palette.getContrastText(e.palette.grey[300]), backgroundColor: e.palette.grey[300], "&:hover": { backgroundColor: e.palette.grey.A100, "@media (hover: none)": { backgroundColor: e.palette.grey[300] }, "&$disabled": { backgroundColor: e.palette.action.disabledBackground }, textDecoration: "none" }, "&$focusVisible": { boxShadow: e.shadows[6] }, "&$disabled": { color: e.palette.action.disabled, boxShadow: e.shadows[0], backgroundColor: e.palette.action.disabledBackground } }), label: { width: "100%", display: "inherit", alignItems: "inherit", justifyContent: "inherit" }, primary: { color: e.palette.primary.contrastText, backgroundColor: e.palette.primary.main, "&:hover": { backgroundColor: e.palette.primary.dark, "@media (hover: none)": { backgroundColor: e.palette.primary.main } } }, secondary: { color: e.palette.secondary.contrastText, backgroundColor: e.palette.secondary.main, "&:hover": { backgroundColor: e.palette.secondary.dark, "@media (hover: none)": { backgroundColor: e.palette.secondary.main } } }, extended: { borderRadius: 24, padding: "0 16px", width: "auto", minHeight: "auto", minWidth: 48, height: 48, "&$sizeSmall": { width: "auto", padding: "0 8px", borderRadius: 17, minWidth: 34, height: 34 }, "&$sizeMedium": { width: "auto", padding: "0 16px", borderRadius: 20, minWidth: 40, height: 40 } }, focusVisible: {}, disabled: {}, colorInherit: { color: "inherit" }, sizeSmall: { width: 40, height: 40 }, sizeMedium: { width: 48, height: 48 } } }), { name: "MuiFab" })(Gj); var Zj = n(64867);

        function Yj(e) { return "up" === e || "down" === e ? "vertical" : "right" === e || "left" === e ? "horizontal" : void 0 } var Xj = a.forwardRef((function(e, t) { var n = e.ariaLabel,
                r = e.FabProps,
                o = (r = void 0 === r ? {} : r).ref,
                i = (0, Oj.A)(r, ["ref"]),
                s = e.children,
                c = e.classes,
                d = e.className,
                u = e.direction,
                h = void 0 === u ? "up" : u,
                m = e.hidden,
                p = void 0 !== m && m,
                f = e.icon,
                v = e.onBlur,
                g = e.onClose,
                y = e.onFocus,
                b = e.onKeyDown,
                w = e.onMouseEnter,
                z = e.onMouseLeave,
                x = e.onOpen,
                A = e.open,
                k = (e.openIcon, e.TransitionComponent),
                S = void 0 === k ? Uj : k,
                M = e.transitionDuration,
                E = void 0 === M ? { enter: Pj.p0.enteringScreen, exit: Pj.p0.leavingScreen } : M,
                C = e.TransitionProps,
                T = (0, Oj.A)(e, ["ariaLabel", "FabProps", "children", "classes", "className", "direction", "hidden", "icon", "onBlur", "onClose", "onFocus", "onKeyDown", "onMouseEnter", "onMouseLeave", "onOpen", "open", "openIcon", "TransitionComponent", "transitionDuration", "TransitionProps"]),
                H = a.useRef();
            a.useEffect((function() { return function() { clearTimeout(H.current) } }), []); var L = a.useRef(0),
                I = a.useRef(),
                j = a.useRef([]);
            j.current = [j.current[0]]; var V = a.useCallback((function(e) { j.current[0] = e }), []),
                O = (0, Nj.A)(o, V),
                R = function(e, t) { return function(n) { j.current[e + 1] = n, t && t(n) } };
            a.useEffect((function() { A || (L.current = 0, I.current = void 0) }), [A]); var P = function(e) { "mouseleave" === e.type && z && z(e), "blur" === e.type && v && v(e), clearTimeout(H.current), g && ("blur" === e.type ? (e.persist(), H.current = setTimeout((function() { g(e, "blur") }))) : g(e, "mouseLeave")) },
                D = function(e) { "mouseenter" === e.type && w && w(e), "focus" === e.type && y && y(e), clearTimeout(H.current), x && !A && (e.persist(), H.current = setTimeout((function() { x(e, { focus: "focus", mouseenter: "mouseEnter" } [e.type]) }))) },
                F = n.replace(/^[^a-z]+|[^\w:.-]+/gi, ""),
                N = a.Children.toArray(s).filter((function(e) { return a.isValidElement(e) })),
                _ = N.map((function(e, t) { var n = e.props.FabProps,
                        r = (n = void 0 === n ? {} : n).ref,
                        o = (0, Oj.A)(n, ["ref"]); return a.cloneElement(e, { FabProps: (0, l.default)({}, o, { ref: R(t, r) }), delay: 30 * (A ? t : N.length - t), open: A, id: "".concat(F, "-action-").concat(t) }) })); return a.createElement("div", (0, l.default)({ className: (0, Rj.A)(c.root, c["direction".concat((0, CM.A)(h))], d), ref: t, role: "presentation", onKeyDown: function(e) { b && b(e); var t = e.key.replace("Arrow", "").toLowerCase(),
                        n = I.current,
                        r = void 0 === n ? t : n; if ("Escape" !== e.key) { if (Yj(t) === Yj(r) && void 0 !== Yj(t)) { e.preventDefault(); var a = t === r ? 1 : -1,
                                o = function(e, t, n) { return e < t ? t : e > n ? n : e }(L.current + a, 0, j.current.length - 1);
                            j.current[o].focus(), L.current = o, I.current = r } } else g && (j.current[0].focus(), g(e, "escapeKeyDown")) }, onBlur: P, onFocus: D, onMouseEnter: D, onMouseLeave: P }, T), a.createElement(S, (0, l.default)({ in: !p, timeout: E, unmountOnExit: !0 }, C), a.createElement(Kj, (0, l.default)({ color: "primary", "aria-label": n, "aria-haspopup": "true", "aria-expanded": A, "aria-controls": "".concat(F, "-actions") }, i, { onClick: function(e) { i.onClick && i.onClick(e), clearTimeout(H.current), A ? g && g(e, "toggle") : x && x(e, "toggle") }, className: (0, Rj.A)(c.fab, i.className), ref: O }), a.isValidElement(f) && (0, Zj.A)(f, ["SpeedDialIcon"]) ? a.cloneElement(f, { open: A }) : f)), a.createElement("div", { id: "".concat(F, "-actions"), role: "menu", "aria-orientation": Yj(h), className: (0, Rj.A)(c.actions, !A && c.actionsClosed) }, _)) })); const $j = (0, s.A)((function(e) { return { root: { zIndex: e.zIndex.speedDial, display: "flex", alignItems: "center", pointerEvents: "none" }, fab: { pointerEvents: "auto" }, directionUp: { flexDirection: "column-reverse", "& $actions": { flexDirection: "column-reverse", marginBottom: -32, paddingBottom: 48 } }, directionDown: { flexDirection: "column", "& $actions": { flexDirection: "column", marginTop: -32, paddingTop: 48 } }, directionLeft: { flexDirection: "row-reverse", "& $actions": { flexDirection: "row-reverse", marginRight: -32, paddingRight: 48 } }, directionRight: { flexDirection: "row", "& $actions": { flexDirection: "row", marginLeft: -32, paddingLeft: 48 } }, actions: { display: "flex", pointerEvents: "auto" }, actionsClosed: { transition: "top 0s linear 0.2s", pointerEvents: "none" } } }), { name: "MuiSpeedDial" })(Xj); var Qj = n(82454),
            Jj = a.forwardRef((function(e, t) { var n = e.classes,
                    r = e.className,
                    o = e.delay,
                    i = void 0 === o ? 0 : o,
                    s = e.FabProps,
                    c = void 0 === s ? {} : s,
                    d = e.icon,
                    u = e.id,
                    h = e.open,
                    m = e.TooltipClasses,
                    p = e.tooltipOpen,
                    f = void 0 !== p && p,
                    v = e.tooltipPlacement,
                    g = void 0 === v ? "left" : v,
                    y = e.tooltipTitle,
                    b = (0, Oj.A)(e, ["classes", "className", "delay", "FabProps", "icon", "id", "open", "TooltipClasses", "tooltipOpen", "tooltipPlacement", "tooltipTitle"]),
                    w = a.useState(f),
                    z = w[0],
                    x = w[1],
                    A = { transitionDelay: "".concat(i, "ms") },
                    k = a.createElement(Kj, (0, l.default)({ size: "small", className: (0, Rj.A)(n.fab, r, !h && n.fabClosed), tabIndex: -1, role: "menuitem", "aria-describedby": "".concat(u, "-label") }, c, { style: (0, l.default)({}, A, c.style) }), d); return f ? a.createElement("span", (0, l.default)({ id: u, ref: t, className: (0, Rj.A)(n.staticTooltip, n["tooltipPlacement".concat((0, CM.A)(g))], !h && n.staticTooltipClosed) }, b), a.createElement("span", { style: A, id: "".concat(u, "-label"), className: n.staticTooltipLabel }, y), k) : a.createElement(wn.Ay, (0, l.default)({ id: u, ref: t, title: y, placement: g, onClose: function() { x(!1) }, onOpen: function() { x(!0) }, open: h && z, classes: m }, b), k) })); const eV = (0, s.A)((function(e) { return { fab: { margin: 8, color: e.palette.text.secondary, backgroundColor: e.palette.background.paper, "&:hover": { backgroundColor: (0, Qj.tL)(e.palette.background.paper, .15) }, transition: "".concat(e.transitions.create("transform", { duration: e.transitions.duration.shorter }), ", opacity 0.8s"), opacity: 1 }, fabClosed: { opacity: 0, transform: "scale(0)" }, staticTooltip: { position: "relative", display: "flex", "& $staticTooltipLabel": { transition: e.transitions.create(["transform", "opacity"], { duration: e.transitions.duration.shorter }), opacity: 1 } }, staticTooltipClosed: { "& $staticTooltipLabel": { opacity: 0, transform: "scale(0.5)" } }, staticTooltipLabel: (0, l.default)({ position: "absolute" }, e.typography.body1, { backgroundColor: e.palette.background.paper, borderRadius: e.shape.borderRadius, boxShadow: e.shadows[1], color: e.palette.text.secondary, padding: "4px 16px", wordBreak: "keep-all" }), tooltipPlacementLeft: { alignItems: "center", "& $staticTooltipLabel": { transformOrigin: "100% 50%", right: "100%", marginRight: 8 } }, tooltipPlacementRight: { alignItems: "center", "& $staticTooltipLabel": { transformOrigin: "0% 50%", left: "100%", marginLeft: 8 } } } }), { name: "MuiSpeedDialAction" })(Jj); var tV; const nV = (0, ee.Ay)($j)(tV || (tV = (0, J.A)(["\n  max-width: 50px;\n"]))),
            rV = e => { let { result: t, handlePrimaryAction: n } = e; const [r, o] = (0, a.useState)(!1), i = (0, Cn.Bx)(t), l = (0, Cn.zY)(t), s = (0, Cn.v8)(t), c = (0, Cn.rx)(t), d = ge.A.MemberFieldValidators.phone(s) && s, u = ge.A.MemberFieldValidators.email(l) && l, h = (0, Tn.RP)("".concat(i), 35), m = (0, Tn.RP)(c || u || d || "", 100); return (0, we.jsxs)(se.A, { bgcolor: "white", px: 2, py: 1, display: "flex", flexDirection: "row", gridGap: 8, alignItems: "center", borderRadius: "5px", onClick: n, button: !0, children: [(0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(eH.A, { variant: "circle", src: (null === t || void 0 === t ? void 0 : t.photo) || "", name: i, width: 35, height: 35, fontSize: .9 }) }), (0, we.jsxs)(se.A, { flex: 1, display: "flex", flexDirection: "column", overflow: "hidden", children: [(0, we.jsx)(Ij, { children: h }), (0, we.jsx)(jj, { children: m })] }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, position: "relative", children: [(0, we.jsx)(Vj.A, { open: r, style: { zIndex: 1 }, onClick: e => { e.stopPropagation() } }), (0, we.jsxs)(nV, { ariaLabel: "Contact Person", hidden: !(u || d), icon: (0, we.jsx)(Ee.Ay, { icon: "AddressBook" }), onClose: e => { e.preventDefault(), e.stopPropagation(), o(!1) }, onOpen: e => { e.stopPropagation(), o(!0) }, open: r, direction: "left", FabProps: { component: "a", size: "small", variant: "extended", color: "primary" }, children: [!!u && (0, we.jsx)(eV, { onClick: e => { e.stopPropagation() }, icon: (0, we.jsx)(Tr.A, { color: "primary", size: "small", href: "mailto:".concat(u), onClick: e => { e.stopPropagation() }, children: (0, we.jsx)(Ee.Ay, { icon: "Email" }) }) }), !!d && (0, we.jsx)(eV, { onClick: e => { e.stopPropagation() }, icon: (0, we.jsx)(Tr.A, { color: "primary", size: "small", href: "sms:".concat(d), onClick: e => { e.stopPropagation() }, children: (0, we.jsx)(Ee.Ay, { icon: "Message" }) }) }), !!d && (0, we.jsx)(eV, { onClick: e => { e.stopPropagation() }, icon: (0, we.jsx)(Tr.A, { color: "primary", size: "small", href: "tel:".concat(d), onClick: e => { e.stopPropagation() }, children: (0, we.jsx)(Ee.Ay, { icon: "PhoneSolid" }) }) })] })] })] }) }; var aV, oV, iV, lV = n(85899);
        (0, ee.Ay)(je.A)(aV || (aV = (0, J.A)(["\n  color: #000000;\n  font-size: 14px;\n  font-weight: 400;\n"]))), (0, ee.Ay)(je.A)(oV || (oV = (0, J.A)(["\n  color: #666666;\n  font-size: 13px;\n  font-weight: 400;\n"]))); const sV = (0, ee.Ay)(je.A)(iV || (iV = (0, J.A)(["\n  color: #aaaaaa;\n  font-size: 12px;\n  font-weight: 400;\n"]))),
            cV = [{ value: Cn.x2.FIRSTNAME, label: "First Name" }, { value: Cn.x2.LASTNAME, label: "Last Name" }],
            dV = () => { const e = (0, ae.wA)(),
                    t = (0, a.useRef)(),
                    { orgId: n } = (0, Sj.g)(),
                    [, r] = (0, un.A)("isDesktopMode", !1, !0),
                    o = (0, rr.A)({ orgId: n }),
                    { query: i, people: l, loading: s, totalResults: c, handleInputChange: d, handleLoadNextPage: u, handleLoadPrevPage: h, handleSortChange: m, curPage: p, sortBy: f, sortOrder: v, handleSortOrderChange: g } = (0, lV.A)({ emptySearch: !0, searchOrg: n, chunkSize: 50, persist: !0, defaultSort: Cn.x2.FIRSTNAME }),
                    y = 1 === v ? f : "-".concat(f),
                    b = t => () => { e((0, $S.gN)({ mode: "view", activePersonId: t.id })) };
                (0, a.useEffect)((() => { null !== t && void 0 !== t && t.current && t.current.scrollTo(0, 0) }), [p]); const w = parseInt(c / 50),
                    z = (l.length || 0) < (c || 0),
                    [x, A] = (0, a.useState)(null),
                    k = Boolean(x),
                    S = e => () => { if (e === f.replace("-", "")) return -1 === v ? (m(Cn.x2.FIRSTNAME), g(1)) : (m("-".concat(Cn.x2.FIRSTNAME)), g(-1)), void A(null);
                        m(e), g(1), A(null) },
                    M = () => (0, we.jsx)(Aj.A, { open: k, onClose: () => A(null), variant: "selectedMenu", anchorEl: x, anchorOrigin: { vertical: "bottom", horizontal: "center" }, children: cV.map((e => (0, we.jsx)(Ze.A, { value: e.value, selected: y.includes(e.value), onClick: S(e.value), children: e.label }))) }),
                    E = s || o; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(se.A, { bgcolor: "#ffffff", pt: 2, pb: 1, px: 2, display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsxs)(se.A, { flexGrow: 1, children: [(0, we.jsx)(bI.A, { placeholder: "Person Name", fullWidth: !0, size: "small", label: "Search", autoFocus: !0, variant: "outlined", onChange: d, InputProps: { startAdornment: (0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(Ee.Ay, { icon: "Search" }) }) } }), i && !E && (0, we.jsx)(se.A, { my: 1, children: (0, we.jsxs)(sV, { children: ["Showing ", l.length, " of ", c] }) })] }), (0, we.jsx)(Tr.A, { onClick: e => { A(e.currentTarget) }, children: (0, we.jsx)(Ee.Ay, { icon: "Sort", size: "sm" }) })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", overflow: "auto", flex: 1, ref: t, children: [E && Array(50).fill(0).map(((e, t) => (0, we.jsxs)(se.A, { bgcolor: "white", p: 2, display: "flex", flexDirection: "row", gridGap: 8, alignItems: "center", borderRadius: "5px", children: [(0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(tr.A, { variant: "circle", width: 40, height: 40 }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, we.jsx)(tr.A, { variant: "text", width: 200 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 })] })] }, "people-mobile-skeleton-".concat(t)))), !E && l.length > 0 && l.map(((e, t) => (0, we.jsx)(rV, { result: e, handlePrimaryAction: b(e) }, "".concat(null === e || void 0 === e ? void 0 : e.id, "-").concat(t)))), !E && 0 === l.length && (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 2, children: (0, we.jsx)(jn.A, { image: Lj.A, text: i ? "No Results Found" : "No members created under this Organization. Please add a member from Desktop view to get started.", textVariant: "body1", handleClick: () => { r(!0) }, buttonText: "Use Desktop App", isButtonVisible: !0 }) })] }), z && (0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", mx: 2, alignItems: "center", py: 1, children: [(0, we.jsx)($e.A, { onClick: h, size: "small", disabled: E || p <= 1, children: "Prev Page" }), (0, we.jsx)($e.A, { onClick: u, size: "small", disabled: E || p >= w, children: "Next Page" })] }), (0, we.jsx)(M, {})] }) }; var uV = n(53629),
            hV = n(85571),
            mV = n(63560); const pV = () => { const e = (0, ae.d4)(hV.yV),
                { notificationsLoading: t, fetchedNotifications: n, handleNotificationDialogOpen: r } = (0, mV.A)(); return (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", flex: 1, children: (0, we.jsx)(se.A, { overflow: "auto", px: 2, py: 2, children: (0, we.jsx)(dn.A, { loading: t && !n, spinnerSize: 40, title: "Loading notifications...", children: 0 === (null === e || void 0 === e ? void 0 : e.length) ? (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", height: "100%", gridGap: 8, children: [(0, we.jsx)(Ee.Ay, { icon: "Flag", size: "x2" }), (0, we.jsx)(je.A, { variant: "caption", children: "No notifications" })] }, "no-notifications") : (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 8, children: null === e || void 0 === e ? void 0 : e.map((e => (0, we.jsx)(se.A, { bgcolor: "white", p: 1, borderRadius: "10px", children: (0, we.jsx)(uV.A, { notification: e, handleDialogOpen: r(e), closeNotificationPanel: () => {} }, "notification-" + (null === e || void 0 === e ? void 0 : e.id)) }))) }, "notifications-container") }) }) }) }; var fV, vV, gV, yV, bV, wV, zV, xV, AV = n(54762),
            kV = n(58550); const SV = (0, ee.Ay)(je.A)(fV || (fV = (0, J.A)(["\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n"]))),
            MV = (0, ee.Ay)(je.A)(vV || (vV = (0, J.A)(["\n  font-size: 14px;\n  font-weight: 400;\n"]))),
            EV = ((0, ee.Ay)(je.A)(gV || (gV = (0, J.A)(["\n  font-size: 16px;\n  color: #444444;\n  text-align: center;\n"]))), (0, ee.Ay)(je.A)(yV || (yV = (0, J.A)(["\n  color: #000000;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n"])))),
            CV = (0, ee.Ay)(je.A)(bV || (bV = (0, J.A)(["\n  color: #000000;\n  font-size: 14px;\n"]))),
            TV = (0, ee.Ay)(je.A)(wV || (wV = (0, J.A)(["\n  color: #666666;\n  font-weight: 400;\n  display: block;\n"]))),
            HV = (0, ee.Ay)(je.A)(zV || (zV = (0, J.A)(["\n  font-size: 16px;\n  color: #000000;\n"]))),
            LV = (0, ee.Ay)(je.A)(xV || (xV = (0, J.A)(["\n  font-size: 14px;\n  ", "\n"])), (e => { let { theme: { palette: { primary: t } } } = e; return "\n  color:".concat(t.main, ";\n  ") })),
            IV = e => { let { title: t, children: n, onClose: r } = e; return (0, we.jsxs)(se.A, { zIndex: 2, position: "fixed", left: 0, right: 0, top: 0, bottom: 0, bgcolor: "#ffffff", display: "flex", flexDirection: "column", gridGap: 16, p: 2, children: [(0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", alignItems: "center", borderBottom: "solid 1px #ccc", pb: 2, children: [(0, we.jsx)(EV, { children: t || "Settings" }), (0, we.jsx)(Tr.A, { onClick: r, size: "small", children: (0, we.jsx)(Ee.Ay, { icon: "Close" }) })] }), n] }) },
            jV = e => { let { handleCloseForm: t } = e; const { show: n } = (0, Un.A)(), r = (0, ae.d4)(Fr.VW), a = (0, ae.wA)(), { register: o, handleSubmit: i, errors: l } = (0, oe.mN)({ defaultValues: { firstName: r.firstName, lastName: r.lastName } }), [s, c] = (0, he.A)(i((async e => { const { error: t } = await a(me.ip.updateProfile({ data: e }));
                    t || n("Success", "success", 3e3) }))); return (0, we.jsx)(IV, { title: "User Profile", onClose: t, children: (0, we.jsx)("form", { onSubmit: c, style: { flex: 1, display: "flex" }, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "space-between", flex: 1, children: [(0, we.jsx)(se.A, { overflow: "auto", flex: 1, children: (0, we.jsxs)(se.A, { my: 2, gridGap: 8, display: "flex", flexDirection: "column", children: [(0, we.jsx)(se.A, { children: (0, we.jsx)(Ve.A, { label: "First Name", name: "firstName", inputRef: o({ required: !0 }), error: !!l.firstName, fullWidth: !0 }) }), (0, we.jsx)(se.A, { children: (0, we.jsx)(Ve.A, { label: "Last Name", name: "lastName", inputRef: o({ required: !0 }), error: !!l.lastName, fullWidth: !0 }) }), (0, we.jsx)(se.A, { children: (0, we.jsx)(Ve.A, { label: "Email", name: "email", error: !!l.lastName, fullWidth: !0, defaultValue: r.username, disabled: !0 }) }), (0, we.jsx)(se.A, { mr: 1, mt: 1, children: (0, we.jsx)(kV.A, {}) })] }) }), (0, we.jsxs)(se.A, { my: 2, display: "flex", gridGap: 16, justifyContent: "center", alignItems: "center", children: [(0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", fullWidth: !0, onClick: t, children: "Cancel" }), (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", fullWidth: !0, disabled: s || !!l.firstName || !!l.lastName, type: "submit", children: "Save" })] })] }) }) }) }; var VV = n(3523); const OV = e => { let { handleCloseForm: t } = e; const n = (0, ae.wA)(),
                    [r, o] = (0, a.useState)(!1),
                    { params: i } = (0, re.u)((0, ne.BC)()),
                    { orgId: l } = i || {},
                    s = (0, Kt.A)(),
                    { name: c, website: d, description: u, logo: h } = s,
                    { register: m, handleSubmit: p, setValue: f, watch: v, errors: g } = (0, oe.mN)({ defaultValues: { name: c, logo: h, website: d, description: u } }),
                    y = p((async e => { const r = (e => { let { website: t, name: n, description: r, logo: a } = e; return { website: t, name: n.trim(), description: r, logo: a } })(e);
                        r.name ? (o(!1), await n(nn.UY.update({ orgId: l, data: r })), t()) : o(!0) })),
                    [b, w] = (0, he.A)(y); return (0, a.useEffect)((() => { m("logo"), f("logo", h) }), []), (0, we.jsx)(IV, { onClose: t, title: "Organization Info", children: (0, we.jsx)("form", { onSubmit: w, style: { flex: 1, display: "flex" }, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, justifyContent: "space-between", flex: 1, children: [(0, we.jsxs)(se.A, { overflow: "auto", flex: 1, children: [(0, we.jsx)(se.A, { display: "flex", alignItems: "center", flexDirection: "column", gridGap: 16, children: (0, we.jsx)(we.Fragment, { children: v("logo") ? (0, we.jsx)("img", { variant: "square", src: v("logo"), style: { border: "solid 2px #dddddd", borderRadius: "8px", objectFit: "contain", width: 100, height: 100, maxHeight: 100 } }, "uploadedLogo") : (0, we.jsx)(uM.A, { width: 100, height: 100, children: (0, we.jsx)(Ee.Ay, { icon: "Building", size: "x2" }, "defaultIconBuilding") }, "defaultIcon") }) }), (0, we.jsxs)(se.A, { my: 1, children: [(0, we.jsx)(Ve.A, { name: "name", defaultValue: c, label: "Organization Name", fullWidth: !0, inputRef: m({ required: !0 }), error: g.name }), (0, we.jsx)(Ve.A, { name: "description", defaultValue: u, label: "Description", fullWidth: !0, inputRef: m }), (0, we.jsx)(Ve.A, { name: "website", inputRef: m({ pattern: VV.Hp }), defaultValue: d, label: "Website", fullWidth: !0, error: g.website })] })] }), (0, we.jsxs)(se.A, { my: 2, display: "flex", gridGap: 16, justifyContent: "center", alignItems: "center", children: [(0, we.jsx)(Ge.A, { variant: "outlined", onClick: t, disabled: b, fullWidth: !0, children: "Cancel" }), (0, we.jsx)(Ge.A, { type: "Submit", variant: "contained", color: "primary", disabled: b, fullWidth: !0, children: "Save" })] })] }) }) }) },
            RV = e => { let { membersUsed: t, membersAvailable: n, chartsUsed: r, chartsAvailable: a, fieldsUsed: o, fieldsAvailable: i, adminsUsed: l, adminsAvailable: s, snapshotsUsed: c, snapshotsAvailable: d } = e; const u = [{ name: "People", icon: "Directory", used: t, available: n }, { name: "Charts", icon: "Chart", used: r, available: a }, { name: "Fields", icon: "Field", used: o, available: i }, { name: "Admins", icon: "UserAdmin", used: l, available: s }, { name: "Backups", icon: "UserAdmin", used: c, available: d }]; return (0, we.jsx)(se.A, { display: "flex", gridGap: 8, flexDirection: "column", children: u.map((e => (0, we.jsxs)(se.A, { display: "flex", container: !0, alignItems: "center", gridGap: 16, children: [(0, we.jsx)(SV, { children: e.name }), (0, we.jsxs)(MV, { children: [e.used || 0, " of ", e.available] })] }, e.name))) }) },
            PV = e => { var t, n, r, a, o, i, l, s, c, d, u, h, m, p, f, v; let { handleCloseForm: g } = e; const { t: y } = (0, ie.B)(), b = (0, ae.d4)(IH.A), { userHasMinAccess: w, userType: z } = (0, Ut.A)(), x = w(Wt.td.OWNER), A = (0, Kt.A)(), k = "trialing" === b.status, S = "standard" === (null === b || void 0 === b ? void 0 : b.type) ? "BASIC" : (null === b || void 0 === b ? void 0 : b.type.toUpperCase()) || "", M = k ? null === b || void 0 === b || null === (t = b.trialInfo) || void 0 === t ? void 0 : t.planName : "".concat((0, Tn.Sn)(S), " Plan") || 0, E = k ? "".concat(null === b || void 0 === b || null === (n = b.trialInfo) || void 0 === n ? void 0 : n.daysRemaining, " ").concat(1 === (null === b || void 0 === b || null === (r = b.trialInfo) || void 0 === r ? void 0 : r.daysRemaining) ? "day" : "days", " remaining") : (0, we.jsxs)(je.A, { variant: "inherit", children: ["Up to ", (null === b || void 0 === b ? void 0 : b.limits.members) || 50, " People,\xa0", (null !== b && void 0 !== b && null !== (a = b.plan) && void 0 !== a && a.frequency && null !== b && void 0 !== b && b.isAnnualPlan ? "Annual Plan" : "Monthly Plan") || "Annual Plan"] }); let C = {};
                null !== b && void 0 !== b && b.companyName || (C = { color: "#cccccc" }); const T = A.name,
                    H = null === b || void 0 === b ? void 0 : b.tempAccessUntil,
                    L = H && new Date(H).toLocaleString(); return (0, we.jsx)(IV, { title: "License Info", onClose: g, children: (0, we.jsx)("form", { onSubmit: () => {}, style: { flex: 1, display: "flex" }, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "space-between", flex: 1, children: [(0, we.jsx)(se.A, { overflow: "auto", flex: 1, children: (0, we.jsxs)(se.A, { my: 2, gridGap: 8, display: "flex", flexDirection: "column", children: [(0, we.jsx)(se.A, { children: (0, we.jsx)(je.A, { variant: "body", color: "primary", weight: "medium", children: "Plan Information" }) }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsx)(SV, { minWidth: 140, children: M }), (0, we.jsx)(MV, { children: E })] }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsx)(SV, { minWidth: 140, children: "Access Level" }), (0, we.jsx)(MV, { variant: "body2", weight: "medium", color: x ? "textPrimary" : rH.A.palette.info.main, display: "inline", children: (0, Tn.ZH)(z) })] }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsx)(SV, { minWidth: 140, children: "Account Number" }), (0, we.jsx)(MV, { variant: "body2", fontSize: "12px", display: "inline", children: b.id })] }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsxs)(SV, { minWidth: 140, children: ["Account Name \xa0", (0, we.jsx)(wn.Ay, { disableFocusListener: !0, arrow: !0, title: y("Definitions.AccountName"), placement: "bottom", children: (0, we.jsx)(Tr.A, { size: "small", children: (0, we.jsx)(Ee.Ay, { icon: "Help" }) }) })] }), (0, we.jsxs)(MV, { maxWidth: 260, align: "left", variant: "body2", fontSize: "12px", display: "inline", ...C, children: [(null === b || void 0 === b ? void 0 : b.companyName) || "< Not provided >", " \xa0", " "] })] }), T && (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsx)(SV, { minWidth: 140, children: "Organization" }), (0, we.jsx)(MV, { variant: "body2", fontSize: "12px", display: "inline", children: T })] }), L && (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, flexDirection: "row", children: [(0, we.jsx)(SV, { minWidth: 140, children: "Temporary Access Until" }), (0, we.jsx)(MV, { variant: "body2", fontSize: "12px", display: "inline", children: L })] }), (0, we.jsx)(ce.A, {}), (0, we.jsx)(se.A, { mt: 2, children: (0, we.jsx)(je.A, { variant: "body", color: "primary", weight: "medium", children: "Plan Usage" }) }), (0, we.jsx)(RV, { membersUsed: null === b || void 0 === b || null === (o = b.usage) || void 0 === o ? void 0 : o.members, membersAvailable: null === b || void 0 === b || null === (i = b.maxLimitsPlaceholder) || void 0 === i ? void 0 : i.members, chartsUsed: null === b || void 0 === b || null === (l = b.usage) || void 0 === l ? void 0 : l.charts, chartsAvailable: null === b || void 0 === b || null === (s = b.maxLimitsPlaceholder) || void 0 === s ? void 0 : s.charts, fieldsUsed: null === b || void 0 === b || null === (c = b.usage) || void 0 === c ? void 0 : c.fields, fieldsAvailable: null === b || void 0 === b || null === (d = b.maxLimitsPlaceholder) || void 0 === d ? void 0 : d.fields, adminsUsed: null === b || void 0 === b || null === (u = b.usage) || void 0 === u ? void 0 : u.admins, adminsAvailable: null === b || void 0 === b || null === (h = b.maxLimitsPlaceholder) || void 0 === h ? void 0 : h.admins, snapshotsUsed: null === b || void 0 === b || null === (m = b.usage) || void 0 === m ? void 0 : m.snapshots, snapshotsAvailable: null === b || void 0 === b || null === (p = b.maxLimitsPlaceholder) || void 0 === p ? void 0 : p.snapshots, orgsUsed: null === b || void 0 === b || null === (f = b.usage) || void 0 === f ? void 0 : f.organizations, orgsAvailable: null === b || void 0 === b || null === (v = b.maxLimitsPlaceholder) || void 0 === v ? void 0 : v.organizations })] }) }), (0, we.jsx)(se.A, { my: 2, display: "flex", gridGap: 16, justifyContent: "center", alignItems: "center", children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", fullWidth: !0, onClick: g, children: "Close" }) })] }) }) }) },
            DV = "user",
            FV = "license",
            NV = "organization",
            _V = () => { const [e, t] = (0, a.useState)(null), n = (0, ae.d4)(AV.$u), { userHasMinAccess: r } = (0, Ut.A)(), o = r(Wt.td.ADMIN), i = r(Wt.td.OWNER), l = e => () => { t(e) }, s = () => { t(null) }; return (0, we.jsxs)(se.A, { p: 2, display: "flex", flexDirection: "column", gridGap: 16, children: [(0, we.jsxs)(se.A, { bgcolor: "white", borderRadius: "10px", p: 2, display: "flex", flexDirection: "column", gridGap: 16, alignItems: "center", justifyContent: "center", children: [(0, we.jsx)(eH.A, { src: "", variant: "circle", height: 80, width: 80, name: null === n || void 0 === n ? void 0 : n.name, fontSize: 2 }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, we.jsx)(HV, { children: null === n || void 0 === n ? void 0 : n.name }), (0, we.jsx)(LV, { variant: "body2", children: null === n || void 0 === n ? void 0 : n.username })] })] }), (0, we.jsxs)(se.A, { bgcolor: "white", borderRadius: "10px", p: 2, display: "flex", flexDirection: "column", gridGap: 8, children: [(0, we.jsxs)(se.A, { display: "flex", gridGap: 16, alignItems: "center", py: 1, button: !0, onClick: l(DV), children: [(0, we.jsx)(Ee.Ay, { icon: "IT", size: "lg" }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 4, children: [(0, we.jsx)(CV, { children: "User Settings" }), (0, we.jsx)(TV, { variant: "caption", children: "Edit your profile, change your password, and more." })] })] }), o && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(ce.A, {}), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, alignItems: "center", py: 1, button: !0, onClick: l(NV), children: [(0, we.jsx)(Ee.Ay, { icon: "Building", size: "lg" }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 4, children: [(0, we.jsx)(CV, { children: "Organization Settings" }), (0, we.jsx)(TV, { variant: "caption", children: "Edit your organization profile." })] })] })] }), i && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(ce.A, {}), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, alignItems: "center", py: 1, button: !0, onClick: l(FV), children: [(0, we.jsx)(Ee.Ay, { icon: "Consultant", size: "lg" }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 4, children: [(0, we.jsx)(CV, { children: "License Settings" }), (0, we.jsx)(TV, { variant: "caption", children: "Edit your license title, upgrade/downgrade and more." })] })] })] })] }), e === DV && (0, we.jsx)(jV, { handleCloseForm: s }), e === NV && (0, we.jsx)(OV, { handleCloseForm: s }), e === FV && (0, we.jsx)(PV, { handleCloseForm: s })] }) },
            BV = () => { const e = (0, Kt.A)(),
                    [t] = (0, un.A)("activeMobileTab", "charts", !0); return (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", overflow: "auto", flex: 1, children: (() => { switch (t) {
                            case "charts":
                            case "people":
                                return e && (0, we.jsx)(se.A, { flex: 1, overflow: "auto", display: "flex", flexDirection: "column", bgcolor: "white", children: (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { path: (0, ne.r2)(), component: Cj }), (0, we.jsx)($.Route, { path: (0, ne.Zm)(), component: dV }), (0, we.jsx)($.Redirect, { to: (0, ne.r2)({ orgId: null === e || void 0 === e ? void 0 : e.id }) })] }) });
                            case "notifications":
                                return (0, we.jsx)(pV, {});
                            case "settings":
                                return (0, we.jsx)(_V, {});
                            default:
                                return (0, we.jsx)(se.A, { flex: 1, children: "Not handled" }) } })() }) }; var WV = n(14308),
            UV = n(77820),
            qV = n(88159); var GV, KV, ZV = n(74772); const YV = { "layout-1": UV.A, "layout-2": qV.A },
            XV = (0, ee.i7)(GV || (GV = (0, J.A)(["\n  0% {\n    left: 600px;\n  }\n  100% {\n    left: 0;\n  }\n"]))),
            $V = (0, ee.Ay)(se.A)(KV || (KV = (0, J.A)(["\n  animation: ", " 0.5s linear 1;\n  overflow-y: auto;\n"])), XV),
            QV = () => { var e, t; const n = (0, ae.wA)(),
                    r = (0, Kt.A)(),
                    o = (0, ae.d4)(WV.hK),
                    i = (0, ae.d4)(WV.zN),
                    l = (0, ae.d4)($S.t0),
                    s = (0, ae.d4)($S.FM),
                    c = (0, ae.d4)(WV.Pq),
                    { selectedRoleId: d, activePersonId: u } = s || {},
                    { role: h, person: m, connections: p, loading: f } = function(e) { let { roleId: t, personId: n, chartId: r, orgId: o } = e; const i = (0, ae.wA)(),
                            [l, s] = (0, a.useState)(null),
                            [c, d] = (0, a.useState)(null),
                            [u, h] = (0, a.useState)(null),
                            [m, p] = (0, he.A)((async () => { if (n) { const { payload: e, error: t } = await i(IS.rk.getPerson({ orgId: o, personId: n }));!t && null !== e && void 0 !== e && e.person && s((0, Cn.SB)(e.person)) } else s(null) })),
                            [f, v] = (0, he.A)((async () => { if (t) { const { payload: e, error: n } = await i(jS.Sq.getRole({ roleId: t, chartId: r }));!n && null !== e && void 0 !== e && e.role && d((0, Cn.SB)(e.role)) } else d(null) })),
                            [g, y] = (0, he.A)((async () => { if (t) { const { payload: a, error: o } = await i(jS.Sq.getConnections({ roleId: t, chartId: r })); if (!o && null !== a && void 0 !== a && a.connections) { var e, n; const t = { ...a.connections };
                                        null !== t && void 0 !== t && t.manager && (t.manager = (0, Cn.SB)(t.manager)), null !== t && void 0 !== t && null !== (e = t.direct) && void 0 !== e && e.length && (t.direct = (t.direct || []).map((e => (0, Cn.SB)(e)))), null !== t && void 0 !== t && null !== (n = t.indirect) && void 0 !== n && n.length && (t.indirect = (t.indirect || []).map((e => (0, Cn.SB)(e)))), h(t) } } else h(null) })); return (0, a.useEffect)((() => { p() }), [n, o]), (0, a.useEffect)((() => { v() }), [t, o]), (0, a.useEffect)((() => { y() }), [t, o]), { role: c, person: l, connections: u, loading: m || f || g } }({ roleId: d, personId: u, orgId: null === r || void 0 === r ? void 0 : r.id, chartId: c }),
                    v = null === i || void 0 === i || null === (e = i.layout) || void 0 === e ? void 0 : e.name,
                    g = ZV.A.find((e => e.id === v)),
                    y = (0, ae.d4)(WV.Dy)("single", null === g || void 0 === g ? void 0 : g.fields),
                    b = (0, ae.d4)(WV.wB)("single", null === g || void 0 === g ? void 0 : g.fields),
                    w = v ? YV[v] : null,
                    z = (null === h || void 0 === h || null === (t = h.fields) || void 0 === t ? void 0 : t.length) && (0, Cn.dP)(h.fields || [], "name") || null; return l && (0, we.jsx)($V, { position: "fixed", display: "flex", flexDirection: "column", top: 0, left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", gridGap: 16, zIndex: 99999, children: (0, we.jsx)(se.A, { flex: 1, children: (0, we.jsx)(dn.A, { loading: f, transparent: !0, title: "loading...", children: (0, we.jsx)(w, { isMobile: !0, role: h, member: m, allRolePeople: (null === h || void 0 === h ? void 0 : h.members) || [], handlePrevSharedPerson: () => { const e = ((null === h || void 0 === h ? void 0 : h.members) || []).findIndex((e => e === u)); if (e > -1) { const t = (h.members.length + e - 1) % h.members.length,
                                            r = h.members[t];
                                        n((0, $S.gN)({ selectedRoleId: d, activePersonId: r })) } }, handleNextSharedPerson: () => { const e = ((null === h || void 0 === h ? void 0 : h.members) || []).findIndex((e => e === u)); if (e > -1) { const t = (e + 1) % h.members.length,
                                            r = h.members[t];
                                        n((0, $S.gN)({ selectedRoleId: d, activePersonId: r })) } }, connections: p, roleNameFieldId: z, theme: o, themeFieldsWithStyles: y, themeBannerFieldsWithStyles: b, editAccess: !1, onClick: !0, cardWidth: "100%", onClose: () => { n((0, $S.Ch)()) } }) }) }) }) },
            JV = () => { const e = (0, ae.d4)($S.t0); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { bgcolor: "#f1f1f1", display: "flex", flexDirection: "column", flex: 1, overflow: "auto", children: (0, we.jsx)(BV, {}) }), e && (0, we.jsx)(QV, {})] }) }; var eO, tO, nO; const rO = (0, ee.Ay)(se.A)(eO || (eO = (0, J.A)(["\n  padding: 12px 16px 8px;\n  border-bottom: solid 1px #ccc;\n"]))),
            aO = (0, ee.Ay)(je.A)(tO || (tO = (0, J.A)(["\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  color: #5c2dbf;\n"]))),
            oO = (0, ee.Ay)(se.A)(nO || (nO = (0, J.A)(["\n  font-size: 12px;\n  font-weight: 400;\n  text-transform: uppercase;\n  color: #aaaaaa;\n"]))),
            iO = () => { const e = (0, Sj.W6)(),
                    [t, n] = (0, a.useState)(!1),
                    [r, o] = (0, un.A)("isDesktopMode", !1, !0); return (0, we.jsxs)(rO, { display: "flex", justifyContent: "space-between", p: 2, alignItems: "center", height: Wt.Ay.app.mobile.headerHeight, children: [(0, we.jsx)(Sj.k2, { to: (0, ne.ze)(), children: (0, we.jsx)("img", { width: 160, src: be, alt: "Organimi Logo" }) }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsxs)(oO, { display: "flex", flexDirection: "column", gridGap: 0, alignItems: "flex-end", borderRight: "solid 1px #ccc", pr: 1, children: [(0, we.jsx)("span", { children: "Mobile View" }), (0, we.jsx)(aO, { component: "span", children: "Beta" })] }), (0, we.jsx)(se.A, { children: (0, we.jsx)(Tr.A, { onClick: e => { n(e.currentTarget) }, children: (0, we.jsx)(Ee.Ay, { icon: "HamburgerMenu" }) }) })] }), (0, we.jsxs)(Aj.A, { open: !!t, anchorEl: t, onClose: () => { n(null) }, children: [(0, we.jsx)(Ze.A, { onClick: () => { n(null), e.push((0, ne.Mz)()) }, children: "Switch Account" }), (0, we.jsx)(Ze.A, { onClick: () => { o(!0) }, children: "Use Desktop App" }), (0, we.jsx)(ce.A, {}), (0, we.jsx)(Ze.A, { children: (0, we.jsx)("a", { href: "".concat("https://app.organimi.com", "/logout"), children: "Logout" }) })] })] }) }; var lO = a.forwardRef((function(e, t) { var n = e.classes,
                r = e.className,
                o = e.icon,
                i = e.label,
                s = e.onChange,
                c = e.onClick,
                d = e.selected,
                u = e.showLabel,
                h = e.value,
                m = (0, Oj.A)(e, ["classes", "className", "icon", "label", "onChange", "onClick", "selected", "showLabel", "value"]); return a.createElement(qj.A, (0, l.default)({ ref: t, className: (0, Rj.A)(n.root, r, d ? n.selected : !u && n.iconOnly), focusRipple: !0, onClick: function(e) { s && s(e, h), c && c(e) } }, m), a.createElement("span", { className: n.wrapper }, o, a.createElement("span", { className: (0, Rj.A)(n.label, d ? n.selected : !u && n.iconOnly) }, i))) })); const sO = (0, s.A)((function(e) { return { root: { transition: e.transitions.create(["color", "padding-top"], { duration: e.transitions.duration.short }), padding: "6px 12px 8px", minWidth: 80, maxWidth: 168, color: e.palette.text.secondary, flex: "1", "&$iconOnly": { paddingTop: 16 }, "&$selected": { paddingTop: 6, color: e.palette.primary.main } }, selected: {}, iconOnly: {}, wrapper: { display: "inline-flex", alignItems: "center", justifyContent: "center", width: "100%", flexDirection: "column" }, label: { fontFamily: e.typography.fontFamily, fontSize: e.typography.pxToRem(12), opacity: 1, transition: "font-size 0.2s, opacity 0.2s", transitionDelay: "0.1s", "&$iconOnly": { opacity: 0, transitionDelay: "0s" }, "&$selected": { fontSize: e.typography.pxToRem(14) } } } }), { name: "MuiBottomNavigationAction" })(lO); var cO = a.forwardRef((function(e, t) { var n = e.children,
                r = e.classes,
                o = e.className,
                i = e.component,
                s = void 0 === i ? "div" : i,
                c = e.onChange,
                d = e.showLabels,
                u = void 0 !== d && d,
                h = e.value,
                m = (0, Oj.A)(e, ["children", "classes", "className", "component", "onChange", "showLabels", "value"]); return a.createElement(s, (0, l.default)({ className: (0, Rj.A)(r.root, o), ref: t }, m), a.Children.map(n, (function(e, t) { if (!a.isValidElement(e)) return null; var n = void 0 === e.props.value ? t : e.props.value; return a.cloneElement(e, { selected: n === h, showLabel: void 0 !== e.props.showLabel ? e.props.showLabel : u, value: n, onChange: c }) }))) })); const dO = (0, s.A)((function(e) { return { root: { display: "flex", justifyContent: "center", height: 56, backgroundColor: e.palette.background.paper } } }), { name: "MuiBottomNavigation" })(cO); var uO, hO = n(44235); const mO = (0, ee.Ay)(sO)(uO || (uO = (0, J.A)(["\n  .MuiBottomNavigationAction-wrapper {\n    grid-gap: 4px;\n  }\n  .MuiBottomNavigationAction-root {\n    min-width: 70px;\n    overflow: hidden;\n  }\n"]))),
            pO = "settings",
            fO = "charts",
            vO = "people",
            gO = "notifications",
            yO = () => { const [e, t] = (0, un.A)("activeMobileTab", "charts", !0), n = (0, $.useHistory)(), { orgId: r } = (0, $.useParams)(), a = (0, Kt.A)(), o = (0, ae.d4)(hV.BT), i = (e, o) => { switch (t(o), o) {
                        case "charts":
                        case "notifications":
                            n.push((0, ne.r2)({ orgId: r || (null === a || void 0 === a ? void 0 : a.id) })); break;
                        case "people":
                            n.push((0, ne.Zm)({ orgId: null === a || void 0 === a ? void 0 : a.id })); break;
                        case "settings":
                            n.push((0, ne.$u)({ orgId: r || (null === a || void 0 === a ? void 0 : a.id) })) } }; return (0, we.jsxs)(dO, { value: e, showLabels: !0, children: [(0, we.jsx)(mO, { onClick: e => i(0, fO), value: "charts", label: "Charts", icon: (0, we.jsx)(Ee.Ay, { icon: "Chart", selected: e === fO }) }), (0, we.jsx)(mO, { onClick: e => i(0, vO), value: "people", label: "People", icon: (0, we.jsx)(Ee.Ay, { icon: "User", selected: e === vO }) }), (0, we.jsx)(mO, { onClick: e => i(0, gO), value: "notifications", label: "Alerts", icon: (0, we.jsx)(hO.A, { variant: "dot", badgeContent: o, color: "primary", overlap: "rectangular", children: (0, we.jsx)(Ee.Ay, { icon: "Bell", selected: e === gO }) }) }), (0, we.jsx)(mO, { onClick: e => i(0, pO), value: "settings", label: "Settings", icon: (0, we.jsx)(Ee.Ay, { icon: "Settings", selected: e === pO }) })] }) }; var bO = n(19367),
            wO = n(25492),
            zO = n(299); const xO = function(e) { return (0, we.jsx)(zO.A, { fontSize: "inherit", style: { width: 14, height: 14 }, ...e, children: (0, we.jsx)("path", { d: "M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 11.023h-11.826q-.375 0-.669.281t-.294.682v0q0 .401.294 .682t.669.281h11.826q.375 0 .669-.281t.294-.682v0q0-.401-.294-.682t-.669-.281z" }) }) }; const AO = function(e) { return (0, we.jsx)(zO.A, { fontSize: "inherit", style: { width: 14, height: 14 }, ...e, children: (0, we.jsx)("path", { d: "M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 12.977h-4.923v4.896q0 .401-.281.682t-.682.281v0q-.375 0-.669-.281t-.294-.682v-4.896h-4.923q-.401 0-.682-.294t-.281-.669v0q0-.401.281-.682t.682-.281h4.923v-4.896q0-.401.294-.682t.669-.281v0q.401 0 .682.281t.281.682v4.896h4.923q.401 0 .682.281t.281.682v0q0 .375-.281.669t-.682.294z" }) }) }; var kO = n(23993),
            SO = n(45418),
            MO = (n(89471), n(74079)); const EO = kO.vv,
            CO = (0, Dr.Mz)(SO.xt, (e => t => t.map((t => e[t])).filter((e => e)))),
            TO = e => !!e.search.selectedResult,
            HO = (0, Dr.Mz)(qT.A, (e => { var t; return null === e || void 0 === e || null === (t = e.chartOptions) || void 0 === t ? void 0 : t.vacantText })); var LO = n(51051); const IO = a.createContext({}); var jO = function(e, t, n) { for (var r = t; r < e.length; r += 1)
                    if (n === e[r]) return r; return -1 },
            VO = [],
            OO = [],
            RO = a.forwardRef((function(e, t) { var n = e.children,
                    r = e.classes,
                    o = e.className,
                    i = e.defaultCollapseIcon,
                    s = e.defaultEndIcon,
                    c = e.defaultExpanded,
                    d = void 0 === c ? VO : c,
                    u = e.defaultExpandIcon,
                    h = e.defaultParentIcon,
                    m = e.defaultSelected,
                    p = void 0 === m ? OO : m,
                    f = e.disableSelection,
                    v = void 0 !== f && f,
                    g = e.multiSelect,
                    y = void 0 !== g && g,
                    b = e.expanded,
                    w = e.onNodeSelect,
                    z = e.onNodeToggle,
                    x = e.selected,
                    A = (0, Oj.A)(e, ["children", "classes", "className", "defaultCollapseIcon", "defaultEndIcon", "defaultExpanded", "defaultExpandIcon", "defaultParentIcon", "defaultSelected", "disableSelection", "multiSelect", "expanded", "onNodeSelect", "onNodeToggle", "selected"]),
                    k = a.useState(null),
                    S = k[0],
                    M = k[1],
                    E = a.useState(null),
                    C = E[0],
                    T = E[1],
                    H = a.useRef({}),
                    L = a.useRef({}),
                    I = a.useRef([]),
                    j = (0, LO.A)({ controlled: b, default: d, name: "TreeView", state: "expanded" }),
                    V = (0, Dj.A)(j, 2),
                    O = V[0],
                    R = V[1],
                    P = (0, LO.A)({ controlled: x, default: p, name: "TreeView", state: "selected" }),
                    D = (0, Dj.A)(P, 2),
                    F = D[0],
                    N = D[1],
                    _ = a.useCallback((function(e) { return !!Array.isArray(O) && -1 !== O.indexOf(e) }), [O]),
                    B = a.useCallback((function(e) { return Array.isArray(F) ? -1 !== F.indexOf(e) : F === e }), [F]),
                    W = function(e) { var t = I.current.indexOf(e); return -1 !== t && t + 1 < I.current.length ? I.current[t + 1] : null },
                    U = function(e) { var t = I.current.indexOf(e); return -1 !== t && t - 1 >= 0 ? I.current[t - 1] : null },
                    q = function() { return I.current[I.current.length - 1] },
                    G = function() { return I.current[0] },
                    K = function(e) { e && (M(e), T(e)) },
                    Z = a.useRef(null),
                    Y = a.useRef(!1),
                    X = a.useRef([]),
                    $ = function(e, t) { var n = F,
                            r = t.start,
                            a = t.end;
                        Y.current && (n = F.filter((function(e) { return -1 === X.current.indexOf(e) }))); var o = function(e, t) { var n = I.current.indexOf(e),
                                r = I.current.indexOf(t),
                                a = Math.min(n, r),
                                o = Math.max(n, r); return I.current.slice(a, o + 1) }(r, a);
                        X.current = o; var i = n.concat(o);
                        i = i.filter((function(e, t) { return i.indexOf(e) === t })), w && w(e, i), N(i) },
                    Q = function(e, t) { var n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2],
                            r = t.start,
                            a = void 0 === r ? Z.current : r,
                            o = t.end,
                            i = t.current; return n ? function(e, t) { var n = F,
                                r = t.start,
                                a = t.next,
                                o = t.current;
                            a && o && (-1 === X.current.indexOf(o) && (X.current = []), Y.current ? -1 !== X.current.indexOf(a) ? (n = n.filter((function(e) { return e === r || e !== o })), X.current = X.current.filter((function(e) { return e === r || e !== o }))) : (n.push(a), X.current.push(a)) : (n.push(a), X.current.push(o, a)), w && w(e, n), N(n)) }(e, { start: a, next: o, current: i }) : $(e, { start: a, end: o }), Y.current = !0, !0 },
                    J = a.useCallback((function(e) { var t = H.current[e],
                            n = []; return t && (n.push(e), t.children && (n.concat(t.children), t.children.forEach((function(e) { n.concat(J(e)) })))), n }), []),
                    ee = a.useCallback((function(e) { var t = (0, l.default)({}, L.current);
                        e.forEach((function(e) { t[e] && delete t[e] })), L.current = t }), []),
                    te = a.useCallback((function(e) { var t = J(e);
                        ee(t); var n = (0, l.default)({}, H.current);
                        t.forEach((function(e) { var t = n[e]; if (t) { if (t.parent) { var r = n[t.parent]; if (r && r.children) { var a = r.children.filter((function(t) { return t !== e }));
                                        n[t.parent] = (0, l.default)({}, r, { children: a }) } } delete n[e] } })), H.current = n, T((function(t) { return t === e ? null : t })) }), [J, ee]),
                    ne = a.useRef([]),
                    re = a.useState(!1),
                    ae = re[0],
                    oe = re[1];
                a.useEffect((function() { var e = [];
                    a.Children.forEach(n, (function(t) { a.isValidElement(t) && t.props.nodeId && e.push(t.props.nodeId) })),
                        function(e, t) { if (e.length !== t.length) return !0; for (var n = 0; n < e.length; n += 1)
                                if (e[n] !== t[n]) return !0; return !1 }(ne.current, e) && (H.current[-1] = { parent: null, children: e }, e.forEach((function(e, t) { 0 === t && M(e) })), I.current = H.current[-1].children, ne.current = e, oe(!0)) }), [n]), a.useEffect((function() { ae && (I.current = function e(t) { for (var n = [], r = 0; r < t.length; r += 1) { var a = t[r];
                            n.push(a); var o = H.current[a].children;
                            _(a) && o && (n = n.concat(e(o))) } return n }(H.current[-1].children)) }), [O, ae, _, n]); var ie = function() { return !1 }; return a.createElement(IO.Provider, { value: { icons: { defaultCollapseIcon: i, defaultExpandIcon: u, defaultParentIcon: h, defaultEndIcon: s }, focus: K, focusFirstNode: function() { return K(G()) }, focusLastNode: function() { return K(q()) }, focusNextNode: function(e) { return K(W(e)) }, focusPreviousNode: function(e) { return K(U(e)) }, focusByFirstCharacter: function(e, t) { var n, r, a = t.toLowerCase(),
                                o = [],
                                i = [];
                            Object.keys(L.current).forEach((function(e) { var t = L.current[e],
                                    n = H.current[e];
                                (!n.parent || _(n.parent)) && (o.push(e), i.push(t)) })), (n = o.indexOf(e) + 1) === H.current.length && (n = 0), -1 === (r = jO(i, n, a)) && (r = jO(i, 0, a)), r > -1 && K(o[r]) }, expandAllSiblings: function(e, t) { var n, r = H.current[t],
                                a = H.current[r.parent];
                            a ? n = a.children.filter((function(e) { return !_(e) })) : n = H.current[-1].children.filter((function(e) { return !_(e) })); var o = O.concat(n);
                            n.length > 0 && (R(o), z && z(e, o)) }, toggleExpansion: function(e) { var t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : C; - 1 !== O.indexOf(n) ? (t = O.filter((function(e) { return e !== n })), M((function(e) { var t = H.current[e]; return e && (t && t.parent ? t.parent.id : null) === n ? n : e }))) : t = [n].concat(O), z && z(e, t), R(t) }, isExpanded: _, isFocused: function(e) { return C === e }, isSelected: B, selectNode: v ? ie : function(e, t) { return !!t && (arguments.length > 2 && void 0 !== arguments[2] && arguments[2] ? function(e, t) { var n = [];
                                n = -1 !== F.indexOf(t) ? F.filter((function(e) { return e !== t })) : [t].concat(F), w && w(e, n), N(n) }(e, t) : function(e, t) { var n = y ? [t] : t;
                                w && w(e, n), N(n) }(e, t), Z.current = t, Y.current = !1, X.current = [], !0) }, selectRange: v ? ie : Q, selectNextNode: v ? ie : function(e, t) { return Q(e, { end: W(t), current: t }, !0) }, selectPreviousNode: v ? ie : function(e, t) { return Q(e, { end: U(t), current: t }, !0) }, rangeSelectToFirst: v ? ie : function(e, t) { Z.current || (Z.current = t); var n = Y.current ? Z.current : t; return Q(e, { start: n, end: G() }) }, rangeSelectToLast: v ? ie : function(e, t) { Z.current || (Z.current = t); var n = Y.current ? Z.current : t; return Q(e, { start: n, end: q() }) }, selectAllNodes: v ? ie : function(e) { return Q(e, { start: G(), end: q() }) }, isTabbable: function(e) { return S === e }, multiSelect: y, getParent: function(e) { return H.current[e].parent }, mapFirstChar: function(e, t) { L.current[e] = t }, addNodeToNodeMap: function(e, t) { var n = H.current[e];
                            H.current[e] = (0, l.default)({}, n, { children: t, id: e }), t.forEach((function(t) { var n = H.current[t];
                                H.current[t] = (0, l.default)({}, n, { parent: e, id: t }) })) }, removeNodeFromNodeMap: te } }, a.createElement("ul", (0, l.default)({ role: "tree", "aria-multiselectable": y, className: (0, Rj.A)(r.root, o), ref: t }, A), n)) })); const PO = (0, s.A)({ root: { padding: 0, margin: 0, listStyle: "none" } }, { name: "MuiTreeView" })(RO); var DO; const FO = (0, ee.Ay)(PO)(DO || (DO = (0, J.A)(["\n  height: 264px;\n  flex-grow: 1;\n  max-width: 100%;\n  padding: 8px;\n"]))); var NO = n(78781),
            _O = n(66187),
            BO = a.forwardRef((function(e, t) { var n = e.children,
                    r = e.classes,
                    o = e.className,
                    i = e.collapseIcon,
                    s = e.endIcon,
                    c = e.expandIcon,
                    d = e.icon,
                    u = e.label,
                    h = e.nodeId,
                    m = e.onClick,
                    p = e.onLabelClick,
                    f = e.onIconClick,
                    v = e.onFocus,
                    g = e.onKeyDown,
                    y = e.onMouseDown,
                    b = e.TransitionComponent,
                    w = void 0 === b ? br.A : b,
                    z = e.TransitionProps,
                    x = (0, Oj.A)(e, ["children", "classes", "className", "collapseIcon", "endIcon", "expandIcon", "icon", "label", "nodeId", "onClick", "onLabelClick", "onIconClick", "onFocus", "onKeyDown", "onMouseDown", "TransitionComponent", "TransitionProps"]),
                    A = a.useContext(IO),
                    k = A.icons,
                    S = A.focus,
                    M = A.focusFirstNode,
                    E = A.focusLastNode,
                    C = A.focusNextNode,
                    T = A.focusPreviousNode,
                    H = A.focusByFirstCharacter,
                    L = A.selectNode,
                    I = A.selectRange,
                    j = A.selectNextNode,
                    V = A.selectPreviousNode,
                    O = A.rangeSelectToFirst,
                    R = A.rangeSelectToLast,
                    P = A.selectAllNodes,
                    D = A.expandAllSiblings,
                    F = A.toggleExpansion,
                    N = A.isExpanded,
                    _ = A.isFocused,
                    B = A.isSelected,
                    W = A.isTabbable,
                    U = A.multiSelect,
                    q = A.getParent,
                    G = A.mapFirstChar,
                    K = A.addNodeToNodeMap,
                    Z = A.removeNodeFromNodeMap,
                    Y = a.useRef(null),
                    X = a.useRef(null),
                    $ = (0, Nj.A)(Y, t),
                    Q = d,
                    J = Boolean(Array.isArray(n) ? n.length : n),
                    ee = !!N && N(h),
                    te = !!_ && _(h),
                    ne = !!W && W(h),
                    re = !!B && B(h),
                    ae = k || {},
                    oe = (0, le.A)();
                Q || (J ? (Q = ee ? i || ae.defaultCollapseIcon : c || ae.defaultExpandIcon) || (Q = ae.defaultParentIcon) : Q = s || ae.defaultEndIcon); var ie, se = function(e) { return J && (ee ? C(h) : F(e)), !0 },
                    ce = function(e) { if (ee) return F(e, h), !0; var t = q(h); return !!t && (S(t), !0) }; return a.useEffect((function() { if (K) { var e = [];
                        a.Children.forEach(n, (function(t) { a.isValidElement(t) && t.props.nodeId && e.push(t.props.nodeId) })), K(h, e) } }), [n, h, K]), a.useEffect((function() { if (Z) return function() { Z(h) } }), [h, Z]), a.useEffect((function() { G && u && G(h, X.current.textContent.substring(0, 1).toLowerCase()) }), [G, h, u]), a.useEffect((function() { te && Y.current.focus() }), [te]), U ? ie = re : re && (ie = !0), a.createElement("li", (0, l.default)({ className: (0, Rj.A)(r.root, o, ee && r.expanded, re && r.selected), role: "treeitem", onKeyDown: function(e) { var t = !1,
                            n = e.key; if (!e.altKey && e.currentTarget === e.target) { var r, a = e.ctrlKey || e.metaKey; switch (n) {
                                case " ":
                                    Y.current === e.currentTarget && (t = U && e.shiftKey ? I(e, { end: h }) : U ? L(e, h, !0) : L(e, h)), e.stopPropagation(); break;
                                case "Enter":
                                    Y.current === e.currentTarget && J && (F(e), t = !0), e.stopPropagation(); break;
                                case "ArrowDown":
                                    U && e.shiftKey && j(e, h), C(h), t = !0; break;
                                case "ArrowUp":
                                    U && e.shiftKey && V(e, h), T(h), t = !0; break;
                                case "ArrowRight":
                                    t = "rtl" === oe.direction ? ce(e) : se(e); break;
                                case "ArrowLeft":
                                    t = "rtl" === oe.direction ? se(e) : ce(e); break;
                                case "Home":
                                    U && a && e.shiftKey && O(e, h), M(), t = !0; break;
                                case "End":
                                    U && a && e.shiftKey && R(e, h), E(), t = !0; break;
                                default:
                                    "*" === n ? (D(e, h), t = !0) : U && a && "a" === n.toLowerCase() ? t = P(e) : !a && !e.shiftKey && ((r = n) && 1 === r.length && r.match(/\S/)) && (H(h, n), t = !0) } t && (e.preventDefault(), e.stopPropagation()), g && g(e) } }, onFocus: function(e) { te || e.currentTarget !== e.target || S(h), v && v(e) }, "aria-expanded": J ? ee : null, "aria-selected": ie, ref: $, tabIndex: ne ? 0 : -1 }, x), a.createElement("div", { className: r.content, onClick: function(e) { te || S(h); var t = U && (e.shiftKey || e.ctrlKey || e.metaKey);!J || e.defaultPrevented || t && N(h) || F(e, h), t ? e.shiftKey ? I(e, { end: h }) : L(e, h, !0) : L(e, h), m && m(e) }, onMouseDown: function(e) {
                        (e.shiftKey || e.ctrlKey || e.metaKey) && e.preventDefault(), y && y(e) }, ref: X }, a.createElement("div", { onClick: f, className: r.iconContainer }, Q), a.createElement(_O.A, { onClick: p, component: "div", className: r.label }, u)), n && a.createElement(w, (0, l.default)({ unmountOnExit: !0, className: r.group, in: ee, component: "ul", role: "group" }, z), n)) })); const WO = (0, s.A)((function(e) { return { root: { listStyle: "none", margin: 0, padding: 0, outline: 0, WebkitTapHighlightColor: "transparent", "&:focus > $content $label": { backgroundColor: e.palette.action.hover }, "&$selected > $content $label": { backgroundColor: (0, Qj.X4)(e.palette.primary.main, e.palette.action.selectedOpacity) }, "&$selected > $content $label:hover, &$selected:focus > $content $label": { backgroundColor: (0, Qj.X4)(e.palette.primary.main, e.palette.action.selectedOpacity + e.palette.action.hoverOpacity), "@media (hover: none)": { backgroundColor: "transparent" } } }, expanded: {}, selected: {}, group: { margin: 0, padding: 0, marginLeft: 17 }, content: { width: "100%", display: "flex", alignItems: "center", cursor: "pointer" }, iconContainer: { marginRight: 4, width: 15, display: "flex", flexShrink: 0, justifyContent: "center", "& svg": { fontSize: 18 } }, label: { width: "100%", paddingLeft: 4, position: "relative", "&:hover": { backgroundColor: e.palette.action.hover, "@media (hover: none)": { backgroundColor: "transparent" } } } } }), { name: "MuiTreeItem" })(BO);

        function UO(e) { const t = (0, NO.zh)({ from: { opacity: 0, transform: "translate3d(20px,0,0)" }, to: { opacity: e.in ? 1 : 0, transform: "translate3d(".concat(e.in ? 0 : 20, "px,0,0)") } }); return (0, we.jsx)(NO.CS.div, { style: t, children: (0, we.jsx)(br.A, { ...e }) }) } const qO = (0, s.A)((e => ({ iconContainer: { "& .close": { opacity: .3 } }, group: { marginLeft: 5, paddingLeft: 13, borderLeft: "1px dashed ".concat((0, Qj.X4)(e.palette.text.primary, .4)) } })))((e => (0, we.jsx)(WO, { ...e, TransitionComponent: UO }))),
            GO = qO,
            KO = () => (0, we.jsxs)(se.A, { border: "1px solid lightgrey", p: .5, display: "flex", gridGap: 1, justifyContent: "space-between", alignItems: "end", borderRadius: "5px", mb: 1, children: [(0, we.jsxs)(se.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(tr.A, { variant: "circle", width: 35, height: 35 }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 1.5, flexDirection: "column", children: [(0, we.jsx)(tr.A, { variant: "text", width: 100, height: 20 }), (0, we.jsx)(tr.A, { variant: "text", width: 100, height: 20 })] })] }), (0, we.jsx)(se.A, { children: (0, we.jsx)(tr.A, { variant: "text", width: 30, height: 20 }) })] }),
            ZO = e => { var t; let { role: n, members: r, counts: a, bgcolor: o, handleRoleClick: i } = e; const { direct: l, recursive: s } = a || { direct: 0, recursive: 0 }; return (0, we.jsxs)(se.A, { border: "1px solid lightgrey", p: 1, display: "flex", gridGap: 16, justifyContent: "space-between", alignItems: "center", borderRadius: 8, mb: 1, onClick: i(n), borderRight: o ? "solid 8px ".concat(o) : void 0, children: [(0, we.jsxs)(se.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(eH.A, { src: (null === (t = r[0]) || void 0 === t ? void 0 : t.photo) || "", shape: "circle", name: null !== r && void 0 !== r && r.length ? (0, Cn.Bx)(r[0]) : (0, Cn.mA)(n), width: 35, height: 35, fontSize: .9 }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 1.5, flexDirection: "column", children: [(0, we.jsx)(je.A, { weight: "medium", color: "#000", fontSize: "14px", children: (0, Cn.mA)(n) }), null === r || void 0 === r ? void 0 : r.map((e => (0, we.jsx)(je.A, { fontSize: "12px", children: (0, Cn.Bx)(e) })))] })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 16, children: [l > 0 && (0, we.jsxs)(se.A, { color: "#000000", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "User", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "#444444", children: l })] }), !(!l || !s) && (0, we.jsx)(se.A, { color: "#cccccc", children: "|" }), s > 0 && (0, we.jsxs)(se.A, { color: "#000000", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "UserGroup", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "#444444", children: s })] })] })] }) },
            YO = e => { var t; let { chartId: n, role: r, handleRoleClick: a, handleLoadChildrenRolesPending: o, members: i, pendingLoadChildrenRoles: l, bgcolor: s, counts: c } = e; return (0, we.jsx)(GO, { nodeId: null === r || void 0 === r ? void 0 : r.id, collapseIcon: (0, we.jsx)(xO, {}), expandIcon: (0, we.jsx)(AO, {}), label: (0, we.jsx)(ZO, { role: r, members: i, handleRoleClick: a, counts: c, bgcolor: s }), onIconClick: () => o(r), children: (null === r || void 0 === r || null === (t = r.children) || void 0 === t ? void 0 : t.length) > 0 && (0, we.jsx)(XO, { chartId: n, roles: null === r || void 0 === r ? void 0 : r.children, handleRoleClick: a, loadingChildren: l }) }) },
            XO = e => { let { chartId: t, roles: n = [], handleRoleClick: r, loadingChildren: a = !1 } = e; const o = (0, ae.d4)(MO.A),
                    i = (0, ae.d4)(kO.LH),
                    l = (0, ae.d4)(CO),
                    s = (0, ae.wA)(),
                    [c, d] = (0, he.A)((async e => { for (let t = 0; t < e.children.length; t++)
                            if (o[e.children[t]]) return;
                        await s(VS.wz.getChildrenRoles({ chartId: t, roleId: e.id })) })); return n ? a ? null === n || void 0 === n ? void 0 : n.map((e => { if ("string" === typeof e && !o[e]) return (0, we.jsx)(GO, { nodeId: e, label: (0, we.jsx)(KO, {}) }); if ("string" === typeof e && o[e]) { const n = o[e],
                            a = l((null === n || void 0 === n ? void 0 : n.members) || []); return (0, we.jsx)(YO, { chartId: t, role: n, handleRoleClick: r, handleLoadChildrenRolesPending: d, members: a, pendingLoadChildrenRoles: c, counts: 0, bgcolor: "" }) } })) : null === n || void 0 === n ? void 0 : n.map((e => { var n, a, s, u; "string" === typeof e && (e = o[e]); const h = l((null === (n = e) || void 0 === n ? void 0 : n.members) || []); return (0, we.jsx)(YO, { chartId: t, role: e, handleRoleClick: r, handleLoadChildrenRolesPending: d, members: h, pendingLoadChildrenRoles: c, counts: (null === (a = e) || void 0 === a ? void 0 : a.counts) || {}, bgcolor: null === (s = i[null === (u = e) || void 0 === u ? void 0 : u.id]) || void 0 === s ? void 0 : s.bgcolor }) })) : null },
            $O = XO; var QO, JO, eR, tR, nR, rR = n(13228); const aR = (0, ee.Ay)(je.A)(QO || (QO = (0, J.A)(["\n  font-size: 14px;\n  color: #000000;\n  text-transform: uppercase;\n  font-weight: 500;\n"]))),
            oR = (0, ee.Ay)(je.A)(JO || (JO = (0, J.A)(["\n  font-size: 13px;\n  color: inherit;\n  text-transform: uppercase;\n"]))),
            iR = (0, ee.Ay)(je.A)(eR || (eR = (0, J.A)(["\n  font-size: 14px;\n  color: #444444;\n"]))),
            lR = (0, ee.Ay)(je.A)(tR || (tR = (0, J.A)(["\n  font-size: 14px;\n  color: #444444;\n  display: flex;\n  flex-direction: row;\n  grid-gap: 8px;\n  align-items: center;\n"]))),
            sR = (0, ee.Ay)(je.A)(nR || (nR = (0, J.A)(["\n  font-size: 14px;\n  color: #666666;\n  font-weight: 300;\n"]))),
            cR = e => { var t; let { bgcolor: n, name: r, description: a, members: o, handleExpand: i, childIds: l, handleCardClick: s, counts: c, type: d } = e; const { direct: u, recursive: h } = c || { direct: 0, recursive: 0 }; return n = n && "#ffffff" !== (null === (t = n) || void 0 === t ? void 0 : t.toLowerCase()) ? n : "#444444", (0, we.jsxs)(se.A, { button: !0, onClick: s, display: "flex", flexDirection: "column", boxShadow: 2, bgcolor: "#ffffff", children: [(0, we.jsxs)(se.A, { bgcolor: n, color: (0, rR.w5)(n), p: 2, display: "flex", justifyContent: "space-between", gridGap: 16, overflow: "hidden", children: [(0, we.jsx)(oR, { children: r }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 16, children: [u > 0 && (0, we.jsxs)(se.A, { color: "inherit", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "User", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "inherit", children: u })] }), !(!u || !h) && (0, we.jsx)(se.A, { color: "#cccccc", children: "|" }), h > 0 && (0, we.jsxs)(se.A, { color: "inherit", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "UserGroup", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "inherit", children: h })] })] })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", justifyContent: "space-between", children: [(0, we.jsxs)(se.A, { flex: 1, children: [a && "description" === d && (0, we.jsx)(se.A, { p: 2, children: (0, we.jsx)(sR, { children: a }) }), (o || []).length > 0 && (0, we.jsx)(se.A, { p: 2, children: (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, children: o.map((e => (0, we.jsx)(dR, { member: e }))) }) })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, justifyContent: "space-between", alignItems: "center", p: 2, children: [(0, we.jsx)(se.A, { color: "primary", onClick: s, children: (0, we.jsx)(Ee.Ay, { icon: "Info", size: "lg", color: "inherit" }) }), i && (null === l || void 0 === l ? void 0 : l.length) > 0 && (0, we.jsx)(se.A, { color: "primary", onClick: i, children: (0, we.jsx)(Ee.Ay, { icon: "ExpandDown", size: "lg", color: "inherit" }) })] })] })] }) },
            dR = e => { let { member: t } = e; const n = (0, Cn.Bx)(t),
                    r = (0, Cn.zY)(t),
                    a = (0, Cn.v8)(t),
                    o = (0, Cn.rx)(t); return (0, we.jsxs)(se.A, { display: "flex", justifyContent: "flex-start", alignItems: "center", gridGap: 16, children: [(0, we.jsx)(eH.A, { width: 50, height: 50, src: t.photo, name: n, avatarId: t.avatar }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 4, children: [(0, we.jsx)(iR, { children: n }), o && (0, we.jsx)(sR, { children: o }), r && (0, we.jsxs)(lR, { children: [(0, we.jsx)(Ee.Ay, { icon: "Email" }), r] }), a && (0, we.jsxs)(lR, { children: [(0, we.jsx)(Ee.Ay, { icon: "Phone" }), a] })] })] }) },
            uR = e => { let { initRole: t, handleCardClick: n, initSiblings: r } = e; const { chartId: o } = (0, Sj.g)(), [i, l] = (0, a.useState)(t || { id: "root", children: (r || []).map((e => e.id)) }), [s, c] = (0, a.useState)(r ? { role: { id: "root", children: (r || []).map((e => e.id)) } } : null), d = (0, ae.wA)(), u = (0, Cn.mA)(i), h = (0, Cn.PA)(i), m = (null === i || void 0 === i ? void 0 : i.members) || [], p = (0, ae.d4)(MO.A), f = (0, ae.d4)(kO.yN), v = ((null === i || void 0 === i ? void 0 : i.children) || r || []).map((e => p[e])).filter((e => e));
                (0, a.useEffect)((() => { var e;
                    (null === i || void 0 === i || null === (e = i.children) || void 0 === e ? void 0 : e.length) > 0 && (async e => { e && await d(VS.wz.getChildrenRoles({ chartId: o, roleId: e })) })(null === i || void 0 === i ? void 0 : i.id) }), [i]); const g = (null === s || void 0 === s ? void 0 : s.role) || r && (null === s || void 0 === s ? void 0 : s.previous); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(se.A, { display: "flex", justifyContent: "flex-end", gridGap: 16, pt: 2, pb: 1, children: [g && (0, we.jsx)(se.A, { p: 1, border: "solid 1px #ccc", button: !0, onClick: () => { var e, t;
                                l(s.role), c({ role: null === s || void 0 === s || null === (e = s.previous) || void 0 === e ? void 0 : e.role, previous: null === s || void 0 === s || null === (t = s.previous) || void 0 === t ? void 0 : t.previous }) }, children: (0, we.jsxs)("a", { style: { color: "#666666", fontSize: "13px" }, children: ["\u2190 ", (0, Tn.RP)((0, Cn.mA)(null === s || void 0 === s ? void 0 : s.role), 30)] }) }), g && (0, we.jsx)(se.A, { p: 1, border: "solid 1px #ccc", button: !0, onClick: () => { l(t || { id: "root", children: (r || []).map((e => e.id)) }), c(r ? { role: { id: "root", children: (r || []).map((e => e.id)) } } : null) }, children: (0, we.jsx)("a", { style: { color: "#666666", fontSize: "13px" }, children: "\u2191 Top of Chart" }) })] }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, flex: 1, overflow: "auto", children: ["root" !== (null === i || void 0 === i ? void 0 : i.id) && (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, children: (0, we.jsx)(cR, { handleCardClick: n(i), name: u, description: h, members: m, bgcolor: null === i || void 0 === i ? void 0 : i.bgcolor, counts: f(null === i || void 0 === i ? void 0 : i.id), type: null === i || void 0 === i ? void 0 : i.type }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, we.jsx)(aR, { children: "Direct Reports" }), (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, pb: 2, children: v.map((e => { const t = (0, Cn.mA)(e),
                                        r = (0, Cn.PA)(e),
                                        a = e.members || []; return (0, we.jsx)(cR, { name: t, description: r, members: a, bgcolor: null === e || void 0 === e ? void 0 : e.bgcolor, childIds: null === e || void 0 === e ? void 0 : e.children, counts: f(null === e || void 0 === e ? void 0 : e.id), type: null === e || void 0 === e ? void 0 : e.type, handleExpand: t => { var n;
                                            t.stopPropagation(), n = e, c({ role: i, previous: s }), l(n) }, handleCardClick: n(e) }) })) })] })] })] }) },
            hR = e => { let { roles: t, handleRoleClick: n } = e; return (0, we.jsx)(se.A, { px: 1, display: "flex", flexDirection: "column", overflow: "auto", flex: 1, bgcolor: "#f1f1f1", children: (0, we.jsx)(uR, { initRole: 1 === (null === t || void 0 === t ? void 0 : t.length) ? t[0] : null, initSiblings: (null === t || void 0 === t ? void 0 : t.length) > 1 ? t : null, handleCardClick: n }) }) }; var mR, pR, fR, vR, gR, yR, bR, wR, zR, xR, AR, kR, SR; const MR = (0, ee.Ay)(je.A)(mR || (mR = (0, J.A)(["\n  font-size: 13px;\n  color: inherit;\n  text-transform: uppercase;\n"]))),
            ER = (0, ee.Ay)(je.A)(pR || (pR = (0, J.A)(["\n  font-size: 12px;\n  color: #444444;\n"]))),
            CR = (0, ee.Ay)(je.A)(fR || (fR = (0, J.A)(["\n  font-size: 14px;\n  color: #444444;\n"]))),
            TR = (0, ee.Ay)(je.A)(vR || (vR = (0, J.A)(["\n  font-size: 15px;\n  color: #666;\n"]))),
            HR = (0, ee.Ay)(je.A)(gR || (gR = (0, J.A)(["\n  font-size: 14px;\n  color: #666666;\n  font-weight: 300;\n"]))),
            LR = (0, ee.Ay)(se.A)(yR || (yR = (0, J.A)(["\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  transform-style: preserve-3d;\n  transform: rotateY(180deg);\n  background-color: #444;\n  color: #ffffff;\n"]))),
            IR = (0, ee.Ay)(je.A)(bR || (bR = (0, J.A)(["\n  font-size: 14px;\n  color: inherit;\n  display: flex;\n  flex-direction: row;\n  grid-gap: 8px;\n  align-items: center;\n"]))),
            jR = (0, ee.Ay)(se.A)(wR || (wR = (0, J.A)(["\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  transform-style: preserve-3d;\n  background-color: #ffffff;\n"]))),
            VR = (0, ee.i7)(zR || (zR = (0, J.A)(["\n  0% {\n    transform: rotateX(90deg) rotateZ(10deg);\n  }\n  100% {\n    transform:  rotateX(0deg) rotateZ(0deg);\n  }\n"]))),
            OR = (0, ee.Ay)(se.A)(xR || (xR = (0, J.A)(["\n  position: relative;\n  ", "\n  transition: transform 0.8s;\n  transform-style: preserve-3d;\n  perspective: 1000px;\n  width: 40%;\n  min-width: 130px;\n"])), (e => { let { flipped: t } = e; return "\n  transform: scale(1) rotateY(".concat(t ? "180deg" : "0deg", ") rotateZ(0deg);\n    ") })),
            RR = (0, ee.Ay)(se.A)(AR || (AR = (0, J.A)(["\n  position: relative;\n  ", "\n  transition: transform 0.8s;\n  transform-style: preserve-3d;\n  animation: ", " 0.3s linear 1;\n  perspective: 1000px;\n  width: 40%;\n  min-width: 130px;\n"])), (e => { let { flipped: t } = e; return "\n  transform: scale(1) rotateY(".concat(t ? "180deg" : "0deg", ") rotateZ(0deg);\n    ") }), VR),
            PR = (0, ee.Ay)(se.A)(kR || (kR = (0, J.A)(["\n  position: relative;\n  text-align: center;\n  transition: transform 0.8s;\n  transform-style: preserve-3d;\n  width: 100%;\n  height: 100%;\n"]))),
            DR = (0, ee.Ay)(se.A)(SR || (SR = (0, J.A)(["\n  opacity: 0.5;\n  &:hover {\n    opacity: 1;\n  }\n"]))),
            FR = e => { let { selectedId: t, onSiblingToggleLeft: n, onSiblingToggleRight: r, onToggleSiblingIndex: a, peers: o = [] } = e; const i = (o || []).findIndex((e => e.id === t)),
                    l = o.length - 1,
                    s = o.slice(Math.max(0, i - 6), i),
                    c = o.slice(i + 1, Math.min(i + 1 + 6, null === o || void 0 === o ? void 0 : o.length)); if (0 === l) return; const d = (e, t) => { var n, r, o, i, l, s; const c = null === e || void 0 === e || null === (n = e.people) || void 0 === n || null === (r = n[0]) || void 0 === r ? void 0 : r.photo,
                        d = null === e || void 0 === e || null === (o = e.people) || void 0 === o || null === (i = o[0]) || void 0 === i ? void 0 : i.avatarId,
                        u = (0, Cn.mA)(e),
                        h = (null === e || void 0 === e || null === (l = e.people) || void 0 === l ? void 0 : l.length) > 0 ? (0, Cn.Bx)(null === e || void 0 === e || null === (s = e.people) || void 0 === s ? void 0 : s[0]) : u; return (0, we.jsx)(DR, { boxShadow: 1, borderRadius: "50%", width: 28, flex: 1, onClick: e => { e.stopPropagation(), a(t) }, children: (0, we.jsx)(eH.A, { width: 28, height: 28, src: c, name: h, avatarId: d }) }, "peer_indicator_".concat(null === e || void 0 === e ? void 0 : e.id)) }; return (0, we.jsxs)(we.Fragment, { children: [(null === s || void 0 === s ? void 0 : s.length) > 0 && (0, we.jsx)(se.A, { position: "absolute", right: "calc(100% + 16px)", bottom: 0, onClick: n, display: "flex", gridGap: 8, children: (s || []).map(d) }), (null === c || void 0 === c ? void 0 : c.length) > 0 && (0, we.jsx)(se.A, { position: "absolute", left: "calc(100% + 16px)", bottom: 0, onClick: r, display: "flex", gridGap: 8, children: (c || []).map(((e, t) => d(e, t + s.length + 1))) })] }) },
            NR = e => { let { transparent: t = !1, vertical: n = !1, horizontal: r = !1 } = e; return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", position: "relative", width: "100%", children: [n && (0, we.jsx)(se.A, { display: "flex", justifyContent: "center", alignItems: "flex-end", children: (0, we.jsx)(se.A, { borderLeft: t ? void 0 : "solid 1px #ccc", height: 15, width: "1px" }) }), r && (0, we.jsx)(se.A, { borderTop: "solid 1px #ccc", width: "100%", height: 30, borderRadius: 16, children: "\xa0" })] }) },
            _R = e => { let { handleContactInfoClick: t, directCounts: n, recursiveCounts: r, handleExpand: a, handlePrevLevel: o } = e; return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 16, justifyContent: "space-between", alignItems: "center", p: 1, children: ["function" === typeof t && (0, we.jsx)(se.A, { onClick: t, children: (0, we.jsx)(Ee.Ay, { icon: "AddressBook", size: "lg", color: "inherit" }) }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 16, children: [n > 0 && (0, we.jsxs)(se.A, { color: "inherit", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "User", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "inherit", children: n })] }), !(!n || !r) && (0, we.jsx)(se.A, { color: "#cccccc", children: "|" }), r > 0 && (0, we.jsxs)(se.A, { color: "inherit", borderRadius: "5px", display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "UserGroup", color: "inherit", size: "sm" }), (0, we.jsx)(je.A, { fontSize: "12px", color: "inherit", children: r })] })] }), a && (0, we.jsx)(se.A, { color: "primary", onClick: a, children: (0, we.jsx)(Ee.Ay, { icon: "ExpandDown", size: "lg", color: "inherit" }) }), o && (0, we.jsx)(se.A, { color: "primary", onClick: o, children: (0, we.jsx)(Ee.Ay, { icon: "ExpandUp", color: "inherit", size: "lg" }) })] }) },
            BR = e => { let { bgcolor: t, name: n, handleInfoClick: r } = e; return (0, we.jsxs)(se.A, { borderBottom: "solid 2px ".concat(t), p: 1, display: "flex", justifyContent: "space-between", alignItems: "center", gridGap: 16, overflow: "hidden", minHeight: 20, children: [(0, we.jsx)(MR, { align: "left", children: n }), (0, we.jsx)(se.A, { onClick: r, children: (0, we.jsx)(Ee.Ay, { icon: "Info", size: "lg", color: "inherit" }) })] }) },
            WR = e => { let { members: t } = e; return t.map((e => { const t = (0, Cn.Bx)(e),
                        n = (0, Cn.zY)(e),
                        r = (0, Cn.v8)(e),
                        a = !(n || r); return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", gridGap: 16, children: [(0, we.jsx)(IR, { children: t }), r && (0, we.jsx)("a", { style: { color: "inherit" }, href: "tel:".concat(r), onClick: e => e.stopPropagation(), children: (0, we.jsxs)(IR, { children: [(0, we.jsx)(Ee.Ay, { icon: "Phone" }), " ", r] }) }), n && (0, we.jsx)("a", { style: { color: "inherit" }, href: "mailto:".concat(n), onClick: e => e.stopPropagation(), children: (0, we.jsxs)(IR, { children: [(0, we.jsx)(Ee.Ay, { icon: "Mail" }), " ", n] }) }), a && (0, we.jsx)(IR, { children: "No contact details" })] }) })) },
            UR = e => { var t; let { bgcolor: n, vacantText: r, name: o, members: i, handleExpand: l, childIds: s, handleCardClick: c, counts: d, type: u, size: h = "lg", siblingComponent: m, animated: p = !1, width: f, minWidth: v = 200, handlePrevLevel: g, minHeight: y = 140 } = e; const [b, w] = (0, a.useState)(!1); let z = 48; "sm" === h && (z = 32, f = "inherit", v = 150); const { direct: x, recursive: A } = d || { direct: 0, recursive: 0 };
                n = n && "#ffffff" !== (null === (t = n) || void 0 === t ? void 0 : t.toLowerCase()) ? n : "#d1d1d1"; const k = p ? RR : OR,
                    S = (() => { const e = (i || []).reduce(((e, t) => { const n = (0, Cn.zY)(t),
                                r = (0, Cn.v8)(t); return !!(e || n || r) }), !1); return u !== Wt.mv.DEPARTMENT && e ? e => { e.stopPropagation(), w(!b) } : null })(),
                    M = (0, a.useMemo)((() => u === Wt.mv.DEPARTMENT ? "Department" : u === Wt.mv.LOCATION ? "Location" : u === Wt.mv.EMBEDDED ? "Embedded" : o), [u]),
                    E = (0, a.useMemo)((() => u === Wt.mv.DEPARTMENT || u === Wt.mv.LOCATION || u === Wt.mv.EMBEDDED ? o : r), [u, i]); return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", justifyContent: "center", flex: 1, zIndex: 1, position: "relative", width: f, minWidth: v, children: [(0, we.jsx)(k, { animated: p, button: !0, onClick: c, display: "flex", flexDirection: "column", bgcolor: "transparent", boxShadow: 1, flex: 1, flipped: b, position: "relative", minHeight: y, children: (0, we.jsxs)(PR, { display: "flex", flexDirection: "column", justifyContent: "space-between", flipped: b, position: "relative", children: [(0, we.jsxs)(jR, { display: "flex", flexDirection: "column", justifyContent: "space-between", children: [(0, we.jsx)(BR, { handleInfoClick: c, bgcolor: n, name: (0, Tn.RP)(M, z) }), (0, we.jsx)(se.A, { overflow: "auto", children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", children: [1 === (i || []).length && (0, we.jsx)(se.A, { p: 2, children: (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, children: i.map((e => (0, we.jsx)(GR, { member: e }))) }) }), (i || []).length > 1 && (0, we.jsx)(se.A, { p: 1, children: (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, children: i.map((e => (0, we.jsx)(qR, { member: e }))) }) }), !(null !== i && void 0 !== i && i.length) && (0, we.jsx)(se.A, { p: 1, children: (0, we.jsx)(TR, { children: E }) })] }) }), (0, we.jsx)(_R, { handleContactInfoClick: S, directCounts: x, recursiveCounts: A, handleExpand: (null === s || void 0 === s ? void 0 : s.length) > 0 && l, handlePrevLevel: g })] }), (0, we.jsxs)(LR, { display: "flex", flexDirection: "column", justifyContent: "space-between", children: [(0, we.jsx)(se.A, { flex: 1, display: "flex", flexDirection: "column", justifyContent: "center", overflow: "auto", p: 1, children: (0, we.jsx)(WR, { members: i }) }), (0, we.jsx)(_R, { handleSlideInfoClick: c, handleContactInfoClick: S, directCounts: x, recursiveCounts: A, handleExpand: (null === s || void 0 === s ? void 0 : s.length) > 0 && l, handlePrevLevel: g })] })] }) }), m] }) },
            qR = e => { let { member: t } = e; const n = (0, Cn.Bx)(t); return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", justifyContent: "left", alignItems: "center", gridGap: 8, pr: 1, overflow: "hidden", children: [(0, we.jsx)(eH.A, { width: 30, height: 30, src: t.photo, name: n, avatarId: t.avatar }), (0, we.jsx)(ER, { children: n })] }) },
            GR = e => { let { member: t } = e; const n = (0, Cn.Bx)(t),
                    r = (0, Cn.rx)(t); return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", justifyContent: "left", alignItems: "center", gridGap: 8, pr: 1, overflow: "hidden", children: [(0, we.jsx)(eH.A, { width: 40, height: 40, src: t.photo, name: n, avatarId: t.avatar }), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 4, children: [(0, we.jsx)(CR, { children: n }), (0, we.jsx)(HR, { children: (0, Tn.RP)(r, 60) })] })] }) },
            KR = e => { var t, n, r, o, i, l, s, c, d, u; let { initRole: h, handleCardClick: m, initSiblings: p } = e; const f = (0, ae.wA)(),
                    { chartId: v } = (0, Sj.g)(),
                    g = (0, ae.d4)(HO),
                    y = (0, ae.d4)(CO),
                    [b, w] = (0, a.useState)(h || { id: "root", children: (p || []).map((e => e.id)) }),
                    [z, x] = (0, a.useState)(p ? { role: { id: "root", children: (p || []).map((e => e.id)) } } : null),
                    A = (0, Cn.mA)(b),
                    k = y((null === b || void 0 === b ? void 0 : b.members) || []),
                    S = (0, Cn.PA)(b),
                    M = (0, ae.d4)(MO.A),
                    [E, C] = (0, he.A)((async e => { e && await f(VS.wz.getChildrenRoles({ chartId: v, roleId: e })) })),
                    T = (null === h || void 0 === h || null === (t = h.children) || void 0 === t ? void 0 : t.length) || 0,
                    H = () => { var e, t;
                        z.role && (w(z.role), x({ role: null === z || void 0 === z || null === (e = z.previous) || void 0 === e ? void 0 : e.role, previous: null === z || void 0 === z || null === (t = z.previous) || void 0 === t ? void 0 : t.previous })) },
                    L = ((null === b || void 0 === b ? void 0 : b.children) || p || []).map((e => M[e])).filter((e => e)),
                    I = ((null === z || void 0 === z || null === (n = z.role) || void 0 === n ? void 0 : n.children) || []).map((e => { const t = M[e]; return { ...t, people: y(t.members) } }));
                (0, a.useEffect)((() => { var e;
                    (null === b || void 0 === b || null === (e = b.children) || void 0 === e ? void 0 : e.length) > 0 && !M[null === b || void 0 === b ? void 0 : b.children[0]] && C(null === b || void 0 === b ? void 0 : b.id) }), [b]), (0, a.useEffect)((() => {
                    (null === b || void 0 === b ? void 0 : b.id) !== (null === h || void 0 === h ? void 0 : h.id) && (w(h || { id: "root", children: (p || []).map((e => e.id)) }), x(p ? { role: { id: "root", children: (p || []).map((e => e.id)) } } : null)) }), [h]); const j = (null === z || void 0 === z ? void 0 : z.role) && (null === z || void 0 === z ? void 0 : z.role.id) !== Wt.Uz,
                    V = (0, Cn.mA)(null === z || void 0 === z ? void 0 : z.role),
                    O = (0, Cn.PA)(null === z || void 0 === z ? void 0 : z.role),
                    R = y((null === z || void 0 === z || null === (r = z.role) || void 0 === r ? void 0 : r.members) || []),
                    P = (e, t) => { switch (e) {
                            case Wt.mv.DEPARTMENT:
                            case Wt.mv.LOCATION:
                            case Wt.mv.EMBEDDED:
                                return 120;
                            case Wt.mv.SINGLE:
                                return t ? 156 : 180;
                            default:
                                return 156 } }; return (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", flex: 1, overflow: "auto", position: "relative", children: [j && (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", width: "100%", children: [(0, we.jsx)(NR, { vertical: !0, transparent: !(null !== z && void 0 !== z && null !== (o = z.role) && void 0 !== o && o.parent) }), (0, we.jsx)(UR, { minHeight: P(null === z || void 0 === z || null === (l = z.role) || void 0 === l ? void 0 : l.type, !1), handleCardClick: H, name: V, description: O, members: R, bgcolor: null === z || void 0 === z || null === (s = z.role) || void 0 === s ? void 0 : s.bgcolor, counts: (null === z || void 0 === z || null === (c = z.role) || void 0 === c ? void 0 : c.counts) || {}, type: null === z || void 0 === z || null === (d = z.role) || void 0 === d ? void 0 : d.type, width: "50%", handlePrevLevel: H }, "root_".concat(null === z || void 0 === z || null === (i = z.role) || void 0 === i ? void 0 : i.id))] }), "root" !== (null === b || void 0 === b ? void 0 : b.id) && (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", width: "100%", children: [(0, we.jsx)(NR, { vertical: !0, transparent: !(null !== z && void 0 !== z && z.role) }), (0, we.jsx)(UR, { minHeight: P(null === b || void 0 === b ? void 0 : b.type, !1), handleCardClick: m(b), name: A, description: S, members: k, bgcolor: null === b || void 0 === b ? void 0 : b.bgcolor, counts: (null === b || void 0 === b ? void 0 : b.counts) || {}, type: null === b || void 0 === b ? void 0 : b.type, width: "50%", siblingComponent: (null === I || void 0 === I ? void 0 : I.length) > 1 && (0, we.jsx)(FR, { onSiblingToggleLeft: () => { var e; const t = null === b || void 0 === b ? void 0 : b.id,
                                            n = (null === z || void 0 === z || null === (e = z.role) || void 0 === e ? void 0 : e.children) || [],
                                            r = n.indexOf(t); if (-1 === r) return void console.log("Cannot find current root in the siblings"); const a = (r + n.length - 1) % n.length;
                                        w(M[n[a]]) }, onSiblingToggleRight: () => { var e; const t = null === b || void 0 === b ? void 0 : b.id,
                                            n = (null === z || void 0 === z || null === (e = z.role) || void 0 === e ? void 0 : e.children) || [],
                                            r = n.indexOf(t); if (-1 === r) return void console.log("Cannot find current root in the siblings"); const a = (r + 1) % n.length;
                                        w(M[n[a]]) }, onToggleSiblingIndex: e => { var t; const n = (null === z || void 0 === z || null === (t = z.role) || void 0 === t ? void 0 : t.children) || [];
                                        w(M[n[e]]) }, peers: I, selectedId: null === b || void 0 === b ? void 0 : b.id }) }, "active_".concat(null === b || void 0 === b || null === (u = b.role) || void 0 === u ? void 0 : u.id))] }), E && T > 0 && (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", position: "relative", width: "100%", children: [(0, we.jsx)(NR, { vertical: !0, horizontal: !0 }), (0, we.jsx)(se.A, { display: "grid", gridAutoFlow: "row", gridTemplateColumns: "repeat(auto-fill, minMax(200px, 1fr))", justifyContent: "center", gridGap: 16, pb: 2, flexWrap: "wrap", width: "100%", children: Array.from(Array(T).keys()).map((e => (0, we.jsx)(se.A, { children: (0, we.jsx)(tr.A, { variant: "rect", minWidth: 200, height: 140 }) }, e))) })] }), (null === L || void 0 === L ? void 0 : L.length) > 0 && (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", position: "relative", width: "100%", children: [(0, we.jsx)(NR, { vertical: !0, horizontal: !0 }), (0, we.jsx)(se.A, { display: "grid", gridAutoFlow: "row", gridTemplateColumns: "repeat(auto-fill, minMax(200px, 1fr))", justifyContent: "center", gridGap: 16, pb: 2, flexWrap: "wrap", width: 1 === (null === L || void 0 === L ? void 0 : L.length) ? "300px" : "100%", children: L.map((e => { const t = (0, Cn.mA)(e),
                                        n = (0, Cn.PA)(e),
                                        r = y(e.members || []); return (0, we.jsx)(UR, { minHeight: P(null === e || void 0 === e ? void 0 : e.type, !0), animated: !0, size: "sm", vacantText: g, name: t, description: n, members: r, bgcolor: null === e || void 0 === e ? void 0 : e.bgcolor, childIds: null === e || void 0 === e ? void 0 : e.children, counts: (null === e || void 0 === e ? void 0 : e.counts) || {}, type: null === e || void 0 === e ? void 0 : e.type, handleExpand: t => { var n;
                                            t.stopPropagation(), n = e, x({ role: b, previous: z }), w(n) }, handleCardClick: m(e) }, "childrole-card-".concat(null === e || void 0 === e ? void 0 : e.id)) })) })] })] }) }) },
            ZR = e => { let { roles: t, handleRoleClick: n } = e; return (0, we.jsx)(se.A, { px: 1, display: "flex", flexDirection: "column", overflow: "auto", flex: 1, bgcolor: "#f1f1f1", children: (0, we.jsx)(KR, { initRole: 1 === (null === t || void 0 === t ? void 0 : t.length) ? t[0] : null, initSiblings: (null === t || void 0 === t ? void 0 : t.length) > 1 ? t : null, handleCardClick: n }) }) }; var YR = n(61);

        function XR(e) { var t; let { defaultTopId: n } = e; const [r] = (0, un.A)("activeMobileChartView", "chart"), [, o] = (0, un.A)("isDesktopMode", !1, !0), { chartId: i } = (0, Sj.g)(), l = (0, ae.d4)(EO), s = (0, ae.wA)(), c = (0, ae.d4)(TO), d = e => t => { t.stopPropagation(), t.preventDefault(), s((0, $S.gN)({ mode: "view", activePersonId: e.members[0], selectedRoleId: e.id })) }, [u, h] = (0, he.A)((async () => { await s(VS.wz.getChildrenRoles({ chartId: i, roleId: n || "root" })) }));
            (0, a.useEffect)((() => { i && h() }), [i]); return (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", overflow: "auto", flex: 1, children: (0, we.jsxs)(dn.A, { loading: u, title: "Building Chart...", children: [0 === (null === l || void 0 === l ? void 0 : l.length) && (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 2, children: (0, we.jsx)(jn.A, { text: "There's nothing here. Please add a role from Desktop view to get started.", image: Vn.A, textVariant: "body1", handleClick: () => { o(!0) }, buttonText: "Use Desktop App", isButtonVisible: !0 }) }), c && (0, we.jsx)(se.A, { display: "flex", flexDirection: "row", gridGap: 12, p: 1, alignItems: "center", children: (0, we.jsx)(zn.A, { label: (0, Cn.mA)(l[0]), onDelete: () => { s((0, YR.EJ)(null)) }, color: "primary", variant: "outlined" }) }), "chart" === r && (0, we.jsx)(FO, { defaultExpanded: null === l || void 0 === l ? void 0 : l.map((e => null === e || void 0 === e ? void 0 : e.id)), defaultCollapseIcon: (0, we.jsx)(xO, {}), defaultExpandIcon: (0, we.jsx)(AO, {}), children: (0, we.jsx)($O, { chartId: i, roles: l, handleRoleClick: d }) }), "twolevel" === r && (0, we.jsx)(hR, { roles: l, handleRoleClick: d }), "verticaltree" === r && (0, we.jsx)(ZR, { roles: l, handleRoleClick: d }, "vertical-tree-".concat((null === l || void 0 === l ? void 0 : l.id) || (null === l || void 0 === l || null === (t = l[0]) || void 0 === t ? void 0 : t.id)))] }) }) } var $R, QR, JR = n(87603),
            eP = n(86255); const tP = (0, ee.Ay)(bI.A)($R || ($R = (0, J.A)(["\n  input {\n    padding: 12px 8px;\n  }\n"]))),
            nP = (0, ee.Ay)(Tr.A)(QR || (QR = (0, J.A)(["\n  border: solid 1px #999;\n  border-radius: 4px;\n  padding: 8px 4px;\n  min-width: 40px;\n  text-align: center;\n  font-size: 24px;\n"]))),
            rP = "chart",
            aP = "verticaltree",
            oP = () => { const e = (0, ae.wA)(),
                    { chartId: t } = (0, $.useParams)(),
                    [n, r] = (0, a.useState)([]),
                    [o, i] = (0, a.useState)(""),
                    l = (0, a.useRef)(),
                    s = (0, ae.d4)(TO),
                    { eventDebounce: c } = (0, eP.A)(),
                    [d, u] = (0, un.A)("activeMobileChartView");
                (0, a.useEffect)((() => { s || (l.current.value = "") }), [s]); const h = e => { u(e) },
                    m = c((async e => { i(e.target.value.trim()) }), 500),
                    [p, f] = (0, he.A)((async () => { const { payload: n } = await e(VS.wz.searchRoles({ chartId: t, query: o }));
                        r(n.results) }));
                (0, a.useEffect)((() => { null !== o && void 0 !== o && o.trim() && f() }), [o]); const v = e => { null === e || void 0 === e || e.stopPropagation(), i("") }; return (0, we.jsxs)(se.A, { p: 1, display: "flex", gridGap: 8, alignItems: "center", children: [(0, we.jsx)(se.A, { position: "relative", flex: 1, children: (0, we.jsxs)(dn.A, { loading: p, transparent: !0, spinnerSize: 30, children: [(0, we.jsx)(tP, { inputRef: l, onChange: m, fullWidth: !0, variant: "outlined", placeholder: "Search chart", InputProps: { startAdornment: (0, we.jsx)(AH.A, { position: "start", children: (0, we.jsx)(Ee.Ay, { icon: "Search" }) }), endAdornment: null !== o && void 0 !== o && o.length ? (0, we.jsx)(AH.A, { position: "end", children: (0, we.jsx)(Tr.A, { size: "small", onClick: e => { null === e || void 0 === e || e.stopPropagation(), null === e || void 0 === e || e.preventDefault(), l.current.value = "" }, children: (0, we.jsx)(Ee.Ay, { color: "inherit", icon: "Close", size: "sm" }) }) }) : (0, we.jsx)(we.Fragment, {}) } }), (null === o || void 0 === o ? void 0 : o.length) > 0 && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(Vj.A, { onClick: v, open: !0, style: { zIndex: 1 }, invisible: !0 }), (0, we.jsxs)(se.A, { position: "absolute", top: 42, left: 0, zIndex: 2, bgcolor: "#ffffff", maxHeight: 400, overflow: "auto", right: 0, border: "solid 1px #ddd", children: [(0, we.jsx)(se.A, { position: "absolute", right: 16, top: 16, onClick: v, zIndex: 1, button: !0, children: (0, we.jsx)(Tr.A, { size: "small", children: (0, we.jsx)(Ee.Ay, { color: "inherit", icon: "Close", size: "sm" }) }) }), (0, we.jsxs)(se.A, { maxHeight: 400, overflow: "auto", children: [0 === (null === n || void 0 === n ? void 0 : n.length) && (0, we.jsx)(se.A, { p: 2, children: p ? "Loading..." : "No results found" }), null === n || void 0 === n ? void 0 : n.map((t => { var n; const r = null === t || void 0 === t || null === (n = t.members) || void 0 === n ? void 0 : n[0],
                                                a = (0, Cn.mA)(t),
                                                o = (0, Cn.Bx)(r),
                                                i = null === r || void 0 === r ? void 0 : r.photo; return (0, we.jsxs)(JR.A, { button: !0, onClick: () => (t => { e((0, YR.EJ)({ role: { ...t, members: ((null === t || void 0 === t ? void 0 : t.members) || []).map((e => e.id)) }, people: (null === t || void 0 === t ? void 0 : t.members) || [] })), v() })(t), style: { gridGap: "16px" }, children: [(0, we.jsx)(eH.A, { width: 30, height: 30, src: i, name: o || a, overrideColor: null === r || void 0 === r ? void 0 : r.memberPhotoColor }), (0, we.jsx)(Sr.A, { primary: a, secondary: o })] }, "".concat(null === t || void 0 === t ? void 0 : t.id, "-chart-search-result")) }))] })] })] })] }) }), (0, we.jsx)(nP, { color: d === rP ? "primary" : "default", variant: "outlined", onClick: () => h(rP), children: (0, we.jsx)(Ee.Ay, { icon: "FileTree" }) }), (0, we.jsx)(nP, { color: d === aP ? "primary" : "default", variant: "contained", onClick: () => h(aP), children: (0, we.jsx)(Ee.Ay, { icon: "Chart" }) })] }) }; var iP = n(65491); const lP = () => { var e, t, n; const [r] = (0, un.A)("activeMobileChartView", "chart"), [, o] = (0, un.A)("isDesktopMode", !1, !0), { chartId: i } = (0, $.useParams)(), l = (0, ae.d4)(EO), s = (0, ae.wA)(), c = (0, ae.d4)(TO), d = (0, ae.d4)(kO.lA), u = (0, ae.d4)(kO.iR), h = (0, ae.d4)(kO.Lj), m = (0, ae.d4)(iP.A), p = (0, ae.d4)(MO.A), [f, v] = a.useState(null === (e = h[0]) || void 0 === e ? void 0 : e.id), [g, y] = a.useState(null === (t = u[0]) || void 0 === t ? void 0 : t.id);
                (0, a.useEffect)((() => { var e, t;
                    f || v(null === (e = h[0]) || void 0 === e ? void 0 : e.id);
                    g || y(null === (t = u[0]) || void 0 === t ? void 0 : t.id) }), [h, u]); const b = (0, a.useMemo)((() => u.find((e => e.id === g))), g, u),
                    w = (0, a.useMemo)((() => h.find((e => e.id === f))), f, h),
                    z = d.find((e => e.teamId === f && e.functionId === g)),
                    x = e => t => { t.stopPropagation(), t.preventDefault(), s((0, $S.gN)({ mode: "view", activePersonId: e.members[0], selectedRoleId: e.id })) },
                    [A, k] = (0, he.A)((async e => { let { roleId: t } = e;
                        await s(VS.wz.getChildrenRoles({ chartId: i, roleId: t || "root" })) })),
                    [S, M] = (0, he.A)((async e => { let { roleId: t } = e;
                        await s(VS.wz.getChildrenRoles({ chartId: i, roleId: t })) }));
                (0, a.useEffect)((() => { i && k({}) }), [i]);
                (0, a.useEffect)((() => { z && M({ roleId: null === z || void 0 === z ? void 0 : z.id }) }), [z]); const E = (0, a.useMemo)((() => { if (c) return l; if (!z) return []; return (m[null === z || void 0 === z ? void 0 : z.id] || []).map((e => p[e])).filter((e => e)) }), [z, m, p, c]); return (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", overflow: "auto", flex: 1, children: (0, we.jsxs)(dn.A, { loading: A, title: "Building Chart...", children: [c && (0, we.jsx)(se.A, { display: "flex", flexDirection: "row", gridGap: 12, p: 1, alignItems: "center", children: (0, we.jsx)(zn.A, { label: (0, Cn.mA)(E[0]), onDelete: () => { s((0, YR.EJ)(null)) }, color: "primary", variant: "outlined" }) }), !c && (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 12, p: 1, alignItems: "center", children: [(0, we.jsx)(bI.A, { select: !0, label: "Team (Row)", fullWidth: !0, variant: "outlined", value: f, onChange: e => { v(e.target.value) }, children: h.map((e => (0, we.jsx)(Ze.A, { value: e.id, children: (0, Cn.mA)(e) }))) }), (0, we.jsx)(bI.A, { select: !0, label: "Function (Column)", fullWidth: !0, variant: "outlined", value: g, onChange: e => { y(e.target.value) }, children: u.map((e => (0, we.jsx)(Ze.A, { value: e.id, children: (0, Cn.mA)(e) }))) })] }), 0 === (null === E || void 0 === E ? void 0 : E.length) && (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 2, children: (0, we.jsx)(jn.A, { text: "There's nothing here for \"".concat((0, Cn.mA)(b), '" and "').concat((0, Cn.mA)(w), '". Please add a role from Desktop view to get started.'), image: Vn.A, textVariant: "body1", handleClick: () => { o(!0) }, buttonText: "Use Desktop App", isButtonVisible: !0 }) }), (0, we.jsxs)(dn.A, { loading: S, title: "Loading chart for the cell...", children: ["chart" === r && (0, we.jsx)(FO, { defaultCollapseIcon: (0, we.jsx)(xO, {}), defaultExpandIcon: (0, we.jsx)(AO, {}), children: (0, we.jsx)($O, { chartId: i, roles: E, handleRoleClick: x }) }), "twolevel" === r && (0, we.jsx)(hR, { roles: E, handleRoleClick: x }), "verticaltree" === r && (0, we.jsx)(ZR, { roles: E, handleRoleClick: x }, "vertical-tree-".concat((null === E || void 0 === E ? void 0 : E.id) || (null === E || void 0 === E || null === (n = E[0]) || void 0 === n ? void 0 : n.id)))] })] }) }) },
            sP = e => { let { resource: t, ...n } = e; const r = (0, ae.d4)(bO.G0),
                    [, a] = (0, un.A)("isDesktopMode", !1, !0),
                    o = () => { a(!0) }; return "chart" === t ? r === Wt.XD.MATRIX ? (0, we.jsx)(lP, {}) : r === Wt.XD.BOARD_OF_DIRECTORS ? (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 2, children: (0, we.jsx)(jn.A, { text: "Board of Directors chart not supported in mobile view. Please switch to desktop mode to view this chart.", textVariant: "body1", handleClick: o, buttonText: "Use Desktop App", isButtonVisible: !0 }) }) : (0, we.jsx)(XR, {}) : "spreadsheet" === t && r === Wt.XD.TRADITIONAL ? (0, we.jsx)(wO.E, { ...n }) : (0, we.jsx)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 2, children: (0, we.jsx)(jn.A, { text: "Flat chart not supported in mobile view. Please switch to desktop mode to view this chart.", textVariant: "body1", handleClick: o, buttonText: "Use Desktop App", isButtonVisible: !0 }) }) },
            cP = () => { const e = (0, ae.wA)(),
                    { params: { orgId: t, chartId: n, resource: r, resourceAction: o, base: i } } = (0, re.u)([(0, ne.si)()]),
                    { pId: l } = (0, Q.A)(),
                    s = l && "public" === i,
                    c = (0, ae.d4)($S.t0),
                    d = "view" === o,
                    [u, h] = (0, he.A)((async () => { await e(VS.wz.get({ orgId: t, chartId: n, pId: l, mode: i })) })),
                    [m, p] = (0, he.A)((async () => { await e(XI.Ww.getPublicChartTheme({ chartId: n, pId: l })) })),
                    [f, v] = (0, he.A)((async () => { await e(nn.UY.get({ orgId: t })) })),
                    g = (0, rr.A)({ orgId: t });
                (0, a.useEffect)((() => (async function() { s && await e((0, NS.Wq)({ primary: "public", secondary: o })) }(), () => {})), [o]), (0, a.useEffect)((() => { n && h(), n && s && p() }), [n, s]), (0, a.useEffect)((() => { t && v() }), [t]); const y = u || f || s && m || g; return (0, we.jsxs)(we.Fragment, { children: [d && (0, we.jsx)(oP, { resource: r }), (0, we.jsx)(dn.A, { loading: y, title: "Loading chart info...", children: (0, we.jsx)(sP, { resource: r, chartId: n, orgId: t }) }), c && (0, we.jsx)(QV, {})] }) };

        function dP(e) { let { children: t } = e; const n = (0, ae.d4)(NS.oL),
                [, r] = (0, un.A)("activeLicenseId", null, !0),
                { show: o } = (0, Un.A)(); return (0, a.useEffect)((() => { if (n) { let e = n.title || (403 === n.responseCode ? "Not Allowed" : "Application Error"); "upgradeRequired" === (null === n || void 0 === n ? void 0 : n.type) ? (r(null), o("Account over limits. Update your account on the desktop app", "error", 3500)) : o("".concat(e, ": ").concat(n.message || "Something went wrong. <NAME_EMAIL>"), "error") } }), [n]), t } const uP = ["embed", "public", "print", "community"],
            hP = () => { const e = (0, $.useHistory)(),
                    t = (0, ae.d4)(SI),
                    n = (0, ae.d4)(Lt.VF),
                    r = null === n || void 0 === n ? void 0 : n.id,
                    [, a] = (0, un.A)("license.recent.organization.".concat(r)),
                    o = (0, Kt.A)(); return (0, we.jsx)(wI, { label: "Organization", select: !0, fullWidth: !0, variant: "outlined", value: null === o || void 0 === o ? void 0 : o.id, children: t.map((t => (0, we.jsx)(Ze.A, { value: t.id, onClick: () => { return n = t.id, a(n), void e.push((0, ne.r2)({ orgId: n })); var n }, children: t.name }, t.id))) }) },
            mP = () => (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", p: 4, gridGap: 16, children: [(0, we.jsx)(hI, { children: "Welcome to Organimi Mobile" }), (0, we.jsx)(mI, { align: "center", children: "Please select an organization to continue. If you find any features unavailable, please visit Organimi on a larger screen." }), (0, we.jsx)(hP, {})] }),
            pP = () => { QI(); const { isReady: e, isAuthorized: t, orgResourcesLoading: n, isUnVerified: r } = $I(), o = ej({ isReady: e, isAuthorized: t }), { redirect: i } = (0, Q.A)(), [l] = (0, un.A)("activeLicenseId", null, !0), [s] = (0, un.A)("activeMobileTab", "charts", !0), c = (0, re.u)([(0, ne.si)()]), d = (0, Kt.A)();
