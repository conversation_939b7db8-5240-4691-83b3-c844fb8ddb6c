                    o = n(52996),
                    i = n(13228),
                    l = n(75156),
                    s = n(54161),
                    c = n(70579); const d = e => { let { recursiveFilterCount: t } = e; return (0, c.jsxs)(o.A, { left: 8, top: 36, children: [(0, c.jsx)("rect", { height: 16, width: 24, rx: 8, fill: "#F5617B" }), (0, c.jsx)(s.A, { fill: "#ffffff", textAnchor: "middle", x: 12, fontSize: "12px", width: 24, y: 12, height: 16, children: t })] }) },
                    u = e => { let { handleFilterExpandClick: t, bgColor: n = "#444444", recursiveFilterCount: r } = e; return (0, c.jsxs)(o.A, { onClick: t, children: [(0, c.jsx)("circle", { cx: 20, cy: 16, r: 20, fill: n || "#ffffff" }), (0, c.jsx)(o.A, { left: 10, top: 8, children: (0, c.jsx)(l.me, { color: (0, i.w5)(n), inlineSvg: !0, name: "chevron-circle-down", variant: "light", fontSize: 20 }) }), r && (0, c.jsx)(d, { recursiveFilterCount: r + 1 })] }) }; var h = n(91945),
                    m = n(33843),
                    p = n(55357),
                    f = n(96364),
                    v = n(84),
                    g = n(10621); const y = e => { let { displayOptions: t, createPageAnchorEl: n, handleCreatePagePopoverClose: r, isNewPageCreationAllowed: a, handleCreateNewPage: o, handleRemovePage: i, role: l } = e; const { openDialog: s } = (0, v.A)("directReportDirection"), { handleChangeDirectReportDirection: d } = (0, h.A)(), u = !(null !== l && void 0 !== l && l.parent); return (0, c.jsxs)(m.Ay, { id: "manual-print-options-popover", sx: { pointerEvents: "none" }, open: Boolean(n), anchorEl: n, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, onClose: r, disableRestoreFocus: !0, children: [t.showCreatePage && (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(p.A, { disabled: !0, children: (0, c.jsx)(f.A, { children: "Page break options" }) }), (0, c.jsx)(p.A, { onClick: a(l) ? () => o({ role: l }) : () => i({ role: l }), disabled: u, children: a(l) ? (0, c.jsxs)(f.A, { children: [u ? "Cannot create page from root role" : "Create new page from ".concat((0, g.mA)(l)), " "] }) : (0, c.jsx)(f.A, { children: "Collapse page from ".concat((0, g.mA)(l)) }) })] }), t.showDirectReportDirectionOverrides && (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(p.A, { disabled: !0, children: (0, c.jsx)(f.A, { children: "Display options" }) }), (0, c.jsx)(p.A, { onClick: () => { r(), s({ handleChangeDirectReportDirection: d(l), role: l }) }, children: (0, c.jsx)(f.A, { children: "Change direct report direction" }) })] })] }) },
                    b = e => { let { role: t, children: n, additionalDisplayCondition: r = !0 } = e; const { createPageAnchorEl: o, handleCreatePagePopoverOpen: i, handleCreatePagePopoverClose: l, addPageBreak: s, removePageBreak: d, displayOptions: u, isNewPageCreationAllowed: m } = (0, h.A)(); return (0, c.jsxs)(c.Fragment, { children: [r && u.showPrintContextMenu && (0, c.jsx)(y, { displayOptions: u, createPageAnchorEl: o, handleCreatePagePopoverClose: l, role: t, isNewPageCreationAllowed: m, handleCreateNewPage: s, handleRemovePage: d }), (0, a.cloneElement)(n, (e => { let t = { ...e }; return r && u.showPrintContextMenu && (t.onClick = i), t })(n.props))] }) },
                    w = "#eeeeee",
                    z = "#eeeeee";

                function x(e) { let { width: t, height: n, role: r } = e; return (0, c.jsx)(b, { role: r, children: (0, c.jsx)("rect", { width: t, height: n, fill: w, stroke: z }) }) } var A = n(14556),
                    k = n(23993),
                    S = n(61),
                    M = n(22264),
                    E = n(69219),
                    C = n(78396),
                    T = n(9197); const H = e => { let { isLastExpanded: t, roleDims: n, parentDims: r, children: o } = e; const i = function(e) { const t = (0, a.useRef)(null); return (0, a.useEffect)((() => { t.current = e })), t.current }(n),
                        { left: l, top: s } = n || { left: 0, top: 0 },
                        { from: d, to: u } = (0, a.useMemo)((() => { let e; return e = t && i ? { x: i.left, y: i.top } : { x: r.left, y: r.top }, { from: e, to: { x: l, y: s } } }), [l, s, t, r, n]); return (0, c.jsx)(T.c7, { from: d, to: u, children: e => o(e) }) }; var L, I = n(13888),
                    j = n(89656),
                    V = n(356),
                    O = n(57528),
                    R = n(72119); const P = R.Ay.rect.attrs((e => { let { frame: t } = e; return function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { shape: t } = e; return { rx: (() => { switch (t) {
                                    case "circle":
                                        return "50%";
                                    case "oval":
                                        return 15;
                                    case "square":
                                        return 0;
                                    default:
                                        return 5 } })() } }(t) }))(L || (L = (0, O.A)(["\n  ", "\n"])), (e => { let { theme: t, shadowId: n, fill: r, frame: a = {}, highlight: o } = e; const { visible: i, thickness: l, color: s } = a; return "\n      stroke-dasharray: 0;\n      fill:".concat(r || t.palette.common.white, ";\n      stroke-width: ").concat(i || o ? o ? 3 : l : o ? 3 : 0, ";\n      stroke: ").concat(i || o ? o ? t.palette.primary.light : s || t.palette.grey[300] : "none", ";\n      }\n      ").concat(n && "filter: url(#".concat(n, ");"), "\n      &:hover {\n        stroke: ").concat(s || t.palette.primary.light, ";\n      }\n      cursor: pointer;\n      * { \n        cursor: pointer;\n      }\n      ") })); var D; const F = (0, R.Ay)(P)(D || (D = (0, O.A)([""]))); var N; const _ = (0, R.Ay)(o.A)(N || (N = (0, O.A)(["\n  * {\n    cursor: pointer;\n  }\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  :hover ".concat(F, " {\n    stroke: ").concat(t.palette.primary.light, ";\n    stroke-width: 2px;\n    opacity: 0.9;\n  }\n  ") })); var B = n(93865),
                    W = n(18858),
                    U = n(72952),
                    q = n(22818),
                    G = n(59177),
                    K = n(97626),
                    Z = n(81635); var Y = n(45418),
                    X = n(16930),
                    $ = n(43862),
                    Q = n(55143),
                    J = n(47517),
                    ee = n(1645),
                    te = n(90310),
                    ne = n(19857); let re = function(e) { return e.Square = "square", e.Oval = "oval", e.Circle = "circle", e }({}); const ae = e => { let { isLeafCluster: t, id: n, children: r, highlight: a, hidden: o, ColorBarProps: s, BadgeBarProps: d, CardBoxProps: u, PhotoProps: h, showEditActions: m, showActions: p, showAddButtons: f, handleCreateRole: v } = e; if (o) return r; const g = (0, i.w5)(u.backgroundColor, "#e1e1e1", "#444444"),
                        y = { tl: 0, bl: 0, tr: 0, br: 0 },
                        b = s.placement !== U.U$.Cover ? u.backgroundColor || "#ffffff" : s.bgcolor || u.backgroundColor || "#ffffff";
                    d.placement === Q.aK.Left ? (y.tr = u.radius, y.br = u.radius) : d.placement === Q.aK.Top ? (y.bl = u.radius, y.br = u.radius) : d.placement === Q.aK.Hidden && (y.tl = u.radius, y.tr = u.radius, y.bl = u.radius, y.br = u.radius), h.shape === Q.qd.Fill && (h.placement === Q.yS.Right && (y.tr = 0, y.br = 0), h.placement === Q.yS.Left && (y.tl = 0, y.bl = 0)); const w = (0, J.dQ)({ x: 0, y: 0 }, { width: u.width, height: u.height }, y),
                        z = function(e) { const t = []; return e[Q.DQ.Left] && t.push(Q.DQ.Left), e[Q.DQ.Right] && t.push(Q.DQ.Right), e[Q.DQ.Bottom] && t.push(Q.DQ.Bottom), e[Q.DQ.Top] && t.push(Q.DQ.Top), t }(f),
                        x = 16,
                        A = (0, J.mz)(z, { width: u.width, height: u.height }, x, t),
                        k = p || a ? (0, te.e$)(u.stroke, .5) : u.stroke,
                        S = a && u.strokeWidth < 2 ? u.strokeWidth + 2 : u.strokeWidth; return (0, c.jsxs)("g", { id: n, children: [(0, c.jsx)("path", { d: w, fill: b, stroke: k, strokeWidth: S }), m && A.map(((e, r) => { let { position: a, path: o, iconCenter: i } = e, h = 0, m = 0, p = 0, f = 0; return a !== Q.DQ.Bottom && a !== Q.DQ.Top || (d.placement === Q.aK.Left && (h += ne.$), s.placement === U.U$.Left && (h += ee.z), h = -h / 2), a === Q.DQ.Top && (m += u.strokeWidth, p = i.x - 8, f = i.y - 8), a === Q.DQ.Bottom && (m -= u.strokeWidth, f = i.y - 8 - (t ? 8 : 0), p = i.x), a === Q.DQ.Left && (h += u.strokeWidth, f = i.y - 8, p = i.x - 8), a === Q.DQ.Right && (h -= u.strokeWidth, f = i.y - 8, p = i.x), (0, c.jsxs)("g", { transform: "translate(".concat(h, ",").concat(m, ")"), children: [(0, c.jsx)("path", { stroke: k, strokeWidth: u.strokeWidth, fill: b, d: o }, "buttonPath_".concat(a, "_").concat(n)), (0, c.jsxs)("g", { transform: "translate(".concat(p, ",").concat(f, ")"), style: { cursor: "pointer" }, onClick: e => { e.stopPropagation(), v(a) }, onMouseEnter: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "visible"; const e = t.querySelector("path");
                                            e && (e.style.transform = "scale(1.4)", e.style.transformOrigin = "50%") } }, onMouseLeave: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "inherit"; const e = t.querySelector("path");
                                            e && (e.style.transform = "scale(1)", e.style.transformOrigin = "50%") } }, children: [(0, c.jsx)("circle", { cx: 8, cy: 8, r: x, fill: "transparent" }), (0, c.jsx)(l.me, { inlineSvg: !0, name: "plus", variant: "light", fontSize: x, color: g })] })] }, "button-path-".concat(a, "-").concat(r, "}")) })), r] }) }; var oe = n(34336),
                    ie = n(91688),
                    le = n(24241),
                    se = n(34314),
                    ce = n(3342),
                    de = n(45256),
                    ue = n(21773),
                    he = n(80286),
                    me = n(61360),
                    pe = n(20965); const fe = e => { var t, n, r; let { field: o, width: i, handleUpdateFieldHeight: l, downloadFile: s } = e; const d = {},
                            { cards: { spacing: u } } = C.Ay,
                            h = (0, ie.useHistory)(),
                            m = (0, a.useRef)(null),
                            { fontDetails: { showIconLabel: p, showLabel: f, align: v, color: y, italic: b, size: w, strikethrough: z, underline: x, weight: A, border: k, fontFamily: S, dateFormat: M }, type: E } = o,
                            T = 18 * (0, pe.Z)(w),
                            H = 13.5 * (0, pe.Z)(w),
                            L = 16 * (0, pe.Z)(w),
                            { value: I, display: j } = o,
                            [V, O] = (0, a.useState)(null === o || void 0 === o ? void 0 : o.value),
                            [R, P] = (0, a.useState)(null === o || void 0 === o ? void 0 : o.display),
                            [D, F] = (0, a.useState)(!1),
                            [N, _] = (0, a.useState)(!1),
                            B = (0, a.useRef)(null),
                            W = e => t => { t && t.stopPropagation(), h.push(e) },
                            U = { fontWeight: A || 500, fontSize: "".concat(w, "px"), fontFamily: "".concat(S, ", ").concat(de.n[S] || "sans-serif") }; let q = "middle",
                            K = 0; var Z;
                        ((z || x) && (U.textDecoration = "".concat(z ? "line-through" : "", " ").concat(x ? "underline" : "")), b && (U.fontStyle = "italic"), null !== o && void 0 !== o && null !== (t = o.fontDetails) && void 0 !== t && t.case) && (U.textTransform = (null === o || void 0 === o || null === (Z = o.fontDetails) || void 0 === Z ? void 0 : Z.case) || "none");
                        E !== g.ZE.URL && "localUrl" !== E || (U.cursor = "pointer"), "left" === v ? (q = "start", K = 0) : "right" === v ? (q = "end", K = i) : (q = "middle", K = i / 2); const Y = { width: i, x: K, verticalAnchor: "start", textAnchor: q, style: U, fill: y },
                            X = {},
                            $ = w; if (E === g.ZE.ICONPICKLIST) { const e = (0, me.FS)(V),
                                t = (p ? L + (0, pe.Z)(w) * ((0, se.A)((null === e || void 0 === e ? void 0 : e.label) || "") || 1) : 0) + T; "center" === v ? (d.x = Y.x - t / 2, Y.x += (T + L - T / 2) / 2) : "left" === v ? (d.x = 0, Y.x += T + L - T / 2) : "right" === v && (d.x = Y.x - t) } k && "none" !== k && ("round" === k && (X.borderRadius = "5%"), X.outline = "".concat(1, "px solid ").concat(y), X.borderWidth = "".concat(1, "px"), X.outlineOffset = 4, "left" === v ? (Y.x += 4, d.x = (d.x || 0) + 4) : "right" === v && (d.x = (d.x || 0) - 4, Y.x -= 4), Y.y = 5, d.y = Y.y), E === g.ZE.BOOLEAN && (Y.width += $); let Q = [],
                            J = []; if (E === g.ZE.RICHTEXT && I && Array.isArray(I) && I.length > 0) { let e = 0; const t = (0, se.A)("\xa0", { margin: "2px" }),
                                n = Y.width || 0;
                            J = (f ? ["".concat(R, ":"), ...I] : I).map((r => { let a = 1,
                                    o = 0;
                                r.split(" ").forEach((e => { const r = ((0, se.A)(e, { ...U }) || 0) + (t || 0),
                                        i = o + r <= n; return i && (o += r), i || (a++, o = r), r })); const i = e; return e = e + (a - 1 + .71) + .5, { sentence: r, textYEm: i } })) } if ((E === g.ZE.TAGS || E === g.ZE.LOCATION) && I && Array.isArray(I) && I.length > 0) { const e = (0, se.A)("\xa0", { margin: "2px" }),
                                t = I.map((t => ({ word: t, width: ((0, se.A)(t, { outline: k && "none" !== k ? "".concat(1, "px solid ").concat(y) : "none", ...U }) || 0) + 2 * (e || 0) })));
                            Q = ((e, t, n) => e.reduce(((e, r) => { let { word: a, width: o } = r; const i = e[e.length - 1],
                                    l = 4 * t + 2; if (i && (null == n || i.width + o + i.words.length * l < n)) i.words.push(a), i.width += o + t;
                                else { const t = { words: [a], width: o };
                                    e.push(t) } return e }), []))(t, e || 0, Y.width || 0) }

                        function ee() { if (E === g.ZE.BOOLEAN || E === g.ZE.ATTACHMENT || E === g.ZE.SWITCH || E === g.ZE.CURRENCY || E === g.ZE.DATE) { const e = (0, se.A)(j, U) || 0; if ((null === j || void 0 === j || !j.includes(" ")) && e > i) { const t = Math.floor((e || 0) / j.length),
                                        n = Math.floor(i / t);
                                    P((0, G.RP)(j, n - 1)), _(!0) } else N || P(j) } else { const e = f ? "".concat(R, ": ").concat(I) : I; if ("string" === typeof I) { const t = (0, se.A)(e, U) || 0; if (null !== B.current && B.current !== I || t > i) { const e = Math.floor((t || 0) / I.length); let n = Math.floor(i / e); if (I.includes(" ")) { const e = I.split(" ").reduce(((e, t) => t.length > e.length ? t : e), ""); if (e.length < i) return O(I), B.current = I, void F(!1); const t = I.indexOf(e);
                                            t > 0 && (n += t) } O((0, G.RP)(I, n - 1)), B.current = I, F(!0) } else D || O(I) } } } if ((0, a.useLayoutEffect)((() => { var e;
                                ee(); const t = null === m || void 0 === m || null === (e = m.current) || void 0 === e ? void 0 : e.getBBox(); if (t) { const { height: e } = t;
                                    l(e + ("none" !== k ? 9 : 0) + u.text) } else l(0) }), [ee, l, k]), E === g.ZE.URL && !ce.A.isUrlSafe(V)) return null; switch (E) {
                            case g.ZE.BOOLEAN:
                                return V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: "".concat(R || "", " \u2714") }) });
                            case g.ZE.URL:
                                return (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, onClick: (ve = I, e => { e.stopPropagation(), window.open(ve, "_blank") }), children: f ? "".concat(R) : V }) });
                            case "localUrl":
                                return (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, onClick: W(V || ""), children: R }) });
                            case g.ZE.CURRENCY:
                                return V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(V) : V }) });
                            case g.ZE.SWITCH:
                                return V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: "".concat(R || "", " \u2714") }) });
                            case g.ZE.COMPUTED:
                                { let e; try { const t = le.c9.fromISO(V || ""); if (null === t || void 0 === t || !t.isValid) return V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(V) : V }) }); "format" === (null === M || void 0 === M ? void 0 : M.type) && (e = V && le.c9.fromISO(V).toFormat(M.value)), "string" === (null === M || void 0 === M ? void 0 : M.type) && (e = V && le.c9.fromISO(V).toLocaleString(le.c9[M.value])), e || (e = V && le.c9.fromISO(V).toLocaleString(le.c9.DATE_FULL)) } catch (ge) { e = V && V.toString && V.toString() } return e && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(e) : e }) }) }
                            case g.ZE.DATE:
                                { let e; try { M ? "format" === M.type ? e = V && le.c9.fromISO(V).toFormat(M.value) : "string" === M.type && (e = V && le.c9.fromISO(V).toLocaleString(le.c9[M.value])) : e = V && le.c9.fromISO(V).toLocaleString(le.c9.DATE_FULL) } catch (ge) { e = V && V.toString && V.toString() } return e && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(e) : e }) }) }
                            case g.ZE.ATTACHMENT:
                                return (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, onClick: e => { e.stopPropagation(), s({ url: V || "", fileName: "", fileType: "", onError: function() { throw new Error("Function not implemented.") }, isPublicDownload: !1 }) }, children: R }) });
                            case g.ZE.RICHTEXT:
                                var te; return (null === (te = J) || void 0 === te ? void 0 : te.length) > 0 && (0, c.jsx)("g", { style: { ...X }, ref: m, children: J.map(((e, t) => (0, c.jsx)(oe.A, { ...Y, y: "".concat(e.textYEm, "em"), children: e.sentence }, "sub-line-".concat(t, "-").concat(e.sentence)))) });
                            case g.ZE.ROLLUP:
                                return (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(V) : V }) });
                            case g.ZE.TAGS:
                                return (null === I || void 0 === I ? void 0 : I.length) > 0 && (null === (n = Q) || void 0 === n ? void 0 : n.length) > 0 && (0, c.jsxs)("g", { style: { ...X, outline: "none" }, ref: m, children: [f ? (0, c.jsx)("text", { ...Y, y: "0.71em", children: "".concat(R, ":") }) : "", Q.map(((e, t) => { const n = 1.6 * (f ? t + 1.25 : t + .5) + (k && "none" !== k && t > 0 ? t : 0) + "em"; return (0, c.jsx)("text", { ...Y, y: n, children: e.words.map(((e, n) => { const r = k && "none" !== k && n > 0 ? "1em" : "0em"; return (0, c.jsxs)(c.Fragment, { children: ["\xa0", (0, c.jsxs)("tspan", { dx: r, dy: 0, style: { ...X, outlineOffset: "0.2em" }, children: ["\xa0", (0, G.RP)(e, i), "\xa0"] }, "".concat(t, "-").concat(e)), "\xa0"] }) })) }) }))] });
                            case g.ZE.LOCATION:
                                var ne; return (null === (r = Q) || void 0 === r ? void 0 : r.length) > 0 ? (null === I || void 0 === I ? void 0 : I.length) > 0 && (null === (ne = Q) || void 0 === ne ? void 0 : ne.length) > 0 && (0, c.jsxs)("g", { style: { ...X, outline: "none" }, ref: m, children: [f ? (0, c.jsx)("text", { ...Y, y: "0.71em", children: "".concat(R, ":") }) : "", Q.map(((e, t) => { const n = 1.6 * (f ? t + 1.25 : t + .5) + (k && "none" !== k && t > 0 ? t : 0) + "em"; return (0, c.jsx)("text", { ...Y, y: n, children: e.words.map(((n, r) => { const a = k && "none" !== k && r > 0 ? "1em" : "0em"; return (0, c.jsxs)(c.Fragment, { children: ["\xa0", (0, c.jsxs)("tspan", { dx: a, dy: 0, style: { ...X, outlineOffset: "0.2em" }, children: ["\xa0", (0, G.RP)(n, i), "\xa0", r < e.words.length - 1 || t < Q.length - 1 ? "," : ""] }, "".concat(t, "-").concat(n))] }) })) }) }))] }) : "string" === typeof V ? V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(V) : V }) }) : null;
                            case g.ZE.ICONPICKLIST:
                                var re, ae, ue, fe; return V && (null === (re = he.Md[V]) || void 0 === re ? void 0 : re.path) && (0, c.jsxs)("g", { style: X, ref: m, children: [(0, c.jsx)("image", { ...d, href: "".concat("https://assets-organimi.s3.amazonaws.com").concat(null === (ae = he.Md[V]) || void 0 === ae ? void 0 : ae.path), width: "".concat(T, "px"), height: "".concat(H, "px") }), p && (null === (ue = he.Md[V]) || void 0 === ue ? void 0 : ue.label) && (0, c.jsx)(oe.A, { ...Y, children: null === (fe = he.Md[V]) || void 0 === fe ? void 0 : fe.label })] });
                            case g.ZE.STRING:
                            default:
                                return V && (0, c.jsx)("g", { style: X, ref: m, children: (0, c.jsx)(oe.A, { ...Y, children: f ? "".concat(R, ": ").concat(V) : V }) }) } var ve },
                    ve = e => { let { data: t = {}, dataParams: n, dataHeightRef: r, dataIndex: i, isShared: l, PhotoProps: s, CardBoxProps: d, children: u } = e; const h = l && d.sharedDisplayDirection === U.jo.Condensed,
                            m = l && d.sharedDisplayDirection !== U.jo.Condensed,
                            [p, f] = (0, a.useState)(0),
                            v = (0, a.useRef)({}),
                            g = (0, a.useRef)({}),
                            y = (0, a.useRef)(null),
                            { offsetY: b, offsetX: w, width: z } = n,
                            { cards: { spacing: x } } = C.Ay,
                            A = Object.keys(t).filter((e => { var n; return "member.id" !== e && (null === (n = t[e]) || void 0 === n ? void 0 : n.value) })),
                            { downloadFile: k } = (0, ue.A)(),
                            S = e => t => { g.current[e] = t || 0 },
                            M = [Q.yS.Above, Q.yS.AboveLeft, Q.yS.AboveRight, Q.yS.Center, Q.yS.TopLeft, Q.yS.TopRight].includes(s.placement) ? 0 : -x.text;
                        (0, a.useLayoutEffect)((() => { let e = 0; const t = {},
                                n = { ...v.current }; let a = !1; const o = g.current,
                                l = Object.keys(n).toString() !== A.toString(); for (const r of A) { const i = o[r];
                                t[r] = e, n[r] !== t[r] && (a = !0), e += i } v.current = t, (a || l) && f((e => e + 1)), r.current[i] = e - x.text })); const E = v.current,
                            T = (() => { const e = [Q.yS.Left, Q.yS.Right].includes(s.placement); if (l && s.shape === Q.qd.Fill) return 0; if (!s.visible) return s.shape === Q.qd.Fill && e ? x.default : 0; if (h || e) { let e = s.size + M - (r.current[i] || 0); if (s.shape !== Q.qd.Fill || l && d.sharedDisplayDirection !== U.jo.Condensed || (e = d.height - (r.current[i] || 0)), e > 0) return e / 2; if (s.shape === Q.qd.Fill) return x.default } return 0 })(),
                            H = g.current[A[A.length - 1]] ? g.current[A[A.length - 1]] - x.text : 0; return (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(o.A, { className: "chart-node", left: w, top: b + T, innerRef: y, children: A.map(((e, n) => { var r; const a = E[A[n]] || 0,
                                        i = null === (r = t[e]) || void 0 === r ? void 0 : r.value; let l = i || ""; try { l = "string" === typeof i ? i : JSON.stringify(i) } catch (s) { l = (null === i || void 0 === i ? void 0 : i.length) || i } return (0, c.jsx)(o.A, { top: a, children: (0, c.jsx)(fe, { field: t[e], width: z, handleUpdateFieldHeight: S(e), downloadFile: k }) }, "variableField-".concat(e, "-").concat(t[e].modelId, "-").concat(l)) })) }), "function" === typeof u && u(w, b + (E[A[A.length - 1]] || 0) + H + (m ? T : 0), z)] }) }; var ge = n(65173),
                    ye = n.n(ge);

                function be() { return be = Object.assign || function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, be.apply(this, arguments) }

                function we(e) { var t = e.id,
                        n = e.children,
                        r = function(e, t) { if (null == e) return {}; var n, r, a = {},
                                o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, ["id", "children"]); return a.createElement("defs", null, a.createElement("clipPath", be({ id: t }, r), n)) }

                function ze() { return ze = Object.assign || function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, ze.apply(this, arguments) }

                function xe(e) { var t = e.id,
                        n = e.cx,
                        r = e.cy,
                        o = e.r,
                        i = function(e, t) { if (null == e) return {}; var n, r, a = {},
                                o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, ["id", "cx", "cy", "r"]); return a.createElement(we, { id: t }, a.createElement("circle", ze({ cx: n, cy: r, r: o }, i))) } we.propTypes = { id: ye().string.isRequired, children: ye().node }, xe.propTypes = { id: ye().string.isRequired, cx: ye().oneOfType([ye().string, ye().number]), cy: ye().oneOfType([ye().string, ye().number]), r: ye().oneOfType([ye().string, ye().number]) }; const Ae = e => { let { shape: t, clipCoords: n, id: r, imageFallbackUrl: a, photoData: o, handleImageErrorForInitialsFallback: i, aspectRatio: l = "xMidYMid slice", ...s } = e; return (0, c.jsxs)(c.Fragment, { children: [n && "circle" === t && (0, c.jsx)(xe, { id: r, cx: n.cx, cy: n.cy, r: n.r }) || n && "oval" === t && (0, c.jsx)(we, { id: r, children: (0, c.jsx)("rect", { x: n.cx - n.r, y: n.cy - n.r, width: 2 * n.r, height: 2 * n.r, rx: n.r / 4 }) }), (0, c.jsx)("image", { ...s, className: "thumbnail", preserveAspectRatio: l, onLoad: e => { e.target.setAttribute("processed", "true") }, onError: e => { "initials" === (null === o || void 0 === o ? void 0 : o.fallbackPhotoAvatar) ? "function" === typeof i && i(): (e.target.setAttribute("processed", "true"), e.target.setAttribute("href", a)) } }, "".concat(a).concat(r))] }) }; var ke = n(25848),
                    Se = n(80539); const Me = e => { let { shape: t, x: n, y: r, width: a, height: o, name: i, overrideColor: l = null } = e; return (0, c.jsx)("foreignObject", { x: n, y: r, width: a, height: o || a, children: (0, c.jsx)(Se.A, { shape: t, width: a, height: o || a, name: i, overrideColor: l, style: { position: "fixed" } }) }) }; var Ee = n(68840),
                    Ce = n(3523);

                function Te(e) { try { const t = (0, Ee.Or)(e); return t || ke } catch (t) { return console.log("ERROR getting avatar image: ", t), ke } } const He = "#005DC9",
                    Le = [Q.yS.Above, Q.yS.AboveLeft, Q.yS.AboveRight];

                function Ie(e) { let { CardBoxProps: t, ColorBarProps: n, PhotoProps: r, photoCoords: i = {}, photoData: s, roleId: d, personId: u, socialData: h, isShared: m } = e; const p = t.stroke,
                        f = "".concat(t.strokeWidth, "px"),
                        v = t.backgroundColor,
                        g = (null === h || void 0 === h ? void 0 : h.linkedIn) && (null === h || void 0 === h ? void 0 : h.linkedIn.length) && ce.A.isUrlSafe(h.linkedIn) && ce.A.isLinkedInUrlValid(h.linkedIn) && i.visible && !s.hidden,
                        { value: y, avatarId: b, name: w, fallbackPhotoAvatar: z } = s,
                        x = i.cx - i.r,
                        k = i.cy,
                        S = i.photoSize,
                        M = Math.min(.3 * S, 20),
                        E = y || b && "initials" !== b && Te(b),
                        [C, T] = (0, a.useState)("initials" === b),
                        [H, L] = (0, a.useState)(!1),
                        I = Te(s.fallbackPhotoAvatar),
                        j = (0, Ce.K7)(E),
                        V = (0, A.d4)((e => (0, Y.Ar)(e.people, u)));
                    (0, a.useEffect)((() => { L(!1), T("initials" === b) }), [z, b]); const O = [Q.yS.Left, Q.yS.Right].includes(r.placement),
                        R = r.shape === Q.qd.Fill && O ? "xMidYMax slice" : "xMidYMid slice",
                        P = r.shape === Q.qd.Fill && O && !m ? t.height - ((null === n || void 0 === n ? void 0 : n.innerHeight) || 0) : r.size; return (0, c.jsxs)(c.Fragment, { children: [i.visible && !s.hidden && Le.includes(null === r || void 0 === r ? void 0 : r.placement) && ((r.shape === Q.qd.Square || r.shape === Q.qd.Oval) && (0, c.jsx)("rect", { rx: r.shape === Q.qd.Oval ? 8 : 0, refY: r.shape === Q.qd.Oval ? 8 : 0, stroke: p, fill: v, strokeWidth: f, x: x - 4, y: k - 4, width: S + 8, height: S + 8, vectorEffect: "non-scaling-stroke" }) || r.shape === Q.qd.Circle && (0, c.jsx)("circle", { stroke: p, fill: v, strokeWidth: f, cx: x + S / 2, cy: k + S / 2, r: (S + 8) / 2, vectorEffect: "non-scaling-stroke" })), i.visible && !s.hidden && (!H && E && (0, c.jsx)(Ae, { shape: i.shape, href: j, aspectRatio: R, x: x, y: k, width: S, height: P, clipCoords: i.clip, clipPath: "url(#".concat(i.shape).concat(d).concat(u, "Photo)"), imageFallbackUrl: I, photoData: s, handleImageErrorForInitialsFallback: () => { L(!0), T(!0) }, id: "".concat(i.shape).concat(d).concat(u, "Photo") }) || C && (0, c.jsx)(Me, { shape: i.shape, name: w, href: E, x: x, y: k, width: S, height: P, clipCoords: i.clip, clipPath: "url(#".concat(i.shape, "Photo)"), id: "".concat(i.shape, "Photo"), overrideColor: null === V || void 0 === V ? void 0 : V.memberPhotoColor })), g && (0, c.jsxs)(o.A, { left: x + S - M - 4, top: k + P - M - 4, children: [(0, c.jsx)("rect", { height: M, width: M, fill: "#ffffff" }), (0, c.jsx)("a", { href: h.linkedIn, target: "_blank", onClick: e => { e && e.stopPropagation() }, children: (0, c.jsx)(l.me, { inlineSvg: !0, color: He, fontSize: M, name: "linkedin", variant: "brands" }) })] })] }) } var je = n(80192),
                    Ve = n(55005),
                    Oe = n(6562),
                    Re = n(34944),
                    Pe = n(279),
                    De = n(30752),
                    Fe = n(47088); const Ne = e => { var t, n; return (null === e || void 0 === e || null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.type) || C.XD.TRADITIONAL },
                    _e = e => { var t; return null === (t = e.chart.info) || void 0 === t ? void 0 : t.legend },
                    Be = (0, je.Mz)(Ve.A, (e => e.reduce(((e, t) => (e[t.id] = t, e)), g.TU.reduce(((e, t) => (e[t.id] = t, e)), {})))),
                    We = (0, je.Mz)(Oe.A, Re.A, (e => { var t; return null === (t = e.chart.info) || void 0 === t ? void 0 : t.legend }), Be, ((e, t, n, r) => { const a = {},
                            o = (null === n || void 0 === n ? void 0 : n.rules) || [],
                            i = !(!n || !n.id) && o.reduce(((e, t) => { let n = []; return t.comparators && t.comparators.length && t.displayType === Fe.tI.ICON && (n = t.comparators.filter((e => !!e.field)).map((e => { let t = "string"; if ("unfilled" === e.field.name || "position" === e.field.name) t = "chart";
                                    else { const n = r[e.field.id];
                                        t = ((null === n || void 0 === n ? void 0 : n.type) || "string").toLowerCase() } return { ...e, type: t } })), e[t.id] = { comparators: n, join: t.join || "AND" }), e }), {}) || {}; return t.reduce(((t, n) => { const { id: a } = n, o = (0, Pe.b)({ role: n, ruleTests: i, roster: e, fieldMap: r }); return t[a] = o, t }), a), a })),
                    Ue = De.A,
                    qe = (0, je.Mz)(E.uo, I.$x, ((e, t) => { var n, r; return "print" !== e && "printPreview" !== e || ((null === t || void 0 === t || null === (n = t.layout) || void 0 === n || null === (r = n.chart) || void 0 === r ? void 0 : r.linkedIn) || !1) })),
                    Ge = e => { const { info: t } = e.chart; return t },
                    Ke = (0, je.Mz)(Ge, (e => { var t; return (null === e || void 0 === e || null === (t = e.displaySettings) || void 0 === t ? void 0 : t.layout) || "topdown" })),
                    Ze = e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.alias }; var Ye = n(10242); const Xe = e => { var t; let { field: n, width: r, handleUpdateFieldHeight: o, downloadFile: i } = e; const { cards: { spacing: l } } = C.Ay, s = (0, ie.useHistory)(), d = (0, a.useRef)(), { fontDetails: { showLabel: u, color: h, italic: m, size: p, strikethrough: f, underline: v, weight: y, border: b, fontFamily: w }, type: z, value: x, display: A } = n, k = e => t => { t && t.stopPropagation(), s.push(e) }, S = { fontWeight: y || 500, fontSize: "".concat(p, "px"), fontFamily: "".concat(w, ", ").concat(de.n[w] || "sans-serif") }; let M = "middle",
                            E = 0; var T;
                        ((f || v) && (S.textDecoration = "".concat(f ? "line-through" : "", " ").concat(v ? "underline" : "")), m && (S.fontStyle = "italic"), null !== n && void 0 !== n && null !== (t = n.fontDetails) && void 0 !== t && t.case) && (S.textTransform = (null === n || void 0 === n || null === (T = n.fontDetails) || void 0 === T ? void 0 : T.case) || "none");
                        z !== g.ZE.URL && "localUrl" !== z || (S.cursor = "pointer"), M = "middle", E = r / 2; const H = { width: r, x: E, verticalAnchor: "start", textAnchor: M, style: S, fill: h },
                            L = {},
                            I = p; switch (b && "none" !== b && ("round" === b && (L.borderRadius = "5%"), L.outline = "".concat(2, "px solid ").concat(h), L.outlineOffset = 4, H.y = 6), z === g.ZE.BOOLEAN && (H.width += I), (0, a.useEffect)((() => { var e; const { height: t } = (null === d || void 0 === d || null === (e = d.current) || void 0 === e ? void 0 : e.getBBox()) || {};
                            o(t + ("none" !== b ? 12 : 0) + l.text) })), z) {
                            case g.ZE.BOOLEAN:
                                return x && (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsxs)(oe.A, { ...H, children: [A, " \u2713"] }) });
                            case g.ZE.URL:
                                return (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, onClick: (j = x, () => { window.open(j, "_blank") }), children: u ? "".concat(A, ": ").concat(x) : x }) });
                            case "localUrl":
                                return (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, onClick: k(x), children: A }) });
                            case g.ZE.CURRENCY:
                                return x && (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, children: u ? "".concat(A, ": ").concat(x) : x }) });
                            case g.ZE.SWITCH:
                                return x && (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsxs)(oe.A, { ...H, children: [A, " \u2713"] }) });
                            case g.ZE.DATE:
                                { let e; try { e = x && le.c9.fromISO(x).toLocaleString(le.c9.DATE_FULL) } catch (V) { e = x && x.toString && x.toString() } return e && (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, children: u ? "".concat(A, ": ").concat(e) : e }) }) }
                            case g.ZE.ATTACHMENT:
                                return (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, onClick: e => { e.stopPropagation(), i({ url: x }) }, children: A }) });
                            case g.ZE.ROLLUP:
                                return (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, children: u ? "".concat(A, ": ").concat(x) : x }) });
                            case g.ZE.STRING:
                            default:
                                return x && (0, c.jsx)("g", { style: L, ref: d, children: (0, c.jsx)(oe.A, { ...H, children: u ? "".concat(A, ": ").concat(x) : x }) }) } var j },
                    $e = e => { let { data: t = {}, dataParams: n, children: r, dataTopHeightRef: i } = e; const { downloadFile: l } = (0, ue.A)(), [s, d] = (0, a.useState)(0), u = (0, a.useRef)({}), h = (0, a.useRef)({}), m = (0, a.useRef)(null), { offsetY: p, offsetX: f, width: v } = n || { offsetY: 0, offsetX: 0, width: 0 }, g = Object.keys(t).filter((e => { var n; return "member.id" !== e && (null === (n = t[e]) || void 0 === n ? void 0 : n.value) })), y = e => t => { h.current[e] = t || 0 };
                        (0, a.useEffect)((() => { let e = 0,
                                t = {},
                                n = { ...u.current },
                                r = !1,
                                a = h.current,
                                o = Object.keys(n).toString() !== g.toString(); for (let i of g) { let o = a[i];
                                t[i] = e, n[i] !== t[i] && (r = !0), e += o } u.current = t, (r || o) && d((e => e + 1)), i.current = e })); const b = h.current[g[g.length - 1]] || 0; let w = u.current; return (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(o.A, { className: "chart-node", left: f, top: p, innerRef: m, children: g.map(((e, n) => { let r = w[g[n]] || 0; return (0, c.jsx)(o.A, { top: r, children: (0, c.jsx)(Xe, { field: t[e], width: v, index: n, handleUpdateFieldHeight: y(e), downloadFile: l }) }, "variableField-".concat(e, "-").concat(t[e].modelId)) })) }), r(f, p + (w[g[g.length - 1]] || 0) + b)] }) },
                    Qe = e => { let { data: t, dataTopHeightRef: n, dataParams: r, children: a } = e; return t ? (0, c.jsx)($e, { data: t, dataParams: r, dataTopHeightRef: n, children: a }) : a(0, 0) }; var Je = n(48853),
                    et = n(74656),
                    tt = n(8122),
                    nt = function(e) { return e.Move = "move", e.Edit = "edit", e.View = "view", e.More = "more", e }(nt || {}); const rt = e => { let { showEditActions: t, CardBoxProps: n, PhotoProps: r, handleEditClick: a, handleViewClick: o, handleMoreMenuClick: s, innerRef: d } = e; const u = (0, te.e$)(n.stroke, .5),
                        h = n.radius,
                        m = [];
                    t && (m.push(nt.Move), m.push(nt.Edit)), m.push(nt.More); const p = 14,
                        f = 22,
                        v = 22 * m.length + 8,
                        g = n.width - v,
                        y = r.placement === Q.yS.Right && r.shape === Q.qd.Fill ? 0 : r.placement === Q.yS.Above ? g : r.placement === Q.yS.AboveRight ? 0 : g,
                        b = 0 === y ? n.radius : 0,
                        w = y === n.width - v ? 0 + n.radius : 0,
                        z = "\n    M 0,".concat(b, "\n    L 0,-").concat(f - h, "\n    S 0,-").concat(f, " ").concat(h, ",-").concat(f, "\n    L ").concat(v - h, ",-").concat(f, "\n    S ").concat(v, ",-").concat(f, " ").concat(v, ",-").concat(f - h, "\n    L ").concat(v, ",").concat(w, "\n  "),
                        x = "\n    M ".concat(n.strokeWidth / 2, ",0\n    L ").concat(v - n.strokeWidth / 2, ",0\n  ");

                    function A(e) { const t = (0, i.w5)(n.backgroundColor, "#e1e1e1", "#444444"),
                            r = "light"; switch (e) {
                            case nt.Move:
                                return (0, c.jsx)(tt.A, { width: p, height: p });
                            case nt.Edit:
                                return (0, c.jsx)(l.me, { inlineSvg: !0, name: "pencil", variant: r, color: t, fontSize: p });
                            case nt.View:
                                return (0, c.jsx)(l.me, { inlineSvg: !0, name: "user", variant: r, color: t, fontSize: p });
                            case nt.More:
                                return (0, c.jsx)(l.me, { inlineSvg: !0, name: "ellipsis-vertical", variant: r, color: t, fontSize: p });
                            default:
                                return null } } return (0, c.jsxs)("g", { transform: "translate(".concat(y, ",0)"), id: "roleActionBar", children: [(0, c.jsx)("path", { d: z, stroke: u, strokeWidth: n.strokeWidth, fill: n.backgroundColor }), (0, c.jsx)("path", { d: x, fill: n.backgroundColor, stroke: n.backgroundColor, strokeWidth: 2 * n.strokeWidth }), (0, c.jsx)("g", { transform: "translate( ".concat(8, ", ").concat(-18, ")"), children: m.map(((e, t) => (0, c.jsxs)("g", { ref: e === nt.Move ? d : null, transform: "translate(".concat(22 * t, ", 0)"), width: 22, height: 22, onClick: t => { var n;
                                    t.stopPropagation(), t.preventDefault(), null === (n = (e => { switch (e) {
                                            case nt.Move:
                                                return;
                                            case nt.Edit:
                                                return a;
                                            case nt.View:
                                                return o;
                                            case nt.More:
                                                return s } })(e)) || void 0 === n || n(t) }, onMouseEnter: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "visible"; const e = t.querySelector("path");
                                        e && (e.style.transform = "scale(1.4)", e.style.transformOrigin = "50%") } }, onMouseLeave: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "inherit"; const e = t.querySelector("path");
                                        e && (e.style.transform = "scale(1)", e.style.transformOrigin = "50%") } }, children: [(0, c.jsx)("rect", { width: 22, height: 22, fill: "transparent" }), A(e)] }, "actionBar_".concat(e)))) })] }) }; var at = n(19367),
                    ot = n(43940),
                    it = n(94642),
                    lt = n(18519); var st = n(37294); const ct = e => { let { CardBoxProps: t, recursive: n, direct: r, visible: a, position: o } = e; if (!a || !o || o === U.d5.below) return null; let i = 0; const l = r > 0,
                        s = n > 0 && n !== r; let d = 0,
                        u = 0;
                    s && (d = Math.max((0, se.A)(n.toString(), { fontSize: 12, fontFamily: "Inter" }) || 0, 20), d += 8, null !== d && (i += d + (l ? 4 : 0))), l && (u = Math.max((0, se.A)(r.toString(), { fontSize: 12, fontFamily: "Inter" }) || 0, 20), u += 8, null !== u && (i += u)); const h = o === U.d5.insideRight ? t.width - i - 8 : 8,
                        m = t.height - 16 - 8,
                        p = "".concat(6, "px"),
                        f = "".concat(Math.max(t.strokeWidth, 1), "px"); return l || s ? (0, c.jsxs)("g", { transform: "translate(".concat(h, ",").concat(m, ")"), children: [s && (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)("rect", { rx: p, ry: p, width: d, height: 16, fill: "#ffffff", stroke: t.stroke, strokeWidth: f }), (0, c.jsx)("text", { x: d / 2, y: 8, dy: ".33em", fill: "#000000", fontSize: 12, textAnchor: "middle", children: n })] }), l && (0, c.jsxs)("g", { transform: "translate(".concat(d + (s ? 4 : 0), ",0)"), children: [(0, c.jsx)("rect", { rx: p, ry: p, radius: 6, width: u, height: 16, fill: st.Qs.Neutrals[250] }), (0, c.jsx)("text", { x: u / 2, y: 8, dy: ".33em", fill: "#000000", fontSize: 12, textAnchor: "middle", children: r })] })] }) : null }; var dt = n(36735),
                    ut = n(43331); var ht = n(299),
                    mt = n(91917); const pt = (0, mt.A)(a.createElement("path", { d: "M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12.5 8H11v6l4.75 2.85.75-1.23-4-2.37V8zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" }), "AccessAlarm"),
                    ft = (0, mt.A)(a.createElement("path", { d: "M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12.5 8H11v6l4.75 2.85.75-1.23-4-2.37V8zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" }), "AccessAlarmOutlined"),
                    vt = (0, mt.A)(a.createElement("path", { d: "M15.87 15.25l-3.37-2V8.72c0-.4-.32-.72-.72-.72h-.06c-.4 0-.72.32-.72.72v4.72c0 .35.18.68.49.86l3.65 2.19c.34.2.78.1.98-.24.21-.35.1-.8-.25-1zm5.31-10.24L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AccessAlarmRounded"),
                    gt = (0, mt.A)(a.createElement("path", { d: "M22 5.7l-4.6-3.9-1.3 1.5 4.6 3.9L22 5.7zM7.9 3.4L6.6 1.9 2 5.7l1.3 1.5 4.6-3.8zM12.5 8H11v6l4.7 2.9.8-1.2-4-2.4V8zM12 4c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm0 16c-3.9 0-7-3.1-7-7s3.1-7 7-7 7 3.1 7 7-3.1 7-7 7z" }), "AccessAlarms"),
                    yt = (0, mt.A)(a.createElement("path", { d: "M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12.5 8H11v6l4.75 2.85.75-1.23-4-2.37V8zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" }), "AccessAlarmSharp"),
                    bt = (0, mt.A)(a.createElement("path", { d: "M22 5.7l-4.6-3.9-1.3 1.5 4.6 3.9L22 5.7zM7.9 3.4L6.6 1.9 2 5.7l1.3 1.5 4.6-3.8zM12.5 8H11v6l4.7 2.9.8-1.2-4-2.4V8zM12 4c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm0 16c-3.9 0-7-3.1-7-7s3.1-7 7-7 7 3.1 7 7-3.1 7-7 7z" }), "AccessAlarmsOutlined"),
                    wt = (0, mt.A)(a.createElement("path", { d: "M15.87 15.25l-3.37-2V8.72c0-.4-.32-.72-.72-.72h-.06c-.4 0-.72.32-.72.72v4.72c0 .35.18.68.49.86l3.65 2.19c.34.2.78.1.98-.24.21-.35.1-.8-.25-1zm5.31-10.24L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AccessAlarmsRounded"),
                    zt = (0, mt.A)(a.createElement("path", { d: "M22 5.7l-4.6-3.9-1.3 1.5 4.6 3.9L22 5.7zM7.9 3.4L6.6 1.9 2 5.7l1.3 1.5 4.6-3.8zM12.5 8H11v6l4.7 2.9.8-1.2-4-2.4V8zM12 4c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm0 16c-3.9 0-7-3.1-7-7s3.1-7 7-7 7 3.1 7 7-3.1 7-7 7z" }), "AccessAlarmsSharp"),
                    xt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.9 0-7 3.1-7 7s3.1 7 7 7 7-3.1 7-7-3.1-7-7-7zm3.7 10.9L11 14V8h1.5v5.3l4 2.4-.8 1.2z", opacity: ".3" }), a.createElement("path", { d: "M22 5.7l-4.6-3.9-1.3 1.5 4.6 3.9zM12.5 8H11v6l4.7 2.9.8-1.2-4-2.4zM12 4c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm0 16c-3.9 0-7-3.1-7-7s3.1-7 7-7 7 3.1 7 7-3.1 7-7 7zM7.9 3.4L6.6 1.9 2 5.7l1.3 1.5z" })), "AccessAlarmsTwoTone"),
                    At = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm3.75 10.85L11 14V8h1.5v5.25l4 2.37-.75 1.23z", opacity: ".3" }), a.createElement("path", { d: "M12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm.5-12H11v6l4.75 2.85.75-1.23-4-2.37zM22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53z" })), "AccessAlarmTwoTone"),
                    kt = (0, mt.A)(a.createElement("path", { d: "M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z" }), "Accessibility"),
                    St = (0, mt.A)(a.createElement("path", { d: "M20.5 6c-2.61.7-5.67 1-8.5 1s-5.89-.3-8.5-1L3 8c1.86.5 4 .83 6 1v13h2v-6h2v6h2V9c2-.17 4.14-.5 6-1l-.5-2zM12 6c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" }), "AccessibilityNew"),
                    Mt = (0, mt.A)(a.createElement("path", { d: "M20.5 6c-2.61.7-5.67 1-8.5 1s-5.89-.3-8.5-1L3 8c1.86.5 4 .83 6 1v13h2v-6h2v6h2V9c2-.17 4.14-.5 6-1l-.5-2zM12 6c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" }), "AccessibilityNewOutlined"),
                    Et = (0, mt.A)(a.createElement("path", { d: "M20.75 6.99c-.14-.55-.69-.87-1.24-.75-2.38.53-5.03.76-7.51.76s-5.13-.23-7.51-.76c-.55-.12-1.1.2-1.24.75-.14.56.2 1.13.75 1.26 1.61.36 3.35.61 5 .75v12c0 .55.45 1 1 1s1-.45 1-1v-5h2v5c0 .55.45 1 1 1s1-.45 1-1V9c1.65-.14 3.39-.39 4.99-.75.56-.13.9-.7.76-1.26zM12 6c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" }), "AccessibilityNewRounded"),
                    Ct = (0, mt.A)(a.createElement("path", { d: "M20.5 6c-2.61.7-5.67 1-8.5 1s-5.89-.3-8.5-1L3 8c1.86.5 4 .83 6 1v13h2v-6h2v6h2V9c2-.17 4.14-.5 6-1l-.5-2zM12 6c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" }), "AccessibilityNewSharp"),
                    Tt = (0, mt.A)(a.createElement("path", { d: "M20.5 6c-2.61.7-5.67 1-8.5 1s-5.89-.3-8.5-1L3 8c1.86.5 4 .83 6 1v13h2v-6h2v6h2V9c2-.17 4.14-.5 6-1l-.5-2zM12 6c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" }), "AccessibilityNewTwoTone"),
                    Ht = (0, mt.A)(a.createElement("path", { d: "M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z" }), "AccessibilityOutlined"),
                    Lt = (0, mt.A)(a.createElement("path", { d: "M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm8 7h-5v12c0 .55-.45 1-1 1s-1-.45-1-1v-5h-2v5c0 .55-.45 1-1 1s-1-.45-1-1V9H4c-.55 0-1-.45-1-1s.45-1 1-1h16c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AccessibilityRounded"),
                    It = (0, mt.A)(a.createElement("path", { d: "M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z" }), "AccessibilitySharp"),
                    jt = (0, mt.A)(a.createElement("path", { d: "M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z" }), "AccessibilityTwoTone"),
                    Vt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "4", r: "2" }), a.createElement("path", { d: "M19 13v-2c-1.54.02-3.09-.75-4.07-1.83l-1.29-1.43c-.17-.19-.38-.34-.61-.45-.01 0-.01-.01-.02-.01H13c-.35-.2-.75-.3-1.19-.26C10.76 7.11 10 8.04 10 9.09V15c0 1.1.9 2 2 2h5v5h2v-5.5c0-1.1-.9-2-2-2h-3v-3.45c1.29 1.07 3.25 1.94 5 1.95zm-6.17 5c-.41 1.16-1.52 2-2.83 2-1.66 0-3-1.34-3-3 0-1.31.84-2.41 2-2.83V12.1c-2.28.46-4 2.48-4 4.9 0 2.76 2.24 5 5 5 2.42 0 4.44-1.72 4.9-4h-2.07z" })), "Accessible"),
                    Ot = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "17", cy: "4.54", r: "2" }), a.createElement("path", { d: "M14 17h-2c0 1.65-1.35 3-3 3s-3-1.35-3-3 1.35-3 3-3v-2c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5zm3-3.5h-1.86l1.67-3.67C17.42 8.5 16.44 7 14.96 7h-5.2c-.81 0-1.54.47-1.87 1.2L7.22 10l1.92.53L9.79 9H12l-1.83 4.1c-.6 1.33.39 2.9 1.85 2.9H17v5h2v-5.5c0-1.1-.9-2-2-2z" })), "AccessibleForward"),
                    Rt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "18", cy: "4.54", r: "2" }), a.createElement("path", { d: "M15 17h-2c0 1.65-1.35 3-3 3s-3-1.35-3-3 1.35-3 3-3v-2c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5zm3-3.5h-1.86l1.67-3.67C18.42 8.5 17.44 7 15.96 7h-5.2c-.81 0-1.54.47-1.87 1.2L8.22 10l1.92.53.65-1.53H13l-1.83 4.1c-.6 1.33.39 2.9 1.85 2.9H18v5h2v-5.5c0-1.1-.9-2-2-2z" })), "AccessibleForwardOutlined"),
                    Pt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "18", cy: "4.54", r: "2" }), a.createElement("path", { d: "M15 17h-2c0 1.65-1.35 3-3 3s-3-1.35-3-3 1.35-3 3-3v-2c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5zm3-3.5h-1.86l1.67-3.67C18.42 8.5 17.44 7 15.96 7h-5.2c-.81 0-1.54.47-1.87 1.2l-.28.76c-.21.56.11 1.17.68 1.33.49.14 1-.11 1.2-.58l.3-.71H13l-1.83 4.1c-.6 1.33.39 2.9 1.85 2.9H18v4c0 .55.45 1 1 1s1-.45 1-1v-4.5c0-1.1-.9-2-2-2z" })), "AccessibleForwardRounded"),
                    Dt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "18", cy: "4.54", r: "2" }), a.createElement("path", { d: "M15 17h-2c0 1.65-1.35 3-3 3s-3-1.35-3-3 1.35-3 3-3v-2c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5zm5-3.5h-3.86l1.67-3.67C18.42 8.5 17.44 7 15.96 7h-5.2c-.81 0-1.54.47-1.87 1.2L8.22 10l1.92.53.65-1.53H13l-3.12 7H18v5h2v-7.5z" })), "AccessibleForwardSharp"),
                    Ft = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "18", cy: "4.54", r: "2" }), a.createElement("path", { d: "M15 17h-2c0 1.65-1.35 3-3 3s-3-1.35-3-3 1.35-3 3-3v-2c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5zm3-3.5h-1.86l1.67-3.67C18.42 8.5 17.44 7 15.96 7h-5.2c-.81 0-1.54.47-1.87 1.2L8.22 10l1.92.53.65-1.53H13l-1.83 4.1c-.6 1.33.39 2.9 1.85 2.9H18v5h2v-5.5c0-1.1-.9-2-2-2z" })), "AccessibleForwardTwoTone"),
                    Nt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "4", r: "2" }), a.createElement("path", { d: "M19 13v-2c-1.54.02-3.09-.75-4.07-1.83l-1.29-1.43c-.17-.19-.38-.34-.61-.45-.01 0-.01-.01-.02-.01H13c-.35-.2-.75-.3-1.19-.26C10.76 7.11 10 8.04 10 9.09V15c0 1.1.9 2 2 2h5v5h2v-5.5c0-1.1-.9-2-2-2h-3v-3.45c1.29 1.07 3.25 1.94 5 1.95zm-9 7c-1.66 0-3-1.34-3-3 0-1.31.84-2.41 2-2.83V12.1c-2.28.46-4 2.48-4 4.9 0 2.76 2.24 5 5 5 2.42 0 4.44-1.72 4.9-4h-2.07c-.41 1.16-1.52 2-2.83 2z" })), "AccessibleOutlined"),
                    _t = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "4", r: "2" }), a.createElement("path", { d: "M19 11.9c0-.49-.36-.89-.84-.97-1.25-.21-2.43-.88-3.23-1.76l-1.29-1.43c-.17-.19-.38-.34-.61-.45-.01 0-.01-.01-.02-.01H13c-.37-.21-.78-.31-1.25-.25C10.73 7.15 10 8.07 10 9.1V15c0 1.1.9 2 2 2h5v4c0 .55.45 1 1 1s1-.45 1-1v-4.5c0-1.1-.9-2-2-2h-3v-3.45c1 .83 2.4 1.54 3.8 1.82.62.13 1.2-.34 1.2-.97zM12.83 18c-.41 1.16-1.52 2-2.83 2-1.66 0-3-1.34-3-3 0-1.31.84-2.41 2-2.83V12.1c-2.28.46-4 2.48-4 4.9 0 2.76 2.24 5 5 5 2.42 0 4.44-1.72 4.9-4h-2.07z" })), "AccessibleRounded"),
                    Bt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "4", r: "2" }), a.createElement("path", { d: "M19 13v-2c-1.54.02-3.09-.75-4.07-1.83l-1.29-1.43c-.17-.19-.38-.34-.61-.45-.01 0-.01-.01-.02-.01H13c-.37-.21-.78-.31-1.25-.25C10.73 7.15 10 8.07 10 9.1V17h7v5h2v-7.5h-5v-3.45c1.29 1.07 3.25 1.94 5 1.95zm-6.17 5c-.41 1.16-1.52 2-2.83 2-1.66 0-3-1.34-3-3 0-1.31.84-2.41 2-2.83V12.1c-2.28.46-4 2.48-4 4.9 0 2.76 2.24 5 5 5 2.42 0 4.44-1.72 4.9-4h-2.07z" })), "AccessibleSharp"),
                    Wt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "4", r: "2" }), a.createElement("path", { d: "M19 13v-2c-1.54.02-3.09-.75-4.07-1.83l-1.29-1.43c-.17-.19-.38-.34-.61-.45-.01 0-.01-.01-.02-.01H13c-.35-.2-.75-.3-1.19-.26C10.76 7.11 10 8.04 10 9.09V15c0 1.1.9 2 2 2h5v5h2v-5.5c0-1.1-.9-2-2-2h-3v-3.45c1.29 1.07 3.25 1.94 5 1.95zm-6.17 5c-.41 1.16-1.52 2-2.83 2-1.66 0-3-1.34-3-3 0-1.31.84-2.41 2-2.83V12.1c-2.28.46-4 2.48-4 4.9 0 2.76 2.24 5 5 5 2.42 0 4.44-1.72 4.9-4h-2.07z" })), "AccessibleTwoTone"),
                    Ut = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z" }), a.createElement("path", { d: "M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" })), "AccessTime"),
                    qt = (0, mt.A)(a.createElement("path", { d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" }), "AccessTimeOutlined"),
                    Gt = (0, mt.A)(a.createElement("path", { d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm-.22-13h-.06c-.4 0-.72.32-.72.72v4.72c0 .35.18.68.49.86l4.15 2.49c.34.2.78.1.98-.24.21-.34.1-.79-.25-.99l-3.87-2.3V7.72c0-.4-.32-.72-.72-.72z" }), "AccessTimeRounded"),
                    Kt = (0, mt.A)(a.createElement("path", { d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" }), "AccessTimeSharp"),
                    Zt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm4.25 12.15L11 13V7h1.5v5.25l4.5 2.67-.75 1.23z", opacity: ".3" }), a.createElement("path", { d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" })), "AccessTimeTwoTone"),
                    Yt = (0, mt.A)(a.createElement("path", { d: "M4 10h3v7H4zM10.5 10h3v7h-3zM2 19h20v3H2zM17 10h3v7h-3zM12 1L2 6v2h20V6z" }), "AccountBalance"),
                    Xt = (0, mt.A)(a.createElement("path", { d: "M6.5 10h-2v7h2v-7zm6 0h-2v7h2v-7zm8.5 9H2v2h19v-2zm-2.5-9h-2v7h2v-7zm-7-6.74L16.71 6H6.29l5.21-2.74m0-2.26L2 6v2h19V6l-9.5-5z" }), "AccountBalanceOutlined"),
                    $t = (0, mt.A)(a.createElement("path", { d: "M4 11.5v4c0 .83.67 1.5 1.5 1.5S7 16.33 7 15.5v-4c0-.83-.67-1.5-1.5-1.5S4 10.67 4 11.5zm6 0v4c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5zM3.5 22h16c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5h-16c-.83 0-1.5.67-1.5 1.5S2.67 22 3.5 22zM16 11.5v4c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5zM10.57 1.49l-7.9 4.16c-.41.21-.67.64-.67 1.1C2 7.44 2.56 8 3.25 8h16.51C20.44 8 21 7.44 21 6.75c0-.46-.26-.89-.67-1.1l-7.9-4.16c-.58-.31-1.28-.31-1.86 0z" }), "AccountBalanceRounded"),
                    Qt = (0, mt.A)(a.createElement("path", { d: "M4 10v7h3v-7H4zm6 0v7h3v-7h-3zM2 22h19v-3H2v3zm14-12v7h3v-7h-3zm-4.5-9L2 6v2h19V6l-9.5-5z" }), "AccountBalanceSharp"),
                    Jt = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M6.29 6l5.21-2.74L16.71 6z", opacity: ".3" }), a.createElement("path", { d: "M6.5 10h-2v7h2v-7zm6 0h-2v7h2v-7zm8.5 9H2v2h19v-2zm-2.5-9h-2v7h2v-7zm-7-9L2 6v2h19V6l-9.5-5zM6.29 6l5.21-2.74L16.71 6H6.29z" })), "AccountBalanceTwoTone"),
                    en = (0, mt.A)(a.createElement("path", { d: "M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" }), "AccountBalanceWallet"),
                    tn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M21 7.28V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-2.28c.59-.35 1-.98 1-1.72V9c0-.74-.41-1.37-1-1.72zM20 9v6h-7V9h7zM5 19V5h14v2h-6c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h6v2H5z" }), a.createElement("circle", { cx: "16", cy: "12", r: "1.5" })), "AccountBalanceWalletOutlined"),
                    nn = (0, mt.A)(a.createElement("path", { d: "M10 16V8c0-1.1.89-2 2-2h9V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-1h-9c-1.11 0-2-.9-2-2zm3-8c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h9V8h-9zm3 5.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" }), "AccountBalanceWalletRounded"),
                    rn = (0, mt.A)(a.createElement("path", { d: "M21 18v3H3V3h18v3H10v12h11zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" }), "AccountBalanceWalletSharp"),
                    an = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M13 17c-1.1 0-2-.9-2-2V9c0-1.1.9-2 2-2h6V5H5v14h14v-2h-6z", opacity: ".3" }), a.createElement("path", { d: "M21 7.28V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-2.28c.59-.35 1-.98 1-1.72V9c0-.74-.41-1.38-1-1.72zM20 9v6h-7V9h7zM5 19V5h14v2h-6c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h6v2H5z" }), a.createElement("circle", { cx: "16", cy: "12", r: "1.5" })), "AccountBalanceWalletTwoTone"),
                    on = (0, mt.A)(a.createElement("path", { d: "M3 5v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2zm12 4c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zm-9 8c0-2 4-3.1 6-3.1s6 1.1 6 3.1v1H6v-1z" }), "AccountBox"),
                    ln = (0, mt.A)(a.createElement("path", { d: "M19 5v14H5V5h14m0-2H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 9c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm6 10H6v-1.53c0-2.5 3.97-3.58 6-3.58s6 1.08 6 3.58V18zm-9.69-2h7.38c-.69-.56-2.38-1.12-3.69-1.12s-3.01.56-3.69 1.12z" }), "AccountBoxOutlined"),
                    sn = (0, mt.A)(a.createElement("path", { d: "M3 5v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2zm12 4c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zm-9 8c0-2 4-3.1 6-3.1s6 1.1 6 3.1v1H6v-1z" }), "AccountBoxRounded"),
                    cn = (0, mt.A)(a.createElement("path", { d: "M3 21h18V3H3v18zM15 9c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zm-9 8c0-2 4-3.1 6-3.1s6 1.1 6 3.1v1H6v-1z" }), "AccountBoxSharp"),
                    dn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M5 19h14V5H5v14zm7-13c1.65 0 3 1.35 3 3s-1.35 3-3 3-3-1.35-3-3 1.35-3 3-3zM6 16.47c0-2.5 3.97-3.58 6-3.58s6 1.08 6 3.58V18H6v-1.53z", opacity: ".3" }), a.createElement("path", { d: "M12 12c1.65 0 3-1.35 3-3s-1.35-3-3-3-3 1.35-3 3 1.35 3 3 3zm0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm7-5H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-1-2.53c0-2.5-3.97-3.58-6-3.58s-6 1.08-6 3.58V18h12v-1.53zM8.31 16c.69-.56 2.38-1.12 3.69-1.12s3.01.56 3.69 1.12H8.31z" })), "AccountBoxTwoTone"),
                    un = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" }), "AccountCircle"),
                    hn = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM7.07 18.28c.43-.9 3.05-1.78 4.93-1.78s4.51.88 4.93 1.78C15.57 19.36 13.86 20 12 20s-3.57-.64-4.93-1.72zm11.29-1.45c-1.43-1.74-4.9-2.33-6.36-2.33s-4.93.59-6.36 2.33C4.62 15.49 4 13.82 4 12c0-4.41 3.59-8 8-8s8 3.59 8 8c0 1.82-.62 3.49-1.64 4.83zM12 6c-1.94 0-3.5 1.56-3.5 3.5S10.06 13 12 13s3.5-1.56 3.5-3.5S13.94 6 12 6zm0 5c-.83 0-1.5-.67-1.5-1.5S11.17 8 12 8s1.5.67 1.5 1.5S12.83 11 12 11z" }), "AccountCircleOutlined"),
                    mn = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" }), "AccountCircleRounded"),
                    pn = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" }), "AccountCircleSharp"),
                    fn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 4c-4.41 0-8 3.59-8 8 0 1.82.62 3.49 1.64 4.83 1.43-1.74 4.9-2.33 6.36-2.33s4.93.59 6.36 2.33C19.38 15.49 20 13.82 20 12c0-4.41-3.59-8-8-8zm0 9c-1.94 0-3.5-1.56-3.5-3.5S10.06 6 12 6s3.5 1.56 3.5 3.5S13.94 13 12 13z", opacity: ".3" }), a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM7.07 18.28c.43-.9 3.05-1.78 4.93-1.78s4.51.88 4.93 1.78C15.57 19.36 13.86 20 12 20s-3.57-.64-4.93-1.72zm11.29-1.45c-1.43-1.74-4.9-2.33-6.36-2.33s-4.93.59-6.36 2.33C4.62 15.49 4 13.82 4 12c0-4.41 3.59-8 8-8s8 3.59 8 8c0 1.82-.62 3.49-1.64 4.83zM12 6c-1.94 0-3.5 1.56-3.5 3.5S10.06 13 12 13s3.5-1.56 3.5-3.5S13.94 6 12 6zm0 5c-.83 0-1.5-.67-1.5-1.5S11.17 8 12 8s1.5.67 1.5 1.5S12.83 11 12 11z" })), "AccountCircleTwoTone"),
                    vn = (0, mt.A)(a.createElement("path", { d: "M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3z" }), "AccountTree"),
                    gn = (0, mt.A)(a.createElement("path", { d: "M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3h7zM7 9H4V5h3v4zm10 6h3v4h-3v-4zm0-10h3v4h-3V5z" }), "AccountTreeOutlined"),
                    yn = (0, mt.A)(a.createElement("path", { d: "M17 11h3c1.11 0 2-.9 2-2V5c0-1.11-.9-2-2-2h-3c-1.11 0-2 .9-2 2v1H9.01V5c0-1.11-.9-2-2-2H4c-1.1 0-2 .9-2 2v4c0 1.11.9 2 2 2h3c1.11 0 2-.9 2-2V8h2v7.01c0 1.65 1.34 2.99 2.99 2.99H15v1c0 1.11.9 2 2 2h3c1.11 0 2-.9 2-2v-4c0-1.11-.9-2-2-2h-3c-1.11 0-2 .9-2 2v1h-1.01c-.54 0-.99-.45-.99-.99V8h2v1c0 1.1.9 2 2 2z" }), "AccountTreeRounded"),
                    bn = (0, mt.A)(a.createElement("path", { d: "M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3z" }), "AccountTreeSharp"),
                    wn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3h7zM7 9H4V5h3v4zm10 6h3v4h-3v-4zm0-10h3v4h-3V5z" }), a.createElement("path", { d: "M7 5v4H4V5h3M20 5v4h-3V5h3M20 15v4h-3v-4h3", opacity: ".3" })), "AccountTreeTwoTone"),
                    zn = (0, mt.A)(a.createElement("path", { d: "M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22z" }), "AcUnit"),
                    xn = (0, mt.A)(a.createElement("path", { d: "M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22v-2z" }), "AcUnitOutlined"),
                    An = (0, mt.A)(a.createElement("path", { d: "M21 11h-3.17l2.54-2.54c.39-.39.39-1.02 0-1.41-.39-.39-1.03-.39-1.42 0L15 11h-2V9l3.95-3.95c.39-.39.39-1.03 0-1.42a.9959.9959 0 00-1.41 0L13 6.17V3c0-.55-.45-1-1-1s-1 .45-1 1v3.17L8.46 3.63a.9959.9959 0 00-1.41 0c-.39.39-.39 1.03 0 1.42L11 9v2H9L5.05 7.05c-.39-.39-1.03-.39-1.42 0-.39.39-.39 1.02 0 1.41L6.17 11H3c-.55 0-1 .45-1 1s.45 1 1 1h3.17l-2.54 2.54c-.39.39-.39 1.02 0 1.41.39.39 1.03.39 1.42 0L9 13h2v2l-3.95 3.95c-.39.39-.39 1.03 0 1.42.39.39 1.02.39 1.41 0L11 17.83V21c0 .55.45 1 1 1s1-.45 1-1v-3.17l2.54 2.54c.39.39 1.02.39 1.41 0 .39-.39.39-1.03 0-1.42L13 15v-2h2l3.95 3.95c.39.39 1.03.39 1.42 0 .39-.39.39-1.02 0-1.41L17.83 13H21c.55 0 1-.45 1-1s-.45-1-1-1z" }), "AcUnitRounded"),
                    kn = (0, mt.A)(a.createElement("path", { d: "M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22v-2z" }), "AcUnitSharp"),
                    Sn = (0, mt.A)(a.createElement("path", { d: "M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22v-2z" }), "AcUnitTwoTone"),
                    Mn = (0, mt.A)(a.createElement("path", { d: "M5 16c0 3.87 3.13 7 7 7s7-3.13 7-7v-4H5v4zM16.12 4.37l2.1-2.1-.82-.83-2.3 2.31C14.16 3.28 13.12 3 12 3s-2.16.28-3.09.75L6.6 1.44l-.82.83 2.1 2.1C6.14 5.64 5 7.68 5 10v1h14v-1c0-2.32-1.14-4.36-2.88-5.63zM9 9c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "Adb"),
                    En = (0, mt.A)(a.createElement("path", { d: "M5 16c0 3.87 3.13 7 7 7s7-3.13 7-7v-4H5v4zM16.12 4.37l2.1-2.1-.82-.83-2.3 2.31C14.16 3.28 13.12 3 12 3s-2.16.28-3.09.75L6.6 1.44l-.82.83 2.1 2.1C6.14 5.64 5 7.68 5 10v1h14v-1c0-2.32-1.14-4.36-2.88-5.63zM9 9c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "AdbOutlined"),
                    Cn = (0, mt.A)(a.createElement("path", { d: "M5 16c0 3.87 3.13 7 7 7s7-3.13 7-7v-4H5v4zM16.12 4.37l2.1-2.1-.82-.83-2.3 2.31C14.16 3.28 13.12 3 12 3s-2.16.28-3.09.75L6.6 1.44l-.82.83 2.1 2.1C6.14 5.64 5 7.68 5 10v1h14v-1c0-2.32-1.14-4.36-2.88-5.63zM9 9c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "AdbRounded"),
                    Tn = (0, mt.A)(a.createElement("path", { d: "M5 16c0 3.87 3.13 7 7 7s7-3.13 7-7v-4H5v4zM16.12 4.37l2.1-2.1-.82-.83-2.3 2.31C14.16 3.28 13.12 3 12 3s-2.16.28-3.09.75L6.6 1.44l-.82.83 2.1 2.1C6.14 5.64 5 7.68 5 10v1h14v-1c0-2.32-1.14-4.36-2.88-5.63zM9 9c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "AdbSharp"),
                    Hn = (0, mt.A)(a.createElement("path", { d: "M5 16c0 3.87 3.13 7 7 7s7-3.13 7-7v-4H5v4zM16.12 4.37l2.1-2.1-.82-.83-2.3 2.31C14.16 3.28 13.12 3 12 3s-2.16.28-3.09.75L6.6 1.44l-.82.83 2.1 2.1C6.14 5.64 5 7.68 5 10v1h14v-1c0-2.32-1.14-4.36-2.88-5.63zM9 9c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "AdbTwoTone"),
                    Ln = (0, mt.A)(a.createElement("path", { d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" }), "Add"),
                    In = (0, mt.A)(a.createElement("path", { d: "M7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V9z" }), "AddAlarm"),
                    jn = (0, mt.A)(a.createElement("path", { d: "M7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V9z" }), "AddAlarmOutlined"),
                    Vn = (0, mt.A)(a.createElement("path", { d: "M15 12h-2v-2c0-.55-.45-1-1-1s-1 .45-1 1v2H9c-.55 0-1 .45-1 1s.45 1 1 1h2v2c0 .55.45 1 1 1s1-.45 1-1v-2h2c.55 0 1-.45 1-1s-.45-1-1-1zm6.18-6.99L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AddAlarmRounded"),
                    On = (0, mt.A)(a.createElement("path", { d: "M7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V9z" }), "AddAlarmSharp"),
                    Rn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm4 8h-3v3h-2v-3H8v-2h3V9h2v3h3v2z", opacity: ".3" }), a.createElement("path", { d: "M12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3zm9-3.28l-4.6-3.86-1.29 1.53 4.6 3.86zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53z" })), "AddAlarmTwoTone"),
                    Pn = (0, mt.A)(a.createElement("path", { d: "M10.01 21.01c0 1.1.89 1.99 1.99 1.99s1.99-.89 1.99-1.99h-3.98zm8.87-4.19V11c0-3.25-2.25-5.97-5.29-6.69v-.72C13.59 2.71 12.88 2 12 2s-1.59.71-1.59 1.59v.72C7.37 5.03 5.12 7.75 5.12 11v5.82L3 18.94V20h18v-1.06l-2.12-2.12zM16 13.01h-3v3h-2v-3H8V11h3V8h2v3h3v2.01z" }), "AddAlert"),
                    Dn = (0, mt.A)(a.createElement("path", { d: "M10.01 21.01c0 1.1.89 1.99 1.99 1.99s1.99-.89 1.99-1.99h-3.98zM12 6c2.76 0 5 2.24 5 5v7H7v-7c0-2.76 2.24-5 5-5zm0-4.5c-.83 0-1.5.67-1.5 1.5v1.17C7.36 4.85 5 7.65 5 11v6l-2 2v1h18v-1l-2-2v-6c0-3.35-2.36-6.15-5.5-6.83V3c0-.83-.67-1.5-1.5-1.5zM13 8h-2v3H8v2h3v3h2v-3h3v-2h-3z" }), "AddAlertOutlined"),
                    Fn = (0, mt.A)(a.createElement("path", { d: "M12 23c1.1 0 1.99-.89 1.99-1.99h-3.98c0 1.1.89 1.99 1.99 1.99zm7-6v-6c0-3.35-2.36-6.15-5.5-6.83V3c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v1.17C7.36 4.85 5 7.65 5 11v6l-1.29 1.29c-.63.63-.19 1.71.7 1.71h15.17c.89 0 1.34-1.08.71-1.71L19 17zm-4-3.99h-2v2c0 .55-.45 1-1 1s-1-.45-1-1v-2H9c-.55 0-1-.45-1-1V12c0-.55.45-1 1-1h2V9c0-.55.45-1 1-1s1 .45 1 1v2h2c.55 0 1 .45 1 1v.01c0 .55-.45 1-1 1z" }), "AddAlertRounded"),
                    Nn = (0, mt.A)(a.createElement("path", { d: "M12 23c1.1 0 1.99-.89 1.99-1.99h-3.98c0 1.1.89 1.99 1.99 1.99zm7-6v-6c0-3.35-2.36-6.15-5.5-6.83V1.5h-3v2.67C7.36 4.85 5 7.65 5 11v6l-2 2v1h18v-1l-2-2zm-3-3.99h-3v3h-2v-3H8V11h3V8h2v3h3v2.01z" }), "AddAlertSharp"),
                    _n = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-2.76 0-5 2.24-5 5v7h10v-7c0-2.76-2.24-5-5-5zm4 7h-3v3h-2v-3H8v-2h3V8h2v3h3v2z", opacity: ".3" }), a.createElement("path", { d: "M12 23c1.1 0 1.99-.89 1.99-1.99h-3.98c0 1.1.89 1.99 1.99 1.99zm7-6v-6c0-3.35-2.36-6.15-5.5-6.83V3c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v1.17C7.36 4.85 5 7.65 5 11v6l-2 2v1h18v-1l-2-2zm-2 1H7v-7c0-2.76 2.24-5 5-5s5 2.24 5 5v7zm-4-7V8h-2v3H8v2h3v3h2v-3h3v-2z" })), "AddAlertTwoTone"),
                    Bn = (0, mt.A)(a.createElement("path", { d: "M3 4V1h2v3h3v2H5v3H3V6H0V4h3zm3 6V7h3V4h7l1.83 2H21c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V10h3zm7 9c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-3.2-5c0 1.77 1.43 3.2 3.2 3.2s3.2-1.43 3.2-3.2-1.43-3.2-3.2-3.2-3.2 1.43-3.2 3.2z" }), "AddAPhoto"),
                    Wn = (0, mt.A)(a.createElement("path", { d: "M21 6h-3.17L16 4h-6v2h5.12l1.83 2H21v12H5v-9H3v9c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM8 14c0 2.76 2.24 5 5 5s5-2.24 5-5-2.24-5-5-5-5 2.24-5 5zm5-3c1.65 0 3 1.35 3 3s-1.35 3-3 3-3-1.35-3-3 1.35-3 3-3zM5 6h3V4H5V1H3v3H0v2h3v3h2z" }), "AddAPhotoOutlined"),
                    Un = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M3 8c0 .55.45 1 1 1s1-.45 1-1V6h2c.55 0 1-.45 1-1s-.45-1-1-1H5V2c0-.55-.45-1-1-1s-1 .45-1 1v2H1c-.55 0-1 .45-1 1s.45 1 1 1h2v2z" }), a.createElement("circle", { cx: "13", cy: "14", r: "3" }), a.createElement("path", { d: "M21 6h-3.17l-1.24-1.35c-.37-.41-.91-.65-1.47-.65h-6.4c.17.3.28.63.28 1 0 1.1-.9 2-2 2H6v1c0 1.1-.9 2-2 2-.37 0-.7-.11-1-.28V20c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 13c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" })), "AddAPhotoRounded"),
                    qn = (0, mt.A)(a.createElement("path", { d: "M3 4V1h2v3h3v2H5v3H3V6H0V4h3zm3 6V7h3V4h7l1.83 2H23v16H3V10h3zm7 9c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-3-5c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z" }), "AddAPhotoSharp"),
                    Gn = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M6 7v3H5v10h16V8h-4.05l-1.83-2H9v1H6zm7 2c2.76 0 5 2.24 5 5s-2.24 5-5 5-5-2.24-5-5 2.24-5 5-5z", opacity: ".3" }), a.createElement("path", { d: "M21 6h-3.17L16 4H9v2h6.12l1.83 2H21v12H5V10H3v10c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM8 14c0 2.76 2.24 5 5 5s5-2.24 5-5-2.24-5-5-5-5 2.24-5 5zm5-3c1.65 0 3 1.35 3 3s-1.35 3-3 3-3-1.35-3-3 1.35-3 3-3zM5 9V6h3V4H5V1H3v3H0v2h3v3z" })), "AddAPhotoTwoTone"),
                    Kn = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" }), "AddBox"),
                    Zn = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-8-2h2v-4h4v-2h-4V7h-2v4H7v2h4z" }), "AddBoxOutlined"),
                    Yn = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-3 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V8c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AddBoxRounded"),
                    Xn = (0, mt.A)(a.createElement("path", { d: "M21 3H3v18h18V3zm-4 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" }), "AddBoxSharp"),
                    $n = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M5 19h14V5H5v14zm2-8h4V7h2v4h4v2h-4v4h-2v-4H7v-2z", opacity: ".3" }), a.createElement("path", { d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-8-2h2v-4h4v-2h-4V7h-2v4H7v2h4z" })), "AddBoxTwoTone"),
                    Qn = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" }), "AddCircle"); var Jn = n(65988); const er = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" }), "AddCircleOutlined"),
                    tr = (0, mt.A)(a.createElement("path", { d: "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" }), "AddCircleOutlineOutlined"),
                    nr = (0, mt.A)(a.createElement("path", { d: "M12 7c-.55 0-1 .45-1 1v3H8c-.55 0-1 .45-1 1s.45 1 1 1h3v3c0 .55.45 1 1 1s1-.45 1-1v-3h3c.55 0 1-.45 1-1s-.45-1-1-1h-3V8c0-.55-.45-1-1-1zm0-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" }), "AddCircleOutlineRounded"),
                    rr = (0, mt.A)(a.createElement("path", { d: "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" }), "AddCircleOutlineSharp"),
                    ar = (0, mt.A)(a.createElement("path", { d: "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" }), "AddCircleOutlineTwoTone"),
                    or = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 11h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V8c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AddCircleRounded"),
                    ir = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" }), "AddCircleSharp"),
                    lr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm5 9h-4v4h-2v-4H7v-2h4V7h2v4h4v2z", opacity: ".3" }), a.createElement("path", { d: "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" })), "AddCircleTwoTone"),
                    sr = (0, mt.A)(a.createElement("path", { d: "M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4-.01-18zM17 11h-4v4h-2v-4H7V9h4V5h2v4h4v2z" }), "AddComment"),
                    cr = (0, mt.A)(a.createElement("path", { d: "M22 4c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4zm-2 13.17L18.83 16H4V4h16v13.17zM13 5h-2v4H7v2h4v4h2v-4h4V9h-4z" }), "AddCommentOutlined"),
                    dr = (0, mt.A)(a.createElement("path", { d: "M22 4c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4zm-6 7h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V6c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AddCommentRounded"),
                    ur = (0, mt.A)(a.createElement("path", { d: "M22 2H2v16h16l4 4V2zm-5 9h-4v4h-2v-4H7V9h4V5h2v4h4v2z" }), "AddCommentSharp"),
                    hr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm0 15.17L18.83 16H4V4h16v13.17zM13 5h-2v4H7v2h4v4h2v-4h4V9h-4z" }), a.createElement("path", { d: "M4 4v12h14.83L20 17.17V4H4zm13 7h-4v4h-2v-4H7V9h4V5h2v4h4v2z", opacity: ".3" })), "AddCommentTwoTone"),
                    mr = (0, mt.A)(a.createElement("path", { d: "M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1zM21 6h-3V3h-2v3h-3v2h3v3h2V8h3z" }), "AddIcCall"),
                    pr = (0, mt.A)(a.createElement("path", { d: "M20 15.45c-1.25 0-2.45-.2-3.57-.57-.1-.03-.21-.05-.31-.05-.26 0-.51.1-.71.29l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.4 8.5 5.2 8.5 3.95c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1zM5.03 4.95h1.5c.07.88.22 1.75.45 2.58l-1.2 1.21c-.4-1.21-.66-2.47-.75-3.79zM19 18.92c-1.32-.09-2.6-.35-3.8-.76l1.2-1.2c.85.24 1.72.39 2.6.45v1.51zM18 5.95v-3h-2v3h-3v2h3v3h2v-3h3v-2z" }), "AddIcCallOutlined"),
                    fr = (0, mt.A)(a.createElement("path", { d: "M14 8h2v2c0 .55.45 1 1 1s1-.45 1-1V8h2c.55 0 1-.45 1-1s-.45-1-1-1h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v2h-2c-.55 0-1 .45-1 1s.45 1 1 1zm5.21 7.27l-2.54-.29c-.61-.07-1.21.14-1.64.57l-1.84 1.84c-2.83-1.44-5.15-3.75-6.59-6.59l1.85-1.85c.43-.43.64-1.04.57-1.64l-.29-2.52c-.11-1.01-.97-1.78-1.98-1.78H5.02c-1.13 0-2.07.94-2 2.07.53 8.54 7.36 15.36 15.89 15.89 1.13.07 2.07-.87 2.07-2v-1.73c.01-1-.76-1.86-1.77-1.97z" }), "AddIcCallRounded"),
                    vr = (0, mt.A)(a.createElement("path", { d: "M21 6h-3V3h-2v3h-3v2h3v3h2V8h3zm0 9.46l-5.27-.61-2.52 2.52c-2.83-1.44-5.15-3.75-6.59-6.59l2.53-2.53L8.54 3H3.03C2.45 13.18 10.82 21.55 21 20.97v-5.51z" }), "AddIcCallSharp"),
                    gr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M19 17.41c-.88-.07-1.75-.22-2.6-.45l-1.2 1.2c1.21.41 2.48.67 3.8.76v-1.51zM6.54 4.95h-1.5c.09 1.32.34 2.58.75 3.79l1.2-1.21c-.24-.83-.39-1.7-.45-2.58z", opacity: ".3" }), a.createElement("path", { d: "M20 20.95c.55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1-1.25 0-2.45-.2-3.57-.57-.1-.03-.21-.05-.31-.05-.26 0-.51.1-.71.29l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.4 8.5 5.2 8.5 3.95c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17zm-3.6-3.99c.85.24 1.72.39 2.6.45v1.5c-1.32-.09-2.6-.35-3.8-.76l1.2-1.19zM5.03 4.95h1.5c.07.88.22 1.75.45 2.58l-1.2 1.21c-.4-1.21-.66-2.47-.75-3.79zm10.97 6h2v-3h3v-2h-3v-3h-2v3h-3v2h3z" })), "AddIcCallTwoTone"),
                    yr = (0, mt.A)(a.createElement("path", { d: "M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7zm4 8h-3v3h-2v-3H8V8h3V5h2v3h3v2z" }), "AddLocation"),
                    br = (0, mt.A)(a.createElement("path", { d: "M13 6v3h3v2h-3v3h-2v-3H8V9h3V6h2zm5 4.2C18 6.57 15.35 4 12 4s-6 2.57-6 6.2c0 2.34 1.95 5.44 6 9.14 4.05-3.7 6-6.8 6-9.14zM12 2c4.2 0 8 3.22 8 8.2 0 3.32-2.67 7.25-8 11.8-5.33-4.55-8-8.48-8-11.8C4 5.22 7.8 2 12 2z" }), "AddLocationOutlined"),
                    wr = (0, mt.A)(a.createElement("path", { d: "M13 7c0-.55-.44-1-1-1-.55 0-1 .44-1 1v2H9c-.55 0-1 .44-1 1 0 .55.44 1 1 1h2v2c0 .55.44 1 1 1 .55 0 1-.44 1-1v-2h2c.55 0 1-.44 1-1 0-.55-.44-1-1-1h-2V7zm-1-5c4.2 0 8 3.22 8 8.2 0 3.18-2.45 6.92-7.34 11.23-.38.33-.95.33-1.33 0C6.45 17.12 4 13.38 4 10.2 4 5.22 7.8 2 12 2z" }), "AddLocationRounded"),
                    zr = (0, mt.A)(a.createElement("path", { d: "M13 6h-2v3H8v2h3v3h2v-3h3V9h-3V6zm-1-4c4.2 0 8 3.22 8 8.2 0 3.32-2.67 7.25-8 11.8-5.33-4.55-8-8.48-8-11.8C4 5.22 7.8 2 12 2z" }), "AddLocationSharp"),
                    xr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M18.5 10.2c0 2.57-2.1 5.79-6.16 9.51l-.34.3-.34-.31C7.6 15.99 5.5 12.77 5.5 10.2c0-3.84 2.82-6.7 6.5-6.7s6.5 2.85 6.5 6.7z", opacity: ".3" }), a.createElement("path", { d: "M13 6v3h3v2h-3v3h-2v-3H8V9h3V6h2zm5 4.2C18 6.57 15.35 4 12 4s-6 2.57-6 6.2c0 2.34 1.95 5.44 6 9.14 4.05-3.7 6-6.8 6-9.14zM12 2c4.2 0 8 3.22 8 8.2 0 3.32-2.67 7.25-8 11.8-5.33-4.55-8-8.48-8-11.8C4 5.22 7.8 2 12 2z" })), "AddLocationTwoTone"),
                    Ar = (0, mt.A)(a.createElement("path", { d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" }), "AddOutlined"),
                    kr = (0, mt.A)(a.createElement("path", { d: "M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z" }), "AddPhotoAlternate"),
                    Sr = (0, mt.A)(a.createElement("path", { d: "M18 20H4V6h9V4H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-9h-2v9zm-7.79-3.17l-1.96-2.36L5.5 18h11l-3.54-4.71zM20 4V1h-2v3h-3c.01.01 0 2 0 2h3v2.99c.01.01 2 0 2 0V6h3V4h-3z" }), "AddPhotoAlternateOutlined"),
                    Mr = (0, mt.A)(a.createElement("path", { d: "M21.02 5H19V2.98c0-.54-.44-.98-.98-.98h-.03c-.55 0-.99.44-.99.98V5h-2.01c-.54 0-.98.44-.99.98v.03c0 .55.44.99.99.99H17v2.01c0 .54.44.99.99.98h.03c.54 0 .98-.44.98-.98V7h2.02c.54 0 .98-.44.98-.98v-.04c0-.54-.44-.98-.98-.98zM16 9.01V8h-1.01c-.53 0-1.03-.21-1.41-.58-.37-.38-.58-.88-.58-1.44 0-.36.1-.69.27-.98H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8.28c-.3.17-.64.28-1.02.28-1.09-.01-1.98-.9-1.98-1.99zM15.96 19H6c-.41 0-.65-.47-.4-.8l1.98-2.63c.21-.28.62-.26.82.02L10 18l2.61-3.48c.2-.26.59-.27.79-.01l2.95 3.68c.26.33.03.81-.39.81z" }), "AddPhotoAlternateRounded"),
                    Er = (0, mt.A)(a.createElement("path", { d: "M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H3v16h16V11h-3zM5 19l3-4 2 3 3-4 4 5H5z" }), "AddPhotoAlternateSharp"),
                    Cr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M10.21 16.83l-1.96-2.36L5.5 18h11l-3.54-4.71z" }), a.createElement("path", { d: "M16.5 18h-11l2.75-3.53 1.96 2.36 2.75-3.54L16.5 18zM17 7h-3V6H4v14h14V10h-1V7z", opacity: ".3" }), a.createElement("path", { d: "M20 4V1h-2v3h-3v2h3v2.99h2V6h3V4zm-2 16H4V6h10V4H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V10h-2v10z" })), "AddPhotoAlternateTwoTone"),
                    Tr = (0, mt.A)(a.createElement("path", { d: "M18 13h-5v5c0 .55-.45 1-1 1s-1-.45-1-1v-5H6c-.55 0-1-.45-1-1s.45-1 1-1h5V6c0-.55.45-1 1-1s1 .45 1 1v5h5c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AddRounded"),
                    Hr = (0, mt.A)(a.createElement("path", { d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" }), "AddSharp"),
                    Lr = (0, mt.A)(a.createElement("path", { d: "M11 9h2V6h3V4h-3V1h-2v3H8v2h3v3zm-4 9c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-9.83-3.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.86-7.01L19.42 4h-.01l-1.1 2-2.76 5H8.53l-.13-.27L6.16 6l-.95-2-.94-2H1v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.13 0-.25-.11-.25-.25z" }), "AddShoppingCart"),
                    Ir = (0, mt.A)(a.createElement("path", { d: "M11 9h2V6h3V4h-3V1h-2v3H8v2h3v3zm-4 9c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03l3.86-7.01L19.42 4l-3.87 7H8.53L4.27 2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2z" }), "AddShoppingCartOutlined"),
                    jr = (0, mt.A)(a.createElement("path", { d: "M12 9c.55 0 1-.45 1-1V6h2c.55 0 1-.45 1-1s-.45-1-1-1h-2V2c0-.55-.45-1-1-1s-1 .45-1 1v2H9c-.55 0-1 .45-1 1s.45 1 1 1h2v2c0 .55.45 1 1 1zm-5 9c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03l3.38-6.13c.27-.48.09-1.09-.39-1.36-.48-.26-1.09-.09-1.35.39L15.55 11H8.53L4.54 2.57c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1s.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2z" }), "AddShoppingCartRounded"),
                    Vr = (0, mt.A)(a.createElement("path", { d: "M11 9h2V6h3V4h-3V1h-2v3H8v2h3v3zm-4 9c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03l3.86-7.01L19.42 4l-3.87 7H8.53L4.27 2H1v2h2l3.6 7.59L3.62 17H19v-2H7l1.1-2z" }), "AddShoppingCartSharp"),
                    Or = (0, mt.A)(a.createElement("path", { d: "M11 9h2V6h3V4h-3V1h-2v3H8v2h3v3zm-4 9c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03l3.86-7.01L19.41 4l-3.86 7H8.53L4.27 2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2z" }), "AddShoppingCartTwoTone"),
                    Rr = (0, mt.A)(a.createElement("path", { d: "M18 1.01L8 1c-1.1 0-2 .9-2 2v3h2V5h10v14H8v-1H6v3c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM10 15h2V8H5v2h3.59L3 15.59 4.41 17 10 11.41z" }), "AddToHomeScreen"),
                    Pr = (0, mt.A)(a.createElement("path", { d: "M18 1.01L8 1c-1.1 0-2 .9-2 2v3h2V5h10v14H8v-1H6v3c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM10 15h2V8H5v2h3.59L3 15.59 4.41 17 10 11.41V15z" }), "AddToHomeScreenOutlined"),
                    Dr = (0, mt.A)(a.createElement("path", { d: "M18 1.01L8 1c-1.1 0-2 .9-2 2v3c0 .55.45 1 1 1s1-.45 1-1V5h10v14H8v-1c0-.55-.45-1-1-1s-1 .45-1 1v3c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM11 15c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1H6c-.55 0-1 .45-1 1s.45 1 1 1h2.59L3.7 14.89c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L10 11.41V14c0 .55.45 1 1 1z" }), "AddToHomeScreenRounded"),
                    Fr = (0, mt.A)(a.createElement("path", { d: "M20 1.01L6 1v5h2V5h10v14H8v-1H6v5h14V1.01zM10 15h2V8H5v2h3.59L3 15.59 4.41 17 10 11.41V15z" }), "AddToHomeScreenSharp"),
                    Nr = (0, mt.A)(a.createElement("path", { d: "M18 1.01L8 1c-1.1 0-2 .9-2 2v3h2V5h10v14H8v-1H6v3c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM10 15h2V8H5v2h3.59L3 15.59 4.41 17 10 11.41V15z" }), "AddToHomeScreenTwoTone"),
                    _r = (0, mt.A)(a.createElement("path", { d: "M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9h-4v4h-2v-4H9V9h4V5h2v4h4v2z" }), "AddToPhotos"),
                    Br = (0, mt.A)(a.createElement("path", { d: "M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H8V4h12v12zm-7-1h2v-4h4V9h-4V5h-2v4H9v2h4z" }), "AddToPhotosOutlined"),
                    Wr = (0, mt.A)(a.createElement("path", { d: "M3 6c-.55 0-1 .45-1 1v13c0 1.1.9 2 2 2h13c.55 0 1-.45 1-1s-.45-1-1-1H5c-.55 0-1-.45-1-1V7c0-.55-.45-1-1-1zm17-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 9h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3V6c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z" }), "AddToPhotosRounded"),
                    Ur = (0, mt.A)(a.createElement("path", { d: "M4 6H2v16h16v-2H4V6zm18-4H6v16h16V2zm-3 9h-4v4h-2v-4H9V9h4V5h2v4h4v2z" }), "AddToPhotosSharp"),
                    qr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M20 4H8v12h12V4zm-1 7h-4v4h-2v-4H9V9h4V5h2v4h4v2z", opacity: ".3" }), a.createElement("path", { d: "M4 22h14v-2H4V6H2v14c0 1.1.9 2 2 2zm4-4h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2zM8 4h12v12H8V4zm7 1h-2v4H9v2h4v4h2v-4h4V9h-4z" })), "AddToPhotosTwoTone"),
                    Gr = (0, mt.A)(a.createElement("path", { d: "M21 3H3c-1.11 0-2 .89-2 2v12c0 1.1.89 2 2 2h5v2h8v-2h5c1.1 0 1.99-.9 1.99-2L23 5c0-1.11-.9-2-2-2zm0 14H3V5h18v12zm-5-7v2h-3v3h-2v-3H8v-2h3V7h2v3h3z" }), "AddToQueue"),
                    Kr = (0, mt.A)(a.createElement("path", { d: "M11 15h2v-3h3v-2h-3V7h-2v3H8v2h3zM21 3H3c-1.11 0-2 .89-2 2v12c0 1.1.89 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V5c0-1.11-.9-2-2-2zm0 14H3V5h18v12z" }), "AddToQueueOutlined"),
                    Zr = (0, mt.A)(a.createElement("path", { d: "M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h5v1c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-1h5c1.1 0 2-.9 2-2V5c0-1.11-.9-2-2-2zm-1 14H4c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h16c.55 0 1 .45 1 1v10c0 .55-.45 1-1 1zm-4-6c0 .55-.45 1-1 1h-2v2c0 .55-.45 1-1 1s-1-.45-1-1v-2H9c-.55 0-1-.45-1-1s.45-1 1-1h2V8c0-.55.45-1 1-1s1 .45 1 1v2h2c.55 0 1 .45 1 1z" }), "AddToQueueRounded"),
                    Yr = (0, mt.A)(a.createElement("path", { d: "M23 3H1v16h7v2h8v-2h7V3zm-2 14H3V5h18v12zm-5-7v2h-3v3h-2v-3H8v-2h3V7h2v3h3z" }), "AddToQueueSharp"),
                    Xr = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M3 17h18V5H3v12zm5-7h3V7h2v3h3v2h-3v3h-2v-3H8v-2z", opacity: ".3" }), a.createElement("path", { d: "M11 15h2v-3h3v-2h-3V7h-2v3H8v2h3zM21 3H3c-1.11 0-2 .89-2 2v12c0 1.1.89 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V5c0-1.11-.9-2-2-2zm0 14H3V5h18v12z" })), "AddToQueueTwoTone"),
                    $r = (0, mt.A)(a.createElement("path", { d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" }), "AddTwoTone"),
                    Qr = (0, mt.A)(a.createElement("path", { d: "M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3-8c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" }), "Adjust"),
                    Jr = (0, mt.A)(a.createElement("path", { d: "M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3-8c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" }), "AdjustOutlined"),
                    ea = (0, mt.A)(a.createElement("path", { d: "M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3-8c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" }), "AdjustRounded"),
                    ta = (0, mt.A)(a.createElement("path", { d: "M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3-8c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" }), "AdjustSharp"),
                    na = (0, mt.A)(a.createElement("path", { d: "M12 9c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3zm0-7C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" }), "AdjustTwoTone"),
                    ra = (0, mt.A)(a.createElement("path", { d: "M22 11v2H9V7h9c2.21 0 4 1.79 4 4zM2 14v2h6v2h8v-2h6v-2H2zm5.14-1.9c1.16-1.19 1.14-3.08-.04-4.24-1.19-1.16-3.08-1.14-4.24.04-1.16 1.19-1.14 3.08.04 4.24 1.19 1.16 3.08 1.14 4.24-.04z" }), "AirlineSeatFlat"),
                    aa = (0, mt.A)(a.createElement("path", { d: "M22.25 14.29l-.69 1.89L9.2 11.71l2.08-5.66 8.56 3.09c2.1.76 3.18 3.06 2.41 5.15zM1.5 12.14L8 14.48V19h8v-1.63L20.52 19l.69-1.89-19.02-6.86-.69 1.89zm5.8-1.94c1.49-.72 2.12-2.51 1.41-4C7.99 4.71 6.2 4.08 4.7 4.8c-1.49.71-2.12 2.5-1.4 4 .71 1.49 2.5 2.12 4 1.4z" }), "AirlineSeatFlatAngled"),
                    oa = (0, mt.A)(a.createElement("path", { d: "M6 6.5c.31 0 .7.15.9.56.24.5.02 1.1-.47 1.34-.14.06-.28.1-.43.1-.3 0-.7-.15-.89-.56-.17-.34-.1-.63-.05-.78.05-.14.18-.4.51-.56.14-.06.28-.1.43-.1m6.47 2.11l6.69 2.41c.52.19.93.56 1.15 1.05.22.48.25 1.03.06 1.53l-.01.02-8.59-3.11.7-1.9M10 15.19l4 1.44V17h-4v-1.81M6 4.5c-.44 0-.88.1-1.3.3-1.49.71-2.12 2.5-1.4 4 .51 1.07 1.58 1.7 2.7 1.7.44 0 .88-.1 1.3-.3 1.49-.72 2.12-2.51 1.41-4C8.19 5.13 7.12 4.5 6 4.5zm5.28 1.55L9.2 11.71l12.36 4.47.69-1.89c.77-2.09-.31-4.39-2.41-5.15l-8.56-3.09zm-9.09 4.2l-.69 1.89L8 14.48V19h8v-1.63L20.52 19l.69-1.89-19.02-6.86z" }), "AirlineSeatFlatAngledOutlined"),
                    ia = (0, mt.A)(a.createElement("path", { d: "M22.25 14.29l-.69 1.89L9.2 11.71l1.39-3.79c.38-1.03 1.52-1.56 2.56-1.19l6.69 2.41c2.1.76 3.18 3.06 2.41 5.15zm-19.8-1.81l5.55 2V18c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-.63l3.58 1.29c.52.19 1.1-.08 1.29-.6.19-.52-.08-1.1-.6-1.29L3.13 10.59c-.52-.19-1.1.08-1.29.6-.18.52.09 1.1.61 1.29zM7.3 10.2c1.49-.72 2.12-2.51 1.41-4C7.99 4.71 6.2 4.08 4.7 4.8c-1.49.71-2.12 2.5-1.4 4 .71 1.49 2.5 2.12 4 1.4z" }), "AirlineSeatFlatAngledRounded"),
                    la = (0, mt.A)(a.createElement("path", { d: "M21.56 16.18L9.2 11.71l2.08-5.66 12.35 4.47-2.07 5.66zM1.5 12.14L8 14.48V19h8v-1.63L20.52 19l.69-1.89-19.02-6.86-.69 1.89zm5.8-1.94c1.49-.72 2.12-2.51 1.41-4C7.99 4.71 6.2 4.08 4.7 4.8c-1.49.71-2.12 2.5-1.4 4 .71 1.49 2.5 2.12 4 1.4z" }), "AirlineSeatFlatAngledSharp"),
                    sa = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M14 16.64l-4-1.45V17h4zM6 8.5c.15 0 .3-.03.44-.1.49-.24.7-.84.46-1.34-.19-.41-.59-.56-.9-.56-.15 0-.3.03-.44.1-.32.16-.45.42-.5.56-.05.15-.12.44.04.77.2.42.59.57.9.57zm13.16 2.52l-6.69-2.41-.7 1.91 8.59 3.11.01-.02c.19-.51.17-1.05-.06-1.53-.23-.5-.63-.87-1.15-1.06z", opacity: ".3" }), a.createElement("path", { d: "M1.5 12.14L8 14.48V19h8v-1.63L20.52 19l.69-1.89-19.02-6.86-.69 1.89zm8.5 3.05l4 1.44V17h-4v-1.81zm9.84-6.05l-8.56-3.09-2.08 5.66 12.36 4.47.69-1.89c.77-2.09-.31-4.39-2.41-5.15zm.53 4.46l-.01.02-8.59-3.11.7-1.91 6.69 2.41c.52.19.93.56 1.15 1.05.23.49.25 1.04.06 1.54zM6 10.5c.44 0 .88-.1 1.3-.3 1.49-.72 2.12-2.51 1.41-4C8.19 5.13 7.12 4.5 6 4.5c-.44 0-.88.1-1.3.3-1.49.71-2.12 2.5-1.4 4 .51 1.07 1.58 1.7 2.7 1.7zm-.94-3.34c.05-.14.18-.4.51-.56.14-.06.28-.1.43-.1.31 0 .7.15.9.56.24.5.02 1.1-.47 1.34-.14.06-.28.1-.43.1-.3 0-.7-.15-.89-.56-.17-.34-.1-.63-.05-.78z" })), "AirlineSeatFlatAngledTwoTone"),
                    ca = (0, mt.A)(a.createElement("path", { d: "M5 13c.78 0 1.55-.3 2.14-.9 1.16-1.19 1.14-3.08-.04-4.24C6.51 7.29 5.75 7 5 7c-.78 0-1.55.3-2.14.9-1.16 1.19-1.14 3.08.04 4.24.59.57 1.35.86 2.1.86zm-.71-3.7c.19-.19.44-.3.71-.3.26 0 .51.1.7.28.4.39.4 1.01.02 1.41-.2.2-.45.31-.72.31-.26 0-.51-.1-.7-.28-.4-.4-.4-1.02-.01-1.42zM18 7H9v6h13v-2c0-2.21-1.79-4-4-4zm-7 4V9h7c1.1 0 2 .9 2 2h-9zm-9 5h6v2h8v-2h6v-2H2z" }), "AirlineSeatFlatOutlined"),
                    da = (0, mt.A)(a.createElement("path", { d: "M22 11v2H9V9c0-1.1.9-2 2-2h7c2.21 0 4 1.79 4 4zM2 15c0 .55.45 1 1 1h5v1c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-1h5c.55 0 1-.45 1-1s-.45-1-1-1H3c-.55 0-1 .45-1 1zm5.14-2.9c1.16-1.19 1.14-3.08-.04-4.24-1.19-1.16-3.08-1.14-4.24.04-1.16 1.19-1.14 3.08.04 4.24 1.19 1.16 3.08 1.14 4.24-.04z" }), "AirlineSeatFlatRounded"),
                    ua = (0, mt.A)(a.createElement("path", { d: "M22 7v6H9V7h13zM2 14v2h6v2h8v-2h6v-2H2zm5.14-1.9c1.16-1.19 1.14-3.08-.04-4.24-1.19-1.16-3.08-1.14-4.24.04-1.16 1.19-1.14 3.08.04 4.24 1.19 1.16 3.08 1.14 4.24-.04z" }), "AirlineSeatFlatSharp"),
                    ha = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M5 11c.27 0 .52-.11.71-.3.39-.4.39-1.02-.01-1.41C5.51 9.11 5.26 9 5 9c-.27 0-.52.11-.71.3-.39.4-.39 1.02.01 1.41.19.18.44.29.7.29zm13-2h-7v2h9c0-1.1-.9-2-2-2z", opacity: ".3" }), a.createElement("path", { d: "M5 13c.78 0 1.55-.3 2.14-.9 1.16-1.19 1.14-3.08-.04-4.24C6.51 7.29 5.75 7 5 7c-.78 0-1.55.3-2.14.9-1.16 1.19-1.14 3.08.04 4.24.59.57 1.35.86 2.1.86zm-.71-3.7c.19-.19.44-.3.71-.3.26 0 .51.1.7.28.4.39.4 1.01.02 1.41-.2.2-.45.31-.72.31-.26 0-.51-.1-.7-.28-.4-.4-.4-1.02-.01-1.42zM18 7H9v6h13v-2c0-2.21-1.79-4-4-4zm-7 4V9h7c1.1 0 2 .9 2 2h-9zm-9 5h6v2h8v-2h6v-2H2z" })), "AirlineSeatFlatTwoTone"),
                    ma = (0, mt.A)(a.createElement("path", { d: "M7 13c1.65 0 3-1.35 3-3S8.65 7 7 7s-3 1.35-3 3 1.35 3 3 3zm12-6h-8v7H3V7H1v10h22v-6c0-2.21-1.79-4-4-4z" }), "AirlineSeatIndividualSuite"),
                    pa = (0, mt.A)(a.createElement("path", { d: "M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm12-3h-8v8H3V7H1v10h22v-6c0-2.21-1.79-4-4-4zm2 8h-8V9h6c1.1 0 2 .9 2 2v4z" }), "AirlineSeatIndividualSuiteOutlined"),
                    fa = (0, mt.A)(a.createElement("path", { d: "M7 13c1.65 0 3-1.35 3-3S8.65 7 7 7s-3 1.35-3 3 1.35 3 3 3zm12-6h-6c-1.1 0-2 .9-2 2v5H3V8c0-.55-.45-1-1-1s-1 .45-1 1v7c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2v-4c0-2.21-1.79-4-4-4z" }), "AirlineSeatIndividualSuiteRounded"),
                    va = (0, mt.A)(a.createElement("path", { d: "M7 13c1.65 0 3-1.35 3-3S8.65 7 7 7s-3 1.35-3 3 1.35 3 3 3zm16-6H11v7H3V7H1v10h22V7z" }), "AirlineSeatIndividualSuiteSharp"),
                    ga = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "7", cy: "11", r: "1", opacity: ".3" }), a.createElement("path", { d: "M19 9h-6v6h8v-4c0-1.1-.9-2-2-2z", opacity: ".3" }), a.createElement("path", { d: "M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm12-3h-8v8H3V7H1v10h22v-6c0-2.21-1.79-4-4-4zm2 8h-8V9h6c1.1 0 2 .9 2 2v4z" })), "AirlineSeatIndividualSuiteTwoTone"),
                    ya = (0, mt.A)(a.createElement("path", { d: "M4 12V3H2v9c0 2.76 2.24 5 5 5h6v-2H7c-1.66 0-3-1.34-3-3zm18.83 5.24c-.38-.72-1.29-.97-2.03-.63l-1.09.5-3.41-6.98c-.34-.68-1.03-1.12-1.79-1.12L11 9V3H5v8c0 1.66 1.34 3 3 3h7l3.41 7 3.72-1.7c.77-.36 1.1-1.3.7-2.06z" }), "AirlineSeatLegroomExtra"),
                    ba = (0, mt.A)(a.createElement("path", { d: "M4 12V3H2v9c0 2.76 2.24 5 5 5h6v-2H7c-1.66 0-3-1.34-3-3zm18.83 5.24c-.38-.72-1.29-.97-2.03-.63l-1.09.5-3.41-6.98C15.96 9.45 15.27 9 14.51 9H11V3H5v8c0 1.66 1.34 3 3 3h7l3.41 7 3.72-1.7c.77-.36 1.1-1.3.7-2.06z" }), "AirlineSeatLegroomExtraOutlined"),
                    wa = (0, mt.A)(a.createElement("path", { d: "M4 12V4c0-.55-.45-1-1-1s-1 .45-1 1v8c0 2.76 2.24 5 5 5h5c.55 0 1-.45 1-1s-.45-1-1-1H7c-1.66 0-3-1.34-3-3zm18.83 5.24c-.38-.72-1.29-.97-2.03-.63l-1.09.5-3.41-6.98C15.96 9.45 15.27 9 14.51 9H11V3H5v8c0 1.66 1.34 3 3 3h7l2.56 5.25c.48.98 1.64 1.39 2.63.94l1.95-.89c.76-.36 1.09-1.3.69-2.06z" }), "AirlineSeatLegroomExtraRounded"),
                    za = (0, mt.A)(a.createElement("path", { d: "M4 3H2v14h11v-2H4zm18.24 12.96l-2.53 1.15-3.41-6.98C15.96 9.45 15.27 9 14.51 9H11V3H5v11h10l3.41 7 5.07-2.32-1.24-2.72z" }), "AirlineSeatLegroomExtraSharp"),
                    xa = (0, mt.A)(a.createElement("path", { d: "M4 12V3H2v9c0 2.76 2.24 5 5 5h6v-2H7c-1.66 0-3-1.34-3-3zm18.83 5.24c-.38-.72-1.29-.97-2.03-.63l-1.09.5-3.41-6.98C15.96 9.45 15.27 9 14.51 9H11V3H5v8c0 1.66 1.34 3 3 3h7l3.41 7 3.72-1.7c.77-.36 1.1-1.3.7-2.06z" }), "AirlineSeatLegroomExtraTwoTone"),
                    Aa = (0, mt.A)(a.createElement("path", { d: "M5 12V3H3v9c0 2.76 2.24 5 5 5h6v-2H8c-1.66 0-3-1.34-3-3zm15.5 6H19v-7c0-1.1-.9-2-2-2h-5V3H6v8c0 1.65 1.35 3 3 3h7v7h4.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z" }), "AirlineSeatLegroomNormal"),
                    ka = (0, mt.A)(a.createElement("path", { d: "M5 12V3H3v9c0 2.76 2.24 5 5 5h6v-2H8c-1.66 0-3-1.34-3-3zm15.5 6H19v-7c0-1.1-.9-2-2-2h-5V3H6v8c0 1.65 1.35 3 3 3h7v7h4.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z" }), "AirlineSeatLegroomNormalOutlined"),
                    Sa = (0, mt.A)(a.createElement("path", { d: "M5 12V4c0-.55-.45-1-1-1s-1 .45-1 1v8c0 2.76 2.24 5 5 5h5c.55 0 1-.45 1-1s-.45-1-1-1H8c-1.66 0-3-1.34-3-3zm15.5 6H19v-7c0-1.1-.9-2-2-2h-5V3H6v8c0 1.65 1.35 3 3 3h7v5c0 1.1.9 2 2 2h2.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z" }), "AirlineSeatLegroomNormalRounded"),
                    Ma = (0, mt.A)(a.createElement("path", { d: "M5 15V3H3v14h11v-2H5zm17 3h-3v-7c0-1.1-.9-2-2-2h-5V3H6v11h10v7h6v-3z" }), "AirlineSeatLegroomNormalSharp"),
                    Ea = (0, mt.A)(a.createElement("path", { d: "M5 12V3H3v9c0 2.76 2.24 5 5 5h6v-2H8c-1.66 0-3-1.34-3-3zm15.5 6H19v-7c0-1.1-.9-2-2-2h-5V3H6v8c0 1.65 1.35 3 3 3h7v7h4.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z" }), "AirlineSeatLegroomNormalTwoTone"),
                    Ca = (0, mt.A)(a.createElement("path", { d: "M19.97 19.2c.18.96-.55 1.8-1.47 1.8H14v-3l1-4H9c-1.65 0-3-1.35-3-3V3h6v6h5c1.1 0 2 .9 2 2l-2 7h1.44c.73 0 1.39.49 1.53 1.2zM5 12V3H3v9c0 2.76 2.24 5 5 5h4v-2H8c-1.66 0-3-1.34-3-3z" }), "AirlineSeatLegroomReduced"),
                    Ta = (0, mt.A)(a.createElement("path", { d: "M19.97 19.2c.18.96-.55 1.8-1.47 1.8H14v-3l1-4H9c-1.65 0-3-1.35-3-3V3h6v6h5c1.1 0 2 .9 2 2l-2 7h1.44c.73 0 1.39.49 1.53 1.2zM5 12V3H3v9c0 2.76 2.24 5 5 5h4v-2H8c-1.66 0-3-1.34-3-3z" }), "AirlineSeatLegroomReducedOutlined"),
                    Ha = (0, mt.A)(a.createElement("path", { d: "M19.97 19.2c.18.96-.55 1.8-1.47 1.8h-2.69c-1.3 0-2.26-1.22-1.94-2.49L15 14H9c-1.65 0-3-1.35-3-3V3h6v6h5c1.1 0 2 .9 2 2l-2 7h1.44c.73 0 1.39.49 1.53 1.2zM5 12V4c0-.55-.45-1-1-1s-1 .45-1 1v8c0 2.76 2.24 5 5 5h3c.55 0 1-.45 1-1s-.45-1-1-1H8c-1.66 0-3-1.34-3-3z" }), "AirlineSeatLegroomReducedRounded"),
                    La = (0, mt.A)(a.createElement("path", { d: "M19.97 21H14v-3l1-4H6V3h6v6h5c1.1 0 2 .9 2 2l-2 7h2.97v3zM5 15V3H3v14h9v-2H5z" }), "AirlineSeatLegroomReducedSharp"),
                    Ia = (0, mt.A)(a.createElement("path", { d: "M19.97 19.2c.18.96-.55 1.8-1.47 1.8H14v-3l1-4H9c-1.65 0-3-1.35-3-3V3h6v6h5c1.1 0 2 .9 2 2l-2 7h1.44c.73 0 1.39.49 1.53 1.2zM5 12V3H3v9c0 2.76 2.24 5 5 5h4v-2H8c-1.66 0-3-1.34-3-3z" }), "AirlineSeatLegroomReducedTwoTone"),
                    ja = (0, mt.A)(a.createElement("path", { d: "M5.35 5.64c-.9-.64-1.12-1.88-.49-2.79.63-.9 1.88-1.12 2.79-.49.9.64 1.12 1.88.49 2.79-.64.9-1.88 1.12-2.79.49zM16 19H8.93c-1.48 0-2.74-1.08-2.96-2.54L4 7H2l1.99 9.76C4.37 19.2 6.47 21 8.94 21H16v-2zm.23-4h-4.88l-1.03-4.1c1.58.89 3.28 1.54 5.15 1.22V9.99c-1.63.31-3.44-.27-4.69-1.25L9.14 7.47c-.23-.18-.49-.3-.76-.38-.32-.09-.66-.12-.99-.06h-.02c-1.23.22-2.05 1.39-1.84 2.61l1.35 5.92C7.16 16.98 8.39 18 9.83 18h6.85l3.82 3 1.5-1.5-5.77-4.5z" }), "AirlineSeatReclineExtra"),
                    Va = (0, mt.A)(a.createElement("path", { d: "M5.35 5.64c-.9-.64-1.12-1.88-.49-2.79.63-.9 1.88-1.12 2.79-.49.9.64 1.12 1.88.49 2.79-.64.9-1.88 1.12-2.79.49zM16 19H8.93c-1.48 0-2.74-1.08-2.96-2.54L4 7H2l1.99 9.76C4.37 19.2 6.47 21 8.94 21H16v-2zm.23-4h-4.88l-1.03-4.1c1.58.89 3.28 1.54 5.15 1.22V9.99c-1.63.31-3.44-.27-4.69-1.25L9.14 7.47c-.23-.18-.49-.3-.76-.38-.32-.09-.66-.12-.99-.06h-.02c-1.23.22-2.05 1.39-1.84 2.61l1.35 5.92C7.16 16.98 8.39 18 9.83 18h6.85l3.82 3 1.5-1.5-5.77-4.5z" }), "AirlineSeatReclineExtraOutlined"),
                    Oa = (0, mt.A)(a.createElement("path", { d: "M5.35 5.64c-.9-.64-1.12-1.88-.49-2.79.63-.9 1.88-1.12 2.79-.49.9.64 1.12 1.88.49 2.79-.64.9-1.88 1.12-2.79.49zM16 20c0-.55-.45-1-1-1H8.93c-1.48 0-2.74-1.08-2.96-2.54L4.16 7.78C4.07 7.33 3.67 7 3.2 7c-.62 0-1.08.57-.96 1.18l1.75 8.58C4.37 19.2 6.47 21 8.94 21H15c.55 0 1-.45 1-1zm-.46-5h-4.19l-1.03-4.1c1.28.72 2.63 1.28 4.1 1.3.58.01 1.05-.49 1.05-1.07 0-.59-.49-1.04-1.08-1.06-1.31-.04-2.63-.56-3.61-1.33L9.14 7.47c-.23-.18-.49-.3-.76-.38-.32-.09-.66-.12-.99-.06h-.02c-1.23.22-2.05 1.39-1.84 2.61l1.35 5.92C7.16 16.98 8.39 18 9.83 18h6.85l3.09 2.42c.42.33 1.02.29 1.39-.08.45-.45.4-1.18-.1-1.57l-4.29-3.35c-.35-.27-.78-.42-1.23-.42z" }), "AirlineSeatReclineExtraRounded"),
                    Ra = (0, mt.A)(a.createElement("path", { d: "M5.35 5.64c-.9-.64-1.12-1.88-.49-2.79.63-.9 1.88-1.12 2.79-.49.9.64 1.12 1.88.49 2.79-.64.9-1.88 1.12-2.79.49zM16 19H6.5L4 7H2l2.85 14H16v-2zm.23-4h-4.88l-1.03-4.1c1.58.89 3.28 1.54 5.15 1.22V9.99c-1.63.31-3.44-.27-4.69-1.25L9.14 7.47c-.23-.18-.49-.3-.76-.38-.32-.09-.66-.12-.99-.06h-.02c-1.23.22-2.05 1.39-1.84 2.61L7.44 18h9.24l3.82 3 1.5-1.5-5.77-4.5z" }), "AirlineSeatReclineExtraSharp"),
                    Pa = (0, mt.A)(a.createElement("path", { d: "M5.35 5.64c-.9-.64-1.12-1.88-.49-2.79.63-.9 1.88-1.12 2.79-.49.9.64 1.12 1.88.49 2.79-.64.9-1.88 1.12-2.79.49zM16 19H8.93c-1.48 0-2.74-1.08-2.96-2.54L4 7H2l1.99 9.76C4.37 19.2 6.47 21 8.94 21H16v-2zm.23-4h-4.88l-1.03-4.1c1.58.89 3.28 1.54 5.15 1.22V9.99c-1.63.31-3.44-.27-4.69-1.25L9.14 7.47c-.23-.18-.49-.3-.76-.38-.32-.09-.66-.12-.99-.06h-.02c-1.23.22-2.05 1.39-1.84 2.61l1.35 5.92C7.16 16.98 8.39 18 9.83 18h6.85l3.82 3 1.5-1.5-5.77-4.5z" }), "AirlineSeatReclineExtraTwoTone"),
                    Da = (0, mt.A)(a.createElement("path", { d: "M7.59 5.41c-.78-.78-.78-2.05 0-2.83.78-.78 2.05-.78 2.83 0 .78.78.78 2.05 0 2.83-.79.79-2.05.79-2.83 0zM6 16V7H4v9c0 2.76 2.24 5 5 5h6v-2H9c-1.66 0-3-1.34-3-3zm14 4.07L14.93 15H11.5v-3.68c1.4 1.15 3.6 2.16 5.5 2.16v-2.16c-1.66.02-3.61-.87-4.67-2.04l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C8.01 7 7 8.01 7 9.25V15c0 1.66 1.34 3 3 3h5.07l3.5 3.5L20 20.07z" }), "AirlineSeatReclineNormal"),
                    Fa = (0, mt.A)(a.createElement("path", { d: "M7.59 5.41c-.78-.78-.78-2.05 0-2.83s2.05-.78 2.83 0 .78 2.05 0 2.83c-.79.79-2.05.79-2.83 0zM6 16V7H4v9c0 2.76 2.24 5 5 5h6v-2H9c-1.66 0-3-1.34-3-3zm14 4.07L14.93 15H11.5v-3.68c1.4 1.15 3.6 2.16 5.5 2.16v-2.16c-1.66.02-3.61-.87-4.67-2.04l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C8.01 7 7 8.01 7 9.25V15c0 1.66 1.34 3 3 3h5.07l3.5 3.5L20 20.07z" }), "AirlineSeatReclineNormalOutlined"),
                    Na = (0, mt.A)(a.createElement("path", { d: "M7.59 5.41c-.78-.78-.78-2.05 0-2.83s2.05-.78 2.83 0 .78 2.05 0 2.83c-.79.79-2.05.79-2.83 0zM6 16V8c0-.55-.45-1-1-1s-1 .45-1 1v8c0 2.76 2.24 5 5 5h5c.55 0 1-.45 1-1s-.45-1-1-1H9c-1.66 0-3-1.34-3-3zm13.28 3.35l-3.77-3.77c-.37-.37-.88-.58-1.41-.58h-2.6v-3.68c1.09.89 2.66 1.7 4.2 2.02.67.14 1.3-.36 1.3-1.04 0-.53-.39-.96-.92-1.05-1.42-.24-2.88-1.01-3.75-1.97l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C8.01 7 7 8.01 7 9.25V15c0 1.66 1.34 3 3 3h5.07l2.78 2.78c.39.39 1.04.39 1.43 0 .4-.39.4-1.03 0-1.43z" }), "AirlineSeatReclineNormalRounded"),
                    _a = (0, mt.A)(a.createElement("path", { d: "M7.59 5.41c-.78-.78-.78-2.05 0-2.83s2.05-.78 2.83 0 .78 2.05 0 2.83c-.79.79-2.05.79-2.83 0zM6 19V7H4v14h11v-2H6zm14 1.07L14.93 15H11.5v-3.68c1.4 1.15 3.6 2.16 5.5 2.16v-2.16c-1.66.02-3.61-.87-4.67-2.04l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C8.01 7 7 8.01 7 9.25V18h8.07l3.5 3.5L20 20.07z" }), "AirlineSeatReclineNormalSharp"),
                    Ba = (0, mt.A)(a.createElement("path", { d: "M7.59 5.41c-.78-.78-.78-2.05 0-2.83s2.05-.78 2.83 0 .78 2.05 0 2.83c-.79.79-2.05.79-2.83 0zM6 16V7H4v9c0 2.76 2.24 5 5 5h6v-2H9c-1.66 0-3-1.34-3-3zm14 4.07L14.93 15H11.5v-3.68c1.4 1.15 3.6 2.16 5.5 2.16v-2.16c-1.66.02-3.61-.87-4.67-2.04l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C8.01 7 7 8.01 7 9.25V15c0 1.66 1.34 3 3 3h5.07l3.5 3.5L20 20.07z" }), "AirlineSeatReclineNormalTwoTone"),
                    Wa = (0, mt.A)(a.createElement("path", { d: "M22 16v-2l-8.5-5V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-5.5L22 16z" }), "AirplanemodeActive"),
                    Ua = (0, mt.A)(a.createElement("path", { d: "M22 16v-2l-8.5-5V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-5.5L22 16z" }), "AirplanemodeActiveOutlined"),
                    qa = (0, mt.A)(a.createElement("path", { d: "M21.48 13.7L13.5 9V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9l-7.98 4.7c-.32.18-.52.53-.52.9 0 .7.67 1.2 1.34 1.01l7.16-2.1V19l-2.26 1.35c-.15.09-.24.26-.24.43v.58c0 .33.31.57.62.49l2.92-.73L12 21l.38.09.42.11 1.9.48.67.17c.32.08.62-.16.62-.49v-.58c0-.18-.09-.34-.24-.43L13.5 19v-5.5l7.16 2.1c.67.2 1.34-.3 1.34-1 0-.37-.2-.72-.52-.9z" }), "AirplanemodeActiveRounded"),
                    Ga = (0, mt.A)(a.createElement("path", { d: "M22 16v-2l-8.5-5V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-5.5L22 16z" }), "AirplanemodeActiveSharp"),
                    Ka = (0, mt.A)(a.createElement("path", { d: "M22 16v-2l-8.5-5V3.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5V9L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-5.5L22 16z" }), "AirplanemodeActiveTwoTone"),
                    Za = (0, mt.A)(a.createElement("path", { d: "M10.5 7.67V3.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9l8.5 5v2l-4.49-1.32-7.01-7.01zm9.28 14.94l1.41-1.41-7.69-7.7-3.94-3.94-6.75-6.75-1.42 1.41 6.38 6.38L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-2.67l6.28 6.28z" }), "AirplanemodeInactive"),
                    Ya = (0, mt.A)(a.createElement("path", { d: "M10.5 7.67V3.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9l8.5 5v2l-4.49-1.32-7.01-7.01zm9.28 14.94l1.41-1.41-7.69-7.7-3.94-3.94-6.75-6.75-1.42 1.41 6.38 6.38L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-2.67l6.28 6.28z" }), "AirplanemodeInactiveOutlined"),
                    Xa = (0, mt.A)(a.createElement("path", { d: "M22 14.6c0 .7-.67 1.2-1.34 1.01l-3.15-.93-7.01-7.01V3.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9l7.98 4.7c.32.18.52.53.52.9zm-8.5-1.1L9.56 9.56 3.51 3.51a.9959.9959 0 00-1.41 0c-.39.39-.39 1.02 0 1.41l5.67 5.67-5.25 3.11c-.32.18-.52.53-.52.9 0 .7.67 1.2 1.34 1.01l7.16-2.1V19l-2.26 1.35c-.15.09-.24.26-.24.43v.58c0 .33.31.57.62.49l2.92-.73L12 21l.38.09.42.11 1.9.48.67.17c.32.08.62-.16.62-.49v-.58c0-.18-.09-.34-.24-.43L13.5 19v-2.67l5.57 5.57c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41L13.5 13.5z" }), "AirplanemodeInactiveRounded"),
                    $a = (0, mt.A)(a.createElement("path", { d: "M10.5 7.67V3.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9l8.5 5v2l-4.49-1.32-7.01-7.01zm9.28 14.94l1.41-1.41-7.69-7.7-3.94-3.94-6.75-6.75-1.42 1.41 6.38 6.38L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-2.67l6.28 6.28z" }), "AirplanemodeInactiveSharp"),
                    Qa = (0, mt.A)(a.createElement("path", { d: "M10.5 7.67V3.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9l8.5 5v2l-4.49-1.32-7.01-7.01zm9.28 14.94l1.41-1.41-7.69-7.7-3.94-3.94-6.75-6.75-1.42 1.41 6.38 6.38L2 14v2l8.5-2.5V19L8 20.5V22l4-1 4 1v-1.5L13.5 19v-2.67l6.28 6.28z" }), "AirplanemodeInactiveTwoTone"),
                    Ja = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M6 22h12l-6-6z" }), a.createElement("path", { d: "M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v-2H3V5h18v12h-4v2h4c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" })), "Airplay"),
                    eo = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M6 22h12l-6-6z" }), a.createElement("path", { d: "M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v-2H3V5h18v12h-4v2h4c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" })), "AirplayOutlined"),
                    to = (0, mt.A)(a.createElement("path", { d: "M8.41 22h7.17c.89 0 1.34-1.08.71-1.71L12.7 16.7a.9959.9959 0 00-1.41 0L7.7 20.29c-.62.63-.18 1.71.71 1.71zM21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h3c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h16c.55 0 1 .45 1 1v10c0 .55-.45 1-1 1h-2c-.55 0-1 .45-1 1s.45 1 1 1h3c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" }), "AirplayRounded"),
                    no = (0, mt.A)(a.createElement("path", { d: "M6 22h12l-6-6-6 6zM23 3H1v16h6v-2H3V5h18v12h-4v2h6V3z" }), "AirplaySharp"),
                    ro = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M6 22h12l-6-6z" }), a.createElement("path", { d: "M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v-2H3V5h18v12h-4v2h4c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" })), "AirplayTwoTone"),
                    ao = (0, mt.A)(a.createElement("path", { d: "M17 5H3c-1.1 0-2 .89-2 2v9h2c0 1.65 1.34 3 3 3s3-1.35 3-3h5.5c0 1.65 1.34 3 3 3s3-1.35 3-3H23v-5l-6-6zM3 11V7h4v4H3zm3 6.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm7-6.5H9V7h4v4zm4.5 6.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM15 11V7h1l4 4h-5z" }), "AirportShuttle"),
                    oo = (0, mt.A)(a.createElement("path", { d: "M17 5H3c-1.1 0-2 .89-2 2v9h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-6-6zm-2 2h1l3 3h-4V7zM9 7h4v3H9V7zM3 7h4v3H3V7zm3 10.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zm12 0c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM21 14h-.78c-.55-.61-1.34-1-2.22-1s-1.67.39-2.22 1H8.22c-.55-.61-1.33-1-2.22-1s-1.67.39-2.22 1H3v-2h18v2z" }), "AirportShuttleOutlined"),
                    io = (0, mt.A)(a.createElement("path", { d: "M22.41 10.41l-4.83-4.83c-.37-.37-.88-.58-1.41-.58H3c-1.1 0-2 .89-2 2v7c0 1.1.9 2 2 2 0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3c1.1 0 2-.9 2-2v-2.17c0-.53-.21-1.04-.59-1.42zM3 10V8c0-.55.45-1 1-1h3v4H4c-.55 0-1-.45-1-1zm3 7.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM13 11H9V7h4v4zm5 6.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM15 11V7h1l4 4h-5z" }), "AirportShuttleRounded"),
                    lo = (0, mt.A)(a.createElement("path", { d: "M17 5H1v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-6-6zM3 11V7h4v4H3zm3 6.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM13 11H9V7h4v4zm5 6.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM15 11V7h1l4 4h-5z" }), "AirportShuttleSharp"),
                    so = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M3 14h.78c.55-.61 1.34-1 2.22-1s1.67.39 2.22 1h7.56c.55-.61 1.34-1 2.22-1s1.67.39 2.22 1H21v-2H3v2z", opacity: ".3" }), a.createElement("path", { d: "M17 5H3c-1.1 0-2 .89-2 2v9h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-6-6zm-2 2h1l3 3h-4V7zM9 7h4v3H9V7zM3 7h4v3H3V7zm3 10.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zm12 0c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zM21 14h-.78c-.55-.61-1.34-1-2.22-1s-1.67.39-2.22 1H8.22c-.55-.61-1.33-1-2.22-1s-1.67.39-2.22 1H3v-2h18v2z" })), "AirportShuttleTwoTone"),
                    co = (0, mt.A)(a.createElement("path", { d: "M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12.5 8H11v6l4.75 2.85.75-1.23-4-2.37V8zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" }), "Alarm"),
                    uo = (0, mt.A)(a.createElement("path", { d: "M7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V9z" }), "AlarmAdd"),
                    ho = (0, mt.A)(a.createElement("path", { d: "M17.337 1.81l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3z" }), "AlarmAddOutlined"),
                    mo = (0, mt.A)(a.createElement("path", { d: "M21.18 5.01L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm3-8h-2v-2c0-.55-.45-1-1-1s-1 .45-1 1v2H9c-.55 0-1 .45-1 1s.45 1 1 1h2v2c0 .55.45 1 1 1s1-.45 1-1v-2h2c.55 0 1-.45 1-1s-.45-1-1-1z" }), "AlarmAddRounded"),
                    po = (0, mt.A)(a.createElement("path", { d: "M17.337 1.81l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3z" }), "AlarmAddSharp"),
                    fo = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.86 0-7 3.14-7 7s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm4 8h-3v3h-2v-3H8v-2h3V9h2v3h3v2z", opacity: ".3" }), a.createElement("path", { d: "M17.337 1.81l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3z" })), "AlarmAddTwoTone"),
                    vo = (0, mt.A)(a.createElement("path", { d: "M12 6c3.87 0 7 3.13 7 7 0 .84-.16 1.65-.43 2.4l1.52 1.52c.58-1.19.91-2.51.91-3.92 0-4.97-4.03-9-9-9-1.41 0-2.73.33-3.92.91L9.6 6.43C10.35 6.16 11.16 6 12 6zm10-.28l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM2.92 2.29L1.65 3.57 2.98 4.9l-1.11.93 1.42 1.42 1.11-.94.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.02 9 9 9 2.25 0 4.31-.83 5.89-2.2l2.2 2.2 1.27-1.27L3.89 3.27l-.97-.98zm13.55 16.1C15.26 19.39 13.7 20 12 20c-3.87 0-7-3.13-7-7 0-1.7.61-3.26 1.61-4.47l9.86 9.86zM8.02 3.28L6.6 1.86l-.86.71 1.42 1.42.86-.71z" }), "AlarmOff"),
                    go = (0, mt.A)(a.createElement("path", { d: "M10.04 6.29C10.66 6.11 11.32 6 12 6c3.86 0 7 3.14 7 7 0 .68-.11 1.34-.29 1.96l1.56 1.56c.47-1.08.73-2.27.73-3.52 0-4.97-4.03-9-9-9-1.25 0-2.44.26-3.53.72l1.57 1.57zm7.297-4.48l4.607 3.845-1.28 1.535-4.61-3.843zM3.02 2.1L1.61 3.51l1.37 1.37-.92.77 1.28 1.54 1.06-.88.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.03 9 9 9 2.25 0 4.31-.83 5.89-2.2l2.1 2.1 1.41-1.41L3.02 2.1zM12 20c-3.86 0-7-3.14-7-7 0-1.7.61-3.26 1.62-4.47l9.85 9.85C15.26 19.39 13.7 20 12 20zM7.48 3.73l.46-.38-1.28-1.54-.6.5z" }), "AlarmOffOutlined"),
                    yo = (0, mt.A)(a.createElement("path", { d: "M10.04 6.29C10.66 6.11 11.32 6 12 6c3.86 0 7 3.14 7 7 0 .68-.11 1.34-.29 1.96l1.56 1.56c.47-1.08.73-2.27.73-3.52 0-4.97-4.03-9-9-9-1.25 0-2.44.26-3.53.72l1.57 1.57zm-6.33-3.5c-.38-.38-1-.38-1.39 0l-.02.03c-.39.39-.39 1.01 0 1.39l.68.68-.17.14c-.42.34-.47.96-.13 1.38l.03.03c.35.42.96.47 1.38.12l.31-.25.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.03 9 9 9 2.25 0 4.31-.83 5.89-2.2l1.41 1.41c.38.38 1 .38 1.39 0l.03-.03c.38-.38.38-1 0-1.39l-17.01-17zM12 20c-3.86 0-7-3.14-7-7 0-1.7.61-3.26 1.62-4.47l9.85 9.85C15.26 19.39 13.7 20 12 20zm7.91-13.44c.42.35 1.03.29 1.38-.12l.03-.03c.35-.42.29-1.03-.12-1.38l-3.1-2.59c-.42-.35-1.03-.29-1.38.12l-.03.03c-.35.42-.29 1.03.12 1.38l3.1 2.59zM7.43 3.68c.18-.34.15-.77-.11-1.09l-.03-.03c-.3-.36-.8-.43-1.2-.22l1.34 1.34z" }), "AlarmOffRounded"),
                    bo = (0, mt.A)(a.createElement("path", { d: "M10.04 6.29C10.66 6.11 11.32 6 12 6c3.86 0 7 3.14 7 7 0 .68-.11 1.34-.29 1.96l1.56 1.56c.47-1.08.73-2.27.73-3.52 0-4.97-4.03-9-9-9-1.25 0-2.44.26-3.53.72l1.57 1.57zm7.297-4.48l4.607 3.845-1.28 1.535-4.61-3.843zM3.02 2.1L1.61 3.51l1.37 1.37-.92.77 1.28 1.54 1.06-.88.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.03 9 9 9 2.25 0 4.31-.83 5.89-2.2l2.1 2.1 1.41-1.41L3.02 2.1zM12 20c-3.86 0-7-3.14-7-7 0-1.7.61-3.26 1.62-4.47l9.85 9.85C15.26 19.39 13.7 20 12 20zM7.48 3.73l.46-.38-1.28-1.54-.6.5z" }), "AlarmOffSharp"),
                    wo = (0, mt.A)(a.createElement("path", { d: "M10.04 6.29C10.66 6.11 11.32 6 12 6c3.86 0 7 3.14 7 7 0 .68-.11 1.34-.29 1.96l1.56 1.56c.47-1.08.73-2.27.73-3.52 0-4.97-4.03-9-9-9-1.25 0-2.44.26-3.53.72l1.57 1.57zm7.297-4.48l4.607 3.845-1.28 1.535-4.61-3.843zm1.903 16.51l-1.43-1.43-9.7-9.7-1.43-1.43-.74-.74L4.52 3.6l-1.5-1.5-1.41 1.41 1.37 1.37-.92.77 1.28 1.54 1.06-.88.8.8C3.83 8.69 3 10.75 3 13c0 4.97 4.03 9 9 9 2.25 0 4.31-.83 5.89-2.2l2.1 2.1 1.41-1.41-2.16-2.17zM12 20c-3.86 0-7-3.14-7-7 0-1.7.61-3.26 1.62-4.47l9.85 9.85C15.26 19.39 13.7 20 12 20zM7.48 3.73l.46-.38-1.28-1.54-.6.5z" }), "AlarmOffTwoTone"),
                    zo = (0, mt.A)(a.createElement("path", { d: "M22 5.72l-4.6-3.86-1.29 1.53 4.6 3.86L22 5.72zM7.88 3.39L6.6 1.86 2 5.71l1.29 1.53 4.59-3.85zM12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9c4.97 0 9-4.03 9-9s-4.03-9-9-9zm0 16c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7zm-1.46-5.47L8.41 12.4l-1.06 1.06 3.18 3.18 6-6-1.06-1.06-4.93 4.95z" }), "AlarmOn"),
                    xo = (0, mt.A)(a.createElement("path", { d: "M10.54 14.53L8.41 12.4l-1.06 1.06 3.18 3.18 6-6-1.06-1.06zm6.797-12.72l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmOnOutlined"),
                    Ao = (0, mt.A)(a.createElement("path", { d: "M14.94 10.11l-4.4 4.42-1.6-1.6c-.29-.29-.77-.29-1.06 0-.29.29-.29.77 0 1.06L10 16.11c.29.29.77.29 1.06 0L16 11.17c.29-.29.29-.77 0-1.06-.29-.29-.77-.29-1.06 0zm6.24-5.1L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmOnRounded"),
                    ko = (0, mt.A)(a.createElement("path", { d: "M10.54 14.53L8.41 12.4l-1.06 1.06 3.18 3.18 6-6-1.06-1.06zm6.797-12.72l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmOnSharp"),
                    So = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.86 0-7 3.14-7 7s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm-1.47 10.64l-3.18-3.18 1.06-1.06 2.13 2.13 4.93-4.95 1.06 1.06-6 6z", opacity: ".3" }), a.createElement("path", { d: "M10.54 14.53L8.41 12.4l-1.06 1.06 3.18 3.18 6-6-1.06-1.06zm6.797-12.72l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" })), "AlarmOnTwoTone"),
                    Mo = (0, mt.A)(a.createElement("path", { d: "M12.5 8H11v6l4.75 2.85.75-1.23-4-2.37zm4.837-6.19l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmOutlined"),
                    Eo = (0, mt.A)(a.createElement("path", { d: "M15.87 15.25l-3.37-2V8.72c0-.4-.32-.72-.72-.72h-.06c-.4 0-.72.32-.72.72v4.72c0 .35.18.68.49.86l3.65 2.19c.34.2.78.1.98-.24.21-.35.1-.8-.25-1zm5.31-10.24L18.1 2.45c-.42-.35-1.05-.3-1.41.13-.35.42-.29 1.05.13 1.41l3.07 2.56c.42.35 1.05.3 1.41-.13.36-.42.3-1.05-.12-1.41zM4.1 6.55l3.07-2.56c.43-.36.49-.99.13-1.41-.35-.43-.98-.48-1.4-.13L2.82 5.01c-.42.36-.48.99-.12 1.41.35.43.98.48 1.4.13zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmRounded"),
                    Co = (0, mt.A)(a.createElement("path", { d: "M12.5 8H11v6l4.75 2.85.75-1.23-4-2.37zm4.837-6.19l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" }), "AlarmSharp"),
                    To = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 6c-3.86 0-7 3.14-7 7s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm3.75 10.85L11 14V8h1.5v5.25l4 2.37-.75 1.23z", opacity: ".3" }), a.createElement("path", { d: "M12.5 8H11v6l4.75 2.85.75-1.23-4-2.37zm4.837-6.19l4.607 3.845-1.28 1.535-4.61-3.843zm-10.674 0l1.282 1.536L3.337 7.19l-1.28-1.536zM12 4c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z" })), "AlarmTwoTone"),
                    Ho = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 14.5c-2.49 0-4.5-2.01-4.5-4.5S9.51 7.5 12 7.5s4.5 2.01 4.5 4.5-2.01 4.5-4.5 4.5zm0-5.5c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1z" }), "Album"),
                    Lo = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-12.5c-2.49 0-4.5 2.01-4.5 4.5s2.01 4.5 4.5 4.5 4.5-2.01 4.5-4.5-2.01-4.5-4.5-4.5zm0 5.5c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" }), "AlbumOutlined"),
                    Io = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 14.5c-2.49 0-4.5-2.01-4.5-4.5S9.51 7.5 12 7.5s4.5 2.01 4.5 4.5-2.01 4.5-4.5 4.5zm0-5.5c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1z" }), "AlbumRounded"),
                    jo = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 14.5c-2.49 0-4.5-2.01-4.5-4.5S9.51 7.5 12 7.5s4.5 2.01 4.5 4.5-2.01 4.5-4.5 4.5zm0-5.5c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1z" }), "AlbumSharp"),
                    Vo = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm0 12.5c-2.49 0-4.5-2.01-4.5-4.5S9.51 7.5 12 7.5s4.5 2.01 4.5 4.5-2.01 4.5-4.5 4.5z", opacity: ".3" }), a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-12.5c-2.49 0-4.5 2.01-4.5 4.5s2.01 4.5 4.5 4.5 4.5-2.01 4.5-4.5-2.01-4.5-4.5-4.5zm0 5.5c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z" })), "AlbumTwoTone"),
                    Oo = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 6h-4c0 1.62-1.38 3-3 3s-3-1.38-3-3H5V5h14v4zm-4 7h6v3c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3z" }), "AllInbox"),
                    Ro = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 10h3.13c.21.78.67 1.47 1.27 2H5v-2zm14 2h-4.4c.6-.53 1.06-1.22 1.27-2H19v2zm0-4h-5v1c0 1.07-.93 2-2 2s-2-.93-2-2V8H5V5h14v3zm-2 7h-3v1c0 .47-.19.9-.48 1.25-.37.45-.92.75-1.52.75s-1.15-.3-1.52-.75c-.29-.35-.48-.78-.48-1.25v-1H3v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4h-4zM5 17h3.13c.02.09.06.17.09.25.24.68.65 1.28 1.18 1.75H5v-2zm14 2h-4.4c.54-.47.95-1.07 1.18-1.75.03-.08.07-.16.09-.25H19v2z" }), "AllInboxOutlined"),
                    Po = (0, mt.A)(a.createElement("path", { d: "M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 6h-3.14c-.47 0-.84.33-.97.78C14.53 11.04 13.35 12 12 12s-2.53-.96-2.89-2.22c-.13-.45-.5-.78-.97-.78H5V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v3zm-3.13 7H20c.55 0 1 .45 1 1v2c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-2c0-.55.45-1 1-1h4.13c.47 0 .85.34.98.8.35 1.27 1.51 2.2 2.89 2.2s2.54-.93 2.89-2.2c.13-.46.51-.8.98-.8z" }), "AllInboxRounded"),
                    Do = (0, mt.A)(a.createElement("path", { d: "M21 3H3v11h18V3zm-2 6h-4c0 1.62-1.38 3-3 3s-3-1.38-3-3H5V5h14v4zm-4 7h6v5H3v-5h6c0 1.66 1.34 3 3 3s3-1.34 3-3z" }), "AllInboxSharp"),
                    Fo = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M19 3H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 10h3.13c.21.78.67 1.47 1.27 2H5v-2zm14 2h-4.4c.6-.53 1.06-1.22 1.27-2H19v2zm0-4h-5v1c0 1.07-.93 2-2 2s-2-.93-2-2V8H5V5h14v3zm-5 7v1c0 .47-.19.9-.48 1.25-.37.45-.92.75-1.52.75s-1.15-.3-1.52-.75c-.29-.35-.48-.78-.48-1.25v-1H3v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4h-7zm-9 2h3.13c.02.09.06.17.09.25.24.68.65 1.28 1.18 1.75H5v-2zm14 2h-4.4c.54-.47.95-1.07 1.18-1.75.03-.08.07-.16.09-.25H19v2z" }), a.createElement("path", { d: "M8.13 10H5v2h4.4c-.6-.53-1.06-1.22-1.27-2zm6.47 2H19v-2h-3.13c-.21.78-.67 1.47-1.27 2zm-6.38 5.25c-.03-.08-.06-.16-.09-.25H5v2h4.4c-.53-.47-.94-1.07-1.18-1.75zm7.65-.25c-.02.09-.06.17-.09.25-.23.68-.64 1.28-1.18 1.75H19v-2h-3.13z", opacity: ".3" })), "AllInboxTwoTone"),
                    No = (0, mt.A)(a.createElement("path", { d: "M18.6 6.62c-1.44 0-2.8.56-3.77 1.53L12 10.66 10.48 12h.01L7.8 14.39c-.64.64-1.49.99-2.4.99-1.87 0-3.39-1.51-3.39-3.38S3.53 8.62 5.4 8.62c.91 0 1.76.35 2.44 1.03l1.13 1 1.51-1.34L9.22 8.2C8.2 7.18 6.84 6.62 5.4 6.62 2.42 6.62 0 9.04 0 12s2.42 5.38 5.4 5.38c1.44 0 2.8-.56 3.77-1.53l2.83-2.5.01.01L13.52 12h-.01l2.69-2.39c.64-.64 1.49-.99 2.4-.99 1.87 0 3.39 1.51 3.39 3.38s-1.52 3.38-3.39 3.38c-.9 0-1.76-.35-2.44-1.03l-1.14-1.01-1.51 1.34 1.27 1.12c1.02 1.01 2.37 1.57 3.82 1.57 2.98 0 5.4-2.41 5.4-5.38s-2.42-5.37-5.4-5.37z" }), "AllInclusive"),
                    _o = (0, mt.A)(a.createElement("path", { d: "M18.6 6.62c-1.44 0-2.8.56-3.77 1.53L7.8 14.39c-.64.64-1.49.99-2.4.99-1.87 0-3.39-1.51-3.39-3.38S3.53 8.62 5.4 8.62c.91 0 1.76.35 2.44 1.03l1.13 1 1.51-1.34L9.22 8.2C8.2 7.18 6.84 6.62 5.4 6.62 2.42 6.62 0 9.04 0 12s2.42 5.38 5.4 5.38c1.44 0 2.8-.56 3.77-1.53l7.03-6.24c.64-.64 1.49-.99 2.4-.99 1.87 0 3.39 1.51 3.39 3.38s-1.52 3.38-3.39 3.38c-.9 0-1.76-.35-2.44-1.03l-1.14-1.01-1.51 1.34 1.27 1.12c1.02 1.01 2.37 1.57 3.82 1.57 2.98 0 5.4-2.41 5.4-5.38s-2.42-5.37-5.4-5.37z" }), "AllInclusiveOutlined"),
                    Bo = (0, mt.A)(a.createElement("path", { d: "M20.22 6.86c-2-.6-4.06-.04-5.39 1.29L12 10.66 10.48 12h.01L7.8 14.39c-.81.81-1.95 1.15-3.12.92-1.25-.25-2.28-1.25-2.57-2.49-.52-2.23 1.16-4.2 3.29-4.2.91 0 1.76.35 2.44 1.03l.47.41c.38.34.95.34 1.33 0 .45-.4.45-1.1 0-1.5l-.42-.36C8.2 7.18 6.84 6.62 5.4 6.62 2.42 6.62 0 9.04 0 12s2.42 5.38 5.4 5.38c1.44 0 2.8-.56 3.77-1.53l2.83-2.5.01.01L13.52 12h-.01l2.69-2.39c.81-.81 1.95-1.15 3.12-.92 1.25.25 2.28 1.25 2.57 2.49.52 2.23-1.16 4.2-3.29 4.2-.9 0-1.76-.35-2.44-1.03l-.48-.42c-.38-.34-.95-.34-1.33 0-.45.4-.45 1.1 0 1.5l.42.37c1.02 1.01 2.37 1.57 3.82 1.57 3.27 0 5.86-2.9 5.33-6.25-.3-1.99-1.77-3.69-3.7-4.26z" }), "AllInclusiveRounded"),
                    Wo = (0, mt.A)(a.createElement("path", { d: "M18.6 6.62c-1.44 0-2.8.56-3.77 1.53L7.8 14.39c-.64.64-1.49.99-2.4.99-1.87 0-3.39-1.51-3.39-3.38S3.53 8.62 5.4 8.62c.91 0 1.76.35 2.44 1.03l1.13 1 1.51-1.34L9.22 8.2C8.2 7.18 6.84 6.62 5.4 6.62 2.42 6.62 0 9.04 0 12s2.42 5.38 5.4 5.38c1.44 0 2.8-.56 3.77-1.53L13.51 12l2.69-2.39c.64-.64 1.49-.99 2.4-.99 1.87 0 3.39 1.51 3.39 3.38s-1.52 3.38-3.39 3.38c-.9 0-1.76-.35-2.44-1.03l-1.14-1.01-1.51 1.34 1.27 1.12c1.02 1.01 2.37 1.57 3.82 1.57 2.98 0 5.4-2.41 5.4-5.38s-2.42-5.37-5.4-5.37z" }), "AllInclusiveSharp"),
                    Uo = (0, mt.A)(a.createElement("path", { d: "M18.6 6.62c-1.44 0-2.8.56-3.77 1.53L7.8 14.39c-.64.64-1.49.99-2.4.99-1.87 0-3.39-1.51-3.39-3.38S3.53 8.62 5.4 8.62c.91 0 1.76.35 2.44 1.03l1.13 1 1.51-1.34L9.22 8.2C8.2 7.18 6.84 6.62 5.4 6.62 2.42 6.62 0 9.04 0 12s2.42 5.38 5.4 5.38c1.44 0 2.8-.56 3.77-1.53l7.03-6.24c.64-.64 1.49-.99 2.4-.99 1.87 0 3.39 1.51 3.39 3.38s-1.52 3.38-3.39 3.38c-.9 0-1.76-.35-2.44-1.03l-1.14-1.01-1.51 1.34 1.27 1.12c1.02 1.01 2.37 1.57 3.82 1.57 2.98 0 5.4-2.41 5.4-5.38s-2.42-5.37-5.4-5.37z" }), "AllInclusiveTwoTone"),
                    qo = (0, mt.A)(a.createElement("path", { d: "M16.21 4.16l4 4v-4zm4 12l-4 4h4zm-12 4l-4-4v4zm-4-12l4-4h-4zm12.95-.95c-2.73-2.73-7.17-2.73-9.9 0s-2.73 7.17 0 9.9 7.17 2.73 9.9 0 2.73-7.16 0-9.9zm-1.1 8.8c-2.13 2.13-5.57 2.13-7.7 0s-2.13-5.57 0-7.7 5.57-2.13 7.7 0 2.13 5.57 0 7.7z" }), "AllOut"),
                    Go = (0, mt.A)(a.createElement("path", { d: "M4 4v4l4-4zm12 0l4 4V4zm4 16v-4l-4 4zM4 20h4l-4-4zm15-8c0-3.87-3.13-7-7-7s-7 3.13-7 7 3.13 7 7 7 7-3.13 7-7zm-7 5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" }), "AllOutOutlined"),
                    Ko = (0, mt.A)(a.createElement("path", { d: "M4 4.5V8l4-4H4.5c-.28 0-.5.22-.5.5zM16 4l4 4V4.5c0-.28-.22-.5-.5-.5H16zm4 15.5V16l-4 4h3.5c.28 0 .5-.22.5-.5zM4.5 20H8l-4-4v3.5c0 .28.22.5.5.5zM19 12c0-3.87-3.13-7-7-7s-7 3.13-7 7 3.13 7 7 7 7-3.13 7-7zm-7 5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" }), "AllOutRounded"),
                    Zo = (0, mt.A)(a.createElement("path", { d: "M4 4v4l4-4zm12 0l4 4V4zm4 16v-4l-4 4zM4 20h4l-4-4zm15-8c0-3.87-3.13-7-7-7s-7 3.13-7 7 3.13 7 7 7 7-3.13 7-7zm-7 5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" }), "AllOutSharp"),
                    Yo = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("circle", { cx: "12", cy: "12", r: "5", opacity: ".3" }), a.createElement("path", { d: "M4 4v4l4-4zm12 0l4 4V4zm4 16v-4l-4 4zM4 20h4l-4-4zm15-8c0-3.87-3.13-7-7-7s-7 3.13-7 7 3.13 7 7 7 7-3.13 7-7zm-7 5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" })), "AllOutTwoTone"),
                    Xo = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10h5v-2h-5c-4.34 0-8-3.66-8-8s3.66-8 8-8 8 3.66 8 8v1.43c0 .79-.71 1.57-1.5 1.57s-1.5-.78-1.5-1.57V12c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5c1.38 0 2.64-.56 3.54-1.47.65.89 1.77 1.47 2.96 1.47 1.97 0 3.5-1.6 3.5-3.57V12c0-5.52-4.48-10-10-10zm0 13c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z" }), "AlternateEmail"),
                    $o = (0, mt.A)(a.createElement("path", { d: "M12 1.95c-5.52 0-10 4.48-10 10s4.48 10 10 10h5v-2h-5c-4.34 0-8-3.66-8-8s3.66-8 8-8 8 3.66 8 8v1.43c0 .79-.71 1.57-1.5 1.57s-1.5-.78-1.5-1.57v-1.43c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5c1.38 0 2.64-.56 3.54-1.47.65.89 1.77 1.47 2.96 1.47 1.97 0 3.5-1.6 3.5-3.57v-1.43c0-5.52-4.48-10-10-10zm0 13c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z" }), "AlternateEmailOutlined"),
                    Qo = (0, mt.A)(a.createElement("path", { d: "M12.72 2.03C6.63 1.6 1.6 6.63 2.03 12.72 2.39 18.01 7.01 22 12.31 22H16c.55 0 1-.45 1-1s-.45-1-1-1h-3.67c-3.73 0-7.15-2.42-8.08-6.03-1.49-5.8 3.91-11.21 9.71-9.71C17.58 5.18 20 8.6 20 12.33v1.1c0 .79-.71 1.57-1.5 1.57s-1.5-.78-1.5-1.57v-1.25c0-2.51-1.78-4.77-4.26-5.12-3.4-.49-6.27 2.45-5.66 5.87.34 1.91 1.83 3.49 3.72 3.94 1.84.43 3.59-.16 4.74-1.33.89 1.22 2.67 1.86 4.3 1.21 1.34-.53 2.16-1.9 2.16-3.34v-1.09c0-5.31-3.99-9.93-9.28-10.29zM12 15c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z" }), "AlternateEmailRounded"),
                    Jo = (0, mt.A)(a.createElement("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10h5v-2h-5c-4.34 0-8-3.66-8-8s3.66-8 8-8 8 3.66 8 8v1.43c0 .79-.71 1.57-1.5 1.57s-1.5-.78-1.5-1.57V12c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5c1.38 0 2.64-.56 3.54-1.47.65.89 1.77 1.47 2.96 1.47 1.97 0 3.5-1.6 3.5-3.57V12c0-5.52-4.48-10-10-10zm0 13c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z" }), "AlternateEmailSharp"),
                    ei = (0, mt.A)(a.createElement("path", { fillOpacity: ".9", d: "M12 21.95h5v-2h-5c-4.34 0-8-3.66-8-8s3.66-8 8-8 8 3.66 8 8v1.43c0 .79-.71 1.57-1.5 1.57s-1.5-.78-1.5-1.57v-1.43c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5c1.38 0 2.64-.56 3.54-1.47.65.89 1.77 1.47 2.96 1.47 1.97 0 3.5-1.6 3.5-3.57v-1.43c0-5.52-4.48-10-10-10s-10 4.48-10 10 4.48 10 10 10zm0-7c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z" }), "AlternateEmailTwoTone"),
                    ti = (0, mt.A)(a.createElement("path", { d: "M7 4h10v15H7zM3 6h2v11H3zM19 6h2v11h-2z" }), "AmpStories"),
                    ni = (0, mt.A)(a.createElement("path", { d: "M7 19h10V4H7v15zM9 6h6v11H9V6zM3 6h2v11H3zM19 6h2v11h-2z" }), "AmpStoriesOutlined"),
                    ri = (0, mt.A)(a.createElement("path", { d: "M16 4H8c-.55 0-1 .45-1 1v13c0 .55.45 1 1 1h8c.55 0 1-.45 1-1V5c0-.55-.45-1-1-1zM4 6c-.55 0-1 .45-1 1v9c0 .55.45 1 1 1s1-.45 1-1V7c0-.55-.45-1-1-1zM20 6c-.55 0-1 .45-1 1v9c0 .55.45 1 1 1s1-.45 1-1V7c0-.55-.45-1-1-1z" }), "AmpStoriesRounded"),
                    ai = (0, mt.A)(a.createElement("path", { d: "M7 4h10v15H7zM3 6h2v11H3zM19 6h2v11h-2z" }), "AmpStoriesSharp"),
                    oi = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M9 6h6v11H9z", opacity: ".3" }), a.createElement("path", { d: "M7 19h10V4H7v15zM9 6h6v11H9V6zM3 6h2v11H3zM19 6h2v11h-2z" })), "AmpStoriesTwoTone"),
                    ii = (0, mt.A)(a.createElement("path", { d: "M17.6 9.48l1.84-3.18c.16-.31.04-.69-.26-.85-.29-.15-.65-.06-.83.22l-1.88 3.24c-2.86-1.21-6.08-1.21-8.94 0L5.65 5.67c-.19-.29-.58-.38-.87-.2-.28.18-.37.54-.22.83L6.4 9.48C3.3 11.25 1.28 14.44 1 18h22c-.28-3.56-2.3-6.75-5.4-8.52zM7 15.25c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25zm10 0c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25z" }), "Android"),
                    li = (0, mt.A)(a.createElement("path", { d: "M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71s-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z" }), "AndroidOutlined"),
                    si = (0, mt.A)(a.createElement("path", { d: "M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71s-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z" }), "AndroidRounded"),
                    ci = (0, mt.A)(a.createElement("path", { d: "M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71s-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z" }), "AndroidSharp"),
                    di = (0, mt.A)(a.createElement("path", { d: "M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71s-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z" }), "AndroidTwoTone"),
                    ui = (0, mt.A)(a.createElement("path", { d: "M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 9h-2V5h2v6zm0 4h-2v-2h2v2z" }), "Announcement"),
                    hi = (0, mt.A)(a.createElement("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12zM11 5h2v6h-2zm0 8h2v2h-2z" }), "AnnouncementOutlined"),
                    mi = (0, mt.A)(a.createElement("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-8 9c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1 4h-2v-2h2v2z" }), "AnnouncementRounded"),
                    pi = (0, mt.A)(a.createElement("path", { d: "M22 2H2v20l4-4h16V2zm-9 9h-2V5h2v6zm0 4h-2v-2h2v2z" }), "AnnouncementSharp"),
                    fi = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M4 4v13.17l.59-.59.58-.58H20V4H4zm9 11h-2v-2h2v2zm0-4h-2V5h2v6z", opacity: ".3" }), a.createElement("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17l-.59.59-.58.58V4h16v12zM11 5h2v6h-2zm0 8h2v2h-2z" })), "AnnouncementTwoTone"),
                    vi = (0, mt.A)(a.createElement("path", { d: "M17 11V3H7v4H3v14h8v-4h2v4h8V11h-4zM7 19H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm4 4H9v-2h2v2zm0-4H9V9h2v2zm0-4H9V5h2v2zm4 8h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm4 12h-2v-2h2v2zm0-4h-2v-2h2v2z" }), "Apartment"),
                    gi = (0, mt.A)(a.createElement("path", { d: "M17 11V3H7v4H3v14h8v-4h2v4h8V11h-4zM7 19H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm4 4H9v-2h2v2zm0-4H9V9h2v2zm0-4H9V5h2v2zm4 8h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm4 12h-2v-2h2v2zm0-4h-2v-2h2v2z" }), "ApartmentOutlined"),
                    yi = (0, mt.A)(a.createElement("path", { d: "M17 11V5c0-1.1-.9-2-2-2H9c-1.1 0-2 .9-2 2v2H5c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h5c.55 0 1-.45 1-1v-3h2v3c0 .55.45 1 1 1h5c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2h-2zM7 19H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm4 4H9v-2h2v2zm0-4H9V9h2v2zm0-4H9V5h2v2zm4 8h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm4 12h-2v-2h2v2zm0-4h-2v-2h2v2z" }), "ApartmentRounded"),
                    bi = (0, mt.A)(a.createElement("path", { d: "M17 11V3H7v4H3v14h8v-4h2v4h8V11h-4zM7 19H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm4 4H9v-2h2v2zm0-4H9V9h2v2zm0-4H9V5h2v2zm4 8h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm4 12h-2v-2h2v2zm0-4h-2v-2h2v2z" }), "ApartmentSharp"),
                    wi = (0, mt.A)(a.createElement("path", { d: "M17 11V3H7v4H3v14h8v-4h2v4h8V11h-4zM7 19H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm4 4H9v-2h2v2zm0-4H9V9h2v2zm0-4H9V5h2v2zm4 8h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm4 12h-2v-2h2v2zm0-4h-2v-2h2v2z" }), "ApartmentTwoTone"),
                    zi = (0, mt.A)(a.createElement("path", { d: "M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" }), "Apple"),
                    xi = (0, mt.A)(a.createElement("path", { d: "M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z" }), "Apps"),
                    Ai = (0, mt.A)(a.createElement("path", { d: "M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z" }), "AppsOutlined"),
                    ki = (0, mt.A)(a.createElement("path", { d: "M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z" }), "AppsRounded"),
                    Si = (0, mt.A)(a.createElement("path", { d: "M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z" }), "AppsSharp"),
                    Mi = (0, mt.A)(a.createElement("path", { d: "M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z" }), "AppsTwoTone"),
                    Ei = (0, mt.A)(a.createElement("path", { d: "M20.54 5.23l-1.39-1.68C18.88 3.21 18.47 3 18 3H6c-.47 0-.88.21-1.16.55L3.46 5.23C3.17 5.57 3 6.02 3 6.5V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6.5c0-.48-.17-.93-.46-1.27zM12 17.5L6.5 12H10v-2h4v2h3.5L12 17.5zM5.12 5l.81-1h12l.94 1H5.12z" }), "Archive"),
                    Ci = (0, mt.A)(a.createElement("path", { d: "M20.54 5.23l-1.39-1.68C18.88 3.21 18.47 3 18 3H6c-.47 0-.88.21-1.16.55L3.46 5.23C3.17 5.57 3 6.02 3 6.5V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6.5c0-.48-.17-.93-.46-1.27zM6.24 5h11.52l.81.97H5.44l.8-.97zM5 19V8h14v11H5zm8.45-9h-2.9v3H8l4 4 4-4h-2.55z" }), "ArchiveOutlined"),
                    Ti = (0, mt.A)(a.createElement("path", { d: "M20.54 5.23l-1.39-1.68C18.88 3.21 18.47 3 18 3H6c-.47 0-.88.21-1.16.55L3.46 5.23C3.17 5.57 3 6.02 3 6.5V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6.5c0-.48-.17-.93-.46-1.27zm-8.89 11.92L6.5 12H10v-2h4v2h3.5l-5.15 5.15c-.19.19-.51.19-.7 0zM5.12 5l.81-1h12l.94 1H5.12z" }), "ArchiveRounded"),
                    Hi = (0, mt.A)(a.createElement("path", { d: "M18.71 3H5.29L3 5.79V21h18V5.79L18.71 3zM12 17.5L6.5 12H10v-2h4v2h3.5L12 17.5zM5.12 5l.81-1h12l.94 1H5.12z" }), "ArchiveSharp"),
