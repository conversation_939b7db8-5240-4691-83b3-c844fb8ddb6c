            pC = () => { const [e, t] = (0, a.useState)(!0), [n, r] = (0, a.useState)(!1), o = (0, ae.d4)(Fr.VW), { isOwnershipEnabled: i } = (0, EM.A)(), { getAllOrgs: l } = (0, rC.A)(), [s, c] = (0, un.A)("ownership.dashboard.filters", null), d = i ? [{ name: "name", label: "Organization name" }, { name: "entityType", label: "Entity Type" }, { name: "hierarchyLevel", label: "Hierarchy Level" }, { name: "updatedAt", label: "Last updated" }, { name: "owners", label: "Owners" }, { name: "subsidiaries", label: "Subsidiaries" }, { name: "peopleCount", label: "People" }, { name: "totalCharts", label: "Total charts" }, { name: "controls", label: "" }] : [{ name: "name", label: "Organization name" }, { name: "website", label: "Website" }, { name: "updatedAt", label: "Last updated" }, { name: "peopleCount", label: "In talent pool" }, { name: "traditional", label: "Org charts" }, { name: "matrix", label: "Matrix charts" }, { name: "bod", label: "BOD charts" }, { name: "controls", label: "" }], { handleSearch: u, createSortHandler: h, orderBy: m, rows: p, order: f, totalCount: v, handleFilter: g, filterBy: y } = (0, Sn.s)({ dataSelector: kM.U3, isPaginated: !1, defaultValues: { orderBy: "name", filterBy: s || null } }); return (0, a.useEffect)((() => { ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_LOADED", extraParams: { isOwnershipEnabled: i } }), (async () => { r(!0), await l(), r(!1) })() }), []), (0, we.jsxs)(fM.A, { p: 2, maxWidth: DM.Fg, display: "flex", flexDirection: "column", height: "100%", children: [(0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", gap: 3, children: [(0, we.jsx)(HM, { user: o }), (0, we.jsx)(GM, { handleSearch: u, createSortHandler: h, orderBy: m || "", headCells: d, order: f, setIsCardView: t, isCardView: e, totalCount: v, mb: 3, filterBy: y, handleFilter: e => { g(e); const t = { ...e };
                                Object.keys(t).filter((e => 0 === t[e].length)).forEach((e => delete t[e])), c(Object.keys(t || {}).length ? t : null) } })] }), (0, we.jsxs)(dn.A, { transparent: !(null === p || void 0 === p || !p.length), loading: n, flex: 1, display: "flex", flexDirection: "column", children: [e && (0, we.jsx)(fM.A, { flex: 1, children: (0, we.jsx)(mC, { rows: p || [] }) }), !e && (0, we.jsx)(nC, { rows: p, createSortHandler: h, headCells: d, order: f, orderBy: m || "" })] })] }) }; var fC = n(1443),
            vC = n(82715),
            gC = n(25197),
            yC = n(49248); const bC = e => { var t, n; let { org: r, isPrintView: a = !1 } = e; if (!r) return (0, we.jsx)(we.Fragment, {}); const { isMinAdmin: o } = (0, EM.A)(), { openOrgEntityDialog: i } = (0, TM.A)(); return (0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", gap: 3, bgcolor: wM.Qs.Neutrals[0], border: a ? "inherit" : "1px solid ".concat(wM.Qs.Neutrals[300]), borderRadius: 2, p: 4, children: [(0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", borderBottom: "1px solid ".concat(wM.Qs.Neutrals[300]), pb: 3, px: a ? 3 : 0, children: [(0, we.jsxs)(fM.A, { display: "flex", gap: 2, height: "100%", children: [r.logo ? (0, we.jsx)("img", { src: r.logo, style: { border: "solid 2px #dddddd", borderRadius: "8px", maxWidth: "80px", maxHeight: "80px" } }) : (0, we.jsx)(uM.A, { color: "primary", width: 80, height: 80, children: (0, we.jsx)(Ee.gF, { icon: "Building", size: "x2" }) }), (0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", gap: .5, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.h1, children: r.name }), (0, we.jsx)(ln.A, { variant: MM.Eq.bodyMD, color: wM.Qs.Neutrals[900], children: r.locationString })] })] }), (0, we.jsxs)(fM.A, { display: "flex", gap: 3, alignItems: "center", mr: 3, children: [(null === r || void 0 === r ? void 0 : r.incorporatedYear) && (0, we.jsxs)(ln.A, { variant: MM.Eq.bodySM, children: ["Incorporated ", r.incorporatedYear] }), !(null === r || void 0 === r || null === (t = r.entityType) || void 0 === t || !t.length) && (0, we.jsx)(fM.A, { gap: 1, display: "flex", children: null === r || void 0 === r || null === (n = r.entityType) || void 0 === n ? void 0 : n.map((e => (0, we.jsx)(gC.A, { selected: !0, color: yC.HF[e], label: e, size: "large" }))) })] })] }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", gap: 3, flexDirection: a ? "column" : "row", px: a ? 5 : 0, children: [(0, we.jsx)(fM.A, { display: "grid", gridTemplateColumns: "repeat(5, 1fr)", gap: "24px", flex: 1, children: Object.keys(r).filter((e => !DM.Wd.includes(e))).sort(((e, t) => (DM.XQ.indexOf(e) + 1 || 999) - (DM.XQ.indexOf(t) + 1 || 999))).map((e => { let t = r[e]; if ("" === t || null === t || void 0 === t || !Array.isArray(t) && "object" === typeof t) return (0, we.jsx)(we.Fragment, {}); if (t && "string" === typeof t && DM.o5.includes(e)) { const e = _r.c9.fromISO(t);
                                t = e.isValid ? e.toLocaleString(_r.c9.DATE_SHORT) : "" } var n;
                            Array.isArray(t) && (t = (null === (n = t) || void 0 === n ? void 0 : n.join(", ")) || "-"); if ("boolean" === typeof t && (t = t ? "Yes" : "No"), "cardSize" === e) { t = { s: "Small", m: "Medium", l: "large", xl: "Large" } [t] || t } return (0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", gap: .5, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, color: wM.Qs.Neutrals[600], children: (0, tE.A)(DM.h2[null === e || void 0 === e ? void 0 : e.toUpperCase()] || (0, vC.D2)(e)) }), (0, we.jsx)(ln.A, { variant: MM.Eq.bodyLG, color: wM.Qs.Neutrals[900], children: t })] }) })) }), o && (0, we.jsx)(fM.A, { alignSelf: "flex-end", children: (0, we.jsx)(SM.A, { variant: "outlined", size: "medium", onClick: () => { i({ mode: "edit", orgOnlyMode: !0, orgToEdit: r }) }, children: "Edit Organization" }) })] })] }) }; var wC = n(38355),
            zC = n(69570),
            xC = n(64e3),
            AC = n(39652),
            kC = n(77502),
            SC = n(90889),
            MC = n(40710),
            EC = n(71806),
            CC = n(84882),
            TC = n(28076),
            HC = n(73460);

        function LC(e) { return (0, rE.Ay)("MuiPagination", e) }(0, nE.A)("MuiPagination", ["root", "ul", "outlined", "text"]); var IC = n(41944); const jC = ["boundaryCount", "componentName", "count", "defaultPage", "disabled", "hideNextButton", "hidePrevButton", "onChange", "page", "showFirstButton", "showLastButton", "siblingCount"]; var VC = n(67266),
            OC = n(10875);

        function RC(e) { return (0, rE.Ay)("MuiPaginationItem", e) } const PC = (0, nE.A)("MuiPaginationItem", ["root", "page", "sizeSmall", "sizeLarge", "text", "textPrimary", "textSecondary", "outlined", "outlinedPrimary", "outlinedSecondary", "rounded", "ellipsis", "firstLast", "previousNext", "focusVisible", "disabled", "selected", "icon", "colorPrimary", "colorSecondary"]); var DC = n(38354),
            FC = n(67884); const NC = (0, QM.A)((0, we.jsx)("path", { d: "M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" }), "NavigateBefore"),
            _C = (0, QM.A)((0, we.jsx)("path", { d: "M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" }), "NavigateNext"),
            BC = ["className", "color", "component", "components", "disabled", "page", "selected", "shape", "size", "slots", "type", "variant"],
            WC = (e, t) => { const { ownerState: n } = e; return [t.root, t[n.variant], t["size".concat((0, tE.A)(n.size))], "text" === n.variant && t["text".concat((0, tE.A)(n.color))], "outlined" === n.variant && t["outlined".concat((0, tE.A)(n.color))], "rounded" === n.shape && t.rounded, "page" === n.type && t.page, ("start-ellipsis" === n.type || "end-ellipsis" === n.type) && t.ellipsis, ("previous" === n.type || "next" === n.type) && t.previousNext, ("first" === n.type || "last" === n.type) && t.firstLast] },
            UC = (0, LM.Ay)("div", { name: "MuiPaginationItem", slot: "Root", overridesResolver: WC })((e => { let { theme: t, ownerState: n } = e; return (0, l.default)({}, t.typography.body2, { borderRadius: 16, textAlign: "center", boxSizing: "border-box", minWidth: 32, padding: "0 6px", margin: "0 3px", color: (t.vars || t).palette.text.primary, height: "auto", ["&.".concat(PC.disabled)]: { opacity: (t.vars || t).palette.action.disabledOpacity } }, "small" === n.size && { minWidth: 26, borderRadius: 13, margin: "0 1px", padding: "0 4px" }, "large" === n.size && { minWidth: 40, borderRadius: 20, padding: "0 10px", fontSize: t.typography.pxToRem(15) }) })),
            qC = (0, LM.Ay)($M.A, { name: "MuiPaginationItem", slot: "Root", overridesResolver: WC })((e => { let { theme: t, ownerState: n } = e; return (0, l.default)({}, t.typography.body2, { borderRadius: 16, textAlign: "center", boxSizing: "border-box", minWidth: 32, height: 32, padding: "0 6px", margin: "0 3px", color: (t.vars || t).palette.text.primary, ["&.".concat(PC.focusVisible)]: { backgroundColor: (t.vars || t).palette.action.focus }, ["&.".concat(PC.disabled)]: { opacity: (t.vars || t).palette.action.disabledOpacity }, transition: t.transitions.create(["color", "background-color"], { duration: t.transitions.duration.short }), "&:hover": { backgroundColor: (t.vars || t).palette.action.hover, "@media (hover: none)": { backgroundColor: "transparent" } }, ["&.".concat(PC.selected)]: { backgroundColor: (t.vars || t).palette.action.selected, "&:hover": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.selectedChannel, " / calc(").concat(t.vars.palette.action.selectedOpacity, " + ").concat(t.vars.palette.action.hoverOpacity, "))") : (0, VC.X4)(t.palette.action.selected, t.palette.action.selectedOpacity + t.palette.action.hoverOpacity), "@media (hover: none)": { backgroundColor: (t.vars || t).palette.action.selected } }, ["&.".concat(PC.focusVisible)]: { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.selectedChannel, " / calc(").concat(t.vars.palette.action.selectedOpacity, " + ").concat(t.vars.palette.action.focusOpacity, "))") : (0, VC.X4)(t.palette.action.selected, t.palette.action.selectedOpacity + t.palette.action.focusOpacity) }, ["&.".concat(PC.disabled)]: { opacity: 1, color: (t.vars || t).palette.action.disabled, backgroundColor: (t.vars || t).palette.action.selected } } }, "small" === n.size && { minWidth: 26, height: 26, borderRadius: 13, margin: "0 1px", padding: "0 4px" }, "large" === n.size && { minWidth: 40, height: 40, borderRadius: 20, padding: "0 10px", fontSize: t.typography.pxToRem(15) }, "rounded" === n.shape && { borderRadius: (t.vars || t).shape.borderRadius }) }), (e => { let { theme: t, ownerState: n } = e; return (0, l.default)({}, "text" === n.variant && {
                    ["&.".concat(PC.selected)]: (0, l.default)({}, "standard" !== n.color && { color: (t.vars || t).palette[n.color].contrastText, backgroundColor: (t.vars || t).palette[n.color].main, "&:hover": { backgroundColor: (t.vars || t).palette[n.color].dark, "@media (hover: none)": { backgroundColor: (t.vars || t).palette[n.color].main } }, ["&.".concat(PC.focusVisible)]: { backgroundColor: (t.vars || t).palette[n.color].dark } }, {
                        ["&.".concat(PC.disabled)]: { color: (t.vars || t).palette.action.disabled } }) }, "outlined" === n.variant && { border: t.vars ? "1px solid rgba(".concat(t.vars.palette.common.onBackgroundChannel, " / 0.23)") : "1px solid ".concat("light" === t.palette.mode ? "rgba(0, 0, 0, 0.23)" : "rgba(255, 255, 255, 0.23)"), ["&.".concat(PC.selected)]: (0, l.default)({}, "standard" !== n.color && { color: (t.vars || t).palette[n.color].main, border: "1px solid ".concat(t.vars ? "rgba(".concat(t.vars.palette[n.color].mainChannel, " / 0.5)") : (0, VC.X4)(t.palette[n.color].main, .5)), backgroundColor: t.vars ? "rgba(".concat(t.vars.palette[n.color].mainChannel, " / ").concat(t.vars.palette.action.activatedOpacity, ")") : (0, VC.X4)(t.palette[n.color].main, t.palette.action.activatedOpacity), "&:hover": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette[n.color].mainChannel, " / calc(").concat(t.vars.palette.action.activatedOpacity, " + ").concat(t.vars.palette.action.focusOpacity, "))") : (0, VC.X4)(t.palette[n.color].main, t.palette.action.activatedOpacity + t.palette.action.focusOpacity), "@media (hover: none)": { backgroundColor: "transparent" } }, ["&.".concat(PC.focusVisible)]: { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette[n.color].mainChannel, " / calc(").concat(t.vars.palette.action.activatedOpacity, " + ").concat(t.vars.palette.action.focusOpacity, "))") : (0, VC.X4)(t.palette[n.color].main, t.palette.action.activatedOpacity + t.palette.action.focusOpacity) } }, {
                        ["&.".concat(PC.disabled)]: { borderColor: (t.vars || t).palette.action.disabledBackground, color: (t.vars || t).palette.action.disabled } }) }) })),
            GC = (0, LM.Ay)("div", { name: "MuiPaginationItem", slot: "Icon", overridesResolver: (e, t) => t.icon })((e => { let { theme: t, ownerState: n } = e; return (0, l.default)({ fontSize: t.typography.pxToRem(20), margin: "0 -8px" }, "small" === n.size && { fontSize: t.typography.pxToRem(18) }, "large" === n.size && { fontSize: t.typography.pxToRem(22) }) })),
            KC = a.forwardRef((function(e, t) { const n = (0, eE.A)({ props: e, name: "MuiPaginationItem" }),
                    { className: r, color: a = "standard", component: o, components: i = {}, disabled: s = !1, page: c, selected: d = !1, shape: u = "circular", size: h = "medium", slots: m = {}, type: p = "page", variant: f = "text" } = n,
                    v = (0, ZM.default)(n, BC),
                    g = (0, l.default)({}, n, { color: a, disabled: s, selected: d, shape: u, size: h, type: p, variant: f }),
                    y = (0, OC.I)(),
                    b = (e => { const { classes: t, color: n, disabled: r, selected: a, size: o, shape: i, type: l, variant: s } = e, c = { root: ["root", "size".concat((0, tE.A)(o)), s, i, "standard" !== n && "color".concat((0, tE.A)(n)), "standard" !== n && "".concat(s).concat((0, tE.A)(n)), r && "disabled", a && "selected", { page: "page", first: "firstLast", last: "firstLast", "start-ellipsis": "ellipsis", "end-ellipsis": "ellipsis", previous: "previousNext", next: "previousNext" } [l]], icon: ["icon"] }; return (0, YM.A)(c, RC, t) })(g),
                    w = (y ? { previous: m.next || i.next || _C, next: m.previous || i.previous || NC, last: m.first || i.first || DC.A, first: m.last || i.last || FC.A } : { previous: m.previous || i.previous || NC, next: m.next || i.next || _C, first: m.first || i.first || DC.A, last: m.last || i.last || FC.A })[p]; return "start-ellipsis" === p || "end-ellipsis" === p ? (0, we.jsx)(UC, { ref: t, ownerState: g, className: (0, XM.A)(b.root, r), children: "\u2026" }) : (0, we.jsxs)(qC, (0, l.default)({ ref: t, ownerState: g, component: o, disabled: s, className: (0, XM.A)(b.root, r) }, v, { children: ["page" === p && c, w ? (0, we.jsx)(GC, { as: w, ownerState: g, className: b.icon }) : null] })) })),
            ZC = KC,
            YC = ["boundaryCount", "className", "color", "count", "defaultPage", "disabled", "getItemAriaLabel", "hideNextButton", "hidePrevButton", "onChange", "page", "renderItem", "shape", "showFirstButton", "showLastButton", "siblingCount", "size", "variant"],
            XC = (0, LM.Ay)("nav", { name: "MuiPagination", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, t[n.variant]] } })({}),
            $C = (0, LM.Ay)("ul", { name: "MuiPagination", slot: "Ul", overridesResolver: (e, t) => t.ul })({ display: "flex", flexWrap: "wrap", alignItems: "center", padding: 0, margin: 0, listStyle: "none" });

        function QC(e, t, n) { return "page" === e ? "".concat(n ? "" : "Go to ", "page ").concat(t) : "Go to ".concat(e, " page") } const JC = a.forwardRef((function(e, t) { const n = (0, eE.A)({ props: e, name: "MuiPagination" }),
                    { boundaryCount: r = 1, className: a, color: o = "standard", count: i = 1, defaultPage: s = 1, disabled: c = !1, getItemAriaLabel: d = QC, hideNextButton: u = !1, hidePrevButton: h = !1, renderItem: m = (e => (0, we.jsx)(ZC, (0, l.default)({}, e))), shape: p = "circular", showFirstButton: f = !1, showLastButton: v = !1, siblingCount: g = 1, size: y = "medium", variant: b = "text" } = n,
                    w = (0, ZM.default)(n, YC),
                    { items: z } = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { boundaryCount: t = 1, componentName: n = "usePagination", count: r = 1, defaultPage: a = 1, disabled: o = !1, hideNextButton: i = !1, hidePrevButton: s = !1, onChange: c, page: d, showFirstButton: u = !1, showLastButton: h = !1, siblingCount: m = 1 } = e, p = (0, ZM.default)(e, jC), [f, v] = (0, IC.A)({ controlled: d, default: a, name: n, state: "page" }), g = (e, t) => { d || v(t), c && c(e, t) }, y = (e, t) => { const n = t - e + 1; return Array.from({ length: n }, ((t, n) => e + n)) }, b = y(1, Math.min(t, r)), w = y(Math.max(r - t + 1, t + 1), r), z = Math.max(Math.min(f - m, r - t - 2 * m - 1), t + 2), x = Math.min(Math.max(f + m, t + 2 * m + 2), w.length > 0 ? w[0] - 2 : r - 1), A = [...u ? ["first"] : [], ...s ? [] : ["previous"], ...b, ...z > t + 2 ? ["start-ellipsis"] : t + 1 < r - t ? [t + 1] : [], ...y(z, x), ...x < r - t - 1 ? ["end-ellipsis"] : r - t > t ? [r - t] : [], ...w, ...i ? [] : ["next"], ...h ? ["last"] : []], k = e => { switch (e) {
                                case "first":
                                    return 1;
                                case "previous":
                                    return f - 1;
                                case "next":
                                    return f + 1;
                                case "last":
                                    return r;
                                default:
                                    return null } }, S = A.map((e => "number" === typeof e ? { onClick: t => { g(t, e) }, type: "page", page: e, selected: e === f, disabled: o, "aria-current": e === f ? "true" : void 0 } : { onClick: t => { g(t, k(e)) }, type: e, page: k(e), selected: !1, disabled: o || -1 === e.indexOf("ellipsis") && ("next" === e || "last" === e ? f >= r : f <= 1) })); return (0, l.default)({ items: S }, p) }((0, l.default)({}, n, { componentName: "Pagination" })),
                    x = (0, l.default)({}, n, { boundaryCount: r, color: o, count: i, defaultPage: s, disabled: c, getItemAriaLabel: d, hideNextButton: u, hidePrevButton: h, renderItem: m, shape: p, showFirstButton: f, showLastButton: v, siblingCount: g, size: y, variant: b }),
                    A = (e => { const { classes: t, variant: n } = e, r = { root: ["root", n], ul: ["ul"] }; return (0, YM.A)(r, LC, t) })(x); return (0, we.jsx)(XC, (0, l.default)({ "aria-label": "pagination navigation", className: (0, XM.A)(A.root, a), ownerState: x, ref: t }, w, { children: (0, we.jsx)($C, { className: A.ul, ownerState: x, children: z.map(((e, t) => (0, we.jsx)("li", { children: m((0, l.default)({}, e, { color: o, "aria-label": d(e.type, e.page, e.selected), shape: p, size: y, variant: b })) }, t))) }) })) })),
            eT = JC; var tT; const nT = (0, ee.Ay)(In.A)(tT || (tT = (0, J.A)(["\n  cursor: pointer;\n\n  &:hover {\n    > .MuiTableCell-root {\n      background-color: ", ";\n    }\n  }\n\n  > .MuiTableCell-root {\n    line-height: 2;\n    padding: 2px 24px 2px 16px;\n    background-color: ", ";\n  }\n"])), wM.Qs.Neutrals[200], wM.Qs.Neutrals[0]),
            rT = (0, LM.Ay)(zC.A)((() => ({
                ["&.".concat(xC.A.focusVisible)]: { backgroundColor: wM.Qs.Neutrals[0] } }))),
            aT = (0, LM.Ay)(AC.A)((() => ({
                ["&.".concat(kC.A.head)]: { backgroundColor: wM.Qs.Neutrals[200] } }))),
            oT = [{ name: "soleVotingPower", label: "Sole Voting Power" }, { name: "sharedDispositivePower", label: "Shared Voting Power" }, { name: "aggregateAmountOwned", label: "Aggregate Amount Owned" }],
            iT = e => { let { type: t, orgId: n, dataSelector: r, isPrintView: a = !1, secView: o, tableProps: i = { requestParams: {}, curry: !1 } } = e; const l = o ? [{ name: "name", label: "Organization name" }, { name: "stake", label: "Ownership %" }, { name: "locationString", label: "Location" }] : [{ name: "name", label: "Organization name" }, { name: "stake", label: "Ownership %" }, { name: "locationString", label: "Location" }, { name: "entityType", label: "Entity type" }];
                t === wC.zZ.Owner && l.push(...oT); const s = (0, ae.d4)(kM.U3),
                    { confirmAction: c } = (0, Mn.A)(),
                    { deleteRelation: d } = (0, rC.A)(),
                    { isMinOwner: u } = (0, EM.A)(),
                    { openRelationsDialog: h, openOrgEntityDialog: m, openOrgEntityInfoDialog: p } = (0, TM.A)(),
                    { handleSearch: f, createSortHandler: v, rows: g, order: y, handleChangePage: b, totalPages: w } = (0, Sn.s)({ ...i, dataSelector: r, defaultValues: { rowsPerPage: 10, orderBy: "name" } }),
                    z = (0, tE.A)(t === wC.zZ.Owner ? DM.GX : DM.su),
                    x = (0, tE.A)(t === wC.zZ.Owner ? DM.Z_ : DM.a1),
                    A = a ? fM.A : SC.A,
                    k = e => async () => {!o && e.id && n && await d(n, e.id, t) }; return (0, we.jsxs)(A, { ...a ? {} : { defaultExpanded: !0, variant: "outlined" }, style: { border: a ? "inherit" : "1px solid ".concat(wM.Qs.Neutrals[300]), borderRadius: a ? "inherit" : "8px" }, children: [(0, we.jsx)(rT, { expandIcon: a ? null : (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.gF, { icon: "SquareDown", size: "x2", color: "inherit" }) }), "aria-controls": "panel1-content", id: "panel1-header", children: (0, we.jsxs)(fM.A, { width: "100%", display: "flex", alignItems: "center", justifyContent: "space-between", children: [(0, we.jsx)(ln.A, { variant: MM.Eq.h1, children: x }), !a && (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", flexWrap: "wrap", gap: 3, mr: 3, my: 0, children: [(0, we.jsx)(VM.A, { placeholder: "Search ".concat(z), variant: "outlined", InputProps: { endAdornment: (0, we.jsx)(Ee.gF, { icon: "Search", size: "xs" }) }, onChange: f, onClick: e => e.stopPropagation(), style: { width: "390px" } }), u && !o && (0, we.jsxs)(SM.A, { color: "primary", variant: "contained", size: "small", style: { width: "130px" }, onClick: e => { e.stopPropagation(), h({ relationType: t, orgId: n, orgs: s }) }, children: [(0, we.jsx)(Ee.gF, { icon: "Add", size: "lg" }), z] })] })] }) }), (0, we.jsx)(MC.A, { children: (0, we.jsxs)(fM.A, { flex: 1, overflow: "auto", height: "100%", maxHeight: a ? "auto" : "60vh", children: [(0, we.jsxs)(EC.A, { "aria-label": "dashboard", size: "small", stickyHeader: !0, children: [(0, we.jsx)(CC.A, { children: (0, we.jsxs)(TC.A, { children: [l.map((e => (0, we.jsx)(aT, { children: (0, we.jsx)(cE, { onClick: v(e.name), direction: y, children: (0, we.jsx)(ln.A, { variant: MM.Eq.subheadingSM, color: wM.Qs.Neutrals[700], children: e.label }) }) }, "organization-chartlist-headcell-".concat(e.name)))), !a && !o && (0, we.jsx)(aT, {}, "organization-chartlist-headcell-settings")] }) }), (0, we.jsx)(HC.A, { children: g.map(((e, n) => (0, we.jsxs)(nT, { onClick: t => { null === t || void 0 === t || t.stopPropagation(), null === t || void 0 === t || t.preventDefault(), p({ org: e, showEdit: !1, showDelete: !1 }) }, hover: !0, style: { position: "relative" }, children: [l.map((t => { var r; let a = null === e || void 0 === e ? void 0 : e[t.name]; var o;
                                            ["typeOfReportingPerson", "entityType"].includes(t.name) && (a = (null === (o = a) || void 0 === o ? void 0 : o.join(", ")) || "-"); return (0, we.jsx)(AC.A, { id: "bodyrow-".concat(e.id, "-cell-").concat(n, "-").concat(t.name), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, color: wM.Qs.Neutrals[900], children: (null === (r = a) || void 0 === r ? void 0 : r.toString()) || "-" }) }, "bodyrow-".concat(e.id, "-cell-").concat(n, "-").concat(t.name)) })), !a && !o && (0, we.jsx)(AC.A, { children: (0, we.jsxs)(fM.A, { display: "flex", alignItems: "center", gap: 1, children: [(0, we.jsx)(XE.A, { size: "medium", color: "secondary", onClick: n => { null === n || void 0 === n || n.stopPropagation(), null === n || void 0 === n || n.preventDefault(), m({ mode: "edit", orgToEdit: e, relationType: t, orgOnlyMode: !u }) }, children: (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.gF, { size: "sm", icon: "Edit" }) }) }), u && (0, we.jsx)(XE.A, { size: "medium", color: "error", onClick: n => { null === n || void 0 === n || n.stopPropagation(), null === n || void 0 === n || n.preventDefault(), c({ title: "Removing ".concat(t), message: "Are you sure you want to remove this ".concat(t, "?"), cancelButtonText: "No", confirmButtonText: "Yes", execFunc: k(e), cancelFunc: () => {} }) }, children: (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.gF, { size: "sm", icon: "Delete" }) }) })] }) }, "bodyrow-".concat(e.id, "-cell-").concat(n, "-setting"))] }, "bodyrow-".concat(e.id)))) })] }), !a && (0, we.jsx)(fM.A, { position: "sticky", bottom: 0, display: "flex", justifyContent: "center", mt: 2, bgcolor: wM.Qs.Neutrals[0], children: (0, we.jsx)(eT, { count: w, shape: "rounded", color: "primary", onChange: (e, t) => b(e, Math.max(0, t - 1)) }) })] }) })] }) }; var lT = n(14429); const sT = e => { let { secView: t } = e; const { params: { orgId: n } } = (0, re.u)((0, ne.wi)()), r = (0, ae.d4)((0, kM.hm)(n || "", !!t)); return r ? (0, we.jsx)(lT.A, { children: (0, we.jsxs)(fM.A, { pb: (0, uE.A)(30), children: [(0, we.jsx)(bC, { org: r }), (0, we.jsx)(iT, { type: wC.zZ.Owner, dataSelector: t ? kM.Q9 : (0, kM.zQ)(n || ""), orgId: n || "", secView: t || !1 }), (0, we.jsx)(iT, { type: wC.zZ.Subsidiary, dataSelector: t ? kM.Z_ : (0, kM.OP)(n || ""), orgId: n || "", secView: t || !1 })] }) }) : (0, we.jsx)(we.Fragment, {}) }; var cT, dT = n(43845); const uT = (0, cC.A)(fM.A)(cT || (cT = (0, J.A)(["\n  ", "\n"])), (e => { let { cardBgColor: t } = e; return "\n    width: 100%;\n    height: 85%;\n    position: absolute;\n    transform: translateY(-50%);\n    top: 50%;\n    left: 3px;\n    z-index: -1;\n    background: ".concat(t && t !== DM.Pi ? t : "transparent", ";\n    border-radius: 4px;\n  ") })),
            hT = e => { var t; let { entity: n, onCardClick: r, onCardHover: o, onCardHoverLeave: i, onAddOwner: l, onAddSubsidiary: s, isHighlightedPath: c, customMenuItems: d, visibleMenuItems: u } = e; const h = DM.US[n.cardSize || DM.Hv],
                    [m, p] = (0, a.useState)(!1); return (0, we.jsxs)(fM.A, { onClick: e => { null === e || void 0 === e || e.stopPropagation(), r(n) }, onMouseEnter: () => { p(!0), o(n) }, onMouseLeave: () => { p(!1), i(n) }, style: { display: "flex", borderRadius: "4px", height: "100%", cursor: "pointer", justifyContent: "end" }, children: [(0, we.jsx)(uT, { cardBgColor: n.cardBgColor }), (0, we.jsxs)(fM.A, { style: { display: "flex", borderRadius: "4px", height: "100%", width: "calc(100% - ".concat(c ? DM.NL + 1 : DM.NL, "px)"), backgroundColor: DM.Pi, border: c ? "1.5px solid ".concat(wM.Qs.Info[500]) : "1px solid ".concat(wM.Qs.Neutrals[400]), padding: "15px 17px", flexDirection: "column", justifyContent: "space-between" }, children: [(0, we.jsxs)(fM.A, { display: "flex", gap: n.cardSize !== NM.zb.Small ? 1 : 0, flexDirection: "column", children: [(0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", children: [(0, we.jsxs)(fM.A, { display: "flex", alignItems: "center", gap: 1, children: [(null === n || void 0 === n ? void 0 : n.logo) && (0, we.jsx)(oC.A, { src: null === n || void 0 === n ? void 0 : n.logo, sx: { width: h.logoWidth, height: h.logoHeight }, slotProps: { img: { style: { objectFit: "scale-down" } } } }, "card-entity-logo-".concat(n.id || n.cik, "-").concat(n.logo)), !(null !== n && void 0 !== n && n.logo) && (0, we.jsx)(oC.A, { sx: { width: h.logoWidth, height: h.logoWidth }, "aria-label": "recipe", children: (0, we.jsx)(Ee.gF, { icon: "Building", size: "xs" }) }, "card-entity-logo-".concat(n.id || n.cik, "-").concat(n.logo)), (0, we.jsx)(ln.A, { variant: h.headingVariant, color: wM.Qs.Neutrals[900], children: null === n || void 0 === n ? void 0 : n.name })] }), (0, we.jsx)(JE, { org: n, color: null, customMenuItems: d, visible: u })] }), (0, we.jsxs)(fM.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: [(0, we.jsx)(fM.A, { display: "flex", gap: 1, alignItems: "center", children: null === n || void 0 === n || null === (t = n.entityType) || void 0 === t ? void 0 : t.map((e => (0, we.jsx)(aC.A, { title: e, arrow: !0, children: (0, we.jsxs)(fM.A, { display: "flex", gap: .5, alignItems: "center", children: [(0, we.jsx)(Ee.me, { name: "circle", variant: "solid", size: "xs", color: yC.HF[e] }, "card-entity-type-icon-".concat(e)), n.entityType.length < 2 && (0, we.jsx)(ln.A, { variant: h.fontVariant, color: wM.Qs.Neutrals[800], children: e })] }) }))) }), (0, we.jsx)(ln.A, { color: "inherit", variant: h.fontVariant, children: (null === n || void 0 === n ? void 0 : n.jurisdiction) || (null === n || void 0 === n ? void 0 : n.locationString) })] })] }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [n.hierarchyLevel && (0, we.jsx)(dT.A, { variant: "outlined", size: h.chipSize, label: (0, we.jsx)(ln.A, { color: "inherit", variant: h.fontVariant, children: (0, tE.A)(n.hierarchyLevel) }) }), n.entityPurpose && (0, we.jsx)(dT.A, { variant: "filled", size: h.chipSize, sx: { backgroundColor: "#f1f1f1" }, label: (0, we.jsx)(ln.A, { color: "inherit", variant: h.fontVariant, children: (0, tE.A)(n.entityPurpose) }) }), m && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(fM.A, { position: "absolute", top: 0, left: "50%", style: { transform: "translateX(-50%)" }, children: (0, we.jsx)(aC.A, { title: "Add ".concat(DM.GX), children: (0, we.jsx)(SM.A, { variant: "text", color: "primary", onClick: e => { e.stopPropagation(), l(n) }, children: (0, we.jsx)(Ee.me, { name: "plus", variant: "solid", size: h.addButtonSize }) }) }) }), (0, we.jsx)(fM.A, { position: "absolute", bottom: 0, left: "50%", style: { transform: "translateX(-50%)" }, children: (0, we.jsx)(aC.A, { title: "Add ".concat(DM.su), children: (0, we.jsx)(SM.A, { variant: "text", color: "primary", onClick: e => { e.stopPropagation(), s(n) }, children: (0, we.jsx)(Ee.me, { name: "plus", variant: "solid", size: h.addButtonSize }) }) }) })] })] })] })] }) }; var mT, pT = n(95622); const fT = ["soleVotingPower", "sharedVotingPower", "soleDispositivePower", "sharedDispositivePower", "aggregateAmountOwned"],
            vT = e => { let { entity: t } = e; const n = DM.US[t.cardSize || DM.Hv] || {}; return (0, we.jsxs)(we.Fragment, { children: [(null === t || void 0 === t ? void 0 : t.logo) && (0, we.jsx)(oC.A, { src: null === t || void 0 === t ? void 0 : t.logo, sx: { width: n.logoWidth || 25, height: n.logoHeight || 25 }, slotProps: { img: { style: { objectFit: "scale-down" } } } }, "card-entity-logo-".concat(t.id || t.cik, "-").concat(t.logo)), !(null !== t && void 0 !== t && t.logo) && (0, we.jsx)(oC.A, { sx: { width: n.logoWidth || 25, height: n.logoWidth || 25 }, "aria-label": "recipe", children: (0, we.jsx)(Ee.gF, { icon: "Building", size: "xs" }) }, "card-entity-logo-".concat(t.id || t.cik, "-").concat(t.logo))] }) },
            gT = (0, LM.Ay)(pT.A)(mT || (mT = (0, J.A)(["\n  ", "\n"])), (() => "\n  display: flex;\n  width: 30vw;\n  max-width: 270px;\n  flex-direction: column;\n  align-items: flex-start;\n  padding: 24px;\n  gap: 24px;\n  border-radius: 6px;\n  border: 2px solid ".concat(wM.Qs.Violet[500], ";\n  background: ").concat(wM.Qs.Neutrals[0], ';\n  box-shadow: 0px 344px 96px 0px rgba(0, 0, 0, 0.00), 0px 220px 88px 0px rgba(0, 0, 0, 0.01), 0px 124px 74px 0px rgba(0, 0, 0, 0.05), 0px 55px 55px 0px rgba(0, 0, 0, 0.09), 0px 14px 30px 0px rgba(0, 0, 0, 0.10);\n \n\n  &&[data-popper-placement*="bottom"] .arrow {\n   top: 0;\n    left: 0;\n    margin-top: -1em;\n    width: 3em;\n    height: 1em;\n    &::before {\n      border-width: 0 1em 1em 1em;\n      border-color: transparent transparent ').concat(wM.Qs.Violet[500], ' transparent;\n    }\n  }\n\n  &&[data-popper-placement*="top"] .arrow {\n     bottom: 0;\n    left: 0;\n    margin-bottom: -1em;\n    width: 3em;\n    height: 1em;\n    &::before {\n      border-width: 1em 1em 0 1em;\n      border-color: ').concat(wM.Qs.Violet[500], ' transparent transparent transparent;\n    }\n  }\n\n  &&[data-popper-placement*="right"] .arrow {\n    left: 0;\n    margin-left: -1em;\n    height: 3em;\n    width: 1em;\n    &::before {\n      border-width: 1em 1em 1em 0;\n      border-color: transparent ').concat(wM.Qs.Violet[500], ' transparent transparent;\n    }\n  }\n\n  &&[data-popper-placement*="left"] .arrow {\n   right: 0;\n    margin-right: -1em;\n    height: 3em;\n    width: 1em;\n    &::before {\n      border-width: 1em 0 1em 1em;\n      border-color: transparent transparent transparent ').concat(wM.Qs.Violet[500], ';\n    }\n  }\n\n  .arrow {\n    position: absolute;\n    font-size: 16px;\n    width: 3em;\n    height: 3em;\n    &::before {\n      content: "";\n      margin: auto;\n      display: block;\n      width: 0;\n      height: 0;\n      border-style: solid;\n    }\n\n  \n  }\n'))),
            yT = e => { let { anchorRef: t, parent: n, childRelation: r, onClose: o } = e; const i = (0, a.useRef)(null),
                    [l, s] = (0, a.useState)(null),
                    { openOrgEntityDialog: c } = (0, TM.A)(),
                    { deleteRelation: d } = (0, rC.A)(),
                    { confirmAction: u } = (0, Mn.A)(),
                    h = fT.some((e => { var t; return !(null === r || void 0 === r || null === (t = r.sec) || void 0 === t || !t[e]) })); return (0, we.jsxs)(gT, { open: !0, ref: i, anchorEl: t, placement: "auto-start", className: "popper", modifiers: [{ name: "preventOverflow", enabled: !0, options: { altAxis: !0, altBoundary: !0, tether: !0, rootBoundary: "document", padding: 8 } }, { name: "arrow", enabled: !0, options: { element: l } }, { name: "offset", enabled: !0, options: { offset: [0, 30] } }], children: [(0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", children: [(0, we.jsx)(ln.A, { variant: "subheadingMD", style: { flex: 1 }, children: "Ownership Details" }), (0, we.jsx)(SM.A, { variant: "text", color: "secondaryGrey", onClick: o, sx: { justifyContent: "right", padding: "0px !important" }, children: (0, we.jsx)(Ee.me, { name: "close", variant: "light", size: "lg" }) })] }), (0, we.jsxs)(ln.A, { variant: "caption", children: [n.name, "'s stake in ", r.name] }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", children: [(0, we.jsx)(vT, { entity: n }), (0, we.jsxs)(fM.A, { position: "relative", flex: 1, height: DM.US[n.cardSize || DM.Hv].logoWidth || 25, display: "flex", flexDirection: "column", justifyContent: "center", children: [(0, we.jsx)(ce.A, { variant: "middle", light: !0 }), (0, we.jsx)(dT.A, { style: { position: "absolute", left: "50%", background: wM.Qs.Neutrals[0], top: "50%", transform: "translateX(-50%) translateY(-50%)" }, variant: "outlined", label: "".concat(((null === r || void 0 === r ? void 0 : r.stake) || 0).toFixed(2), "%") })] }), (0, we.jsx)(vT, { entity: r })] }), h && (0, we.jsx)(fM.A, { display: "flex", flexDirection: "column", gap: 1, width: "100%", children: fT.map(((e, t) => { var n; if (0 === t) return (0, we.jsx)(fM.A, { mb: 2, children: (0, we.jsx)(ln.A, { variant: "subheadingSM", style: { flex: 1 }, children: "Voting Control" }) }); const a = null === r || void 0 === r || null === (n = r.sec) || void 0 === n ? void 0 : n[e]; return void 0 === a || null === a || "" === a ? (0, we.jsx)(we.Fragment, {}) : (0, we.jsxs)(fM.A, { width: "100%", children: [(0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", width: "100%", mb: 1, children: [(0, we.jsx)(ln.A, { variant: "labelLG", style: { flex: 3 }, children: DM.h2[e.toUpperCase()] || "" }), (0, we.jsx)(ln.A, { variant: "caption", style: { flex: 1 }, children: (null === a || void 0 === a ? void 0 : a.toLocaleString()) || 0 })] }), t !== fT.length - 1 && (0, we.jsx)(ce.A, { light: !0, variant: "fullWidth" })] }, "sec-field-".concat(e)) })) }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", width: "100%", children: [(0, we.jsx)(fM.A, { children: (0, we.jsx)(SM.A, { variant: "outlined", color: "error", size: "small", style: { marginLeft: "auto" }, onClick: e => { null === e || void 0 === e || e.stopPropagation(), u({ title: "Removing ".concat((0, tE.A)(DM.su)), message: "Are you sure you want to remove?", cancelButtonText: "No", confirmButtonText: "Yes", execFunc: async () => { await d(n.id, r.id || "", wC.zZ.Subsidiary) }, cancelFunc: () => {} }) }, children: "Remove" }) }), (0, we.jsxs)(SM.A, { variant: "outlined", color: "secondary", size: "small", onClick: e => { null === e || void 0 === e || e.stopPropagation(), c({ mode: "edit", orgOnlyMode: !1, primaryOrgId: n.id, orgToEdit: r, relationType: wC.zZ.Subsidiary }) }, children: ["Edit ", DM.su] })] }), (0, we.jsx)(fM.A, { className: "arrow", ref: s })] }) }; var bT = function(e) { return e[e.Reverse = -1] = "Reverse", e[e.Forward = 1] = "Forward", e[e.Center = 0] = "Center", e }(bT || {}); const wT = 10;

        function zT(e, t, n, r) { if (n.level === r.level) return []; const a = e.slice(r.level + 1, n.level),
                o = [],
                i = n.x <= r.x,
                l = n.x > r.x; for (const s of a) { let e = null,
                    a = 1e4; for (const o of s) { const s = t[o.id];
                    s && (e ? ((i && s.x <= r.x && Math.abs(s.x - n.x) < a || i && Math.abs(s.x + s.width - n.x) < a) && (e = s, a = Math.abs(Math.min(s.x - n.x, s.x + s.width - n.x))), l && s.x + s.width >= r.x && Math.abs(n.x - (s.x + s.width)) < a && (e = s, a = Math.abs(n.x - (s.x + s.width)))) : (e = s, a = Math.abs(n.x - s.x))) } e && o.unshift(e) } return o.unshift(n), o.push(r), o }

        function xT(e, t, n) { const r = 55,
                a = e.x,
                o = e.y,
                i = n.x - DM.le,
                l = n.y,
                s = e.x - n.x < n.x + n.width - e.x,
                c = n.x <= e.x && n.x + n.width >= e.x,
                d = e.x < n.x && n.x - DM.le - wT < e.x,
                u = n.x + n.width < e.x && n.x + n.width + 2 * DM.le > e.x ? bT.Center : e.x < n.x + n.width || t.x < n.x || c && !s ? bT.Forward : n.x < t.x || c && s ? bT.Reverse : bT.Center,
                h = o < l ? bT.Forward : o > l ? bT.Reverse : bT.Center; if (u === bT.Center || d) return { path: " M ".concat(a, " ").concat(o, "  \n        V ").concat(l, "\n      "), endDim: { id: t.id, x: a, y: l } }; const m = c && u === bT.Reverse ? n.x - DM.le : c || u === bT.Forward ? n.x + n.width + DM.le : i; return h === bT.Forward ? { path: " M ".concat(a, " ").concat(o, " \n        V ").concat(l - r - wT, "\n        Q ").concat(a, " ").concat(l - r, ", ").concat(a + wT * u, " ").concat(l - r, "\n        H ").concat(m - wT * u, "\n        Q ").concat(m, " ").concat(l - r, ", ").concat(m, " ").concat(l - r + wT, "\n        V ").concat(l, "\n        "), endDim: { id: t.id, x: m, y: l } } : { path: " M ".concat(a, " ").concat(o, " \n      V ").concat(o - r + wT, "\n      Q ").concat(a, " ").concat(o - r, ", ").concat(a + wT * u, " ").concat(o - r, "\n      H ").concat(m - u * wT, "\n      Q ").concat(m, " ").concat(o - r, ", ").concat(m, " ").concat(o - r - 20, "\n      V ").concat(l, "\n      "), endDim: { id: t.id, x: m, y: l } } }

        function AT(e, t, n) { const r = n["".concat(e.id, "-").concat(t.id)],
                a = 55,
                o = e.x,
                i = e.y,
                l = t.x,
                s = t.y,
                c = t.x + (r || t.width / 2),
                d = o < c ? bT.Forward : c < o ? bT.Reverse : bT.Center,
                u = i < s ? bT.Forward : i > s ? bT.Reverse : bT.Center; return u === bT.Reverse && bT.Center, d === bT.Center ? { path: " M ".concat(o, " ").concat(i, "\n        V ").concat(s, "\n      "), endDim: { id: e.id, x: l, y: s } } : u === bT.Forward ? { path: " M ".concat(o, " ").concat(i, "\n        V ").concat(s - a - wT, "\n        Q ").concat(o, " ").concat(s - a, ", ").concat(o + wT * d, " ").concat(s - a, "\n        H ").concat(c - wT * d, "\n        Q ").concat(c, " ").concat(s - a, ", ").concat(c, " ").concat(s - a + wT, "\n        V ").concat(s, "\n      "), endDim: { id: e.id, x: c, y: s } } : { path: " M ".concat(o, " ").concat(i, " \n    V ").concat(i - a + wT, "\n    Q ").concat(o, " ").concat(i - a, ", ").concat(o + wT * d, " ").concat(i - a, "\n    H ").concat(c - wT * d, "\n    Q ").concat(c, " ").concat(i - a, ", ").concat(c, " ").concat(i - a + wT, "\n    V ").concat(s, "\n    "), endDim: { id: e.id, x: l, y: s } } }

        function kT(e, t) { const n = DM.AU / 2,
                r = e.x + e.width / 2,
                a = e.y + e.height,
                o = e.x,
                i = t.x + t.width / 2,
                l = e.y,
                s = r > i ? bT.Reverse : r <= i ? bT.Forward : bT.Center,
                c = s === bT.Forward ? o + e.width + DM.le : o - DM.le; return { path: "M ".concat(r, " ").concat(a, " \n    V ").concat(a + n - wT, " \n    Q ").concat(r, " ").concat(a + n, ", ").concat(r + wT * s, " ").concat(a + n, "\n    H ").concat(c - wT * s, " \n    Q ").concat(c, " ").concat(a + n, ", ").concat(c, " ").concat(a + n - wT, "\n    V ").concat(l), endDim: { id: e.id, x: c, y: l } } } const ST = () => { var e; const { chartContainerWidth: t, chartContainerHeight: n } = (() => { const [e, t] = (0, a.useState)(0), [n, r] = (0, a.useState)(0); return (0, a.useEffect)((() => { const a = document.getElementById("ownershipContentContainer"),
                        { offsetWidth: o, offsetHeight: i } = a || { offsetWidth: 0, offsetHeight: 0 };
                    o > 0 && o !== e && t(o), i > 0 && i !== n && r(i) })), { chartContainerWidth: e, chartContainerHeight: n } })(), [r, o] = (0, a.useState)(null), [i, l] = (0, a.useState)(null), s = (0, ae.d4)(kM.U3), c = (0, ae.d4)(kM.$q), d = (0, ae.d4)(kM.s1), u = (0, ae.d4)(kM.N8), h = (0, ae.d4)(kM.EU), m = (0, ae.d4)(kM.KS), { levels: p, nodeDims: f, leveledParentChildMap: v, roots: g, levelMap: y } = (0, ae.d4)(kM.Xy), b = (0, $.useHistory)(), { containerRef: w } = function() { const e = (0, a.useRef)({}),
                    t = (0, a.useRef)(null),
                    n = n => { const a = t.current;
                        a && (e.current.scrollLeft = n.pageX + a.scrollLeft, e.current.scrollTop = n.pageY + a.scrollTop, e.current.hasMouseDown = !0, a.addEventListener("mousemove", r)) },
                    r = n => { const r = t.current; if (r && e.current.hasMouseDown) { const t = (e.current.scrollLeft || 0) - n.pageX,
                                a = (e.current.scrollTop || 0) - n.pageY;
                            r.scrollLeft = t, r.scrollTop = a } },
                    o = () => { const n = t.current;
                        e.current.hasMouseDown = !1, n && n.removeEventListener("mousemove", r) }; return (0, a.useEffect)((() => null !== t && void 0 !== t && t.current ? (t.current.addEventListener("mousedown", n), window.addEventListener("mouseup", o), () => { window.removeEventListener("mouseup", o) }) : () => {}), []), { containerRef: t } }(); let z = 0; const x = m * (null === (e = p[0] || []) || void 0 === e ? void 0 : e.reduce(((e, t) => { var n; return e + (null === (n = f[t.id]) || void 0 === n ? void 0 : n.recursiveWidth) || 0 }), 0)) || 0,
                A = Math.max(x, t),
                k = Math.max(Object.values(y).reduce(((e, t) => e + t.maxHeight), 0) + Object.values(y).length * (2 * DM.AU) + DM.s, n);
            x < t && (z = (t - x) / 2); const { openOrgEntityInfoDialog: S, openRelationsDialog: M } = (0, TM.A)(), E = e => { S({ org: e, showEdit: !1, showDelete: !1 }) }, C = e => { M({ relationType: wC.zZ.Owner, orgId: e.id, orgs: s }) }, T = e => { M({ relationType: wC.zZ.Subsidiary, orgId: e.id, orgs: s }) }, H = [{ label: "Focus", onClick: e => b.push((0, ne.wi)({ orgId: e.id, view: "chart", base: "protected" })) }, { label: "Ownership Report", onClick: e => b.push((0, ne.wi)({ orgId: e.id, view: "table", base: "protected" })) }, { label: "Add ".concat(DM.GX), onClick: C }, { label: "Add ".concat(DM.su), onClick: T }], L = e => { o(e.id) }, I = e => { e && o(null) }, j = (e, t, n) => { l((r => null === r && t && n ? { parent: { ...c[t.id] }, childRelation: { ...c[n.id] || {}, ...n }, anchorRef: e.currentTarget } : null)) }, V = e => { console.log("Show More Stakes", e) }, O = (() => { if (!r) return {}; const e = [],
                    t = {}; return function n(r, a, o) { const i = (null === (o = v[r]) || void 0 === o ? void 0 : o.parents) || [],
                        l = "".concat(a, "-").concat(r); if (t[l]) return null;
                    t[l] = !0, i.length || e.push({ fromId: r, id: null }); for (const t of i)
                        if (t.id) { const a = n(t.id, r);
                            a && e.push(a) } return { fromId: a, id: r } }(r, null), e.reduce(((e, t) => (null !== t && void 0 !== t && t.fromId && !e[t.fromId] && (e[t.fromId] = []), null !== t && void 0 !== t && t.fromId && null !== t && void 0 !== t && t.id && e[null === t || void 0 === t ? void 0 : t.fromId].push(t.id), e)), {}) })(), R = (() => { if (null === s || void 0 === s || !s.length) return []; const e = []; return (e => { for (const t of Object.values(f)) { const n = c[t.id],
                            r = !!O[t.id];
                        (null === t || void 0 === t ? void 0 : t.nodeType) === vC.Z6.Connection && e.push((0, we.jsx)("path", { d: "M ".concat(t.x + t.width / 2, " ").concat(t.y, " V ").concat(t.y + t.height), stroke: r ? DM.iW.stroke : DM.jb.stroke, strokeWidth: r ? DM.iW.strokeWidth : DM.jb.strokeWidth, fill: "none" }, t.id)), (null === t || void 0 === t ? void 0 : t.nodeType) === vC.Z6.Card && e.push((0, we.jsx)("foreignObject", { x: t.x, y: t.y, width: t.width, height: t.height, children: (0, we.jsx)(hT, { isHighlightedPath: !!O[t.id], entity: n, onCardClick: E, onCardHover: L, onCardHoverLeave: I, customMenuItems: H, onAddOwner: C, onAddSubsidiary: T, visibleMenuItems: { delete: !1, ownershipCharts: !1 } }) })) } })(e), e })(), P = (0, a.useMemo)((() => ((e, t) => { const n = []; return Object.keys(e).forEach((r => { Object.keys(e[r]).forEach((a => { var o; const i = e[r][a],
                            l = f[a],
                            s = null === (o = t[a] || []) || void 0 === o ? void 0 : o.includes(r),
                            c = (0, we.jsx)("path", { d: i, stroke: s ? DM.iW.stroke : DM.jb.stroke, strokeWidth: s ? DM.iW.strokeWidth : DM.jb.strokeWidth, fill: "none", markerEnd: (null === l || void 0 === l ? void 0 : l.nodeType) !== vC.Z6.Card ? void 0 : "url(#arrow-down".concat(s ? "-active" : "", ")") }, "line-".concat(r, "-").concat(a));
                        s ? n.push(c) : n.unshift(c) })) })), n })((() => { const e = {}; for (const r of Object.values(f)) { var t; const a = e[r.id] = {},
                        o = r.level,
                        i = (null === (t = v[r.id]) || void 0 === t ? void 0 : t.children) || []; for (const e of i) { var n; if (!e.id) continue; const t = f[e.id]; if (!t) continue; if (-1 === ((null === (n = v[e.id]) || void 0 === n ? void 0 : n.parents) || []).findIndex((e => e.id === r.id))) continue; const i = t.level; if (o === i) { let n = "",
                                o = null; const i = kT(r, t);
                            n += i.path, o = i.endDim; const l = AT({ ...o, id: r.id }, t, h);
                            n += l.path, o = l.endDim, a[e.id] = n } else if (o < i) { const n = t,
                                o = (zT(p, f, n, r) || []).map((e => ({ ...e, orgName: c[e.id].name }))); let i = "",
                                l = null; for (let t = o.length - 1; t > 0; t--) { const s = o[t],
                                    c = o[t - 1]; if (c) { if (l || (l = { id: s.id, x: s.x + s.width / 2, y: s.y + s.height }), c.id !== n.id) { const e = xT(l || s, s, c);
                                        i += " ".concat(e.path, " "), l = e.endDim } if (c.id === n.id) { const e = AT({ ...l, id: r.id }, c, h);
                                        i += " ".concat(e.path, " "), l = e.endDim } a[e.id] = i } } } else { const n = t,
                                o = zT(p, f, r, n) || []; let i = "",
                                l = null; for (let e = 0; e < o.length - 1; e++) { const t = o[e],
                                    a = o[e + 1]; if (!a) continue; if (0 === e) { const e = kT(t, a);
                                    i += " ".concat(e.path, " "), l = null === e || void 0 === e ? void 0 : e.endDim } const s = xT(l || t, t, a); if (i += " ".concat(s.path, " "), l = s.endDim, a.id === n.id) { const e = AT({ ...l, id: r.id }, a, h);
                                    i += " ".concat(e.path, " "), l = e.endDim } } a[e.id] = i } } } return e })(), O)), [f, O]), D = (0, a.useMemo)((() => { const e = ((null === p || void 0 === p ? void 0 : p[0]) || []).map((e => f[e.id])),
                    t = function(e) { if (e.length < 2) return ""; let t = ""; const n = DM.AU / 2,
                            r = e[0],
                            a = e[e.length - 1],
                            o = r.width,
                            i = a.width,
                            l = r.y - n; return t += "M ".concat(r.x + o / 2 + wT, " ").concat(l, "\n    H ").concat(a.x + i / 2 - wT), e.forEach(((n, r) => { const a = n.x + n.width / 2,
                                o = n.y,
                                i = r < e.length / 2 ? bT.Reverse : bT.Forward,
                                s = "M ".concat(a, " ").concat(o, "\n      V ").concat(l + wT, "\n      Q ").concat(a, " ").concat(l, ", ").concat(a - wT * i, " ").concat(l);
                            t += " ".concat(s, " ") })), t }(e); return (0, we.jsx)("path", { d: t, stroke: DM.$6, fill: "none" }, "root-path") }), [f, p]), F = (() => { const e = []; return (e => { const t = [],
                        n = {},
                        r = {}; for (const a of g) ! function e(a) { if (n[a.id]) return;
                        n[a.id] = !0; const o = d[a.id],
                            l = (null === o || void 0 === o ? void 0 : o.children) || []; for (const n of l) { var s, c; const o = f[n.id]; if (!o) continue; const l = !!O[n.id],
                                d = (null === i || void 0 === i || null === (s = i.childRelation) || void 0 === s ? void 0 : s.id) === (null === n || void 0 === n ? void 0 : n.id) && (null === i || void 0 === i || null === (c = i.parent) || void 0 === c ? void 0 : c.id) === a.id,
                                m = "".concat(a.id, "-").concat(n.id),
                                p = h[m],
                                v = o.x + p,
                                g = o.y - 30,
                                y = !h[m],
                                b = u[m];
                            b && (y && !r[n.id] && (r[n.id] = !0, t.push((0, we.jsxs)("g", { onClick: () => V(n), style: { cursor: "pointer" }, transform: "translate(".concat(o.x + o.width - 16, ", ").concat(o.y - 30 - 11, ")"), children: [(0, we.jsx)("rect", { width: 32, height: 20, fill: l || d ? DM.iW.invertFill : DM.jb.fill, rx: 10, ry: 10, stroke: l || d ? DM.iW.invertStroke : DM.jb.stroke }, "stake-rect-".concat(o.id)), (0, we.jsx)("text", { dx: 16, dy: 10, fill: l || d ? DM.iW.invertStroke : DM.jb.stroke, fontSize: "14", fontWeight: "bold", textAnchor: "middle", dominantBaseline: "middle", children: "..." }, "stake-".concat(o.id, "-more"))] }))), (null === o || void 0 === o ? void 0 : o.nodeType) === vC.Z6.Connection || y || (t.push((0, we.jsxs)("g", { onClick: e => j(e, a, n), style: { cursor: "pointer" }, children: [(0, we.jsx)("rect", { x: v - 25, y: g - 11, width: 50, height: 20, fill: l || d ? DM.iW.invertFill : DM.jb.fill, rx: 10, ry: 10, stroke: l || d ? DM.iW.invertStroke : DM.jb.stroke }, "stake-rect-".concat(o.id)), (0, we.jsxs)("text", { x: v, y: g, fill: l || d ? DM.iW.invertStroke : DM.jb.stroke, fontSize: "12", textAnchor: "middle", dominantBaseline: "middle", children: [b.toFixed(2), "%"] }, "stake-".concat(o.id))] })), e(n))) } }(a);
                    e.push(...t) })(e), e })(); return (0, we.jsx)(fM.A, { height: "100%", overflow: "auto", ref: w, style: { cursor: "grab" }, children: (0, we.jsx)(fM.A, { overflow: "hidden", width: A, height: k * m, children: (0, we.jsxs)("svg", { style: { transform: "translate(".concat(z, "px, 0) scale(").concat(m, ") "), transformOrigin: "0 0" }, width: "".concat(x / m, "px"), height: "".concat(k, "px"), children: [(0, we.jsxs)("defs", { children: [(0, we.jsx)("marker", { id: "arrow-down-active", viewBox: "0 0 20 20", refX: "12", refY: "10", markerWidth: DM.iW.arrowWidth, markerHeight: DM.iW.arrowHeight, markerUnits: "userSpaceOnUse", orient: "auto", children: (0, we.jsx)("polygon", { points: "0,0 20,10 0,20", fill: DM.iW.invertFill }) }), (0, we.jsx)("marker", { id: "arrow-down", viewBox: "0 0 20 20", refX: "12", refY: "10", markerWidth: DM.jb.arrowWidth, markerHeight: DM.jb.arrowHeight, markerUnits: "userSpaceOnUse", orient: "auto", children: (0, we.jsx)("polygon", { points: "0,0 20,10 0,20", fill: DM.jb.invertFill }) })] }), i && (0, we.jsx)(yT, { ...i, onClose: j }), D, R, P, F] }) }) }) }; var MT = n(50044),
            ET = n(63336),
            CT = n(35721),
            TT = n(37918),
            HT = n(61475),
            LT = n(55013),
            IT = n(95849),
            jT = n(51347),
            VT = n(95434); const OT = ["alignItems", "autoFocus", "component", "children", "dense", "disableGutters", "divider", "focusVisibleClassName", "selected", "className"],
            RT = (0, LM.Ay)($M.A, { shouldForwardProp: e => (0, HT.A)(e) || "classes" === e, name: "MuiListItemButton", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, n.dense && t.dense, "flex-start" === n.alignItems && t.alignItemsFlexStart, n.divider && t.divider, !n.disableGutters && t.gutters] } })((e => { let { theme: t, ownerState: n } = e; return (0, l.default)({ display: "flex", flexGrow: 1, justifyContent: "flex-start", alignItems: "center", position: "relative", textDecoration: "none", minWidth: 0, boxSizing: "border-box", textAlign: "left", paddingTop: 8, paddingBottom: 8, transition: t.transitions.create("background-color", { duration: t.transitions.duration.shortest }), "&:hover": { textDecoration: "none", backgroundColor: (t.vars || t).palette.action.hover, "@media (hover: none)": { backgroundColor: "transparent" } }, ["&.".concat(VT.A.selected)]: { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.primary.mainChannel, " / ").concat(t.vars.palette.action.selectedOpacity, ")") : (0, VC.X4)(t.palette.primary.main, t.palette.action.selectedOpacity), ["&.".concat(VT.A.focusVisible)]: { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.primary.mainChannel, " / calc(").concat(t.vars.palette.action.selectedOpacity, " + ").concat(t.vars.palette.action.focusOpacity, "))") : (0, VC.X4)(t.palette.primary.main, t.palette.action.selectedOpacity + t.palette.action.focusOpacity) } }, ["&.".concat(VT.A.selected, ":hover")]: { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.primary.mainChannel, " / calc(").concat(t.vars.palette.action.selectedOpacity, " + ").concat(t.vars.palette.action.hoverOpacity, "))") : (0, VC.X4)(t.palette.primary.main, t.palette.action.selectedOpacity + t.palette.action.hoverOpacity), "@media (hover: none)": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.primary.mainChannel, " / ").concat(t.vars.palette.action.selectedOpacity, ")") : (0, VC.X4)(t.palette.primary.main, t.palette.action.selectedOpacity) } }, ["&.".concat(VT.A.focusVisible)]: { backgroundColor: (t.vars || t).palette.action.focus }, ["&.".concat(VT.A.disabled)]: { opacity: (t.vars || t).palette.action.disabledOpacity } }, n.divider && { borderBottom: "1px solid ".concat((t.vars || t).palette.divider), backgroundClip: "padding-box" }, "flex-start" === n.alignItems && { alignItems: "flex-start" }, !n.disableGutters && { paddingLeft: 16, paddingRight: 16 }, n.dense && { paddingTop: 4, paddingBottom: 4 }) })),
            PT = a.forwardRef((function(e, t) { const n = (0, eE.A)({ props: e, name: "MuiListItemButton" }),
                    { alignItems: r = "center", autoFocus: o = !1, component: i = "div", children: s, dense: c = !1, disableGutters: d = !1, divider: u = !1, focusVisibleClassName: h, selected: m = !1, className: p } = n,
                    f = (0, ZM.default)(n, OT),
                    v = a.useContext(jT.A),
                    g = a.useMemo((() => ({ dense: c || v.dense || !1, alignItems: r, disableGutters: d })), [r, v.dense, c, d]),
                    y = a.useRef(null);
                (0, LT.A)((() => { o && y.current && y.current.focus() }), [o]); const b = (0, l.default)({}, n, { alignItems: r, dense: g.dense, disableGutters: d, divider: u, selected: m }),
                    w = (e => { const { alignItems: t, classes: n, dense: r, disabled: a, disableGutters: o, divider: i, selected: s } = e, c = { root: ["root", r && "dense", !o && "gutters", i && "divider", a && "disabled", "flex-start" === t && "alignItemsFlexStart", s && "selected"] }, d = (0, YM.A)(c, VT.Y, n); return (0, l.default)({}, n, d) })(b),
                    z = (0, IT.A)(y, t); return (0, we.jsx)(jT.A.Provider, { value: g, children: (0, we.jsx)(RT, (0, l.default)({ ref: z, href: f.href || f.to, component: (f.href || f.to) && "div" === i ? "button" : i, focusVisibleClassName: (0, XM.A)(w.focusVisible, h), ownerState: b, className: (0, XM.A)(w.root, p) }, f, { classes: w, children: s })) }) })),
            DT = PT; var FT = n(26353); var NT = n(40245),
            _T = function(e) { return e.CHART = "chart", e.TABLE = "table", e.BUILDER = "builder", e }(_T || {}); const BT = e => { let { label: t, iconName: n, handleClick: r } = e; return (0, we.jsx)(SM.A, { variant: "text", onClick: r, startIcon: (0, we.jsx)(Ee.me, { name: n, variant: "sharp" }), children: (0, we.jsx)(ln.A, { fontSize: 15, color: wM.Qs.Neutrals[600], variant: "bodyMD", children: t }) }) },
            WT = e => { let { secView: t } = e; const n = (0, $.useHistory)(),
                    r = (0, ae.wA)(),
                    { handleSearch: o, filteredOrgs: i, resetSearch: l, query: s } = (() => { const e = (0, ae.d4)(kM.U3),
                            [t, n] = (0, a.useState)(""); return { handleSearch: e => { n(e.target.value) }, filteredOrgs: t.trim().length > 0 && e.filter((e => { var n; return null === (n = e.name) || void 0 === n ? void 0 : n.toLowerCase().includes(t.toLowerCase()) })) || [], query: t, resetSearch: () => n("") } })(),
                    { params: { orgId: c, cik: d, view: u } } = (0, re.u)([(0, ne.wi)(), (0, ne.dH)()]),
                    h = (0, ae.d4)((0, kM.hm)(c, !!t)),
                    m = (0, ae.d4)((0, kM.a2)(c)),
                    p = e => r => { var a, o;
                        r.preventDefault(), r.stopPropagation(), ve.A.trackEvent({ eventName: "OWNERSHIP_CHART_CHANGE_VIEW_CLICKED", extraParams: { view: e, cik: null === h || void 0 === h ? void 0 : h.cik, orgName: null === h || void 0 === h ? void 0 : h.name, owners: null === m || void 0 === m || null === (a = m.owners) || void 0 === a ? void 0 : a.length, subsidiaries: null === m || void 0 === m || null === (o = m.subsidiaries) || void 0 === o ? void 0 : o.length } }), t ? n.push((0, ne.dH)({ cik: d, view: e })) : n.push((0, ne.wi)({ orgId: c, base: "protected", view: e })) }; return (0, we.jsx)(fM.A, { bgcolor: wM.Qs.Neutrals[0], p: 1, borderBottom: "solid 1px #e0e0e0", children: (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [(0, we.jsxs)(fM.A, { children: [(0, we.jsx)(XE.A, { onClick: () => { n.push("/dashboard/organizations") }, children: (0, we.jsx)(Ee.me, { name: "angle-left", variant: "light" }) }), (0, we.jsx)(BT, { label: "Diagram", iconName: "sitemap", handleClick: p("chart") }), (0, we.jsx)(BT, { label: "Table", iconName: "table", handleClick: p("table") }), !t && (0, we.jsx)(BT, { label: "Builder", iconName: "diagram-successor", handleClick: p("builder") })] }), (0, we.jsxs)(fM.A, { display: "flex", gap: 2, alignItems: "center", children: [(0, we.jsxs)(fM.A, { width: 275, position: "relative", children: [(0, we.jsx)(VM.A, { value: s, onChange: o, fullWidth: !0, variant: "outlined", size: "medium", placeholder: "Search Organizations" }), i.length > 0 && (0, we.jsx)(MT.x, { onClickAway: l, children: (0, we.jsx)(ET.A, { elevation: 8, sx: { zIndex: 1, position: "absolute", top: "100%", left: 0, width: "100%", maxWidth: 300, maxHeight: 250, overflowY: "auto", bgcolor: "white", boxShadow: 3 }, children: (0, we.jsx)(CT.A, { children: i.map((e => (0, we.jsx)(TT.Ay, { disablePadding: !0, dense: !0, children: (0, we.jsx)(DT, { dense: !0, onClick: t => ((e, t) => { e.preventDefault(), e.stopPropagation(), t && n.push((0, ne.wi)({ orgId: t, view: u, base: "protected" })) })(t, e.id), children: (0, we.jsx)(FT.A, { primary: e.name, secondary: e.entityType || e.category || e.jurisdiction || e.createdAt }) }) }, e.id))) }) }) })] }), (0, we.jsxs)(fM.A, { display: "flex", gap: 1, alignItems: "center", children: [u === _T.BUILDER && (0, we.jsx)(XE.A, { size: "medium", color: "default", onClick: e => { e.preventDefault(), e.stopPropagation(), r((0, NT.ke)()) }, children: (0, we.jsx)(Ee.me, { name: "magnifying-glass-minus", variant: "light" }) }), u === _T.BUILDER && (0, we.jsx)(XE.A, { size: "medium", color: "default", onClick: e => { e.preventDefault(), e.stopPropagation(), r((0, NT.nF)()) }, children: (0, we.jsx)(Ee.me, { name: "magnifying-glass-plus", variant: "light" }) })] })] })] }) }) },
            UT = e => { let { secView: t } = e; const n = (0, ae.wA)(),
                    { params: { view: r, orgId: o } } = (0, re.u)([(0, ne.wi)(), (0, ne.dH)()]),
                    i = (0, $.useHistory)(),
                    l = (0, ae.d4)((0, kM.hm)(o, !!t)),
                    s = (0, ae.d4)(kM.U3),
                    c = (0, ae.d4)((0, kM.a2)(o)),
                    { isOwnershipEnabled: d } = (0, EM.A)(),
                    { getAllOrgs: u, loading: h, importSECOwnership: m, getAllRelations: p } = (0, rC.A)(),
                    [f, v] = (0, he.A)((async () => { await n(nn.no.get({ orgId: o })) })),
                    [g, y] = (0, he.A)((async () => { const e = [p];
                        s.length || e.push(u), await Promise.all(e.map((e => e()))) })); return (0, a.useEffect)((() => { d || i.push("/dashboard") }), [d]), (0, a.useEffect)((() => { var e, t;
                    ve.A.trackEvent({ eventName: "OWNERSHIP_CHART_LOADED", extraParams: { org: null === l || void 0 === l ? void 0 : l.name, owners: (null === c || void 0 === c || null === (e = c.owners) || void 0 === e ? void 0 : e.length) || 0, subsidiaries: (null === c || void 0 === c || null === (t = c.subsidiaries) || void 0 === t ? void 0 : t.length) || 0 } }) }), []), (0, a.useEffect)((() => { t ? null !== l && void 0 !== l && l.cik || i.push("/dashboard/organizations") : y() }), []), (0, a.useEffect)((() => (v(), () => {})), [o]), (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", bgcolor: wM.Qs.Neutrals[200], height: "100%", position: "relative", children: [(0, we.jsx)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", children: (0, we.jsx)(fM.A, { display: "flex", gap: 3, alignItems: "center", children: l && t && (0, we.jsxs)(SM.A, { variant: "outlined", color: "secondary", onClick: () => { var e, t;
                                        ve.A.trackEvent({ eventName: "OWNERSHIP_CHART_SAVE_CHART_CLICKED", extraParams: { cik: null === l || void 0 === l ? void 0 : l.cik, orgName: null === l || void 0 === l ? void 0 : l.name, owners: null === c || void 0 === c || null === (e = c.owners) || void 0 === e ? void 0 : e.length, subsidiaries: null === c || void 0 === c || null === (t = c.subsidiaries) || void 0 === t ? void 0 : t.length } }), m(l) }, size: "small", children: ["Save Chart", h && (0, we.jsx)(rn.A, { color: "secondary", size: 10 })] }) }) }), (0, we.jsx)(dn.A, { loading: g || f, spinnerType: "block", spinnerSize: 20, transparent: !0, children: (0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", alignItems: "flex-start", justifyContent: "center", flexShrink: 0, overflow: "hidden", height: "100%", children: [(0, we.jsx)(fM.A, { width: "100%", children: (0, we.jsx)(WT, { secView: t }) }), (0, we.jsxs)(fM.A, { display: "flex", flex: 1, overflow: "auto", width: "100%", id: "ownershipContentContainer", children: ["chart" === r && (0, we.jsx)(fC.A, { secView: t }), "table" === r && (0, we.jsx)(sT, { secView: t }), "builder" === r && (0, we.jsx)(ST, {})] })] }) })] }) }) }; var qT = n(30752),
            GT = n(29794); const KT = a.memo((e => { let { children: t } = e; return (0, we.jsx)(QS.A, { children: t }) })),
            ZT = e => { let { children: t, hideBg: n } = e; return (0, we.jsx)(se.A, { overflow: "auto", position: "absolute", top: 16, bottom: 16, left: 16, right: 16, bgcolor: n ? void 0 : "#ffffff", border: n ? void 0 : "solid 1px #cccccc", display: "flex", flexDirection: "column", children: t }) },
            YT = () => { var e; const { orgId: t } = (0, $.useParams)(), n = (0, Kt.A)(), r = (0, Q.A)(), a = "?".concat(null === (e = new URLSearchParams(r)) || void 0 === e ? void 0 : e.toString()) || 0; return (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { path: (0, ne.N$)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(AM, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.Oq)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(CS, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.ts)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(GS, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.q5)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(tn, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.OW)(), children: (0, we.jsx)(ZT, { hideBg: !0, children: (0, we.jsx)(bS, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.Io)(), children: (0, we.jsx)(ZT, { hideBg: !0, children: (0, we.jsx)(Or, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.K7)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(sr, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.BC)(), children: (0, we.jsx)(ZT, { children: (0, we.jsx)(sr, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.YQ)(), children: (0, we.jsx)(ZT, { hideBg: !0, children: (0, we.jsx)(pC, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.wi)(), children: (0, we.jsx)(se.A, { overflow: "auto", position: "absolute", top: 0, bottom: 0, left: 0, right: 0, display: "flex", flexDirection: "column", bgcolor: wM.Qs.Neutrals[200], children: (0, we.jsx)(UT, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.dH)(), children: (0, we.jsx)(se.A, { overflow: "auto", position: "absolute", top: 0, bottom: 0, left: 0, right: 0, display: "flex", flexDirection: "column", bgcolor: wM.Qs.Neutrals[200], children: (0, we.jsx)(UT, { secView: !0 }) }) }), (0, we.jsx)($.Redirect, { to: null !== n && void 0 !== n && n.id ? (0, ne.r2)({ orgId: null === n || void 0 === n ? void 0 : n.id }) + a : t ? (0, ne.r2)({ orgId: t }) + a : (0, ne.ze)() + a })] }) },
            XT = () => { var e, t, n; const { appHeaderHeight: r } = (0, GT.A)(), o = (0, $.useHistory)(), [i, l] = (0, a.useState)(!0), s = (0, ae.d4)(Lt.t0), c = (0, ae.d4)(qT.A), d = null === c || void 0 === c || null === (e = c.data) || void 0 === e || null === (t = e.detailsPane) || void 0 === t || null === (n = t.layout) || void 0 === n ? void 0 : n.name, u = s && d && "layout-3" !== d && "layout-4" !== d ? Wt.Ay.dialogs.profileCardWidth : 0, { open: h } = (0, Ft.A)("userCard"), { toggleDialog: m } = (0, Ft.A)("whatsNewDialog"), p = (0, Q.A)(), f = "true" === (null === p || void 0 === p ? void 0 : p.whatsnew), v = (0, ae.wA)(); return (0, a.useEffect)((() => (async function() { await v(xS.Ah.getAvailableIntegrations()), l(!1) }(), () => { v((0, $S.Ch)()) })), []), (0, a.useEffect)((() => { f && (m(), o.replace({ search: "" })) }), [f]), (0, we.jsx)(KT, { children: (0, we.jsxs)(HS.A, { container: !0, direction: "column", wrap: "nowrap", children: [(0, we.jsx)(TS.A, { item: !0, direction: "vers", height: r, children: (0, we.jsx)("div", { style: { height: 65 } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: !0, children: (0, we.jsxs)(HS.A, { container: !0, wrap: "nowrap", children: [(0, we.jsx)(TS.A, { item: !0, width: Wt.Ay.dashboard.navWidth, children: (0, we.jsx)(Qt, {}) }), (0, we.jsx)(Tt.A, { item: !0, xs: !0, children: (0, we.jsx)(HS.A, { container: !0, direction: "column", wrap: "nowrap", children: (0, we.jsx)(Tt.A, { item: !0, xs: !0, children: (0, we.jsxs)(HS.A, { container: !0, wrap: "nowrap", children: [(0, we.jsx)(se.A, { display: "flex", bgcolor: "#eeeeee", position: "relative", height: "100%", width: "100%", children: (0, we.jsx)(dn.A, { loading: i, children: (0, we.jsx)(se.A, { flex: 1, children: (0, we.jsx)(YT, {}) }) }) }), s && (0, we.jsx)(TS.A, { item: !0, width: u, children: (0, we.jsx)(Jt.A, {}) }), h && (0, we.jsx)(TS.A, { item: !0, width: Wt.Ay.dialogs.userCardWidth, children: (0, we.jsx)(JS.A, {}) })] }) }) }) })] }) })] }) }) }; var $T = n(16567); const QT = () => { const e = (0, ae.wA)(),
                { orgId: t, chartId: n } = (0, $.useParams)(),
                r = async (r, a) => await e($T.yD.communityBuildSearch({ orgId: t, chartId: n, email: r, token: a })); return { getRolesByEmail: async (e, t) => { if (t) { const { error: n, payload: a } = await r(t, e); return n ? [] : a.roleList.map((e => ({ role: (0, Cn.SB)(e.role), member: (0, Cn.SB)(e.member) }))) } } } }; var JT = n(56070),
            eH = n(80539); const tH = e => { let { person: t, role: n, defaultFieldsMap: r } = e; const a = t[r.firstName.id],
                    o = t[r.lastName.id],
                    i = t[r.email.id],
                    l = t.photo,
                    s = n[r.roleName.id]; let c = Boolean(a) || Boolean(o) ? "".concat(a, " ").concat(o) : i; return (0, we.jsxs)(Tt.A, { container: !0, alignContent: "center", alignItems: "center", children: [(0, we.jsx)(se.A, { mr: 2, children: (0, we.jsx)(eH.A, { width: 30, height: 30, src: l, name: c, overrideColor: null === t || void 0 === t ? void 0 : t.memberPhotoColor }) }), (0, we.jsxs)(Tt.A, { item: !0, xs: !0, children: [(0, we.jsx)(je.A, { display: "inline", children: "".concat(a, " ").concat(o, " ") }), Boolean(i) && (0, we.jsx)(je.A, { children: i }), s && (0, we.jsx)(je.A, { children: s })] })] }) },
            nH = e => { let { singleUseToken: t, showNewBoss: n, setShowNewBoss: r, supervisorRefersTo: o, defaultFieldsMap: i } = e; const { props: { members: l = [] } } = (0, Ft.A)("newRole"), s = (0, a.useRef)(null), { getRolesByEmail: c } = QT(), { register: d, control: u, setValue: h, trigger: m } = (0, oe.xW)(), [p, f] = (0, a.useState)(""), [v, g] = (0, a.useState)(l), [y, b] = (0, a.useState)(""), w = e => { const { name: t, value: n } = e.target;
                    h("".concat(t), n) }; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { mb: 1, mt: 3, children: (0, we.jsxs)("label", { children: ["Who is your ", o || "Boss", "? (enter email)"] }) }), (0, we.jsx)(Tt.A, { container: !0, justifyContent: "flex-start", children: (0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(se.A, { children: (0, we.jsx)(JT.A, { onChange: async e => { if (f(e.target.value), h("bossEmail", e.target.value), e.target.value && (0, Tn.B9)(e.target.value)) { let s = await c(t, e.target.value); var n, a, o, l; if (s.length > 0) g(s), m(), d("bossEmail", s[0].member[i.email.id]), b(null === (n = s[0].role) || void 0 === n ? void 0 : n.id), h("bossEmail", s[0].member[i.email.id]), h("bossFirstName", s[0].member[i.firstName.id]), h("bossLastName", s[0].member[i.lastName.id]), h("bossPhoto", null === (a = s[0].member) || void 0 === a ? void 0 : a.photo), h("bossJobTitle", s[0].role[i.roleName.id]), h("memberId", null === (o = s[0].member) || void 0 === o ? void 0 : o.id), h("roleId", null === (l = s[0].role) || void 0 === l ? void 0 : l.id), h("existingManagerRole", s[0]), r(!1);
                                            else r(!0), h("bossFirstName", ""), h("bossLastName", ""), h("bossPhoto", ""), h("bossJobTitle", ""), h("memberId", ""), h("roleId", ""), h("existingManagerRole", null) } }, inputRef: s, handleClear: () => { f(""), s.current.value = "", r(!1) }, query: p, label: o ? "Search ".concat(o) : "Search Boss", placeholder: o ? "Search ".concat(o, " by email") : "Search Boss by email", style: { width: "100%" } }) }) }) }), n ? (0, we.jsxs)(Tt.A, { container: !0, justifyContent: "center", spacing: 1, children: [(0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "bossFirstName", control: u, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { fullWidth: "true", required: "true", value: n, name: t, label: o ? "".concat(o, " First Name") : "Boss First Name", variant: "outlined", onChange: e => { w(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "bossLastName", control: u, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { fullWidth: "true", required: "true", value: n, name: t, label: o ? "".concat(o, " Last Name") : "Boss Last Name", variant: "outlined", onChange: e => { w(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "bossEmail", control: u, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { fullWidth: "true", required: "true", value: n, name: t, label: o ? "".concat(o, " Email") : "Boss Email", variant: "outlined", onChange: e => { w(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "bossJobTitle", control: u, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { fullWidth: "true", required: "true", value: n, name: t, label: o ? "".concat(o, " Job Title/Role") : "Boss Job Title/Role", variant: "outlined", onChange: e => { w(e) } }) } }) })] }) : (0, we.jsx)(Tt.A, { container: !0, spacing: 2, children: v && v.length > 0 && v.map((e => (0, we.jsx)(Tt.A, { item: !0, md: 4, children: (0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", children: [(0, we.jsx)(bn.A, { checked: e.role.id === y && !0, onChange: () => (e => { var t, n, r, a, o, i, l, s;
                                        b(null === (t = e.role) || void 0 === t ? void 0 : t.id), h("bossEmail", null === (n = e.member) || void 0 === n ? void 0 : n.email), h("bossFirstName", null === (r = e.member) || void 0 === r ? void 0 : r.firstName), h("bossLastName", null === (a = e.member) || void 0 === a ? void 0 : a.lastName), h("bossPhoto", null === (o = e.member) || void 0 === o ? void 0 : o.photo), h("bossJobTitle", null === (i = e.role) || void 0 === i ? void 0 : i.name), h("memberId", null === (l = e.member) || void 0 === l ? void 0 : l.id), h("roleId", null === (s = e.role) || void 0 === s ? void 0 : s.id) })(e) }), (0, we.jsx)(tH, { person: e.member, role: e.role, defaultFieldsMap: i })] }) }, e.role.id))) })] }) }; var rH = n(42197),
            aH = n(96382),
            oH = n(89656); const iH = 90,
            lH = e => { let { size: t = iH, person: n, showEdit: r = !1, handleChangePhotoClick: a } = e; const o = (0, ae.d4)(oH.YY); let i, l, s; if (n) { const e = n.name || (n.firstName ? "".concat(n.firstName || "", " ").concat(n.lastName || "") : "");
                    i = (0, we.jsx)(eH.A, { width: t, height: t, src: o, name: e, overrideColor: n.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") }, onError: e => { e.target.setAttribute("processed", "true"), e.target.setAttribute("href", aH.A) } } }), l = (0, we.jsx)(eH.A, { width: t, height: t, src: n.photo || o, name: e, overrideColor: n.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.target.setAttribute("processed", "true") } }, children: i }) } return n && r && "function" === typeof a && (s = (0, we.jsx)(eH.u, { position: "br", handleClick: a })), (0, we.jsxs)(se.A, { children: [l, s] }) },
            sH = e => { let { org: t, chartId: n, singleUseToken: r } = e; const { setValue: a, watch: o } = (0, oe.xW)(), { openDialog: i } = (0, Ft.A)("communityBuildPhoto"), l = o("photo"), s = o("memberPhotoColor"), c = o("firstName"), d = o("lastName"), u = e => { a("photo", e.photo), a("memberPhotoColor", e.memberPhotoColor) }; return (0, we.jsx)(lH, { handleChangePhotoClick: () => { return e = "upload", void i({ member: "".concat(c, " ").concat(d), handleChangePhoto: u, type: "photo", mode: e, organization: t.name, initialAvatarBackgroundColor: s, photo: l, orgId: t.id, chartId: n, singleUseToken: r }); var e }, person: { name: "".concat(c || "", " ").concat(d || ""), photo: l, memberPhotoColor: s }, size: 90, handle: !0, showEdit: !0 }) }; var cH; const dH = ee.Ay.div(cH || (cH = (0, J.A)(["\n  padding: 22px 10px;\n  margin: 0 auto;\n"]))),
            uH = e => { let { org: t, chartId: n, singleUseToken: r, userFound: a, setUserFound: o, defaultFieldsMap: i } = e; const { getRolesByEmail: l } = QT(), { getValues: s, control: c, setValue: d, errors: u, setError: h, clearErrors: m, trigger: p, register: f } = (0, oe.xW)(), v = e => { const { name: t, value: n } = e.target; if (d("".concat(t), n), "phoneNumber" === t) {!ge.A.MemberFieldValidators.phone || ge.A.MemberFieldValidators.phone(n) ? m("phone") : h("phone", { message: ge.A.ERROR_MESSAGES.phone, shouldFocus: !0 }) } }; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(se.A, { display: "flex", position: "relative", gridGap: 16, alignItems: "center", children: [(0, we.jsx)(se.A, { flexGrow: 1, children: (0, we.jsxs)(se.A, { flex: 1, display: "flex", children: [(0, we.jsx)(se.A, { flexGrow: 1, children: (0, we.jsx)(oe.xI, { name: "email", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { required: !0, value: n, name: t, fullWidth: "true", disabled: s("disableEmail"), label: "Email Address", variant: "outlined", onChange: e => {
                                                    (async e => { if (d("email", e.target.value), o(!1), e.target.value && (0, Tn.B9)(e.target.value)) { let f = await l(r, e.target.value); var t, n, a, s, c, u, h, m;
                                                            f.length > 0 ? (o(!0), p(), d("firstName", null === (t = f[0]) || void 0 === t ? void 0 : t.member[i.firstName.id]), d("lastName", null === (n = f[0]) || void 0 === n ? void 0 : n.member[i.lastName.id]), d("jobTitle", null === (a = f[0]) || void 0 === a ? void 0 : a.role[i.roleName.id]), d("jobDescription", null === (s = f[0]) || void 0 === s ? void 0 : s.role[i.roleDescription.id]), d("biography", null === (c = f[0]) || void 0 === c ? void 0 : c.member[i.description.id]), d("phoneNumber", null === (u = f[0]) || void 0 === u ? void 0 : u.member[i.phone.id]), d("photo", null === (h = f[0]) || void 0 === h || null === (m = h.member) || void 0 === m ? void 0 : m.photo), d("existingPersonalRole", f[0])) : (d("firstName", ""), d("lastName", ""), d("jobTitle", ""), d("jobDescription", ""), d("biography", ""), d("phoneNumber", ""), d("photo", ""), d("existingPersonalRole", null), o(!1)) } })(e) } }) } }) }), (0, we.jsx)(se.A, { children: s("email") && (0, we.jsxs)(dH, { children: [a && (0, we.jsx)(wn.Ay, { placement: "top", title: "User exists", arrow: !0, children: (0, we.jsx)("div", { children: (0, we.jsx)(Ee.Ay, { icon: "CheckCircle", size: "lg", color: rH.A.palette.success.main }) }, "userFound") }), !a && (0, we.jsx)(wn.Ay, { placement: "top", title: "User not found, enter the information below", arrow: !0, children: (0, we.jsx)("div", { children: (0, we.jsx)(Ee.Ay, { icon: "ExclamationCircle", size: "lg", color: rH.A.palette.warning.light }) }, "userNotFound") })] }) })] }) }), (0, we.jsx)(se.A, { children: (0, we.jsx)(sH, { org: t, chartId: n, singleUseToken: r }) })] }), (0, we.jsx)("br", {}), (0, we.jsxs)(Tt.A, { container: !0, justifyContent: "space-between", spacing: 1, children: [(0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "firstName", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { required: !0, value: n, name: t, fullWidth: "true", label: "First Name", variant: "outlined", onChange: e => { v(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "lastName", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { required: !0, value: n, name: t, fullWidth: "true", label: "Last Name", variant: "outlined", onChange: e => { v(e) } }) } }) })] }), (0, we.jsxs)(Tt.A, { direction: "column", justifyContent: "center", rowSpacing: 4, children: [(0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "phoneNumber", control: c, render: e => { var t; let { name: n, value: r } = e; return (0, we.jsx)(Ve.A, { value: r, name: n, fullWidth: "true", label: "Phone Number(Optional)", variant: "outlined", inputRef: f({ name: "phone" }), error: !(null === u || void 0 === u || !u.phone), helperText: null === u || void 0 === u || null === (t = u.phone) || void 0 === t ? void 0 : t.message, onChange: e => { v(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 12, children: (0, we.jsx)(oe.xI, { name: "biography", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { value: n, name: t, multiline: !0, fullWidth: "true", maxRows: 4, label: "Short Biography(Optional)", variant: "outlined", onChange: e => { v(e) } }) } }) })] }), (0, we.jsx)("hr", {}), (0, we.jsxs)(Tt.A, { direction: "column", justifyContent: "center", rowSpacing: 4, children: [(0, we.jsx)(Tt.A, { item: !0, xs: 6, children: (0, we.jsx)(oe.xI, { name: "jobTitle", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { value: n, name: t, fullWidth: "true", required: "true", label: "Primary Job Title/Role", variant: "outlined", onChange: e => { v(e) } }) } }) }), (0, we.jsx)(Tt.A, { item: !0, xs: 12, children: (0, we.jsx)(oe.xI, { name: "jobDescription", control: c, render: e => { let { name: t, value: n } = e; return (0, we.jsx)(Ve.A, { value: n, name: t, fullWidth: "true", multiline: !0, maxRows: 4, label: "Primary Job Description(Optional)", variant: "outlined", onChange: e => { v(e) } }) } }) })] })] }) }; var hH, mH;

        function pH() { return pH = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, pH.apply(this, arguments) }

        function fH(e, t) { let { title: n, titleId: r, ...o } = e; return a.createElement("svg", pH({ xmlns: "http://www.w3.org/2000/svg", xmlSpace: "preserve", viewBox: "0 0 150 50", ref: t, "aria-labelledby": r }, o), n ? a.createElement("title", { id: r }, n) : null, hH || (hH = a.createElement("path", { stroke: "#000", strokeMiterlimit: 10, strokeWidth: 6, d: "M1.266 24.704H125.3" })), mH || (mH = a.createElement("path", { d: "M124.4 6.284v38.322l23.95-20.916z" }))) } const vH = a.forwardRef(fH),
            gH = (n.p, e => { let { createdRole: t, returnToLandingPage: n, defaultFieldsMap: r } = e; const { communityBuild: a, manager: o, personal: i } = t; return (0, we.jsxs)(se.A, { m: 5, alignItems: "center", children: [(0, we.jsx)(se.A, { mb: 2, children: (0, we.jsx)(je.A, { variant: "h4", weight: "medium", children: "Chart Position Added" }) }), (0, we.jsx)(se.A, { mb: 5, children: (0, we.jsx)(je.A, { variant: "body2", children: "Thank you for adding yourself to this Community Build Chart - you have added:" }) }), (0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", mb: 5, children: [(0, we.jsx)(tH, { person: i.member, role: i.role, defaultFieldsMap: r }), (0, we.jsxs)(se.A, { m: 2, width: 230, alignContent: "center", children: [a.supervisorRefersTo, (0, we.jsx)(vH, {})] }), (0, we.jsx)(tH, { person: o.member, role: o.role, defaultFieldsMap: r })] }), a.adminDetails && (0, we.jsx)(se.A, { mb: 5, children: (0, we.jsxs)(je.A, { variant: "body2", children: ["This chart was created and maintained by ", a.adminDetails.name, ",", " ", a.adminDetails.email, ", please contact them if you have any questions or comments."] }) }), a.publicLink && (0, we.jsx)(se.A, { mb: 5, children: (0, we.jsxs)(je.A, { variant: "body2", children: ["This chart is available for viewing the following URL, if you would like to access the chart as it comes together:", (0, we.jsxs)("a", { href: a.publicLink, target: "_blank", children: [" ", "Open Public Link"] })] }) }), (0, we.jsx)(se.A, { mb: 3, children: (0, we.jsx)($e.A, { variant: "contained", color: "primary", onClick: n, children: "CLOSE" }) })] }) }); var yH, bH = n(30208),
            wH = n(7743); const zH = ee.Ay.div(yH || (yH = (0, J.A)(["\n  width: 55%;\n  padding-top: 30px;\n  background: #ffffff;\n  box-shadow: 0 0 6px 0px #888;\n  background-color: rgba(255, 255, 255, 0.9);\n  min-height: 630px;\n  text-align: left;\n"]))),
            xH = e => { var t; let { org: n, chartId: r, singleUseToken: o, setToken: i, communityBuild: l } = e; const s = (0, ae.wA)(),
                    c = (0, $.useHistory)(),
                    { show: d } = (0, Un.A)(),
                    { getRolesByEmail: u } = QT(),
                    h = (0, ae.d4)(wH.gJ),
                    m = (0, oe.mN)({ defaultValues: { email: "", photo: "", memberPhotoColor: "#B82020", firstName: "", lastName: "", phoneNumber: "", biography: "", jobTitle: "", jobDescription: "", disableEmail: !1, existingPersonalRole: null, bossFirstName: "", bossLastName: "", bossPhoto: "", bossEmail: "", bossJobTitle: "", memberId: "", roleId: "", existingManagerRole: null }, shouldUnregister: !1 }),
                    { handleSubmit: p, setValue: f } = m,
                    [v, g] = (0, a.useState)(!1),
                    [y, b] = (0, a.useState)(!1),
                    [w, z] = (0, a.useState)(!1),
                    [x, A] = (0, a.useState)(null),
                    k = (0, ae.d4)(bH.m);
                (0, a.useEffect)((() => {!async function() { try { const { error: w, payload: x } = await s($T.yD.getCommunityBuildAuth({ ignoreErrorHandler: !0, orgId: n.id, chartId: r, token: o })); if (w) { z(!1); let e = new URL(window.location.href);
                                e.searchParams.delete("singleUseToken"), c.push(e), i("") } else { if (ve.A.trackEvent({ eventName: "COMMUNITY_BUILD_FORM_LOAD" }), x && x.authDetails && x.authDetails.email) { f("email", x.authDetails.email), f("disableEmail", !0); let n = await u(o, x.authDetails.email); var e, t, a, l, d, m, p, v, y, b; if (n.length > 0) g(!0), f("firstName", null === (e = n[0]) || void 0 === e ? void 0 : e.member[h.firstName.id]), f("lastName", null === (t = n[0]) || void 0 === t ? void 0 : t.member[h.lastName.id]), f("jobTitle", null === (a = n[0]) || void 0 === a ? void 0 : a.role[h.roleName.id]), f("jobDescription", null === (l = n[0]) || void 0 === l ? void 0 : l.role[h.roleDescription.id]), f("biography", null === (d = n[0]) || void 0 === d ? void 0 : d.member[h.description.id]), f("phoneNumber", null === (m = n[0]) || void 0 === m ? void 0 : m.member[h.phone.id]), f("photo", null === (p = n[0]) || void 0 === p || null === (v = p.member) || void 0 === v ? void 0 : v.photo), f("memberPhotoColor", null === (y = n[0]) || void 0 === y || null === (b = y.member) || void 0 === b ? void 0 : b.memberPhotoColor), f("existingPersonalRole", n[0]) } z(!0) } } catch (w) { z(!1); let e = new URL(window.location.href);
                            e.searchParams.delete("singleUseToken"), c.push(e), i("") } }() }), []); const [, S] = (0, he.A)(p((async e => { if (e.email && (0, Tn.B9)(e.bossEmail)) { let t = { token: o, data: { manager: { member: { email: e.bossEmail, firstName: e.bossFirstName, lastName: e.bossLastName, photo: e.bossPhoto }, memberId: e.memberId, role: { name: e.bossJobTitle }, roleId: e.roleId }, personal: { member: { email: e.email, firstName: e.firstName, lastName: e.lastName, description: e.biography, phone: e.phoneNumber, photo: e.photo }, role: { name: e.jobTitle, description: e.jobDescription } } } };
                        t.data = (0, Cn.Ow)({ data: t.data, cbSetup: l, defaultFieldsMap: h }); const { error: a, payload: i } = await s($T.yD.createSmartRole({ orgId: n.id, chartId: r, singleUseToken: o, ...t })); if (a) d("Failed to update the chart!!", "error", 3e3);
                        else { const e = (0, Cn.Tx)(i.result);
                            A(e), ve.A.trackEvent({ eventName: "COMMUNITY_BUILD_FORM_SUBMIT" }) } } else b(!0) }))), M = () => { let e = new URL(window.location.href);
                    e.searchParams.delete("singleUseToken"), c.push(e), i("") }; return (0, we.jsx)(zH, { children: x ? (0, we.jsx)(gH, { createdRole: x, returnToLandingPage: M, defaultFieldsMap: h }) : (0, we.jsx)(we.Fragment, { children: w && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { display: "flex", justifyContent: "center", children: (0, we.jsx)("h1", { children: "Enter Your Details" }) }), (0, we.jsx)(se.A, { ml: "auto", mt: 0, mb: 0, mr: "auto", width: "90%", alignContent: "start", alignItems: "start", children: (0, we.jsx)(oe.Op, { ...m, children: (0, we.jsxs)("form", { id: "communityBuildForm", onSubmit: S, children: [(0, we.jsx)(uH, { org: n, chartId: r, singleUseToken: o, userFound: v, setUserFound: g, defaultFieldsMap: h }), (0, we.jsx)("hr", {}), (0, we.jsx)(nH, { singleUseToken: o, showNewBoss: y, setShowNewBoss: b, supervisorRefersTo: null === k || void 0 === k || null === (t = k.communityBuild) || void 0 === t ? void 0 : t.supervisorRefersTo, defaultFieldsMap: h }), (0, we.jsxs)(se.A, { display: "flex", justifyContent: "center", mb: 6, mt: 4, gridGap: 16, children: [(0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", onClick: M, children: "CANCEL" }), (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", type: "submit", children: "SAVE" })] })] }) }) })] }) }) }) }; var AH = n(99229); const kH = n.p + "static/media/getAccess.4fce02dbae54a2a1f7f4932ed1f8734f.svg"; var SH; const MH = ee.Ay.div(SH || (SH = (0, J.A)(["\n  display: flex;\n  align-items: center;\n  margin-top: 5px;\n  margin-bottom: 5px;\n  width: 105%;\n  .MuiFormControl-fullWidth {\n    margin-right: 5px;\n  }\n"]))),
            EH = e => { let { setToken: t, communityBuild: n } = e; const { orgId: r, chartId: o } = (0, $.useParams)(), [i, l] = (0, a.useState)(""), [s, c] = (0, a.useState)(""), [d, u] = (0, a.useState)(!1), h = n.domainSecurity, m = n.passwordSecurity, { show: p } = (0, Un.A)(), { t: f } = (0, ie.B)(), v = (0, ae.wA)(); return (0, we.jsx)(ze, { containerStyle: { height: 750, alignItems: "center" }, style: { alignItems: "center" }, children: (0, we.jsxs)("form", { onSubmit: async e => { null === e || void 0 === e || e.preventDefault(); try { if (m ? h ? !(!s || !i) : !!i : !!h && !!s) { let e = { password: i, email: s, ip: "" }; const { error: n, payload: a } = await v($T.yD.getCommunityBuildAccessToken({ ignoreErrorHandler: !0, orgId: r, chartId: o, ...e }));
                                    n ? p("Please check the credentials", "error", 3e3) : a.token && a.token.tokenRequest && "email" === a.token.tokenRequest ? (p("Please check your mail box for the access url", "success", 3e3), ve.A.trackEvent({ eventName: "COMMUNITY_BUILD_SENT_INVITATION_LINK" })) : a.token && a.token.token && t(a.token.token) } } catch (e) { p("Please check the credentials", "error", 3e3) } }, children: [(0, we.jsxs)(se.A, { my: 2, gridGap: 16, display: "flex", flexDirection: "column", alignItems: "center", children: [(0, we.jsxs)(se.A, { gridGap: 8, display: "flex", flexDirection: "column", children: [(0, we.jsx)(je.A, { variant: "h1", fontSize: "24px", children: "Welcome to your" }), (0, we.jsx)(je.A, { variant: "h1", fontSize: "28px", color: "primary", weight: "medium", children: "Community Build" })] }), (0, we.jsx)(je.A, { variant: "h6", fontSize: "16px", children: "Collaborate and build your chart in real time" }), (0, we.jsx)(wS.A, { width: 350, src: kH })] }), (0, we.jsxs)(Ke.A, { spacing: { all: 4 }, children: [h && (0, we.jsx)(Ve.A, { color: "primary", id: "email", name: "email", label: "Email Address", type: "text", placeholder: "Enter email address", value: s, onChange: e => c(e.target.value), fullWidth: !0 }), m && (0, we.jsxs)(MH, { children: [(0, we.jsx)(Ve.A, { color: "primary", id: "password", name: "password", label: "Community Build Access Password", type: d ? "text" : "password", placeholder: "Enter Community Build Access Password", value: i, onChange: e => l(e.target.value), fullWidth: !0, InputProps: { endAdornment: (0, we.jsx)(AH.A, { position: "end", children: (0, we.jsx)(Tr.A, { "aria-label": "toggle password visibility", onClick: () => { u(!d) }, onMouseDown: e => { e.preventDefault() }, tabIndex: -1, size: "small", children: !0 === d ? (0, we.jsx)(Ee.Ay, { icon: "ShowPassword" }) : (0, we.jsx)(Ee.Ay, { icon: "HidePassword" }) }, "password_".concat(d)) }) } }), (0, we.jsx)(wn.Ay, { placement: "top", title: "The chart owner has set an access password for this community build and should have sent it to you along with the url link - enter that password here, or contact the community build owner for the access password", arrow: !0, children: (0, we.jsx)("div", { children: (0, we.jsx)(Ee.Ay, { icon: "Info", size: "lg" }) }) })] }), (0, we.jsx)(Ke.A, { spacing: { top: 1 }, children: (0, we.jsx)(Re.A, { fullWidth: !0, variant: "contained", color: "primary", type: "submit", children: "Get Access Link" }) })] }), (0, we.jsxs)(Ke.A, { spacing: { vertical: 1 }, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", f("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: f("Auth.Link.Privacy") }) })] })] }) }) },
            CH = n.p + "static/media/5.86057f11cfff6b8f69de.jpg",
            TH = () => { const { orgId: e, chartId: t } = (0, $.useParams)(), { singleUseToken: n } = (0, Q.A)(), [r, o] = (0, a.useState)(null), [i, l] = (0, a.useState)(null), [s, c] = (0, a.useState)(n), d = (0, ae.wA)(), { showSnack: u } = (0, Un.A)(), [h, m] = (0, he.A)((async () => { if (e && t) try { const { payload: n, error: r } = await d($T.yD.getCommunityBuildSafe({ ignoreErrorHandler: !0, orgId: e, chartId: t })); if (r) return u("Could not verify community org chart form", "info");
                        null !== n && void 0 !== n && n.communityBuild && o(n.communityBuild) } catch (n) { console.log(n) } }));
                (0, a.useEffect)((() => { m() }), []); const [p, f] = (0, he.A)((async () => { const { payload: t, error: n } = await d(nn.UY.getSimple({ orgId: e, singleUseToken: s })); if (n) return u("Could not verify community org chart form", "info");
                    l(null === t || void 0 === t ? void 0 : t.organization) }));
                (0, a.useEffect)((() => { s && f() }), [s]); const v = h || p; return (0, we.jsxs)(dn.A, { loading: v, transparent: !0, children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: s ? "" : "center", bgimagesrc: CH }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsx)(we.Fragment, { children: i && s && r ? (0, we.jsx)(xH, { org: i, chartId: t, singleUseToken: s, setToken: c, communityBuild: r }) : (0, we.jsx)(we.Fragment, { children: r && (0, we.jsx)(EH, { token: s, setToken: c, communityBuild: r }) }) }) })] }) }; var HH = n(15194); const LH = e => { let { onClose: t } = e; return (0, we.jsxs)("div", { className: "error-page", children: [t && (0, we.jsx)("span", { className: "close-btn", onClick: t, children: "\xd7" }), (0, we.jsx)("br", {}), (0, we.jsx)("br", {}), (0, we.jsx)("br", {}), (0, we.jsx)("br", {}), (0, we.jsx)("h1", { className: "text-center", children: "There's nothing to see here." }), (0, we.jsx)("div", { className: "organimi-logo", children: (0, we.jsx)("img", { src: HH, width: "100" }) }), (0, we.jsx)("p", { className: "text-center", children: "Don't hesitate to contact us if we can help you find something." }), (0, we.jsx)("div", { className: "btn-bar", children: (0, we.jsxs)("div", { children: [(0, we.jsx)("a", { className: "e-link secondary", href: "https://organimi.zendesk.com/hc/en-us/requests/new", children: "Contact Support" }), (0, we.jsx)("a", { className: "e-link", onClick: () => { window.location.assign("/") }, children: "Back to Organimi" })] }) })] }) }; var IH = n(15622),
            jH = n(92746); var VH, OH, RH, PH = n(43699); const DH = (0, ee.Ay)("div")(VH || (VH = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display: flex;\n  flex-direction: column;\n  min-height: 100%;\n  align-items: center;\n  justify-content: center;\n  \n  position: relative;\n  h1{\n    font-size:30px;\n    font-weight:700;\n    color:".concat(t.palette.black.main, ";\n  }\n  .large{\n    font-size:24px;\n    color:").concat(t.palette.black.main, ";\n  }\n  .logo{\n    position:absolute;\n    top:").concat(t.spacing(3), "px;\n    left:").concat(t.spacing(3), "px;\n  }\n  .footer{\n    position:absolute;\n    bottom:0;\n  }\n  .headings{\n    width: 90%;\n    margin:").concat(t.spacing(3), "px auto;\n    font-weight:700;\n  }\n  ") })),
            FH = (0, ee.Ay)(kS.A).attrs({ elevation: 2 })(OH || (OH = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width:150px;\n  position:relative;\n  min-height:200px;\n  height:100%;\n  margin:0 ".concat(t.spacing(3), "px ").concat(t.spacing(3), "px ").concat(t.spacing(3), "px;\n  cursor:pointer;\n  color:").concat(t.palette.primary.main, ";\n  &:hover{\n    color:").concat(t.palette.secondary.main, ";\n  }\n") })),
            NH = (0, ee.Ay)("div")(RH || (RH = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position:relative;\n  min-height:30px;\n  padding:".concat(t.spacing(2), "px ").concat(t.spacing(3), "px;\n  margin:0 ").concat(t.spacing(3), "px;\n  cursor:pointer;\n  background-color:").concat(t.palette.primary.main, ";\n  font-size:30px;\n  color:#FFF;\n  \n") })); var _H = n(47556); const BH = () => { const e = (0, $.useHistory)(),
                { openDialog: t } = (0, Ft.A)("newChart"),
                n = (0, ae.wA)(),
                { source: r } = (0, Q.A)(),
                a = (0, ae.d4)(Fr.VF),
                o = (0, ae.d4)(Fr.VW),
                i = (0, le.A)(),
                l = (0, _H.A)(),
                s = n => async () => { let a = r && r.length ? { source: r } : {}; switch (n) {
                        case "dashboard":
                            ve.A.trackEvent({ extraParams: { ...a }, eventName: "GETSTARTED_MY_CHARTS" }), l(); break;
                        case "new":
                            ve.A.trackEvent({ extraParams: { ...a }, eventName: "GETSTARTED_NEW_CHART" }), t(); break;
                        case "import":
                            ve.A.trackEvent({ extraParams: { ...a }, eventName: "GETSTARTED_IMPORT" }), e.push((0, ne.qj)()) } }, [c, d] = (0, aM.A)({ onSubmit: async e => { e && e.preventDefault(); let t = r ? { eventName: "GETSTARTED_SAMPLE_CHART", extraParams: { source: r } } : { eventName: "GETSTARTED_SAMPLE_CHART" }; return ve.A.trackEvent(t), await n(VS.te.createDemo({ activeLicense: a })) }, minDelay: 2500, onComplete: t => { t && t.redirectUrl && e.push(t.redirectUrl) } }), u = () => { l() }; return c && (0, we.jsx)(DH, { children: (0, we.jsx)(PH.A, { width: 400, message: "Loading your Practice Chart. Please Wait..." }) }) || (0, we.jsxs)(DH, { children: [(0, we.jsx)(se.A, { mt: 4, mb: 3, children: (0, we.jsx)(wS.A, { height: 100, src: be, onClick: u }) }), "welcome" !== r && (0, we.jsx)("span", { children: (0, we.jsx)(hr.A, { "aria-label": "close", onClick: u, style: { position: "absolute", right: 0, top: 0 }, children: (0, we.jsx)(Ee.Ay, { icon: "Close" }) }) }), "welcome" === r || "v6" === r ? (0, we.jsxs)(se.A, { mb: 3, children: [(0, we.jsxs)(je.A, { variant: "h1", align: "center", children: ["Hey,", " ", (0, we.jsxs)("span", { style: { color: i.palette.secondary.main }, children: [(0, CM.A)(o.firstName), "!"] })] }), (0, we.jsx)(je.A, { variant: "subtitle1", className: "large", align: "center", children: "Let's get started" })] }) : (0, we.jsxs)(se.A, { mb: 3, children: [(0, we.jsx)(je.A, { variant: "h1", align: "center", children: "Create a New Chart" }), (0, we.jsx)(je.A, { variant: "subtitle1", className: "large", align: "center", children: "Select an option" })] }), (0, we.jsx)(se.A, { maxWidth: "80%", clone: !0, m: 3, children: (0, we.jsxs)(Tt.A, { container: !0, children: [(0, we.jsxs)(Tt.A, { item: !0, xs: 12, sm: 6, md: 4, children: [(0, we.jsx)(NH, { onClick: d, children: "Learn" }), (0, we.jsxs)(FH, { onClick: d, children: [(0, we.jsx)(Ee.Ay, { icon: "ProjectDiagram", size: "x4" }), (0, we.jsx)(je.A, { className: "headings", variant: "body1", align: "center", children: "Practice with a sample chart" })] })] }), (0, we.jsxs)(Tt.A, { item: !0, xs: 12, sm: 6, md: 4, children: [(0, we.jsx)(NH, { onClick: s("new"), children: "Build Chart" }), (0, we.jsxs)(FH, { onClick: s("new"), children: [(0, we.jsx)(Ee.Ay, { icon: "Pencil", size: "x4" }), (0, we.jsx)(je.A, { className: "headings", variant: "body1", align: "center", children: "Create a chart from scratch or using a template" })] })] }), (0, we.jsxs)(Tt.A, { item: !0, xs: 12, sm: 6, md: 4, children: [(0, we.jsx)(NH, { onClick: s("import"), children: "Import Data" }), (0, we.jsxs)(FH, { onClick: s("import"), children: [(0, we.jsx)(Ee.Ay, { icon: "Download", size: "x5" }), (0, we.jsx)(je.A, { className: "headings", variant: "body1", align: "center", children: "Create a chart with my own data (CSV, Excel)" })] })] })] }) })] }) }; var WH, UH, qH = n(83340); const GH = (0, ee.Ay)("div")(WH || (WH = (0, J.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100%;\n  align-items: center;\n  justify-content: center;\n  \n  position: relative;\n  h1{\n    font-size:30px;\n    font-weight:700;\n  }\n"]))),
            KH = (0, ee.Ay)("div")(UH || (UH = (0, J.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100%;\n  align-items: center;\n  justify-content: center;\n\n  position: relative;\n  h1{\n    font-size:30px;\n    font-weight:700;\n  }\n"]))); var ZH = n(38161),
            YH = n.n(ZH),
            XH = n(66366),
            $H = n.n(XH),
            QH = n(42123),
            JH = n.n(QH),
            eL = "bodyAttributes",
            tL = "htmlAttributes",
            nL = "titleAttributes",
            rL = { BASE: "base", BODY: "body", HEAD: "head", HTML: "html", LINK: "link", META: "meta", NOSCRIPT: "noscript", SCRIPT: "script", STYLE: "style", TITLE: "title" },
            aL = (Object.keys(rL).map((function(e) { return rL[e] })), "charset"),
            oL = "cssText",
            iL = "href",
            lL = "http-equiv",
            sL = "innerHTML",
            cL = "itemprop",
            dL = "name",
            uL = "property",
            hL = "rel",
            mL = "src",
            pL = "target",
            fL = { accesskey: "accessKey", charset: "charSet", class: "className", contenteditable: "contentEditable", contextmenu: "contextMenu", "http-equiv": "httpEquiv", itemprop: "itemProp", tabindex: "tabIndex" },
            vL = "defaultTitle",
            gL = "defer",
            yL = "encodeSpecialCharacters",
            bL = "onChangeClientState",
            wL = "titleTemplate",
            zL = Object.keys(fL).reduce((function(e, t) { return e[fL[t]] = t, e }), {}),
            xL = [rL.NOSCRIPT, rL.SCRIPT, rL.STYLE],
            AL = "data-react-helmet",
            kL = "function" === typeof Symbol && "symbol" === typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" === typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e },
            SL = function() {
                function e(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                        r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } return function(t, n, r) { return n && e(t.prototype, n), r && e(t, r), t } }(),
            ML = Object.assign || function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e },
            EL = function(e, t) { var n = {}; for (var r in e) t.indexOf(r) >= 0 || Object.prototype.hasOwnProperty.call(e, r) && (n[r] = e[r]); return n },
            CL = function(e) { return !1 === (!(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1]) ? String(e) : String(e).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;") },
            TL = function(e) { var t = VL(e, rL.TITLE),
                    n = VL(e, wL); if (n && t) return n.replace(/%s/g, (function() { return Array.isArray(t) ? t.join("") : t })); var r = VL(e, vL); return t || r || void 0 },
            HL = function(e) { return VL(e, bL) || function() {} },
            LL = function(e, t) { return t.filter((function(t) { return "undefined" !== typeof t[e] })).map((function(t) { return t[e] })).reduce((function(e, t) { return ML({}, e, t) }), {}) },
            IL = function(e, t) { return t.filter((function(e) { return "undefined" !== typeof e[rL.BASE] })).map((function(e) { return e[rL.BASE] })).reverse().reduce((function(t, n) { if (!t.length)
                        for (var r = Object.keys(n), a = 0; a < r.length; a++) { var o = r[a].toLowerCase(); if (-1 !== e.indexOf(o) && n[o]) return t.concat(n) }
                    return t }), []) },
            jL = function(e, t, n) { var r = {}; return n.filter((function(t) { return !!Array.isArray(t[e]) || ("undefined" !== typeof t[e] && FL("Helmet: " + e + ' should be of type "Array". Instead found type "' + kL(t[e]) + '"'), !1) })).map((function(t) { return t[e] })).reverse().reduce((function(e, n) { var a = {};
                    n.filter((function(e) { for (var n = void 0, o = Object.keys(e), i = 0; i < o.length; i++) { var l = o[i],
                                s = l.toLowerCase(); - 1 === t.indexOf(s) || n === hL && "canonical" === e[n].toLowerCase() || s === hL && "stylesheet" === e[s].toLowerCase() || (n = s), -1 === t.indexOf(l) || l !== sL && l !== oL && l !== cL || (n = l) } if (!n || !e[n]) return !1; var c = e[n].toLowerCase(); return r[n] || (r[n] = {}), a[n] || (a[n] = {}), !r[n][c] && (a[n][c] = !0, !0) })).reverse().forEach((function(t) { return e.push(t) })); for (var o = Object.keys(a), i = 0; i < o.length; i++) { var l = o[i],
                            s = JH()({}, r[l], a[l]);
                        r[l] = s } return e }), []).reverse() },
            VL = function(e, t) { for (var n = e.length - 1; n >= 0; n--) { var r = e[n]; if (r.hasOwnProperty(t)) return r[t] } return null },
            OL = function() { var e = Date.now(); return function(t) { var n = Date.now();
                    n - e > 16 ? (e = n, t(n)) : setTimeout((function() { OL(t) }), 0) } }(),
            RL = function(e) { return clearTimeout(e) },
            PL = "undefined" !== typeof window ? window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || OL : n.g.requestAnimationFrame || OL,
            DL = "undefined" !== typeof window ? window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || RL : n.g.cancelAnimationFrame || RL,
            FL = function(e) { return console && "function" === typeof console.warn && console.warn(e) },
            NL = null,
            _L = function(e, t) { var n = e.baseTag,
                    r = e.bodyAttributes,
                    a = e.htmlAttributes,
                    o = e.linkTags,
                    i = e.metaTags,
                    l = e.noscriptTags,
                    s = e.onChangeClientState,
                    c = e.scriptTags,
                    d = e.styleTags,
                    u = e.title,
                    h = e.titleAttributes;
                UL(rL.BODY, r), UL(rL.HTML, a), WL(u, h); var m = { baseTag: qL(rL.BASE, n), linkTags: qL(rL.LINK, o), metaTags: qL(rL.META, i), noscriptTags: qL(rL.NOSCRIPT, l), scriptTags: qL(rL.SCRIPT, c), styleTags: qL(rL.STYLE, d) },
                    p = {},
                    f = {};
                Object.keys(m).forEach((function(e) { var t = m[e],
                        n = t.newTags,
                        r = t.oldTags;
                    n.length && (p[e] = n), r.length && (f[e] = m[e].oldTags) })), t && t(), s(e, p, f) },
            BL = function(e) { return Array.isArray(e) ? e.join("") : e },
            WL = function(e, t) { "undefined" !== typeof e && document.title !== e && (document.title = BL(e)), UL(rL.TITLE, t) },
            UL = function(e, t) { var n = document.getElementsByTagName(e)[0]; if (n) { for (var r = n.getAttribute(AL), a = r ? r.split(",") : [], o = [].concat(a), i = Object.keys(t), l = 0; l < i.length; l++) { var s = i[l],
                            c = t[s] || "";
                        n.getAttribute(s) !== c && n.setAttribute(s, c), -1 === a.indexOf(s) && a.push(s); var d = o.indexOf(s); - 1 !== d && o.splice(d, 1) } for (var u = o.length - 1; u >= 0; u--) n.removeAttribute(o[u]);
                    a.length === o.length ? n.removeAttribute(AL) : n.getAttribute(AL) !== i.join(",") && n.setAttribute(AL, i.join(",")) } },
            qL = function(e, t) { var n = document.head || document.querySelector(rL.HEAD),
                    r = n.querySelectorAll(e + "[" + AL + "]"),
                    a = Array.prototype.slice.call(r),
                    o = [],
                    i = void 0; return t && t.length && t.forEach((function(t) { var n = document.createElement(e); for (var r in t)
                        if (t.hasOwnProperty(r))
                            if (r === sL) n.innerHTML = t.innerHTML;
                            else if (r === oL) n.styleSheet ? n.styleSheet.cssText = t.cssText : n.appendChild(document.createTextNode(t.cssText));
                    else { var l = "undefined" === typeof t[r] ? "" : t[r];
                        n.setAttribute(r, l) } n.setAttribute(AL, "true"), a.some((function(e, t) { return i = t, n.isEqualNode(e) })) ? a.splice(i, 1) : o.push(n) })), a.forEach((function(e) { return e.parentNode.removeChild(e) })), o.forEach((function(e) { return n.appendChild(e) })), { oldTags: a, newTags: o } },
            GL = function(e) { return Object.keys(e).reduce((function(t, n) { var r = "undefined" !== typeof e[n] ? n + '="' + e[n] + '"' : "" + n; return t ? t + " " + r : r }), "") },
            KL = function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return Object.keys(e).reduce((function(t, n) { return t[fL[n] || n] = e[n], t }), t) },
            ZL = function(e, t, n) { switch (e) {
                    case rL.TITLE:
                        return { toComponent: function() { return function(e, t, n) { var r, o = ((r = { key: t })[AL] = !0, r),
                                        i = KL(n, o); return [a.createElement(rL.TITLE, i, t)] }(0, t.title, t.titleAttributes) }, toString: function() { return function(e, t, n, r) { var a = GL(n),
                                        o = BL(t); return a ? "<" + e + " " + AL + '="true" ' + a + ">" + CL(o, r) + "</" + e + ">" : "<" + e + " " + AL + '="true">' + CL(o, r) + "</" + e + ">" }(e, t.title, t.titleAttributes, n) } };
                    case eL:
                    case tL:
                        return { toComponent: function() { return KL(t) }, toString: function() { return GL(t) } };
                    default:
                        return { toComponent: function() { return function(e, t) { return t.map((function(t, n) { var r, o = ((r = { key: n })[AL] = !0, r); return Object.keys(t).forEach((function(e) { var n = fL[e] || e; if (n === sL || n === oL) { var r = t.innerHTML || t.cssText;
                                                o.dangerouslySetInnerHTML = { __html: r } } else o[n] = t[e] })), a.createElement(e, o) })) }(e, t) }, toString: function() { return function(e, t, n) { return t.reduce((function(t, r) { var a = Object.keys(r).filter((function(e) { return !(e === sL || e === oL) })).reduce((function(e, t) { var a = "undefined" === typeof r[t] ? t : t + '="' + CL(r[t], n) + '"'; return e ? e + " " + a : a }), ""),
                                            o = r.innerHTML || r.cssText || "",
                                            i = -1 === xL.indexOf(e); return t + "<" + e + " " + AL + '="true" ' + a + (i ? "/>" : ">" + o + "</" + e + ">") }), "") }(e, t, n) } } } },
            YL = function(e) { var t = e.baseTag,
                    n = e.bodyAttributes,
                    r = e.encode,
                    a = e.htmlAttributes,
                    o = e.linkTags,
                    i = e.metaTags,
                    l = e.noscriptTags,
                    s = e.scriptTags,
                    c = e.styleTags,
                    d = e.title,
                    u = void 0 === d ? "" : d,
                    h = e.titleAttributes; return { base: ZL(rL.BASE, t, r), bodyAttributes: ZL(eL, n, r), htmlAttributes: ZL(tL, a, r), link: ZL(rL.LINK, o, r), meta: ZL(rL.META, i, r), noscript: ZL(rL.NOSCRIPT, l, r), script: ZL(rL.SCRIPT, s, r), style: ZL(rL.STYLE, c, r), title: ZL(rL.TITLE, { title: u, titleAttributes: h }, r) } },
            XL = function(e) { var t, n; return n = t = function(t) {
                    function n() { return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, n),
                            function(e, t) { if (!e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return !t || "object" !== typeof t && "function" !== typeof t ? e : t }(this, t.apply(this, arguments)) } return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function, not " + typeof t);
                        e.prototype = Object.create(t && t.prototype, { constructor: { value: e, enumerable: !1, writable: !0, configurable: !0 } }), t && (Object.setPrototypeOf ? Object.setPrototypeOf(e, t) : e.__proto__ = t) }(n, t), n.prototype.shouldComponentUpdate = function(e) { return !$H()(this.props, e) }, n.prototype.mapNestedChildrenToProps = function(e, t) { if (!t) return null; switch (e.type) {
                            case rL.SCRIPT:
                            case rL.NOSCRIPT:
                                return { innerHTML: t };
                            case rL.STYLE:
                                return { cssText: t } } throw new Error("<" + e.type + " /> elements are self-closing and can not contain children. Refer to our API for more information.") }, n.prototype.flattenArrayTypeChildren = function(e) { var t, n = e.child,
                            r = e.arrayTypeChildren,
                            a = e.newChildProps,
                            o = e.nestedChildren; return ML({}, r, ((t = {})[n.type] = [].concat(r[n.type] || [], [ML({}, a, this.mapNestedChildrenToProps(n, o))]), t)) }, n.prototype.mapObjectTypeChildren = function(e) { var t, n, r = e.child,
                            a = e.newProps,
                            o = e.newChildProps,
                            i = e.nestedChildren; switch (r.type) {
                            case rL.TITLE:
                                return ML({}, a, ((t = {})[r.type] = i, t.titleAttributes = ML({}, o), t));
                            case rL.BODY:
                                return ML({}, a, { bodyAttributes: ML({}, o) });
                            case rL.HTML:
                                return ML({}, a, { htmlAttributes: ML({}, o) }) } return ML({}, a, ((n = {})[r.type] = ML({}, o), n)) }, n.prototype.mapArrayTypeChildrenToProps = function(e, t) { var n = ML({}, t); return Object.keys(e).forEach((function(t) { var r;
                            n = ML({}, n, ((r = {})[t] = e[t], r)) })), n }, n.prototype.warnOnInvalidChildren = function(e, t) { return !0 }, n.prototype.mapChildrenToProps = function(e, t) { var n = this,
                            r = {}; return a.Children.forEach(e, (function(e) { if (e && e.props) { var a = e.props,
                                    o = a.children,
                                    i = function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return Object.keys(e).reduce((function(t, n) { return t[zL[n] || n] = e[n], t }), t) }(EL(a, ["children"])); switch (n.warnOnInvalidChildren(e, o), e.type) {
                                    case rL.LINK:
                                    case rL.META:
                                    case rL.NOSCRIPT:
                                    case rL.SCRIPT:
                                    case rL.STYLE:
                                        r = n.flattenArrayTypeChildren({ child: e, arrayTypeChildren: r, newChildProps: i, nestedChildren: o }); break;
                                    default:
                                        t = n.mapObjectTypeChildren({ child: e, newProps: t, newChildProps: i, nestedChildren: o }) } } })), t = this.mapArrayTypeChildrenToProps(r, t) }, n.prototype.render = function() { var t = this.props,
                            n = t.children,
                            r = EL(t, ["children"]),
                            o = ML({}, r); return n && (o = this.mapChildrenToProps(n, o)), a.createElement(e, o) }, SL(n, null, [{ key: "canUseDOM", set: function(t) { e.canUseDOM = t } }]), n }(a.Component), t.propTypes = { base: os().object, bodyAttributes: os().object, children: os().oneOfType([os().arrayOf(os().node), os().node]), defaultTitle: os().string, defer: os().bool, encodeSpecialCharacters: os().bool, htmlAttributes: os().object, link: os().arrayOf(os().object), meta: os().arrayOf(os().object), noscript: os().arrayOf(os().object), onChangeClientState: os().func, script: os().arrayOf(os().object), style: os().arrayOf(os().object), title: os().string, titleAttributes: os().object, titleTemplate: os().string }, t.defaultProps = { defer: !0, encodeSpecialCharacters: !0 }, t.peek = e.peek, t.rewind = function() { var t = e.rewind(); return t || (t = YL({ baseTag: [], bodyAttributes: {}, encodeSpecialCharacters: !0, htmlAttributes: {}, linkTags: [], metaTags: [], noscriptTags: [], scriptTags: [], styleTags: [], title: "", titleAttributes: {} })), t }, n }(YH()((function(e) { return { baseTag: IL([iL, pL], e), bodyAttributes: LL(eL, e), defer: VL(e, gL), encode: VL(e, yL), htmlAttributes: LL(tL, e), linkTags: jL(rL.LINK, [hL, iL], e), metaTags: jL(rL.META, [dL, aL, lL, uL, cL], e), noscriptTags: jL(rL.NOSCRIPT, [sL], e), onChangeClientState: HL(e), scriptTags: jL(rL.SCRIPT, [mL, sL], e), styleTags: jL(rL.STYLE, [oL], e), title: TL(e), titleAttributes: LL(nL, e) } }), (function(e) { NL && DL(NL), e.defer ? NL = PL((function() { _L(e, (function() { NL = null })) })) : (_L(e), NL = null) }), YL)((function() { return null })));
        XL.renderStatic = XL.rewind; var $L = n(77604); const QL = e => { var t; return (null === (t = e.user) || void 0 === t ? void 0 : t.firstName) || "" },
            JL = ((0, Dr.Mz)(IH.A, (e => null === e || void 0 === e ? void 0 : e.id)), e => e.user.goals || {}); var eI, tI, nI, rI; const aI = ee.Ay.div(eI || (eI = (0, J.A)(["\n  background-color: #2c2c2c;\n  width: 100%;\n  align-items: center;\n  height: 100%;\n  padding: 30px;\n  margin: 40px 0 30px;\n  .MuiGrid-container {\n    text-align: center;\n    margin: 35px 0;\n  }\n"]))),
            oI = ee.Ay.div(tI || (tI = (0, J.A)(["\n  width: 100%;\n  align-items: center;\n  padding: 30px 40px;\n  text-align: center;\n"]))),
            iI = (0, ee.Ay)(Ge.A)(nI || (nI = (0, J.A)(["\n  color: white;\n  border-color: white;\n  margin-top: 30px;\n  padding: 5px 25px;\n"]))),
            lI = ee.Ay.a(rI || (rI = (0, J.A)(["\n  color: #303030;\n  text-decoration: underline;\n"]))); const sI = function() { const [e, t] = (0, a.useState)(!1), [n, r] = (0, un.A)("activeLicenseId", null, !0), o = (0, le.A)(), i = (0, ae.d4)(QL), l = (0, ae.d4)(JL), s = (0, ae.d4)(IH.A), { type: c, sessionId: d, license: u } = (0, Q.A)(), h = (null === s || void 0 === s ? void 0 : s.id) || u, m = (0, $.useHistory)(), p = (0, ae.wA)(), [f, v] = (0, he.A)((async () => { const { error: e, payload: t } = await p(qH.AV.activateLicense({ licenseId: h, sessionId: d })), n = null === t || void 0 === t ? void 0 : t.subscription, r = null === n || void 0 === n ? void 0 : n.licenseType, a = null === n || void 0 === n ? void 0 : n.planId;!0 === (null === n || void 0 === n ? void 0 : n.success) && b(r, a), e && m.push((0, ne.gz)()) })), [g, y] = (0, aM.A)({ minDelay: 500, maxRetries: 10, onSubmit: async () => await p(qH.AV.checkLicenseActivationStatus({ licenseId: h, sessionId: d, ignoreErrorHandler: !0 })), onError: e => { let { payload: t } = e;
                    t && "timeout" === t.type && v() }, shouldRetry: e => { var t; let { payload: n, error: r } = e; return !(!1 !== (null === n || void 0 === n || null === (t = n.subscription) || void 0 === t ? void 0 : t.success) && !r) }, onComplete: e => { const n = null === e || void 0 === e ? void 0 : e.subscription,
                        r = null === n || void 0 === n ? void 0 : n.licenseType,
                        a = null === n || void 0 === n ? void 0 : n.planId;!0 === (null === n || void 0 === n ? void 0 : n.success) && (b(r, a), t(!0)) } });
            (0, a.useEffect)((() => (!n && h && r(h), "upgrade" === c && d && h && y(), () => {!async function() { await p(me.ip.getMe({ ignoreErrorHandler: !0 })) }() })), []); const b = (e, t) => { if (d && (null === l || void 0 === l || !l.upgradeAccount)) try { if (e && window.dataLayer) { const n = (0, $L.vS)(t); if (!n) return; const { frequency: r, userTier: a, cost: o } = n;
                            window.dataLayer = window.dataLayer || [], window.dataLayer.push({ event: "account_upgraded_new", category: "Account", action: "account_upgraded_new", label: e, value: o, LicenseUserTier: a, LicenseFrequency: r, LicenseCost: o, LicenseType: e }), window.dataLayer.push({ event: "buy_now", value: o, LicenseUserTier: a, LicenseFrequency: r, LicenseCost: o, LicenseType: e }), (async () => { await p(me.ip.trackGoal({ goalId: "upgradeAccount" })) })() } } catch (n) { console.log("Problem logging upgrade event in GA"), console.log(n) } },
                w = g || f,
                z = g ? "Processing your payment..." : f ? "Activating your plan..." : "",
                x = [{ imgSrc: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/tutorial.png"), icon: "Help", title: "Tutorials", description: "Looking for more assistance on building your chart? Check out our video library!", buttonLink: "https://www.organimi.com/the-huddle-tv/" }, { imgSrc: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/webinar.png"), icon: "PlayCircle", title: "Webinars", description: "Our experts are here to help! Book a call or take a look at our product webinars!", buttonLink: "https://www.youtube.com/c/Organimi/videos" }, { imgSrc: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/docs.png"), icon: "Doc", title: "Help Docs", description: "You've got questions, we've got answers! Check out or help docs for all things Organimi!", buttonLink: "https://organimi.zendesk.com/hc/en-us" }],
                A = [{ src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/1.svg"), alt: "amazon" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/2.svg"), alt: "siemens" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/3.svg"), alt: "university of toronto" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/4.svg"), alt: "facebook" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/5.svg"), alt: "habitat" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/6.svg"), alt: "shopify" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/7.svg"), alt: "seal of the city and county of san francisco" }, { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/upgrade-success/8.svg"), alt: "ADP" }]; return (0, we.jsxs)(KH, { style: { justifyContent: "flex-start" }, children: [(0, we.jsx)(XL, { children: (0, we.jsx)("title", { children: "Thank you" }) }), w ? (0, we.jsx)(iM, { isComplete: e, status: z, logo: be }) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 4, children: (0, we.jsx)(wS.A, { height: 100, src: be }) }), (0, we.jsx)(se.A, { mt: 3, mb: 2, children: (0, we.jsxs)(je.A, { variant: "h1", color: o.palette.black.main, children: ["upgrade" === c ? "Thanks for upgrading, " : "Your card has been updated, ", (0, we.jsxs)("span", { style: { color: o.palette.secondary.main }, children: [(0, Tn.ZH)(i), "!"] })] }) }), (0, we.jsxs)(se.A, { mt: 2, mb: 3, children: ["upgrade" === c && d && (0, we.jsx)(je.A, { variant: "h6", align: "center", weight: "medium", color: o.palette.black.main, children: "Your receipt will be emailed to you." }), (0, we.jsxs)(je.A, { variant: "h6", align: "center", weight: "medium", color: o.palette.black.main, children: ["If you have any questions, please feel free to", " ", (0, we.jsx)(lI, { href: "mailto:<EMAIL>", children: "reach out" }), " to us!"] })] }), (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", onClick: () => { window.location.assign("/") }, children: "Continue to Organimi >" }), "upgrade" === c && d && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(aI, { children: [(0, we.jsx)(je.A, { variant: "h1", align: "center", color: "white", children: "Or get started with some resources:" }), (0, we.jsx)(Tt.A, { container: !0, spacing: 3, children: x.map((e => (0, we.jsxs)(Tt.A, { item: !0, xs: 4, children: [(0, we.jsx)("img", { width: "Help Docs" === e.title ? "150px" : "200px", height: "200px", src: e.imgSrc, alt: e.title }), (0, we.jsxs)(se.A, { display: "flex", justifyContent: "center", alignItems: "center", m: 2, children: [(0, we.jsx)(Ee.Ay, { icon: e.icon, size: "x3", color: o.palette.secondary.main }), (0, we.jsx)(je.A, { variant: "h3", weight: "medium", color: "white", style: { marginLeft: 10 }, children: e.title })] }), (0, we.jsx)(je.A, { color: "white", style: { margin: "0 65px" }, children: e.description }), (0, we.jsx)(iI, { variant: "outlined", href: e.buttonLink, target: "_blank", children: "View >" })] }))) })] }), (0, we.jsxs)(oI, { children: [(0, we.jsxs)(se.A, { display: "flex", justifyContent: "center", children: [(0, we.jsx)(je.A, { variant: "h1", color: o.palette.black.main, children: "You're a part of the\xa0" }), " ", (0, we.jsx)(je.A, { variant: "h1", color: o.palette.primary.main, children: "Organimi Community" })] }), (0, we.jsx)(je.A, { variant: "h6", weight: "medium", style: { marginTop: 10 }, children: "Joining thousands of others in building great org charts!" }), (0, we.jsx)(Tt.A, { container: !0, spacing: 3, style: { margin: "20px 0" }, children: A.map((e => (0, we.jsx)(Tt.A, { item: !0, xs: 3, children: (0, we.jsx)("img", { src: e.src, alt: e.alt }) }))) }), (0, we.jsx)(Ge.A, { target: "_blank", variant: "contained", href: "https://www.organimi.com/case-studies/", color: "primary", children: "Read Success Stories >" })] })] })] })] }) }; const cI = function() { const e = (0, le.A)(),
                { openDialog: t } = (0, Ft.A)("upgradeDialog"),
                n = (0, ae.d4)(QL),
                r = (0, _H.A)(); return (0, we.jsxs)(GH, { style: { justifyContent: "flex-start" }, children: [(0, we.jsx)(se.A, { my: 4, children: (0, we.jsx)(wS.A, { height: 100, src: be }) }), (0, we.jsx)(se.A, { my: 3, children: (0, we.jsxs)(je.A, { variant: "h1", align: "center", children: ["Something went wrong trying to upgrade your account,", " ", (0, we.jsxs)("span", { style: { color: e.palette.secondary.main }, children: [(0, Tn.ZH)(n), "!"] })] }) }), (0, we.jsxs)(se.A, { my: 3, children: [(0, we.jsx)(je.A, { variant: "body1", align: "center" }), (0, we.jsxs)(je.A, { variant: "body1", align: "center", children: ["If you have any questions, please feel free to", " ", (0, we.jsx)("a", { href: "mailto:<EMAIL>", children: "reach out" }), " to us!"] })] }), (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", justifyContent: "center", children: [(0, we.jsx)(se.A, { mr: 2, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", onClick: t, children: "Try Again" }) }), (0, we.jsx)(se.A, { mx: 2, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "secondary", onClick: () => window.open("https://organimi.zendesk.com/hc/en-us/requests/new", "_blank"), children: "Contact Support" }) }), (0, we.jsx)(se.A, { ml: 2, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", endIcon: (0, we.jsx)(Ee.Ay, { icon: "ArrowRight" }), onClick: () => { r() }, children: "Back to Organimi" }) })] })] }) }; var dI, uI; const hI = (0, ee.Ay)(je.A)(dI || (dI = (0, J.A)(["\n  font-size: 14px;\n  color: #000000;\n  font-weight: 500;\n  text-transform: uppercase;\n"]))),
            mI = (0, ee.Ay)(je.A)(uI || (uI = (0, J.A)(["\n  font-size: 15px;\n  color: #444444;\n  font-weight: 400;\n"]))); var pI, fI = n(75995); const vI = (0, ee.Ay)(je.A)(pI || (pI = (0, J.A)(["\n  font-size: 20px;\n  text-align: center;\n"]))),
            gI = e => { let { message: t } = e; return (0, we.jsx)(se.A, { width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", children: (0, we.jsxs)(se.A, { flexDirection: "column", width: 300, gridGap: 64, children: [(0, we.jsx)("img", { src: fI.A, width: 300 }), (0, we.jsx)(vI, { children: t || "Connecting to Organimi" })] }) }) }; var yI, bI = n(48655); const wI = (0, ee.Ay)(bI.A)(yI || (yI = (0, J.A)(["\n  background: #ffffff;\n  margin-top: 0;\n  margin-bottom: 0;\n  .MuiSelect-nativeInput {\n    box-sizing: border-box;\n  }\n  .MuiSelect-select:focus {\n    background: transparent;\n  }\n  label {\n    color: ", ";\n    text-transform: uppercase;\n    font-size: 16px;\n    margin-top: 12px;\n    padding-top: 12px;\n  }\n  fieldset {\n    border-top: none;\n    border-left: none;\n    border-right: none;\n    border-radius: 0;\n  }\n  .MuiOutlinedInput-input {\n    margin-left: 8px;\n    margin-top: 24px;\n  }\n"])), (e => { let { theme: t } = e; return t.palette.primary.main })),
            zI = e => { let { error: t, onSubmit: n } = e; const r = "".concat(pe.Yr, ".").concat(pe.ue, ".").concat(pe.cM),
                    { t: a } = (0, ie.B)(),
                    o = de(),
                    i = (0, oe.mN)({ mode: "onChange" }),
                    { handleSubmit: l, errors: s } = i,
                    [c, d] = (0, he.A)(l(n)); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: o }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsxs)(ze, { version: "".concat(r), children: [(0, we.jsx)(Ke.A, { spacing: { vertical: 2 }, children: (0, we.jsx)(je.A, { variant: "h1", children: "Enter Password" }) }), (0, we.jsx)(Ke.A, { spacing: { all: 4 }, children: (0, we.jsx)(oe.Op, { ...i, children: (0, we.jsxs)("form", { onSubmit: d, children: [(0, we.jsx)(ye.A, { id: "password-link-input", name: "password", label: a("Auth.Label.Password"), fullWidth: !0, fieldRefParams: { required: "Please enter password" }, error: !!s.password }), (0, we.jsx)(Oe.A, { children: t || s.password && s.password.message }), (0, we.jsx)(Ke.A, { spacing: { top: 1 }, children: (0, we.jsx)(Ge.A, { type: "submit", variant: "contained", color: "primary", fullWidth: !0, disabled: c, "data-testid": "submit-button", children: "Submit" }) })] }) }) }), (0, we.jsxs)(Ke.A, { spacing: { vertical: 1 }, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", a("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: a("Auth.Link.Privacy") }) })] })] }) })] }) }; var xI = n(17785); const AI = e => { let { children: t } = e; const n = (0, ae.wA)(),
                [r, o] = (0, un.A)("token", null, !0),
                [i, l] = (0, un.A)("publicId"),
                [s, c] = (0, a.useState)(),
                { orgId: d, chartId: u } = (0, $.useParams)(),
                [h, m] = (0, a.useState)(null),
                [p, f] = (0, a.useState)(null),
                v = (null === r || void 0 === r ? void 0 : r.type) === Wt.em.PASSWORD;
            (0, a.useEffect)((() => { r && i && (async () => { const { payload: e } = await n(xI.GQ.getPublicAccessDetails({ orgId: d, chartId: u, pId: i }));
                    m(null === e || void 0 === e ? void 0 : e.type), f(null === e || void 0 === e ? void 0 : e.requiresPassword) })() }), [i]); return (0, we.jsxs)(dn.A, { loading: !h, empty: !0, children: [!p && t, p && v && t, p && !v && (0, we.jsx)(zI, { onSubmit: async e => { let { password: t } = e; if (!t) return; const { error: r, payload: a } = await n(xI.GQ.validatePasswordForLink({ orgId: d, chartId: u, pId: i, password: t, ignoreErrorHandler: !0 })); return !r && null !== a && void 0 !== a && a.token || c((null === a || void 0 === a ? void 0 : a.error) || "Something went wrong"), !r && null !== a && void 0 !== a && a.token ? (o({ token: null === a || void 0 === a ? void 0 : a.token, savedAt: new Date, type: Wt.G6.PASSWORD }), void c(null)) : void 0 }, error: s })] }) }; var kI = n(59269); const SI = e => e.user.organizations || [],
            MI = (0, Dr.Mz)(kI._, (e => null === e || void 0 === e ? void 0 : e.activeLicenseId)),
            EI = (0, Dr.Mz)(Fr.VW, Fr.AU, ((e, t) => { var n; const r = ((null === e || void 0 === e ? void 0 : e.licenses) || []).length > 0,
                    a = null === t || void 0 === t ? void 0 : t.accessLevel,
                    o = null === e || void 0 === e || null === (n = e.userProfile) || void 0 === n ? void 0 : n.licenseSetup,
                    i = !(null !== o && void 0 !== o && o.isRequired) || (null === o || void 0 === o ? void 0 : o.isComplete); return !!r || (a === Wt.td.VIEWER || a === Wt.td.EDITOR || a === Wt.td.PUBLIC || null === e || void 0 === e || !e.userProfile || (!o || i)) })); var CI = n(50882),
            TI = n(6124); const HI = () => { var e; const t = (0, Q.A)(),
                    [n, r] = (0, a.useState)(parseInt(t.firstPageNumber || "1")),
                    { params: { orgId: o } } = (0, re.u)((0, ne.wi)()),
                    i = (0, ae.d4)((0, kM.hm)(o || "", !1)),
                    l = (0, ae.d4)(kM.U3),
                    { pageMap: s, lastPage: c } = (0, ae.d4)((0, kM.SN)(o || "")),
                    { getAllOrgs: d, loading: u, getOrgRelations: h } = (0, rC.A)(),
                    { updatePageReady: m, resetPageReady: p } = (0, TI.A)();
                (0, a.useEffect)((() => { f(parseInt(t.firstPageNumber || "1")) }), [t.firstPageNumber]); const f = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1; const t = Math.max(3, c);
                    e = Math.min(t, e), p(), r(e), c >= 3 && m() }; return (0, a.useEffect)((() => { const e = parseInt(t.firstPageNumber || "1");
                    f(!e || !u && c && e > c && c >= 3 ? c : e) }), [t.firstPageNumber, c]), (0, a.useEffect)((() => { o && !u && !i && (async e => { null !== l && void 0 !== l && l.length || await d(), e && await h(e) })(o) }), []), o && i ? (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)("button", { id: "nextPagePrintButton", onClick: () => f(n + 1), style: { zIndex: 9999, position: "absolute", display: "none" }, children: "next page" }), (0, we.jsx)(fM.A, { p: "25px 20px 21px 11px", height: "100%", className: "ownership", children: (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", flexDirection: "column", height: "100%", children: [1 === n && (0, we.jsx)(bC, { isPrintView: !0, org: i }), 1 !== n && (0, we.jsx)(iT, { type: (null === (e = s[n]) || void 0 === e ? void 0 : e.display) === vC.JL.Owners ? wC.zZ.Owner : wC.zZ.Subsidiary, dataSelector: (0, kM.SO)({ pageNumber: 0, orgId: o }), orgId: o, isPrintView: !0, secView: !1, tableProps: { requestParams: { pageNumber: n }, curry: !0 } }, "table-".concat(n)), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", p: 2, children: [(0, we.jsxs)(ln.A, { color: wM.Qs.Neutrals[700], variant: MM.Eq.caption, children: [i.name, " - Ownership Chart"] }), (0, we.jsxs)(ln.A, { color: wM.Qs.Neutrals[700], variant: MM.Eq.caption, children: ["Page ", n, "/", c] })] })] }) })] }) : (0, we.jsx)(we.Fragment, {}) },
            LI = a.lazy((() => (0, hn.D)((() => Promise.all([n.e(1802), n.e(9454), n.e(7824), n.e(4592)]).then(n.bind(n, 84592)))))),
            II = a.lazy((() => (0, hn.D)((() => Promise.all([n.e(7824), n.e(1229)]).then(n.bind(n, 41609)))))),
            jI = a.lazy((() => (0, hn.D)((() => n.e(3517).then(n.bind(n, 53517)))))),
            VI = a.lazy((() => (0, hn.D)((() => Promise.all([n.e(766), n.e(8076)]).then(n.bind(n, 18076)))))),
            OI = ["embed", "public", "community"],
            RI = ["embed", "public", "print", "community"],
            PI = e => { let { isAuthorized: t, isUnVerified: n, isReady: r, orgResourcesLoading: o } = e; const [i] = (0, un.A)("activeLicenseId", null, !0), l = (0, ae.d4)(EI), { redirect: s } = (0, Q.A)(), { pathname: c } = (0, $.useLocation)(), d = (0, re.u)([(0, ne.si)(), (0, ne.wi)()]), { params: { base: u, action: h } } = d || { params: {} }, m = "print" === u || "print" === h; if (function() { const e = (0, ae.wA)(),
                            t = (0, ae.d4)(NS.oL),
                            n = (0, ae.d4)(IH.A),
                            r = (0, ae.d4)(jH.A),
                            { openDialog: o } = (0, Ft.A)("alert"),
                            { openDialog: i } = (0, Ft.A)("upgradeRequiredDialog"),
                            { openDialog: l } = (0, Ft.A)("deactivatedAccount"),
                            { openDialog: s } = (0, Ft.A)("canceledAccount"),
                            { isExact: c } = (0, re.u)("/thankyou"),
                            { userHasMinAccess: d } = (0, Ut.A)(),
                            u = d(Wt.td.ADMIN),
                            h = (null === t || void 0 === t ? void 0 : t.license) || n;
                        (0, a.useEffect)((() => { var n; if (t) switch (t.type) {
                                case "invalidLicense":
                                    if ("canceled" === (null === h || void 0 === h ? void 0 : h.status)) { s(); break } l(); break;
                                case "invalidUser":
                                    l(); break;
                                case "upgradeRequired":
                                    if (c) return;
                                    i({ license: h, resources: t.resourcesOverLimit || [], isLicenseOverLimit: t.isOverLimit || !1, isOwnerOrAdmin: u, showAccountSwitcher: t.isOverLimit && (null === r || void 0 === r ? void 0 : r.length) > 1, accountOwnerEmail: null === h || void 0 === h || null === (n = h.user) || void 0 === n ? void 0 : n.username }); break;
                                default:
                                    { let n = t.title || (403 === t.responseCode ? "Not Allowed" : "Application Error");o({ btnText: t.btnText || "Ok", disableClose: t.disableClose || !1, message: t.message || "Something went wrong. <NAME_EMAIL>", title: n, cb: t.cb || (() => { e((0, NS.Z0)()) }) }) } } }), [t]) }(), t && !r) return (0, we.jsx)(gI, {}); if (t && !i && l && "/thankyou" !== c && !RI.includes(u)) return (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.Mz)(), component: () => (0, we.jsx)(CI.Ay, { children: (0, we.jsx)(qt.A, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.Mz)(), component: () => (0, we.jsx)(CI.Ay, { children: (0, we.jsx)(qt.A, {}) }) }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.iD)(), component: zt }), (0, we.jsx)($.Redirect, { to: "".concat((0, ne.Mz)(), "?autoselect=false") }), ";"] }); if (t && o) return (0, we.jsx)(dn.A, { loading: o, spinnerType: "block", spinnerSize: 30 }); const p = !RI.includes(u) && !RI.includes(h) && "/getstarted" !== c && "/thankyou" !== c && "/failed" !== c && "/welcome" !== c && "/forgotRequested" !== c; return window.parent.location !== window.location ? (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.si)(), render: e => { let { match: t } = e; const { params: { base: n } } = t || {}; return OI.includes(n) ? (0, we.jsx)(II, {}) : (0, we.jsx)($.Redirect, { to: (0, ne.mX)() }) } }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mX)(), component: LH }), (0, we.jsx)($.Redirect, { to: (0, ne.mX)() })] }) : l || !t || RI.includes(u) ? t ? s ? (0, we.jsx)($.Redirect, { to: s }) : (0, we.jsx)(CI.Ay, { children: (0, we.jsxs)(a.Suspense, { fallback: () => (0, we.jsx)(we.Fragment, {}), children: [p && (0, we.jsx)(Ct.A, {}), (0, we.jsx)(te, { isPrintScreen: m, children: (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.wi)({ base: "print", view: "table" }), children: (0, we.jsx)(RS.A, { children: (0, we.jsx)(HI, {}) }) }), (0, we.jsx)($.Route, { path: (0, ne.ve)(), component: VI }), (0, we.jsx)($.Route, { path: (0, ne.xv)(), component: VI }), (0, we.jsx)($.Route, { path: (0, ne._i)(), component: VI }), (0, we.jsx)($.Route, { path: (0, ne.eK)(), component: BH }), (0, we.jsx)($.Route, { path: (0, ne.SN)(), component: sI }), (0, we.jsx)($.Route, { path: (0, ne.gz)(), component: cI }), !l && (0, we.jsx)($.Route, { path: (0, ne.w4)(), component: jI }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mF)(), component: St }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.qM)(), component: TH }), (0, we.jsx)($.Route, { path: (0, ne.si)(), render: e => { let { match: t } = e; const { params: { base: n } } = t || {}; return OI.includes(n) ? (0, we.jsx)(II, {}) : (0, we.jsx)(LI, {}) } }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.ts)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.Io)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.OW)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.N9)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.wi)({ base: "protected" }), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.dH)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.uN)(), component: XT }), (0, we.jsx)($.Route, { path: (0, ne.K7)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.ze)(), component: XT }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mX)(), component: LH }), (0, we.jsx)($.Redirect, { to: (0, ne.ze)() })] }) })] }) }) : (0, we.jsx)(a.Suspense, { fallback: () => {}, children: (0, we.jsxs)($.Switch, { children: [(0, we.jsx)($.Route, { exact: !0, path: (0, ne.kz)(), component: wt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.iD)(), component: zt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.A$)(), component: At }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.yZ)(), component: kt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mF)(), component: St }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.wO)(), component: xt }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.qM)(), component: TH }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.si)(), render: e => { let { match: t } = e; const { params: { base: n } } = t || {}; return OI.includes(n) ? (0, we.jsx)(AI, { children: (0, we.jsx)(II, {}) }) : m ? (0, we.jsx)(LI, {}) : (0, we.jsx)($.Redirect, { to: (0, ne.iD)() }) } }), (0, we.jsx)($.Route, { exact: !0, path: (0, ne.mX)(), component: LH }), n && (0, we.jsx)($.Redirect, { to: (0, ne.A$)() }), (0, we.jsx)($.Redirect, { to: (0, ne.iD)() })] }) }) : (0, we.jsx)(jI, {}) }; var DI = n(54091),
            FI = n(1523),
            NI = n(23523),
            _I = n(40854),
            BI = n.n(_I); const WI = (e, t) => { let { error: n, errorInfo: r, pageInfo: a, lastAction: o, logType: i, e: l } = e; const s = function() { var e, t = navigator.userAgent,
                        n = t.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || []; return /trident/i.test(n[1]) ? { name: "IE", version: (e = /\brv[ :]+(\d+)/g.exec(t) || [])[1] || "" } : "Chrome" === n[1] && null != (e = t.match(/\bOPR|Edge\/(\d+)/)) ? { name: "Opera", version: e[1] } : (n = n[2] ? [n[1], n[2]] : [navigator.appName, navigator.appVersion, "-?"], null != (e = t.match(/version\/(\d+)/i)) && n.splice(1, 1, e[1]), { name: n[0], version: n[1] }) }(); let c = ""; if (l) try { if (c = l.toString(), "error: request aborted" === c.toLowerCase()) return } catch (l) {}
                let d = JSON.stringify({ logType: i, errorInfo: r || "no details", browserInfo: s || "", pageInfo: a || "", error: n || "", lastAction: o || "", exception: c });
                console.log("Browser Error: ", d), BI().post("/client_error", d, { headers: { Accept: "application/json", "Content-Type": "application/json" } }).catch((() => { t && "function" === typeof t && t() })) },
            UI = e => { let { onClose: t } = e; return (0, we.jsxs)("div", { className: "error-page", children: [t && (0, we.jsx)("span", { className: "close-btn", onClick: t, children: "\xd7" }), (0, we.jsx)("h3", { className: "text-center", children: "Something went wrong !" }), (0, we.jsx)("div", { className: "organimi-logo", children: (0, we.jsx)("img", { src: HH, alt: "organimi error image", width: "100" }) }), (0, we.jsxs)("p", { className: "text-center", children: ["Please Full Refresh your browser (Ctrl + Shift + R ) to ensure the latest issue is not caused by a new release.", (0, we.jsx)("br", {}), " Feel free to contact <NAME_EMAIL> if the issue persists!"] }), (0, we.jsx)("div", { className: "btn-bar", children: (0, we.jsxs)("div", { children: [(0, we.jsx)("a", { className: "e-link secondary", href: "https://organimi.zendesk.com/hc/en-us/requests/new", children: "Contact Support" }), (0, we.jsx)("a", { className: "e-link", onClick: () => { window.location.reload(!0) }, children: "Back to Organimi" })] }) })] }) };
        class qI extends a.Component { constructor(e) { super(e), this.state = { hasError: !1 } } static getDerivedStateFromError() { return { hasError: !0 } } componentDidMount() { window.onerror = e => { try { var t; const r = window.location.pathname,
                            a = (/register|login/.test(r), (null === e || void 0 === e ? void 0 : e.includes("ResizeObserver")) || (null === e || void 0 === e || null === (t = e.message) || void 0 === t || t.includes("ResizeObserver")), { error: e, errorInfo: "Non render error - async or event handler", pageInfo: { href: document.location.href } });
                        WI(a); try { ve.A.captureException({ exception: e }) } catch (n) {} } catch (r) {} } } componentDidCatch(e, t) { WI({ error: (e || {}).message || "", errorInfo: t, pageInfo: { href: document.location.href } }); try { ve.A.captureException({ exception: e }) } catch (n) {} } render() { return this.state.hasError ? (0, we.jsx)(UI, { onClose: this.props.onClose }) : this.props.children } }
        const GI = qI; var KI = n(9037);

        function ZI() { const e = (0, ae.wA)(),
                t = (0, ae.d4)(Fr.VW),
                n = (0, ae.d4)(Fr.AU),
                { id: r, username: o = "Anonymous", firstName: i = "Not set", lastName: l = "Not Set", licenses: s = [], goals: c } = t || {};
