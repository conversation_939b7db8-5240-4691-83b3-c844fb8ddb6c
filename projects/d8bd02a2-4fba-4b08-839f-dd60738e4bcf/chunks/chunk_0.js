

// === path.js ===

/*! For license information please see main.9e9a31d6.js.LICENSE.txt */
(() => { var e = { 34725: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/animal.ea0a0e852fae294ece205ac2391b2885.svg" }, 72416: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/emoticons.48ef0a08e68d576aa2e5595c28b3b3b5.svg" }, 46662: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/flags.0c7f14f9bf13aa6d3ff5e0f5ec1b82d2.svg" }, 27266: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/lego.86f27cbfca29fc38049fd3dcba88ce33.svg" }, 14106: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/silhouettes.b673f63265520e05817e6bc5368cd394.svg" }, 96382: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/user.8ea005632114c63d6b2fb0ead06cecaa.svg" }, 51836: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/noCharts.8447a394ddd085db43fe6a58ac7f42b7.svg" }, 60038: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/noPeople.9d13633a0201940c839b18b16c040edc.svg" }, 37259: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/vacantRole.1f6a6d8a989e3e5e9fa77f99c49b2d50.svg" }, 47483: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/azure.1b35c4b94eb8636170153be1f7c23862.svg" }, 12499: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/gdrive.060ed90587302f73a2d134eafdbaeece.svg" }, 52991: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/gsuite.92b8040a324c9dcebfa6dc9e128552ef.svg" }, 75995: (e, t, n) => { "use strict";
                n.d(t, { A: () => r });
                n(65043); const r = n.p + "static/media/organimi_chartlogo_small.d472b46032a15d49c2253c0340b5742f.svg" }, 87279: (e, t, n) => { "use strict";
                n.d(t, { K: () => s }); var r = n(69219),
                    a = n(66856),
                    o = n(61531),
                    i = n(37259),
                    l = n(70579);
                class s { constructor(e) { this.config = e } static createErrorHandler(e) { const t = /protected\/organizations\/\w+\/charts\/\w+\//.test(window.location.pathname),
                            n = window.location.pathname.includes("public/"),
                            r = window.location.pathname.includes("embed/"); return n ? new d(e) : r ? new u(e) : t ? new c(e) : new s(e) } handle400() { throw "handler not defined" } handle401() { throw "handler not defined" } handle403(e) { e() } handle404() { throw "handler not defined" } handle402() { throw "handler not defined" } handle410(e) { var t, n;
                        null === (t = this.config) || void 0 === t || null === (n = t.thunkAPI) || void 0 === n || n.dispatch((0, r.hR)(e)) } handle412() { window.localStorageSafe.removeItem("activeLicenseId"), window.location.assign("".concat((0, a.Mz)(), "?autoselect=false")) } handle429() { throw "handler not defined" } handle500() { throw "handler not defined" } } class c extends s { constructor(e) { super(e) } handle400() { throw "handler not defined" } handle401() { throw "handler not defined" } handle403(e) { e() } handle404() { throw "handler not defined" } handle402() { throw "handler not defined" } handle410(e) { var t, n;
                        null === (t = this.config) || void 0 === t || null === (n = t.thunkAPI) || void 0 === n || n.dispatch((0, r.hR)(e)) } handle412() { var e, t;
                        null === (e = this.config) || void 0 === e || null === (t = e.thunkAPI) || void 0 === t || t.dispatch((0, r.hR)({ btnText: "Dashboard", cb: () => { window.localStorageSafe.removeItem("activeLicenseId"), window.location.assign("".concat((0, a.Mz)(), "?autoselect=false")) }, title: "Chart Not Found", message: (0, l.jsxs)("div", { children: [(0, l.jsxs)(o.A, { p: 1, width: 400, children: ["Request a new link, or email", " ", (0, l.jsx)("a", { href: "mailto:<EMAIL>", children: "<EMAIL>" })] }), (0, l.jsx)(o.A, { my: 1, children: (0, l.jsx)("i", { children: "ERR-404" }) })] }), details: "SLS 403" })) } handle429() { throw "handler not defined" } handle500() { throw "handler not defined" } } class d extends s { constructor(e) { super(e) } handle400() { throw "handler not defined" } handle401() { throw "handler not defined" } handle403(e, t) { var n, a; if ("getOrgCfPolicy" === t) return e();
                        null === (n = this.config) || void 0 === n || null === (a = n.thunkAPI) || void 0 === a || a.dispatch((0, r.hR)({ btnText: "Organimi Home", title: "Chart Not Found", disableClose: !0, message: (0, l.jsxs)(o.A, { p: 1, width: 450, children: [(0, l.jsxs)(o.A, { display: "flex", alignItems: "center", gridGap: 32, flexDirection: "column", children: [(0, l.jsx)(o.A, { children: "Request a new link from the Organimi Chart Administrator who provided the link." }), (0, l.jsx)("img", { src: i.A, width: "100" }), (0, l.jsx)(o.A, { children: (0, l.jsx)("i", { children: "ERR-404" }) })] }), (0, l.jsxs)(o.A, { mt: 3, pt: 3, borderTop: "solid 1px #ddd", fontSize: 13, display: "flex", flexDirection: "column", gridGap: 16, children: [(0, l.jsxs)(o.A, { children: ["Have an Organimi account? ", (0, l.jsx)("a", { href: "/", children: "Click here to sign in" })] }), (0, l.jsxs)(o.A, { children: ["Reached this page in error?", " ", (0, l.jsx)("a", { href: "mailto:<EMAIL>", children: "Email <EMAIL>" })] })] })] }), details: "SLS 403" })) } handle404() { throw "handler not defined" } handle402() { throw "handler not defined" } handle410(e) { var t, n;
                        null === (t = this.config) || void 0 === t || null === (n = t.thunkAPI) || void 0 === n || n.dispatch((0, r.hR)(e)) } handle412() { throw "handler not defined" } handle429() { throw "handler not defined" } handle500() { throw "handler not defined" } } class u extends s { constructor(e) { super(e) } handle400() { throw "handler not defined" } handle401() { throw "handler not defined" } handle403(e, t) { var n, a; if ("getOrgCfPolicy" === t) return e();
                        null === (n = this.config) || void 0 === n || null === (a = n.thunkAPI) || void 0 === a || a.dispatch((0, r.hR)({ btnText: "Organimi Home", title: "Chart Not Found", disableClose: !0, message: (0, l.jsx)(o.A, { p: 1, width: 400, children: (0, l.jsxs)(o.A, { display: "flex", alignItems: "center", gridGap: 24, flexDirection: "column", children: [(0, l.jsx)(o.A, { children: "Contact the Organimi Administrator who setup this embedded chart" }), (0, l.jsx)("img", { src: i.A, width: "100" }), (0, l.jsx)(o.A, { children: (0, l.jsx)("i", { children: "ERR-404" }) })] }) }), details: "SLS 403" })) } handle404() { throw "handler not defined" } handle402() { throw "handler not defined" } handle412() { throw "handler not defined" } handle429() { throw "handler not defined" } handle500() { throw "handler not defined" } } }, 9787: (e, t, n) => { "use strict";
                n.d(t, { a: () => B }); var r = n(80907),
                    a = n(87769),
                    o = n(40854),
                    i = n.n(o); const l = { getCharts: function() { return i().get("/me/charts", this.config) }, getMe: function() { return i().get("/me", this.config) }, getMeLite: function() { return i().get("/profile", this.config) }, logout: function() { return i().post("/auth/logout", {}, this.config) }, login: function(e) { let { username: t, password: n, queryParams: r } = e; return i().post("/auth/login", { username: t, password: n }, { ...this.config, params: { ...r || {} } }) }, backToV6: function(e) { let { reason: t, rating: n, subject: r } = e; return i().post("/me/backtov6", { reason: t, rating: n, subject: r }, this.config) }, register: function(e) { let { firstName: t, lastName: n, username: r, password: a, queryParams: o } = e; return i().post("/auth/register", { firstName: t, lastName: n, username: r, password: a }, { ...this.config, params: { ...o || {} } }) }, forgotPassword: function(e) { let { email: t } = e; return i().post("/auth/passwordresets/request", { username: t }, this.config) }, resetPassword: function(e) { let { email: t, token: n, password: r } = e; return i().post("/auth/passwordresets/accept", { resetRequest: { username: t, token: n, password: r } }, this.config) }, updateProfile: function(e) { let { data: t } = e; return i().patch("/me/profile", { user: { ...t } }, this.config) }, changeEmail: function(e) { let { oldEmail: t, newEmail: n, verification: r } = e; return i().put("/me/email", { oldEmail: t, newEmail: n, verification: r }, this.config) }, changePassword: function(e) { let { current: t, newPassword: n, confirm: r } = e; return i().put("/me/password", { current: t, new: n, confirm: r }, this.config) }, deleteMyAccount: function() { return i().delete("/me/account/permanent", this.config) }, trackGoal: function(e) { let { goalId: t } = e; const n = "/me/goals/".concat(t); return i().post(n, {}, this.config) }, resendVerification: function() { return i().get("/auth/resendVerification", this.config) }, getEmailVerifyToken: function() { return i().get("/auth/autoVerifyToken", this.config) }, activateFree: function() { return i().post("/me/onboarding/plan/free", {}, this.config) }, createNewTrial: function(e) { let { employeeSize: t, planFrequency: n } = e; return i().post("/me/onboarding/plan/trial", { employeeSize: t, planFrequency: n }, this.config) } },
                    s = l; var c = n(14502),
                    d = n.n(c); const u = { getSimple: function(e) { let { orgId: t, singleUseToken: n } = e; const r = { ...this.config }; return n && (r.headers = { ...this.config.headers, "single-use-token": n }), i().get(d()().template("/organizations/:orgId/simple").param("orgId", t).toString(), r) }, get: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId").param("orgId", t).toString(), this.config) }, getCharts: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/charts").param("orgId", t).toString(), this.config) }, create: function(e) { let { licenseId: t, data: n } = e; return i().post(d()().template("licenses/:licenseId/organizations").param("licenseId", t).toString(), { organization: n }, this.config) }, update: function(e) { let { orgId: t, data: n } = e; return i().patch(d()().template("/organizations/:orgId").param("orgId", t).toString(), { organization: n }, this.config) }, remove: function(e) { let { orgId: t } = e; return i().delete(d()().template("/organizations/:orgId").param("orgId", t).toString(), this.config) }, uploadBase64: function(e) { let { orgId: t, type: n, data: r, chartId: a } = e; return i().post(d()().template("/organizations/:orgId/upload/:type").param("orgId", t).param("type", n).toString(), { data: r, chartId: a }, this.config) }, uploadBase64ToMember: function(e) { let { orgId: t, memberId: n, data: r } = e; return i().post(d()().template("/organizations/:orgId/upload/photo").param("orgId", t).param("autosave", !0).toString(), { data: r, memberId: n }, this.config) }, createSignedAttachmentUrl: function(e) { let { orgId: t, chartId: n, fileOptions: r } = e; return i().post(d()().template("/organizations/:orgId/attachments").param("orgId", t).toString(), { fileOptions: r, chartId: n }, this.config) }, checkIfThirdPartyCredentialsExist: function(e) { let { orgId: t, provider: n } = e; return i().get(d()().template("/organizations/:orgId/provider/:provider").param("orgId", t).param("provider", n).toString(), this.config) }, getAccountsAvailable: function(e) { let { orgId: t, provider: n } = e; return i().get(d()().template("/organizations/:orgId/provider/:provider/accounts").param("orgId", t).param("provider", n).toString(), this.config) }, deleteThirdPartyCredentials: function(e) { let { orgId: t, provider: n } = e; return i().delete(d()().template("/organizations/:orgId/provider/:provider").param("orgId", t).param("provider", n).toString(), this.config) } },
                    h = { getByOrganization: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/members").param("orgId", t).toString(), this.config) }, get: function(e) { let { orgId: t, memberId: n } = e; return i().get(d()().template("/organizations/:orgId/members/:memberId").param("orgId", t).param("memberId", n).toString(), this.config) }, create: function(e) { let { orgId: t, data: n } = e; return i().post(d()().template("/organizations/:orgId/members").param("orgId", t).toString(), { ...n }, this.config) }, update: function(e) { let { orgId: t, memberId: n, data: r } = e; return i().put(d()().template("/organizations/:orgId/members/:memberId").param("orgId", t).param("memberId", n).toString(), { ...r }, this.config) }, delete: function(e) { let { orgId: t, memberId: n } = e; return i().delete(d()().template("/organizations/:orgId/members/:memberId").param("orgId", t).param("memberId", n).toString(), this.config) } }; var m = n(78396),
                    p = n(22908),
                    f = n.n(p); const v = { get: function(e) { let { orgId: t, chartId: n, mode: r, pId: a } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId").param("orgId", t).param("chartId", n).query("mode", r).query("pId", a).toString(), this.config) }, create: function(e) { let { orgId: t, data: n } = e; return i().post(d()().template("/organizations/:orgId/charts").param("orgId", t).toString(), { ...n }, this.config) }, createAliasLink: function(e) { let { orgId: t, chartId: n, roleId: r, name: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/alias").param("orgId", t).param("chartId", n).toString(), { roleId: r, name: a }, this.config) }, update: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().patch(d()().template("/organizations/:orgId/charts/:chartId").param("orgId", t).param("chartId", n).toString(), { chart: { ...r } }, this.config) }, delete: function(e) { let { data: t } = e; return i().delete(d()().template("/me/charts").toString(), { data: { charts: t }, ...this.config }) }, getRoles: function(e) { var t, n; let { orgId: r, chartId: a } = e; const o = f().parse(null === (t = window) || void 0 === t || null === (n = t.location) || void 0 === n ? void 0 : n.search, { ignoreQueryPrefix: !0 }); return i().get(d()().template("/organizations/:orgId/charts/:chartId/roles").param("orgId", r).param("chartId", a).query("pageSize", m.Ay.maxLimits.apiDataSize).query("pId", null === o || void 0 === o ? void 0 : o.pId).toString(), this.config) }, getRolesSilent: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/roles").param("orgId", t).param("chartId", n).query("pageSize", m.Ay.maxLimits.apiDataSize).toString(), this.config) }, refreshRoles: function(e) { var t, n; let { orgId: r, chartId: a } = e; const o = f().parse(null === (t = window) || void 0 === t || null === (n = t.location) || void 0 === n ? void 0 : n.search, { ignoreQueryPrefix: !0 }); return i().get(d()().template("/organizations/:orgId/charts/:chartId/roles").param("orgId", r).param("chartId", a).query("pageSize", m.Ay.maxLimits.apiDataSize).query("pId", null === o || void 0 === o ? void 0 : o.pId).toString(), this.config) }, exportChart: function(e) { let { orgId: t, chartId: n, type: r, dataType: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/exports/chart").param("orgId", t).param("chartId", n).toString(), { dataType: a, exportTo: r }, this.config) }, downloadChartFile: function(e) { let { orgId: t, chartId: n, type: r, fileKey: a } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/chart/:type/download").param("orgId", t).param("chartId", n).param("type", r).param("file", a).toString(), { ...this.config, responseType: "blob" }) }, copyMove: function(e) { let { orgId: t, chartId: n, chartName: r, organizationId: a, options: o, copy: l = !0 } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/duplicate").param("orgId", t).param("chartId", n).toString(), { chartName: r, organizationId: a, copyMode: l ? "sameOrg" : "diffOrg", copyOptions: { ...o } }, this.config) }, copyMoveSubChart: function(e) { let { orgId: t, chartId: n, chartName: r, organizationId: a, options: o } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/duplicate").param("orgId", t).param("chartId", n).toString(), { chartName: r, organizationId: a, copyMode: "subChart", copyOptions: { ...o } }, this.config) }, getJobStatus: function(e) { let { orgId: t, chartId: n, jobId: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/duplicate/jobs/:jobId").param("orgId", t).param("chartId", n).param("jobId", r).toString(), this.config) }, cancelJobStatus: function(e) { let { orgId: t, chartId: n, jobId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/duplicate/jobs/:jobId").param("orgId", t).param("chartId", n).param("jobId", r).toString(), this.config) }, createDemo: function(e) { let { activeLicense: t } = e; return i().put(d()().template("/demo/charts/").toString(), { activeLicense: t }, this.config) }, sharePublic: function(e) { let { orgId: t, chartId: n, access: r, theme: a, publicShareType: o, createNew: l } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/share/public").param("orgId", t).param("chartId", n).toString(), { access: r, theme: a, createNew: l, publicShareType: o }, this.config) }, deletePublicShareLink: function(e) { let { orgId: t, chartId: n, publicId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/share/public/:publicId").param("orgId", t).param("chartId", n).param("publicId", r).toString(), this.config) }, updatePublicShareLink: function(e) { let { orgId: t, chartId: n, publicId: r, data: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/share/public/:publicId").param("orgId", t).param("chartId", n).param("publicId", r).toString(), { ...a }, this.config) }, shareIFrame: function(e) { let { orgId: t, chartId: n, domains: r, theme: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/share/iframe").param("orgId", t).param("chartId", n).toString(), { domains: r, theme: a }, this.config) }, getChartIntegration: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/integration").param("orgId", t).param("chartId", n).toString(), this.config) }, getChartTemplates: function() { return i().get(d()().template("/charts/templates").toString(), this.config) }, migrateFormat: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/migrateformat").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, getLinkedCharts: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/linkedcharts").param("orgId", t).param("chartId", n).toString(), this.config) }, linkChartToSalesforce: function(e) { let { orgId: t, chartId: n, sfAccountId: r, data: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/salesforce/:sfAccountId").param("orgId", t).param("chartId", n).param("sfAccountId", r).toString(), { ...a }, this.config) }, unlinkChartFromSalesforce: function(e) { let { orgId: t, chartId: n, sfAccountId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/salesforce/:sfAccountId").param("orgId", t).param("chartId", n).param("sfAccountId", r).toString(), this.config) } },
                    g = v,
                    y = { get: function(e) { let { orgId: t, personId: n } = e; return i().get(d()().template("/organizations/:orgId/people/:personId").param("orgId", t).param("personId", n).toString(), this.config) } },
                    b = { deleteUsers: function(e) { let { orgId: t, users: n } = e; return i().delete(d()().template("/organizations/:orgId/users").param("orgId", t).toString(), { data: { users: n }, ...this.config }) }, deleteChartUsers: function(e) { let { orgId: t, chartId: n, users: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/users").param("orgId", t).param("chartId", n).toString(), { data: { users: r }, ...this.config }) }, getChartUsers: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/users").param({ orgId: t, chartId: n }).toString(), this.config) }, getOrganizationUsers: function(e) { let { orgId: t, select: n } = e; return i().get(d()().template("/organizations/:orgId/users").param({ orgId: t, select: n }).toString(), this.config) }, createUser: function(e) { let { type: t, users: n, orgId: r, organizations: a, charts: o, message: l, licenseId: s, theme: c, settings: u, silentInvite: h } = e; return i().post(d()().template("/organizations/:orgId/users").param("orgId", r).toString(), { type: t, users: n, organizations: a, licenseId: s, charts: o, message: l, theme: c, settings: u, silentInvite: h }, this.config) }, updateUser: function(e) { let { type: t, orgId: n, email: r, organizations: a, charts: o, message: l, licenseId: s } = e; return i().patch(d()().template("/organizations/:orgId/users/email/:email").param("orgId", n).param("email", r).toString(), { type: t, organizations: a, charts: o, message: l, licenseId: s }, this.config) }, getUserWithPerms: function(e) { let { orgId: t, email: n } = e; return i().get(d()().template("/organizations/:orgId/users/email/:email").param("orgId", t).param("email", n).toString(), this.config) }, removeChartAccess: function(e) { let { orgId: t, chartId: n } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/removeaccess").param("orgId", t).param("chartId", n).toString(), this.config) } },
                    w = { getPeoplePaginated: function(e) { let { orgId: t, page: n, pageSize: r, sort: a, search: o } = e; return i().get(d()().template("/organizations/:orgId/people").param({ orgId: t, page: n, pageSize: r, sort: a, search: o }).toString(), this.config) }, getPeople: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/people").param("pageSize", m.Ay.maxLimits.apiDataSize).param("orgId", t).toString(), this.config) }, getPeopleForPhotos: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/people").param("pageSize", m.Ay.maxLimits.apiDataSize).param("orgId", t).toString(), this.config) }, getPeopleInChart: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/people").param("orgId", t).param("chartId", n).toString(), this.config) }, deletePeople: function(e) { let { orgId: t, data: n } = e; return i().delete(d()().template("/organizations/:orgId/people").param("orgId", t).toString(), { data: { people: n }, ...this.config }) }, addPerson: function(e) { let { orgId: t, data: n, chartId: r, forceDuplicate: a } = e; return i().post(d()().template("/organizations/:orgId/people").param("orgId", t).toString(), { person: { ...n }, chartId: r, forceDuplicate: a }, this.config) }, updatePerson: function(e) { let { orgId: t, personId: n, data: r, chartId: a } = e; return i().patch(d()().template("/organizations/:orgId/people/:personId").param("orgId", t).param("personId", n).toString(), { person: { ...r }, chartId: a }, this.config) }, getPerson: function(e) { let { orgId: t, personId: n } = e; return i().get(d()().template("/organizations/:orgId/people/:personId").param("orgId", t).param("personId", n).toString(), this.config) }, getRolesByPerson: function(e) { let { orgId: t, personId: n } = e; return i().get(d()().template("/organizations/:orgId/people/:personId/roles").param("orgId", t).param("personId", n).toString(), this.config) }, exportRoster: function(e) { let { orgId: t, type: n } = e; return i().get(d()().template("/organizations/:orgId/exports/roster/:type").param("orgId", t).param("type", n).toString(), this.config) }, exportPhotos: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/exports/photos").param("orgId", t).toString(), this.config) }, downloadPhotosZipFile: function(e) { let { orgId: t, fileKey: n } = e; return i().get(d()().template("/organizations/:orgId/exports/photos/download").param("orgId", t).param("file", n).toString(), { ...this.config, responseType: "blob" }) }, downloadRosterFile: function(e) { let { orgId: t, type: n, fileKey: r } = e; return i().get(d()().template("/organizations/:orgId/exports/roster/:type/download").param("orgId", t).param("type", n).param("file", r).toString(), { ...this.config, responseType: "blob" }) }, bulkUpdate: function(e) { let { orgId: t, data: n } = e; return i().patch(d()().template("/organizations/:orgId/spreadsheet/people").param("orgId", t).toString(), { ...n }, this.config) } },
                    z = { get: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId").param("orgId", t).param("chartId", n).toString(), this.config) }, create: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/roles").param("orgId", t).param("chartId", n).toString(), { ...r, returnAllChanged: !0 }, this.config) }, update: function(e) { var t; let { orgId: n, chartId: r, data: a } = e; return i().patch(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId").param("roleId", null === a || void 0 === a || null === (t = a.role) || void 0 === t ? void 0 : t.id).param("orgId", n).param("chartId", r).toString(), { ...a, returnAllChanged: !0 }, this.config) }, bulkUpdate: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().patch(d()().template("/organizations/:orgId/charts/:chartId/spreadsheet/roles").param("orgId", t).param("chartId", n).toString(), { ...r, returnAllChanged: !0 }, this.config) }, removeRole: function(e) { let { orgId: t, chartId: n, roleId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId").param({ orgId: t, chartId: n, roleId: r }).toString(), this.config) }, removeRoleRecursive: function(e) { let { orgId: t, chartId: n, roleId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/recursive").param({ orgId: t, chartId: n, roleId: r }).toString(), this.config) }, dropPeople: function(e) { let { orgId: t, chartId: n, roleId: r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/dropPeople").param({ orgId: t, chartId: n, roleId: r }).toString(), {}, this.config) }, assignPeople: function(e) { let { orgId: t, chartId: n, roleId: r, data: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/assignPeople").param({ orgId: t, chartId: n, roleId: r }).toString(), { ...a }, this.config) }, getRoleConnections: function(e) { let { orgId: t, chartId: n, roleId: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/connections").param("orgId", t).param("roleId", r).param("chartId", n).toString(), this.config) }, changeManager: function(e) { let { orgId: t, roleId: n, managerId: r } = e; return i().put(d()().template("/organizations/:orgId/roles/:roleId/changeManager").param("orgId", t).param("roleId", n).toString(), { managerId: r }, this.config) }, moveRoleInChart: function(e) { var t; let { orgId: n, chartId: r, data: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/move").param("roleId", null === a || void 0 === a || null === (t = a.role) || void 0 === t ? void 0 : t.id).param("orgId", n).param("chartId", r).toString(), { ...a, returnAllChanged: !0 }, this.config) }, mergeChartLink: function(e) { let { orgId: t, chartId: n, roleId: r } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/mergeChartLink").param("roleId", r).param("orgId", t).param("chartId", n).toString(), { returnAllChanged: !0 }, this.config) }, duplicateMatrixStructure: function(e) { let { orgId: t, chartId: n, roleId: r, roleType: a, data: o } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/roles/:roleId/:roleType/duplicate").param("roleId", r).param("orgId", t).param("chartId", n).param("roleType", a).toString(), { ...o, returnAllChanged: !0 }, this.config) } },
                    x = { findPeoplePublic: function(e) { let { query: t = "", type: n = "organimi", entity: r = "person" } = e; return i().get(d()().template("/public/search/people").param("query", t).param("type", n).param("entity", r).toString(), this.config) }, findPeople: function(e) { let { orgId: t, chartId: n, query: r, fields: a } = e; return i().get(d()().template("/search/people").param({ orgId: t, chartId: n, fields: a || "name", query: r }).toString(), this.config) }, findRoles: function(e) { let { orgId: t, chartId: n, query: r, fields: a } = e; return i().get(d()().template("/search/roles").param({ orgId: t, chartId: n, fields: a || "role.name", query: r }).toString(), this.config) } },
                    A = { config: {}, getThemes: function() { return i().get(d()().template("/themes").toString(), this.config) }, update: function(e) { let { orgId: t, themeId: n, data: r } = e; return i().patch(d()().template("/:orgId/themes/:themeId").param("themeId", n).param("orgId", t).toString(), { orgId: t, theme: r }, this.config) }, setActive: function(e) { let { orgId: t, themeId: n, chartId: r, mode: a } = e; return i().put(d()().template("/:orgId/themes/:themeId/active").param("themeId", n).param("orgId", t).toString(), { mode: a, orgId: t, chartId: r }, this.config) }, create: function(e) { let { orgId: t, data: n, licenseId: r } = e; return i().post(d()().template("/:orgId/themes/").param("orgId", t).toString(), { orgId: t, licenseId: r, theme: n }, this.config) }, delete: function(e) { let { themeId: t, orgId: n } = e; return i().delete(d()().template("/:orgId/themes/:themeId").param("themeId", t).param("orgId", n).toString(), { data: { orgId: n }, ...this.config }) } },
                    k = { getAvailableFields: function() { return i().get(d()().template("/fields/all").toString(), this.config) }, getCustomFields: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/fields").param("orgId", t).toString(), this.config) }, updateCustomField: function(e) { let { orgId: t, fieldId: n, data: r } = e; return i().patch(d()().template("/organizations/:orgId/fields/:fieldId").param("orgId", t).param("fieldId", n).toString(), { field: r }, this.config) }, createCustomField: function(e) { let { orgId: t, data: n } = e; return i().post(d()().template("/organizations/:orgId/fields").param("orgId", t).toString(), { field: { ...n } }, this.config) }, deleteCustomField: function(e) { let { orgId: t, fieldId: n } = e; return i().delete(d()().template("/organizations/:orgId/fields/:fieldId").param("orgId", t).param("fieldId", n).toString(), this.config) } },
                    S = { config: {}, getPrintTemplates: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/printTemplates").param("orgId", t).toString(), this.config) }, get: function(e) { let { orgId: t, templateId: n } = e; return i().get(d()().template("/organizations/:orgId/printTemplates/:templateId").param("orgId", t).param("templateId", n).toString(), this.config) }, create: function(e) { let { orgId: t, data: n } = e; return i().post(d()().template("/organizations/:orgId/printTemplates").param("orgId", t).toString(), { template: n }, this.config) }, update: function(e) { let { orgId: t, templateId: n, data: r, skipRePrint: a = !1 } = e; return i().patch(d()().template("/organizations/:orgId/printTemplates/:templateId").param("orgId", t).param("templateId", n).toString(), { template: r, skipRePrint: a }, this.config) }, delete: function(e) { let { orgId: t, templateId: n } = e; return i().delete(d()().template("/organizations/:orgId/printTemplates/:templateId").param("orgId", t).param("templateId", n).toString(), this.config) }, addPrintJob: function(e) { let { orgId: t, chartId: n, type: r, data: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/exports").param("orgId", t).param("chartId", n).param("type", r).toString(), { ...a }, this.config) }, addPrintJobForResource: function(e) { let { orgId: t, resourceType: n, data: r } = e; return i().post(d()().template("/organizations/:orgId/resources/:resourceType/exports").param("orgId", t).param("resourceType", n).toString(), { ...r }, this.config) }, getPrintJobStatus: function(e) { let { orgId: t, chartId: n, jobId: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/jobs/:jobId").param("orgId", t).param("chartId", n).param("jobId", r).toString(), this.config) }, getPrintJobSettings: function(e) { let { orgId: t, chartId: n, jobId: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/settings").param("orgId", t).param("chartId", n).param("jobId", r).toString(), this.config) }, cancelPrintJob: function(e) { let { orgId: t, chartId: n, jobId: r } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/exports/jobs/:jobId").param("orgId", t).param("chartId", n).param("jobId", r).param("request", "cancel").toString(), {}, this.config) }, getInProgressPrintJobs: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/inprogress").param("orgId", t).param("chartId", n).toString(), this.config) }, getInProgressResourcePrintJobs: function(e) { let { orgId: t, resourceType: n } = e; return i().get(d()().template("/organizations/:orgId/resources/:resourceType/exports/inprogress").param("orgId", t).param("resourceType", n).toString(), this.config) }, getLatestCompletedResourcePrint: function(e) { let { orgId: t, resourceType: n } = e; return i().get(d()().template("/organizations/:orgId/resources/:resourceType/exports/latest").param("orgId", t).param("resourceType", n).toString(), this.config) }, getPrintHistory: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/history").param("orgId", t).param("chartId", n).toString(), this.config) }, updateSaveStatusOfFileInPrintHistory: function(e) { let { orgId: t, chartId: n, historyId: r, saveFile: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/exports/history/:historyId/save").param("orgId", t).param("chartId", n).param("historyId", r).toString(), { saveFile: a }, this.config) }, deletePrintHistory: function(e) { let { orgId: t, chartId: n, historyId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/exports/history/:historyId").param("orgId", t).param("chartId", n).param("historyId", r).toString(), this.config) }, updateFileNameInPrintHistory: function(e) { let { orgId: t, chartId: n, historyId: r, fileName: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/exports/history/:historyId/filename").param("orgId", t).param("chartId", n).param("historyId", r).toString(), { fileName: a }, this.config) }, addPrintJobPublic: function(e) { let { orgId: t, chartId: n, historyId: r, isPublicPrint: a, printTemplate: o, publicId: l } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/exports/public/:pId/history/:historyId/print").param("orgId", t).param("chartId", n).param("pId", l).param("historyId", r).toString(), { isPublicPrint: a, printTemplate: o }, this.config) }, getPublicPrintFiles: function(e) { let { orgId: t, chartId: n, pId: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/exports/public/:pId/history").param("orgId", t).param("chartId", n).param("pId", r).toString(), this.config) } },
                    M = { endTrial: function(e) { let { licenseId: t } = e; return i().post(d()().template("/licenses/:licenseId/endTrial").param("licenseId", t).toString(), {}, this.config) }, cancelPlan: function(e) { let { licenseId: t, cancelRequest: n, surveyResponse: r } = e; return i().delete(d()().template("/licenses/:licenseId").param("licenseId", t).toString(), { data: { cancelRequest: n, surveyResponse: r }, ...this.config }) }, getBilling: function(e) { let { licenseId: t } = e; return i().get(d()().template("/licenses/:licenseId/billing").param("licenseId", t).toString(), this.config) }, getCustomer: function(e) { let { licenseId: t } = e; return i().get(d()().template("/licenses/:licenseId/customer").param("licenseId", t).toString(), this.config) }, updateCustomer: function(e) { let { licenseId: t, data: n } = e; return i().put(d()().template("/licenses/:licenseId/customer").param("licenseId", t).toString(), { data: { ...n } }, this.config) }, getUpgradeCost: function(e) { let { licenseId: t, planId: n } = e; return i().get(d()().template("/licenses/:licenseId/upgrade/:planId/price").param("licenseId", t).param("planId", n).toString(), this.config) }, createCheckoutSession: function(e) { let { licenseId: t, planId: n, countryCode: r, provinceCode: a, prorationDate: o, source: l = "" } = e; return i().post(d()().template("/licenses/:licenseId/upgrade").param("licenseId", t).toString(), { subscription: { planId: n, countryCode: r, provinceCode: a, prorationDate: o }, source: l }, this.config) }, createNewPlanCheckout: function(e) { let { employeeSize: t, planFrequency: n, planType: r, country: a, province: o } = e; return i().post(d()().template("/me/onboarding/plan/checkout").toString(), { employeeSize: t, planFrequency: n, planType: r, country: a, province: o }, this.config) }, createUpdateBillingSession: function(e) { let { licenseId: t, planId: n } = e; return i().post(d()().template("/licenses/:licenseId/updatebilling").param("licenseId", t).toString(), { subscription: { planId: n } }, this.config) }, createCustomerPortalSession: function(e) { let { licenseId: t } = e; return i().post(d()().template("/licenses/:licenseId/customerportal").param("licenseId", t).toString(), {}, this.config) }, checkLicenseActivationStatus: function(e) { let { licenseId: t, sessionId: n } = e; return i().get(d()().template("/licenses/:licenseId/activate/:sessionId/status").param("licenseId", t).param("sessionId", n).toString(), this.config) }, activateLicense: function(e) { let { licenseId: t, sessionId: n } = e; return i().post(d()().template("/licenses/:licenseId/activate").param("licenseId", t).toString(), { subscription: { sessionId: n } }, this.config) }, updateAllowedLoginMethods: function(e) { let { licenseId: t, allowedLoginMethods: n } = e; return i().patch(d()().template("/licenses/:licenseId/login").param("licenseId", t).toString(), { allowedLoginMethods: n }, this.config) }, updateMetadata: function(e) { let { licenseId: t, data: n } = e; return i().patch(d()().template("/licenses/:licenseId/metadata").param("licenseId", t).toString(), { ...n }, this.config) } },
                    E = { getAvailableIntegrations: function() { return i().get(d()().template("/me/integrations").toString(), this.config) }, getIntegrations: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/integrations").param("orgId", t).toString(), this.config) }, validate: function(e) { let { orgId: t, importData: n, columnMap: r, options: a } = e; return i().post(d()().template("/organizations/:orgId/imports/validate").param("orgId", t).toString(), { importData: n, columnMap: r, options: a }, this.config) }, import: function(e) { let { orgId: t, importData: n, columns: r, exclusionRules: a, structuredFields: o, autoBuild: l, options: s } = e; return i().post(d()().template("/organizations/:orgId/imports").param("orgId", t).toString(), { autoBuild: l, importData: n, columns: r, structuredFields: o, exclusionRules: a, options: s }, this.config) }, getImportJob: function(e) { let { orgId: t, jobId: n, type: r = "get_data_integration" } = e; return i().get(d()().template("/organizations/:orgId/imports/jobs/:jobId").param("orgId", t).param("jobId", n).param("type", r).toString(), this.config) }, cancelImportJob: function(e) { let { orgId: t, jobId: n } = e; return i().delete(d()().template("/organizations/:orgId/imports/jobs/:jobId").param("orgId", t).param("jobId", n).toString(), this.config) }, checkAvailableData: function(e) { let { orgId: t, integrationType: n, options: r, dontAdvanceToNextStep: a = !1, connection: o = {} } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/checkdata").param("orgId", t).param("integrationType", n).toString(), { options: { ...r, integrationOptions: { ...r.integrationOptions, exclusionRules: { ...r.integrationOptions.exclusionRules, companies: r.integrationOptions.exclusionRules.companies || {}, teams: r.integrationOptions.exclusionRules.teams || {}, departments: r.integrationOptions.exclusionRules.departments || {} } } }, dontAdvanceToNextStep: a, connection: o }, this.config) }, getAvailableData: function(e) { let { orgId: t, integrationType: n, columnMap: r, options: a, tryToAutoFixErrors: o = !0, connection: l = {} } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/data").param("orgId", t).param("integrationType", n).toString(), { options: { ...a, integrationOptions: { ...a.integrationOptions, exclusionRules: { ...a.integrationOptions.exclusionRules, companies: a.integrationOptions.exclusionRules.companies || {}, teams: a.integrationOptions.exclusionRules.teams || {}, departments: a.integrationOptions.exclusionRules.departments || {} } } }, columnMap: r, tryToAutoFixErrors: o, connection: l }, this.config) }, getIntegration: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), this.config) }, getIntegrationLogs: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType/logs").param("orgId", t).param("integrationType", n).toString(), this.config) }, updateIntegrationOptions: function(e) { let { orgId: t, integrationType: n, options: r } = e; return i().patch(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), { ...r }, this.config) }, updateChartIntegration: function(e) { let { options: t, orgId: n, chartId: r, integrationType: a } = e; return i().patch(d()().template("/organizations/:orgId/chart/:chartId/integrations/:integrationType").param("orgId", n).param("chartId", r).param("integrationType", a).toString(), { ...t }, this.config) }, updateSFTPConnection: function(e) { let { connection: t, orgId: n, chartIntegrationId: r } = e; return i().put(d()().template("/organizations/:orgId/chartIntegrations/:chartIntegrationId/connection").param("orgId", n).param("chartIntegrationId", r).toString(), { connection: t }, this.config) }, refreshChartIntegration: function(e) { let { orgId: t, chartId: n, integrationType: r } = e; return i().patch(d()().template("/organizations/:orgId/chart/:chartId/integrations/:integrationType/refresh").param("orgId", t).param("chartId", n).param("integrationType", r).toString(), {}, this.config) }, refreshRosterIntegration: function(e) { let { orgId: t, integrationType: n } = e; return i().put(d()().template("/organizations/:orgId/integrations/:integrationType/refresh-roster").param("orgId", t).param("integrationType", n).toString(), {}, this.config) }, deleteChartIntegration: function(e) { let { orgId: t, chartId: n, integrationType: r } = e; return i().delete(d()().template("/organizations/:orgId/chart/:chartId/integrations/:integrationType").param("orgId", t).param("chartId", n).param("integrationType", r).toString(), this.config) }, deleteIntegration: function(e) { let { orgId: t, integrationType: n } = e; return i().delete(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), this.config) }, checkSyncWorkerStatus: function() { return i().get("/status/sync", this.config) } },
                    C = { getIntegrations: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/integrations").param("orgId", t).toString(), this.config) }, getIntegration: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), this.config) }, getIntegrationLogs: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType/logs").param("orgId", t).param("integrationType", n).toString(), this.config) }, updateIntegrationOptions: function(e) { let { orgId: t, integrationType: n, params: r } = e; return i().put(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), { ...r }, this.config) }, checkSyncWorkerStatus: function() { return i().get("/status/sync", this.config) }, getIntegrationDataOverview: function(e) { let { chartId: t, orgId: n, inclusions: r, integrationType: a, fileLocation: o, selectedFileId: l, externalDataSource: s, uploadType: c } = e; const u = {}; return o && (u.fileLocation = o), l && (u.selectedFileId = l), c && (u.uploadType = c), s && (u.externalDataSource = s), t && (u.chartId = t), r && (u.inclusions = r), i().get(d()().template("/organizations/:orgId/integrations/:integrationType/overview").param("orgId", n).param("integrationType", a).param(u).toString(), this.config) }, createInitIntegration: function(e) { let { orgId: t, integrationType: n, setup: r } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), { ...r }, this.config) }, createChartIntegration: function(e) { let { orgId: t, integrationType: n, uploadType: r, setup: a } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/:uploadType").param("orgId", t).param("integrationType", n).param("uploadType", r).toString(), { ...a }, this.config) }, createRosterIntegration: function(e) { let { orgId: t, integrationType: n, setup: r } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/roster").param("orgId", t).param("integrationType", n).toString(), { ...r }, this.config) }, invokeRosterIntegration: function(e) { let { orgId: t, integrationType: n, chartIntegrationId: r } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/roster/:chartIntegrationId/invoke").param("orgId", t).param("integrationType", n).param("chartIntegrationId", r).toString(), {}, this.config) }, invokePhotoIntegration: function(e) { let { orgId: t, integrationType: n, chartIntegrationId: r } = e; return i().post(d()().template("/organizations/:orgId/integrations/:integrationType/photos/:chartIntegrationId/invoke").param("orgId", t).param("integrationType", n).param("chartIntegrationId", r).toString(), {}, this.config) }, getFilesAvailable: function(e) { let { orgId: t, integrationType: n, query: r } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType/files").param("orgId", t).param("integrationType", n).param("fileLocation", r).toString(), this.config) }, getIntegrationConnectionInfo: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType/auth/connection").param("orgId", t).param("integrationType", n).toString(), this.config) }, updateIntegrationConnection: function(e) { let { orgId: t, integrationType: n, setup: r } = e; return i().put(d()().template("/organizations/:orgId/integrations/:integrationType/auth/connection").param("orgId", t).param("integrationType", n).toString(), { connection: r }, this.config) }, removeIntegration: function(e) { let { orgId: t, integrationType: n } = e; return i().delete(d()().template("/organizations/:orgId/integrations/:integrationType").param("orgId", t).param("integrationType", n).toString(), this.config) }, removeChartIntegration: function(e) { let { orgId: t, chartIntegrationId: n, integrationType: r } = e; return i().delete(d()().template("/organizations/:orgId/chartIntegrations/:chartIntegrationId").param("orgId", t).param("chartIntegrationId", n).param("integrationType", r).toString(), this.config) }, invokeChartIntegration: function(e) { let { orgId: t, chartId: n, integrationType: r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/integrations/:integrationType/invoke").param("orgId", t).param("chartId", n).param("integrationType", r).toString(), {}, this.config) }, updateChartIntegration: function(e) { let { orgId: t, chartIntegrationId: n, integrationType: r, name: a, value: o } = e; return i().patch(d()().template("/organizations/:orgId/chartIntegrations/:chartIntegrationId/integrations/:integrationType/:propertyName").param("orgId", t).param("chartIntegrationId", n).param("propertyName", a).param("integrationType", r).toString(), { value: o }, this.config) }, updateIntegration: function(e) { let { orgId: t, integrationType: n, name: r, value: a } = e; return i().put(d()().template("/organizations/:orgId/integrations/:integrationType/:propertyName").param("orgId", t).param("propertyName", r).param("integrationType", n).toString(), { value: a }, this.config) }, testConnection: function(e) { let { orgId: t, integrationType: n } = e; return i().get(d()().template("/organizations/:orgId/integrations/:integrationType/testconnection").param("orgId", t).param("integrationType", n).toString(), this.config) } },
                    T = { createLegend: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/legends").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, copyLegend: function(e) { let { orgId: t, chartId: n, sync: r, sourceChartId: a } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/legends/copy").param("orgId", t).param("chartId", n).toString(), { sync: r, sourceChartId: a }, this.config) }, updateLegend: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().patch(d()().template("/organizations/:orgId/charts/:chartId/legends").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, addUpdateRule: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/legends/rules").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, updateFolderName: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/legends/rules/folder").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, deleteRule: function(e) { let { orgId: t, chartId: n, ruleId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/legends/rules/:ruleId").param("orgId", t).param("chartId", n).param("ruleId", r).toString(), this.config) }, updateLegendRulePositionAndFolderName: function(e) { let { orgId: t, chartId: n, ruleId: r, data: a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/legends/rules/:ruleId/position").param("orgId", t).param("chartId", n).param("ruleId", r).toString(), { ...a }, this.config) }, deleteFolderName: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/legends/:legend/delete/foldername/:folder").param("orgId", t).param("chartId", n).param("folder", r.folder).param("legend", r.id).toString(), this.config) }, deleteFolder: function(e) { let { orgId: t, chartId: n, data: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/legends/:legend/delete/folder/:folder").param("orgId", t).param("chartId", n).param("folder", r.folder).param("legend", r.id).toString(), this.config) } },
                    H = { getCommunityBuild: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/community").param("orgId", t).param("chartId", n).toString(), this.config) }, getCommunityBuildSafe: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/communitySafe").param("orgId", t).param("chartId", n).toString(), this.config) }, createCommunityObject: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/createCommunity").param("orgId", t).param("chartId", n).toString(), this.config) }, removeCommunityObject: function(e) { let { orgId: t, chartId: n } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/removeCommunity").param("orgId", t).param("chartId", n).toString(), this.config) }, updateCommunityBuildObject: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/updateCommunity").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, getCommunityBuildAccessUrl: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/accessUrl").param("orgId", t).param("chartId", n).toString(), this.config) }, getCommunityBuildAccessToken: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/accessToken").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, getCommunityBuildAuth: function(e) { let { orgId: t, chartId: n, token: r } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/communityBuildAuth").param("orgId", t).param("chartId", n).toString(), { ...this.config, headers: { ...this.config.headers, "single-use-token": r } }) }, communityBuildSearch: function(e) { let { orgId: t, chartId: n, email: r, token: a } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/communityBuildSearch").param("orgId", t).param("chartId", n).param("email", r).toString(), { ...this.config, headers: { ...this.config.headers, "single-use-token": a } }) }, createSmartRole: function(e) { let { orgId: t, chartId: n, token: r, ...a } = e; return i().put(d()().template("/organizations/:orgId/charts/:chartId/createSmartRole").param("orgId", t).param("chartId", n).toString(), { ...a }, { ...this.config, headers: { ...this.config.headers, "single-use-token": r } }) }, findPeoplePublicInCommunityBuild: function(e) { let { orgId: t, chartId: n, token: r, query: a = "", type: o = "organimi", entity: l = "person" } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/search/people/communityBuild").param("orgId", t).param("chartId", n).param("query", a).param("type", o).param("entity", l).toString(), { ...this.config, headers: { ...this.config.headers, "single-use-token": r } }) }, uploadBase64CommunityBuild: function(e) { let { orgId: t, type: n, data: r, chartId: a } = e; return i().post(d()().template("/organizations/:orgId/upload/communityBuild/:type").param("orgId", t).param("type", n).toString(), { data: r, chartId: a }, this.config) } },
                    L = { getGovernanceChart: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/getGovernanceChart").param("orgId", t).param("chartId", n).toString(), this.config) }, addMemberToGovernanceChart: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/addMemberToGovernanceChart").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, updateGovernanceChartObject: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/updateGovernanceChart").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, addNewCommittee: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/addNewCommittee").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, updateCommittee: function(e) { let { orgId: t, chartId: n, ...r } = e; return i().post(d()().template("/organizations/:orgId/charts/:chartId/editCommittee").param("orgId", t).param("chartId", n).toString(), { ...r }, this.config) }, getAllBoardMembers: function(e) { let { orgId: t, chartId: n } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/getAllBoardMembers").param("orgId", t).param("chartId", n).toString(), this.config) }, deleteMemberWithType: function(e) { let { orgId: t, chartId: n, memberId: r, memberType: a } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/gc/member/:memberId/:memberType").param("orgId", t).param("chartId", n).param("memberId", r).param("memberType", a).toString(), this.config) }, deleteCommittee: function(e) { let { orgId: t, chartId: n, committeeId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/gc/committee/:committeeId").param("orgId", t).param("chartId", n).param("committeeId", r).toString(), this.config) }, deleteCommitteeCharter: function(e) { let { orgId: t, code: n } = e; return i().delete(d()().template("/organizations/:orgId/attachments/:code").param("orgId", t).param("code", n).toString(), this.config) } },
                    I = { getReportMetrics: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/reports/metrics").param("orgId", t).toString(), this.config) }, getOrgReports: function(e) { let { orgId: t, startDate: n, endDate: r } = e; return i().get(d()().template("/organizations/:orgId/reports").param("orgId", t).param("startDate", n).param("endDate", r).toString(), this.config) }, getChartReports: function(e) { let { orgId: t, chartId: n, startDate: r, endDate: a } = e; return i().get(d()().template("/organizations/:orgId/charts/:chartId/reports").param("orgId", t).param("chartId", n).param("startDate", r).param("endDate", a).toString(), this.config) }, getReportConfig: function(e) { let { orgId: t } = e; return i().get(d()().template("/organizations/:orgId/reports/config").param("orgId", t).toString(), this.config) }, updateReportConfig: function(e) { let { orgId: t, model: n, prop: r, value: a } = e; return i().patch(d()().template("/organizations/:orgId/reports/config/:model/:prop").param("orgId", t).param("model", n).param("prop", r).toString(), { value: a }, this.config) }, exportOrgReport: function(e) { return i().post(d()().template("/organizations/:orgId/reports/export").param("orgId", null === e || void 0 === e ? void 0 : e.orgId).toString(), e, this.config) }, exportChartReport: function(e) { return i().post(d()().template("/organizations/:orgId/charts/:chartId/reports/export").param("orgId", null === e || void 0 === e ? void 0 : e.orgId).param("chartId", null === e || void 0 === e ? void 0 : e.chartIds[0]).toString(), e, this.config) } }; var j = n(69219),
                    V = n(66856); const O = { createAPIKey: function(e) { let { licenseId: t, name: n = "Organimi API", description: r = "" } = e; return i().post("/licenses/".concat(t, "/clients"), { licenseId: t, name: n, description: r }, this.config) }, reGenerateClientSecret: function(e) { let { licenseId: t, apiId: n } = e; return i().post("/licenses/".concat(t, "/clients/").concat(n, "/keys"), {}, this.config) }, getAPIClients: function(e) { let { licenseId: t } = e; return i().get("/licenses/".concat(t, "/clients/list"), this.config) }, deleteApiClient: function(e) { let { licenseId: t, apiId: n } = e; return i().delete("/licenses/".concat(t, "/clients/").concat(n), this.config) }, getLogs: function(e) { let { licenseId: t, apiId: n, startDate: r, endDate: a, scope: o, responseCode: l, reqMethod: s, page: c, pageSize: u, sort: h } = e; return i().get(d()().template("/licenses/:licenseId/clients/:apiId/logs").param({ licenseId: t, apiId: n, startDate: r, endDate: a, scope: o, responseCode: l, reqMethod: s, page: c, pageSize: u, sort: h }).toString(), this.config) }, createSupportRequest: function(e) { let { licenseId: t, message: n } = e; return i().post("/licenses/".concat(t, "/clients/support"), { message: n }, this.config) } },
                    R = { createNewWebhook: function(e) { let { licenseId: t, name: n = "Organimi Webhook", description: r = "", url: a, triggerEvents: o } = e; return i().post("/licenses/".concat(t, "/webhooks"), { licenseId: t, name: n, url: a, triggerEvents: o, description: r }, this.config) }, getWebhooks: function(e) { let { licenseId: t } = e; return i().get("/licenses/".concat(t, "/webhooks/list"), this.config) }, deleteWebhook: function(e) { let { licenseId: t, webhookId: n } = e; return i().delete("/licenses/".concat(t, "/webhooks/").concat(n), this.config) }, getLogs: function(e) { let { licenseId: t, webhookId: n } = e; return i().get("/licenses/".concat(t, "/webhooks/").concat(n, "/logs"), this.config) }, getUsage: function(e) { let { licenseId: t, webhookId: n } = e; return i().get("/licenses/".concat(t, "/webhooks/").concat(n, "/usage"), this.config) } }; var P = n(86942); const D = { getSPConfig: function(e) { let { licenseId: t } = e; return i().get(d()().template("/licenses/:licenseId/saml/sp/config").param("licenseId", t).param("sp", !0).toString(), this.config) }, getConfigByLicense: function(e) { let { licenseId: t } = e; return i().get(d()().template("/licenses/:licenseId/saml/config").param("licenseId", t).toString(), this.config) }, getConfigByCompanyAlias: function(e) { let { licenseId: t, companyAlias: n } = e; return i().get(d()().template("/licenses/:licenseId/saml/config/alias/:companyAlias").param("licenseId", t).param("companyAlias", n).toString(), this.config) }, createConfig: function(e) { let { licenseId: t, companyAlias: n, ssoURL: r, entityId: a, cert: o, wantReqsSigned: l, nameIdFormat: s, disableRequestedAuthnContext: c } = e; return i().post(d()().template("/licenses/:licenseId/saml/config").param("licenseId", t).toString(), { companyAlias: n, ssoURL: r, entityId: a, cert: o, wantReqsSigned: l, nameIdFormat: s, disableRequestedAuthnContext: c }, this.config) }, updateConfig: function(e) { let { licenseId: t, configId: n, companyAlias: r, ssoURL: a, entityId: o, cert: l, wantReqsSigned: s, nameIdFormat: c, disableRequestedAuthnContext: u } = e; return i().patch(d()().template("/licenses/:licenseId/saml/config/:configId").param("licenseId", t).param("configId", n).toString(), { companyAlias: r, ssoURL: a, entityId: o, cert: l, wantReqsSigned: s, nameIdFormat: c, disableRequestedAuthnContext: u }, this.config) }, deleteConfig: function(e) { let { licenseId: t, configId: n } = e; return i().delete(d()().template("/licenses/:licenseId/saml/config/:configId").param("licenseId", t).param("configId", n).toString(), this.config) }, deleteAllLicenseConfigs: function(e) { let { licenseId: t } = e; return i().delete(d()().template("/licenses/:licenseId/saml/config").param("licenseId", t).toString(), this.config) } },
                    F = { deletePublicLink: function(e) { let { orgId: t, publicId: n, chartId: r } = e; return i().delete(d()().template("/organizations/:orgId/charts/:chartId/share/public/:publicId").param("orgId", t).param("chartId", r).param("publicId", n).toString(), this.config) } }; var N = n(87279); const _ = { users: s, userManagement: b, organizations: u, members: h, charts: g, profiles: y, people: w, roles: z, search: x, themes: A, fields: k, print: S, license: M, import: E, integration: C, legends: T, talentPools: { getTalentPool: () => {} }, communityBuild: H, boardOfDirectors: L, reports: I, client: O, webhook: R, token: P.A, saml: D, access: F, config: a.A }; const B = e => { let { slice: t, scope: n } = e; return Object.keys(_[n]).reduce(((e, a) => (e[a] = (0, r.zD)("".concat(t, "/").concat(a), (async (e, t) => { var r, o, i; let l; try { l = N.K.createErrorHandler({ thunkAPI: t, options: e }) } catch (p) { console.log({ e: p, ERROR: "YES" }) } const { onError: s, ignoreErrorHandler: c, ...d } = e || {}; try { const e = await _[n][a].call(_, d),
                                { data: t } = e || {}; return t } catch (p) { var u, h;
                            console.log({ e: p }); const e = null === (u = p.response) || void 0 === u ? void 0 : u.status,
                                d = [403, 402, 429].includes(e); var m; if (d && p.response.data && (p.response.data.errorHandled = !0), !d && s) s(null === p || void 0 === p || null === (m = p.response) || void 0 === m ? void 0 : m.data, e);
                            else if (!c || d) switch (e) {
                                case 401:
                                    window.parent.location !== window.location ? window.location.assign((0, V.a5)()) : window.location.assign((0, V.iD)()); break;
                                case 400:
                                case 402:
                                    t.dispatch((0, j.hR)(p.response.data)); break;
                                case 429:
                                    t.dispatch((0, j.hR)({ status: 429, message: "Too many requests" })); break;
                                case 403:
                                    null === (r = l) || void 0 === r || r.handle403((() => t.dispatch((0, j.hR)(p.response.data)))); break;
                                case 404:
                                    t.dispatch((0, j.hR)({ name: "".concat(n, "/").concat(a), message: "Resource not found", cb: () => { window.location.assign((0, V.ze)()) } })); break;
                                case 410:
                                    null === (o = l) || void 0 === o || o.handle410(p.response.data); break;
                                case 412:
                                    null === (i = l) || void 0 === i || i.handle412(); break;
                                case 406:
                                    window.location.assign((0, V.A$)()); break;
                                case 500:
                                    t.dispatch((0, j.hR)({ name: "".concat(n, "/").concat(a), cb: () => { window.location.assign((0, V.ze)()) } })) }
                            return t.rejectWithValue({ ...(null === (h = p.response) || void 0 === h ? void 0 : h.data) || {} }) } })), e)), {}) } }, 87769: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(59177); const a = window.location != window.parent.location ? document.referrer : document.location.href,
                    o = (document.location.ancestorOrigins || [])[0] || a,
                    i = { baseURL: window.location.origin + "/api/v7" || 0, headers: { "Content-Type": "application/json", "Content-Referrer": o }, transformRequest: [function(e, t) { var n; const a = window.localStorageSafe.getItem("activeLicenseId"),
                                o = (0, r.xn)(a);
                            o && (t["active-license-id"] = o); const i = new URLSearchParams(window.location.search).get("pId"),
                                l = window.localStorageSafe.getItem("token") || "{}",
                                s = null === (n = (0, r.xn)(l)) || void 0 === n ? void 0 : n.token; return i && (t["public-id"] = i), s && (t.Authorization = s), JSON.stringify(e) }], withCredentials: !1, responseType: "json", responseEncoding: "utf8", xsrfCookieName: "XSRF-TOKEN", xsrfHeaderName: "X-XSRF-TOKEN", onUploadProgress: function() {}, onDownloadProgress: function() {}, maxContentLength: 2e3, maxBodyLength: 2e3, validateStatus: function(e) { return e >= 200 && e < 300 } } }, 86942: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r = n(14502),
                    a = n.n(r),
                    o = n(40854),
                    i = n.n(o); const l = { getToken: function() { return i().get(a()().template("/auth/token").toString(), this.config) }, getPublicToken: function(e) { let { jobId: t, pId: n, cbId: r } = e; return i().get(a()().template("/auth/token/public").param("jobId", t).param("pId", n).param("cbId", r).toString(), this.config) } } }, 47730: (e, t, n) => { "use strict";
                n.d(t, { B: () => P }); var r = n(80907),
                    a = n(59177); const o = { baseURL: "https://".concat("us", "-bg.organimi.com/").concat("prod"), headers: { "Content-Type": "application/json" }, transformRequest: [function(e, t) { const n = window.localStorageSafe.getItem("activeLicenseId"),
                            r = (0, a.xn)(n); let o;
                        r && (t["active-license-id"] = r); try { var i;
                            o = null === (i = JSON.parse(window.localStorageSafe.getItem("token") || {})) || void 0 === i ? void 0 : i.token, t.Authorization = o } catch (l) { window.localStorageSafe.removeItem("token") } return JSON.stringify(e) }], responseEncoding: "utf8", xsrfCookieName: "XSRF-TOKEN", xsrfHeaderName: "X-XSRF-TOKEN", maxContentLength: 2e3, maxBodyLength: 2e3, validateStatus: function(e) { return e >= 200 && e < 300 } }; var i = n(14502),
                    l = n.n(i),
                    s = n(40854),
                    c = n.n(s); const d = { config: {}, getJobStatus: function(e) { let { jobId: t } = e; return c().get(l()().template("/status/print").param("jobId", t).toString(), this.config) }, getPrintJobSettings: function(e) { let { jobId: t } = e; return c().get(l()().template("/print/exports/settings").param("jobId", t).toString(), this.config) } },
                    u = { getPeople: function(e) { let { orgId: t, jobId: n } = e; return c().get(l()().template("/people").param("orgId", t).param("jobId", n).toString(), this.config) }, getPerson: function(e) { let { orgId: t, personId: n } = e; return c().get(l()().template("/people/:personId").param("personId", n).param("orgId", t).toString(), this.config) } },
                    h = { get: function(e) { let { chartId: t, jobId: n } = e; return c().get(l()().template("/charts/:chartId").param("chartId", t).param("jobId", n).toString(), this.config) }, getRoles: function(e) { let { chartId: t, jobId: n } = e; return c().get(l()().template("/roles").param("chartId", t).param("jobId", n).toString(), this.config) }, getChildrenRoles: function(e) { let { chartId: t, roleId: n } = e; return c().get(l()().template("/roles/children").param("chartId", t).param("roleId", n).toString(), this.config) }, searchRoles: function(e) { let { chartId: t, query: n } = e; return c().get(l()().template("/charts/:chartId/roles/search").param("chartId", t).param("query", n).toString(), this.config) }, refreshDynamicFieldValues: function(e) { let { chartId: t, orgId: n } = e; return c().post(l()().template("fields/organization/:orgId/chart/:chartId/processFields").param("orgId", n).param("chartId", t).toString(), {}, this.config) }, getChartInsights: function(e) { let { chartId: t } = e; return c().get(l()().template("/charts/:chartId/insights").param("chartId", t).toString(), this.config) }, runChartInsights: function(e) { let { chartId: t } = e; return c().post(l()().template("/charts/:chartId/insights").param("chartId", t).toString(), {}, this.config) }, confirmChartInsights: function(e) { let { chartId: t, includeGaps: n = !0 } = e; return c().post(l()().template("/charts/:chartId/suggestions/all").param("chartId", t).toString(), { includeGaps: n }, this.config) }, removeAutobuildChart: function(e) { let { chartId: t } = e; return c().delete(l()().template("/charts/:chartId/suggestions/all").param("chartId", t).toString(), this.config) }, updateChartSuggestion: function(e) { let { chartId: t, suggestionId: n, parentRoleId: r, action: a } = e; return c().put(l()().template("/charts/:chartId/suggestions").param("chartId", t).toString(), { parentRoleId: r, suggestionId: n, action: a }, this.config) } },
                    m = { config: {}, getLicenseThemes: function(e) { let { licenseId: t } = e; return c().get(l()().template("/licenses/:licenseId/themes").param("licenseId", t).toString(), this.config) }, getPrintThemes: function(e) { let { jobId: t } = e; return c().get(l()().template("/themes/print").param("jobId", t).toString(), this.config) }, getPublicChartTheme: function(e) { let { chartId: t, pId: n } = e; return c().get(l()().template("/themes/public/:chartId").param("chartId", t).param("pId", n).toString(), this.config) } },
                    p = { get: function(e) { let { orgId: t, jobId: n } = e; return c().get(l()().template("/organizations/:orgId").param("orgId", t).param("jobId", n).toString(), this.config) }, create: function(e) { let { licenseId: t, data: n } = e; return c().post(l()().template("/organizations").toString(), { licenseId: t, org: n }, this.config) }, update: function(e) { let { orgId: t, data: n } = e; return c().patch(l()().template("/organizations/:orgId").param("orgId", t).toString(), { org: n }, this.config) }, delete: function(e) { let { orgId: t } = e; return c().delete(l()().template("/organizations/:orgId").param("orgId", t).toString(), this.config) }, getCharts: function(e) { let { orgId: t, jobId: n } = e; return c().get(l()().template("/charts").param("orgId", t).param("jobId", n).toString(), this.config) }, search: function(e) { let { orgId: t, searchEntity: n, searchQuery: r } = e; return c().get(l()().template("/organizations/:orgId/search/:searchEntity").param("orgId", t).param("searchEntity", n).param("searchQuery", r).toString(), this.config) }, getOrgCfPolicy: function(e) { let { orgId: t, resourceType: n = "photos" } = e; return c().get(l()().template("/organizations/:orgId/cfPolicy").param("orgId", t).param("resourceType", n).toString(), this.config) }, copyCustomFields: function(e) { let { orgId: t, sourceOrgId: n, fieldIds: r } = e; return c().post(l()().template("fields/organization/:orgId/copyFields").param("orgId", t).toString(), { fieldIds: r, sourceOrgId: n }, this.config) }, getCopyableOrgFields: function(e) { let { orgId: t } = e; return c().get(l()().template("fields/organization/:orgId/copyableFields").param("orgId", t).toString(), this.config) } },
                    f = { getJobStatus: function(e) { let { jobId: t } = e; return c().get(l()().template("/status/integration").param("jobId", t).toString(), this.config) }, getGroupsAvailable: function(e) { let { orgId: t, integrationType: n, groupFilter: r } = e; return c().get(l()().template("/integrations/:integrationType/organization/:orgId/groups").param("integrationType", n).param("orgId", t).param("groupFilter", r).toString(), this.config) }, createInitIntegration: function(e) { let { integrationType: t, orgId: n, apiKey: r, userId: a, apiRequestDomain: o = "", externalApiCredentials: i = {} } = e; return c().post("/integrations/initIntegration", { integrationType: t, orgId: n, apiKey: r, userId: a, apiRequestDomain: o, externalApiCredentials: i }, this.config) }, updateIntegrationConnection: function(e) { let { integrationType: t, orgId: n, setup: r } = e; return c().post("/integrations/updateExternalApiCredentials", { integrationType: t, orgId: n, apiKey: r.apiKey, apiRequestDomain: null === r || void 0 === r ? void 0 : r.apiRequestDomain, externalApiCredentials: null === r || void 0 === r ? void 0 : r.externalApiCredentials }, this.config) }, autobuildChartImport: function(e) { return c().post(l()().template("/integrations/organizations/:orgId/imports/autobuild").param("orgId", e.orgId).toString(), e, this.config) } },
                    v = { getUsage: function(e) { let { licenseId: t } = e; return c().get(l()().template("/licenses/:licenseId/usage").param("licenseId", t).toString(), this.config) }, allowAllLoginMethods: function(e) { let { licenseId: t } = e; return c().get(l()().template("/licenses/:licenseId/login/allowAll").param("licenseId", t).toString(), this.config) }, getSubscriptionInfo: function(e) { let { licenseId: t } = e; return c().get(l()().template("/licenses/:licenseId/subscription").param("licenseId", t).toString(), this.config) }, requestAccess: function(e) { let { email: t } = e; return c().get(l()().template("/licenses/requestAccess").param("email", t).toString(), this.config) }, acceptAccess: function(e) { let { licenseId: t, token: n } = e; return c().post(l()().template("/licenses/:licenseId/invites/:token").param("licenseId", t).param("token", n).toString(), {}, this.config) }, rejectAccess: function(e) { let { licenseId: t, token: n } = e; return c().delete(l()().template("/licenses/:licenseId/invites/:token").param("licenseId", t).param("token", n).toString(), this.config) } },
                    g = { getChartPermits: function(e) { let { orgId: t, chartId: n } = e; return c().get(l()().template("/access/organizations/:orgId/charts/:chartId").param("chartId", n).param("orgId", t).toString(), this.config) }, createGenericPermit: function(e) { let { orgId: t, chartId: n, policyType: r, data: a = {} } = e; return c().post(l()().template("/access/organizations/:orgId/charts/:chartId/policy/:policyType").param("chartId", n).param("orgId", t).param("policyType", r).toString(), { ...a }, this.config) }, updateGenericPermit: function(e) { let { orgId: t, chartId: n, policyType: r, data: a = {} } = e; return c().patch(l()().template("/access/organizations/:orgId/charts/:chartId/policy/:policyType").param("chartId", n).param("orgId", t).param("policyType", r).toString(), { ...a }, this.config) }, removeGenericPermit: function(e) { let { orgId: t, chartId: n, policyType: r } = e; return c().delete(l()().template("/access/organizations/:orgId/charts/:chartId/policy/:policyType").param("chartId", n).param("orgId", t).param("policyType", r).toString(), this.config) }, getPublicAccessDetails: function(e) { let { orgId: t, chartId: n, pId: r } = e; return c().get(l()().template("/access/organizations/:orgId/charts/:chartId/public").param("orgId", t).param("chartId", n).param("pId", r).toString(), this.config) }, validatePasswordForLink: function(e) { let { orgId: t, chartId: n, pId: r, password: a } = e; return c().post(l()().template("/access/organizations/:orgId/charts/:chartId/public/validatePassword").param("orgId", t).param("chartId", n).toString(), { pId: r, password: a }, this.config) }, updateLinkSecurity: function(e) { let { publicId: t, type: n, password: r, ipCidrs: a, orgId: o, chartId: i } = e; return c().patch(l()().template("/access/organizations/:orgId/charts/:chartId/policy/publicurl/security").param("orgId", o).param("chartId", i).toString(), { pId: t, type: n, password: r, ipCidrs: a }, this.config) }, disableLinkSecurity: function(e) { let { publicId: t, orgId: n, chartId: r } = e; return c().delete(l()().template("/access/organizations/:orgId/charts/:chartId/policy/publicurl/security").param("orgId", n).param("chartId", r).toString(), { data: { pId: t }, ...this.config }) } },
                    y = { getLicenses: function() { return c().get(l()().template("/licenses").toString(), this.config) }, getPublicLicense: function(e) { let { pId: t, jobId: n, cbId: r } = e; return c().get(l()().template("/licenses/public").param("pId", t).param("jobId", n).param("cbId", r).toString(), this.config) }, getOrganizations: function() { return c().get(l()().template("/organizations").toString(), this.config) }, getUserTours: function(e) { let { orgId: t, utilizeTemplateTour: n } = e; return c().get(l()().template("/user/tour/pending").param("orgId", t).param("utilizeTemplateTour", n).toString(), this.config) }, updateUserProgress: function(e) { let { orgId: t, data: n } = e; return c().post(l()().template("/user/tour").param("orgId", t).toString(), { ...n }, this.config) } },
                    b = { getDuplicatePeople: function(e) { let { licenseId: t, orgId: n } = e; return c().get(l()().template("/cleanup/license/:licenseId/organization/:orgId/duplicates").param("licenseId", t).param("orgId", n).toString(), this.config) }, mergeDuplicatePeople: function(e) { let { licenseId: t, orgId: n, data: r = [] } = e; return c().post(l()().template("/cleanup/license/:licenseId/organization/:orgId/duplicates").param("licenseId", t).param("orgId", n).toString(), { data: r }, this.config) }, getUnassignedPeople: function(e) { let { licenseId: t } = e; return c().get(l()().template("/cleanup/license/:licenseId/unassigned").param("licenseId", t).toString(), this.config) }, getOrgUnassignedPeople: function(e) { let { licenseId: t, orgId: n } = e; return c().get(l()().template("/cleanup/license/:licenseId/organization/:orgId/unassigned").param("licenseId", t).param("orgId", n).toString(), this.config) }, deleteUnassignedPeople: function(e) { let { licenseId: t, data: n = [] } = e; return c().delete(l()().template("/cleanup/license/:licenseId/unassigned").param("licenseId", t).toString(), { data: { memberIds: n }, ...this.config }) }, deleteOrgUnassignedPeople: function(e) { let { licenseId: t, orgId: n, data: r = [] } = e; return c().delete(l()().template("/cleanup/license/:licenseId/organization/:orgId/unassigned").param("licenseId", t).param("orgId", n).toString(), { data: { memberIds: r }, ...this.config }) }, getCharts: function(e) { let { licenseId: t } = e; return c().get(l()().template("/cleanup/license/:licenseId/charts").param("licenseId", t).toString(), this.config) }, deleteCharts: function(e) { let { licenseId: t, data: n = [] } = e; return c().delete(l()().template("/cleanup/license/:licenseId/charts").param("licenseId", t).toString(), { data: { chartIds: n }, ...this.config }) }, getFields: function(e) { let { licenseId: t } = e; return c().get(l()().template("/cleanup/license/:licenseId/fields").param("licenseId", t).toString(), this.config) }, deleteFields: function(e) { let { licenseId: t, data: n = [] } = e; return c().delete(l()().template("/cleanup/license/:licenseId/fields").param("licenseId", t).toString(), { data: { fields: n }, ...this.config }) }, getOwnersAndAdmins: function(e) { let { licenseId: t } = e; return c().get(l()().template("/cleanup/license/:licenseId/ownersAndAdmins").param("licenseId", t).toString(), this.config) }, deleteOwnersAndAdmins: function(e) { let { licenseId: t, data: n = [] } = e; return c().delete(l()().template("/cleanup/license/:licenseId/ownersAndAdmins").param("licenseId", t).toString(), { data: { ownersAndAdmins: n }, ...this.config }) } }; var w = n(86942); const z = { getRole: function(e) { let { roleId: t, chartId: n } = e; return c().get(l()().template("/roles/:roleId").param("roleId", t).param("chartId", n).toString(), this.config) }, getConnections: function(e) { let { roleId: t, chartId: n } = e; return c().get(l()().template("/roles/:roleId/connections").param("roleId", t).param("chartId", n).toString(), this.config) } },
                    x = { all: function(e) { let { lastNotificationId: t } = e; return c().get(l()().template("/notification/all").param("lastNotificationId", t).toString(), this.config) }, updateReadStatus: function(e) { let { notificationIdsToMarkAsRead: t } = e; return c().put(l()().template("/notification/read").toString(), { notificationIdsToMarkAsRead: t }, this.config) }, getDialogContent: function(e) { let { notificationId: t } = e; return c().get(l()().template("/notification/dialogcontent").param("notificationId", t).toString(), this.config) } },
                    A = { enableAutoSnapshots: function(e) { let { orgId: t } = e; return c().post(l()().template("/snapshots/:orgId/config").param("orgId", t).toString(), {}, this.config) }, disableAutoSnapshots: function(e) { let { orgId: t } = e; return c().delete(l()().template("/snapshots/:orgId/config").param("orgId", t).toString(), this.config) }, takeSnapshot: function(e) { let { orgId: t } = e; return c().post(l()().template("/snapshots/:orgId/snapshots").param("orgId", t).toString(), {}, this.config) }, getSnapshotsList: function(e) { let { orgId: t } = e; return c().get(l()().template("/snapshots/:orgId/snapshots").param("orgId", t).toString(), this.config) }, deleteSnapshot: function(e) { let { orgId: t, snapshotIds: n } = e; return c().delete(l()().template("/snapshots/:orgId/snapshots").param("orgId", t).toString(), { data: { snapshotIds: n }, ...this.config }) }, restoreSnapshot: function(e) { let { orgId: t, snapshotId: n, chartIds: r } = e; return c().post(l()().template("/snapshots/:orgId/snapshots/:snapshotId").param("orgId", t).param("snapshotId", n).toString(), { chartIds: r }, this.config) } },
                    k = { getActionLogReport: function(e) { let { licenseId: t, filters: n = {} } = e; return c().post(l()().template("/reports/license/:licenseId/audit/report").param("licenseId", t).toString(), { filters: n }, this.config) }, getActionLogs: function(e) { let { licenseId: t, filters: n = {}, page: r, pageSize: a, sort: o } = e; return c().post(l()().template("/reports/license/:licenseId/audit/logs").param("licenseId", t).param("page", r).param("pageSize", a).param("sort", o).toString(), { filters: n }, this.config) }, exportActionLogs: function(e) { let { licenseId: t, filters: n = {} } = e; return c().post(l()().template("/reports/license/:licenseId/audit/export").param("licenseId", t).toString(), { filters: n }, this.config) } }; var S = n(38355); const M = { markUserDeactivated: function(e) { let { userId: t, deactivatedUser: n } = e; return c().post(l()().template("/user/lifecycle/deactivated").toString(), { userId: t, deactivatedUser: n }, this.config) }, markUserActivated: function(e) { let { userId: t } = e; return c().post(l()().template("/user/lifecycle/activate").toString(), { userId: t }, this.config) } }; const E = { parseTitlesBulk: function(e) { let { orgId: t, jobTitles: n } = e; return c().post(l()().template("/jtp/parse/bulk").param("orgId", t).toString(), { jobTitle: n }, this.config) } }; var C = n(87769),
                    T = n(69219),
                    H = n(66856),
                    L = n(78396),
                    I = n(87279); const j = { getTemplates: function(e) { let { env: t, branded: n, orgId: r } = e; return c().get(l()().template("/templates/published").param("env", t).param("branded", n).param("orgId", r).toString(), this.config) }, restoreTemplate: function(e) { let { templateId: t, inclusions: n, orgId: r, chartIds: a, chartName: o = null } = e; return c().post(l()().template("/templates/:templateId/restore").param("templateId", t).param("orgId", r).toString(), { orgId: r, chartIds: a, inclusions: n, chartName: o }, this.config) }, viewTemplate: function(e) { let { templateId: t, orgId: n } = e; return c().patch(l()().template("/templates/:templateId/viewed").param("templateId", t).param("orgId", n).toString(), {}, this.config) } }; var V = n(70579); const O = { access: g, print: d, integration: f, people: u, charts: h, roles: z, organizations: p, themes: m, license: v, users: y, notification: x, cleanup: b, snapshots: A, audit: k, templates: j, ownership: S.Ay, userlifecycle: M, jtp: E, config: o },
                    R = async () => { var e, t; const n = (() => { var e; const t = window.localStorageSafe.getItem("token"); let n = null; try { n = t ? JSON.parse(t) : null } catch (r) { window.localStorageSafe.removeItem("token") } return null === (e = n) || void 0 === e ? void 0 : e.type })(); let r; if (n === L.em.PASSWORD) return window.localStorageSafe.removeItem("token"), window.location.reload(); if (n && n !== L.em.SESSION || (r = await w.A.getToken.call({ config: C.A }, {})), n === L.em.PUBLIC) { const e = new URLSearchParams(window.location.search).get("pId");
                            r = await w.A.getPublicToken.call({ config: C.A }, { pId: e }) } const a = null === (e = r) || void 0 === e || null === (t = e.data) || void 0 === t ? void 0 : t.token; if (!a) throw window.localStorageSafe.removeItem("token"), { response: { status: 500 } }; return window.localStorageSafe.setItem("token", JSON.stringify({ token: a, savedAt: new Date })), a }, P = e => { let { slice: t, scope: n } = e; return Object.keys(O[n]).reduce(((e, a) => (e[a] = (0, r.zD)("".concat(t, "/").concat(a), (async (e, t) => { const r = I.K.createErrorHandler({ thunkAPI: t, options: e, api: O }),
                                { onError: o, ignoreErrorHandler: i, ...l } = e || {}; try { const e = await O[n][a].call(O, l),
                                    { data: t } = e || {}; if (null !== t && void 0 !== t && t.fetchResponse && null !== t && void 0 !== t && t.signedUrl) { const e = await c().get(t.signedUrl),
                                        { data: n } = e || {}; return n } return t } catch (h) { var s, d; const e = null === (s = h.response) || void 0 === s ? void 0 : s.status,
                                    c = -1 !== [403, 402, 429].indexOf(e); var u; if (c && h.response.data && (h.response.data.errorHandled = !0), !c && o) o(null === h || void 0 === h || null === (u = h.response) || void 0 === u ? void 0 : u.data, e);
                                else if (!i || c) switch (e) {
                                    case 401:
                                        window.location.assign((0, H.iD)()); break;
                                    case 400:
                                    case 402:
                                        t.dispatch((0, T.hR)(h.response.data)); break;
                                    case 429:
                                        t.dispatch((0, T.hR)({ status: 429, message: "Too many requests" })); break;
                                    case 410:
                                        r.handle410(h.response.data); break;
                                    case 403:
                                        r.handle403((async () => { try { await R(); const e = await O[n][a].call(O, l),
                                                    { data: t } = e || {}; return t } catch (e) { t.dispatch((0, T.hR)({ message: (0, V.jsxs)("div", { children: [(0, V.jsx)("div", { children: "Something went wrong. Refresh your page <NAME_EMAIL> for more information" }), (0, V.jsx)("br", {}), (0, V.jsx)("br", {}), (0, V.jsx)("div", { children: (0, V.jsx)("i", { children: "Error code SLS-403" }) })] }), details: "SLS 403" })) } }), a); break;
                                    case 404:
                                        t.dispatch((0, T.hR)({ name: "".concat(n, "/").concat(a), message: "Resource not found", cb: () => { window.location.assign((0, H.ze)()) } })); break;
                                    case 500:
                                        t.dispatch((0, T.hR)({ name: "".concat(n, "/").concat(a), cb: () => { window.location.assign((0, H.ze)()) } })) }
                                return t.rejectWithValue({ ...(null === (d = h.response) || void 0 === d ? void 0 : d.data) || {}, status: e }) } })), e)), {}) } }, 38355: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, zZ: () => l }); var r = n(14502),
                    a = n.n(r),
                    o = n(40854),
                    i = n.n(o); let l = function(e) { return e.Owner = "owner", e.Subsidiary = "subsidiary", e }({}); const s = { getOrgsStats: function() { let { page: e, pageSize: t, sort: n, search: r } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return i().get(a()().template("/organizations/stats").param({ page: e, pageSize: t, sort: n, search: r }).toString(), this.config) }, getOrgRelations: function(e) { let { orgId: t } = e; return i().get(a()().template("/organizations/:orgId/relations").param({ orgId: t }).toString(), this.config) }, upsertOrgRelation: function(e) { let { orgId: t, data: n } = e; return i().post(a()().template("/organizations/:orgId/relations").param("orgId", t).toString(), { ...n }, this.config) }, deleteOrgRelation: function(e) { let { orgId: t, relatedOrgId: n, type: r } = e; return i().delete(a()().template("/organizations/:orgId/relations/:type/:relatedOrgId").param({ orgId: t }).param({ relatedOrgId: n }).param({ type: r }).toString(), this.config) }, searchExternalOrg: function(e) { let { search: t } = e; return i().get(a()().template("/organizations/external/search/:search").param({ search: t }).toString(), this.config) }, searchExternalRelations: function(e) { let { cik: t } = e; return i().get(a()().template("/organizations/external/cik/:cik").param({ cik: t }).toString(), this.config) }, importSECOwnership: function(e) { let { entity: t } = e; return i().post(a()().template("/organizations/external/import").toString(), { entity: t }, this.config) }, importOwnership: function(e) { let { data: t } = e; return i().post(a()().template("/organizations/import").toString(), { data: t }, this.config) }, getAllRelations: function() { return i().get(a()().template("/organizations/relations").toString(), this.config) } } }, 66856: (e, t, n) => { "use strict";
                n.d(t, { Yj: () => Q, qM: () => re, Qo: () => ne, yv: () => Z, Po: () => Y, iw: () => J, Wx: () => $, si: () => X, ze: () => S, q5: () => I, OW: () => V, qj: () => E, N$: () => T, uN: () => C, Oq: () => H, Io: () => j, N9: () => M, ts: () => L, RI: () => ee, gz: () => P, yZ: () => w, mF: () => z, eK: () => O, a5: () => x, iD: () => f, ri: () => v, mX: () => A, r2: () => G, YQ: () => K, xv: () => B, ve: () => W, _i: () => U, Zm: () => q, K7: () => F, BC: () => _, $u: () => N, wi: () => ae, dH: () => oe, K5: () => te, kz: () => y, wZ: () => ie, Mz: () => k, wO: () => b, SN: () => R, A$: () => g, w4: () => D }); var r = n(91688);
                n(72835), n(65043), n(75156), n(14556), n(42006), n(59177), n(49015), n(15622), n(79825), n(24115);
                n.p;
                n(47483), n(52991);
                n.p, n.p;
                n(12499), n(70579); const a = ["company", "industryType", "dataInput", "dataSource", "jobFunction", "done"]; var o = n(78396); const i = "auth",
                    l = "dashboard",
                    s = ["charts", "activity", "reports", "audit", "import", "integrations", "themes", "intro", ":type", ":resource"],
                    c = ["organizations"],
                    d = ["charts", "people", "settings", "reports", "audit", "fields", "permissions", "snapshots", "ownership", ":resource"],
                    u = "organizations",
                    h = ["print", "public", "protected", "embed", ":base", "community", "salesforce"],
                    m = ["charts"];

                function p(e) { if (l === e) return t => { if (-1 !== s.indexOf(t)) return "import" === t ? e => e ? (0, r.generatePath)("/dashboard/:resource/:type", { resource: t, type: e }) : (0, r.generatePath)("/dashboard/:resource", { resource: t }) : "themes" === t ? e => (0, r.generatePath)("/dashboard/:resource/:orgId", { resource: t, orgId: e }) : (0, r.generatePath)("/dashboard/:resource", { resource: t }); if (-1 !== c.indexOf(t)) return n => a => { if (Array.isArray(a) && a.length > 1) { const [o, i] = a; return (0, r.generatePath)("/".concat(e, "/").concat(t, "/:orgId/:resource(").concat(d.join("|"), ")/:activeTab"), { orgId: n, resource: o, activeTab: i }) } return "settings" === a ? (0, r.generatePath)("/".concat(e, "/").concat(t, "/:orgId/:resource(").concat(d.join("|"), ")/:activeTab"), { orgId: n, resource: a, activeTab: "profile" }) : "ownership" === n ? o => i => (0, r.generatePath)("/".concat(e, "/").concat(t, "/").concat(n, "/").concat(a, "/:cik/:view"), { cik: o, view: i }) : (0, r.generatePath)("/".concat(e, "/").concat(t, "/:orgId/:resource(").concat(d.join("|"), ")"), { orgId: n, resource: a }) }; throw Error("Path Not Implementd") }; if (u === e) return t => n => { if ("import" === n || "localImport" === n) return a => a ? (0, r.generatePath)("/".concat(e, "/:orgId/:orgResource/:importType"), { orgId: t, orgResource: n, importType: a }) : (0, r.generatePath)("/".concat(e, "/:orgId/:orgResource"), { orgId: t, orgResource: n }); if ("integrations" === n) return a => (0, r.generatePath)("/".concat(e, "/:orgId/:orgResource/:integrationType"), { orgId: t, orgResource: n, integrationType: a }); if (-1 !== m.indexOf(n)) return a => o => i => (0, r.generatePath)("/".concat(e, "/:orgId/").concat(n, "/:chartId/:resource/:resourceAction"), { orgId: t, chartId: a, resource: o, resourceAction: i }); throw Error("Path Not Implemented") }; if (-1 !== h.indexOf(e)) return t => n => a => { if ("ownership" === a) return o => (0, r.generatePath)("/:base/".concat(t, "/:orgId/:resource(").concat(d.join("|"), ")/:view"), { base: e, orgId: n, resource: a, view: o }); if (-1 !== m.indexOf(a)) return o => i => l => (0, r.generatePath)("/:base/".concat(t, "/:orgId/").concat(a, "/:chartId/:resource/:resourceAction"), { base: e, orgId: n, chartId: o, resource: i, resourceAction: l }); throw Error("Path Not Implemented") }; if (i === e) return e => (0, r.generatePath)("/:resource", { resource: e }); throw Error("Path Not Implemented") }

                function f() { return p("auth")("login") }

                function v() { return "/logout" }

                function g() { return p("auth")("verifyEmail") }

                function y() { return p("auth")("register") }

                function b() { return p("auth")("setpassword") }

                function w() { return p("auth")("forgot") }

                function z() { return p("auth")("forgotRequested") }

                function x() { return "/iframeSetup.html" }

                function A() { return "/NotFound" }

                function k() { return "/selectAccount" }

                function S() { return "/dashboard" }

                function M() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { resource: t } = e; return p("dashboard")(t || ":resource") }

                function E() { return p("dashboard")("import")() }

                function C() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { type: t } = e; return p("dashboard")("import")(t || ":type") }

                function T() { return p("dashboard")("import")("photos") }

                function H() { return p("dashboard")("integrations") }

                function L(e) { return p("dashboard")("themes")(e || ":orgId") }

                function I() { return p("dashboard")("activity") }

                function j() { return p("dashboard")("reports") }

                function V() { return p("dashboard")("audit") }

                function O() { return "/getstarted".concat(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "") }

                function R() { return "/thankyou" }

                function P() { return "/failed" }

                function D(e) { return e && a.includes(e) ? "/welcome?onboardingStep=".concat(e) : "/welcome" }

                function F() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, resource: n } = e; return p("dashboard")("organizations")(t || ":orgId")(n || ":resource") }

                function N() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t } = e; return p("dashboard")("organizations")(t || ":orgId")("settings") }

                function _() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, activeTab: n, resource: r } = e; return p("dashboard")("organizations")(t || ":orgId")([r || ":resource", n || ":activeTab"]) }

                function B() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t } = e; return p("organizations")(t || ":orgId")("import")() }

                function W() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, importType: n } = e; return p("organizations")(t || ":orgId")("import")(n || ":importType") }

                function U() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, integrationType: n } = e; return p("organizations")(t || ":orgId")("integrations")(n || ":integrationType") }

                function q() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t } = e; return p("dashboard")("organizations")(t || ":orgId")("people") }

                function G() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t } = e; return p("dashboard")("organizations")(t || ":orgId")("charts") }

                function K() { return "/dashboard/organizations" }

                function Z() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, resource: r, base: a, resourceAction: o } = e; return p(a || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")(r || ":resource")(o || ":resourceAction") }

                function Y() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, resource: r, resourceAction: a } = e; return p("protected")("organizations")(t || ":orgId")("charts")(n || ":chartId")(r || ":resource")(a || ":resourceAction") }

                function X() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, resource: r, resourceAction: a, base: o } = e; return p(o || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")(r || ":resource")(a || ":resourceAction") }

                function $() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")("view") }

                function Q() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")(o.uI.AI_INSIGHTS) }

                function J() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n } = e; return p("protected")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")("theme") }

                function ee() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n } = e; return p("protected")("organizations")(t || ":orgId")("charts")(n || ":chartId")("directory")("theme") }

                function te() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n } = e; return p("protected")("organizations")(t || ":orgId")("charts")(n || ":chartId")("photoboard")("theme") }

                function ne() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, resource: r, base: a } = e; return p(a || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")(r || ":resource")("printPreview") }

                function re() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n } = e; return p("community")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")("communitybuild") }

                function ae() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, view: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("ownership")(n || ":view") }

                function oe() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { cik: t, view: n } = e; return p("dashboard")("organizations")("ownership")("sec")(t || ":cik")(n || ":view") } const ie = { login: f, register: y, setPassword: b, forgot: w, forgotRequested: z, dashboardResource: M, dashboardImportResource: C, dashboardActivity: I, dashboardReports: j, dashboardCharts: function() { return p("dashboard")("charts") }, dashboardImport: E, dashboardImportPhotos: T, dashboardIntegrations: H, dashboardThemes: L, getStarted: O, getStartedFromNewChart: function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : e ? "&orgId=".concat(e) : ""; return "/getstarted?source=newChart".concat(t) }, dashboardOrganizations: function() { return p("dashboard")("organizations") }, organizationLocalImportType: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, importType: n } = e; return p("organizations")(t || ":orgId")("localImport")(n || ":importType") }, organizationResource: F, organizationSettings: N, organizationPeople: q, organizationCharts: G, chartSettings: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n } = e; return p("protected")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")("settings") }, chartTheme: J, chartPrint: ne, chartShare: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, resource: r, base: a } = e; return p(a || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")(r || ":resource")("share") }, chartView: $, chartCommunityBuild: re, photoboardView: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("photoboard")("view") }, directoryView: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("directory")("view") }, directoryTheme: ee, photoboardTheme: te, spreadsheetView: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("spreadsheet")("view") }, chartAiInsights: Q, chartHistory: function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { orgId: t, chartId: n, base: r } = e; return p(r || ":base")("organizations")(t || ":orgId")("charts")(n || ":chartId")("chart")("chartHistory") }, chartViewResource: X, chartPrintResource: Z, thankYou: R, failed: P, welcome: D, ownershipChart: ae } }, 42197: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = (0, n(71705).A)({ name: "Default Theme", breakpoints: { values: { xs: 0, sm: 600, md: 960, lg: 1280, xl: 1920 } }, overrides: { MuiRoot: { height: "50%", width: "50%" }, MuiToolbar: { root: { backgroundColor: "#ffffff" } }, MuiListSubheader: { root: { lineHeight: "32px" } }, MuiDrawer: { paper: { top: "70px", height: "100%", bottom: 0, width: "250px", position: "absolute", zIndex: 5 } }, MuiTablePagination: { toolbar: { backgroundColor: "inherit" } }, MuiTableRow: { root: { cursor: "pointer" } }, MuiBadge: { anchorOriginTopRightRectangle: { transform: "scale(1.2) translate(50%, -50%)" } }, MuiAlert: { message: { flex: 1 } } }, palette: { type: "light", default: { main: "#1C67A0" }, primary: { main: "#5C2DBF", contrastText: "#ffffff", shaded: "#e6def7" }, secondary: { main: "#3CD3C2", contrastText: "#ffffff" }, error: { light: "#e57373", main: "#eb493a", dark: "#d32f23", contrastText: "#ffffff" }, warning: { light: "#ffb74d", main: "#ff9800", dark: "#f27c22", contrastText: "#ffffff" }, success: { light: "#94d8a8", main: "#5aa96b", dark: "#258730", contrastText: "#ffffff" }, info: { light: "#a2c3f9", main: "#4285f4", dark: "#0a47ac", contrastText: "#fff" }, black: { main: "#303030", dark: "#303030", contrastText: "#fff", light: "#616161" }, common: { black: "#151515" }, leftChartToolbar: { background: "#fcfcfc" }, dashboardNav: { background: "#fcfcfc" } }, status: { danger: "orange" }, buttonSizes: { default: 180, small: 150, medium: 200, large: 240 }, typography: { htmlFontSize: 15, fontFamily: '"Poppins", "Helvetica", "Arial", "sans-serif"', fontSize: 15, fontWeight: { light: 300, regular: 400, medium: 500, bold: 600 }, h1: { fontSize: 30, fontWeight: 400 }, h2: { fontSize: 28, fontWeight: 400 }, h3: { fontSize: 24, fontWeight: 400 }, h4: { fontSize: 22, fontWeight: 300 }, h5: { fontSize: 20, fontWeight: 400 }, h6: { fontSize: 18, fontWeight: 300 }, subtitle1: { fontSize: 18, fontWeight: 400 }, subtitle2: { fontSize: 15, fontWeight: 400 }, body1: { fontSize: 15 }, body2: { fontSize: 14 }, button: {}, caption: { fontSize: 13, fontWeight: 400 }, overline: { fontSize: 14, lineHeight: "19.8px", fontWeight: 400, textTransform: "capitalize" } }, zIndex: { mobileStepper: 1e3, speedDial: 1050, appBar: 1100, drawer: 1200, modal: 1300, secondaryModal: 1350, aboveModal: 1351, snackbar: 1400, confirmAction: 1500, tooltip: 1600, alert: 1550, help: 1650 } }) }, 80286: (e, t, n) => { "use strict";
