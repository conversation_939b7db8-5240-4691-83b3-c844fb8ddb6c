                        o = function(e) { var t = e.isEnabled,
                                n = e.onBottomArrive,
                                a = e.onBottomLeave,
                                o = e.onTopArrive,
                                i = e.onTopLeave,
                                l = (0, r.useRef)(!1),
                                s = (0, r.useRef)(!1),
                                c = (0, r.useRef)(0),
                                d = (0, r.useRef)(null),
                                u = (0, r.useCallback)((function(e, t) { if (null !== d.current) { var r = d.current,
                                            c = r.scrollTop,
                                            u = r.scrollHeight,
                                            h = r.clientHeight,
                                            m = d.current,
                                            p = t > 0,
                                            f = u - h - c,
                                            v = !1;
                                        f > t && l.current && (a && a(e), l.current = !1), p && s.current && (i && i(e), s.current = !1), p && t > f ? (n && !l.current && n(e), m.scrollTop = u, v = !0, l.current = !0) : !p && -t > c && (o && !s.current && o(e), m.scrollTop = 0, v = !0, s.current = !0), v && function(e) { e.preventDefault(), e.stopPropagation() }(e) } }), [n, a, o, i]),
                                h = (0, r.useCallback)((function(e) { u(e, e.deltaY) }), [u]),
                                m = (0, r.useCallback)((function(e) { c.current = e.changedTouches[0].clientY }), []),
                                p = (0, r.useCallback)((function(e) { var t = c.current - e.changedTouches[0].clientY;
                                    u(e, t) }), [u]),
                                f = (0, r.useCallback)((function(e) { if (e) { var t = !!Ur && { passive: !1 };
                                        e.addEventListener("wheel", h, t), e.addEventListener("touchstart", m, t), e.addEventListener("touchmove", p, t) } }), [p, m, h]),
                                v = (0, r.useCallback)((function(e) { e && (e.removeEventListener("wheel", h, !1), e.removeEventListener("touchstart", m, !1), e.removeEventListener("touchmove", p, !1)) }), [p, m, h]); return (0, r.useEffect)((function() { if (t) { var e = d.current; return f(e),
                                            function() { v(e) } } }), [t, f, v]),
                                function(e) { d.current = e } }({ isEnabled: void 0 === a || a, onBottomArrive: e.onBottomArrive, onBottomLeave: e.onBottomLeave, onTopArrive: e.onTopArrive, onTopLeave: e.onTopLeave }),
                        i = function(e) { var t = e.isEnabled,
                                n = e.accountForScrollbars,
                                a = void 0 === n || n,
                                o = (0, r.useRef)({}),
                                i = (0, r.useRef)(null),
                                l = (0, r.useCallback)((function(e) { if (Ga) { var t = document.body,
                                            n = t && t.style; if (a && Na.forEach((function(e) { var t = n && n[e];
                                                o.current[e] = t })), a && Ka < 1) { var r = parseInt(o.current.paddingRight, 10) || 0,
                                                i = document.body ? document.body.clientWidth : 0,
                                                l = window.innerWidth - i + r || 0;
                                            Object.keys(_a).forEach((function(e) { var t = _a[e];
                                                n && (n[e] = t) })), n && (n.paddingRight = "".concat(l, "px")) } t && qa() && (t.addEventListener("touchmove", Ba, Za), e && (e.addEventListener("touchstart", Ua, Za), e.addEventListener("touchmove", Wa, Za))), Ka += 1 } }), [a]),
                                s = (0, r.useCallback)((function(e) { if (Ga) { var t = document.body,
                                            n = t && t.style;
                                        Ka = Math.max(Ka - 1, 0), a && Ka < 1 && Na.forEach((function(e) { var t = o.current[e];
                                            n && (n[e] = t) })), t && qa() && (t.removeEventListener("touchmove", Ba, Za), e && (e.removeEventListener("touchstart", Ua, Za), e.removeEventListener("touchmove", Wa, Za))) } }), [a]); return (0, r.useEffect)((function() { if (t) { var e = i.current; return l(e),
                                            function() { s(e) } } }), [t, l, s]),
                                function(e) { i.current = e } }({ isEnabled: n }); return Xn(r.Fragment, null, n && Xn("div", { onClick: Ya, css: Xa }), t((function(e) { o(e), i(e) }))) } var Qa = { name: "5kkxb2-requiredInput-RequiredInput", styles: "label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%;label:RequiredInput;", map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIlJlcXVpcmVkSW5wdXQudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWNJIiwiZmlsZSI6IlJlcXVpcmVkSW5wdXQudHN4Iiwic291cmNlc0NvbnRlbnQiOlsiLyoqIEBqc3gganN4ICovXG5pbXBvcnQgeyBGb2N1c0V2ZW50SGFuZGxlciwgRnVuY3Rpb25Db21wb25lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5cbmNvbnN0IFJlcXVpcmVkSW5wdXQ6IEZ1bmN0aW9uQ29tcG9uZW50PHtcbiAgcmVhZG9ubHkgbmFtZT86IHN0cmluZztcbiAgcmVhZG9ubHkgb25Gb2N1czogRm9jdXNFdmVudEhhbmRsZXI8SFRNTElucHV0RWxlbWVudD47XG59PiA9ICh7IG5hbWUsIG9uRm9jdXMgfSkgPT4gKFxuICA8aW5wdXRcbiAgICByZXF1aXJlZFxuICAgIG5hbWU9e25hbWV9XG4gICAgdGFiSW5kZXg9ey0xfVxuICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXG4gICAgb25Gb2N1cz17b25Gb2N1c31cbiAgICBjc3M9e3tcbiAgICAgIGxhYmVsOiAncmVxdWlyZWRJbnB1dCcsXG4gICAgICBvcGFjaXR5OiAwLFxuICAgICAgcG9pbnRlckV2ZW50czogJ25vbmUnLFxuICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICBib3R0b206IDAsXG4gICAgICBsZWZ0OiAwLFxuICAgICAgcmlnaHQ6IDAsXG4gICAgICB3aWR0aDogJzEwMCUnLFxuICAgIH19XG4gICAgLy8gUHJldmVudCBgU3dpdGNoaW5nIGZyb20gdW5jb250cm9sbGVkIHRvIGNvbnRyb2xsZWRgIGVycm9yXG4gICAgdmFsdWU9XCJcIlxuICAgIG9uQ2hhbmdlPXsoKSA9PiB7fX1cbiAgLz5cbik7XG5cbmV4cG9ydCBkZWZhdWx0IFJlcXVpcmVkSW5wdXQ7XG4iXX0= */", toString: function() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)." } },
                    Ja = function(e) { var t = e.name,
                            n = e.onFocus; return Xn("input", { required: !0, name: t, tabIndex: -1, "aria-hidden": "true", onFocus: n, css: Qa, value: "", onChange: function() {} }) },
                    eo = { clearIndicator: ha, container: function(e) { var t = e.isDisabled; return { label: "container", direction: e.isRtl ? "rtl" : void 0, pointerEvents: t ? "none" : void 0, position: "relative" } }, control: function(e, t) { var n = e.isDisabled,
                                r = e.isFocused,
                                a = e.theme,
                                o = a.colors,
                                i = a.borderRadius; return Re({ label: "control", alignItems: "center", cursor: "default", display: "flex", flexWrap: "wrap", justifyContent: "space-between", minHeight: a.spacing.controlHeight, outline: "0 !important", position: "relative", transition: "all 100ms" }, t ? {} : { backgroundColor: n ? o.neutral5 : o.neutral0, borderColor: n ? o.neutral10 : r ? o.primary : o.neutral20, borderRadius: i, borderStyle: "solid", borderWidth: 1, boxShadow: r ? "0 0 0 1px ".concat(o.primary) : void 0, "&:hover": { borderColor: r ? o.primary : o.neutral30 } }) }, dropdownIndicator: ua, group: function(e, t) { var n = e.theme.spacing; return t ? {} : { paddingBottom: 2 * n.baseUnit, paddingTop: 2 * n.baseUnit } }, groupHeading: function(e, t) { var n = e.theme,
                                r = n.colors,
                                a = n.spacing; return Re({ label: "group", cursor: "default", display: "block" }, t ? {} : { color: r.neutral40, fontSize: "75%", fontWeight: 500, marginBottom: "0.25em", paddingLeft: 3 * a.baseUnit, paddingRight: 3 * a.baseUnit, textTransform: "uppercase" }) }, indicatorsContainer: function() { return { alignItems: "center", alignSelf: "stretch", display: "flex", flexShrink: 0 } }, indicatorSeparator: function(e, t) { var n = e.isDisabled,
                                r = e.theme,
                                a = r.spacing.baseUnit,
                                o = r.colors; return Re({ label: "indicatorSeparator", alignSelf: "stretch", width: 1 }, t ? {} : { backgroundColor: n ? o.neutral10 : o.neutral20, marginBottom: 2 * a, marginTop: 2 * a }) }, input: function(e, t) { var n = e.isDisabled,
                                r = e.value,
                                a = e.theme,
                                o = a.spacing,
                                i = a.colors; return Re(Re({ visibility: n ? "hidden" : "visible", transform: r ? "translateZ(0)" : "" }, ya), t ? {} : { margin: o.baseUnit / 2, paddingBottom: o.baseUnit / 2, paddingTop: o.baseUnit / 2, color: i.neutral80 }) }, loadingIndicator: function(e, t) { var n = e.isFocused,
                                r = e.size,
                                a = e.theme,
                                o = a.colors,
                                i = a.spacing.baseUnit; return Re({ label: "loadingIndicator", display: "flex", transition: "color 150ms", alignSelf: "center", fontSize: r, lineHeight: 1, marginRight: r, textAlign: "center", verticalAlign: "middle" }, t ? {} : { color: n ? o.neutral60 : o.neutral20, padding: 2 * i }) }, loadingMessage: ra, menu: function(e, t) { var n, r = e.placement,
                                a = e.theme,
                                o = a.borderRadius,
                                i = a.spacing,
                                l = a.colors; return Re((Ve(n = { label: "menu" }, function(e) { return e ? { bottom: "top", top: "bottom" } [e] : "bottom" }(r), "100%"), Ve(n, "position", "absolute"), Ve(n, "width", "100%"), Ve(n, "zIndex", 1), n), t ? {} : { backgroundColor: l.neutral0, borderRadius: o, boxShadow: "0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)", marginBottom: i.menuGutter, marginTop: i.menuGutter }) }, menuList: function(e, t) { var n = e.maxHeight,
                                r = e.theme.spacing.baseUnit; return Re({ maxHeight: n, overflowY: "auto", position: "relative", WebkitOverflowScrolling: "touch" }, t ? {} : { paddingBottom: r, paddingTop: r }) }, menuPortal: function(e) { var t = e.rect,
                                n = e.offset,
                                r = e.position; return { left: t.left, position: r, top: n, width: t.width, zIndex: 1 } }, multiValue: function(e, t) { var n = e.theme,
                                r = n.spacing,
                                a = n.borderRadius,
                                o = n.colors; return Re({ label: "multiValue", display: "flex", minWidth: 0 }, t ? {} : { backgroundColor: o.neutral10, borderRadius: a / 2, margin: r.baseUnit / 2 }) }, multiValueLabel: function(e, t) { var n = e.theme,
                                r = n.borderRadius,
                                a = n.colors,
                                o = e.cropWithEllipsis; return Re({ overflow: "hidden", textOverflow: o || void 0 === o ? "ellipsis" : void 0, whiteSpace: "nowrap" }, t ? {} : { borderRadius: r / 2, color: a.neutral80, fontSize: "85%", padding: 3, paddingLeft: 6 }) }, multiValueRemove: function(e, t) { var n = e.theme,
                                r = n.spacing,
                                a = n.borderRadius,
                                o = n.colors,
                                i = e.isFocused; return Re({ alignItems: "center", display: "flex" }, t ? {} : { borderRadius: a / 2, backgroundColor: i ? o.dangerLight : void 0, paddingLeft: r.baseUnit, paddingRight: r.baseUnit, ":hover": { backgroundColor: o.dangerLight, color: o.danger } }) }, noOptionsMessage: na, option: function(e, t) { var n = e.isDisabled,
                                r = e.isFocused,
                                a = e.isSelected,
                                o = e.theme,
                                i = o.spacing,
                                l = o.colors; return Re({ label: "option", cursor: "default", display: "block", fontSize: "inherit", width: "100%", userSelect: "none", WebkitTapHighlightColor: "rgba(0, 0, 0, 0)" }, t ? {} : { backgroundColor: a ? l.primary : r ? l.primary25 : "transparent", color: n ? l.neutral20 : a ? l.neutral0 : "inherit", padding: "".concat(2 * i.baseUnit, "px ").concat(3 * i.baseUnit, "px"), ":active": { backgroundColor: n ? void 0 : a ? l.primary : l.primary50 } }) }, placeholder: function(e, t) { var n = e.theme,
                                r = n.spacing,
                                a = n.colors; return Re({ label: "placeholder", gridArea: "1 / 1 / 2 / 3" }, t ? {} : { color: a.neutral50, marginLeft: r.baseUnit / 2, marginRight: r.baseUnit / 2 }) }, singleValue: function(e, t) { var n = e.isDisabled,
                                r = e.theme,
                                a = r.spacing,
                                o = r.colors; return Re({ label: "singleValue", gridArea: "1 / 1 / 2 / 3", maxWidth: "100%", overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }, t ? {} : { color: n ? o.neutral40 : o.neutral80, marginLeft: a.baseUnit / 2, marginRight: a.baseUnit / 2 }) }, valueContainer: function(e, t) { var n = e.theme.spacing,
                                r = e.isMulti,
                                a = e.hasValue,
                                o = e.selectProps.controlShouldRenderValue; return Re({ alignItems: "center", display: r && a && o ? "flex" : "grid", flex: 1, flexWrap: "wrap", WebkitOverflowScrolling: "touch", position: "relative", overflow: "hidden" }, t ? {} : { padding: "".concat(n.baseUnit / 2, "px ").concat(2 * n.baseUnit, "px") }) } },
                    to = { borderRadius: 4, colors: { primary: "#2684FF", primary75: "#4C9AFF", primary50: "#B2D4FF", primary25: "#DEEBFF", danger: "#DE350B", dangerLight: "#FFBDAD", neutral0: "hsl(0, 0%, 100%)", neutral5: "hsl(0, 0%, 95%)", neutral10: "hsl(0, 0%, 90%)", neutral20: "hsl(0, 0%, 80%)", neutral30: "hsl(0, 0%, 70%)", neutral40: "hsl(0, 0%, 60%)", neutral50: "hsl(0, 0%, 50%)", neutral60: "hsl(0, 0%, 40%)", neutral70: "hsl(0, 0%, 30%)", neutral80: "hsl(0, 0%, 20%)", neutral90: "hsl(0, 0%, 10%)" }, spacing: { baseUnit: 4, controlHeight: 38, menuGutter: 8 } },
                    no = { "aria-live": "polite", backspaceRemovesValue: !0, blurInputOnSelect: Nr(), captureMenuScroll: !Nr(), classNames: {}, closeMenuOnSelect: !0, closeMenuOnScroll: !1, components: {}, controlShouldRenderValue: !0, escapeClearsValue: !1, filterOption: function(e, t) { if (e.data.__isNew__) return !0; var n = Re({ ignoreCase: !0, ignoreAccents: !0, stringify: Pa, trim: !0, matchFrom: "any" }, undefined),
                                r = n.ignoreCase,
                                a = n.ignoreAccents,
                                o = n.stringify,
                                i = n.trim,
                                l = n.matchFrom,
                                s = i ? Ra(t) : t,
                                c = i ? Ra(o(e)) : o(e); return r && (s = s.toLowerCase(), c = c.toLowerCase()), a && (s = Oa(s), c = Va(c)), "start" === l ? c.substr(0, s.length) === s : c.indexOf(s) > -1 }, formatGroupLabel: function(e) { return e.label }, getOptionLabel: function(e) { return e.label }, getOptionValue: function(e) { return e.value }, isDisabled: !1, isLoading: !1, isMulti: !1, isRtl: !1, isSearchable: !0, isOptionDisabled: function(e) { return !!e.isDisabled }, loadingMessage: function() { return "Loading..." }, maxMenuHeight: 300, minMenuHeight: 140, menuIsOpen: !1, menuPlacement: "bottom", menuPosition: "absolute", menuShouldBlockScroll: !1, menuShouldScrollIntoView: ! function() { try { return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) } catch (A) { return !1 } }(), noOptionsMessage: function() { return "No options" }, openMenuOnFocus: !1, openMenuOnClick: !0, options: [], pageSize: 5, placeholder: "Select...", screenReaderStatus: function(e) { var t = e.count; return "".concat(t, " result").concat(1 !== t ? "s" : "", " available") }, styles: {}, tabIndex: 0, tabSelectsValue: !0, unstyled: !1 };

                function ro(e, t, n, r) { return { type: "option", data: t, isDisabled: co(e, t, n), isSelected: uo(e, t, n), label: lo(e, t), value: so(e, t), index: r } }

                function ao(e, t) { return e.options.map((function(n, r) { if ("options" in n) { var a = n.options.map((function(n, r) { return ro(e, n, t, r) })).filter((function(t) { return io(e, t) })); return a.length > 0 ? { type: "group", data: n, options: a, index: r } : void 0 } var o = ro(e, n, t, r); return io(e, o) ? o : void 0 })).filter(qr) }

                function oo(e) { return e.reduce((function(e, t) { return "group" === t.type ? e.push.apply(e, Ke(t.options.map((function(e) { return e.data })))) : e.push(t.data), e }), []) }

                function io(e, t) { var n = e.inputValue,
                        r = void 0 === n ? "" : n,
                        a = t.data,
                        o = t.isSelected,
                        i = t.label,
                        l = t.value; return (!mo(e) || !o) && ho(e, { label: i, value: l, data: a }, r) } var lo = function(e, t) { return e.getOptionLabel(t) },
                    so = function(e, t) { return e.getOptionValue(t) };

                function co(e, t, n) { return "function" == typeof e.isOptionDisabled && e.isOptionDisabled(t, n) }

                function uo(e, t, n) { if (n.indexOf(t) > -1) return !0; if ("function" == typeof e.isOptionSelected) return e.isOptionSelected(t, n); var r = so(e, t); return n.some((function(t) { return so(e, t) === r })) }

                function ho(e, t, n) { return !e.filterOption || e.filterOption(t, n) } var mo = function(e) { var t = e.hideSelectedOptions,
                            n = e.isMulti; return void 0 === t ? n : t },
                    po = 1,
                    fo = function(e) {! function(e, t) { if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                            e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && Ue(e, t) }(i, r.Component); var t, n, a, o = Ge(i);

                        function i(e) { var t; if (function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, i), (t = o.call(this, e)).state = { ariaSelection: null, focusedOption: null, focusedValue: null, inputIsHidden: !1, isFocused: !1, selectValue: [], clearFocusValueOnUpdate: !1, prevWasFocused: !1, inputIsHiddenAfterUpdate: void 0, prevProps: void 0 }, t.blockOptionHover = !1, t.isComposing = !1, t.commonProps = void 0, t.initialTouchX = 0, t.initialTouchY = 0, t.instancePrefix = "", t.openAfterFocus = !1, t.scrollToFocusedOptionOnUpdate = !1, t.userIsDragging = void 0, t.controlRef = null, t.getControlRef = function(e) { t.controlRef = e }, t.focusedOptionRef = null, t.getFocusedOptionRef = function(e) { t.focusedOptionRef = e }, t.menuListRef = null, t.getMenuListRef = function(e) { t.menuListRef = e }, t.inputRef = null, t.getInputRef = function(e) { t.inputRef = e }, t.focus = t.focusInput, t.blur = t.blurInput, t.onChange = function(e, n) { var r = t.props,
                                        a = r.onChange,
                                        o = r.name;
                                    n.name = o, t.ariaOnChange(e, n), a(e, n) }, t.setValue = function(e, n, r) { var a = t.props,
                                        o = a.closeMenuOnSelect,
                                        i = a.isMulti,
                                        l = a.inputValue;
                                    t.onInputChange("", { action: "set-value", prevInputValue: l }), o && (t.setState({ inputIsHiddenAfterUpdate: !i }), t.onMenuClose()), t.setState({ clearFocusValueOnUpdate: !0 }), t.onChange(e, { action: n, option: r }) }, t.selectOption = function(e) { var n = t.props,
                                        r = n.blurInputOnSelect,
                                        a = n.isMulti,
                                        o = n.name,
                                        i = t.state.selectValue,
                                        l = a && t.isOptionSelected(e, i),
                                        s = t.isOptionDisabled(e, i); if (l) { var c = t.getOptionValue(e);
                                        t.setValue(i.filter((function(e) { return t.getOptionValue(e) !== c })), "deselect-option", e) } else { if (s) return void t.ariaOnChange(e, { action: "select-option", option: e, name: o });
                                        a ? t.setValue([].concat(Ke(i), [e]), "select-option", e) : t.setValue(e, "select-option") } r && t.blurInput() }, t.removeValue = function(e) { var n = t.props.isMulti,
                                        r = t.state.selectValue,
                                        a = t.getOptionValue(e),
                                        o = r.filter((function(e) { return t.getOptionValue(e) !== a })),
                                        i = Gr(n, o, o[0] || null);
                                    t.onChange(i, { action: "remove-value", removedValue: e }), t.focusInput() }, t.clearValue = function() { var e = t.state.selectValue;
                                    t.onChange(Gr(t.props.isMulti, [], null), { action: "clear", removedValues: e }) }, t.popValue = function() { var e = t.props.isMulti,
                                        n = t.state.selectValue,
                                        r = n[n.length - 1],
                                        a = n.slice(0, n.length - 1),
                                        o = Gr(e, a, a[0] || null);
                                    t.onChange(o, { action: "pop-value", removedValue: r }) }, t.getValue = function() { return t.state.selectValue }, t.cx = function() { for (var e = arguments.length, n = new Array(e), r = 0; r < e; r++) n[r] = arguments[r]; return Lr.apply(void 0, [t.props.classNamePrefix].concat(n)) }, t.getOptionLabel = function(e) { return lo(t.props, e) }, t.getOptionValue = function(e) { return so(t.props, e) }, t.getStyles = function(e, n) { var r = t.props.unstyled,
                                        a = eo[e](n, r);
                                    a.boxSizing = "border-box"; var o = t.props.styles[e]; return o ? o(a, n) : a }, t.getClassNames = function(e, n) { var r, a; return null === (r = (a = t.props.classNames)[e]) || void 0 === r ? void 0 : r.call(a, n) }, t.getElementId = function(e) { return "".concat(t.instancePrefix, "-").concat(e) }, t.getComponents = function() { return e = t.props, Re(Re({}, za), e.components); var e }, t.buildCategorizedOptions = function() { return ao(t.props, t.state.selectValue) }, t.getCategorizedOptions = function() { return t.props.menuIsOpen ? t.buildCategorizedOptions() : [] }, t.buildFocusableOptions = function() { return oo(t.buildCategorizedOptions()) }, t.getFocusableOptions = function() { return t.props.menuIsOpen ? t.buildFocusableOptions() : [] }, t.ariaOnChange = function(e, n) { t.setState({ ariaSelection: Re({ value: e }, n) }) }, t.onMenuMouseDown = function(e) { 0 === e.button && (e.stopPropagation(), e.preventDefault(), t.focusInput()) }, t.onMenuMouseMove = function(e) { t.blockOptionHover = !1 }, t.onControlMouseDown = function(e) { if (!e.defaultPrevented) { var n = t.props.openMenuOnClick;
                                        t.state.isFocused ? t.props.menuIsOpen ? "INPUT" !== e.target.tagName && "TEXTAREA" !== e.target.tagName && t.onMenuClose() : n && t.openMenu("first") : (n && (t.openAfterFocus = !0), t.focusInput()), "INPUT" !== e.target.tagName && "TEXTAREA" !== e.target.tagName && e.preventDefault() } }, t.onDropdownIndicatorMouseDown = function(e) { if (!(e && "mousedown" === e.type && 0 !== e.button || t.props.isDisabled)) { var n = t.props,
                                            r = n.isMulti,
                                            a = n.menuIsOpen;
                                        t.focusInput(), a ? (t.setState({ inputIsHiddenAfterUpdate: !r }), t.onMenuClose()) : t.openMenu("first"), e.preventDefault() } }, t.onClearIndicatorMouseDown = function(e) { e && "mousedown" === e.type && 0 !== e.button || (t.clearValue(), e.preventDefault(), t.openAfterFocus = !1, "touchend" === e.type ? t.focusInput() : setTimeout((function() { return t.focusInput() }))) }, t.onScroll = function(e) { "boolean" == typeof t.props.closeMenuOnScroll ? e.target instanceof HTMLElement && Or(e.target) && t.props.onMenuClose() : "function" == typeof t.props.closeMenuOnScroll && t.props.closeMenuOnScroll(e) && t.props.onMenuClose() }, t.onCompositionStart = function() { t.isComposing = !0 }, t.onCompositionEnd = function() { t.isComposing = !1 }, t.onTouchStart = function(e) { var n = e.touches,
                                        r = n && n.item(0);
                                    r && (t.initialTouchX = r.clientX, t.initialTouchY = r.clientY, t.userIsDragging = !1) }, t.onTouchMove = function(e) { var n = e.touches,
                                        r = n && n.item(0); if (r) { var a = Math.abs(r.clientX - t.initialTouchX),
                                            o = Math.abs(r.clientY - t.initialTouchY);
                                        t.userIsDragging = a > 5 || o > 5 } }, t.onTouchEnd = function(e) { t.userIsDragging || (t.controlRef && !t.controlRef.contains(e.target) && t.menuListRef && !t.menuListRef.contains(e.target) && t.blurInput(), t.initialTouchX = 0, t.initialTouchY = 0) }, t.onControlTouchEnd = function(e) { t.userIsDragging || t.onControlMouseDown(e) }, t.onClearIndicatorTouchEnd = function(e) { t.userIsDragging || t.onClearIndicatorMouseDown(e) }, t.onDropdownIndicatorTouchEnd = function(e) { t.userIsDragging || t.onDropdownIndicatorMouseDown(e) }, t.handleInputChange = function(e) { var n = t.props.inputValue,
                                        r = e.currentTarget.value;
                                    t.setState({ inputIsHiddenAfterUpdate: !1 }), t.onInputChange(r, { action: "input-change", prevInputValue: n }), t.props.menuIsOpen || t.onMenuOpen() }, t.onInputFocus = function(e) { t.props.onFocus && t.props.onFocus(e), t.setState({ inputIsHiddenAfterUpdate: !1, isFocused: !0 }), (t.openAfterFocus || t.props.openMenuOnFocus) && t.openMenu("first"), t.openAfterFocus = !1 }, t.onInputBlur = function(e) { var n = t.props.inputValue;
                                    t.menuListRef && t.menuListRef.contains(document.activeElement) ? t.inputRef.focus() : (t.props.onBlur && t.props.onBlur(e), t.onInputChange("", { action: "input-blur", prevInputValue: n }), t.onMenuClose(), t.setState({ focusedValue: null, isFocused: !1 })) }, t.onOptionHover = function(e) { t.blockOptionHover || t.state.focusedOption === e || t.setState({ focusedOption: e }) }, t.shouldHideSelectedOptions = function() { return mo(t.props) }, t.onValueInputFocus = function(e) { e.preventDefault(), e.stopPropagation(), t.focus() }, t.onKeyDown = function(e) { var n = t.props,
                                        r = n.isMulti,
                                        a = n.backspaceRemovesValue,
                                        o = n.escapeClearsValue,
                                        i = n.inputValue,
                                        l = n.isClearable,
                                        s = n.isDisabled,
                                        c = n.menuIsOpen,
                                        d = n.onKeyDown,
                                        u = n.tabSelectsValue,
                                        h = n.openMenuOnFocus,
                                        m = t.state,
                                        p = m.focusedOption,
                                        f = m.focusedValue,
                                        v = m.selectValue; if (!(s || "function" == typeof d && (d(e), e.defaultPrevented))) { switch (t.blockOptionHover = !0, e.key) {
                                            case "ArrowLeft":
                                                if (!r || i) return;
                                                t.focusValue("previous"); break;
                                            case "ArrowRight":
                                                if (!r || i) return;
                                                t.focusValue("next"); break;
                                            case "Delete":
                                            case "Backspace":
                                                if (i) return; if (f) t.removeValue(f);
                                                else { if (!a) return;
                                                    r ? t.popValue() : l && t.clearValue() } break;
                                            case "Tab":
                                                if (t.isComposing) return; if (e.shiftKey || !c || !u || !p || h && t.isOptionSelected(p, v)) return;
                                                t.selectOption(p); break;
                                            case "Enter":
                                                if (229 === e.keyCode) break; if (c) { if (!p) return; if (t.isComposing) return;
                                                    t.selectOption(p); break } return;
                                            case "Escape":
                                                c ? (t.setState({ inputIsHiddenAfterUpdate: !1 }), t.onInputChange("", { action: "menu-close", prevInputValue: i }), t.onMenuClose()) : l && o && t.clearValue(); break;
                                            case " ":
                                                if (i) return; if (!c) { t.openMenu("first"); break } if (!p) return;
                                                t.selectOption(p); break;
                                            case "ArrowUp":
                                                c ? t.focusOption("up") : t.openMenu("last"); break;
                                            case "ArrowDown":
                                                c ? t.focusOption("down") : t.openMenu("first"); break;
                                            case "PageUp":
                                                if (!c) return;
                                                t.focusOption("pageup"); break;
                                            case "PageDown":
                                                if (!c) return;
                                                t.focusOption("pagedown"); break;
                                            case "Home":
                                                if (!c) return;
                                                t.focusOption("first"); break;
                                            case "End":
                                                if (!c) return;
                                                t.focusOption("last"); break;
                                            default:
                                                return } e.preventDefault() } }, t.instancePrefix = "react-select-" + (t.props.instanceId || ++po), t.state.selectValue = Ir(e.value), e.menuIsOpen && t.state.selectValue.length) { var n = t.buildFocusableOptions(),
                                    r = n.indexOf(t.state.selectValue[0]);
                                t.state.focusedOption = n[r] } return t } return t = i, n = [{ key: "componentDidMount", value: function() { this.startListeningComposition(), this.startListeningToTouch(), this.props.closeMenuOnScroll && document && document.addEventListener && document.addEventListener("scroll", this.onScroll, !0), this.props.autoFocus && this.focusInput(), this.props.menuIsOpen && this.state.focusedOption && this.menuListRef && this.focusedOptionRef && Fr(this.menuListRef, this.focusedOptionRef) } }, { key: "componentDidUpdate", value: function(e) { var t = this.props,
                                    n = t.isDisabled,
                                    r = t.menuIsOpen,
                                    a = this.state.isFocused;
                                (a && !n && e.isDisabled || a && r && !e.menuIsOpen) && this.focusInput(), a && n && !e.isDisabled ? this.setState({ isFocused: !1 }, this.onMenuClose) : a || n || !e.isDisabled || this.inputRef !== document.activeElement || this.setState({ isFocused: !0 }), this.menuListRef && this.focusedOptionRef && this.scrollToFocusedOptionOnUpdate && (Fr(this.menuListRef, this.focusedOptionRef), this.scrollToFocusedOptionOnUpdate = !1) } }, { key: "componentWillUnmount", value: function() { this.stopListeningComposition(), this.stopListeningToTouch(), document.removeEventListener("scroll", this.onScroll, !0) } }, { key: "onMenuOpen", value: function() { this.props.onMenuOpen() } }, { key: "onMenuClose", value: function() { this.onInputChange("", { action: "menu-close", prevInputValue: this.props.inputValue }), this.props.onMenuClose() } }, { key: "onInputChange", value: function(e, t) { this.props.onInputChange(e, t) } }, { key: "focusInput", value: function() { this.inputRef && this.inputRef.focus() } }, { key: "blurInput", value: function() { this.inputRef && this.inputRef.blur() } }, { key: "openMenu", value: function(e) { var t = this,
                                    n = this.state,
                                    r = n.selectValue,
                                    a = n.isFocused,
                                    o = this.buildFocusableOptions(),
                                    i = "first" === e ? 0 : o.length - 1; if (!this.props.isMulti) { var l = o.indexOf(r[0]);
                                    l > -1 && (i = l) } this.scrollToFocusedOptionOnUpdate = !(a && this.menuListRef), this.setState({ inputIsHiddenAfterUpdate: !1, focusedValue: null, focusedOption: o[i] }, (function() { return t.onMenuOpen() })) } }, { key: "focusValue", value: function(e) { var t = this.state,
                                    n = t.selectValue,
                                    r = t.focusedValue; if (this.props.isMulti) { this.setState({ focusedOption: null }); var a = n.indexOf(r);
                                    r || (a = -1); var o = n.length - 1,
                                        i = -1; if (n.length) { switch (e) {
                                            case "previous":
                                                i = 0 === a ? 0 : -1 === a ? o : a - 1; break;
                                            case "next":
                                                a > -1 && a < o && (i = a + 1) } this.setState({ inputIsHidden: -1 !== i, focusedValue: n[i] }) } } } }, { key: "focusOption", value: function() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "first",
                                    t = this.props.pageSize,
                                    n = this.state.focusedOption,
                                    r = this.getFocusableOptions(); if (r.length) { var a = 0,
                                        o = r.indexOf(n);
                                    n || (o = -1), "up" === e ? a = o > 0 ? o - 1 : r.length - 1 : "down" === e ? a = (o + 1) % r.length : "pageup" === e ? (a = o - t) < 0 && (a = 0) : "pagedown" === e ? (a = o + t) > r.length - 1 && (a = r.length - 1) : "last" === e && (a = r.length - 1), this.scrollToFocusedOptionOnUpdate = !0, this.setState({ focusedOption: r[a], focusedValue: null }) } } }, { key: "getTheme", value: function() { return this.props.theme ? "function" == typeof this.props.theme ? this.props.theme(to) : Re(Re({}, to), this.props.theme) : to } }, { key: "getCommonProps", value: function() { var e = this.clearValue,
                                    t = this.cx,
                                    n = this.getStyles,
                                    r = this.getClassNames,
                                    a = this.getValue,
                                    o = this.selectOption,
                                    i = this.setValue,
                                    l = this.props,
                                    s = l.isMulti,
                                    c = l.isRtl,
                                    d = l.options; return { clearValue: e, cx: t, getStyles: n, getClassNames: r, getValue: a, hasValue: this.hasValue(), isMulti: s, isRtl: c, options: d, selectOption: o, selectProps: l, setValue: i, theme: this.getTheme() } } }, { key: "hasValue", value: function() { return this.state.selectValue.length > 0 } }, { key: "hasOptions", value: function() { return !!this.getFocusableOptions().length } }, { key: "isClearable", value: function() { var e = this.props,
                                    t = e.isClearable,
                                    n = e.isMulti; return void 0 === t ? n : t } }, { key: "isOptionDisabled", value: function(e, t) { return co(this.props, e, t) } }, { key: "isOptionSelected", value: function(e, t) { return uo(this.props, e, t) } }, { key: "filterOption", value: function(e, t) { return ho(this.props, e, t) } }, { key: "formatOptionLabel", value: function(e, t) { if ("function" == typeof this.props.formatOptionLabel) { var n = this.props.inputValue,
                                        r = this.state.selectValue; return this.props.formatOptionLabel(e, { context: t, inputValue: n, selectValue: r }) } return this.getOptionLabel(e) } }, { key: "formatGroupLabel", value: function(e) { return this.props.formatGroupLabel(e) } }, { key: "startListeningComposition", value: function() { document && document.addEventListener && (document.addEventListener("compositionstart", this.onCompositionStart, !1), document.addEventListener("compositionend", this.onCompositionEnd, !1)) } }, { key: "stopListeningComposition", value: function() { document && document.removeEventListener && (document.removeEventListener("compositionstart", this.onCompositionStart), document.removeEventListener("compositionend", this.onCompositionEnd)) } }, { key: "startListeningToTouch", value: function() { document && document.addEventListener && (document.addEventListener("touchstart", this.onTouchStart, !1), document.addEventListener("touchmove", this.onTouchMove, !1), document.addEventListener("touchend", this.onTouchEnd, !1)) } }, { key: "stopListeningToTouch", value: function() { document && document.removeEventListener && (document.removeEventListener("touchstart", this.onTouchStart), document.removeEventListener("touchmove", this.onTouchMove), document.removeEventListener("touchend", this.onTouchEnd)) } }, { key: "renderInput", value: function() { var e = this.props,
                                    t = e.isDisabled,
                                    n = e.isSearchable,
                                    a = e.inputId,
                                    o = e.inputValue,
                                    i = e.tabIndex,
                                    l = e.form,
                                    s = e.menuIsOpen,
                                    c = e.required,
                                    d = this.getComponents().Input,
                                    u = this.state,
                                    h = u.inputIsHidden,
                                    m = u.ariaSelection,
                                    p = this.commonProps,
                                    f = a || this.getElementId("input"),
                                    v = Re(Re(Re({ "aria-autocomplete": "list", "aria-expanded": s, "aria-haspopup": !0, "aria-errormessage": this.props["aria-errormessage"], "aria-invalid": this.props["aria-invalid"], "aria-label": this.props["aria-label"], "aria-labelledby": this.props["aria-labelledby"], "aria-required": c, role: "combobox" }, s && { "aria-controls": this.getElementId("listbox"), "aria-owns": this.getElementId("listbox") }), !n && { "aria-readonly": !0 }), this.hasValue() ? "initial-input-focus" === (null == m ? void 0 : m.action) && { "aria-describedby": this.getElementId("live-region") } : { "aria-describedby": this.getElementId("placeholder") }); return n ? r.createElement(d, Be({}, p, { autoCapitalize: "none", autoComplete: "off", autoCorrect: "off", id: f, innerRef: this.getInputRef, isDisabled: t, isHidden: h, onBlur: this.onInputBlur, onChange: this.handleInputChange, onFocus: this.onInputFocus, spellCheck: "false", tabIndex: i, form: l, type: "text", value: o }, v)) : r.createElement(Fa, Be({ id: f, innerRef: this.getInputRef, onBlur: this.onInputBlur, onChange: Tr, onFocus: this.onInputFocus, disabled: t, tabIndex: i, inputMode: "none", form: l, value: "" }, v)) } }, { key: "renderPlaceholderOrValue", value: function() { var e = this,
                                    t = this.getComponents(),
                                    n = t.MultiValue,
                                    a = t.MultiValueContainer,
                                    o = t.MultiValueLabel,
                                    i = t.MultiValueRemove,
                                    l = t.SingleValue,
                                    s = t.Placeholder,
                                    c = this.commonProps,
                                    d = this.props,
                                    u = d.controlShouldRenderValue,
                                    h = d.isDisabled,
                                    m = d.isMulti,
                                    p = d.inputValue,
                                    f = d.placeholder,
                                    v = this.state,
                                    g = v.selectValue,
                                    y = v.focusedValue,
                                    b = v.isFocused; if (!this.hasValue() || !u) return p ? null : r.createElement(s, Be({}, c, { key: "placeholder", isDisabled: h, isFocused: b, innerProps: { id: this.getElementId("placeholder") } }), f); if (m) return g.map((function(t, l) { var s = t === y,
                                        d = "".concat(e.getOptionLabel(t), "-").concat(e.getOptionValue(t)); return r.createElement(n, Be({}, c, { components: { Container: a, Label: o, Remove: i }, isFocused: s, isDisabled: h, key: d, index: l, removeProps: { onClick: function() { return e.removeValue(t) }, onTouchEnd: function() { return e.removeValue(t) }, onMouseDown: function(e) { e.preventDefault() } }, data: t }), e.formatOptionLabel(t, "value")) })); if (p) return null; var w = g[0]; return r.createElement(l, Be({}, c, { data: w, isDisabled: h }), this.formatOptionLabel(w, "value")) } }, { key: "renderClearIndicator", value: function() { var e = this.getComponents().ClearIndicator,
                                    t = this.commonProps,
                                    n = this.props,
                                    a = n.isDisabled,
                                    o = n.isLoading,
                                    i = this.state.isFocused; if (!this.isClearable() || !e || a || !this.hasValue() || o) return null; var l = { onMouseDown: this.onClearIndicatorMouseDown, onTouchEnd: this.onClearIndicatorTouchEnd, "aria-hidden": "true" }; return r.createElement(e, Be({}, t, { innerProps: l, isFocused: i })) } }, { key: "renderLoadingIndicator", value: function() { var e = this.getComponents().LoadingIndicator,
                                    t = this.commonProps,
                                    n = this.props,
                                    a = n.isDisabled,
                                    o = n.isLoading,
                                    i = this.state.isFocused; return e && o ? r.createElement(e, Be({}, t, { innerProps: { "aria-hidden": "true" }, isDisabled: a, isFocused: i })) : null } }, { key: "renderIndicatorSeparator", value: function() { var e = this.getComponents(),
                                    t = e.DropdownIndicator,
                                    n = e.IndicatorSeparator; if (!t || !n) return null; var a = this.commonProps,
                                    o = this.props.isDisabled,
                                    i = this.state.isFocused; return r.createElement(n, Be({}, a, { isDisabled: o, isFocused: i })) } }, { key: "renderDropdownIndicator", value: function() { var e = this.getComponents().DropdownIndicator; if (!e) return null; var t = this.commonProps,
                                    n = this.props.isDisabled,
                                    a = this.state.isFocused,
                                    o = { onMouseDown: this.onDropdownIndicatorMouseDown, onTouchEnd: this.onDropdownIndicatorTouchEnd, "aria-hidden": "true" }; return r.createElement(e, Be({}, t, { innerProps: o, isDisabled: n, isFocused: a })) } }, { key: "renderMenu", value: function() { var e = this,
                                    t = this.getComponents(),
                                    n = t.Group,
                                    a = t.GroupHeading,
                                    o = t.Menu,
                                    i = t.MenuList,
                                    l = t.MenuPortal,
                                    s = t.LoadingMessage,
                                    c = t.NoOptionsMessage,
                                    d = t.Option,
                                    u = this.commonProps,
                                    h = this.state.focusedOption,
                                    m = this.props,
                                    p = m.captureMenuScroll,
                                    f = m.inputValue,
                                    v = m.isLoading,
                                    g = m.loadingMessage,
                                    y = m.minMenuHeight,
                                    b = m.maxMenuHeight,
                                    w = m.menuIsOpen,
                                    z = m.menuPlacement,
                                    x = m.menuPosition,
                                    A = m.menuPortalTarget,
                                    k = m.menuShouldBlockScroll,
                                    S = m.menuShouldScrollIntoView,
                                    M = m.noOptionsMessage,
                                    E = m.onMenuScrollToTop,
                                    C = m.onMenuScrollToBottom; if (!w) return null; var T, H = function(t, n) { var a = t.type,
                                        o = t.data,
                                        i = t.isDisabled,
                                        l = t.isSelected,
                                        s = t.label,
                                        c = t.value,
                                        m = h === o,
                                        p = i ? void 0 : function() { return e.onOptionHover(o) },
                                        f = i ? void 0 : function() { return e.selectOption(o) },
                                        v = "".concat(e.getElementId("option"), "-").concat(n),
                                        g = { id: v, onClick: f, onMouseMove: p, onMouseOver: p, tabIndex: -1 }; return r.createElement(d, Be({}, u, { innerProps: g, data: o, isDisabled: i, isSelected: l, key: v, label: s, type: a, value: c, isFocused: m, innerRef: m ? e.getFocusedOptionRef : void 0 }), e.formatOptionLabel(t.data, "menu")) }; if (this.hasOptions()) T = this.getCategorizedOptions().map((function(t) { if ("group" === t.type) { var o = t.data,
                                            i = t.options,
                                            l = t.index,
                                            s = "".concat(e.getElementId("group"), "-").concat(l),
                                            c = "".concat(s, "-heading"); return r.createElement(n, Be({}, u, { key: s, data: o, options: i, Heading: a, headingProps: { id: c, data: t.data }, label: e.formatGroupLabel(t.data) }), t.options.map((function(e) { return H(e, "".concat(l, "-").concat(e.index)) }))) } if ("option" === t.type) return H(t, "".concat(t.index)) }));
                                else if (v) { var L = g({ inputValue: f }); if (null === L) return null;
                                    T = r.createElement(s, u, L) } else { var I = M({ inputValue: f }); if (null === I) return null;
                                    T = r.createElement(c, u, I) } var j = { minMenuHeight: y, maxMenuHeight: b, menuPlacement: z, menuPosition: x, menuShouldScrollIntoView: S },
                                    V = r.createElement(ea, Be({}, u, j), (function(t) { var n = t.ref,
                                            a = t.placerProps,
                                            l = a.placement,
                                            s = a.maxHeight; return r.createElement(o, Be({}, u, j, { innerRef: n, innerProps: { onMouseDown: e.onMenuMouseDown, onMouseMove: e.onMenuMouseMove, id: e.getElementId("listbox") }, isLoading: v, placement: l }), r.createElement($a, { captureEnabled: p, onTopArrive: E, onBottomArrive: C, lockEnabled: k }, (function(t) { return r.createElement(i, Be({}, u, { innerRef: function(n) { e.getMenuListRef(n), t(n) }, isLoading: v, maxHeight: s, focusedOption: h }), T) }))) })); return A || "fixed" === x ? r.createElement(l, Be({}, u, { appendTo: A, controlElement: this.controlRef, menuPlacement: z, menuPosition: x }), V) : V } }, { key: "renderFormField", value: function() { var e = this,
                                    t = this.props,
                                    n = t.delimiter,
                                    a = t.isDisabled,
                                    o = t.isMulti,
                                    i = t.name,
                                    l = t.required,
                                    s = this.state.selectValue; if (l && !this.hasValue() && !a) return r.createElement(Ja, { name: i, onFocus: this.onValueInputFocus }); if (i && !a) { if (o) { if (n) { var c = s.map((function(t) { return e.getOptionValue(t) })).join(n); return r.createElement("input", { name: i, type: "hidden", value: c }) } var d = s.length > 0 ? s.map((function(t, n) { return r.createElement("input", { key: "i-".concat(n), name: i, type: "hidden", value: e.getOptionValue(t) }) })) : r.createElement("input", { name: i, type: "hidden", value: "" }); return r.createElement("div", null, d) } var u = s[0] ? this.getOptionValue(s[0]) : ""; return r.createElement("input", { name: i, type: "hidden", value: u }) } } }, { key: "renderLiveRegion", value: function() { var e = this.commonProps,
                                    t = this.state,
                                    n = t.ariaSelection,
                                    a = t.focusedOption,
                                    o = t.focusedValue,
                                    i = t.isFocused,
                                    l = t.selectValue,
                                    s = this.getFocusableOptions(); return r.createElement(Ea, Be({}, e, { id: this.getElementId("live-region"), ariaSelection: n, focusedOption: a, focusedValue: o, isFocused: i, selectValue: l, focusableOptions: s })) } }, { key: "render", value: function() { var e = this.getComponents(),
                                    t = e.Control,
                                    n = e.IndicatorsContainer,
                                    a = e.SelectContainer,
                                    o = e.ValueContainer,
                                    i = this.props,
                                    l = i.className,
                                    s = i.id,
                                    c = i.isDisabled,
                                    d = i.menuIsOpen,
                                    u = this.state.isFocused,
                                    h = this.commonProps = this.getCommonProps(); return r.createElement(a, Be({}, h, { className: l, innerProps: { id: s, onKeyDown: this.onKeyDown }, isDisabled: c, isFocused: u }), this.renderLiveRegion(), r.createElement(t, Be({}, h, { innerRef: this.getControlRef, innerProps: { onMouseDown: this.onControlMouseDown, onTouchEnd: this.onControlTouchEnd }, isDisabled: c, isFocused: u, menuIsOpen: d }), r.createElement(o, Be({}, h, { isDisabled: c }), this.renderPlaceholderOrValue(), this.renderInput()), r.createElement(n, Be({}, h, { isDisabled: c }), this.renderClearIndicator(), this.renderLoadingIndicator(), this.renderIndicatorSeparator(), this.renderDropdownIndicator())), this.renderMenu(), this.renderFormField()) } }], a = [{ key: "getDerivedStateFromProps", value: function(e, t) { var n = t.prevProps,
                                    r = t.clearFocusValueOnUpdate,
                                    a = t.inputIsHiddenAfterUpdate,
                                    o = t.ariaSelection,
                                    i = t.isFocused,
                                    l = t.prevWasFocused,
                                    s = e.options,
                                    c = e.value,
                                    d = e.menuIsOpen,
                                    u = e.inputValue,
                                    h = e.isMulti,
                                    m = Ir(c),
                                    p = {}; if (n && (c !== n.value || s !== n.options || d !== n.menuIsOpen || u !== n.inputValue)) { var f = d ? function(e, t) { return oo(ao(e, t)) }(e, m) : [],
                                        v = r ? function(e, t) { var n = e.focusedValue,
                                                r = e.selectValue.indexOf(n); if (r > -1) { if (t.indexOf(n) > -1) return n; if (r < t.length) return t[r] } return null }(t, m) : null,
                                        g = function(e, t) { var n = e.focusedOption; return n && t.indexOf(n) > -1 ? n : t[0] }(t, f);
                                    p = { selectValue: m, focusedOption: g, focusedValue: v, clearFocusValueOnUpdate: !1 } } var y = null != a && e !== n ? { inputIsHidden: a, inputIsHiddenAfterUpdate: void 0 } : {},
                                    b = o,
                                    w = i && l; return i && !w && (b = { value: Gr(h, m, m[0] || null), options: m, action: "initial-input-focus" }, w = !l), "initial-input-focus" === (null == o ? void 0 : o.action) && (b = null), Re(Re(Re({}, p), y), {}, { prevProps: e, ariaSelection: b, prevWasFocused: w }) } }], n && We(t.prototype, n), a && We(t, a), Object.defineProperty(t, "prototype", { writable: !1 }), i }();
                fo.defaultProps = no; var vo = (0, r.forwardRef)((function(e, t) { var n = function(e) { var t = e.defaultInputValue,
                                n = void 0 === t ? "" : t,
                                a = e.defaultMenuIsOpen,
                                o = void 0 !== a && a,
                                i = e.defaultValue,
                                l = void 0 === i ? null : i,
                                s = e.inputValue,
                                c = e.menuIsOpen,
                                d = e.onChange,
                                u = e.onInputChange,
                                h = e.onMenuClose,
                                m = e.onMenuOpen,
                                p = e.value,
                                f = Ne(e, _e),
                                v = Fe((0, r.useState)(void 0 !== s ? s : n), 2),
                                g = v[0],
                                y = v[1],
                                b = Fe((0, r.useState)(void 0 !== c ? c : o), 2),
                                w = b[0],
                                z = b[1],
                                x = Fe((0, r.useState)(void 0 !== p ? p : l), 2),
                                A = x[0],
                                k = x[1],
                                S = (0, r.useCallback)((function(e, t) { "function" == typeof d && d(e, t), k(e) }), [d]),
                                M = (0, r.useCallback)((function(e, t) { var n; "function" == typeof u && (n = u(e, t)), y(void 0 !== n ? n : e) }), [u]),
                                E = (0, r.useCallback)((function() { "function" == typeof m && m(), z(!0) }), [m]),
                                C = (0, r.useCallback)((function() { "function" == typeof h && h(), z(!1) }), [h]),
                                T = void 0 !== s ? s : g,
                                H = void 0 !== c ? c : w,
                                L = void 0 !== p ? p : A; return Re(Re({}, f), {}, { inputValue: T, menuIsOpen: H, onChange: S, onInputChange: M, onMenuClose: C, onMenuOpen: E, value: L }) }(e); return r.createElement(fo, Be({ ref: t }, n)) })),
                    go = vo,
                    yo = function() {
                        function e() {} return e.prototype.getCompatibleCell = function(e) { var t; try { t = se(e, "selectedValue", "string") } catch (e) { t = void 0 } var n, r, a = se(e, "values", "object"),
                                o = t ? parseFloat(t) : NaN,
                                i = !0; try { i = se(e, "isDisabled", "boolean") } catch (e) { i = !1 } try { n = se(e, "inputValue", "string") } catch (e) { n = void 0 } try { r = se(e, "isOpen", "boolean") } catch (e) { r = !1 } var l = t || ""; return h(h({}, e), { selectedValue: t, text: l, value: o, values: a, isDisabled: i, isOpen: r, inputValue: n }) }, e.prototype.update = function(e, t) { var n = e.values.some((function(e) { return e.value === t.text })) ? t.text : void 0; return this.getCompatibleCell(h(h({}, e), { selectedValue: n, isOpen: t.isOpen, inputValue: t.inputValue })) }, e.prototype.getClassName = function(e, t) { var n = e.isOpen ? "open" : "closed"; return "".concat(e.className ? e.className : "").concat(n) }, e.prototype.handleKeyDown = function(e, t, n, r, a, o, i) { if ((t === x.SPACE || t === x.ENTER) && !r) return { cell: this.getCompatibleCell(h(h({}, e), { isOpen: !e.isOpen })), enableEditMode: !1 }; var l = xe(o, r, i); return n || a || !de(t) || r && t === x.SPACE ? { cell: e, enableEditMode: !1 } : { cell: this.getCompatibleCell(h(h({}, e), { inputValue: l, isOpen: !e.isOpen })), enableEditMode: !1 } }, e.prototype.handleCompositionEnd = function(e, t) { return { cell: h(h({}, e), { inputValue: t, isOpen: !e.isOpen }), enableEditMode: !1 } }, e.prototype.render = function(e, t, n) { var a = this; return r.createElement(bo, { onCellChanged: function(e) { return n(a.getCompatibleCell(e), !0) }, cell: e }) }, e }(),
                    bo = function(e) { var t = e.onCellChanged,
                            n = e.cell,
                            a = r.useRef(null),
                            o = r.useState(n.inputValue),
                            i = o[0],
                            l = o[1],
                            s = r.useMemo((function() { return n.values.find((function(e) { return e.value === n.text })) }), [n.text, n.values]); return r.useEffect((function() { n.isOpen && a.current && (a.current.focus(), l(n.inputValue)) }), [n.isOpen, n.inputValue]), r.createElement("div", { style: { width: "100%" }, onPointerDown: function(e) { return t(h(h({}, n), { isOpen: !0 })) } }, r.createElement(go, h({}, n.inputValue && { inputValue: i, defaultInputValue: i, onInputChange: function(e) { return l(e) } }, { isSearchable: !0, ref: a }, void 0 !== n.isOpen && { menuIsOpen: n.isOpen }, { onMenuClose: function() { return t(h(h({}, n), { isOpen: !n.isOpen, inputValue: void 0 })) }, onMenuOpen: function() { return t(h(h({}, n), { isOpen: !0 })) }, onChange: function(e) { return t(h(h({}, n), { selectedValue: e.value, isOpen: !1, inputValue: void 0 })) }, blurInputOnSelect: !0, defaultValue: s, value: void 0 !== s ? s : null, isDisabled: n.isDisabled, options: n.values, onKeyDown: function(e) { if (e.stopPropagation(), "Escape" === e.key) return a.current.blur(), t(h(h({}, n), { isOpen: !1, inputValue: void 0 })) }, components: { Option: wo, Menu: zo }, styles: { container: function(e) { var t; return h(h(h({}, e), { width: "100%", height: "100%" }), null === (t = n.styles) || void 0 === t ? void 0 : t.container) }, control: function(e) { var t; return h(h(h({}, e), { border: "none", borderColor: "transparent", minHeight: "25px", background: "transparent", boxShadow: "none" }), null === (t = n.styles) || void 0 === t ? void 0 : t.control) }, indicatorsContainer: function(e) { var t; return h(h(h({}, e), { paddingTop: "0px" }), null === (t = n.styles) || void 0 === t ? void 0 : t.indicatorsContainer) }, dropdownIndicator: function(e) { var t; return h(h(h({}, e), { padding: "0px 4px" }), null === (t = n.styles) || void 0 === t ? void 0 : t.dropdownIndicator) }, singleValue: function(e) { var t; return h(h(h({}, e), { color: "inherit" }), null === (t = n.styles) || void 0 === t ? void 0 : t.singleValue) }, indicatorSeparator: function(e) { var t; return h(h(h({}, e), { marginTop: "4px", marginBottom: "4px" }), null === (t = n.styles) || void 0 === t ? void 0 : t.indicatorSeparator) }, input: function(e) { var t; return h(h(h({}, e), { padding: 0 }), null === (t = n.styles) || void 0 === t ? void 0 : t.input) }, valueContainer: function(e) { var t; return h(h(h({}, e), { padding: "0 8px" }), null === (t = n.styles) || void 0 === t ? void 0 : t.valueContainer) } } }))) },
                    wo = function(e) { var t = e.innerProps,
                            n = e.label,
                            a = e.isSelected,
                            o = e.isFocused,
                            i = e.isDisabled; return r.createElement("div", h({}, t, { onPointerDown: function(e) { return e.stopPropagation() }, className: "rg-dropdown-option".concat(a ? " selected" : "").concat(o ? " focused" : "").concat(i ? " disabled" : "") }), n) },
                    zo = function(e) { var t = e.innerProps,
                            n = e.children; return r.createElement("div", h({}, t, { className: "rg-dropdown-menu", onPointerDown: function(e) { return e.stopPropagation() } }), n) },
                    xo = { text: new He, number: new Te, header: new Me, checkbox: new ce, date: new Ae, email: new ke, time: new Le, chevron: new Se, dropdown: new yo },
                    Ao = function(e) { var t = [],
                            n = 0; return e.forEach((function(r, a) { if (e[a - 1]) { var o = e[a - 1];
                                r.idx - o.idx == 1 ? t[n] ? t[n].push(r) : t.push([o, r]) : (t.push([r]), n += 1) } else t.push([r]) })), t },
                    ko = function(e) { var t = [],
                            n = 0; return e.forEach((function(r, a) { if (e[a - 1]) { var o = e[a - 1];
                                r.idx - o.idx == 1 ? t[n] ? t[n].push(r) : t.push([o, r]) : (t.push([r]), n += 1) } else t.push([r]) })), t };

                function So(e, t) { return t = "row" === t.selectionMode && t.selectedIds.length > 0 ? function(e) { var t = e.cellMatrix.first.column,
                            n = e.cellMatrix.last.column,
                            r = e.cellMatrix.rows.filter((function(t) { return e.selectedIds.includes(t.rowId) })).sort((function(e, t) { return e.idx - t.idx })),
                            a = Ao(r).map((function(r) { return e.cellMatrix.getRange(v(r[0], t), v(r[r.length - 1], n)) })),
                            o = e.selectedRanges.length - 1; return e.focusedLocation && a.forEach((function(t, n) { t.rows.forEach((function(t) { var r;
                                (null === (r = e.focusedLocation) || void 0 === r ? void 0 : r.row.rowId) === t.rowId && (o = n) })) })), h(h({}, e), { selectionMode: "row", activeSelectedRangeIdx: o, selectedRanges: f([], a, !0), selectedIndexes: r.map((function(e) { return e.idx })), selectedIds: r.map((function(e) { return e.rowId })) }) }(t) : "column" === t.selectionMode && t.selectedIds.length > 0 ? function(e) { var t = e.cellMatrix.first.row,
                            n = e.cellMatrix.last.row,
                            r = e.cellMatrix.columns.filter((function(t) { return e.selectedIds.includes(t.columnId) })).sort((function(e, t) { return e.idx - t.idx })),
                            a = ko(r).map((function(r) { return e.cellMatrix.getRange(v(t, r[0]), v(n, r[r.length - 1])) })),
                            o = e.selectedRanges.length - 1; return e.focusedLocation && a.forEach((function(t, n) { t.columns.forEach((function(t) { var r;
                                (null === (r = e.focusedLocation) || void 0 === r ? void 0 : r.column.columnId) === t.columnId && (o = n) })) })), h(h({}, e), { selectionMode: "column", activeSelectedRangeIdx: o, selectedRanges: f([], a, !0), selectedIndexes: r.map((function(e) { return e.idx })), selectedIds: r.map((function(e) { return e.columnId })) }) }(t) : h(h({}, t), { selectedRanges: f([], t.selectedRanges, !0).map((function(e) { return t.cellMatrix.validateRange(e) })) }) }

                function Mo(e, t) { return h(h({}, t), { enableFillHandle: !!e.enableFillHandle, enableRangeSelection: !!e.enableRangeSelection, enableColumnSelection: !!e.enableColumnSelection, enableRowSelection: !!e.enableRowSelection }) } var Eo = function(e, t) { var n, r, a, o; return (null === (n = e.focusLocation) || void 0 === n ? void 0 : n.columnId) !== (null === (r = t.focusedLocation) || void 0 === r ? void 0 : r.column.columnId) || (null === (a = e.focusLocation) || void 0 === a ? void 0 : a.rowId) !== (null === (o = t.focusedLocation) || void 0 === o ? void 0 : o.row.rowId) || void 0 !== e.stickyRightColumns && e.stickyRightColumns !== t.rightStickyColumns || void 0 !== e.stickyBottomRows && e.stickyBottomRows !== t.bottomStickyRows },
                    Co = function(e) { return function(t) { return function(n) { return n(e, t) } } },
                    To = function(e, t) { return !t.cellMatrix || e !== t.cellMatrix.props || void 0 !== e.stickyLeftColumns && e.stickyLeftColumns !== t.leftStickyColumns || void 0 !== e.stickyTopRows && e.stickyTopRows !== t.topStickyRows || void 0 !== e.stickyBottomRows && e.stickyBottomRows !== t.bottomStickyRows || void 0 !== e.stickyRightColumns && e.stickyRightColumns !== t.rightStickyColumns },
                    Ho = function(e, t) { var n; return e.highlights !== (null === (n = t.props) || void 0 === n ? void 0 : n.highlights) };

                function Lo(e, t) { return t.props !== e && (t = h(h({}, t), { props: e })), t }

                function Io(e, t) { var n = new le; return h(h({}, t), { cellMatrix: n.setProps(e).fillRowsAndCols({ leftStickyColumns: t.leftStickyColumns || 0, topStickyRows: t.topStickyRows || 0, rightStickyColumns: t.rightStickyColumns || 0, bottomStickyRows: t.bottomStickyRows || 0 }).setRangesToRenderLookup().fillSticky({ leftStickyColumns: t.leftStickyColumns || 0, topStickyRows: t.topStickyRows || 0, rightStickyColumns: t.rightStickyColumns || 0, bottomStickyRows: t.bottomStickyRows || 0 }).fillScrollableRange({ leftStickyColumns: t.leftStickyColumns || 0, topStickyRows: t.topStickyRows || 0, rightStickyColumns: t.rightStickyColumns || 0, bottomStickyRows: t.bottomStickyRows || 0 }).setEdgeLocations().getCellMatrix() }) }

                function jo(e, t) { return t.cellMatrix.columns.length > 0 && t.focusedLocation && !t.currentlyEditedCell && (t = h(h({}, t), { focusedLocation: t.cellMatrix.validateLocation(t.focusedLocation) })), t }

                function Vo(e, t) { return t.visibleRange && (t = te(t)), t }

                function Oo(e, t) { return h(h({}, t), { cellTemplates: h(h({}, xo), e.customCellTemplates) }) }

                function Ro(e, t) { return h(h({}, t), { enableGroupIdRender: !!e.enableGroupIdRender }) }

                function Po(e, t) { return h(h({}, t), { disableVirtualScrolling: !!e.disableVirtualScrolling }) }

                function Do(e, t) { var n, r, a = null === (n = e.highlights) || void 0 === n ? void 0 : n.filter((function(e) { return void 0 !== t.cellMatrix.rowIndexLookup[e.rowId] && void 0 !== t.cellMatrix.columnIndexLookup[e.columnId] })); return (null == a ? void 0 : a.length) !== (null === (r = e.highlights) || void 0 === r ? void 0 : r.length) && console.error('Data inconsistency in ReactGrid "highlights" prop'), h(h({}, t), { highlightLocations: a || [] }) }

                function Fo(e, t) { var n = e.initialFocusLocation,
                        r = !!t.focusedLocation; if (n && !t.focusedLocation)
                        if (_o(t, n)) console.error('Data inconsistency in ReactGrid "initialFocusLocation" prop');
                        else { var a = t.cellMatrix.getLocationById(n.rowId, n.columnId);
                            t = E(t, a) } var o = t.focusedLocation; return !r && o && (t = g(t, o)), t }

                function No(e, t) { var n = e.focusLocation,
                        r = !!t.focusedLocation; if (n)
                        if (_o(t, n)) console.error('Data inconsistency in ReactGrid "focusLocation" prop');
                        else { var a = t.cellMatrix.getLocationById(n.rowId, n.columnId);
                            t = E(t, a) } var o = t.focusedLocation; return !r && o && e.focusLocation && t.selectedRanges.length <= 1 && (t = g(t, o)), t }

                function _o(e, t) { return !(void 0 !== e.cellMatrix.columnIndexLookup[t.columnId] && void 0 !== e.cellMatrix.rowIndexLookup[t.rowId]) } var Bo = (0, r.createContext)({}),
                    Wo = function(e) { var t = e.children,
                            n = e.state; return r.createElement(Bo.Provider, { value: n }, t) },
                    Uo = function() { return r.useContext(Bo) },
                    qo = function(e) { var t, n, a, o, i, l, s, c, d, u, h, m, p, f, v = e.cellRenderer,
                            g = Uo(),
                            y = g.cellMatrix,
                            b = function(e) { return e.cellMatrix.ranges.stickyTopRange.height > 0 }(g),
                            w = function(e) { return !!(e.cellMatrix.scrollableRange.height > 0 && e.cellMatrix.scrollableRange.first.column && e.cellMatrix.scrollableRange.first.row && e.cellMatrix.scrollableRange.last.row && e.visibleRange && e.visibleRange.height > 0) }(g),
                            z = function(e) { return e.cellMatrix.ranges.stickyLeftRange.width > 0 }(g),
                            x = function(e) { return !!(e.visibleRange && e.visibleRange.width > 0) }(g),
                            A = function(e) { return !!(e.cellMatrix.ranges.stickyBottomRange.height > 0 && e.cellMatrix.rows.length > 0) }(g),
                            k = function(e) { return !!(e.cellMatrix.ranges.stickyRightRange.width > 0) }(g); if (!(b || w || z || x)) return null; var S = void 0,
                            M = g.visibleRange;
                        w && (S = y.scrollableRange.slice(M, "rows")); var E = y.ranges.stickyTopRange.height ? -y.ranges.stickyBottomRange.height : 0,
                            C = y.ranges.stickyLeftRange.width ? -y.ranges.stickyRightRange.width : 0,
                            T = y.ranges.stickyRightRange.width ? -y.ranges.stickyLeftRange.width : 0,
                            H = y.ranges.stickyBottomRange.height ? -y.ranges.stickyTopRange.height : 0,
                            L = 0 !== y.scrollableRange.rows.length ? y.ranges.stickyTopRange.height : 0,
                            I = 0 !== y.scrollableRange.columns.length ? y.ranges.stickyLeftRange.width : 0,
                            j = 0 !== y.scrollableRange.rows.length ? y.ranges.stickyBottomRange.height : 0,
                            V = 0 !== y.scrollableRange.columns.length ? y.ranges.stickyRightRange.width : 0; return r.createElement(r.Fragment, null, r.createElement(ml, { renderChildren: w && x, className: "rg-pane-center-middle", style: { position: "relative", width: "calc(100% - ".concat(y.ranges.stickyLeftRange.width + y.ranges.stickyRightRange.width, "px)"), height: y.scrollableRange.height, marginLeft: C, marginRight: T, marginTop: E, marginBottom: H, order: 4 } }, r.createElement(pl, { state: g, range: bl(S)(M), borders: { bottom: !A, right: !k, left: !z, top: !b }, cellRenderer: v })), r.createElement(vl, { renderCondition: z, className: "shadow-left", zIndex: 2, style: { width: y.ranges.stickyLeftRange.width, height: y.height, marginTop: -y.height, order: 9 } }), r.createElement(vl, { renderCondition: k, className: "shadow-right", zIndex: 2, style: { width: y.ranges.stickyRightRange.width, height: y.height, marginLeft: -y.ranges.stickyRightRange.width, marginTop: b || A ? -y.height : 0, order: b || A ? 12 : 8 } }), r.createElement(vl, { renderCondition: b, className: "shadow-top", zIndex: 1, style: { width: (null === (t = g.props) || void 0 === t ? void 0 : t.enableFullWidthHeader) ? "calc(100%)" : y.width, height: y.ranges.stickyTopRange.height, marginTop: -y.height, order: 10 } }), r.createElement(vl, { renderCondition: A, className: "shadow-bottom", zIndex: 1, style: { width: (null === (n = g.props) || void 0 === n ? void 0 : n.enableFullWidthHeader) ? "calc(100%)" : y.width, height: y.ranges.stickyBottomRange.height, marginTop: -y.ranges.stickyBottomRange.height, order: 11 } }), r.createElement(ml, { renderChildren: x && A, className: "rg-pane-bottom", style: { width: "calc(100% - ".concat(y.ranges.stickyLeftRange.width + y.ranges.stickyRightRange.width, "px)"), height: y.ranges.stickyBottomRange.height, marginLeft: C, marginRight: T, marginTop: L, order: 7 } }, r.createElement(pl, { state: g, range: bl(y.ranges.stickyBottomRange)(M), borders: { top: !0, bottom: !0, right: !k, left: !z }, cellRenderer: v })), r.createElement(ml, { renderChildren: w && k || !S, className: "rg-pane-right", style: { height: y.scrollableRange.height, width: y.width - y.ranges.stickyLeftRange.width - y.scrollableRange.width, marginTop: E, marginBottom: H, marginLeft: I, order: 5 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyRightRange)(S || y.ranges.stickyLeftRange), borders: { left: !0, top: !b, bottom: !A }, cellRenderer: v })), r.createElement(ml, { renderChildren: b && x, className: "rg-pane-top", style: { width: "calc(100% - ".concat(y.ranges.stickyLeftRange.width + y.ranges.stickyRightRange.width, "px)"), height: y.ranges.stickyTopRange.height, marginBottom: j, marginLeft: C, marginRight: T, order: 1, zIndex: (null !== (o = null === (a = g.props) || void 0 === a ? void 0 : a.zIndexBase) && void 0 !== o ? o : 0) + 1 } }, r.createElement(pl, { state: g, range: bl(y.ranges.stickyTopRange)(M), borders: { top: !0, right: !k, left: !z }, cellRenderer: v })), r.createElement(ml, { renderChildren: w && z || !S, className: "rg-pane-left", style: { height: y.scrollableRange.height, width: y.width - y.scrollableRange.width - y.ranges.stickyRightRange.width, marginRight: V, marginBottom: H, marginTop: E, order: 3, zIndex: (null !== (l = null === (i = g.props) || void 0 === i ? void 0 : i.zIndexBase) && void 0 !== l ? l : 0) + 1 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyLeftRange)(S || y.ranges.stickyLeftRange), borders: { bottom: !A, top: !b, left: !0 }, cellRenderer: v })), r.createElement(ml, { renderChildren: A && k, className: "rg-pane-bottom rg-pane-right rg-pane-shadow shadow-bottom-right-corner", style: { height: y.ranges.stickyBottomRange.height, width: y.width - y.ranges.stickyLeftRange.width - y.scrollableRange.width, marginTop: L, marginLeft: I, order: 8, zIndex: (null !== (c = null === (s = g.props) || void 0 === s ? void 0 : s.zIndexBase) && void 0 !== c ? c : 0) + 1 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyRightRange)(y.ranges.stickyBottomRange), borders: { top: !0, left: !0, right: !0, bottom: !0 }, cellRenderer: v })), r.createElement(ml, { renderChildren: A && z, className: "rg-pane-bottom rg-pane-left rg-pane-shadow shadow-bottom-left-corner", style: { height: y.ranges.stickyBottomRange.height, width: y.width - y.ranges.stickyRightRange.width - y.scrollableRange.width, marginRight: V, marginTop: L, order: 6, zIndex: (null !== (u = null === (d = g.props) || void 0 === d ? void 0 : d.zIndexBase) && void 0 !== u ? u : 0) + 2 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyLeftRange)(y.ranges.stickyBottomRange), borders: { top: !0, left: !0, right: !0, bottom: !0 }, cellRenderer: v })), r.createElement(ml, { renderChildren: b && k, className: "rg-pane-top rg-pane-right rg-pane-shadow shadow-top-right-corner", style: { height: y.ranges.stickyTopRange.height, width: y.width - y.scrollableRange.width - y.ranges.stickyLeftRange.width, marginLeft: I, marginBottom: j, order: 2, zIndex: (null !== (m = null === (h = g.props) || void 0 === h ? void 0 : h.zIndexBase) && void 0 !== m ? m : 0) + 2 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyRightRange)(y.ranges.stickyTopRange), borders: { top: !0, left: !0, right: !0, bottom: !0 }, cellRenderer: v })), r.createElement(ml, { renderChildren: b && z, className: "rg-pane-top rg-pane-left rg-pane-shadow shadow-top-left-corner", style: { height: y.ranges.stickyTopRange.height, width: y.width - y.scrollableRange.width - y.ranges.stickyRightRange.width, marginRight: V, marginBottom: j, order: 0, zIndex: (null !== (f = null === (p = g.props) || void 0 === p ? void 0 : p.zIndexBase) && void 0 !== f ? f : 0) + 3 } }, r.createElement(pl, { state: g, range: wl(y.ranges.stickyLeftRange)(y.ranges.stickyTopRange), borders: { top: !0, left: !0, right: !0, bottom: !0 }, cellRenderer: v }))) },
                    Go = function() { var e = Uo(),
                            t = e.linePosition,
                            n = e.lineOrientation,
                            a = e.cellMatrix,
                            o = "vertical" === n,
                            i = Object.assign({}, o ? { left: t, height: a.height } : { top: t, width: a.width }); return -1 === t ? null : r.createElement("div", { className: "rg-line ".concat(o ? "rg-line-vertical" : "rg-line-horizontal"), style: i }) },
                    Ko = function() { var e = Uo(),
                            t = e.lineOrientation,
                            n = e.shadowSize,
                            a = e.shadowPosition,
                            o = e.shadowCursor,
                            i = e.cellMatrix,
                            l = "vertical" === t; return -1 === a ? null : r.createElement("div", { className: "rg-shadow", style: { cursor: o, top: l ? 0 : a, left: l ? a : 0, width: l ? n : i.width, height: l ? i.height : n } }) };

                function Zo(e) { return e.selectedRanges[e.activeSelectedRangeIdx] } var Yo = { type: "", text: "", value: NaN };

                function Xo(e, t, n) { void 0 === n && (n = !1); var r = function(e) { var t = document.createElement("div"),
                                n = document.createElement("table"); return n.setAttribute("empty-cells", "show"), n.setAttribute("data-reactgrid", "reactgrid-content"), { div: t, table: n, location: { row: e.first.row, column: e.first.column } } }(t),
                        a = r.div,
                        o = r.table,
                        i = (r.location, function(e, t, n, r, a) { var o = "",
                                i = ""; return n.rows.forEach((function(t) { var l = r.insertRow();
                                n.columns.forEach((function(n) { var r = l.insertCell(),
                                        s = S(e, { row: t, column: n }).cell,
                                        c = s.text || " ";
                                    r.textContent = c, o = "" === i ? s.text : o + (i === t.rowId ? "\t" : "\n") + c, i = t.rowId, r.setAttribute("data-reactgrid", JSON.stringify(s)), r.style.color = "initial", r.style.border = "initial", r.style.fontSize = "initial", r.style.backgroundColor = "initial",
                                        function(e, t, n) { n && (e = M(e, t, Yo)) }(e, { row: t, column: n }, a) })) })), o }(e, 0, t, o, n)); return function(e, t) { e.classList.add("rg-copy-container"), e.setAttribute("contenteditable", "true"), e.style.position = "fixed", e.style.top = "50%", e.style.left = "50%", e.appendChild(t) }(a, o), { div: a, text: i } }

                function $o(e, t, n) { return S(e, t).cell.groupId === n.groupId ? M(e, t, n) : (console.warn("New cells data can't be appended into location: ('".concat(t.column.columnId, "', '").concat(t.row.rowId, "'). Cell's 'groupId' field doesn't match!")), e) }

                function Qo(e, t) { var n, r, a = Zo(e); if (1 !== t.length || 1 !== t[0].length) { var o, i = e.cellMatrix; if (t.forEach((function(t, n) { return t.forEach((function(t, r) { var l = a.first.row.idx + n,
                                        s = a.first.column.idx + r;
                                    l <= i.last.row.idx && s <= i.last.column.idx && (o = i.getLocation(l, s), e = $o(e, o, t)) })) })), !o) return e; var l = i.getRange(a.first, o); return (null === (n = null == e ? void 0 : e.props) || void 0 === n ? void 0 : n.onSelectionChanging) && !e.props.onSelectionChanging([l]) ? e : ((null === (r = null == e ? void 0 : e.props) || void 0 === r ? void 0 : r.onSelectionChanged) && e.props.onSelectionChanged([l]), h(h({}, e), { selectedRanges: [i.getRange(a.first, o)], activeSelectedRangeIdx: 0 })) } return a.rows.forEach((function(n) { return a.columns.forEach((function(r) { e = $o(e, v(n, r), t[0][0]) })) })), e }

                function Jo(e) { return e.selectedRanges.map((function(e) { return e.rows.flatMap((function(t) { return e.columns.map((function(e) { return { columnId: e.columnId, rowId: t.rowId } })) })) })) }

                function ei() { return m(this, void 0, void 0, (function() { return p(this, (function(e) { switch (e.label) {
                                case 0:
                                    return [4, navigator.clipboard.readText().catch((function() { throw new Error("Failed to read textual data from clipboard!") }))];
                                case 1:
                                    return [2, e.sent().split("\n").map((function(e) { return e.split("\t").map((function(e) { return { type: "text", text: e, value: Ce(e) } })) }))] } })) })) }

                function ti(e) { return m(this, void 0, void 0, (function() { var t; return p(this, (function(n) { switch (n.label) {
                                case 0:
                                    return [4, e.getType("text/html").catch((function() { throw new Error("Failed to get HTML Blob data from clipboard!") }))];
                                case 1:
                                    return [4, n.sent().text().catch((function() { throw new Error("Failed to parse HTML Blob to text!") }))];
                                case 2:
                                    t = n.sent(); try { return [2, (new DOMParser).parseFromString(t, "text/html")] } catch (e) { throw new Error("Failed to parse HTML string to DOM!") } return [2] } })) })) }

                function ni(e) { var t, n; return m(this, void 0, void 0, (function() { var r, a, o, i, l, s, c, d; return p(this, (function(u) { switch (u.label) {
                                case 0:
                                    return r = [], (null === (t = e.firstElementChild) || void 0 === t ? void 0 : t.firstElementChild) ? [3, 2] : [4, ei()];
                                case 1:
                                    return [2, u.sent()];
                                case 2:
                                    for (a = e.firstElementChild.firstElementChild.children, o = 0; o < a.length; o++) { for (i = [], l = 0; l < a[o].children.length; l++) s = a[o].children[l].getAttribute("data-reactgrid"), c = s && JSON.parse(s), d = null !== (n = a[o].children[l].textContent) && void 0 !== n ? n : "", i.push(c || { type: "text", text: d, value: Ce(d) });
                                        r.push(i) } return [2, r] } })) })) } var ri = function() { var e = r.useRef(null),
                        t = Uo(),
                        n = t.contextMenuPosition,
                        a = t.selectedIds,
                        o = t.selectionMode,
                        i = n.left,
                        l = n.top; if (-1 !== l && -1 !== i && e.current) { var s = window.innerWidth,
                            c = window.innerHeight,
                            d = e.current.offsetWidth,
                            u = e.current.offsetHeight;
                        n.top = c - l < u ? c - u - 20 : l, n.left = s - i < d ? s - d - 20 : i } var f, v, g, y, b = function(e) { var t = $i(e); return [{ id: "copy", label: t.copyLabel, handler: function() { return ai(e, !1) } }, { id: "cut", label: t.cutLabel, handler: function() { return ai(e, !0) } }, { id: "paste", label: t.pasteLabel, handler: function() { return function(e) { var t = L() || I(); if (Zi() || t) { var n = $i(e),
                                                r = n.appleMobileDeviceContextMenuPasteAlert,
                                                a = n.otherBrowsersContextMenuPasteAlert,
                                                o = n.actionNotSupported;
                                            alert("".concat(o, " ").concat(t ? r : a)) } else m(void 0, void 0, void 0, (function() { var e, t, n, r, a; return p(this, (function(o) { switch (o.label) {
                                                    case 0:
                                                        return [4, navigator.clipboard.read()];
                                                    case 1:
                                                        return e = o.sent(), (t = e.find((function(e) { return e.types.includes("text/html") }))) ? [4, ti(t)] : [3, 3];
                                                    case 2:
                                                        return r = o.sent(), [3, 4];
                                                    case 3:
                                                        r = null, o.label = 4;
                                                    case 4:
                                                        return "reactgrid-content" === (null === (a = null == (n = r) ? void 0 : n.body.firstElementChild) || void 0 === a ? void 0 : a.getAttribute("data-reactgrid")) ? [2, ni(n.body)] : [4, ei()];
                                                    case 5:
                                                        return [2, o.sent()] } })) })).then((function(t) { e.update((function(e) { return Qo(e, t) })) })) }(e) } }] }(t),
                        w = (f = b, null !== (y = null === (g = null === (v = t.props) || void 0 === v ? void 0 : v.onContextMenu) || void 0 === g ? void 0 : g.call(v, "row" === t.selectionMode ? t.selectedIds : [], "column" === t.selectionMode ? t.selectedIds : [], t.selectionMode, f, Jo(t))) && void 0 !== y ? y : []); return w.length >= 0 && (b = w), r.createElement("div", { ref: e, className: "rg-context-menu", style: { visibility: -1 === l && -1 === i ? "hidden" : "visible", top: n.top + "px", left: n.left + "px" } }, b.map((function(e, n) { var i = e.handler,
                            l = e.id,
                            s = e.label; return r.createElement("div", { key: n, className: "rg-context-menu-option", onPointerDown: function(e) { return e.stopPropagation() }, onClick: function() { i("row" === o ? a : [], "column" === o ? a : [], o, Jo(t)), t.update((function(e) { return h(h(h({}, e), { contextMenuPosition: { top: -1, left: -1 } }), ("copy" === l || "cut" === l) && { copyRange: Zo(e) }) })) } }, s) }))) };

                function ai(e, t) { void 0 === t && (t = !1),
                        function(e, t) { var n;
                            void 0 === t && (t = !1); var r = Zo(e); if (r) { var a = Xo(e, r, t).div;
                                document.body.appendChild(a), a.focus(), document.execCommand("selectAll", !1, void 0), document.execCommand("copy"), document.body.removeChild(a), null === (n = e.hiddenFocusElement) || void 0 === n || n.focus() } }(e, t) } var oi = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.updateState = function(e) { return t.setState(e) }, t.stateUpdater = function(e) { return function(e, t, n, r) { var a = f([], e.queuedCellChanges, !0);
                                    a.length > 0 && (n.onCellsChanged && n.onCellsChanged(f([], a, !0)), a.forEach((function() { return e.queuedCellChanges.pop() }))), e !== t && r(e) }(e(t.state), t.state, t.props, t.updateState) }, t.pointerEventsController = new $(t.stateUpdater), t.eventHandlers = new ie(t.stateUpdater, t.pointerEventsController), t.cellMatrixBuilder = new le, t.state = h(h({ update: t.stateUpdater }, Ri), { currentBehavior: new Oi, cellMatrix: t.cellMatrixBuilder.setProps(t.props).fillRowsAndCols().setRangesToRenderLookup().fillSticky().fillScrollableRange().setEdgeLocations().getCellMatrix() }), t.clearSelections = function() { t.setState((function(e) { return 0 === e.selectedIds.length && 0 === e.selectedIndexes.length && 0 === e.selectedRanges.length ? null : { selectedIds: [], selectedIndexes: [], selectedRanges: [] } })) }, t } return u(t, e), t.getDerivedStateFromProps = function(e, t) { try { return function(e, t) { var n = Co(e);
                                    Ho(e, t) && (t = n(t)(Do)), t = n(t)(Lo), t = n(t)(Oo), t = n(t)(Ro); var r = To(e, t); return t = n(t)(oe), t = n(t)(Po), r && (t = n(t)(Io)), t = n(t)(So), t = n(t)(jo), r && (t = n(t)(Vo)), t = n(t)(Fo), Eo(e, t) && (t = n(t)(No)), n(t)(Mo) }(e, t) } catch (e) { return console.error(e), null } }, t.prototype.componentDidUpdate = function(e, t) { var n;!t.reactGridElement && this.state.reactGridElement && (null === (n = this.state.scrollableElement) || void 0 === n || n.addEventListener("scroll", this.eventHandlers.scrollHandler)),
                                function(e, t, n) { var r = n.focusedLocation; if (r) { var a = !(s(r, t.focusedLocation) || n.currentBehavior instanceof B),
                                            o = void 0 !== n.currentlyEditedCell && n.currentlyEditedCell !== t.currentlyEditedCell; if (a || o) { var i = K(n, r),
                                                l = i.left;
                                            U(n, i.top, l) } } }(0, t, this.state) }, t.prototype.componentDidMount = function() { window.addEventListener("resize", this.eventHandlers.windowResizeHandler) }, t.prototype.componentWillUnmount = function() { var e;
                            window.removeEventListener("resize", this.eventHandlers.windowResizeHandler), null === (e = this.state.scrollableElement) || void 0 === e || e.removeEventListener("scroll", this.eventHandlers.scrollHandler), this.setState({ contextMenuPosition: { top: -1, left: -1 } }) }, t.prototype.render = function() { var e = this.state,
                                t = this.eventHandlers; return e.legacyBrowserMode ? r.createElement(Wo, { state: e }, r.createElement(Qi, { eventHandlers: t })) : r.createElement(Wo, { state: e }, r.createElement(Yi, { eventHandlers: t }, r.createElement(qo, { cellRenderer: rl }), r.createElement(Go, null), r.createElement(Ko, null), r.createElement(ri, null), e.currentlyEditedCell && r.createElement(Ni, null))) }, t }(r.Component),
                    ii = function(e) { return function(t, n, r, a) { return e(r, r.cellMatrix.getLocation(n, t), !0, a) } }(E),
                    li = function(e) { return function(t) { if (t.focusedLocation) { var n = gi(t, t.focusedLocation.row.idx, 0); if (!n) { var r = t.cellMatrix.getLocation(t.focusedLocation.row.idx, 0),
                                        a = bi(t, r); return a ? e(a.column.idx, a.row.idx, t) : t } return e(n.column.idx, n.row.idx, t) } return t } }(ii),
                    si = function(e) { return function(t) { if (t.focusedLocation) { var n = gi(t, t.focusedLocation.row.idx, t.cellMatrix.columns.length - 1); if (!n) { var r = t.cellMatrix.getLocation(t.focusedLocation.row.idx, t.cellMatrix.columns.length - 1),
                                        a = yi(t, r); return a ? e(a.column.idx, a.row.idx, t) : t } return e(n.column.idx, n.row.idx, t) } return t } }(ii),
                    ci = function(e) { return function(t) { var n = yi(t, t.focusedLocation); return n ? e(n.column.idx, n.row.idx, t) : t } }(ii),
                    di = function(e) { return function(t, n) { var r = bi(t, t.focusedLocation); return r ? e(r.column.idx, r.row.idx, t, n) : t } }(ii),
                    ui = function(e) { return function(t) { var n = wi(t, t.focusedLocation); return n ? e(n.column.idx, n.row.idx, t) : t } }(ii),
                    hi = function(e) { return function(t, n) { var r = zi(t, t.focusedLocation); return r ? e(r.column.idx, r.row.idx, t, n) : t } }(ii),
                    mi = function(e) { return function(t) { return function(n) { var r = n.focusedLocation; if (!r) return n; var a = t(n, r); return e(r.column.idx, a, n) } } }(ii),
                    pi = mi((function(e, t) { var n, r = xi(e, e.cellMatrix.ranges.stickyTopRange.height + e.cellMatrix.ranges.stickyBottomRange.height),
                            a = e.cellMatrix.ranges.stickyTopRange.rows.length > 0,
                            o = a && G(e, t),
                            i = e.cellMatrix.scrollableRange.rows.length > 0,
                            l = i && t.row.idx > e.cellMatrix.scrollableRange.first.row.idx && t.row.idx <= e.cellMatrix.scrollableRange.last.row.idx,
                            s = i && t.row.idx === e.cellMatrix.scrollableRange.first.row.idx,
                            c = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0,
                            d = c && t.row.idx >= e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                            u = c && (null == t ? void 0 : t.row.idx) === (null === (n = e.cellMatrix.ranges) || void 0 === n ? void 0 : n.stickyBottomRange.first.row.idx),
                            h = e.cellMatrix.scrollableRange.rows.filter((function(e) { return e.top + e.height < r })),
                            m = 0; if (d ? m = d && !u ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : i ? e.cellMatrix.scrollableRange.last.row.idx : a ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : e.cellMatrix.ranges.stickyBottomRange.first.row.idx : l ? m = t.row.idx - h.length < e.cellMatrix.scrollableRange.first.row.idx ? e.cellMatrix.scrollableRange.first.row.idx : t.row.idx - h.length : s ? m = a ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : e.cellMatrix.scrollableRange.first.row.idx : o && (m = e.cellMatrix.ranges.stickyTopRange.first.row.idx), !gi(e, m, t.column.idx)) { var p = e.cellMatrix.getLocation(m, t.column.idx),
                                f = zi(e, p); return f ? f.row.idx : t.row.idx } return m })),
                    fi = mi((function(e, t) { var n, r = e.cellMatrix.ranges.stickyTopRange.rows.length > 0,
                            a = r && G(e, t),
                            o = r && t.row.idx === (null === (n = e.cellMatrix.ranges) || void 0 === n ? void 0 : n.stickyTopRange.last.row.idx),
                            i = e.cellMatrix.scrollableRange.rows.length > 0,
                            l = i && t.row.idx >= e.cellMatrix.scrollableRange.first.row.idx && t.row.idx < e.cellMatrix.scrollableRange.last.row.idx,
                            s = i && t.row.idx === e.cellMatrix.scrollableRange.last.row.idx,
                            c = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0,
                            d = c && t.row.idx >= e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                            u = xi(e, e.cellMatrix.ranges.stickyTopRange.height + e.cellMatrix.ranges.stickyBottomRange.height),
                            h = e.cellMatrix.scrollableRange.rows.filter((function(e) { return e.top + e.height < u })),
                            m = 0; if (a ? m = a && !o ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : i ? e.cellMatrix.scrollableRange.first.row.idx : c ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : e.cellMatrix.ranges.stickyTopRange.last.row.idx : l ? m = t.row.idx + h.length < e.cellMatrix.scrollableRange.rows.length ? t.row.idx + h.length : e.cellMatrix.scrollableRange.last.row.idx : s ? m = c ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : e.cellMatrix.scrollableRange.last.row.idx : d && (m = e.cellMatrix.ranges.stickyBottomRange.last.row.idx), !gi(e, m, t.column.idx)) { var p = e.cellMatrix.getLocation(m, t.column.idx),
                                f = wi(e, p); return f ? f.row.idx : t.row.idx } return m }));

                function gi(e, t, n) { var r = e.cellMatrix.getLocation(t, n),
                        a = S(e, r),
                        o = a.cell,
                        i = a.cellTemplate; if (!e.props) throw new Error('"props" field on "state" object should be initiated before possible location focus'); var l = e.props.onFocusLocationChanging,
                        s = { rowId: r.row.rowId, columnId: r.column.columnId },
                        c = !l || l(s); return i.isFocusable && !i.isFocusable(o) || !c ? void 0 : r }

                function yi(e, t) { if (t)
                        for (var n = t.column.idx - 1; n >= e.cellMatrix.first.column.idx; --n) { var r = gi(e, t.row.idx, n); if (r) return r } }

                function bi(e, t) { if (t)
                        for (var n = t.column.idx + 1; n <= e.cellMatrix.last.column.idx; ++n) { var r = gi(e, t.row.idx, n); if (r) return r } }

                function wi(e, t) { if (t)
                        for (var n = t.row.idx - 1; n >= e.cellMatrix.first.row.idx; --n) { var r = gi(e, n, t.column.idx); if (r) return r } }

                function zi(e, t) { if (t)
                        for (var n = t.row.idx + 1; n <= e.cellMatrix.last.row.idx; ++n) { var r = gi(e, n, t.column.idx); if (r) return r } }

                function xi(e, t) { return q(e, t) }

                function Ai(e, t) { var n = function(e, t) { var n, r, a, o, i, l, c, d, u, m, p, f, v, y, b, w, z, A, k = e.focusedLocation; if (!k) return e; var C = null !== (n = Zo(e)) && void 0 !== n ? n : e.cellMatrix.getRange(k, k); if (t.ctrlKey && H() && t.keyCode === x.SPACE) return Mi(e, C.first.column.idx, C.last.column.idx, 0, e.cellMatrix.last.row.idx); var T = 1 === e.selectedRanges.length && s(C.first, C.last),
                            L = function(e, t) { var n = e.focusedLocation; if (!n) return e; var r = S(e, n),
                                    a = r.cell,
                                    o = r.cellTemplate; if (o.handleKeyDown && !e.currentlyEditedCell) { var i = o.handleKeyDown(a, t.keyCode, gl(t), t.shiftKey, t.altKey, t.key, t.getModifierState("CapsLock")),
                                        l = i.cell,
                                        s = i.enableEditMode; if (JSON.stringify(l) !== JSON.stringify(a) || s) return s && !a.nonEditable ? h(h({}, e), { currentlyEditedCell: l }) : M(e, n, l) } return e }(e, t); if (L !== e) { if (!T && t.keyCode === x.ENTER) { var I = t.shiftKey ? "up" : (null === (r = e.props) || void 0 === r ? void 0 : r.moveRightOnEnter) ? "right" : "down"; return null === (a = e.hiddenFocusElement) || void 0 === a || a.focus(), ki(e, I, C, k) } return L } if (t.altKey) return e; if (gl(t) && t.shiftKey) switch (t.keyCode) {
                            case x.HOME:
                                return Mi(e, C.first.column.idx, C.last.column.idx, 0, C.last.row.idx);
                            case x.END:
                                return Mi(e, C.first.column.idx, C.last.column.idx, C.first.row.idx, e.cellMatrix.last.row.idx) } else if (gl(t)) { var j = e.cellMatrix; switch (t.keyCode) {
                                case x.KEY_A:
                                    if (1 === e.selectedRanges.length && s(e.selectedRanges[0].first, j.first) && s(e.selectedRanges[0].last, j.last)) return g(e, k); var V = j.getRange(j.first, j.last); return (null === (o = e.props) || void 0 === o ? void 0 : o.onSelectionChanging) && !e.props.onSelectionChanging([V]) ? e : h(h({}, e), { selectedRanges: [V], selectionMode: "range", activeSelectedRangeIdx: 0 });
                                case x.HOME:
                                    return E(e, e.cellMatrix.first);
                                case x.END:
                                    return E(e, e.cellMatrix.last);
                                case x.SPACE:
                                    return Mi(e, C.first.column.idx, C.last.column.idx, 0, e.cellMatrix.last.row.idx) } } else if (t.shiftKey) switch (t.keyCode) {
                            case x.UP_ARROW:
                                return function(e, t, n) { return t.first.row.idx >= 0 ? t.last.row.idx > n.row.idx ? Mi(e, t.first.column.idx, t.last.column.idx, t.first.row.idx, t.last.row.idx > 0 ? t.last.row.idx - 1 : 0, "vertical") : Mi(e, t.last.column.idx, t.first.column.idx, t.last.row.idx, t.first.row.idx > 0 ? t.first.row.idx - 1 : 0, "vertical") : e }(e, C, k);
                            case x.DOWN_ARROW:
                                return function(e, t, n) { return t.last.row.idx <= e.cellMatrix.last.row.idx ? t.first.row.idx < n.row.idx ? Mi(e, t.last.column.idx, t.first.column.idx, t.last.row.idx, t.first.row.idx >= e.cellMatrix.last.row.idx ? e.cellMatrix.last.row.idx : t.first.row.idx + 1, "vertical") : Mi(e, t.first.column.idx, t.last.column.idx, t.first.row.idx, t.last.row.idx >= e.cellMatrix.last.row.idx ? e.cellMatrix.last.row.idx : t.last.row.idx + 1, "vertical") : e }(e, C, k);
                            case x.LEFT_ARROW:
                                return function(e, t, n) { return t.first.column.idx >= 0 ? t.last.column.idx > n.column.idx ? Mi(e, t.first.column.idx, t.last.column.idx > 0 ? t.last.column.idx - 1 : 0, t.first.row.idx, t.last.row.idx, "horizontal") : Mi(e, t.last.column.idx, t.first.column.idx > 0 ? t.first.column.idx - 1 : 0, t.last.row.idx, t.first.row.idx, "horizontal") : e }(e, C, k);
                            case x.RIGHT_ARROW:
                                return function(e, t, n) { return t.last.column.idx <= e.cellMatrix.last.column.idx ? t.first.column.idx < n.column.idx ? Mi(e, t.last.column.idx, t.first.column.idx >= e.cellMatrix.last.column.idx ? e.cellMatrix.last.column.idx : t.first.column.idx + 1, t.last.row.idx, t.first.row.idx, "horizontal") : Mi(e, t.first.column.idx, t.last.column.idx >= e.cellMatrix.last.column.idx ? e.cellMatrix.last.column.idx : t.last.column.idx + 1, t.first.row.idx, t.last.row.idx, "horizontal") : e }(e, C, k);
                            case x.TAB:
                                return t.preventDefault(), T ? ci(e) : ki(e, "left", C, k);
                            case x.ENTER:
                                return null === (i = e.hiddenFocusElement) || void 0 === i || i.focus(), T ? ui(e) : ki(e, "up", C, k);
                            case x.SPACE:
                                return Mi(e, 0, e.cellMatrix.last.column.idx, C.first.row.idx, C.last.row.idx);
                            case x.HOME:
                                return Mi(e, 0, C.last.column.idx, C.first.row.idx, C.last.row.idx);
                            case x.END:
                                return Mi(e, C.first.column.idx, e.cellMatrix.last.column.idx, C.first.row.idx, C.last.row.idx);
                            case x.PAGE_UP:
                                return function(e, t, n) { var r = Si(e),
                                        a = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.first.row.idx > e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                                        o = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.ranges.stickyBottomRange.last.row.idx,
                                        i = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                                        l = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.first.row.idx === e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                                        s = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        c = e.cellMatrix.scrollableRange.rows.length > 0 && e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.first.row.idx === e.cellMatrix.scrollableRange.first.row.idx,
                                        d = e.cellMatrix.scrollableRange.rows.length > 0 && e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.scrollableRange.first.row.idx,
                                        u = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.first.row.idx <= e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        h = e.cellMatrix.scrollableRange.rows.filter((function(e) { return e.top + e.height < r })); return t.first.row.idx >= 0 ? t.last.row.idx > n.row.idx ? Mi(e, t.first.column.idx, t.last.column.idx, t.first.row.idx, o ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : i ? e.cellMatrix.scrollableRange.rows.length > 0 ? e.cellMatrix.scrollableRange.last.row.idx : e.cellMatrix.ranges.stickyTopRange.first.row.idx : d ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : s ? e.cellMatrix.ranges.stickyTopRange.first.row.idx : t.last.row.idx - h.length > e.cellMatrix.scrollableRange.first.row.idx ? t.last.row.idx - h.length : e.cellMatrix.scrollableRange.first.row.idx, "vertical") : Mi(e, t.last.column.idx, t.first.column.idx, t.last.row.idx, a ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : l ? e.cellMatrix.scrollableRange.rows.length > 0 ? e.cellMatrix.scrollableRange.last.row.idx : e.cellMatrix.ranges.stickyTopRange.rows.length > 0 ? e.cellMatrix.ranges.stickyTopRange.first.row.idx : e.cellMatrix.ranges.stickyBottomRange.first.row.idx : c ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : u ? e.cellMatrix.ranges.stickyTopRange.first.row.idx : a ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : t.first.row.idx - h.length > e.cellMatrix.scrollableRange.first.row.idx ? t.first.row.idx - h.length : e.cellMatrix.scrollableRange.first.row.idx, "vertical") : e }(e, C, k);
                            case x.PAGE_DOWN:
                                return function(e, t, n) { var r = Si(e),
                                        a = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.last.row.idx < e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        o = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.first.row.idx < e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        i = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        l = e.cellMatrix.ranges.stickyTopRange.rows.length > 0 && t.first.row.idx === e.cellMatrix.ranges.stickyTopRange.last.row.idx,
                                        s = e.cellMatrix.scrollableRange.rows.length > 0 && e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.last.row.idx === e.cellMatrix.scrollableRange.last.row.idx,
                                        c = e.cellMatrix.scrollableRange.rows.length > 0 && e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.first.row.idx === e.cellMatrix.scrollableRange.last.row.idx,
                                        d = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.last.row.idx >= e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                                        u = e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 && t.first.row.idx >= e.cellMatrix.ranges.stickyBottomRange.first.row.idx,
                                        h = e.cellMatrix.scrollableRange.rows.filter((function(e) { return e.top + e.height < r })); return t.last.row.idx <= e.cellMatrix.last.row.idx ? t.first.row.idx < n.row.idx ? Mi(e, t.last.column.idx, t.first.column.idx, t.last.row.idx, o ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : l ? e.cellMatrix.scrollableRange.rows.length > 0 ? e.cellMatrix.scrollableRange.first.row.idx : e.cellMatrix.ranges.stickyBottomRange.first.row.idx : c ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : u ? e.cellMatrix.ranges.stickyBottomRange.last.row.idx : t.first.row.idx + h.length >= e.cellMatrix.scrollableRange.last.row.idx ? e.cellMatrix.scrollableRange.last.row.idx : t.first.row.idx + h.length, "vertical") : Mi(e, t.first.column.idx, t.last.column.idx, t.first.row.idx, d ? e.cellMatrix.ranges.stickyBottomRange.last.row.idx : i ? e.cellMatrix.scrollableRange.rows.length > 0 ? e.cellMatrix.scrollableRange.first.row.idx : e.cellMatrix.ranges.stickyBottomRange.rows.length > 0 ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : e.cellMatrix.ranges.stickyTopRange.last.row.idx : s ? e.cellMatrix.ranges.stickyBottomRange.first.row.idx : a ? e.cellMatrix.ranges.stickyTopRange.last.row.idx : t.last.row.idx + h.length >= e.cellMatrix.scrollableRange.last.row.idx ? e.cellMatrix.scrollableRange.last.row.idx : t.last.row.idx + h.length, "vertical") : e }(e, C, k) } else switch (t.keyCode) {
                            case x.DELETE:
                            case x.BACKSPACE:
                                return null === (l = e.hiddenFocusElement) || void 0 === l || l.focus(),
                                    function(e) { return e.selectedRanges.forEach((function(t) { return t.rows.forEach((function(n) { return t.columns.forEach((function(t) { return e = M(e, { row: n, column: t }, Yo) })) })) })), e }(e);
                            case x.UP_ARROW:
                                return null === (c = e.hiddenFocusElement) || void 0 === c || c.focus(), ui(e);
                            case x.DOWN_ARROW:
                                return null === (d = e.hiddenFocusElement) || void 0 === d || d.focus(), hi(e);
                            case x.LEFT_ARROW:
                                return null === (u = e.hiddenFocusElement) || void 0 === u || u.focus(), ci(e);
                            case x.RIGHT_ARROW:
                                return null === (m = e.hiddenFocusElement) || void 0 === m || m.focus(), di(e);
                            case x.TAB:
                                return null === (p = e.hiddenFocusElement) || void 0 === p || p.focus(), t.preventDefault(), T ? di(e) : ki(e, "right", C, k);
                            case x.HOME:
                                return null === (f = e.hiddenFocusElement) || void 0 === f || f.focus(), li(e);
                            case x.END:
                                return null === (v = e.hiddenFocusElement) || void 0 === v || v.focus(), si(e);
                            case x.PAGE_UP:
                                return null === (y = e.hiddenFocusElement) || void 0 === y || y.focus(), pi(e);
                            case x.PAGE_DOWN:
                                return null === (b = e.hiddenFocusElement) || void 0 === b || b.focus(), fi(e);
                            case x.ENTER:
                                var O = (null === (w = e.props) || void 0 === w ? void 0 : w.moveRightOnEnter) ? h(h({}, di(e, t.keyCode)), { currentlyEditedCell: void 0 }) : h(h({}, hi(e, t.keyCode)), { currentlyEditedCell: void 0 }); return null === (z = e.hiddenFocusElement) || void 0 === z || z.focus(), T ? O : ki(e, "right", C, k);
                            case x.ESCAPE:
                                return t.preventDefault(), null === (A = e.hiddenFocusElement) || void 0 === A || A.focus(), e.currentlyEditedCell ? h(h({}, e), { currentlyEditedCell: void 0 }) : e }
                        return e }(e, t); return n !== e && (t.stopPropagation(), t.preventDefault()), n }

                function ki(e, t, n, r) { var a = e.activeSelectedRangeIdx,
                        o = n ? n.columns.length : 0,
                        i = n ? n.rows.length : 0,
                        l = "up" === t || "left" === t ? -1 : 1,
                        s = "up" === t || "down" === t ? r.row.idx - n.first.row.idx + (r.column.idx - n.first.column.idx) * i : (r.row.idx - n.first.row.idx) * o + (r.column.idx - n.first.column.idx),
                        c = (s + l) % (n.rows.length * n.columns.length),
                        d = c < 0 && 0 === s || 1 === i && 1 === o && -1 === l,
                        u = 0 === c && s === n.rows.length * n.columns.length - 1 && (i >= 3 && o >= 1 || i >= 1 && o >= 3) || 0 === c && s === n.rows.length * n.columns.length - 1 && (2 === i && o >= 1 || i >= 1 && 2 === o) && 1 === l || c < 0 && 0 === s || 1 === i && 1 === o && 1 === l; if (d) { var m = 0 === a ? e.selectedRanges.length - 1 : (a - 1) % e.selectedRanges.length,
                            p = e.selectedRanges[m]; return e = E(e, v(p.last.row, p.last.column), !1), h(h({}, e), { activeSelectedRangeIdx: m }) } if (u) return m = (a + 1) % e.selectedRanges.length, p = e.selectedRanges[m], e = E(e, v(p.first.row, p.first.column), !1), h(h({}, e), { activeSelectedRangeIdx: m }); var f = "up" === t || "down" === t ? Math.floor(c / i) : c % o,
                        g = "up" === t || "down" === t ? c % i : Math.floor(c / o),
                        y = n.first.column.idx + f,
                        b = n.first.row.idx + g; return E(e, e.cellMatrix.getLocation(b, y), !n || !(n.columns.length > 1 || n.rows.length > 1)) }

                function Si(e) { var t = e.cellMatrix.ranges,
                        n = t.stickyBottomRange,
                        r = t.stickyTopRange; return q(e, n.height + r.height) }

                function Mi(e, t, n, r, a, o) { var i, l, s; if (!e.enableRangeSelection) return e; var c = e.cellMatrix.getLocation(r, t),
                        d = e.cellMatrix.getLocation(a, n),
                        u = e.selectedRanges.slice(); if (u[e.activeSelectedRangeIdx] = e.cellMatrix.getRange(c, d), o) { var m = e.focusedLocation; if (!m) return e; var p = 0,
                            f = 0; switch (o) {
                            case "horizontal":
                                p = m.row.idx, f = m.column.idx !== t ? t : n; break;
                            case "vertical":
                                p = m.row.idx !== r ? r : a, f = m.column.idx } var v = K(e, e.cellMatrix.getLocation(p, f), o),
                            g = v.left;
                        U(e, v.top, g) } return (null === (i = e.props) || void 0 === i ? void 0 : i.onSelectionChanging) && !e.props.onSelectionChanging(u) ? e : (null === (s = null === (l = e.props) || void 0 === l ? void 0 : l.onSelectionChanged) || void 0 === s || s.call(l, u), h(h({}, e), { selectedRanges: u })) } var Ei = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.autoScrollDirection = "horizontal", t } return u(t, e), t.prototype.handlePointerDown = function(e, t, n) { return this.initialColumn = t.column, gl(e) && "column" === n.selectionMode && n.selectedIds.some((function(e) { return e === t.column.columnId })) ? function(e, t) { var n = e.selectedIndexes.filter((function(e) { return e !== t.idx })),
                                    r = e.selectedIds.filter((function(e) { return e !== t.columnId })); return h(h({}, e), { selectionMode: "column", selectedIndexes: n, selectedIds: r }) }(n, t.column) : e.shiftKey && n.focusedLocation ? w(n, n.focusedLocation.column, t.column, gl(e)) : function(e, t, n) { return h(h({}, e), { selectionMode: "column", selectedIndexes: (n && "column" === e.selectionMode ? e.selectedIndexes : []).concat(t.idx), selectedIds: (n && "column" === e.selectionMode ? e.selectedIds : []).concat(t.columnId) }) }(n = E(n, t, !1), t.column, gl(e)) }, t.prototype.handlePointerEnter = function(e, t, n) { return w(n, this.initialColumn, t.column, gl(e)) }, t.prototype.handlePointerUp = function(e, t, n) { var r, a; if ((null === (r = n.props) || void 0 === r ? void 0 : r.onSelectionChanging) && !n.props.onSelectionChanging(n.selectedRanges)) { var o = f([], n.selectedRanges, !0).filter((function(e, t) { return t !== n.activeSelectedRangeIdx })); return h(h({}, n), { selectedRanges: o, activeSelectedRangeIdx: o.length - 1 }) } return (null === (a = n.props) || void 0 === a ? void 0 : a.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges), n }, t.prototype.handleContextMenu = function(e, t) { return D(e, t) }, t }(k),
                    Ci = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.autoScrollDirection = "vertical", t } return u(t, e), t.prototype.handlePointerDown = function(e, t, n) { return this.initialRow = t.row, gl(e) && "row" === n.selectionMode && n.selectedIds.some((function(e) { return e === t.row.rowId })) ? function(e, t) { var n = e.selectedIndexes.filter((function(e) { return e !== t.idx })),
                                    r = e.selectedIds.filter((function(e) { return e !== t.rowId })); return h(h({}, e), { selectionMode: "row", selectedIndexes: n, selectedIds: r }) }(n, t.row) : e.shiftKey && n.focusedLocation ? z(n, n.focusedLocation.row, t.row, gl(e)) : function(e, t, n) { return h(h({}, e), { selectionMode: "row", selectedIndexes: (n && "row" === e.selectionMode ? e.selectedIndexes : []).concat(t.idx), selectedIds: (n && "row" === e.selectionMode ? e.selectedIds : []).concat(t.rowId) }) }(n = E(n, t, !1), t.row, gl(e)) }, t.prototype.handlePointerEnter = function(e, t, n) { return z(n, this.initialRow, t.row, gl(e)) }, t.prototype.handlePointerUp = function(e, t, n) { var r, a; if ((null === (r = n.props) || void 0 === r ? void 0 : r.onSelectionChanging) && !n.props.onSelectionChanging(n.selectedRanges)) { var o = f([], n.selectedRanges, !0).filter((function(e, t) { return t !== n.activeSelectedRangeIdx })); return h(h({}, n), { selectedRanges: o, activeSelectedRangeIdx: o.length - 1 }) } return (null === (a = n.props) || void 0 === a ? void 0 : a.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges), n }, t.prototype.handleContextMenu = function(e, t) { return D(e, t) }, t }(k),
                    Ti = function(e) { var t = e.range,
                            n = e.pane,
                            a = e.style,
                            o = e.className,
                            i = t.first.row.idx <= n.first.row.idx ? n.first.row.top : t.first.row.top,
                            l = t.first.column.idx <= n.first.column.idx ? n.first.column.left : t.first.column.left,
                            s = (t.last.column.idx > n.last.column.idx ? n.last.column.right : t.last.column.right) - l,
                            c = (t.last.row.idx > n.last.row.idx ? n.last.row.bottom : t.last.row.bottom) - i,
                            d = t.first.row.idx >= n.first.row.idx,
                            u = t.last.row.idx <= n.last.row.idx,
                            m = t.last.column.idx <= n.last.column.idx,
                            p = t.first.column.idx >= n.first.column.idx; return r.createElement("div", { className: "rg-partial-area ".concat(o), key: t.first.column.idx + n.last.column.idx, style: h(h({}, a), { top: i - (0 === i ? 0 : 1), left: l - (0 === l ? 0 : 1), width: s + (0 === l ? 0 : 1), height: c + (0 === i ? 0 : 1), borderTop: d ? a.borderTop : "unset", borderBottom: u ? a.borderBottom : "unset", borderRight: m ? a.borderRight : "unset", borderLeft: p ? a.borderLeft : "unset" }) }) };

                function Hi(e, t) { return t.first.column.idx <= e.last.column.idx && t.first.row.idx <= e.last.row.idx && t.last.column.idx >= e.first.column.idx && t.last.row.idx >= e.first.row.idx } var Li = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.fillDirection = "", t } return u(t, e), t.prototype.handlePointerEnter = function(e, t, n) { var r = Zo(n); return this.fillDirection = this.getFillDirection(r, t), this.fillRange = this.getFillRange(n.cellMatrix, r, t, this.fillDirection), h({}, n) }, t.prototype.handlePointerUp = function(e, t, n) { var r, a, o, i, l, s, c, d, u = this,
                                m = Zo(n),
                                p = n.cellMatrix; if (!m || void 0 === this.fillRange) return n; var g = H() ? e.altKey : e.ctrlKey;
                            this.fillRange = n.cellMatrix.validateRange(this.fillRange); var y = function(e) { return S(n, e) },
                                b = function(e, t) { var n = e.map((function(e) { return e.value })),
                                        r = u.findRegressionFunction(n, Array.from({ length: n.length }, (function(e, t) { return t + 1 }))),
                                        a = isNaN(r.a) && isNaN(r.b); return t.map((function(t, o) { var i = u.calculateXForRegressionFunction(o + n.length + 1, r.a, r.b),
                                            l = e[o % e.length]; return h(h(h({}, t), "checkbox" === t.type && { checked: "checked" in l ? l.checked : !!l.value }), { text: a || g ? l.text : i.toString(), groupId: l.groupId, value: a || g ? l.value : i }) })) },
                                w = function(e, t, n) { return t.columns.forEach((function(r) { var a = t.rows.map((function(e) { return y(v(e, r)).cell })); if (a = "up" === n ? a.reverse() : a, u.fillRange) { var o = u.fillRange.rows.map((function(e) { return y(v(e, r)).cell }));
                                            o = b(a, o), o = "up" === n ? o.reverse() : o, e = u.fillColumn(e, r, o) } })), e },
                                z = function(e, t, n) { return t.rows.forEach((function(r) { var a = t.columns.map((function(e) { return y(v(r, e)).cell })); if (a = "left" === n ? a.reverse() : a, u.fillRange) { var o = u.fillRange.columns.map((function(e) { return y(v(r, e)).cell }));
                                            o = b(a, o), o = "left" === n ? o.reverse() : o, e = u.fillRow(e, r, o) } })), e }; switch (this.fillDirection) {
                                case "right":
                                    var x = p.getRange(m.first, v(m.last.row, t.column)); if ((null === (r = null == (n = z(n, m, "right")) ? void 0 : n.props) || void 0 === r ? void 0 : r.onSelectionChanging) && !n.props.onSelectionChanging([x])) return n;
                                    (null === (a = (n = h(h({}, n), { selectedRanges: [x], selectedIds: f(f([], m.columns.map((function(e) { return e.columnId })), !0), this.fillRange.columns.map((function(e) { return e.columnId })), !0) })).props) || void 0 === a ? void 0 : a.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges); break;
                                case "left":
                                    if (x = p.getRange(m.last, v(m.first.row, t.column)), (null === (o = null == (n = z(n, m, "left")) ? void 0 : n.props) || void 0 === o ? void 0 : o.onSelectionChanging) && !n.props.onSelectionChanging([x])) return n;
                                    (null === (i = (n = h(h({}, n), { selectedRanges: [x], selectedIds: f(f([], m.columns.map((function(e) { return e.columnId })), !0), this.fillRange.columns.map((function(e) { return e.columnId })), !0) })).props) || void 0 === i ? void 0 : i.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges); break;
                                case "up":
                                    if (x = p.getRange(m.last, { row: t.row, column: m.first.column }), (null === (l = null == (n = w(n, m, "up")) ? void 0 : n.props) || void 0 === l ? void 0 : l.onSelectionChanging) && !n.props.onSelectionChanging([x])) return n;
                                    (null === (s = (n = h(h({}, n), { selectedRanges: [x], selectedIds: f(f([], m.rows.map((function(e) { return e.rowId })), !0), this.fillRange.rows.map((function(e) { return e.rowId })), !0) })).props) || void 0 === s ? void 0 : s.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges); break;
                                case "down":
                                    if (x = p.getRange(m.first, v(t.row, m.last.column)), (null === (c = null == (n = w(n, m, "down")) ? void 0 : n.props) || void 0 === c ? void 0 : c.onSelectionChanging) && !n.props.onSelectionChanging([x])) return n;
                                    (null === (d = (n = h(h({}, n), { selectedRanges: [x], selectedIds: f(f([], m.rows.map((function(e) { return e.rowId })), !0), this.fillRange.rows.map((function(e) { return e.rowId })), !0) })).props) || void 0 === d ? void 0 : d.onSelectionChanged) && n.props.onSelectionChanged(n.selectedRanges) } return n }, t.prototype.calculateXForRegressionFunction = function(e, t, n) { return Math.round((e - t) / n * 1e5) / 1e5 }, t.prototype.findRegressionFunction = function(e, t) { var n = this.sumArray(e),
                                r = this.sumArray(t),
                                a = this.sumArray(this.multipleArrays(e, t)),
                                o = this.sumArray(this.powerArray(e, 2)),
                                i = e.length,
                                l = Math.fround(i * a - n * r) / Math.fround(i * o - Math.pow(n, 2)); return { a: r / i - l * (n / i), b: l } }, t.prototype.sumArray = function(e) { return e.reduce((function(e, t) { return e + t })) }, t.prototype.multipleArrays = function(e, t) { for (var n = [], r = e.length <= t.length ? e.length : t.length, a = 0; a < r; ++a) n.push(e[a] * t[a]); return n }, t.prototype.powerArray = function(e, t) { return e.map((function(e) { return Math.pow(e, t) })) }, t.prototype.renderPanePart = function(e, t) { return this.fillDirection && this.fillRange && Hi(t, this.fillRange) && r.createElement(Ti, { range: e.cellMatrix.validateRange(this.fillRange), className: "rg-partial-area-part", pane: t, style: { backgroundColor: "", borderTop: "down" === this.fillDirection ? "0px solid transparent" : "", borderBottom: "up" === this.fillDirection ? "0px solid transparent" : "", borderLeft: "right" === this.fillDirection ? "0px solid transparent" : "", borderRight: "left" === this.fillDirection ? "0px solid transparent" : "" } }) }, t.prototype.getFillDirection = function(e, t) { var n = []; return n.push({ direction: "", value: 0 }), n.push({ direction: "up", value: t.row.idx < e.first.row.idx ? e.first.row.idx - t.row.idx : 0 }), n.push({ direction: "down", value: t.row.idx > e.last.row.idx ? t.row.idx - e.last.row.idx : 0 }), n.push({ direction: "left", value: t.column.idx < e.first.column.idx ? e.first.column.idx - t.column.idx : 0 }), n.push({ direction: "right", value: t.column.idx > e.last.column.idx ? t.column.idx - e.last.column.idx : 0 }), n.reduce((function(e, t) { return e.value >= t.value ? e : t })).direction }, t.prototype.getFillRange = function(e, t, n, r) { switch (r) {
                                case "right":
                                    return e.getRange(e.getLocation(t.first.row.idx, e.last.column.idx < t.last.column.idx + 1 ? e.last.column.idx : t.last.column.idx + 1), v(t.last.row, n.column));
                                case "left":
                                    return e.getRange(v(t.first.row, n.column), e.getLocation(t.last.row.idx, e.first.column.idx > t.first.column.idx - 1 ? e.first.column.idx : t.first.column.idx - 1));
                                case "up":
                                    return e.getRange(v(n.row, t.first.column), e.getLocation(e.first.row.idx > t.first.row.idx - 1 ? e.first.row.idx : t.first.row.idx - 1, t.last.column.idx));
                                case "down":
                                    return e.getRange(e.getLocation(e.last.row.idx < t.last.row.idx + 1 ? e.last.row.idx : t.last.row.idx + 1, t.first.column.idx), v(n.row, t.last.column)) } }, t.prototype.fillRow = function(e, t, n) { var r; return null === (r = this.fillRange) || void 0 === r || r.columns.forEach((function(r, a) { e = $o(e, v(t, r), n[a]) })), e }, t.prototype.fillColumn = function(e, t, n) { var r; return null === (r = this.fillRange) || void 0 === r || r.rows.forEach((function(r, a) { e = $o(e, v(r, t), n[a]) })), e }, t }(k),
                    Ii = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.autoScrollDirection = "vertical", t } return u(t, e), t.prototype.handlePointerDown = function(e, t, n) { this.initialRowIdx = t.row.idx, this.lastPossibleDropLocation = t; var r = n.selectedIndexes.sort(),
                                a = r.map((function(e) { return n.cellMatrix.rows[e] })),
                                o = r.filter((function(e) { return e < t.row.idx })),
                                i = o.map((function(e) { return n.cellMatrix.rows[e] })),
                                l = i.reduce((function(e, t) { return e + t.height }), 0); return this.pointerOffset = l + t.cellY, this.selectedIds = a.map((function(e) { return e.rowId })), h(h({}, n), { lineOrientation: "horizontal", shadowSize: a.reduce((function(e, t) { return e + t.height }), 0), shadowPosition: this.getShadowPosition(t, n) }) }, t.prototype.handlePointerMove = function(e, t, n) { var r, a, o = this.getShadowPosition(t, n),
                                i = "-webkit-grabbing",
                                l = n.linePosition,
                                s = C(n.scrollableElement).scrollTop,
                                c = t.viewportY + 0; if (this.lastPossibleDropLocation = this.getLastPossibleDropLocation(n, t), this.lastPossibleDropLocation && this.lastPossibleDropLocation.row.idx !== this.initialRowIdx) { var d = this.lastPossibleDropLocation.row.idx > this.initialRowIdx;
                                l = Math.min(this.lastPossibleDropLocation.viewportY - this.lastPossibleDropLocation.cellY + (d ? this.lastPossibleDropLocation.row.height : 0), ((null === (r = n.visibleRange) || void 0 === r ? void 0 : r.height) || 0) + n.cellMatrix.ranges.stickyTopRange.height + n.cellMatrix.ranges.stickyBottomRange.height + s), (null === (a = n.props) || void 0 === a ? void 0 : a.canReorderRows) ? n.props.canReorderRows && n.props.canReorderRows(this.lastPossibleDropLocation.row.rowId, this.selectedIds, this.position) ? d ? c > t.row.top + n.cellMatrix.ranges.stickyTopRange.height && c < t.row.top + n.cellMatrix.ranges.stickyTopRange.height + t.row.height / 2 ? (this.position = "on", i = "move", l = -1) : this.position = "after" : c > t.row.top + n.cellMatrix.ranges.stickyTopRange.height + t.row.height / 2 && c < t.row.top + n.cellMatrix.ranges.stickyTopRange.height + t.row.height ? (this.position = "on", i = "move", l = -1) : this.position = "before" : l = -1 : this.position = d ? "after" : "before" } return h(h({}, n), { shadowPosition: o, linePosition: l, shadowCursor: i }) }, t.prototype.getShadowPosition = function(e, t) { var n = e.viewportY - this.pointerOffset,
                                r = t.cellMatrix.height - t.shadowSize; return n < 0 ? 0 : n > r ? r : n }, t.prototype.getLastPossibleDropLocation = function(e, t) { var n; return !(null === (n = e.props) || void 0 === n ? void 0 : n.canReorderRows) || e.props.canReorderRows(t.row.rowId, this.selectedIds, this.position) ? t : this.lastPossibleDropLocation }, t.prototype.handlePointerUp = function(e, t, n) { var r, a; return t.row.idx !== this.initialRowIdx && this.lastPossibleDropLocation && (null === (r = n.props) || void 0 === r ? void 0 : r.onRowsReordered) && (null === (a = n.props) || void 0 === a || a.onRowsReordered(this.lastPossibleDropLocation.row.rowId, this.selectedIds, this.position)), h(h({}, n), { linePosition: -1, shadowPosition: -1, shadowCursor: "default" }) }, t.prototype.handleContextMenu = function(e, t) { return D(e, t) }, t }(k);

                function ji(e, t, n) { void 0 === n && (n = !1); var r = Zo(t); return r ? (function(e, t, n) { var r; "undefined" != typeof window && -1 !== window.navigator.userAgent.indexOf("Safari") && -1 === navigator.userAgent.indexOf("Chrome") ? e.clipboardData.setData("text/html", n.innerHTML) : (document.body.appendChild(n), n.focus(), document.execCommand("selectAll", !1), document.execCommand("copy"), document.body.removeChild(n)), null === (r = t.hiddenFocusElement) || void 0 === r || r.focus({ preventScroll: !0 }), e.preventDefault() }(e, t, Xo(t, r, n).div), h(h({}, t), { copyRange: r })) : t } var Vi = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.autoScrollDirection = "vertical", t } return u(t, e), t.prototype.handlePointerDown = function(e, t, n) { var r = this; return this.initialLocation = t, this.resizedRow = t.row, this.isInScrollableRange = n.cellMatrix.scrollableRange.rows.some((function(e) { return e.idx === r.resizedRow.idx })), n }, t.prototype.handlePointerMove = function(e, t, n) { var r, a, o, l, s = t.viewportY; if (!(t.row.idx === this.resizedRow.idx && t.cellY > (null !== (a = null === (r = n.props) || void 0 === r ? void 0 : r.minRowHeight) && void 0 !== a ? a : i.MIN_ROW_HEIGHT) || t.row.idx > this.resizedRow.idx)) { var c = this.getLinePositionOffset(n);
                                s = (null !== (l = null === (o = n.props) || void 0 === o ? void 0 : o.minRowHeight) && void 0 !== l ? l : i.MIN_ROW_HEIGHT) + this.resizedRow.top + c } return h(h({}, n), { linePosition: s, lineOrientation: "horizontal" }) }, t.prototype.handlePointerUp = function(e, t, n) { var r, a, o, l, s, c = this.resizedRow.height + t.viewportY - this.initialLocation.viewportY; if (null === (r = n.props) || void 0 === r ? void 0 : r.onRowResized) { var d = c >= (null !== (o = null === (a = n.props) || void 0 === a ? void 0 : a.minRowHeight) && void 0 !== o ? o : i.MIN_ROW_HEIGHT) ? c : null !== (s = null === (l = n.props) || void 0 === l ? void 0 : l.minRowHeight) && void 0 !== s ? s : i.MIN_ROW_HEIGHT;
                                n.props.onRowResized(this.resizedRow.rowId, d, n.selectedIds) } var u = n.focusedLocation; if (void 0 !== u && this.resizedRow.rowId === u.row.idx) { var m = h(h({}, u.row), { height: c });
                                u = h(h({}, u), { row: m }) } return h(h({}, n), { linePosition: -1, focusedLocation: u }) }, t.prototype.renderPanePart = function(e, t) { var n = this.getLinePositionOffset(e); return t.contains(this.initialLocation) && r.createElement(_, { top: this.resizedRow.top, linePosition: e.linePosition, offset: n }) }, t.prototype.getLinePositionOffset = function(e) { var t = this,
                                n = C(e.scrollableElement).scrollTop,
                                r = V(e).top,
                                a = R(n, r),
                                o = O(e).height + a - e.cellMatrix.ranges.stickyBottomRange.height; return e.cellMatrix.scrollableRange.rows.some((function(e) { return e.idx === t.resizedRow.idx })) ? e.cellMatrix.ranges.stickyTopRange.height : e.cellMatrix.ranges.stickyBottomRange.rows.some((function(e) { return e.idx === t.resizedRow.idx })) ? o : n }, t }(k),
                    Oi = function(e) {
                        function t() { return null !== e && e.apply(this, arguments) || this } return u(t, e), t.prototype.handlePointerDown = function(e, t, n) { return (n = h(h({}, n), { currentBehavior: this.getNewBehavior(e, t, n), contextMenuPosition: { top: -1, left: -1 } })).currentBehavior.handlePointerDown(e, t, n) }, t.prototype.getNewBehavior = function(e, t, n) { var r, a, o, i, l = S(n, t).cell,
                                s = e.target; return ("mouse" === e.pointerType && "rg-resize-handle" === s.className || "touch" === e.pointerType && ("rg-touch-column-resize-handle" === s.className || "rg-resize-handle" === s.className)) && (0 === t.row.idx || "header" === l.type) && t.column.resizable && t.cellX > t.column.width - ((null === (a = null === (r = n.reactGridElement) || void 0 === r ? void 0 : r.querySelector(".rg-resize-handle")) || void 0 === a ? void 0 : a.clientWidth) || 0) - C(n.scrollableElement).scrollLeft ? new B : ("mouse" === e.pointerType && "rg-resize-handle" === s.className || "touch" === e.pointerType && ("rg-touch-row-resize-handle" === s.className || "rg-resize-handle" === s.className)) && 0 === t.column.idx && t.row.resizable && t.cellY > t.row.height - ((null === (i = null === (o = n.reactGridElement) || void 0 === o ? void 0 : o.querySelector(".rg-resize-handle")) || void 0 === i ? void 0 : i.clientHeight) || 0) - C(n.scrollableElement).scrollTop ? new Vi : n.enableColumnSelection && 0 === t.row.idx && n.selectedIds.includes(t.column.columnId) && !gl(e) && "column" === n.selectionMode && t.column.reorderable ? new W : n.enableColumnSelection && 0 === t.row.idx && "rg-fill-handle" !== s.className && "rg-touch-fill-handle" !== s.className ? new Ei : n.enableRowSelection && 0 === t.column.idx && n.selectedIds.includes(t.row.rowId) && !gl(e) && "row" === n.selectionMode && t.row.reorderable ? new Ii : n.enableRowSelection && 0 === t.column.idx && "rg-fill-handle" !== s.className && "rg-touch-fill-handle" !== s.className ? new Ci : ("mouse" === e.pointerType && "rg-fill-handle" === s.className || "touch" === e.pointerType && ("rg-touch-fill-handle" === s.className || "rg-fill-handle" === s.className)) && n.enableFillHandle ? new Li : new F }, t.prototype.handleContextMenu = function(e, t) { return D(e, t) }, t.prototype.handleDoubleClick = function(e, t, n) { return function(e, t, n) { if (s(t, n.focusedLocation)) { var r = S(n, t),
                                        a = r.cell,
                                        o = r.cellTemplate; if (o.handleKeyDown) { var i = o.handleKeyDown(a, 1, gl(e), e.shiftKey, e.altKey, "DoubleClick"),
                                            l = i.cell; if (i.enableEditMode && !a.nonEditable) return h(h({}, n), { currentlyEditedCell: l }) } } return n }(e, t, n) }, t.prototype.handleKeyDown = function(e, t) { return Ai(t, e) }, t.prototype.handleKeyUp = function(e, t) { return function(e, t) { return e.keyCode !== x.TAB && e.keyCode !== x.ENTER || (e.preventDefault(), e.stopPropagation()), t }(e, t) }, t.prototype.handleCompositionEnd = function(e, t) { return function(e, t) { var n = function(e, t) { var n = e.focusedLocation; if (!n) return e; var r = S(e, n),
                                        a = r.cell,
                                        o = r.cellTemplate; if (o.handleCompositionEnd && !e.currentlyEditedCell) { var i = o.handleCompositionEnd(a, t.data),
                                            l = i.cell,
                                            s = i.enableEditMode; if (JSON.stringify(l) !== JSON.stringify(a) || s) return s && !a.nonEditable ? h(h({}, e), { currentlyEditedCell: l }) : M(e, n, l) } return e }(t, e); return n !== t && (e.stopPropagation(), e.preventDefault()), n }(e, t) }, t.prototype.handleCopy = function(e, t) { return ji(e, t) }, t.prototype.handlePaste = function(e, t) { return function(e, t) { var n, r; if (!Zo(t)) return t; var a = [],
                                    o = e.clipboardData.getData("text/html"),
                                    i = (new DOMParser).parseFromString(o, "text/html"); if ("reactgrid-content" === (null === (n = i.body.firstElementChild) || void 0 === n ? void 0 : n.getAttribute("data-reactgrid")) && (null === (r = i.body.firstElementChild) || void 0 === r ? void 0 : r.firstElementChild))
                                    for (var l = i.body.firstElementChild.firstElementChild.children, s = 0; s < l.length; s++) { for (var c = [], d = 0; d < l[s].children.length; d++) { var u = l[s].children[d].getAttribute("data-reactgrid"),
                                                m = u && JSON.parse(u),
                                                p = l[s].children[d].innerHTML;
                                            c.push(m || { type: "text", text: p, value: Ce(p) }) } a.push(c) } else a = e.clipboardData.getData("text/plain").replace(/(\r\n)$/, "").split("\n").map((function(e) { return e.split("\t").map((function(e) { return { type: "text", text: e, value: Ce(e) } })) })); return e.preventDefault(), h({}, Qo(t, a)) }(e, t) }, t.prototype.handleCut = function(e, t) { return ji(e, t, !0) }, t }(k),
                    Ri = { legacyBrowserMode: "undefined" != typeof window && window.navigator.userAgent.indexOf("Trident") > 0 || "undefined" != typeof window && window.navigator.userAgent.indexOf("Edge/") > 0, focusedLocation: void 0, currentBehavior: new Oi, cellTemplates: xo, hiddenFocusElement: void 0, reactGridElement: void 0, scrollableElement: void 0, queuedCellChanges: [], currentlyEditedCell: void 0, highlightLocations: [], visibleRange: void 0, topScrollBoudary: -1, bottomScrollBoudary: -1, leftScrollBoudary: -1, rightScrollBoudary: -1, enableGroupIdRender: !1, leftStickyColumns: void 0, topStickyRows: void 0, enableFillHandle: !1, enableRangeSelection: !0, enableColumnSelection: !1, enableRowSelection: !1, contextMenuPosition: { top: -1, left: -1 }, lineOrientation: "horizontal", linePosition: -1, shadowSize: 0, shadowPosition: -1, shadowCursor: "default", selectionMode: "range", selectedRanges: [], selectedIndexes: [], selectedIds: [], activeSelectedRangeIdx: 0, copyRange: void 0, rightStickyColumns: void 0, bottomStickyRows: void 0, disableVirtualScrolling: !1 },
                    Pi = function(e, t) { var n = t.cellMatrix; return function(e, t, n) { if (e.ranges.stickyRightRange.first.column && t.column.idx >= e.ranges.stickyRightRange.first.column.idx) { var r = C(n.scrollableElement).scrollLeft,
                                    a = V(n).left,
                                    o = R(r, a); return O(n).width + o - e.ranges.stickyRightRange.width } }(n, e, t) || Bi(n, e) || Ui(n, e, t) || 0 },
                    Di = function(e, t) { var n = t.cellMatrix; return function(e, t, n) { if (e.ranges.stickyBottomRange.first.row && t.row.idx >= e.ranges.stickyBottomRange.first.row.idx) { var r = C(n.scrollableElement).scrollTop,
                                    a = V(n).top,
                                    o = R(r, a); return O(n).height + o - e.ranges.stickyBottomRange.height } }(n, e, t) || Wi(n, e) || qi(n, e, t) || 0 },
                    Fi = function(e) { var t = e.state,
                            n = e.location,
                            r = C(t.scrollableElement),
                            a = r.scrollTop,
                            o = r.scrollLeft,
                            i = V(t),
                            l = i.top,
                            s = i.left,
                            c = 0,
                            d = 0; if (t.scrollableElement !== T()) { var u = t.scrollableElement.getBoundingClientRect();
                            c = u.left, d = u.top } return { state: t, location: n, left: n.column.left + Pi(n, t) + c + s - o, top: n.row.top + Di(n, t) + d + l - a } },
                    Ni = function() { var e = Uo(),
                            t = e.currentlyEditedCell,
                            n = e.focusedLocation,
                            a = r.useRef(0),
                            o = r.useReducer(Fi, { state: e, location: n }),
                            i = o[0],
                            l = o[1]; if (r.useEffect((function() { a.current += 1, l() }), []), !t || !n || 0 === a.current) return null; var s = e.cellTemplates[t.type]; return r.createElement(_i, { cellType: t.type, style: { top: i.top && i.top - 1, left: i.left && i.left - 1, height: n.row.height + 1, width: n.column.width + 1, position: "fixed" } }, s.render(t, !0, (function(t, r) { e.currentlyEditedCell = r ? void 0 : t, r && e.update((function(e) { return M(e, n, t) })) }))) },
                    _i = function(e) { var t = e.style,
                            n = e.cellType,
                            a = e.children; return r.createElement("div", { className: "rg-celleditor rg-".concat(n, "-celleditor"), style: t }, a) };

                function Bi(e, t) { var n; if (t.column.idx > (e.ranges.stickyLeftRange.last.column ? e.ranges.stickyLeftRange.last.column.idx : e.first.column.idx) || t.column.idx === e.last.column.idx && t.column.idx !== (null === (n = e.ranges.stickyLeftRange.last.column) || void 0 === n ? void 0 : n.idx)) return e.ranges.stickyLeftRange.width }

                function Wi(e, t) { var n; if (t.row.idx > (e.ranges.stickyTopRange.last.row ? e.ranges.stickyTopRange.last.row.idx : e.first.row.idx) || t.row.idx === e.last.row.idx && t.row.idx !== (null === (n = e.ranges.stickyTopRange.last.row) || void 0 === n ? void 0 : n.idx)) return e.ranges.stickyTopRange.height }

                function Ui(e, t, n) { if (e.ranges.stickyLeftRange.first.column && t.column.idx >= e.ranges.stickyLeftRange.first.column.idx && t.column.idx <= e.ranges.stickyLeftRange.last.column.idx) { var r = C(n.scrollableElement).scrollLeft,
                            a = V(n).left; return R(r, a) } }

                function qi(e, t, n) { if (e.ranges.stickyTopRange.first.row && t.row.idx >= e.ranges.stickyTopRange.first.row.idx && t.row.idx <= e.ranges.stickyTopRange.last.row.idx) { var r = C(n.scrollableElement).scrollTop,
                            a = V(n).top; return R(r, a) } } var Gi = function(e) { var t = e.hiddenElementRefHandler,
                            n = Uo().hiddenFocusElement; return r.createElement("input", { className: "rg-hidden-element", ref: t, inputMode: "none", onBlur: function(e) { e.relatedTarget || null == n || n.focus({ preventScroll: !0 }) } }) },
                    Ki = function(e) {
                        function t() { var t = null !== e && e.apply(this, arguments) || this; return t.state = { hasError: !1 }, t } return u(t, e), t.getDerivedStateFromError = function(e) { return { hasError: !0, error: e } }, t.prototype.componentDidCatch = function(e, t) { this.setState({ errorInfo: t }) }, t.prototype.render = function() { var e = this.state,
                                t = e.hasError,
                                n = e.errorInfo,
                                a = e.error; return t ? r.createElement(r.Fragment, null, r.createElement("h1", null, null == a ? void 0 : a.message), " ", r.createElement("br", null), r.createElement("br", null), r.createElement("details", null, null == a ? void 0 : a.stack, null == n ? void 0 : n.componentStack)) : this.props.children }, t }(r.Component);

                function Zi() { return "undefined" != typeof window && navigator.userAgent.includes("Firefox") } var Yi = function(e) { var t = e.eventHandlers,
                            n = e.children,
                            a = Uo(),
                            o = a.cellMatrix,
                            i = a.props,
                            l = { width: (null == i ? void 0 : i.enableFullWidthHeader) ? "100%" : o.width, height: o.height }; return r.createElement(Ki, null, r.createElement("div", { className: "reactgrid", style: h({ position: "relative", paddingRight: Zi() ? "10px" : "" }, l), ref: t.reactgridRefHandler }, r.createElement("div", { className: "reactgrid-content", onKeyDown: t.keyDownHandler, onKeyUp: t.keyUpHandler, onCompositionEnd: t.compositionEndHandler, onPointerDown: t.pointerDownHandler, onPasteCapture: t.pasteCaptureHandler, onPaste: t.pasteHandler, onCopy: t.copyHandler, onCut: t.cutHandler, onBlur: t.blurHandler, style: l }, n, r.createElement(Gi, { hiddenElementRefHandler: t.hiddenElementRefHandler })))) },
                    Xi = { legacyBrowserHeader: "Please update to a modern browser.", legacyBrowserText: "Your current browser cannot run our content, please make sure you browser is fully updated or try adifferent browser. We highly recommend using the most recent release of Google Chrome, Microsoft Edge, Firefox, Safari, and Opera browser", copyLabel: "Copy", cutLabel: "Cut", pasteLabel: "Paste", appleMobileDeviceContextMenuPasteAlert: "Use \u2318 + c for copy, \u2318 + x for cut and \u2318 + v for paste.", otherBrowsersContextMenuPasteAlert: " Use ctrl + c for copy, ctrl + x for cut and ctrl + v for paste.", actionNotSupported: "This action is not supported in this browser." };

                function $i(e) { var t; return h(h({}, Xi), null === (t = e.props) || void 0 === t ? void 0 : t.labels) } var Qi = function() { var e = Uo(); return r.createElement(r.Fragment, null, r.createElement("h3", null, $i(e).legacyBrowserHeader), r.createElement("p", null, $i(e).legacyBrowserText)) };

                function Ji() { return "undefined" != typeof window && (void 0 !== window.orientation || -1 !== navigator.userAgent.indexOf("IEMobile")) } var el = function() { return r.createElement("div", { className: "rg-touch-column-resize-handle", "data-cy": "rg-touch-column-resize-handle" }, r.createElement("div", { className: "rg-resize-handle", "data-cy": "rg-resize-handle" })) },
