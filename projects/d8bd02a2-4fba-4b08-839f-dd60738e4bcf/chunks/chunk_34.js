                    dr = { 1: { n: "CodePage", t: ar }, 2: { n: "Category", t: lr }, 3: { n: "PresentationFormat", t: lr }, 4: { n: "ByteCount", t: or }, 5: { n: "LineCount", t: or }, 6: { n: "ParagraphCount", t: or }, 7: { n: "SlideCount", t: or }, 8: { n: "NoteCount", t: or }, 9: { n: "HiddenCount", t: or }, 10: { n: "MultimediaClipCount", t: or }, 11: { n: "ScaleCrop", t: 11 }, 12: { n: "HeadingPairs", t: 4108 }, 13: { n: "TitlesOfParts", t: 4126 }, 14: { n: "Manager", t: lr }, 15: { n: "Company", t: lr }, 16: { n: "LinksUpToDate", t: 11 }, 17: { n: "CharacterCount", t: or }, 19: { n: "SharedDoc", t: 11 }, 22: { n: "HyperlinksChanged", t: 11 }, 23: { n: "AppVersion", t: or, p: "version" }, 24: { n: "DigSig", t: 65 }, 26: { n: "ContentType", t: lr }, 27: { n: "ContentStatus", t: lr }, 28: { n: "Language", t: lr }, 29: { n: "Version", t: lr }, 255: {}, 2147483648: { n: "Locale", t: 19 }, 2147483651: { n: "Behavior", t: 19 }, 1919054434: {} },
                    ur = { 1: { n: "CodePage", t: ar }, 2: { n: "Title", t: lr }, 3: { n: "Subject", t: lr }, 4: { n: "Author", t: lr }, 5: { n: "Keywords", t: lr }, 6: { n: "Comments", t: lr }, 7: { n: "Template", t: lr }, 8: { n: "LastAuthor", t: lr }, 9: { n: "RevNumber", t: lr }, 10: { n: "EditTime", t: 64 }, 11: { n: "LastPrinted", t: 64 }, 12: { n: "CreatedDate", t: 64 }, 13: { n: "ModifiedDate", t: 64 }, 14: { n: "PageCount", t: or }, 15: { n: "WordCount", t: or }, 16: { n: "CharCount", t: or }, 17: { n: "Thumbnail", t: 71 }, 18: { n: "Application", t: lr }, 19: { n: "DocSecurity", t: or }, 255: {}, 2147483648: { n: "Locale", t: 19 }, 2147483651: { n: "Behavior", t: 19 }, 1919054434: {} },
                    hr = { 1: "US", 2: "CA", 3: "", 7: "RU", 20: "EG", 30: "GR", 31: "NL", 32: "BE", 33: "FR", 34: "ES", 36: "HU", 39: "IT", 41: "CH", 43: "AT", 44: "GB", 45: "DK", 46: "SE", 47: "NO", 48: "PL", 49: "DE", 52: "MX", 55: "BR", 61: "AU", 64: "NZ", 66: "TH", 81: "JP", 82: "KR", 84: "VN", 86: "CN", 90: "TR", 105: "JS", 213: "DZ", 216: "MA", 218: "LY", 351: "PT", 354: "IS", 358: "FI", 420: "CZ", 886: "TW", 961: "LB", 962: "JO", 963: "SY", 964: "IQ", 965: "KW", 966: "SA", 971: "AE", 972: "IL", 974: "QA", 981: "IR", 65535: "US" },
                    mr = [null, "solid", "mediumGray", "darkGray", "lightGray", "darkHorizontal", "darkVertical", "darkDown", "darkUp", "darkGrid", "darkTrellis", "lightHorizontal", "lightVertical", "lightDown", "lightUp", "lightGrid", "lightTrellis", "gray125", "gray0625"];

                function pr(e) { return e.map((function(e) { return [e >> 16 & 255, e >> 8 & 255, 255 & e] })) } var fr = _e(pr([0, 16777215, 16711680, 65280, 255, 16776960, 16711935, 65535, 0, 16777215, 16711680, 65280, 255, 16776960, 16711935, 65535, 8388608, 32768, 128, 8421376, 8388736, 32896, 12632256, 8421504, 10066431, 10040166, 16777164, 13434879, 6684774, 16744576, 26316, 13421823, 128, 16711935, 16776960, 65535, 8388736, 8388608, 32896, 255, 52479, 13434879, 13434828, 16777113, 10079487, 16751052, 13408767, 16764057, 3368703, 3394764, 10079232, 16763904, 16750848, 16737792, 6710937, 9868950, 13158, 3381606, 13056, 3355392, 10040064, 10040166, 3355545, 3355443, 16777215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])),
                    vr = { 0: "#NULL!", 7: "#DIV/0!", 15: "#VALUE!", 23: "#REF!", 29: "#NAME?", 36: "#NUM!", 42: "#N/A", 43: "#GETTING_DATA", 255: "#WTF?" },
                    gr = { "#NULL!": 0, "#DIV/0!": 7, "#VALUE!": 15, "#REF!": 23, "#NAME?": 29, "#NUM!": 36, "#N/A": 42, "#GETTING_DATA": 43, "#WTF?": 255 },
                    yr = { "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml": "workbooks", "application/vnd.ms-excel.sheet.macroEnabled.main+xml": "workbooks", "application/vnd.ms-excel.sheet.binary.macroEnabled.main": "workbooks", "application/vnd.ms-excel.addin.macroEnabled.main+xml": "workbooks", "application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml": "workbooks", "application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml": "sheets", "application/vnd.ms-excel.worksheet": "sheets", "application/vnd.ms-excel.binIndexWs": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml": "charts", "application/vnd.ms-excel.chartsheet": "charts", "application/vnd.ms-excel.macrosheet+xml": "macros", "application/vnd.ms-excel.macrosheet": "macros", "application/vnd.ms-excel.intlmacrosheet": "TODO", "application/vnd.ms-excel.binIndexMs": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml": "dialogs", "application/vnd.ms-excel.dialogsheet": "dialogs", "application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml": "strs", "application/vnd.ms-excel.sharedStrings": "strs", "application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml": "styles", "application/vnd.ms-excel.styles": "styles", "application/vnd.openxmlformats-package.core-properties+xml": "coreprops", "application/vnd.openxmlformats-officedocument.custom-properties+xml": "custprops", "application/vnd.openxmlformats-officedocument.extended-properties+xml": "extprops", "application/vnd.openxmlformats-officedocument.customXmlProperties+xml": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml": "comments", "application/vnd.ms-excel.comments": "comments", "application/vnd.ms-excel.threadedcomments+xml": "threadedcomments", "application/vnd.ms-excel.person+xml": "people", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml": "metadata", "application/vnd.ms-excel.sheetMetadata": "metadata", "application/vnd.ms-excel.pivotTable": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawingml.chart+xml": "TODO", "application/vnd.ms-office.chartcolorstyle+xml": "TODO", "application/vnd.ms-office.chartstyle+xml": "TODO", "application/vnd.ms-office.chartex+xml": "TODO", "application/vnd.ms-excel.calcChain": "calcchains", "application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml": "calcchains", "application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings": "TODO", "application/vnd.ms-office.activeX": "TODO", "application/vnd.ms-office.activeX+xml": "TODO", "application/vnd.ms-excel.attachedToolbars": "TODO", "application/vnd.ms-excel.connections": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml": "TODO", "application/vnd.ms-excel.externalLink": "links", "application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml": "links", "application/vnd.ms-excel.pivotCacheDefinition": "TODO", "application/vnd.ms-excel.pivotCacheRecords": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml": "TODO", "application/vnd.ms-excel.queryTable": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml": "TODO", "application/vnd.ms-excel.userNames": "TODO", "application/vnd.ms-excel.revisionHeaders": "TODO", "application/vnd.ms-excel.revisionLog": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml": "TODO", "application/vnd.ms-excel.tableSingleCells": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml": "TODO", "application/vnd.ms-excel.slicer": "TODO", "application/vnd.ms-excel.slicerCache": "TODO", "application/vnd.ms-excel.slicer+xml": "TODO", "application/vnd.ms-excel.slicerCache+xml": "TODO", "application/vnd.ms-excel.wsSortMap": "TODO", "application/vnd.ms-excel.table": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml": "TODO", "application/vnd.openxmlformats-officedocument.theme+xml": "themes", "application/vnd.openxmlformats-officedocument.themeOverride+xml": "TODO", "application/vnd.ms-excel.Timeline+xml": "TODO", "application/vnd.ms-excel.TimelineCache+xml": "TODO", "application/vnd.ms-office.vbaProject": "vba", "application/vnd.ms-office.vbaProjectSignature": "TODO", "application/vnd.ms-office.volatileDependencies": "TODO", "application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml": "TODO", "application/vnd.ms-excel.controlproperties+xml": "TODO", "application/vnd.openxmlformats-officedocument.model+data": "TODO", "application/vnd.ms-excel.Survey+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawing+xml": "drawings", "application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml": "TODO", "application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml": "TODO", "application/vnd.openxmlformats-officedocument.vmlDrawing": "TODO", "application/vnd.openxmlformats-package.relationships+xml": "rels", "application/vnd.openxmlformats-officedocument.oleObject": "TODO", "image/png": "TODO", sheet: "js" }; var br = { WB: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument", SHEET: "http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument", HLINK: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", VML: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing", XPATH: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath", XMISS: "http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing", XLINK: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink", CXML: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml", CXMLP: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps", CMNT: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments", CORE_PROPS: "http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties", EXT_PROPS: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties", CUST_PROPS: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties", SST: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings", STY: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles", THEME: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme", CHART: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart", CHARTEX: "http://schemas.microsoft.com/office/2014/relationships/chartEx", CS: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet", WS: ["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet", "http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"], DS: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet", MS: "http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet", IMG: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/image", DRAW: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing", XLMETA: "http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata", TCMNT: "http://schemas.microsoft.com/office/2017/10/relationships/threadedComment", PEOPLE: "http://schemas.microsoft.com/office/2017/10/relationships/person", VBA: "http://schemas.microsoft.com/office/2006/relationships/vbaProject" };

                function wr(e) { var t = e.lastIndexOf("/"); return e.slice(0, t + 1) + "_rels/" + e.slice(t + 1) + ".rels" }

                function zr(e, t) { var n = { "!id": {} }; if (!e) return n; "/" !== t.charAt(0) && (t = "/" + t); var r = {}; return (e.match(lt) || []).forEach((function(e) { var a = dt(e); if ("<Relationship" === a[0]) { var o = {};
                            o.Type = a.Type, o.Target = a.Target, o.Id = a.Id, a.TargetMode && (o.TargetMode = a.TargetMode); var i = "External" === a.TargetMode ? a.Target : rt(a.Target, t);
                            n[i] = o, r[a.Id] = o } })), n["!id"] = r, n } var xr = "application/vnd.oasis.opendocument.spreadsheet"; var Ar = [
                        ["cp:category", "Category"],
                        ["cp:contentStatus", "ContentStatus"],
                        ["cp:keywords", "Keywords"],
                        ["cp:lastModifiedBy", "LastAuthor"],
                        ["cp:lastPrinted", "LastPrinted"],
                        ["cp:revision", "RevNumber"],
                        ["cp:version", "Version"],
                        ["dc:creator", "Author"],
                        ["dc:description", "Comments"],
                        ["dc:identifier", "Identifier"],
                        ["dc:language", "Language"],
                        ["dc:subject", "Subject"],
                        ["dc:title", "Title"],
                        ["dcterms:created", "CreatedDate", "date"],
                        ["dcterms:modified", "ModifiedDate", "date"]
                    ],
                    kr = function() { for (var e = new Array(Ar.length), t = 0; t < Ar.length; ++t) { var n = Ar[t],
                                r = "(?:" + n[0].slice(0, n[0].indexOf(":")) + ":)" + n[0].slice(n[0].indexOf(":") + 1);
                            e[t] = new RegExp("<" + r + "[^>]*>([\\s\\S]*?)</" + r + ">") } return e }();

                function Sr(e) { var t = {};
                    e = kt(e); for (var n = 0; n < Ar.length; ++n) { var r = Ar[n],
                            a = e.match(kr[n]);
                        null != a && a.length > 0 && (t[r[1]] = pt(a[1])), "date" === r[2] && t[r[1]] && (t[r[1]] = Fe(t[r[1]])) } return t } var Mr = [
                    ["Application", "Application", "string"],
                    ["AppVersion", "AppVersion", "string"],
                    ["Company", "Company", "string"],
                    ["DocSecurity", "DocSecurity", "string"],
                    ["Manager", "Manager", "string"],
                    ["HyperlinksChanged", "HyperlinksChanged", "bool"],
                    ["SharedDoc", "SharedDoc", "bool"],
                    ["LinksUpToDate", "LinksUpToDate", "bool"],
                    ["ScaleCrop", "ScaleCrop", "bool"],
                    ["HeadingPairs", "HeadingPairs", "raw"],
                    ["TitlesOfParts", "TitlesOfParts", "raw"]
                ];

                function Er(e, t, n, r) { var a = []; if ("string" == typeof e) a = Lt(e, r);
                    else
                        for (var o = 0; o < e.length; ++o) a = a.concat(e[o].map((function(e) { return { v: e } }))); var i = "string" == typeof t ? Lt(t, r).map((function(e) { return e.v })) : t,
                        l = 0,
                        s = 0; if (i.length > 0)
                        for (var c = 0; c !== a.length; c += 2) { switch (s = +a[c + 1].v, a[c].v) {
                                case "Worksheets":
                                case "\u5de5\u4f5c\u8868":
                                case "\u041b\u0438\u0441\u0442\u044b":
                                case "\u0623\u0648\u0631\u0627\u0642 \u0627\u0644\u0639\u0645\u0644":
                                case "\u30ef\u30fc\u30af\u30b7\u30fc\u30c8":
                                case "\u05d2\u05dc\u05d9\u05d5\u05e0\u05d5\u05ea \u05e2\u05d1\u05d5\u05d3\u05d4":
                                case "Arbeitsbl\xe4tter":
                                case "\xc7al\u0131\u015fma Sayfalar\u0131":
                                case "Feuilles de calcul":
                                case "Fogli di lavoro":
                                case "Folhas de c\xe1lculo":
                                case "Planilhas":
                                case "Regneark":
                                case "Hojas de c\xe1lculo":
                                case "Werkbladen":
                                    n.Worksheets = s, n.SheetNames = i.slice(l, l + s); break;
                                case "Named Ranges":
                                case "Rangos con nombre":
                                case "\u540d\u524d\u4ed8\u304d\u4e00\u89a7":
                                case "Benannte Bereiche":
                                case "Navngivne omr\xe5der":
                                    n.NamedRanges = s, n.DefinedNames = i.slice(l, l + s); break;
                                case "Charts":
                                case "Diagramme":
                                    n.Chartsheets = s, n.ChartNames = i.slice(l, l + s) } l += s } } var Cr = /<[^>]+>[^<]*/g; var Tr, Hr = { Title: "Title", Subject: "Subject", Author: "Author", Keywords: "Keywords", Comments: "Description", LastAuthor: "LastAuthor", RevNumber: "Revision", Application: "AppName", LastPrinted: "LastPrinted", CreatedDate: "Created", ModifiedDate: "LastSaved", Category: "Category", Manager: "Manager", Company: "Company", AppVersion: "Version", ContentStatus: "ContentStatus", Identifier: "Identifier", Language: "Language" };

                function Lr(e, t, n) { Tr || (Tr = Ce(Hr)), e[t = Tr[t] || t] = n }

                function Ir(e) { var t = e.read_shift(4),
                        n = e.read_shift(4); return new Date(1e3 * (n / 1e7 * Math.pow(2, 32) + t / 1e7 - 11644473600)).toISOString().replace(/\.000/, "") }

                function jr(e, t, n) { var r = e.l,
                        a = e.read_shift(0, "lpstr-cp"); if (n)
                        for (; e.l - r & 3;) ++e.l; return a }

                function Vr(e, t, n) { var r = e.read_shift(0, "lpwstr"); return n && (e.l += 4 - (r.length + 1 & 3) & 3), r }

                function Or(e, t, n) { return 31 === t ? Vr(e) : jr(e, 0, n) }

                function Rr(e, t, n) { return Or(e, t, !1 === n ? 0 : 4) }

                function Pr(e) { var t = e.l,
                        n = Nr(e, sr); return 0 == e[e.l] && 0 == e[e.l + 1] && e.l - t & 2 && (e.l += 2), [n, Nr(e, or)] }

                function Dr(e, t) { for (var n = e.read_shift(4), r = {}, a = 0; a != n; ++a) { var o = e.read_shift(4),
                            i = e.read_shift(4);
                        r[o] = e.read_shift(i, 1200 === t ? "utf16le" : "utf8").replace(T, "").replace(H, "!"), 1200 === t && i % 2 && (e.l += 2) } return 3 & e.l && (e.l = e.l >> 3 << 2), r }

                function Fr(e) { var t = e.read_shift(4),
                        n = e.slice(e.l, e.l + t); return e.l += t, (3 & t) > 0 && (e.l += 4 - (3 & t) & 3), n }

                function Nr(e, t, n) { var r, a = e.read_shift(2),
                        o = n || {}; if (e.l += 2, t !== ir && a !== t && -1 === cr.indexOf(t) && (4126 != (65534 & t) || 4126 != (65534 & a))) throw new Error("Expected type " + t + " saw " + a); switch (t === ir ? a : t) {
                        case 2:
                            return r = e.read_shift(2, "i"), o.raw || (e.l += 2), r;
                        case 3:
                            return r = e.read_shift(4, "i");
                        case 11:
                            return 0 !== e.read_shift(4);
                        case 19:
                            return r = e.read_shift(4);
                        case 30:
                            return jr(e, 0, 4).replace(T, "");
                        case 31:
                            return Vr(e);
                        case 64:
                            return Ir(e);
                        case 65:
                            return Fr(e);
                        case 71:
                            return function(e) { var t = {}; return t.Size = e.read_shift(4), e.l += t.Size + 3 - (t.Size - 1) % 4, t }(e);
                        case 80:
                            return Rr(e, a, !o.raw).replace(T, "");
                        case 81:
                            return function(e, t) { if (!t) throw new Error("VtUnalignedString must have positive length"); return Or(e, t, 0) }(e, a).replace(T, "");
                        case 4108:
                            return function(e) { for (var t = e.read_shift(4), n = [], r = 0; r < t / 2; ++r) n.push(Pr(e)); return n }(e);
                        case 4126:
                        case 4127:
                            return 4127 == a ? function(e) { for (var t = e.read_shift(4), n = [], r = 0; r != t; ++r) { var a = e.l;
                                    n[r] = e.read_shift(0, "lpwstr").replace(T, ""), e.l - a & 2 && (e.l += 2) } return n }(e) : function(e) { for (var t = e.read_shift(4), n = [], r = 0; r != t; ++r) n[r] = e.read_shift(0, "lpstr-cp").replace(T, ""); return n }(e);
                        default:
                            throw new Error("TypedPropertyValue unrecognized type " + t + " " + a) } }

                function _r(e, t) { var n = e.l,
                        r = e.read_shift(4),
                        a = e.read_shift(4),
                        o = [],
                        i = 0,
                        l = 0,
                        s = -1,
                        d = {}; for (i = 0; i != a; ++i) { var u = e.read_shift(4),
                            h = e.read_shift(4);
                        o[i] = [u, h + n] } o.sort((function(e, t) { return e[1] - t[1] })); var m = {}; for (i = 0; i != a; ++i) { if (e.l !== o[i][1]) { var p = !0; if (i > 0 && t) switch (t[o[i - 1][0]].t) {
                                case 2:
                                    e.l + 2 === o[i][1] && (e.l += 2, p = !1); break;
                                case 80:
                                case 4108:
                                    e.l <= o[i][1] && (e.l = o[i][1], p = !1) }
                            if ((!t || 0 == i) && e.l <= o[i][1] && (p = !1, e.l = o[i][1]), p) throw new Error("Read Error: Expected address " + o[i][1] + " at " + e.l + " :" + i) } if (t) { var f = t[o[i][0]]; if (m[f.n] = Nr(e, f.t, { raw: !0 }), "version" === f.p && (m[f.n] = String(m[f.n] >> 16) + "." + ("0000" + String(65535 & m[f.n])).slice(-4)), "CodePage" == f.n) switch (m[f.n]) {
                                case 0:
                                    m[f.n] = 1252;
                                case 874:
                                case 932:
                                case 936:
                                case 949:
                                case 950:
                                case 1250:
                                case 1251:
                                case 1253:
                                case 1254:
                                case 1255:
                                case 1256:
                                case 1257:
                                case 1258:
                                case 1e4:
                                case 1200:
                                case 1201:
                                case 1252:
                                case 65e3:
                                case -536:
                                case 65001:
                                case -535:
                                    c(l = m[f.n] >>> 0 & 65535); break;
                                default:
                                    throw new Error("Unsupported CodePage: " + m[f.n]) } } else if (1 === o[i][0]) { if (l = m.CodePage = Nr(e, ar), c(l), -1 !== s) { var v = e.l;
                                e.l = o[s][1], d = Dr(e, l), e.l = v } } else if (0 === o[i][0]) { if (0 === l) { s = i, e.l = o[i + 1][1]; continue } d = Dr(e, l) } else { var g, y = d[o[i][0]]; switch (e[e.l]) {
                                case 65:
                                    e.l += 4, g = Fr(e); break;
                                case 30:
                                case 31:
                                    e.l += 4, g = Rr(e, e[e.l - 4]).replace(/\u0000+$/, ""); break;
                                case 3:
                                    e.l += 4, g = e.read_shift(4, "i"); break;
                                case 19:
                                    e.l += 4, g = e.read_shift(4); break;
                                case 5:
                                    e.l += 4, g = e.read_shift(8, "f"); break;
                                case 11:
                                    e.l += 4, g = Ur(e, 4); break;
                                case 64:
                                    e.l += 4, g = Fe(Ir(e)); break;
                                default:
                                    throw new Error("unparsed value: " + e[e.l]) } m[y] = g } } return e.l = n + r, m }

                function Br(e, t, n) { var r = e.content; if (!r) return {};
                    wn(r, 0); var a, o, i, l, s = 0;
                    r.chk("feff", "Byte Order: "), r.read_shift(2); var c = r.read_shift(4),
                        d = r.read_shift(16); if (d !== Se.utils.consts.HEADER_CLSID && d !== n) throw new Error("Bad PropertySet CLSID " + d); if (1 !== (a = r.read_shift(4)) && 2 !== a) throw new Error("Unrecognized #Sets: " + a); if (o = r.read_shift(16), l = r.read_shift(4), 1 === a && l !== r.l) throw new Error("Length mismatch: " + l + " !== " + r.l);
                    2 === a && (i = r.read_shift(16), s = r.read_shift(4)); var u, h = _r(r, t),
                        m = { SystemIdentifier: c }; for (var p in h) m[p] = h[p]; if (m.FMTID = o, 1 === a) return m; if (s - r.l == 2 && (r.l += 2), r.l !== s) throw new Error("Length mismatch 2: " + r.l + " !== " + s); try { u = _r(r, null) } catch (f) {} for (p in u) m[p] = u[p]; return m.FMTID = [o, i], m }

                function Wr(e, t) { return e.read_shift(t), null }

                function Ur(e, t) { return 1 === e.read_shift(t) }

                function qr(e) { return e.read_shift(2, "u") }

                function Gr(e, t) { return function(e, t, n) { for (var r = [], a = e.l + t; e.l < a;) r.push(n(e, a - e.l)); if (a !== e.l) throw new Error("Slurp error"); return r }(e, t, qr) }

                function Kr(e, t, n) { var r = e.read_shift(n && n.biff >= 12 ? 2 : 1),
                        o = "sbcs-cont",
                        i = a;
                    (n && n.biff >= 8 && (a = 1200), n && 8 != n.biff) ? 12 == n.biff && (o = "wstr"): e.read_shift(1) && (o = "dbcs-cont");
                    n.biff >= 2 && n.biff <= 5 && (o = "cpstr"); var l = r ? e.read_shift(r, o) : ""; return a = i, l }

                function Zr(e) { var t = a;
                    a = 1200; var n, r = e.read_shift(2),
                        o = e.read_shift(1),
                        i = 4 & o,
                        l = 8 & o,
                        s = 1 + (1 & o),
                        c = 0,
                        d = {};
                    l && (c = e.read_shift(2)), i && (n = e.read_shift(4)); var u = 2 == s ? "dbcs-cont" : "sbcs-cont",
                        h = 0 === r ? "" : e.read_shift(r, u); return l && (e.l += 4 * c), i && (e.l += n), d.t = h, l || (d.raw = "<t>" + d.t + "</t>", d.r = d.t), a = t, d }

                function Yr(e, t, n) { if (n) { if (n.biff >= 2 && n.biff <= 5) return e.read_shift(t, "cpstr"); if (n.biff >= 12) return e.read_shift(t, "dbcs-cont") } return 0 === e.read_shift(1) ? e.read_shift(t, "sbcs-cont") : e.read_shift(t, "dbcs-cont") }

                function Xr(e, t, n) { var r = e.read_shift(n && 2 == n.biff ? 1 : 2); return 0 === r ? (e.l++, "") : Yr(e, r, n) }

                function $r(e, t, n) { if (n.biff > 5) return Xr(e, 0, n); var r = e.read_shift(1); return 0 === r ? (e.l++, "") : e.read_shift(r, n.biff <= 4 || !e.lens ? "cpstr" : "sbcs-cont") }

                function Qr(e, t) { var n = e.read_shift(16); switch (16, n) {
                        case "e0c9ea79f9bace118c8200aa004ba90b":
                            return function(e) { var t = e.read_shift(4),
                                    n = e.l,
                                    r = !1;
                                t > 24 && (e.l += t - 24, "795881f43b1d7f48af2c825dc4852763" === e.read_shift(16) && (r = !0), e.l = n); var a = e.read_shift((r ? t - 24 : t) >> 1, "utf16le").replace(T, ""); return r && (e.l += 24), a }(e);
                        case "0303000000000000c000000000000046":
                            return function(e) { for (var t = e.read_shift(2), n = ""; t-- > 0;) n += "../"; var r = e.read_shift(0, "lpstr-ansi"); if (e.l += 2, 57005 != e.read_shift(2)) throw new Error("Bad FileMoniker"); if (0 === e.read_shift(4)) return n + r.replace(/\\/g, "/"); var a = e.read_shift(4); if (3 != e.read_shift(2)) throw new Error("Bad FileMoniker"); return n + e.read_shift(a >> 1, "utf16le").replace(T, "") }(e);
                        default:
                            throw new Error("Unsupported Moniker " + n) } }

                function Jr(e) { var t = e.read_shift(4); return t > 0 ? e.read_shift(t, "utf16le").replace(T, "") : "" }

                function ea(e) { return [e.read_shift(1), e.read_shift(1), e.read_shift(1), e.read_shift(1)] }

                function ta(e, t) { var n = ea(e); return n[3] = 0, n }

                function na(e) { return { r: e.read_shift(2), c: e.read_shift(2), ixfe: e.read_shift(2) } }

                function ra(e, t, n) { var r = n.biff > 8 ? 4 : 2; return [e.read_shift(r), e.read_shift(r, "i"), e.read_shift(r, "i")] }

                function aa(e) { return [e.read_shift(2), Jn(e)] }

                function oa(e) { var t = e.read_shift(2),
                        n = e.read_shift(2); return { s: { c: e.read_shift(2), r: t }, e: { c: e.read_shift(2), r: n } } }

                function ia(e) { var t = e.read_shift(2),
                        n = e.read_shift(2); return { s: { c: e.read_shift(1), r: t }, e: { c: e.read_shift(1), r: n } } } var la = ia;

                function sa(e) { e.l += 4; var t = e.read_shift(2),
                        n = e.read_shift(2),
                        r = e.read_shift(2); return e.l += 12, [n, t, r] }

                function ca(e) { e.l += 2, e.l += e.read_shift(2) } var da = { 0: ca, 4: ca, 5: ca, 6: ca, 7: function(e) { return e.l += 4, e.cf = e.read_shift(2), {} }, 8: ca, 9: ca, 10: ca, 11: ca, 12: ca, 13: function(e) { var t = {}; return e.l += 4, e.l += 16, t.fSharedNote = e.read_shift(2), e.l += 4, t }, 14: ca, 15: ca, 16: ca, 17: ca, 18: ca, 19: ca, 20: ca, 21: sa };

                function ua(e, t) { var n = { BIFFVer: 0, dt: 0 }; switch (n.BIFFVer = e.read_shift(2), (t -= 2) >= 2 && (n.dt = e.read_shift(2), e.l -= 2), n.BIFFVer) {
                        case 1536:
                        case 1280:
                        case 1024:
                        case 768:
                        case 512:
                        case 2:
                        case 7:
                            break;
                        default:
                            if (t > 6) throw new Error("Unexpected BIFF Ver " + n.BIFFVer) } return e.read_shift(t), n }

                function ha(e, t, n) { var r = 0;
                    n && 2 == n.biff || (r = e.read_shift(2)); var a = e.read_shift(2); return n && 2 == n.biff && (r = 1 - (a >> 15), a &= 32767), [{ Unsynced: 1 & r, DyZero: (2 & r) >> 1, ExAsc: (4 & r) >> 2, ExDsc: (8 & r) >> 3 }, a] } var ma = $r;

                function pa(e, t, n) { var r = e.l + t,
                        a = 8 != n.biff && n.biff ? 2 : 4,
                        o = e.read_shift(a),
                        i = e.read_shift(a),
                        l = e.read_shift(2),
                        s = e.read_shift(2); return e.l = r, { s: { r: o, c: l }, e: { r: i, c: s } } }

                function fa(e, t, n) { var r = na(e);
                    2 != n.biff && 9 != t || ++e.l; var a = function(e) { var t = e.read_shift(1); return 1 === e.read_shift(1) ? t : 1 === t }(e); return r.val = a, r.t = !0 === a || !1 === a ? "b" : "e", r } var va = function(e, t, n) { return 0 === t ? "" : $r(e, 0, n) };

                function ga(e, t, n) { var r, a = e.read_shift(2),
                        o = { fBuiltIn: 1 & a, fWantAdvise: a >>> 1 & 1, fWantPict: a >>> 2 & 1, fOle: a >>> 3 & 1, fOleLink: a >>> 4 & 1, cf: a >>> 5 & 1023, fIcon: a >>> 15 & 1 }; return 14849 === n.sbcch && (r = function(e, t, n) { e.l += 4, t -= 4; var r = e.l + t,
                            a = Kr(e, 0, n),
                            o = e.read_shift(2); if (o !== (r -= e.l)) throw new Error("Malformed AddinUdf: padding = " + r + " != " + o); return e.l += o, a }(e, t - 2, n)), o.body = r || e.read_shift(t - 2), "string" === typeof r && (o.Name = r), o } var ya = ["_xlnm.Consolidate_Area", "_xlnm.Auto_Open", "_xlnm.Auto_Close", "_xlnm.Extract", "_xlnm.Database", "_xlnm.Criteria", "_xlnm.Print_Area", "_xlnm.Print_Titles", "_xlnm.Recorder", "_xlnm.Data_Form", "_xlnm.Auto_Activate", "_xlnm.Auto_Deactivate", "_xlnm.Sheet_Title", "_xlnm._FilterDatabase"];

                function ba(e, t, n) { var r = e.l + t,
                        a = e.read_shift(2),
                        o = e.read_shift(1),
                        i = e.read_shift(1),
                        l = e.read_shift(n && 2 == n.biff ? 1 : 2),
                        s = 0;
                    (!n || n.biff >= 5) && (5 != n.biff && (e.l += 2), s = e.read_shift(2), 5 == n.biff && (e.l += 2), e.l += 4); var c = Yr(e, i, n);
                    32 & a && (c = ya[c.charCodeAt(0)]); var d = r - e.l;
                    n && 2 == n.biff && --d; var u = r != e.l && 0 !== l && d > 0 ? function(e, t, n, r) { var a, o = e.l + t,
                            i = hi(e, r, n);
                        o !== e.l && (a = ui(e, o - e.l, i, n)); return [i, a] }(e, d, n, l) : []; return { chKey: o, Name: c, itab: s, rgce: u } }

                function wa(e, t, n) { if (n.biff < 8) return function(e, t, n) { 3 == e[e.l + 1] && e[e.l]++; var r = Kr(e, 0, n); return 3 == r.charCodeAt(0) ? r.slice(1) : r }(e, 0, n); for (var r = [], a = e.l + t, o = e.read_shift(n.biff > 8 ? 4 : 2); 0 !== o--;) r.push(ra(e, n.biff, n)); if (e.l != a) throw new Error("Bad ExternSheet: " + e.l + " != " + a); return r }

                function za(e, t, n) { var r = la(e, 6); switch (n.biff) {
                        case 2:
                            e.l++, t -= 7; break;
                        case 3:
                        case 4:
                            e.l += 2, t -= 8; break;
                        default:
                            e.l += 6, t -= 12 } return [r, yi(e, t, n)] } var xa = { 8: function(e, t) { var n = e.l + t;
                        e.l += 10; var r = e.read_shift(2);
                        e.l += 4, e.l += 2, e.l += 2, e.l += 2, e.l += 4; var a = e.read_shift(1); return e.l += a, e.l = n, { fmt: r } } };

                function Aa(e, t, n) { if (!n.cellStyles) return zn(e, t); var r = n && n.biff >= 12 ? 4 : 2,
                        a = e.read_shift(r),
                        o = e.read_shift(r),
                        i = e.read_shift(r),
                        l = e.read_shift(r),
                        s = e.read_shift(2);
                    2 == r && (e.l += 2); var c = { s: a, e: o, w: i, ixfe: l, flags: s }; return (n.biff >= 5 || !n.biff) && (c.level = s >> 8 & 7), c } var ka = na,
                    Sa = Gr,
                    Ma = Xr; var Ea = [2, 3, 48, 49, 131, 139, 140, 245],
                    Ca = function() { var e = { 1: 437, 2: 850, 3: 1252, 4: 1e4, 100: 852, 101: 866, 102: 865, 103: 861, 104: 895, 105: 620, 106: 737, 107: 857, 120: 950, 121: 949, 122: 936, 123: 932, 124: 874, 125: 1255, 126: 1256, 150: 10007, 151: 10029, 152: 10006, 200: 1250, 201: 1251, 202: 1254, 203: 1253, 0: 20127, 8: 865, 9: 437, 10: 850, 11: 437, 13: 437, 14: 850, 15: 437, 16: 850, 17: 437, 18: 850, 19: 932, 20: 850, 21: 437, 22: 850, 23: 865, 24: 437, 25: 437, 26: 850, 27: 437, 28: 863, 29: 850, 31: 852, 34: 852, 35: 852, 36: 860, 37: 850, 38: 866, 55: 850, 64: 852, 77: 936, 78: 949, 79: 950, 80: 874, 87: 1252, 88: 1252, 89: 1252, 108: 863, 134: 737, 135: 852, 136: 857, 204: 1257, 255: 16969 },
                            t = Ce({ 1: 437, 2: 850, 3: 1252, 4: 1e4, 100: 852, 101: 866, 102: 865, 103: 861, 104: 895, 105: 620, 106: 737, 107: 857, 120: 950, 121: 949, 122: 936, 123: 932, 124: 874, 125: 1255, 126: 1256, 150: 10007, 151: 10029, 152: 10006, 200: 1250, 201: 1251, 202: 1254, 203: 1253, 0: 20127 });

                        function n(t, n) { var r = n || {};
                            r.dateNF || (r.dateNF = "yyyymmdd"); var a = Bn(function(t, n) { var r = [],
                                    a = A(1); switch (n.type) {
                                    case "base64":
                                        a = S(w(t)); break;
                                    case "binary":
                                        a = S(t); break;
                                    case "buffer":
                                    case "array":
                                        a = t } wn(a, 0); var o = a.read_shift(1),
                                    i = !!(136 & o),
                                    l = !1,
                                    s = !1; switch (o) {
                                    case 2:
                                    case 3:
                                    case 131:
                                    case 139:
                                    case 245:
                                        break;
                                    case 48:
                                    case 49:
                                        l = !0, i = !0; break;
                                    case 140:
                                        s = !0; break;
                                    default:
                                        throw new Error("DBF Unsupported Version: " + o.toString(16)) } var c = 0,
                                    d = 521;
                                2 == o && (c = a.read_shift(2)), a.l += 3, 2 != o && (c = a.read_shift(4)), c > 1048576 && (c = 1e6), 2 != o && (d = a.read_shift(2)); var u = a.read_shift(2),
                                    h = n.codepage || 1252;
                                2 != o && (a.l += 16, a.read_shift(1), 0 !== a[a.l] && (h = e[a[a.l]]), a.l += 1, a.l += 2), s && (a.l += 36); for (var p = [], f = {}, v = Math.min(a.length, 2 == o ? 521 : d - 10 - (l ? 264 : 0)), g = s ? 32 : 11; a.l < v && 13 != a[a.l];) switch ((f = {}).name = m.utils.decode(h, a.slice(a.l, a.l + g)).replace(/[\u0000\r\n].*$/g, ""), a.l += g, f.type = String.fromCharCode(a.read_shift(1)), 2 == o || s || (f.offset = a.read_shift(4)), f.len = a.read_shift(1), 2 == o && (f.offset = a.read_shift(2)), f.dec = a.read_shift(1), f.name.length && p.push(f), 2 != o && (a.l += s ? 13 : 14), f.type) {
                                    case "B":
                                        l && 8 == f.len || !n.WTF || console.log("Skipping " + f.name + ":" + f.type); break;
                                    case "G":
                                    case "P":
                                        n.WTF && console.log("Skipping " + f.name + ":" + f.type); break;
                                    case "+":
                                    case "0":
                                    case "@":
                                    case "C":
                                    case "D":
                                    case "F":
                                    case "I":
                                    case "L":
                                    case "M":
                                    case "N":
                                    case "O":
                                    case "T":
                                    case "Y":
                                        break;
                                    default:
                                        throw new Error("Unknown Field Type: " + f.type) }
                                if (13 !== a[a.l] && (a.l = d - 1), 13 !== a.read_shift(1)) throw new Error("DBF Terminator not found " + a.l + " " + a[a.l]);
                                a.l = d; var y = 0,
                                    b = 0; for (r[0] = [], b = 0; b != p.length; ++b) r[0][b] = p[b].name; for (; c-- > 0;)
                                    if (42 !== a[a.l])
                                        for (++a.l, r[++y] = [], b = 0, b = 0; b != p.length; ++b) { var z = a.slice(a.l, a.l + p[b].len);
                                            a.l += p[b].len, wn(z, 0); var x = m.utils.decode(h, z); switch (p[b].type) {
                                                case "C":
                                                    x.trim().length && (r[y][b] = x.replace(/\s+$/, "")); break;
                                                case "D":
                                                    8 === x.length ? r[y][b] = new Date(+x.slice(0, 4), +x.slice(4, 6) - 1, +x.slice(6, 8)) : r[y][b] = x; break;
                                                case "F":
                                                    r[y][b] = parseFloat(x.trim()); break;
                                                case "+":
                                                case "I":
                                                    r[y][b] = s ? 2147483648 ^ z.read_shift(-4, "i") : z.read_shift(4, "i"); break;
                                                case "L":
                                                    switch (x.trim().toUpperCase()) {
                                                        case "Y":
                                                        case "T":
                                                            r[y][b] = !0; break;
                                                        case "N":
                                                        case "F":
                                                            r[y][b] = !1; break;
                                                        case "":
                                                        case "?":
                                                            break;
                                                        default:
                                                            throw new Error("DBF Unrecognized L:|" + x + "|") } break;
                                                case "M":
                                                    if (!i) throw new Error("DBF Unexpected MEMO for type " + o.toString(16));
                                                    r[y][b] = "##MEMO##" + (s ? parseInt(x.trim(), 10) : z.read_shift(4)); break;
                                                case "N":
                                                    (x = x.replace(/\u0000/g, "").trim()) && "." != x && (r[y][b] = +x || 0); break;
                                                case "@":
                                                    r[y][b] = new Date(z.read_shift(-8, "f") - 621356832e5); break;
                                                case "T":
                                                    r[y][b] = new Date(864e5 * (z.read_shift(4) - 2440588) + z.read_shift(4)); break;
                                                case "Y":
                                                    r[y][b] = z.read_shift(4, "i") / 1e4 + z.read_shift(4, "i") / 1e4 * Math.pow(2, 32); break;
                                                case "O":
                                                    r[y][b] = -z.read_shift(-8, "f"); break;
                                                case "B":
                                                    if (l && 8 == p[b].len) { r[y][b] = z.read_shift(8, "f"); break }
                                                case "G":
                                                case "P":
                                                    z.l += p[b].len; break;
                                                case "0":
                                                    if ("_NullFlags" === p[b].name) break;
                                                default:
                                                    throw new Error("DBF Unsupported data type " + p[b].type) } } else a.l += u; if (2 != o && a.l < a.length && 26 != a[a.l++]) throw new Error("DBF EOF Marker missing " + (a.l - 1) + " of " + a.length + " " + a[a.l - 1].toString(16)); return n && n.sheetRows && (r = r.slice(0, n.sheetRows)), n.DBF = p, r }(t, r), r); return a["!cols"] = r.DBF.map((function(e) { return { wch: e.len, DBF: e } })), delete r.DBF, a } var r = { B: 8, C: 250, L: 1, D: 8, "?": 0, "": 0 }; return { to_workbook: function(e, t) { try { return Nn(n(e, t), t) } catch (r) { if (t && t.WTF) throw r } return { SheetNames: [], Sheets: {} } }, to_sheet: n, from_sheet: function(e, n) { var a = n || {}; if (+a.codepage >= 0 && c(+a.codepage), "string" == a.type) throw new Error("Cannot write DBF to JS string"); var i = kn(),
                                    l = js(e, { header: 1, raw: !0, cellDates: !0 }),
                                    s = l[0],
                                    d = l.slice(1),
                                    u = e["!cols"] || [],
                                    h = 0,
                                    m = 0,
                                    p = 0,
                                    f = 1; for (h = 0; h < s.length; ++h)
                                    if (((u[h] || {}).DBF || {}).name) s[h] = u[h].DBF.name, ++p;
                                    else if (null != s[h]) { if (++p, "number" === typeof s[h] && (s[h] = s[h].toString(10)), "string" !== typeof s[h]) throw new Error("DBF Invalid column name " + s[h] + " |" + typeof s[h] + "|"); if (s.indexOf(s[h]) !== h)
                                        for (m = 0; m < 1024; ++m)
                                            if (-1 == s.indexOf(s[h] + "_" + m)) { s[h] += "_" + m; break } } var v = Pn(e["!ref"]),
                                    g = [],
                                    y = [],
                                    b = []; for (h = 0; h <= v.e.c - v.s.c; ++h) { var w = "",
                                        z = "",
                                        x = 0,
                                        A = []; for (m = 0; m < d.length; ++m) null != d[m][h] && A.push(d[m][h]); if (0 != A.length && null != s[h]) { for (m = 0; m < A.length; ++m) { switch (typeof A[m]) {
                                                case "number":
                                                    z = "B"; break;
                                                case "string":
                                                default:
                                                    z = "C"; break;
                                                case "boolean":
                                                    z = "L"; break;
                                                case "object":
                                                    z = A[m] instanceof Date ? "D" : "C" } x = Math.max(x, String(A[m]).length), w = w && w != z ? "C" : z } x > 250 && (x = 250), "C" == (z = ((u[h] || {}).DBF || {}).type) && u[h].DBF.len > x && (x = u[h].DBF.len), "B" == w && "N" == z && (w = "N", b[h] = u[h].DBF.dec, x = u[h].DBF.len), y[h] = "C" == w || "N" == z ? x : r[w] || 0, f += y[h], g[h] = w } else g[h] = "?" } var k = i.next(32); for (k.write_shift(4, 318902576), k.write_shift(4, d.length), k.write_shift(2, 296 + 32 * p), k.write_shift(2, f), h = 0; h < 4; ++h) k.write_shift(4, 0); for (k.write_shift(4, (+t[o] || 3) << 8), h = 0, m = 0; h < s.length; ++h)
                                    if (null != s[h]) { var S = i.next(32),
                                            M = (s[h].slice(-10) + "\0\0\0\0\0\0\0\0\0\0\0").slice(0, 11);
                                        S.write_shift(1, M, "sbcs"), S.write_shift(1, "?" == g[h] ? "C" : g[h], "sbcs"), S.write_shift(4, m), S.write_shift(1, y[h] || r[g[h]] || 0), S.write_shift(1, b[h] || 0), S.write_shift(1, 2), S.write_shift(4, 0), S.write_shift(1, 0), S.write_shift(4, 0), S.write_shift(4, 0), m += y[h] || r[g[h]] || 0 } var E = i.next(264); for (E.write_shift(4, 13), h = 0; h < 65; ++h) E.write_shift(4, 0); for (h = 0; h < d.length; ++h) { var C = i.next(f); for (C.write_shift(1, 0), m = 0; m < s.length; ++m)
                                        if (null != s[m]) switch (g[m]) {
                                            case "L":
                                                C.write_shift(1, null == d[h][m] ? 63 : d[h][m] ? 84 : 70); break;
                                            case "B":
                                                C.write_shift(8, d[h][m] || 0, "f"); break;
                                            case "N":
                                                var T = "0"; for ("number" == typeof d[h][m] && (T = d[h][m].toFixed(b[m] || 0)), p = 0; p < y[m] - T.length; ++p) C.write_shift(1, 32);
                                                C.write_shift(1, T, "sbcs"); break;
                                            case "D":
                                                d[h][m] ? (C.write_shift(4, ("0000" + d[h][m].getFullYear()).slice(-4), "sbcs"), C.write_shift(2, ("00" + (d[h][m].getMonth() + 1)).slice(-2), "sbcs"), C.write_shift(2, ("00" + d[h][m].getDate()).slice(-2), "sbcs")) : C.write_shift(8, "00000000", "sbcs"); break;
                                            case "C":
                                                var H = String(null != d[h][m] ? d[h][m] : "").slice(0, y[m]); for (C.write_shift(1, H, "sbcs"), p = 0; p < y[m] - H.length; ++p) C.write_shift(1, 32) } } return i.next(1).write_shift(1, 26), i.end() } } }(),
                    Ta = function() { var e = { AA: "\xc0", BA: "\xc1", CA: "\xc2", DA: 195, HA: "\xc4", JA: 197, AE: "\xc8", BE: "\xc9", CE: "\xca", HE: "\xcb", AI: "\xcc", BI: "\xcd", CI: "\xce", HI: "\xcf", AO: "\xd2", BO: "\xd3", CO: "\xd4", DO: 213, HO: "\xd6", AU: "\xd9", BU: "\xda", CU: "\xdb", HU: "\xdc", Aa: "\xe0", Ba: "\xe1", Ca: "\xe2", Da: 227, Ha: "\xe4", Ja: 229, Ae: "\xe8", Be: "\xe9", Ce: "\xea", He: "\xeb", Ai: "\xec", Bi: "\xed", Ci: "\xee", Hi: "\xef", Ao: "\xf2", Bo: "\xf3", Co: "\xf4", Do: 245, Ho: "\xf6", Au: "\xf9", Bu: "\xfa", Cu: "\xfb", Hu: "\xfc", KC: "\xc7", Kc: "\xe7", q: "\xe6", z: "\u0153", a: "\xc6", j: "\u0152", DN: 209, Dn: 241, Hy: 255, S: 169, c: 170, R: 174, "B ": 180, 0: 176, 1: 177, 2: 178, 3: 179, 5: 181, 6: 182, 7: 183, Q: 185, k: 186, b: 208, i: 216, l: 222, s: 240, y: 248, "!": 161, '"': 162, "#": 163, "(": 164, "%": 165, "'": 167, "H ": 168, "+": 171, ";": 187, "<": 188, "=": 189, ">": 190, "?": 191, "{": 223 },
                            t = new RegExp("\x1bN(" + Ee(e).join("|").replace(/\|\|\|/, "|\\||").replace(/([?()+])/g, "\\$1") + "|\\|)", "gm"),
                            n = function(t, n) { var r = e[n]; return "number" == typeof r ? v(r) : r },
                            r = function(e, t, n) { var r = t.charCodeAt(0) - 32 << 4 | n.charCodeAt(0) - 48; return 59 == r ? e : v(r) };

                        function a(e, a) { var o, i = e.split(/[\n\r]+/),
                                l = -1,
                                s = -1,
                                d = 0,
                                u = 0,
                                h = [],
                                p = [],
                                f = null,
                                v = {},
                                g = [],
                                y = [],
                                b = [],
                                w = 0; for (+a.codepage >= 0 && c(+a.codepage); d !== i.length; ++d) { w = 0; var z, x = i[d].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g, r).replace(t, n),
                                    A = x.replace(/;;/g, "\0").split(";").map((function(e) { return e.replace(/\u0000/g, ";") })),
                                    k = A[0]; if (x.length > 0) switch (k) {
                                    case "ID":
                                    case "E":
                                    case "B":
                                    case "O":
                                    case "W":
                                        break;
                                    case "P":
                                        "P" == A[1].charAt(0) && p.push(x.slice(3).replace(/;;/g, ";")); break;
                                    case "C":
                                        var S = !1,
                                            M = !1,
                                            E = !1,
                                            C = !1,
                                            T = -1,
                                            H = -1; for (u = 1; u < A.length; ++u) switch (A[u].charAt(0)) {
                                            case "A":
                                            case "G":
                                                break;
                                            case "X":
                                                s = parseInt(A[u].slice(1)) - 1, M = !0; break;
                                            case "Y":
                                                for (l = parseInt(A[u].slice(1)) - 1, M || (s = 0), o = h.length; o <= l; ++o) h[o] = []; break;
                                            case "K":
                                                '"' === (z = A[u].slice(1)).charAt(0) ? z = z.slice(1, z.length - 1) : "TRUE" === z ? z = !0 : "FALSE" === z ? z = !1 : isNaN(We(z)) ? isNaN(qe(z).getDate()) || (z = Fe(z)) : (z = We(z), null !== f && fe(f) && (z = Ve(z))), "undefined" !== typeof m && "string" == typeof z && "string" != (a || {}).type && (a || {}).codepage && (z = m.utils.decode(a.codepage, z)), S = !0; break;
                                            case "E":
                                                C = !0; var L = No(A[u].slice(1), { r: l, c: s });
                                                h[l][s] = [h[l][s], L]; break;
                                            case "S":
                                                E = !0, h[l][s] = [h[l][s], "S5S"]; break;
                                            case "R":
                                                T = parseInt(A[u].slice(1)) - 1; break;
                                            case "C":
                                                H = parseInt(A[u].slice(1)) - 1; break;
                                            default:
                                                if (a && a.WTF) throw new Error("SYLK bad record " + x) }
                                        if (S && (h[l][s] && 2 == h[l][s].length ? h[l][s][0] = z : h[l][s] = z, f = null), E) { if (C) throw new Error("SYLK shared formula cannot have own formula"); var I = T > -1 && h[T][H]; if (!I || !I[1]) throw new Error("SYLK shared formula cannot find base");
                                            h[l][s][1] = Wo(I[1], { r: l - T, c: s - H }) } break;
                                    case "F":
                                        var j = 0; for (u = 1; u < A.length; ++u) switch (A[u].charAt(0)) {
                                            case "X":
                                                s = parseInt(A[u].slice(1)) - 1, ++j; break;
                                            case "Y":
                                                for (l = parseInt(A[u].slice(1)) - 1, o = h.length; o <= l; ++o) h[o] = []; break;
                                            case "M":
                                                w = parseInt(A[u].slice(1)) / 20; break;
                                            case "F":
                                            case "G":
                                            case "S":
                                            case "D":
                                            case "N":
                                                break;
                                            case "P":
                                                f = p[parseInt(A[u].slice(1))]; break;
                                            case "W":
                                                for (b = A[u].slice(1).split(" "), o = parseInt(b[0], 10); o <= parseInt(b[1], 10); ++o) w = parseInt(b[2], 10), y[o - 1] = 0 === w ? { hidden: !0 } : { wch: w }, fo(y[o - 1]); break;
                                            case "C":
                                                y[s = parseInt(A[u].slice(1)) - 1] || (y[s] = {}); break;
                                            case "R":
                                                g[l = parseInt(A[u].slice(1)) - 1] || (g[l] = {}), w > 0 ? (g[l].hpt = w, g[l].hpx = yo(w)) : 0 === w && (g[l].hidden = !0); break;
                                            default:
                                                if (a && a.WTF) throw new Error("SYLK bad record " + x) } j < 1 && (f = null); break;
                                    default:
                                        if (a && a.WTF) throw new Error("SYLK bad record " + x) } } return g.length > 0 && (v["!rows"] = g), y.length > 0 && (v["!cols"] = y), a && a.sheetRows && (h = h.slice(0, a.sheetRows)), [h, v] }

                        function o(e, t) { var n = function(e, t) { switch (t.type) {
                                        case "base64":
                                            return a(w(e), t);
                                        case "binary":
                                            return a(e, t);
                                        case "buffer":
                                            return a(z && Buffer.isBuffer(e) ? e.toString("binary") : M(e), t);
                                        case "array":
                                            return a(Ne(e), t) } throw new Error("Unrecognized type " + t.type) }(e, t),
                                r = n[0],
                                o = n[1],
                                i = Bn(r, t); return Ee(o).forEach((function(e) { i[e] = o[e] })), i }

                        function i(e, t, n, r) { var a = "C;Y" + (n + 1) + ";X" + (r + 1) + ";K"; switch (e.t) {
                                case "n":
                                    a += e.v || 0, e.f && !e.F && (a += ";E" + Bo(e.f, { r: n, c: r })); break;
                                case "b":
                                    a += e.v ? "TRUE" : "FALSE"; break;
                                case "e":
                                    a += e.w || e.v; break;
                                case "d":
                                    a += '"' + (e.w || e.v) + '"'; break;
                                case "s":
                                    a += '"' + e.v.replace(/"/g, "").replace(/;/g, ";;") + '"' } return a } return e["|"] = 254, { to_workbook: function(e, t) { return Nn(o(e, t), t) }, to_sheet: o, from_sheet: function(e, t) { var n, r, a = ["ID;PWXL;N;E"],
                                    o = [],
                                    l = Pn(e["!ref"]),
                                    s = Array.isArray(e),
                                    c = "\r\n";
                                a.push("P;PGeneral"), a.push("F;P0;DG0G8;M255"), e["!cols"] && (r = a, e["!cols"].forEach((function(e, t) { var n = "F;W" + (t + 1) + " " + (t + 1) + " ";
                                    e.hidden ? n += "0" : ("number" != typeof e.width || e.wpx || (e.wpx = co(e.width)), "number" != typeof e.wpx || e.wch || (e.wch = uo(e.wpx)), "number" == typeof e.wch && (n += Math.round(e.wch))), " " != n.charAt(n.length - 1) && r.push(n) }))), e["!rows"] && function(e, t) { t.forEach((function(t, n) { var r = "F;";
                                        t.hidden ? r += "M0;" : t.hpt ? r += "M" + 20 * t.hpt + ";" : t.hpx && (r += "M" + 20 * go(t.hpx) + ";"), r.length > 2 && e.push(r + "R" + (n + 1)) })) }(a, e["!rows"]), a.push("B;Y" + (l.e.r - l.s.r + 1) + ";X" + (l.e.c - l.s.c + 1) + ";D" + [l.s.c, l.s.r, l.e.c, l.e.r].join(" ")); for (var d = l.s.r; d <= l.e.r; ++d)
                                    for (var u = l.s.c; u <= l.e.c; ++u) { var h = Vn({ r: d, c: u });
                                        (n = s ? (e[d] || [])[u] : e[h]) && (null != n.v || n.f && !n.F) && o.push(i(n, 0, d, u)) }
                                return a.join(c) + c + o.join(c) + c + "E" + c } } }(),
                    Ha = function() {
                        function e(e, t) { for (var n = e.split("\n"), r = -1, a = -1, o = 0, i = []; o !== n.length; ++o)
                                if ("BOT" !== n[o].trim()) { if (!(r < 0)) { for (var l = n[o].trim().split(","), s = l[0], c = l[1], d = n[++o] || ""; 1 & (d.match(/["]/g) || []).length && o < n.length - 1;) d += "\n" + n[++o]; switch (d = d.trim(), +s) {
                                            case -1:
                                                if ("BOT" === d) { i[++r] = [], a = 0; continue } if ("EOD" !== d) throw new Error("Unrecognized DIF special command " + d); break;
                                            case 0:
                                                "TRUE" === d ? i[r][a] = !0 : "FALSE" === d ? i[r][a] = !1 : isNaN(We(c)) ? isNaN(qe(c).getDate()) ? i[r][a] = c : i[r][a] = Fe(c) : i[r][a] = We(c), ++a; break;
                                            case 1:
                                                (d = (d = d.slice(1, d.length - 1)).replace(/""/g, '"')) && d.match(/^=".*"$/) && (d = d.slice(2, -1)), i[r][a++] = "" !== d ? d : null } if ("EOD" === d) break } } else i[++r] = [], a = 0; return t && t.sheetRows && (i = i.slice(0, t.sheetRows)), i }

                        function t(t, n) { return Bn(function(t, n) { switch (n.type) {
                                    case "base64":
                                        return e(w(t), n);
                                    case "binary":
                                        return e(t, n);
                                    case "buffer":
                                        return e(z && Buffer.isBuffer(t) ? t.toString("binary") : M(t), n);
                                    case "array":
                                        return e(Ne(t), n) } throw new Error("Unrecognized type " + n.type) }(t, n), n) } return { to_workbook: function(e, n) { return Nn(t(e, n), n) }, to_sheet: t, from_sheet: function() { var e = function(e, t, n, r, a) { e.push(t), e.push(n + "," + r), e.push('"' + a.replace(/"/g, '""') + '"') },
                                    t = function(e, t, n, r) { e.push(t + "," + n), e.push(1 == t ? '"' + r.replace(/"/g, '""') + '"' : r) }; return function(n) { var r, a = [],
                                        o = Pn(n["!ref"]),
                                        i = Array.isArray(n);
                                    e(a, "TABLE", 0, 1, "sheetjs"), e(a, "VECTORS", 0, o.e.r - o.s.r + 1, ""), e(a, "TUPLES", 0, o.e.c - o.s.c + 1, ""), e(a, "DATA", 0, 0, ""); for (var l = o.s.r; l <= o.e.r; ++l) { t(a, -1, 0, "BOT"); for (var s = o.s.c; s <= o.e.c; ++s) { var c = Vn({ r: l, c: s }); if (r = i ? (n[l] || [])[s] : n[c]) switch (r.t) {
                                                case "n":
                                                    var d = r.w;
                                                    d || null == r.v || (d = r.v), null == d ? r.f && !r.F ? t(a, 1, 0, "=" + r.f) : t(a, 1, 0, "") : t(a, 0, d, "V"); break;
                                                case "b":
                                                    t(a, 0, r.v ? 1 : 0, r.v ? "TRUE" : "FALSE"); break;
                                                case "s":
                                                    t(a, 1, 0, isNaN(r.v) ? r.v : '="' + r.v + '"'); break;
                                                case "d":
                                                    r.w || (r.w = be(r.z || N[14], He(Fe(r.v)))), t(a, 0, r.w, "V"); break;
                                                default:
                                                    t(a, 1, 0, "") } else t(a, 1, 0, "") } } t(a, -1, 0, "EOD"); return a.join("\r\n") } }() } }(),
                    La = function() {
                        function e(e) { return e.replace(/\\b/g, "\\").replace(/\\c/g, ":").replace(/\\n/g, "\n") }

                        function t(e) { return e.replace(/\\/g, "\\b").replace(/:/g, "\\c").replace(/\n/g, "\\n") }

                        function n(t, n) { return Bn(function(t, n) { for (var r = t.split("\n"), a = -1, o = -1, i = 0, l = []; i !== r.length; ++i) { var s = r[i].trim().split(":"); if ("cell" === s[0]) { var c = jn(s[1]); if (l.length <= c.r)
                                            for (a = l.length; a <= c.r; ++a) l[a] || (l[a] = []); switch (a = c.r, o = c.c, s[2]) {
                                            case "t":
                                                l[a][o] = e(s[3]); break;
                                            case "v":
                                                l[a][o] = +s[3]; break;
                                            case "vtf":
                                                var d = s[s.length - 1];
                                            case "vtc":
                                                "nl" === s[3] ? l[a][o] = !!+s[4] : l[a][o] = +s[4], "vtf" == s[2] && (l[a][o] = [l[a][o], d]) } } } return n && n.sheetRows && (l = l.slice(0, n.sheetRows)), l }(t, n), n) } var r = ["socialcalc:version:1.5", "MIME-Version: 1.0", "Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),
                            a = ["--SocialCalcSpreadsheetControlSave", "Content-type: text/plain; charset=UTF-8"].join("\n") + "\n",
                            o = ["# SocialCalc Spreadsheet Control Save", "part:sheet"].join("\n"),
                            i = "--SocialCalcSpreadsheetControlSave--";

                        function l(e) { if (!e || !e["!ref"]) return ""; for (var n, r = [], a = [], o = "", i = On(e["!ref"]), l = Array.isArray(e), s = i.s.r; s <= i.e.r; ++s)
                                for (var c = i.s.c; c <= i.e.c; ++c)
                                    if (o = Vn({ r: s, c: c }), (n = l ? (e[s] || [])[c] : e[o]) && null != n.v && "z" !== n.t) { switch (a = ["cell", o, "t"], n.t) {
                                            case "s":
                                            case "str":
                                                a.push(t(n.v)); break;
                                            case "n":
                                                n.f ? (a[2] = "vtf", a[3] = "n", a[4] = n.v, a[5] = t(n.f)) : (a[2] = "v", a[3] = n.v); break;
                                            case "b":
                                                a[2] = "vt" + (n.f ? "f" : "c"), a[3] = "nl", a[4] = n.v ? "1" : "0", a[5] = t(n.f || (n.v ? "TRUE" : "FALSE")); break;
                                            case "d":
                                                var d = He(Fe(n.v));
                                                a[2] = "vtc", a[3] = "nd", a[4] = "" + d, a[5] = n.w || be(n.z || N[14], d); break;
                                            case "e":
                                                continue } r.push(a.join(":")) } return r.push("sheet:c:" + (i.e.c - i.s.c + 1) + ":r:" + (i.e.r - i.s.r + 1) + ":tvf:1"), r.push("valueformat:1:text-wiki"), r.join("\n") } return { to_workbook: function(e, t) { return Nn(n(e, t), t) }, to_sheet: n, from_sheet: function(e) { return [r, a, o, a, l(e), i].join("\n") } } }(),
                    Ia = function() {
                        function e(e, t, n, r, a) { a.raw ? t[n][r] = e : "" === e || ("TRUE" === e ? t[n][r] = !0 : "FALSE" === e ? t[n][r] = !1 : isNaN(We(e)) ? isNaN(qe(e).getDate()) ? t[n][r] = e : t[n][r] = Fe(e) : t[n][r] = We(e)) } var t = { 44: ",", 9: "\t", 59: ";", 124: "|" },
                            n = { 44: 3, 9: 2, 59: 1, 124: 0 };

                        function r(e) { for (var r = {}, a = !1, o = 0, i = 0; o < e.length; ++o) 34 == (i = e.charCodeAt(o)) ? a = !a : !a && i in t && (r[i] = (r[i] || 0) + 1); for (o in i = [], r) Object.prototype.hasOwnProperty.call(r, o) && i.push([r[o], o]); if (!i.length)
                                for (o in r = n) Object.prototype.hasOwnProperty.call(r, o) && i.push([r[o], o]); return i.sort((function(e, t) { return e[0] - t[0] || n[e[1]] - n[t[1]] })), t[i.pop()[1]] || 44 }

                        function a(e, t) { var n = t || {},
                                a = "";
                            null != g && null == n.dense && (n.dense = g); var o = n.dense ? [] : {},
                                i = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } }; "sep=" == e.slice(0, 4) ? 13 == e.charCodeAt(5) && 10 == e.charCodeAt(6) ? (a = e.charAt(4), e = e.slice(7)) : 13 == e.charCodeAt(5) || 10 == e.charCodeAt(5) ? (a = e.charAt(4), e = e.slice(6)) : a = r(e.slice(0, 1024)) : a = n && n.FS ? n.FS : r(e.slice(0, 1024)); var l = 0,
                                s = 0,
                                c = 0,
                                d = 0,
                                u = 0,
                                h = a.charCodeAt(0),
                                m = !1,
                                p = 0,
                                f = e.charCodeAt(0);
                            e = e.replace(/\r\n/gm, "\n"); var v = null != n.dateNF ? function(e) { var t = "number" == typeof e ? N[e] : e; return t = t.replace(Ae, "(\\d+)"), new RegExp("^" + t + "$") }(n.dateNF) : null;

                            function y() { var t = e.slice(d, u),
                                    r = {}; if ('"' == t.charAt(0) && '"' == t.charAt(t.length - 1) && (t = t.slice(1, -1).replace(/""/g, '"')), 0 === t.length) r.t = "z";
                                else if (n.raw) r.t = "s", r.v = t;
                                else if (0 === t.trim().length) r.t = "s", r.v = t;
                                else if (61 == t.charCodeAt(0)) 34 == t.charCodeAt(1) && 34 == t.charCodeAt(t.length - 1) ? (r.t = "s", r.v = t.slice(2, -1).replace(/""/g, '"')) : 1 != t.length ? (r.t = "n", r.f = t.slice(1)) : (r.t = "s", r.v = t);
                                else if ("TRUE" == t) r.t = "b", r.v = !0;
                                else if ("FALSE" == t) r.t = "b", r.v = !1;
                                else if (isNaN(c = We(t)))
                                    if (!isNaN(qe(t).getDate()) || v && t.match(v)) { r.z = n.dateNF || N[14]; var a = 0;
                                        v && t.match(v) && (t = function(e, t, n) { var r = -1,
                                                a = -1,
                                                o = -1,
                                                i = -1,
                                                l = -1,
                                                s = -1;
                                            (t.match(Ae) || []).forEach((function(e, t) { var c = parseInt(n[t + 1], 10); switch (e.toLowerCase().charAt(0)) {
                                                    case "y":
                                                        r = c; break;
                                                    case "d":
                                                        o = c; break;
                                                    case "h":
                                                        i = c; break;
                                                    case "s":
                                                        s = c; break;
                                                    case "m":
                                                        i >= 0 ? l = c : a = c } })), s >= 0 && -1 == l && a >= 0 && (l = a, a = -1); var c = ("" + (r >= 0 ? r : (new Date).getFullYear())).slice(-4) + "-" + ("00" + (a >= 1 ? a : 1)).slice(-2) + "-" + ("00" + (o >= 1 ? o : 1)).slice(-2);
                                            7 == c.length && (c = "0" + c), 8 == c.length && (c = "20" + c); var d = ("00" + (i >= 0 ? i : 0)).slice(-2) + ":" + ("00" + (l >= 0 ? l : 0)).slice(-2) + ":" + ("00" + (s >= 0 ? s : 0)).slice(-2); return -1 == i && -1 == l && -1 == s ? c : -1 == r && -1 == a && -1 == o ? d : c + "T" + d }(0, n.dateNF, t.match(v) || []), a = 1), n.cellDates ? (r.t = "d", r.v = Fe(t, a)) : (r.t = "n", r.v = He(Fe(t, a))), !1 !== n.cellText && (r.w = be(r.z, r.v instanceof Date ? He(r.v) : r.v)), n.cellNF || delete r.z } else r.t = "s", r.v = t;
                                else r.t = "n", !1 !== n.cellText && (r.w = t), r.v = c; if ("z" == r.t || (n.dense ? (o[l] || (o[l] = []), o[l][s] = r) : o[Vn({ c: s, r: l })] = r), d = u + 1, f = e.charCodeAt(d), i.e.c < s && (i.e.c = s), i.e.r < l && (i.e.r = l), p == h) ++s;
                                else if (s = 0, ++l, n.sheetRows && n.sheetRows <= l) return !0 } e: for (; u < e.length; ++u) switch (p = e.charCodeAt(u)) {
                                case 34:
                                    34 === f && (m = !m); break;
                                case h:
                                case 10:
                                case 13:
                                    if (!m && y()) break e }
                            return u - d > 0 && y(), o["!ref"] = Rn(i), o }

                        function o(t, n) { return n && n.PRN ? n.FS || "sep=" == t.slice(0, 4) || t.indexOf("\t") >= 0 || t.indexOf(",") >= 0 || t.indexOf(";") >= 0 ? a(t, n) : Bn(function(t, n) { var r = n || {},
                                    a = []; if (!t || 0 === t.length) return a; for (var o = t.split(/[\r\n]/), i = o.length - 1; i >= 0 && 0 === o[i].length;) --i; for (var l = 10, s = 0, c = 0; c <= i; ++c) - 1 == (s = o[c].indexOf(" ")) ? s = o[c].length : s++, l = Math.max(l, s); for (c = 0; c <= i; ++c) { a[c] = []; var d = 0; for (e(o[c].slice(0, l).trim(), a, c, d, r), d = 1; d <= (o[c].length - l) / 10 + 1; ++d) e(o[c].slice(l + 10 * (d - 1), l + 10 * d).trim(), a, c, d, r) } return r.sheetRows && (a = a.slice(0, r.sheetRows)), a }(t, n), n) : a(t, n) }

                        function i(e, t) { var n = "",
                                r = "string" == t.type ? [0, 0, 0, 0] : Cs(e, t); switch (t.type) {
                                case "base64":
                                    n = w(e); break;
                                case "binary":
                                case "string":
                                    n = e; break;
                                case "buffer":
                                    n = 65001 == t.codepage ? e.toString("utf8") : t.codepage && "undefined" !== typeof m ? m.utils.decode(t.codepage, e) : z && Buffer.isBuffer(e) ? e.toString("binary") : M(e); break;
                                case "array":
                                    n = Ne(e); break;
                                default:
                                    throw new Error("Unrecognized type " + t.type) } return 239 == r[0] && 187 == r[1] && 191 == r[2] ? n = kt(n.slice(3)) : "string" != t.type && "buffer" != t.type && 65001 == t.codepage ? n = kt(n) : "binary" == t.type && "undefined" !== typeof m && t.codepage && (n = m.utils.decode(t.codepage, m.utils.encode(28591, n))), "socialcalc:version:" == n.slice(0, 19) ? La.to_sheet("string" == t.type ? n : kt(n), t) : o(n, t) } return { to_workbook: function(e, t) { return Nn(i(e, t), t) }, to_sheet: i, from_sheet: function(e) { for (var t, n = [], r = Pn(e["!ref"]), a = Array.isArray(e), o = r.s.r; o <= r.e.r; ++o) { for (var i = [], l = r.s.c; l <= r.e.c; ++l) { var s = Vn({ r: o, c: l }); if ((t = a ? (e[o] || [])[l] : e[s]) && null != t.v) { for (var c = (t.w || (Fn(t), t.w) || "").slice(0, 10); c.length < 10;) c += " ";
                                            i.push(c + (0 === l ? " " : "")) } else i.push("          ") } n.push(i.join("")) } return n.join("\n") } } }(); var ja = function() {
                    function e(e, t, n) { if (e) { wn(e, e.l || 0); for (var r = n.Enum || b; e.l < e.length;) { var a = e.read_shift(2),
                                    o = r[a] || r[65535],
                                    i = e.read_shift(2),
                                    l = e.l + i,
                                    s = o.f && o.f(e, i, n); if (e.l = l, t(s, o, a)) return } } }

                    function t(t, n) { if (!t) return t; var r = n || {};
                        null != g && null == r.dense && (r.dense = g); var a = r.dense ? [] : {},
                            o = "Sheet1",
                            i = "",
                            l = 0,
                            s = {},
                            c = [],
                            d = [],
                            u = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } },
                            h = r.sheetRows || 0; if (0 == t[2] && (8 == t[3] || 9 == t[3]) && t.length >= 16 && 5 == t[14] && 108 === t[15]) throw new Error("Unsupported Works 3 for Mac file"); if (2 == t[2]) r.Enum = b, e(t, (function(e, t, n) { switch (n) {
                                case 0:
                                    r.vers = e, e >= 4096 && (r.qpro = !0); break;
                                case 6:
                                    u = e; break;
                                case 204:
                                    e && (i = e); break;
                                case 222:
                                    i = e; break;
                                case 15:
                                case 51:
                                    r.qpro || (e[1].v = e[1].v.slice(1));
                                case 13:
                                case 14:
                                case 16:
                                    14 == n && 112 == (112 & e[2]) && (15 & e[2]) > 1 && (15 & e[2]) < 15 && (e[1].z = r.dateNF || N[14], r.cellDates && (e[1].t = "d", e[1].v = Ve(e[1].v))), r.qpro && e[3] > l && (a["!ref"] = Rn(u), s[o] = a, c.push(o), a = r.dense ? [] : {}, u = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }, l = e[3], o = i || "Sheet" + (l + 1), i = ""); var d = r.dense ? (a[e[0].r] || [])[e[0].c] : a[Vn(e[0])]; if (d) { d.t = e[1].t, d.v = e[1].v, null != e[1].z && (d.z = e[1].z), null != e[1].f && (d.f = e[1].f); break } r.dense ? (a[e[0].r] || (a[e[0].r] = []), a[e[0].r][e[0].c] = e[1]) : a[Vn(e[0])] = e[1] } }), r);
                        else { if (26 != t[2] && 14 != t[2]) throw new Error("Unrecognized LOTUS BOF " + t[2]);
                            r.Enum = z, 14 == t[2] && (r.qpro = !0, t.l = 0), e(t, (function(e, t, n) { switch (n) {
                                    case 204:
                                        o = e; break;
                                    case 22:
                                        e[1].v = e[1].v.slice(1);
                                    case 23:
                                    case 24:
                                    case 25:
                                    case 37:
                                    case 39:
                                    case 40:
                                        if (e[3] > l && (a["!ref"] = Rn(u), s[o] = a, c.push(o), a = r.dense ? [] : {}, u = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }, l = e[3], o = "Sheet" + (l + 1)), h > 0 && e[0].r >= h) break;
                                        r.dense ? (a[e[0].r] || (a[e[0].r] = []), a[e[0].r][e[0].c] = e[1]) : a[Vn(e[0])] = e[1], u.e.c < e[0].c && (u.e.c = e[0].c), u.e.r < e[0].r && (u.e.r = e[0].r); break;
                                    case 27:
                                        e[14e3] && (d[e[14e3][0]] = e[14e3][1]); break;
                                    case 1537:
                                        d[e[0]] = e[1], e[0] == l && (o = e[1]) } }), r) } if (a["!ref"] = Rn(u), s[i || o] = a, c.push(i || o), !d.length) return { SheetNames: c, Sheets: s }; for (var m = {}, p = [], f = 0; f < d.length; ++f) s[c[f]] ? (p.push(d[f] || c[f]), m[d[f]] = s[d[f]] || s[c[f]]) : (p.push(d[f]), m[d[f]] = { "!ref": "A1" }); return { SheetNames: p, Sheets: m } }

                    function n(e, t, n) { var r = [{ c: 0, r: 0 }, { t: "n", v: 0 }, 0, 0]; return n.qpro && 20768 != n.vers ? (r[0].c = e.read_shift(1), r[3] = e.read_shift(1), r[0].r = e.read_shift(2), e.l += 2) : (r[2] = e.read_shift(1), r[0].c = e.read_shift(2), r[0].r = e.read_shift(2)), r }

                    function r(e, t, r) { var a = e.l + t,
                            o = n(e, 0, r); if (o[1].t = "s", 20768 == r.vers) { e.l++; var i = e.read_shift(1); return o[1].v = e.read_shift(i, "utf8"), o } return r.qpro && e.l++, o[1].v = e.read_shift(a - e.l, "cstr"), o }

                    function a(e, t, n) { var r = xn(7 + n.length);
                        r.write_shift(1, 255), r.write_shift(2, t), r.write_shift(2, e), r.write_shift(1, 39); for (var a = 0; a < r.length; ++a) { var o = n.charCodeAt(a);
                            r.write_shift(1, o >= 128 ? 95 : o) } return r.write_shift(1, 0), r }

                    function o(e, t, n) { var r = xn(7); return r.write_shift(1, 255), r.write_shift(2, t), r.write_shift(2, e), r.write_shift(2, n, "i"), r }

                    function i(e, t, n) { var r = xn(13); return r.write_shift(1, 255), r.write_shift(2, t), r.write_shift(2, e), r.write_shift(8, n, "f"), r }

                    function l(e, t, n) { var r = 32768 & t; return t = (r ? e : 0) + ((t &= -32769) >= 8192 ? t - 16384 : t), (r ? "" : "$") + (n ? In(t) : Hn(t)) } var s = { 51: ["FALSE", 0], 52: ["TRUE", 0], 70: ["LEN", 1], 80: ["SUM", 69], 81: ["AVERAGEA", 69], 82: ["COUNTA", 69], 83: ["MINA", 69], 84: ["MAXA", 69], 111: ["T", 1] },
                        d = ["", "", "", "", "", "", "", "", "", "+", "-", "*", "/", "^", "=", "<>", "<=", ">=", "<", ">", "", "", "", "", "&", "", "", "", "", "", "", ""];

                    function u(e) { var t = [{ c: 0, r: 0 }, { t: "n", v: 0 }, 0]; return t[0].r = e.read_shift(2), t[3] = e[e.l++], t[0].c = e[e.l++], t }

                    function h(e, t, n, r) { var a = xn(6 + r.length);
                        a.write_shift(2, e), a.write_shift(1, n), a.write_shift(1, t), a.write_shift(1, 39); for (var o = 0; o < r.length; ++o) { var i = r.charCodeAt(o);
                            a.write_shift(1, i >= 128 ? 95 : i) } return a.write_shift(1, 0), a }

                    function m(e, t) { var n = u(e),
                            r = e.read_shift(4),
                            a = e.read_shift(4),
                            o = e.read_shift(2); if (65535 == o) return 0 === r && 3221225472 === a ? (n[1].t = "e", n[1].v = 15) : 0 === r && 3489660928 === a ? (n[1].t = "e", n[1].v = 42) : n[1].v = 0, n; var i = 32768 & o; return o = (32767 & o) - 16446, n[1].v = (1 - 2 * i) * (a * Math.pow(2, o + 32) + r * Math.pow(2, o)), n }

                    function p(e, t, n, r) { var a = xn(14); if (a.write_shift(2, e), a.write_shift(1, n), a.write_shift(1, t), 0 == r) return a.write_shift(4, 0), a.write_shift(4, 0), a.write_shift(2, 65535), a; var o, i = 0,
                            l = 0,
                            s = 0; return r < 0 && (i = 1, r = -r), l = 0 | Math.log2(r), 0 == (2147483648 & (s = (r /= Math.pow(2, l - 31)) >>> 0)) && (++l, s = (r /= 2) >>> 0), r -= s, s |= 2147483648, s >>>= 0, o = (r *= Math.pow(2, 32)) >>> 0, a.write_shift(4, o), a.write_shift(4, s), l += 16383 + (i ? 32768 : 0), a.write_shift(2, l), a }

                    function f(e, t) { var n = u(e),
                            r = e.read_shift(8, "f"); return n[1].v = r, n }

                    function v(e, t) { return 0 == e[e.l + t - 1] ? e.read_shift(t, "cstr") : "" }

                    function y(e, t) { var n = xn(5 + e.length);
                        n.write_shift(2, 14e3), n.write_shift(2, t); for (var r = 0; r < e.length; ++r) { var a = e.charCodeAt(r);
                            n[n.l++] = a > 127 ? 95 : a } return n[n.l++] = 0, n } var b = { 0: { n: "BOF", f: qr }, 1: { n: "EOF" }, 2: { n: "CALCMODE" }, 3: { n: "CALCORDER" }, 4: { n: "SPLIT" }, 5: { n: "SYNC" }, 6: { n: "RANGE", f: function(e, t, n) { var r = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } }; return 8 == t && n.qpro ? (r.s.c = e.read_shift(1), e.l++, r.s.r = e.read_shift(2), r.e.c = e.read_shift(1), e.l++, r.e.r = e.read_shift(2), r) : (r.s.c = e.read_shift(2), r.s.r = e.read_shift(2), 12 == t && n.qpro && (e.l += 2), r.e.c = e.read_shift(2), r.e.r = e.read_shift(2), 12 == t && n.qpro && (e.l += 2), 65535 == r.s.c && (r.s.c = r.e.c = r.s.r = r.e.r = 0), r) } }, 7: { n: "WINDOW1" }, 8: { n: "COLW1" }, 9: { n: "WINTWO" }, 10: { n: "COLW2" }, 11: { n: "NAME" }, 12: { n: "BLANK" }, 13: { n: "INTEGER", f: function(e, t, r) { var a = n(e, 0, r); return a[1].v = e.read_shift(2, "i"), a } }, 14: { n: "NUMBER", f: function(e, t, r) { var a = n(e, 0, r); return a[1].v = e.read_shift(8, "f"), a } }, 15: { n: "LABEL", f: r }, 16: { n: "FORMULA", f: function(e, t, r) { var a = e.l + t,
                                        o = n(e, 0, r); if (o[1].v = e.read_shift(8, "f"), r.qpro) e.l = a;
                                    else { var i = e.read_shift(2);! function(e, t) { wn(e, 0); var n = [],
                                                r = 0,
                                                a = "",
                                                o = "",
                                                i = "",
                                                c = ""; for (; e.l < e.length;) { var u = e[e.l++]; switch (u) {
                                                    case 0:
                                                        n.push(e.read_shift(8, "f")); break;
                                                    case 1:
                                                        o = l(t[0].c, e.read_shift(2), !0), a = l(t[0].r, e.read_shift(2), !1), n.push(o + a); break;
                                                    case 2:
                                                        var h = l(t[0].c, e.read_shift(2), !0),
                                                            m = l(t[0].r, e.read_shift(2), !1);
                                                        o = l(t[0].c, e.read_shift(2), !0), a = l(t[0].r, e.read_shift(2), !1), n.push(h + m + ":" + o + a); break;
                                                    case 3:
                                                        if (e.l < e.length) return void console.error("WK1 premature formula end"); break;
                                                    case 4:
                                                        n.push("(" + n.pop() + ")"); break;
                                                    case 5:
                                                        n.push(e.read_shift(2)); break;
                                                    case 6:
                                                        for (var p = ""; u = e[e.l++];) p += String.fromCharCode(u);
                                                        n.push('"' + p.replace(/"/g, '""') + '"'); break;
                                                    case 8:
                                                        n.push("-" + n.pop()); break;
                                                    case 23:
                                                        n.push("+" + n.pop()); break;
                                                    case 22:
                                                        n.push("NOT(" + n.pop() + ")"); break;
                                                    case 20:
                                                    case 21:
                                                        c = n.pop(), i = n.pop(), n.push(["AND", "OR"][u - 20] + "(" + i + "," + c + ")"); break;
                                                    default:
                                                        if (u < 32 && d[u]) c = n.pop(), i = n.pop(), n.push(i + d[u] + c);
                                                        else { if (!s[u]) return u <= 7 ? console.error("WK1 invalid opcode " + u.toString(16)) : u <= 24 ? console.error("WK1 unsupported op " + u.toString(16)) : u <= 30 ? console.error("WK1 invalid opcode " + u.toString(16)) : u <= 115 ? console.error("WK1 unsupported function opcode " + u.toString(16)) : console.error("WK1 unrecognized opcode " + u.toString(16)); if (69 == (r = s[u][1]) && (r = e[e.l++]), r > n.length) return void console.error("WK1 bad formula parse 0x" + u.toString(16) + ":|" + n.join("|") + "|"); var f = n.slice(-r);
                                                            n.length -= r, n.push(s[u][0] + "(" + f.join(",") + ")") } } } 1 == n.length ? t[1].f = "" + n[0] : console.error("WK1 bad formula parse |" + n.join("|") + "|") }(e.slice(e.l, e.l + i), o), e.l += i } return o } }, 24: { n: "TABLE" }, 25: { n: "ORANGE" }, 26: { n: "PRANGE" }, 27: { n: "SRANGE" }, 28: { n: "FRANGE" }, 29: { n: "KRANGE1" }, 32: { n: "HRANGE" }, 35: { n: "KRANGE2" }, 36: { n: "PROTEC" }, 37: { n: "FOOTER" }, 38: { n: "HEADER" }, 39: { n: "SETUP" }, 40: { n: "MARGINS" }, 41: { n: "LABELFMT" }, 42: { n: "TITLES" }, 43: { n: "SHEETJS" }, 45: { n: "GRAPH" }, 46: { n: "NGRAPH" }, 47: { n: "CALCCOUNT" }, 48: { n: "UNFORMATTED" }, 49: { n: "CURSORW12" }, 50: { n: "WINDOW" }, 51: { n: "STRING", f: r }, 55: { n: "PASSWORD" }, 56: { n: "LOCKED" }, 60: { n: "QUERY" }, 61: { n: "QUERYNAME" }, 62: { n: "PRINT" }, 63: { n: "PRINTNAME" }, 64: { n: "GRAPH2" }, 65: { n: "GRAPHNAME" }, 66: { n: "ZOOM" }, 67: { n: "SYMSPLIT" }, 68: { n: "NSROWS" }, 69: { n: "NSCOLS" }, 70: { n: "RULER" }, 71: { n: "NNAME" }, 72: { n: "ACOMM" }, 73: { n: "AMACRO" }, 74: { n: "PARSE" }, 102: { n: "PRANGES??" }, 103: { n: "RRANGES??" }, 104: { n: "FNAME??" }, 105: { n: "MRANGES??" }, 204: { n: "SHEETNAMECS", f: v }, 222: { n: "SHEETNAMELP", f: function(e, t) { var n = e[e.l++];
                                    n > t - 1 && (n = t - 1); for (var r = ""; r.length < n;) r += String.fromCharCode(e[e.l++]); return r } }, 65535: { n: "" } },
                        z = { 0: { n: "BOF" }, 1: { n: "EOF" }, 2: { n: "PASSWORD" }, 3: { n: "CALCSET" }, 4: { n: "WINDOWSET" }, 5: { n: "SHEETCELLPTR" }, 6: { n: "SHEETLAYOUT" }, 7: { n: "COLUMNWIDTH" }, 8: { n: "HIDDENCOLUMN" }, 9: { n: "USERRANGE" }, 10: { n: "SYSTEMRANGE" }, 11: { n: "ZEROFORCE" }, 12: { n: "SORTKEYDIR" }, 13: { n: "FILESEAL" }, 14: { n: "DATAFILLNUMS" }, 15: { n: "PRINTMAIN" }, 16: { n: "PRINTSTRING" }, 17: { n: "GRAPHMAIN" }, 18: { n: "GRAPHSTRING" }, 19: { n: "??" }, 20: { n: "ERRCELL" }, 21: { n: "NACELL" }, 22: { n: "LABEL16", f: function(e, t) { var n = u(e); return n[1].t = "s", n[1].v = e.read_shift(t - 4, "cstr"), n } }, 23: { n: "NUMBER17", f: m }, 24: { n: "NUMBER18", f: function(e, t) { var n = u(e);
                                    n[1].v = e.read_shift(2); var r = n[1].v >> 1; if (1 & n[1].v) switch (7 & r) {
                                        case 0:
                                            r = 5e3 * (r >> 3); break;
                                        case 1:
                                            r = 500 * (r >> 3); break;
                                        case 2:
                                            r = (r >> 3) / 20; break;
                                        case 3:
                                            r = (r >> 3) / 200; break;
                                        case 4:
                                            r = (r >> 3) / 2e3; break;
                                        case 5:
                                            r = (r >> 3) / 2e4; break;
                                        case 6:
                                            r = (r >> 3) / 16; break;
                                        case 7:
                                            r = (r >> 3) / 64 }
                                    return n[1].v = r, n } }, 25: { n: "FORMULA19", f: function(e, t) { var n = m(e); return e.l += t - 14, n } }, 26: { n: "FORMULA1A" }, 27: { n: "XFORMAT", f: function(e, t) { for (var n = {}, r = e.l + t; e.l < r;) { var a = e.read_shift(2); if (14e3 == a) { for (n[a] = [0, ""], n[a][0] = e.read_shift(2); e[e.l];) n[a][1] += String.fromCharCode(e[e.l]), e.l++;
                                            e.l++ } } return n } }, 28: { n: "DTLABELMISC" }, 29: { n: "DTLABELCELL" }, 30: { n: "GRAPHWINDOW" }, 31: { n: "CPA" }, 32: { n: "LPLAUTO" }, 33: { n: "QUERY" }, 34: { n: "HIDDENSHEET" }, 35: { n: "??" }, 37: { n: "NUMBER25", f: function(e, t) { var n = u(e),
                                        r = e.read_shift(4); return n[1].v = r >> 6, n } }, 38: { n: "??" }, 39: { n: "NUMBER27", f: f }, 40: { n: "FORMULA28", f: function(e, t) { var n = f(e); return e.l += t - 10, n } }, 142: { n: "??" }, 147: { n: "??" }, 150: { n: "??" }, 151: { n: "??" }, 152: { n: "??" }, 153: { n: "??" }, 154: { n: "??" }, 155: { n: "??" }, 156: { n: "??" }, 163: { n: "??" }, 174: { n: "??" }, 175: { n: "??" }, 176: { n: "??" }, 177: { n: "??" }, 184: { n: "??" }, 185: { n: "??" }, 186: { n: "??" }, 187: { n: "??" }, 188: { n: "??" }, 195: { n: "??" }, 201: { n: "??" }, 204: { n: "SHEETNAMECS", f: v }, 205: { n: "??" }, 206: { n: "??" }, 207: { n: "??" }, 208: { n: "??" }, 256: { n: "??" }, 259: { n: "??" }, 260: { n: "??" }, 261: { n: "??" }, 262: { n: "??" }, 263: { n: "??" }, 265: { n: "??" }, 266: { n: "??" }, 267: { n: "??" }, 268: { n: "??" }, 270: { n: "??" }, 271: { n: "??" }, 384: { n: "??" }, 389: { n: "??" }, 390: { n: "??" }, 393: { n: "??" }, 396: { n: "??" }, 512: { n: "??" }, 514: { n: "??" }, 513: { n: "??" }, 516: { n: "??" }, 517: { n: "??" }, 640: { n: "??" }, 641: { n: "??" }, 642: { n: "??" }, 643: { n: "??" }, 644: { n: "??" }, 645: { n: "??" }, 646: { n: "??" }, 647: { n: "??" }, 648: { n: "??" }, 658: { n: "??" }, 659: { n: "??" }, 660: { n: "??" }, 661: { n: "??" }, 662: { n: "??" }, 665: { n: "??" }, 666: { n: "??" }, 768: { n: "??" }, 772: { n: "??" }, 1537: { n: "SHEETINFOQP", f: function(e, t, n) { if (n.qpro && !(t < 21)) { var r = e.read_shift(1); return e.l += 17, e.l += 1, e.l += 2, [r, e.read_shift(t - 21, "cstr")] } } }, 1600: { n: "??" }, 1602: { n: "??" }, 1793: { n: "??" }, 1794: { n: "??" }, 1795: { n: "??" }, 1796: { n: "??" }, 1920: { n: "??" }, 2048: { n: "??" }, 2049: { n: "??" }, 2052: { n: "??" }, 2688: { n: "??" }, 10998: { n: "??" }, 12849: { n: "??" }, 28233: { n: "??" }, 28484: { n: "??" }, 65535: { n: "" } }; return { sheet_to_wk1: function(e, t) { var n = t || {}; if (+n.codepage >= 0 && c(+n.codepage), "string" == n.type) throw new Error("Cannot write WK1 to JS string"); var r = kn(),
                                l = Pn(e["!ref"]),
                                s = Array.isArray(e),
                                d = [];
                            Bl(r, 0, function(e) { var t = xn(2); return t.write_shift(2, e), t }(1030)), Bl(r, 6, function(e) { var t = xn(8); return t.write_shift(2, e.s.c), t.write_shift(2, e.s.r), t.write_shift(2, e.e.c), t.write_shift(2, e.e.r), t }(l)); for (var u = Math.min(l.e.r, 8191), h = l.s.r; h <= u; ++h)
                                for (var m = Hn(h), p = l.s.c; p <= l.e.c; ++p) { h === l.s.r && (d[p] = In(p)); var f = d[p] + m,
                                        v = s ? (e[h] || [])[p] : e[f]; if (v && "z" != v.t)
                                        if ("n" == v.t)(0 | v.v) == v.v && v.v >= -32768 && v.v <= 32767 ? Bl(r, 13, o(h, p, v.v)) : Bl(r, 14, i(h, p, v.v));
                                        else Bl(r, 15, a(h, p, Fn(v).slice(0, 239))) }
                            return Bl(r, 1), r.end() }, book_to_wk3: function(e, t) { var n = t || {}; if (+n.codepage >= 0 && c(+n.codepage), "string" == n.type) throw new Error("Cannot write WK3 to JS string"); var r = kn();
                            Bl(r, 0, function(e) { var t = xn(26);
                                t.write_shift(2, 4096), t.write_shift(2, 4), t.write_shift(4, 0); for (var n = 0, r = 0, a = 0, o = 0; o < e.SheetNames.length; ++o) { var i = e.SheetNames[o],
                                        l = e.Sheets[i]; if (l && l["!ref"]) {++a; var s = On(l["!ref"]);
                                        n < s.e.r && (n = s.e.r), r < s.e.c && (r = s.e.c) } } n > 8191 && (n = 8191); return t.write_shift(2, n), t.write_shift(1, a), t.write_shift(1, r), t.write_shift(2, 0), t.write_shift(2, 0), t.write_shift(1, 1), t.write_shift(1, 2), t.write_shift(4, 0), t.write_shift(4, 0), t }(e)); for (var a = 0, o = 0; a < e.SheetNames.length; ++a)(e.Sheets[e.SheetNames[a]] || {})["!ref"] && Bl(r, 27, y(e.SheetNames[a], o++)); var i = 0; for (a = 0; a < e.SheetNames.length; ++a) { var l = e.Sheets[e.SheetNames[a]]; if (l && l["!ref"]) { for (var s = Pn(l["!ref"]), d = Array.isArray(l), u = [], m = Math.min(s.e.r, 8191), f = s.s.r; f <= m; ++f)
                                        for (var v = Hn(f), g = s.s.c; g <= s.e.c; ++g) { f === s.s.r && (u[g] = In(g)); var b = u[g] + v,
                                                w = d ? (l[f] || [])[g] : l[b]; if (w && "z" != w.t)
                                                if ("n" == w.t) Bl(r, 23, p(f, g, i, w.v));
                                                else Bl(r, 22, h(f, g, i, Fn(w).slice(0, 239))) }++i } } return Bl(r, 1), r.end() }, to_workbook: function(e, n) { switch (n.type) {
                                case "base64":
                                    return t(S(w(e)), n);
                                case "binary":
                                    return t(S(e), n);
                                case "buffer":
                                case "array":
                                    return t(e, n) } throw "Unsupported type " + n.type } } }(); var Va = function() { var e = Mt("t"),
                            t = Mt("rPr");

                        function n(n) { var r = n.match(e); if (!r) return { t: "s", v: "" }; var a = { t: "s", v: pt(r[1]) },
                                o = n.match(t); return o && (a.s = function(e) { var t = {},
                                    n = e.match(lt),
                                    r = 0,
                                    a = !1; if (n)
                                    for (; r != n.length; ++r) { var o = dt(n[r]); switch (o[0].replace(/\w*:/g, "")) {
                                            case "<condense":
                                            case "<extend":
                                                break;
                                            case "<shadow":
                                                if (!o.val) break;
                                            case "<shadow>":
                                            case "<shadow/>":
                                                t.shadow = 1; break;
                                            case "</shadow>":
                                                break;
                                            case "<charset":
                                                if ("1" == o.val) break;
                                                t.cp = l[parseInt(o.val, 10)]; break;
                                            case "<outline":
                                                if (!o.val) break;
                                            case "<outline>":
                                            case "<outline/>":
                                                t.outline = 1; break;
                                            case "</outline>":
                                                break;
                                            case "<rFont":
                                                t.name = o.val; break;
                                            case "<sz":
                                                t.sz = o.val; break;
                                            case "<strike":
                                                if (!o.val) break;
                                            case "<strike>":
                                            case "<strike/>":
                                                t.strike = 1; break;
                                            case "</strike>":
                                                break;
                                            case "<u":
                                                if (!o.val) break; switch (o.val) {
                                                    case "double":
                                                        t.uval = "double"; break;
                                                    case "singleAccounting":
                                                        t.uval = "single-accounting"; break;
                                                    case "doubleAccounting":
                                                        t.uval = "double-accounting" }
                                            case "<u>":
                                            case "<u/>":
                                                t.u = 1; break;
                                            case "</u>":
                                                break;
                                            case "<b":
                                                if ("0" == o.val) break;
                                            case "<b>":
                                            case "<b/>":
                                                t.b = 1; break;
                                            case "</b>":
                                                break;
                                            case "<i":
                                                if ("0" == o.val) break;
                                            case "<i>":
                                            case "<i/>":
                                                t.i = 1; break;
                                            case "</i>":
                                                break;
                                            case "<color":
                                                o.rgb && (t.color = o.rgb.slice(2, 8)); break;
                                            case "<color>":
                                            case "<color/>":
                                            case "</color>":
                                                break;
                                            case "<family":
                                                t.family = o.val; break;
                                            case "<family>":
                                            case "<family/>":
                                            case "</family>":
                                                break;
                                            case "<vertAlign":
                                                t.valign = o.val; break;
                                            case "<vertAlign>":
                                            case "<vertAlign/>":
                                            case "</vertAlign>":
                                            case "<scheme":
                                            case "<scheme>":
                                            case "<scheme/>":
                                            case "</scheme>":
                                            case "<extLst":
                                            case "<extLst>":
                                            case "</extLst>":
                                                break;
                                            case "<ext":
                                                a = !0; break;
                                            case "</ext>":
                                                a = !1; break;
                                            default:
                                                if (47 !== o[0].charCodeAt(1) && !a) throw new Error("Unrecognized rich format " + o[0]) } }
                                return t }(o[1])), a } var r = /<(?:\w+:)?r>/g,
                            a = /<\/(?:\w+:)?r>/; return function(e) { return e.replace(r, "").split(a).map(n).filter((function(e) { return e.v })) } }(),
                    Oa = function() { var e = /(\r\n|\n)/g;

                        function t(t) { var n = [
                                [], t.v, []
                            ]; return t.v ? (t.s && function(e, t, n) { var r = [];
                                e.u && r.push("text-decoration: underline;"), e.uval && r.push("text-underline-style:" + e.uval + ";"), e.sz && r.push("font-size:" + e.sz + "pt;"), e.outline && r.push("text-effect: outline;"), e.shadow && r.push("text-shadow: auto;"), t.push('<span style="' + r.join("") + '">'), e.b && (t.push("<b>"), n.push("</b>")), e.i && (t.push("<i>"), n.push("</i>")), e.strike && (t.push("<s>"), n.push("</s>")); var a = e.valign || ""; "superscript" == a || "super" == a ? a = "sup" : "subscript" == a && (a = "sub"), "" != a && (t.push("<" + a + ">"), n.push("</" + a + ">")), n.push("</span>") }(t.s, n[0], n[2]), n[0].join("") + n[1].replace(e, "<br/>") + n[2].join("")) : "" } return function(e) { return e.map(t).join("") } }(),
                    Ra = /<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,
                    Pa = /<(?:\w+:)?r>/,
                    Da = /<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;

                function Fa(e, t) { var n = !t || t.cellHTML,
                        r = {}; return e ? (e.match(/^\s*<(?:\w+:)?t[^>]*>/) ? (r.t = pt(kt(e.slice(e.indexOf(">") + 1).split(/<\/(?:\w+:)?t>/)[0] || "")), r.r = kt(e), n && (r.h = gt(r.t))) : e.match(Pa) && (r.r = kt(e), r.t = pt(kt((e.replace(Da, "").match(Ra) || []).join("").replace(lt, ""))), n && (r.h = Oa(Va(r.r)))), r) : { t: "" } } var Na = /<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,
                    _a = /<(?:\w+:)?(?:si|sstItem)>/g,
                    Ba = /<\/(?:\w+:)?(?:si|sstItem)>/;

                function Wa(e) { if ("undefined" !== typeof m) return m.utils.encode(o, e); for (var t = [], n = e.split(""), r = 0; r < n.length; ++r) t[r] = n[r].charCodeAt(0); return t }

                function Ua(e, t) { var n = {}; return n.Major = e.read_shift(2), n.Minor = e.read_shift(2), t >= 4 && (e.l += t - 4), n }

                function qa(e) { for (var t = e.read_shift(4), n = e.l + t - 4, r = {}, a = e.read_shift(4), o = []; a-- > 0;) o.push({ t: e.read_shift(4), v: e.read_shift(0, "lpp4") }); if (r.name = e.read_shift(0, "lpp4"), r.comps = o, e.l != n) throw new Error("Bad DataSpaceMapEntry: " + e.l + " != " + n); return r }

                function Ga(e) { var t = function(e) { var t = {}; return e.read_shift(4), e.l += 4, t.id = e.read_shift(0, "lpp4"), t.name = e.read_shift(0, "lpp4"), t.R = Ua(e, 4), t.U = Ua(e, 4), t.W = Ua(e, 4), t }(e); if (t.ename = e.read_shift(0, "8lpp4"), t.blksz = e.read_shift(4), t.cmode = e.read_shift(4), 4 != e.read_shift(4)) throw new Error("Bad !Primary record"); return t }

                function Ka(e, t) { var n = e.l + t,
                        r = {};
                    r.Flags = 63 & e.read_shift(4), e.l += 4, r.AlgID = e.read_shift(4); var a = !1; switch (r.AlgID) {
                        case 26126:
                        case 26127:
                        case 26128:
                            a = 36 == r.Flags; break;
                        case 26625:
                            a = 4 == r.Flags; break;
                        case 0:
                            a = 16 == r.Flags || 4 == r.Flags || 36 == r.Flags; break;
                        default:
                            throw "Unrecognized encryption algorithm: " + r.AlgID } if (!a) throw new Error("Encryption Flags/AlgID mismatch"); return r.AlgIDHash = e.read_shift(4), r.KeySize = e.read_shift(4), r.ProviderType = e.read_shift(4), e.l += 8, r.CSPName = e.read_shift(n - e.l >> 1, "utf16le"), e.l = n, r }

                function Za(e, t) { var n = {},
                        r = e.l + t; return e.l += 4, n.Salt = e.slice(e.l, e.l + 16), e.l += 16, n.Verifier = e.slice(e.l, e.l + 16), e.l += 16, e.read_shift(4), n.VerifierHash = e.slice(e.l, r), e.l = r, n }

                function Ya(e) { if (36 != (63 & e.read_shift(4))) throw new Error("EncryptionInfo mismatch"); var t = e.read_shift(4); return { t: "Std", h: Ka(e, t), v: Za(e, e.length - e.l) } }

                function Xa() { throw new Error("File is password-protected: ECMA-376 Extensible") }

                function $a(e) { var t = ["saltSize", "blockSize", "keyBits", "hashSize", "cipherAlgorithm", "cipherChaining", "hashAlgorithm", "saltValue"];
                    e.l += 4; var n = e.read_shift(e.length - e.l, "utf8"),
                        r = {}; return n.replace(lt, (function(e) { var n = dt(e); switch (ut(n[0])) {
                            case "<?xml":
                            case "<encryption":
                            case "</encryption>":
                            case "</keyEncryptors>":
                            case "</keyEncryptor>":
                                break;
                            case "<keyData":
                                t.forEach((function(e) { r[e] = n[e] })); break;
                            case "<dataIntegrity":
                                r.encryptedHmacKey = n.encryptedHmacKey, r.encryptedHmacValue = n.encryptedHmacValue; break;
                            case "<keyEncryptors>":
                            case "<keyEncryptors":
                                r.encs = []; break;
                            case "<keyEncryptor":
                                r.uri = n.uri; break;
                            case "<encryptedKey":
                                r.encs.push(n); break;
                            default:
                                throw n[0] } })), r }

                function Qa(e) { var t, n, r = 0,
                        a = Wa(e),
                        o = a.length + 1; for ((t = A(o))[0] = a.length, n = 1; n != o; ++n) t[n] = a[n - 1]; for (n = o - 1; n >= 0; --n) r = ((0 === (16384 & r) ? 0 : 1) | r << 1 & 32767) ^ t[n]; return 52811 ^ r } var Ja = function() { var e = [187, 255, 255, 186, 255, 255, 185, 128, 0, 190, 15, 0, 191, 15, 0],
                            t = [57840, 7439, 52380, 33984, 4364, 3600, 61902, 12606, 6258, 57657, 54287, 34041, 10252, 43370, 20163],
                            n = [44796, 19929, 39858, 10053, 20106, 40212, 10761, 31585, 63170, 64933, 60267, 50935, 40399, 11199, 17763, 35526, 1453, 2906, 5812, 11624, 23248, 885, 1770, 3540, 7080, 14160, 28320, 56640, 55369, 41139, 20807, 41614, 21821, 43642, 17621, 28485, 56970, 44341, 19019, 38038, 14605, 29210, 60195, 50791, 40175, 10751, 21502, 43004, 24537, 18387, 36774, 3949, 7898, 15796, 31592, 63184, 47201, 24803, 49606, 37805, 14203, 28406, 56812, 17824, 35648, 1697, 3394, 6788, 13576, 27152, 43601, 17539, 35078, 557, 1114, 2228, 4456, 30388, 60776, 51953, 34243, 7079, 14158, 28316, 14128, 28256, 56512, 43425, 17251, 34502, 7597, 13105, 26210, 52420, 35241, 883, 1766, 3532, 4129, 8258, 16516, 33032, 4657, 9314, 18628],
                            r = function(e, t) { return 255 & ((n = e ^ t) / 2 | 128 * n); var n }; return function(a) { for (var o, i, l, s = Wa(a), c = function(e) { for (var r = t[e.length - 1], a = 104, o = e.length - 1; o >= 0; --o)
                                        for (var i = e[o], l = 0; 7 != l; ++l) 64 & i && (r ^= n[a]), i *= 2, --a; return r }(s), d = s.length, u = A(16), h = 0; 16 != h; ++h) u[h] = 0; for (1 === (1 & d) && (o = c >> 8, u[d] = r(e[0], o), --d, o = 255 & c, i = s[s.length - 1], u[d] = r(i, o)); d > 0;) o = c >> 8, u[--d] = r(s[d], o), o = 255 & c, u[--d] = r(s[d], o); for (d = 15, l = 15 - s.length; l > 0;) o = c >> 8, u[d] = r(e[l], o), --l, o = 255 & c, u[--d] = r(s[d], o), --d, --l; return u } }(),
                    eo = function(e) { var t = 0,
                            n = Ja(e); return function(e) { var r = function(e, t, n, r, a) { var o, i; for (a || (a = t), r || (r = Ja(e)), o = 0; o != t.length; ++o) i = t[o], i = 255 & ((i ^= r[n]) >> 5 | i << 3), a[o] = i, ++n; return [a, n, r] }("", e, t, n); return t = r[1], r[0] } };

                function to(e, t, n) { var r = n || {}; return r.Info = e.read_shift(2), e.l -= 2, 1 === r.Info ? r.Data = function(e) { var t = {},
                            n = t.EncryptionVersionInfo = Ua(e, 4); if (1 != n.Major || 1 != n.Minor) throw "unrecognized version code " + n.Major + " : " + n.Minor; return t.Salt = e.read_shift(16), t.EncryptedVerifier = e.read_shift(16), t.EncryptedVerifierHash = e.read_shift(16), t }(e) : r.Data = function(e, t) { var n = {},
                            r = n.EncryptionVersionInfo = Ua(e, 4); if (t -= 4, 2 != r.Minor) throw new Error("unrecognized minor version code: " + r.Minor); if (r.Major > 4 || r.Major < 2) throw new Error("unrecognized major version code: " + r.Major);
                        n.Flags = e.read_shift(4), t -= 4; var a = e.read_shift(4); return t -= 4, n.EncryptionHeader = Ka(e, a), t -= a, n.EncryptionVerifier = Za(e, t), n }(e, t), r } var no = function() {
                    function e(e, n) { switch (n.type) {
                            case "base64":
                                return t(w(e), n);
                            case "binary":
                                return t(e, n);
                            case "buffer":
                                return t(z && Buffer.isBuffer(e) ? e.toString("binary") : M(e), n);
                            case "array":
                                return t(Ne(e), n) } throw new Error("Unrecognized type " + n.type) }

                    function t(e, t) { var n = (t || {}).dense ? [] : {},
                            r = e.match(/\\trowd.*?\\row\b/g); if (!r.length) throw new Error("RTF missing table"); var a = { s: { c: 0, r: 0 }, e: { c: 0, r: r.length - 1 } }; return r.forEach((function(e, t) { Array.isArray(n) && (n[t] = []); for (var r, o = /\\\w+\b/g, i = 0, l = -1; r = o.exec(e);) { if ("\\cell" === r[0]) { var s = e.slice(i, o.lastIndex - r[0].length); if (" " == s[0] && (s = s.slice(1)), ++l, s.length) { var c = { v: s, t: "s" };
                                        Array.isArray(n) ? n[t][l] = c : n[Vn({ r: t, c: l })] = c } } i = o.lastIndex } l > a.e.c && (a.e.c = l) })), n["!ref"] = Rn(a), n } return { to_workbook: function(t, n) { return Nn(e(t, n), n) }, to_sheet: e, from_sheet: function(e) { for (var t, n = ["{\\rtf1\\ansi"], r = Pn(e["!ref"]), a = Array.isArray(e), o = r.s.r; o <= r.e.r; ++o) { n.push("\\trowd\\trautofit1"); for (var i = r.s.c; i <= r.e.c; ++i) n.push("\\cellx" + (i + 1)); for (n.push("\\pard\\intbl"), i = r.s.c; i <= r.e.c; ++i) { var l = Vn({ r: o, c: i });
                                    (t = a ? (e[o] || [])[i] : e[l]) && (null != t.v || t.f && !t.F) && (n.push(" " + (t.w || (Fn(t), t.w))), n.push("\\cell")) } n.push("\\pard\\intbl\\row") } return n.join("") + "}" } } }();

                function ro(e) { for (var t = 0, n = 1; 3 != t; ++t) n = 256 * n + (e[t] > 255 ? 255 : e[t] < 0 ? 0 : e[t]); return n.toString(16).toUpperCase().slice(1) }

                function ao(e, t) { if (0 === t) return e; var n = function(e) { var t = e[0] / 255,
                            n = e[1] / 255,
                            r = e[2] / 255,
                            a = Math.max(t, n, r),
                            o = Math.min(t, n, r),
                            i = a - o; if (0 === i) return [0, 0, t]; var l, s = 0,
                            c = a + o; switch (l = i / (c > 1 ? 2 - c : c), a) {
                            case t:
                                s = ((n - r) / i + 6) % 6; break;
                            case n:
                                s = (r - t) / i + 2; break;
                            case r:
                                s = (t - n) / i + 4 } return [s / 6, l, c / 2] }(function(e) { var t = e.slice("#" === e[0] ? 1 : 0).slice(0, 6); return [parseInt(t.slice(0, 2), 16), parseInt(t.slice(2, 4), 16), parseInt(t.slice(4, 6), 16)] }(e)); return n[2] = t < 0 ? n[2] * (1 + t) : 1 - (1 - n[2]) * (1 - t), ro(function(e) { var t, n = e[0],
                            r = e[1],
                            a = e[2],
                            o = 2 * r * (a < .5 ? a : 1 - a),
                            i = a - o / 2,
                            l = [i, i, i],
                            s = 6 * n; if (0 !== r) switch (0 | s) {
                            case 0:
                            case 6:
                                t = o * s, l[0] += o, l[1] += t; break;
                            case 1:
                                t = o * (2 - s), l[0] += t, l[1] += o; break;
                            case 2:
                                t = o * (s - 2), l[1] += o, l[2] += t; break;
                            case 3:
                                t = o * (4 - s), l[1] += t, l[2] += o; break;
                            case 4:
                                t = o * (s - 4), l[2] += o, l[0] += t; break;
                            case 5:
                                t = o * (6 - s), l[2] += t, l[0] += o }
                        for (var c = 0; 3 != c; ++c) l[c] = Math.round(255 * l[c]); return l }(n)) } var oo = 6,
                    io = 15,
                    lo = 1,
                    so = oo;

                function co(e) { return Math.floor((e + Math.round(128 / so) / 256) * so) }

                function uo(e) { return Math.floor((e - 5) / so * 100 + .5) / 100 }

                function ho(e) { return Math.round((e * so + 5) / so * 256) / 256 }

                function mo(e) { return ho(uo(co(e))) }

                function po(e) { var t = Math.abs(e - mo(e)),
                        n = so; if (t > .005)
                        for (so = lo; so < io; ++so) Math.abs(e - mo(e)) <= t && (t = Math.abs(e - mo(e)), n = so);
                    so = n }

                function fo(e) { e.width ? (e.wpx = co(e.width), e.wch = uo(e.wpx), e.MDW = so) : e.wpx ? (e.wch = uo(e.wpx), e.width = ho(e.wch), e.MDW = so) : "number" == typeof e.wch && (e.width = ho(e.wch), e.wpx = co(e.width), e.MDW = so), e.customWidth && delete e.customWidth } var vo = 96;

                function go(e) { return 96 * e / vo }

                function yo(e) { return e * vo / 96 } var bo = { None: "none", Solid: "solid", Gray50: "mediumGray", Gray75: "darkGray", Gray25: "lightGray", HorzStripe: "darkHorizontal", VertStripe: "darkVertical", ReverseDiagStripe: "darkDown", DiagStripe: "darkUp", DiagCross: "darkGrid", ThickDiagCross: "darkTrellis", ThinHorzStripe: "lightHorizontal", ThinVertStripe: "lightVertical", ThinReverseDiagStripe: "lightDown", ThinHorzCross: "lightGrid" }; var wo = ["numFmtId", "fillId", "fontId", "borderId", "xfId"],
                    zo = ["applyAlignment", "applyBorder", "applyFill", "applyFont", "applyNumberFormat", "applyProtection", "pivotButton", "quotePrefix"]; var xo = function() { var e = /<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,
                        t = /<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,
                        n = /<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,
                        r = /<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,
                        a = /<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/; return function(o, i, s) { var c, d = {}; return o ? ((c = (o = o.replace(/<!--([\s\S]*?)-->/gm, "").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm, "")).match(e)) && function(e, t, n) { t.NumberFmt = []; for (var r = Ee(N), a = 0; a < r.length; ++a) t.NumberFmt[r[a]] = N[r[a]]; var o = e[0].match(lt); if (o)
                                for (a = 0; a < o.length; ++a) { var i = dt(o[a]); switch (ut(i[0])) {
                                        case "<numFmts":
                                        case "</numFmts>":
                                        case "<numFmts/>":
                                        case "<numFmts>":
                                        case "</numFmt>":
                                            break;
                                        case "<numFmt":
                                            var l = pt(kt(i.formatCode)),
                                                s = parseInt(i.numFmtId, 10); if (t.NumberFmt[s] = l, s > 0) { if (s > 392) { for (s = 392; s > 60 && null != t.NumberFmt[s]; --s);
                                                    t.NumberFmt[s] = l } we(l, s) } break;
                                        default:
                                            if (n.WTF) throw new Error("unrecognized " + i[0] + " in numFmts") } } }(c, d, s), (c = o.match(r)) && function(e, t, n, r) { t.Fonts = []; var a = {},
                                o = !1;
                            (e[0].match(lt) || []).forEach((function(e) { var i = dt(e); switch (ut(i[0])) {
                                    case "<fonts":
                                    case "<fonts>":
                                    case "</fonts>":
                                    case "<font":
                                    case "<font>":
                                    case "<name/>":
                                    case "</name>":
                                    case "<sz/>":
                                    case "</sz>":
                                    case "<vertAlign/>":
                                    case "</vertAlign>":
                                    case "<family/>":
                                    case "</family>":
                                    case "<scheme/>":
                                    case "</scheme>":
                                    case "<color/>":
                                    case "</color>":
                                    case "<extLst":
                                    case "<extLst>":
                                    case "</extLst>":
                                        break;
                                    case "</font>":
                                    case "<font/>":
                                        t.Fonts.push(a), a = {}; break;
                                    case "<name":
                                        i.val && (a.name = kt(i.val)); break;
                                    case "<b":
                                        a.bold = i.val ? bt(i.val) : 1; break;
                                    case "<b/>":
                                        a.bold = 1; break;
                                    case "<i":
                                        a.italic = i.val ? bt(i.val) : 1; break;
                                    case "<i/>":
                                        a.italic = 1; break;
                                    case "<u":
                                        switch (i.val) {
                                            case "none":
                                                a.underline = 0; break;
                                            case "single":
                                                a.underline = 1; break;
                                            case "double":
                                                a.underline = 2; break;
                                            case "singleAccounting":
                                                a.underline = 33; break;
                                            case "doubleAccounting":
                                                a.underline = 34 } break;
                                    case "<u/>":
                                        a.underline = 1; break;
                                    case "<strike":
                                        a.strike = i.val ? bt(i.val) : 1; break;
                                    case "<strike/>":
                                        a.strike = 1; break;
                                    case "<outline":
                                        a.outline = i.val ? bt(i.val) : 1; break;
                                    case "<outline/>":
                                        a.outline = 1; break;
                                    case "<shadow":
                                        a.shadow = i.val ? bt(i.val) : 1; break;
                                    case "<shadow/>":
                                        a.shadow = 1; break;
                                    case "<condense":
                                        a.condense = i.val ? bt(i.val) : 1; break;
                                    case "<condense/>":
                                        a.condense = 1; break;
                                    case "<extend":
                                        a.extend = i.val ? bt(i.val) : 1; break;
                                    case "<extend/>":
                                        a.extend = 1; break;
                                    case "<sz":
                                        i.val && (a.sz = +i.val); break;
                                    case "<vertAlign":
                                        i.val && (a.vertAlign = i.val); break;
                                    case "<family":
                                        i.val && (a.family = parseInt(i.val, 10)); break;
                                    case "<scheme":
                                        i.val && (a.scheme = i.val); break;
                                    case "<charset":
                                        if ("1" == i.val) break;
                                        i.codepage = l[parseInt(i.val, 10)]; break;
                                    case "<color":
                                        if (a.color || (a.color = {}), i.auto && (a.color.auto = bt(i.auto)), i.rgb) a.color.rgb = i.rgb.slice(-6);
                                        else if (i.indexed) { a.color.index = parseInt(i.indexed, 10); var s = fr[a.color.index];
                                            81 == a.color.index && (s = fr[1]), s || (s = fr[1]), a.color.rgb = s[0].toString(16) + s[1].toString(16) + s[2].toString(16) } else i.theme && (a.color.theme = parseInt(i.theme, 10), i.tint && (a.color.tint = parseFloat(i.tint)), i.theme && n.themeElements && n.themeElements.clrScheme && (a.color.rgb = ao(n.themeElements.clrScheme[a.color.theme].rgb, a.color.tint || 0))); break;
                                    case "<AlternateContent":
                                    case "<ext":
                                        o = !0; break;
                                    case "</AlternateContent>":
                                    case "</ext>":
                                        o = !1; break;
                                    default:
                                        if (r && r.WTF && !o) throw new Error("unrecognized " + i[0] + " in fonts") } })) }(c, d, i, s), (c = o.match(n)) && function(e, t, n, r) { t.Fills = []; var a = {},
                                o = !1;
                            (e[0].match(lt) || []).forEach((function(e) { var n = dt(e); switch (ut(n[0])) {
                                    case "<fills":
                                    case "<fills>":
                                    case "</fills>":
                                    case "</fill>":
                                    case "<gradientFill>":
                                    case "<patternFill/>":
                                    case "</patternFill>":
                                    case "<bgColor/>":
                                    case "</bgColor>":
                                    case "<fgColor/>":
                                    case "</fgColor>":
                                    case "<stop":
                                    case "<stop/>":
                                    case "</stop>":
                                    case "<color":
                                    case "<color/>":
                                    case "</color>":
                                    case "<extLst":
                                    case "<extLst>":
                                    case "</extLst>":
                                        break;
                                    case "<fill>":
                                    case "<fill":
                                    case "<fill/>":
                                        a = {}, t.Fills.push(a); break;
                                    case "<gradientFill":
                                    case "</gradientFill>":
                                        t.Fills.push(a), a = {}; break;
                                    case "<patternFill":
                                    case "<patternFill>":
                                        n.patternType && (a.patternType = n.patternType); break;
                                    case "<bgColor":
                                        a.bgColor || (a.bgColor = {}), n.indexed && (a.bgColor.indexed = parseInt(n.indexed, 10)), n.theme && (a.bgColor.theme = parseInt(n.theme, 10)), n.tint && (a.bgColor.tint = parseFloat(n.tint)), n.rgb && (a.bgColor.rgb = n.rgb.slice(-6)); break;
                                    case "<fgColor":
                                        a.fgColor || (a.fgColor = {}), n.theme && (a.fgColor.theme = parseInt(n.theme, 10)), n.tint && (a.fgColor.tint = parseFloat(n.tint)), null != n.rgb && (a.fgColor.rgb = n.rgb.slice(-6)); break;
                                    case "<ext":
                                        o = !0; break;
                                    case "</ext>":
                                        o = !1; break;
                                    default:
                                        if (r && r.WTF && !o) throw new Error("unrecognized " + n[0] + " in fills") } })) }(c, d, 0, s), (c = o.match(a)) && function(e, t, n, r) { t.Borders = []; var a = {},
                                o = !1;
                            (e[0].match(lt) || []).forEach((function(e) { var n = dt(e); switch (ut(n[0])) {
                                    case "<borders":
                                    case "<borders>":
                                    case "</borders>":
                                    case "</border>":
                                    case "<left/>":
                                    case "<left":
                                    case "<left>":
                                    case "</left>":
                                    case "<right/>":
                                    case "<right":
                                    case "<right>":
                                    case "</right>":
                                    case "<top/>":
                                    case "<top":
                                    case "<top>":
                                    case "</top>":
                                    case "<bottom/>":
                                    case "<bottom":
                                    case "<bottom>":
                                    case "</bottom>":
                                    case "<diagonal":
                                    case "<diagonal>":
                                    case "<diagonal/>":
                                    case "</diagonal>":
                                    case "<horizontal":
                                    case "<horizontal>":
                                    case "<horizontal/>":
                                    case "</horizontal>":
                                    case "<vertical":
                                    case "<vertical>":
                                    case "<vertical/>":
                                    case "</vertical>":
                                    case "<start":
                                    case "<start>":
                                    case "<start/>":
                                    case "</start>":
                                    case "<end":
                                    case "<end>":
                                    case "<end/>":
                                    case "</end>":
                                    case "<color":
                                    case "<color>":
                                    case "<color/>":
                                    case "</color>":
                                    case "<extLst":
                                    case "<extLst>":
                                    case "</extLst>":
                                        break;
                                    case "<border":
                                    case "<border>":
                                    case "<border/>":
                                        a = {}, n.diagonalUp && (a.diagonalUp = bt(n.diagonalUp)), n.diagonalDown && (a.diagonalDown = bt(n.diagonalDown)), t.Borders.push(a); break;
                                    case "<ext":
                                        o = !0; break;
                                    case "</ext>":
                                        o = !1; break;
                                    default:
                                        if (r && r.WTF && !o) throw new Error("unrecognized " + n[0] + " in borders") } })) }(c, d, 0, s), (c = o.match(t)) && function(e, t, n) { var r;
                            t.CellXf = []; var a = !1;
                            (e[0].match(lt) || []).forEach((function(e) { var o = dt(e),
                                    i = 0; switch (ut(o[0])) {
                                    case "<cellXfs":
                                    case "<cellXfs>":
                                    case "<cellXfs/>":
                                    case "</cellXfs>":
                                    case "</xf>":
                                    case "</alignment>":
                                    case "<protection":
                                    case "</protection>":
                                    case "<protection/>":
                                    case "<extLst":
                                    case "<extLst>":
                                    case "</extLst>":
                                        break;
                                    case "<xf":
                                    case "<xf/>":
                                        for (delete(r = o)[0], i = 0; i < wo.length; ++i) r[wo[i]] && (r[wo[i]] = parseInt(r[wo[i]], 10)); for (i = 0; i < zo.length; ++i) r[zo[i]] && (r[zo[i]] = bt(r[zo[i]])); if (t.NumberFmt && r.numFmtId > 392)
                                            for (i = 392; i > 60; --i)
                                                if (t.NumberFmt[r.numFmtId] == t.NumberFmt[i]) { r.numFmtId = i; break } t.CellXf.push(r); break;
                                    case "<alignment":
                                    case "<alignment/>":
                                        var l = {};
                                        o.vertical && (l.vertical = o.vertical), o.horizontal && (l.horizontal = o.horizontal), null != o.textRotation && (l.textRotation = o.textRotation), o.indent && (l.indent = o.indent), o.wrapText && (l.wrapText = bt(o.wrapText)), r.alignment = l; break;
                                    case "<AlternateContent":
                                    case "<ext":
                                        a = !0; break;
                                    case "</AlternateContent>":
                                    case "</ext>":
                                        a = !1; break;
                                    default:
                                        if (n && n.WTF && !a) throw new Error("unrecognized " + o[0] + " in cellXfs") } })) }(c, d, s), d) : d } }(); var Ao = zn; var ko = zn; var So = ["</a:lt1>", "</a:dk1>", "</a:lt2>", "</a:dk2>", "</a:accent1>", "</a:accent2>", "</a:accent3>", "</a:accent4>", "</a:accent5>", "</a:accent6>", "</a:hlink>", "</a:folHlink>"];

                function Mo(e, t, n) { t.themeElements.clrScheme = []; var r = {};
                    (e[0].match(lt) || []).forEach((function(e) { var a = dt(e); switch (a[0]) {
                            case "<a:clrScheme":
                            case "</a:clrScheme>":
                                break;
                            case "<a:srgbClr":
                                r.rgb = a.val; break;
                            case "<a:sysClr":
                                r.rgb = a.lastClr; break;
                            case "<a:dk1>":
                            case "</a:dk1>":
                            case "<a:lt1>":
                            case "</a:lt1>":
                            case "<a:dk2>":
                            case "</a:dk2>":
                            case "<a:lt2>":
                            case "</a:lt2>":
                            case "<a:accent1>":
                            case "</a:accent1>":
                            case "<a:accent2>":
                            case "</a:accent2>":
                            case "<a:accent3>":
                            case "</a:accent3>":
                            case "<a:accent4>":
                            case "</a:accent4>":
                            case "<a:accent5>":
                            case "</a:accent5>":
                            case "<a:accent6>":
                            case "</a:accent6>":
                            case "<a:hlink>":
                            case "</a:hlink>":
                            case "<a:folHlink>":
                            case "</a:folHlink>":
                                "/" === a[0].charAt(1) ? (t.themeElements.clrScheme[So.indexOf(a[0])] = r, r = {}) : r.name = a[0].slice(3, a[0].length - 1); break;
                            default:
                                if (n && n.WTF) throw new Error("Unrecognized " + a[0] + " in clrScheme") } })) }

                function Eo() {}

                function Co() {} var To = /<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,
                    Ho = /<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,
                    Lo = /<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/; var Io = /<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;

                function jo(e, t) { var n;
                    e && 0 !== e.length || (e = Vo()); var r = {}; if (!(n = e.match(Io))) throw new Error("themeElements not found in theme"); return function(e, t, n) { var r;
                        t.themeElements = {}, [
                            ["clrScheme", To, Mo],
                            ["fontScheme", Ho, Eo],
                            ["fmtScheme", Lo, Co]
                        ].forEach((function(a) { if (!(r = e.match(a[1]))) throw new Error(a[0] + " not found in themeElements");
                            a[2](r, t, n) })) }(n[0], r, t), r.raw = e, r }

