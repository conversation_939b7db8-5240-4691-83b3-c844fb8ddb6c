                    s = JSON.parse('{"L":{"flags":["af","ax","al","dz","as","ad","ao","ai","aq","ag","ar","am","aw","ac","au","at","az","bs","bh","bd","bb","pv","by","be","bz","bj","bm","bt","bo","bq","ba","bw","bv","br","io","bn","bg","bf","bi","cv","kh","cm","ca","ic","ct","ky","cf","cefta","td","cl","cn","cx","cp","cc","co","km","ck","cr","hr","cu","cw","cy","cz","ci","cd","dk","dg","dj","dm","do","eac","ec","eg","sv","eng","gq","er","ee","sz","et","eu","fk","fo","fm","fj","fi","fr","gf","pf","tf","ga","es-ga","gm","ge","de","gh","gi","gr","gl","gd","gp","gu","gt","gg","gn","gw","gy","ht","hm","va","hn","hk","hu","is","in","id","ir","iq","ie","im","il","it","jm","jp","je","jo","kz","ke","ki","xk","kw","kg","la","lv","arab","lb","ls","lr","ly","li","lt","lu","mo","mg","mw","my","mv","ml","mt","mh","mq","mr","mu","yt","mx","md","mc","mn","me","ms","ma","mz","mm","na","nr","np","nl","nc","nz","ni","ne","ng","nu","nf","kp","mk","nir","mp","no","om","pc","pk","pw","pa","pg","py","pe","ph","pn","pl","pt","pr","qa","cg","ro","ru","rw","re","bl","hl","sh","kn","lc","mf","pm","vc","ws","sm","st","sa","sct","sn","rs","sc","sl","sg","sx","sk","si","sb","so","za","gs","kr","ss","es","lk","ps","sd","sr","sj","se","ch","sy","tw","tj","tz","th","tl","tg","tk","to","tt","ta","tn","tm","tc","tv","tr","ug","ua","ae","gb","un","um","us","xx","uy","uz","vu","ve","vn","vg","vi","wls","wf","eh","ye","zm","zw"]},"P":{"af":{"class":"fi fi-af","path":"/custom-icons/country-flags/af.svg","label":"Afghanistan","category":"flags"},"ax":{"class":"fi fi-ax","path":"/custom-icons/country-flags/ax.svg","label":"Aland Islands","category":"flags"},"al":{"class":"fi fi-al","path":"/custom-icons/country-flags/al.svg","label":"Albania","category":"flags"},"dz":{"class":"fi fi-dz","path":"/custom-icons/country-flags/dz.svg","label":"Algeria","category":"flags"},"as":{"class":"fi fi-as","path":"/custom-icons/country-flags/as.svg","label":"American Samoa","category":"flags"},"ad":{"class":"fi fi-ad","path":"/custom-icons/country-flags/ad.svg","label":"Andorra","category":"flags"},"ao":{"class":"fi fi-ao","path":"/custom-icons/country-flags/ao.svg","label":"Angola","category":"flags"},"ai":{"class":"fi fi-ai","path":"/custom-icons/country-flags/ai.svg","label":"Anguilla","category":"flags"},"aq":{"class":"fi fi-aq","path":"/custom-icons/country-flags/aq.svg","label":"Antarctica","category":"flags"},"ag":{"class":"fi fi-ag","path":"/custom-icons/country-flags/ag.svg","label":"Antigua and Barbuda","category":"flags"},"ar":{"class":"fi fi-ar","path":"/custom-icons/country-flags/ar.svg","label":"Argentina","category":"flags"},"am":{"class":"fi fi-am","path":"/custom-icons/country-flags/am.svg","label":"Armenia","category":"flags"},"aw":{"class":"fi fi-aw","path":"/custom-icons/country-flags/aw.svg","label":"Aruba","category":"flags"},"ac":{"class":"fi fi-ac","path":"/custom-icons/country-flags/sh-ac.svg","label":"Ascension Island","category":"flags"},"au":{"class":"fi fi-au","path":"/custom-icons/country-flags/au.svg","label":"Australia","category":"flags"},"at":{"class":"fi fi-at","path":"/custom-icons/country-flags/at.svg","label":"Austria","category":"flags"},"az":{"class":"fi fi-az","path":"/custom-icons/country-flags/az.svg","label":"Azerbaijan","category":"flags"},"bs":{"class":"fi fi-bs","path":"/custom-icons/country-flags/bs.svg","label":"Bahamas","category":"flags"},"bh":{"class":"fi fi-bh","path":"/custom-icons/country-flags/bh.svg","label":"Bahrain","category":"flags"},"bd":{"class":"fi fi-bd","path":"/custom-icons/country-flags/bd.svg","label":"Bangladesh","category":"flags"},"bb":{"class":"fi fi-bb","path":"/custom-icons/country-flags/bb.svg","label":"Barbados","category":"flags"},"pv":{"class":"fi fi-pv","path":"/custom-icons/country-flags/es-pv.svg","label":"Basque Country","category":"flags"},"by":{"class":"fi fi-by","path":"/custom-icons/country-flags/by.svg","label":"Belarus","category":"flags"},"be":{"class":"fi fi-be","path":"/custom-icons/country-flags/be.svg","label":"Belgium","category":"flags"},"bz":{"class":"fi fi-bz","path":"/custom-icons/country-flags/bz.svg","label":"Belize","category":"flags"},"bj":{"class":"fi fi-bj","path":"/custom-icons/country-flags/bj.svg","label":"Benin","category":"flags"},"bm":{"class":"fi fi-bm","path":"/custom-icons/country-flags/bm.svg","label":"Bermuda","category":"flags"},"bt":{"class":"fi fi-bt","path":"/custom-icons/country-flags/bt.svg","label":"Bhutan","category":"flags"},"bo":{"class":"fi fi-bo","path":"/custom-icons/country-flags/bo.svg","label":"Bolivia","category":"flags"},"bq":{"class":"fi fi-bq","path":"/custom-icons/country-flags/bq.svg","label":"Bonaire, Sint Eustatius and Saba","category":"flags"},"ba":{"class":"fi fi-ba","path":"/custom-icons/country-flags/ba.svg","label":"Bosnia and Herzegovina","category":"flags"},"bw":{"class":"fi fi-bw","path":"/custom-icons/country-flags/bw.svg","label":"Botswana","category":"flags"},"bv":{"class":"fi fi-bv","path":"/custom-icons/country-flags/bv.svg","label":"Bouvet Island","category":"flags"},"br":{"class":"fi fi-br","path":"/custom-icons/country-flags/br.svg","label":"Brazil","category":"flags"},"io":{"class":"fi fi-io","path":"/custom-icons/country-flags/io.svg","label":"British Indian Ocean Territory","category":"flags"},"bn":{"class":"fi fi-bn","path":"/custom-icons/country-flags/bn.svg","label":"Brunei Darussalam","category":"flags"},"bg":{"class":"fi fi-bg","path":"/custom-icons/country-flags/bg.svg","label":"Bulgaria","category":"flags"},"bf":{"class":"fi fi-bf","path":"/custom-icons/country-flags/bf.svg","label":"Burkina Faso","category":"flags"},"bi":{"class":"fi fi-bi","path":"/custom-icons/country-flags/bi.svg","label":"Burundi","category":"flags"},"cv":{"class":"fi fi-cv","path":"/custom-icons/country-flags/cv.svg","label":"Cabo Verde","category":"flags"},"kh":{"class":"fi fi-kh","path":"/custom-icons/country-flags/kh.svg","label":"Cambodia","category":"flags"},"cm":{"class":"fi fi-cm","path":"/custom-icons/country-flags/cm.svg","label":"Cameroon","category":"flags"},"ca":{"class":"fi fi-ca","path":"/custom-icons/country-flags/ca.svg","label":"Canada","category":"flags"},"ic":{"class":"fi fi-ic","path":"/custom-icons/country-flags/ic.svg","label":"Canary Islands","category":"flags"},"ct":{"class":"fi fi-ct","path":"/custom-icons/country-flags/es-ct.svg","label":"Catalonia","category":"flags"},"ky":{"class":"fi fi-ky","path":"/custom-icons/country-flags/ky.svg","label":"Cayman Islands","category":"flags"},"cf":{"class":"fi fi-cf","path":"/custom-icons/country-flags/cf.svg","label":"Central African Republic","category":"flags"},"cefta":{"class":"fi fi-cefta","path":"/custom-icons/country-flags/cefta.svg","label":"Central European Free Trade Agreement","category":"flags"},"td":{"class":"fi fi-td","path":"/custom-icons/country-flags/td.svg","label":"Chad","category":"flags"},"cl":{"class":"fi fi-cl","path":"/custom-icons/country-flags/cl.svg","label":"Chile","category":"flags"},"cn":{"class":"fi fi-cn","path":"/custom-icons/country-flags/cn.svg","label":"China","category":"flags"},"cx":{"class":"fi fi-cx","path":"/custom-icons/country-flags/cx.svg","label":"Christmas Island","category":"flags"},"cp":{"class":"fi fi-cp","path":"/custom-icons/country-flags/cp.svg","label":"Clipperton Island","category":"flags"},"cc":{"class":"fi fi-cc","path":"/custom-icons/country-flags/cc.svg","label":"Cocos (Keeling) Islands","category":"flags"},"co":{"class":"fi fi-co","path":"/custom-icons/country-flags/co.svg","label":"Colombia","category":"flags"},"km":{"class":"fi fi-km","path":"/custom-icons/country-flags/km.svg","label":"Comoros","category":"flags"},"ck":{"class":"fi fi-ck","path":"/custom-icons/country-flags/ck.svg","label":"Cook Islands","category":"flags"},"cr":{"class":"fi fi-cr","path":"/custom-icons/country-flags/cr.svg","label":"Costa Rica","category":"flags"},"hr":{"class":"fi fi-hr","path":"/custom-icons/country-flags/hr.svg","label":"Croatia","category":"flags"},"cu":{"class":"fi fi-cu","path":"/custom-icons/country-flags/cu.svg","label":"Cuba","category":"flags"},"cw":{"class":"fi fi-cw","path":"/custom-icons/country-flags/cw.svg","label":"Cura\xe7ao","category":"flags"},"cy":{"class":"fi fi-cy","path":"/custom-icons/country-flags/cy.svg","label":"Cyprus","category":"flags"},"cz":{"class":"fi fi-cz","path":"/custom-icons/country-flags/cz.svg","label":"Czech Republic","category":"flags"},"ci":{"class":"fi fi-ci","path":"/custom-icons/country-flags/ci.svg","label":"C\xf4te d\'Ivoire","category":"flags"},"cd":{"class":"fi fi-cd","path":"/custom-icons/country-flags/cd.svg","label":"Democratic Republic of the Congo","category":"flags"},"dk":{"class":"fi fi-dk","path":"/custom-icons/country-flags/dk.svg","label":"Denmark","category":"flags"},"dg":{"class":"fi fi-dg","path":"/custom-icons/country-flags/dg.svg","label":"Diego Garcia","category":"flags"},"dj":{"class":"fi fi-dj","path":"/custom-icons/country-flags/dj.svg","label":"Djibouti","category":"flags"},"dm":{"class":"fi fi-dm","path":"/custom-icons/country-flags/dm.svg","label":"Dominica","category":"flags"},"do":{"class":"fi fi-do","path":"/custom-icons/country-flags/do.svg","label":"Dominican Republic","category":"flags"},"eac":{"class":"fi fi-eac","path":"/custom-icons/country-flags/eac.svg","label":"East African Community","category":"flags"},"ec":{"class":"fi fi-ec","path":"/custom-icons/country-flags/ec.svg","label":"Ecuador","category":"flags"},"eg":{"class":"fi fi-eg","path":"/custom-icons/country-flags/eg.svg","label":"Egypt","category":"flags"},"sv":{"class":"fi fi-sv","path":"/custom-icons/country-flags/sv.svg","label":"El Salvador","category":"flags"},"eng":{"class":"fi fi-eng","path":"/custom-icons/country-flags/gb-eng.svg","label":"England","category":"flags"},"gq":{"class":"fi fi-gq","path":"/custom-icons/country-flags/gq.svg","label":"Equatorial Guinea","category":"flags"},"er":{"class":"fi fi-er","path":"/custom-icons/country-flags/er.svg","label":"Eritrea","category":"flags"},"ee":{"class":"fi fi-ee","path":"/custom-icons/country-flags/ee.svg","label":"Estonia","category":"flags"},"sz":{"class":"fi fi-sz","path":"/custom-icons/country-flags/sz.svg","label":"Eswatini","category":"flags"},"et":{"class":"fi fi-et","path":"/custom-icons/country-flags/et.svg","label":"Ethiopia","category":"flags"},"eu":{"class":"fi fi-eu","path":"/custom-icons/country-flags/eu.svg","label":"Europe","category":"flags"},"fk":{"class":"fi fi-fk","path":"/custom-icons/country-flags/fk.svg","label":"Falkland Islands","category":"flags"},"fo":{"class":"fi fi-fo","path":"/custom-icons/country-flags/fo.svg","label":"Faroe Islands","category":"flags"},"fm":{"class":"fi fi-fm","path":"/custom-icons/country-flags/fm.svg","label":"Federated States of Micronesia","category":"flags"},"fj":{"class":"fi fi-fj","path":"/custom-icons/country-flags/fj.svg","label":"Fiji","category":"flags"},"fi":{"class":"fi fi-fi","path":"/custom-icons/country-flags/fi.svg","label":"Finland","category":"flags"},"fr":{"class":"fi fi-fr","path":"/custom-icons/country-flags/fr.svg","label":"France","category":"flags"},"gf":{"class":"fi fi-gf","path":"/custom-icons/country-flags/gf.svg","label":"French Guiana","category":"flags"},"pf":{"class":"fi fi-pf","path":"/custom-icons/country-flags/pf.svg","label":"French Polynesia","category":"flags"},"tf":{"class":"fi fi-tf","path":"/custom-icons/country-flags/tf.svg","label":"French Southern Territories","category":"flags"},"ga":{"class":"fi fi-ga","path":"/custom-icons/country-flags/ga.svg","label":"Gabon","category":"flags"},"es-ga":{"class":"fi fi-es-ga","path":"/custom-icons/country-flags/es-ga.svg","label":"Galicia","category":"flags"},"gm":{"class":"fi fi-gm","path":"/custom-icons/country-flags/gm.svg","label":"Gambia","category":"flags"},"ge":{"class":"fi fi-ge","path":"/custom-icons/country-flags/ge.svg","label":"Georgia","category":"flags"},"de":{"class":"fi fi-de","path":"/custom-icons/country-flags/de.svg","label":"Germany","category":"flags"},"gh":{"class":"fi fi-gh","path":"/custom-icons/country-flags/gh.svg","label":"Ghana","category":"flags"},"gi":{"class":"fi fi-gi","path":"/custom-icons/country-flags/gi.svg","label":"Gibraltar","category":"flags"},"gr":{"class":"fi fi-gr","path":"/custom-icons/country-flags/gr.svg","label":"Greece","category":"flags"},"gl":{"class":"fi fi-gl","path":"/custom-icons/country-flags/gl.svg","label":"Greenland","category":"flags"},"gd":{"class":"fi fi-gd","path":"/custom-icons/country-flags/gd.svg","label":"Grenada","category":"flags"},"gp":{"class":"fi fi-gp","path":"/custom-icons/country-flags/gp.svg","label":"Guadeloupe","category":"flags"},"gu":{"class":"fi fi-gu","path":"/custom-icons/country-flags/gu.svg","label":"Guam","category":"flags"},"gt":{"class":"fi fi-gt","path":"/custom-icons/country-flags/gt.svg","label":"Guatemala","category":"flags"},"gg":{"class":"fi fi-gg","path":"/custom-icons/country-flags/gg.svg","label":"Guernsey","category":"flags"},"gn":{"class":"fi fi-gn","path":"/custom-icons/country-flags/gn.svg","label":"Guinea","category":"flags"},"gw":{"class":"fi fi-gw","path":"/custom-icons/country-flags/gw.svg","label":"Guinea-Bissau","category":"flags"},"gy":{"class":"fi fi-gy","path":"/custom-icons/country-flags/gy.svg","label":"Guyana","category":"flags"},"ht":{"class":"fi fi-ht","path":"/custom-icons/country-flags/ht.svg","label":"Haiti","category":"flags"},"hm":{"class":"fi fi-hm","path":"/custom-icons/country-flags/hm.svg","label":"Heard Island and McDonald Islands","category":"flags"},"va":{"class":"fi fi-va","path":"/custom-icons/country-flags/va.svg","label":"Holy See","category":"flags"},"hn":{"class":"fi fi-hn","path":"/custom-icons/country-flags/hn.svg","label":"Honduras","category":"flags"},"hk":{"class":"fi fi-hk","path":"/custom-icons/country-flags/hk.svg","label":"Hong Kong","category":"flags"},"hu":{"class":"fi fi-hu","path":"/custom-icons/country-flags/hu.svg","label":"Hungary","category":"flags"},"is":{"class":"fi fi-is","path":"/custom-icons/country-flags/is.svg","label":"Iceland","category":"flags"},"in":{"class":"fi fi-in","path":"/custom-icons/country-flags/in.svg","label":"India","category":"flags"},"id":{"class":"fi fi-id","path":"/custom-icons/country-flags/id.svg","label":"Indonesia","category":"flags"},"ir":{"class":"fi fi-ir","path":"/custom-icons/country-flags/ir.svg","label":"Iran","category":"flags"},"iq":{"class":"fi fi-iq","path":"/custom-icons/country-flags/iq.svg","label":"Iraq","category":"flags"},"ie":{"class":"fi fi-ie","path":"/custom-icons/country-flags/ie.svg","label":"Ireland","category":"flags"},"im":{"class":"fi fi-im","path":"/custom-icons/country-flags/im.svg","label":"Isle of Man","category":"flags"},"il":{"class":"fi fi-il","path":"/custom-icons/country-flags/il.svg","label":"Israel","category":"flags"},"it":{"class":"fi fi-it","path":"/custom-icons/country-flags/it.svg","label":"Italy","category":"flags"},"jm":{"class":"fi fi-jm","path":"/custom-icons/country-flags/jm.svg","label":"Jamaica","category":"flags"},"jp":{"class":"fi fi-jp","path":"/custom-icons/country-flags/jp.svg","label":"Japan","category":"flags"},"je":{"class":"fi fi-je","path":"/custom-icons/country-flags/je.svg","label":"Jersey","category":"flags"},"jo":{"class":"fi fi-jo","path":"/custom-icons/country-flags/jo.svg","label":"Jordan","category":"flags"},"kz":{"class":"fi fi-kz","path":"/custom-icons/country-flags/kz.svg","label":"Kazakhstan","category":"flags"},"ke":{"class":"fi fi-ke","path":"/custom-icons/country-flags/ke.svg","label":"Kenya","category":"flags"},"ki":{"class":"fi fi-ki","path":"/custom-icons/country-flags/ki.svg","label":"Kiribati","category":"flags"},"xk":{"class":"fi fi-xk","path":"/custom-icons/country-flags/xk.svg","label":"Kosovo","category":"flags"},"kw":{"class":"fi fi-kw","path":"/custom-icons/country-flags/kw.svg","label":"Kuwait","category":"flags"},"kg":{"class":"fi fi-kg","path":"/custom-icons/country-flags/kg.svg","label":"Kyrgyzstan","category":"flags"},"la":{"class":"fi fi-la","path":"/custom-icons/country-flags/la.svg","label":"Laos","category":"flags"},"lv":{"class":"fi fi-lv","path":"/custom-icons/country-flags/lv.svg","label":"Latvia","category":"flags"},"arab":{"class":"fi fi-arab","path":"/custom-icons/country-flags/arab.svg","label":"League of Arab States","category":"flags"},"lb":{"class":"fi fi-lb","path":"/custom-icons/country-flags/lb.svg","label":"Lebanon","category":"flags"},"ls":{"class":"fi fi-ls","path":"/custom-icons/country-flags/ls.svg","label":"Lesotho","category":"flags"},"lr":{"class":"fi fi-lr","path":"/custom-icons/country-flags/lr.svg","label":"Liberia","category":"flags"},"ly":{"class":"fi fi-ly","path":"/custom-icons/country-flags/ly.svg","label":"Libya","category":"flags"},"li":{"class":"fi fi-li","path":"/custom-icons/country-flags/li.svg","label":"Liechtenstein","category":"flags"},"lt":{"class":"fi fi-lt","path":"/custom-icons/country-flags/lt.svg","label":"Lithuania","category":"flags"},"lu":{"class":"fi fi-lu","path":"/custom-icons/country-flags/lu.svg","label":"Luxembourg","category":"flags"},"mo":{"class":"fi fi-mo","path":"/custom-icons/country-flags/mo.svg","label":"Macau","category":"flags"},"mg":{"class":"fi fi-mg","path":"/custom-icons/country-flags/mg.svg","label":"Madagascar","category":"flags"},"mw":{"class":"fi fi-mw","path":"/custom-icons/country-flags/mw.svg","label":"Malawi","category":"flags"},"my":{"class":"fi fi-my","path":"/custom-icons/country-flags/my.svg","label":"Malaysia","category":"flags"},"mv":{"class":"fi fi-mv","path":"/custom-icons/country-flags/mv.svg","label":"Maldives","category":"flags"},"ml":{"class":"fi fi-ml","path":"/custom-icons/country-flags/ml.svg","label":"Mali","category":"flags"},"mt":{"class":"fi fi-mt","path":"/custom-icons/country-flags/mt.svg","label":"Malta","category":"flags"},"mh":{"class":"fi fi-mh","path":"/custom-icons/country-flags/mh.svg","label":"Marshall Islands","category":"flags"},"mq":{"class":"fi fi-mq","path":"/custom-icons/country-flags/mq.svg","label":"Martinique","category":"flags"},"mr":{"class":"fi fi-mr","path":"/custom-icons/country-flags/mr.svg","label":"Mauritania","category":"flags"},"mu":{"class":"fi fi-mu","path":"/custom-icons/country-flags/mu.svg","label":"Mauritius","category":"flags"},"yt":{"class":"fi fi-yt","path":"/custom-icons/country-flags/yt.svg","label":"Mayotte","category":"flags"},"mx":{"class":"fi fi-mx","path":"/custom-icons/country-flags/mx.svg","label":"Mexico","category":"flags"},"md":{"class":"fi fi-md","path":"/custom-icons/country-flags/md.svg","label":"Moldova","category":"flags"},"mc":{"class":"fi fi-mc","path":"/custom-icons/country-flags/mc.svg","label":"Monaco","category":"flags"},"mn":{"class":"fi fi-mn","path":"/custom-icons/country-flags/mn.svg","label":"Mongolia","category":"flags"},"me":{"class":"fi fi-me","path":"/custom-icons/country-flags/me.svg","label":"Montenegro","category":"flags"},"ms":{"class":"fi fi-ms","path":"/custom-icons/country-flags/ms.svg","label":"Montserrat","category":"flags"},"ma":{"class":"fi fi-ma","path":"/custom-icons/country-flags/ma.svg","label":"Morocco","category":"flags"},"mz":{"class":"fi fi-mz","path":"/custom-icons/country-flags/mz.svg","label":"Mozambique","category":"flags"},"mm":{"class":"fi fi-mm","path":"/custom-icons/country-flags/mm.svg","label":"Myanmar","category":"flags"},"na":{"class":"fi fi-na","path":"/custom-icons/country-flags/na.svg","label":"Namibia","category":"flags"},"nr":{"class":"fi fi-nr","path":"/custom-icons/country-flags/nr.svg","label":"Nauru","category":"flags"},"np":{"class":"fi fi-np","path":"/custom-icons/country-flags/np.svg","label":"Nepal","category":"flags"},"nl":{"class":"fi fi-nl","path":"/custom-icons/country-flags/nl.svg","label":"Netherlands","category":"flags"},"nc":{"class":"fi fi-nc","path":"/custom-icons/country-flags/nc.svg","label":"New Caledonia","category":"flags"},"nz":{"class":"fi fi-nz","path":"/custom-icons/country-flags/nz.svg","label":"New Zealand","category":"flags"},"ni":{"class":"fi fi-ni","path":"/custom-icons/country-flags/ni.svg","label":"Nicaragua","category":"flags"},"ne":{"class":"fi fi-ne","path":"/custom-icons/country-flags/ne.svg","label":"Niger","category":"flags"},"ng":{"class":"fi fi-ng","path":"/custom-icons/country-flags/ng.svg","label":"Nigeria","category":"flags"},"nu":{"class":"fi fi-nu","path":"/custom-icons/country-flags/nu.svg","label":"Niue","category":"flags"},"nf":{"class":"fi fi-nf","path":"/custom-icons/country-flags/nf.svg","label":"Norfolk Island","category":"flags"},"kp":{"class":"fi fi-kp","path":"/custom-icons/country-flags/kp.svg","label":"North Korea","category":"flags"},"mk":{"class":"fi fi-mk","path":"/custom-icons/country-flags/mk.svg","label":"North Macedonia","category":"flags"},"nir":{"class":"fi fi-nir","path":"/custom-icons/country-flags/gb-nir.svg","label":"Northern Ireland","category":"flags"},"mp":{"class":"fi fi-mp","path":"/custom-icons/country-flags/mp.svg","label":"Northern Mariana Islands","category":"flags"},"no":{"class":"fi fi-no","path":"/custom-icons/country-flags/no.svg","label":"Norway","category":"flags"},"om":{"class":"fi fi-om","path":"/custom-icons/country-flags/om.svg","label":"Oman","category":"flags"},"pc":{"class":"fi fi-pc","path":"/custom-icons/country-flags/pc.svg","label":"Pacific Community","category":"flags"},"pk":{"class":"fi fi-pk","path":"/custom-icons/country-flags/pk.svg","label":"Pakistan","category":"flags"},"pw":{"class":"fi fi-pw","path":"/custom-icons/country-flags/pw.svg","label":"Palau","category":"flags"},"pa":{"class":"fi fi-pa","path":"/custom-icons/country-flags/pa.svg","label":"Panama","category":"flags"},"pg":{"class":"fi fi-pg","path":"/custom-icons/country-flags/pg.svg","label":"Papua New Guinea","category":"flags"},"py":{"class":"fi fi-py","path":"/custom-icons/country-flags/py.svg","label":"Paraguay","category":"flags"},"pe":{"class":"fi fi-pe","path":"/custom-icons/country-flags/pe.svg","label":"Peru","category":"flags"},"ph":{"class":"fi fi-ph","path":"/custom-icons/country-flags/ph.svg","label":"Philippines","category":"flags"},"pn":{"class":"fi fi-pn","path":"/custom-icons/country-flags/pn.svg","label":"Pitcairn","category":"flags"},"pl":{"class":"fi fi-pl","path":"/custom-icons/country-flags/pl.svg","label":"Poland","category":"flags"},"pt":{"class":"fi fi-pt","path":"/custom-icons/country-flags/pt.svg","label":"Portugal","category":"flags"},"pr":{"class":"fi fi-pr","path":"/custom-icons/country-flags/pr.svg","label":"Puerto Rico","category":"flags"},"qa":{"class":"fi fi-qa","path":"/custom-icons/country-flags/qa.svg","label":"Qatar","category":"flags"},"cg":{"class":"fi fi-cg","path":"/custom-icons/country-flags/cg.svg","label":"Republic of the Congo","category":"flags"},"ro":{"class":"fi fi-ro","path":"/custom-icons/country-flags/ro.svg","label":"Romania","category":"flags"},"ru":{"class":"fi fi-ru","path":"/custom-icons/country-flags/ru.svg","label":"Russia","category":"flags"},"rw":{"class":"fi fi-rw","path":"/custom-icons/country-flags/rw.svg","label":"Rwanda","category":"flags"},"re":{"class":"fi fi-re","path":"/custom-icons/country-flags/re.svg","label":"R\xe9union","category":"flags"},"bl":{"class":"fi fi-bl","path":"/custom-icons/country-flags/bl.svg","label":"Saint Barth\xe9lemy","category":"flags"},"hl":{"class":"fi fi-hl","path":"/custom-icons/country-flags/sh-hl.svg","label":"Saint Helena","category":"flags"},"sh":{"class":"fi fi-sh","path":"/custom-icons/country-flags/sh.svg","label":"Saint Helena, Ascension and Tristan da Cunha","category":"flags"},"kn":{"class":"fi fi-kn","path":"/custom-icons/country-flags/kn.svg","label":"Saint Kitts and Nevis","category":"flags"},"lc":{"class":"fi fi-lc","path":"/custom-icons/country-flags/lc.svg","label":"Saint Lucia","category":"flags"},"mf":{"class":"fi fi-mf","path":"/custom-icons/country-flags/mf.svg","label":"Saint Martin","category":"flags"},"pm":{"class":"fi fi-pm","path":"/custom-icons/country-flags/pm.svg","label":"Saint Pierre and Miquelon","category":"flags"},"vc":{"class":"fi fi-vc","path":"/custom-icons/country-flags/vc.svg","label":"Saint Vincent and the Grenadines","category":"flags"},"ws":{"class":"fi fi-ws","path":"/custom-icons/country-flags/ws.svg","label":"Samoa","category":"flags"},"sm":{"class":"fi fi-sm","path":"/custom-icons/country-flags/sm.svg","label":"San Marino","category":"flags"},"st":{"class":"fi fi-st","path":"/custom-icons/country-flags/st.svg","label":"Sao Tome and Principe","category":"flags"},"sa":{"class":"fi fi-sa","path":"/custom-icons/country-flags/sa.svg","label":"Saudi Arabia","category":"flags"},"sct":{"class":"fi fi-sct","path":"/custom-icons/country-flags/gb-sct.svg","label":"Scotland","category":"flags"},"sn":{"class":"fi fi-sn","path":"/custom-icons/country-flags/sn.svg","label":"Senegal","category":"flags"},"rs":{"class":"fi fi-rs","path":"/custom-icons/country-flags/rs.svg","label":"Serbia","category":"flags"},"sc":{"class":"fi fi-sc","path":"/custom-icons/country-flags/sc.svg","label":"Seychelles","category":"flags"},"sl":{"class":"fi fi-sl","path":"/custom-icons/country-flags/sl.svg","label":"Sierra Leone","category":"flags"},"sg":{"class":"fi fi-sg","path":"/custom-icons/country-flags/sg.svg","label":"Singapore","category":"flags"},"sx":{"class":"fi fi-sx","path":"/custom-icons/country-flags/sx.svg","label":"Sint Maarten","category":"flags"},"sk":{"class":"fi fi-sk","path":"/custom-icons/country-flags/sk.svg","label":"Slovakia","category":"flags"},"si":{"class":"fi fi-si","path":"/custom-icons/country-flags/si.svg","label":"Slovenia","category":"flags"},"sb":{"class":"fi fi-sb","path":"/custom-icons/country-flags/sb.svg","label":"Solomon Islands","category":"flags"},"so":{"class":"fi fi-so","path":"/custom-icons/country-flags/so.svg","label":"Somalia","category":"flags"},"za":{"class":"fi fi-za","path":"/custom-icons/country-flags/za.svg","label":"South Africa","category":"flags"},"gs":{"class":"fi fi-gs","path":"/custom-icons/country-flags/gs.svg","label":"South Georgia and the South Sandwich Islands","category":"flags"},"kr":{"class":"fi fi-kr","path":"/custom-icons/country-flags/kr.svg","label":"South Korea","category":"flags"},"ss":{"class":"fi fi-ss","path":"/custom-icons/country-flags/ss.svg","label":"South Sudan","category":"flags"},"es":{"class":"fi fi-es","path":"/custom-icons/country-flags/es.svg","label":"Spain","category":"flags"},"lk":{"class":"fi fi-lk","path":"/custom-icons/country-flags/lk.svg","label":"Sri Lanka","category":"flags"},"ps":{"class":"fi fi-ps","path":"/custom-icons/country-flags/ps.svg","label":"State of Palestine","category":"flags"},"sd":{"class":"fi fi-sd","path":"/custom-icons/country-flags/sd.svg","label":"Sudan","category":"flags"},"sr":{"class":"fi fi-sr","path":"/custom-icons/country-flags/sr.svg","label":"Suriname","category":"flags"},"sj":{"class":"fi fi-sj","path":"/custom-icons/country-flags/sj.svg","label":"Svalbard and Jan Mayen","category":"flags"},"se":{"class":"fi fi-se","path":"/custom-icons/country-flags/se.svg","label":"Sweden","category":"flags"},"ch":{"class":"fi fi-ch","path":"/custom-icons/country-flags/ch.svg","label":"Switzerland","category":"flags"},"sy":{"class":"fi fi-sy","path":"/custom-icons/country-flags/sy.svg","label":"Syria","category":"flags"},"tw":{"class":"fi fi-tw","path":"/custom-icons/country-flags/tw.svg","label":"Taiwan","category":"flags"},"tj":{"class":"fi fi-tj","path":"/custom-icons/country-flags/tj.svg","label":"Tajikistan","category":"flags"},"tz":{"class":"fi fi-tz","path":"/custom-icons/country-flags/tz.svg","label":"Tanzania","category":"flags"},"th":{"class":"fi fi-th","path":"/custom-icons/country-flags/th.svg","label":"Thailand","category":"flags"},"tl":{"class":"fi fi-tl","path":"/custom-icons/country-flags/tl.svg","label":"Timor-Leste","category":"flags"},"tg":{"class":"fi fi-tg","path":"/custom-icons/country-flags/tg.svg","label":"Togo","category":"flags"},"tk":{"class":"fi fi-tk","path":"/custom-icons/country-flags/tk.svg","label":"Tokelau","category":"flags"},"to":{"class":"fi fi-to","path":"/custom-icons/country-flags/to.svg","label":"Tonga","category":"flags"},"tt":{"class":"fi fi-tt","path":"/custom-icons/country-flags/tt.svg","label":"Trinidad and Tobago","category":"flags"},"ta":{"class":"fi fi-ta","path":"/custom-icons/country-flags/sh-ta.svg","label":"Tristan da Cunha","category":"flags"},"tn":{"class":"fi fi-tn","path":"/custom-icons/country-flags/tn.svg","label":"Tunisia","category":"flags"},"tm":{"class":"fi fi-tm","path":"/custom-icons/country-flags/tm.svg","label":"Turkmenistan","category":"flags"},"tc":{"class":"fi fi-tc","path":"/custom-icons/country-flags/tc.svg","label":"Turks and Caicos Islands","category":"flags"},"tv":{"class":"fi fi-tv","path":"/custom-icons/country-flags/tv.svg","label":"Tuvalu","category":"flags"},"tr":{"class":"fi fi-tr","path":"/custom-icons/country-flags/tr.svg","label":"T\xfcrkiye","category":"flags"},"ug":{"class":"fi fi-ug","path":"/custom-icons/country-flags/ug.svg","label":"Uganda","category":"flags"},"ua":{"class":"fi fi-ua","path":"/custom-icons/country-flags/ua.svg","label":"Ukraine","category":"flags"},"ae":{"class":"fi fi-ae","path":"/custom-icons/country-flags/ae.svg","label":"United Arab Emirates","category":"flags"},"gb":{"class":"fi fi-gb","path":"/custom-icons/country-flags/gb.svg","label":"United Kingdom","category":"flags"},"un":{"class":"fi fi-un","path":"/custom-icons/country-flags/un.svg","label":"United Nations","category":"flags"},"um":{"class":"fi fi-um","path":"/custom-icons/country-flags/um.svg","label":"United States Minor Outlying Islands","category":"flags"},"us":{"class":"fi fi-us","path":"/custom-icons/country-flags/us.svg","label":"United States of America","category":"flags"},"xx":{"class":"fi fi-xx","path":"/custom-icons/country-flags/xx.svg","label":"Unknown","category":"flags"},"uy":{"class":"fi fi-uy","path":"/custom-icons/country-flags/uy.svg","label":"Uruguay","category":"flags"},"uz":{"class":"fi fi-uz","path":"/custom-icons/country-flags/uz.svg","label":"Uzbekistan","category":"flags"},"vu":{"class":"fi fi-vu","path":"/custom-icons/country-flags/vu.svg","label":"Vanuatu","category":"flags"},"ve":{"class":"fi fi-ve","path":"/custom-icons/country-flags/ve.svg","label":"Venezuela","category":"flags"},"vn":{"class":"fi fi-vn","path":"/custom-icons/country-flags/vn.svg","label":"Vietnam","category":"flags"},"vg":{"class":"fi fi-vg","path":"/custom-icons/country-flags/vg.svg","label":"Virgin Islands (British)","category":"flags"},"vi":{"class":"fi fi-vi","path":"/custom-icons/country-flags/vi.svg","label":"Virgin Islands (U.S.)","category":"flags"},"wls":{"class":"fi fi-wls","path":"/custom-icons/country-flags/gb-wls.svg","label":"Wales","category":"flags"},"wf":{"class":"fi fi-wf","path":"/custom-icons/country-flags/wf.svg","label":"Wallis and Futuna","category":"flags"},"eh":{"class":"fi fi-eh","path":"/custom-icons/country-flags/eh.svg","label":"Western Sahara","category":"flags"},"ye":{"class":"fi fi-ye","path":"/custom-icons/country-flags/ye.svg","label":"Yemen","category":"flags"},"zm":{"class":"fi fi-zm","path":"/custom-icons/country-flags/zm.svg","label":"Zambia","category":"flags"},"zw":{"class":"fi fi-zw","path":"/custom-icons/country-flags/zw.svg","label":"Zimbabwe","category":"flags"}}}'); var c = n(74822); const d = Object.keys(s.L).reduce(((e, t) => (e[t] = { icons: s.L[t], label: (0, c.A)(t) }, e)), {}),
                    u = s.P,
                    h = [...r, ...Object.keys(u)],
                    m = { ...i, ...d },
                    p = { ...l, ...u },
                    f = a,
                    v = o }, 37294: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => i, Gc: () => r, Qs: () => o, o3: () => a }); let r = function(e) { return e.primary = "primary", e.secondary = "secondary", e.secondaryGrey = "secondaryGrey", e.info = "info", e.success = "success", e.warning = "warning", e.error = "error", e.light = "light", e.dark = "dark", e.black = "black", e.white = "white", e.text = "text", e.neutral = "neutral", e.default = "default", e.common = "common", e.leftChartToolbar = "leftChartToolbar", e.dashboardNav = "dashboardNav", e }({}); const a = { White: "#FFFFFF", Teal: "#116F83", GrassGreen: "#258935", BabyBlue: "#90CCFF", Black: "#000000", BrightGreen: "#9FEEAA", BrightRed: "#D62626", DarkGrey: "#828282", FireOrange: "#E14611", HunterGreen: "#0F7C53", Lavender: "#C3AEFF", LightBlue: "#83E2EE", LightGrey: "#E4E4E4", MintGreen: "#80EECB", Orange: "#EB7600", Orchid: "#E8ADF8", Peach: "#FFCC94", Pink: "#FCB1CB", Purple: "#8629A2", Rose: "#FFBABA", RoyalBlue: "#175BB1", Scarlet: "#B11E48", Violet: "#5232CE", Yellow: "#FFE683", DarkBlue: "#11457c", DarkGreen: "#0f5944", DarkRed: "#691010", DarkPurple: "#440f5b", DarkYellow: "#646410", DarkOrange: "#6b3b11", DarkPink: "#641036", DarkViolet: "#3e1370" },
                    o = { Secondary: { 0: "#F5FAFF", 100: "#8CC1FC", 200: "#529FF7", 250: "#529FF7", 300: "#2F88EF", 400: "#1978E4", 500: "#005DC9", 600: "#0052AF", 700: "#004697", 800: "#003572", 900: "#002856" }, Blue: { 0: "#F5FAFF", 100: "#8CC1FC", 200: "#529FF7", 250: "#529FF7", 300: "#2F88EF", 400: "#1978E4", 500: "#005DC9", 600: "#0052AF", 700: "#004697", 800: "#003572", 900: "#002856" }, Teal: { 0: "#F5FFFE", 100: "#C5FDF6", 200: "#98F9ED", 250: "#98F9ED", 300: "#70F2E3", 400: "#50E9D7", 500: "#35CDBA", 600: "#21C2AF", 700: "#13A998", 800: "#0B8E7F", 900: "#087568" }, Primary: { 0: "#F8F5FF", 100: "#D7C5FD", 200: "#B698F9", 250: "#F0E9FF", 300: "#9970F2", 400: "#8150E9", 500: "#5C2DBF", 600: "#4F1CBF", 700: "#4415A9", 800: "#380D97", 900: "#2B0876" }, Violet: { 0: "#F8F5FF", 100: "#D7C5FD", 200: "#B698F9", 250: "#F0E9FF", 300: "#9970F2", 400: "#8150E9", 500: "#5C2DBF", 600: "#4F1CBF", 700: "#4415A9", 800: "#380D97", 900: "#2B0876" }, Neutrals: { 0: "#FFFFFF", 100: "#FCFCFC", 200: "#F8F8F8", 250: "#F0F0F0", 300: "#E4E4E4", 400: "#CBCBCB", 500: "#AAAAAA", 600: "#828282", 700: "#575757", 800: "#2B2B2B", 900: "#000000" }, Success: { 0: "#F5FFFC", 100: "#C4FBED", 200: "#94F4DC", 250: "#94F4DC", 300: "#6BE8C9", 400: "#4AD8B4", 500: "#31C19D", 600: "#1FA483", 700: "#127F64", 800: "#0A5341", 900: "#04261E" }, Warning: { 0: "#FFD0A3", 100: "#FBBB7F", 200: "#F4A55C", 250: "#F4A55C", 300: "#E8913F", 400: "#D87D27", 500: "#C16916", 600: "#A4550B", 700: "#7F3F04", 800: "#532901", 900: "#261200" }, Error: { 0: "#FFF5F5", 100: "#FBC8C4", 200: "#F49B94", 250: "#F49B94", 300: "#E8746B", 400: "#D8544A", 500: "#C13B31", 600: "#A4281F", 700: "#7F1A12", 800: "#530F0A", 900: "#260604" }, Info: { 0: "#F5FAFF", 100: "#C4E0FB", 200: "#94C6F4", 250: "#94C6F4", 300: "#6BACE8", 400: "#4A93D8", 500: "#317CC1", 600: "#1F64A4", 700: "#124A7F", 800: "#0A3053", 900: "#041626" } },
                    i = { default: { main: o.Blue[500], light: o.Blue[300], dark: o.Blue[700], contrastText: o.Neutrals[0], hover: { contained: o.Blue[800], outlined: o.Blue[0], text: o.Blue[800] }, down: { contained: o.Blue[700], outlined: o.Blue[700], text: o.Blue[700] } }, text: { main: o.Neutrals[900], primary: o.Neutrals[900] }, primary: { main: o.Violet[500], light: o.Violet[300], dark: o.Violet[700], contrastText: o.Neutrals[0], hover: { contained: o.Violet[800], outlined: o.Violet[0], text: o.Violet[800] }, down: { contained: o.Violet[700], outlined: o.Violet[700], text: o.Violet[700] } }, secondary: { main: o.Blue[500], light: o.Blue[300], dark: o.Blue[700], contrastText: o.Neutrals[0], hover: { contained: o.Blue[800], outlined: o.Blue[0], text: o.Blue[800] }, down: { contained: o.Blue[700], outlined: o.Blue[700], text: o.Blue[700] } }, secondaryGrey: { main: o.Neutrals[500], light: o.Neutrals[300], dark: o.Neutrals[700], contrastText: o.Neutrals[0], hover: { contained: o.Neutrals[600], outlined: o.Neutrals[0], text: o.Neutrals[600] }, down: { contained: o.Neutrals[700], outlined: o.Neutrals[700], text: o.Neutrals[700] } }, success: { main: o.Success[500], contrastText: o.Neutrals[0], light: o.Success[300], dark: o.Success[700], hover: { contained: o.Success[800], outlined: o.Success[0], text: o.Success[800] }, down: { contained: o.Success[700], outlined: o.Success[700], text: o.Success[700] } }, warning: { main: o.Warning[500], contrastText: o.Neutrals[0], light: o.Warning[300], dark: o.Warning[700], hover: { contained: o.Warning[800], outlined: o.Warning[0], text: o.Warning[800] }, down: { contained: o.Warning[700], outlined: o.Warning[700], text: o.Warning[700] } }, error: { main: o.Error[500], contrastText: o.Neutrals[0], light: o.Error[300], dark: o.Error[700], hover: { contained: o.Error[800], outlined: o.Error[0], text: o.Error[800] }, down: { contained: o.Error[700], outlined: o.Error[700], text: o.Error[700] } }, info: { main: o.Info[500], contrastText: o.Neutrals[0], light: o.Info[300], dark: o.Info[700], hover: { contained: o.Info[800], outlined: o.Info[0], text: o.Info[800] }, down: { contained: o.Info[700], outlined: o.Info[700], text: o.Info[700] } }, neutral: { main: o.Neutrals[700], hover: { contained: o.Neutrals[100], outlined: o.Neutrals[100], text: o.Info[700] }, down: { contained: o.Neutrals[100], outlined: o.Neutrals[100], text: o.Info[700] } }, black: { main: o.Neutrals[900], dark: o.Neutrals[900], contrastText: o.Neutrals[0], light: o.Neutrals[700], hover: { contained: o.Neutrals[800], outlined: o.Neutrals[200], text: o.Neutrals[800] }, down: { contained: o.Neutrals[700], outlined: o.Neutrals[700], text: o.Neutrals[700] } }, dark: { main: o.Neutrals[900], light: o.Neutrals[300], dark: o.Neutrals[700], contrastText: o.Neutrals[0] }, light: { main: o.Neutrals[0], light: o.Neutrals[300], dark: o.Neutrals[700], contrastText: o.Neutrals[900] }, white: { main: o.Neutrals[0], contrastText: o.Neutrals[900], dark: o.Neutrals[700], light: o.Neutrals[100], hover: { contained: o.Neutrals[200], outlined: o.Neutrals[200], text: o.Neutrals[200] }, down: { contained: o.Neutrals[100], outlined: o.Neutrals[100], text: o.Neutrals[100] } }, common: { main: "", black: o.Neutrals[900] }, leftChartToolbar: { main: "", background: o.Neutrals[100] }, dashboardNav: { main: "", background: o.Neutrals[100] } } }, 98022: (e, t, n) => { "use strict";
                n.d(t, { $F: () => o, Qi: () => a, vR: () => r }); let r = function(e) { return e.none = "elevationNone", e.lowest = "elevationLowest", e.low = "elevationLow", e.medium = "elevationMedium", e.high = "elevationHigh", e }({}); const a = {
                        [r.none]: { boxShadow: "none" }, [r.lowest]: { boxShadow: "0px 4px 4px 0px #00000040" }, [r.low]: { boxShadow: "0px 2px 5px 0px #0000001A, 0px 10px 10px 0px #00000017, 0px 22px 13px 0px #0000000D, 0px 39px 16px 0px #00000003, 0px 61px 17px 0px #00000000;" }, [r.medium]: { boxShadow: "0px 7px 15px 0px #0000001A, 0px 27px 27px 0px #00000017, 0px 60px 36px 0px #0000000D, 0px 107px 43px 0px #00000003, 0px 168px 47px 0px #00000000;" }, [r.high]: { boxShadow: "0px 14px 30px 0px #0000001A, 0px 55px 55px 0px #00000017, 0px 124px 74px 0px #0000000D, 0px 220px 88px 0px #00000003, 0px 344px 96px 0px #00000000" } },
                    o = Object.keys(a).map((e => ({ props: { variant: e }, style: a[e] }))) }, 32115: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => i, Eq: () => a, uT: () => o }); var r = n(20965); let a = function(e) { return e.h1 = "h1", e.h2 = "h2", e.h3 = "h3", e.h4 = "h4", e.displayJumbo = "displayJumbo", e.displayXXL = "displayXXL", e.displayXL = "displayXL", e.displayLG = "displayLG", e.displayMD = "displayMD", e.displaySM = "displaySM", e.bodyXL = "bodyXL", e.bodyLG = "bodyLG", e.bodyMD = "bodyMD", e.bodySM = "bodySM", e.caption = "caption", e.subheadingXL = "subheadingXL", e.subheadingLG = "subheadingLG", e.subheadingMD = "subheadingMD", e.subheadingSM = "subheadingSM", e.subheadingXS = "subheadingXS", e.tabSM = "tabSM", e.labelLG = "labelLG", e.labelMD = "labelMD", e.labelSM = "labelSM", e }({}); const o = { inherit: { fontSize: "inherit", lineHeight: "inherit", fontWeight: "inherit" }, displayJumbo: { fontSize: (0, r.A)(50), lineHeight: (0, r.A)(61), fontWeight: 700 }, displayXXL: { fontSize: (0, r.A)(32), lineHeight: (0, r.A)(39), fontWeight: 700 }, displayXL: { fontSize: (0, r.A)(28), lineHeight: (0, r.A)(34), fontWeight: 700 }, displayLG: { fontSize: (0, r.A)(26), lineHeight: (0, r.A)(31), fontWeight: 400 }, displayMD: { fontSize: (0, r.A)(24), lineHeight: (0, r.A)(29), fontWeight: 700 }, displaySM: { fontSize: (0, r.A)(24), lineHeight: (0, r.A)(29), fontWeight: 700 }, bodyXL: { fontSize: (0, r.A)(20), lineHeight: (0, r.A)(30), fontWeight: 300 }, bodyLG: { fontSize: (0, r.A)(16), lineHeight: (0, r.A)(24), fontWeight: 400 }, bodyMD: { fontSize: (0, r.A)(14), lineHeight: (0, r.A)(17), fontWeight: 400 }, bodySM: { fontSize: (0, r.A)(12), lineHeight: (0, r.A)(15), fontWeight: 400 }, caption: { fontSize: (0, r.A)(10), lineHeight: (0, r.A)(12), fontWeight: 400 }, h1: { fontSize: (0, r.A)(20), lineHeight: (0, r.A)(24), fontWeight: 600 }, h2: { fontSize: (0, r.A)(18), lineHeight: (0, r.A)(22), fontWeight: 700 }, h3: { fontSize: (0, r.A)(16), lineHeight: (0, r.A)(19), fontWeight: 700 }, h4: { fontSize: (0, r.A)(16), lineHeight: (0, r.A)(19), fontWeight: 600 }, subheadingXL: { fontSize: (0, r.A)(18), lineHeight: (0, r.A)(22), fontWeight: 600 }, subheadingLG: { fontSize: (0, r.A)(14), lineHeight: (0, r.A)(17), fontWeight: 700 }, subheadingMD: { fontSize: (0, r.A)(14), lineHeight: (0, r.A)(17), fontWeight: 600 }, subheadingSM: { fontSize: (0, r.A)(12), lineHeight: (0, r.A)(14), fontWeight: 600 }, subheadingXS: { fontSize: (0, r.A)(10), lineHeight: (0, r.A)(12), fontWeight: 600 }, labelLG: { fontSize: (0, r.A)(11), lineHeight: (0, r.A)(13), fontWeight: 700 }, labelMD: { fontSize: (0, r.A)(8), lineHeight: (0, r.A)(10), fontWeight: 700 }, labelSM: { fontSize: (0, r.A)(8), lineHeight: (0, r.A)(10), fontWeight: 500 }, tabSM: { fontSize: (0, r.A)(10), lineHeight: (0, r.A)(12), fontWeight: 600 } },
                    i = { variants: [{ props: { variant: a.displayJumbo }, style: o.displayJumbo }, { props: { variant: a.displayXXL }, style: o.displayXXL }, { props: { variant: a.displayXL }, style: o.displayXL }, { props: { variant: a.displayLG }, style: o.displayLG }, { props: { variant: a.displayMD }, style: o.displayMD }, { props: { variant: a.displaySM }, style: o.displaySM }, { props: { variant: a.bodyXL }, style: o.bodyXL }, { props: { variant: a.bodyLG }, style: o.bodyLG }, { props: { variant: a.bodyMD }, style: o.bodyMD }, { props: { variant: a.bodySM }, style: o.bodySM }, { props: { variant: a.caption }, style: o.caption }, { props: { variant: a.h1 }, style: o.h1 }, { props: { variant: a.h2 }, style: o.h2 }, { props: { variant: a.h3 }, style: o.h3 }, { props: { variant: a.h4 }, style: o.h4 }, { props: { variant: a.subheadingXL }, style: o.subheadingXL }, { props: { variant: a.subheadingLG }, style: o.subheadingLG }, { props: { variant: a.subheadingMD }, style: o.subheadingMD }, { props: { variant: a.subheadingSM }, style: o.subheadingSM }, { props: { variant: a.subheadingXS }, style: o.subheadingXS }, { props: { variant: a.labelLG }, style: o.labelLG }, { props: { variant: a.labelMD }, style: o.labelMD }, { props: { variant: a.labelSM }, style: o.labelSM }, { props: { variant: a.tabSM }, style: o.tabSM }], styleOverrides: { root: e => { let { ownerState: t } = e; return "function" === typeof(null === t || void 0 === t ? void 0 : t.onClick) ? { cursor: "pointer" } : {} } } } }, 5613: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => h, Hd: () => l, SK: () => s, ym: () => u }); var r = n(37294),
                    a = n(20965),
                    o = n(98022); const i = { styleOverrides: { tooltip: {} } }; let l = function(e) { return e.BuiltIn = "built-in", e.Padding16 = "padding-16", e.Padding0 = "padding-0", e }({}); const s = {
                        [r.Gc.default]: r.Gc.default, [r.Gc.primary]: r.Gc.primary, [r.Gc.secondary]: r.Gc.secondary, [r.Gc.error]: r.Gc.error, [r.Gc.warning]: r.Gc.warning, [r.Gc.success]: r.Gc.success, [r.Gc.info]: r.Gc.info, [r.Gc.light]: r.Gc.light, [r.Gc.dark]: r.Gc.dark, [r.Gc.neutral]: r.Gc.neutral },
                    c = e => ({ tooltip: { sx: { background: e === r.Gc.default ? "" : r.Ay[e].main, color: r.Ay[e].contrastText, borderRadius: (0, a.A)(10), border: "1px solid ".concat(e === r.Gc.default ? "" : r.Ay[e].light), ...o.Qi.elevationHigh } }, arrow: { sx: { color: e === r.Gc.default ? "" : r.Ay[e].main } } }),
                    d = (e, t) => { var n, r; const o = c(e); return o ? { tooltip: { sx: { ...(null === o || void 0 === o || null === (n = o.tooltip) || void 0 === n ? void 0 : n.sx) || {}, padding: (0, a.A)(t) } }, arrow: { sx: { ...(null === o || void 0 === o || null === (r = o.arrow) || void 0 === r ? void 0 : r.sx) || {} } } } : {} },
                    u = {
                        [l.BuiltIn]: {
                            [r.Gc.default]: c(r.Gc.default), [r.Gc.primary]: c(r.Gc.primary), [r.Gc.secondary]: c(r.Gc.secondary), [r.Gc.error]: c(r.Gc.error), [r.Gc.warning]: c(r.Gc.warning), [r.Gc.success]: c(r.Gc.success), [r.Gc.info]: c(r.Gc.info), [r.Gc.light]: c(r.Gc.light), [r.Gc.dark]: c(r.Gc.dark), [r.Gc.neutral]: c(r.Gc.neutral) }, [l.Padding16]: {
                            [r.Gc.default]: d(r.Gc.default, 16), [r.Gc.primary]: d(r.Gc.primary, 16), [r.Gc.secondary]: d(r.Gc.secondary, 16), [r.Gc.error]: d(r.Gc.error, 16), [r.Gc.warning]: d(r.Gc.warning, 16), [r.Gc.success]: d(r.Gc.success, 16), [r.Gc.info]: d(r.Gc.info, 16), [r.Gc.light]: d(r.Gc.light, 16), [r.Gc.dark]: d(r.Gc.dark, 16), [r.Gc.neutral]: d(r.Gc.neutral, 16) }, [l.Padding0]: {
                            [r.Gc.default]: d(r.Gc.default, 0), [r.Gc.primary]: d(r.Gc.primary, 0), [r.Gc.secondary]: d(r.Gc.secondary, 0), [r.Gc.error]: d(r.Gc.error, 0), [r.Gc.warning]: d(r.Gc.warning, 0), [r.Gc.success]: d(r.Gc.success, 0), [r.Gc.info]: d(r.Gc.info, 0), [r.Gc.light]: d(r.Gc.light, 0), [r.Gc.dark]: d(r.Gc.dark, 0), [r.Gc.neutral]: d(r.Gc.neutral, 16) } },
                    h = i }, 20965: (e, t, n) => { "use strict";

                function r(e) { return e / (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 16) } n.d(t, { A: () => a, Z: () => r }); const a = function(e) { return "".concat(e / (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 16), "rem") } }, 21933: (e, t, n) => { "use strict";
                n.d(t, { ML: () => o, du: () => i, q7: () => r, zg: () => a }); const r = "#f8f8f8",
                    a = [{ label: "yyyy-MM-dd", value: { type: "format", value: "yyyy-MM-dd" } }, { label: "dd/MM/yyyy", value: { type: "format", value: "dd/MM/yyyy" } }, { label: "MM/dd/yyyy", value: { type: "format", value: "MM/dd/yyyy" } }, { label: "MM/yyyy", value: { type: "format", value: "MM/yyyy" } }, { label: "DATE_FULL", value: { type: "string", value: "DATE_FULL" } }],
                    o = [{ label: "--", value: "none" }, { label: "ab", value: "lowercase" }, { label: "AB", value: "uppercase" }, { label: "Ab", value: "capitalize" }],
                    i = [{ label: "None", value: "none" }, { label: "Square", value: "square" }, { label: "Rounded", value: "round" }] }, 47088: (e, t, n) => { "use strict";
                n.d(t, { N4: () => h, NB: () => u, Tl: () => o, oL: () => a, yw: () => r, tI: () => m, at: () => c, Vc: () => d, ix: () => i }); let r = function(e) { return e.String = "string", e.RichText = "richText", e.Number = "number", e.Boolean = "boolean", e.Url = "url", e.Tags = "tags", e.Date = "date", e.Attachment = "attachment", e.Location = "location", e.Switch = "switch", e.Currency = "currency", e.Chart = "chart", e.Computed = "computed", e.Rollup = "rollup", e.IconPickList = "iconPicklist", e }({}),
                    a = function(e) { return e.Member = "member", e.Role = "role", e }({}),
                    o = function(e) { return e.Private = "private", e.Protected = "protected", e }({}),
                    i = function(e) { return e.NewUser = "newUser", e.NewOrg = "newOrg", e.NewChart = "newChart", e.NewReport = "newReport", e.NewBackup = "newBackup", e.PrintCompleted = "printCompleted", e.PrintFailed = "printFailed", e.PermissionRequest = "permissionRequest", e.SystemMaintenance = "systemMaintenance", e.SystemAnnouncement = "systemAnnouncement", e.SystemWebinar = "systemWebinar", e.DeletedChart = "deletedChart", e.DeletedReport = "deletedReport", e.DeletedOrg = "deletedOrg", e.DeletedUser = "deletedUser", e.DeletedBackup = "deletedBackup", e.LicenseExpiring = "licenseExpiring", e.LicenseExpired = "licenseExpired", e.LicenseUsage = "licenseUsage", e.InviteReceivedOwner = "inviteReceivedOwner", e.InviteReceivedAdmin = "inviteReceivedAdmin", e.InviteReceivedEditor = "inviteReceivedEditor", e.InviteReceivedViewer = "inviteReceivedViewer", e.InviteAcceptedOwner = "inviteAcceptedOwner", e.InviteAcceptedAdmin = "inviteAcceptedAdmin", e.InviteAcceptedEditor = "inviteAcceptedEditor", e.InviteAcceptedViewer = "inviteAcceptedViewer", e.InvitePendingOwner = "invitePendingOwner", e.ReplicateChartSuccess = "replicateChartSuccess", e.SnapshotCreated = "snapshotCreated", e.SnapshotDeleted = "snapshotDeleted", e.SnapshotRestored = "snapshotRestored", e.SnapshotRestoreFailed = "snapshotRestoreFailed", e.SnapshotLimitReached = "snapshotLimitReached", e.IntegrationFileSyncSuccess = "integrationFileSyncSuccess", e }({}),
                    l = function(e) { return e.Email = "email", e.InApp = "inApp", e.None = "none", e }({}),
                    s = function(e) { return e.Daily = "daily", e.Weekly = "weekly", e.Monthly = "monthly", e.Never = "never", e }({}),
                    c = function(e) { return e.Print = "print", e.User = "user", e.Chart = "chart", e.Report = "report", e.Org = "org", e.Backup = "backup", e.License = "license", e.System = "system", e.Invite = "invite", e.Snapshot = "snapshot", e.PendingInvite = "pendingInvite", e.Integration = "integration", e }({});
                c.Print, i.PrintCompleted, i.PrintFailed, c.User, i.NewUser, i.DeletedUser, c.Chart, i.NewChart, i.DeletedChart, c.Report, i.NewReport, i.DeletedReport, c.Org, i.NewOrg, i.DeletedOrg, c.Backup, i.NewBackup, i.DeletedBackup, c.License, i.LicenseExpiring, i.LicenseExpired, i.LicenseUsage, c.System, i.SystemMaintenance, i.SystemAnnouncement, c.Invite, i.InviteAcceptedOwner, i.InviteAcceptedAdmin, i.InviteAcceptedEditor, i.InviteAcceptedViewer, c.Snapshot, i.SnapshotCreated, i.SnapshotDeleted, i.SnapshotRestored, i.SnapshotRestoreFailed, i.SnapshotLimitReached; const d = {
                    [i.NewUser]: { category: c.User, displayName: "New User", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.NewOrg]: { category: c.Org, displayName: "New Organization", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.NewChart]: { category: c.Chart, displayName: "New Chart", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.NewReport]: { category: c.Report, displayName: "New Report", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.NewBackup]: { category: c.Backup, displayName: "New Backup", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.PrintCompleted]: { category: c.Print, displayName: "Print Completed", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.PrintFailed]: { category: c.Print, displayName: "Print Failed", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.PermissionRequest]: { category: c.User, displayName: "Permission Request", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SystemMaintenance]: { category: c.System, displayName: "System Maintenance", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SystemAnnouncement]: { category: c.System, displayName: "System Announcement", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SystemWebinar]: { category: c.System, displayName: "Organimi Webinar", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.DeletedChart]: { category: c.Chart, displayName: "Deleted Chart", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.DeletedReport]: { category: c.Report, displayName: "Deleted Report", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.DeletedOrg]: { category: c.Org, displayName: "Deleted Organization", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.DeletedUser]: { category: c.User, displayName: "Deleted User", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.DeletedBackup]: { category: c.Backup, displayName: "Deleted Backup", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.LicenseExpiring]: { category: c.License, displayName: "License Expiring", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.LicenseExpired]: { category: c.License, displayName: "License Expired", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.LicenseUsage]: { category: c.License, displayName: "License Usage", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteAcceptedOwner]: { category: c.Invite, displayName: "Invite Accepted Owner", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteAcceptedAdmin]: { category: c.Invite, displayName: "Invite Accepted Admin", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteAcceptedEditor]: { category: c.Invite, displayName: "Invite Accepted Editor", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteAcceptedViewer]: { category: c.Invite, displayName: "Invite Accepted Viewer", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteReceivedOwner]: { category: c.Invite, displayName: "Invite Received Owner", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteReceivedAdmin]: { category: c.Invite, displayName: "Invite Received Admin", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteReceivedEditor]: { category: c.Invite, displayName: "Invite Received Editor", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InviteReceivedViewer]: { category: c.Invite, displayName: "Invite Received Viewer", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.InvitePendingOwner]: { category: c.PendingInvite, displayName: "Account Owner Invitation Awaiting Response", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Never, allowedChannels: [l.InApp, l.Email] } }, [i.SnapshotCreated]: { category: c.Snapshot, displayName: "Backup Snapshot Created", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SnapshotDeleted]: { category: c.Snapshot, displayName: "Backup Snapshot Deleted", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SnapshotRestored]: { category: c.Snapshot, displayName: "Backup Snapshot Restored", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SnapshotRestoreFailed]: { category: c.Snapshot, displayName: "Backup Snapshot Restore Failed", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.SnapshotLimitReached]: { category: c.Snapshot, displayName: "Backup Snapshots Limit Reached", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.IntegrationFileSyncSuccess]: { category: c.Integration, displayName: "Integration File Sync Success", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } }, [i.ReplicateChartSuccess]: { category: c.Chart, displayName: "Chart Replicated Successfully", defaultSetting: { notificationChannel: [l.InApp], frequency: s.Daily, allowedChannels: [l.InApp, l.Email] } } }; let u = function(e) { return e.Traditional = "traditional", e.Matrix = "matrix", e.GC = "board of directors", e }({}),
                    h = function(e) { return e.Draft = "draft", e.Published = "published", e }({}),
                    m = function(e) { return e.ICON = "icon", e.COLORBAR = "colorBar", e }({}) }, 8868: (e, t, n) => { "use strict";
                n.d(t, { $: () => r, p: () => a }); let r = function(e) { return e.VIEW = "view", e.PRINTPREVIEW = "printpreview", e.PRINT = "print", e.AI = "ai", e }({}),
                    a = function(e) { return e.PUBLIC = "public", e.SETTINGS = "settings", e.THEMES = "themes", e.THEME = "theme", e.AI = "aiInsights", e }({}) }, 72952: (e, t, n) => { "use strict";
                n.d(t, { U$: () => s, UF: () => o, ck: () => i, zb: () => r, bV: () => a, d5: () => d, $: () => c, jo: () => l });
                n(47088); let r = function(e) { return e.Small = "s", e.Medium = "m", e.Large = "l", e.ExtraLarge = "xl", e.DummyNodeSize = "dummyNodeSize", e }({}),
                    a = function(e) { return e.Primary = "Primary", e.Secondary = "Secondary", e.Tertiary = "Tertiary", e }({}),
                    o = function(e) { return e.OperatingEntity = "Operating Entity", e.Holding = "Holding", e.TaxHolding = "Tax Holding", e }({}),
                    i = function(e) { return e.IN = "Individual", e.CO = "Corporation", e.LLC = "Limited Liability Company", e.PN = "Partnership", e.LP = "Limited Partnership", e.LLP = "Limited Liability Partnership", e.TR = "Trust", e.OO = "Other", e }({}),
                    l = function(e) { return e.Horizontal = "horizontal", e.Vertical = "vertical", e.Condensed = "condensed", e.Grid = "grid", e }({}); let s = function(e) { return e.Left = "left", e.Top = "top", e.Right = "right", e.Hidden = "hidden", e.Bottom = "bottom", e.Cover = "cover", e.TopRight = "topright", e.TopLeft = "topleft", e.BottomRight = "bottomright", e.BottomLeft = "bottomleft", e }({}); let c = function(e) { return e.Visible = "visible", e.Hover = "hover", e.Click = "click", e }({}),
                    d = function(e) { return e.below = "below", e.insideLeft = "insideLeft", e.insideRight = "insideRight", e.insideRightStacked = "insideRightStacked", e }({}) }, 18519: (e, t, n) => { "use strict";
                n.d(t, { T: () => r }); let r = function(e) { return e.Shared = "shared", e.Embedded = "embedded", e.Department = "department", e.Location = "location", e.Single = "single", e.Team = "team", e.Function = "function", e.Assistant = "assistant", e }({}) }, 37091: (e, t, n) => { "use strict";
                n.d(t, { TK: () => a, hC: () => r }); let r = function(e) { return e.ROLE = "role", e.LINK = "link", e }({}),
                    a = function(e) { return e.PENDING = "pending", e.APPROVED = "approved", e.APPLIED = "applied", e.REJECTED = "rejected", e }({}) }, 15622: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(80192),
                    a = n(59269); const o = (0, r.Mz)(a._, (e => e.user.licenses), (function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : []; return e.activeLicenseId ? (t || []).find((t => t.id === e.activeLicenseId)) : null })) }, 30752: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r = n(16091),
                    a = n(80192),
                    o = n(39362),
                    i = n(13888); const l = (0, a.Mz)((e => e.appInfo.mode), (e => e.themes.customThemes), i.il, (e => e.themes.shareTheme), (e => e.organization.theme), (e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.theme }), (e => { var t; return null === (t = e.organization) || void 0 === t ? void 0 : t.fields }), ((e, t, n, a, i, l, s) => { const c = [...(0, o.r)(s) || [], ...t].filter((e => e)); let d, u = c.find((e => "organimi" !== e.type)); const h = c.find((e => e.id === l)),
                        m = c.find((e => e.id === i));
                    ["print", "printPreview"].includes(e) ? n && (d = c.find((e => e.id === n))) : ["public", "embed"].includes(e) ? d = a : (l && h && (d = h), !d && i && m && (d = m), d || (d = u)); try { if (d || (d = h || m || u || c[0] || (0, r.A)("fallbackTheme")), d) { var p, f;
                            d = JSON.parse(JSON.stringify(d)); for (let e of (null === (v = d) || void 0 === v || null === (g = v.data) || void 0 === g ? void 0 : g.chart) || []) { var v, g; if (!e.field.isDefault) continue; const t = (s || []).find((t => { var n, r; return t.model === (null === (n = e.field) || void 0 === n ? void 0 : n.model) && t.name === (null === (r = e.field) || void 0 === r ? void 0 : r.name) }));
                                t && (e.field.id = t.id) } null !== (p = d.chartOptions) && void 0 !== p && null !== (f = p.legend) && void 0 !== f && f.badgeBarPosition || (d.chartOptions.legend.badgeBarPosition = "left") } } catch (y) { console.log(y) } return d || h || m || u || c[0] || (0, r.A)("fallbackTheme") })) }, 36735: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = e => e.appInfo.mode }, 55005: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(80192),
                    a = n(10621); const o = (0, r.Mz)((e => e.organization.fields), (e => (e || []).map((e => (e => ({ ...e, typeMetadata: (0, a.to)(e) }))(e))))) }, 84726: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = e => { var t; return null === (t = e.chart.info) || void 0 === t ? void 0 : t.legend } }, 95695: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => h, yP: () => u, RW: () => d }); var r = n(8868),
                    a = n(37091); var o = n(80192),
                    i = n(55005),
                    l = n(59445),
                    s = n(53109),
                    c = n(45956); const d = (0, o.Mz)((e => e.appInfo.secondaryMode), (e => { var t; return null === (t = e.chart.insights) || void 0 === t ? void 0 : t.suggestions }), ((e, t) => e !== r.p.AI ? [] : t || [])),
                    u = (0, o.Mz)((e => e.appInfo.secondaryMode), (e => { var t; return null === (t = e.chart.insights) || void 0 === t ? void 0 : t.suggestions }), (e => { var t; return null === (t = e.chart.insights) || void 0 === t ? void 0 : t.filters }), ((e, t, n) => e !== r.p.AI ? [] : (t || []).filter((e => { let t = e.status != a.TK.REJECTED; return (null === n || void 0 === n ? void 0 : n.confidence) === s.jJ.high ? t = t && e.confidence >= c.r_.HIGH : (null === n || void 0 === n ? void 0 : n.confidence) === s.jJ.medium && (t = t && e.confidence >= c.r_.MEDIUM), t })).reduce(((e, t) => { const n = e.find((e => e.role.id === t.role.id)); return n && n.confidence >= t.confidence ? e : [...e, t] }), []))),
                    h = (0, o.Mz)(d, (e => e.appInfo.secondaryMode), i.A, (function(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : []; return t !== r.p.AI ? [] : e.filter((e => e.type === a.hC.ROLE)).map((e => (0, l.cf)(e, n))) })) }, 13895: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(78396); const a = e => e.chart.info.type || r.XD.TRADITIONAL }, 64495: (e, t, n) => { "use strict";
                n.d(t, { o: () => i }); var r = n(80192),
                    a = n(10621),
                    o = n(55005); const i = (0, r.Mz)(o.A, (e => ({ firstName: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.FIRSTNAME && e.model === a.A2.MEMBER && e.isDefault)), lastName: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.LASTNAME && e.model === a.A2.MEMBER && e.isDefault)), email: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.EMAIL && e.model === a.A2.MEMBER && e.isDefault)), phone: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.PHONE && e.model === a.A2.MEMBER && e.isDefault)), description: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.DESCRIPTION && e.model === a.A2.MEMBER && e.isDefault)), linkedIn: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.x2.LINKEDIN && e.model === a.A2.MEMBER && e.isDefault)), roleName: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.dj.NAME && e.isDefault && e.model === a.A2.ROLE)), locationAddress: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.dj.LOCATIONADDRESS && e.isDefault && e.model === a.A2.ROLE)), roleDescription: null === e || void 0 === e ? void 0 : e.find((e => e.name === a.dj.DESCRIPTION && e.isDefault && e.model === a.A2.ROLE)) }))) }, 78226: (e, t, n) => { "use strict";
                n.d(t, { B: () => r }); const r = (0, n(80192).Mz)((e => e.organization), (e => ({ id: e.id, name: e.name, license: e.license, logo: e.logo, website: e.website, description: e.description, ownershipDetails: e.ownershipDetails }))) }, 65491: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r = n(80192),
                    a = n(78396),
                    o = n(31041),
                    i = n(74079); const l = (0, r.Mz)(i.A, o.A, (e => { var t; return null === (t = e.chart) || void 0 === t ? void 0 : t.info }), ((e, t, n) => { var r; const o = null === n || void 0 === n || null === (r = n.alias) || void 0 === r ? void 0 : r.roleId,
                        i = {},
                        l = []; for (const a of t) { const { children: t = [], parent: n } = e[a] || { children: [], parent: null };
                        i[a] = (t || []).filter((e => e)), (!n || o && o === a) && l.push(a) } return i[a.Uz] = l.filter((e => e)), i })) }, 6562: (e, t, n) => { "use strict";

                function r(e) { return e.people.entities } n.d(t, { A: () => r }) }, 34944: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(80192),
                    a = n(74079); const o = (0, r.Mz)(a.A, (e => Object.values(e))) }, 59770: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => m, Qo: () => u, Y$: () => h }); var r = n(80192),
                    a = n(279),
                    o = n(7743),
                    i = n(6562),
                    l = n(34944),
                    s = n(74079); const c = (0, r.Mz)(i.A, l.A, (e => { var t; return null === (t = e.chart.info) || void 0 === t ? void 0 : t.legend }), o.C1, ((e, t, n, r) => { const o = {},
                            i = (n || {}).rules || [],
                            l = !!(n && n.id && n.visible) && i.reduce(((e, t) => { let n = []; return t.comparators && t.comparators.length && (e || (e = {}), n = t.comparators.map((e => { let t = "string"; if ("unfilled" === e.field.name || "position" === e.field.name) t = "chart";
                                    else { let n = r[e.field.id];
                                        t = ((null === n || void 0 === n ? void 0 : n.type) || "string").toLowerCase() } return { ...e, type: t } })), e[t.id] = { comparators: n, join: t.join || "AND" }), e }), null) || {}; return t.reduce(((t, n) => { const { id: o } = n, i = (0, a.b)({ role: n, ruleTests: l, roster: e, fieldMap: r }); return t[o] = i, t }), o), o })),
                    d = (0, r.Mz)(c, (e => Object.keys(e).reduce(((t, n) => (Object.keys(e[n]).forEach((r => { t[r] || (t[r] = []), "object" === typeof e[n][r] ? Object.values(e[n][r] || {}).some((e => e)) && t[r].push(n) : e[n][r] && t[r].push(n) })), t)), {}))),
                    u = (0, r.Mz)(d, s.A, i.A, (e => e.legend.filterBadges), ((e, t, n, r) => { let a = [];
                        r.forEach((t => { e[t] && a.push(...e[t]) })), a = [...new Set(a)]; return a.map((e => { const r = { ...t[e] || {} },
                                a = { role: r }; return r.members ? (a.member = n[r.members[0]], a) : (a.member = {}, a) })) })),
                    h = (0, r.Mz)(d, s.A, i.A, (e => e.legend.filterBadges), ((e, t, n, r) => { let a = [];
                        r.forEach((t => { e[t] && a.push(...e[t]) })), a = [...new Set(a)]; let o = []; const i = []; return a.map((e => { const r = { ...t[e] || {} },
                                a = { role: r };
                            r.members ? a.member = n[r.members[0]] : a.member = {}, o.push(a),
                                function e(n) { const r = t[n]; if (r && !i.includes(n)) return "department" === (null === r || void 0 === r ? void 0 : r.type) && o.push({ role: r, member: null }), i.push(n), e(r.parent) }(null === r || void 0 === r ? void 0 : r.parent) })), o })),
                    m = c }, 31041: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(80192),
                    a = n(95695); const o = (0, r.Mz)((e => e.roles.ids), a.Ay, ((e, t) => [...e || [], ...t.map((e => e.id))])) }, 74079: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(80192),
                    a = n(55005),
                    o = n(59445),
                    i = n(95695),
                    l = n(37091); const s = (0, r.Mz)((e => e.roles.entities), i.yP, a.A, ((e, t, n) => { const r = t.filter((e => e.type === l.hC.ROLE)).map((e => (0, o.cf)(e, n))),
                        a = r.reduce(((e, t) => (e[t.id] = t, e)), {}),
                        i = JSON.parse(JSON.stringify({ ...a, ...e })); try { const e = t.filter((e => e.type === l.hC.LINK || e.type === l.hC.ROLE && e.status === l.TK.APPROVED)); for (let t = 0; t < e.length; t++) { var s; const n = e[t],
                                r = null === (s = n.parent) || void 0 === s ? void 0 : s.id,
                                a = n.role.id; if (!r || r === a) continue; const o = i[r]; if (!o) continue; const l = (o.children || []).filter((e => e !== n.role.id));
                            i[r] = { ...o, children: [...l, n.role.id] }; const c = i[n.role.id]; if (null !== c && void 0 !== c && c.parent && c.parent !== r) { const e = i[c.parent];
                                e && (e.children = e.children.filter((e => e !== n.role.id))) } i[n.role.id] = { ...c, parent: r } } const n = r.filter((e => e.suggestion.status === l.TK.PENDING)); for (let t = 0; t < n.length; t++) { var c; const e = n[t],
                                r = null === (c = e.suggestion.parent) || void 0 === c ? void 0 : c.id,
                                a = e.suggestion.role.id,
                                o = e.suggestion.gapReports.map((e => e.id)) || []; if (!r || r === a) continue; const l = i[r],
                                s = (null === l || void 0 === l ? void 0 : l.children) || []; if (!l) continue; const d = s.filter((e => !o.includes(e)));
                            i[r] = { ...l, children: [...d, e.id] }, i[a] = { ...i[a], children: [...o] } } } catch (d) { console.error("Error while transforming role map", d) } return i })) }, 92746: (e, t, n) => { "use strict";
                n.d(t, { A: () => a, H: () => r }); const r = e => e.user.licenses || [],
                    a = r }, 41583: (e, t, n) => { "use strict";
                n.d(t, { D: () => s, W: () => c }); var r, a, o = n(57528),
                    i = n(72119),
                    l = n(40454); const s = (0, i.Ay)("div")(r || (r = (0, o.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    flex:1;\n    overflow-y:auto;\n    .MuiListItemIcon-root {\n      min-width: auto;\n    }\n    .MuiPaper-root{\n      border: 1px solid ".concat(t.palette.grey[400], ";\n      border-radius:0;\n      padding:").concat(t.spacing(2), "px;\n    }\n    .content-wrapper{\n      display:flex;\n      flex-direction:column;\n      justify-content:flex-start;\n    }\n    .MuiListItem-gutters {\n      padding-left:0;\n    }\n    .MuiButton-root{\n      text-transform:none;\n    }\n    ") })),
                    c = (0, i.Ay)(l.A)(a || (a = (0, o.A)(["\n  width: auto;\n  flex: 1;\n  .wrapper {\n    width: 100%;\n  }\n  .access-select {\n    flex: 1;\n  }\n"]))) }, 47287: (e, t, n) => { "use strict";
                n.d(t, { A: () => u }); var r = n(99229),
                    a = n(17339),
                    o = n(75156),
                    i = n(84866),
                    l = n(65043),
                    s = n(3342),
                    c = n(61258),
                    d = n(70579); const u = e => { let { error: t, fullWidth: n, helperText: u, fieldRefParams: h, label: m, name: p, doValidation: f } = e; const { register: v } = (0, c.xW)(), [g, y] = (0, l.useState)(!1), [b, w] = (0, l.useState)({ ...h }); return (0, l.useEffect)((() => { f && w((e => { let t = { ...e }; return t.validate || (t.validate = {}), t.validate.validatePasswordStrength = e => (e => "weak" !== s.A.getPasswordStrength(e).strength.toLowerCase())(e), t })) }), []), (0, d.jsx)(i.A, { name: p, fullWidth: n, label: m, type: g ? "text" : "password", inputRef: v(b), error: t, inputProps: { maxLength: 128 }, InputProps: { endAdornment: (0, d.jsx)(r.A, { position: "end", children: (0, d.jsx)(a.A, { "aria-label": "toggle password visibility", onClick: () => { y(!g) }, onMouseDown: e => { e.preventDefault() }, tabIndex: -1, children: !0 === g ? (0, d.jsx)(o.Ay, { icon: "ShowPassword" }) : (0, d.jsx)(o.Ay, { icon: "HidePassword" }) }, "".concat(p, "_").concat(g)) }) }, helperText: u || "" }) } }, 35276: (e, t, n) => { "use strict";
                n.d(t, { e: () => s }); var r, a, o = n(57528),
                    i = n(72119),
                    l = (n(75156), n(70579)); const s = (0, i.Ay)((e => { let { align: t, size: n, dropRef: r = null, isOver: a = null, position: o, ...i } = e; return (0, l.jsx)("div", { ref: r, ...i }) })).attrs((e => { let { size: t, align: n, isOver: r, position: a } = e; const o = "lg" === t ? 36 : "x2" === t ? 42 : 56,
                        i = o / 2,
                        l = { position: a }; return "absolute" === a && ("top" === n ? (l.marginLeft = "-".concat(i, "px"), l.left = "50%", l.top = "".concat(0, "px")) : "topright" === n ? (l.left = "100%", l.top = "".concat(0, "px"), l.marginLeft = "".concat(-o, "px"), l.border = "none", l.background = "none") : "left" === n ? (l.left = "".concat(0, "px"), l.top = "50%", l.marginTop = "-".concat(i, "px")) : "bottom" === n ? (l.left = "50%", l.bottom = "".concat(0, "px"), l.marginLeft = "-".concat(i, "px")) : "right" === n && (l.right = "".concat(0, "px"), l.top = "50%", l.marginTop = "-".concat(i, "px"))), { style: { width: o, height: o, ...l } } }))(r || (r = (0, o.A)(["\n  pointer-events: auto;\n  border: solid 1px #ffffff;\n  padding: 6px 4px 4px 4px;\n  background: ", ";\n  color: ", ";\n  z-index: 10;\n  cursor: pointer;\n  text-align: center;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  :hover {\n    border-color: #000000;\n    background: #000000;\n    color: #ffffff;\n  }\n"])), (e => e.isOver ? "#000000" : "#3cd3c2"), (e => (e.isOver, "#ffffff")));
                i.Ay.div.attrs((e => { let { viewerAccess: t, bgShade: n, placement: r, position: a } = e; const o = "40px"; return { style: { flexAlign: "space-between", width: "40px", height: "100%", ...{ background: t ? "transparent" : "light" === n ? "rgba(242, 242, 242, 0.8)" : "rgba(0, 0, 0, 0.2)" }, ...(() => { switch (r) {
                                    case "left":
                                        return { left: 0, top: 0, height: "100%", width: o };
                                    case "right":
                                        return { right: 0, top: 0, height: "100%", width: o };
                                    case "top":
                                        return { left: 0, top: 0, width: "100%", height: o };
                                    case "bottom":
                                        return { bottom: 0, left: 0, width: "100%", height: o } } })() } } }))(a || (a = (0, o.A)(["\n  position: ", ";\n  pointer-events: auto;\n"])), (e => { let { position: t } = e; return t })) }, 52996: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r, a = n(57528),
                    o = n(52861); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)(["\n  pointer-events: bounding-box;\n  cursor: pointer;\n"]))) }, 34336: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(54161),
                    i = n(72119),
                    l = n(45256); const s = (0, i.Ay)(o.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { var t; let { theme: n, fill: r } = e; return "\n    fill: ".concat(r || n.palette.common.black, ";\n\n    -webkit-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n\n    font-family: ").concat(n.typography.fontFamily, ", ").concat(l.n[null === n || void 0 === n || null === (t = n.typography) || void 0 === t ? void 0 : t.fontFamily] || "sans-serif", ";\n\n    ::selection {\n        background: none;\n    }\n  ") })) }, 89015: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = n(72119).Ay.path(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { options: { style: t, thickness: n, color: r } } = e; return "\n  stroke: ".concat(r, ";\n  stroke-width: ").concat(n || 1, ";\n  stroke-dasharray: ").concat(function(e, t) { switch (e) {
                            case "solid":
                            default:
                                return "0";
                            case "dashed":
                                return "8 ".concat(2 * t);
                            case "dotted":
                                return "".concat(t) } }(t, n), ";\n  fill: none;\n  ") })) }, 2267: (e, t, n) => { "use strict";
                n.d(t, { A: () => c }); var r = n(57528),
                    a = n(72119),
                    o = n(70579); const i = e => { let { id: t, opacity: n = .1, fill: r = "rgb(0,0,0)", dx: a = 0, dy: i = 2, spread: l = 2 } = e; return (0, o.jsxs)("filter", { id: t, height: "160%", children: [(0, o.jsx)("feGaussianBlur", { in: "SourceAlpha", stdDeviation: l }), (0, o.jsx)("feOffset", { dx: a, dy: i, result: "offsetblur" }), (0, o.jsx)("feFlood", { floodColor: r, floodOpacity: n }), (0, o.jsx)("feComposite", { in2: "offsetblur", operator: "in" }), (0, o.jsxs)("feMerge", { children: [(0, o.jsx)("feMergeNode", {}), (0, o.jsx)("feMergeNode", { in: "SourceGraphic" })] })] }) }; var l; const s = a.Ay.svg(l || (l = (0, r.A)(["\n  overflow: visible;\n  position: absolute;\n  cursor: hand;\n"]))),
                    c = e => { let { children: t, innerRef: n, ...r } = e; return (0, o.jsxs)(s, { ...r, ref: n, children: [(0, o.jsxs)("defs", { children: [(0, o.jsx)(i, { id: "darkDropShadow" }), (0, o.jsx)(i, { id: "lightDropShadow", fill: "white", opacity: 1, spread: 3, dy: 0 })] }), t] }) } }, 52527: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a, o = n(57528),
                    i = n(72119); const l = (0, i.i7)(r || (r = (0, o.A)(["\n  0% {\n    transform: scale(0.1);\n    background: rgba(50, 50, 50, 0);\n  }\n\n  100% {\n    transform: scale(1);\n    background: rgba(50, 50, 50, 0.7);\n  }\n"]))),
                    s = i.Ay.div(a || (a = (0, o.A)(["\n  cursor: default;\n  min-height: ", "px;\n  animation: ", " ", " linear 1;\n  background: rgba(50, 50, 50, 0.7);\n  align-content: center;\n  justify-content: center;\n  text-align: center;\n  position: absolute;\n  ", "\n"])), 150, l, "0.2s", (e => { let { theme: t, width: n, height: r, left: a, top: o, borderRadius: i, showBorder: l, isRoleTypeDroppable: s } = e; return "\n    width: ".concat(n, "px;\n    height: ").concat(r, "px;\n    left: ").concat(a, "px;\n    top: ").concat(o, "px;\n    padding: 0px ").concat(t.spacing(6), "px 0px ").concat(t.spacing(6), "px;\n    border-radius: ").concat(i, ";\n    border: ").concat(l ? "2px dashed ".concat(s ? t.palette.secondary.main : t.palette.error.light) : "0px", ";\n  ") })) }, 9727: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(96364); const l = o.Ay.div(r || (r = (0, a.A)(["\n  text-align: center;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  ", " {\n    text-shadow: 0 0 4px #000000;\n  }\n"])), i.A) }, 58481: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(30105),
                    i = n(72119),
                    l = n(70579); const s = (0, i.Ay)((e => { let { flash: t, ...n } = e; return (0, l.jsx)(o.A, { ...n }) }))(r || (r = (0, a.A)(["\n  .MuiButton-label {\n    text-transform: none;\n  }\n  z-index: 1;\n  position: fixed;\n  left: 75px;\n"]))) }, 12987: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = n(72119).Ay.div(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { allowScroll: t, bgcolor: n } = e; return "\n  height:100%;\n  position:relative;\n  overflow: ".concat(t ? "auto" : "clip", ";\n  background-color: ").concat(n, ";\n  cursor: ").concat(t ? "grab" : "", ";\n  ") })) }, 79043: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(40454); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", ";\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  ", ";\n  height: 100%;\n"])), (e => { let { isPrintScreen: t } = e; return t ? "position: relative;" : "" }), (e => { let { isPrintScreen: t } = e; return t ? "" : "overflow: auto;" })) }, 78300: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(72119),
                    a = n(96962); const o = (0, r.Ay)(a.A)((e => { let { width: t = 40, height: n = 40, color: r, fontSize: a } = e; return { width: t + "px", height: n + "px", backgroundColor: r, color: "#fff", fontWeight: "600", fontSize: a ? a + "em" : n && n < 40 ? "1.1rem" : "1em" } })) }, 72835: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(30105),
                    i = n(72119),
                    l = n(70579); const s = (0, i.Ay)((e => { let { flash: t, ...n } = e; return (0, l.jsx)(o.A, { ...n }) }))(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { var t, n, r, a, o, i, l, s; let { theme: c, color: d, flash: u, variant: h, rotateicon: m = !1 } = e; return "\n    white-space:nowrap;\n    ".concat(!["primary", "secondary"].includes(d) && "contained" === h && "\n    background-color:".concat(null === (t = c.palette[d]) || void 0 === t ? void 0 : t.main, ";\n    color: ").concat(null === (n = c.palette[d]) || void 0 === n ? void 0 : n.contrastText, ";\n    &:hover{\n      background-color:").concat(null === (r = c.palette[d]) || void 0 === r ? void 0 : r.dark, ";\n      color: ").concat(null === (a = c.palette[d]) || void 0 === a ? void 0 : a.contrastText, ";\n    }") || "", "\n    ").concat(u && "outlined" === h ? "\n      &:hover{\n        background-color:".concat(null === (o = c.palette[d]) || void 0 === o ? void 0 : o.main, ";\n        color: ").concat(null === (i = c.palette[d]) || void 0 === i ? void 0 : i.contrastText, ";\n      }\n    ") : u && "\n      &:hover{\n        color:".concat(null === (l = c.palette[d]) || void 0 === l ? void 0 : l.main, ";\n        background-color: ").concat(null === (s = c.palette[d]) || void 0 === s ? void 0 : s.contrastText, ";\n      }\n    ") || "", "\n    .MuiButton-label {\n      text-transform:none;\n    }\n    .MuiButton-endIcon{\n      transition: transform 0.5s linear;\n      ").concat(m ? "transform: rotate(180deg);" : "", "\n    }\n\n  ") })) }, 76031: (e, t, n) => { "use strict";
                n.d(t, { A: () => c }); var r, a = n(57528),
                    o = n(72119),
                    i = n(35801),
                    l = n(70579); const s = (0, o.Ay)((e => { let { zIndexType: t, ...n } = e; return (0, l.jsx)(i.A, { ...n }) })).attrs((e => { let { maxWidth: t, zIndexType: n = "modal", theme: r } = e; return { style: { zIndex: r.zIndex[n] }, disableEnforceFocus: !0, maxWidth: t || !1 } }))(r || (r = (0, a.A)(["\n  .MuiDialog-paper {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n"]))),
                    c = e => (0, l.jsx)(s, { ...e, onClose: t => { if ("function" === typeof e.onClose) { if (null !== e && void 0 !== e && e.requestPending) return;
                                e.onClose(t) } } }) }, 25438: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(72119),
                    i = n(43867),
                    l = n(70579); const s = (0, o.Ay)((e => (0, l.jsx)(i.A, { ...e })))(r || (r = (0, a.A)(["\n  .MuiDialog-paper {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  ", "\n"])), (e => { let { width: t } = e; return "".concat(t ? "width: ".concat(t, ";") : "") })) }, 64021: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(53492),
                    a = n(61531),
                    o = n(96364),
                    i = n(72835),
                    l = n(70579); const s = e => { let { text: t, smallText: n = null, image: s, handleClick: c, buttonText: d, isButtonVisible: u, layout: h = "default", textVariant: m = "h4" } = e; return (0, l.jsxs)(l.Fragment, { children: ["default" === h && (0, l.jsxs)(a.A, { display: "flex", justifyContent: "center", flexDirection: "column", alignItems: "center", gridGap: 32, children: [(0, l.jsxs)(a.A, { children: [(0, l.jsx)(o.A, { variant: m, color: "textPrimary", children: t }), n && (0, l.jsx)(a.A, { mt: 1, children: (0, l.jsx)(o.A, { variant: "body2", color: "textPrimary", children: n }) })] }), s && (0, l.jsx)(a.A, { variant: "div", children: (0, l.jsx)(r.A, { width: 110, src: s }) }), u && (0, l.jsx)(a.A, { children: (0, l.jsx)(i.A, { variant: "contained", color: "primary", onClick: c, children: d }) })] }), "layout-1" === h && (0, l.jsxs)(a.A, { display: "flex", gridGap: 32, children: [(0, l.jsx)(a.A, { variant: "div", children: (0, l.jsx)(r.A, { width: 150, src: s }) }), (0, l.jsxs)(a.A, { display: "flex", flexDirection: "column", gridGap: 32, children: [(0, l.jsxs)(a.A, { children: [(0, l.jsx)(o.A, { variant: m, color: "textPrimary", children: t }), n && (0, l.jsx)(a.A, { mt: 1, children: (0, l.jsx)(o.A, { variant: "body2", color: "textPrimary", children: n }) })] }), u && (0, l.jsx)(a.A, { children: (0, l.jsx)(i.A, { variant: "contained", color: "primary", onClick: c, children: d }) })] })] })] }) } }, 49157: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(40454),
                    i = n(72119),
                    l = n(70579); const s = (0, i.Ay)((e => { let { direction: t, width: n, height: r, maxOnly: a, ...i } = e; return (0, l.jsx)(o.A, { ...i }) }))(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { width: t, height: n, direction: r = "hors", maxOnly: a } = e; return "\n  position: relative;\n  ".concat("hors" === r && "\n    height:100%;\n    ".concat(a && "\n      max-width:".concat(t, "px;\n    ") || "width:".concat(t, "px;"), "\n  ") || "width:100%;\n  ".concat(a && "\n    max-height:".concat(n, "px;\n  ") || "height:".concat(n, "px;"), "\n  "), "\n") })) }, 74593: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(30105),
                    i = n(72119); const l = (0, i.Ay)(o.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, size: n = "default" } = e; return "\n    width: ".concat(t.buttonSizes[n] || 180, "px;\n    .MuiButton-label {\n      text-transform:none;\n    }\n  ") })) }, 6904: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r, a = n(57528),
                    o = n(67467); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)(["\n  width: 100%;\n"]))) }, 8255: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(61531),
                    a = n(96364),
                    o = n(70579); const i = e => { let { children: t } = e; return (0, o.jsx)(r.A, { mb: 1, children: (0, o.jsx)(a.A, { variant: "caption", color: "error", align: "center", display: "block", children: t }) }) } }, 85725: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(40454); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  height: 100%;\n"]))) }, 85657: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(40454); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  position: relative;\n  max-height: 100%;\n  overflow: auto;\n  top: 0;\n  bottom: 0;\n"]))) }, 77454: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(40454); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n  position: relative;\n  height: 100%;\n"])), (e => { let { isPrintScreen: t } = e; return t ? "overflow:visible;" : "overflow-y:auto;" })) }, 20447: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(40454); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  position: relative;\n  overflow: auto;\n"]))) }, 86852: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(70579); const l = (0, o.Ay)((e => { let { variant: t, spacing: n, ...r } = e; const a = "".concat(t || "div"); return (0, i.jsx)(a, { ...r }) }))(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, spacing: n, variant: r } = e; return "\n    ".concat("span" === r ? "\n      display: inline-block;\n    " : "", "\n    ").concat(n.all && "\n      margin: ".concat(t.spacing(n.all), "px;\n    ") || "", "\n    ").concat(n.vertical && "\n      margin-top: ".concat(t.spacing(n.vertical), "px;\n      margin-bottom: ").concat(t.spacing(n.vertical), "px;\n      ") || "", "\n    ").concat(n.horizontal && "\n      margin-left: ".concat(t.spacing(n.horizontal), "px;\n      margin-right: ").concat(t.spacing(n.horizontal), "px;\n      ") || "", "\n    ").concat(n.left && "\n      margin-left: ".concat(t.spacing(n.left), "px;\n      ") || "", "\n    ").concat(n.right && "\n      margin-right: ".concat(t.spacing(n.right), "px;\n    ") || "", "\n    ").concat(n.top && "\n      margin-top: ".concat(t.spacing(n.top), "px;\n    ") || "", "\n    ").concat(n.bottom && "\n      margin-bottom: ".concat(t.spacing(n.bottom), "px;\n    ") || "", "\n    \n  ") })) }, 75156: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => c, gF: () => u, me: () => m }); var r = n(72119),
                    a = (n(65043), n(20965)),
                    o = n(70579); const i = { AiInsights: { icon: "fal fa-microchip-ai", from: "fa" }, History: { icon: "fal fa-history", from: "fa" }, Mobile: { icon: "fal fa-mobile", from: "fa" }, IntegrationConnection: { icon: "fa-solid fa-bolt", from: "fa" }, Important: { icon: "fal fa-exclamation-triangle", from: "fa" }, LastPage: { icon: "fal fa-arrow-to-right", from: "fa" }, Email: { icon: "fal fa-envelope", from: "fa" }, Pin: { icon: "fal fa-thumbtack", from: "fa" }, Target: { icon: "fal fa-file-search", from: "fa" }, Bold: { icon: "fal fa-bold", from: "fa" }, Italic: { icon: "fal fa-italic", from: "fa" }, Underline: { icon: "fal fa-underline", from: "fa" }, RemoveFormat: { icon: "fal fa-remove-format", from: "fa" }, Redo: { icon: "fal fa-redo", from: "fa" }, Undo: { icon: "fal fa-undo", from: "fa" }, GetApp: { icon: "fal fa-file-download", from: "fa" }, Publish: { icon: "fal fa-file-export", from: "fa" }, Upload: { icon: "fal fa-upload", from: "fa" }, Delete: { icon: "fal fa-trash-alt", from: "fa" }, Close: { icon: "fal fa-times", from: "fa" }, CloseCircle: { icon: "fa-solid fa-times-circle", from: "fa" }, CloseCircleRegular: { icon: "fa-regular fa-times-circle", from: "fa" }, CloseCircleLight: { icon: "fal fa-times-circle", from: "fa" }, FileCopy: { icon: "fal fa-clone", from: "fa" }, Copy: { icon: "fal fa-copy", from: "fa" }, File: { icon: "fal fa-file", from: "fa" }, FileArrowDown: { icon: "fal fa-file-arrow-down", from: "fa" }, Paste: { icon: "fal fa-paste", from: "fa" }, Doc: { icon: "fal fa-file-alt", from: "fa" }, Share: { icon: "fal fa-share-alt", from: "fa" }, PrintOutlined: { icon: "fal fa-print", from: "fa" }, Dashboard: { icon: "fal fa-home-alt", from: "fa" }, Chart: { icon: "fal fa-sitemap", from: "fa" }, ChartSolid: { icon: "fa-solid fa-sitemap", from: "fa" }, Recent: { icon: "fal fa-user-clock", from: "fa" }, Activity: { icon: "fal fa-poll-people", from: "fa" }, Reports: { icon: "fal fa-poll-h", from: "fa" }, Settings: { icon: "fal fa-cog", from: "fa" }, Directory: { icon: "fal fa-list-ul", from: "fa" }, Legend: { icon: "fal fa-map-signs", from: "fa" }, Photoboard: { icon: "fal fa-portrait", from: "fa" }, LinkedIn: { icon: "fab fa-linkedin", from: "fa" }, LinkedInInverse: { icon: "fab fa-linkedin-in", from: "fa" }, Camera: { icon: "fal fa-camera-alt", from: "fa" }, ZoomIn: { icon: "fal fa-search-plus", from: "fa" }, ZoomOut: { icon: "fal fa-search-minus", from: "fa" }, FullScreen: { icon: "fal fa-expand", from: "fa" }, FlipChart: { icon: "fal fa-sort", from: "fa" }, Levels: { icon: "fal fa-layer-group", from: "fa" }, ExpandDownCircle: { icon: "fa-solid fa-chevron-circle-down", from: "fa" }, ExpandDown: { icon: "fal fa-angle-down", from: "fa" }, ExpandUp: { icon: "fal fa-angle-up", from: "fa" }, ArrowRight: { icon: "fal fa-chevron-right", from: "fa" }, ArrowLeft: { icon: "fal fa-chevron-left", from: "fa" }, Fit: { icon: "fal fa-arrows-left-right-to-line", from: "fa" }, Theme: { icon: "fal fa-palette", from: "fa" }, Filter: { icon: "fal fa-filter", from: "fa" }, Sort: { icon: "fal fa-sort", from: "fa" }, Group: { icon: "fal fa-ball-pile", from: "fa" }, Edit: { icon: "fal fa-pencil", from: "fa" }, Add: { icon: "fal fa-plus", from: "fa" }, Minus: { icon: "fal fa-minus", from: "fa" }, Increment: { icon: "fa-solid fa-plus", from: "fa" }, Decrement: { icon: "fa-solid fa-minus", from: "fa" }, MinusSquare: { icon: "fal fa-minus-square", from: "fa" }, Check: { icon: "fal fa-check", from: "fa" }, CheckRegular: { icon: "fa-regular fa-check", from: "fa" }, CheckCircle: { icon: "fal fa-check-circle", from: "fa" }, CheckCircleSolid: { icon: "fa-solid fa-check-circle", from: "fa" }, FontCase: { icon: "fal fa-font-case", from: "fa" }, User: { icon: "fal fa-user", from: "fa" }, UserGroup: { icon: "fal fa-user-friends", from: "fa" }, Location: { icon: "fal fa-location", from: "fa" }, Shared: { icon: "fad fa-share-alt", from: "fa" }, Search: { icon: "fal fa-search", from: "fa" }, Loading: { icon: "fal fa-hourglass-half", from: "fa" }, Remove: { icon: "fal fa-trash-alt", from: "fa" }, Power: { icon: "fal fa-power-off", from: "fa" }, Phone: { icon: "fal fa-phone", from: "fa" }, PhoneSolid: { icon: "fa-solid fa-phone", from: "fa" }, Message: { icon: "fa-solid fa-comment", from: "fa" }, Mail: { icon: "fal fa-envelope", from: "fa" }, Favourite: { icon: "fal fa-star", from: "fa" }, FavouriteCircle: { icon: "fa-solid fa-circle", from: "fa" }, Help: { icon: "fal fa-question-circle", from: "fa" }, HelpSolid: { icon: "fa-solid fa-question-circle", from: "fa" }, Language: { icon: "fal fa-globe", from: "fa" }, Key: { icon: "fal fa-key", from: "fa" }, Lock: { icon: "fal fa-lock-alt", from: "fa" }, Unlock: { icon: "fal fa-lock-open-alt", from: "fa" }, LoadMore: { icon: "fal fa-arrow-alt-circle-down", from: "fa" }, AlignLeft: { icon: "fa-solid fa-align-left", from: "fa" }, AlignRight: { icon: "fa-solid fa-align-right", from: "fa" }, AlignCenter: { icon: "fa-solid fa-align-center", from: "fa" }, AlignJustify: { icon: "fa-solid fa-align-justify", from: "fa" }, StrikeThrough: { icon: "fal fa-strikethrough", from: "fa" }, Attach: { icon: "fal fa-paperclip", from: "fa" }, Play: { icon: "fal fa-play", from: "fa" }, PlayCircle: { icon: "fal fa-play-circle", from: "fa" }, Info: { icon: "fal fa-info-circle", from: "fa" }, InfoSolid: { icon: "fa-solid fa-info-circle", from: "fa" }, RoleInfo: { icon: "fal fa-user", from: "fa" }, Checkbox: { icon: "fal fa-check-square", from: "fa" }, CheckboxSolid: { icon: "fa-solid fa-check-square", from: "fa" }, NoCheckbox: { icon: "fal fa-times-square", from: "fa" }, NoCheckboxSolid: { icon: "fa-solid fa-times-square", from: "fa" }, Link: { icon: "fal fa-link", from: "fa" }, Unlink: { icon: "fal fa-unlink", from: "fa" }, Circle: { icon: "fa-solid fa-circle", from: "fa" }, CircleOutline: { icon: "fa-regular fa-circle", from: "fa" }, LightBulb: { icon: "fa-solid fa-lightbulb-on", from: "fa" }, Square: { icon: "fa-solid fa-square-full", from: "fa" }, ExclamationTriangle: { icon: "fal fa-exclamation-triangle", from: "fa" }, ExclamationCircle: { icon: "fal fa-exclamation-circle", from: "fa" }, EllipsesV: { icon: "fa-solid fa-ellipsis-v", from: "fa" }, Building: { icon: "fal fa-building", from: "fa" }, Sync: { icon: "fa-solid fa-sync-alt", from: "fa" }, ElipsisH: { icon: "fal fa-ellipsis-h", from: "fa" }, Spreadsheet: { icon: "fal fa-table", from: "fa" }, Wrench: { icon: "fal fa-wrench", from: "fa" }, ChartProject: { icon: "fal fa-project-diagram", from: "fa" }, CaretLeft: { icon: "fa-solid fa-caret-left", from: "fa" }, CaretRight: { icon: "fa-solid fa-caret-right", from: "fa" }, CaretDown: { icon: "fa-solid fa-caret-down", from: "fa" }, CaretUp: { icon: "fa-solid fa-caret-up", from: "fa" }, Home: { icon: "fa-solid fa-home-lg-alt", from: "fa" }, Pencil: { icon: "fa-solid fa-pencil", from: "fa" }, Download: { icon: "fa-solid fa-arrow-alt-to-bottom", from: "fa" }, DownloadOutline: { icon: "fal fa-download", from: "fa" }, ProjectDiagram: { icon: "fa-solid fa-project-diagram", from: "fa" }, TalentPool: { icon: "fal fa-people-arrows", from: "fa" }, AddUser: { icon: "fal fa-user-plus", from: "fa" }, AngleDown: { icon: "fa-solid fa-angle-down", from: "fa" }, LeftArrow: { icon: "fal fa-arrow-to-left", from: "fa" }, Bell: { icon: "fal fa-bell", from: "fa" }, Drag: { icon: "fal fa-arrows-alt", from: "fa" }, BreadCrumbConnector: { icon: "fal fa-angle-right", from: "fa" }, AngleRight: { icon: "fa-solid fa-angle-right", from: "fa" }, OneTime: { icon: "fa-solid fa-presentation", from: "fa" }, Onboarding: { icon: "fal fa-address-card", from: "fa" }, SalesAccounts: { icon: "fal fa-chart-line", from: "fa" }, PeoplePlanning: { icon: "fal fa-users-class", from: "fa" }, HR: { icon: "fal fa-people-arrows", from: "fa" }, Sales: { icon: "fal fa-user-chart", from: "fa" }, Recruiter: { icon: "fal fa-user-headset", from: "fa" }, IT: { icon: "fal fa-user-cog", from: "fa" }, Assistant: { icon: "fal fa-hands-helping", from: "fa" }, Consultant: { icon: "fal fa-id-card-alt", from: "fa" }, Education: { icon: "fal fa-graduation-cap", from: "fa" }, NonProfit: { icon: "fal fa-hand-holding-heart", from: "fa" }, RealEstate: { icon: "fal fa-home", from: "fa" }, HealthCare: { icon: "fal fa-plus-square", from: "fa" }, Manufacturing: { icon: "fal fa-industry-alt", from: "fa" }, Finance: { icon: "fal fa-comments-dollar", from: "fa" }, Government: { icon: "fal fa-landmark", from: "fa" }, MediaEntertainment: { icon: "fa-solid fa-photo-video", from: "fa" }, FoodBeverage: { icon: "fa-solid fa-utensils", from: "fa" }, Retail: { icon: "fa-solid fa-shopping-cart", from: "fa" }, Distribution: { icon: "fa-solid fa-truck-moving", from: "fa" }, Technology: { icon: "fa-solid fa-wifi", from: "fa" }, FilterExpand: { icon: "fal fa-expand-alt", from: "fa" }, FilterExpandRegular: { icon: "fa-regular fa-expand-alt", from: "fa" }, HidePassword: { icon: "fal fa-eye-slash", from: "fa" }, ShowPassword: { icon: "fal fa-eye", from: "fa" }, RemoveAccess: { icon: "fal fa-file-times", from: "fa" }, ColorInherit: { icon: "fa-solid fa-arrow-alt-square-up", from: "fa" }, Broom: { icon: "fa-regular fa-broom", from: "fa" }, HamburgerMenu: { icon: "fa-solid fa-bars", from: "fa" }, DragGrid: { icon: "fa-solid fa-grip-vertical", from: "fa" }, SortDown: { icon: "fal fa-sort-down", from: "fa" }, SortUp: { icon: "fal fa-sort-up", from: "fa" }, Pending: { icon: "fal fa-envelope-square", from: "fa" }, FindLocation: { icon: "fal fa-location", from: "fa" }, BusyCircle: { icon: "fal fa-circle-notch", from: "fa" }, Offline: { icon: "fal fa-power-off", from: "fa" }, ImportPeople: { icon: "fad fa-poll-people", from: "fa" }, ImportChart: { icon: "fad fa-sitemap", from: "fa" }, ImportRoles: { icon: "fad fa-users-slash", from: "fa" }, ImportContact: { icon: "fa-solid fa-question-circle", from: "fa" }, ImportPhotos: { icon: "fad fa-images", from: "fa" }, Anchor: { icon: "fal fa-anchor", from: "fa" }, AddCircle: { icon: "fal fa-plus-circle", from: "fa" }, Save: { icon: "fal fa-save", from: "fa" }, UserAdmin: { icon: "fal fa-user-shield", from: "fa" }, Upgrade: { icon: "fal fa-arrow-circle-up", from: "fa" }, Billing: { icon: "fal fa-file-invoice", from: "fa" }, Transfer: { icon: "fal fa-exchange-alt", from: "fa" }, NotFound: { icon: "fa-regular fa-times-circle", from: "fa" }, CircleUser: { icon: "fad fa-user-circle", from: "fa" }, Chair: { icon: "fal fa-chair-office", from: "fa" }, CheckCircleFilled: { icon: "fa-solid fa-check-circle", from: "fa" }, CharterFile: { icon: "fal fa-file-alt", from: "fa" }, Twitter: { icon: "fab fa-twitter", from: "fa" }, GridView: { icon: "fal fa-th-large", from: "fa" }, GridViewOutlined: { icon: "fal fa-th", from: "fa" }, ListView: { icon: "fal fa-list-ul", from: "fa" }, GridView2: { icon: "fa-light fa-grid-2", from: "fa" }, GridView3: { icon: "fa-light fa-grid-round-2", from: "fa" }, TableColumn: { icon: "fa-solid fa-columns", from: "fa" }, ExternalLink: { icon: "fa-solid fa-external-link", from: "fa" }, AlphabetsAsc: { icon: "fa-solid fa-sort-alpha-up", from: "fa" }, AlphabetsDsc: { icon: "fa-solid fa-sort-alpha-down", from: "fa" }, Business: { icon: "fal fa-briefcase", from: "fa" }, CSuite: { icon: "fal fa-users", from: "fa" }, Cross: { icon: "fal fa-times", from: "fa" }, SSO: { icon: "fa-light fa-user-lock", from: "fa" }, KeySolid: { icon: "fa-solid fa-key", from: "fa" }, HideColumn: { icon: "fa-solid fa-eye-slash", from: "fa" }, Previous: { icon: "fal fa-chevron-up", from: "fa" }, Next: { icon: "fal fa-chevron-down", from: "fa" }, Flag: { icon: "fa-solid fa-flag", from: "fa" }, Microsoft: { icon: "fa-solid fa-windows", from: "fa" }, GWorkspace: { icon: "fa-solid fa-google", from: "fa" }, Student: { icon: "fa-solid fa-graduation-cap", from: "fa" }, Database: { icon: "fa-solid fa-database", from: "fa" }, DatabaseLight: { icon: "fal fa-database", from: "fa" }, ManualBuild: { icon: "fal fa-shapes", from: "fa" }, OnboardingIntegration: { icon: "fal fa-bolt", from: "fa" }, OnboardingHelp: { icon: "fa-solid fa-envelope", from: "fa" }, OnboardingUnknown: { icon: "fal fa-question-circle", from: "fa" }, PlaySolid: { icon: "fa-solid fa-play", from: "fa" }, UserCircle: { icon: "fal fa-user-circle", from: "fa" }, UserRectangle: { icon: "fa-light fa-user", from: "fa" }, UserVacant: { icon: "fa-solid fa-user-slash", from: "fa" }, Salesforce: { icon: "fab fa-salesforce", from: "fa" }, ListSort: { icon: "fa-solid fa-sort-amount-down", from: "fa" }, ListSortUp: { icon: "fa-solid fa-sort-amount-up", from: "fa" }, UserRole: { icon: "fal fa-user-secret", from: "fa" }, AddressBook: { icon: "fal fa-address-book", from: "fa" }, FileTree: { icon: "fal fa-stream", from: "fa" }, TalentpoolDrag: { icon: "fa-solid fa-grip-vertical", from: "fa" }, CircleRight: { icon: "fal fa-chevron-circle-right", from: "fa" }, CircleLeft: { icon: "fal fa-chevron-circle-left", from: "fa" }, SquareUp: { icon: "fal fa-square-chevron-up", from: "fa" }, SquareDown: { icon: "fal fa-square-chevron-down", from: "fa" }, Baby: { icon: "fa-solid fa-baby", from: "fa" }, Employee: { icon: "fa-solid fa-head-side-brain", from: "fa" }, Manager: { icon: "fa-solid fa-user-tie", from: "fa" }, CSuiteSolid: { icon: "fa-solid fa-users-crown", from: "fa" }, InOffice: { icon: "fa-solid fa-building", from: "fa" }, Hybrid: { icon: "fa-solid fa-car-building", from: "fa" }, Remote: { icon: "fa-solid fa-home", from: "fa" }, Toggle: { icon: "fa-solid fa-toggle-on", from: "fa" }, Dollar: { icon: "fa-solid fa-dollar-sign", from: "fa" }, Date: { icon: "fa-solid fa-calendar-alt", from: "fa" }, Number: { icon: "fa-solid fa-hashtag", from: "fa" }, Marker: { icon: "fa-solid fa-map-marker-alt", from: "fa" }, Boolean: { icon: "fa-solid fa-check-square", from: "fa" }, PickList: { icon: "fa-solid fa-caret-circle-down", from: "fa" }, String: { icon: "fa-solid fa-font-case", from: "fa" }, Rollup: { icon: "fa-solid fa-arrow-up-right-dots", from: "fa" }, IconPicklist: { icon: "fa-solid fa-basket-shopping-plus", from: "fa" }, Url: { icon: "fa-solid fa-link", from: "fa" }, RichText: { icon: "fa-solid fa-align-left", from: "fa" }, Tags: { icon: "fa-solid fa-list-timeline", from: "fa" }, Switch: { icon: "fa-solid fa-toggle-on", from: "fa" }, Currency: { icon: "fa-solid fa-dollar", from: "fa" }, Computed: { icon: "fa-solid fa-display-chart-up", from: "fa" }, Attachment: { icon: "fa-solid fa-paperclip", from: "fa" }, FinancialServices: { icon: "fal fa-landmark", from: "fa" }, GovernmentAlt: { icon: "fal fa-landmark-alt", from: "fa" }, EssentialServices: { icon: "fal fa-hard-hat", from: "fa" }, Industrial: { icon: "fal fa-industry-alt", from: "fa" }, Transportation: { icon: "fal fa-plane", from: "fa" }, TechnologyAlt: { icon: "fal fa-tv", from: "fa" }, HealthcareAlt: { icon: "fal fa-briefcase-medical", from: "fa" }, Energy: { icon: "fal fa-wind-turbine", from: "fa" }, Consumer: { icon: "fal fa-shopping-cart", from: "fa" }, CustomFields: { icon: "fal fa-calculator", from: "fa" }, BoardOfDirectors: { icon: "fal fa-users-class", from: "fa" }, Badges: { icon: "fal fa-certificate", from: "fa" }, Collaboration: { icon: "fal fa-people-arrows", from: "fa" }, Integration: { icon: "fal fa-code-merge", from: "fa" }, ImportUpload: { icon: "fa-solid fa-cloud-arrow-up", from: "fa" }, CameraNew: { icon: "fa-sharp fa-solid fa-camera-retro", from: "fa" }, Person: { icon: "fa-thin fa-person", from: "fa" }, DrillDown: { icon: "far fa-bullseye", from: "fa" }, DiagonalArrows: { icon: "fal fa-up-right-and-down-left-from-center", from: "fa" }, UserEdit: { icon: "fa-light fa-user-pen", from: "fa" } },
                    l = { xs: "fa-xs", sm: "fa-sm", lg: "fa-lg", xl: "fa-xl", x2: "fa-2x", x3: "fa-3x", x4: "fa-4x", x5: "fa-5x" },
                    s = r.Ay.i.attrs((e => { let { color: t, icon: n, size: r = "sm", className: a, ...o } = e; return { style: { ...o.style || {}, color: t || "inherit" }, className: "".concat(i[n].icon, " ").concat(l[r], " ").concat(a || ""), ...o } }))((e => { let { icon: t, size: n = "sm", color: r, ...a } = e; const o = "".concat(i[t].icon, " ").concat(a.style.width ? "" : l[n]); return { style: { ...a.style || {}, color: r || "inherit" }, ...a.style || {}, className: o, ...a } })),
                    c = s,
                    d = { xxs: { width: "".concat((0, a.A)(8), " !important"), height: "".concat((0, a.A)(8), " !important"), fontSize: "".concat((0, a.A)(8)) }, xs: { width: "".concat((0, a.A)(12), " !important"), height: "".concat((0, a.A)(12), " !important"), fontSize: "".concat((0, a.A)(12)) }, sm: { width: "".concat((0, a.A)(16), " !important"), height: "".concat((0, a.A)(16), " !important"), fontSize: "".concat((0, a.A)(16)) }, lg: { width: "".concat((0, a.A)(20), " !important"), height: "".concat((0, a.A)(20), " !important"), fontSize: "".concat((0, a.A)(20)) }, xl: { width: "".concat((0, a.A)(24), " !important"), height: "".concat((0, a.A)(24), " !important"), fontSize: "".concat((0, a.A)(24)) }, x2: { width: "".concat((0, a.A)(28), " !important"), height: "".concat((0, a.A)(28), " !important"), fontSize: "".concat((0, a.A)(28)) } },
                    u = e => { let { icon: t, size: n = "xs", color: r, onClick: a, className: i } = e; const l = { ...d[n], display: "flex", alignItems: "center", justifyContent: "center" }; return "function" === typeof a && (l.cursor = "pointer"), (0, o.jsx)(s, { icon: t, color: r, style: l, onClick: a, className: i }) },
                    h = { solid: "fa-solid", light: "fal", sharp: "fa-sharp", brands: "fa-brands" },
                    m = e => { let { variant: t = "solid", name: n, size: r = "sm", color: a, className: i = "", fontSize: s = "", dataId: c = "", inlineSvg: d = !1, overrideStyles: u = {} } = e; if ("number" === typeof s && (s = "".concat(s, "px")), !h[t] || !l[r]) return (0, o.jsx)("i", {}); const m = { color: a || "inherit", ...u || {} }; let p = ""; return s ? m.width = s : p = l[r], d ? (0, o.jsx)("svg", { width: s, height: s, "data-id": c, className: "".concat(i, " ").concat(h[t], " fa-").concat(n, " ").concat(p), style: m }) : (0, o.jsx)("i", { "data-id": c, className: "".concat(i, " ").concat(h[t], " fa-").concat(n, " ").concat(p), style: m }) } }, 70318: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(17392); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { rotate: t } = e; return "\n  transition: transform 0.5s linear;\n  ".concat(t && "transform: rotate(180deg);", "\n  width:auto;\n") })) }, 80539: (e, t, n) => { "use strict";
                n.d(t, { A: () => y, u: () => g }); var r, a = n(57528),
                    o = n(25848),
                    i = n(78300),
                    l = n(75156),
                    s = n(13228),
                    c = n(68840),
                    d = n(61531),
                    u = n(9579),
                    h = n(72119),
                    m = n(70318),
                    p = n(3523),
                    f = n(70579); const v = (0, h.Ay)(d.A).attrs({ position: "absolute", fontSize: 14, color: "white", bgcolor: "grey.500", opacity: .8, borderRadius: "50%" })(r || (r = (0, a.A)([""]))),
                    g = e => { let { position: t = "br", handleClick: n } = e; const r = { bottom: 0, right: 0 }; return "br" === t && (r.bottom = 0, r.right = 5), (0, f.jsx)(v, { ...r, children: (0, f.jsx)(u.Ay, { title: "Change Photo", placement: "right", children: (0, f.jsx)(m.A, { size: "small", onClick: n, children: (0, f.jsx)(l.Ay, { icon: "Camera", color: "#ffffff" }) }) }) }) },
                    y = e => { let { name: t, avatarId: n, overrideColor: r = null, src: a, children: l, variant: d, shape: u, useImageWithCfPolicy: h = !0, ...m } = e; const v = (0, s.IM)(t || "Organimi"); let g = (0, s.uh)((0, s.s5)(t || "Organimi"));
                        g = (0, s.ap)(g), n && !a && (a = function(e) { try { return (0, c.Or)(e) || o } catch (t) { return console.log("ERROR getting avatar image: ", t), o } }(n)); const y = h ? (0, p.K7)(a) : a,
                            b = "square" === u || "fill" === u ? "square" : "oval" === u ? "rounded" : "circular"; return (0, f.jsx)(i.A, { ...m, variant: d || b, src: y, color: a && l ? "" : r || g, children: a ? l || v : v.toUpperCase() }) } }, 54229: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r, a = n(57528),
                    o = n(72119),
                    i = n(48655),
                    l = n(70579); const s = (0, o.Ay)((e => (0, l.jsx)(i.A, { variant: "outlined", ...e })))(r || (r = (0, a.A)(["\n  text-align: left;\n  input {\n    padding: 14px 12px;\n  }\n"]))) }, 172: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r, a = n(57528),
                    o = n(87603); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)([""]))) }, 27017: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(28269); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  min-width: 30px;\n"]))) }, 61071: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r, a = n(57528),
                    o = n(77325); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)([""]))) }, 53492: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = n(72119).Ay.img(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { ...t } = e; return "\n    height: ".concat(t.height + "px" || 0, ";\n    width: ").concat(t.width + "px" || 0, ";\n    max-width: 100%;\n    cursor: pointer;\n  ") })) }, 44676: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(49768); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)([""]))) }, 90318: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(70579); const l = (0, o.Ay)((e => { let { variant: t, spacing: n, ...r } = e; const a = "".concat(t || "div"); return (0, i.jsx)(a, { ...r }) }))(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, spacing: n, variant: r } = e; return "\n    ".concat("span" === r ? "\n      display: inline-block;\n    " : "", "\n    ").concat(n.all && "\n      padding: ".concat(t.spacing(n.all), "px;\n    ") || "", "\n    ").concat(n.vertical && "\n      padding-top: ".concat(t.spacing(n.vertical), "px;\n      padding-bottom: ").concat(t.spacing(n.vertical), "px;\n      ") || "", "\n    ").concat(n.horizontal && "\n      padding-left: ".concat(t.spacing(n.horizontal), "px;\n      padding-right: ").concat(t.spacing(n.horizontal), "px;\n      ") || "", "\n    ").concat(n.left && "\n      padding-left: ".concat(t.spacing(n.left), "px;\n      ") || "", "\n    ").concat(n.right && "\n      padding-right: ".concat(t.spacing(n.right), "px;\n    ") || "", "\n    ").concat(n.top && "\n      padding-top: ".concat(t.spacing(n.top), "px;\n    ") || "", "\n    ").concat(n.bottom && "\n      padding-bottom: ".concat(t.spacing(n.bottom), "px;\n    ") || "", "\n    \n  ") })) }, 79718: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(33843); const l = (0, o.Ay)(i.Ay)(r || (r = (0, a.A)([""]))) }, 43699: (e, t, n) => { "use strict";
                n.d(t, { A: () => c }); var r = n(61531),
                    a = n(77925),
                    o = n(96364),
                    i = n(53492),
                    l = n(28623),
                    s = n(70579);

                function c(e) { let { width: t, message: n, showLogo: c = !0 } = e; return (0, s.jsxs)(s.Fragment, { children: [c && (0, s.jsx)(r.A, { my: 3, children: (0, s.jsx)(i.A, { height: 70, src: l }) }), (0, s.jsx)(r.A, { width: "100%", maxWidth: t, children: (0, s.jsx)(a.A, { style: { height: 15 } }) }), (0, s.jsx)(r.A, { my: 3, children: (0, s.jsx)(o.A, { variant: "h5", children: n }) })] }) } }, 56579: (e, t, n) => { "use strict";
                n.d(t, { A: () => m }); var r, a, o = n(57528),
                    i = n(72119),
                    l = n(6904),
                    s = n(59548),
                    c = n(29829),
                    d = n(70579); const u = (0, i.Ay)(s.A).attrs((e => { let { theme: t, MenuProps: n = {} } = e; return { MenuProps: { ...n, style: { ...(null === n || void 0 === n ? void 0 : n.style) || {}, zIndex: t.zIndex.aboveModal } } } }))(r || (r = (0, o.A)(["\n  ", "\n  margin-top: 8px;\n  margin-bottom: 8px;\n  text-align: left;\n"])), (e => { let { size: t } = e; return "\n    ".concat("medium" === t ? "\n        .MuiSelect-select.MuiSelect-selectMenu.MuiInputBase-input {\n          padding: 16px 8px;\n        }" : "", "\n  ") })),
                    h = (0, i.Ay)(l.A)(a || (a = (0, o.A)(["\n  width: 100%;\n  label {\n    background: #ffffff;\n    margin-top: 6px;\n    padding: 4px 10px;\n  }\n"]))),
                    m = e => { let { labelId: t, label: n, variant: r = "outlined", size: a, ...o } = e; return (0, d.jsxs)(h, { variant: r, children: [(0, d.jsx)(c.A, { id: t, shrink: !0, children: n }), (0, d.jsx)(u, { labelId: t, ...o, size: a })] }) } }, 82244: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = n(72119).Ay.span(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, width: n, height: r, weight: a, color: o, orientation: i = "hors" } = e; return "\n  display: inline-block;\n  vertical-align: middle;\n  ".concat("vers" === i ? "\n      height:  ".concat(r ? "".concat(r, "px") : "100%", ";\n      width: 0;\n      border-left-width: ").concat(a, "px;\n      border-left-style: solid;\n      border-left-color: ").concat(o || t.palette.grey[400], ";\n    ") : "\n      width:  ".concat(n ? "".concat(n, "px") : "100%", ";\n      height:0;\n      border-bottom-width: ").concat(a, "px;\n      border-bottom-style: solid;\n      border-bottom-color: ").concat(o || t.palette.grey[400], ";"), "\n  ") })) }, 35033: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r, a = n(57528),
                    o = n(28814); const i = (0, n(72119).Ay)(o.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    border-bottom: solid 2px ".concat(t.palette.grey[400], ";\n  ") })) }, 45904: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(58425); const l = (0, o.Ay)(i.A).attrs((e => ({ ...e })))(r || (r = (0, a.A)([""]))) }, 94363: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(67503); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n\tmin-width: 100%;\n"]))) }, 77887: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(72703); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, highlight: n } = e; return "\n    ".concat(n && "\n      background: ".concat(t.palette.grey[100], ";\n      font-weight:600;\n    ") || "", "\n  ") })) }, 695: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(18885); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)([""]))) }, 96364: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r, a = n(57528),
                    o = n(72119),
                    i = n(66187),
                    l = n(65173),
                    s = n.n(l); const c = (0, o.Ay)(i.A).attrs((e => { let { fontSize: t, weight: n } = e; return { fontSize: t || "", weight: n || "" } }))(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, weight: n, color: r, fontSize: a, component: o } = e; return "\n    ".concat(n ? "font-weight: ".concat(t.typography.fontWeight[n], ";") : "", "\n    ").concat(r ? "color: ".concat(r, ";") : "", "\n    ").concat(a ? "font-size: ".concat(a, ";") : "", "\n    :hover{\n      ").concat("a" === o ? "text-decoration:underline" : "", "\n    }\n  ") }));
                c.propTypes = { weight: s().oneOf(["light", "regular", "medium", "bold"]), color: s().string, fontSize: s().string, component: s().string, theme: s().object }; const d = c }, 84866: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(48655); const l = (0, o.Ay)(i.A).attrs({ variant: "outlined", InputLabelProps: { shrink: !0 } })(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { size: t } = e; return "\n  margin-top: 8px;\n  margin-bottom: 8px;\n  text-align: left;\n  input {\n    background-color: #ffffff;\n    padding: ".concat("small" === t ? "10px" : "14px", " 12px;\n  }\n  .MuiOutlinedInput-input {\n    padding: ").concat("small" === t ? "10px" : "14.5px", ' 8px;\n  }\n  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {\n    padding: 4.5px 8px;\n  }\n') })) }, 96942: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => p }); var r, a, o = n(57528),
                    i = n(65043),
                    l = n(72119),
                    s = n(13228),
                    c = n(37294),
                    d = n(91812),
                    u = n(17392),
                    h = n(75156),
                    m = n(70579);
                l.Ay.div(r || (r = (0, o.A)(["\n  ", "\n"])), (e => { let { color: t } = e; return "\n  width: 26px;\n  height: 26px;\n  border-radius: 2px;\n  background: ".concat(t || "#ffffff", ";\n  border: solid 1px ").concat(c.Qs.Neutrals[500], ';\n  position: relative;\n  &:before {\n    content: "\u25be";\n    border-top:1px solid ').concat(c.Qs.Neutrals[500], ";\n    border-left:1px solid ").concat(c.Qs.Neutrals[500], ";\n    border-top-left-radius: 4px;\n    width:12px;\n    height:12px;\n    background: #ffffff;\n    position: absolute;\n    bottom: -1px;\n    right: -1px;\n    line-height: 10px;\n    padding-left:2px;\n    color: ").concat(c.Qs.Neutrals[900], ";\n    font-size: 12px;\n  }\n") })), l.Ay.div(a || (a = (0, o.A)(["\n  padding: 3px;\n  background: #fff;\n  border: solid 1px ", ";\n\n  cursor: pointer;\n"])), c.Qs.Neutrals[400]); const p = e => { var t; let { children: n, handleChangeAvatarBackground: r, initialColor: a, showClear: o } = e; let l = null; var c;
                    (null !== n && void 0 !== n && null !== (t = n.props) && void 0 !== t && t.name || !a) && (l = (0, s.ap)((0, s.uh)((0, s.s5)((null === n || void 0 === n || null === (c = n.props) || void 0 === c ? void 0 : c.name) || "Organimi")))); const [p, f] = (0, i.useState)(!1), [v, g] = (0, i.useState)("string" == typeof a ? a : l || "#000000"), [y, b] = (0, i.useState)(!1), w = p ? "simple-popover" : void 0; return (0, i.useEffect)((() => { y && r(v) }), [v]), (0, m.jsxs)(m.Fragment, { children: [(0, m.jsx)("div", { onClick: () => { f(!0) }, "aria-describedby": w, children: n }), (0, m.jsx)(d.A, { initialColor: v, onChange: e => { b(!0), g(e) } }), o && v && "#ffffff" !== v && (0, m.jsx)(u.A, { size: "small", onClick: () => { b(!0), g(null) }, children: (0, m.jsx)(h.Ay, { icon: "Close", size: "sm" }) })] }) } }, 3879: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = (0, n(72119).Ay)("div")(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n border: 1px solid ".concat(t.palette.primary.main, ";\n padding:").concat(t.spacing(2), "px;\n width:100%;\n min-height:250px;\n") })) }, 38872: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(749); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, linkedin: n } = e; return "\n &:hover{border: 1px solid ".concat(t.palette.primary.main, ";}\n padding:").concat(t.spacing(2), "px;\n margin:").concat(t.spacing(1), "px;\n ").concat(!n ? "min-height:180px;" : "\n      min-height:240px;\n    ", "\n width: 216px;\n overflow:auto;\n display:flex;\n flex-direction:column;\n justify-content: flex-start;\n align-items:center;\n\n") })) }, 1997: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r, a = n(57528); const o = (0, n(72119).Ay)("div")(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n width: 100%; \n border: 1px dashed ".concat(t.palette.primary.main, ";\n padding:").concat(t.spacing(2), "px;\n min-height:250px;\n height:100%;\n .uploadGrid{\n   min-height:100%;\n }\n") })) }, 82945: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(61531),
                    a = n(90539),
                    o = n(70579); const i = e => { let { size: t = 24, icon: n, color: i, variant: l = "light", margin: s, shape: c = "circle" } = e; const d = t,
                        u = t,
                        h = "light" === l ? "#ffffff" : "#000000"; let m = 0; return "circle" === c && (m = "50%"), (0, o.jsx)(r.A, { bgcolor: i, textAlign: "center", width: d, height: u, display: "flex", justifyContent: "center", alignItems: "center", margin: s, style: { color: h }, borderRadius: m, children: (0, o.jsx)(a.A, { color: "inherit", style: { fontSize: t }, children: n }) }) } }, 24657: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => g, rU: () => p, uF: () => f }); var r, a = n(57528),
                    o = n(65043),
                    i = n(52996),
                    l = n(34336),
                    s = n(42197),
                    c = n(72119),
                    d = n(23993),
                    u = n(14556),
                    h = n(70579); const m = { verticalAnchor: "start", textAnchor: "middle", fill: "gray" },
                    p = 240,
                    f = 30,
                    v = (0, c.Ay)(i.A)(r || (r = (0, a.A)(["\n  .buttonRect {\n    transition-duration: 250ms;\n  }\n  .buttonRect :hover {\n    fill: #7bbdf5;\n    stroke: #035fa2;\n  }\n  pointer-events: fill;\n  cursor: pointer;\n"]))),
                    g = e => { let { left: t = 0, top: n = 0, label: r = "Add Header", handleClick: a } = e; const i = (0, o.useRef)(),
                            c = (0, u.d4)(d.UX); return (0, o.useEffect)((() => { const e = i.current; if (e) { const t = e.getBBox().height;
                                e.setAttribute("y", (f - t / 2) / 2) } })), (0, h.jsxs)(v, { left: t, top: n, onClick: c ? a : () => {}, children: [(0, h.jsx)("rect", { width: p, height: f, stroke: s.A.palette.default.main, fill: "white", rx: 5, className: "buttonRect" }), (0, h.jsx)(l.A, { ...m, width: p, x: p / 2, y: 0, innerRef: i, children: (c ? "Add " : "") + r })] }, r + String(c)) } }, 85510: (e, t, n) => { "use strict";
                n.d(t, { A: () => d, r: () => s }); var r = n(34336),
                    a = n(52996),
                    o = n(65043),
                    i = n(42197),
                    l = n(70579); const s = 30,
                    c = { verticalAnchor: "start", textAnchor: "initial", fill: "gray" },
                    d = e => { let { handleClick: t, label: n, cellWidth: d, recursiveRoleCount: u = 0, cellHeight: h, isCellCollapsed: m } = e; const p = (0, o.useRef)(),
                            f = (0, o.useRef)(),
                            v = (0, o.useRef)(),
                            g = (0, o.useRef)(),
                            y = (0, o.useRef)(),
                            b = (0, o.useRef)(); return (0, o.useEffect)((() => { if (p.current && f.current) { const e = p.current.getBBox(),
                                    t = (null === e || void 0 === e ? void 0 : e.width) + 50,
                                    n = (d - t) / 2;
                                f.current.setAttribute("width", t), m ? (v.current.setAttribute("x", 0), v.current.setAttribute("transform", "translate(0,".concat(h / 2, ")"))) : v.current.setAttribute("transform", "translate(0,0)"); const r = (d - (null === e || void 0 === e ? void 0 : e.width)) / 2; if (p.current.setAttribute("x", r), f.current.setAttribute("x", n), g.current && b.current && y.current) { const e = y.current.getBBox();
                                    g.current.setAttribute("x", n + t - 12), b.current.setAttribute("x", n + t - 12), y.current.setAttribute("x", n + t - e.width / 2), y.current.setAttribute("y", e.height / 4) } } })), (0, l.jsxs)(a.A, { innerRef: v, onClick: t, pointerEvents: "fill", cursor: "pointer", children: [(0, l.jsx)("rect", { ref: f, height: s, fill: "grey", rx: 10, ry: 10 }), (0, l.jsx)(r.A, { innerRef: p, ...c, y: 10, fill: "white", children: n }), u && (0, l.jsxs)(a.A, { innerRef: b, height: 16, width: 24, children: [(0, l.jsx)("rect", { ref: g, height: 16, width: 24, rx: 8, y: -8, fill: i.A.palette.secondary.main }), (0, l.jsx)(r.A, { fill: "black", innerRef: y, children: u })] })] }) } }, 12176: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(66187); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  width: 85%;\n"]))) }, 5387: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(87603); const l = (0, o.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  .MuiSvgIcon-root{\n    margin: 0 ".concat(t.spacing(2), "px;\n  }\n  &:hover,{\n    color: ").concat(t.palette.primary.main, ";\n  }\n") })) }, 393: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => d }); var r, a, o, i = n(57528),
                    l = n(72119),
                    s = n(20495),
                    c = n(70579);
                (0, l.Ay)("div")(r || (r = (0, i.A)(["\n  display: flex;\n  flex-direction: column;\n  height: 95%;\n  overflow-y: hidden;\n  .MuiTabs-root {\n    width: 100%;\n  }\n  .MuiTab-root {\n    min-width: 130px;\n    width: 50%;\n  }\n  .MuiTabs-flexContainer {\n    justify-content: space-between;\n  }\n\n  .MuiGrid-container {\n    min-height: 100%;\n    height: 100%;\n    overflow: hidden;\n  }\n  .profileBodyContainer {\n    position: relative;\n    overflow: hidden;\n  }\n  .profileCardDetails {\n    overflow: auto;\n    flex: 1;\n    width: 100%;\n    text-align: left;\n  }\n"]))), (0, l.Ay)(s.A)(a || (a = (0, i.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n\t\tbackground-color: ".concat(t.palette.primary.main, ";\n\t\tborder-bottom-left-radius: 0px;\n\t\tborder-bottom-right-radius: 0px;\n\t\tcolor: ").concat(t.palette.common.white, ";\n\t\tpadding: ").concat(t.spacing(1), "px ").concat(t.spacing(3), "px;\n\t\t") })); const d = (0, l.Ay)((e => { let { top: t, appHeaderHeight: n, zIndexType: r, narrow: a, width: o, right: i, ...l } = e; return (0, c.jsx)(s.A, { ...l }) }))(o || (o = (0, i.A)(["\n  ", "\n"])), (e => { let { theme: t, width: n, right: r, zIndexType: a = "modal", top: o, appHeaderHeight: i } = e; return "\n\tbackground-color: ".concat(t.palette.common.white, ";\n\tborder: 1px solid ").concat(t.palette.grey[400], ";\n\twidth: ").concat(n || 450, "px;\n  display:flex;\n  height: calc(100% - ").concat(i || 0, "px);\n  position:absolute;\n  right:0;\n  top: ").concat(o || 0, "px;\n\tbottom:0;\n\tflex-direction: column;\n\tpadding: 0;\n  animation-duration: .5s;\n  // animation-name: slidein;\n  z-index:").concat(t.zIndex[a], ";\n\t@keyframes slidein {\n\t\tfrom {\n\t\t  right: -600px;\n\t\t}\n\t\tto {\n\t\t  right:").concat(r || 0, "px;\n\t\t}\n    }\n    .fullHeight{\n      height: 100%;\n    }\n  ") })) }, 46615: (e, t, n) => { "use strict";
                n.d(t, { A: () => l }); var r, a = n(57528),
                    o = n(72119),
                    i = n(35801); const l = (0, o.Ay)(i.A).attrs({ disableEnforceFocus: !0, maxWidth: !1 })(r || (r = (0, a.A)(["\n  ", "\n"])), (() => "\n    .MuiDialog-paper{\n      min-height:500px;\n    }\n  ")) }, 56070: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r, a = n(57528),
                    o = n(72119),
                    i = n(48655),
                    l = n(99229),
                    s = n(75156),
                    c = n(70579); const d = (0, o.Ay)((e => { var t; let { showDropdown: n, isDropdownOpen: r, handleDropdownClick: a, handleClear: o, hideTags: d, query: u, handleDropdownCloseClick: h, ...m } = e; return (0, c.jsx)(i.A, { variant: "outlined", ...m, InputProps: { ...m.InputProps, startAdornment: (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(l.A, { position: "start", children: (0, c.jsx)(s.Ay, { icon: "Search" }) }), !d && (null === (t = m.InputProps) || void 0 === t ? void 0 : t.startAdornment)] }), endAdornment: (0, c.jsxs)(c.Fragment, { children: [u && (null === u || void 0 === u ? void 0 : u.length) > 0 && (0, c.jsx)(c.Fragment, { children: (0, c.jsx)(l.A, { position: "end", onClick: o || void 0, children: (0, c.jsx)(s.Ay, { icon: "Close", style: { cursor: "pointer" } }) }) }), n && !r && (0, c.jsx)(l.A, { position: "end", onClick: a || void 0, children: (0, c.jsx)(s.Ay, { size: "md", icon: "SortDown", style: { cursor: "pointer", marginBottom: "8px" } }) }), n && r && (0, c.jsx)(l.A, { position: "end", onClick: h || void 0, children: (0, c.jsx)(s.Ay, { size: "md", icon: "SortUp", style: { cursor: "pointer", marginTop: "8px" } }) })] }) }, InputLabelProps: { shrink: !0 } }) }))(r || (r = (0, a.A)(['\n  background-color: #ffffff;\n  margin-top: 8px;\n  margin-bottom: 8px;\n  text-align: left;\n  .MuiInputBase-input {\n    padding: 10.5px 4px;\n  }\n  .MuiOutlinedInput-adornedEnd {\n    padding-right: 8px;\n  }\n  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {\n    padding: 4.5px 8px;\n  }\n']))) }, 46392: (e, t, n) => { "use strict";
                n.d(t, { A: () => u }); var r = n(84373),
                    a = n(8713),
                    o = n(80539),
                    i = n(61071),
                    l = n(96364),
                    s = n(78396),
                    c = n(172),
                    d = n(70579); const u = e => { let { ListItemEl: t = c.A } = e; return (0, d.jsxs)(t, { children: [(0, d.jsx)(a.A, { children: (0, d.jsx)(r.A, { variant: "circle", children: (0, d.jsx)(o.A, { name: "Organimi" }) }) }), (0, d.jsxs)(i.A, { children: [(0, d.jsx)(r.A, { children: (0, d.jsx)(l.A, { variant: "body1", children: "Full Name" }) }), (0, d.jsx)(r.A, { children: (0, d.jsx)(l.A, { variant: "body1", style: { width: s.Ay.dialogs.talentPoolCardWidth - 100, overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }, children: "Member Role" }) })] })] }) } }, 91693: (e, t, n) => { "use strict";
                n.d(t, { A: () => p }); var r, a = n(57528),
                    o = n(72119),
                    i = n(96446),
                    l = n(96364),
                    s = n(70318),
                    c = n(75156),
                    d = n(80539),
                    u = n(96962),
                    h = n(70579); const m = o.Ay.div(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, selected: n } = e; return "\n  padding:8px;\n  position:relative;\n  width: 100%;\n  border:1px solid ".concat(t.palette.grey[200], ";\n  display:flex;\n  flex-direction:column;\n  align-items:center;\n  justify-content:center;\n  cursor:pointer;\n  ").concat(n && "\n    border:1px solid ".concat(t.palette.secondary.main, ";\n  ") || "", "\n  &:hover{border:1px solid ").concat(t.palette.secondary.main, ";}\n  .icon-selected{\n    position:absolute;\n    right:0px;\n    top:0px;\n  }\n  .icon-upload{\n    position:absolute;\n    right:-42px;\n    bottom:-14px;\n  }\n  ") })),
                    p = e => { let { image: t, id: n, handleClick: r, handleEdit: a, handleSearch: o, selected: p, isNew: f, isSearch: v, label: g, variant: y } = e; return (0, h.jsxs)(i.A, { children: [(0, h.jsx)(m, { selected: p, children: "initials" === n ? (0, h.jsx)("span", { onClick: r(n), children: (0, h.jsx)(d.A, { width: 35, height: 35, name: "A B", variant: y }) }) : f ? (0, h.jsx)(h.Fragment, { children: (0, h.jsx)(u.A, { style: { backgroundColor: "white" }, width: 35, height: 35, onClick: a, variant: y, children: (0, h.jsx)(c.Ay, { icon: "Upload", size: "lg" }) }) }) : v ? (0, h.jsx)(h.Fragment, { children: (0, h.jsx)(s.A, { onClick: o, children: (0, h.jsx)(c.Ay, { icon: "Search" }) }) }) : (0, h.jsx)(u.A, { style: { width: 35, height: 35 }, src: t, onClick: r(n), variant: y }) }), g && (0, h.jsx)(l.A, { variant: "body2", align: "center", children: g })] }) } }, 76790: (e, t, n) => { "use strict";
                n.d(t, { A: () => i });
                n(65043); var r = n(51962),
                    a = n(95382),
                    o = n(70579); const i = e => (0, o.jsx)(r.A, { ...e, checkedIcon: (0, o.jsx)(a.A, {}) }) }, 91812: (e, t, n) => { "use strict";
                n.d(t, { A: () => b }); var r = n(65043),
                    a = n(96446),
                    o = n(85865),
                    i = n(39336),
                    l = n(67784),
                    s = n(42518),
                    c = n(81673),
                    d = n(20965),
                    u = n(37294),
                    h = n(32115),
                    m = n(68586),
                    p = n(70579); const f = ["#596980", "#606775", "#66666F", "#686868", "#6D6661", "#F23753", "#E93F8E", "#D43DED", "#9E4BF6", "#8052F5", "#585BEF", "#3477F5", "#109BE6", "#0AADCE", "#14AF9C", "#11B076", "#1FBD53", "#79C516", "#E7AA0C", "#F3940E", "#F86816", "#ED3C3C"],
                    v = {
                        [f[0]]: ["#F7F9FC", "#EFF3F8", "#DEE5EE", "#C4CFDD", "#8999AF", "#596980", "#3E4B5E", "#2D394B", "#1C2534", "#111725", "#060A17"], [f[1]]: ["#F8F9FA", "#F1F2F5", "#E1E3E8", "#CBCFD6", "#9199A6", "#606775", "#424B58", "#303947", "#1D2530", "#121723", "#070B13"], [f[2]]: ["#F9F9F9", "#F2F2F3", "#E0E0E3", "#CECED2", "#9797A0", "#66666F", "#484851", "#37373D", "#232325", "#17171A", "#0D0D0E"], [f[3]]: ["#F9F9F9", "#F3F3F3", "#E1E1E1", "#CECECE", "#999999", "#686868", "#484848", "#383838", "#222222", "#171717", "#0D0D0D"], [f[4]]: ["#F9F9F8", "#F3F3F2", "#E3E1E0", "#D0CDCB", "#9E9894", "#6D6661", "#4D4945", "#3C3835", "#252121", "#1A1817", "#0F0D0D"], [f[5]]: ["#FFEFF0", "#FFE0E2", "#FEC6CD", "#FD9AA6", "#FA667A", "#F23753", "#DD1B3F", "#B61335", "#951332", "#7D1430", "#430918"], [f[6]]: ["#FDF0F7", "#FCE3F1", "#FAC8E5", "#F89ECE", "#F267AD", "#E93F8E", "#D6236C", "#B61753", "#921744", "#78173B", "#460B21"], [f[7]]: ["#FDF2FF", "#F9E5FF", "#F3CAFE", "#EEA1FC", "#E56EF8", "#D43DED", "#B822CD", "#981AA6", "#7B1884", "#65196A", "#410845"], [f[8]]: ["#F9F3FF", "#F1E5FF", "#E6CFFF", "#D2ABFE", "#B879FC", "#9E4BF6", "#882DE7", "#731FC7", "#601E9E", "#4E1A7C", "#340B59"], [f[9]]: ["#F3F1FF", "#EAE6FE", "#D8D0FE", "#BCACFD", "#9D80F9", "#8052F5", "#7133EA", "#6224D4", "#511EAD", "#431B8A", "#29115A"], [f[10]]: ["#EBF0FF", "#DBE3FF", "#C0CCFE", "#9BABFC", "#7681F7", "#585BEF", "#463DE1", "#3B31C3", "#302A99", "#2B2976", "#1C1A42"], [f[11]]: ["#EDF5FF", "#D6E7FE", "#B7D6FE", "#88BDFD", "#559BF9", "#3477F5", "#2158E8", "#1B45D2", "#1C38A6", "#1C337F", "#17214A"], [f[12]]: ["#EEF8FF", "#DBF0FE", "#B1E2FD", "#72CDFC", "#31B5F7", "#109BE6", "#0679C0", "#075E97", "#0B4F7A", "#0F4163", "#0C2940"], [f[13]]: ["#E9FEFF", "#C8F9FE", "#9BF1FC", "#5CE5F8", "#1FCDEB", "#0AADCE", "#0C86A9", "#106985", "#15536A", "#164558", "#0C2D3C"], [f[14]]: ["#EEFDF9", "#C5FAEF", "#8EF5E0", "#53E7CE", "#28CEB7", "#14AF9C", "#0F897D", "#116B63", "#12534F", "#144541", "#082929"], [f[15]]: ["#E9FDF3", "#CBF9E1", "#9DF1CA", "#63E3AE", "#2ECD8E", "#11B076", "#098B5E", "#086D4D", "#0A543D", "#0A4534", "#06271F"], [f[16]]: ["#EEFDF2", "#D7FCE3", "#B3F6CA", "#7BEDA2", "#41D975", "#1FBD53", "#169941", "#157535", "#165A2E", "#144928", "#092916"], [f[17]]: ["#F6FEE3", "#E9FCC4", "#D4F892", "#B6F059", "#99E22F", "#79C516", "#5A990F", "#447111", "#375713", "#2F4914", "#192909"], [f[18]]: ["#FEFCE5", "#FEF8BB", "#FEEE7F", "#FDDB3E", "#F9C515", "#E7AA0C", "#C37F08", "#97570B", "#7A4410", "#663713", "#3A1D0A"], [f[19]]: ["#FFFAE8", "#FEF1C0", "#FDE27F", "#FCCD44", "#FAB721", "#F3940E", "#D46C0A", "#AB490D", "#873810", "#6D2F11", "#3C1907"], [f[20]]: ["#FFF6EA", "#FFEACF", "#FED1A0", "#FDB169", "#FA8735", "#F86816", "#E74E0F", "#BA390F", "#8F2E13", "#712813", "#3B140B"], [f[21]]: ["#FEF0F0", "#FEDEDE", "#FEC3C3", "#FC9B9B", "#F76666", "#ED3C3C", "#D72222", "#B01A1A", "#8E1A1A", "#741B1B", "#3C0D0D"] },
                    g = e => { let { color: t, selected: n, handleClick: r } = e; return (0, p.jsx)(a.A, { width: (0, d.A)(16), height: (0, d.A)(16), borderRadius: n ? 0 : (0, d.A)(2), border: "1px solid ".concat(u.Qs.Neutrals[500]), display: "flex", sx: { cursor: "pointer", transition: "border 0.2s" }, onClick: () => r(t), children: (0, p.jsx)(a.A, { width: "100%", height: "100%", bgcolor: t, border: n ? "1px solid white" : "", sx: { transition: "border 0.2s" } }) }) },
                    y = Object.entries(v).reduce(((e, t) => { let [n, r] = t; return r.forEach((t => { e[t] = n })), e }), {}),
                    b = e => { let { initialColor: t, onChange: n } = e; const [b, w] = (0, r.useState)(t), [z, x] = (0, r.useState)(null), A = Boolean(z);
                        (0, r.useEffect)((() => { w(t) }), [t]); const k = e => { w(e) },
                            S = e => { if (z) return w(t), void x(null);
                                x(e.currentTarget) },
                            M = y[b],
                            E = v[M] || v[M],
                            C = (0, r.useMemo)((() => !/^#(?:[0-9a-f]{3}|[0-9a-f]{6})$/i.test(b)), [b]); return (0, p.jsx)(m.A, { swatchElement: (0, p.jsx)(a.A, { width: "100%", height: "100%", bgcolor: b }), popperElement: (0, p.jsxs)(a.A, { width: (0, d.A)(267), height: (0, d.A)(242), padding: (0, d.A)(18), display: "flex", flexDirection: "column", border: "1px solid ".concat(u.Qs.Neutrals[400]), borderRadius: "0 ".concat((0, d.A)(2), " ").concat((0, d.A)(2), " ").concat((0, d.A)(2)), gap: (0, d.A)(20), children: [(0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", justifyContent: "space-between", flex: 1, children: [(0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", gap: (0, d.A)(10), children: [(0, p.jsx)(o.A, { variant: h.Eq.subheadingXS, children: "Base colors" }), (0, p.jsx)(a.A, { display: "flex", columnGap: (0, d.A)(5), rowGap: (0, d.A)(5), flexWrap: "wrap", children: f.map((e => (0, p.jsx)(g, { color: e, selected: e === M, handleClick: k }, e))) })] }), (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", gap: (0, d.A)(6), children: [(0, p.jsx)(o.A, { variant: h.Eq.subheadingXS, children: "Shades" }), E ? (0, p.jsx)(a.A, { display: "flex", columnGap: (0, d.A)(5), rowGap: (0, d.A)(5), flexWrap: "wrap", children: E.map((e => (0, p.jsx)(g, { color: e, selected: e === b, handleClick: k }, e))) }) : (0, p.jsx)(o.A, { variant: h.Eq.caption, children: "No shades available. Please select base color" })] })] }), (0, p.jsx)(i.A, {}), (0, p.jsxs)(a.A, { display: "flex", flexDirection: "column", justifyContent: "flex-end", children: [(0, p.jsxs)(a.A, { display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "flex-end", children: [(0, p.jsx)(l.A, { size: "small", variant: "outlined", value: b, onChange: e => k(e.target.value), InputProps: { startAdornment: (0, p.jsx)(a.A, { width: (0, d.A)(14), height: (0, d.A)(14), minWidth: (0, d.A)(14), maxWidth: (0, d.A)(14), border: "1px solid ".concat(u.Qs.Neutrals[0]), bgcolor: b }) }, error: C, label: "Custom" }), (0, p.jsxs)(a.A, { display: "flex", paddingBottom: (0, d.A)(3), gap: (0, d.A)(6), children: [(0, p.jsx)(s.A, { variant: "text", onClick: S, size: "xsmall", children: "Cancel" }), (0, p.jsx)(s.A, { variant: "contained", size: "xsmall", onClick: () => (e => { n(e), x(null) })(b), disabled: C, children: "Save" })] })] }), C && (0, p.jsx)(c.A, { error: C, children: "Invalid color" })] })] }), popperOpen: A, handleToggleSwatch: S, popperAnchorEl: z }) } }, 16528: (e, t, n) => { "use strict";
                n.d(t, { r: () => x }); var r = n(65043),
                    a = n(80286),
                    o = n(8122),
                    i = n(96446),
                    l = n(58168),
                    s = n(98587),
                    c = n(69292),
                    d = n(68606),
                    u = n(34535),
                    h = n(72876),
                    m = n(6803),
                    p = n(57056),
                    f = n(32400);

                function v(e) { return (0, f.Ay)("MuiIcon", e) }(0, p.A)("MuiIcon", ["root", "colorPrimary", "colorSecondary", "colorAction", "colorError", "colorDisabled", "fontSizeInherit", "fontSizeSmall", "fontSizeMedium", "fontSizeLarge"]); var g = n(70579); const y = ["baseClassName", "className", "color", "component", "fontSize"],
                    b = (0, u.Ay)("span", { name: "MuiIcon", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, "inherit" !== n.color && t["color".concat((0, m.A)(n.color))], t["fontSize".concat((0, m.A)(n.fontSize))]] } })((e => { let { theme: t, ownerState: n } = e; return { userSelect: "none", width: "1em", height: "1em", overflow: "hidden", display: "inline-block", textAlign: "center", flexShrink: 0, fontSize: { inherit: "inherit", small: t.typography.pxToRem(20), medium: t.typography.pxToRem(24), large: t.typography.pxToRem(36) } [n.fontSize], color: { primary: (t.vars || t).palette.primary.main, secondary: (t.vars || t).palette.secondary.main, info: (t.vars || t).palette.info.main, success: (t.vars || t).palette.success.main, warning: (t.vars || t).palette.warning.main, action: (t.vars || t).palette.action.active, error: (t.vars || t).palette.error.main, disabled: (t.vars || t).palette.action.disabled, inherit: void 0 } [n.color] } })),
                    w = r.forwardRef((function(e, t) { const n = (0, h.A)({ props: e, name: "MuiIcon" }),
                            { baseClassName: r = "material-icons", className: a, color: o = "inherit", component: i = "span", fontSize: u = "medium" } = n,
                            p = (0, s.default)(n, y),
                            f = (0, l.default)({}, n, { baseClassName: r, color: o, component: i, fontSize: u }),
                            w = (e => { const { color: t, fontSize: n, classes: r } = e, a = { root: ["root", "inherit" !== t && "color".concat((0, m.A)(t)), "fontSize".concat((0, m.A)(n))] }; return (0, d.A)(a, v, r) })(f); return (0, g.jsx)(b, (0, l.default)({ as: i, className: (0, c.A)(r, "notranslate", w.root, a), ownerState: f, "aria-hidden": !0, ref: t }, p)) }));
                w.muiName = "Icon"; const z = w,
                    x = e => { var t, n; let { icon: l, iconColor: s, iconVariant: c, width: d = 16, height: u = "auto", svg: h = !1, ...m } = e; const p = !(null === (t = a.Wb[l]) || void 0 === t || !t.path); return h && p ? (0, g.jsx)("image", { ...m, href: "".concat("https://assets-organimi.s3.amazonaws.com").concat(a.Wb[l].path), width: "".concat(d, "px"), height: "".concat(u, "px") }) : h && !p ? (0, r.createElement)(o.A, { ...m, className: "fa-".concat(c || (null === (f = a.Wb[l]) || void 0 === f ? void 0 : f.style) || "solid", " fa-").concat(l), viewBox: d ? "0 0 ".concat(d, " ").concat(d) : "0 0 24 24", width: "".concat(d, "px"), height: "".concat(d, "px"), key: "colorBadgeIcon-".concat(l, "-").concat(s) }) : p ? (0, g.jsx)(i.A, { children: (0, g.jsx)("img", { ...m, src: "".concat("https://assets-organimi.s3.amazonaws.com").concat(a.Wb[l].path), width: d, height: u }) }, "".concat(s, "-").concat(l, "-wrapper")) : (0, g.jsx)(i.A, { children: (0, g.jsx)(z, { ...m, className: "fa-".concat((null === (n = a.Wb[l]) || void 0 === n ? void 0 : n.style) || "solid", " fa-").concat(l) }) }, "".concat(s, "-").concat(l, "-wrapper")); var f } }, 49194: (e, t, n) => { "use strict";
                n.d(t, { A: () => m }); var r = n(75156),
                    a = n(96446),
                    o = n(85865),
                    i = n(65043),
                    l = n(18858),
                    s = n(93865),
                    c = n(32115),
                    d = n(70579),
                    u = function(e) { return e.GenericListItem = "dragme", e }(u || {}); const h = e => { let { item: t, renderItem: n, handleItemDrop: r, itemIndex: a, type: o } = e; const [c, h] = (0, i.useState)(!1), [, m] = (0, l.i)({ item: { type: o || u.GenericListItem, ...t, itemIndex: a }, collect: e => ({ isDragging: e.isDragging(), handlerId: e.getHandlerId() }), end() {} }), [{ isOver: p, overSelf: f, overItemIndex: v }, g] = (0, s.H)({ accept: [o || u.GenericListItem], drop(e) { r(a, null === e || void 0 === e ? void 0 : e.itemIndex, null === e || void 0 === e ? void 0 : e.folder, null === t || void 0 === t ? void 0 : t.folder, e), h(!0) }, collect(e) { const t = e.getItem(); return { isOver: e.isOver(), overSelf: (null === t || void 0 === t ? void 0 : t.itemIndex) === a, overItemIndex: null === t || void 0 === t ? void 0 : t.itemIndex } }, canDrop: () => !0 }); return m(g((0, d.jsx)("div", { className: "draggable-item ".concat(p && (v >= a ? "hovered-above" : "hovered-below"), " ").concat(c ? "dropped" : ""), children: n(t, a, { isHoveredFromAbove: p && !f && v > a, isHoveredFromBelow: p && !f && v < a }) }))) },
