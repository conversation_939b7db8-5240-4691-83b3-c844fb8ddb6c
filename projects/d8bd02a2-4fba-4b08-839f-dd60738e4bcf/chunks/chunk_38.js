                    l = n(23993); const s = () => { const e = (0, r.wA)(),
                        t = (0, r.d4)(a.xy),
                        n = (0, r.d4)(a.kT),
                        s = (0, r.d4)(l.Xp),
                        c = (0, r.d4)(a.ez),
                        { resetPageReady: d, updatePageReady: u } = (0, o.A)(),
                        h = (0, r.d4)(a.Vq),
                        { pageCount: m } = n === i._6.CHART ? s : { pageCount: h || 1 }; return { printSettings: t, updateSetup: t => { e((0, a.L$)(t)) }, updateChart: t => { e((0, a.gb)(t)) }, updateLegend: t => { e((0, a.$o)(t)) }, updateHeaderFooter: t => { e((0, a.jz)(t)) }, updateResource: t => { e((0, a.mh)(t)) }, updateThemeId: t => { e((0, a.j$)(t)) }, nextPage: () => { d(), e((0, a.IR)(Math.min(c + 1, m))), u() }, prevPage: () => { d(), e((0, a.IR)(Math.max(c - 1, 1))), u() }, setPageNumber: t => { d(), e((0, a.IR)(t)), u() }, resetPageNumber: () => { d(), e((0, a.IR)(1)), u() }, pageCount: m } } }, 57546: (e, t, n) => { "use strict";
                n.d(t, { A: () => i, _: () => l }); var r = n(22908),
                    a = n.n(r),
                    o = n(91688);

                function i() { const { search: e } = (0, o.useLocation)(); return a().parse(e, { ignoreQueryPrefix: !0 }) }

                function l() { window.history.replaceState({}, document.title, window.location.pathname) } }, 65025: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(65043),
                    a = n(27999),
                    o = n(86255); const i = () => { const { showVacantRolesState: e, groupByState: t, orderState: n, orderByState: i, selectedGroupState: l, queryState: s, advancedQueryState: c } = (0, r.useContext)(a.P), [d, u] = e, [h, m] = t, [p, f] = n, [v, g] = i, [y, b] = l, [w, z] = s, [x, A] = c, { eventDebounce: k } = (0, o.A)(), S = k((e => { var t; const n = null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value;
                        z(n) }), 400); return { showVacantRoles: d, selectedGroup: y, groupBy: h, order: p, orderBy: v, query: w, advancedQuery: x, handleAdvancedQueryChange: e => { A(e) }, handleQueryChange: S, handleQueryClear: () => { z("") }, handleShowVacantRoles: e => { u(e) }, handleGroupByChange: e => { if (e) { if (!d) { const t = { ...e || {} };
                                    m(t), b(null) } } else m(null), b(null) }, createSortByHandler: (e, t) => () => { const [n, r] = v;
                            f(n === t && r === e && "asc" === p ? "desc" : "asc"), g([t, e]) } } } }, 74656: (e, t, n) => { "use strict";
                n.d(t, { A: () => m }); var r = n(65043),
                    a = n(31312),
                    o = n(14556),
                    i = n(45418),
                    l = n(78396),
                    s = n(19367),
                    c = n(36138),
                    d = n(66856),
                    u = n(74079); const h = [l.mv.SINGLE, l.mv.SHARED, l.mv.LOCATION, l.mv.DEPARTMENT];

                function m(e) { const [t, n] = (0, r.useContext)(a.z), { params: m } = (0, c.u)([(0, d.si)()]), { resourceAction: p } = m || {}, f = p === l.uI.AI_INSIGHTS, v = (0, o.d4)(u.A)[e], g = (0, o.d4)((e => (0, i.Yk)(e, { ids: (null === v || void 0 === v ? void 0 : v.members) || [] }))), y = (0, o.d4)(s.G0), b = (0, o.d4)((t => { var n; return ((null === (n = t.organization) || void 0 === n ? void 0 : n.charts) || []).find((t => { var n; return (null === (n = t.alias) || void 0 === n ? void 0 : n.roleId) === e })) })), w = h.includes(null === v || void 0 === v ? void 0 : v.type); return { toggle: e => { var r, a, o;
                            e && e.stopPropagation(), e && e.preventDefault(); const i = {},
                                s = [l.mv.FUNCTION, l.mv.TEAM].includes(null === v || void 0 === v ? void 0 : v.type),
                                c = y === l.XD.MATRIX,
                                d = c && !(null !== v && void 0 !== v && v.parent),
                                u = { assignPerson: (null === v || void 0 === v || null === (r = v.members) || void 0 === r ? void 0 : r.length) || s, replaceWithVacant: !(null !== v && void 0 !== v && null !== (a = v.members) && void 0 !== a && a.length), mergeChart: f || "embedded" !== v.type || !v.embedded_chart, createNewManager: f || s || d, duplicateStructure: f || !c, moveRole: c, printFromHere: f || c, pinRole: f || c, saveAlias: f || b || !w || 0 === (null === v || void 0 === v || null === (o = v.children) || void 0 === o ? void 0 : o.length) || !(null !== v && void 0 !== v && v.parent), saveSubchart: f || c, generateLink: f };
                            null !== t && void 0 !== t && t.target ? n((e => ({ ...e, target: null }))) : n({ target: e.target, hiddenMoreOptions: u, disabledMoreOptions: i, role: v, members: g }) } } } }, 65453: (e, t, n) => { "use strict";
                n.d(t, { A: () => p }); var r = n(65043),
                    a = n(14556),
                    o = n(69219),
                    i = n(53109),
                    l = n(79091),
                    s = n(81635),
                    c = n(70669),
                    d = n(78396),
                    u = n(22264),
                    h = n(86255); const m = 10;

                function p() { const e = (0, a.wA)(),
                        t = (0, a.d4)(s.t0),
                        n = (0, a.d4)(l.tO),
                        p = (0, a.d4)(c.Q$),
                        f = (0, a.d4)(o.uo),
                        { eventDebounce: v } = (0, h.A)(),
                        { chartContainerSizeRef: g, chartContainerRef: y } = (0, u.A)(),
                        [b] = g.current || [],
                        w = f === d.uI.PRINT ? void 0 : v((() => { const t = null === y || void 0 === y ? void 0 : y.current;
                            (t => { const n = y.current; if (!n) return; const r = n.scrollWidth,
                                    a = n.offsetWidth / 2,
                                    o = Math.ceil(r / a),
                                    l = t / r,
                                    s = Math.floor(l * o);
                                e((0, i.yX)(s)) })(null === t || void 0 === t ? void 0 : t.scrollLeft) }), m);
                    (0, r.useEffect)((() => { const e = y.current; return e && "function" === typeof w && (e.addEventListener("scroll", w), w()),
                            function() { e && "function" === typeof w && e.removeEventListener("scroll", w) } }), [t, n, p]), (0, r.useEffect)((() => { "function" === typeof w && w() }), [b]) } }, 21773: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(40854),
                    a = n.n(r); const o = { ...n(87769).A, responseType: "arraybuffer" },
                    i = e => { let { buffer: t, fileType: n, fileName: r = "file" } = e; if (!t) return; const a = n ? { type: n } : {},
                            o = URL.createObjectURL(new Blob([t], a)),
                            i = document.createElement("a");
                        i.href = o, i.target = "blank", r && (i.download = r), i.click(), i.remove() },
                    l = e => { var t, n, r, a, o; const i = null === e || void 0 === e || null === (t = e.headers) || void 0 === t ? void 0 : t["content-disposition"]; let l = (null === (n = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/) || void 0 === n || null === (r = n.exec(i)) || void 0 === r || null === (a = r[1]) || void 0 === a ? void 0 : a.replace(/['"]/g, "")) || [];
                        l = decodeURIComponent(l); return { fileName: l, fileType: (null === (o = /[^.]+$/.exec(l)) || void 0 === o ? void 0 : o[0]) || "" } };

                function s() { return { downloadFile: e => { let { url: t, fileName: n, fileType: r, onError: s, isPublicDownload: c } = e; const d = c ? { responseType: "arraybuffer" } : o; return a().get(t, d).then((e => { const t = e.data; if (200 === e.status && e.data) { if (!n || !r) { const t = l(e);
                                        n = n || (null === t || void 0 === t ? void 0 : t.fileName), r = null === t || void 0 === t ? void 0 : t.fileType } return i({ buffer: t, fileType: r, fileName: n }) } })).catch((() => { "function" === typeof s && s() })) } } } }, 66588: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(54091);

                function o() { const e = (0, r.useRef)(),
                        [, t] = (0, r.useContext)(a.$); return { show: function(n, r) { let a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 3e3,
                                o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : { vertical: "bottom", horizontal: "left" };
                            e.current && clearTimeout(e.current), e.current = setTimeout((() => { n ? t({ open: !0, message: n, severity: r, duration: a, anchorOrigin: o }) : console.error("Message required to show snack") }), 100) } } } }, 75687: (e, t, n) => { "use strict";
                n.d(t, { U: () => l, s: () => s }); var r = n(65043),
                    a = n(14556),
                    o = n(86255),
                    i = n(1619); const l = e => { let { selectField: t } = e; const [n, a] = (0, r.useState)([]); return { selected: n, setSelected: a, isItemSelected: e => n.indexOf(e[t]) > -1, handleSelectAll: e => r => { r && r.stopPropagation(), n.length ? a([]) : a(e.map((e => e[t]))) }, handleSelectItem: e => r => { r && r.stopPropagation(); const o = n.indexOf(e[t]); let i = [...n]; - 1 === o ? i = i.concat(e[t]) : i.splice(o, 1), a(i) }, resetSelected: () => { a([]) } } },
                    s = e => { let { defaultValues: t, dataSelector: n, rawData: l = null, requestAsyncData: s, requestParams: c = {}, requestParamsGetter: d = (() => {}), isPaginated: u = !0 } = e; const h = (0, a.wA)(),
                            [m, p] = (0, r.useState)(t.groupBy),
                            [f, v] = (0, r.useState)(),
                            [g, y] = (0, r.useState)(!!s),
                            [b, w] = (0, r.useState)(""),
                            [z, x] = (0, r.useState)(t.filterBy || null),
                            [A, k] = (0, r.useState)(0),
                            [S, M] = (0, r.useState)(t.rowsPerPage || 10),
                            [E, C] = (0, r.useState)(t.order || "asc"),
                            [T, H] = (0, r.useState)(t.orderBy),
                            [L, I] = (0, r.useState)(0),
                            { eventDebounce: j } = (0, o.A)(),
                            V = { ...c || {}, groupBy: m, groupId: null === f || void 0 === f ? void 0 : f.id, page: A + 1, pageSize: S, sort: "".concat("asc" === E ? "-" : "").concat(T), search: b },
                            O = l || (0, a.d4)(n),
                            R = ("function" === typeof O ? O(V) : O) || [],
                            P = Math.ceil(R.length / S),
                            D = (e, t, n) => t[n] < e[n] ? -1 : t[n] > e[n] ? 1 : 0,
                            F = (0, r.useCallback)(((e, t) => "desc" === e ? (e, n) => D(e, n, t) : (e, n) => -D(e, n, t)), []),
                            N = j((e => { var t; const n = null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value;
                                w(n || "") }), s ? 1e3 : 0),
                            _ = e => { if (!b) return !0; const t = new RegExp(i.A.escapeRegExp(b), "i"); let n = !1; const r = Object.keys(e); for (let a = 0; a < r.length; a++)
                                    if (t.test(e[r[a]])) { n = !0; break } return n },
                            B = e => { if (null === z || !Object.keys(z).length) return !0; let t = !0; return Object.keys(z).forEach((n => { var r;
                                    t = t && z[n].includes((null === (r = e[n]) || void 0 === r ? void 0 : r.toString()) || "") })), t };
                        (0, r.useEffect)((() => { "function" === typeof s ? (async () => { y(!0); let e = { ...V }; if ("function" === typeof d) { const t = d();
                                    e = { ...e, ...t || {} } } const { error: t, payload: n } = await h(s(e)); if (y(!1), !t) { const { totalCount: e } = n;
                                    I(e) } })() : y(!1) }), [A, S, T, E, b, z]); const W = (0, r.useMemo)((() => { if (!s) { const e = [A * S, A * S + S]; let t = [...R];
                                    t = z ? t.filter(B) : t, t = b ? t.filter(_) : t; const n = t.sort(F(E, T || "")); return u ? Array.prototype.slice.apply(n, e) : n } return R }), [T, E, R, F, A, S, s, b, z]),
                            U = s ? L : (R || []).length; return { asyncLoading: g, getComparator: F, handleChangePage: (e, t) => { k(t) }, handleChangeRowsPerPage: e => { M(parseInt(e.target.value, 10)), k(0) }, handleSearch: N, handleFilter: e => { const t = { ...e };
                                Object.keys(t).filter((e => 0 === t[e].length)).forEach((e => delete t[e])), x(Object.keys(t || {}).length ? t : null) }, handleGroupByChange: e => { p(e) }, handleGroupSelected: e => { v(e) }, createSortHandler: e => () => { C(T === e && "asc" === E ? "desc" : "asc"), H(e) }, groupBy: m, filterBy: z, selectedGroup: f, order: E, orderBy: T, rows: W, page: A, totalPages: P, rowsPerPage: S, rawData: R, totalCount: U, data: R } } }, 20252: (e, t, n) => { "use strict";
                n.d(t, { A: () => y }); var r = n(65043),
                    a = n(14556),
                    o = n(91688),
                    i = n(66856),
                    l = n(53109),
                    s = n(94234),
                    c = n(56650),
                    d = n(89656),
                    u = n(42006),
                    h = n(64418),
                    m = n(36138),
                    p = n(84),
                    f = n(57546),
                    v = n(43940),
                    g = n(86597);

                function y(e) { let { sample: t } = e; const { selectedSetting: [n, y] } = (0, r.useContext)(c.y), { onTourEventComplete: b } = (0, v.M)({}), { openDialog: w } = (0, p.A)("createTheme"), z = { general: "General", roleSpecific: "Role Specific" }, x = [{ id: 1, slug: "setup", name: "All Themes", title: "Themes", subtitle: "Apply to Current Chart", area: "", hidden: !0 }, { id: 2, slug: "cards", name: "Cards", title: "Cards", area: z.general, anchorDataAttribute: "themes-bar-cards", icon: "table-layout" }, { id: 3, slug: "colors", name: "Colors", title: "Colors", area: z.general, icon: "palette" }, { id: 4, slug: "counts", name: "Counts", title: "Counts", area: z.general, icon: "square-3" }, { id: 5, slug: "fields", name: "Fields", title: "Display Fields", area: z.general, anchorDataAttribute: "themes-bar-fields", icon: "input-text" }, { id: 6, slug: "legend", name: "Legend", title: "Legend", area: z.general }, { id: 7, slug: "lines", name: "Lines", title: "Connecting Lines", area: z.general, icon: "scribble" }, { id: 8, slug: "photos", name: "Photos", title: "Photos", area: z.general, anchorDataAttribute: "themes-bar-photos", icon: "image-user" }, { id: 9, slug: "positions", name: "Positions", title: "Sorting & Stacking", area: z.general, icon: "grid-dividers" }, { id: 10, slug: "sharedRole", name: "Shared", title: "Shared Roles", value: "shared", area: z.roleSpecific, anchorDataAttribute: "themes-bar-shared-roles", icon: "users" }, { id: 11, slug: "assistantRole", name: "Assistant", title: "Assistant Roles", value: "assistant", area: z.roleSpecific, icon: "hands-helping" }, { id: 12, slug: "departmentRole", name: "Department", title: "Departments", value: "department", area: z.roleSpecific, icon: "screen-users" }, { id: 13, slug: "locationRole", name: "Location", title: "Locations", value: "location", area: z.roleSpecific, icon: "location-dot" }], A = (0, o.useParams)(), k = (0, f.A)(); let S = null === A || void 0 === A ? void 0 : A.resource; const M = null === A || void 0 === A ? void 0 : A.chartId;
                    S = S || ("string" === typeof(null === k || void 0 === k ? void 0 : k.resource) ? k.resource : "chart"); const E = (0, g.A)(),
                        C = null === E || void 0 === E ? void 0 : E.id,
                        T = (0, a.wA)(),
                        H = (0, a.d4)(d.P0),
                        L = (0, a.d4)(d.xu),
                        { confirmAction: I } = (0, h.A)(),
                        j = (0, a.d4)(u.VF),
                        V = (0, m.u)([(0, i.si)()]),
                        { params: O } = V || { params: { resource: "" } },
                        R = (0, o.useHistory)(),
                        P = (0, a.d4)(d.QD),
                        [D, F] = (0, r.useState)(!1),
                        N = e => async function(n, r) { let { applyTo: a, name: o } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                            n && n.stopPropagation(); let i = H; if (e && (i = L.find((t => t.id === e)) || H), !i) throw Error("theme not found");
                            F(!0), y("setup"); const { error: c, payload: d } = await T(s.ZO.create({ orgId: C, licenseId: null === j || void 0 === j ? void 0 : j.id, data: { ...i, name: o || "".concat(i.name, " Copy") } })); if (c || t) { if (!c && t) { var u;
                                    T((0, l.Kj)(null === d || void 0 === d || null === (u = d.theme) || void 0 === u ? void 0 : u.id)), y(r = r || "directory" === S && "table" || "chart" === S && "cards" || "photoboard" === S && "layout" || "setup"), F(!1) } } else { var h; const e = a === s.cD.makeDefault ? s.cD.applyToChartAndMakeDefault : s.cD.applyToChart,
                                    { error: t } = await T(s.ZO.setActive({ orgId: C, chartId: M, mode: e, themeId: null === d || void 0 === d || null === (h = d.theme) || void 0 === h ? void 0 : h.id }));
                                t || y(r = r || "directory" === S && "table" || "chart" === S && "cards" || "photoboard" === S && "layout" || "setup"), F(!1) } c || b({ event: "theme-created" }) }, _ = e => { w({ onSuccess: N(e || H.id), dashboardMode: t }) }, B = e => t => { t.stopPropagation(), I({ title: "Confirm deletion of theme", message: "This action is irreversible", execFunc: async () => { const t = L.find((t => t.id === e)); if (!t) throw new Error("Theme not found"); const { error: n } = await T(s.ZO.delete({ orgId: C, themeId: t.id }));
                                    n || y("setup") }, cancelFunc: () => {} }) }, W = B(H.id), U = async (e, t) => { e && e.stopPropagation(), y(t = t || "directory" === S && "table" || "chart" === S && "cards" || "photoboard" === S && "layout" || "cards") }, q = U, G = () => { if (C && O) { let e = O; "detailsPane" === S && (e = { ...O, resource: "chart" }), R.push((0, i.si)({ ...e, resourceAction: "view" })) } }; return { handleManualSave: () => { y("setup"), G() }, handleDuplicateCurrentTheme: _, handleEditCurrentTheme: q, handleThemeEdit: U, handleDeleteCurrentTheme: W, handleThemeDelete: B, handleSettingClick: e => t => { null === t || void 0 === t || t.stopPropagation(), e !== n && ("setup" !== n || D ? !D && y(e) : P ? _(void 0) : q(null, e)) }, handleCloseClick: G, handleSetActiveTheme: async (e, t) => await T(s.ZO.setActive({ orgId: C, chartId: M, themeId: e, mode: t || s.cD.applyToChart })), createStackSettingsHandler: e => t => { const n = "boolean" === typeof H.chartOptions[e] ? t.target.checked : t.target.value,
                                r = { ...H, chartOptions: { ...H.chartOptions } };
                            e in H.chartOptions && (r.chartOptions[e] = n, T(s.ZO.update({ orgId: C, themeId: H.id, data: r }))) }, copyDefaultTheme: async () => { const e = H;
                            F(!0); const { error: t, payload: { theme: { id: n } } } = await T(s.ZO.create({ orgId: C, licenseId: null === j || void 0 === j ? void 0 : j.id, data: { ...e, name: "Custom Theme ".concat(L.length) } }));
                            t || await T(s.ZO.setActive({ orgId: C, chartId: M, themeId: n, mode: s.cD.applyToChart })), F(!1) }, handleToggleRoleTypeOverride: e => async () => { const t = null === H || void 0 === H ? void 0 : H.roleTypeOverrides[e].enabled,
                                n = { ...H, roleTypeOverrides: { ...H.roleTypeOverrides, [e]: { ...(() => { switch (e) {
                                                    case "department":
                                                        { var n; const e = { displayType: "card", borderVisible: !1, borderColor: "#ddd" }; return t ? { fields: [], ...e } : { fields: null === H || void 0 === H || null === (n = H.data) || void 0 === n ? void 0 : n.chart, ...e } }
                                                    case "shared":
                                                    case "assistant":
                                                    case "location":
                                                        var r, a; return t ? { fields: [], photos: {} } : { fields: null === H || void 0 === H || null === (r = H.data) || void 0 === r ? void 0 : r.chart, photos: null === H || void 0 === H || null === (a = H.base) || void 0 === a ? void 0 : a.photos };
                                                    default:
                                                        return {} } })(), enabled: !H.roleTypeOverrides[e].enabled } } };
                            await T(s.ZO.update({ orgId: C, themeId: H.id, data: n })) }, onTitleUpdate: async e => await T(s.ZO.update({ orgId: C, themeId: H.id, data: { ...H, name: e } })), selectedSetting: n, setSelectedSetting: y, isCopying: D, chartSettings: x, settingTypes: { chart: "CHART", directory: "DIRECTORY", photoboard: "PHOTOBOARD", detailsPane: "DETAILS PANE" }, chartSettingAreas: z, directorySettings: [{ id: 1, slug: "setup", name: "All Themes", title: "Themes", subtitle: "Apply to Current Chart", area: "", hidden: !0 }, { id: 2, slug: "table", name: "Table", title: "Table", icon: "table" }, { id: 3, slug: "columns", name: "Columns", title: "Columns", icon: "input-text" }, { id: 4, slug: "photos", name: "Photos", title: "Photos", icon: "image-user" }, { id: 5, slug: "data", name: "Data", title: "Data", icon: "arrow-down-a-z" }], detailsPaneSettings: [{ id: 1, slug: "setup", name: "All Themes", title: "Themes", subtitle: "Apply to Current Chart", area: "", hidden: !0 }, { id: 2, slug: "layout", name: "Layout", title: "Layout", icon: "objects-column" }, { id: 3, slug: "banner", name: "Banner", title: "Banner", icon: "border-top" }, { id: 4, slug: "data", name: "Data", title: "Data", icon: "input-text" }, { id: 5, slug: "photos", name: "Photos", title: "Photos", icon: "image-user" }], photoboardSettings: [{ id: 1, slug: "setup", name: "All Themes", title: "Themes", subtitle: "Apply to Current Chart", area: "", hidden: !0 }, { id: 2, slug: "layout", name: "Layout", title: "Layout", icon: "objects-column" }, { id: 3, slug: "fields", name: "Fields", title: "Fields", icon: "input-text" }, { id: 4, slug: "photos", name: "Photos", title: "Photos", icon: "image-user" }, { id: 5, slug: "data", name: "Data", title: "Data", icon: "arrow-down-a-z" }] } } }, 80575: (e, t, n) => { "use strict";
                n.d(t, { A: () => a }); var r = n(65043); const a = e => { let { onSubmit: t, onComplete: n, onError: a, onCancel: o, shouldRetry: i, maxRetries: l, minDelay: s = 0, intervalTime: c = 500 } = e; const [d, u] = (0, r.useState)(!1), h = (0, r.useRef)(), m = (0, r.useRef)(l || 10), p = (0, r.useRef)(); const f = async () => { "function" === typeof o && await o(); let e = h.current;
                        e && clearTimeout(e), u(!1) }; return (0, r.useEffect)((() => { let e = !0; const r = async () => { try { if (d) { const o = Date.now(),
                                        l = t ? await t.apply(null, p.current) : await (async () => ({ payload: {}, error: null }))(),
                                        { payload: d, error: f } = l || {}; if (f && "function" !== typeof i) { let e = h.current;
                                        e && clearTimeout(e), u(!1), "function" === typeof a && a({ payload: d, error: f }) } else { const t = Date.now() - o;
                                        e && (h.current && clearTimeout(h.current), h.current = setTimeout((() => { if ("function" === typeof i) { const e = m.current,
                                                    t = i({ payload: d, error: f });
                                                t && e > 0 ? (m.current = (m.current || 1) - 1, r()) : t ? (u(!1), a({ payload: { message: "Server Timeout Error", type: "timeout" }, error: !0 })) : (u(!1), "function" === typeof n && n(d)) } else u(!1), "function" === typeof n && n(d) }), Math.max(s - t, c))) } } } catch (o) { let e = h.current;
                                e && clearTimeout(e), u(!1) } }; return r(), () => { e = !1 } }), [d]), (0, r.useEffect)((() => async () => { await f() }), []), [d, function() { p.current = arguments, u(!0) }, f] } }, 67264: (e, t, n) => { "use strict";
                n.d(t, { A: () => h }); var r = n(91688),
                    a = n(66856),
                    o = n(66588),
                    i = n(84),
                    l = n(14556),
                    s = n(53109),
                    c = n(43331),
                    d = n(23993),
                    u = n(22264); const h = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const t = (0, l.wA)(),
                        n = (0, r.useParams)(),
                        h = (0, r.useHistory)(),
                        { show: m } = (0, o.A)(),
                        { openDialog: p } = (0, i.A)("export2Dialog"),
                        { openDialog: f } = (0, i.A)("copyMove"),
                        { openDialog: v } = (0, i.A)("importFile"),
                        g = (0, l.d4)(d.vZ),
                        y = (0, u.A)(),
                        { actionCardRef: b } = y || {},
                        [w] = (null === b || void 0 === b ? void 0 : b.current) || []; return { handleButtonActionClick: r => function() { if ("function" === typeof e[r]) return e[r].apply(this, arguments); switch (r) {
                                case "import":
                                    v(); break;
                                case "share":
                                    h.push((0, a.BC)({ ...n, resource: "settings", activeTab: "users" })); break;
                                case "print":
                                    g ? h.push("".concat((0, a.Qo)({ ...n, base: "protected" }), "?topRole=").concat(g)) : w ? h.push("".concat((0, a.Qo)({ ...n, base: "protected" }), "?topRole=").concat(w)) : h.push((0, a.Qo)({ ...n, base: "protected" })); break;
                                case "export":
                                    p({ orgId: n.orgId, chartId: n.chartId }); break;
                                case "downloadPhotos":
                                    break;
                                case "fileCopy":
                                    f({ mode: "copy", chartId: n.chartId, orgId: n.orgId, selector: c.Ot }); break;
                                case "zoomIn":
                                    t((0, s.nF)()); break;
                                case "zoomOut":
                                    t((0, s.ke)()); break;
                                default:
                                    return m("Action not yet handled", "warning", 4e3) } } } } }, 48853: (e, t, n) => { "use strict";
                n.d(t, { A: () => s }); var r = n(65043),
                    a = n(14556),
                    o = n(15622),
                    i = n(78396),
                    l = n(42006);

                function s(e) { const t = (0, a.d4)(l.eW),
                        n = (0, a.d4)(o.A),
                        s = null === n || void 0 === n ? void 0 : n.accessLevel,
                        c = (0, r.useMemo)((() => "account" === e ? s : t), [e, t, s]); return { userHasMinAccess: e => (i.ZE[c] || 0) >= (i.ZE[e] || 0), userHasMaxAccess: e => (i.ZE[c] || 0) <= (i.ZE[e] || 0), userType: c } } }, 22440: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(86255);

                function o() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 400; const [t, n] = (0, r.useState)(window.innerWidth), [o, i] = (0, r.useState)(window.innerHeight), { eventDebounce: l } = (0, a.A)(); return (0, r.useEffect)((() => { const t = l((() => { n(window.innerWidth), i(window.innerHeight) }), e); return window.addEventListener("resize", t), () => { window.removeEventListener("resize", t) } }), []), [t, o] } }, 10242: (e, t, n) => { "use strict";
                n.d(t, { A: () => f, Y: () => m }); var r = n(65043),
                    a = n(14556),
                    o = n(30752),
                    i = n(69219),
                    l = n(43862),
                    s = n(78396),
                    c = n(22264),
                    d = n(6124),
                    u = n(89656),
                    h = n(70579); const m = (0, r.createContext)([]),
                    p = 100;

                function f(e) { let { children: t } = e; const n = (0, r.useRef)(null),
                        f = (0, r.useRef)(null),
                        v = (0, r.useRef)(!1),
                        [g, y] = (0, r.useState)(0),
                        { visibleNodeRefs: b } = (0, c.A)(),
                        w = (0, r.useRef)({}),
                        z = (0, a.d4)(o.A),
                        x = (0, r.useRef)(null),
                        A = (0, a.wA)(),
                        { updatePageReady: k } = (0, d.A)(),
                        S = (0, a.d4)(u.cl),
                        M = (0, a.d4)(i.uo),
                        E = M === s.uI.PRINT_PREVIEW || M === s.uI.PRINT,
                        C = () => { v.current = !1, S ? (async () => { let e = {},
                                    t = !1,
                                    n = Object.keys(b.current); for (let a of n) e[a] = b.current[a], b.current[a] && b.current[a] !== w.current[a] && (t = !0); const r = x.current !== z.id;
                                t || r ? (w.current = e, await A((0, l.wu)({ sizes: e, themeChanged: r })), x.current = z.id) : E || k() })() : E || k() }; return (0, r.useEffect)((() => { let e = !0; return clearTimeout(n.current), n.current = setTimeout((() => { e && C() }), p),
                            function() { e = !1 } }), [g]), (0, h.jsx)(m.Provider, { value: (e, t) => { b.current[e] !== t ? (v.current = !0, clearTimeout(f.current), b.current[e] = t, y((e => e + 1))) : v.current || (f.current = setTimeout((() => { E || k() }), p)) }, children: t }) } }, 41473: (e, t, n) => { "use strict";
                n.d(t, { A: () => s, B: () => o }); var r = n(65043),
                    a = n(70579); const o = r.createContext(),
                    i = { height: 0, width: 0 },
                    l = { height: 0, width: 0 };

                function s(e) { const t = (0, r.useRef)({ type: "DEFAULT", dragItemType: "", item: null, itemClickOrDragCB: null, itemClickOrDragErrorCB: null, itemAssignOrCancelCB: null }),
                        n = (0, r.useRef)(i),
                        s = (0, r.useRef)(l),
                        c = (0, r.useRef)(null),
                        d = (0, r.useRef)(null),
                        u = (0, r.useRef)([]),
                        h = (0, r.useRef)({}),
                        m = (0, r.useRef)({}),
                        p = (0, r.useRef)(null),
                        f = (0, r.useRef)(null),
                        v = (0, r.useRef)({}),
                        g = (0, r.useRef)([]),
                        y = (0, r.useState)(null),
                        b = (0, r.useRef)([]),
                        w = (0, r.useState)(null),
                        z = (0, r.useRef)([]); return (0, a.jsx)(o.Provider, { value: { backPageLinkRef: s, managerPathRef: n, chartContainerRef: d, chartSvgRef: p, visibleNodeRefs: v, chartControlState: t, actionCardRef: g, dropZoneCardRef: b, loadingCardRef: z, chartSearchRef: c, chartContainerSizeRef: u, chartScaleRef: m, chartSpaceRef: h, topGroupRef: f, newActionCardState: y, newDropZoneCardRef: w }, children: e.children }) } }, 23523: (e, t, n) => { "use strict";
                n.d(t, { c: () => b, A: () => z }); var r, a = n(65043),
                    o = n(57528),
                    i = n(72119),
                    l = n(35801); const s = (0, i.Ay)(l.A).attrs((e => { let { theme: t } = e; return { style: { zIndex: t.zIndex.confirmAction } } }))(r || (r = (0, o.A)([""]))); var c = n(72835),
                    d = n(52907),
                    u = n(43867),
                    h = n(50419),
                    m = n(5816),
                    p = n(40454),
                    f = n(61531),
                    v = n(2173),
                    g = n(24115),
                    y = n(70579); const b = a.createContext(),
                    w = { open: !1, title: "", message: "", execFunc: null, confirmButtonText: "Confirm", cancelButtonText: "Cancel" };

                function z(e) { const [t, n] = (0, a.useState)(w), r = () => { "function" === typeof t.cancelFunc && t.cancelFunc.call(), n(w) }, [o, i] = (0, v.A)((async () => { await t.execFunc.call(), n(w) })); return (0, y.jsxs)(b.Provider, { value: [t, n], children: [e.children, (0, y.jsxs)(s, { open: t.open, onClose: r, "aria-labelledby": "alert-dialog-title", "aria-describedby": "alert-dialog-description", fullWidth: !0, maxWidth: "sm", disableEnforceFocus: !0, children: [(0, y.jsx)(m.A, { id: "alert-dialog-title", children: t.title }), (0, y.jsx)(u.A, { children: (0, y.jsx)(g.A, { loading: o, transparent: !0, children: (0, y.jsx)(h.A, { id: "alert-dialog-description", align: "center", children: t.message }) }) }), (0, y.jsx)(d.A, { children: (0, y.jsxs)(p.A, { container: !0, justifyContent: "center", children: [(0, y.jsx)(f.A, { m: 2, children: (0, y.jsx)(c.A, { disabled: o, variant: "outlined", onClick: r, color: "primary", children: t.cancelButtonText }) }), (0, y.jsx)(f.A, { m: 2, children: (0, y.jsx)(c.A, { disabled: o, onClick: i, color: "primary", variant: "contained", children: t.confirmButtonText }) })] }) })] })] }) } }, 1523: (e, t, n) => { "use strict";
                n.d(t, { MV: () => xa, Ay: () => ka }); const r = function(e) { return Element.prototype.closest || function(t) { let n = this; for (; n;) { if (e.call(n, t)) return n;
                            n = n.parentElement } return null } }(function() { const e = Element.prototype; return e.matches || e.matchesSelector || e.webkitMatchesSelector || e.mozMatchesSelector || e.msMatchesSelector || e.oMatchesSelector }());

                function a(e) { if (void 0 === e || null === e) throw new TypeError("Cannot convert first argument to object"); let t = Object(e); for (let n = 0; n < (arguments.length <= 1 ? 0 : arguments.length - 1); n++) { let e = n + 1 < 1 || arguments.length <= n + 1 ? void 0 : arguments[n + 1]; if (void 0 === e || null === e) continue; let r = Object.keys(Object(e)); for (let n = 0, a = r.length; n < a; n++) { let a = r[n],
                                o = Object.getOwnPropertyDescriptor(e, a);
                            void 0 !== o && o.enumerable && (t[a] = e[a]) } } return t } const o = Object.assign || a; const i = function() { const e = CustomEvent.prototype; let t = CustomEvent; return "function" !== typeof t && (t = function(t, n) { n = n || { bubbles: !1, cancelable: !1 }; let r = document.createEvent("CustomEvent"); return r.initCustomEvent(t, n.bubbles, n.cancelable, n.detail), r.preventDefault = function() { if (e.preventDefault.apply(this), this.cancelable) try { Object.defineProperty(this, "defaultPrevented", { configurable: !0, get: () => !0 }) } catch (t) {} }, r }, t.prototype = e), t }(),
                    l = { eventLast: "submitlast", eventBefore: "submitbefore", eventStart: "submitstart", eventEnd: "submitend" },
                    s = document,
                    c = s.defaultView; let d, u = Element.prototype.closest,
                    h = Object.assign,
                    m = c.CustomEvent,
                    p = null,
                    f = null,
                    v = !1,
                    g = null;

                function y(e) { let t = e.target;
                    t = t && u.call(t, "button,input"), !t || "submit" !== t.type && "image" !== t.type || (f = t, setTimeout((() => { f = null }), 1)) }

                function b() { p = null, v = !1, c.removeEventListener("submit", x), c.addEventListener("submit", x) }

                function w(e, t) { let n = { transport: "default" }; return e === g.eventBefore && (n.activeButton = f), void 0 !== t && (n.timeout = t), new m(e, { bubbles: !0, cancelable: !1, detail: n }) }

                function z(e, t, n) { let r = w(t, n);
                    e.dispatchEvent(r) }

                function x(e) { c.removeEventListener("submit", x); let t = e.target,
                        n = new m(g.eventLast, { bubbles: !0, cancelable: !0, detail: { activeButton: f } });
                    e.defaultPrevented && n.preventDefault(), t.dispatchEvent(n), n.defaultPrevented ? e.preventDefault() : function(e) { p = e, z(e, g.eventBefore) }(t) }

                function A() { p && !v && z(p, g.eventStart), v = !0, d = d || w(g.eventEnd) }

                function k(e) { p && (d ? (d.detail.timeout = e, p.dispatchEvent(d)) : z(p, g.eventEnd, e)), p = null, v = !1 }

                function S() { k(!1) } const M = () => g,
                    E = e => { if (g) throw new Error("form-extra-events already registered"); return g = h({}, l, e || {}), c.addEventListener("click", y), s.addEventListener("submit", b), c.addEventListener("beforeunload", A), c.addEventListener("unload", S), g };
                (function(e, t, n) { u = e || u, h = t || h, m = n || m })(r, o, i); const C = function() { const e = Event; let t = e; return "function" !== typeof t && (t = function(t, n) { n = n || { bubbles: !1, cancelable: !1 }; let r = document.createEvent("Event"); return r.initEvent(t, n.bubbles, n.cancelable), r.preventDefault = function() { if (e.prototype.preventDefault.apply(this), this.cancelable) try { Object.defineProperty(this, "defaultPrevented", { configurable: !0, get: () => !0 }) } catch (t) {} }, r }, Object.keys(e).forEach((n => { t[n] = e[n] })), t.prototype = e.prototype), t }(),
                    T = document,
                    H = T.defaultView; let L, I, j, V, O, R, P = Element.prototype.closest,
                    D = H.Event;

                function F(e) { return e.style.display = "none", e }

                function N(e) { return "submit" === e.type || "image" === e.type }

                function _(e, t) { let n, r = e.compareDocumentPosition(t),
                        a = 2 === (19 & r); if (!a && !(4 === (21 & r))) return null;
                    a ? (n = V = V || F(T.createElement("div")), e.insertBefore(n, e.firstChild)) : (n = O = O || F(T.createElement("div")), e.appendChild(n)); let o = t.cloneNode(!0); return t.parentNode.replaceChild(o, t), n.appendChild(t), setTimeout(B, 0), o }

                function B() { j && j.parentNode.replaceChild(I, j), V && V.parentNode.removeChild(V), O && O.parentNode.removeChild(O), V = O = j = I = null }

                function W(e) { for (let t = 0; t < e.length; t++) { let n = e[t]; if (N(n)) return n } }

                function U(e) { let t = e.target; if (!e.defaultPrevented && -1 !== ["INPUT", "BUTTON"].indexOf(t.nodeName) && ("Enter" === e.key || 13 === (e.keyCode || e.which || e.charCode))) { let n = t.getAttribute("form"),
                            r = n ? T.querySelector("form#" + n) : t.form; if (r && r.id) { let t, n = W(T.querySelectorAll('[form="' + r.id + '"]')),
                                a = W(r.elements);
                            t = n && a ? 4 === (4 & n.compareDocumentPosition(a)) ? n : a : n || a, e.preventDefault(), t && t.dispatchEvent(new D("click", { bubbles: !0, cancelable: !0 })) } else n && e.preventDefault() } }

                function q(e) { if (!e.defaultPrevented) { let t = e.target; if (t = t && P.call(t, "button,input"), t && N(t)) { let n = t.getAttribute("form"); if (n) { let r = P.call(t, "form");
                                r && r.id === n || (r = T.querySelector("form#" + n), r ? (I = t, j = _(r, t)) : e.preventDefault()) } } } }

                function G() { B() }

                function K(e) { let t = e.target;
                    R = []; for (let n = 0; n < t.elements.length; n++) { let e = t.elements[n],
                            r = e.getAttribute("form");
                        r && r !== t.id && "" !== e.name && !e.disabled && -1 === ["reset", "submit", "button", "image"].indexOf(e.type) && (R.push([null, e.name, e]), e.removeAttribute("name")) } if (t.id) { let e = T.querySelectorAll('[form="' + t.id + '"]'); for (let n = 0; n < e.length; n++) { let r = e[n],
                                a = _(t, r);
                            a && R.push([a, null, r]) } } }

                function Z() { for (let e = 0; e < R.length; e++) { let [t, n, r] = R[e];
                        n ? r.setAttribute("name", n) : t.parentNode.replaceChild(r, t) } B(), R = [] } const Y = { setShim: function(e, t) { P = e || P, D = t || D }, register: () => { let e = () => { if (! function() { let e = T.createElement("div"),
                                        t = T.createElement("form"),
                                        n = T.createElement("input"),
                                        r = "_tmp" + Date.now();
                                    t.id = r, n.setAttribute("form", r), T.body.appendChild(F(e)), e.appendChild(t), e.appendChild(n); let a = n.form === t; return T.body.removeChild(e), a }()) { if (L) throw new Error("form-association-polyfill already registered");
                                L = M(), L || (L = E()), H.addEventListener("keypress", U), H.addEventListener("click", q), H.addEventListener("submit", G, !0), H.addEventListener(L.eventBefore, K), H.addEventListener(L.eventStart, Z) } };
                        T.body ? e() : T.addEventListener("DOMContentLoaded", e) }, unregister: () => { L && (H.removeEventListener("keypress", U), H.removeEventListener("click", q), H.removeEventListener("submit", G, !0), H.removeEventListener(L.eventBefore, K), H.removeEventListener(L.eventStart, Z), L = null) } };
                Y.setShim(r, C);
                Y.register(); var X = n(65043),
                    $ = n(14556),
                    Q = n(53109),
                    J = n(84),
                    ee = n(91688),
                    te = n(66856),
                    ne = n(86597),
                    re = n(24115),
                    ae = n(83462),
                    oe = n(96446),
                    ie = n(17392),
                    le = n(85865),
                    se = n(67784),
                    ce = n(42518),
                    de = n(2828),
                    ue = n(43845),
                    he = n(88446),
                    me = n(75156),
                    pe = n(32115),
                    fe = n(37294),
                    ve = n(2173),
                    ge = n(29210),
                    ye = n(42006),
                    be = n(70579); const we = [{ title: "Organization templates", message: "Choose from common business and organization categories.", icon: ge.I.largeOrgs, value: "templates" }, { title: "Auto chart builder", message: "Easily create a smart org chart with AI - just provide a few details.", icon: ge.I.largeOrgs, value: "autobuild", isBeta: !0 }, { title: "Real brands", message: "Start with the org hierarchy of well-known, worldwide brands.", icon: ge.I.realBrands, value: "brands" }, { title: "Start from scratch", message: "Begin with a basic org structure that you can build on as needed.", icon: ge.I.fromScratch, value: "manual" }],
                    ze = () => { var e; const t = (0, ee.useHistory)(),
                            n = (0, ne.A)(),
                            r = (0, $.d4)(ye.VF),
                            a = !(null === r || void 0 === r || null === (e = r.featureSet) || void 0 === e || !e.autobuild),
                            { openDialog: o, closeDialog: i } = (0, J.A)("demoSpace"),
                            { openDialog: l } = (0, J.A)("autoChartBuilder"),
                            { openDialog: s, closeDialog: c } = (0, J.A)("newChart"),
                            d = (0, $.wA)(),
                            u = a ? [...we] : [...we].filter((e => "autobuild" !== e.value)),
                            [h, m] = (0, X.useState)(null),
                            [p, f] = (0, ve.A)((async () => { var e; if (null === n || void 0 === n || !n.id) return; const { error: r, payload: a } = await d(Q.te.create({ orgId: n.id, data: { chart: { name: (null === h || void 0 === h ? void 0 : h.name) || "".concat(n.name, " Chart"), type: (null === h || void 0 === h ? void 0 : h.type) || "traditional" } } }));!r && null !== a && void 0 !== a && null !== (e = a.chart) && void 0 !== e && e.organization && (t.push((0, te.Wx)({ base: "protected", chartId: a.chart.id, orgId: a.chart.organization })), c()) })); return (0, be.jsxs)(ae.A, { open: !0, onClose: c, maxWidth: "lg", fullWidth: !0, children: [(0, be.jsx)(oe.A, { pt: 3, pr: 3, width: "100%", display: "flex", justifyContent: "flex-end", alignItems: "center", children: (0, be.jsx)(ie.A, { onClick: c, children: (0, be.jsx)(me.gF, { icon: "Close", size: "lg", color: "inherit" }) }) }), (0, be.jsx)("form", { onSubmit: "function" === typeof f ? f : void 0, children: (0, be.jsxs)(re.A, { transparent: !0, loading: p, px: 8, pb: 8, display: "flex", flexDirection: "column", gridGap: 48, children: [(0, be.jsxs)(oe.A, { display: "flex", flexDirection: "column", gap: 1, children: [(0, be.jsx)(le.A, { variant: pe.Eq.displayXXL, children: "Create a new chart" }), (0, be.jsx)(le.A, { variant: pe.Eq.bodyXL, color: fe.Qs.Neutrals[700], children: "Choose a starting point to automatically include roles that fit your organization's structure or start from scratch." })] }), (0, be.jsxs)(oe.A, { display: "flex", flexDirection: "column", gap: 3, children: [(0, be.jsx)(le.A, { variant: pe.Eq.displayMD, color: fe.Qs.Neutrals[800], children: h ? "New chart name" : "Traditional org charts" }), h && (0, be.jsxs)(oe.A, { display: "flex", gap: 3, children: [(0, be.jsx)(se.A, { placeholder: "Enter a name for this chart", size: "large", fullWidth: !0, onChange: e => { var t;
                                                    (null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value) && m({ ...h, name: e.target.value }) } }), (0, be.jsx)(ce.A, { size: "large", color: "primary", variant: "contained", disabled: !h.name, type: "submit", children: "Create" })] }), !h && (0, be.jsx)(oe.A, { display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: 2, children: u.map(((e, t) => (0, be.jsx)(de.A, { onClick: () => { "manual" === e.value && m({ type: "traditional", name: "" }), ["brands", "templates"].includes(e.value) && (o({ brandFilter: e.value, mode: "newChart", source: "new-chart", onBackClick: () => { i(), s() } }), c()), "autobuild" === e.value && (c(), l()) }, children: (0, be.jsxs)(oe.A, { p: 3, border: "1px solid #ececec", borderRadius: 1, display: "flex", flexDirection: "column", gap: 4, height: "100%", children: [(0, be.jsx)(oe.A, { mb: 1, children: (0, be.jsx)("img", { height: 80, src: e.icon, alt: "Large organizations" }) }), (0, be.jsxs)(oe.A, { display: "flex", flexDirection: "column", gap: 1, children: [(0, be.jsxs)(oe.A, { display: "flex", alignItems: "center", gap: 1, children: [(0, be.jsx)(le.A, { variant: pe.Eq.h2, color: fe.Qs.Neutrals[800], children: e.title }), e.isBeta && (0, be.jsx)(ue.A, { label: "Beta", color: "primary" })] }), (0, be.jsx)(le.A, { variant: pe.Eq.bodyMD, color: fe.Qs.Neutrals[600], children: e.message })] })] }) }, "card-".concat(e.value, "-").concat(t)))) })] }), h && (0, be.jsx)(oe.A, { flex: 1, alignContent: "flex-end", children: (0, be.jsxs)(ce.A, { variant: "outlined", style: { width: "max-content" }, color: "neutral", onClick: () => m(null), children: [(0, be.jsx)(me.gF, { icon: "ArrowLeft" }), "Back"] }) }), !h && (0, be.jsxs)(oe.A, { display: "flex", flexDirection: "column", gap: 3, children: [(0, be.jsx)(le.A, { variant: pe.Eq.displayMD, color: fe.Qs.Neutrals[800], children: "Other chart types" }), (0, be.jsxs)(oe.A, { display: "flex", gap: 6, children: [(0, be.jsx)(he.A, { onClick: () => m({ type: "matrix", name: "" }), variant: pe.Eq.bodyXL, children: "Matrix chart" }), (0, be.jsx)(he.A, { onClick: () => m({ type: "board of directors", name: "" }), variant: pe.Eq.bodyXL, children: "Board of Directors chart" })] })] })] }) })] }) }; var xe = n(393),
                    Ae = n(90318),
                    ke = n(40454),
                    Se = n(87603),
                    Me = n(28269),
                    Ee = n(16853),
                    Ce = n(77325),
                    Te = n(78300),
                    He = n(96364),
                    Le = n(70318),
                    Ie = n(41583),
                    je = n(86852); const Ve = () => { const { toggleDialog: e } = (0, J.A)("adminCard"); return (0, be.jsx)(xe.Ay, { children: (0, be.jsx)(Ae.A, { variant: "div", spacing: { all: 1 }, children: (0, be.jsxs)(ke.A, { container: !0, direction: "column", justifyContent: "space-between", children: [(0, be.jsxs)(ke.A, { container: !0, justifyContent: "space-between", children: [(0, be.jsxs)(Ie.W, { container: !0, justifyContent: "flex-start", children: [(0, be.jsx)(je.A, { variant: "div", spacing: { left: 1, right: 2, top: 1 }, children: (0, be.jsx)(Te.A, { width: 100, height: 100 }) }), (0, be.jsxs)(je.A, { variant: "div", spacing: { top: 3 }, children: [(0, be.jsx)(He.A, { variant: "h3", children: "Administrator" }), (0, be.jsx)(He.A, { variant: "h4", children: "Rosemary Barton" }), (0, be.jsx)(He.A, { variant: "body1", children: "Chief People Officer" })] })] }), (0, be.jsx)(ke.A, { item: !0, children: (0, be.jsx)(Le.A, { onClick: () => e(), children: (0, be.jsx)(me.Ay, { icon: "Close" }) }) })] }), (0, be.jsxs)(Ie.D, { children: [(0, be.jsx)(je.A, { varian: "div", spacing: { horizontal: 2, top: 2 }, children: (0, be.jsx)(He.A, { variant: "body1", children: "PEOPLE" }) }), (0, be.jsxs)(Se.A, { dense: !0, button: !0, children: [(0, be.jsx)(Me.A, { children: (0, be.jsx)(Ee.A, { edge: "start", tabIndex: -1, disableRipple: !0, inputProps: { "aria-labelledby": "people" } }) }), (0, be.jsx)(Ce.A, { primary: "add, import and edit people" })] }), (0, be.jsx)(je.A, { varian: "div", spacing: { horizontal: 2 }, children: (0, be.jsx)(He.A, { variant: "body1", children: "CHARTS" }) }), (0, be.jsxs)(Se.A, { dense: !0, button: !0, children: [(0, be.jsx)(Me.A, { children: (0, be.jsx)(Ee.A, { edge: "start", tabIndex: -1, disableRipple: !0, inputProps: { "aria-labelledby": "charts" } }) }), (0, be.jsx)(Ce.A, { primary: "add, edit and delete charts" })] }), (0, be.jsx)(je.A, { varian: "div", spacing: { horizontal: 2 }, children: (0, be.jsx)(He.A, { variant: "body1", children: "SHARES" }) }), (0, be.jsxs)(Se.A, { dense: !0, button: !0, children: [(0, be.jsx)(Me.A, { children: (0, be.jsx)(Ee.A, { edge: "start", tabIndex: -1, disableRipple: !0, inputProps: { "aria-labelledby": "shares" } }) }), (0, be.jsx)(Ce.A, { primary: "share charts, reports and lists of people" })] }), (0, be.jsx)(je.A, { varian: "div", spacing: { horizontal: 2 }, children: (0, be.jsx)(He.A, { variant: "body1", children: "ACTIVITY LOGS" }) }), (0, be.jsxs)(Se.A, { dense: !0, button: !0, children: [(0, be.jsx)(Me.A, { children: (0, be.jsx)(Ee.A, { edge: "start", tabIndex: -1, disableRipple: !0, inputProps: { "aria-labelledby": "activity logs" } }) }), (0, be.jsx)(Ce.A, { primary: "view and customize activity logs" })] })] })] }) }) }) }; var Oe, Re, Pe, De, Fe, Ne, _e, Be, We, Ue = n(43867),
                    qe = n(61531),
                    Ge = n(52907),
                    Ke = n(72835),
                    Ze = n(5816),
                    Ye = n(356),
                    Xe = n(57528),
                    $e = n(72119),
                    Qe = n(35801),
                    Je = n(749); const et = (0, $e.Ay)(Qe.A)(Oe || (Oe = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  .MuiDialog-paperWidthSm{\n    min-width:1098px;\n    min-height:90vh;\n  }\n .MuiButton-label{\n   text-transform:none;\n }\n .MuiDialog-paper{\n    padding:".concat(t.spacing(2), "px;\n  }\n  .MuiStepper-root{\n    padding:0;\n  }\n") })),
                    tt = ((0, $e.Ay)(Je.A).attrs({ elevation: 0 })(Re || (Re = (0, Xe.A)(["\n  width: 100%;\n  min-height: 300px;\n  padding-top: 0;\n  border-radius: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n"]))), (0, $e.Ay)("h2")(Pe || (Pe = (0, Xe.A)(["\n  ", "\n"])), (e => { let { weight: t = "normal" } = e; return "\n    font-size:24px;\n    font-weight:".concat(t, ";\n    margin-top:0;\n    margin-bottom:0;\n    color: inherit;\n  ") }))),
                    nt = (0, $e.Ay)(qe.A)(De || (De = (0, Xe.A)(["\n  ", "\n  height: 100px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n"])), (e => { let { theme: t, background: n } = e; return "\n    border:".concat(n ? "2px solid ".concat(t.palette.grey[600]) : "none", ";\n    background-color:").concat(n || "transparent", ";\n    color:").concat(n ? "#FFFFFF" : "#000000", ";\n  ") })),
                    rt = ((0, $e.Ay)("div")(Fe || (Fe = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n height:250px;\n display:flex;\n flex-direction:column;\n align-items:center;\n justify-content:space-between;\n margin-top:0;\n padding:".concat(t.spacing(2), "px 0;\n h4{font-size:30px; font-weight:bold;}\n h3{font-size:24px; font-weight:bold;text-align:center}\n h4,p{\n   margin:0;\n   text-align:center;\n   \n }\n p{margin:0 10px;}\n border:2px solid ").concat(t.palette.grey[600], ";\n") })), (0, $e.Ay)("div")(Ne || (Ne = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display:flex;\n  flex-direction:column;\n  align-items:center;\n  flex:1;\n  width:100%;\n  margin-bottom:".concat(t.spacing(2), "px;\n  align-items:center;\n") })), (0, $e.Ay)(He.A)(_e || (_e = (0, Xe.A)(["\n  display: inline-block;\n  text-align: center;\n  margin: 10px;\n  border-radius: 4px;\n  font-weight: 500;\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    padding: 2px 6px;\n    border: solid 1px ".concat(t.palette.primary.main, ";\n  ") })), (0, $e.Ay)(He.A)(Be || (Be = (0, Xe.A)(["\n  font-size: 13px;\n  font-weight: 500;\n  color: #666;\n  margin-right: 8px;\n"])))),
                    at = (0, $e.Ay)(He.A)(We || (We = (0, Xe.A)(["\n  font-size: 13px;\n  color: #666;\n"]))); var ot = n(66795),
                    it = n(96795),
                    lt = n(66187),
                    st = n(18219),
                    ct = n(50227),
                    dt = n(55345),
                    ut = n(77604); const ht = [{ title: "Basic", id: "standard", subheader: "", price: "15", description: ["20 Admins / Owners", "Basic Custom Fields", "CSV / Excel Data Import", "Unlimited Traditional Charts", "Data Hosting - US only", "Email Support"], buttonText: "Buy Now", buttonVariant: "contained", recommended: !1, isTrialAvailable: !0 }, { title: "Premium", id: "premium", subheader: "Everything in Basic PLUS...", price: "15", description: ["Unlimited Admins / Owners", "SSO ( Single Sign On )", "Reporting and Analytics", "Matrix and Governance Charts", "One Hour Dedicated Onboarding", "Data Hosting on AWS - US, EU, AU", "Organimi Connect\u2122 Integrations"], buttonText: "Buy Now", buttonVariant: "contained", recommended: !0, isTrialAvailable: !0 }],
                    mt = (e, t) => { if ("standard" === e) { const n = ft[e][t].admins; return ["".concat(n, " Admins / Owners"), "Basic Custom Fields", "CSV / Excel Data Import", "Unlimited Traditional Charts", "Data Hosting - US only", "Email Support"] } if ("premium" === e) return ["Unlimited Admins / Owners", "SSO ( Single Sign On )", "Reporting and Analytics", "Matrix and Governance Charts", "One Hour Dedicated Onboarding", "Data Hosting on AWS - US, EU, AU", "Organimi Connect\u2122 Integrations"] },
                    pt = { title: "Enterprise", id: "enterprise", price: "0", description: ["Reach out if you have more than 2500 people to chart. "], buttonText: "Contact us", buttonVariant: "outlined", recommended: !0, isTrialAvailable: !1 },
                    ft = { trial: { 25: { annual: 0, monthly: 0, admins: 1 } }, standard: { 150: { annual: 11, monthly: 20, admins: 3 }, 250: { annual: 18, monthly: 33, admins: 3 }, 500: { annual: 35, monthly: 61, admins: 5 }, 750: { annual: 51, monthly: 89, admins: 5 }, 1e3: { annual: 67, monthly: 116, admins: 10 }, 1250: { annual: 83, monthly: 145, admins: 10 }, 1500: { annual: 97, monthly: 172, admins: 10 }, 1750: { annual: 111, monthly: 200, admins: 10 }, 2e3: { annual: 126, monthly: 220, admins: 15 }, 2250: { annual: 140, monthly: 246, admins: 15 }, 2500: { annual: 150, monthly: 270, admins: 15 }, "2500plus": { admins: "Unlimited" } }, premium: { 150: { annual: 22, monthly: 39, admins: "Unlimited" }, 250: { annual: 36, monthly: 64, admins: "Unlimited" }, 500: { annual: 70, monthly: 122, admins: "Unlimited" }, 750: { annual: 102, monthly: 178, admins: "Unlimited" }, 1e3: { annual: 133, monthly: 235, admins: "Unlimited" }, 1250: { annual: 165, monthly: 289, admins: "Unlimited" }, 1500: { annual: 194, monthly: 340, admins: "Unlimited" }, 1750: { annual: 222, monthly: 394, admins: "Unlimited" }, 2e3: { annual: 250, monthly: 438, admins: "Unlimited" }, 2250: { annual: 280, monthly: 490, admins: "Unlimited" }, 2500: { annual: 302, monthly: 540, admins: "Unlimited" }, "2500plus": { admins: "Unlimited" } } }; var vt, gt, yt, bt, wt, zt, xt, At, kt, St, Mt = n(22440),
                    Et = n(75308); const Ct = (0, $e.Ay)(He.A)(vt || (vt = (0, Xe.A)(["\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n"]))),
                    Tt = (0, $e.Ay)(He.A)(gt || (gt = (0, Xe.A)(["\n  color: #000000;\n  font-size: 24px;\n  font-weight: 400;\n"]))),
                    Ht = (0, $e.Ay)(qe.A)(yt || (yt = (0, Xe.A)(["\n  border: solid #ccc 1px;\n  border-radius: 8px;\n  position: relative;\n  overflow: hidden;\n  cursor: pointer;\n"]))),
                    Lt = (0, $e.Ay)(He.A)(bt || (bt = (0, Xe.A)(["\n  padding: 4px 12px;\n  z-index: 1;\n  text-align: center;\n  min-width: 100px;\n  ", "\n"])), (e => { let { active: t } = e; return "\n    ".concat(t ? "\n      color: #ffffff;\n      " : "", "\n  ") })),
                    It = (0, $e.Ay)(qe.A)(wt || (wt = (0, Xe.A)(["\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 50%;\n  border-radius: 6px;\n  z-index: 0;\n  transition: left 0.2s linear;\n  ", "\n"])), (e => { let { theme: t, enabled: n } = e; return "\n    left: ".concat(n ? "50%" : 0, ";\n    background: ").concat(t.palette.primary.main, ";\n  ") })),
                    jt = (0, $e.Ay)(qe.A)(zt || (zt = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t, showRecommendedBorder: n, showRecommendedContent: r } = e; return n ? r ? "\n        position: relative;\n        border: 2px solid ".concat(t.palette.primary.main, ';\n        &::before {\n          content: "Active Plan";\n          position: absolute;\n          top: -0.7rem;\n          left: 50%;\n          transform: translateX(-50%);\n          display: flex;\n          justify-content: center;\n          background-color: #fff;\n          padding: 0 0.5rem;\n          font-size: 0.8rem;\n          font-weight: 400;\n          color: ').concat(t.palette.primary.main, ";\n        }\n      ") : "\n        position: relative;\n        border: 2px solid ".concat(t.palette.primary.main, ';\n        &::before {\n          content: "Recommended";\n          position: absolute;\n          top: -0.7rem;\n          left: 50%;\n          transform: translateX(-50%);\n          display: flex;\n          justify-content: center;\n          background-color: #fff;\n          padding: 0 0.5rem;\n          font-size: 0.8rem;\n          font-weight: 400;\n          color: ').concat(t.palette.primary.main, ";\n        }\n      ") : "" })),
                    Vt = (0, $e.Ay)(He.A)(xt || (xt = (0, Xe.A)(["\n  ul {\n    list-style: disc;\n    padding-inline-start: 1rem;\n  }\n\n  li {\n    margin-bottom: 0;\n  }\n"]))),
                    Ot = (0, $e.Ay)(He.A)(At || (At = (0, Xe.A)(["\n  font-size: 36px;\n  font-weight: 500;\n  color: #000000;\n"]))),
                    Rt = (0, $e.Ay)(He.A)(kt || (kt = (0, Xe.A)(["\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 24px;\n"]))),
                    Pt = (0, $e.Ay)(He.A)(St || (St = (0, Xe.A)(["\n  font-size: 20px;\n  color: ", ";\n"])), (e => { let { theme: t } = e; return t.palette.primary.main })),
                    Dt = e => { let { labelLeft: t = "Left", labelRight: n = "Right", enabled: r = !0, handleLeft: a, handleRight: o } = e; return (0, be.jsxs)(Ht, { display: "flex", gridGap: 8, children: [(0, be.jsx)(It, { enabled: r }), (0, be.jsx)(Lt, { flex: 1, active: !r, onClick: a, children: t }), (0, be.jsx)(Lt, { flex: 1, active: r, onClick: o, children: n })] }) },
                    Ft = e => { let { planAttrs: t } = e; const { userTier: n, frequency: r, planType: a, status: o } = t || {}, i = "standard" === a ? "basic" : a, l = "current" === o ? "Active" : "Recommended"; return (0, be.jsxs)(qe.A, { display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column", mt: 1, children: [(0, be.jsxs)(rt, { children: [l, " Plan"] }), (0, be.jsxs)(at, { children: [n, " Employees \xa0| \xa0 ", i, " \xa0 | \xa0", r] })] }) };

                function Nt(e) { let { onSelectPlan: t, activeLicense: n } = e; const r = (0, ut.T$)(n),
                        [a, o] = (0, X.useState)(null === r || void 0 === r ? void 0 : r.userTier),
                        [i, l] = (0, X.useState)(null === r || void 0 === r ? void 0 : r.frequency),
                        [s] = (0, Mt.A)(),
                        c = s < 950,
                        d = (0, $.d4)(Et.$),
                        u = (0, X.useMemo)((() => { const e = _t.find((e => e.count === a)); return e ? e.value : 150 }), []),
                        h = e => { l(e) },
                        m = e => () => { if ("enterprise" === e) return t({ planId: "v7_enterprise", planType: "enterprise", userTier: 2500, frequency: "annual" }); const n = "2024_v7_".concat(e, "_").concat(i, "_").concat(a);
                            t({ planId: n, frequency: i, planType: e, userTier: a }) },
                        p = e => e.planType === r.planType && e.userTier === r.userTier && e.frequency === r.frequency,
                        f = p({ planType: "standard", userTier: a, frequency: i }),
                        v = p({ planType: "premium", userTier: a, frequency: i }),
                        g = f && "current" === r.status,
                        y = v && "current" === r.status,
                        b = pt,
                        w = () => { const e = (e => null !== e && void 0 !== e && e.username && null !== e && void 0 !== e && e.firstName && null !== e && void 0 !== e && e.lastName ? "https://www.organimi.com/request-a-quote/?firstname=".concat(null === e || void 0 === e ? void 0 : e.firstName, "&lastname=").concat(null === e || void 0 === e ? void 0 : e.lastName, "&email=").concat(null === e || void 0 === e ? void 0 : e.username) : "https://www.organimi.com/request-a-quote/")(d);
                            window.open(e, "_self") }; return (0, be.jsx)(be.Fragment, { children: (0, be.jsxs)(qe.A, { display: "flex", flexDirection: "row", gridGap: 32, fullWidth: !0, position: "relative", justifyContent: "center", flexWrap: "wrap", children: [(0, be.jsxs)(qe.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", width: "100%", children: [(0, be.jsxs)(nt, { children: [(0, be.jsx)(tt, { weight: "bold", children: "Configure New Plan" }), (0, be.jsx)(Ft, { planAttrs: r })] }), (0, be.jsx)(qe.A, { maxWidth: 750, width: "100%", display: "flex", flexDirection: "column", alignItems: "stretch", children: (0, be.jsxs)(qe.A, { flex: 1, children: [(0, be.jsx)(dt.A, { marks: _t, handleChange: (e, t) => { o((() => { const e = _t.find((e => e.value === t)); return e && (null === e || void 0 === e ? void 0 : e.count) || 150 })) }, defaultValue: u }), (0, be.jsx)(qe.A, { display: "flex", justifyContent: "space-between", children: (0, be.jsxs)(He.A, { fontSize: "inherit", children: [(0, be.jsx)(Tt, { component: "span", children: "2500plus" === a ? "2500+" : a }), "\xa0Employees"] }) })] }) }), (0, be.jsx)(Dt, { labelLeft: "Monthly", labelRight: "Annual", enabled: "annual" === i, handleLeft: () => h("monthly"), handleRight: () => h("annual") })] }), (0, be.jsx)(ot.A, { maxWidth: "lg", component: "main", children: (0, be.jsxs)(ke.A, { container: !0, spacing: 2, children: [ht.map(((e, t) => { var n, o; return (0, be.jsx)(ke.A, { item: !0, xs: 12, sm: 6, md: 4, children: (0, be.jsx)(jt, { showRecommendedBorder: "2500plus" !== a && e.recommended, showRecommendedContent: "current" === (null === r || void 0 === r ? void 0 : r.status) && ("standard" === e.id && f || "premium" === e.id && v), height: "100%", display: "flex", flexDirection: "column", justifyContent: "stretch", py: 0 !== t || c ? 0 : 6, children: (0, be.jsxs)(Je.A, { variant: "outlined", style: { height: "100%", display: "flex", flexDirection: "column", borderRadius: 0, border: "solid 1px ".concat("trial" === e.id ? "#cccccc" : "#7C57CB") }, children: [(0, be.jsx)(it.A, { title: (0, be.jsx)(Ct, { align: "center", children: e.title }), subheader: (0, be.jsx)(qe.A, { children: "trial" === e.id || null !== (n = ut.ZL[e.id][a]) && void 0 !== n && n[i] ? (0, be.jsxs)(qe.A, { mt: "trial" !== e.id ? 2 : 1, mb: "trial" !== e.id ? 2 : 0, flexDirection: "column", display: "flex", alignItems: "center", children: ["trial" !== e.id && (0, be.jsxs)(qe.A, { display: "flex", flexDirection: "row", alignItems: "baseline", children: [(0, be.jsxs)(Ot, { children: ["$", null === (o = ut.ZL[e.id][a]) || void 0 === o ? void 0 : o[i]] }), (0, be.jsx)(lt.A, { variant: "h6", color: "text.secondary", children: "/mo" })] }), "trial" !== e.id && (0, be.jsx)(Rt, { color: "primary", children: e.subheader })] }) : (0, be.jsx)(qe.A, { my: 4, children: (0, be.jsx)(Pt, { children: "Contact Us for a Quote" }) }) }), action: "Pro" === e.title ? "*" : null, subheaderTypographyProps: { align: "center" }, style: { backgroundColor: e => "light" === e.palette.mode ? e.palette.grey[200] : e.palette.grey[700], paddingBottom: 0 } }), (0, be.jsxs)(qe.A, { display: "flex", justifyContent: "space-between", flexDirection: "column", flex: 1, children: [(0, be.jsx)(st.A, { children: (0, be.jsx)(Vt, { children: (0, be.jsxs)(qe.A, { pt: "trial" === (null === e || void 0 === e ? void 0 : e.id) ? 3 : void 0, children: [mt(null === e || void 0 === e ? void 0 : e.id, a).map((e => (0, be.jsxs)(qe.A, { display: "flex", gridGap: 16, alignItems: "center", children: [(0, be.jsx)(me.Ay, { icon: "CheckCircleSolid" }), (0, be.jsx)(He.A, { variant: "caption", weight: "medium", children: e })] }, e))), "annual" === i && (e.annualDescription || []).map((e => (0, be.jsxs)(qe.A, { display: "flex", gridGap: 16, alignItems: "center", children: [(0, be.jsx)(me.Ay, { icon: "CheckCircleSolid" }), (0, be.jsx)(He.A, { variant: "caption", weight: "medium", children: e })] }, e)))] }) }) }), (0, be.jsx)(qe.A, { p: 2, pb: 3, display: "flex", alignItems: "center", flexDirection: "column", gridGap: 16, children: (0, be.jsx)(Ke.A, { variant: e.buttonVariant, onClick: "2500plus" === a ? w : m(e.id), disabled: "standard" === e.id && g || "premium" === e.id && y, color: "primary", children: "2500plus" === a ? "Contact Us" : "standard" === e.id && g || "premium" === e.id && y ? "Active" : "Select Plan" }) })] })] }) }) }, e.title) })), (0, be.jsx)(ke.A, { item: !0, xs: 12, sm: 6, md: 4, children: (0, be.jsx)(qe.A, { display: "flex", flexDirection: "column", gridGap: 16, height: "100%", children: (0, be.jsx)(qe.A, { display: c ? "block" : "flex", height: "100%", py: c ? 0 : 6, children: (0, be.jsx)(jt, { showRecommendedBorder: "2500plus" === a && b.recommended, children: (0, be.jsxs)(Je.A, { variant: "outlined", style: { height: "100%", display: "flex", flexDirection: "column", borderRadius: 0, border: "solid 1px #cccccc" }, children: [(0, be.jsx)(it.A, { title: (0, be.jsx)(Ct, { align: "center", children: b.title }), subheader: (0, be.jsx)(qe.A, { my: 2, children: (0, be.jsx)(He.A, { variant: "h2", children: b.subheader }) }), action: "Pro" === b.title ? "*" : null, subheaderTypographyProps: { align: "center" }, style: { backgroundColor: e => "light" === e.palette.mode ? e.palette.grey[200] : e.palette.grey[700], paddingBottom: 0 } }), (0, be.jsx)(qe.A, { display: "flex", flex: 1, justifyContent: "center", children: (0, be.jsx)(st.A, { children: (0, be.jsx)(Vt, { children: b.description.map((e => (0, be.jsx)(qe.A, { display: "flex", alignItems: "center", justifyContent: "center", pt: 3, children: (0, be.jsx)(He.A, { variant: "caption", weight: "medium", align: "center", children: e }) }, e))) }) }) }), (0, be.jsx)(ct.A, { children: (0, be.jsx)(qe.A, { m: 2, display: "flex", justifyContent: "center", flex: 1, children: (0, be.jsx)(Ke.A, { variant: b.buttonVariant, onClick: m(b.id), color: "primary", children: b.buttonText }) }) })] }) }) }, b.title) }) })] }) })] }) }) } const _t = [{ value: 0, count: 150, label: "150" }, { value: 10, count: 250, label: "250" }, { value: 20, count: 500, label: "500" }, { value: 30, count: 750, label: "750" }, { value: 40, count: 1e3, label: "1000" }, { value: 50, count: 1250, label: "1250" }, { value: 60, count: 1500, label: "1500" }, { value: 68, count: 1750, label: "1750" }, { value: 76, count: 2e3, label: "2000" }, { value: 84, count: 2250, label: "2250" }, { value: 92, count: 2500, label: "2500" }, { value: 100, count: "2500plus", label: "2500 +" }]; var Bt, Wt = n(63990); const Ut = (0, $e.Ay)(Wt.A)(Bt || (Bt = (0, Xe.A)(["\n  width: 100%;\n"]))); var qt, Gt = n(81497); const Kt = (0, $e.Ay)(Gt.A)(qt || (qt = (0, Xe.A)([""]))); var Zt, Yt = n(26841); const Xt = (0, $e.Ay)(Yt.A)(Zt || (Zt = (0, Xe.A)([""]))); var $t; const Qt = (0, $e.Ay)("div")($t || ($t = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t, color: n } = e; return "\n  width:100%;\n  color:#FFF;\n  background:".concat(t.palette[n].main, ";\n  height:60px;\n  display:flex;\n  flex-direction:column;\n  justify-content:center;\n  align-items:center;\n  text-align:center;\n  font-size: 14pt;\n  font-weight:500;\n") }));

                function Jt(e) { let { planType: t = "standard", planSize: n = 150 } = e; const r = (0, ut.jk)(t, n),
                        a = "standard" === t ? "basic" : t; return (0, be.jsxs)(Je.A, { style: { minHeight: "250px" }, children: [(0, be.jsx)(Qt, { color: "primary", children: a.toUpperCase() }), (0, be.jsx)(He.A, { variant: "body1", align: "center", style: { fontWeight: 500, margin: "10px 10px" }, children: "enterprise" === t ? "2500+ Employees" : "Up to ".concat(n, " employees") }), r.map((e => (0, be.jsx)(He.A, { variant: "body1", align: "center", style: { fontWeight: 500, margin: "10px 10px" }, children: e }, "".concat(t + "label", "-").concat(e))))] }) } var en = n(55357); const tn = { starter: { color: "secondary", period: "year", periodDescription: "USD/month", periodDescription2: "Charged annually", action: "select", type: "starter", name: "Starter", slogan: "Your organization", monthly: "$10", id: "v7_starter", cost: 120, limits: { members: 250, charts: 10, admins: 3, fields: 4, support: "Email", share: "Entire Company (View Only)", "file attachments": !1, onboarding: !1, "matrix org charts": !1, integrations: !1, "payment by PO": !1, "custom hosting": !1 }, tier: 0 }, growth: { color: "info", period: "year", periodDescription: "USD/month", periodDescription2: "Charged annually", action: "select", type: "growth", name: "Growth", slogan: "Always up to date", monthly: "$30", id: "v7_growth", cost: 360, limits: { members: 500, charts: 50, admins: 10, fields: -1, support: "Email", sharing: "Entire Company (View & Edit)", "file attachments": !0, onboarding: !0, "matrix org charts": !0, integrations: !1, "payment by PO": !1, "custom hosting": !1 }, tier: 0 }, pro: { color: "primary", period: "year", periodDescription: "USD/month", periodDescription2: "Charged annually", action: "select", type: "pro", name: "Pro", slogan: "Go pro, or go home.", monthly: "$50", id: "v7_pro", cost: 600, limits: { members: 1e3, charts: -1, admins: -1, fields: -1, support: "Dedicated Onboarding Assistance", share: "Unlimited", "file attachments": !0, onboarding: !0, "matrix org charts": !0, integrations: !0, "payment by PO": !0, "custom hosting": !1 }, tier: 0 }, enterprise: { color: "black", period: "year", periodDescription: "Multi year pricing", periodDescription2: "available", action: "contact", type: "enterprise", name: "Enterprise", slogan: "Enterprise", monthly: "Contact Us", id: "v7_enterprise", cost: "Contact Us", limits: { members: -1, charts: -1, admins: -1, fields: -1, support: "Dedicated Onboarding Assistance", share: "Unlimited", "file attachments": !0, onboarding: !0, "matrix org charts": !0, integrations: !0, "payment by PO": !0, "custom hosting": !0 }, tier: 0 } },
                    nn = { members: "Employees", charts: "Org Charts", admins: "Admins", fields: "Custom Fields", support: "Support", share: "Sharing", "file attachments": "File Attachments", onboarding: "Onboarding", "matrix org charts": "Matrix Org Charts", integrations: "Integrations", "payment by PO": "Payment By PO", "custom hosting": "Custom Hosting" },
                    rn = { free: 0, single: 1, personal: 2, starter: 3, team: 4, planner: 5, growth: 6, pro: 7, enterprise: 8 },
                    an = { AB: 5, BC: 5, MB: 5, NB: 15, NL: 15, NS: 15, PE: 15, NT: 5, NU: 5, ON: 13, QC: 5, SK: 5, YT: 5 }; const on = { getPlans: function() { return tn }, getCost: function(e) { const t = tn[e]; return t && t.cost || 0 }, labels: function() { return nn }, isDowngrade: function(e, t) { return rn[e] > rn[t] }, getTaxRate: function(e) { return an[e] } }; var ln, sn = n(59177),
                    cn = n(49232),
                    dn = n(65038),
                    un = n(12571),
                    hn = n(42197),
                    mn = n(84866); const pn = (0, $e.Ay)(Je.A)(ln || (ln = (0, Xe.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display:block;\n  margin:0 0 0 ".concat(t.spacing(2), "px;\n  min-height:200px;\n  div{\n    display:flex;\n    justify-content:space-between;\n    font-size:16px;\n    font-weight:600;\n    min-height: 50px;\n    align-items:center;\n    padding:0 ").concat(t.spacing(2), "px;\n    margin:0 ").concat(t.spacing(2), "px;\n  }\n  div:first-of-type{\n    color:").concat(t.palette.grey[600], ";\n  }\n  div:nth-of-type(2),.credit,.tax {\n    \n    border-bottom: 1px solid black;\n  }\n  div:nth-of-type(5) {\n    font-size:18px;\n    font-weight:700;\n  }\n  .credit{\n    color:").concat(t.palette.secondary.main, ";\n    font-weight:600;\n  }\n") })),
                    fn = e => { let { plan: t, upgradeCost: n = 0, upgradeTax: r = 0, customerLocation: a, activeLicense: o, handleChangeCountry: i, handleChangeState: l } = e; const { planType: s, userTier: c, frequency: d } = t, u = "".concat((0, sn.ZH)(s), " ").concat(c), h = ut.ZL[s], m = h && h[c], p = m && m[d], f = "annual" === d ? 12 * p : p, v = a.country, g = a.state, y = cn.A.map((e => ({ label: e.name, value: e.code }))), b = (0, X.useMemo)((() => "US" === v ? dn.A.map((e => ({ label: e.name, value: e.code }))) : "CA" === v ? un.A.map((e => ({ label: e.name, value: e.code }))) : []), [v]), w = (e, t) => t.map(((t, n) => (0, be.jsx)(en.A, { value: t.value, children: t.label }, "select_".concat(e, "_").concat(t.value, "_").concat(n)))), z = n || f, x = n ? (f + r - n || 0).toFixed(2) : 0; return (0, be.jsxs)(be.Fragment, { children: ["free" === (null === o || void 0 === o ? void 0 : o.paidPlanType) && (0, be.jsxs)("form", { style: { paddingLeft: hn.A.spacing(2) }, children: [(0, be.jsxs)(mn.A, { name: "country", label: "Billing Country", fullWidth: !0, value: a.country, select: !0, onChange: i, children: [(0, be.jsx)(en.A, { value: "none", children: "Select your country" }), w("country", y)] }), "CA" === v && (0, be.jsx)(mn.A, { name: "state", label: "Billing Province", fullWidth: !0, value: a.state, select: !0, onChange: l, children: w("state", b) })] }), (0, be.jsxs)(pn, { children: [(0, be.jsxs)("div", { children: [(0, be.jsx)("span", { children: "Plan" }), (0, be.jsx)("span", { children: "Cost" })] }), (0, be.jsxs)("div", { children: [(0, be.jsxs)("span", { children: [u, " \xa0@ $", p, "/month"] }), (0, be.jsxs)("span", { children: ["$", f] })] }), x > 0 && (0, be.jsxs)("div", { className: "credit", children: [(0, be.jsx)("span", { children: "Credit from current plan" }), (0, be.jsxs)("span", { children: ["-$", x] })] }), 0 !== r && (0, be.jsxs)("div", { className: "tax", children: [(0, be.jsx)("span", { children: "Tax" }), (0, be.jsxs)("span", { children: ["$", r.toFixed(2)] })] }), "free" === (null === o || void 0 === o ? void 0 : o.paidPlanType) && "CA" === v ? (0, be.jsxs)(be.Fragment, { children: [(0, be.jsxs)("div", { className: "tax", children: [(0, be.jsxs)("span", { children: [5 === on.getTaxRate(g) ? "GST" : "HST", "(", on.getTaxRate(g), "%)"] }), (0, be.jsxs)("span", { children: ["$", (on.getTaxRate(g) / 100 * z).toFixed(2)] })] }), (0, be.jsxs)("div", { children: [(0, be.jsxs)("span", { children: [n ? "Upgrade Cost" : "", " "] }), (0, be.jsxs)("span", { children: ["Total = $", (z + on.getTaxRate(g) / 100 * z).toFixed(2), " ", "USD *"] })] })] }) : (0, be.jsxs)("div", { children: [(0, be.jsxs)("span", { children: [n ? "Upgrade Cost" : "", " "] }), (0, be.jsxs)("span", { children: ["Total = $", z.toFixed(2), " USD *"] })] })] })] }) }; var vn = n(83340),
                    gn = n(81972),
                    yn = n(36138),
                    bn = n(54762); const wn = (0, gn.c)("pk_live_n6PPZyt7BsjhSWY8abAvz8Ft"),
                    zn = { planType: "standard", frequency: "annual", planId: "2024_v7_standard_annual_150", userTier: 150 },
                    xn = e => { let { open: t, children: n } = e; const r = (0, $.wA)(),
                            [a, o] = (0, X.useState)(0),
                            [i, l] = (0, X.useState)(zn),
                            [s, c] = (0, X.useState)(0),
                            [d, u] = (0, X.useState)(0),
                            [h, m] = (0, X.useState)(null),
                            { openDialog: p } = (0, J.A)("errorDialog"),
                            { openDialog: f } = (0, J.A)("trialExpiredDialog"),
                            { closeDialog: v } = (0, J.A)("upgradeDialog"),
                            g = (0, $.d4)(bn.VF),
                            y = (0, $.d4)(bn.$u); let b = null === g || void 0 === g ? void 0 : g.id; const w = (0, yn.u)([(0, te.Io)(), (0, te.BC)({ resource: "settings", activeTab: "reports" })]),
                            [z, x] = (0, X.useState)({ country: "none", state: "" }),
                            A = () => { var e;
                                g && "trialing" === g.status && (null === (e = g.trialInfo) || void 0 === e ? void 0 : e.daysRemaining) < 1 ? (v(), f()) : v() },
                            k = async e => { const { error: t, payload: n } = await r(vn.AV.getUpgradeCost({ licenseId: b, planId: e })); if (!t) { const { subscription: { upgradeCost: e, prorationDate: t, upgradeTax: r } } = n;
                                    c(e || 0), u(r || 0), m(t) } return { error: t, payload: n } }, [S, M] = (0, ve.A)(k), [E, C] = (0, ve.A)((async () => { const { planId: e } = i, t = null !== w && void 0 !== w && w.isExact ? "reports" : "", { error: n, payload: a } = await r(vn.AV.createCheckoutSession({ licenseId: b, planId: e, countryCode: z.country, provinceCode: z.state, prorationDate: h, source: t })); if (!n) { const { subscription: { sessionId: e, redirectUrl: t } } = a; if (t) window.location.assign(t);
                                    else if (e) { const t = await wn;
                                        await t.redirectToCheckout({ sessionId: e }) } } })); return (0, X.useEffect)((() => { Ye.A.trackEvent({ eventName: "UPGRADE_DIALOG_OPEN" }) }), []), (0, be.jsxs)(be.Fragment, { children: [n, (0, be.jsxs)(et, { open: t, children: [(0, be.jsxs)(Ze.A, { id: "upgrade-dialog-title", align: "center", onClose: A, children: ["Account Upgrade", (0, be.jsx)(Ut, { activeStep: a, alternativeLabel: !0, children: ["Select Plan", "Review & Pay"].map((e => (0, be.jsx)(Kt, { children: (0, be.jsx)(Xt, { children: e }) }, e))) })] }), (0, be.jsx)(Ue.A, { children: 0 === a ? (0, be.jsx)(be.Fragment, { children: (0, be.jsx)(Nt, { onSelectPlan: async e => { const { planId: t, planType: n } = e || {}; if ("enterprise" === n) { const e = (e => null !== e && void 0 !== e && e.username && null !== e && void 0 !== e && e.firstName && null !== e && void 0 !== e && e.lastName ? "https://www.organimi.com/request-a-quote/?firstname=".concat(null === e || void 0 === e ? void 0 : e.firstName, "&lastname=").concat(null === e || void 0 === e ? void 0 : e.lastName, "&email=").concat(null === e || void 0 === e ? void 0 : e.username) : "https://www.organimi.com/request-a-quote/")(y); return window.open(e, "_self") } if (l(e), "free" !== g.paidPlanType) { var r; const { payload: e, error: n } = await k(t); if (n) return p({ title: "Cannot Select Plan", message: "Something seems to be wrong connecting to our payment provider. Contact <NAME_EMAIL> for more information." }); if (((null === e || void 0 === e || null === (r = e.subscription) || void 0 === r ? void 0 : r.endingBalance) || 0) < 0) return p({ title: "Cannot Select Plan", message: "Due to your existing plan, in-app downgrades currently require changes to be manually configured by our support team.  <NAME_EMAIL> with your requested changes" }) } o((e => e + 1)) }, activeLicense: g }) }) : (0, be.jsxs)(ke.A, { container: !0, alignItems: "flex-start", children: [(0, be.jsxs)(ke.A, { item: !0, md: 4, children: [(0, be.jsx)(qe.A, { mb: 2, children: (0, be.jsx)(He.A, { variant: "h1", children: "Selected Plan" }) }), (0, be.jsx)(Jt, { planType: i.planType, planSize: i.userTier })] }), (0, be.jsxs)(ke.A, { item: !0, md: 8, children: [(0, be.jsx)(qe.A, { mb: 2, children: (0, be.jsx)(He.A, { variant: "h1", children: "Summary of Charges" }) }), (0, be.jsx)(fn, { plan: i, upgradeCost: s, upgradeTax: d, customerLocation: z, activeLicense: g, handleChangeState: e => { x((t => ({ country: t.country, state: e.target.value }))) }, handleChangeCountry: e => { x((() => { const t = e.target.value; let n; return "CA" === t && (n = "ON"), "US" === t && (n = "NY"), { state: n, country: e.target.value } })) } })] })] }) }), (0, be.jsx)(Ge.A, { children: (0, be.jsxs)(ke.A, { container: !0, alignItems: "center", justifyContent: "center", children: [(0, be.jsx)(qe.A, { mr: 2, children: (0, be.jsx)(Ke.A, { variant: "outlined", color: "primary", disabled: S, onClick: 0 === a ? A : () => { o((e => e - 1)) }, children: "".concat(0 === a ? "Cancel" : "Back") }) }), (0, be.jsxs)(qe.A, { ml: 2, children: [1 === a && (0, be.jsx)(Ke.A, { variant: "contained", color: "primary", disabled: S || E || "none" === (null === z || void 0 === z ? void 0 : z.country) && "free" === (null === g || void 0 === g ? void 0 : g.paidPlanType), onClick: C, children: "Pay & Upgrade" }), 0 === a && (0, be.jsx)(Ke.A, { variant: "contained", color: "primary", href: "https://www.organimi.com/questions/", target: "_blank", children: "View FAQs" })] })] }) })] })] }) }; var An = n(66434); const kn = X.lazy((() => (0, An.D)((() => n.e(1620).then(n.bind(n, 51620)))))),
                    Sn = X.lazy((() => (0, An.D)((() => n.e(6346).then(n.bind(n, 96346)))))),
                    Mn = X.lazy((() => (0, An.D)((() => Promise.all([n.e(8786), n.e(9587)]).then(n.bind(n, 15693)))))),
                    En = X.lazy((() => (0, An.D)((() => n.e(3760).then(n.bind(n, 53760)))))),
                    Cn = X.lazy((() => (0, An.D)((() => n.e(3657).then(n.bind(n, 63657)))))),
                    Tn = X.lazy((() => (0, An.D)((() => n.e(2298).then(n.bind(n, 92298)))))),
                    Hn = X.lazy((() => (0, An.D)((() => Promise.resolve().then(n.bind(n, 5560)))))),
                    Ln = X.lazy((() => (0, An.D)((() => n.e(8911).then(n.bind(n, 18911)))))),
                    In = X.lazy((() => (0, An.D)((() => n.e(248).then(n.bind(n, 50248)))))),
                    jn = X.lazy((() => (0, An.D)((() => n.e(2394).then(n.bind(n, 52394)))))),
                    Vn = X.lazy((() => (0, An.D)((() => n.e(4883).then(n.bind(n, 64883)))))),
                    On = X.lazy((() => (0, An.D)((() => n.e(8409).then(n.bind(n, 18409)))))),
                    Rn = X.lazy((() => (0, An.D)((() => n.e(4806).then(n.bind(n, 14806)))))),
                    Pn = X.lazy((() => (0, An.D)((() => n.e(8913).then(n.bind(n, 98913)))))),
                    Dn = X.lazy((() => (0, An.D)((() => n.e(8705).then(n.bind(n, 18705)))))),
                    Fn = X.lazy((() => (0, An.D)((() => n.e(7826).then(n.bind(n, 97826)))))),
                    Nn = X.lazy((() => (0, An.D)((() => n.e(5277).then(n.bind(n, 5277)))))),
                    _n = X.lazy((() => (0, An.D)((() => n.e(1649).then(n.bind(n, 1649)))))),
                    Bn = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1139), n.e(4279)]).then(n.bind(n, 74279)))))),
                    Wn = X.lazy((() => (0, An.D)((() => n.e(353).then(n.bind(n, 60353)))))),
                    Un = X.lazy((() => (0, An.D)((() => n.e(7132).then(n.bind(n, 7132)))))),
                    qn = X.lazy((() => (0, An.D)((() => n.e(6710).then(n.bind(n, 46710)))))),
                    Gn = X.lazy((() => (0, An.D)((() => Promise.all([n.e(6996), n.e(1566)]).then(n.bind(n, 81566)))))),
                    Kn = X.lazy((() => (0, An.D)((() => n.e(7920).then(n.bind(n, 57920)))))),
                    Zn = X.lazy((() => (0, An.D)((() => n.e(8855).then(n.bind(n, 68855)))))),
                    Yn = X.lazy((() => (0, An.D)((() => n.e(9868).then(n.bind(n, 39868)))))),
                    Xn = X.lazy((() => (0, An.D)((() => n.e(2884).then(n.bind(n, 22884)))))),
                    $n = X.lazy((() => (0, An.D)((() => Promise.resolve().then(n.bind(n, 649)))))),
                    Qn = X.lazy((() => (0, An.D)((() => Promise.resolve().then(n.bind(n, 14279)))))),
                    Jn = X.lazy((() => (0, An.D)((() => n.e(2518).then(n.bind(n, 62518)))))),
                    er = X.lazy((() => (0, An.D)((() => n.e(9597).then(n.bind(n, 99597)))))),
                    tr = X.lazy((() => (0, An.D)((() => n.e(6928).then(n.bind(n, 76928)))))),
                    nr = X.lazy((() => (0, An.D)((() => n.e(5938).then(n.bind(n, 55938)))))),
                    rr = X.lazy((() => (0, An.D)((() => n.e(3691).then(n.bind(n, 73691)))))),
                    ar = X.lazy((() => (0, An.D)((() => n.e(4381).then(n.bind(n, 14381)))))),
                    or = X.lazy((() => (0, An.D)((() => Promise.all([n.e(766), n.e(9321), n.e(8385)]).then(n.bind(n, 48385)))))),
                    ir = X.lazy((() => (0, An.D)((() => n.e(5380).then(n.bind(n, 35380)))))),
                    lr = X.lazy((() => (0, An.D)((() => n.e(1222).then(n.bind(n, 21222)))))),
                    sr = X.lazy((() => (0, An.D)((() => n.e(6142).then(n.bind(n, 36142)))))),
                    cr = X.lazy((() => (0, An.D)((() => n.e(5369).then(n.bind(n, 55369)))))),
                    dr = X.lazy((() => (0, An.D)((() => n.e(6014).then(n.bind(n, 76014)))))),
                    ur = X.lazy((() => (0, An.D)((() => n.e(9361).then(n.bind(n, 29361)))))),
                    hr = X.lazy((() => (0, An.D)((() => n.e(8274).then(n.bind(n, 68274)))))),
                    mr = X.lazy((() => (0, An.D)((() => n.e(9080).then(n.bind(n, 9080)))))),
                    pr = X.lazy((() => (0, An.D)((() => n.e(9584).then(n.bind(n, 49584)))))),
                    fr = X.lazy((() => (0, An.D)((() => n.e(8132).then(n.bind(n, 58132)))))),
                    vr = X.lazy((() => (0, An.D)((() => n.e(9321).then(n.bind(n, 89321)))))),
                    gr = X.lazy((() => (0, An.D)((() => n.e(7909).then(n.bind(n, 57909)))))),
                    yr = X.lazy((() => (0, An.D)((() => n.e(2723).then(n.bind(n, 42723)))))),
                    br = X.lazy((() => (0, An.D)((() => n.e(2420).then(n.bind(n, 2420)))))),
                    wr = X.lazy((() => (0, An.D)((() => Promise.all([n.e(8171), n.e(8041)]).then(n.bind(n, 98041)))))),
                    zr = X.lazy((() => (0, An.D)((() => n.e(3263).then(n.bind(n, 3263)))))),
                    xr = X.lazy((() => (0, An.D)((() => Promise.all([n.e(8171), n.e(4195)]).then(n.bind(n, 84195)))))),
                    Ar = X.lazy((() => (0, An.D)((() => n.e(1448).then(n.bind(n, 91448)))))),
                    kr = X.lazy((() => (0, An.D)((() => Promise.all([n.e(8786), n.e(4456)]).then(n.bind(n, 13494)))))),
                    Sr = X.lazy((() => (0, An.D)((() => n.e(8858).then(n.bind(n, 96477)))))),
                    Mr = X.lazy((() => (0, An.D)((() => n.e(2998).then(n.bind(n, 72998)))))),
                    Er = X.lazy((() => (0, An.D)((() => n.e(2497).then(n.bind(n, 22497)))))),
                    Cr = X.lazy((() => (0, An.D)((() => n.e(5167).then(n.bind(n, 45167)))))),
                    Tr = X.lazy((() => (0, An.D)((() => n.e(4352).then(n.bind(n, 24352)))))),
                    Hr = X.lazy((() => (0, An.D)((() => n.e(491).then(n.bind(n, 40491)))))),
                    Lr = X.lazy((() => (0, An.D)((() => n.e(9395).then(n.bind(n, 79395)))))),
                    Ir = X.lazy((() => (0, An.D)((() => n.e(3194).then(n.bind(n, 13194)))))),
                    jr = X.lazy((() => (0, An.D)((() => n.e(8053).then(n.bind(n, 78053)))))),
                    Vr = X.lazy((() => (0, An.D)((() => n.e(9755).then(n.bind(n, 89755)))))),
                    Or = X.lazy((() => (0, An.D)((() => n.e(6940).then(n.bind(n, 46940)))))),
                    Rr = X.lazy((() => (0, An.D)((() => n.e(4430).then(n.bind(n, 94430)))))),
                    Pr = X.lazy((() => (0, An.D)((() => n.e(9040).then(n.bind(n, 29040)))))),
                    Dr = X.lazy((() => (0, An.D)((() => n.e(7326).then(n.bind(n, 77326)))))),
                    Fr = X.lazy((() => (0, An.D)((() => Promise.resolve().then(n.bind(n, 94251)))))),
                    Nr = X.lazy((() => (0, An.D)((() => n.e(8248).then(n.bind(n, 58248)))))),
                    _r = X.lazy((() => (0, An.D)((() => n.e(9620).then(n.bind(n, 79620)))))),
                    Br = X.lazy((() => (0, An.D)((() => n.e(9895).then(n.bind(n, 49895)))))),
                    Wr = X.lazy((() => (0, An.D)((() => n.e(4259).then(n.bind(n, 34259)))))),
                    Ur = X.lazy((() => (0, An.D)((() => n.e(5858).then(n.bind(n, 35858)))))),
                    qr = X.lazy((() => (0, An.D)((() => n.e(6222).then(n.bind(n, 76222)))))),
                    Gr = X.lazy((() => (0, An.D)((() => n.e(2315).then(n.bind(n, 22315)))))),
                    Kr = X.lazy((() => (0, An.D)((() => n.e(927).then(n.bind(n, 927)))))),
                    Zr = X.lazy((() => (0, An.D)((() => n.e(3335).then(n.bind(n, 23335)))))),
                    Yr = X.lazy((() => (0, An.D)((() => n.e(1950).then(n.bind(n, 1950)))))),
                    Xr = X.lazy((() => (0, An.D)((() => n.e(2470).then(n.bind(n, 82470)))))),
                    $r = X.lazy((() => (0, An.D)((() => n.e(5862).then(n.bind(n, 55862)))))),
                    Qr = X.lazy((() => (0, An.D)((() => n.e(8665).then(n.bind(n, 38665)))))),
                    Jr = X.lazy((() => (0, An.D)((() => Promise.all([n.e(3649), n.e(6808)]).then(n.bind(n, 86808)))))),
                    ea = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1783), n.e(1249), n.e(600)]).then(n.bind(n, 45342)))))),
                    ta = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1802), n.e(955)]).then(n.bind(n, 50955)))))),
                    na = X.lazy((() => (0, An.D)((() => n.e(8893).then(n.bind(n, 78893)))))),
                    ra = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1802), n.e(7512)]).then(n.bind(n, 37512)))))),
                    aa = X.lazy((() => (0, An.D)((() => n.e(5647).then(n.bind(n, 95647)))))),
                    oa = X.lazy((() => (0, An.D)((() => n.e(3679).then(n.bind(n, 3679)))))),
                    ia = X.lazy((() => (0, An.D)((() => n.e(8444).then(n.bind(n, 48444)))))),
                    la = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1249), n.e(526)]).then(n.bind(n, 70526)))))),
                    sa = X.lazy((() => (0, An.D)((() => n.e(8658).then(n.bind(n, 28658)))))),
                    ca = X.lazy((() => (0, An.D)((() => n.e(9955).then(n.bind(n, 49955)))))),
                    da = X.lazy((() => (0, An.D)((() => n.e(2180).then(n.bind(n, 82180)))))),
                    ua = X.lazy((() => (0, An.D)((() => n.e(9051).then(n.bind(n, 39051)))))),
                    ha = X.lazy((() => (0, An.D)((() => n.e(5250).then(n.bind(n, 85250)))))),
                    ma = X.lazy((() => (0, An.D)((() => n.e(9138).then(n.bind(n, 59138)))))),
                    pa = X.lazy((() => (0, An.D)((() => Promise.all([n.e(1802), n.e(1249), n.e(1704)]).then(n.bind(n, 91704)))))),
                    fa = X.lazy((() => (0, An.D)((() => n.e(2650).then(n.bind(n, 82650)))))),
                    va = X.lazy((() => (0, An.D)((() => n.e(8780).then(n.bind(n, 48780)))))),
                    ga = X.lazy((() => (0, An.D)((() => n.e(9419).then(n.bind(n, 29419)))))),
                    ya = X.lazy((() => (0, An.D)((() => n.e(1407).then(n.bind(n, 91407)))))),
                    ba = X.lazy((() => (0, An.D)((() => n.e(7020).then(n.bind(n, 17020)))))),
                    wa = X.lazy((() => (0, An.D)((() => n.e(4482).then(n.bind(n, 74482)))))),
                    za = X.lazy((() => (0, An.D)((() => n.e(6863).then(n.bind(n, 66863)))))),
                    xa = X.createContext(),
                    Aa = { unsyncChart: { open: !1, props: {} }, newChart: { open: !1, props: {} }, selectOrganization: { open: !1, props: {} }, organizationMeta: { open: !1, props: {} }, orgEntity: { open: !1, props: {} }, orgEntityInfo: { open: !1, props: {} }, newOwnershipChart: { open: !1, props: {} }, secImport: { open: !1, props: {} }, ownershipManualEntry: { open: !1, props: {} }, ownershipImportFile: { open: !1, props: {} }, deleteOrganization: { open: !1, props: {} }, deleteChart: { open: !1, props: {} }, profileCard: { open: !1, props: {} }, adminCard: { open: !1, props: {} }, userCard: { open: !1, props: {} }, cropper: { open: !1, props: {} }, newRole: { open: !1, props: {} }, newRoleSimple: { open: !1, props: {} }, changeManager: { open: !1, props: {} }, memberPhoto: { open: !1, props: {} }, boardMemberPhoto: { open: !1, props: {} }, communityBuildPhoto: { open: !1, props: {} }, communityBuildCropper: { open: !1, props: {} }, unlockReports: { open: !1, props: {}, ignoreRouteEffects: !0 }, exportDialog: { open: !1, props: {} }, export2Dialog: { open: !1, props: {} }, trialExpiredDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, accountPastDueDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, changeAvatar: { open: !1, props: {} }, upgradeDialog: { open: !1, props: {} }, billingDialog: { open: !1, props: {} }, morePlanDetailsDialog: { open: !1, props: {} }, whatsNewDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, savePrintDialog: { open: !1, props: {} }, errorDialog: { open: !1, props: {} }, progressDialog: { open: !1, props: {} }, customFields: { open: !1, props: {} }, backToV6Dialog: { open: !1, props: {} }, upgradeRequiredDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, deleteRole: { open: !1, props: {} }, account: { open: !1, props: {} }, changeEmail: { open: !1, props: {} }, changePassword: { open: !1, props: {} }, cancelPlanConfirmDialog: { open: !1, props: {} }, cancelPlanSurveyDialog: { open: !1, props: {} }, copyMove: { open: !1, props: {} }, alert: { open: !1, props: {} }, applyTheme: { open: !1, props: {} }, createTheme: { open: !1, props: {} }, copyCustomFields: { open: !1, props: {} }, deleteMyAccountDialog: { open: !1, props: {} }, selectMember: { open: !1, props: {} }, assignPersonFromTalentPoolDialog: { open: !1, props: {} }, shareChartDialog: { open: !1, props: {} }, cleanupDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, cleanupSuccessDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, shareAsAFileDialog: { open: !1, props: {} }, shareAsIFrameDialog: { open: !1, props: {} }, shareChartPrivateDialog: { open: !1, props: {} }, comingSoonDialog: { open: !1, props: {} }, selectChartTemplate: { open: !1, props: {} }, showChartTemplateImageDialog: { open: !1, props: {} }, formatMigration: { open: !1, props: {}, ignoreRouteEffects: !0 }, newAliasLink: { open: !1, props: {} }, editChart: { open: !1, props: {} }, chartSettingsPlus: { open: !1, props: {} }, editOrganization: { open: !1, props: {} }, reportsSettings: { open: !1, props: {} }, duplicatePersonFoundDialog: { open: !1, props: {} }, duplicatePersonFoundSelectableDialog: { open: !1, props: {} }, downgradeDialog: { open: !1, props: {} }, moveRole: { open: !1, props: {} }, shareChartPublicSettingsDialog: { open: !1, props: {} }, genericPrompt: { open: !1, props: {} }, manualPrintRoleStackSettingsDialog: { open: !1, props: {} }, createPrintTemplateDialog: { open: !1, props: {} }, screenshotJobDialog: { open: !1, props: {} }, addMatrixHeaderDialog: { open: !1, props: {} }, backgroundJobIndicator: { open: !1, props: {}, ignoreRouteEffects: !0 }, addNewMember: { open: !1, props: {} }, editMember: { open: !1, props: {} }, addNewCommittee: { open: !1, props: {} }, editCommittee: { open: !1, props: {} }, discardChanges: { open: !1, props: {} }, sortColumn: { open: !1, props: {} }, spreadsheetHelp: { open: !1, props: {} }, snapshotDetails: { open: !1, props: {} }, importFile: { open: !1, props: {} }, tourTooltip: { open: !1, props: {}, ignoreRouteEffects: !0 }, onboardingLetsStart: { open: !1, props: {} }, onboardingUserDetailsForm: { open: !1, props: {} }, onboardingRoleVsPeople: { open: !1, props: {} }, onboardingPeopleOption: { open: !1, props: {} }, onboardingRolesOption: { open: !1, props: {} }, selectOnboardingTemplate: { open: !1, props: {} }, chartOnboardingNextSteps: { open: !1, props: {} }, onboardingCompleteImport: { open: !1, props: {} }, utilizeTemplate: { open: !1, props: {} }, toursCatalog: { open: !1, props: {} }, addPeople: { open: !1, props: {} }, importPeople: { open: !1, props: {} }, demoSpace: { open: !1, props: {} }, orgRelationDialog: { open: !1, props: {}, ignoreRouteEffects: !0 }, deactivatedAccount: { open: !1, props: {} }, canceledAccount: { open: !1, props: {} }, legendFolderDelete: { open: !1, props: {} }, createNewTemplate: { open: !1, props: {} }, directReportDirection: { open: !1, props: {} }, autoChartBuilder: { open: !1, props: {} }, verifySuggestion: { open: !1, props: {} }, rejectSuggestion: { open: !1, props: {} }, bulkApproveSuggestions: { open: !1, props: {} }, welcomeAutobuild: { open: !1, props: {} }, changeSuggestionManager: { open: !1, props: {} }, approveSuggestionBranch: { open: !1, props: {} }, approveAutobuildChart: { open: !1, props: {} } };

                function ka(e) { const t = (0, ee.useLocation)(),
                        [n, r] = (0, X.useState)(Aa),
                        { newChart: a, selectOrganization: o, organizationMeta: i, orgEntity: l, orgEntityInfo: s, deleteOrganization: c, deleteChart: d, adminCard: u, cropper: h, newRole: m, newRoleSimple: p, changeManager: f, memberPhoto: v, boardMemberPhoto: g, communityBuildPhoto: y, communityBuildCropper: b, unlockReports: w, exportDialog: z, export2Dialog: x, trialExpiredDialog: A, accountPastDueDialog: k, changeAvatar: S, upgradeDialog: M, billingDialog: E, morePlanDetailsDialog: C, whatsNewDialog: T, errorDialog: H, progressDialog: L, backgroundJobIndicator: I, customFields: j, backToV6Dialog: V, upgradeRequiredDialog: O, deleteRole: R, account: P, changeEmail: D, changePassword: F, cancelPlanConfirmDialog: N, cancelPlanSurveyDialog: _, copyMove: B, alert: W, applyTheme: U, createTheme: q, copyCustomFields: G, deleteMyAccountDialog: K, unsyncChart: Z, shareChartDialog: Y, cleanupDialog: $, cleanupSuccessDialog: Q, selectMember: J, assignPersonFromTalentPoolDialog: te, comingSoonDialog: ne, selectChartTemplate: re, showChartTemplateImageDialog: ae, formatMigration: oe, downgradeDialog: ie, moveRole: le, shareChartPublicSettingsDialog: se, addMatrixHeaderDialog: ce, genericPrompt: de, manualPrintRoleStackSettingsDialog: ue, createPrintTemplateDialog: he, screenshotJobDialog: me, addNewMember: pe, editMember: fe, addNewCommittee: ve, editCommittee: ge, discardChanges: ye, sortColumn: we, spreadsheetHelp: xe, newAliasLink: Ae, editChart: ke, chartSettingsPlus: Se, reportsSettings: Me, duplicatePersonFoundDialog: Ee, duplicatePersonFoundSelectableDialog: Ce, snapshotDetails: Te, tourTooltip: He, onboardingLetsStart: Le, onboardingUserDetailsForm: Ie, onboardingRoleVsPeople: je, onboardingPeopleOption: Oe, onboardingRolesOption: Re, selectOnboardingTemplate: Pe, chartOnboardingNextSteps: De, onboardingCompleteImport: Fe, utilizeTemplate: Ne, toursCatalog: _e, addPeople: Be, importPeople: We, demoSpace: Ue, importFile: qe, orgRelationDialog: Ge, deactivatedAccount: Ke, canceledAccount: Ze, autoChartBuilder: Ye, verifySuggestion: Xe, rejectSuggestion: $e, bulkApproveSuggestions: Qe, welcomeAutobuild: Je, changeSuggestionManager: et, approveSuggestionBranch: tt, approveAutobuildChart: nt, newOwnershipChart: rt, secImport: at, ownershipManualEntry: ot, ownershipImportFile: it, legendFolderDelete: lt, createNewTemplate: st, directReportDirection: ct } = n; return (0, X.useEffect)((() => { r((e => { let t = Object.keys(e).filter((t => e[t].ignoreRouteEffects)).reduce(((t, n) => (t[n] = e[n], t)), {}); return { ...Aa, ...t } })) }), [t]), (0, be.jsxs)(xa.Provider, { value: [n, r, Aa], children: [e.children, M.open && (0, be.jsx)(xn, { ...M.props, open: M.open }), (0, be.jsxs)(X.Suspense, { fallback: () => {}, children: [E.open && (0, be.jsx)(kn, { ...E.props, open: E.open }), C.open && (0, be.jsx)(Sn, { ...C.props, open: C.open }), a.open && (0, be.jsx)(ze, { open: a.open }), o.open && (0, be.jsx)(Dn, { open: o.open, ...o.props }), i.open && (0, be.jsx)(na, { open: i.open, ...i.props }), l.open && (0, be.jsx)(ra, { open: l.open, ...l.props }), s.open && (0, be.jsx)(aa, { open: s.open, ...s.props }), rt.open && (0, be.jsx)(oa, { open: rt.open, ...rt.props }), at.open && (0, be.jsx)(ia, { open: at.open, ...at.props }), ot.open && (0, be.jsx)(sa, { open: ot.open, ...ot.props }), it.open && (0, be.jsx)(la, { open: it.open, ...it.props }), c.open && (0, be.jsx)(Jn, { open: c.open, ...c.props }), d.open && (0, be.jsx)(gr, { open: d.open, ...d.props }), u.open && (0, be.jsx)(Ve, { ...u.props }), m.open && (0, be.jsx)(En, { open: m.open, ...m.props }), p.open && (0, be.jsx)(Cn, { open: p.open, ...p.props }), R.open && (0, be.jsx)(qn, { open: R.open, ...R.props }), j.open && (0, be.jsx)(Bn, { open: j.open, ...j.props }), f.open && (0, be.jsx)(Tn, { open: f.open, ...f.props }), v.open && (0, be.jsx)(Hn, { open: v.open, ...v.props }), y.open && (0, be.jsx)(Ar, { open: y.open, ...y.props }), g.open && (0, be.jsx)(Ln, { open: g.open, ...g.props }), z.open && (0, be.jsx)(In, { open: z.open, ...z.props }), x.open && (0, be.jsx)(jn, { open: x.open, ...x.props }), S.open && (0, be.jsx)(Rn, { ...S.props, open: S.open }), h.open && (0, be.jsx)(Mn, { ...h.props, open: h.open }), b.open && (0, be.jsx)(kr, { ...b.props, open: b.open }), w.open && (0, be.jsx)(Sr, { ...w.props, open: w.open }), A.open && (0, be.jsx)(Vn, { ...A.props, open: A.open }), k.open && (0, be.jsx)(On, { ...k.props, open: k.open }), ie.open && (0, be.jsx)(pr, { ...ie.props, open: ie.open }), T.open && (0, be.jsx)(Pn, { ...T.props, open: T.open }), L.open && (0, be.jsx)(Nn, { ...L.props, open: L.open }), V.open && (0, be.jsx)(Un, { ...V.props, open: V.open }), O.open && (0, be.jsx)(Wn, { ...O.props, open: O.open }), H.open && (0, be.jsx)(Fn, { ...H.props, open: H.open }), P.open && (0, be.jsx)(Gn, { ...P.props, open: P.open }), D.open && (0, be.jsx)(Kn, { ...D.props, open: D.open }), F.open && (0, be.jsx)(Zn, { ...F.props, open: F.open }), N.open && (0, be.jsx)(rr, { ...N.props, open: N.open }), _.open && (0, be.jsx)(ar, { ..._.props, open: _.open }), B.open && (0, be.jsx)(Yn, { open: B.open }), U.open && (0, be.jsx)($n, { ...U.props, open: U.open }), q.open && (0, be.jsx)(Qn, { ...q.props, open: q.open }), G.open && (0, be.jsx)(er, { ...G.props, open: G.open }), W.open && (0, be.jsx)(Xn, {}), K.open && (0, be.jsx)(tr, { ...K.props, open: K.open }), Z.open && (0, be.jsx)(nr, {}), te.open && (0, be.jsx)(dr, { ...te.props, open: te.open }), oe.open && (0, be.jsx)(ur, {}), J.open && (0, be.jsx)(cr, { ...J.props, open: J.open }), Y.open && (0, be.jsx)(or, { ...Y.props, open: Y.open }), $.open && (0, be.jsx)(ir, { ...$.props, open: $.open }), Q.open && (0, be.jsx)(lr, { ...Q.props, open: Q.open }), ne.open && (0, be.jsx)(sr, { ...J.props, open: ne.open }), re.open && (0, be.jsx)(hr, { open: re.open, ...re.props }), ae.open && (0, be.jsx)(mr, { open: ae.open, ...ae.props }), le.open && (0, be.jsx)(fr, { open: le.open, ...le.props }), se.open && (0, be.jsx)(vr, { open: se.open, ...se.props }), ce.open && (0, be.jsx)(zr, { open: ce.open, ...ce.props }), de.open && (0, be.jsx)(yr, { open: de.open }), ue.open && (0, be.jsx)(br, { open: ue.open, ...ue.props }), he.open && (0, be.jsx)(wr, { open: he.open, ...he.props }), me.open && (0, be.jsx)(xr, { ...me.props }), (0, be.jsx)(_n, { ...I.props, open: I.open }), pe.open && (0, be.jsx)(Mr, { open: pe.open, ...pe.props }), fe.open && (0, be.jsx)(Er, { open: fe.open, ...fe.props }), ve.open && (0, be.jsx)(Tr, { open: ve.open }), ge.open && (0, be.jsx)(Hr, { open: ge.open, ...ge.props }), ye.open && (0, be.jsx)(Cr, { open: ye.open, ...ye.props }), we.open && (0, be.jsx)(Lr, { open: we.open, ...we.props }), xe.open && (0, be.jsx)(Ir, { open: xe.open, ...xe.props }), Ae.open && (0, be.jsx)(jr, {}), ke.open && (0, be.jsx)(Vr, {}), Se.open && (0, be.jsx)(Or, {}), Me.open && (0, be.jsx)(Rr, {}), Ee.open && (0, be.jsx)(Pr, { open: Ee.open, ...Ee.props }), Ce.open && (0, be.jsx)(Dr, { open: Ce.open, ...Ce.props }), Te.open && (0, be.jsx)(Fr, { ...Te.props }), qe.open && (0, be.jsx)(ea, {}), He.open && (0, be.jsx)(Nr, { ...He.props }), Le.open && (0, be.jsx)(_r, { ...Le.props }), Ie.open && (0, be.jsx)(Br, { ...Ie.props }), je.open && (0, be.jsx)(Wr, { ...je.props }), Oe.open && (0, be.jsx)(Ur, { ...Oe.props }), Re.open && (0, be.jsx)(qr, { ...Re.props }), Pe.open && (0, be.jsx)(Gr, { ...Pe.props }), De.open && (0, be.jsx)(Kr, { ...De.props }), Fe.open && (0, be.jsx)(Zr, { ...Fe.props }), Ne.open && (0, be.jsx)(Yr, { ...Ne.props }), _e.open && (0, be.jsx)(Xr, { ..._e.props }), Be.open && (0, be.jsx)($r, { ...Be.props }), We.open && (0, be.jsx)(Qr, { ...We.props }), Ue.open && (0, be.jsx)(Jr, { ...Ue.props }), Ge.open && (0, be.jsx)(ta, { ...Ge.props }), Ke.open && (0, be.jsx)(ca, { ...Ke.props }), Ze.open && (0, be.jsx)(da, { ...Ze.props }), Ke.open && (0, be.jsx)(ca, { ...Ke.props }), Ze.open && (0, be.jsx)(da, { ...Ze.props }), lt.open && (0, be.jsx)(ua, { open: lt.open, ...lt.props }), st.open && (0, be.jsx)(ha, { open: st.open, ...st.props }), ct.open && (0, be.jsx)(ma, { open: ct.open, ...ct.props }), Ye.open && (0, be.jsx)(pa, { open: Ye.open, ...Ye.props }), Xe.open && (0, be.jsx)(fa, { open: Xe.open, ...Xe.props }), $e.open && (0, be.jsx)(va, { open: $e.open, ...$e.props }), Qe.open && (0, be.jsx)(ga, { open: Qe.open, ...Qe.props }), Je.open && (0, be.jsx)(ya, { open: Je.open, ...Je.props }), et.open && (0, be.jsx)(ba, { open: et.open, ...et.props }), (null === tt || void 0 === tt ? void 0 : tt.open) && (0, be.jsx)(wa, { open: tt.open, ...tt.props }), nt.open && (0, be.jsx)(za, { open: nt.open, ...nt.props })] })] }) } }, 49244: (e, t, n) => { "use strict";
                n.d(t, { A: () => l, O: () => o }); var r = n(65043),
                    a = n(70579); const o = (0, r.createContext)(),
                    i = {};

                function l(e) { let { children: t } = e; const [n, l] = (0, r.useState)(i); return (0, a.jsx)(o.Provider, { value: [n, l], children: t }) } }, 50882: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => g, J0: () => v, V2: () => f, iC: () => m, j4: () => p }); var r = n(65043),
                    a = n(14556),
                    o = n(42006),
                    i = n(85571),
                    l = n(31286),
                    s = n(15622),
                    c = n(94916),
                    d = n(47088),
                    u = n(8606),
                    h = n(70579); let m = function(e) { return e.FILE_SYNC = "fileSync", e }({}); const p = {
                        [m.FILE_SYNC]: { maxAutoFetchAttempts: 100, pingInterval: 2e4, shouldInvokeCallback: e => e.some((e => e.notificationTopic === d.ix.IntegrationFileSyncSuccess)), terminateAfterCallbackInvoke: !0 } },
                    f = (0, r.createContext)({}),
                    v = 12e4,
                    g = e => { let { children: t } = e; const n = (0, a.d4)(s.A),
                            g = (0, a.d4)(i.E_),
                            y = (0, a.d4)(o.VW),
                            b = (0, r.useRef)(null),
                            w = (0, r.useRef)(0),
                            z = (0, r.useRef)({
                                [m.FILE_SYNC]: null }),
                            [x, A] = (0, r.useState)(!0),
                            [k, S] = (0, r.useState)(null),
                            [M, E] = (0, r.useState)(!1),
                            [C, T] = (0, r.useState)(!1),
                            [H, L] = (0, r.useState)(10),
                            [I, j] = (0, r.useState)(5e3),
                            [V] = (0, c.A)("activeMobileTab"),
                            [O, R] = r.useState(null),
                            [P, D] = r.useState(null),
                            F = (0, a.wA)(),
                            N = () => { R({}), D("") },
                            _ = e => { var t; const { notifications: n = [] } = e.payload;
                                null !== (t = n || []) && void 0 !== t && t.length && Object.keys(m).forEach((e => { const t = m[e],
                                        r = p[t],
                                        a = z.current[t];
                                    r.shouldInvokeCallback && "function" === typeof r.shouldInvokeCallback && r.shouldInvokeCallback(n) && (null === a || void 0 === a || a(n), r.terminateAfterCallbackInvoke && (z.current[t] = null)) })) },
                            B = () => { A(!0), setTimeout((() => { null !== y && void 0 !== y && y.id && null !== n && void 0 !== n && n.id ? (C || T(!0), F(l.tZ.all({ lastNotificationId: g || "" })).then(_), M || (E(!0), j(v)), A(!1), S(new Date)) : null !== n && void 0 !== n && n.id || T(!1) }), 1e3) };
                        (0, r.useEffect)((() => { "notifications" === V && B() }), [V]); const W = () => { w.current < H ? (w.current += 1, B()) : clearInterval(b.current) };
                        (0, r.useEffect)((() => (T(!0), () => { U() })), [null === n || void 0 === n ? void 0 : n.id]); const U = () => { clearInterval(b.current), Object.keys(m).forEach((e => { const t = m[e];
                                z.current[t] = null })) }; return (0, r.useEffect)((() => { b.current && clearInterval(b.current), w.current < H ? b.current = setInterval(W, I) : U() }), [g, M, n, H, I]), (0, h.jsxs)(f.Provider, { value: { activeNotificationForDialog: O, canFetchNotifications: C, fetchedNotifications: M, getNotifications: B, handleNotificationDialogOpen: e => { var t; const n = (null === (t = d.Vc[null === e || void 0 === e ? void 0 : e.notificationTopic]) || void 0 === t ? void 0 : t.category) || "";
                                    D(n), R(e) }, handleNotificationDialogClose: N, notificationsLastUpdated: k, notificationsLoading: x, openDialogCategory: P, restartNotificationCallbackRef: z, pingIntervalState: [I, j], maxAutoFetchAttemptsState: [H, L], notificationAutoFetchCountRef: w }, children: [t, (0, h.jsx)(u.A, { open: "system" === P, notification: O, handleClose: N })] }) } }, 61342: (e, t, n) => { "use strict";
                n.d(t, { A: () => l, w: () => i }); var r = n(61531),
                    a = n(65043),
                    o = n(70579); const i = (0, a.createContext)(!1);

                function l(e) { let { children: t } = e; const [n, l] = (0, a.useState)(!1); return (0, o.jsxs)(i.Provider, { value: [n, l], children: [t, (0, o.jsx)(r.A, { visibility: "hidden", id: "pageReady", display: "none", children: n.toString() })] }) } }, 31312: (e, t, n) => { "use strict";
                n.d(t, { z: () => j, A: () => V }); var r = n(65043),
                    a = n(14556),
                    o = n(44676),
                    i = n(55357),
                    l = n(5571),
                    s = n(43862),
                    c = n(66856),
                    d = n(91688),
                    u = n(84),
                    h = n(64418),
                    m = n(43331),
                    p = n(61),
                    f = n(356),
                    v = n(19367),
                    g = n(48853),
                    y = n(78396),
                    b = n(96364),
                    w = n(98433),
                    z = n(80192),
                    x = n(6562),
                    A = n(10621),
                    k = n(39362); const S = (0, z.Mz)((e => e.organization.theme), (e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.theme }), (e => e.themes.customThemes), ((e, t, n) => { var r, a; const o = (0, k.r)(),
                            i = o && (null === (r = o[0]) || void 0 === r ? void 0 : r.id),
                            l = n && (null === (a = n[0]) || void 0 === a ? void 0 : a.id); return t || e || i || l })),
                    M = (0, z.Mz)((e => e.search.selectedResult), S, x.A, ((e, t, n) => r => { let { topRole: a } = r; const o = []; var i; if (a) o.push({ id: a.id, name: (0, A.mA)(a), personName: (null === (i = n[a.members[0]]) || void 0 === i ? void 0 : i.name) || "" });
                        else if (null !== e && void 0 !== e && e.id) { var l;
                            o.push({ id: e.id, name: (0, A.mA)(e), personName: (null === (l = n[e.members[0]]) || void 0 === l ? void 0 : l.name) || "" }) } return { fileType: "pdf", setup: { topRoles: o, pageBreaks: {}, orientation: "landscape", range: { type: "roles", departments: [], roles: [], nLevels: 0, nTopLevels: 0 }, page: { size: { auto: !0, value: "auto", label: "Auto" }, multiOptions: {}, margins: {}, orientation: "landscape", paginationType: "single" }, resource: "chart", optimizeFor: "screen" }, layout: { chart: { roleColors: !0, roleCounts: !0, themeId: t }, legend: { visible: !1 }, meta: { header: { visible: !1 }, footer: { visible: !1 } } } } })); var E = n(66588),
                    C = n(3523),
                    T = n(10268),
                    H = n(70579); const L = "https://organimi.zendesk.com/",
                    I = e => (0, H.jsxs)(b.A, { display: "inline", children: ["Are you sure you want to delete", " ", (0, H.jsx)(b.A, { display: "inline", weight: "bold", children: (0, A.mA)(e) }), "?"] }),
                    j = r.createContext();

                function V(e) { let { children: t, orgId: n, chartId: o } = e; const [i, l] = (0, r.useState)(), b = (0, d.useHistory)(), z = (0, a.wA)(), x = (0, d.useParams)(), k = (0, a.d4)(v.Mk), S = (0, a.d4)(v.Jf), { openDialog: V } = (0, u.A)("errorDialog"), { openDialog: R } = (0, u.A)("newRoleSimple"), { openDialog: P } = (0, u.A)("deleteRole"), { openDialog: D } = (0, u.A)("newRole"), { openDialog: F } = (0, u.A)("copyMove"), { openDialog: N } = (0, u.A)("unsyncChart"), { openDialog: _ } = (0, u.A)("screenshotJobDialog"), { openDialog: B } = (0, u.A)("addMatrixHeaderDialog"), { handleRejectRelatedSuggestions: W, handleChangeRoleManager: U } = (0, T.n)(), { confirmAction: q } = (0, h.A)(), { userHasMinAccess: G } = (0, g.A)(), { show: K } = (0, E.A)(), { resourceAction: Z } = x || {}, Y = Z === y.uI.AI_INSIGHTS, X = (0, a.d4)(M), $ = (0, a.d4)(v.kw), Q = $ && $.syncEnabled, J = "protected" === (null === x || void 0 === x ? void 0 : x.base) || (null === S || void 0 === S ? void 0 : S.generateRoleLink) && "public" === (null === x || void 0 === x ? void 0 : x.base) || !1, { target: ee, hiddenMoreOptions: te, disabledMoreOptions: ne, role: re, members: ae } = i || {}, { openDialog: oe } = (0, u.A)("changeManager"), { openDialog: ie } = (0, u.A)("newAliasLink"), le = { edit: () => {
                            [y.mv.TEAM, y.mv.FUNCTION].includes(null === re || void 0 === re ? void 0 : re.type) ? B({ itemType: null === re || void 0 === re ? void 0 : re.type, role: re, mode: "edit" }) : D({ role: re, members: ae }) }, remove: () => { q({ title: "Delete Role", message: "Are you sure you want to delete this role?", confirmButtonText: "Delete Role", cancelButtonText: "Cancel", execFunc: async () => { await W(re), await z(s.h7.removeRole({ orgId: n, chartId: o, roleId: re.id })), K("Role deleted successfully", "success") } }) }, assignPerson: () => { Q ? N({ chartId: o }) : D({ role: re, members: ae }) }, replaceWithVacant: () => { Q ? N({ chartId: o }) : q({ title: "Make Role Vacant", message: "The role will remain as a vacant position. The people will remain in the roster", execFunc: () => { z(s.h7.dropPeople({ orgId: n, chartId: o, roleId: re.id })) } }) }, move: () => { U(re) } }, se = { createNewManager: () => { Q || Q ? N({ chartId: o }) : R({ rel: { id: null === re || void 0 === re ? void 0 : re.id, position: "above" } }) }, edit: () => {
                            [y.mv.TEAM, y.mv.FUNCTION].includes(null === re || void 0 === re ? void 0 : re.type) ? B({ itemType: null === re || void 0 === re ? void 0 : re.type, role: re, mode: "edit" }) : D({ role: re, members: ae }) }, duplicateStructure: () => { Q ? N({ chartId: o }) : [y.mv.TEAM, y.mv.FUNCTION].includes(null === re || void 0 === re ? void 0 : re.type) ? B({ itemType: null === re || void 0 === re ? void 0 : re.type, role: re, mode: "duplicate", duplicateParams: { roleId: null === re || void 0 === re ? void 0 : re.id } }) : D({ role: re, members: ae }) }, move: () => { Q ? N({ chartId: o }) : oe({ handleChange: (e, t) => { z(s.h7.update({ orgId: n, chartId: o, data: { role: { ...re, parent: e }, moveHierarchy: t, moveOne: !t } })) }, roleId: re.id, currentParentId: re.parent }) }, remove: () => { Q || Q ? N({ chartId: o }) : null !== re && void 0 !== re && re.children && re.children.length ? P({ role: re, orgId: n, chartId: o }) : q({ title: "Delete Role", message: I(re), confirmButtonText: "Yes", cancelButtonText: "No", execFunc: async () => await z(s.h7.removeRole({ orgId: n, chartId: o, roleId: re.id })) }) }, assignPerson: () => { Q ? N({ chartId: o }) : D({ role: re, members: ae }) }, replaceWithVacant: () => { Q ? N({ chartId: o }) : q({ title: "Make Role Vacant", message: "The role will remain as a vacant position. The people will remain in the roster", execFunc: () => { z(s.h7.dropPeople({ orgId: n, chartId: o, roleId: re.id })) } }) }, print: () => { f.A.trackEvent({ eventName: "PRINT_FROM_HERE_CLICK" }), b.push("".concat((0, c.Qo)({ ...x || {}, orgId: n, chartId: o, resource: "chart", base: "protected" }), "?topRole=").concat(re.id)) }, screenshot: () => {
                            (async () => { var e; const t = X({ topRole: re }),
                                    { error: r, payload: a } = await z(w.kv.addPrintJob({ orgId: n, chartId: o, type: t.fileType, data: { ...t, pageCount: 1 } })); if (r) return V({ title: "Error", message: "Could not generate quick screenshot." }); if (null !== a && void 0 !== a && null !== (e = a.currentRequested) && void 0 !== e && e.id) { const { currentRequested: { id: e, fileCode: t } } = a;
                                    _({ jobId: e, fileCode: t }) } })() }, createAlias: () => { ie({ roleId: re.id, orgId: n, chartId: re.chart, defaultRole: re, defaultName: "".concat((0, A.mA)(re), " (").concat(k.name, ")") }) }, newchart: () => { F({ orgId: n, chartId: o, roleId: re.id, chartName: (0, A.mA)(re), selector: m.Ot }) }, pin: () => { z((0, p.EJ)({ role: re })) }, help: () => { window.open(L, "_blank") }, mergeChart: () => { Q ? N({ chartId: o }) : q({ title: "Merge Chart", message: "This action will move all the roles from the referenced chart into this chart, removing the old chart.", execFunc: () => { z(s.h7.mergeChartLink({ embeddedChartId: re.embedded_chart, roleId: re.id, orgId: n, chartId: re.chart })) } }) }, generateLink: () => { const e = (0, C.cl)({ role: re.id }, ["email"]);
                            navigator.clipboard.writeText(e), K("Link copied to clipboard") } }; return (0, H.jsxs)(j.Provider, { value: [i, l], children: [t, (null === i || void 0 === i ? void 0 : i.target) && (0, H.jsx)(O, { disabled: ne, hidden: te, handleOptionClick: e => async () => { const t = Y ? le : se;
                                await ("function" === typeof t[e] && t[e]()), l(null) }, onClose: () => { l(null) }, anchorEl: ee, editAccess: G(y.td.EDITOR), adminAccess: G(y.td.ADMIN), showGenerateLinkOption: J })] }) } const O = e => { let { handleOptionClick: t, anchorEl: n, onClose: r, disabled: a = {}, hidden: s = {}, editAccess: c, adminAccess: d, showGenerateLinkOption: u } = e; return (0, H.jsxs)(o.A, { id: "simple-menu", anchorEl: n, keepMounted: !0, open: Boolean(n), onClose: r, children: [!s.replaceWithVacant && c && (0, H.jsx)(i.A, { disabled: a.replaceWithVacant, onClick: t("replaceWithVacant"), children: "Replace with Vacant Role" }), !s.assignPerson && c && (0, H.jsxs)(H.Fragment, { children: [(0, H.jsx)(i.A, { disabled: a.assignPerson, onClick: t("assignPerson"), children: "Assign Person" }), (0, H.jsx)(l.A, {})] }), c && (0, H.jsxs)("div", { children: [(0, H.jsx)(i.A, { onClick: t("edit"), children: "Edit Role" }), !s.duplicateStructure && (0, H.jsx)(i.A, { onClick: t("duplicateStructure"), children: "Duplicate Structure" }), !s.createNewManager && (0, H.jsx)(i.A, { onClick: t("createNewManager"), children: "Create New Manager" }), (0, H.jsx)(l.A, {}), !s.moveRole && (0, H.jsx)(i.A, { onClick: t("move"), children: "Move Role" }), (0, H.jsx)(i.A, { onClick: t("remove"), children: "Delete Role" }), (0, H.jsx)(l.A, {}), !s.printFromHere && (0, H.jsxs)(H.Fragment, { children: [(0, H.jsx)(i.A, { onClick: t("print"), children: "Print from here" }), (0, H.jsx)(i.A, { onClick: t("screenshot"), children: "Screenshot As Is" }), (0, H.jsx)(l.A, {})] })] }), !s.saveAlias && d && (0, H.jsx)(i.A, { onClick: t("createAlias"), children: "Link Subchart" }), !s.pinRole && (0, H.jsx)(i.A, { onClick: t("pin"), children: "Pin Role" }), !s.saveSubchart && d && (0, H.jsx)(i.A, { onClick: t("newchart"), children: "Save as New Chart" }), !s.mergeChart && d && (0, H.jsx)(i.A, { onClick: t("mergeChart"), children: "Merge Linked Chart" }), !s.generateLink && u && (0, H.jsx)(i.A, { onClick: t("generateLink"), children: "Generate Direct URL" }), (0, H.jsx)(l.A, {}), (0, H.jsx)(i.A, { onClick: t("help"), children: "Help" })] }) } }, 54091: (e, t, n) => { "use strict";
                n.d(t, { $: () => c, A: () => d }); var r = n(65043),
                    a = n(18729),
                    o = n(67254),
                    i = n(37294),
                    l = n(70579); const s = { open: !1, message: "", severity: "success", duration: 6e3, anchorOrigin: { vertical: "bottom", horizontal: "center" } },
                    c = r.createContext([s, () => {}]);

                function d(e) { const [t, n] = (0, r.useState)(s), d = () => { n({ ...s, severity: t.severity }) }; let u; switch (t.severity) {
                        case "success":
                        default:
                            u = i.Qs.Success[600]; break;
                        case "error":
                            u = i.Qs.Error[500]; break;
                        case "warning":
                            u = i.Qs.Warning[500]; break;
                        case "info":
                            u = i.Qs.Info[600] } return (0, l.jsxs)(c.Provider, { value: [t, n], children: [e.children, (0, l.jsx)(a.A, { open: t.open, autoHideDuration: t.duration, onClose: d, anchorOrigin: t.anchorOrigin, children: (0, l.jsx)(o.A, { onClose: d, severity: t.severity, variant: "filled", sx: { backgroundColor: u, color: i.Qs.Neutrals[0] }, children: t.message }) })] }) } }, 56650: (e, t, n) => { "use strict";
                n.d(t, { A: () => i, y: () => o }); var r = n(65043),
                    a = n(70579); const o = (0, r.createContext)(null);

                function i(e) { let { children: t } = e; const [n, i] = (0, r.useState)("setup"); return (0, a.jsx)(o.Provider, { value: { selectedSetting: [n, i] }, children: t }) } }, 27999: (e, t, n) => { "use strict";
                n.d(t, { A: () => m, P: () => u }); var r = n(65043),
                    a = n(66856),
                    o = n(78396),
                    i = n(36138),
                    l = n(89656),
                    s = n(14556),
                    c = n(55005),
                    d = n(70579); const u = r.createContext(),
                    h = "asc";

                function m(e) { const t = (0, s.d4)(c.A).find((e => "lastName" === e.name && "member" === e.model)),
                        n = ["member", (null === t || void 0 === t ? void 0 : t.id) || "lastName"],
                        [m, p] = (0, r.useState)(!1),
                        [f, v] = (0, r.useState)(),
                        [g, y] = (0, r.useState)(h),
                        [b, w] = (0, r.useState)(n),
                        [z, x] = (0, r.useState)(),
                        [A, k] = (0, r.useState)(""),
                        [S, M] = (0, r.useState)({}),
                        { params: { base: E, resource: C, resourceAction: T } } = (0, i.u)([(0, a.si)()]),
                        H = (0, s.d4)(l.P0),
                        { directory: L, photoboard: I } = (null === H || void 0 === H ? void 0 : H.data) || {};
                    (0, r.useEffect)((() => { w(["member", (null === t || void 0 === t ? void 0 : t.id) || "lastName"]) }), [t]); return (0, r.useEffect)((() => { if (T === o.uI.PRINT_PREVIEW || E === o.uI.PRINT) p(!1), v(null), x(null), k(""), M({}), y(h), w(n);
                        else if (T === o.uI.DEFAULT || T === o.uI.THEME) { var e, t, r; if ("directory" === C)
                                if (null !== L && void 0 !== L && L.groupBy ? v(null === L || void 0 === L ? void 0 : L.groupBy) : v(null), null !== L && void 0 !== L && L.orderBy) w([null === L || void 0 === L || null === (e = L.orderBy) || void 0 === e ? void 0 : e.model, null === L || void 0 === L || null === (t = L.orderBy) || void 0 === t ? void 0 : t.attr]), y(null === L || void 0 === L || null === (r = L.orderBy) || void 0 === r ? void 0 : r.order);
                                else y(h), w(n);
                            else if ("photoboard" === C) { var a, i, l; if (null !== I && void 0 !== I && I.groupBy ? v(null === I || void 0 === I ? void 0 : I.groupBy) : v(null), null !== I && void 0 !== I && I.orderBy) w([null === I || void 0 === I || null === (a = I.orderBy) || void 0 === a ? void 0 : a.model, null === I || void 0 === I || null === (i = I.orderBy) || void 0 === i ? void 0 : i.attr]), y(null === I || void 0 === I || null === (l = I.orderBy) || void 0 === l ? void 0 : l.order);
                                else y(h), w(n) } } }), [E, T, L, I, C]), (0, r.useEffect)((() => { if ("directory" === C) { var e, t, n; if (null !== L && void 0 !== L && L.orderBy) w([null === L || void 0 === L || null === (e = L.orderBy) || void 0 === e ? void 0 : e.model, null === L || void 0 === L || null === (t = L.orderBy) || void 0 === t ? void 0 : t.attr]), y(null === L || void 0 === L || null === (n = L.orderBy) || void 0 === n ? void 0 : n.order);
                            null !== L && void 0 !== L && L.groupBy && v(L.groupBy) } if ("photoboard" === C) { var r, a, o; if (null !== I && void 0 !== I && I.orderBy) w([null === I || void 0 === I || null === (r = I.orderBy) || void 0 === r ? void 0 : r.model, null === I || void 0 === I || null === (a = I.orderBy) || void 0 === a ? void 0 : a.attr]), y(null === I || void 0 === I || null === (o = I.orderBy) || void 0 === o ? void 0 : o.order);
                            null !== I && void 0 !== I && I.groupBy && v(I.groupBy) } }), [C]), (0, d.jsx)(u.Provider, { value: { showVacantRolesState: [m, p], groupByState: [f, v], orderState: [g, y], orderByState: [b, w], selectedGroupState: [z, x], queryState: [A, k], advancedQueryState: [S, M] }, children: e.children }) } }, 9037: (e, t, n) => { "use strict";
                n.d(t, { A: () => i, r: () => o }); var r = n(65043),
                    a = n(70579); const o = r.createContext(),
                    i = e => { let { children: t } = e; const [n, i] = (0, r.useState)("list"); return (0, a.jsx)(o.Provider, { value: { viewOption: n, setViewOption: i }, children: t }) } }, 19367: (e, t, n) => { "use strict";
                n.d(t, { G0: () => p, Jf: () => g, LL: () => u, Mk: () => s, Pq: () => c, Qn: () => f, Uq: () => v, ZT: () => m, Zx: () => h, iM: () => l, jb: () => i, kw: () => d }); var r = n(80192),
                    a = n(69219),
                    o = n(78396); const i = e => e.chart.visibleContainerWidth,
                    l = e => e.chart.visibleQuadrant,
                    s = e => { const { info: t } = e.chart; return t },
                    c = e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.id },
                    d = e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.chartIntegration },
                    u = (0, r.Mz)((e => e.chart.zoom), (e => e.chart.printPreviewZoom), a.uo, ((e, t, n) => "printPreview" === n ? t : e)),
                    h = (0, r.Mz)((e => { var t; return null === (t = e.chart.info) || void 0 === t ? void 0 : t.legend }), (e => e)),
                    m = e => { var t; return null === e || void 0 === e || null === (t = e.chart) || void 0 === t ? void 0 : t.chartTemplates },
                    p = e => { var t, n; return (null === e || void 0 === e || null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.type) || o.XD.TRADITIONAL },
                    f = (0, r.Mz)(s, (e => { var t, n; return { matrixTopLabel: (null === e || void 0 === e || null === (t = e.displaySettings) || void 0 === t ? void 0 : t.matrixTopLabel) || o.dd.FUNCTION, matrixSideLabel: (null === e || void 0 === e || null === (n = e.displaySettings) || void 0 === n ? void 0 : n.matrixSideLabel) || o.dd.TEAM } })),
                    v = (0, r.Mz)(s, (e => { var t; return (null === e || void 0 === e || null === (t = e.displaySettings) || void 0 === t ? void 0 : t.layout) || "topdown" })),
                    g = ((0, r.Mz)(s, (e => { var t; return { boardOfDirectorsSideLabels: null === e || void 0 === e || null === (t = e.displaySettings) || void 0 === t ? void 0 : t.boardOfDirectorsSideLabels } })), e => { var t; return null === (t = e.chart) || void 0 === t ? void 0 : t.sharePermission }) }, 7743: (e, t, n) => { "use strict";
                n.d(t, { $B: () => w, C1: () => p, J0: () => m, KN: () => f, Op: () => k, PE: () => A, Xi: () => S, Z7: () => b, bH: () => x, dS: () => d, gJ: () => c, gP: () => v, ge: () => y, kA: () => s, m1: () => z, rq: () => g }); var r = n(80192),
                    a = n(10621),
                    o = n(64495),
                    i = n(55005),
                    l = n(42006); const s = i.A,
                    c = o.o,
                    d = (0, r.Mz)(s, (e => [...a.TU, ...e])),
                    u = (0, r.Mz)(s, (e => [...a.OX, ...e])),
                    h = (0, r.Mz)(s, (e => [...a.wM, ...e])),
                    m = (0, r.Mz)(s, (e => e.reduce(((e, t) => (e[t.id] = t, e)), {}))),
                    p = (0, r.Mz)(s, (e => e.reduce(((e, t) => (e[t.id] = t, e)), a.TU.reduce(((e, t) => (e[t.id] = t, e)), {})))),
                    f = (0, r.Mz)(s, (e => e.filter((e => e.model === a.A2.ROLE)))),
                    v = (0, r.Mz)(s, (e => e.filter((e => e.model === a.A2.MEMBER)))),
                    g = ((0, r.Mz)(s, (e => e)), (0, r.Mz)(s, (e => e.filter((e => e.model === a.A2.MEMBER && e.isDefault))))),
                    y = (0, r.Mz)(s, l.eW, ((e, t) => { let n = { member: ["name"], role: ["members"] }; return e.filter((e => !["public", "viewer"].includes(t) || e.searchable)).forEach((e => { n[e.model].push(e.id) })), n })),
                    b = (0, r.Mz)(d, (e => (0, a.JM)(e.map(a.y)).filter((e => !e.themeHidden)))),
                    w = (0, r.Mz)(d, (e => (0, a.zL)(e.map(a.y)).filter((e => !e.themeHidden)))),
                    z = (0, r.Mz)(s, (e => (e = [...e, { access: "protected", active: !0, isDefault: !0, label: "Department Role", model: "role", name: "departmentRole", type: "string" }], (0, a.qD)(e.map(a.y)).filter((e => !e.themeHidden))))),
                    x = (0, r.Mz)(u, (e => (0, a.zL)(e.map(a.y)).filter((e => !e.themeHidden)))),
                    A = (0, r.Mz)(h, (e => (0, a.RG)(e.map(a.y)).filter((e => !e.themeHidden)))),
                    k = (0, r.Mz)(b, (e => { const t = []; return e.reduce(((e, n) => (t.includes(n.name) || (t.push(n.name), e[n.id] = n), e))) })),
                    S = (0, r.Mz)(s, (e => [{ name: "", model: "member", label: "None", attr: "", isDefault: !0, groupable: !0 }, { name: "", model: "role", label: "Department Role", attr: "department", isDefault: !0, groupable: !0 }, { name: "", model: "role", label: "Location Role", attr: "location", isDefault: !0, groupable: !0 }, ...e.map((e => ({ ...e, attr: e.id })))].filter((e => e.type !== a.ZE.ATTACHMENT && e.name !== a.x2.EMAIL && e.name !== a.x2.PHONE && e.groupable)))) }, 45418: (e, t, n) => { "use strict";
                n.d(t, { Ar: () => s, Ij: () => o, Kj: () => l, TO: () => u, Yk: () => h, wr: () => d, xt: () => c }); var r = n(80192),
                    a = n(84091); const { selectIds: o, selectAll: i, selectEntities: l, selectById: s } = a.FK.getSelectors(), c = e => l(e.people), d = e => i(e.people), u = (0, r.Mz)(c, (e => e.people.visiblePeople), ((e, t) => t.map((t => e[t])).filter((e => e)))), h = (e, t) => { let { ids: n } = t; const r = e.people.entities; return null !== n && void 0 !== n && n.length ? n.map((e => r[e])).filter((e => e)) : [] } }, 13888: (e, t, n) => { "use strict";
                n.d(t, { $x: () => A, A4: () => S, D9: () => k, Ic: () => d, Y1: () => z, id: () => h, il: () => u, lM: () => m, m7: () => b, oJ: () => x, zk: () => w }); var r = n(80192),
                    a = n(69219),
                    o = n(19367),
                    i = n(43862),
                    l = n(78396),
                    s = n(92517),
                    c = n(15813); const d = e => { var t; return null === (t = e.print) || void 0 === t ? void 0 : t.settings },
                    u = e => { var t, n; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n ? void 0 : n.themeId },
                    h = (0, r.Mz)((e => { var t, n, r; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.chartElements) || void 0 === r ? void 0 : r.legend }), o.Mk, ((e, t) => { var n, r, a, o; return { ...e, visible: (null === t || void 0 === t || null === (n = t.legend) || void 0 === n || null === (r = n.rules) || void 0 === r ? void 0 : r.length) > 0 && e.visible, ownPage: (null === t || void 0 === t || null === (a = t.legend) || void 0 === a || null === (o = a.rules) || void 0 === o ? void 0 : o.length) > 0 && e.ownPage } })),
                    m = e => { var t; return null === (t = e.print) || void 0 === t ? void 0 : t.page.current },
                    p = e => { var t, n, r; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.chartElements) || void 0 === r ? void 0 : r.chartNavigation },
                    { selectIds: f, selectAll: v, selectEntities: g, selectById: y } = i.k9.getSelectors(),
                    b = (0, r.Mz)((e => { var t, n; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n ? void 0 : n.pageSetup }), (e => g(e.roles)), o.G0, ((e, t, n) => { var r; return { ...e, breakBy: (() => { const t = null === e || void 0 === e ? void 0 : e.breakBy; return n === l.XD.MATRIX ? ["matrixCells", "function", "team"].includes(t) ? t : "matrixCells" : ["department", "levels", "location", "manual", "bestfit"].includes(t) ? t : "department" })(), topRoles: null === e || void 0 === e || null === (r = e.topRoles) || void 0 === r ? void 0 : r.filter((e => Boolean(t[null === e || void 0 === e ? void 0 : e.id]))) } })),
                    w = (0, r.Mz)(b, (e => e.range === c.J_.ROLES ? null === e || void 0 === e ? void 0 : e.topRoles : [])),
                    z = (0, r.Mz)(a.uo, p, w, m, ((e, t, n, r) => { const a = n.length > 0 || 1 !== r; return "managerPath" === t && a && ("print" === e || "printPreview" === e) })),
                    x = (0, r.Mz)(a.uo, o.G0, p, w, m, ((e, t, n, r, a) => { const o = r.length > 0 || 1 !== a; return t !== l.XD.MATRIX && "parentPageLink" === n && o && ("print" === e || "printPreview" === e) })),
                    A = ((0, r.Mz)((e => { var t; return null === (t = e.print) || void 0 === t ? void 0 : t.activeTemplateId }), (e => e.print.templates), (function(e) { return (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : []).find((t => t.id === e)) || {} })), (0, r.Mz)(a.uo, d, ((e, t) => { var n, r; return "print" !== e && "printPreview" !== e || null === t || void 0 === t || null === (n = t.pageSetup) || void 0 === n || null === (r = n.pageFormat) || void 0 === r || !r.size ? null : (0, s.CQ)(t) }))),
                    k = (0, r.Mz)(A, (e => { var t, n; return null === e || void 0 === e || null === (t = e.setup) || void 0 === t || null === (n = t.page) || void 0 === n ? void 0 : n.size })),
                    S = (0, r.Mz)(o.Mk, d, ((e, t) => { var n, r; return (null !== t && void 0 !== t && null !== (n = t.chartElements) && void 0 !== n && n.directReportDirectionOverrides ? null === t || void 0 === t || null === (r = t.chartElements) || void 0 === r ? void 0 : r.directReportDirectionOverrides[null === e || void 0 === e ? void 0 : e.id] : {}) || {} })) }, 89656: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => H, FV: () => k, K6: () => _, NL: () => L, NZ: () => S, Nh: () => w, Od: () => R, P0: () => b, QD: () => x, Qw: () => C, R3: () => N, Ud: () => O, YY: () => T, ZM: () => P, bh: () => j, cl: () => A, hL: () => M, o4: () => V, pM: () => B, tS: () => I, w$: () => z, xu: () => y, y$: () => F, y2: () => E, z0: () => D }); var r = n(80192),
                    a = n(75995),
                    o = n(69219),
                    i = n(43331),
                    l = n(68840),
                    s = n(10621),
                    c = n(7743),
                    d = n(45418),
                    u = n(78396),
                    h = n(30752),
                    m = n(39362),
                    p = n(21933),
                    f = n(18519),
                    v = n(74079); const g = { left: "left", right: "right", center: "center", topLeft: "left", topRight: "right", aboveLeft: "left", aboveRight: "right", above: "center" },
                    y = (0, r.Mz)((e => e.themes.customThemes), (e => e.organization.fields), ((e, t) => [...(0, m.r)(t), ...e.map((e => ({ ...e, type: "custom" })))].map((e => ({ ...e, image: a.A }))))),
                    b = h.A,
                    w = (0, r.Mz)(b, (e => ((null === e || void 0 === e ? void 0 : e.roleTypeOverrides) || {}).department || { borderDisplay: "card", borderVisible: !1 })),
                    z = (0, r.Mz)(b, (e => { var t; return (null === e || void 0 === e || null === (t = e.chartOptions) || void 0 === t ? void 0 : t.backgroundColor) || p.q7 })),
                    x = (0, r.Mz)(b, (e => (0, m.r)().map((e => e.id)).includes(e.id))),
                    A = (0, r.Mz)(b, (e => { const { cards: { cardFrame: { size: { auto: t } } } } = e.base; return t })),
                    k = ((0, r.Mz)(b, (e => { const { chartOptions: { backgroundColor: t = "transparent" } } = e || {}; return t })), (0, r.Mz)((e => { var t; return null === (t = e.themes) || void 0 === t ? void 0 : t.isDirty }), (e => !0 === e)), (0, r.Mz)(b, (e => e.base))),
                    S = (0, r.Mz)(b, (e => t => { var n, r, a, o, i, l, s, c, d, u, h, m; const p = null === e || void 0 === e || null === (n = e.roleTypeOverrides) || void 0 === n || null === (r = n[t]) || void 0 === r ? void 0 : r.enabled; let f = null === e || void 0 === e || null === (a = e.base) || void 0 === a || null === (o = a.photos) || void 0 === o ? void 0 : o.position; return "fill" === (null === e || void 0 === e || null === (i = e.base) || void 0 === i || null === (l = i.photos) || void 0 === l ? void 0 : l.shape) && (f = g[f]), p ? (f = (null === e || void 0 === e || null === (s = e.roleTypeOverrides) || void 0 === s || null === (c = s[t].photos) || void 0 === c ? void 0 : c.position) || f, "fill" === (null === e || void 0 === e || null === (d = e.base) || void 0 === d || null === (u = d.photos) || void 0 === u ? void 0 : u.shape) && (f = g[f]), { ...e.base, ...null === e || void 0 === e || null === (h = e.roleTypeOverrides) || void 0 === h ? void 0 : h[t], photos: { ...(null === e || void 0 === e || null === (m = e.roleTypeOverrides) || void 0 === m ? void 0 : m[t].photos) || e.base.photos, position: f } }) : { ...e.base, photos: { ...e.base.photos, position: f } } })),
                    M = (0, r.Mz)(k, (e => { var t; return null === e || void 0 === e || null === (t = e.cards) || void 0 === t ? void 0 : t.cardFrame })),
                    E = (0, r.Mz)(k, (e => { var t; return null === e || void 0 === e || null === (t = e.lines) || void 0 === t ? void 0 : t.primary })),
                    C = ((0, r.Mz)(k, (e => { var t; return null === e || void 0 === e || null === (t = e.lines) || void 0 === t ? void 0 : t.secondary })), (0, r.Mz)(k, (e => { var t, n, r; return (null === e || void 0 === e || null === (t = e.cards) || void 0 === t || null === (n = t.cardFrame) || void 0 === n || null === (r = n.size) || void 0 === r ? void 0 : r.width) || 200 }))),
                    T = (0, r.Mz)(k, (e => { var t, n, r, a, o, i; return null !== e && void 0 !== e && null !== (t = e.photos) && void 0 !== t && null !== (n = t.noPhoto) && void 0 !== n && n.visible && "initials" !== (null === e || void 0 === e || null === (r = e.photos) || void 0 === r || null === (a = r.noPhoto) || void 0 === a ? void 0 : a.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (o = e.photos) || void 0 === o || null === (i = o.noPhoto) || void 0 === i ? void 0 : i.imageAvatarId) : "" })),
                    H = (0, r.Mz)(k, (e => { var t, n, r, a, o, i; return null !== e && void 0 !== e && null !== (t = e.photos) && void 0 !== t && null !== (n = t.vacant) && void 0 !== n && n.visible && "initials" !== (null === e || void 0 === e || null === (r = e.photos) || void 0 === r || null === (a = r.vacant) || void 0 === a ? void 0 : a.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (o = e.photos) || void 0 === o || null === (i = o.vacant) || void 0 === i ? void 0 : i.imageAvatarId) : "" })),
                    L = (0, r.Mz)(k, (e => { var t, n, r, a, o, i; return null !== e && void 0 !== e && null !== (t = e.directoryPhotos) && void 0 !== t && null !== (n = t.noPhoto) && void 0 !== n && n.visible && "initials" !== (null === e || void 0 === e || null === (r = e.directoryPhotos) || void 0 === r || null === (a = r.noPhoto) || void 0 === a ? void 0 : a.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (o = e.directoryPhotos) || void 0 === o || null === (i = o.noPhoto) || void 0 === i ? void 0 : i.imageAvatarId) : "" })),
                    I = (0, r.Mz)(b, (e => { var t, n, r, a, o, i, s, c, d, u, h, m; return null !== e && void 0 !== e && null !== (t = e.data) && void 0 !== t && null !== (n = t.photoboard) && void 0 !== n && null !== (r = n.photos) && void 0 !== r && null !== (a = r.noPhoto) && void 0 !== a && a.visible && "initials" !== (null === e || void 0 === e || null === (o = e.data) || void 0 === o || null === (i = o.photoboard) || void 0 === i || null === (s = i.photos) || void 0 === s || null === (c = s.noPhoto) || void 0 === c ? void 0 : c.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (d = e.data) || void 0 === d || null === (u = d.photoboard) || void 0 === u || null === (h = u.photos) || void 0 === h || null === (m = h.noPhoto) || void 0 === m ? void 0 : m.imageAvatarId) : "" })),
                    j = (0, r.Mz)(k, (e => { var t, n, r, a, o, i; return null !== e && void 0 !== e && null !== (t = e.directoryPhotos) && void 0 !== t && null !== (n = t.vacant) && void 0 !== n && n.visible && "initials" !== (null === e || void 0 === e || null === (r = e.directoryPhotos) || void 0 === r || null === (a = r.vacant) || void 0 === a ? void 0 : a.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (o = e.directoryPhotos) || void 0 === o || null === (i = o.vacant) || void 0 === i ? void 0 : i.imageAvatarId) : "" })),
                    V = (0, r.Mz)(b, (e => { var t, n, r, a, o, i, s, c, d, u, h, m; return null !== e && void 0 !== e && null !== (t = e.data) && void 0 !== t && null !== (n = t.detailsPane) && void 0 !== n && null !== (r = n.photos) && void 0 !== r && null !== (a = r.noPhoto) && void 0 !== a && a.visible && "initials" !== (null === e || void 0 === e || null === (o = e.data) || void 0 === o || null === (i = o.detailsPane) || void 0 === i || null === (s = i.photos) || void 0 === s || null === (c = s.noPhoto) || void 0 === c ? void 0 : c.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (d = e.data) || void 0 === d || null === (u = d.detailsPane) || void 0 === u || null === (h = u.photos) || void 0 === h || null === (m = h.noPhoto) || void 0 === m ? void 0 : m.imageAvatarId) : "" })),
                    O = (0, r.Mz)(b, (e => { var t, n, r, a, o, i, s, c, d, u, h, m; return null !== e && void 0 !== e && null !== (t = e.data) && void 0 !== t && null !== (n = t.photoboard) && void 0 !== n && null !== (r = n.photos) && void 0 !== r && null !== (a = r.vacant) && void 0 !== a && a.visible && "initials" !== (null === e || void 0 === e || null === (o = e.data) || void 0 === o || null === (i = o.photoboard) || void 0 === i || null === (s = i.photos) || void 0 === s || null === (c = s.vacant) || void 0 === c ? void 0 : c.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (d = e.data) || void 0 === d || null === (u = d.photoboard) || void 0 === u || null === (h = u.photos) || void 0 === h || null === (m = h.vacant) || void 0 === m ? void 0 : m.imageAvatarId) : "" })),
                    R = (0, r.Mz)(b, (e => { var t, n, r, a, o, i, s, c, d, u, h, m; return null !== e && void 0 !== e && null !== (t = e.data) && void 0 !== t && null !== (n = t.detailsPane) && void 0 !== n && null !== (r = n.photos) && void 0 !== r && null !== (a = r.vacant) && void 0 !== a && a.visible && "initials" !== (null === e || void 0 === e || null === (o = e.data) || void 0 === o || null === (i = o.detailsPane) || void 0 === i || null === (s = i.photos) || void 0 === s || null === (c = s.vacant) || void 0 === c ? void 0 : c.imageAvatarId) ? (0, l.Or)(null === e || void 0 === e || null === (d = e.data) || void 0 === d || null === (u = d.detailsPane) || void 0 === u || null === (h = u.photos) || void 0 === h || null === (m = h.vacant) || void 0 === m ? void 0 : m.imageAvatarId) : "" })),
