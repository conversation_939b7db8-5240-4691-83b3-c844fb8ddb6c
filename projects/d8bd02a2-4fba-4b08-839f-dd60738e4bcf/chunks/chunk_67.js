                c = e.tickFormatter,
                d = e.unit,
                u = e.angle; if (!a || !a.length || !r) return []; if (Ha(s) || ui.isSsr) return function(e, t) { return Wx(e, t + 1) }(a, "number" === typeof s && Ha(s) ? s : 0); var h = [],
                m = "top" === l || "bottom" === l ? "width" : "height",
                p = d && "width" === m ? Vd(d, { fontSize: t, letterSpacing: n }) : { width: 0, height: 0 },
                f = function(e, r) { var a = Ba()(c) ? c(e.value, r) : e.value; return "width" === m ? function(e, t, n) { var r = { width: e.width + t.width, height: e.height + t.height }; return yw(r, n) }(Vd(a, { fontSize: t, letterSpacing: n }), p, u) : Vd(a, { fontSize: t, letterSpacing: n })[m] },
                v = a.length >= 2 ? Ca(a[1].coordinate - a[0].coordinate) : 1,
                g = function(e, t, n) { var r = "width" === n,
                        a = e.x,
                        o = e.y,
                        i = e.width,
                        l = e.height; return 1 === t ? { start: r ? a : o, end: r ? a + i : o + l } : { start: r ? a + i : o + l, end: r ? a : o } }(o, v, m); return "equidistantPreserveStart" === s ? function(e, t, n, r, a) { for (var o, i = (r || []).slice(), l = t.start, s = t.end, c = 0, d = 1, u = l, h = function() { var t = null === r || void 0 === r ? void 0 : r[c]; if (void 0 === t) return { v: Wx(r, d) }; var o, i = c,
                            h = function() { return void 0 === o && (o = n(t, i)), o },
                            m = t.coordinate,
                            p = 0 === c || Ux(e, m, h, u, s);
                        p || (c = 0, u = l, d += 1), p && (u = m + e * (h() / 2 + a), c += d) }; d <= i.length;)
                    if (o = h()) return o.v; return [] }(v, g, f, a, i) : (h = "preserveStart" === s || "preserveStartEnd" === s ? function(e, t, n, r, a, o) { var i = (r || []).slice(),
                    l = i.length,
                    s = t.start,
                    c = t.end; if (o) { var d = r[l - 1],
                        u = n(d, l - 1),
                        h = e * (d.coordinate + e * u / 2 - c);
                    i[l - 1] = d = Kx(Kx({}, d), {}, { tickCoord: h > 0 ? d.coordinate - h * e : d.coordinate }), Ux(e, d.tickCoord, (function() { return u }), s, c) && (c = d.tickCoord - e * (u / 2 + a), i[l - 1] = Kx(Kx({}, d), {}, { isShow: !0 })) } for (var m = o ? l - 1 : l, p = function(t) { var r, o = i[t],
                            l = function() { return void 0 === r && (r = n(o, t)), r }; if (0 === t) { var d = e * (o.coordinate - e * l() / 2 - s);
                            i[t] = o = Kx(Kx({}, o), {}, { tickCoord: d < 0 ? o.coordinate - d * e : o.coordinate }) } else i[t] = o = Kx(Kx({}, o), {}, { tickCoord: o.coordinate });
                        Ux(e, o.tickCoord, l, s, c) && (s = o.tickCoord + e * (l() / 2 + a), i[t] = Kx(Kx({}, o), {}, { isShow: !0 })) }, f = 0; f < m; f++) p(f); return i }(v, g, f, a, i, "preserveStartEnd" === s) : function(e, t, n, r, a) { for (var o = (r || []).slice(), i = o.length, l = t.start, s = t.end, c = function(t) { var r, c = o[t],
                            d = function() { return void 0 === r && (r = n(c, t)), r }; if (t === i - 1) { var u = e * (c.coordinate + e * d() / 2 - s);
                            o[t] = c = Kx(Kx({}, c), {}, { tickCoord: u > 0 ? c.coordinate - u * e : c.coordinate }) } else o[t] = c = Kx(Kx({}, c), {}, { tickCoord: c.coordinate });
                        Ux(e, c.tickCoord, d, l, s) && (s = c.tickCoord - e * (d() / 2 + a), o[t] = Kx(Kx({}, c), {}, { isShow: !0 })) }, d = i - 1; d >= 0; d--) c(d); return o }(v, g, f, a, i), h.filter((function(e) { return e.isShow }))) } var Xx = ["viewBox"],
            $x = ["viewBox"],
            Qx = ["ticks"];

        function Jx(e) { return Jx = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Jx(e) }

        function eA() { return eA = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, eA.apply(this, arguments) }

        function tA(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function nA(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? tA(Object(n), !0).forEach((function(t) { cA(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : tA(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function rA(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function aA(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, dA(r.key), r) } }

        function oA(e, t, n) { return t = lA(t),
                function(e, t) { if (t && ("object" === Jx(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return function(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }(e) }(e, iA() ? Reflect.construct(t, n || [], lA(e).constructor) : t.apply(e, n)) }

        function iA() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (iA = function() { return !!e })() }

        function lA(e) { return lA = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, lA(e) }

        function sA(e, t) { return sA = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, sA(e, t) }

        function cA(e, t, n) { return (t = dA(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function dA(e) { var t = function(e, t) { if ("object" != Jx(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Jx(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Jx(t) ? t : String(t) } var uA = function(e) {
            function t(e) { var n; return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), (n = oA(this, t, [e])).state = { fontSize: "", letterSpacing: "" }, n } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && sA(e, t) }(t, e), n = t, r = [{ key: "shouldComponentUpdate", value: function(e, t) { var n = e.viewBox,
                        r = rA(e, Xx),
                        a = this.props,
                        o = a.viewBox,
                        i = rA(a, $x); return !qa(n, o) || !qa(r, i) || !qa(t, this.state) } }, { key: "componentDidMount", value: function() { var e = this.layerReference; if (e) { var t = e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];
                        t && this.setState({ fontSize: window.getComputedStyle(t).fontSize, letterSpacing: window.getComputedStyle(t).letterSpacing }) } } }, { key: "getTickLineCoord", value: function(e) { var t, n, r, a, o, i, l = this.props,
                        s = l.x,
                        c = l.y,
                        d = l.width,
                        u = l.height,
                        h = l.orientation,
                        m = l.tickSize,
                        p = l.mirror,
                        f = l.tickMargin,
                        v = p ? -1 : 1,
                        g = e.tickSize || m,
                        y = Ha(e.tickCoord) ? e.tickCoord : e.coordinate; switch (h) {
                        case "top":
                            t = n = e.coordinate, i = (r = (a = c + +!p * u) - v * g) - v * f, o = y; break;
                        case "left":
                            r = a = e.coordinate, o = (t = (n = s + +!p * d) - v * g) - v * f, i = y; break;
                        case "right":
                            r = a = e.coordinate, o = (t = (n = s + +p * d) + v * g) + v * f, i = y; break;
                        default:
                            t = n = e.coordinate, i = (r = (a = c + +p * u) + v * g) + v * f, o = y } return { line: { x1: t, y1: r, x2: n, y2: a }, tick: { x: o, y: i } } } }, { key: "getTickTextAnchor", value: function() { var e, t = this.props,
                        n = t.orientation,
                        r = t.mirror; switch (n) {
                        case "left":
                            e = r ? "start" : "end"; break;
                        case "right":
                            e = r ? "end" : "start"; break;
                        default:
                            e = "middle" } return e } }, { key: "getTickVerticalAnchor", value: function() { var e = this.props,
                        t = e.orientation,
                        n = e.mirror,
                        r = "end"; switch (t) {
                        case "left":
                        case "right":
                            r = "middle"; break;
                        case "top":
                            r = n ? "start" : "end"; break;
                        default:
                            r = n ? "end" : "start" } return r } }, { key: "renderAxisLine", value: function() { var e = this.props,
                        t = e.x,
                        n = e.y,
                        r = e.width,
                        o = e.height,
                        i = e.orientation,
                        l = e.mirror,
                        s = e.axisLine,
                        c = nA(nA(nA({}, po(this.props, !1)), po(s, !1)), {}, { fill: "none" }); if ("top" === i || "bottom" === i) { var d = +("top" === i && !l || "bottom" === i && l);
                        c = nA(nA({}, c), {}, { x1: t, y1: n + d * o, x2: t + r, y2: n + d * o }) } else { var u = +("left" === i && !l || "right" === i && l);
                        c = nA(nA({}, c), {}, { x1: t + u * r, y1: n, x2: t + u * r, y2: n + o }) } return a.createElement("line", eA({}, c, { className: va("recharts-cartesian-axis-line", Sa()(s, "className")) })) } }, { key: "renderTicks", value: function(e, n, r) { var o = this,
                        i = this.props,
                        l = i.tickLine,
                        s = i.stroke,
                        c = i.tick,
                        d = i.tickFormatter,
                        u = i.unit,
                        h = Yx(nA(nA({}, this.props), {}, { ticks: e }), n, r),
                        m = this.getTickTextAnchor(),
                        p = this.getTickVerticalAnchor(),
                        f = po(this.props, !1),
                        v = po(c, !1),
                        g = nA(nA({}, f), {}, { fill: "none" }, po(l, !1)),
                        y = h.map((function(e, n) { var r = o.getTickLineCoord(e),
                                i = r.line,
                                y = r.tick,
                                b = nA(nA(nA(nA({ textAnchor: m, verticalAnchor: p }, f), {}, { stroke: "none", fill: s }, v), y), {}, { index: n, payload: e, visibleTicksCount: h.length, tickFormatter: d }); return a.createElement(Po, eA({ className: "recharts-cartesian-axis-tick", key: "tick-".concat(e.value, "-").concat(e.coordinate, "-").concat(e.tickCoord) }, Qa(o.props, e, n)), l && a.createElement("line", eA({}, g, i, { className: va("recharts-cartesian-axis-tick-line", Sa()(l, "className")) })), c && t.renderTickItem(c, b, "".concat(Ba()(d) ? d(e.value, n) : e.value).concat(u || ""))) })); return a.createElement("g", { className: "recharts-cartesian-axis-ticks" }, y) } }, { key: "render", value: function() { var e = this,
                        t = this.props,
                        n = t.axisLine,
                        r = t.width,
                        o = t.height,
                        i = t.ticksGenerator,
                        l = t.className; if (t.hide) return null; var s = this.props,
                        c = s.ticks,
                        d = rA(s, Qx),
                        u = c; return Ba()(i) && (u = c && c.length > 0 ? i(this.props) : i(d)), r <= 0 || o <= 0 || !u || !u.length ? null : a.createElement(Po, { className: va("recharts-cartesian-axis", l), ref: function(t) { e.layerReference = t } }, n && this.renderAxisLine(), this.renderTicks(u, this.state.fontSize, this.state.letterSpacing), Hy.renderCallByParent(this.props)) } }], o = [{ key: "renderTickItem", value: function(e, t, n) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement(cu, eA({}, t, { className: "recharts-cartesian-axis-tick-value" }), n) } }], r && aA(n.prototype, r), o && aA(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.Component);

        function hA() { return hA = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, hA.apply(this, arguments) } cA(uA, "displayName", "CartesianAxis"), cA(uA, "defaultProps", { x: 0, y: 0, width: 0, height: 0, viewBox: { x: 0, y: 0, width: 0, height: 0 }, orientation: "bottom", ticks: [], stroke: "#666", tickLine: !0, axisLine: !0, tick: !0, mirror: !1, minTickGap: 5, tickSize: 6, tickMargin: 2, interval: "preserveEnd" }); var mA = function(e) { var t = e.xAxisId,
                n = _w(),
                r = Bw(),
                o = Fw(t); return null == o ? null : a.createElement(uA, hA({}, o, { className: va("recharts-".concat(o.axisType, " ").concat(o.axisType), o.className), viewBox: { x: 0, y: 0, width: n, height: r }, ticksGenerator: function(e) { return bg(e, !0) } })) };

        function pA() { return pA = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, pA.apply(this, arguments) } mA.displayName = "XAxis", mA.defaultProps = { allowDecimals: !0, hide: !1, orientation: "bottom", width: 0, height: 30, mirror: !1, xAxisId: 0, tickCount: 5, type: "category", padding: { left: 0, right: 0 }, allowDataOverflow: !1, scale: "auto", reversed: !1, allowDuplicatedCategory: !0 }; var fA = function(e) { var t = e.yAxisId,
                n = _w(),
                r = Bw(),
                o = Nw(t); return null == o ? null : a.createElement(uA, pA({}, o, { className: va("recharts-".concat(o.axisType, " ").concat(o.axisType), o.className), viewBox: { x: 0, y: 0, width: n, height: r }, ticksGenerator: function(e) { return bg(e, !0) } })) };
        fA.displayName = "YAxis", fA.defaultProps = { allowDuplicatedCategory: !0, allowDecimals: !0, hide: !1, orientation: "left", width: 60, height: 0, mirror: !1, yAxisId: 0, tickCount: 5, type: "number", padding: { top: 0, bottom: 0 }, allowDataOverflow: !1, scale: "auto", reversed: !1 }; var vA = Bx({ chartName: "BarChart", GraphicalChild: lw, defaultTooltipEventType: "axis", validateTooltipEventTypes: ["axis", "item"], axisComponents: [{ axisType: "xAxis", AxisComp: mA }, { axisType: "yAxis", AxisComp: fA }], formatAxisMap: pw }),
            gA = ["x1", "y1", "x2", "y2", "key"],
            yA = ["offset"];

        function bA(e) { return bA = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, bA(e) }

        function wA(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function zA(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? wA(Object(n), !0).forEach((function(t) { xA(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : wA(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function xA(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != bA(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != bA(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == bA(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function AA() { return AA = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, AA.apply(this, arguments) }

        function kA(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a } var SA = function(e) { var t = e.fill; if (!t || "none" === t) return null; var n = e.fillOpacity,
                r = e.x,
                o = e.y,
                i = e.width,
                l = e.height; return a.createElement("rect", { x: r, y: o, width: i, height: l, stroke: "none", fill: t, fillOpacity: n, className: "recharts-cartesian-grid-bg" }) };

        function MA(e, t) { var n; if (a.isValidElement(e)) n = a.cloneElement(e, t);
            else if (Ba()(e)) n = e(t);
            else { var r = t.x1,
                    o = t.y1,
                    i = t.x2,
                    l = t.y2,
                    s = t.key,
                    c = kA(t, gA),
                    d = po(c, !1),
                    u = (d.offset, kA(d, yA));
                n = a.createElement("line", AA({}, u, { x1: r, y1: o, x2: i, y2: l, fill: "none", key: s })) } return n }

        function EA(e) { var t = e.x,
                n = e.width,
                r = e.horizontal,
                o = void 0 === r || r,
                i = e.horizontalPoints; if (!o || !i || !i.length) return null; var l = i.map((function(r, a) { var i = zA(zA({}, e), {}, { x1: t, y1: r, x2: t + n, y2: r, key: "line-".concat(a), index: a }); return MA(o, i) })); return a.createElement("g", { className: "recharts-cartesian-grid-horizontal" }, l) }

        function CA(e) { var t = e.y,
                n = e.height,
                r = e.vertical,
                o = void 0 === r || r,
                i = e.verticalPoints; if (!o || !i || !i.length) return null; var l = i.map((function(r, a) { var i = zA(zA({}, e), {}, { x1: r, y1: t, x2: r, y2: t + n, key: "line-".concat(a), index: a }); return MA(o, i) })); return a.createElement("g", { className: "recharts-cartesian-grid-vertical" }, l) }

        function TA(e) { var t = e.horizontalFill,
                n = e.fillOpacity,
                r = e.x,
                o = e.y,
                i = e.width,
                l = e.height,
                s = e.horizontalPoints,
                c = e.horizontal; if (!(void 0 === c || c) || !t || !t.length) return null; var d = s.map((function(e) { return Math.round(e + o - o) })).sort((function(e, t) { return e - t }));
            o !== d[0] && d.unshift(0); var u = d.map((function(e, s) { var c = !d[s + 1] ? o + l - e : d[s + 1] - e; if (c <= 0) return null; var u = s % t.length; return a.createElement("rect", { key: "react-".concat(s), y: e, x: r, height: c, width: i, stroke: "none", fill: t[u], fillOpacity: n, className: "recharts-cartesian-grid-bg" }) })); return a.createElement("g", { className: "recharts-cartesian-gridstripes-horizontal" }, u) }

        function HA(e) { var t = e.vertical,
                n = void 0 === t || t,
                r = e.verticalFill,
                o = e.fillOpacity,
                i = e.x,
                l = e.y,
                s = e.width,
                c = e.height,
                d = e.verticalPoints; if (!n || !r || !r.length) return null; var u = d.map((function(e) { return Math.round(e + i - i) })).sort((function(e, t) { return e - t }));
            i !== u[0] && u.unshift(0); var h = u.map((function(e, t) { var n = !u[t + 1] ? i + s - e : u[t + 1] - e; if (n <= 0) return null; var d = t % r.length; return a.createElement("rect", { key: "react-".concat(t), x: e, y: l, width: n, height: c, stroke: "none", fill: r[d], fillOpacity: o, className: "recharts-cartesian-grid-bg" }) })); return a.createElement("g", { className: "recharts-cartesian-gridstripes-vertical" }, h) } var LA = function(e, t) { var n = e.xAxis,
                    r = e.width,
                    a = e.height,
                    o = e.offset; return yg(Yx(zA(zA(zA({}, uA.defaultProps), n), {}, { ticks: bg(n, !0), viewBox: { x: 0, y: 0, width: r, height: a } })), o.left, o.left + o.width, t) },
            IA = function(e, t) { var n = e.yAxis,
                    r = e.width,
                    a = e.height,
                    o = e.offset; return yg(Yx(zA(zA(zA({}, uA.defaultProps), n), {}, { ticks: bg(n, !0), viewBox: { x: 0, y: 0, width: r, height: a } })), o.top, o.top + o.height, t) },
            jA = { horizontal: !0, vertical: !0, horizontalPoints: [], verticalPoints: [], stroke: "#ccc", fill: "none", verticalFill: [], horizontalFill: [] };

        function VA(e) { var t, n, r, o, i, l, s = _w(),
                c = Bw(),
                d = (0, a.useContext)(Vw),
                u = zA(zA({}, e), {}, { stroke: null !== (t = e.stroke) && void 0 !== t ? t : jA.stroke, fill: null !== (n = e.fill) && void 0 !== n ? n : jA.fill, horizontal: null !== (r = e.horizontal) && void 0 !== r ? r : jA.horizontal, horizontalFill: null !== (o = e.horizontalFill) && void 0 !== o ? o : jA.horizontalFill, vertical: null !== (i = e.vertical) && void 0 !== i ? i : jA.vertical, verticalFill: null !== (l = e.verticalFill) && void 0 !== l ? l : jA.verticalFill, x: Ha(e.x) ? e.x : d.left, y: Ha(e.y) ? e.y : d.top, width: Ha(e.width) ? e.width : d.width, height: Ha(e.height) ? e.height : d.height }),
                h = u.x,
                m = u.y,
                p = u.width,
                f = u.height,
                v = u.syncWithTicks,
                g = u.horizontalValues,
                y = u.verticalValues,
                b = function() { var e = (0, a.useContext)(Lw); return Oa(e) }(),
                w = function() { var e = (0, a.useContext)(Iw); return Cw()(e, (function(e) { return Ry()(e.domain, Number.isFinite) })) || Oa(e) }(); if (!Ha(p) || p <= 0 || !Ha(f) || f <= 0 || !Ha(h) || h !== +h || !Ha(m) || m !== +m) return null; var z = u.verticalCoordinatesGenerator || LA,
                x = u.horizontalCoordinatesGenerator || IA,
                A = u.horizontalPoints,
                k = u.verticalPoints; if ((!A || !A.length) && Ba()(x)) { var S = g && g.length,
                    M = x({ yAxis: w ? zA(zA({}, w), {}, { ticks: S ? g : w.ticks }) : void 0, width: s, height: c, offset: d }, !!S || v);
                Da(Array.isArray(M), "horizontalCoordinatesGenerator should return Array but instead it returned [".concat(bA(M), "]")), Array.isArray(M) && (A = M) } if ((!k || !k.length) && Ba()(z)) { var E = y && y.length,
                    C = z({ xAxis: b ? zA(zA({}, b), {}, { ticks: E ? y : b.ticks }) : void 0, width: s, height: c, offset: d }, !!E || v);
                Da(Array.isArray(C), "verticalCoordinatesGenerator should return Array but instead it returned [".concat(bA(C), "]")), Array.isArray(C) && (k = C) } return a.createElement("g", { className: "recharts-cartesian-grid" }, a.createElement(SA, { fill: u.fill, fillOpacity: u.fillOpacity, x: u.x, y: u.y, width: u.width, height: u.height }), a.createElement(EA, AA({}, u, { offset: d, horizontalPoints: A, xAxis: b, yAxis: w })), a.createElement(CA, AA({}, u, { offset: d, verticalPoints: k, xAxis: b, yAxis: w })), a.createElement(TA, AA({}, u, { horizontalPoints: A })), a.createElement(HA, AA({}, u, { verticalPoints: k }))) } VA.displayName = "CartesianGrid"; const OA = e => { var t, n; let { active: r, payload: a, label: o, color: i } = e; const l = r && (null === a || void 0 === a ? void 0 : a.length),
                    s = (null === a || void 0 === a ? void 0 : a[0]) || {},
                    c = o || (null === s || void 0 === s ? void 0 : s.label) || (null === s || void 0 === s || null === (t = s.payload) || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.label) || "",
                    d = Object.keys($r.U),
                    u = Object.keys((null === s || void 0 === s ? void 0 : s.payload) || {}).filter((e => d.includes(e))).sort(((e, t) => { var n, r; const a = null === s || void 0 === s || null === (n = s.payload) || void 0 === n ? void 0 : n[e],
                            o = null === s || void 0 === s || null === (r = s.payload) || void 0 === r ? void 0 : r[t]; return a < o ? 1 : a > o ? -1 : 0 })); return l ? (0, we.jsxs)(se.A, { bgcolor: "white", p: 2, boxShadow: 3, borderRadius: 8, children: [(0, we.jsxs)(je.A, { children: ["".concat(c, " : ").concat(null === s || void 0 === s ? void 0 : s.value), " actions"] }), (0, we.jsx)(se.A, { p: 2, children: u.map((e => { var t; return (0, we.jsxs)(se.A, { display: "flex", justifyContent: "space-between", gridGap: 16, children: [(0, we.jsx)(je.A, { color: i, children: $r.U[e] }), (0, we.jsx)(je.A, { children: null === s || void 0 === s || null === (t = s.payload) || void 0 === t ? void 0 : t[e] })] }, "".concat(e, "-").concat(o, "-").concat(null === s || void 0 === s ? void 0 : s.value, "-tooltip")) })) })] }) : (0, we.jsx)(we.Fragment, {}) },
            RA = e => { let { data: t = [], isLoading: n, height: r, color: a, drillDown: o } = e; const i = !t.length; return (0, we.jsx)(dn.A, { loading: n, transparent: !0, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 32, children: [(0, we.jsx)(Qr, { title: "Activity by Charts", subTitle: "Number of actions performed in each chart based on applied filters" }), i && (0, we.jsx)(se.A, { m: 3, children: (0, we.jsx)(jn.A, { text: "No activity based on applied filters." }) }), !i && (0, we.jsx)(ko, { height: r, children: (0, we.jsxs)(vA, { data: t, margin: { top: 5, right: 35, bottom: 5 }, children: [(0, we.jsx)(VA, { vertical: !1, strokeDasharray: "3" }), (0, we.jsx)(mA, { dataKey: "label" }), (0, we.jsx)(fA, { width: 35 }), (0, we.jsx)(Mi, { content: (0, we.jsx)(OA, { color: a }) }), (0, we.jsx)(lw, { dataKey: "value", fill: a, cursor: "pointer", onClick: e => o && o("charts", { id: e.id, label: e.label }) })] }) })] }) }) }; var PA = ["type", "layout", "connectNulls", "ref"];

        function DA(e) { return DA = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, DA(e) }

        function FA(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function NA() { return NA = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, NA.apply(this, arguments) }

        function _A(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function BA(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? _A(Object(n), !0).forEach((function(t) { $A(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : _A(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function WA(e) { return function(e) { if (Array.isArray(e)) return UA(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return UA(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return UA(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function UA(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function qA(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, QA(r.key), r) } }

        function GA(e, t, n) { return t = ZA(t),
                function(e, t) { if (t && ("object" === DA(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return YA(e) }(e, KA() ? Reflect.construct(t, n || [], ZA(e).constructor) : t.apply(e, n)) }

        function KA() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (KA = function() { return !!e })() }

        function ZA(e) { return ZA = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, ZA(e) }

        function YA(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }

        function XA(e, t) { return XA = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, XA(e, t) }

        function $A(e, t, n) { return (t = QA(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function QA(e) { var t = function(e, t) { if ("object" != DA(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != DA(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == DA(t) ? t : String(t) } var JA = function(e) {
            function t() { var e;! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t); for (var n = arguments.length, r = new Array(n), a = 0; a < n; a++) r[a] = arguments[a]; return $A(YA(e = GA(this, t, [].concat(r))), "state", { isAnimationFinished: !0, totalLength: 0 }), $A(YA(e), "generateSimpleStrokeDasharray", (function(e, t) { return "".concat(t, "px ").concat(e - t, "px") })), $A(YA(e), "getStrokeDasharray", (function(n, r, a) { var o = a.reduce((function(e, t) { return e + t })); if (!o) return e.generateSimpleStrokeDasharray(r, n); for (var i = Math.floor(n / o), l = n % o, s = r - n, c = [], d = 0, u = 0; d < a.length; u += a[d], ++d)
                        if (u + a[d] > l) { c = [].concat(WA(a.slice(0, d)), [l - u]); break } var h = c.length % 2 === 0 ? [0, s] : [s]; return [].concat(WA(t.repeat(a, i)), WA(c), h).map((function(e) { return "".concat(e, "px") })).join(", ") })), $A(YA(e), "id", ja("recharts-line-")), $A(YA(e), "pathRef", (function(t) { e.mainCurve = t })), $A(YA(e), "handleAnimationEnd", (function() { e.setState({ isAnimationFinished: !0 }), e.props.onAnimationEnd && e.props.onAnimationEnd() })), $A(YA(e), "handleAnimationStart", (function() { e.setState({ isAnimationFinished: !1 }), e.props.onAnimationStart && e.props.onAnimationStart() })), e } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && XA(e, t) }(t, e), n = t, r = [{ key: "componentDidMount", value: function() { if (this.props.isAnimationActive) { var e = this.getTotalLength();
                        this.setState({ totalLength: e }) } } }, { key: "componentDidUpdate", value: function() { if (this.props.isAnimationActive) { var e = this.getTotalLength();
                        e !== this.state.totalLength && this.setState({ totalLength: e }) } } }, { key: "getTotalLength", value: function() { var e = this.mainCurve; try { return e && e.getTotalLength && e.getTotalLength() || 0 } catch (t) { return 0 } } }, { key: "renderErrorBar", value: function(e, t) { if (this.props.isAnimationActive && !this.state.isAnimationFinished) return null; var n = this.props,
                        r = n.points,
                        o = n.xAxis,
                        i = n.yAxis,
                        l = n.layout,
                        s = so(n.children, eg); if (!s) return null; var c = function(e, t) { return { x: e.x, y: e.y, value: e.value, errorVal: hg(e.payload, t) } },
                        d = { clipPath: e ? "url(#clipPath-".concat(t, ")") : null }; return a.createElement(Po, d, s.map((function(e) { return a.cloneElement(e, { key: "bar-".concat(e.props.dataKey), data: r, xAxis: o, yAxis: i, layout: l, dataPointFormatter: c }) }))) } }, { key: "renderDots", value: function(e, n, r) { if (this.props.isAnimationActive && !this.state.isAnimationFinished) return null; var o = this.props,
                        i = o.dot,
                        l = o.points,
                        s = o.dataKey,
                        c = po(this.props, !1),
                        d = po(i, !0),
                        u = l.map((function(e, n) { var r = BA(BA(BA({ key: "dot-".concat(n), r: 3 }, c), d), {}, { value: e.value, dataKey: s, cx: e.x, cy: e.y, index: n, payload: e.payload }); return t.renderDotItem(i, r) })),
                        h = { clipPath: e ? "url(#clipPath-".concat(n ? "" : "dots-").concat(r, ")") : null }; return a.createElement(Po, NA({ className: "recharts-line-dots", key: "dots" }, h), u) } }, { key: "renderCurveStatically", value: function(e, t, n, r) { var o = this.props,
                        i = o.type,
                        l = o.layout,
                        s = o.connectNulls,
                        c = (o.ref, FA(o, PA)),
                        d = BA(BA(BA({}, po(c, !0)), {}, { fill: "none", className: "recharts-line-curve", clipPath: t ? "url(#clipPath-".concat(n, ")") : null, points: e }, r), {}, { type: i, layout: l, connectNulls: s }); return a.createElement(Kz, NA({}, d, { pathRef: this.pathRef })) } }, { key: "renderCurveWithAnimation", value: function(e, t) { var n = this,
                        r = this.props,
                        o = r.points,
                        i = r.strokeDasharray,
                        l = r.isAnimationActive,
                        s = r.animationBegin,
                        c = r.animationDuration,
                        d = r.animationEasing,
                        u = r.animationId,
                        h = r.animateNewValues,
                        m = r.width,
                        p = r.height,
                        f = this.state,
                        v = f.prevPoints,
                        g = f.totalLength; return a.createElement(rd, { begin: s, duration: c, isActive: l, easing: d, from: { t: 0 }, to: { t: 1 }, key: "line-".concat(u), onAnimationEnd: this.handleAnimationEnd, onAnimationStart: this.handleAnimationStart }, (function(r) { var a = r.t; if (v) { var l = v.length / o.length,
                                s = o.map((function(e, t) { var n = Math.floor(t * l); if (v[n]) { var r = v[n],
                                            o = Ra(r.x, e.x),
                                            i = Ra(r.y, e.y); return BA(BA({}, e), {}, { x: o(a), y: i(a) }) } if (h) { var s = Ra(2 * m, e.x),
                                            c = Ra(p / 2, e.y); return BA(BA({}, e), {}, { x: s(a), y: c(a) }) } return BA(BA({}, e), {}, { x: e.x, y: e.y }) })); return n.renderCurveStatically(s, e, t) } var c, d = Ra(0, g)(a); if (i) { var u = "".concat(i).split(/[,\s]+/gim).map((function(e) { return parseFloat(e) }));
                            c = n.getStrokeDasharray(d, g, u) } else c = n.generateSimpleStrokeDasharray(g, d); return n.renderCurveStatically(o, e, t, { strokeDasharray: c }) })) } }, { key: "renderCurve", value: function(e, t) { var n = this.props,
                        r = n.points,
                        a = n.isAnimationActive,
                        o = this.state,
                        i = o.prevPoints,
                        l = o.totalLength; return a && r && r.length && (!i && l > 0 || !bv()(i, r)) ? this.renderCurveWithAnimation(e, t) : this.renderCurveStatically(r, e, t) } }, { key: "render", value: function() { var e, t = this.props,
                        n = t.hide,
                        r = t.dot,
                        o = t.points,
                        i = t.className,
                        l = t.xAxis,
                        s = t.yAxis,
                        c = t.top,
                        d = t.left,
                        u = t.width,
                        h = t.height,
                        m = t.isAnimationActive,
                        p = t.id; if (n || !o || !o.length) return null; var f = this.state.isAnimationFinished,
                        v = 1 === o.length,
                        g = va("recharts-line", i),
                        y = l && l.allowDataOverflow,
                        b = s && s.allowDataOverflow,
                        w = y || b,
                        z = Na()(p) ? this.id : p,
                        x = null !== (e = po(r, !1)) && void 0 !== e ? e : { r: 3, strokeWidth: 2 },
                        A = x.r,
                        k = void 0 === A ? 3 : A,
                        S = x.strokeWidth,
                        M = void 0 === S ? 2 : S,
                        E = (function(e) { return e && "object" === no(e) && "cx" in e && "cy" in e && "r" in e }(r) ? r : {}).clipDot,
                        C = void 0 === E || E,
                        T = 2 * k + M; return a.createElement(Po, { className: g }, y || b ? a.createElement("defs", null, a.createElement("clipPath", { id: "clipPath-".concat(z) }, a.createElement("rect", { x: y ? d : d - u / 2, y: b ? c : c - h / 2, width: y ? u : 2 * u, height: b ? h : 2 * h })), !C && a.createElement("clipPath", { id: "clipPath-dots-".concat(z) }, a.createElement("rect", { x: d - T / 2, y: c - T / 2, width: u + T, height: h + T }))) : null, !v && this.renderCurve(w, z), this.renderErrorBar(w, z), (v || r) && this.renderDots(w, C, z), (!m || f) && $y.renderCallByParent(this.props, o)) } }], o = [{ key: "getDerivedStateFromProps", value: function(e, t) { return e.animationId !== t.prevAnimationId ? { prevAnimationId: e.animationId, curPoints: e.points, prevPoints: t.curPoints } : e.points !== t.curPoints ? { curPoints: e.points } : null } }, { key: "repeat", value: function(e, t) { for (var n = e.length % 2 !== 0 ? [].concat(WA(e), [0]) : e, r = [], a = 0; a < t; ++a) r = [].concat(WA(r), WA(n)); return r } }, { key: "renderDotItem", value: function(e, t) { var n; if (a.isValidElement(e)) n = a.cloneElement(e, t);
                    else if (Ba()(e)) n = e(t);
                    else { var r = va("recharts-line-dot", "boolean" !== typeof e ? e.className : "");
                        n = a.createElement(rs, NA({}, t, { className: r })) } return n } }], r && qA(n.prototype, r), o && qA(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);
        $A(JA, "displayName", "Line"), $A(JA, "defaultProps", { xAxisId: 0, yAxisId: 0, connectNulls: !1, activeDot: !0, dot: !0, legendType: "line", stroke: "#3182bd", strokeWidth: 1, fill: "#fff", points: [], isAnimationActive: !ui.isSsr, animateNewValues: !0, animationBegin: 0, animationDuration: 1500, animationEasing: "ease", hide: !1, label: !1 }), $A(JA, "getComposedData", (function(e) { var t = e.props,
                n = e.xAxis,
                r = e.yAxis,
                a = e.xAxisTicks,
                o = e.yAxisTicks,
                i = e.dataKey,
                l = e.bandSize,
                s = e.displayedData,
                c = e.offset,
                d = t.layout,
                u = s.map((function(e, t) { var s = hg(e, i); return "horizontal" === d ? { x: Cg({ axis: n, ticks: a, bandSize: l, entry: e, index: t }), y: Na()(s) ? null : r.scale(s), value: s, payload: e } : { x: Na()(s) ? null : n.scale(s), y: Cg({ axis: r, ticks: o, bandSize: l, entry: e, index: t }), value: s, payload: e } })); return BA({ points: u, layout: d }, c) })); var ek = Bx({ chartName: "LineChart", GraphicalChild: JA, axisComponents: [{ axisType: "xAxis", AxisComp: mA }, { axisType: "yAxis", AxisComp: fA }], formatAxisMap: pw }); const tk = e => { let { data: t = [], isLoading: n, height: r, color: a, drillDown: o } = e; const i = !t.length; return (0, we.jsx)(dn.A, { loading: n, transparent: !0, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 32, children: [(0, we.jsx)(Qr, { title: "Activity Trend", subTitle: "Number of actions performed on each day based on applied filters" }), i && (0, we.jsx)(se.A, { m: 3, children: (0, we.jsx)(jn.A, { text: "No activity based on applied filters." }) }), !i && (0, we.jsx)(ko, { height: r, children: (0, we.jsxs)(ek, { data: t, margin: { top: 5, right: 35, bottom: 5 }, children: [(0, we.jsx)(VA, { strokeDasharray: "3 3" }), (0, we.jsx)(mA, { dataKey: "label" }), (0, we.jsx)(fA, { width: 35 }), (0, we.jsx)(Mi, { content: (0, we.jsx)(OA, { color: a }) }), (0, we.jsx)(JA, { type: "monotone", dataKey: "value", stroke: a, strokeWidth: 2, activeDot: { r: 8, cursor: "pointer", onClick: (e, t) => { var n; return o && o("date", null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.id) } } })] }) })] }) }) }; var nk = ["points", "className", "baseLinePoints", "connectNulls"];

        function rk() { return rk = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, rk.apply(this, arguments) }

        function ak(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function ok(e) { return function(e) { if (Array.isArray(e)) return ik(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return ik(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ik(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function ik(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } var lk = function(e) { return e && e.x === +e.x && e.y === +e.y },
            sk = function(e, t) { var n = function() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
                        t = [
                            []
                        ]; return e.forEach((function(e) { lk(e) ? t[t.length - 1].push(e) : t[t.length - 1].length > 0 && t.push([]) })), lk(e[0]) && t[t.length - 1].push(e[0]), t[t.length - 1].length <= 0 && (t = t.slice(0, -1)), t }(e);
                t && (n = [n.reduce((function(e, t) { return [].concat(ok(e), ok(t)) }), [])]); var r = n.map((function(e) { return e.reduce((function(e, t, n) { return "".concat(e).concat(0 === n ? "M" : "L").concat(t.x, ",").concat(t.y) }), "") })).join(""); return 1 === n.length ? "".concat(r, "Z") : r },
            ck = function(e) { var t = e.points,
                    n = e.className,
                    r = e.baseLinePoints,
                    o = e.connectNulls,
                    i = ak(e, nk); if (!t || !t.length) return null; var l = va("recharts-polygon", n); if (r && r.length) { var s = i.stroke && "none" !== i.stroke,
                        c = function(e, t, n) { var r = sk(e, n); return "".concat("Z" === r.slice(-1) ? r.slice(0, -1) : r, "L").concat(sk(t.reverse(), n).slice(1)) }(t, r, o); return a.createElement("g", { className: l }, a.createElement("path", rk({}, po(i, !0), { fill: "Z" === c.slice(-1) ? i.fill : "none", stroke: "none", d: c })), s ? a.createElement("path", rk({}, po(i, !0), { fill: "none", d: sk(t, o) })) : null, s ? a.createElement("path", rk({}, po(i, !0), { fill: "none", d: sk(r, o) })) : null) } var d = sk(t, o); return a.createElement("path", rk({}, po(i, !0), { fill: "Z" === d.slice(-1) ? i.fill : "none", className: l, d: d })) };

        function dk(e) { return dk = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, dk(e) }

        function uk() { return uk = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, uk.apply(this, arguments) }

        function hk(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function mk(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? hk(Object(n), !0).forEach((function(t) { bk(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : hk(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function pk(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, wk(r.key), r) } }

        function fk(e, t, n) { return t = gk(t),
                function(e, t) { if (t && ("object" === dk(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return function(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }(e) }(e, vk() ? Reflect.construct(t, n || [], gk(e).constructor) : t.apply(e, n)) }

        function vk() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (vk = function() { return !!e })() }

        function gk(e) { return gk = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, gk(e) }

        function yk(e, t) { return yk = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, yk(e, t) }

        function bk(e, t, n) { return (t = wk(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function wk(e) { var t = function(e, t) { if ("object" != dk(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != dk(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == dk(t) ? t : String(t) } var zk = Math.PI / 180,
            xk = 1e-5,
            Ak = function(e) {
                function t() { return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), fk(this, t, arguments) } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && yk(e, t) }(t, e), n = t, r = [{ key: "getTickLineCoord", value: function(e) { var t = this.props,
                            n = t.cx,
                            r = t.cy,
                            a = t.radius,
                            o = t.orientation,
                            i = t.tickSize || 8,
                            l = dy(n, r, a, e.coordinate),
                            s = dy(n, r, a + ("inner" === o ? -1 : 1) * i, e.coordinate); return { x1: l.x, y1: l.y, x2: s.x, y2: s.y } } }, { key: "getTickTextAnchor", value: function(e) { var t = this.props.orientation,
                            n = Math.cos(-e.coordinate * zk); return n > xk ? "outer" === t ? "start" : "end" : n < -xk ? "outer" === t ? "end" : "start" : "middle" } }, { key: "renderAxisLine", value: function() { var e = this.props,
                            t = e.cx,
                            n = e.cy,
                            r = e.radius,
                            o = e.axisLine,
                            i = e.axisLineType,
                            l = mk(mk({}, po(this.props, !1)), {}, { fill: "none" }, po(o, !1)); if ("circle" === i) return a.createElement(rs, uk({ className: "recharts-polar-angle-axis-line" }, l, { cx: t, cy: n, r: r })); var s = this.props.ticks.map((function(e) { return dy(t, n, r, e.coordinate) })); return a.createElement(ck, uk({ className: "recharts-polar-angle-axis-line" }, l, { points: s })) } }, { key: "renderTicks", value: function() { var e = this,
                            n = this.props,
                            r = n.ticks,
                            o = n.tick,
                            i = n.tickLine,
                            l = n.tickFormatter,
                            s = n.stroke,
                            c = po(this.props, !1),
                            d = po(o, !1),
                            u = mk(mk({}, c), {}, { fill: "none" }, po(i, !1)),
                            h = r.map((function(n, r) { var h = e.getTickLineCoord(n),
                                    m = mk(mk(mk({ textAnchor: e.getTickTextAnchor(n) }, c), {}, { stroke: "none", fill: s }, d), {}, { index: r, payload: n, x: h.x2, y: h.y2 }); return a.createElement(Po, uk({ className: va("recharts-polar-angle-axis-tick", fy(o)), key: "tick-".concat(n.coordinate) }, Qa(e.props, n, r)), i && a.createElement("line", uk({ className: "recharts-polar-angle-axis-tick-line" }, u, h)), o && t.renderTickItem(o, m, l ? l(n.value, r) : n.value)) })); return a.createElement(Po, { className: "recharts-polar-angle-axis-ticks" }, h) } }, { key: "render", value: function() { var e = this.props,
                            t = e.ticks,
                            n = e.radius,
                            r = e.axisLine; return n <= 0 || !t || !t.length ? null : a.createElement(Po, { className: va("recharts-polar-angle-axis", this.props.className) }, r && this.renderAxisLine(), this.renderTicks()) } }], o = [{ key: "renderTickItem", value: function(e, t, n) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement(cu, uk({}, t, { className: "recharts-polar-angle-axis-tick-value" }), n) } }], r && pk(n.prototype, r), o && pk(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);
        bk(Ak, "displayName", "PolarAngleAxis"), bk(Ak, "axisType", "angleAxis"), bk(Ak, "defaultProps", { type: "category", angleAxisId: 0, scale: "auto", cx: 0, cy: 0, orientation: "outer", axisLine: !0, tickLine: !0, tickSize: 8, tick: !0, hide: !1, allowDuplicatedCategory: !0 }); var kk = n(22794),
            Sk = n.n(kk),
            Mk = n(59364),
            Ek = n.n(Mk),
            Ck = ["cx", "cy", "angle", "ticks", "axisLine"],
            Tk = ["ticks", "tick", "angle", "tickFormatter", "stroke"];

        function Hk(e) { return Hk = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Hk(e) }

        function Lk() { return Lk = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Lk.apply(this, arguments) }

        function Ik(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function jk(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Ik(Object(n), !0).forEach((function(t) { Nk(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ik(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Vk(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function Ok(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, _k(r.key), r) } }

        function Rk(e, t, n) { return t = Dk(t),
                function(e, t) { if (t && ("object" === Hk(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return function(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }(e) }(e, Pk() ? Reflect.construct(t, n || [], Dk(e).constructor) : t.apply(e, n)) }

        function Pk() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (Pk = function() { return !!e })() }

        function Dk(e) { return Dk = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, Dk(e) }

        function Fk(e, t) { return Fk = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, Fk(e, t) }

        function Nk(e, t, n) { return (t = _k(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function _k(e) { var t = function(e, t) { if ("object" != Hk(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Hk(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Hk(t) ? t : String(t) } var Bk, Wk = function(e) {
            function t() { return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), Rk(this, t, arguments) } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && Fk(e, t) }(t, e), n = t, r = [{ key: "getTickValueCoord", value: function(e) { var t = e.coordinate,
                        n = this.props,
                        r = n.angle,
                        a = n.cx,
                        o = n.cy; return dy(a, o, t, r) } }, { key: "getTickTextAnchor", value: function() { var e; switch (this.props.orientation) {
                        case "left":
                            e = "end"; break;
                        case "right":
                            e = "start"; break;
                        default:
                            e = "middle" } return e } }, { key: "getViewBox", value: function() { var e = this.props,
                        t = e.cx,
                        n = e.cy,
                        r = e.angle,
                        a = e.ticks,
                        o = Sk()(a, (function(e) { return e.coordinate || 0 })); return { cx: t, cy: n, startAngle: r, endAngle: r, innerRadius: Ek()(a, (function(e) { return e.coordinate || 0 })).coordinate || 0, outerRadius: o.coordinate || 0 } } }, { key: "renderAxisLine", value: function() { var e = this.props,
                        t = e.cx,
                        n = e.cy,
                        r = e.angle,
                        o = e.ticks,
                        i = e.axisLine,
                        l = Vk(e, Ck),
                        s = o.reduce((function(e, t) { return [Math.min(e[0], t.coordinate), Math.max(e[1], t.coordinate)] }), [1 / 0, -1 / 0]),
                        c = dy(t, n, s[0], r),
                        d = dy(t, n, s[1], r),
                        u = jk(jk(jk({}, po(l, !1)), {}, { fill: "none" }, po(i, !1)), {}, { x1: c.x, y1: c.y, x2: d.x, y2: d.y }); return a.createElement("line", Lk({ className: "recharts-polar-radius-axis-line" }, u)) } }, { key: "renderTicks", value: function() { var e = this,
                        n = this.props,
                        r = n.ticks,
                        o = n.tick,
                        i = n.angle,
                        l = n.tickFormatter,
                        s = n.stroke,
                        c = Vk(n, Tk),
                        d = this.getTickTextAnchor(),
                        u = po(c, !1),
                        h = po(o, !1),
                        m = r.map((function(n, r) { var c = e.getTickValueCoord(n),
                                m = jk(jk(jk(jk({ textAnchor: d, transform: "rotate(".concat(90 - i, ", ").concat(c.x, ", ").concat(c.y, ")") }, u), {}, { stroke: "none", fill: s }, h), {}, { index: r }, c), {}, { payload: n }); return a.createElement(Po, Lk({ className: va("recharts-polar-radius-axis-tick", fy(o)), key: "tick-".concat(n.coordinate) }, Qa(e.props, n, r)), t.renderTickItem(o, m, l ? l(n.value, r) : n.value)) })); return a.createElement(Po, { className: "recharts-polar-radius-axis-ticks" }, m) } }, { key: "render", value: function() { var e = this.props,
                        t = e.ticks,
                        n = e.axisLine,
                        r = e.tick; return t && t.length ? a.createElement(Po, { className: va("recharts-polar-radius-axis", this.props.className) }, n && this.renderAxisLine(), r && this.renderTicks(), Hy.renderCallByParent(this.props, this.getViewBox())) : null } }], o = [{ key: "renderTickItem", value: function(e, t, n) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement(cu, Lk({}, t, { className: "recharts-polar-radius-axis-tick-value" }), n) } }], r && Ok(n.prototype, r), o && Ok(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);

        function Uk(e) { return Uk = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Uk(e) }

        function qk() { return qk = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, qk.apply(this, arguments) }

        function Gk(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Kk(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Gk(Object(n), !0).forEach((function(t) { eS(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Gk(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Zk(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, tS(r.key), r) } }

        function Yk(e, t, n) { return t = $k(t),
                function(e, t) { if (t && ("object" === Uk(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return Qk(e) }(e, Xk() ? Reflect.construct(t, n || [], $k(e).constructor) : t.apply(e, n)) }

        function Xk() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (Xk = function() { return !!e })() }

        function $k(e) { return $k = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, $k(e) }

        function Qk(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }

        function Jk(e, t) { return Jk = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, Jk(e, t) }

        function eS(e, t, n) { return (t = tS(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function tS(e) { var t = function(e, t) { if ("object" != Uk(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Uk(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Uk(t) ? t : String(t) } Nk(Wk, "displayName", "PolarRadiusAxis"), Nk(Wk, "axisType", "radiusAxis"), Nk(Wk, "defaultProps", { type: "number", radiusAxisId: 0, cx: 0, cy: 0, angle: 0, orientation: "right", stroke: "#ccc", axisLine: !0, tick: !0, tickCount: 5, allowDataOverflow: !1, scale: "auto", allowDuplicatedCategory: !0 }); var nS = function(e) {
            function t(e) { var n; return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), eS(Qk(n = Yk(this, t, [e])), "pieRef", null), eS(Qk(n), "sectorRefs", []), eS(Qk(n), "id", ja("recharts-pie-")), eS(Qk(n), "handleAnimationEnd", (function() { var e = n.props.onAnimationEnd;
                    n.setState({ isAnimationFinished: !0 }), Ba()(e) && e() })), eS(Qk(n), "handleAnimationStart", (function() { var e = n.props.onAnimationStart;
                    n.setState({ isAnimationFinished: !1 }), Ba()(e) && e() })), n.state = { isAnimationFinished: !e.isAnimationActive, prevIsAnimationActive: e.isAnimationActive, prevAnimationId: e.animationId, sectorToFocus: 0 }, n } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && Jk(e, t) }(t, e), n = t, r = [{ key: "isActiveIndex", value: function(e) { var t = this.props.activeIndex; return Array.isArray(t) ? -1 !== t.indexOf(e) : e === t } }, { key: "hasActiveIndex", value: function() { var e = this.props.activeIndex; return Array.isArray(e) ? 0 !== e.length : e || 0 === e } }, { key: "renderLabels", value: function(e) { if (this.props.isAnimationActive && !this.state.isAnimationFinished) return null; var n = this.props,
                        r = n.label,
                        o = n.labelLine,
                        i = n.dataKey,
                        l = n.valueKey,
                        s = po(this.props, !1),
                        c = po(r, !1),
                        d = po(o, !1),
                        u = r && r.offsetRadius || 20,
                        h = e.map((function(e, n) { var h = (e.startAngle + e.endAngle) / 2,
                                m = dy(e.cx, e.cy, e.outerRadius + u, h),
                                p = Kk(Kk(Kk(Kk({}, s), e), {}, { stroke: "none" }, c), {}, { index: n, textAnchor: t.getTextAnchor(m.x, e.cx) }, m),
                                f = Kk(Kk(Kk(Kk({}, s), e), {}, { fill: "none", stroke: e.fill }, d), {}, { index: n, points: [dy(e.cx, e.cy, e.outerRadius, h), m], key: "line" }),
                                v = i; return Na()(i) && Na()(l) ? v = "value" : Na()(i) && (v = l), a.createElement(Po, { key: "label-".concat(e.startAngle, "-").concat(e.endAngle, "-").concat(e.midAngle, "-").concat(n) }, o && t.renderLabelLineItem(o, f), t.renderLabelItem(r, p, hg(e, v))) })); return a.createElement(Po, { className: "recharts-pie-labels" }, h) } }, { key: "renderSectorsStatically", value: function(e) { var t = this,
                        n = this.props,
                        r = n.activeShape,
                        o = n.blendStroke,
                        i = n.inactiveShape; return e.map((function(n, l) { if (0 === (null === n || void 0 === n ? void 0 : n.startAngle) && 0 === (null === n || void 0 === n ? void 0 : n.endAngle) && 1 !== e.length) return null; var s = t.isActiveIndex(l),
                            c = i && t.hasActiveIndex() ? i : null,
                            d = s ? r : c,
                            u = Kk(Kk({}, n), {}, { stroke: o ? n.fill : n.stroke, tabIndex: -1 }); return a.createElement(Po, qk({ ref: function(e) { e && !t.sectorRefs.includes(e) && t.sectorRefs.push(e) }, tabIndex: -1, className: "recharts-pie-sector" }, Qa(t.props, n, l), { key: "sector-".concat(null === n || void 0 === n ? void 0 : n.startAngle, "-").concat(null === n || void 0 === n ? void 0 : n.endAngle, "-").concat(n.midAngle, "-").concat(l) }), a.createElement(Tb, qk({ option: d, isActive: s, shapeType: "sector" }, u))) })) } }, { key: "renderSectorsWithAnimation", value: function() { var e = this,
                        t = this.props,
                        n = t.sectors,
                        r = t.isAnimationActive,
                        o = t.animationBegin,
                        i = t.animationDuration,
                        l = t.animationEasing,
                        s = t.animationId,
                        c = this.state,
                        d = c.prevSectors,
                        u = c.prevIsAnimationActive; return a.createElement(rd, { begin: o, duration: i, isActive: r, easing: l, from: { t: 0 }, to: { t: 1 }, key: "pie-".concat(s, "-").concat(u), onAnimationStart: this.handleAnimationStart, onAnimationEnd: this.handleAnimationEnd }, (function(t) { var r = t.t,
                            o = [],
                            i = (n && n[0]).startAngle; return n.forEach((function(e, t) { var n = d && d[t],
                                a = t > 0 ? Sa()(e, "paddingAngle", 0) : 0; if (n) { var l = Ra(n.endAngle - n.startAngle, e.endAngle - e.startAngle),
                                    s = Kk(Kk({}, e), {}, { startAngle: i + a, endAngle: i + l(r) + a });
                                o.push(s), i = s.endAngle } else { var c = e.endAngle,
                                    u = e.startAngle,
                                    h = Ra(0, c - u)(r),
                                    m = Kk(Kk({}, e), {}, { startAngle: i + a, endAngle: i + h + a });
                                o.push(m), i = m.endAngle } })), a.createElement(Po, null, e.renderSectorsStatically(o)) })) } }, { key: "attachKeyboardHandlers", value: function(e) { var t = this;
                    e.onkeydown = function(e) { if (!e.altKey) switch (e.key) {
                            case "ArrowLeft":
                                var n = ++t.state.sectorToFocus % t.sectorRefs.length;
                                t.sectorRefs[n].focus(), t.setState({ sectorToFocus: n }); break;
                            case "ArrowRight":
                                var r = --t.state.sectorToFocus < 0 ? t.sectorRefs.length - 1 : t.state.sectorToFocus % t.sectorRefs.length;
                                t.sectorRefs[r].focus(), t.setState({ sectorToFocus: r }); break;
                            case "Escape":
                                t.sectorRefs[t.state.sectorToFocus].blur(), t.setState({ sectorToFocus: 0 }) } } } }, { key: "renderSectors", value: function() { var e = this.props,
                        t = e.sectors,
                        n = e.isAnimationActive,
                        r = this.state.prevSectors; return !(n && t && t.length) || r && bv()(r, t) ? this.renderSectorsStatically(t) : this.renderSectorsWithAnimation() } }, { key: "componentDidMount", value: function() { this.pieRef && this.attachKeyboardHandlers(this.pieRef) } }, { key: "render", value: function() { var e = this,
                        t = this.props,
                        n = t.hide,
                        r = t.sectors,
                        o = t.className,
                        i = t.label,
                        l = t.cx,
                        s = t.cy,
                        c = t.innerRadius,
                        d = t.outerRadius,
                        u = t.isAnimationActive,
                        h = this.state.isAnimationFinished; if (n || !r || !r.length || !Ha(l) || !Ha(s) || !Ha(c) || !Ha(d)) return null; var m = va("recharts-pie", o); return a.createElement(Po, { tabIndex: this.props.rootTabIndex, className: m, ref: function(t) { e.pieRef = t } }, this.renderSectors(), i && this.renderLabels(r), Hy.renderCallByParent(this.props, null, !1), (!u || h) && $y.renderCallByParent(this.props, r, !1)) } }], o = [{ key: "getDerivedStateFromProps", value: function(e, t) { return t.prevIsAnimationActive !== e.isAnimationActive ? { prevIsAnimationActive: e.isAnimationActive, prevAnimationId: e.animationId, curSectors: e.sectors, prevSectors: [], isAnimationFinished: !0 } : e.isAnimationActive && e.animationId !== t.prevAnimationId ? { prevAnimationId: e.animationId, curSectors: e.sectors, prevSectors: t.curSectors, isAnimationFinished: !0 } : e.sectors !== t.curSectors ? { curSectors: e.sectors, isAnimationFinished: !0 } : null } }, { key: "getTextAnchor", value: function(e, t) { return e > t ? "start" : e < t ? "end" : "middle" } }, { key: "renderLabelLineItem", value: function(e, t) { if (a.isValidElement(e)) return a.cloneElement(e, t); if (Ba()(e)) return e(t); var n = va("recharts-pie-label-line", "boolean" !== typeof e ? e.className : ""); return a.createElement(Kz, qk({}, t, { type: "linear", className: n })) } }, { key: "renderLabelItem", value: function(e, t, n) { if (a.isValidElement(e)) return a.cloneElement(e, t); var r = n; if (Ba()(e) && (r = e(t), a.isValidElement(r))) return r; var o = va("recharts-pie-label-text", "boolean" === typeof e || Ba()(e) ? "" : e.className); return a.createElement(cu, qk({}, t, { alignmentBaseline: "middle", className: o }), r) } }], r && Zk(n.prototype, r), o && Zk(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);
        Bk = nS, eS(nS, "displayName", "Pie"), eS(nS, "defaultProps", { stroke: "#fff", fill: "#808080", legendType: "rect", cx: "50%", cy: "50%", startAngle: 0, endAngle: 360, innerRadius: 0, outerRadius: "80%", paddingAngle: 0, labelLine: !0, hide: !1, minAngle: 0, isAnimationActive: !ui.isSsr, animationBegin: 400, animationDuration: 1500, animationEasing: "ease", nameKey: "name", blendStroke: !1, rootTabIndex: 0 }), eS(nS, "parseDeltaAngle", (function(e, t) { return Ca(t - e) * Math.min(Math.abs(t - e), 360) })), eS(nS, "getRealPieData", (function(e) { var t = e.props,
                n = t.data,
                r = t.children,
                a = po(e.props, !1),
                o = so(r, Py); return n && n.length ? n.map((function(e, t) { return Kk(Kk(Kk({ payload: e }, a), e), o && o[t] && o[t].props) })) : o && o.length ? o.map((function(e) { return Kk(Kk({}, a), e.props) })) : [] })), eS(nS, "parseCoordinateOfPie", (function(e, t) { var n = t.top,
                r = t.left,
                a = t.width,
                o = t.height,
                i = uy(a, o); return { cx: r + Va(e.props.cx, a, a / 2), cy: n + Va(e.props.cy, o, o / 2), innerRadius: Va(e.props.innerRadius, i, 0), outerRadius: Va(e.props.outerRadius, i, .8 * i), maxRadius: e.props.maxRadius || Math.sqrt(a * a + o * o) / 2 } })), eS(nS, "getComposedData", (function(e) { var t = e.item,
                n = e.offset,
                r = Bk.getRealPieData(t); if (!r || !r.length) return null; var a = t.props,
                o = a.cornerRadius,
                i = a.startAngle,
                l = a.endAngle,
                s = a.paddingAngle,
                c = a.dataKey,
                d = a.nameKey,
                u = a.valueKey,
                h = a.tooltipType,
                m = Math.abs(t.props.minAngle),
                p = Bk.parseCoordinateOfPie(t, n),
                f = Bk.parseDeltaAngle(i, l),
                v = Math.abs(f),
                g = c;
            Na()(c) && Na()(u) ? (Da(!1, 'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'), g = "value") : Na()(c) && (Da(!1, 'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'), g = u); var y, b, w = r.filter((function(e) { return 0 !== hg(e, g, 0) })).length,
                z = v - w * m - (v >= 360 ? w : w - 1) * s,
                x = r.reduce((function(e, t) { var n = hg(t, g, 0); return e + (Ha(n) ? n : 0) }), 0);
            x > 0 && (y = r.map((function(e, t) { var n, r = hg(e, g, 0),
                    a = hg(e, d, t),
                    l = (Ha(r) ? r : 0) / x,
                    c = (n = t ? b.endAngle + Ca(f) * s * (0 !== r ? 1 : 0) : i) + Ca(f) * ((0 !== r ? m : 0) + l * z),
                    u = (n + c) / 2,
                    v = (p.innerRadius + p.outerRadius) / 2,
                    y = [{ name: a, value: r, payload: e, dataKey: g, type: h }],
                    w = dy(p.cx, p.cy, v, u); return b = Kk(Kk(Kk({ percent: l, cornerRadius: o, name: a, tooltipPayload: y, midAngle: u, middleRadius: v, tooltipPosition: w }, e), p), {}, { value: hg(e, g), startAngle: n, endAngle: c, payload: e, paddingAngle: Ca(f) * s }) }))); return Kk(Kk({}, p), {}, { sectors: y, data: r }) })); var rS = Bx({ chartName: "PieChart", GraphicalChild: nS, validateTooltipEventTypes: ["item"], defaultTooltipEventType: "item", legendContent: "children", axisComponents: [{ axisType: "angleAxis", AxisComp: Ak }, { axisType: "radiusAxis", AxisComp: Wk }], formatAxisMap: function(e, t, n, r, a) { var o = e.width,
                    i = e.height,
                    l = e.startAngle,
                    s = e.endAngle,
                    c = Va(e.cx, o, o / 2),
                    d = Va(e.cy, i, i / 2),
                    u = uy(o, i, n),
                    h = Va(e.innerRadius, u, 0),
                    m = Va(e.outerRadius, u, .8 * u); return Object.keys(t).reduce((function(e, n) { var o, i = t[n],
                        u = i.domain,
                        p = i.reversed; if (Na()(i.range)) "angleAxis" === r ? o = [l, s] : "radiusAxis" === r && (o = [h, m]), p && (o = [o[1], o[0]]);
                    else { var f = iy(o = i.range, 2);
                        l = f[0], s = f[1] } var v = xg(i, a),
                        g = v.realScaleType,
                        y = v.scale;
                    y.domain(u).range(o), kg(y); var b = Eg(y, ay(ay({}, i), {}, { realScaleType: g })),
                        w = ay(ay(ay({}, i), b), {}, { range: o, radius: m, realScaleType: g, scale: y, cx: c, cy: d, innerRadius: h, outerRadius: m, startAngle: l, endAngle: s }); return ay(ay({}, e), {}, oy({}, n, w)) }), {}) }, defaultProps: { layout: "centric", startAngle: 0, endAngle: 360, cx: "50%", cy: "50%", innerRadius: 0, outerRadius: "80%" } }); const aS = e => { let { data: t = [], isLoading: n, height: r, colors: a = [], drillDown: o } = e; const i = t.reduce(((e, t) => e + (null === t || void 0 === t ? void 0 : t.value) || 0), 0),
                    l = !t.length,
                    s = !(null === a || void 0 === a || !a.length),
                    c = t.map(((e, t) => ({ ...e, fill: s ? a[t % a.length] : "#51B5A9" }))); return (0, we.jsx)(dn.A, { loading: n, transparent: !0, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 32, children: [(0, we.jsx)(Qr, { title: "Activity by Organizations", subTitle: "Number of actions performed in each organization based on applied filters" }), l && (0, we.jsx)(se.A, { m: 3, children: (0, we.jsx)(jn.A, { text: "No activity based on applied filters." }) }), !l && (0, we.jsx)(ko, { height: r, children: (0, we.jsxs)(rS, { children: [(0, we.jsx)(nS, { dataKey: "value", data: c, innerRadius: 90, fill: "#8884d8", nameKey: "label", style: { outline: "none" }, label: e => "".concat((0, Tn.RP)(null === e || void 0 === e ? void 0 : e.label, 10), " (").concat(null === e || void 0 === e ? void 0 : e.value, ")"), labelLine: !1, cursor: "pointer", onClick: e => o && o("organizations", { id: e.id, label: e.label }), children: (0, we.jsx)(Hy, { value: "Total Activities: ".concat(i), position: "center" }) }), (0, we.jsx)(Mi, { content: (0, we.jsx)(OA, { color: a[0] }) })] }) })] }) }) },
            oS = e => { let { data: t = [], isLoading: n, height: r, color: a, drillDown: o } = e; const i = !t.length; return (0, we.jsx)(dn.A, { loading: n, transparent: !0, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 32, children: [(0, we.jsx)(Qr, { title: "Activity by Users", subTitle: "Number of actions performed by each user based on applied filters" }), i && (0, we.jsx)(se.A, { m: 3, children: (0, we.jsx)(jn.A, { text: "No activity based on applied filters." }) }), !i && (0, we.jsx)(ko, { height: r, children: (0, we.jsxs)(vA, { data: t, margin: { top: 5, right: 35, bottom: 5 }, children: [(0, we.jsx)(VA, { vertical: !1, strokeDasharray: "3" }), (0, we.jsx)(mA, { dataKey: "label" }), (0, we.jsx)(fA, { width: 35 }), (0, we.jsx)(Mi, { content: (0, we.jsx)(OA, { color: a }) }), (0, we.jsx)(lw, { dataKey: "value", fill: a, cursor: "pointer", onClick: e => o && o("users", { id: e.id, label: e.label }) })] }) })] }) }) }; var iS, lS, sS, cS, dS; const uS = 380,
            hS = ["actions", "organizations", "charts", "users", "permissions", "logins"],
            mS = ["#51B5A9", "#80C9C0", "#21A191"],
            pS = (0, ee.Ay)(se.A)(iS || (iS = (0, J.A)(["\n  ", "\n"])), (e => { let { gridGap: t } = e; return "\n  background-color: #ffffff;\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n  gap: ".concat(t || 16, "px; \n  ") })),
            fS = (0, ee.Ay)(se.A)(lS || (lS = (0, J.A)(["\n  max-width: 2500px;\n  gap: 16px;\n  display: flex;\n  flex-direction: column;\n"]))),
            vS = (0, ee.Ay)(se.A)(sS || (sS = (0, J.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(", "px, 1fr));\n  gap: 16px;\n"])), 500),
            gS = (0, ee.Ay)(se.A)(cS || (cS = (0, J.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(", "px, 1fr));\n  gap: 16px;\n  align-items: center;\n"])), 250),
            yS = (0, ee.Ay)(pS)(dS || (dS = (0, J.A)(["\n  ", "\n"])), (e => { let { isSmallScreen: t, isLargeScreen: n } = e; return "\n    ".concat(t || n ? "" : "grid-column: span 2", "\n    ").concat(n ? "grid-column: 1 / -1" : "", " \n  ") })),
            bS = () => { var e, t, n, r; const o = (0, ae.wA)(),
                    i = (0, ae.d4)(Kr),
                    l = (0, ae.d4)(Yr),
                    [s, c] = (0, a.useState)(!0),
                    d = (0, Rr.A)((e => e.breakpoints.down("md"))),
                    u = (0, Rr.A)((() => "(min-width:2320px)")),
                    h = l.id,
                    { control: m, setValue: p, getValues: f } = (0, oe.mN)({ defaultValues: i.appliedFilters }),
                    v = () => { const { organizations: e = [], charts: t = [], users: n = [], actions: r = [], permissions: a = [], logins: o = [] } = i.appliedFilters || {}, { startDate: l = null, endDate: s = null, organizations: c = [], charts: d = [], users: u = [], actions: h = [], permissions: m = [], logins: p = [] } = f() || {}; return { startDate: l, endDate: s, organizations: null !== c && void 0 !== c && c.length ? c : e, charts: null !== d && void 0 !== d && d.length ? d : t, users: null !== u && void 0 !== u && u.length ? u : n, actions: null !== h && void 0 !== h && h.length ? h : r, permissions: null !== m && void 0 !== m && m.length ? m : a, logins: null !== p && void 0 !== p && p.length ? p : o } },
                    g = async (e, t) => { var n, r; const a = "date" === e,
                            o = null === (n = i.appliedFilters) || void 0 === n ? void 0 : n[e];
                        a && o === t || !a && 1 === (null === o || void 0 === o ? void 0 : o.length) && (null === (r = o[0]) || void 0 === r ? void 0 : r.id) === (null === t || void 0 === t ? void 0 : t.id) || (a && (p("startDate", new Date(t)), p("endDate", new Date(t))), a || p(e, [t]), await y(v()), ve.A.trackEvent({ eventName: "AUDIT_CHART_CLICK", extraParams: { licenseId: h, filterType: e, selectedValue: t } })) }, y = async e => { c(!0), await Promise.all([o(qr.getActionLogReport({ licenseId: h, filters: e })), o(qr.getActionLogs({ licenseId: h, filters: e }))]), c(!1) }; return (0, a.useEffect)((() => { var e, t;
                    hS.forEach((e => p(e, [], { shouldDirty: !0 }))), p("startDate", null === i || void 0 === i || null === (e = i.appliedFilters) || void 0 === e ? void 0 : e.startDate, { shouldDirty: !0 }), p("endDate", null === i || void 0 === i || null === (t = i.appliedFilters) || void 0 === t ? void 0 : t.endDate, { shouldDirty: !0 }) }), [null === i || void 0 === i ? void 0 : i.appliedFilters]), (0, a.useEffect)((() => { ve.A.trackEvent({ eventName: "AUDIT_VISIT", extraParams: { licenseId: h } }), h && y(i.appliedFilters) }), [h]), (0, we.jsxs)(fS, { children: [(0, we.jsxs)(pS, { children: [(0, we.jsx)(je.A, { variant: "h3", children: "Organimi Audit Report" }), (0, we.jsx)(je.A, { children: "Inspect the actions performed by your internal team, external contractors, and shared audience. Find out which chart is the most active, who is viewing or editing the chart and their permissions." })] }), (0, we.jsxs)(pS, { children: [(0, we.jsx)(je.A, { variant: "h4", children: "Filters" }), (0, we.jsxs)(gS, { children: [(0, we.jsx)(pa, { control: m, disabled: s }), hS.map((e => { var t; return (0, we.jsx)(ca, { control: m, filterName: e, label: (0, Tn.ZH)(e), options: null === i || void 0 === i || null === (t = i.filters) || void 0 === t ? void 0 : t[e], disabled: s }, "".concat(e, "-filter-audit")) })), (0, we.jsx)(se.A, { children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", disabled: s, onClick: async () => { const e = v();
                                        ve.A.trackEvent({ eventName: "AUDIT_FILTERS_APPLY", extraParams: { licenseId: h, filters: e } }), await y(e) }, children: "Apply Filters" }) })] }), (0, we.jsx)(ha, { filters: i.appliedFilters, resetFilters: e => { if ("all" === e) return Object.keys((null === i || void 0 === i ? void 0 : i.appliedFilters) || {}).forEach((e => { p(e, ["startDate", "endDate"].includes(e) ? null : [], { shouldDirty: !0 }) })), void y({}); const t = ["startDate", "endDate"].includes(e) ? null : [];
                                y({ ...(null === i || void 0 === i ? void 0 : i.appliedFilters) || {}, [e]: t }), ve.A.trackEvent({ eventName: "AUDIT_FILTERS_RESET", extraParams: { licenseId: h, type: e } }) }, disabled: s })] }), (0, we.jsxs)(vS, { children: [(0, we.jsx)(pS, { children: (0, we.jsx)(aS, { data: null === i || void 0 === i || null === (e = i.report) || void 0 === e ? void 0 : e.organizations, isLoading: s, height: uS, colors: mS, drillDown: g }) }), (0, we.jsx)(pS, { children: (0, we.jsx)(RA, { data: null === i || void 0 === i || null === (t = i.report) || void 0 === t ? void 0 : t.charts, isLoading: s, height: uS, color: mS[0], drillDown: g }) }), (0, we.jsx)(pS, { children: (0, we.jsx)(oS, { data: null === i || void 0 === i || null === (n = i.report) || void 0 === n ? void 0 : n.users, isLoading: s, height: uS, color: mS[0], drillDown: g }) }), (0, we.jsx)(pS, { children: (0, we.jsx)(tk, { data: null === i || void 0 === i || null === (r = i.report) || void 0 === r ? void 0 : r.dates, isLoading: s, height: uS, color: mS[0], drillDown: g }) }), (0, we.jsx)(yS, { isSmallScreen: d, isLargeScreen: u, children: (0, we.jsx)(oa, { dataSelector: Zr, pageInfo: null === i || void 0 === i ? void 0 : i.pageInfo, requestParams: { licenseId: h, filters: null === i || void 0 === i ? void 0 : i.appliedFilters }, isLoading: s, height: uS, isLargeScreen: u, csvString: null === i || void 0 === i ? void 0 : i.csvString, color: mS[0], fetchApi: qr.getActionLogs, onExport: async () => { const { payload: e, error: t } = await o(qr.exportActionLogs({ licenseId: h, filters: null === i || void 0 === i ? void 0 : i.appliedFilters }));
                                    null !== e && void 0 !== e && e.csvString && !t && (vr(e.csvString, "organimi-audit-report-".concat((new Date).getTime())), ve.A.trackEvent({ eventName: "AUDIT_LOGS_EXPORTED", extraParams: { licenseId: h, filters: null === i || void 0 === i ? void 0 : i.appliedFilters } })) } }) })] })] }) }; var wS = n(53492); const zS = n.p + "static/media/organimi_connect_logo.599c55311dde8dd2953c1d52aa11cf98.svg"; var xS = n(17323),
            AS = n(9922),
            kS = n(749),
            SS = n(84187),
            MS = n(4981); const ES = e => { var t; let { integration: n, handleClick: r } = e; const a = MS.CF[n.id]; return (0, we.jsx)(kS.A, { variant: "outlined", children: (0, we.jsx)(SS.A, { children: (0, we.jsxs)(se.A, { width: 280, onClick: r, bgcolor: "#ffffff", display: "flex", alignItems: "center", gridGap: 16, p: 1, position: "relative", children: [n.active && (0, we.jsx)(se.A, { position: "absolute", top: 0, right: 0, bgcolor: "#3CD3C2", borderRadius: "2px", p: "3px", children: (0, we.jsx)(je.A, { fontSize: "10px", weight: "bold", color: "#ffffff", children: "Active" }) }), (0, we.jsx)(se.A, { m: 1, width: 70, children: (0, we.jsx)(wS.A, { height: 50, src: null === (t = (0, AS.NJ)(n.id)) || void 0 === t ? void 0 : t.icon, alt: a }) }), (0, we.jsx)(je.A, { variant: "caption", color: "primary", children: a })] }) }) }) },
            CS = e => { let { hideTitle: t } = e; const [n, r] = a.useState(!0), o = (0, $.useHistory)(), i = (0, Kt.A)(), l = null === i || void 0 === i ? void 0 : i.id, s = (0, ae.wA)(), c = (0, ae.d4)(Lt.fg), d = e => () => { var t;
                    ve.A.trackEvent({ eventName: "INTEGRATION_CLICK", extraParams: { importType: e } }), (t = e, e => { o.push((0, ne.ve)({ orgId: e, importType: t }), { appRoute: !0 }) })(l) }; return (0, a.useEffect)((() => { l && s(xS.Ah.getIntegrations({ orgId: l })).then((() => { r(!1) })) }), [l]), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", alignItems: "center", position: "relative", overflow: "auto", children: [!t && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(wS.A, { height: 70, src: zS }) }), (0, we.jsx)(se.A, { mb: 3, children: (0, we.jsx)(je.A, { variant: "subtitle1", align: "center", children: "Sync Organimi with applications you use every day!" }) })] }), (0, we.jsx)(dn.A, { loading: n, title: "Loading available and active integrations", children: (0, we.jsx)(se.A, { display: "flex", flexWrap: "wrap", gridGap: 28, py: 2, justifyContent: "center", maxWidth: 950, overflow: "auto", flex: !0, position: "relative", children: c.map((e => (0, we.jsx)(ES, { integration: e, handleClick: d(e.id) }))) }) }), (0, we.jsx)(se.A, { py: 2, mt: 2, borderTop: "solid 1px #eeeeee;", children: (0, we.jsx)("a", { href: "mailto:<EMAIL>", children: "Contact us if you do not see your system listed above!" }) })] }) }; var TS = n(49157),
            HS = n(85725),
            LS = n(58053),
            IS = n(84091),
            jS = n(43862),
            VS = n(53109),
            OS = n(93545),
            RS = n(61342),
            PS = n(41473),
            DS = n(31312),
            FS = n(20447),
            NS = n(69219),
            _S = n(16091),
            BS = n(27999),
            WS = n(8622),
            US = n(10075),
            qS = n(3259); const GS = () => { const { orgId: e } = (0, $.useParams)(), t = (0, ae.wA)(), n = (0, ae.d4)(qS.vi), { editThemeMode: r, resource: o } = (0, Q.A)(), i = r ? 1 : .7, [l, s] = (0, he.A)((async () => { const { people: e, roles: r, chart: a } = (0, _S.A)("dashboardThemeChartData");
                await Promise.all([await t((0, VS.jv)({ chart: a })), await t((0, jS.CK)({ roles: r.map((e => (0, Cn.ic)(e, n))) })), await t((0, IS.qu)({ people: e.map((e => (0, Cn.ic)(e, n))) }))]) })), [c, d] = (0, he.A)((async () => { await t(nn.UY.get({ orgId: e })) }));
            (0, a.useEffect)((() => { e && null !== n && void 0 !== n && n.length && s() }), [n]), (0, a.useEffect)((() => {!e || null !== n && void 0 !== n && n.length || d() }), []), (0, a.useEffect)((() => (async function() { await t((0, NS.Wq)({ primary: "dashboard", secondary: "themes" })) }(), () => {})), ["dashboard"]); const u = l || c; return (0, we.jsx)(HS.A, { container: !0, children: (0, we.jsxs)(BS.A, { children: [(0, we.jsx)(TS.A, { width: Wt.Ay.dialogs.themeSettingsWidth * i, children: (0, we.jsx)(OS.LG, { sample: !0 }) }), (0, we.jsx)(FS.A, { item: !0, xs: !0, children: (0, we.jsx)(dn.A, { loading: u, children: (0, we.jsx)(RS.A, { children: (0, we.jsx)(PS.A, { children: (0, we.jsx)(DS.A, { orgId: "sampleOrganization", chartId: "sampleChart", children: "directory" === o ? (0, we.jsx)(WS.A, { defaults: { resourceAction: "view", resource: "directory" } }) : "photoboard" === o ? (0, we.jsx)(US.A, { defaults: { resourceAction: "view", resource: "photoboard" } }) : (0, we.jsx)(LS.A, { readOnly: !0, orgId: "sampleOrganization" }) }) }) }) }) })] }) }) }; var KS, ZS, YS, XS, $S = n(81635),
            QS = n(56650),
            JS = n(75490),
            eM = n(20913);
        (0, ee.Ay)("div")(KS || (KS = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: relative;\n  align-items: flex-start;\n\n  a{color:".concat(t.palette.common.black, "}\n  ") })); const tM = (0, ee.Ay)("div")(ZS || (ZS = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow:hidden;\n  position: relative;\n  align-items: center;\n  #absoluteLogo{\n    position:absolute;\n    top:".concat(t.spacing(3), "px;\n    left:").concat(t.spacing(3), "px;\n  }\n  a{color:").concat(t.palette.common.black, "}\n  ") })),
            nM = (0, ee.Ay)("div")(YS || (YS = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  background:white;\n  z-index:1;\n  left:0;\n  right:0;\n  position:absolute;\n  bottom:0;\n  display:flex;\n  align-items:center;\n  justify-content:center;\n  padding:".concat(t.spacing(2), "px;\n  ") })),
            rM = (0, ee.Ay)("div")(XS || (XS = (0, J.A)(["\n  background: white;\n  left: 0;\n  right: 0;\n  top: 0;\n  position: absolute;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 20;\n  .MuiLinearProgress-root {\n    width: 60%;\n  }\n"]))); var aM = n(80575),
            oM = n(77925);

        function iM(e) { let { isComplete: t, status: n, logo: r = null } = e; const a = (0, le.A)(); return (0, we.jsxs)(rM, { children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(wS.A, { height: 70, src: r || zS }) }), t ? (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(Ee.Ay, { icon: "CheckCircle", style: { fontSize: "8rem" }, color: a.palette.secondary.main }) }), (0, we.jsx)(je.A, { variant: "h5", children: n }), (0, we.jsx)(nM, {})] }) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(oM.A, { variant: "indeterminate", style: { height: 15 } }), (0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(je.A, { variant: "h5", children: n }) }), (0, we.jsx)(nM, {})] })] }) } const lM = e => { let { setActiveStep: t, setState: n, state: r, cancel: o } = e; const [i, l] = (0, a.useState)("none"), s = (0, ae.d4)(eM.z), c = (0, ae.wA)(), [d, u] = (0, aM.A)({ minDelay: 2e3, onSubmit: async () => await c(IS.OH.getPeopleForPhotos({ orgId: i })), onComplete: e => { e && t("photoMatch") } }); return (0, we.jsx)(tM, { children: d ? (0, we.jsx)(iM, { status: "Please wait..." }) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(wS.A, { height: 70, src: zS }) }), (0, we.jsx)(se.A, { mb: 2, children: (0, we.jsxs)(je.A, { variant: "h1", align: "center", children: ["Which ", (0, we.jsx)("strong", { children: "Organization" }), " do you want to add the photos to?"] }) }), (0, we.jsx)(se.A, { p: 2, width: 640, children: (0, we.jsxs)(Ve.A, { select: !0, name: "organization", value: i, fullWidth: !0, children: [(0, we.jsx)(Ze.A, { value: "none", children: "Select an Organization" }), s.map((e => (0, we.jsx)(Ze.A, { value: e.id, onClick: () => { return t = e.id, l(t), void n((() => ({ ...r, orgId: t }))); var t }, children: e.name }, e.id)))] }) }), (0, we.jsxs)(nM, { children: [(0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(Ge.A, { onClick: o, children: "Cancel" }) }), (0, we.jsx)(se.A, { ml: 1, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", disabled: d || "none" === i, onClick: u, children: "Next" }) })] })] }) }) }; var sM, cM = n(70491),
            dM = n(67698),
            uM = n(78300); const hM = e => { let { state: t, setState: n, cancel: r, orgId: o } = e; const { bulkPhotoImages: i } = t, { openDialog: l } = (0, Ft.A)("selectMember"), s = (0, le.A)(), { show: c } = (0, Un.A)(), [d, u] = (0, a.useState)(""), h = (0, a.useRef)(1), [m, p] = (0, a.useState)(!1), f = (0, ae.wA)(), v = (0, $.useHistory)(), [g, y] = (0, aM.A)({ minDelay: 2e3, onComplete: () => { v.push((0, ne.Zm)({ orgId: o })) } }), b = e => r => { let a = i; for (var o = 0; o < a.length; o++)
                        if (a[o].name.toString() === e.toString()) { a[o].member = { ...r }, n((() => ({ ...t, bulkPhotoImages: [...a] }))); break } }, w = (0, a.useMemo)((() => i.reduce(((e, t) => (t.member && e.push(t.member.id), e)), [])), [i]), z = e => () => { l({ matched: w, onSelect: b(e) }) }, x = (e, r) => () => { let a = i; for (var o = 0; o < a.length; o++)
                        if (a[o].name.toString() === r.toString()) { A(a[o], new Blob([k(a[o].src.replace(/^data:image\/(png|jpeg|jpg);base64,/, ""))], { type: "image/png" }), e).then((function(i) { null !== i ? (a[o].src = i.src, n((() => ({ ...t, bulkPhotoImages: [...a] }))), c("Photo '" + r + "' has been rotated 90 degrees to the " + e, "success")) : c("Failed to rotate photo '" + r + "'", "error") })).catch((function(e) { console.log(e), c("Failed to rotate photo '" + r + "'", "error") })); break } }, A = (e, t, n) => new Promise((function(r) { let a = new FileReader;
                    a.onload = function() { let t = new Image;
                        t.onload = function() { let a = document.createElement("canvas"); var o = a.getContext("2d");
                            o.clearRect(0, 0, a.width, a.height), a.width = 200, a.height = 200, "left" === n ? (o.rotate(-.5 * Math.PI), o.translate(-a.width, 0)) : "right" === n && (o.rotate(.5 * Math.PI), o.translate(0, -a.height)), o.drawImage(t, 0, 0, t.width, t.height), r({ src: a.toDataURL("image/png"), name: e.name.replace(/\.[^/.]+$/, ""), ext: "png", format: "base64" }) }, t.onerror = function() { let t = "Failed to load image: " + e.name;
                            console.log(t), r(null) }, t.src = a.result }, a.onerror = function() { let t = "Failed to read image: " + e.name;
                        console.log(t), r(null) }, "undefined" !== typeof t && null !== t ? a.readAsDataURL(t) : a.readAsDataURL(e) })), k = e => { for (var t = window.atob(e), n = t.length, r = new Uint8Array(n), a = 0; a < n; a++) r[a] = t.charCodeAt(a); return r.buffer }, S = m || g; return (0, we.jsx)(tM, { children: S ? (0, we.jsx)(iM, { isComplete: g, status: d }) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(wS.A, { height: 70, src: zS }) }), (0, we.jsxs)(se.A, { mb: 2, children: [(0, we.jsx)(je.A, { variant: "body1", align: "left", children: "".concat((null === i || void 0 === i ? void 0 : i.length) || 0, " photos detected. ").concat((null === i || void 0 === i ? void 0 : i.length) > 0 ? w.length + " matching people found." : "") }), (null === i || void 0 === i ? void 0 : i.length) - w.length > 0 && (0, we.jsx)(je.A, { variant: "body1", align: "left", color: "error", children: "".concat(i.length - w.length, " photos could not be matched with an existing person. Please review and select the corresponding person.") })] }), (0, we.jsx)(se.A, { p: 2, style: { flex: 1, overflow: "auto", paddingBottom: 100 }, clone: !0, children: (0, we.jsx)(Tt.A, { container: !0, spacing: 2, justifyContent: "center", children: i && i.map((e => { let { name: t, member: n, src: r } = e; return (0, we.jsxs)(mM, { children: [(0, we.jsxs)(Tt.A, { container: !0, justifyContent: "flex-end", children: [(0, we.jsx)(hr.A, { onClick: x("left", t), children: (0, we.jsx)(cM.A, {}) }), (0, we.jsx)(hr.A, { onClick: x("right", t), children: (0, we.jsx)(dM.A, {}) })] }), r ? (0, we.jsx)(uM.A, { variant: "square", width: 120, height: 120, src: r }) : (0, we.jsx)(tr.A, { variant: "circle", width: 120, height: 120 }), n ? (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(Tt.A, { container: !0, spacing: 2, justifyContent: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "Check", color: s.palette.primary.main, size: "sm" }), (0, we.jsx)(je.A, { variant: "body1", align: "left", children: "".concat(n.name) })] }), (0, we.jsxs)(Tt.A, { container: !0, spacing: 2, justifyContent: "center", children: [(0, we.jsx)(Ee.Ay, { icon: "File", color: s.palette.primary.main, size: "xs" }), (0, we.jsx)(je.A, { variant: "body2", align: "left", children: t })] })] }) : (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(Tt.A, { container: !0, spacing: 2, justifyContent: "center", style: { color: s.palette.error.main }, children: [(0, we.jsx)(Ee.Ay, { icon: "ExclamationTriangle", color: s.palette.error.main, size: "xs" }), (0, we.jsx)(je.A, { variant: "body1", align: "left", color: "inherit", children: "No person found with the name '".concat(t, "'") })] }) }), (0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", onClick: z(t), children: n ? "Change Person" : "Find Person" })] }, "photo-".concat(n ? n.id : t)) })) }) }), (0, we.jsxs)(nM, { children: [(0, we.jsx)(se.A, { mr: 1, children: (0, we.jsx)(Ge.A, { onClick: r, children: "Cancel" }) }), (0, we.jsx)(se.A, { ml: 1, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", disabled: !w.length, onClick: async () => { u("Processing ".concat(w.length, " photos")), p(!0); const e = i.reduce(((e, t) => (t.member && e.push(t), e)), []);
                                        await Promise.all(e.map((async e => { await f(IS.OH.updatePerson({ orgId: o, personId: e.member.id, data: { newMemberPhoto: e.src } })), h.current += 1, u("Processed ".concat(h.current, " of ").concat(w.length, " photos")) }))), u("Finished uploading your images"), y(), p(!1) }, children: "Upload Photos Now" }) })] })] }) }) },
            mM = (0, ee.Ay)(kS.A)(sM || (sM = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n &:hover{border: 1px solid ".concat(t.palette.primary.main, ";}\n padding:").concat(t.spacing(2), "px;\n margin:").concat(t.spacing(1), "px;\n height:300px;\n width: 216px;\n overflow:hidden;\n display:flex;\n flex-direction:column;\n justify-content: space-between;\n align-items:center;\n .MuiTypography-body1{font-size:12px;}\n .MuiTypography-body2{font-size:10px;}\n\n") })); var pM, fM = n(96446),
            vM = n(68903),
            gM = n(94281),
            yM = n(26159),
            bM = n.n(yM),
            wM = n(37294); const zM = ee.Ay.div(pM || (pM = (0, J.A)(["\n  display: flex;\n  padding: 48px 72px;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 10px;\n  align-self: stretch;\n  border-radius: var(--sds-size-radius-200);\n  border: 2px dashed var(--Neutral-500, #aaa);\n  background: var(--Violet-0, #f8f5ff);\n"]))),
            xM = e => { let { state: t, setState: n, setActiveStep: r } = e; const { openDialog: a } = (0, Ft.A)("errorDialog"), [o, i] = (0, aM.A)({ minDelay: 2e3, onComplete: () => { r("orgSelect") } }), [l, s] = (0, aM.A)({ minDelay: 1e3, onSubmit: async e => { if (n({ processingFileMsg: "Processing File(s)...", bulkPhotoImages: [], bulkPhotoImageMembersNotFound: 0, editBulkPhotoMember: null }), e && e.length > 0 && "" !== e[0].name) { let t = 0,
                                r = 300; if (e.forEach((function(n, r) { t += e[r].size / 1024 / 1024 })), t > r) return n({ processingFileMsg: null }), { payload: {}, error: "The file(s) you have selected are too big. You can only upload " + r + "MB at a time and your selection is " + parseInt(t) + "MB. Please try uploading less images at once." }; let a = [],
                                o = ge.A.getFileExtensionFromFilename(e[0].name); return "zip" === o ? u(e).then((function(e) { return e.forEach((function(e) { null !== e && (e.name.indexOf("/") > 0 && (e.name = e.name.substring(e.name.lastIndexOf("/") + 1)), a.push(e)) })), n({ bulkPhotoImages: a, processingFileMsg: null }), { payload: {}, error: null } })) : "png" === o || "jpg" === o || "jpeg" === o ? m(e).then((function(e) { return e.forEach((function(e) { null !== e && a.push(e) })), n({ bulkPhotoImages: a, processingFileMsg: null }), { payload: {}, error: null } })) : (n({ processingFileMsg: null }), { payload: {}, error: "The file type '" + o + "' is not supported. Please upload file(s) with the extension .zip, .png, .jpg, .jpeg" }) } return n({ processingFileMsg: null }), { payload: {}, error: "Please select a file" } }, onError: e => { let { error: t } = e;
                        a({ title: "File Error", message: t }) }, onComplete: () => { i() } }), { getRootProps: c, getInputProps: d } = (0, gM.VB)({ onDrop: s, accept: "image/jpeg,image/png,.zip" }), u = e => (n({ bulkPhotoImages: [] }), new Promise((function(t, n) { new(bM().external.Promise)((function(t) { t(e[0]) })).then((function(e) { return bM().loadAsync(e) })).then((function(e) { if (e && "undefined" !== typeof e.files && null !== e) { let n = []; return Object.keys(e.files).forEach((function(t) { var r = t.substr(t.lastIndexOf(".") + 1).toLowerCase();
                                t.indexOf("__MACOSX") < 0 && ("png" === r || "jpg" === r || "jpeg" === r) && n.push(h(e.files[t])) })), Promise.all(n).then((function(e) { t(e) })) } n("No files could be found in the zip file.") })) }))), h = e => new Promise((function(t) { try { e.async("uint8array").then((function(n) { let r = ge.A.getFileExtensionFromUInt8Array(e.name, n); if ("png" !== r && "jpg" !== r && "jpeg" !== r) return t(null);
                            p(e, new Blob([n], { type: "image/" + r })).then((function(e) { t(e) })).catch((function(e) { console.log(e), t(null) })) })) } catch (n) { console.log(n), t(null) } })), m = e => new Promise((function(t, n) { var r = []; return e.forEach((function(e) { r.push(ge.A.getFileExtension(e)) })), Promise.all(r).then((function(r) { for (var a = [], o = 0; o < e.length; o++) "png" !== r[o] && "jpg" !== r[o] && "jpeg" !== r[o] || a.push(p(e[o])); return Promise.all(a).then((function(e) { t(e) })).catch((function(e) { console.log(e), n("An error occurred processing the images") })) })).catch((function(e) { console.log(e), n("An error occurred processing the images") })) })), p = (e, t, r) => (n({ processingFileMsg: "Processing File(s)... " + e.name }), new Promise((function(n) { let a = new FileReader,
                        o = 200;
                    a.onload = function() { let t = new Image;
                        t.onload = function() { let a = document.createElement("canvas");
                            t.height > o ? (t.width *= o / t.height, t.height = o) : t.width > o && (t.height *= o / t.width, t.width = o); var i = a.getContext("2d");
                            i.clearRect(0, 0, a.width, a.height), a.width = t.width, a.height = t.height, "left" === r ? (i.rotate(-.5 * Math.PI), i.translate(-a.width, 0)) : "right" === r && (i.rotate(.5 * Math.PI), i.translate(0, -a.height)), i.drawImage(t, 0, 0, t.width, t.height), n({ src: a.toDataURL("image/png"), name: e.name.replace(/\.[^/.]+$/, ""), ext: "png", format: "base64" }) }, t.onerror = function() { let t = "Failed to load image: " + e.name;
                            console.log(t), n(null) }, t.src = a.result }, a.onerror = function() { let t = "Failed to read image: " + e.name;
                        console.log(t), n(null) }, "undefined" !== typeof t && null !== t ? a.readAsDataURL(t) : a.readAsDataURL(e) }))), f = l || o, v = l ? t.processingFileMsg ? t.processingFileMsg : "Loading your file. Please wait..." : o ? "Success! Your file is ready for import" : ""; return (0, we.jsx)(fM.A, { display: "flex", flexDirection: "column", alignItems: "center", ...c(), children: f ? (0, we.jsx)(iM, { isComplete: o, status: v }) : (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)("input", { ...d() }), (0, we.jsx)(fM.A, { my: 3, children: (0, we.jsx)(wS.A, { height: 70, src: zS }) }), (0, we.jsx)(vM.Ay, { container: !0, justifyContent: "center", children: (0, we.jsxs)(zM, { children: [(0, we.jsxs)(fM.A, { mb: 3, textAlign: "center", display: "flex", flexDirection: "column", gap: 2, children: [(0, we.jsx)(ln.A, { variant: "displayMD", color: wM.Qs.Blue[500], children: "Bulk Photo Import" }), (0, we.jsx)(ln.A, { variant: "h2", color: wM.Qs.Neutrals[700], children: "Import photos for everyone in minutes!" }), (0, we.jsx)(fM.A, { display: "flex", gap: 2, my: 2, justifyContent: "center", children: [1, 2, 3, 4].map((e => (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.me, { name: 1 === e ? "image-polaroid-user" : 2 === e ? "image-portrait" : 3 === e ? "id-badge" : "image-user", size: "x3", variant: e % 2 ? "light" : "solid", color: wM.Qs.Neutrals[600] }) }))) })] }), (0, we.jsx)(fM.A, { display: "flex", flexDirection: "column", gap: 4, alignItems: "center", alignSelf: "center", children: (0, we.jsx)(Ge.A, { startIcon: (0, we.jsx)(Ee.Ay, { icon: "Add" }), variant: "contained", color: "primary", children: "Upload a Zip File" }) })] }) })] }) }) },
            AM = () => { const [e, t] = (0, a.useState)("selectFile"), n = (0, ae.d4)(eM.K), [r, o] = (0, a.useState)({ bulkPhotoImages: [], bulkPhotoImageMembersNotFound: 0, processingFileMsg: null, orgId: null }), i = () => { o({ bulkPhotoImages: [], bulkPhotoImageMembersNotFound: 0, processingFileMsg: null }), t("selectFile") }; return (0, a.useEffect)((() => { "photoMatch" === e && (e => {
                        function t(e) { try { return e.normalize("NFD").replace(/[\u0300-\u036f]/g, "") } catch (t) { return e } } let a = 0,
                            i = Object.values(n || {}),
                            l = e || [];
                        l = l.map((function(e) { e.member = null; for (var n = 0; n < i.length; n++) { const r = i[n].name,
                                    a = (0, Cn.zY)(i[n]),
                                    o = (0, Cn.II)(i[n]),
                                    l = (0, Cn.US)(i[n]); if (a && t(e.name.toLowerCase()) === t(a.toLowerCase())) { e.member = i[n]; break } if (r && t(e.name.replace(/\s/g, "").toLowerCase()) === t(r.replace(/\s/g, "").toLowerCase())) { e.member = i[n]; break } if (o && l && t(e.name.replace(/\s/g, "").replace(/[,]/g, "").toLowerCase()) === t(l.toLowerCase() + o.toLowerCase()) || t(e.name.replace(/\s/g, "").replace(/[,]/g, "").toLowerCase()) === t(o.toLowerCase() + l.toLowerCase())) { e.member = i[n]; break } } return null === e.member && a++, e })), o({ ...r, bulkPhotoImages: l.sort((e => e.member ? 1 : -1)), bulkPhotoImageMembersNotFound: a }) })(r.bulkPhotoImages) }), [n, e]), (0, we.jsxs)(dn.A, { loading: "loading" === e, children: ["selectFile" === e && (0, we.jsx)(xM, { state: r, setState: o, setActiveStep: t }), "orgSelect" === e && (0, we.jsx)(lM, { state: r, cancel: i, setState: o, setActiveStep: t }), "photoMatch" === e && (0, we.jsx)(hM, { state: r, cancel: i, setState: o, setActiveStep: t, orgId: r.orgId })] }) }; var kM = n(50543),
            SM = n(42518),
            MM = n(32115),
            EM = n(63296),
            CM = n(74822),
            TM = n(97184); const HM = e => { let { user: t } = e; const { isOwnershipEnabled: n, isMinOwner: r } = (0, EM.A)(), { openOrgEntityDialog: a, openOrgMetaDialog: o, openCreateOwnershipChartDialog: i } = (0, TM.A)(), l = ["Good Morning", "Good Afternoon", "Good Evening"][(() => { const e = (new Date).getHours(); return e < 12 ? 0 : e < 16 ? 1 : 2 })()], s = (0, CM.A)(t.firstName || ""); return (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", alignItems: "center", flexWrap: "wrap", gap: 3, children: [(0, we.jsxs)(ln.A, { variant: MM.Eq.displayLG, children: [l, ", ", s] }), r && (0, we.jsxs)(fM.A, { display: "flex", gap: 1, children: [n && (0, we.jsx)(fM.A, { display: "flex", gap: 1, children: (0, we.jsxs)(SM.A, { variant: "outlined", size: "medium", onClick: () => i({}), children: [(0, we.jsx)(Ee.gF, { icon: "Add", size: "lg" }), "Create Ownership Chart"] }) }), (0, we.jsxs)(SM.A, { variant: "contained", size: "medium", onClick: () => n ? a({ mode: "create", orgOnlyMode: !0 }) : o({ redirectToOrganization: !0 }), children: [(0, we.jsx)(Ee.gF, { icon: "Add", size: "lg" }), "New Organization"] })] })] }) }; var LM = n(34535),
            IM = n(72152),
            jM = n(43466),
            VM = n(67784),
            OM = n(32143),
            RM = n(84511),
            PM = n(39336),
            DM = n(69384),
            FM = n(32601),
            NM = n(72952); const _M = { "& .MuiInputBase-root": { backgroundColor: wM.Qs.Neutrals[0], cursor: "pointer", height: DM.WM } },
            BM = (0, LM.Ay)(IM.A)((() => ({ backgroundColor: wM.Qs.Neutrals[0], ["& .".concat(jM.A.grouped)]: { border: 0, ["&.".concat(jM.A.disabled)]: { border: 0 }, padding: 0, margin: 0 }, ["& .".concat(jM.A.firstButton)]: { margin: "10px 10px 10px 12px" }, ["& .".concat(jM.A.lastButton)]: { margin: "10px 12px 10px 10px" } }))),
            WM = (0, LM.Ay)(VM.A)(_M),
            UM = (0, LM.Ay)(fM.A)((() => ({ display: "flex", flexWrap: "wrap", alignItems: "center", "> .filter": { minWidth: 300, maxWidth: 400, flex: 1 } }))),
            qM = e => { let { color: t, icon: n } = e; return (0, we.jsx)(fM.A, { lineHeight: 0, children: (0, we.jsx)(Ee.gF, { size: "lg", icon: n, color: t }) }, "toggle-view-".concat(t, "-").concat(n)) },
            GM = e => { var t; let { handleSearch: n, createSortHandler: r, orderBy: a, headCells: o, order: i, setIsCardView: l, isCardView: s, totalCount: c, filterBy: d, handleFilter: u } = e; const { isOwnershipEnabled: h } = (0, EM.A)(); return (0, we.jsxs)(UM, { gap: 4, mb: 2, children: [(0, we.jsx)(WM, { placeholder: "Search organizations", variant: "outlined", InputProps: { endAdornment: (0, we.jsx)(Ee.gF, { icon: "Search", size: "xs" }) }, onChange: n, onFocus: () => { ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_FILTERS_CLICKED", extraParams: { action: "search-org", isOwnershipEnabled: h } }) }, className: "filter" }), (0, we.jsx)(WM, { select: !0, placeholder: "Select", value: a, className: "filter", onClick: () => { ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_FILTERS_CLICKED", extraParams: { action: "sort-org", isOwnershipEnabled: h } }) }, children: o.map((e => { if ("controls" !== e.name) return (0, we.jsx)(OM.A, { value: e.name, onClick: r(e.name), children: (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", width: "100%", children: [(0, we.jsxs)(fM.A, { display: "flex", gap: 1, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.subheadingSM, children: "Sort: " }), (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: e.label })] }), a === e.name && (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.gF, { icon: "desc" === i ? "ListSort" : "ListSortUp" }) }, "icon-lbl-".concat(i, "-").concat(e.name))] }) }, e.name) })) }), h && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(fM.A, { className: "filter", style: { backgroundColor: wM.Qs.Neutrals[0], cursor: "pointer", height: DM.WM }, children: (0, we.jsx)(FM.A, { selected: (null === d || void 0 === d ? void 0 : d.hierarchyLevel) || [], name: "filterHierarchyLevel", handleSelect: (e, t) => u({ ...d, hierarchyLevel: t }), options: Object.values(NM.bV), freeSolo: !1, limitTags: 1, multiple: !0, fullWidth: !0, size: "medium", hideChips: !1, customPlaceHolder: "Filter by hierarchy level" }) }), (0, we.jsx)(fM.A, { className: "filter", style: { backgroundColor: wM.Qs.Neutrals[0], cursor: "pointer", height: DM.WM }, children: (0, we.jsx)(FM.A, { selected: (null === d || void 0 === d || null === (t = d.hasRelations) || void 0 === t ? void 0 : t.map((e => "true" === e ? "Yes" : "No"))) || [], name: "filterHierarchyLevel", handleSelect: (e, t) => { let n = null; return "string" === typeof t && (n = [("Yes" === t).toString()]), Array.isArray(t) && (n = t.map((e => ("Yes" === e).toString()))), u({ ...d, hasRelations: n }) }, options: ["Yes", "No"], freeSolo: !1, limitTags: 1, multiple: !0, fullWidth: !0, size: "medium", hideChips: !1, customPlaceHolder: "Filter by has ownership" }) })] }), (0, we.jsxs)(fM.A, { ml: "auto", display: "flex", gap: 1, height: DM.WM, children: [(0, we.jsxs)(fM.A, { bgcolor: wM.Qs.Neutrals[0], px: 2, display: "flex", alignItems: "center", gap: 1, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Total Organizations:" }), (0, we.jsx)(ln.A, { variant: MM.Eq.subheadingMD, children: c })] }), (0, we.jsxs)(BM, { size: "small", exclusive: !0, "aria-label": "text alignment", className: "filter", children: [(0, we.jsx)(RM.A, { value: !0, disabled: s, onClick: () => { l(!0), ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_CHANGE_VIEW_CLICKED", extraParams: { action: "card-view", isOwnershipEnabled: h } }) }, children: (0, we.jsx)(qM, { icon: "GridView2", color: wM.Qs.Neutrals[s ? 400 : 900] }) }), (0, we.jsx)(PM.A, { flexItem: !0, orientation: "vertical", sx: { mx: .5, my: 1 } }), (0, we.jsx)(RM.A, { value: !1, onClick: () => { l(!1), ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_CHANGE_VIEW_CLICKED", extraParams: { action: "table-view", isOwnershipEnabled: h } }) }, disabled: !s, children: (0, we.jsx)(qM, { icon: "ListView", color: wM.Qs.Neutrals[s ? 900 : 400] }) })] })] })] }) }; var KM = n(176),
            ZM = n(98587),
            YM = n(68606),
            XM = n(69292),
            $M = n(75429),
            QM = n(66734); const JM = (0, QM.A)((0, we.jsx)("path", { d: "M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z" }), "ArrowDownward"); var eE = n(72876),
            tE = n(6803),
            nE = n(57056),
            rE = n(32400);

        function aE(e) { return (0, rE.Ay)("MuiTableSortLabel", e) } const oE = (0, nE.A)("MuiTableSortLabel", ["root", "active", "icon", "iconDirectionDesc", "iconDirectionAsc"]),
            iE = ["active", "children", "className", "direction", "hideSortIcon", "IconComponent"],
            lE = (0, LM.Ay)($M.A, { name: "MuiTableSortLabel", slot: "Root", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.root, n.active && t.active] } })((e => { let { theme: t } = e; return { cursor: "pointer", display: "inline-flex", justifyContent: "flex-start", flexDirection: "inherit", alignItems: "center", "&:focus": { color: (t.vars || t).palette.text.secondary }, "&:hover": { color: (t.vars || t).palette.text.secondary, ["& .".concat(oE.icon)]: { opacity: .5 } }, ["&.".concat(oE.active)]: { color: (t.vars || t).palette.text.primary, ["& .".concat(oE.icon)]: { opacity: 1, color: (t.vars || t).palette.text.secondary } } } })),
            sE = (0, LM.Ay)("span", { name: "MuiTableSortLabel", slot: "Icon", overridesResolver: (e, t) => { const { ownerState: n } = e; return [t.icon, t["iconDirection".concat((0, tE.A)(n.direction))]] } })((e => { let { theme: t, ownerState: n } = e; return (0, l.default)({ fontSize: 18, marginRight: 4, marginLeft: 4, opacity: 0, transition: t.transitions.create(["opacity", "transform"], { duration: t.transitions.duration.shorter }), userSelect: "none" }, "desc" === n.direction && { transform: "rotate(0deg)" }, "asc" === n.direction && { transform: "rotate(180deg)" }) })),
            cE = a.forwardRef((function(e, t) { const n = (0, eE.A)({ props: e, name: "MuiTableSortLabel" }),
                    { active: r = !1, children: a, className: o, direction: i = "asc", hideSortIcon: s = !1, IconComponent: c = JM } = n,
                    d = (0, ZM.default)(n, iE),
                    u = (0, l.default)({}, n, { active: r, direction: i, hideSortIcon: s, IconComponent: c }),
                    h = (e => { const { classes: t, direction: n, active: r } = e, a = { root: ["root", r && "active"], icon: ["icon", "iconDirection".concat((0, tE.A)(n))] }; return (0, YM.A)(a, aE, t) })(u); return (0, we.jsxs)(lE, (0, l.default)({ className: (0, XM.A)(h.root, o), component: "span", disableRipple: !0, ownerState: u, ref: t }, d, { children: [a, s && !r ? null : (0, we.jsx)(sE, { as: c, className: (0, XM.A)(h.icon), ownerState: u })] })) })); var dE = n(37344),
            uE = n(20965); const hE = { gap: (0, uE.A)(14) },
            mE = { gap: (0, uE.A)(12) },
            pE = { gap: (0, uE.A)(10) },
            fE = { gap: (0, uE.A)(8) },
            vE = { display: "inline-flex", gap: (0, uE.A)(12), justifyContent: "center", alignItems: "center", borderRadius: (0, uE.A)(3), textAlign: "center", textTransform: "none", transition: "all 150ms ease-in", boxShadow: "0 0 0 0 transparent !important", "& .MuiButton-startIcon": { margin: 0 }, "&:disabled": { opacity: .5, pointerEvents: "auto", cursor: "not-allowed" } },
            { white: gE } = wM.Ay,
            yE = e => { var t, n, r, a; let { ownerState: o } = e; const i = o.color || "white"; return { backgroundColor: wM.Ay[i].main, color: wM.Ay[i].contrastText, "&:hover": { backgroundColor: null === (t = wM.Ay[i]) || void 0 === t || null === (n = t.hover) || void 0 === n ? void 0 : n.contained }, "&:active": { backgroundColor: null === (r = wM.Ay[i]) || void 0 === r || null === (a = r.down) || void 0 === a ? void 0 : a.contained }, "&:disabled": { backgroundColor: wM.Ay[i].main, color: "".concat(wM.Ay[i].contrastText, " !important"), "&:hover": { backgroundColor: wM.Ay[i].main } } } },
            bE = { contained: yE, outlined: e => { var t, n, r, a, o, i; let { ownerState: l } = e; const s = l.color || wM.Gc.black; return { backgroundColor: gE.main, color: wM.Ay[s].main, border: "".concat((0, uE.A)(1), " solid ").concat(wM.Ay[s].main), "&:hover": { backgroundColor: null === (t = wM.Ay[s]) || void 0 === t || null === (n = t.hover) || void 0 === n ? void 0 : n.outlined, border: "".concat((0, uE.A)(1), " solid ").concat(wM.Ay[s].main) }, "&:active": { color: null === (r = wM.Ay[s]) || void 0 === r || null === (a = r.down) || void 0 === a ? void 0 : a.outlined, borderColor: null === (o = wM.Ay[s]) || void 0 === o || null === (i = o.down) || void 0 === i ? void 0 : i.outlined }, "&:disabled": { borderColor: "".concat(wM.Ay[s].main, " !important"), color: wM.Ay[s].main, "&:hover": { borderColor: "".concat(wM.Ay[s].main, " !important"), color: wM.Ay[s].main } } } }, text: e => { var t, n, r, a; let { ownerState: o } = e; const i = o.color || wM.Gc.black; return { color: wM.Ay[i].main, "&:hover": { color: null === (t = wM.Ay[i]) || void 0 === t || null === (n = t.hover) || void 0 === n ? void 0 : n.text }, "&:active": { color: null === (r = wM.Ay[i]) || void 0 === r || null === (a = r.down) || void 0 === a ? void 0 : a.text }, "&:disabled": { color: wM.Ay[i].main, "&:hover": { color: wM.Ay[i].main } } } }, iconOnly: e => ({ ...yE(e), padding: "".concat((0, uE.A)(3), " !important"), borderRadius: "".concat((0, uE.A)(4), " !important"), minWidth: "auto" }) },
            wE = bE,
            zE = { padding: "".concat((0, uE.A)(6.5), " ").concat((0, uE.A)(12), " !important"), borderRadius: "".concat((0, uE.A)(4), " !important"), ...MM.uT.labelLG, ...pE },
            xE = { padding: "".concat((0, uE.A)(9.5), " ").concat((0, uE.A)(24), " !important"), ...MM.uT.subheadingMD, ...mE },
            AE = { small: zE, large: { padding: "".concat((0, uE.A)(14), " ").concat((0, uE.A)(36), " !important"), ...MM.uT.h2, ...hE }, medium: xE, xsmall: { padding: "".concat((0, uE.A)(3), " ").concat((0, uE.A)(8), " !important"), borderRadius: "".concat((0, uE.A)(4), " !important"), ...MM.uT.subheadingXS, ...fE } },
            kE = { styleOverrides: { root: vE, contained: wE.contained, outlined: wE.outlined, text: wE.text, sizeSmall: AE.small, sizeMedium: AE.medium, sizeLarge: AE.large }, variants: [{ props: { size: "xsmall" }, style: AE.xsmall }, { props: { variant: "iconOnly" }, style: wE.iconOnly }] },
            SE = { root: {}, paper: { borderRadius: (0, uE.A)(4), border: "solid 1px ".concat(wM.Ay.white.main) } },
            ME = { MuiDialogTitle: { styleOverrides: { root: { padding: "".concat((0, uE.A)(12), " ").concat((0, uE.A)(20), " ").concat((0, uE.A)(12), " ").concat((0, uE.A)(20)), gap: (0, uE.A)(10), display: "flex", flexDirection: "row", justifyContent: "space-between", borderRadius: "".concat((0, uE.A)(4), " ").concat((0, uE.A)(4), " 0 0"), alignItems: "center", backgroundColor: wM.Qs.Neutrals[800], color: "".concat(wM.Ay.white.main, " !important"), ...MM.uT.h1, "& .MuiIconButton-root": { color: wM.Ay.white.main, padding: 0 } } } }, MuiDialog: { styleOverrides: SE }, MuiDialogActions: { styleOverrides: { root: { paddingBottom: (0, uE.A)(31), gap: "".concat((0, uE.A)(8), " !important"), display: "flex", flexDirection: "row", justifyContent: "flex-end", alignItems: "center", borderRadius: "0 0 ".concat((0, uE.A)(4), " ").concat((0, uE.A)(4)) } } } }; var EE = n(98022); const CE = e => { const t = e.ownerState.required ? { "& .MuiInputLabel-root span": { display: "none" } } : { "& .MuiInputLabel-root span": { display: "none" }, "& .MuiInputLabel-root:after": { marginLeft: (0, uE.A)(4), content: '"(Optional)"', color: wM.Qs.Neutrals[800], ...MM.uT.caption } }; return { gap: (0, uE.A)(6), "& .MuiInputBase-root": { color: wM.Qs.Neutrals[700], gap: (0, uE.A)(10), padding: "".concat((0, uE.A)(10), " ").concat((0, uE.A)(12)), ...MM.uT.bodySM, "&.Mui-disabled fieldset": { borderColor: wM.Qs.Neutrals[400] } }, "& .MuiInputLabel-root": { color: wM.Qs.Neutrals[800], position: "relative !important", transformOrigin: "50% 50% 0 !important", transform: "none !important", "-webkit-transform": "none !important", ...MM.uT.labelLG, "&.Mui-focused": { color: wM.Qs.Neutrals[800] }, "&.Mui-disabled": { color: wM.Qs.Neutrals[800] } }, "& fieldset": { top: "0 !important", border: "1px solid ".concat(wM.Qs.Neutrals[400]), borderRadius: (0, uE.A)(4) }, ...t } },
            TE = { outlined: { "&.Mui-disabled": { backgroundColor: wM.Qs.Neutrals[300], color: wM.Qs.Neutrals[600], "& :placeholder": { color: wM.Qs.Neutrals[400] } }, "& .MuiInputBase-input": { height: "auto", padding: "0", "& :placeholder": { color: wM.Qs.Neutrals[400], ...MM.uT.bodySM }, "&.MuiSelect-select": { minHeight: "auto" } }, "& .MuiInputLabel-root": { ...MM.uT.subheadingLG }, "& legend": { display: "none" } }, standard: {}, filled: {} },
            HE = { small: { gap: "{pxToRem(10)} !important", padding: "".concat((0, uE.A)(6), " ").concat((0, uE.A)(12), " !important"), "& .MuiInputBase-input": { ...MM.uT.caption } }, medium: {}, large: {} },
            LE = { root: CE, outlined: TE.outlined, outlinedSizeSmall: HE.small, standardSizeSmall: HE.small },
            IE = { styleOverrides: LE, variants: [{ props: { labelPlacement: "start" }, style: { flexDirection: "row", alignItems: "center", "& .MuiInputLabel-root": { overflow: "visible" } } }, { props: { labelPlacement: "top" }, style: { flexDirection: "column" } }, { props: { size: "large" }, style: { gap: (0, uE.A)(18), "& .MuiInputBase-root": { gap: (0, uE.A)(10), padding: "".concat((0, uE.A)(16)), ...MM.uT.bodyLG }, "& .MuiInputLabel-root": { ...MM.uT.h2 } } }, { props: { size: "small" }, style: { gap: (0, uE.A)(6), "& .MuiInputLabel-root": { ...MM.uT.subheadingXS }, "& .MuiInputBase-root": LE.standardSizeSmall } }] }; var jE = n(21337); const VE = { padding: "0 !important", paddingLeft: "".concat((0, uE.A)(8), " !important"), paddingRight: "".concat((0, uE.A)(8), " !important"), display: "flex", alignItems: "flex-start", gap: (0, uE.A)(6), "& .MuiSvgIcon-root": { fontSize: (0, uE.A)(14) }, "& .MuiCheckbox-root": { padding: 0 }, '&[aria-selected="true"]': { backgroundColor: "transparent !important", "&.Mui-focused": { backgroundColor: "".concat(wM.Qs.Neutrals[200], " !important") } }, "&.Mui-focused": { backgroundColor: "".concat(wM.Qs.Neutrals[200], " !important") } },
            OE = { "& .MuiSvgIcon-root": { fontSize: (0, uE.A)(12) } },
            RE = { padding: "".concat((0, uE.A)(8), " ").concat((0, uE.A)(16), " !important") },
            PE = { styleOverrides: { endAdornment: OE, input: { padding: "0 !important" }, listbox: { display: "flex", flexDirection: "column", gap: (0, uE.A)(4) }, noOptions: RE, option: VE, tag: { margin: "0 !important" }, tagSizeSmall: { ...MM.uT.caption }, paper: { ...EE.Qi.elevationLowest } } },
            DE = { "& .MuiChip-label": { padding: 0, display: "flex", alignItems: "center", flexDirection: "row" } },
            FE = { height: (0, uE.A)(14), borderRadius: (0, uE.A)(8), padding: "".concat((0, uE.A)(2), " ").concat((0, uE.A)(6)), border: "".concat((0, uE.A)(1), " solid ").concat(wM.Qs.Neutrals[900]), backgroundColor: wM.Qs.Neutrals[200], ...MM.uT.labelMD, color: wM.Qs.Neutrals[900], "& .MuiChip-label": { gap: (0, uE.A)(5) }, "& .MuiChip-deleteIcon": { marginLeft: (0, uE.A)(5), marginRight: (0, uE.A)(0), fontSize: (0, uE.A)(12), color: wM.Qs.Neutrals[900] } },
            NE = { height: (0, uE.A)(16), borderRadius: (0, uE.A)(99), padding: "".concat((0, uE.A)(2), " ").concat((0, uE.A)(10)), ...MM.uT.subheadingSM, display: "flex", gap: (0, uE.A)(2), "& .MuiChip-icon": { marginLeft: (0, uE.A)(0), marginRight: (0, uE.A)(0), fontSize: (0, uE.A)(8) }, "& .MuiChip-deleteIcon": { marginLeft: (0, uE.A)(5), marginRight: (0, uE.A)(0), fontSize: (0, uE.A)(12) } },
            _E = { height: (0, uE.A)(24), borderRadius: (0, uE.A)(99), padding: "".concat((0, uE.A)(4), " ").concat((0, uE.A)(8)), ...MM.uT.bodySM, display: "flex", gap: (0, uE.A)(8), "& .MuiChip-icon": { marginLeft: (0, uE.A)(0), marginRight: (0, uE.A)(0), fontSize: (0, uE.A)(8) } },
            BE = { xsmall: { height: (0, uE.A)(14), borderRadius: (0, uE.A)(99), padding: "".concat((0, uE.A)(2), " ").concat((0, uE.A)(6)), border: "".concat((0, uE.A)(1), " solid ").concat(wM.Qs.Neutrals[300]), backgroundColor: wM.Qs.Neutrals[200], ...MM.uT.labelSM, color: wM.Qs.Neutrals[600], "& .MuiChip-label": { gap: (0, uE.A)(3) } }, small: FE, medium: NE, large: _E, xlarge: {} },
            WE = { styleOverrides: { root: DE, sizeSmall: BE.small, sizeMedium: BE.medium }, variants: [{ props: { size: "xsmall" }, style: BE.xsmall }, { props: { size: "large" }, style: BE.large }] },
            UE = { styleOverrides: { root: { "&.MuiMenu-list": { padding: "".concat((0, uE.A)(16), " 0 !important"), display: "flex", flexDirection: "column", justifyContent: "center" }, "& .MuiButton-text": { paddingLeft: "0 !important", paddingRight: "0 !important", justifyContent: "flex-start" }, ".MuiDivider-root": { backgroundColor: wM.Qs.Neutrals[200], borderColor: wM.Qs.Neutrals[200], margin: "0 !important" }, hr: { backgroundColor: wM.Qs.Neutrals[200], borderColor: wM.Qs.Neutrals[200], margin: 0, marginTop: 0, marginBottom: 0 } } } },
            qE = { styleOverrides: { root: { ...MM.uT.bodySM, color: wM.Qs.Neutrals[600], "svg.menu-item-checked-icon": { display: "none" }, form: { "svg.menu-item-checked-icon": { display: "block !important" } }, "&:hover": { color: wM.Ay.primary.main }, "&.Mui-selected": { "svg.menu-item-checked-icon": { display: "block !important" }, backgroundColor: wM.Ay.white.main, color: wM.Ay.primary.main, "&:hover": { color: wM.Ay.primary.main } }, transition: "all 0.3s ease-in-out", borderColor: wM.Qs.Neutrals[200], paddingBottom: (0, uE.A)(6), paddingTop: (0, uE.A)(6) } } },
            GE = { styleOverrides: { root: { width: (0, uE.A)(40), height: (0, uE.A)(20), display: "flex", alignItems: "center", padding: 0, "& .MuiSwitch-switchBase": { padding: 0, paddingLeft: (0, uE.A)(4), top: "50%", transform: "translateY(-50%)", "&.Mui-checked": { transform: "translate(18px, -50%) !important", paddingRight: (0, uE.A)(4), color: "".concat(wM.Qs.Neutrals[0], " !important"), "& + .MuiSwitch-track": { opacity: 1 } }, "&.Mui-disabled": { cursor: "not-allowed", pointerEvents: "unset", "& + .MuiSwitch-track": { opacity: .5 } } }, "& .MuiSwitch-track": { opacity: 1, width: (0, uE.A)(40), height: (0, uE.A)(20), borderRadius: (0, uE.A)(24), backgroundColor: wM.Qs.Neutrals[500] }, "& .MuiSwitch-thumb": { width: (0, uE.A)(14), height: (0, uE.A)(14), borderRadius: (0, uE.A)(24), boxShadow: "none" }, "&.MuiSwitch-sizeSmall": { scale: "0.75" } } } }; var KE, ZE = n(5613); const YE = (0, dE.A)({ components: { MuiAccordionSummary: { styleOverrides: { root: { padding: "36px 32px 28px 32px !important", minHeight: "auto !important" }, content: { margin: "0 !important" } } }, MuiAccordionDetails: { styleOverrides: { root: { padding: "0px 32px 16px 32px !important" } } }, MuiAutocomplete: { ...PE }, MuiButton: { defaultProps: { disableRipple: !0 }, ...kE }, MuiButtonGroup: { defaultProps: { disableRipple: !0 }, styleOverrides: { firstButton: { borderTopRightRadius: "0 !important", borderBottomRightRadius: "0 !important", padding: "".concat((0, uE.A)(6), " ").concat((0, uE.A)(12), " !important"), ...MM.uT.subheadingXS }, lastButton: { borderTopLeftRadius: "0 !important", borderBottomLeftRadius: "0 !important", padding: "".concat((0, uE.A)(6), " ").concat((0, uE.A)(12), " !important"), ...MM.uT.subheadingXS }, middleButton: { borderRadius: "0 !important", padding: "".concat((0, uE.A)(6), " ").concat((0, uE.A)(12), " !important"), ...MM.uT.subheadingXS } } }, MuiChip: { ...WE }, MuiFormHelperText: { styleOverrides: { root: { margin: 0 } } }, MuiIconButton: { defaultProps: { disableRipple: !0 } }, MuiList: { ...UE }, MuiMenu: { styleOverrides: { paper: { ...EE.Qi[EE.vR.low] } } }, MuiMenuItem: { defaultProps: { disableRipple: !0 }, ...qE }, MuiOutlinedInput: { styleOverrides: { root: IE.styleOverrides.outlined, sizeSmall: IE.styleOverrides.outlinedSizeSmall } }, MuiPaper: { defaultProps: { variant: EE.vR.none }, variants: EE.$F }, MuiPopover: { styleOverrides: { paper: { ...EE.Qi[EE.vR.low], maxHeight: "".concat((0, uE.A)(300), " !important") } } }, MuiSelect: { defaultProps: { IconComponent: jE.A }, styleOverrides: { select: { "&:focus": { backgroundColor: "transparent" } } } }, MuiSwitch: { defaultProps: { disableRipple: !0 }, ...GE }, MuiTextField: { defaultProps: { variant: "outlined", InputLabelProps: { disableAnimation: !0, shrink: !0 }, labelPlacement: "top" }, variants: IE.variants, styleOverrides: { root: null === IE || void 0 === IE || null === (KE = IE.styleOverrides) || void 0 === KE ? void 0 : KE.root } }, MuiTooltip: { styleOverrides: ZE.Ay.styleOverrides }, MuiTypography: { defaultProps: { fontFamily: '"Inter", "Helvetica", "Arial", sans-serif !important' }, ...MM.Ay }, ...ME }, palette: { ...wM.Ay }, typography: { allVariants: { color: wM.Ay.text.primary }, fontFamily: '"Inter", "Helvetica", "Arial", sans-serif' } }); var XE = n(17392),
            $E = n(688); const QE = ["view", "edit", "delete", "charts", "talentPool", "ownershipCharts", "infoIcon", "chartsIcon"],
            JE = e => { let { org: t, color: n, visible: r, customMenuItems: o = [] } = e; const [i, l] = (0, a.useState)(null), [s, c] = (0, a.useState)(null), { isMinAdmin: d, gotoCharts: u, gotoTalentPool: h, isOwnershipEnabled: m, onOrgSelect: p } = (0, EM.A)(), { openOrgEntityDialog: f, openOrgMetaDialog: v, openOrgEntityInfoDialog: g, openDeleteOrgDialog: y } = (0, TM.A)(), b = r || {}, w = QE.reduce(((e, t) => (e[t] = function(e) { return null !== e && void 0 !== e }(b[t]) ? b[t] : !["infoIcon", "chartsIcon"].includes(t), e)), {}), z = e => { e && (null === e || void 0 === e || e.stopPropagation()), e && (null === e || void 0 === e || e.preventDefault()), c(null), l(null) }, x = e => t => { null === t || void 0 === t || t.stopPropagation(), g({ org: e, showDelete: d, showEdit: d }), ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_ORG_ACTION_CLICKED", extraParams: { action: "view-org", orgName: null === e || void 0 === e ? void 0 : e.name, isOwnershipEnabled: m } }) }; return (0, we.jsxs)(we.Fragment, { children: [(null === w || void 0 === w ? void 0 : w.infoIcon) && (0, we.jsx)(XE.A, { color: n || "default", size: "medium", onClick: x(t), children: (0, we.jsx)(fM.A, { children: (0, we.jsx)(Ee.gF, { size: "sm", icon: "Info" }) }) }), (null === w || void 0 === w ? void 0 : w.chartsIcon) && (0, we.jsx)(XE.A, { size: "medium", onClick: u(t), children: (0, we.jsx)("img", { src: DM.XU[0].icon, alt: "".concat(DM.XU[0].name, " charts") }) }), (0, we.jsx)(XE.A, { color: n || "default", onClick: (k = (null === t || void 0 === t ? void 0 : t.id) || "", e => { var t, n;
                            e && (null === (t = e) || void 0 === t || t.stopPropagation()), c(null === (n = e) || void 0 === n ? void 0 : n.currentTarget), l(k) }), children: (0, we.jsx)(Ee.gF, { icon: "EllipsesV", size: "sm" }) }), (0, we.jsxs)($E.A, { id: "".concat(t.id, "-context-menu"), anchorEl: s, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, open: i === t.id && null !== s, onClose: e => z(e), children: [(null === w || void 0 === w ? void 0 : w.view) && (0, we.jsx)(OM.A, { onClick: e => { x(t)(null), z(e) }, children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "View" }) }), (null === w || void 0 === w ? void 0 : w.edit) && d && (0, we.jsx)(OM.A, { onClick: (A = t, e => { e.stopPropagation(), z(e), m ? f({ mode: "edit", orgOnlyMode: !0, orgToEdit: A }) : v({ mode: "edit", orgToEdit: A, redirectToOrganization: !1 }), ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_ORG_ACTION_CLICKED", extraParams: { action: "edit-org", orgName: null === A || void 0 === A ? void 0 : A.name, isOwnershipEnabled: m } }) }), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Edit" }) }), (o || []).map((e => (0, we.jsx)(OM.A, { onClick: n => { n.stopPropagation(), e.onClick(t), c(null) }, children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: e.label }) }))), (null === w || void 0 === w ? void 0 : w.delete) && d && (0, we.jsx)(OM.A, { onClick: (e => t => { t.stopPropagation(), z(t), ve.A.trackEvent({ eventName: "ORGS_DASHBOARD_ORG_ACTION_CLICKED", extraParams: { action: "delete-org", orgName: null === e || void 0 === e ? void 0 : e.name, isOwnershipEnabled: m } }), y({ org: e, navigateOnSuccess: !1 }) })(t), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Delete" }) }), (null === w || void 0 === w ? void 0 : w.charts) && (0, we.jsx)(OM.A, { onClick: u(t), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Go to Org Charts" }) }), (null === w || void 0 === w ? void 0 : w.talentPool) && (0, we.jsx)(OM.A, { onClick: h(t), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Go to Talent Pool" }) }), (null === w || void 0 === w ? void 0 : w.ownershipCharts) && m && (0, we.jsx)(OM.A, { onClick: p(t), children: (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, children: "Go to Ownership Charts" }) })] })] }); var A, k }; var eC; const tC = (0, LM.Ay)(KM.XI)(eC || (eC = (0, J.A)(["\n  .ReactVirtualized__Table__row,\n  .ReactVirtualized__Table__headerRow {\n    display: flex;\n    padding-left: 16px;\n    border-bottom: 1px solid ", ";\n    background-color: ", ";\n    align-items: center;\n    cursor: pointer;\n\n    :hover {\n      background-color: ", ";\n    }\n  }\n"])), wM.Qs.Neutrals[200], wM.Qs.Neutrals[0], wM.Qs.Neutrals[200]),
            nC = e => { let { rows: t, headCells: n, createSortHandler: r, order: a, orderBy: o } = e; const { onOrgSelect: i } = (0, EM.A)(); return (0, we.jsx)(KM.t$, { children: e => { let { height: l, width: s } = e; const c = s <= YE.breakpoints.values.lg; return (0, we.jsx)(tC, { headerHeight: 50, containerStyle: { maxWidth: s, width: s }, columnCount: (null === n || void 0 === n ? void 0 : n.length) || 0, rowGetter: e => { let { index: n } = e; return t[n] }, rowCount: (null === t || void 0 === t ? void 0 : t.length) || 0, overscanRowCount: 20, rowHeight: 50, width: s || 0, height: l || 0, onRowClick: e => { let { event: t, rowData: n } = e; return i(n)(t) }, children: n.map(((e, t) => (0, we.jsx)(KM.VP, { dataKey: e.name, width: s * (t < 2 ? .2 : .1), headerRenderer: e => { let { dataKey: t } = e; const i = n.find((e => e.name === t)); return i ? (0, we.jsx)(ln.A, { variant: MM.Eq.subheadingSM, color: wM.Qs.Neutrals[700], children: (0, we.jsx)(cE, { onClick: r(t), direction: a, active: t === o, children: i.label }) }) : (0, we.jsx)(we.Fragment, {}) }, cellRenderer: e => { let { dataKey: t, rowData: n, cellData: r } = e, a = r; if (a && DM.o5.includes(t)) { const e = _r.c9.fromISO(a);
                                        a = e.isValid ? e.toLocaleString(_r.c9.DATE_SHORT) : "" } var o, i;
                                    (a || "").length > 35 && (a = (null === (o = a) || void 0 === o ? void 0 : o.substring(0, 35)) + "..."); if ("controls" === t) return (0, we.jsx)(fM.A, { display: "flex", alignItems: "center", gap: 1, children: (0, we.jsx)(JE, { org: n, visible: { infoIcon: !c, chartsIcon: !c }, color: "secondary" }) });
                                    ["typeOfReportingPerson", "entityType"].includes(t) && (a = (null === (i = a) || void 0 === i ? void 0 : i.join(", ")) || "-"); return (0, we.jsx)(ln.A, { variant: MM.Eq.bodySM, color: wM.Qs.Neutrals[700], children: a || "-" }) } }, "column-".concat(e.name)))) }) } }) }; var rC = n(99713),
            aC = n(77739),
            oC = n(60587); const iC = n.p + "static/media/person.e42e542e4b82c03364dfd5dd29c37064.svg"; var lC, sC, cC = n(79644),
            dC = n(60350); const uC = (0, cC.A)(fM.A)(lC || (lC = (0, J.A)(["\n  display: flex;\n  flex-direction: column;\n  border-radius: ", ";\n  border: ", ";\n  align-items: flex-start;\n  padding: ", " ", ";\n  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\n  background-color: ", ";\n\n  &:hover {\n    border: 1px ", " solid;\n    transition: border 0.3s ease-in-out;\n  }\n"])), (0, uE.A)(4), "1px ".concat(wM.Qs.Neutrals[300], " solid"), (0, uE.A)(17), (0, uE.A)(15), wM.Qs.Neutrals[0], wM.Qs.Violet[500]),
            hC = (0, cC.A)(XE.A)(sC || (sC = (0, J.A)(["\n  border-radius: ", ";\n"])), (0, uE.A)(4)),
            mC = e => { let { rows: t } = e; const { isOwnershipEnabled: n, gotoTalentPool: r, onOrgSelect: a, gotoCharts: o } = (0, EM.A)(), i = function(e, n) { let r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; const a = 24,
                        o = e - (r ? a : 0),
                        l = DM.SY + 48,
                        s = Math.max(Math.min(5, Math.ceil(o / (DM.Ou + a))), 2),
                        c = Math.ceil(t.length / s),
                        d = Math.max(o / s, 25),
                        u = d - a,
                        h = a / (s - 1),
                        m = Math.min(10, Math.ceil(20 / s)),
                        p = Math.floor(n / l) * s,
                        f = t.length > p; return !r && f ? i(e, n, !0) : { rowHeight: l, containerWidth: o, columnCount: s, columnWidth: d, distributedMargin: h, rowCount: c, overScanRows: m, cardWidth: u } }; return (0, we.jsx)(KM.t$, { children: e => { let { height: l, width: s } = e; if (!l || !s) return (0, we.jsx)(we.Fragment, {}); const c = i(s, l); return (0, we.jsx)(KM.xA, { containerStyle: { maxWidth: c.containerWidth, width: c.containerWidth }, columnCount: c.columnCount || 0, columnWidth: c.columnWidth || 0, rowCount: c.rowCount || 0, rowHeight: c.rowHeight || 0, overscanRowCount: c.overScanRows, width: s || 0, height: l || 0, cellRenderer: e => { var i; let { columnIndex: l, key: s, rowIndex: d, style: u } = e; const h = d * c.columnCount + l,
                                    m = t[h]; if (!m) return (0, we.jsx)(we.Fragment, {}); let p = ""; if ("updatedAt" in m && m.updatedAt) { const e = _r.c9.fromISO(m.updatedAt);
                                    p = e.isValid ? e.toLocaleString(_r.c9.DATE_SHORT) : "" } return (0, we.jsx)(fM.A, { style: u, children: (0, we.jsxs)(uC, { gap: 1, width: c.cardWidth, ml: "".concat(c.distributedMargin * l, "px"), children: [(0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", width: "100%", alignItems: "center", children: [(0, we.jsx)(aC.A, { title: "Go to ".concat(m.name), children: (0, we.jsx)(hC, { onClick: o(m), children: (0, we.jsxs)(fM.A, { display: "flex", gap: 1, alignItems: "center", flexWrap: "wrap", children: [m.logo ? (0, we.jsx)("img", { src: m.logo, height: 25, width: 40, style: { objectFit: "contain" } }) : (0, we.jsx)(oC.A, { sx: { width: 25, height: 25 }, "aria-label": "recipe", children: (0, we.jsx)(Ee.gF, { icon: "Building" }) }), (0, we.jsx)(ln.A, { variant: MM.Eq.subheadingSM, children: (0, Tn.RP)(m.name, 20) })] }) }) }), (0, we.jsx)(JE, { org: m, visible: { infoIcon: !1, chartsIcon: !1 }, color: null })] }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: n && m.hasRelations ? "space-between" : "flex-end", width: "100%", alignItems: "center", flex: "1", children: [n && m.hasRelations && (0, we.jsx)(aC.A, { title: "Go to Ownership Chart", children: (0, we.jsx)(hC, { onClick: a(m), children: (0, we.jsxs)(fM.A, { display: "flex", gap: 1, alignItems: "flex-end", children: [(0, we.jsx)(Ee.gF, { icon: "DrillDown", size: "lg" }), (0, we.jsxs)(fM.A, { children: [(0, we.jsxs)(fM.A, { display: "flex", alignItems: "center", gap: 1, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.subheadingLG, children: (null === m || void 0 === m ? void 0 : m.owners) || 0 }), (0, we.jsx)(ln.A, { color: wM.Qs.Neutrals[600], variant: MM.Eq.caption, children: (0, tE.A)("".concat(1 === (null === m || void 0 === m ? void 0 : m.owners) ? DM.GX : DM.Z_)) })] }), (0, we.jsxs)(fM.A, { display: "flex", alignItems: "center", gap: 1, children: [(0, we.jsx)(ln.A, { variant: MM.Eq.subheadingLG, children: (null === m || void 0 === m ? void 0 : m.subsidiaries) || 0 }), (0, we.jsx)(ln.A, { color: wM.Qs.Neutrals[600], variant: MM.Eq.caption, children: (0, tE.A)("".concat(1 === (null === m || void 0 === m ? void 0 : m.subsidiaries) ? DM.su : DM.a1)) })] })] })] }) }) }), (0, we.jsx)(aC.A, { title: "Go to Talent Pool", children: (0, we.jsx)(hC, { onClick: r(m), children: (0, we.jsxs)(fM.A, { display: "flex", gap: 1, children: [(0, we.jsxs)(fM.A, { display: "flex", flexDirection: "column", alignItems: "flex-end", children: [(0, we.jsx)(ln.A, { variant: MM.Eq.subheadingXL, children: (null === m || void 0 === m || null === (i = m.peopleCount) || void 0 === i ? void 0 : i.toString()) || "0" }), (0, we.jsx)(ln.A, { color: wM.Qs.Neutrals[600], variant: MM.Eq.caption, children: "In talent pool" })] }), (0, we.jsx)("img", { color: wM.Qs.Neutrals[500], src: iC, alt: "People in talent pool" })] }) }) })] }), (0, we.jsxs)(fM.A, { display: "flex", justifyContent: "space-between", width: "100%", alignItems: "center", children: [(0, we.jsx)(dC.K, { org: m }), p && (0, we.jsxs)(ln.A, { color: wM.Qs.Neutrals[800], variant: MM.Eq.caption, children: ["Last updated ", p] })] })] }) }, s) } }) } }) },
