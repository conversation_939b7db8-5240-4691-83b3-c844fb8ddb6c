                                            break;
                                        default:
                                            ne = !1 } break;
                                case "sorting":
                                case "conditionalformatting":
                                    switch (a[3]) {
                                        case "range":
                                        case "type":
                                        case "min":
                                        case "max":
                                        case "sort":
                                        case "descending":
                                        case "order":
                                        case "casesensitive":
                                        case "value":
                                        case "errorstyle":
                                        case "errormessage":
                                        case "errortitle":
                                        case "cellrangelist":
                                        case "inputmessage":
                                        case "inputtitle":
                                        case "combohide":
                                        case "inputhide":
                                        case "condition":
                                        case "qualifier":
                                        case "useblank":
                                        case "value1":
                                        case "value2":
                                        case "format":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "mapinfo":
                                case "schema":
                                case "data":
                                    switch (a[3]) {
                                        case "map":
                                        case "entry":
                                        case "range":
                                        case "xpath":
                                        case "field":
                                        case "xsdtype":
                                        case "filteron":
                                        case "aggregate":
                                        case "elementtype":
                                        case "attributetype":
                                        case "schema":
                                        case "element":
                                        case "complextype":
                                        case "datatype":
                                        case "all":
                                        case "attribute":
                                        case "extends":
                                        case "row":
                                            break;
                                        default:
                                            ne = !1 } break;
                                case "smarttags":
                                    break;
                                default:
                                    ne = !1 } if (ne) break; if (a[3].match(/!\[CDATA/)) break; if (!c[c.length - 1][1]) throw "Unrecognized tag: " + a[3] + "|" + c.join("|"); if ("customdocumentproperties" === c[c.length - 1][0]) { if ("/>" === a[0].slice(-2)) break; "/" === a[1] ? Sl(I, K, V, r.slice(j, a.index)) : (V = a, j = a.index + a[0].length); break } if (n.WTF) throw "Unrecognized tag: " + a[3] + "|" + c.join("|") }
                    var ae = {}; return n.bookSheets || n.bookProps || (ae.Sheets = h), ae.SheetNames = f, ae.Workbook = q, ae.SSF = _e(N), ae.Props = L, ae.Custprops = I, ae }

                function Ll(e, t) { switch (As(t = t || {}), t.type || "base64") {
                        case "base64":
                            return Hl(w(e), t);
                        case "binary":
                        case "buffer":
                        case "file":
                            return Hl(e, t);
                        case "array":
                            return Hl(M(e), t) } }

                function Il(e) { var t = {},
                        n = e.content; if (n.l = 28, t.AnsiUserType = n.read_shift(0, "lpstr-ansi"), t.AnsiClipboardFormat = function(e) { return rr(e, 1) }(n), n.length - n.l <= 4) return t; var r = n.read_shift(4); return 0 == r || r > 40 ? t : (n.l -= 4, t.Reserved1 = n.read_shift(0, "lpstr-ansi"), n.length - n.l <= 4 || 1907505652 !== (r = n.read_shift(4)) ? t : (t.UnicodeClipboardFormat = function(e) { return rr(e, 2) }(n), 0 == (r = n.read_shift(4)) || r > 40 ? t : (n.l -= 4, void(t.Reserved2 = n.read_shift(0, "lpwstr"))))) } var jl = [60, 1084, 2066, 2165, 2175];

                function Vl(e, t, n, r, a) { var o = r,
                        i = [],
                        l = n.slice(n.l, n.l + o); if (a && a.enc && a.enc.insitu && l.length > 0) switch (e) {
                        case 9:
                        case 521:
                        case 1033:
                        case 2057:
                        case 47:
                        case 405:
                        case 225:
                        case 406:
                        case 312:
                        case 404:
                        case 10:
                        case 133:
                            break;
                        default:
                            a.enc.insitu(l) } i.push(l), n.l += o; for (var s = cn(n, n.l), c = _l[s], d = 0; null != c && jl.indexOf(s) > -1;) o = cn(n, n.l + 2), d = n.l + 4, 2066 == s ? d += 4 : 2165 != s && 2175 != s || (d += 12), l = n.slice(d, n.l + 4 + o), i.push(l), n.l += 4 + o, c = _l[s = cn(n, n.l)]; var u = C(i);
                    wn(u, 0); var h = 0;
                    u.lens = []; for (var m = 0; m < i.length; ++m) u.lens.push(h), h += i[m].length; if (u.length < r) throw "XLS Record 0x" + e.toString(16) + " Truncated: " + u.length + " < " + r; return t.f(u, u.length, a) }

                function Ol(e, t, n) { if ("z" !== e.t && e.XF) { var r = 0; try { r = e.z || e.XF.numFmtId || 0, t.cellNF && (e.z = N[r]) } catch (o) { if (t.WTF) throw o } if (!t || !1 !== t.cellText) try { "e" === e.t ? e.w = e.w || vr[e.v] : 0 === r || "General" == r ? "n" === e.t ? (0 | e.v) === e.v ? e.w = e.v.toString(10) : e.w = X(e.v) : e.w = Q(e.v) : e.w = be(r, e.v, { date1904: !!n, dateNF: t && t.dateNF }) } catch (o) { if (t.WTF) throw o }
                        if (t.cellDates && r && "n" == e.t && fe(N[r] || String(r))) { var a = U(e.v);
                            a && (e.t = "d", e.v = new Date(a.y, a.m - 1, a.d, a.H, a.M, a.S, a.u)) } } }

                function Rl(e, t, n) { return { v: e, ixfe: t, t: n } }

                function Pl(e, t) { var n = { opts: {} },
                        r = {};
                    null != g && null == t.dense && (t.dense = g); var a, o, i, l, s, d, u, h, m = t.dense ? [] : {},
                        p = {},
                        f = {},
                        v = null,
                        y = [],
                        b = "",
                        w = {},
                        z = "",
                        x = {},
                        A = [],
                        k = [],
                        S = [],
                        M = { Sheets: [], WBProps: { date1904: !1 }, Views: [{}] },
                        E = {},
                        C = function(e) { return e < 8 ? fr[e] : e < 64 && S[e - 8] || fr[e] },
                        T = function(e, t, n) { if (!(F > 1) && !(n.sheetRows && e.r >= n.sheetRows)) { if (n.cellStyles && t.XF && t.XF.data && function(e, t, n) { var r, a = t.XF.data;
                                        a && a.patternType && n && n.cellStyles && (t.s = {}, t.s.patternType = a.patternType, (r = ro(C(a.icvFore))) && (t.s.fgColor = { rgb: r }), (r = ro(C(a.icvBack))) && (t.s.bgColor = { rgb: r })) }(0, t, n), delete t.ixfe, delete t.XF, a = e, z = Vn(e), f && f.s && f.e || (f = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }), e.r < f.s.r && (f.s.r = e.r), e.c < f.s.c && (f.s.c = e.c), e.r + 1 > f.e.r && (f.e.r = e.r + 1), e.c + 1 > f.e.c && (f.e.c = e.c + 1), n.cellFormula && t.f)
                                    for (var r = 0; r < A.length; ++r)
                                        if (!(A[r][0].s.c > e.c || A[r][0].s.r > e.r) && !(A[r][0].e.c < e.c || A[r][0].e.r < e.r)) { t.F = Rn(A[r][0]), A[r][0].s.c == e.c && A[r][0].s.r == e.r || delete t.f, t.f && (t.f = "" + gi(A[r][1], 0, e, R, H)); break } n.dense ? (m[e.r] || (m[e.r] = []), m[e.r][e.c] = t) : m[z] = t } },
                        H = { enc: !1, sbcch: 0, snames: [], sharedf: x, arrayf: A, rrtabid: [], lastuser: "", biff: 8, codepage: 0, winlocked: 0, cellStyles: !!t && !!t.cellStyles, WTF: !!t && !!t.wtf };
                    t.password && (H.password = t.password); var L = [],
                        I = [],
                        j = [],
                        V = [],
                        O = !1,
                        R = [];
                    R.SheetNames = H.snames, R.sharedf = H.sharedf, R.arrayf = H.arrayf, R.names = [], R.XTI = []; var P, D = 0,
                        F = 0,
                        _ = 0,
                        B = [],
                        W = [];
                    H.codepage = 1200, c(1200); for (var U = !1; e.l < e.length - 1;) { var q = e.l,
                            G = e.read_shift(2); if (0 === G && 10 === D) break; var K = e.l === e.length ? 0 : e.read_shift(2),
                            Z = _l[G]; if (Z && Z.f) { if (t.bookSheets && 133 === D && 133 !== G) break; if (D = G, 2 === Z.r || 12 == Z.r) { var Y = e.read_shift(2); if (K -= 2, !H.enc && Y !== G && ((255 & Y) << 8 | Y >> 8) !== G) throw new Error("rt mismatch: " + Y + "!=" + G);
                                12 == Z.r && (e.l += 10, K -= 10) } var X = {}; if (X = 10 === G ? Z.f(e, K, H) : Vl(G, Z, e, K, H), 0 == F && -1 === [9, 521, 1033, 2057].indexOf(D)) continue; switch (G) {
                                case 34:
                                    n.opts.Date1904 = M.WBProps.date1904 = X; break;
                                case 134:
                                    n.opts.WriteProtect = !0; break;
                                case 47:
                                    if (H.enc || (e.l = 0), H.enc = X, !t.password) throw new Error("File is password-protected"); if (null == X.valid) throw new Error("Encryption scheme unsupported"); if (!X.valid) throw new Error("Password is incorrect"); break;
                                case 92:
                                    H.lastuser = X; break;
                                case 66:
                                    var $ = Number(X); switch ($) {
                                        case 21010:
                                            $ = 1200; break;
                                        case 32768:
                                            $ = 1e4; break;
                                        case 32769:
                                            $ = 1252 } c(H.codepage = $), U = !0; break;
                                case 317:
                                    H.rrtabid = X; break;
                                case 25:
                                    H.winlocked = X; break;
                                case 439:
                                    n.opts.RefreshAll = X; break;
                                case 12:
                                    n.opts.CalcCount = X; break;
                                case 16:
                                    n.opts.CalcDelta = X; break;
                                case 17:
                                    n.opts.CalcIter = X; break;
                                case 13:
                                    n.opts.CalcMode = X; break;
                                case 14:
                                    n.opts.CalcPrecision = X; break;
                                case 95:
                                    n.opts.CalcSaveRecalc = X; break;
                                case 15:
                                    H.CalcRefMode = X; break;
                                case 2211:
                                    n.opts.FullCalc = X; break;
                                case 129:
                                    X.fDialog && (m["!type"] = "dialog"), X.fBelow || ((m["!outline"] || (m["!outline"] = {})).above = !0), X.fRight || ((m["!outline"] || (m["!outline"] = {})).left = !0); break;
                                case 224:
                                    k.push(X); break;
                                case 430:
                                    R.push([X]), R[R.length - 1].XTI = []; break;
                                case 35:
                                case 547:
                                    R[R.length - 1].push(X); break;
                                case 24:
                                case 536:
                                    P = { Name: X.Name, Ref: gi(X.rgce, 0, null, R, H) }, X.itab > 0 && (P.Sheet = X.itab - 1), R.names.push(P), R[0] || (R[0] = [], R[0].XTI = []), R[R.length - 1].push(X), "_xlnm._FilterDatabase" == X.Name && X.itab > 0 && X.rgce && X.rgce[0] && X.rgce[0][0] && "PtgArea3d" == X.rgce[0][0][0] && (W[X.itab - 1] = { ref: Rn(X.rgce[0][0][1][2]) }); break;
                                case 22:
                                    H.ExternCount = X; break;
                                case 23:
                                    0 == R.length && (R[0] = [], R[0].XTI = []), R[R.length - 1].XTI = R[R.length - 1].XTI.concat(X), R.XTI = R.XTI.concat(X); break;
                                case 2196:
                                    if (H.biff < 8) break;
                                    null != P && (P.Comment = X[1]); break;
                                case 18:
                                    m["!protect"] = X; break;
                                case 19:
                                    0 !== X && H.WTF && console.error("Password verifier: " + X); break;
                                case 133:
                                    p[X.pos] = X, H.snames.push(X.name); break;
                                case 10:
                                    if (--F) break; if (f.e) { if (f.e.r > 0 && f.e.c > 0) { if (f.e.r--, f.e.c--, m["!ref"] = Rn(f), t.sheetRows && t.sheetRows <= f.e.r) { var Q = f.e.r;
                                                f.e.r = t.sheetRows - 1, m["!fullref"] = m["!ref"], m["!ref"] = Rn(f), f.e.r = Q } f.e.r++, f.e.c++ } L.length > 0 && (m["!merges"] = L), I.length > 0 && (m["!objects"] = I), j.length > 0 && (m["!cols"] = j), V.length > 0 && (m["!rows"] = V), M.Sheets.push(E) } "" === b ? w = m : r[b] = m, m = t.dense ? [] : {}; break;
                                case 9:
                                case 521:
                                case 1033:
                                case 2057:
                                    if (8 === H.biff && (H.biff = { 9: 2, 521: 3, 1033: 4 } [G] || { 512: 2, 768: 3, 1024: 4, 1280: 5, 1536: 8, 2: 2, 7: 2 } [X.BIFFVer] || 8), H.biffguess = 0 == X.BIFFVer, 0 == X.BIFFVer && 4096 == X.dt && (H.biff = 5, U = !0, c(H.codepage = 28591)), 8 == H.biff && 0 == X.BIFFVer && 16 == X.dt && (H.biff = 2), F++) break; if (m = t.dense ? [] : {}, H.biff < 8 && !U && (U = !0, c(H.codepage = t.codepage || 1252)), H.biff < 5 || 0 == X.BIFFVer && 4096 == X.dt) { "" === b && (b = "Sheet1"), f = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }; var J = { pos: e.l - K, name: b };
                                        p[J.pos] = J, H.snames.push(b) } else b = (p[q] || { name: "" }).name;
                                    32 == X.dt && (m["!type"] = "chart"), 64 == X.dt && (m["!type"] = "macro"), L = [], I = [], H.arrayf = A = [], j = [], V = [], O = !1, E = { Hidden: (p[q] || { hs: 0 }).hs, name: b }; break;
                                case 515:
                                case 3:
                                case 2:
                                    "chart" == m["!type"] && (t.dense ? (m[X.r] || [])[X.c] : m[Vn({ c: X.c, r: X.r })]) && ++X.c, d = { ixfe: X.ixfe, XF: k[X.ixfe] || {}, v: X.val, t: "n" }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t); break;
                                case 5:
                                case 517:
                                    d = { ixfe: X.ixfe, XF: k[X.ixfe], v: X.val, t: X.t }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t); break;
                                case 638:
                                    d = { ixfe: X.ixfe, XF: k[X.ixfe], v: X.rknum, t: "n" }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t); break;
                                case 189:
                                    for (var ee = X.c; ee <= X.C; ++ee) { var te = X.rkrec[ee - X.c][0];
                                        d = { ixfe: te, XF: k[te], v: X.rkrec[ee - X.c][1], t: "n" }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: ee, r: X.r }, d, t) } break;
                                case 6:
                                case 518:
                                case 1030:
                                    if ("String" == X.val) { v = X; break } if ((d = Rl(X.val, X.cell.ixfe, X.tt)).XF = k[d.ixfe], t.cellFormula) { var ne = X.formula; if (ne && ne[0] && ne[0][0] && "PtgExp" == ne[0][0][0]) { var re = ne[0][0][1][0],
                                                ae = ne[0][0][1][1],
                                                oe = Vn({ r: re, c: ae });
                                            x[oe] ? d.f = "" + gi(X.formula, 0, X.cell, R, H) : d.F = ((t.dense ? (m[re] || [])[ae] : m[oe]) || {}).F } else d.f = "" + gi(X.formula, 0, X.cell, R, H) } _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T(X.cell, d, t), v = X; break;
                                case 7:
                                case 519:
                                    if (!v) throw new Error("String record expects Formula");
                                    v.val = X, (d = Rl(X, v.cell.ixfe, "s")).XF = k[d.ixfe], t.cellFormula && (d.f = "" + gi(v.formula, 0, v.cell, R, H)), _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T(v.cell, d, t), v = null; break;
                                case 33:
                                case 545:
                                    A.push(X); var ie = Vn(X[0].s); if (o = t.dense ? (m[X[0].s.r] || [])[X[0].s.c] : m[ie], t.cellFormula && o) { if (!v) break; if (!ie || !o) break;
                                        o.f = "" + gi(X[1], 0, X[0], R, H), o.F = Rn(X[0]) } break;
                                case 1212:
                                    if (!t.cellFormula) break; if (z) { if (!v) break;
                                        x[Vn(v.cell)] = X[0], ((o = t.dense ? (m[v.cell.r] || [])[v.cell.c] : m[Vn(v.cell)]) || {}).f = "" + gi(X[0], 0, a, R, H) } break;
                                case 253:
                                    d = Rl(y[X.isst].t, X.ixfe, "s"), y[X.isst].h && (d.h = y[X.isst].h), d.XF = k[d.ixfe], _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t); break;
                                case 513:
                                    t.sheetStubs && (d = { ixfe: X.ixfe, XF: k[X.ixfe], t: "z" }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t)); break;
                                case 190:
                                    if (t.sheetStubs)
                                        for (var le = X.c; le <= X.C; ++le) { var se = X.ixfe[le - X.c];
                                            d = { ixfe: se, XF: k[se], t: "z" }, _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: le, r: X.r }, d, t) }
                                    break;
                                case 214:
                                case 516:
                                case 4:
                                    (d = Rl(X.val, X.ixfe, "s")).XF = k[d.ixfe], _ > 0 && (d.z = B[d.ixfe >> 8 & 63]), Ol(d, t, n.opts.Date1904), T({ c: X.c, r: X.r }, d, t); break;
                                case 0:
                                case 512:
                                    1 === F && (f = X); break;
                                case 252:
                                    y = X; break;
                                case 1054:
                                    if (4 == H.biff) { B[_++] = X[1]; for (var ce = 0; ce < _ + 163 && N[ce] != X[1]; ++ce);
                                        ce >= 163 && we(X[1], _ + 163) } else we(X[1], X[0]); break;
                                case 30:
                                    B[_++] = X; for (var de = 0; de < _ + 163 && N[de] != X; ++de);
                                    de >= 163 && we(X, _ + 163); break;
                                case 229:
                                    L = L.concat(X); break;
                                case 93:
                                    I[X.cmo[0]] = H.lastobj = X; break;
                                case 438:
                                    H.lastobj.TxO = X; break;
                                case 127:
                                    H.lastobj.ImData = X; break;
                                case 440:
                                    for (s = X[0].s.r; s <= X[0].e.r; ++s)
                                        for (l = X[0].s.c; l <= X[0].e.c; ++l)(o = t.dense ? (m[s] || [])[l] : m[Vn({ c: l, r: s })]) && (o.l = X[1]); break;
                                case 2048:
                                    for (s = X[0].s.r; s <= X[0].e.r; ++s)
                                        for (l = X[0].s.c; l <= X[0].e.c; ++l)(o = t.dense ? (m[s] || [])[l] : m[Vn({ c: l, r: s })]) && o.l && (o.l.Tooltip = X[1]); break;
                                case 28:
                                    if (H.biff <= 5 && H.biff >= 2) break;
                                    o = t.dense ? (m[X[0].r] || [])[X[0].c] : m[Vn(X[0])]; var ue = I[X[2]];
                                    o || (t.dense ? (m[X[0].r] || (m[X[0].r] = []), o = m[X[0].r][X[0].c] = { t: "z" }) : o = m[Vn(X[0])] = { t: "z" }, f.e.r = Math.max(f.e.r, X[0].r), f.s.r = Math.min(f.s.r, X[0].r), f.e.c = Math.max(f.e.c, X[0].c), f.s.c = Math.min(f.s.c, X[0].c)), o.c || (o.c = []), i = { a: X[1], t: ue.TxO.t }, o.c.push(i); break;
                                case 2173:
                                    k[X.ixfe], X.ext.forEach((function(e) { e[0] })); break;
                                case 125:
                                    if (!H.cellStyles) break; for (; X.e >= X.s;) j[X.e--] = { width: X.w / 256, level: X.level || 0, hidden: !!(1 & X.flags) }, O || (O = !0, po(X.w / 256)), fo(j[X.e + 1]); break;
                                case 520:
                                    var he = {};
                                    null != X.level && (V[X.r] = he, he.level = X.level), X.hidden && (V[X.r] = he, he.hidden = !0), X.hpt && (V[X.r] = he, he.hpt = X.hpt, he.hpx = yo(X.hpt)); break;
                                case 38:
                                case 39:
                                case 40:
                                case 41:
                                    m["!margins"] || ji(m["!margins"] = {}), m["!margins"][{ 38: "left", 39: "right", 40: "top", 41: "bottom" } [G]] = X; break;
                                case 161:
                                    m["!margins"] || ji(m["!margins"] = {}), m["!margins"].header = X.header, m["!margins"].footer = X.footer; break;
                                case 574:
                                    X.RTL && (M.Views[0].RTL = !0); break;
                                case 146:
                                    S = X; break;
                                case 2198:
                                    h = X; break;
                                case 140:
                                    u = X; break;
                                case 442:
                                    b ? E.CodeName = X || E.name : M.WBProps.CodeName = X || "ThisWorkbook" } } else Z || console.error("Missing Info for XLS Record 0x" + G.toString(16)), e.l += K } return n.SheetNames = Ee(p).sort((function(e, t) { return Number(e) - Number(t) })).map((function(e) { return p[e].name })), t.bookSheets || (n.Sheets = r), !n.SheetNames.length && w["!ref"] ? (n.SheetNames.push("Sheet1"), n.Sheets && (n.Sheets.Sheet1 = w)) : n.Preamble = w, n.Sheets && W.forEach((function(e, t) { n.Sheets[n.SheetNames[t]]["!autofilter"] = e })), n.Strings = y, n.SSF = _e(N), H.enc && (n.Encryption = H.enc), h && (n.Themes = h), n.Metadata = {}, void 0 !== u && (n.Metadata.Country = u), R.names.length > 0 && (M.Names = R.names), n.Workbook = M, n } var Dl = { SI: "e0859ff2f94f6810ab9108002b27b3d9", DSI: "02d5cdd59c2e1b10939708002b2cf9ae", UDI: "05d5cdd59c2e1b10939708002b2cf9ae" };

                function Fl(e, t) { var n, r, a, o; if (t || (t = {}), As(t), d(), t.codepage && s(t.codepage), e.FullPaths) { if (Se.find(e, "/encryption")) throw new Error("File is password-protected");
                        n = Se.find(e, "!CompObj"), r = Se.find(e, "/Workbook") || Se.find(e, "/Book") } else { switch (t.type) {
                            case "base64":
                                e = S(w(e)); break;
                            case "binary":
                                e = S(e); break;
                            case "buffer":
                                break;
                            case "array":
                                Array.isArray(e) || (e = Array.prototype.slice.call(e)) } wn(e, 0), r = { content: e } } if (n && Il(n), t.bookProps && !t.bookSheets) a = {};
                    else { var i = z ? "buffer" : "array"; if (r && r.content) a = Pl(r.content, t);
                        else if ((o = Se.find(e, "PerfectOffice_MAIN")) && o.content) a = ja.to_workbook(o.content, (t.type = i, t));
                        else { if (!(o = Se.find(e, "NativeContent_MAIN")) || !o.content) throw (o = Se.find(e, "MN0")) && o.content ? new Error("Unsupported Works 4 for Mac file") : new Error("Cannot find Workbook stream");
                            a = ja.to_workbook(o.content, (t.type = i, t)) } t.bookVBA && e.FullPaths && Se.find(e, "/_VBA_PROJECT_CUR/VBA/dir") && (a.vbaraw = function(e) { var t = Se.utils.cfb_new({ root: "R" }); return e.FullPaths.forEach((function(n, r) { if ("/" !== n.slice(-1) && n.match(/_VBA_PROJECT_CUR/)) { var a = n.replace(/^[^\/]*/, "R").replace(/\/_VBA_PROJECT_CUR\u0000*/, "");
                                    Se.utils.cfb_add(t, a, e.FileIndex[r].content) } })), Se.write(t) }(e)) } var l = {}; return e.FullPaths && function(e, t, n) { var r = Se.find(e, "/!DocumentSummaryInformation"); if (r && r.size > 0) try { var a = Br(r, dr, Dl.DSI); for (var o in a) t[o] = a[o] } catch (c) { if (n.WTF) throw c }
                        var i = Se.find(e, "/!SummaryInformation"); if (i && i.size > 0) try { var l = Br(i, ur, Dl.SI); for (var s in l) null == t[s] && (t[s] = l[s]) } catch (c) { if (n.WTF) throw c } t.HeadingPairs && t.TitlesOfParts && (Er(t.HeadingPairs, t.TitlesOfParts, t, n), delete t.HeadingPairs, delete t.TitlesOfParts) }(e, l, t), a.Props = a.Custprops = l, t.bookFiles && (a.cfb = e), a } var Nl = { 0: { f: function(e, t) { var n = {},
                                    r = e.l + t;
                                n.r = e.read_shift(4), e.l += 4; var a = e.read_shift(2);
                                e.l += 1; var o = e.read_shift(1); return e.l = r, 7 & o && (n.level = 7 & o), 16 & o && (n.hidden = !0), 32 & o && (n.hpt = a / 20), n } }, 1: { f: function(e) { return [Kn(e)] } }, 2: { f: function(e) { return [Kn(e), Jn(e), "n"] } }, 3: { f: function(e) { return [Kn(e), e.read_shift(1), "e"] } }, 4: { f: function(e) { return [Kn(e), e.read_shift(1), "b"] } }, 5: { f: function(e) { return [Kn(e), nr(e), "n"] } }, 6: { f: function(e) { return [Kn(e), Wn(e), "str"] } }, 7: { f: function(e) { return [Kn(e), e.read_shift(4), "s"] } }, 8: { f: function(e, t, n) { var r = e.l + t,
                                    a = Kn(e);
                                a.r = n["!row"]; var o = [a, Wn(e), "str"]; if (n.cellFormula) { e.l += 2; var i = Ai(e, r - e.l, n);
                                    o[3] = gi(i, 0, a, n.supbooks, n) } else e.l = r; return o } }, 9: { f: function(e, t, n) { var r = e.l + t,
                                    a = Kn(e);
                                a.r = n["!row"]; var o = [a, nr(e), "n"]; if (n.cellFormula) { e.l += 2; var i = Ai(e, r - e.l, n);
                                    o[3] = gi(i, 0, a, n.supbooks, n) } else e.l = r; return o } }, 10: { f: function(e, t, n) { var r = e.l + t,
                                    a = Kn(e);
                                a.r = n["!row"]; var o = [a, e.read_shift(1), "b"]; if (n.cellFormula) { e.l += 2; var i = Ai(e, r - e.l, n);
                                    o[3] = gi(i, 0, a, n.supbooks, n) } else e.l = r; return o } }, 11: { f: function(e, t, n) { var r = e.l + t,
                                    a = Kn(e);
                                a.r = n["!row"]; var o = [a, e.read_shift(1), "e"]; if (n.cellFormula) { e.l += 2; var i = Ai(e, r - e.l, n);
                                    o[3] = gi(i, 0, a, n.supbooks, n) } else e.l = r; return o } }, 12: { f: function(e) { return [Zn(e)] } }, 13: { f: function(e) { return [Zn(e), Jn(e), "n"] } }, 14: { f: function(e) { return [Zn(e), e.read_shift(1), "e"] } }, 15: { f: function(e) { return [Zn(e), e.read_shift(1), "b"] } }, 16: { f: Xi }, 17: { f: function(e) { return [Zn(e), Wn(e), "str"] } }, 18: { f: function(e) { return [Zn(e), e.read_shift(4), "s"] } }, 19: { f: qn }, 20: {}, 21: {}, 22: {}, 23: {}, 24: {}, 25: {}, 26: {}, 27: {}, 28: {}, 29: {}, 30: {}, 31: {}, 32: {}, 33: {}, 34: {}, 35: { T: 1 }, 36: { T: -1 }, 37: { T: 1 }, 38: { T: -1 }, 39: { f: function(e, t, n) { var r = e.l + t;
                                e.l += 4, e.l += 1; var a = e.read_shift(4),
                                    o = $n(e),
                                    i = ki(e, 0, n),
                                    l = Xn(e);
                                e.l = r; var s = { Name: o, Ptg: i }; return a < 268435455 && (s.Sheet = a), l && (s.Comment = l), s } }, 40: {}, 42: {}, 43: { f: function(e, t, n) { var r = {};
                                r.sz = e.read_shift(2) / 20; var a = function(e) { var t = e.read_shift(1); return e.l++, { fBold: 1 & t, fItalic: 2 & t, fUnderline: 4 & t, fStrikeout: 8 & t, fOutline: 16 & t, fShadow: 32 & t, fCondense: 64 & t, fExtend: 128 & t } }(e); switch (a.fItalic && (r.italic = 1), a.fCondense && (r.condense = 1), a.fExtend && (r.extend = 1), a.fShadow && (r.shadow = 1), a.fOutline && (r.outline = 1), a.fStrikeout && (r.strike = 1), 700 === e.read_shift(2) && (r.bold = 1), e.read_shift(2)) {
                                    case 1:
                                        r.vertAlign = "superscript"; break;
                                    case 2:
                                        r.vertAlign = "subscript" } var o = e.read_shift(1);
                                0 != o && (r.underline = o); var i = e.read_shift(1);
                                i > 0 && (r.family = i); var l = e.read_shift(1); switch (l > 0 && (r.charset = l), e.l++, r.color = function(e) { var t = {},
                                        n = e.read_shift(1) >>> 1,
                                        r = e.read_shift(1),
                                        a = e.read_shift(2, "i"),
                                        o = e.read_shift(1),
                                        i = e.read_shift(1),
                                        l = e.read_shift(1); switch (e.l++, n) {
                                        case 0:
                                            t.auto = 1; break;
                                        case 1:
                                            t.index = r; var s = fr[r];
                                            s && (t.rgb = ro(s)); break;
                                        case 2:
                                            t.rgb = ro([o, i, l]); break;
                                        case 3:
                                            t.theme = r } return 0 != a && (t.tint = a > 0 ? a / 32767 : a / 32768), t }(e), e.read_shift(1)) {
                                    case 1:
                                        r.scheme = "major"; break;
                                    case 2:
                                        r.scheme = "minor" } return r.name = Wn(e), r } }, 44: { f: function(e, t) { return [e.read_shift(2), Wn(e)] } }, 45: { f: Ao }, 46: { f: ko }, 47: { f: function(e, t) { var n = e.l + t,
                                    r = e.read_shift(2),
                                    a = e.read_shift(2); return e.l = n, { ixfe: r, numFmtId: a } } }, 48: {}, 49: { f: function(e) { return e.read_shift(4, "i") } }, 50: {}, 51: { f: function(e) { for (var t = [], n = e.read_shift(4); n-- > 0;) t.push([e.read_shift(4), e.read_shift(4)]); return t } }, 52: { T: 1 }, 53: { T: -1 }, 54: { T: 1 }, 55: { T: -1 }, 56: { T: 1 }, 57: { T: -1 }, 58: {}, 59: {}, 60: { f: Aa }, 62: { f: function(e) { return [Kn(e), qn(e), "is"] } }, 63: { f: function(e) { var t = {};
                                t.i = e.read_shift(4); var n = {};
                                n.r = e.read_shift(4), n.c = e.read_shift(4), t.r = Vn(n); var r = e.read_shift(1); return 2 & r && (t.l = "1"), 8 & r && (t.a = "1"), t } }, 64: { f: function() {} }, 65: {}, 66: {}, 67: {}, 68: {}, 69: {}, 70: {}, 128: {}, 129: { T: 1 }, 130: { T: -1 }, 131: { T: 1, f: zn, p: 0 }, 132: { T: -1 }, 133: { T: 1 }, 134: { T: -1 }, 135: { T: 1 }, 136: { T: -1 }, 137: { T: 1, f: function(e) { var t = e.read_shift(2); return e.l += 28, { RTL: 32 & t } } }, 138: { T: -1 }, 139: { T: 1 }, 140: { T: -1 }, 141: { T: 1 }, 142: { T: -1 }, 143: { T: 1 }, 144: { T: -1 }, 145: { T: 1 }, 146: { T: -1 }, 147: { f: function(e, t) { var n = {},
                                    r = e[e.l]; return ++e.l, n.above = !(64 & r), n.left = !(128 & r), e.l += 18, n.name = Yn(e, t - 19), n } }, 148: { f: Yi, p: 16 }, 151: { f: function() {} }, 152: {}, 153: { f: function(e, t) { var n = {},
                                    r = e.read_shift(4);
                                n.defaultThemeVersion = e.read_shift(4); var a = t > 8 ? Wn(e) : ""; return a.length > 0 && (n.CodeName = a), n.autoCompressPictures = !!(65536 & r), n.backupFile = !!(64 & r), n.checkCompatibility = !!(4096 & r), n.date1904 = !!(1 & r), n.filterPrivacy = !!(8 & r), n.hidePivotFieldList = !!(1024 & r), n.promptedSolutions = !!(16 & r), n.publishItems = !!(2048 & r), n.refreshAllConnections = !!(262144 & r), n.saveExternalLinkValues = !!(128 & r), n.showBorderUnselectedTables = !!(4 & r), n.showInkAnnotation = !!(32 & r), n.showObjects = ["all", "placeholders", "none"][r >> 13 & 3], n.showPivotChartFilter = !!(32768 & r), n.updateLinks = ["userSet", "never", "always"][r >> 8 & 3], n } }, 154: {}, 155: {}, 156: { f: function(e, t) { var n = {}; return n.Hidden = e.read_shift(4), n.iTabID = e.read_shift(4), n.strRelID = Qn(e, t - 8), n.name = Wn(e), n } }, 157: {}, 158: {}, 159: { T: 1, f: function(e) { return [e.read_shift(4), e.read_shift(4)] } }, 160: { T: -1 }, 161: { T: 1, f: tr }, 162: { T: -1 }, 163: { T: 1 }, 164: { T: -1 }, 165: { T: 1 }, 166: { T: -1 }, 167: {}, 168: {}, 169: {}, 170: {}, 171: {}, 172: { T: 1 }, 173: { T: -1 }, 174: {}, 175: {}, 176: { f: $i }, 177: { T: 1 }, 178: { T: -1 }, 179: { T: 1 }, 180: { T: -1 }, 181: { T: 1 }, 182: { T: -1 }, 183: { T: 1 }, 184: { T: -1 }, 185: { T: 1 }, 186: { T: -1 }, 187: { T: 1 }, 188: { T: -1 }, 189: { T: 1 }, 190: { T: -1 }, 191: { T: 1 }, 192: { T: -1 }, 193: { T: 1 }, 194: { T: -1 }, 195: { T: 1 }, 196: { T: -1 }, 197: { T: 1 }, 198: { T: -1 }, 199: { T: 1 }, 200: { T: -1 }, 201: { T: 1 }, 202: { T: -1 }, 203: { T: 1 }, 204: { T: -1 }, 205: { T: 1 }, 206: { T: -1 }, 207: { T: 1 }, 208: { T: -1 }, 209: { T: 1 }, 210: { T: -1 }, 211: { T: 1 }, 212: { T: -1 }, 213: { T: 1 }, 214: { T: -1 }, 215: { T: 1 }, 216: { T: -1 }, 217: { T: 1 }, 218: { T: -1 }, 219: { T: 1 }, 220: { T: -1 }, 221: { T: 1 }, 222: { T: -1 }, 223: { T: 1 }, 224: { T: -1 }, 225: { T: 1 }, 226: { T: -1 }, 227: { T: 1 }, 228: { T: -1 }, 229: { T: 1 }, 230: { T: -1 }, 231: { T: 1 }, 232: { T: -1 }, 233: { T: 1 }, 234: { T: -1 }, 235: { T: 1 }, 236: { T: -1 }, 237: { T: 1 }, 238: { T: -1 }, 239: { T: 1 }, 240: { T: -1 }, 241: { T: 1 }, 242: { T: -1 }, 243: { T: 1 }, 244: { T: -1 }, 245: { T: 1 }, 246: { T: -1 }, 247: { T: 1 }, 248: { T: -1 }, 249: { T: 1 }, 250: { T: -1 }, 251: { T: 1 }, 252: { T: -1 }, 253: { T: 1 }, 254: { T: -1 }, 255: { T: 1 }, 256: { T: -1 }, 257: { T: 1 }, 258: { T: -1 }, 259: { T: 1 }, 260: { T: -1 }, 261: { T: 1 }, 262: { T: -1 }, 263: { T: 1 }, 264: { T: -1 }, 265: { T: 1 }, 266: { T: -1 }, 267: { T: 1 }, 268: { T: -1 }, 269: { T: 1 }, 270: { T: -1 }, 271: { T: 1 }, 272: { T: -1 }, 273: { T: 1 }, 274: { T: -1 }, 275: { T: 1 }, 276: { T: -1 }, 277: {}, 278: { T: 1 }, 279: { T: -1 }, 280: { T: 1 }, 281: { T: -1 }, 282: { T: 1 }, 283: { T: 1 }, 284: { T: -1 }, 285: { T: 1 }, 286: { T: -1 }, 287: { T: 1 }, 288: { T: -1 }, 289: { T: 1 }, 290: { T: -1 }, 291: { T: 1 }, 292: { T: -1 }, 293: { T: 1 }, 294: { T: -1 }, 295: { T: 1 }, 296: { T: -1 }, 297: { T: 1 }, 298: { T: -1 }, 299: { T: 1 }, 300: { T: -1 }, 301: { T: 1 }, 302: { T: -1 }, 303: { T: 1 }, 304: { T: -1 }, 305: { T: 1 }, 306: { T: -1 }, 307: { T: 1 }, 308: { T: -1 }, 309: { T: 1 }, 310: { T: -1 }, 311: { T: 1 }, 312: { T: -1 }, 313: { T: -1 }, 314: { T: 1 }, 315: { T: -1 }, 316: { T: 1 }, 317: { T: -1 }, 318: { T: 1 }, 319: { T: -1 }, 320: { T: 1 }, 321: { T: -1 }, 322: { T: 1 }, 323: { T: -1 }, 324: { T: 1 }, 325: { T: -1 }, 326: { T: 1 }, 327: { T: -1 }, 328: { T: 1 }, 329: { T: -1 }, 330: { T: 1 }, 331: { T: -1 }, 332: { T: 1 }, 333: { T: -1 }, 334: { T: 1 }, 335: { f: function(e, t) { return { flags: e.read_shift(4), version: e.read_shift(4), name: Wn(e) } } }, 336: { T: -1 }, 337: { f: function(e) { return e.l += 4, 0 != e.read_shift(4) }, T: 1 }, 338: { T: -1 }, 339: { T: 1 }, 340: { T: -1 }, 341: { T: 1 }, 342: { T: -1 }, 343: { T: 1 }, 344: { T: -1 }, 345: { T: 1 }, 346: { T: -1 }, 347: { T: 1 }, 348: { T: -1 }, 349: { T: 1 }, 350: { T: -1 }, 351: {}, 352: {}, 353: { T: 1 }, 354: { T: -1 }, 355: { f: Qn }, 357: {}, 358: {}, 359: {}, 360: { T: 1 }, 361: {}, 362: { f: wa }, 363: {}, 364: {}, 366: {}, 367: {}, 368: {}, 369: {}, 370: {}, 371: {}, 372: { T: 1 }, 373: { T: -1 }, 374: { T: 1 }, 375: { T: -1 }, 376: { T: 1 }, 377: { T: -1 }, 378: { T: 1 }, 379: { T: -1 }, 380: { T: 1 }, 381: { T: -1 }, 382: { T: 1 }, 383: { T: -1 }, 384: { T: 1 }, 385: { T: -1 }, 386: { T: 1 }, 387: { T: -1 }, 388: { T: 1 }, 389: { T: -1 }, 390: { T: 1 }, 391: { T: -1 }, 392: { T: 1 }, 393: { T: -1 }, 394: { T: 1 }, 395: { T: -1 }, 396: {}, 397: {}, 398: {}, 399: {}, 400: {}, 401: { T: 1 }, 403: {}, 404: {}, 405: {}, 406: {}, 407: {}, 408: {}, 409: {}, 410: {}, 411: {}, 412: {}, 413: {}, 414: {}, 415: {}, 416: {}, 417: {}, 418: {}, 419: {}, 420: {}, 421: {}, 422: { T: 1 }, 423: { T: 1 }, 424: { T: -1 }, 425: { T: -1 }, 426: { f: function(e, t, n) { var r = e.l + t,
                                    a = er(e),
                                    o = e.read_shift(1),
                                    i = [a]; if (i[2] = o, n.cellFormula) { var l = xi(e, r - e.l, n);
                                    i[1] = l } else e.l = r; return i } }, 427: { f: function(e, t, n) { var r = e.l + t,
                                    a = [tr(e, 16)]; if (n.cellFormula) { var o = Si(e, r - e.l, n);
                                    a[1] = o, e.l = r } else e.l = r; return a } }, 428: {}, 429: { T: 1 }, 430: { T: -1 }, 431: { T: 1 }, 432: { T: -1 }, 433: { T: 1 }, 434: { T: -1 }, 435: { T: 1 }, 436: { T: -1 }, 437: { T: 1 }, 438: { T: -1 }, 439: { T: 1 }, 440: { T: -1 }, 441: { T: 1 }, 442: { T: -1 }, 443: { T: 1 }, 444: { T: -1 }, 445: { T: 1 }, 446: { T: -1 }, 447: { T: 1 }, 448: { T: -1 }, 449: { T: 1 }, 450: { T: -1 }, 451: { T: 1 }, 452: { T: -1 }, 453: { T: 1 }, 454: { T: -1 }, 455: { T: 1 }, 456: { T: -1 }, 457: { T: 1 }, 458: { T: -1 }, 459: { T: 1 }, 460: { T: -1 }, 461: { T: 1 }, 462: { T: -1 }, 463: { T: 1 }, 464: { T: -1 }, 465: { T: 1 }, 466: { T: -1 }, 467: { T: 1 }, 468: { T: -1 }, 469: { T: 1 }, 470: { T: -1 }, 471: {}, 472: {}, 473: { T: 1 }, 474: { T: -1 }, 475: {}, 476: { f: function(e) { var t = {}; return Qi.forEach((function(n) { t[n] = nr(e) })), t } }, 477: {}, 478: {}, 479: { T: 1 }, 480: { T: -1 }, 481: { T: 1 }, 482: { T: -1 }, 483: { T: 1 }, 484: { T: -1 }, 485: { f: function() {} }, 486: { T: 1 }, 487: { T: -1 }, 488: { T: 1 }, 489: { T: -1 }, 490: { T: 1 }, 491: { T: -1 }, 492: { T: 1 }, 493: { T: -1 }, 494: { f: function(e, t) { var n = e.l + t,
                                    r = tr(e, 16),
                                    a = Xn(e),
                                    o = Wn(e),
                                    i = Wn(e),
                                    l = Wn(e);
                                e.l = n; var s = { rfx: r, relId: a, loc: o, display: l }; return i && (s.Tooltip = i), s } }, 495: { T: 1 }, 496: { T: -1 }, 497: { T: 1 }, 498: { T: -1 }, 499: {}, 500: { T: 1 }, 501: { T: -1 }, 502: { T: 1 }, 503: { T: -1 }, 504: {}, 505: { T: 1 }, 506: { T: -1 }, 507: {}, 508: { T: 1 }, 509: { T: -1 }, 510: { T: 1 }, 511: { T: -1 }, 512: {}, 513: {}, 514: { T: 1 }, 515: { T: -1 }, 516: { T: 1 }, 517: { T: -1 }, 518: { T: 1 }, 519: { T: -1 }, 520: { T: 1 }, 521: { T: -1 }, 522: {}, 523: {}, 524: {}, 525: {}, 526: {}, 527: {}, 528: { T: 1 }, 529: { T: -1 }, 530: { T: 1 }, 531: { T: -1 }, 532: { T: 1 }, 533: { T: -1 }, 534: {}, 535: {}, 536: {}, 537: {}, 538: { T: 1 }, 539: { T: -1 }, 540: { T: 1 }, 541: { T: -1 }, 542: { T: 1 }, 548: {}, 549: {}, 550: { f: Qn }, 551: {}, 552: {}, 553: {}, 554: { T: 1 }, 555: { T: -1 }, 556: { T: 1 }, 557: { T: -1 }, 558: { T: 1 }, 559: { T: -1 }, 560: { T: 1 }, 561: { T: -1 }, 562: {}, 564: {}, 565: { T: 1 }, 566: { T: -1 }, 569: { T: 1 }, 570: { T: -1 }, 572: {}, 573: { T: 1 }, 574: { T: -1 }, 577: {}, 578: {}, 579: {}, 580: {}, 581: {}, 582: {}, 583: {}, 584: {}, 585: {}, 586: {}, 587: {}, 588: { T: -1 }, 589: {}, 590: { T: 1 }, 591: { T: -1 }, 592: { T: 1 }, 593: { T: -1 }, 594: { T: 1 }, 595: { T: -1 }, 596: {}, 597: { T: 1 }, 598: { T: -1 }, 599: { T: 1 }, 600: { T: -1 }, 601: { T: 1 }, 602: { T: -1 }, 603: { T: 1 }, 604: { T: -1 }, 605: { T: 1 }, 606: { T: -1 }, 607: {}, 608: { T: 1 }, 609: { T: -1 }, 610: {}, 611: { T: 1 }, 612: { T: -1 }, 613: { T: 1 }, 614: { T: -1 }, 615: { T: 1 }, 616: { T: -1 }, 617: { T: 1 }, 618: { T: -1 }, 619: { T: 1 }, 620: { T: -1 }, 625: {}, 626: { T: 1 }, 627: { T: -1 }, 628: { T: 1 }, 629: { T: -1 }, 630: { T: 1 }, 631: { T: -1 }, 632: { f: Do }, 633: { T: 1 }, 634: { T: -1 }, 635: { T: 1, f: function(e) { var t = {};
                                t.iauthor = e.read_shift(4); var n = tr(e, 16); return t.rfx = n.s, t.ref = Vn(n.s), e.l += 16, t } }, 636: { T: -1 }, 637: { f: Gn }, 638: { T: 1 }, 639: {}, 640: { T: -1 }, 641: { T: 1 }, 642: { T: -1 }, 643: { T: 1 }, 644: {}, 645: { T: -1 }, 646: { T: 1 }, 648: { T: 1 }, 649: {}, 650: { T: -1 }, 651: { f: function(e, t) { return e.l += 10, { name: Wn(e) } } }, 652: {}, 653: { T: 1 }, 654: { T: -1 }, 655: { T: 1 }, 656: { T: -1 }, 657: { T: 1 }, 658: { T: -1 }, 659: {}, 660: { T: 1 }, 661: {}, 662: { T: -1 }, 663: {}, 664: { T: 1 }, 665: {}, 666: { T: -1 }, 667: {}, 668: {}, 669: {}, 671: { T: 1 }, 672: { T: -1 }, 673: { T: 1 }, 674: { T: -1 }, 675: {}, 676: {}, 677: {}, 678: {}, 679: {}, 680: {}, 681: {}, 1024: {}, 1025: {}, 1026: { T: 1 }, 1027: { T: -1 }, 1028: { T: 1 }, 1029: { T: -1 }, 1030: {}, 1031: { T: 1 }, 1032: { T: -1 }, 1033: { T: 1 }, 1034: { T: -1 }, 1035: {}, 1036: {}, 1037: {}, 1038: { T: 1 }, 1039: { T: -1 }, 1040: {}, 1041: { T: 1 }, 1042: { T: -1 }, 1043: {}, 1044: {}, 1045: {}, 1046: { T: 1 }, 1047: { T: -1 }, 1048: { T: 1 }, 1049: { T: -1 }, 1050: {}, 1051: { T: 1 }, 1052: { T: 1 }, 1053: { f: function() {} }, 1054: { T: 1 }, 1055: {}, 1056: { T: 1 }, 1057: { T: -1 }, 1058: { T: 1 }, 1059: { T: -1 }, 1061: {}, 1062: { T: 1 }, 1063: { T: -1 }, 1064: { T: 1 }, 1065: { T: -1 }, 1066: { T: 1 }, 1067: { T: -1 }, 1068: { T: 1 }, 1069: { T: -1 }, 1070: { T: 1 }, 1071: { T: -1 }, 1072: { T: 1 }, 1073: { T: -1 }, 1075: { T: 1 }, 1076: { T: -1 }, 1077: { T: 1 }, 1078: { T: -1 }, 1079: { T: 1 }, 1080: { T: -1 }, 1081: { T: 1 }, 1082: { T: -1 }, 1083: { T: 1 }, 1084: { T: -1 }, 1085: {}, 1086: { T: 1 }, 1087: { T: -1 }, 1088: { T: 1 }, 1089: { T: -1 }, 1090: { T: 1 }, 1091: { T: -1 }, 1092: { T: 1 }, 1093: { T: -1 }, 1094: { T: 1 }, 1095: { T: -1 }, 1096: {}, 1097: { T: 1 }, 1098: {}, 1099: { T: -1 }, 1100: { T: 1 }, 1101: { T: -1 }, 1102: {}, 1103: {}, 1104: {}, 1105: {}, 1111: {}, 1112: {}, 1113: { T: 1 }, 1114: { T: -1 }, 1115: { T: 1 }, 1116: { T: -1 }, 1117: {}, 1118: { T: 1 }, 1119: { T: -1 }, 1120: { T: 1 }, 1121: { T: -1 }, 1122: { T: 1 }, 1123: { T: -1 }, 1124: { T: 1 }, 1125: { T: -1 }, 1126: {}, 1128: { T: 1 }, 1129: { T: -1 }, 1130: {}, 1131: { T: 1 }, 1132: { T: -1 }, 1133: { T: 1 }, 1134: { T: -1 }, 1135: { T: 1 }, 1136: { T: -1 }, 1137: { T: 1 }, 1138: { T: -1 }, 1139: { T: 1 }, 1140: { T: -1 }, 1141: {}, 1142: { T: 1 }, 1143: { T: -1 }, 1144: { T: 1 }, 1145: { T: -1 }, 1146: {}, 1147: { T: 1 }, 1148: { T: -1 }, 1149: { T: 1 }, 1150: { T: -1 }, 1152: { T: 1 }, 1153: { T: -1 }, 1154: { T: -1 }, 1155: { T: -1 }, 1156: { T: -1 }, 1157: { T: 1 }, 1158: { T: -1 }, 1159: { T: 1 }, 1160: { T: -1 }, 1161: { T: 1 }, 1162: { T: -1 }, 1163: { T: 1 }, 1164: { T: -1 }, 1165: { T: 1 }, 1166: { T: -1 }, 1167: { T: 1 }, 1168: { T: -1 }, 1169: { T: 1 }, 1170: { T: -1 }, 1171: {}, 1172: { T: 1 }, 1173: { T: -1 }, 1177: {}, 1178: { T: 1 }, 1180: {}, 1181: {}, 1182: {}, 2048: { T: 1 }, 2049: { T: -1 }, 2050: {}, 2051: { T: 1 }, 2052: { T: -1 }, 2053: {}, 2054: {}, 2055: { T: 1 }, 2056: { T: -1 }, 2057: { T: 1 }, 2058: { T: -1 }, 2060: {}, 2067: {}, 2068: { T: 1 }, 2069: { T: -1 }, 2070: {}, 2071: {}, 2072: { T: 1 }, 2073: { T: -1 }, 2075: {}, 2076: {}, 2077: { T: 1 }, 2078: { T: -1 }, 2079: {}, 2080: { T: 1 }, 2081: { T: -1 }, 2082: {}, 2083: { T: 1 }, 2084: { T: -1 }, 2085: { T: 1 }, 2086: { T: -1 }, 2087: { T: 1 }, 2088: { T: -1 }, 2089: { T: 1 }, 2090: { T: -1 }, 2091: {}, 2092: {}, 2093: { T: 1 }, 2094: { T: -1 }, 2095: {}, 2096: { T: 1 }, 2097: { T: -1 }, 2098: { T: 1 }, 2099: { T: -1 }, 2100: { T: 1 }, 2101: { T: -1 }, 2102: {}, 2103: { T: 1 }, 2104: { T: -1 }, 2105: {}, 2106: { T: 1 }, 2107: { T: -1 }, 2108: {}, 2109: { T: 1 }, 2110: { T: -1 }, 2111: { T: 1 }, 2112: { T: -1 }, 2113: { T: 1 }, 2114: { T: -1 }, 2115: {}, 2116: {}, 2117: {}, 2118: { T: 1 }, 2119: { T: -1 }, 2120: {}, 2121: { T: 1 }, 2122: { T: -1 }, 2123: { T: 1 }, 2124: { T: -1 }, 2125: {}, 2126: { T: 1 }, 2127: { T: -1 }, 2128: {}, 2129: { T: 1 }, 2130: { T: -1 }, 2131: { T: 1 }, 2132: { T: -1 }, 2133: { T: 1 }, 2134: {}, 2135: {}, 2136: {}, 2137: { T: 1 }, 2138: { T: -1 }, 2139: { T: 1 }, 2140: { T: -1 }, 2141: {}, 3072: {}, 3073: {}, 4096: { T: 1 }, 4097: { T: -1 }, 5002: { T: 1 }, 5003: { T: -1 }, 5081: { T: 1 }, 5082: { T: -1 }, 5083: {}, 5084: { T: 1 }, 5085: { T: -1 }, 5086: { T: 1 }, 5087: { T: -1 }, 5088: {}, 5089: {}, 5090: {}, 5092: { T: 1 }, 5093: { T: -1 }, 5094: {}, 5095: { T: 1 }, 5096: { T: -1 }, 5097: {}, 5099: {}, 65535: { n: "" } },
                    _l = { 6: { f: wi }, 10: { f: Wr }, 12: { f: qr }, 13: { f: qr }, 14: { f: Ur }, 15: { f: Ur }, 16: { f: nr }, 17: { f: Ur }, 18: { f: Ur }, 19: { f: qr }, 20: { f: va }, 21: { f: va }, 23: { f: wa }, 24: { f: ba }, 25: { f: Ur }, 26: {}, 27: {}, 28: { f: function(e, t, n) { return function(e, t, n) { if (!(n.biff < 8)) { var r = e.read_shift(2),
                                            a = e.read_shift(2),
                                            o = e.read_shift(2),
                                            i = e.read_shift(2),
                                            l = $r(e, 0, n); return n.biff < 8 && e.read_shift(1), [{ r: r, c: a }, l, i, o] } }(e, 0, n) } }, 29: {}, 34: { f: Ur }, 35: { f: ga }, 38: { f: nr }, 39: { f: nr }, 40: { f: nr }, 41: { f: nr }, 42: { f: Ur }, 43: { f: Ur }, 47: { f: function(e, t, n) { var r = { Type: n.biff >= 8 ? e.read_shift(2) : 0 }; return r.Type ? to(e, t - 2, r) : function(e, t, n, r) { var a = { key: qr(e), verificationBytes: qr(e) };
                                    n.password && (a.verifier = Qa(n.password)), r.valid = a.verificationBytes === a.verifier, r.valid && (r.insitu = eo(n.password)) }(e, n.biff, n, r), r } }, 49: { f: function(e, t, n) { var r = { dyHeight: e.read_shift(2), fl: e.read_shift(2) }; switch (n && n.biff || 8) {
                                    case 2:
                                        break;
                                    case 3:
                                    case 4:
                                        e.l += 2; break;
                                    default:
                                        e.l += 10 } return r.name = Kr(e, 0, n), r } }, 51: { f: qr }, 60: {}, 61: { f: function(e) { return { Pos: [e.read_shift(2), e.read_shift(2)], Dim: [e.read_shift(2), e.read_shift(2)], Flags: e.read_shift(2), CurTab: e.read_shift(2), FirstTab: e.read_shift(2), Selected: e.read_shift(2), TabRatio: e.read_shift(2) } } }, 64: { f: Ur }, 65: { f: function() {} }, 66: { f: qr }, 77: {}, 80: {}, 81: {}, 82: {}, 85: { f: qr }, 89: {}, 90: {}, 91: {}, 92: { f: function(e, t, n) { if (n.enc) return e.l += t, ""; var r = e.l,
                                    a = $r(e, 0, n); return e.read_shift(t + r - e.l), a } }, 93: { f: function(e, t, n) { if (n && n.biff < 8) return function(e, t, n) { e.l += 4; var r = e.read_shift(2),
                                        a = e.read_shift(2),
                                        o = e.read_shift(2);
                                    e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 2, e.l += 6, t -= 36; var i = []; return i.push((xa[r] || zn)(e, t, n)), { cmo: [a, r, o], ft: i } }(e, t, n); var r = sa(e),
                                    a = function(e, t) { for (var n = e.l + t, r = []; e.l < n;) { var a = e.read_shift(2);
                                            e.l -= 2; try { r.push(da[a](e, n - e.l)) } catch (o) { return e.l = n, r } } return e.l != n && (e.l = n), r }(e, t - 22, r[1]); return { cmo: r, ft: a } } }, 94: {}, 95: { f: Ur }, 96: {}, 97: {}, 99: { f: Ur }, 125: { f: Aa }, 128: { f: function(e) { e.l += 4; var t = [e.read_shift(2), e.read_shift(2)]; if (0 !== t[0] && t[0]--, 0 !== t[1] && t[1]--, t[0] > 7 || t[1] > 7) throw new Error("Bad Gutters: " + t.join("|")); return t } }, 129: { f: function(e, t, n) { var r = n && 8 == n.biff || 2 == t ? e.read_shift(2) : (e.l += t, 0); return { fDialog: 16 & r, fBelow: 64 & r, fRight: 128 & r } } }, 130: { f: qr }, 131: { f: Ur }, 132: { f: Ur }, 133: { f: function(e, t, n) { var r = e.read_shift(4),
                                    a = 3 & e.read_shift(1),
                                    o = e.read_shift(1); switch (o) {
                                    case 0:
                                        o = "Worksheet"; break;
                                    case 1:
                                        o = "Macrosheet"; break;
                                    case 2:
                                        o = "Chartsheet"; break;
                                    case 6:
                                        o = "VBAModule" } var i = Kr(e, 0, n); return 0 === i.length && (i = "Sheet1"), { pos: r, hs: a, dt: o, name: i } } }, 134: {}, 140: { f: function(e) { var t, n = [0, 0]; return t = e.read_shift(2), n[0] = hr[t] || t, t = e.read_shift(2), n[1] = hr[t] || t, n } }, 141: { f: qr }, 144: {}, 146: { f: function(e) { for (var t = e.read_shift(2), n = []; t-- > 0;) n.push(ta(e)); return n } }, 151: {}, 152: {}, 153: {}, 154: {}, 155: {}, 156: { f: qr }, 157: {}, 158: {}, 160: { f: Sa }, 161: { f: function(e, t) { var n = {}; return t < 32 || (e.l += 16, n.header = nr(e), n.footer = nr(e), e.l += 2), n } }, 174: {}, 175: {}, 176: {}, 177: {}, 178: {}, 180: {}, 181: {}, 182: {}, 184: {}, 185: {}, 189: { f: function(e, t) { for (var n = e.l + t - 2, r = e.read_shift(2), a = e.read_shift(2), o = []; e.l < n;) o.push(aa(e)); if (e.l !== n) throw new Error("MulRK read error"); var i = e.read_shift(2); if (o.length != i - a + 1) throw new Error("MulRK length mismatch"); return { r: r, c: a, C: i, rkrec: o } } }, 190: { f: function(e, t) { for (var n = e.l + t - 2, r = e.read_shift(2), a = e.read_shift(2), o = []; e.l < n;) o.push(e.read_shift(2)); if (e.l !== n) throw new Error("MulBlank read error"); var i = e.read_shift(2); if (o.length != i - a + 1) throw new Error("MulBlank length mismatch"); return { r: r, c: a, C: i, ixfe: o } } }, 193: { f: Wr }, 197: {}, 198: {}, 199: {}, 200: {}, 201: {}, 202: { f: Ur }, 203: {}, 204: {}, 205: {}, 206: {}, 207: {}, 208: {}, 209: {}, 210: {}, 211: {}, 213: {}, 215: {}, 216: {}, 217: {}, 218: { f: qr }, 220: {}, 221: { f: Ur }, 222: {}, 224: { f: function(e, t, n) { var r = {}; return r.ifnt = e.read_shift(2), r.numFmtId = e.read_shift(2), r.flags = e.read_shift(2), r.fStyle = r.flags >> 2 & 1, 6, r.data = function(e, t, n, r) { var a = {},
                                        o = e.read_shift(4),
                                        i = e.read_shift(4),
                                        l = e.read_shift(4),
                                        s = e.read_shift(2); return a.patternType = mr[l >> 26], r.cellStyles ? (a.alc = 7 & o, a.fWrap = o >> 3 & 1, a.alcV = o >> 4 & 7, a.fJustLast = o >> 7 & 1, a.trot = o >> 8 & 255, a.cIndent = o >> 16 & 15, a.fShrinkToFit = o >> 20 & 1, a.iReadOrder = o >> 22 & 2, a.fAtrNum = o >> 26 & 1, a.fAtrFnt = o >> 27 & 1, a.fAtrAlc = o >> 28 & 1, a.fAtrBdr = o >> 29 & 1, a.fAtrPat = o >> 30 & 1, a.fAtrProt = o >> 31 & 1, a.dgLeft = 15 & i, a.dgRight = i >> 4 & 15, a.dgTop = i >> 8 & 15, a.dgBottom = i >> 12 & 15, a.icvLeft = i >> 16 & 127, a.icvRight = i >> 23 & 127, a.grbitDiag = i >> 30 & 3, a.icvTop = 127 & l, a.icvBottom = l >> 7 & 127, a.icvDiag = l >> 14 & 127, a.dgDiag = l >> 21 & 15, a.icvFore = 127 & s, a.icvBack = s >> 7 & 127, a.fsxButton = s >> 14 & 1, a) : a }(e, 0, r.fStyle, n), r } }, 225: { f: function(e, t) { return 0 === t || e.read_shift(2), 1200 } }, 226: { f: Wr }, 227: {}, 229: { f: function(e, t) { for (var n = [], r = e.read_shift(2); r--;) n.push(oa(e)); return n } }, 233: {}, 235: {}, 236: {}, 237: {}, 239: {}, 240: {}, 241: {}, 242: {}, 244: {}, 245: {}, 246: {}, 247: {}, 248: {}, 249: {}, 251: {}, 252: { f: function(e, t) { for (var n = e.l + t, r = e.read_shift(4), a = e.read_shift(4), o = [], i = 0; i != a && e.l < n; ++i) o.push(Zr(e)); return o.Count = r, o.Unique = a, o } }, 253: { f: function(e) { var t = na(e); return t.isst = e.read_shift(4), t } }, 255: { f: function(e, t) { var n = {}; return n.dsst = e.read_shift(2), e.l += t - 2, n } }, 256: {}, 259: {}, 290: {}, 311: {}, 312: {}, 315: {}, 317: { f: Gr }, 318: {}, 319: {}, 320: {}, 330: {}, 331: {}, 333: {}, 334: {}, 335: {}, 336: {}, 337: {}, 338: {}, 339: {}, 340: {}, 351: {}, 352: { f: Ur }, 353: { f: Wr }, 401: {}, 402: {}, 403: {}, 404: {}, 405: {}, 406: {}, 407: {}, 408: {}, 425: {}, 426: {}, 427: {}, 428: {}, 429: {}, 430: { f: function(e, t, n) { var r = e.l + t,
                                    a = e.read_shift(2),
                                    o = e.read_shift(2); if (n.sbcch = o, 1025 == o || 14849 == o) return [o, a]; if (o < 1 || o > 255) throw new Error("Unexpected SupBook type: " + o); for (var i = Yr(e, o), l = []; r > e.l;) l.push(Xr(e)); return [o, a, i, l] } }, 431: { f: Ur }, 432: {}, 433: {}, 434: {}, 437: {}, 438: { f: function(e, t, n) { var r = e.l,
                                    a = ""; try { e.l += 4; var o = (n.lastobj || { cmo: [0, 0] }).cmo[1]; - 1 == [0, 5, 7, 11, 12, 14].indexOf(o) ? e.l += 6 : function(e) { var t = e.read_shift(1);
                                        e.l++; var n = e.read_shift(2); return e.l += 2, [t, n] }(e); var i = e.read_shift(2);
                                    e.read_shift(2), qr(e); var l = e.read_shift(2);
                                    e.l += l; for (var s = 1; s < e.lens.length - 1; ++s) { if (e.l - r != e.lens[s]) throw new Error("TxO: bad continue record"); var c = e[e.l]; if ((a += Yr(e, e.lens[s + 1] - e.lens[s] - 1)).length >= (c ? i : 2 * i)) break } if (a.length !== i && a.length !== 2 * i) throw new Error("cchText: " + i + " != " + a.length); return e.l = r + t, { t: a } } catch (d) { return e.l = r + t, { t: a } } } }, 439: { f: Ur }, 440: { f: function(e, t) { var n = oa(e);
                                e.l += 16; var r = function(e, t) { var n = e.l + t,
                                        r = e.read_shift(4); if (2 !== r) throw new Error("Unrecognized streamVersion: " + r); var a = e.read_shift(2);
                                    e.l += 2; var o, i, l, s, c, d, u = "";
                                    16 & a && (o = Jr(e, e.l)), 128 & a && (i = Jr(e, e.l)), 257 === (257 & a) && (l = Jr(e, e.l)), 1 === (257 & a) && (s = Qr(e, e.l)), 8 & a && (u = Jr(e, e.l)), 32 & a && (c = e.read_shift(16)), 64 & a && (d = Ir(e)), e.l = n; var h = i || l || s || "";
                                    h && u && (h += "#" + u), h || (h = "#" + u), 2 & a && "/" == h.charAt(0) && "/" != h.charAt(1) && (h = "file://" + h); var m = { Target: h }; return c && (m.guid = c), d && (m.time = d), o && (m.Tooltip = o), m }(e, t - 24); return [n, r] } }, 441: {}, 442: { f: Xr }, 443: {}, 444: { f: qr }, 445: {}, 446: {}, 448: { f: Wr }, 449: { f: function(e) { return e.read_shift(2), e.read_shift(4) }, r: 2 }, 450: { f: Wr }, 512: { f: pa }, 513: { f: ka }, 515: { f: function(e, t, n) { n.biffguess && 2 == n.biff && (n.biff = 5); var r = na(e),
                                    a = nr(e); return r.val = a, r } }, 516: { f: function(e, t, n) { n.biffguess && 2 == n.biff && (n.biff = 5), e.l; var r = na(e);
                                2 == n.biff && e.l++; var a = Xr(e, e.l, n); return r.val = a, r } }, 517: { f: fa }, 519: { f: Ma }, 520: { f: function(e) { var t = {};
                                t.r = e.read_shift(2), t.c = e.read_shift(2), t.cnt = e.read_shift(2) - t.c; var n = e.read_shift(2);
                                e.l += 4; var r = e.read_shift(1); return e.l += 3, 7 & r && (t.level = 7 & r), 32 & r && (t.hidden = !0), 64 & r && (t.hpt = n / 20), t } }, 523: {}, 545: { f: za }, 549: { f: ha }, 566: {}, 574: { f: function(e, t, n) { return n && n.biff >= 2 && n.biff < 5 ? {} : { RTL: 64 & e.read_shift(2) } } }, 638: { f: function(e) { var t = e.read_shift(2),
                                    n = e.read_shift(2),
                                    r = aa(e); return { r: t, c: n, ixfe: r[0], rknum: r[1] } } }, 659: {}, 1048: {}, 1054: { f: function(e, t, n) { return [e.read_shift(2), $r(e, 0, n)] } }, 1084: {}, 1212: { f: function(e, t, n) { var r = ia(e);
                                e.l++; var a = e.read_shift(1); return [bi(e, t -= 8, n), a, r] } }, 2048: { f: function(e, t) { e.read_shift(2); var n = oa(e),
                                    r = e.read_shift((t - 10) / 2, "dbcs-cont"); return [n, r = r.replace(T, "")] } }, 2049: {}, 2050: {}, 2051: {}, 2052: {}, 2053: {}, 2054: {}, 2055: {}, 2056: {}, 2057: { f: ua }, 2058: {}, 2059: {}, 2060: {}, 2061: {}, 2062: {}, 2063: {}, 2064: {}, 2066: {}, 2067: {}, 2128: {}, 2129: {}, 2130: {}, 2131: {}, 2132: {}, 2133: {}, 2134: {}, 2135: {}, 2136: {}, 2137: {}, 2138: {}, 2146: {}, 2147: { r: 12 }, 2148: {}, 2149: {}, 2150: {}, 2151: { f: Wr }, 2152: {}, 2154: {}, 2155: {}, 2156: {}, 2161: {}, 2162: {}, 2164: {}, 2165: {}, 2166: {}, 2167: {}, 2168: {}, 2169: {}, 2170: {}, 2171: {}, 2172: { f: function(e) { e.l += 2; var t = { cxfs: 0, crc: 0 }; return t.cxfs = e.read_shift(2), t.crc = e.read_shift(4), t }, r: 12 }, 2173: { f: function(e, t) { e.l, e.l += 2; var n = e.read_shift(2);
                                e.l += 2; for (var r = e.read_shift(2), a = []; r-- > 0;) a.push(Ro(e, e.l)); return { ixfe: n, ext: a } }, r: 12 }, 2174: {}, 2175: {}, 2180: {}, 2181: {}, 2182: {}, 2183: {}, 2184: {}, 2185: {}, 2186: {}, 2187: {}, 2188: { f: Ur, r: 12 }, 2189: {}, 2190: { r: 12 }, 2191: {}, 2192: {}, 2194: {}, 2195: {}, 2196: { f: function(e, t, n) { if (!(n.biff < 8)) { var r = e.read_shift(2),
                                        a = e.read_shift(2); return [Yr(e, r, n), Yr(e, a, n)] } e.l += t }, r: 12 }, 2197: {}, 2198: { f: function(e, t, n) { var r = e.l + t; if (124226 !== e.read_shift(4))
                                    if (n.cellStyles) { var a, o = e.slice(e.l);
                                        e.l = r; try { a = nt(o, { type: "array" }) } catch (l) { return } var i = Qe(a, "theme/theme/theme1.xml", !0); if (i) return jo(i, n) } else e.l = r }, r: 12 }, 2199: {}, 2200: {}, 2201: {}, 2202: { f: function(e) { return [0 !== e.read_shift(4), 0 !== e.read_shift(4), e.read_shift(4)] }, r: 12 }, 2203: { f: Wr }, 2204: {}, 2205: {}, 2206: {}, 2207: {}, 2211: { f: function(e) { var t = function(e) { var t = e.read_shift(2),
                                        n = e.read_shift(2); return e.l += 8, { type: t, flags: n } }(e); if (2211 != t.type) throw new Error("Invalid Future Record " + t.type); return 0 !== e.read_shift(4) } }, 2212: {}, 2213: {}, 2214: {}, 2215: {}, 4097: {}, 4098: {}, 4099: {}, 4102: {}, 4103: {}, 4105: {}, 4106: {}, 4107: {}, 4108: {}, 4109: {}, 4116: {}, 4117: {}, 4118: {}, 4119: {}, 4120: {}, 4121: {}, 4122: {}, 4123: {}, 4124: {}, 4125: {}, 4126: {}, 4127: {}, 4128: {}, 4129: {}, 4130: {}, 4132: {}, 4133: {}, 4134: { f: qr }, 4135: {}, 4146: {}, 4147: {}, 4148: {}, 4149: {}, 4154: {}, 4156: {}, 4157: {}, 4158: {}, 4159: {}, 4160: {}, 4161: {}, 4163: {}, 4164: { f: function(e, t, n) { var r = { area: !1 }; if (5 != n.biff) return e.l += t, r; var a = e.read_shift(1); return e.l += 3, 16 & a && (r.area = !0), r } }, 4165: {}, 4166: {}, 4168: {}, 4170: {}, 4171: {}, 4174: {}, 4175: {}, 4176: {}, 4177: {}, 4187: {}, 4188: { f: function(e) { for (var t = e.read_shift(2), n = []; t-- > 0;) n.push(ta(e)); return n } }, 4189: {}, 4191: {}, 4192: {}, 4193: {}, 4194: {}, 4195: {}, 4196: {}, 4197: {}, 4198: {}, 4199: {}, 4200: {}, 0: { f: pa }, 1: {}, 2: { f: function(e) { var t = na(e);++e.l; var n = e.read_shift(2); return t.t = "n", t.val = n, t } }, 3: { f: function(e) { var t = na(e);++e.l; var n = nr(e); return t.t = "n", t.val = n, t } }, 4: { f: function(e, t, n) { n.biffguess && 5 == n.biff && (n.biff = 2); var r = na(e);++e.l; var a = $r(e, 0, n); return r.t = "str", r.val = a, r } }, 5: { f: fa }, 7: { f: function(e) { var t = e.read_shift(1); return 0 === t ? (e.l++, "") : e.read_shift(t, "sbcs-cont") } }, 8: {}, 9: { f: ua }, 11: {}, 22: { f: qr }, 30: { f: ma }, 31: {}, 32: {}, 33: { f: za }, 36: {}, 37: { f: ha }, 50: { f: function(e, t) { e.l += 6, e.l += 2, e.l += 1, e.l += 3, e.l += 1, e.l += t - 13 } }, 62: {}, 52: {}, 67: {}, 68: { f: qr }, 69: {}, 86: {}, 126: {}, 127: { f: function(e) { var t = e.read_shift(2),
                                    n = e.read_shift(2),
                                    r = e.read_shift(4),
                                    a = { fmt: t, env: n, len: r, data: e.slice(e.l, e.l + r) }; return e.l += r, a } }, 135: {}, 136: {}, 137: {}, 145: {}, 148: {}, 149: {}, 150: {}, 169: {}, 171: {}, 188: {}, 191: {}, 192: {}, 194: {}, 195: {}, 214: { f: function(e, t, n) { var r = e.l + t,
                                    a = na(e),
                                    o = e.read_shift(2),
                                    i = Yr(e, o, n); return e.l = r, a.t = "str", a.val = i, a } }, 223: {}, 234: {}, 354: {}, 421: {}, 518: { f: wi }, 521: { f: ua }, 536: { f: ba }, 547: { f: ga }, 561: {}, 579: {}, 1030: { f: wi }, 1033: { f: ua }, 1091: {}, 2157: {}, 2163: {}, 2177: {}, 2240: {}, 2241: {}, 2242: {}, 2243: {}, 2244: {}, 2245: {}, 2246: {}, 2247: {}, 2248: {}, 2249: {}, 2250: {}, 2251: {}, 2262: { r: 12 }, 29282: {} };

                function Bl(e, t, n, r) { var a = t; if (!isNaN(a)) { var o = r || (n || []).length || 0,
                            i = e.next(4);
                        i.write_shift(2, a), i.write_shift(2, o), o > 0 && on(n) && e.push(n) } }

                function Wl(e, t) { var n = t || {};
                    null != g && null == n.dense && (n.dense = g); var r = n.dense ? [] : {},
                        a = (e = e.replace(/<!--.*?-->/g, "")).match(/<table/i); if (!a) throw new Error("Invalid HTML: could not find <table>"); var o = e.match(/<\/table/i),
                        i = a.index,
                        l = o && o.index || e.length,
                        s = Ge(e.slice(i, l), /(:?<tr[^>]*>)/i, "<tr>"),
                        c = -1,
                        d = 0,
                        u = 0,
                        h = 0,
                        m = { s: { r: 1e7, c: 1e7 }, e: { r: 0, c: 0 } },
                        p = []; for (i = 0; i < s.length; ++i) { var f = s[i].trim(),
                            v = f.slice(0, 3).toLowerCase(); if ("<tr" != v) { if ("<td" == v || "<th" == v) { var y = f.split(/<\/t[dh]>/i); for (l = 0; l < y.length; ++l) { var b = y[l].trim(); if (b.match(/<t[dh]/i)) { for (var w = b, z = 0;
                                            "<" == w.charAt(0) && (z = w.indexOf(">")) > -1;) w = w.slice(z + 1); for (var x = 0; x < p.length; ++x) { var A = p[x];
                                            A.s.c == d && A.s.r < c && c <= A.e.r && (d = A.e.c + 1, x = -1) } var k = dt(b.slice(0, b.indexOf(">")));
                                        h = k.colspan ? +k.colspan : 1, ((u = +k.rowspan) > 1 || h > 1) && p.push({ s: { r: c, c: d }, e: { r: c + (u || 1) - 1, c: d + h - 1 } }); var S = k.t || k["data-t"] || ""; if (w.length)
                                            if (w = Et(w), m.s.r > c && (m.s.r = c), m.e.r < c && (m.e.r = c), m.s.c > d && (m.s.c = d), m.e.c < d && (m.e.c = d), w.length) { var M = { t: "s", v: w };
                                                n.raw || !w.trim().length || "s" == S || ("TRUE" === w ? M = { t: "b", v: !0 } : "FALSE" === w ? M = { t: "b", v: !1 } : isNaN(We(w)) ? isNaN(qe(w).getDate()) || (M = { t: "d", v: Fe(w) }, n.cellDates || (M = { t: "n", v: He(M.v) }), M.z = n.dateNF || N[14]) : M = { t: "n", v: We(w) }), n.dense ? (r[c] || (r[c] = []), r[c][d] = M) : r[Vn({ r: c, c: d })] = M, d += h } else d += h;
                                        else d += h } } } } else { if (++c, n.sheetRows && n.sheetRows <= c) {--c; break } d = 0 } } return r["!ref"] = Rn(m), p.length && (r["!merges"] = p), r }

                function Ul(e, t, n, r) { for (var a = e["!merges"] || [], o = [], i = t.s.c; i <= t.e.c; ++i) { for (var l = 0, s = 0, c = 0; c < a.length; ++c)
                            if (!(a[c].s.r > n || a[c].s.c > i) && !(a[c].e.r < n || a[c].e.c < i)) { if (a[c].s.r < n || a[c].s.c < i) { l = -1; break } l = a[c].e.r - a[c].s.r + 1, s = a[c].e.c - a[c].s.c + 1; break } if (!(l < 0)) { var d = Vn({ r: n, c: i }),
                                u = r.dense ? (e[n] || [])[i] : e[d],
                                h = u && null != u.v && (u.h || gt(u.w || (Fn(u), u.w) || "")) || "",
                                m = {};
                            l > 1 && (m.rowspan = l), s > 1 && (m.colspan = s), r.editable ? h = '<span contenteditable="true">' + h + "</span>" : u && (m["data-t"] = u && u.t || "z", null != u.v && (m["data-v"] = u.v), null != u.z && (m["data-z"] = u.z), u.l && "#" != (u.l.Target || "#").charAt(0) && (h = '<a href="' + u.l.Target + '">' + h + "</a>")), m.id = (r.id || "sjs") + "-" + d, o.push(Vt("td", h, m)) } } return "<tr>" + o.join("") + "</tr>" } var ql = '<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',
                    Gl = "</body></html>";

                function Kl(e, t, n) { return [].join("") + "<table" + (n && n.id ? ' id="' + n.id + '"' : "") + ">" }

                function Zl(e, t) { var n = t || {},
                        r = null != n.header ? n.header : ql,
                        a = null != n.footer ? n.footer : Gl,
                        o = [r],
                        i = On(e["!ref"]);
                    n.dense = Array.isArray(e), o.push(Kl(0, 0, n)); for (var l = i.s.r; l <= i.e.r; ++l) o.push(Ul(e, i, l, n)); return o.push("</table>" + a), o.join("") }

                function Yl(e, t, n) { var r = n || {};
                    null != g && (r.dense = g); var a = 0,
                        o = 0; if (null != r.origin)
                        if ("number" == typeof r.origin) a = r.origin;
                        else { var i = "string" == typeof r.origin ? jn(r.origin) : r.origin;
                            a = i.r, o = i.c } var l = t.getElementsByTagName("tr"),
                        s = Math.min(r.sheetRows || 1e7, l.length),
                        c = { s: { r: 0, c: 0 }, e: { r: a, c: o } }; if (e["!ref"]) { var d = On(e["!ref"]);
                        c.s.r = Math.min(c.s.r, d.s.r), c.s.c = Math.min(c.s.c, d.s.c), c.e.r = Math.max(c.e.r, d.e.r), c.e.c = Math.max(c.e.c, d.e.c), -1 == a && (c.e.r = a = d.e.r + 1) } var u = [],
                        h = 0,
                        m = e["!rows"] || (e["!rows"] = []),
                        p = 0,
                        f = 0,
                        v = 0,
                        y = 0,
                        b = 0,
                        w = 0; for (e["!cols"] || (e["!cols"] = []); p < l.length && f < s; ++p) { var z = l[p]; if ($l(z)) { if (r.display) continue;
                            m[f] = { hidden: !0 } } var x = z.children; for (v = y = 0; v < x.length; ++v) { var A = x[v]; if (!r.display || !$l(A)) { var k = A.hasAttribute("data-v") ? A.getAttribute("data-v") : A.hasAttribute("v") ? A.getAttribute("v") : Et(A.innerHTML),
                                    S = A.getAttribute("data-z") || A.getAttribute("z"); for (h = 0; h < u.length; ++h) { var M = u[h];
                                    M.s.c == y + o && M.s.r < f + a && f + a <= M.e.r && (y = M.e.c + 1 - o, h = -1) } w = +A.getAttribute("colspan") || 1, ((b = +A.getAttribute("rowspan") || 1) > 1 || w > 1) && u.push({ s: { r: f + a, c: y + o }, e: { r: f + a + (b || 1) - 1, c: y + o + (w || 1) - 1 } }); var E = { t: "s", v: k },
                                    C = A.getAttribute("data-t") || A.getAttribute("t") || "";
                                null != k && (0 == k.length ? E.t = C || "z" : r.raw || 0 == k.trim().length || "s" == C || ("TRUE" === k ? E = { t: "b", v: !0 } : "FALSE" === k ? E = { t: "b", v: !1 } : isNaN(We(k)) ? isNaN(qe(k).getDate()) || (E = { t: "d", v: Fe(k) }, r.cellDates || (E = { t: "n", v: He(E.v) }), E.z = r.dateNF || N[14]) : E = { t: "n", v: We(k) })), void 0 === E.z && null != S && (E.z = S); var T = "",
                                    H = A.getElementsByTagName("A"); if (H && H.length)
                                    for (var L = 0; L < H.length && (!H[L].hasAttribute("href") || "#" == (T = H[L].getAttribute("href")).charAt(0)); ++L);
                                T && "#" != T.charAt(0) && (E.l = { Target: T }), r.dense ? (e[f + a] || (e[f + a] = []), e[f + a][y + o] = E) : e[Vn({ c: y + o, r: f + a })] = E, c.e.c < y + o && (c.e.c = y + o), y += w } }++f } return u.length && (e["!merges"] = (e["!merges"] || []).concat(u)), c.e.r = Math.max(c.e.r, f - 1 + a), e["!ref"] = Rn(c), f >= s && (e["!fullref"] = Rn((c.e.r = l.length - p + f - 1 + a, c))), e }

                function Xl(e, t) { return Yl((t || {}).dense ? [] : {}, e, t) }

                function $l(e) { var t = "",
                        n = function(e) { return e.ownerDocument.defaultView && "function" === typeof e.ownerDocument.defaultView.getComputedStyle ? e.ownerDocument.defaultView.getComputedStyle : "function" === typeof getComputedStyle ? getComputedStyle : null }(e); return n && (t = n(e).getPropertyValue("display")), t || (t = e.style && e.style.display), "none" === t }

                function Ql(e) { var t = e.replace(/[\t\r\n]/g, " ").trim().replace(/ +/g, " ").replace(/<text:s\/>/g, " ").replace(/<text:s text:c="(\d+)"\/>/g, (function(e, t) { return Array(parseInt(t, 10) + 1).join(" ") })).replace(/<text:tab[^>]*\/>/g, "\t").replace(/<text:line-break\/>/g, "\n"); return [pt(t.replace(/<[^>]*>/g, ""))] } var Jl = { day: ["d", "dd"], month: ["m", "mm"], year: ["y", "yy"], hours: ["h", "hh"], minutes: ["m", "mm"], seconds: ["s", "ss"], "am-pm": ["A/P", "AM/PM"], "day-of-week": ["ddd", "dddd"], era: ["e", "ee"], quarter: ["\\Qm", 'm\\"th quarter"'] };

                function es(e, t) { var n = t || {};
                    null != g && null == n.dense && (n.dense = g); var r, a, o, i, l, s, c = Ot(e),
                        d = [],
                        u = { name: "" },
                        h = "",
                        m = 0,
                        p = {},
                        f = [],
                        v = n.dense ? [] : {},
                        y = { value: "" },
                        b = "",
                        w = 0,
                        z = [],
                        x = -1,
                        A = -1,
                        k = { s: { r: 1e6, c: 1e7 }, e: { r: 0, c: 0 } },
                        S = 0,
                        M = {},
                        E = [],
                        C = {},
                        T = [],
                        H = 1,
                        L = 1,
                        I = [],
                        j = { Names: [] },
                        V = {},
                        O = ["", ""],
                        R = [],
                        P = {},
                        D = "",
                        F = 0,
                        N = !1,
                        _ = !1,
                        B = 0; for (Rt.lastIndex = 0, c = c.replace(/<!--([\s\S]*?)-->/gm, "").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm, ""); l = Rt.exec(c);) switch (l[3] = l[3].replace(/_.*$/, "")) {
                        case "table":
                        case "\u5de5\u4f5c\u8868":
                            "/" === l[1] ? (k.e.c >= k.s.c && k.e.r >= k.s.r ? v["!ref"] = Rn(k) : v["!ref"] = "A1:A1", n.sheetRows > 0 && n.sheetRows <= k.e.r && (v["!fullref"] = v["!ref"], k.e.r = n.sheetRows - 1, v["!ref"] = Rn(k)), E.length && (v["!merges"] = E), T.length && (v["!rows"] = T), o.name = o["\u540d\u79f0"] || o.name, "undefined" !== typeof JSON && JSON.stringify(o), f.push(o.name), p[o.name] = v, _ = !1) : "/" !== l[0].charAt(l[0].length - 2) && (o = dt(l[0], !1), x = A = -1, k.s.r = k.s.c = 1e7, k.e.r = k.e.c = 0, v = n.dense ? [] : {}, E = [], T = [], _ = !0); break;
                        case "table-row-group":
                            "/" === l[1] ? --S : ++S; break;
                        case "table-row":
                        case "\u884c":
                            if ("/" === l[1]) { x += H, H = 1; break } if ((i = dt(l[0], !1))["\u884c\u53f7"] ? x = i["\u884c\u53f7"] - 1 : -1 == x && (x = 0), (H = +i["number-rows-repeated"] || 1) < 10)
                                for (B = 0; B < H; ++B) S > 0 && (T[x + B] = { level: S });
                            A = -1; break;
                        case "covered-table-cell":
                            "/" !== l[1] && ++A, n.sheetStubs && (n.dense ? (v[x] || (v[x] = []), v[x][A] = { t: "z" }) : v[Vn({ r: x, c: A })] = { t: "z" }), b = "", z = []; break;
                        case "table-cell":
                        case "\u6570\u636e":
                            if ("/" === l[0].charAt(l[0].length - 2)) ++A, y = dt(l[0], !1), L = parseInt(y["number-columns-repeated"] || "1", 10), s = { t: "z", v: null }, y.formula && 0 != n.cellFormula && (s.f = Ti(pt(y.formula))), "string" == (y["\u6570\u636e\u7c7b\u578b"] || y["value-type"]) && (s.t = "s", s.v = pt(y["string-value"] || ""), n.dense ? (v[x] || (v[x] = []), v[x][A] = s) : v[Vn({ r: x, c: A })] = s), A += L - 1;
                            else if ("/" !== l[1]) { b = "", w = 0, z = [], L = 1; var W = H ? x + H - 1 : x; if (++A > k.e.c && (k.e.c = A), A < k.s.c && (k.s.c = A), x < k.s.r && (k.s.r = x), W > k.e.r && (k.e.r = W), R = [], P = {}, s = { t: (y = dt(l[0], !1))["\u6570\u636e\u7c7b\u578b"] || y["value-type"], v: null }, n.cellFormula)
                                    if (y.formula && (y.formula = pt(y.formula)), y["number-matrix-columns-spanned"] && y["number-matrix-rows-spanned"] && (C = { s: { r: x, c: A }, e: { r: x + (parseInt(y["number-matrix-rows-spanned"], 10) || 0) - 1, c: A + (parseInt(y["number-matrix-columns-spanned"], 10) || 0) - 1 } }, s.F = Rn(C), I.push([C, s.F])), y.formula) s.f = Ti(y.formula);
                                    else
                                        for (B = 0; B < I.length; ++B) x >= I[B][0].s.r && x <= I[B][0].e.r && A >= I[B][0].s.c && A <= I[B][0].e.c && (s.F = I[B][1]); switch ((y["number-columns-spanned"] || y["number-rows-spanned"]) && (C = { s: { r: x, c: A }, e: { r: x + (parseInt(y["number-rows-spanned"], 10) || 0) - 1, c: A + (parseInt(y["number-columns-spanned"], 10) || 0) - 1 } }, E.push(C)), y["number-columns-repeated"] && (L = parseInt(y["number-columns-repeated"], 10)), s.t) {
                                    case "boolean":
                                        s.t = "b", s.v = bt(y["boolean-value"]); break;
                                    case "float":
                                    case "percentage":
                                    case "currency":
                                        s.t = "n", s.v = parseFloat(y.value); break;
                                    case "date":
                                        s.t = "d", s.v = Fe(y["date-value"]), n.cellDates || (s.t = "n", s.v = He(s.v)), s.z = "m/d/yy"; break;
                                    case "time":
                                        s.t = "n", s.v = Oe(y["time-value"]) / 86400, n.cellDates && (s.t = "d", s.v = Ve(s.v)), s.z = "HH:MM:SS"; break;
                                    case "number":
                                        s.t = "n", s.v = parseFloat(y["\u6570\u636e\u6570\u503c"]); break;
                                    default:
                                        if ("string" !== s.t && "text" !== s.t && s.t) throw new Error("Unsupported value type " + s.t);
                                        s.t = "s", null != y["string-value"] && (b = pt(y["string-value"]), z = []) } } else { if (N = !1, "s" === s.t && (s.v = b || "", z.length && (s.R = z), N = 0 == w), V.Target && (s.l = V), R.length > 0 && (s.c = R, R = []), b && !1 !== n.cellText && (s.w = b), N && (s.t = "z", delete s.v), (!N || n.sheetStubs) && !(n.sheetRows && n.sheetRows <= x))
                                    for (var U = 0; U < H; ++U) { if (L = parseInt(y["number-columns-repeated"] || "1", 10), n.dense)
                                            for (v[x + U] || (v[x + U] = []), v[x + U][A] = 0 == U ? s : _e(s); --L > 0;) v[x + U][A + L] = _e(s);
                                        else
                                            for (v[Vn({ r: x + U, c: A })] = s; --L > 0;) v[Vn({ r: x + U, c: A + L })] = _e(s);
                                        k.e.c <= A && (k.e.c = A) } A += (L = parseInt(y["number-columns-repeated"] || "1", 10)) - 1, L = 0, s = {}, b = "", z = [] } V = {}; break;
                        case "document":
                        case "document-content":
                        case "\u7535\u5b50\u8868\u683c\u6587\u6863":
                        case "spreadsheet":
                        case "\u4e3b\u4f53":
                        case "scripts":
                        case "styles":
                        case "font-face-decls":
                        case "master-styles":
                            if ("/" === l[1]) { if ((r = d.pop())[0] !== l[3]) throw "Bad state: " + r } else "/" !== l[0].charAt(l[0].length - 2) && d.push([l[3], !0]); break;
                        case "annotation":
                            if ("/" === l[1]) { if ((r = d.pop())[0] !== l[3]) throw "Bad state: " + r;
                                P.t = b, z.length && (P.R = z), P.a = D, R.push(P) } else "/" !== l[0].charAt(l[0].length - 2) && d.push([l[3], !1]);
                            D = "", F = 0, b = "", w = 0, z = []; break;
                        case "creator":
                            "/" === l[1] ? D = c.slice(F, l.index) : F = l.index + l[0].length; break;
                        case "meta":
                        case "\u5143\u6570\u636e":
                        case "settings":
                        case "config-item-set":
                        case "config-item-map-indexed":
                        case "config-item-map-entry":
                        case "config-item-map-named":
                        case "shapes":
                        case "frame":
                        case "text-box":
                        case "image":
                        case "data-pilot-tables":
                        case "list-style":
                        case "form":
                        case "dde-links":
                        case "event-listeners":
                        case "chart":
                            if ("/" === l[1]) { if ((r = d.pop())[0] !== l[3]) throw "Bad state: " + r } else "/" !== l[0].charAt(l[0].length - 2) && d.push([l[3], !1]);
                            b = "", w = 0, z = []; break;
                        case "scientific-number":
                        case "currency-symbol":
                        case "currency-style":
                        case "script":
                        case "libraries":
                        case "automatic-styles":
                        case "default-style":
                        case "page-layout":
                        case "style":
                        case "map":
                        case "font-face":
                        case "paragraph-properties":
                        case "table-properties":
                        case "table-column-properties":
                        case "table-row-properties":
                        case "table-cell-properties":
                        case "fraction":
                        case "boolean-style":
                        case "boolean":
                        case "text-style":
                        case "text-content":
                        case "text-properties":
                        case "embedded-text":
                        case "body":
                        case "\u7535\u5b50\u8868\u683c":
                        case "forms":
                        case "table-column":
                        case "table-header-rows":
                        case "table-rows":
                        case "table-column-group":
                        case "table-header-columns":
                        case "table-columns":
                        case "null-date":
                        case "graphic-properties":
                        case "calculation-settings":
                        case "named-expressions":
                        case "label-range":
                        case "label-ranges":
                        case "named-expression":
                        case "sort":
                        case "sort-by":
                        case "sort-groups":
                        case "tab":
                        case "line-break":
                        case "span":
                        case "s":
                        case "date":
                        case "object":
                        case "title":
                        case "\u6807\u9898":
                        case "desc":
                        case "binary-data":
                        case "table-source":
                        case "scenario":
                        case "iteration":
                        case "content-validations":
                        case "content-validation":
                        case "help-message":
                        case "error-message":
                        case "database-ranges":
                        case "filter":
                        case "filter-and":
                        case "filter-or":
                        case "filter-condition":
                        case "list-level-style-bullet":
                        case "list-level-style-number":
                        case "list-level-properties":
                        case "sender-firstname":
                        case "sender-lastname":
                        case "sender-initials":
                        case "sender-title":
                        case "sender-position":
                        case "sender-email":
                        case "sender-phone-private":
                        case "sender-fax":
                        case "sender-company":
                        case "sender-phone-work":
                        case "sender-street":
                        case "sender-city":
                        case "sender-postal-code":
                        case "sender-country":
                        case "sender-state-or-province":
                        case "author-name":
                        case "author-initials":
                        case "chapter":
                        case "file-name":
                        case "template-name":
                        case "sheet-name":
                        case "event-listener":
                        case "initial-creator":
                        case "creation-date":
                        case "print-date":
                        case "generator":
                        case "document-statistic":
                        case "user-defined":
                        case "editing-duration":
                        case "editing-cycles":
                        case "config-item":
                        case "page-number":
                        case "page-count":
                        case "time":
                        case "cell-range-source":
                        case "detective":
                        case "operation":
                        case "highlighted-range":
                        case "data-pilot-table":
                        case "source-cell-range":
                        case "source-service":
                        case "data-pilot-field":
                        case "data-pilot-level":
                        case "data-pilot-subtotals":
                        case "data-pilot-subtotal":
                        case "data-pilot-members":
                        case "data-pilot-member":
                        case "data-pilot-display-info":
                        case "data-pilot-sort-info":
                        case "data-pilot-layout-info":
                        case "data-pilot-field-reference":
                        case "data-pilot-groups":
                        case "data-pilot-group":
                        case "data-pilot-group-member":
                        case "rect":
                        case "dde-connection-decls":
                        case "dde-connection-decl":
                        case "dde-link":
                        case "dde-source":
                        case "properties":
                        case "property":
                        case "table-protection":
                        case "data-pilot-grand-total":
                        case "office-document-common-attrs":
                            break;
                        case "number-style":
                        case "percentage-style":
                        case "date-style":
                        case "time-style":
                            if ("/" === l[1]) { if (M[u.name] = h, (r = d.pop())[0] !== l[3]) throw "Bad state: " + r } else "/" !== l[0].charAt(l[0].length - 2) && (h = "", u = dt(l[0], !1), d.push([l[3], !0])); break;
                        case "number":
                        case "day":
                        case "month":
                        case "year":
                        case "era":
                        case "day-of-week":
                        case "week-of-year":
                        case "quarter":
                        case "hours":
                        case "minutes":
                        case "seconds":
                        case "am-pm":
                            switch (d[d.length - 1][0]) {
                                case "time-style":
                                case "date-style":
                                    a = dt(l[0], !1), h += Jl[l[3]]["long" === a.style ? 1 : 0] } break;
                        case "text":
                            if ("/>" === l[0].slice(-2)) break; if ("/" === l[1]) switch (d[d.length - 1][0]) {
                                case "number-style":
                                case "date-style":
                                case "time-style":
                                    h += c.slice(m, l.index) } else m = l.index + l[0].length; break;
                        case "named-range":
                            O = Hi((a = dt(l[0], !1))["cell-range-address"]); var q = { Name: a.name, Ref: O[0] + "!" + O[1] };
                            _ && (q.Sheet = f.length), j.Names.push(q); break;
                        case "p":
                        case "\u6587\u672c\u4e32":
                            if (["master-styles"].indexOf(d[d.length - 1][0]) > -1) break; if ("/" !== l[1] || y && y["string-value"]) dt(l[0], !1), w = l.index + l[0].length;
                            else { var G = Ql(c.slice(w, l.index));
                                b = (b.length > 0 ? b + "\n" : "") + G[0] } break;
                        case "database-range":
                            if ("/" === l[1]) break; try { p[(O = Hi(dt(l[0])["target-range-address"]))[0]]["!autofilter"] = { ref: O[1] } } catch (Z) {} break;
                        case "a":
                            if ("/" !== l[1]) { if (!(V = dt(l[0], !1)).href) break;
                                V.Target = pt(V.href), delete V.href, "#" == V.Target.charAt(0) && V.Target.indexOf(".") > -1 ? (O = Hi(V.Target.slice(1)), V.Target = "#" + O[0] + "!" + O[1]) : V.Target.match(/^\.\.[\\\/]/) && (V.Target = V.Target.slice(3)) } break;
                        default:
                            switch (l[2]) {
                                case "dc:":
                                case "calcext:":
                                case "loext:":
                                case "ooo:":
                                case "chartooo:":
                                case "draw:":
                                case "style:":
                                case "chart:":
                                case "form:":
                                case "uof:":
                                case "\u8868:":
                                case "\u5b57:":
                                    break;
                                default:
                                    if (n.WTF) throw new Error(l) } }
                    var K = { Sheets: p, SheetNames: f, Workbook: j }; return n.bookSheets && delete K.Sheets, K }

                function ts(e, t) { t = t || {}, Ye(e, "META-INF/manifest.xml") && function(e, t) { for (var n, r, a = Ot(e); n = Rt.exec(a);) switch (n[3]) {
                            case "manifest":
                                break;
                            case "file-entry":
                                if ("/" == (r = dt(n[0], !1)).path && r.type !== xr) throw new Error("This OpenDocument is not a spreadsheet"); break;
                            case "encryption-data":
                            case "algorithm":
                            case "start-key-generation":
                            case "key-derivation":
                                throw new Error("Unsupported ODS Encryption");
                            default:
                                if (t && t.WTF) throw n } }($e(e, "META-INF/manifest.xml"), t); var n = Qe(e, "content.xml"); if (!n) throw new Error("Missing content.xml in ODS / UOF file"); var r = es(kt(n), t); return Ye(e, "meta.xml") && (r.Props = Sr($e(e, "meta.xml"))), r }

                function ns(e, t) { return es(e, t) }

                function rs(e) { return new DataView(e.buffer, e.byteOffset, e.byteLength) }

                function as(e) { return "undefined" != typeof TextDecoder ? (new TextDecoder).decode(e) : kt(M(e)) }

                function os(e) { var t = e.reduce((function(e, t) { return e + t.length }), 0),
                        n = new Uint8Array(t),
                        r = 0; return e.forEach((function(e) { n.set(e, r), r += e.length })), n }

                function is(e) { return 16843009 * ((e = (858993459 & (e -= e >> 1 & 1431655765)) + (e >> 2 & 858993459)) + (e >> 4) & 252645135) >>> 24 }

                function ls(e, t) { var n = t ? t[0] : 0,
                        r = 127 & e[n];
                    e: if (e[n++] >= 128) { if (r |= (127 & e[n]) << 7, e[n++] < 128) break e; if (r |= (127 & e[n]) << 14, e[n++] < 128) break e; if (r |= (127 & e[n]) << 21, e[n++] < 128) break e; if (r += (127 & e[n]) * Math.pow(2, 28), ++n, e[n++] < 128) break e; if (r += (127 & e[n]) * Math.pow(2, 35), ++n, e[n++] < 128) break e; if (r += (127 & e[n]) * Math.pow(2, 42), ++n, e[n++] < 128) break e } return t && (t[0] = n), r }

                function ss(e) { var t = 0,
                        n = 127 & e[t];
                    e: if (e[t++] >= 128) { if (n |= (127 & e[t]) << 7, e[t++] < 128) break e; if (n |= (127 & e[t]) << 14, e[t++] < 128) break e; if (n |= (127 & e[t]) << 21, e[t++] < 128) break e;
                        n |= (127 & e[t]) << 28 } return n }

                function cs(e) { for (var t = [], n = [0]; n[0] < e.length;) { var r, a = n[0],
                            o = ls(e, n),
                            i = 7 & o,
                            l = 0; if (0 == (o = Math.floor(o / 8))) break; switch (i) {
                            case 0:
                                for (var s = n[0]; e[n[0]++] >= 128;);
                                r = e.slice(s, n[0]); break;
                            case 5:
                                l = 4, r = e.slice(n[0], n[0] + l), n[0] += l; break;
                            case 1:
                                l = 8, r = e.slice(n[0], n[0] + l), n[0] += l; break;
                            case 2:
                                l = ls(e, n), r = e.slice(n[0], n[0] + l), n[0] += l; break;
                            default:
                                throw new Error("PB Type ".concat(i, " for Field ").concat(o, " at offset ").concat(a)) } var c = { data: r, type: i };
                        null == t[o] ? t[o] = [c] : t[o].push(c) } return t }

                function ds(e, t) { return (null == e ? void 0 : e.map((function(e) { return t(e.data) }))) || [] }

                function us(e) { for (var t, n = [], r = [0]; r[0] < e.length;) { var a = ls(e, r),
                            o = cs(e.slice(r[0], r[0] + a));
                        r[0] += a; var i = { id: ss(o[1][0].data), messages: [] };
                        o[2].forEach((function(t) { var n = cs(t.data),
                                a = ss(n[3][0].data);
                            i.messages.push({ meta: n, data: e.slice(r[0], r[0] + a) }), r[0] += a })), (null == (t = o[3]) ? void 0 : t[0]) && (i.merge = ss(o[3][0].data) >>> 0 > 0), n.push(i) } return n }

                function hs(e, t) { if (0 != e) throw new Error("Unexpected Snappy chunk type ".concat(e)); for (var n = [0], r = ls(t, n), a = []; n[0] < t.length;) { var o = 3 & t[n[0]]; if (0 != o) { var i = 0,
                                l = 0; if (1 == o ? (l = 4 + (t[n[0]] >> 2 & 7), i = (224 & t[n[0]++]) << 3, i |= t[n[0]++]) : (l = 1 + (t[n[0]++] >> 2), 2 == o ? (i = t[n[0]] | t[n[0] + 1] << 8, n[0] += 2) : (i = (t[n[0]] | t[n[0] + 1] << 8 | t[n[0] + 2] << 16 | t[n[0] + 3] << 24) >>> 0, n[0] += 4)), a = [os(a)], 0 == i) throw new Error("Invalid offset 0"); if (i > a[0].length) throw new Error("Invalid offset beyond length"); if (l >= i)
                                for (a.push(a[0].slice(-i)), l -= i; l >= a[a.length - 1].length;) a.push(a[a.length - 1]), l -= a[a.length - 1].length;
                            a.push(a[0].slice(-i, -i + l)) } else { var s = t[n[0]++] >> 2; if (s < 60) ++s;
                            else { var c = s - 59;
                                s = t[n[0]], c > 1 && (s |= t[n[0] + 1] << 8), c > 2 && (s |= t[n[0] + 2] << 16), c > 3 && (s |= t[n[0] + 3] << 24), s >>>= 0, s++, n[0] += c } a.push(t.slice(n[0], n[0] + s)), n[0] += s } } var d = os(a); if (d.length != r) throw new Error("Unexpected length: ".concat(d.length, " != ").concat(r)); return d }

                function ms(e) { for (var t = [], n = 0; n < e.length;) { var r = e[n++],
                            a = e[n] | e[n + 1] << 8 | e[n + 2] << 16;
                        n += 3, t.push(hs(r, e.slice(n, n + a))), n += a } if (n !== e.length) throw new Error("data is not a valid framed stream!"); return os(t) }

                function ps(e, t, n) { var r, a = rs(e),
                        o = a.getUint32(8, !0),
                        i = 12,
                        l = -1,
                        s = -1,
                        c = NaN,
                        d = NaN,
                        u = new Date(2001, 0, 1); switch (1 & o && (c = function(e, t) { for (var n = (127 & e[t + 15]) << 7 | e[t + 14] >> 1, r = 1 & e[t + 14], a = t + 13; a >= t; --a) r = 256 * r + e[a]; return (128 & e[t + 15] ? -r : r) * Math.pow(10, n - 6176) }(e, i), i += 16), 2 & o && (d = a.getFloat64(i, !0), i += 8), 4 & o && (u.setTime(u.getTime() + 1e3 * a.getFloat64(i, !0)), i += 8), 8 & o && (s = a.getUint32(i, !0), i += 4), 16 & o && (l = a.getUint32(i, !0), i += 4), e[1]) {
                        case 0:
                            break;
                        case 2:
                        case 10:
                            r = { t: "n", v: c }; break;
                        case 3:
                            r = { t: "s", v: t[s] }; break;
                        case 5:
                            r = { t: "d", v: u }; break;
                        case 6:
                            r = { t: "b", v: d > 0 }; break;
                        case 7:
                            r = { t: "n", v: d / 86400 }; break;
                        case 8:
                            r = { t: "e", v: 0 }; break;
                        case 9:
                            if (!(l > -1)) throw new Error("Unsupported cell type ".concat(e[1], " : ").concat(31 & o, " : ").concat(e.slice(0, 4)));
                            r = { t: "s", v: n[l] }; break;
                        default:
                            throw new Error("Unsupported cell type ".concat(e[1], " : ").concat(31 & o, " : ").concat(e.slice(0, 4))) } return r }

                function fs(e, t, n) { switch (e[0]) {
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                            return function(e, t, n, r) { var a, o = rs(e),
                                    i = o.getUint32(4, !0),
                                    l = (r > 1 ? 12 : 8) + 4 * is(i & (r > 1 ? 3470 : 398)),
                                    s = -1,
                                    c = -1,
                                    d = NaN,
                                    u = new Date(2001, 0, 1); switch (512 & i && (s = o.getUint32(l, !0), l += 4), l += 4 * is(i & (r > 1 ? 12288 : 4096)), 16 & i && (c = o.getUint32(l, !0), l += 4), 32 & i && (d = o.getFloat64(l, !0), l += 8), 64 & i && (u.setTime(u.getTime() + 1e3 * o.getFloat64(l, !0)), l += 8), e[2]) {
                                    case 0:
                                        break;
                                    case 2:
                                        a = { t: "n", v: d }; break;
                                    case 3:
                                        a = { t: "s", v: t[c] }; break;
                                    case 5:
                                        a = { t: "d", v: u }; break;
                                    case 6:
                                        a = { t: "b", v: d > 0 }; break;
                                    case 7:
                                        a = { t: "n", v: d / 86400 }; break;
                                    case 8:
                                        a = { t: "e", v: 0 }; break;
                                    case 9:
                                        if (s > -1) a = { t: "s", v: n[s] };
                                        else if (c > -1) a = { t: "s", v: t[c] };
                                        else { if (isNaN(d)) throw new Error("Unsupported cell type ".concat(e.slice(0, 4)));
                                            a = { t: "n", v: d } } break;
                                    default:
                                        throw new Error("Unsupported cell type ".concat(e.slice(0, 4))) } return a }(e, t, n, e[0]);
                        case 5:
                            return ps(e, t, n);
                        default:
                            throw new Error("Unsupported payload version ".concat(e[0])) } }

                function vs(e) { return ls(cs(e)[1][0].data) }

                function gs(e, t) { var n = cs(t.data),
                        r = ss(n[1][0].data),
                        a = n[3],
                        o = []; return (a || []).forEach((function(t) { var n = cs(t.data),
                            a = ss(n[1][0].data) >>> 0; switch (r) {
                            case 1:
                                o[a] = as(n[3][0].data); break;
                            case 8:
                                var i = cs(e[vs(n[9][0].data)][0].data),
                                    l = e[vs(i[1][0].data)][0],
                                    s = ss(l.meta[1][0].data); if (2001 != s) throw new Error("2000 unexpected reference to ".concat(s)); var c = cs(l.data);
                                o[a] = c[3].map((function(e) { return as(e.data) })).join("") } })), o }

                function ys(e, t) { var n, r = cs(t.data),
                        a = (null == (n = null == r ? void 0 : r[7]) ? void 0 : n[0]) ? ss(r[7][0].data) >>> 0 > 0 ? 1 : 0 : -1,
                        o = ds(r[5], (function(e) { return function(e, t) { var n, r, a, o, i, l, s, c, d, u, h, m, p, f, v, g, y = cs(e),
                                    b = ss(y[1][0].data) >>> 0,
                                    w = ss(y[2][0].data) >>> 0,
                                    z = (null == (r = null == (n = y[8]) ? void 0 : n[0]) ? void 0 : r.data) && ss(y[8][0].data) > 0 || !1; if ((null == (o = null == (a = y[7]) ? void 0 : a[0]) ? void 0 : o.data) && 0 != t) v = null == (l = null == (i = y[7]) ? void 0 : i[0]) ? void 0 : l.data, g = null == (c = null == (s = y[6]) ? void 0 : s[0]) ? void 0 : c.data;
                                else { if (!(null == (u = null == (d = y[4]) ? void 0 : d[0]) ? void 0 : u.data) || 1 == t) throw "NUMBERS Tile missing ".concat(t, " cell storage");
                                    v = null == (m = null == (h = y[4]) ? void 0 : h[0]) ? void 0 : m.data, g = null == (f = null == (p = y[3]) ? void 0 : p[0]) ? void 0 : f.data } for (var x = z ? 4 : 1, A = rs(v), k = [], S = 0; S < v.length / 2; ++S) { var M = A.getUint16(2 * S, !0);
                                    M < 65535 && k.push([S, M]) } if (k.length != w) throw "Expected ".concat(w, " cells, found ").concat(k.length); var E = []; for (S = 0; S < k.length - 1; ++S) E[k[S][0]] = g.subarray(k[S][1] * x, k[S + 1][1] * x); return k.length >= 1 && (E[k[k.length - 1][0]] = g.subarray(k[k.length - 1][1] * x)), { R: b, cells: E } }(e, a) })); return { nrows: ss(r[4][0].data) >>> 0, data: o.reduce((function(e, t) { return e[t.R] || (e[t.R] = []), t.cells.forEach((function(n, r) { if (e[t.R][r]) throw new Error("Duplicate cell r=".concat(t.R, " c=").concat(r));
                                e[t.R][r] = n })), e }), []) } }

                function bs(e, t) { var n = { "!ref": "A1" },
                        r = e[vs(cs(t.data)[2][0].data)],
                        a = ss(r[0].meta[1][0].data); if (6001 != a) throw new Error("6000 unexpected reference to ".concat(a)); return function(e, t, n) { var r, a = cs(t.data),
                            o = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }; if (o.e.r = (ss(a[6][0].data) >>> 0) - 1, o.e.r < 0) throw new Error("Invalid row varint ".concat(a[6][0].data)); if (o.e.c = (ss(a[7][0].data) >>> 0) - 1, o.e.c < 0) throw new Error("Invalid col varint ".concat(a[7][0].data));
                        n["!ref"] = Rn(o); var i = cs(a[4][0].data),
                            l = gs(e, e[vs(i[4][0].data)][0]),
                            s = (null == (r = i[17]) ? void 0 : r[0]) ? gs(e, e[vs(i[17][0].data)][0]) : [],
                            c = cs(i[3][0].data),
                            d = 0;
                        c[1].forEach((function(t) { var r = cs(t.data),
                                a = e[vs(r[2][0].data)][0],
                                o = ss(a.meta[1][0].data); if (6002 != o) throw new Error("6001 unexpected reference to ".concat(o)); var i = ys(0, a);
                            i.data.forEach((function(e, t) { e.forEach((function(e, r) { var a = Vn({ r: d + t, c: r }),
                                        o = fs(e, l, s);
                                    o && (n[a] = o) })) })), d += i.nrows })) }(e, r[0], n), n }

                function ws(e, t) { var n = { SheetNames: [], Sheets: {} }; if (ds(cs(t.data)[1], vs).forEach((function(t) { e[t].forEach((function(t) { if (2 == ss(t.meta[1][0].data)) { var r = function(e, t) { var n, r = cs(t.data),
                                            a = { name: (null == (n = r[1]) ? void 0 : n[0]) ? as(r[1][0].data) : "", sheets: [] }; return ds(r[2], vs).forEach((function(t) { e[t].forEach((function(t) { 6e3 == ss(t.meta[1][0].data) && a.sheets.push(bs(e, t)) })) })), a }(e, t);
                                    r.sheets.forEach((function(e, t) { _s(n, e, 0 == t ? r.name : r.name + "_" + t, !0) })) } })) })), 0 == n.SheetNames.length) throw new Error("Empty NUMBERS file"); return n }

                function zs(e) { var t, n, r, a, o = {},
                        i = []; if (e.FullPaths.forEach((function(e) { if (e.match(/\.iwpv2/)) throw new Error("Unsupported password protection") })), e.FileIndex.forEach((function(e) { if (e.name.match(/\.iwa$/)) { var t, n; try { t = ms(e.content) } catch (r) { return console.log("?? " + e.content.length + " " + (r.message || r)) } try { n = us(t) } catch (r) { return console.log("## " + (r.message || r)) } n.forEach((function(e) { o[e.id] = e.messages, i.push(e.id) })) } })), !i.length) throw new Error("File has no messages"); var l = (null == (a = null == (r = null == (n = null == (t = null == o ? void 0 : o[1]) ? void 0 : t[0]) ? void 0 : n.meta) ? void 0 : r[1]) ? void 0 : a[0].data) && 1 == ss(o[1][0].meta[1][0].data) && o[1][0]; if (l || i.forEach((function(e) { o[e].forEach((function(e) { if (1 == ss(e.meta[1][0].data) >>> 0) { if (l) throw new Error("Document has multiple roots");
                                    l = e } })) })), !l) throw new Error("Cannot find Document root"); return ws(o, l) }

                function xs(e) { return function(t) { for (var n = 0; n != e.length; ++n) { var r = e[n];
                            void 0 === t[r[0]] && (t[r[0]] = r[1]), "n" === r[2] && (t[r[0]] = Number(t[r[0]])) } } }

                function As(e) { xs([
                        ["cellNF", !1],
                        ["cellHTML", !0],
                        ["cellFormula", !0],
                        ["cellStyles", !1],
                        ["cellText", !0],
                        ["cellDates", !1],
                        ["sheetStubs", !1],
                        ["sheetRows", 0, "n"],
                        ["bookDeps", !1],
                        ["bookSheets", !1],
                        ["bookProps", !1],
                        ["bookFiles", !1],
                        ["bookVBA", !1],
                        ["password", ""],
                        ["WTF", !1]
                    ])(e) }

                function ks(e, t, n, r, a, o, i, l, s, c, d, u) { try { o[r] = zr(Qe(e, n, !0), t); var h, m = $e(e, t); switch (l) {
                            case "sheet":
                                h = hl(m, t, a, s, o[r], c, d, u); break;
                            case "chart":
                                if (!(h = ml(m, t, a, s, o[r], c)) || !h["!drawel"]) break; var p = rt(h["!drawel"].Target, t),
                                    f = wr(p),
                                    v = function(e, t) { if (!e) return "??"; var n = (e.match(/<c:chart [^>]*r:id="([^"]*)"/) || ["", ""])[1]; return t["!id"][n].Target }(Qe(e, p, !0), zr(Qe(e, f, !0), p)),
                                    g = rt(v, p),
                                    y = wr(g);
                                h = Ji(Qe(e, g, !0), 0, 0, zr(Qe(e, y, !0), g), 0, h); break;
                            case "macro":
                                w = t, o[r], w.slice(-4), h = { "!type": "macro" }; break;
                            case "dialog":
                                h = function(e, t, n, r, a, o, i, l) { return t.slice(-4), { "!type": "dialog" } }(0, t, 0, 0, o[r]); break;
                            default:
                                throw new Error("Unrecognized sheet type " + l) } i[r] = h; var b = [];
                        o && o[r] && Ee(o[r]).forEach((function(n) { var a = ""; if (o[r][n].Type == br.CMNT) { a = rt(o[r][n].Target, t); var i = vl($e(e, a, !0), a, s); if (!i || !i.length) return;
                                Po(h, i, !1) } o[r][n].Type == br.TCMNT && (a = rt(o[r][n].Target, t), b = b.concat(function(e, t) { var n = [],
                                    r = !1,
                                    a = {},
                                    o = 0; return e.replace(lt, (function(i, l) { var s = dt(i); switch (ut(s[0])) {
                                        case "<?xml":
                                        case "<ThreadedComments":
                                        case "</ThreadedComments>":
                                        case "<extLst":
                                        case "<extLst>":
                                        case "</extLst>":
                                        case "<extLst/>":
                                            break;
                                        case "<threadedComment":
                                            a = { author: s.personId, guid: s.id, ref: s.ref, T: 1 }; break;
                                        case "</threadedComment>":
                                            null != a.t && n.push(a); break;
                                        case "<text>":
                                        case "<text":
                                            o = l + i.length; break;
                                        case "</text>":
                                            a.t = e.slice(o, l).replace(/\r\n/g, "\n").replace(/\r/g, "\n"); break;
                                        case "<mentions":
                                        case "<mentions>":
                                        case "<ext":
                                            r = !0; break;
                                        case "</mentions>":
                                        case "</ext>":
                                            r = !1; break;
                                        default:
                                            if (!r && t.WTF) throw new Error("unrecognized " + s[0] + " in threaded comments") } return i })), n }($e(e, a, !0), s))) })), b && b.length && Po(h, b, !0, s.people || []) } catch (z) { if (s.WTF) throw z } var w }

                function Ss(e) { return "/" == e.charAt(0) ? e.slice(1) : e }

                function Ms(e, t) { if (ze(), As(t = t || {}), Ye(e, "META-INF/manifest.xml")) return ts(e, t); if (Ye(e, "objectdata.xml")) return ts(e, t); if (Ye(e, "Index/Document.iwa")) { if ("undefined" == typeof Uint8Array) throw new Error("NUMBERS file parsing requires Uint8Array support"); if (e.FileIndex) return zs(e); var n = Se.utils.cfb_new(); return et(e).forEach((function(t) { tt(n, t, Je(e, t)) })), zs(n) } if (!Ye(e, "[Content_Types].xml")) { if (Ye(e, "index.xml.gz")) throw new Error("Unsupported NUMBERS 08 file"); if (Ye(e, "index.xml")) throw new Error("Unsupported NUMBERS 09 file"); throw new Error("Unsupported ZIP file") } var r, a, o = et(e),
                        i = function(e) { var t = { workbooks: [], sheets: [], charts: [], dialogs: [], macros: [], rels: [], strs: [], comments: [], threadedcomments: [], links: [], coreprops: [], extprops: [], custprops: [], themes: [], styles: [], calcchains: [], vba: [], drawings: [], metadata: [], people: [], TODO: [], xmlns: "" }; if (!e || !e.match) return t; var n = {}; if ((e.match(lt) || []).forEach((function(e) { var r = dt(e); switch (r[0].replace(st, "<")) {
                                        case "<?xml":
                                            break;
                                        case "<Types":
                                            t.xmlns = r["xmlns" + (r[0].match(/<(\w+):/) || ["", ""])[1]]; break;
                                        case "<Default":
                                            n[r.Extension] = r.ContentType; break;
                                        case "<Override":
                                            void 0 !== t[yr[r.ContentType]] && t[yr[r.ContentType]].push(r.PartName) } })), t.xmlns !== Pt.CT) throw new Error("Unknown Namespace: " + t.xmlns); return t.calcchain = t.calcchains.length > 0 ? t.calcchains[0] : "", t.sst = t.strs.length > 0 ? t.strs[0] : "", t.style = t.styles.length > 0 ? t.styles[0] : "", t.defaults = n, delete t.calcchains, t }(Qe(e, "[Content_Types].xml")),
                        l = !1; if (0 === i.workbooks.length && $e(e, a = "xl/workbook.xml", !0) && i.workbooks.push(a), 0 === i.workbooks.length) { if (!$e(e, a = "xl/workbook.bin", !0)) throw new Error("Could not find workbook");
                        i.workbooks.push(a), l = !0 } "bin" == i.workbooks[0].slice(-3) && (l = !0); var s = {},
                        c = {}; if (!t.bookSheets && !t.bookProps) { if (Li = [], i.sst) try { Li = fl($e(e, Ss(i.sst)), i.sst, t) } catch (H) { if (t.WTF) throw H } t.cellStyles && i.themes.length && (s = function(e, t, n) { return jo(e, n) }(Qe(e, i.themes[0].replace(/^\//, ""), !0) || "", i.themes[0], t)), i.style && (c = pl($e(e, Ss(i.style)), i.style, s, t)) } i.links.map((function(n) { try { zr(Qe(e, wr(Ss(n))), n); return yl($e(e, Ss(n)), 0, n, t) } catch (H) {} })); var d = ul($e(e, Ss(i.workbooks[0])), i.workbooks[0], t),
                        u = {},
                        h = "";
                    i.coreprops.length && ((h = $e(e, Ss(i.coreprops[0]), !0)) && (u = Sr(h)), 0 !== i.extprops.length && (h = $e(e, Ss(i.extprops[0]), !0)) && function(e, t, n) { var r = {};
                        t || (t = {}), e = kt(e), Mr.forEach((function(n) { var a = (e.match(Mt(n[0])) || [])[1]; switch (n[2]) {
                                case "string":
                                    a && (t[n[1]] = pt(a)); break;
                                case "bool":
                                    t[n[1]] = "true" === a; break;
                                case "raw":
                                    var o = e.match(new RegExp("<" + n[0] + "[^>]*>([\\s\\S]*?)</" + n[0] + ">"));
                                    o && o.length > 0 && (r[n[1]] = o[1]) } })), r.HeadingPairs && r.TitlesOfParts && Er(r.HeadingPairs, r.TitlesOfParts, t, n) }(h, u, t)); var m = {};
                    t.bookSheets && !t.bookProps || 0 !== i.custprops.length && (h = Qe(e, Ss(i.custprops[0]), !0)) && (m = function(e, t) { var n = {},
                            r = "",
                            a = e.match(Cr); if (a)
                            for (var o = 0; o != a.length; ++o) { var i = a[o],
                                    l = dt(i); switch (l[0]) {
                                    case "<?xml":
                                    case "<Properties":
                                        break;
                                    case "<property":
                                        r = pt(l.name); break;
                                    case "</property>":
                                        r = null; break;
                                    default:
                                        if (0 === i.indexOf("<vt:")) { var s = i.split(">"),
                                                c = s[0].slice(4),
                                                d = s[1]; switch (c) {
                                                case "lpstr":
                                                case "bstr":
                                                case "lpwstr":
                                                case "cy":
                                                case "error":
                                                    n[r] = pt(d); break;
                                                case "bool":
                                                    n[r] = bt(d); break;
                                                case "i1":
                                                case "i2":
                                                case "i4":
                                                case "i8":
                                                case "int":
                                                case "uint":
                                                    n[r] = parseInt(d, 10); break;
                                                case "r4":
                                                case "r8":
                                                case "decimal":
                                                    n[r] = parseFloat(d); break;
                                                case "filetime":
                                                case "date":
                                                    n[r] = Fe(d); break;
                                                default:
                                                    if ("/" == c.slice(-1)) break;
                                                    t.WTF && "undefined" !== typeof console && console.warn("Unexpected", i, c, s) } } else if ("</" === i.slice(0, 2));
                                        else if (t.WTF) throw new Error(i) } }
                        return n }(h, t)); var p = {}; if ((t.bookSheets || t.bookProps) && (d.Sheets ? r = d.Sheets.map((function(e) { return e.name })) : u.Worksheets && u.SheetNames.length > 0 && (r = u.SheetNames), t.bookProps && (p.Props = u, p.Custprops = m), t.bookSheets && "undefined" !== typeof r && (p.SheetNames = r), t.bookSheets ? p.SheetNames : t.bookProps)) return p;
                    r = {}; var f = {};
                    t.bookDeps && i.calcchain && (f = gl($e(e, Ss(i.calcchain)), i.calcchain)); var v, g, y = 0,
                        b = {},
                        w = d.Sheets;
                    u.Worksheets = w.length, u.SheetNames = []; for (var z = 0; z != w.length; ++z) u.SheetNames[z] = w[z].name; var x = l ? "bin" : "xml",
                        A = i.workbooks[0].lastIndexOf("/"),
                        k = (i.workbooks[0].slice(0, A + 1) + "_rels/" + i.workbooks[0].slice(A + 1) + ".rels").replace(/^\//, "");
                    Ye(e, k) || (k = "xl/_rels/workbook." + x + ".rels"); var S = zr(Qe(e, k, !0), k.replace(/_rels.*/, "s5s"));
                    (i.metadata || []).length >= 1 && (t.xlmeta = bl($e(e, Ss(i.metadata[0])), i.metadata[0], t)), (i.people || []).length >= 1 && (t.people = function(e, t) { var n = [],
                            r = !1; return e.replace(lt, (function(e) { var a = dt(e); switch (ut(a[0])) {
                                case "<?xml":
                                case "<personList":
                                case "</personList>":
                                case "</person>":
                                case "<extLst":
                                case "<extLst>":
                                case "</extLst>":
                                case "<extLst/>":
                                    break;
                                case "<person":
                                    n.push({ name: a.displayname, id: a.id }); break;
                                case "<ext":
                                    r = !0; break;
                                case "</ext>":
                                    r = !1; break;
                                default:
                                    if (!r && t.WTF) throw new Error("unrecognized " + a[0] + " in threaded comments") } return e })), n }($e(e, Ss(i.people[0])), t)), S && (S = function(e, t) { if (!e) return 0; try { e = t.map((function(t) { return t.id || (t.id = t.strRelID), [t.name, e["!id"][t.id].Target, (n = e["!id"][t.id].Type, br.WS.indexOf(n) > -1 ? "sheet" : br.CS && n == br.CS ? "chart" : br.DS && n == br.DS ? "dialog" : br.MS && n == br.MS ? "macro" : n && n.length ? n : "sheet")]; var n })) } catch (H) { return null } return e && 0 !== e.length ? e : null }(S, d.Sheets)); var M = $e(e, "xl/worksheets/sheet.xml", !0) ? 1 : 0;
                    e: for (y = 0; y != u.Worksheets; ++y) { var E = "sheet"; if (S && S[y] ? (v = "xl/" + S[y][1].replace(/[\/]?xl\//, ""), Ye(e, v) || (v = S[y][1]), Ye(e, v) || (v = k.replace(/_rels\/.*$/, "") + S[y][1]), E = S[y][2]) : v = (v = "xl/worksheets/sheet" + (y + 1 - M) + "." + x).replace(/sheet0\./, "sheet."), g = v.replace(/^(.*)(\/)([^\/]*)$/, "$1/_rels/$3.rels"), t && null != t.sheets) switch (typeof t.sheets) {
                            case "number":
                                if (y != t.sheets) continue e; break;
                            case "string":
                                if (u.SheetNames[y].toLowerCase() != t.sheets.toLowerCase()) continue e; break;
                            default:
                                if (Array.isArray && Array.isArray(t.sheets)) { for (var C = !1, T = 0; T != t.sheets.length; ++T) "number" == typeof t.sheets[T] && t.sheets[T] == y && (C = 1), "string" == typeof t.sheets[T] && t.sheets[T].toLowerCase() == u.SheetNames[y].toLowerCase() && (C = 1); if (!C) continue e } } ks(e, v, g, u.SheetNames[y], y, b, r, E, t, d, s, c) }
                    return p = { Directory: i, Workbook: d, Props: u, Custprops: m, Deps: f, Sheets: r, SheetNames: u.SheetNames, Strings: Li, Styles: c, Themes: s, SSF: _e(N) }, t && t.bookFiles && (e.files ? (p.keys = o, p.files = e.files) : (p.keys = [], p.files = {}, e.FullPaths.forEach((function(t, n) { t = t.replace(/^Root Entry[\/]/, ""), p.keys.push(t), p.files[t] = e.FileIndex[n] })))), t && t.bookVBA && (i.vba.length > 0 ? p.vbaraw = $e(e, Ss(i.vba[0]), !0) : i.defaults && i.defaults.bin === Fo && (p.vbaraw = $e(e, "xl/vbaProject.bin", !0))), p }

                function Es(e, t) { var n = t || {},
                        r = "Workbook",
                        a = Se.find(e, r); try { if (r = "/!DataSpaces/Version", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r); if (function(e) { var t = {};
                                t.id = e.read_shift(0, "lpp4"), t.R = Ua(e, 4), t.U = Ua(e, 4), t.W = Ua(e, 4) }(a.content), r = "/!DataSpaces/DataSpaceMap", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r); var o = function(e) { var t = [];
                            e.l += 4; for (var n = e.read_shift(4); n-- > 0;) t.push(qa(e)); return t }(a.content); if (1 !== o.length || 1 !== o[0].comps.length || 0 !== o[0].comps[0].t || "StrongEncryptionDataSpace" !== o[0].name || "EncryptedPackage" !== o[0].comps[0].v) throw new Error("ECMA-376 Encrypted file bad " + r); if (r = "/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r); var i = function(e) { var t = [];
                            e.l += 4; for (var n = e.read_shift(4); n-- > 0;) t.push(e.read_shift(0, "lpp4")); return t }(a.content); if (1 != i.length || "StrongEncryptionTransform" != i[0]) throw new Error("ECMA-376 Encrypted file bad " + r); if (r = "/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r);
                        Ga(a.content) } catch (s) {} if (r = "/EncryptionInfo", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r); var l = function(e) { var t = Ua(e); switch (t.Minor) {
                            case 2:
                                return [t.Minor, Ya(e)];
                            case 3:
                                return [t.Minor, Xa()];
                            case 4:
                                return [t.Minor, $a(e)] } throw new Error("ECMA-376 Encrypted file unrecognized Version: " + t.Minor) }(a.content); if (r = "/EncryptedPackage", !(a = Se.find(e, r)) || !a.content) throw new Error("ECMA-376 Encrypted file missing " + r); if (4 == l[0] && "undefined" !== typeof decrypt_agile) return decrypt_agile(l[1], a.content, n.password || "", n); if (2 == l[0] && "undefined" !== typeof decrypt_std76) return decrypt_std76(l[1], a.content, n.password || "", n); throw new Error("File is password-protected") }

                function Cs(e, t) { var n = ""; switch ((t || {}).type || "base64") {
                        case "buffer":
                        case "array":
                            return [e[0], e[1], e[2], e[3], e[4], e[5], e[6], e[7]];
                        case "base64":
                            n = w(e.slice(0, 12)); break;
                        case "binary":
                            n = e; break;
                        default:
                            throw new Error("Unrecognized type " + (t && t.type || "undefined")) } return [n.charCodeAt(0), n.charCodeAt(1), n.charCodeAt(2), n.charCodeAt(3), n.charCodeAt(4), n.charCodeAt(5), n.charCodeAt(6), n.charCodeAt(7)] }

                function Ts(e, t) { var n = 0;
                    e: for (; n < e.length;) switch (e.charCodeAt(n)) {
                        case 10:
                        case 13:
                        case 32:
                            ++n; break;
                        case 60:
                            return Ll(e.slice(n), t);
                        default:
                            break e }
                    return Ia.to_workbook(e, t) }

                function Hs(e, t, n, r) { return r ? (n.type = "string", Ia.to_workbook(e, n)) : Ia.to_workbook(t, n) }

                function Ls(e, t) { d(); var n = t || {}; if ("undefined" !== typeof ArrayBuffer && e instanceof ArrayBuffer) return Ls(new Uint8Array(e), ((n = _e(n)).type = "array", n)); "undefined" !== typeof Uint8Array && e instanceof Uint8Array && !n.type && (n.type = "undefined" !== typeof Deno ? "buffer" : "array"); var r, a = e,
                        o = !1; if (n.cellStyles && (n.cellNF = !0, n.sheetStubs = !0), Ii = {}, n.dateNF && (Ii.dateNF = n.dateNF), n.type || (n.type = z && Buffer.isBuffer(e) ? "buffer" : "base64"), "file" == n.type && (n.type = z ? "buffer" : "binary", a = function(e) { if ("undefined" !== typeof Me) return Me.readFileSync(e); if ("undefined" !== typeof Deno) return Deno.readFileSync(e); if ("undefined" !== typeof $ && "undefined" !== typeof File && "undefined" !== typeof Folder) try { var t = File(e);
                                t.open("r"), t.encoding = "binary"; var n = t.read(); return t.close(), n } catch (r) { if (!r.message || !r.message.match(/onstruct/)) throw r }
                            throw new Error("Cannot access file " + e) }(e), "undefined" === typeof Uint8Array || z || (n.type = "array")), "string" == n.type && (o = !0, n.type = "binary", n.codepage = 65001, a = function(e) { return e.match(/[^\x00-\x7F]/) ? St(e) : e }(e)), "array" == n.type && "undefined" !== typeof Uint8Array && e instanceof Uint8Array && "undefined" !== typeof ArrayBuffer) { var i = new ArrayBuffer(3),
                            l = new Uint8Array(i); if (l.foo = "bar", !l.foo) return (n = _e(n)).type = "array", Ls(E(a), n) } switch ((r = Cs(a, n))[0]) {
                        case 208:
                            if (207 === r[1] && 17 === r[2] && 224 === r[3] && 161 === r[4] && 177 === r[5] && 26 === r[6] && 225 === r[7]) return function(e, t) { return Se.find(e, "EncryptedPackage") ? Es(e, t) : Fl(e, t) }(Se.read(a, n), n); break;
                        case 9:
                            if (r[1] <= 8) return Fl(a, n); break;
                        case 60:
                            return Ll(a, n);
                        case 73:
                            if (73 === r[1] && 42 === r[2] && 0 === r[3]) throw new Error("TIFF Image File is not a spreadsheet"); if (68 === r[1]) return function(e, t) { var n = t || {},
                                    r = !!n.WTF;
                                n.WTF = !0; try { var a = Ta.to_workbook(e, n); return n.WTF = r, a } catch (o) { if (n.WTF = r, !o.message.match(/SYLK bad record ID/) && r) throw o; return Ia.to_workbook(e, t) } }(a, n); break;
                        case 84:
                            if (65 === r[1] && 66 === r[2] && 76 === r[3]) return Ha.to_workbook(a, n); break;
                        case 80:
                            return 75 === r[1] && r[2] < 9 && r[3] < 9 ? function(e, t) { var n = e,
                                    r = t || {}; return r.type || (r.type = z && Buffer.isBuffer(e) ? "buffer" : "base64"), Ms(nt(n, r), r) }(a, n) : Hs(e, a, n, o);
                        case 239:
                            return 60 === r[3] ? Ll(a, n) : Hs(e, a, n, o);
                        case 255:
                            if (254 === r[1]) return function(e, t) { var n = e; return "base64" == t.type && (n = w(n)), n = m.utils.decode(1200, n.slice(2), "str"), t.type = "binary", Ts(n, t) }(a, n); if (0 === r[1] && 2 === r[2] && 0 === r[3]) return ja.to_workbook(a, n); break;
                        case 0:
                            if (0 === r[1]) { if (r[2] >= 2 && 0 === r[3]) return ja.to_workbook(a, n); if (0 === r[2] && (8 === r[3] || 9 === r[3])) return ja.to_workbook(a, n) } break;
                        case 3:
                        case 131:
                        case 139:
                        case 140:
                            return Ca.to_workbook(a, n);
                        case 123:
                            if (92 === r[1] && 114 === r[2] && 116 === r[3]) return no.to_workbook(a, n); break;
                        case 10:
                        case 13:
                        case 32:
                            return function(e, t) { var n = "",
                                    r = Cs(e, t); switch (t.type) {
                                    case "base64":
                                        n = w(e); break;
                                    case "binary":
                                        n = e; break;
                                    case "buffer":
                                        n = e.toString("binary"); break;
                                    case "array":
                                        n = Ne(e); break;
                                    default:
                                        throw new Error("Unrecognized type " + t.type) } return 239 == r[0] && 187 == r[1] && 191 == r[2] && (n = kt(n)), t.type = "binary", Ts(n, t) }(a, n);
                        case 137:
                            if (80 === r[1] && 78 === r[2] && 71 === r[3]) throw new Error("PNG Image File is not a spreadsheet") } return Ea.indexOf(r[0]) > -1 && r[2] <= 12 && r[3] <= 31 ? Ca.to_workbook(a, n) : Hs(e, a, n, o) }

                function Is(e, t, n, r, a, o, i, l) { var s = Hn(n),
                        c = l.defval,
                        d = l.raw || !Object.prototype.hasOwnProperty.call(l, "raw"),
                        u = !0,
                        h = 1 === a ? [] : {}; if (1 !== a)
                        if (Object.defineProperty) try { Object.defineProperty(h, "__rowNum__", { value: n, enumerable: !1 }) } catch (v) { h.__rowNum__ = n } else h.__rowNum__ = n; if (!i || e[n])
                        for (var m = t.s.c; m <= t.e.c; ++m) { var p = i ? e[n][m] : e[r[m] + s]; if (void 0 !== p && void 0 !== p.t) { var f = p.v; switch (p.t) {
                                    case "z":
                                        if (null == f) break; continue;
                                    case "e":
                                        f = 0 == f ? null : void 0; break;
                                    case "s":
                                    case "d":
                                    case "b":
                                    case "n":
                                        break;
                                    default:
                                        throw new Error("unrecognized type " + p.t) } if (null != o[m]) { if (null == f)
                                        if ("e" == p.t && null === f) h[o[m]] = null;
                                        else if (void 0 !== c) h[o[m]] = c;
                                    else { if (!d || null !== f) continue;
                                        h[o[m]] = null } else h[o[m]] = d && ("n" !== p.t || "n" === p.t && !1 !== l.rawNumbers) ? f : Fn(p, f, l);
                                    null != f && (u = !1) } } else { if (void 0 === c) continue;
                                null != o[m] && (h[o[m]] = c) } }
                    return { row: h, isempty: u } }

                function js(e, t) { if (null == e || null == e["!ref"]) return []; var n = { t: "n", v: 0 },
                        r = 0,
                        a = 1,
                        o = [],
                        i = 0,
                        l = "",
                        s = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } },
                        c = t || {},
                        d = null != c.range ? c.range : e["!ref"]; switch (1 === c.header ? r = 1 : "A" === c.header ? r = 2 : Array.isArray(c.header) ? r = 3 : null == c.header && (r = 0), typeof d) {
                        case "string":
                            s = Pn(d); break;
                        case "number":
                            (s = Pn(e["!ref"])).s.r = d; break;
                        default:
                            s = d } r > 0 && (a = 0); var u = Hn(s.s.r),
                        h = [],
                        m = [],
                        p = 0,
                        f = 0,
                        v = Array.isArray(e),
                        g = s.s.r,
                        y = 0,
                        b = {};
                    v && !e[g] && (e[g] = []); var w = c.skipHidden && e["!cols"] || [],
                        z = c.skipHidden && e["!rows"] || []; for (y = s.s.c; y <= s.e.c; ++y)
                        if (!(w[y] || {}).hidden) switch (h[y] = In(y), n = v ? e[g][y] : e[h[y] + u], r) {
                            case 1:
                                o[y] = y - s.s.c; break;
                            case 2:
                                o[y] = h[y]; break;
                            case 3:
                                o[y] = c.header[y - s.s.c]; break;
                            default:
                                if (null == n && (n = { w: "__EMPTY", t: "s" }), l = i = Fn(n, null, c), f = b[i] || 0) { do { l = i + "_" + f++ } while (b[l]);
                                    b[i] = f, b[l] = 1 } else b[i] = 1;
                                o[y] = l }
                    for (g = s.s.r + a; g <= s.e.r; ++g)
                        if (!(z[g] || {}).hidden) { var x = Is(e, s, g, h, r, o, v, c);
                            (!1 === x.isempty || (1 === r ? !1 !== c.blankrows : c.blankrows)) && (m[p++] = x.row) } return m.length = p, m } var Vs = /"/g;

                function Os(e, t, n, r, a, o, i, l) { for (var s = !0, c = [], d = "", u = Hn(n), h = t.s.c; h <= t.e.c; ++h)
                        if (r[h]) { var m = l.dense ? (e[n] || [])[h] : e[r[h] + u]; if (null == m) d = "";
                            else if (null != m.v) { s = !1, d = "" + (l.rawNumbers && "n" == m.t ? m.v : Fn(m, null, l)); for (var p = 0, f = 0; p !== d.length; ++p)
                                    if ((f = d.charCodeAt(p)) === a || f === o || 34 === f || l.forceQuotes) { d = '"' + d.replace(Vs, '""') + '"'; break }
                                "ID" == d && (d = '"ID"') } else null == m.f || m.F ? d = "" : (s = !1, (d = "=" + m.f).indexOf(",") >= 0 && (d = '"' + d.replace(Vs, '""') + '"'));
                            c.push(d) } return !1 === l.blankrows && s ? null : c.join(i) }

                function Rs(e, t) { var n = [],
                        r = null == t ? {} : t; if (null == e || null == e["!ref"]) return ""; var a = Pn(e["!ref"]),
                        o = void 0 !== r.FS ? r.FS : ",",
                        i = o.charCodeAt(0),
                        l = void 0 !== r.RS ? r.RS : "\n",
                        s = l.charCodeAt(0),
                        c = new RegExp(("|" == o ? "\\|" : o) + "+$"),
                        d = "",
                        u = [];
                    r.dense = Array.isArray(e); for (var h = r.skipHidden && e["!cols"] || [], m = r.skipHidden && e["!rows"] || [], p = a.s.c; p <= a.e.c; ++p)(h[p] || {}).hidden || (u[p] = In(p)); for (var f = 0, v = a.s.r; v <= a.e.r; ++v)(m[v] || {}).hidden || null != (d = Os(e, a, v, u, i, s, o, r)) && (r.strip && (d = d.replace(c, "")), (d || !1 !== r.blankrows) && n.push((f++ ? l : "") + d)); return delete r.dense, n.join("") }

                function Ps(e, t) { t || (t = {}), t.FS = "\t", t.RS = "\n"; var n = Rs(e, t); if ("undefined" == typeof m || "string" == t.type) return n; var r = m.utils.encode(1200, n, "str"); return String.fromCharCode(255) + String.fromCharCode(254) + r }

                function Ds(e, t, n) { var r, a = n || {},
                        o = +!a.skipHeader,
                        i = e || {},
                        l = 0,
                        s = 0; if (i && null != a.origin)
                        if ("number" == typeof a.origin) l = a.origin;
                        else { var c = "string" == typeof a.origin ? jn(a.origin) : a.origin;
                            l = c.r, s = c.c } var d = { s: { c: 0, r: 0 }, e: { c: s, r: l + t.length - 1 + o } }; if (i["!ref"]) { var u = Pn(i["!ref"]);
                        d.e.c = Math.max(d.e.c, u.e.c), d.e.r = Math.max(d.e.r, u.e.r), -1 == l && (l = u.e.r + 1, d.e.r = l + t.length - 1 + o) } else -1 == l && (l = 0, d.e.r = t.length - 1 + o); var h = a.header || [],
                        m = 0;
                    t.forEach((function(e, t) { Ee(e).forEach((function(n) {-1 == (m = h.indexOf(n)) && (h[m = h.length] = n); var c = e[n],
                                d = "z",
                                u = "",
                                p = Vn({ c: s + m, r: l + t + o });
                            r = Fs(i, p), !c || "object" !== typeof c || c instanceof Date ? ("number" == typeof c ? d = "n" : "boolean" == typeof c ? d = "b" : "string" == typeof c ? d = "s" : c instanceof Date ? (d = "d", a.cellDates || (d = "n", c = He(c)), u = a.dateNF || N[14]) : null === c && a.nullError && (d = "e", c = 0), r ? (r.t = d, r.v = c, delete r.w, delete r.R, u && (r.z = u)) : i[p] = r = { t: d, v: c }, u && (r.z = u)) : i[p] = c })) })), d.e.c = Math.max(d.e.c, s + h.length - 1); var p = Hn(l); if (o)
                        for (m = 0; m < h.length; ++m) i[In(m + s) + p] = { t: "s", v: h[m] }; return i["!ref"] = Rn(d), i }

                function Fs(e, t, n) { if ("string" == typeof t) { if (Array.isArray(e)) { var r = jn(t); return e[r.r] || (e[r.r] = []), e[r.r][r.c] || (e[r.r][r.c] = { t: "z" }) } return e[t] || (e[t] = { t: "z" }) } return Fs(e, Vn("number" != typeof t ? t : { r: t, c: n || 0 })) }

                function Ns() { return { SheetNames: [], Sheets: {} } }

                function _s(e, t, n, r) { var a = 1; if (!n)
                        for (; a <= 65535 && -1 != e.SheetNames.indexOf(n = "Sheet" + a); ++a, n = void 0); if (!n || e.SheetNames.length >= 65535) throw new Error("Too many worksheets"); if (r && e.SheetNames.indexOf(n) >= 0) { var o = n.match(/(^.*?)(\d+)$/);
                        a = o && +o[2] || 0; var i = o && o[1] || n; for (++a; a <= 65535 && -1 != e.SheetNames.indexOf(n = i + a); ++a); } if (sl(n), e.SheetNames.indexOf(n) >= 0) throw new Error("Worksheet with name |" + n + "| already exists!"); return e.SheetNames.push(n), e.Sheets[n] = t, n }

                function Bs(e, t, n) { return t ? (e.l = { Target: t }, n && (e.l.Tooltip = n)) : delete e.l, e } var Ws = { encode_col: In, encode_row: Hn, encode_cell: Vn, encode_range: Rn, decode_col: Ln, decode_row: Tn, split_cell: function(e) { return e.replace(/(\$?[A-Z]*)(\$?\d*)/, "$1,$2").split(",") }, decode_cell: jn, decode_range: On, format_cell: Fn, sheet_add_aoa: _n, sheet_add_json: Ds, sheet_add_dom: Yl, aoa_to_sheet: Bn, json_to_sheet: function(e, t) { return Ds(null, e, t) }, table_to_sheet: Xl, table_to_book: function(e, t) { return Nn(Xl(e, t), t) }, sheet_to_csv: Rs, sheet_to_txt: Ps, sheet_to_json: js, sheet_to_html: Zl, sheet_to_formulae: function(e) { var t, n = "",
                            r = ""; if (null == e || null == e["!ref"]) return []; var a, o = Pn(e["!ref"]),
                            i = "",
                            l = [],
                            s = [],
                            c = Array.isArray(e); for (a = o.s.c; a <= o.e.c; ++a) l[a] = In(a); for (var d = o.s.r; d <= o.e.r; ++d)
                            for (i = Hn(d), a = o.s.c; a <= o.e.c; ++a)
                                if (n = l[a] + i, r = "", void 0 !== (t = c ? (e[d] || [])[a] : e[n])) { if (null != t.F) { if (n = t.F, !t.f) continue;
                                        r = t.f, -1 == n.indexOf(":") && (n = n + ":" + n) } if (null != t.f) r = t.f;
                                    else { if ("z" == t.t) continue; if ("n" == t.t && null != t.v) r = "" + t.v;
                                        else if ("b" == t.t) r = t.v ? "TRUE" : "FALSE";
                                        else if (void 0 !== t.w) r = "'" + t.w;
                                        else { if (void 0 === t.v) continue;
                                            r = "s" == t.t ? "'" + t.v : "" + t.v } } s[s.length] = n + "=" + r } return s }, sheet_to_row_object_array: js, sheet_get_cell: Fs, book_new: Ns, book_append_sheet: _s, book_set_sheet_visibility: function(e, t, n) { e.Workbook || (e.Workbook = {}), e.Workbook.Sheets || (e.Workbook.Sheets = []); var r = function(e, t) { if ("number" == typeof t) { if (t >= 0 && e.SheetNames.length > t) return t; throw new Error("Cannot find sheet # " + t) } if ("string" == typeof t) { var n = e.SheetNames.indexOf(t); if (n > -1) return n; throw new Error("Cannot find sheet name |" + t + "|") } throw new Error("Cannot find sheet |" + t + "|") }(e, t); switch (e.Workbook.Sheets[r] || (e.Workbook.Sheets[r] = {}), n) {
                            case 0:
                            case 1:
                            case 2:
                                break;
                            default:
                                throw new Error("Bad sheet visibility setting " + n) } e.Workbook.Sheets[r].Hidden = n }, cell_set_number_format: function(e, t) { return e.z = t, e }, cell_set_hyperlink: Bs, cell_set_internal_link: function(e, t, n) { return Bs(e, "#" + t, n) }, cell_add_comment: function(e, t, n) { e.c || (e.c = []), e.c.push({ t: t, a: n || "SheetJS" }) }, sheet_set_array_formula: function(e, t, n, r) { for (var a = "string" != typeof t ? t : Pn(t), o = "string" == typeof t ? t : Rn(t), i = a.s.r; i <= a.e.r; ++i)
                            for (var l = a.s.c; l <= a.e.c; ++l) { var s = Fs(e, i, l);
                                s.t = "n", s.F = o, delete s.v, i == a.s.r && l == a.s.c && (s.f = n, r && (s.D = !0)) }
                        return e }, consts: { SHEET_VISIBLE: 0, SHEET_HIDDEN: 1, SHEET_VERY_HIDDEN: 2 } };
                r.version;
                n(65043); const Us = n.p + "static/media/csv.bcb93896a0893d88860b8b706f9bcae1.svg"; const qs = n.p + "static/media/excel.0bf540f08f35f28053ff034358b6f624.svg"; var Gs = n(47483),
                    Ks = n(52991); const Zs = n.p + "static/media/gsheets.a30a5b83e302b03b2dc237e8d13b67ea.svg"; var Ys = n(12499); const Xs = n.p + "static/media/office365.0ff1d72bb72d40137efd1002ff11d25c.svg"; const $s = n.p + "static/media/teams.b5471bf10afb8695aa0528e11064f85f.svg"; const Qs = n.p + "static/media/sftp.75fc8c7032de56b139b042071be7ae90.svg"; const Js = n.p + "static/media/salesforce.445c0629ad560febf4d5e498d27d558c.svg"; const ec = n.p + "static/media/peoplehr.ad679782b617cead2cc19bb0a4f80fbd.svg"; const tc = n.p + "static/media/okta.4271d47c15da415667adc92e3e7ef6b7.svg"; const nc = n.p + "static/media/jumpcloud.17403813acef3c2bdd74657afeee2aed.svg"; var rc = n(5766),
                    ac = n.n(rc); const oc = n.p + "static/media/dropbox.********************************.svg"; const ic = n.p + "static/media/paycor.34cb92a96739bfa4ebdf69067705970e.svg"; const lc = n.p + "static/media/paylocity.f0ba946f4353cce19635c988accd0758.svg",
                    sc = n.p + "static/media/bamboohr.189e7f52d3fac6386f9a.png"; const cc = n.p + "static/media/15five.cb3fa250b0e5a49ed684ecf31b905a2d.svg"; const dc = n.p + "static/media/personio.5554e73c40da4d6df0a227ba30d0512e.svg",
                    uc = e => ({ csv: { icon: Us, isLocal: !0 }, photos: { icon: Us, isLocal: !0 }, people: { icon: Us, isLocal: !0 }, excel: { icon: qs, isLocal: !0 }, activedirectory: { icon: Gs.A, isLocal: !1 }, gdrive: { icon: Ys.A, isLocal: !1 }, gsheets: { icon: Zs, isLocal: !1 }, gsuite: { icon: Ks.A, isLocal: !1 }, office365: { icon: Xs, isLocal: !1 }, dropbox: { icon: oc, isLocal: !1 }, msteams: { icon: $s, isLocal: !1 }, salesforce: { icon: Js, isLocal: !1 }, sftp: { icon: Qs, isLocal: !1 }, peoplehr: { icon: ec, isLocal: !1 }, jumpcloud: { icon: nc, isLocal: !1 }, okta: { icon: tc, isLocal: !1 }, paycor: { icon: ic, isLocal: !1 }, paylocity: { icon: lc, isLocal: !1 }, bamboohr: { icon: sc, isLocal: !1 }, "15five": { icon: cc, isLocal: !1 }, personio: { icon: dc, isLocal: !1 } } [e]);

                function hc(e, t, n) { if (null === e || "" === e || "undefined" === typeof e || null === t || "undefined" === typeof t || t.length <= 0) return ""; const r = { value: "", example: "", label: "" }; let a = []; switch (e) {
                        case "id":
                            a = ["id", "memberid", "member id", "member_id", "roleid", "role id", "role_id", "employee id", "employee_id", "employeeid", "empid", "emp_id", "unique id", "userid", "user id", "user_id", "associate id", "PLANS"]; break;
                        case "firstName":
                            a = ["firstName", "name", "first name", "first", "first_name", "f_name", "fname", "f name", "f.name", "givenname", "given name", "member - first name", "VORNA"]; break;
                        case "lastName":
                            a = ["lastName", "last name", "last", "last_name", "family name", "family_name", "l_name", "lname", "l name", "l.name", "surname", "member - last name", "NACHN"]; break;
                        case "role":
                            a = ["role", "job", "job title", "jobtitle", "jobTitle", "position", "role title", "roletitle", "role_title", "title", "position", "position title", "position_title", "role - job title"]; break;
                        case "email":
