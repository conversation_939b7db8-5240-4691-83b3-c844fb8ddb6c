                function Fr(e, t, n) { if ("number" === typeof e && "number" === typeof t && "number" === typeof n) return Mr(e, t, n); return Or(e)(e, t) }

                function Nr(e, t) { let { clamp: n = !0, ease: r, mixer: a } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; const o = e.length; if ((0, Gt.V)(o === t.length, "Both input and output ranges must be the same length"), 1 === o) return () => t[0]; if (2 === o && e[0] === e[1]) return () => t[1];
                    e[0] > e[o - 1] && (e = [...e].reverse(), t = [...t].reverse()); const i = function(e, t, n) { const r = [],
                                a = n || Fr,
                                o = e.length - 1; for (let i = 0; i < o; i++) { let n = a(e[i], e[i + 1]); if (t) { const e = Array.isArray(t) ? t[i] || rt.l : t;
                                    n = vt(e, n) } r.push(n) } return r }(t, r, a),
                        l = i.length,
                        s = t => { let n = 0; if (l > 1)
                                for (; n < e.length - 2 && !(t < e[n + 1]); n++); const r = Sr(e[n], e[n + 1], t); return i[n](r) }; return n ? t => s(Y(e[0], e[o - 1], t)) : s }

                function _r(e) { const t = [0]; return function(e, t) { const n = e[e.length - 1]; for (let r = 1; r <= t; r++) { const a = Sr(0, t, r);
                            e.push(Mr(n, 1, a)) } }(t, e.length - 1), t }

                function Br(e) { let { duration: t = 300, keyframes: n, times: r, ease: a = "easeInOut" } = e; const o = (e => Array.isArray(e) && "number" !== typeof e[0])(a) ? a.map(kr) : kr(a),
                        i = { done: !1, value: n[0] },
                        l = function(e, t) { return e.map((e => e * t)) }(r && r.length === n.length ? r : _r(n), t),
                        s = Nr(l, n, { ease: Array.isArray(o) ? o : (c = n, d = o, c.map((() => d || pr)).splice(0, c.length - 1)) }); var c, d; return { calculatedDuration: t, next: e => (i.value = s(e), i.done = e >= t, i) } } const Wr = e => { const t = t => { let { timestamp: n } = t; return e(n) }; return { start: () => at.update(t, !0), stop: () => ot(t), now: () => it.isProcessing ? it.timestamp : Wn.now() } },
                    Ur = { decay: lr, inertia: lr, tween: Br, keyframes: Br, spring: ir },
                    qr = e => e / 100;
                class Gr extends qn { constructor(e) { super(e), this.holdTime = null, this.cancelTime = null, this.currentTime = 0, this.playbackSpeed = 1, this.pendingPlayState = "running", this.startTime = null, this.state = "idle", this.stop = () => { if (this.resolver.cancel(), this.isStopped = !0, "idle" === this.state) return;
                            this.teardown(); const { onStop: e } = this.options;
                            e && e() }; const { name: t, motionValue: n, element: r, keyframes: a } = this.options, o = (null === r || void 0 === r ? void 0 : r.KeyframeResolver) || hn;
                        this.resolver = new o(a, ((e, t) => this.onKeyframesResolved(e, t)), t, n, r), this.resolver.scheduleResolve() } initPlayback(e) { const { type: t = "keyframes", repeat: n = 0, repeatDelay: r = 0, repeatType: a, velocity: o = 0 } = this.options, i = Ur[t] || Br; let l, s;
                        i !== Br && "number" !== typeof e[0] && (l = vt(qr, Fr(e[0], e[1])), e = [0, 100]); const c = i({ ...this.options, keyframes: e }); "mirror" === a && (s = i({ ...this.options, keyframes: [...e].reverse(), velocity: -o })), null === c.calculatedDuration && (c.calculatedDuration = function(e) { let t = 0,
                                n = e.next(t); for (; !n.done && t < 2e4;) t += 50, n = e.next(t); return t >= 2e4 ? 1 / 0 : t }(c)); const { calculatedDuration: d } = c, u = d + r; return { generator: c, mirroredGenerator: s, mapPercentToKeyframes: l, calculatedDuration: d, resolvedDuration: u, totalDuration: u * (n + 1) - r } } onPostResolved() { const { autoplay: e = !0 } = this.options;
                        this.play(), "paused" !== this.pendingPlayState && e ? this.state = this.pendingPlayState : this.pause() } tick(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const { resolved: n } = this; if (!n) { const { keyframes: e } = this.options; return { done: !0, value: e[e.length - 1] } } const { finalKeyframe: r, generator: a, mirroredGenerator: o, mapPercentToKeyframes: i, keyframes: l, calculatedDuration: s, totalDuration: c, resolvedDuration: d } = n; if (null === this.startTime) return a.next(0); const { delay: u, repeat: h, repeatType: m, repeatDelay: p, onUpdate: f } = this.options;
                        this.speed > 0 ? this.startTime = Math.min(this.startTime, e) : this.speed < 0 && (this.startTime = Math.min(e - c / this.speed, this.startTime)), t ? this.currentTime = e : null !== this.holdTime ? this.currentTime = this.holdTime : this.currentTime = Math.round(e - this.startTime) * this.speed; const v = this.currentTime - u * (this.speed >= 0 ? 1 : -1),
                            g = this.speed >= 0 ? v < 0 : v > c;
                        this.currentTime = Math.max(v, 0), "finished" === this.state && null === this.holdTime && (this.currentTime = c); let y = this.currentTime,
                            b = a; if (h) { const e = Math.min(this.currentTime, c) / d; let t = Math.floor(e),
                                n = e % 1;!n && e >= 1 && (n = 1), 1 === n && t--, t = Math.min(t, h + 1);
                            Boolean(t % 2) && ("reverse" === m ? (n = 1 - n, p && (n -= p / d)) : "mirror" === m && (b = o)), y = Y(0, 1, n) * d } const w = g ? { done: !1, value: l[0] } : b.next(y);
                        i && (w.value = i(w.value)); let { done: z } = w;
                        g || null === s || (z = this.speed >= 0 ? this.currentTime >= c : this.currentTime <= 0); const x = null === this.holdTime && ("finished" === this.state || "running" === this.state && z); return x && void 0 !== r && (w.value = Ut(l, this.options, r)), f && f(w.value), x && this.finish(), w } get duration() { const { resolved: e } = this; return e ? Rt(e.calculatedDuration) : 0 } get time() { return Rt(this.currentTime) } set time(e) { e = Ot(e), this.currentTime = e, null !== this.holdTime || 0 === this.speed ? this.holdTime = e : this.driver && (this.startTime = this.driver.now() - e / this.speed) } get speed() { return this.playbackSpeed } set speed(e) { const t = this.playbackSpeed !== e;
                        this.playbackSpeed = e, t && (this.time = Rt(this.currentTime)) } play() { if (this.resolver.isScheduled || this.resolver.resume(), !this._resolved) return void(this.pendingPlayState = "running"); if (this.isStopped) return; const { driver: e = Wr, onPlay: t, startTime: n } = this.options;
                        this.driver || (this.driver = e((e => this.tick(e)))), t && t(); const r = this.driver.now();
                        null !== this.holdTime ? this.startTime = r - this.holdTime : this.startTime ? "finished" === this.state && (this.startTime = r) : this.startTime = null !== n && void 0 !== n ? n : this.calcStartTime(), "finished" === this.state && this.updateFinishedPromise(), this.cancelTime = this.startTime, this.holdTime = null, this.state = "running", this.driver.start() } pause() { var e;
                        this._resolved ? (this.state = "paused", this.holdTime = null !== (e = this.currentTime) && void 0 !== e ? e : 0) : this.pendingPlayState = "paused" } complete() { "running" !== this.state && this.play(), this.pendingPlayState = this.state = "finished", this.holdTime = null } finish() { this.teardown(), this.state = "finished"; const { onComplete: e } = this.options;
                        e && e() } cancel() { null !== this.cancelTime && this.tick(this.cancelTime), this.teardown(), this.updateFinishedPromise() } teardown() { this.state = "idle", this.stopDriver(), this.resolveFinishedPromise(), this.updateFinishedPromise(), this.startTime = this.cancelTime = null, this.resolver.cancel() } stopDriver() { this.driver && (this.driver.stop(), this.driver = void 0) } sample(e) { return this.startTime = 0, this.tick(e, !0) } } const Kr = e => Array.isArray(e) && "number" === typeof e[0];

                function Zr(e) { return Boolean(!e || "string" === typeof e && e in Xr || Kr(e) || Array.isArray(e) && e.every(Zr)) } const Yr = e => { let [t, n, r, a] = e; return "cubic-bezier(".concat(t, ", ").concat(n, ", ").concat(r, ", ").concat(a, ")") },
                    Xr = { linear: "linear", ease: "ease", easeIn: "ease-in", easeOut: "ease-out", easeInOut: "ease-in-out", circIn: Yr([0, .65, .55, 1]), circOut: Yr([.55, 0, 1, .45]), backIn: Yr([.31, .01, .66, -.59]), backOut: Yr([.33, 1.53, .69, .99]) };

                function $r(e) { return Qr(e) || Xr.easeOut }

                function Qr(e) { return e ? Kr(e) ? Yr(e) : Array.isArray(e) ? e.map($r) : Xr[e] : void 0 } const Jr = Nn((() => Object.hasOwnProperty.call(Element.prototype, "animate")));
                class ea extends qn { constructor(e) { super(e); const { name: t, motionValue: n, element: r, keyframes: a } = this.options;
                        this.resolver = new Fn(a, ((e, t) => this.onKeyframesResolved(e, t)), t, n, r), this.resolver.scheduleResolve() } initPlayback(e, t) { var n; let { duration: r = 300, times: a, ease: o, type: i, motionValue: l, name: s, startTime: c } = this.options; if (!(null === (n = l.owner) || void 0 === n ? void 0 : n.current)) return !1; if ("spring" === (d = this.options).type || !Zr(d.ease)) { const { onComplete: t, onUpdate: n, motionValue: l, element: s, ...c } = this.options, d = function(e, t) { const n = new Gr({ ...t, keyframes: e, repeat: 0, delay: 0, isGenerator: !0 }); let r = { done: !1, value: e[0] }; const a = []; let o = 0; for (; !r.done && o < 2e4;) r = n.sample(o), a.push(r.value), o += 10; return { times: void 0, keyframes: a, duration: o - 10, ease: "linear" } }(e, c);
                            1 === (e = d.keyframes).length && (e[1] = e[0]), r = d.duration, a = d.times, o = d.ease, i = "keyframes" } var d; const u = function(e, t, n) { let { delay: r = 0, duration: a = 300, repeat: o = 0, repeatType: i = "loop", ease: l, times: s } = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {}; const c = {
                                [t]: n };
                            s && (c.offset = s); const d = Qr(l); return Array.isArray(d) && (c.easing = d), e.animate(c, { delay: r, duration: a, easing: Array.isArray(d) ? "linear" : d, fill: "both", iterations: o + 1, direction: "reverse" === i ? "alternate" : "normal" }) }(l.owner.current, s, e, { ...this.options, duration: r, times: a, ease: o }); return u.startTime = null !== c && void 0 !== c ? c : this.calcStartTime(), this.pendingTimeline ? (u.timeline = this.pendingTimeline, this.pendingTimeline = void 0) : u.onfinish = () => { const { onComplete: n } = this.options;
                            l.set(Ut(e, this.options, t)), n && n(), this.cancel(), this.resolveFinishedPromise() }, { animation: u, duration: r, times: a, type: i, ease: o, keyframes: e } } get duration() { const { resolved: e } = this; if (!e) return 0; const { duration: t } = e; return Rt(t) } get time() { const { resolved: e } = this; if (!e) return 0; const { animation: t } = e; return Rt(t.currentTime || 0) } set time(e) { const { resolved: t } = this; if (!t) return; const { animation: n } = t;
                        n.currentTime = Ot(e) } get speed() { const { resolved: e } = this; if (!e) return 1; const { animation: t } = e; return t.playbackRate } set speed(e) { const { resolved: t } = this; if (!t) return; const { animation: n } = t;
                        n.playbackRate = e } get state() { const { resolved: e } = this; if (!e) return "idle"; const { animation: t } = e; return t.playState } get startTime() { const { resolved: e } = this; if (!e) return null; const { animation: t } = e; return t.startTime } attachTimeline(e) { if (this._resolved) { const { resolved: t } = this; if (!t) return rt.l; const { animation: n } = t;
                            n.timeline = e, n.onfinish = null } else this.pendingTimeline = e; return rt.l } play() { if (this.isStopped) return; const { resolved: e } = this; if (!e) return; const { animation: t } = e; "finished" === t.playState && this.updateFinishedPromise(), t.play() } pause() { const { resolved: e } = this; if (!e) return; const { animation: t } = e;
                        t.pause() } stop() { if (this.resolver.cancel(), this.isStopped = !0, "idle" === this.state) return;
                        this.resolveFinishedPromise(), this.updateFinishedPromise(); const { resolved: e } = this; if (!e) return; const { animation: t, keyframes: n, duration: r, type: a, ease: o, times: i } = e; if ("idle" === t.playState || "finished" === t.playState) return; if (this.time) { const { motionValue: e, onUpdate: t, onComplete: l, element: s, ...c } = this.options, d = new Gr({ ...c, keyframes: n, duration: r, type: a, ease: o, times: i, isGenerator: !0 }), u = Ot(this.time);
                            e.setWithVelocity(d.sample(u - 10).value, d.sample(u).value, 10) } const { onStop: l } = this.options;
                        l && l(), this.cancel() } complete() { const { resolved: e } = this;
                        e && e.animation.finish() } cancel() { const { resolved: e } = this;
                        e && e.animation.cancel() } static supports(e) { const { motionValue: t, name: n, repeatDelay: r, repeatType: a, damping: o, type: i } = e; return Jr() && n && Ye.has(n) && t && t.owner && t.owner.current instanceof HTMLElement && !t.owner.getProps().onUpdate && !r && "mirror" !== a && 0 !== o && "inertia" !== i } } const ta = Nn((() => void 0 !== window.ScrollTimeline));
                class na { constructor(e) { this.stop = () => this.runAll("stop"), this.animations = e.filter(Boolean) } then(e, t) { return Promise.all(this.animations).then(e).catch(t) } getAll(e) { return this.animations[0][e] } setAll(e, t) { for (let n = 0; n < this.animations.length; n++) this.animations[n][e] = t } attachTimeline(e) { const t = this.animations.map((t => { if (!ta() || !t.attachTimeline) return t.pause(),
                                function(e, t) { let n; const r = () => { const { currentTime: r } = t, a = (null === r ? 0 : r.value) / 100;
                                        n !== a && e(a), n = a }; return at.update(r, !0), () => ot(r) }((e => { t.time = t.duration * e }), e);
                            t.attachTimeline(e) })); return () => { t.forEach(((e, t) => { e && e(), this.animations[t].stop() })) } } get time() { return this.getAll("time") } set time(e) { this.setAll("time", e) } get speed() { return this.getAll("speed") } set speed(e) { this.setAll("speed", e) } get startTime() { return this.getAll("startTime") } get duration() { let e = 0; for (let t = 0; t < this.animations.length; t++) e = Math.max(e, this.animations[t].duration); return e } runAll(e) { this.animations.forEach((t => t[e]())) } play() { this.runAll("play") } pause() { this.runAll("pause") } cancel() { this.runAll("cancel") } complete() { this.runAll("complete") } } const ra = function(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {},
                        a = arguments.length > 4 ? arguments[4] : void 0,
                        o = arguments.length > 5 ? arguments[5] : void 0,
                        i = arguments.length > 6 ? arguments[6] : void 0; return l => { const s = _t(r, e) || {},
                            c = s.delay || r.delay || 0; let { elapsed: d = 0 } = r;
                        d -= Ot(c); let u = { keyframes: Array.isArray(n) ? n : [null, n], ease: "easeOut", velocity: t.getVelocity(), ...s, delay: -d, onUpdate: e => { t.set(e), s.onUpdate && s.onUpdate(e) }, onComplete: () => { l(), s.onComplete && s.onComplete(), i && i() }, onStop: i, name: e, motionValue: t, element: o ? void 0 : a };
                        (function(e) { let { when: t, delay: n, delayChildren: r, staggerChildren: a, staggerDirection: o, repeat: i, repeatType: l, repeatDelay: s, from: c, elapsed: d, ...u } = e; return !!Object.keys(u).length })(s) || (u = { ...u, ...Nt(e, u) }), u.duration && (u.duration = Ot(u.duration)), u.repeatDelay && (u.repeatDelay = Ot(u.repeatDelay)), void 0 !== u.from && (u.keyframes[0] = u.from); let m = !1; if ((!1 === u.type || 0 === u.duration && !u.repeatDelay) && (u.duration = 0, 0 === u.delay && (m = !0)), (Bt || h) && (m = !0, u.duration = 0, u.delay = 0), m && !o && void 0 !== t.get()) { const e = Ut(u.keyframes, s); if (void 0 !== e) return at.update((() => { u.onUpdate(e), u.onComplete() })), new na([]) } return !o && ea.supports(u) ? new ea(u) : new Gr(u) } };
                class aa { constructor() { this.subscriptions = [] } add(e) { return $e(this.subscriptions, e), () => Qe(this.subscriptions, e) } notify(e, t, n) { const r = this.subscriptions.length; if (r)
                            if (1 === r) this.subscriptions[0](e, t, n);
                            else
                                for (let a = 0; a < r; a++) { const r = this.subscriptions[a];
                                    r && r(e, t, n) } } getSize() { return this.subscriptions.length } clear() { this.subscriptions.length = 0 } } const oa = { current: void 0 };
                class ia { constructor(e) { var t = this; let n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                        this.version = "11.3.29", this.canTrackVelocity = null, this.events = {}, this.updateAndNotify = function(e) { let n = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1]; const r = Wn.now();
                            t.updatedAt !== r && t.setPrevFrameValue(), t.prev = t.current, t.setCurrent(e), t.current !== t.prev && t.events.change && t.events.change.notify(t.current), n && t.events.renderRequest && t.events.renderRequest.notify(t.current) }, this.hasAnimated = !1, this.setCurrent(e), this.owner = n.owner } setCurrent(e) { var t;
                        this.current = e, this.updatedAt = Wn.now(), null === this.canTrackVelocity && void 0 !== e && (this.canTrackVelocity = (t = this.current, !isNaN(parseFloat(t)))) } setPrevFrameValue() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.current;
                        this.prevFrameValue = e, this.prevUpdatedAt = this.updatedAt } onChange(e) { return this.on("change", e) } on(e, t) { this.events[e] || (this.events[e] = new aa); const n = this.events[e].add(t); return "change" === e ? () => { n(), at.read((() => { this.events.change.getSize() || this.stop() })) } : n } clearListeners() { for (const e in this.events) this.events[e].clear() } attach(e, t) { this.passiveEffect = e, this.stopPassiveEffect = t } set(e) { let t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
                        t && this.passiveEffect ? this.passiveEffect(e, this.updateAndNotify) : this.updateAndNotify(e, t) } setWithVelocity(e, t, n) { this.set(t), this.prev = void 0, this.prevFrameValue = e, this.prevUpdatedAt = this.updatedAt - n } jump(e) { let t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
                        this.updateAndNotify(e), this.prev = e, this.prevUpdatedAt = this.prevFrameValue = void 0, t && this.stop(), this.stopPassiveEffect && this.stopPassiveEffect() } get() { return oa.current && oa.current.push(this), this.current } getPrevious() { return this.prev } getVelocity() { const e = Wn.now(); if (!this.canTrackVelocity || void 0 === this.prevFrameValue || e - this.updatedAt > 30) return 0; const t = Math.min(this.updatedAt - this.prevUpdatedAt, 30); return Gn(parseFloat(this.current) - parseFloat(this.prevFrameValue), t) } start(e) { return this.stop(), new Promise((t => { this.hasAnimated = !0, this.animation = e(t), this.events.animationStart && this.events.animationStart.notify() })).then((() => { this.events.animationComplete && this.events.animationComplete.notify(), this.clearAnimation() })) } stop() { this.animation && (this.animation.stop(), this.events.animationCancel && this.events.animationCancel.notify()), this.clearAnimation() } isAnimating() { return !!this.animation } clearAnimation() { delete this.animation } destroy() { this.clearListeners(), this.stop(), this.stopPassiveEffect && this.stopPassiveEffect() } }

                function la(e, t) { return new ia(e, t) }

                function sa(e, t, n) { e.hasValue(t) ? e.getValue(t).set(n) : e.addValue(t, la(n)) }

                function ca(e) { return e.getProps()[u] } class da extends ia { constructor() { super(...arguments), this.output = [], this.counts = new Map } add(e) { const t = Xe(e); if (!t) return; const n = this.counts.get(t) || 0;
                        this.counts.set(t, n + 1), 0 === n && (this.output.push(t), this.update()); let r = !1; return () => { if (r) return;
                            r = !0; const e = this.counts.get(t) - 1;
                            this.counts.set(t, e), 0 === e && (Qe(this.output, t), this.update()) } } update() { this.set(this.output.length ? this.output.join(", ") : "auto") } }

                function ua(e, t) { var n; if (!e.applyWillChange) return; let r = e.getValue("willChange"); return r || (null === (n = e.props.style) || void 0 === n ? void 0 : n.willChange) || (r = new da("auto"), e.addValue("willChange", r)), a = r, Boolean(K(a) && a.add) ? r.add(t) : void 0; var a }

                function ha(e, t) { let { protectedKeys: n, needsAnimating: r } = e; const a = n.hasOwnProperty(t) && !0 !== r[t]; return r[t] = !1, a }

                function ma(e, t) { let { delay: n = 0, transitionOverride: r, type: a } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; var o; let { transition: i = e.getDefaultTransition(), transitionEnd: l, ...s } = t;
                    r && (i = r); const c = [],
                        d = a && e.animationState && e.animationState.getState()[a]; for (const u in s) { const t = e.getValue(u, null !== (o = e.latestValues[u]) && void 0 !== o ? o : null),
                            r = s[u]; if (void 0 === r || d && ha(d, u)) continue; const a = { delay: n, ..._t(i || {}, u) }; let l = !1; if (window.MotionHandoffAnimation) { const t = ca(e); if (t) { const e = window.MotionHandoffAnimation(t, u, at);
                                null !== e && (a.startTime = e, l = !0) } } t.start(ra(u, t, r, e.shouldReduceMotion && q.has(u) ? { type: !1 } : a, e, l, ua(e, u))); const h = t.animation;
                        h && c.push(h) } return l && Promise.all(c).then((() => { at.update((() => { l && function(e, t) { const n = Vt(e, t); let { transitionEnd: r = {}, transition: a = {}, ...o } = n || {};
                                o = { ...o, ...r }; for (const i in o) sa(e, i, Ke(o[i])) }(e, l) })) })), c }

                function pa(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; var r; const a = Vt(e, t, "exit" === n.type ? null === (r = e.presenceContext) || void 0 === r ? void 0 : r.custom : void 0); let { transition: o = e.getDefaultTransition() || {} } = a || {};
                    n.transitionOverride && (o = n.transitionOverride); const i = a ? () => Promise.all(ma(e, a, n)) : () => Promise.resolve(),
                        l = e.variantChildren && e.variantChildren.size ? function() { let r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0; const { delayChildren: a = 0, staggerChildren: i, staggerDirection: l } = o; return function(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                                    r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0,
                                    a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 1,
                                    o = arguments.length > 5 ? arguments[5] : void 0; const i = [],
                                    l = (e.variantChildren.size - 1) * r,
                                    s = 1 === a ? function() { return (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0) * r } : function() { return l - (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0) * r }; return Array.from(e.variantChildren).sort(fa).forEach(((e, r) => { e.notify("AnimationStart", t), i.push(pa(e, t, { ...o, delay: n + s(r) }).then((() => e.notify("AnimationComplete", t)))) })), Promise.all(i) }(e, t, a + r, i, l, n) } : () => Promise.resolve(),
                        { when: s } = o; if (s) { const [e, t] = "beforeChildren" === s ? [i, l] : [l, i]; return e().then((() => t())) } return Promise.all([i(), l(n.delay)]) }

                function fa(e, t) { return e.sortNodePosition(t) } const va = [...E].reverse(),
                    ga = E.length;

                function ya(e) { return t => Promise.all(t.map((t => { let { animation: n, options: r } = t; return function(e, t) { let n, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; if (e.notify("AnimationStart", t), Array.isArray(t)) { const a = t.map((t => pa(e, t, r)));
                                n = Promise.all(a) } else if ("string" === typeof t) n = pa(e, t, r);
                            else { const a = "function" === typeof t ? Vt(e, t, r.custom) : t;
                                n = Promise.all(ma(e, a, r)) } return n.then((() => { e.notify("AnimationComplete", t) })) }(e, n, r) }))) }

                function ba(e, t) { return "string" === typeof t ? t !== e : !!Array.isArray(t) && !jt(t, e) }

                function wa() { return { isActive: arguments.length > 0 && void 0 !== arguments[0] && arguments[0], protectedKeys: {}, needsAnimating: {}, prevResolvedValues: {} } }

                function za() { return { animate: wa(!0), whileInView: wa(), whileHover: wa(), whileTap: wa(), whileDrag: wa(), whileFocus: wa(), exit: wa() } } let xa = 0; const Aa = { animation: { Feature: class extends xt { constructor(e) { super(e), e.animationState || (e.animationState = function(e) { let t = ya(e),
                                            n = za(),
                                            r = !0; const a = t => (n, r) => { var a; const o = Vt(e, r, "exit" === t ? null === (a = e.presenceContext) || void 0 === a ? void 0 : a.custom : void 0); if (o) { const { transition: e, transitionEnd: t, ...r } = o;
                                                n = { ...n, ...r, ...t } } return n };

                                        function o(o) { const i = e.getProps(),
                                                l = e.getVariantContext(!0) || {},
                                                s = [],
                                                c = new Set; let d = {},
                                                u = 1 / 0; for (let t = 0; t < ga; t++) { const h = va[t],
                                                    m = n[h],
                                                    p = void 0 !== i[h] ? i[h] : l[h],
                                                    f = S(p),
                                                    v = h === o ? m.isActive : null;!1 === v && (u = t); let g = p === l[h] && p !== i[h] && f; if (g && r && e.manuallyAnimateOnMount && (g = !1), m.protectedKeys = { ...d }, !m.isActive && null === v || !p && !m.prevProp || M(p) || "boolean" === typeof p) continue; let y = ba(m.prevProp, p) || h === o && m.isActive && !g && f || t > u && f,
                                                    b = !1; const w = Array.isArray(p) ? p : [p]; let z = w.reduce(a(h), {});!1 === v && (z = {}); const { prevResolvedValues: x = {} } = m, A = { ...x, ...z }, k = t => { y = !0, c.has(t) && (b = !0, c.delete(t)), m.needsAnimating[t] = !0; const n = e.getValue(t);
                                                    n && (n.liveStyle = !1) }; for (const e in A) { const t = z[e],
                                                        n = x[e]; if (d.hasOwnProperty(e)) continue; let r = !1;
                                                    r = qe(t) && qe(n) ? !jt(t, n) : t !== n, r ? void 0 !== t && null !== t ? k(e) : c.add(e) : void 0 !== t && c.has(e) ? k(e) : m.protectedKeys[e] = !0 } m.prevProp = p, m.prevResolvedValues = z, m.isActive && (d = { ...d, ...z }), r && e.blockInitialAnimation && (y = !1), !y || g && !b || s.push(...w.map((e => ({ animation: e, options: { type: h } })))) } if (c.size) { const t = {};
                                                c.forEach((n => { const r = e.getBaseTarget(n),
                                                        a = e.getValue(n);
                                                    a && (a.liveStyle = !0), t[n] = null !== r && void 0 !== r ? r : null })), s.push({ animation: t }) } let h = Boolean(s.length); return !r || !1 !== i.initial && i.initial !== i.animate || e.manuallyAnimateOnMount || (h = !1), r = !1, h ? t(s) : Promise.resolve() } return { animateChanges: o, setActive: function(t, r) { var a; if (n[t].isActive === r) return Promise.resolve();
                                                null === (a = e.variantChildren) || void 0 === a || a.forEach((e => { var n; return null === (n = e.animationState) || void 0 === n ? void 0 : n.setActive(t, r) })), n[t].isActive = r; const i = o(t); for (const e in n) n[e].protectedKeys = {}; return i }, setAnimateFunction: function(n) { t = n(e) }, getState: () => n, reset: () => { n = za(), r = !0 } } }(e)) } updateAnimationControlsSubscription() { const { animate: e } = this.node.getProps();
                                    M(e) && (this.unmountControls = e.subscribe(this.node)) } mount() { this.updateAnimationControlsSubscription() } update() { const { animate: e } = this.node.getProps(), { animate: t } = this.node.prevProps || {};
                                    e !== t && this.updateAnimationControlsSubscription() } unmount() { var e;
                                    this.node.animationState.reset(), null === (e = this.unmountControls) || void 0 === e || e.call(this) } } }, exit: { Feature: class extends xt { constructor() { super(...arguments), this.id = xa++ } update() { if (!this.node.presenceContext) return; const { isPresent: e, onExitComplete: t } = this.node.presenceContext, { isPresent: n } = this.node.prevPresenceContext || {}; if (!this.node.animationState || e === n) return; const r = this.node.animationState.setActive("exit", !e);
                                    t && !e && r.then((() => t(this.id))) } mount() { const { register: e } = this.node.presenceContext || {};
                                    e && (this.unmount = e(this.id)) } unmount() {} } } },
                    ka = (e, t) => Math.abs(e - t);
                class Sa { constructor(e, t) { let { transformPagePoint: n, contextWindow: r, dragSnapToOrigin: a = !1 } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; if (this.startEvent = null, this.lastMoveEvent = null, this.lastMoveEventInfo = null, this.handlers = {}, this.contextWindow = window, this.updatePoint = () => { if (!this.lastMoveEvent || !this.lastMoveEventInfo) return; const e = Ca(this.lastMoveEventInfo, this.history),
                                    t = null !== this.startEvent,
                                    n = function(e, t) { const n = ka(e.x, t.x),
                                            r = ka(e.y, t.y); return Math.sqrt(n ** 2 + r ** 2) }(e.offset, { x: 0, y: 0 }) >= 3; if (!t && !n) return; const { point: r } = e, { timestamp: a } = it;
                                this.history.push({ ...r, timestamp: a }); const { onStart: o, onMove: i } = this.handlers;
                                t || (o && o(this.lastMoveEvent, e), this.startEvent = this.lastMoveEvent), i && i(this.lastMoveEvent, e) }, this.handlePointerMove = (e, t) => { this.lastMoveEvent = e, this.lastMoveEventInfo = Ma(t, this.transformPagePoint), at.update(this.updatePoint, !0) }, this.handlePointerUp = (e, t) => { this.end(); const { onEnd: n, onSessionEnd: r, resumeAnimation: a } = this.handlers; if (this.dragSnapToOrigin && a && a(), !this.lastMoveEvent || !this.lastMoveEventInfo) return; const o = Ca("pointercancel" === e.type ? this.lastMoveEventInfo : Ma(t, this.transformPagePoint), this.history);
                                this.startEvent && n && n(e, o), r && r(e, o) }, !ut(e)) return;
                        this.dragSnapToOrigin = a, this.handlers = t, this.transformPagePoint = n, this.contextWindow = r || window; const o = Ma(ht(e), this.transformPagePoint),
                            { point: i } = o,
                            { timestamp: l } = it;
                        this.history = [{ ...i, timestamp: l }]; const { onSessionStart: s } = t;
                        s && s(e, Ca(o, this.history)), this.removeListeners = vt(pt(this.contextWindow, "pointermove", this.handlePointerMove), pt(this.contextWindow, "pointerup", this.handlePointerUp), pt(this.contextWindow, "pointercancel", this.handlePointerUp)) } updateHandlers(e) { this.handlers = e } end() { this.removeListeners && this.removeListeners(), ot(this.updatePoint) } }

                function Ma(e, t) { return t ? { point: t(e.point) } : e }

                function Ea(e, t) { return { x: e.x - t.x, y: e.y - t.y } }

                function Ca(e, t) { let { point: n } = e; return { point: n, delta: Ea(n, Ha(t)), offset: Ea(n, Ta(t)), velocity: La(t, .1) } }

                function Ta(e) { return e[0] }

                function Ha(e) { return e[e.length - 1] }

                function La(e, t) { if (e.length < 2) return { x: 0, y: 0 }; let n = e.length - 1,
                        r = null; const a = Ha(e); for (; n >= 0 && (r = e[n], !(a.timestamp - r.timestamp > Ot(t)));) n--; if (!r) return { x: 0, y: 0 }; const o = Rt(a.timestamp - r.timestamp); if (0 === o) return { x: 0, y: 0 }; const i = { x: (a.x - r.x) / o, y: (a.y - r.y) / o }; return i.x === 1 / 0 && (i.x = 0), i.y === 1 / 0 && (i.y = 0), i } const Ia = .9999,
                    ja = 1.0001,
                    Va = -.01,
                    Oa = .01;

                function Ra(e) { return e.max - e.min }

                function Pa(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : .5;
                    e.origin = r, e.originPoint = Mr(t.min, t.max, e.origin), e.scale = Ra(n) / Ra(t), e.translate = Mr(n.min, n.max, e.origin) - e.originPoint, (e.scale >= Ia && e.scale <= ja || isNaN(e.scale)) && (e.scale = 1), (e.translate >= Va && e.translate <= Oa || isNaN(e.translate)) && (e.translate = 0) }

                function Da(e, t, n, r) { Pa(e.x, t.x, n.x, r ? r.originX : void 0), Pa(e.y, t.y, n.y, r ? r.originY : void 0) }

                function Fa(e, t, n) { e.min = n.min + t.min, e.max = e.min + Ra(t) }

                function Na(e, t, n) { e.min = t.min - n.min, e.max = e.min + Ra(t) }

                function _a(e, t, n) { Na(e.x, t.x, n.x), Na(e.y, t.y, n.y) }

                function Ba(e, t, n) { return { min: void 0 !== t ? e.min + t : void 0, max: void 0 !== n ? e.max + n - (e.max - e.min) : void 0 } }

                function Wa(e, t) { let n = t.min - e.min,
                        r = t.max - e.max; return t.max - t.min < e.max - e.min && ([n, r] = [r, n]), { min: n, max: r } } const Ua = .35;

                function qa(e, t, n) { return { min: Ga(e, t), max: Ga(e, n) } }

                function Ga(e, t) { return "number" === typeof e ? e : e[t] || 0 } const Ka = () => ({ x: { translate: 0, scale: 1, origin: 0, originPoint: 0 }, y: { translate: 0, scale: 1, origin: 0, originPoint: 0 } }),
                    Za = () => ({ x: { min: 0, max: 0 }, y: { min: 0, max: 0 } });

                function Ya(e) { return [e("x"), e("y")] }

                function Xa(e) { let { top: t, left: n, right: r, bottom: a } = e; return { x: { min: n, max: r }, y: { min: t, max: a } } }

                function $a(e) { return void 0 === e || 1 === e }

                function Qa(e) { let { scale: t, scaleX: n, scaleY: r } = e; return !$a(t) || !$a(n) || !$a(r) }

                function Ja(e) { return Qa(e) || eo(e) || e.z || e.rotate || e.rotateX || e.rotateY || e.skewX || e.skewY }

                function eo(e) { return to(e.x) || to(e.y) }

                function to(e) { return e && "0%" !== e }

                function no(e, t, n) { return n + t * (e - n) }

                function ro(e, t, n, r, a) { return void 0 !== a && (e = no(e, a, r)), no(e, n, r) + t }

                function ao(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1,
                        r = arguments.length > 3 ? arguments[3] : void 0,
                        a = arguments.length > 4 ? arguments[4] : void 0;
                    e.min = ro(e.min, t, n, r, a), e.max = ro(e.max, t, n, r, a) }

                function oo(e, t) { let { x: n, y: r } = t;
                    ao(e.x, n.translate, n.scale, n.originPoint), ao(e.y, r.translate, r.scale, r.originPoint) } const io = .999999999999,
                    lo = 1.0000000000001;

                function so(e, t) { e.min = e.min + t, e.max = e.max + t }

                function co(e, t, n, r) { let a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : .5;
                    ao(e, t, n, Mr(e.min, e.max, a), r) }

                function uo(e, t) { co(e.x, t.x, t.scaleX, t.scale, t.originX), co(e.y, t.y, t.scaleY, t.scale, t.originY) }

                function ho(e, t) { return Xa(function(e, t) { if (!t) return e; const n = t({ x: e.left, y: e.top }),
                            r = t({ x: e.right, y: e.bottom }); return { top: n.y, left: n.x, bottom: r.y, right: r.x } }(e.getBoundingClientRect(), t)) } const mo = e => { let { current: t } = e; return t ? t.ownerDocument.defaultView : null },
                    po = new WeakMap;
                class fo { constructor(e) { this.openGlobalLock = null, this.isDragging = !1, this.currentDirection = null, this.originPoint = { x: 0, y: 0 }, this.constraints = !1, this.hasMutatedConstraints = !1, this.elastic = Za(), this.visualElement = e } start(e) { let { snapToCursor: t = !1 } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const { presenceContext: n } = this.visualElement; if (n && !1 === n.isPresent) return; const { dragSnapToOrigin: r } = this.getProps();
                        this.panSession = new Sa(e, { onSessionStart: e => { const { dragSnapToOrigin: n } = this.getProps();
                                n ? this.pauseAnimation() : this.stopAnimation(), t && this.snapToCursor(ht(e, "page").point) }, onStart: (e, t) => { var n; const { drag: r, dragPropagation: a, onDragStart: o } = this.getProps(); if (r && !a && (this.openGlobalLock && this.openGlobalLock(), this.openGlobalLock = wt(r), !this.openGlobalLock)) return;
                                this.isDragging = !0, this.currentDirection = null, this.resolveConstraints(), this.visualElement.projection && (this.visualElement.projection.isAnimationBlocked = !0, this.visualElement.projection.target = void 0), Ya((e => { let t = this.getAxisMotionValue(e).get() || 0; if (ie.test(t)) { const { projection: n } = this.visualElement; if (n && n.layout) { const r = n.layout.layoutBox[e]; if (r) { t = Ra(r) * (parseFloat(t) / 100) } } } this.originPoint[e] = t })), o && at.postRender((() => o(e, t))), null === (n = this.removeWillChange) || void 0 === n || n.call(this), this.removeWillChange = ua(this.visualElement, "transform"); const { animationState: i } = this.visualElement;
                                i && i.setActive("whileDrag", !0) }, onMove: (e, t) => { const { dragPropagation: n, dragDirectionLock: r, onDirectionLock: a, onDrag: o } = this.getProps(); if (!n && !this.openGlobalLock) return; const { offset: i } = t; if (r && null === this.currentDirection) return this.currentDirection = function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10,
                                        n = null;
                                    Math.abs(e.y) > t ? n = "y" : Math.abs(e.x) > t && (n = "x"); return n }(i), void(null !== this.currentDirection && a && a(this.currentDirection));
                                this.updateAxis("x", t.point, i), this.updateAxis("y", t.point, i), this.visualElement.render(), o && o(e, t) }, onSessionEnd: (e, t) => this.stop(e, t), resumeAnimation: () => Ya((e => { var t; return "paused" === this.getAnimationState(e) && (null === (t = this.getAxisMotionValue(e).animation) || void 0 === t ? void 0 : t.play()) })) }, { transformPagePoint: this.visualElement.getTransformPagePoint(), dragSnapToOrigin: r, contextWindow: mo(this.visualElement) }) } stop(e, t) { var n;
                        null === (n = this.removeWillChange) || void 0 === n || n.call(this); const r = this.isDragging; if (this.cancel(), !r) return; const { velocity: a } = t;
                        this.startAnimation(a); const { onDragEnd: o } = this.getProps();
                        o && at.postRender((() => o(e, t))) } cancel() { this.isDragging = !1; const { projection: e, animationState: t } = this.visualElement;
                        e && (e.isAnimationBlocked = !1), this.panSession && this.panSession.end(), this.panSession = void 0; const { dragPropagation: n } = this.getProps();!n && this.openGlobalLock && (this.openGlobalLock(), this.openGlobalLock = null), t && t.setActive("whileDrag", !1) } updateAxis(e, t, n) { const { drag: r } = this.getProps(); if (!n || !vo(e, r, this.currentDirection)) return; const a = this.getAxisMotionValue(e); let o = this.originPoint[e] + n[e];
                        this.constraints && this.constraints[e] && (o = function(e, t, n) { let { min: r, max: a } = t; return void 0 !== r && e < r ? e = n ? Mr(r, e, n.min) : Math.max(e, r) : void 0 !== a && e > a && (e = n ? Mr(a, e, n.max) : Math.min(e, a)), e }(o, this.constraints[e], this.elastic[e])), a.set(o) } resolveConstraints() { var e; const { dragConstraints: t, dragElastic: n } = this.getProps(), r = this.visualElement.projection && !this.visualElement.projection.layout ? this.visualElement.projection.measure(!1) : null === (e = this.visualElement.projection) || void 0 === e ? void 0 : e.layout, a = this.constraints;
                        t && y(t) ? this.constraints || (this.constraints = this.resolveRefConstraints()) : this.constraints = !(!t || !r) && function(e, t) { let { top: n, left: r, bottom: a, right: o } = t; return { x: Ba(e.x, r, o), y: Ba(e.y, n, a) } }(r.layoutBox, t), this.elastic = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : Ua; return !1 === e ? e = 0 : !0 === e && (e = Ua), { x: qa(e, "left", "right"), y: qa(e, "top", "bottom") } }(n), a !== this.constraints && r && this.constraints && !this.hasMutatedConstraints && Ya((e => {!1 !== this.constraints && this.getAxisMotionValue(e) && (this.constraints[e] = function(e, t) { const n = {}; return void 0 !== t.min && (n.min = t.min - e.min), void 0 !== t.max && (n.max = t.max - e.min), n }(r.layoutBox[e], this.constraints[e])) })) } resolveRefConstraints() { const { dragConstraints: e, onMeasureDragConstraints: t } = this.getProps(); if (!e || !y(e)) return !1; const n = e.current;
                        (0, Gt.V)(null !== n, "If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop."); const { projection: r } = this.visualElement; if (!r || !r.layout) return !1; const a = function(e, t, n) { const r = ho(e, n),
                                { scroll: a } = t; return a && (so(r.x, a.offset.x), so(r.y, a.offset.y)), r }(n, r.root, this.visualElement.getTransformPagePoint()); let o = function(e, t) { return { x: Wa(e.x, t.x), y: Wa(e.y, t.y) } }(r.layout.layoutBox, a); if (t) { const e = t(function(e) { let { x: t, y: n } = e; return { top: n.min, right: t.max, bottom: n.max, left: t.min } }(o));
                            this.hasMutatedConstraints = !!e, e && (o = Xa(e)) } return o } startAnimation(e) { const { drag: t, dragMomentum: n, dragElastic: r, dragTransition: a, dragSnapToOrigin: o, onDragTransitionEnd: i } = this.getProps(), l = this.constraints || {}, s = Ya((i => { if (!vo(i, t, this.currentDirection)) return; let s = l && l[i] || {};
                            o && (s = { min: 0, max: 0 }); const c = r ? 200 : 1e6,
                                d = r ? 40 : 1e7,
                                u = { type: "inertia", velocity: n ? e[i] : 0, bounceStiffness: c, bounceDamping: d, timeConstant: 750, restDelta: 1, restSpeed: 10, ...a, ...s }; return this.startAxisValueAnimation(i, u) })); return Promise.all(s).then(i) } startAxisValueAnimation(e, t) { const n = this.getAxisMotionValue(e); return n.start(ra(e, n, 0, t, this.visualElement, !1, ua(this.visualElement, e))) } stopAnimation() { Ya((e => this.getAxisMotionValue(e).stop())) } pauseAnimation() { Ya((e => { var t; return null === (t = this.getAxisMotionValue(e).animation) || void 0 === t ? void 0 : t.pause() })) } getAnimationState(e) { var t; return null === (t = this.getAxisMotionValue(e).animation) || void 0 === t ? void 0 : t.state } getAxisMotionValue(e) { const t = "_drag".concat(e.toUpperCase()),
                            n = this.visualElement.getProps(),
                            r = n[t]; return r || this.visualElement.getValue(e, (n.initial ? n.initial[e] : void 0) || 0) } snapToCursor(e) { Ya((t => { const { drag: n } = this.getProps(); if (!vo(t, n, this.currentDirection)) return; const { projection: r } = this.visualElement, a = this.getAxisMotionValue(t); if (r && r.layout) { const { min: n, max: o } = r.layout.layoutBox[t];
                                a.set(e[t] - Mr(n, o, .5)) } })) } scalePositionWithinConstraints() { if (!this.visualElement.current) return; const { drag: e, dragConstraints: t } = this.getProps(), { projection: n } = this.visualElement; if (!y(t) || !n || !this.constraints) return;
                        this.stopAnimation(); const r = { x: 0, y: 0 };
                        Ya((e => { const t = this.getAxisMotionValue(e); if (t && !1 !== this.constraints) { const n = t.get();
                                r[e] = function(e, t) { let n = .5; const r = Ra(e),
                                        a = Ra(t); return a > r ? n = Sr(t.min, t.max - r, e.min) : r > a && (n = Sr(e.min, e.max - a, t.min)), Y(0, 1, n) }({ min: n, max: n }, this.constraints[e]) } })); const { transformTemplate: a } = this.visualElement.getProps();
                        this.visualElement.current.style.transform = a ? a({}, "") : "none", n.root && n.root.updateScroll(), n.updateLayout(), this.resolveConstraints(), Ya((t => { if (!vo(t, e, null)) return; const n = this.getAxisMotionValue(t),
                                { min: a, max: o } = this.constraints[t];
                            n.set(Mr(a, o, r[t])) })) } addListeners() { if (!this.visualElement.current) return;
                        po.set(this.visualElement, this); const e = pt(this.visualElement.current, "pointerdown", (e => { const { drag: t, dragListener: n = !0 } = this.getProps();
                                t && n && this.start(e) })),
                            t = () => { const { dragConstraints: e } = this.getProps();
                                y(e) && e.current && (this.constraints = this.resolveRefConstraints()) },
                            { projection: n } = this.visualElement,
                            r = n.addEventListener("measure", t);
                        n && !n.layout && (n.root && n.root.updateScroll(), n.updateLayout()), at.read(t); const a = dt(window, "resize", (() => this.scalePositionWithinConstraints())),
                            o = n.addEventListener("didUpdate", (e => { let { delta: t, hasLayoutChanged: n } = e;
                                this.isDragging && n && (Ya((e => { const n = this.getAxisMotionValue(e);
                                    n && (this.originPoint[e] += t[e].translate, n.set(n.get() + t[e].translate)) })), this.visualElement.render()) })); return () => { a(), e(), r(), o && o() } } getProps() { const e = this.visualElement.getProps(),
                            { drag: t = !1, dragDirectionLock: n = !1, dragPropagation: r = !1, dragConstraints: a = !1, dragElastic: o = Ua, dragMomentum: i = !0 } = e; return { ...e, drag: t, dragDirectionLock: n, dragPropagation: r, dragConstraints: a, dragElastic: o, dragMomentum: i } } }

                function vo(e, t, n) { return (!0 === t || t === e) && (null === n || n === e) } const go = e => (t, n) => { e && at.postRender((() => e(t, n))) }; const yo = { hasAnimatedSinceResize: !0, hasEverUpdated: !1 };

                function bo(e, t) { return t.max === t.min ? 0 : e / (t.max - t.min) * 100 } const wo = { correct: (e, t) => { if (!t.target) return e; if ("string" === typeof e) { if (!le.test(e)) return e;
                                e = parseFloat(e) } const n = bo(e, t.target.x),
                                r = bo(e, t.target.y); return "".concat(n, "% ").concat(r, "%") } },
                    zo = { correct: (e, t) => { let { treeScale: n, projectionDelta: r } = t; const a = e,
                                o = Hn.parse(e); if (o.length > 5) return a; const i = Hn.createTransformer(e),
                                l = "number" !== typeof o[0] ? 1 : 0,
                                s = r.x.scale * n.x,
                                c = r.y.scale * n.y;
                            o[0 + l] /= s, o[1 + l] /= c; const d = Mr(s, c, .5); return "number" === typeof o[2 + l] && (o[2 + l] /= d), "number" === typeof o[3 + l] && (o[3 + l] /= d), i(o) } };
                class xo extends a.Component { componentDidMount() { const { visualElement: e, layoutGroup: t, switchLayoutGroup: n, layoutId: r } = this.props, { projection: a } = e; var o;
                        o = ko, Object.assign(W, o), a && (t.group && t.group.add(a), n && n.register && r && n.register(a), a.root.didUpdate(), a.addEventListener("animationComplete", (() => { this.safeToRemove() })), a.setOptions({ ...a.options, onExitComplete: () => this.safeToRemove() })), yo.hasEverUpdated = !0 } getSnapshotBeforeUpdate(e) { const { layoutDependency: t, visualElement: n, drag: r, isPresent: a } = this.props, o = n.projection; return o ? (o.isPresent = a, r || e.layoutDependency !== t || void 0 === t ? o.willUpdate() : this.safeToRemove(), e.isPresent !== a && (a ? o.promote() : o.relegate() || at.postRender((() => { const e = o.getStack();
                            e && e.members.length || this.safeToRemove() }))), null) : null } componentDidUpdate() { const { projection: e } = this.props.visualElement;
                        e && (e.root.didUpdate(), v.postRender((() => {!e.currentAnimation && e.isLead() && this.safeToRemove() }))) } componentWillUnmount() { const { visualElement: e, layoutGroup: t, switchLayoutGroup: n } = this.props, { projection: r } = e;
                        r && (r.scheduleCheckAfterUnmount(), t && t.group && t.group.remove(r), n && n.deregister && n.deregister(r)) } safeToRemove() { const { safeToRemove: e } = this.props;
                        e && e() } render() { return null } }

                function Ao(e) { const [t, n] = function() { const e = (0, a.useContext)(l.t); if (null === e) return [!0, null]; const { isPresent: t, onExitComplete: n, register: r } = e, o = (0, a.useId)();
                        (0, a.useEffect)((() => r(o)), []); const i = (0, a.useCallback)((() => n && n(o)), [o, n]); return !t && n ? [!1, i] : [!0] }(), o = (0, a.useContext)(R.L); return (0, r.jsx)(xo, { ...e, layoutGroup: o, switchLayoutGroup: (0, a.useContext)(b), isPresent: t, safeToRemove: n }) } const ko = { borderRadius: { ...wo, applyTo: ["borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius"] }, borderTopLeftRadius: wo, borderTopRightRadius: wo, borderBottomLeftRadius: wo, borderBottomRightRadius: wo, boxShadow: zo },
                    So = ["TopLeft", "TopRight", "BottomLeft", "BottomRight"],
                    Mo = So.length,
                    Eo = e => "string" === typeof e ? parseFloat(e) : e,
                    Co = e => "number" === typeof e || le.test(e);

                function To(e, t) { return void 0 !== e[t] ? e[t] : e.borderRadius } const Ho = Io(0, .5, yr),
                    Lo = Io(.5, .95, rt.l);

                function Io(e, t, n) { return r => r < e ? 0 : r > t ? 1 : n(Sr(e, t, r)) }

                function jo(e, t) { e.min = t.min, e.max = t.max }

                function Vo(e, t) { jo(e.x, t.x), jo(e.y, t.y) }

                function Oo(e, t) { e.translate = t.translate, e.scale = t.scale, e.originPoint = t.originPoint, e.origin = t.origin }

                function Ro(e, t, n, r, a) { return e = no(e -= t, 1 / n, r), void 0 !== a && (e = no(e, 1 / a, r)), e }

                function Po(e, t, n, r, a) { let [o, i, l] = n;! function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
                            n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1,
                            r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : .5,
                            a = arguments.length > 4 ? arguments[4] : void 0,
                            o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : e,
                            i = arguments.length > 6 && void 0 !== arguments[6] ? arguments[6] : e;
                        ie.test(t) && (t = parseFloat(t), t = Mr(i.min, i.max, t / 100) - i.min); if ("number" !== typeof t) return; let l = Mr(o.min, o.max, r);
                        e === o && (l -= t), e.min = Ro(e.min, t, n, l, a), e.max = Ro(e.max, t, n, l, a) }(e, t[o], t[i], t[l], t.scale, r, a) } const Do = ["x", "scaleX", "originX"],
                    Fo = ["y", "scaleY", "originY"];

                function No(e, t, n, r) { Po(e.x, t, Do, n ? n.x : void 0, r ? r.x : void 0), Po(e.y, t, Fo, n ? n.y : void 0, r ? r.y : void 0) }

                function _o(e) { return 0 === e.translate && 1 === e.scale }

                function Bo(e) { return _o(e.x) && _o(e.y) }

                function Wo(e, t) { return e.min === t.min && e.max === t.max }

                function Uo(e, t) { return Math.round(e.min) === Math.round(t.min) && Math.round(e.max) === Math.round(t.max) }

                function qo(e, t) { return Uo(e.x, t.x) && Uo(e.y, t.y) }

                function Go(e) { return Ra(e.x) / Ra(e.y) }

                function Ko(e, t) { return e.translate === t.translate && e.scale === t.scale && e.originPoint === t.originPoint } class Zo { constructor() { this.members = [] } add(e) { $e(this.members, e), e.scheduleRender() } remove(e) { if (Qe(this.members, e), e === this.prevLead && (this.prevLead = void 0), e === this.lead) { const e = this.members[this.members.length - 1];
                            e && this.promote(e) } } relegate(e) { const t = this.members.findIndex((t => e === t)); if (0 === t) return !1; let n; for (let r = t; r >= 0; r--) { const e = this.members[r]; if (!1 !== e.isPresent) { n = e; break } } return !!n && (this.promote(n), !0) } promote(e, t) { const n = this.lead; if (e !== n && (this.prevLead = n, this.lead = e, e.show(), n)) { n.instance && n.scheduleRender(), e.scheduleRender(), e.resumeFrom = n, t && (e.resumeFrom.preserveOpacity = !0), n.snapshot && (e.snapshot = n.snapshot, e.snapshot.latestValues = n.animationValues || n.latestValues), e.root && e.root.isUpdating && (e.isLayoutDirty = !0); const { crossfade: r } = e.options;!1 === r && n.hide() } } exitAnimationComplete() { this.members.forEach((e => { const { options: t, resumingFrom: n } = e;
                            t.onExitComplete && t.onExitComplete(), n && n.options.onExitComplete && n.options.onExitComplete() })) } scheduleRender() { this.members.forEach((e => { e.instance && e.scheduleRender(!1) })) } removeLeadSnapshot() { this.lead && this.lead.snapshot && (this.lead.snapshot = void 0) } } const Yo = (e, t) => e.depth - t.depth;
                class Xo { constructor() { this.children = [], this.isDirty = !1 } add(e) { $e(this.children, e), this.isDirty = !0 } remove(e) { Qe(this.children, e), this.isDirty = !0 } forEach(e) { this.isDirty && this.children.sort(Yo), this.isDirty = !1, this.children.forEach(e) } } const $o = { type: "projectionFrame", totalNodes: 0, resolvedTargetDeltas: 0, recalculatedProjection: 0 },
                    Qo = "undefined" !== typeof window && void 0 !== window.MotionDebug,
                    Jo = ["", "X", "Y", "Z"],
                    ei = { visibility: "hidden" }; let ti = 0;

                function ni(e, t, n, r) { const { latestValues: a } = t;
                    a[e] && (n[e] = a[e], t.setStaticValue(e, 0), r && (r[e] = 0)) }

                function ri(e) { if (e.hasCheckedOptimisedAppear = !0, e.root === e) return; const { visualElement: t } = e.options; if (!t) return; const n = ca(t);
                    window.MotionHasOptimisedTransformAnimation(n) && window.MotionCancelOptimisedTransform(n); const { parent: r } = e;
                    r && !r.hasCheckedOptimisedAppear && ri(r) }

                function ai(e) { let { attachResizeListener: t, defaultParent: n, measureScroll: r, checkIsScrollRoot: a, resetTransform: o } = e; return class { constructor() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                                t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null === n || void 0 === n ? void 0 : n();
                            this.id = ti++, this.animationId = 0, this.children = new Set, this.options = {}, this.isTreeAnimating = !1, this.isAnimationBlocked = !1, this.isLayoutDirty = !1, this.isProjectionDirty = !1, this.isSharedProjectionDirty = !1, this.isTransformDirty = !1, this.updateManuallyBlocked = !1, this.updateBlockedByResize = !1, this.isUpdating = !1, this.isSVG = !1, this.needsReset = !1, this.shouldResetTransform = !1, this.hasCheckedOptimisedAppear = !1, this.treeScale = { x: 1, y: 1 }, this.eventHandlers = new Map, this.hasTreeAnimated = !1, this.updateScheduled = !1, this.scheduleUpdate = () => this.update(), this.projectionUpdateScheduled = !1, this.checkUpdateFailed = () => { this.isUpdating && (this.isUpdating = !1, this.clearAllSnapshots()) }, this.updateProjection = () => { this.projectionUpdateScheduled = !1, Qo && ($o.totalNodes = $o.resolvedTargetDeltas = $o.recalculatedProjection = 0), this.nodes.forEach(li), this.nodes.forEach(pi), this.nodes.forEach(fi), this.nodes.forEach(si), Qo && window.MotionDebug.record($o) }, this.resolvedRelativeTargetAt = 0, this.hasProjected = !1, this.isVisible = !0, this.animationProgress = 0, this.sharedNodes = new Map, this.latestValues = e, this.root = t ? t.root || t : this, this.path = t ? [...t.path, t] : [], this.parent = t, this.depth = t ? t.depth + 1 : 0; for (let n = 0; n < this.path.length; n++) this.path[n].shouldResetTransform = !0;
                            this.root === this && (this.nodes = new Xo) } addEventListener(e, t) { return this.eventHandlers.has(e) || this.eventHandlers.set(e, new aa), this.eventHandlers.get(e).add(t) } notifyListeners(e) { const t = this.eventHandlers.get(e); for (var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++) r[a - 1] = arguments[a];
                            t && t.notify(...r) } hasListeners(e) { return this.eventHandlers.has(e) } mount(e) { let n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.root.hasTreeAnimated; if (this.instance) return; var r;
                            this.isSVG = (r = e) instanceof SVGElement && "svg" !== r.tagName, this.instance = e; const { layoutId: a, layout: o, visualElement: i } = this.options; if (i && !i.current && i.mount(e), this.root.nodes.add(this), this.parent && this.parent.children.add(this), n && (o || a) && (this.isLayoutDirty = !0), t) { let n; const r = () => this.root.updateBlockedByResize = !1;
                                t(e, (() => { this.root.updateBlockedByResize = !0, n && n(), n = function(e, t) { const n = Wn.now(),
                                            r = a => { let { timestamp: o } = a; const i = o - n;
                                                i >= t && (ot(r), e(i - t)) }; return at.read(r, !0), () => ot(r) }(r, 250), yo.hasAnimatedSinceResize && (yo.hasAnimatedSinceResize = !1, this.nodes.forEach(mi)) })) } a && this.root.registerSharedNode(a, this), !1 !== this.options.animate && i && (a || o) && this.addEventListener("didUpdate", (e => { let { delta: t, hasLayoutChanged: n, hasRelativeTargetChanged: r, layout: a } = e; if (this.isTreeAnimationBlocked()) return this.target = void 0, void(this.relativeTarget = void 0); const o = this.options.transition || i.getDefaultTransition() || zi,
                                    { onLayoutAnimationStart: l, onLayoutAnimationComplete: s } = i.getProps(),
                                    c = !this.targetLayout || !qo(this.targetLayout, a) || r,
                                    d = !n && r; if (this.options.layoutRoot || this.resumeFrom && this.resumeFrom.instance || d || n && (c || !this.currentAnimation)) { this.resumeFrom && (this.resumingFrom = this.resumeFrom, this.resumingFrom.resumingFrom = void 0), this.setAnimationOrigin(t, d); const e = { ..._t(o, "layout"), onPlay: l, onComplete: s };
                                    (i.shouldReduceMotion || this.options.layoutRoot) && (e.delay = 0, e.type = !1), this.startAnimation(e) } else n || mi(this), this.isLead() && this.options.onExitComplete && this.options.onExitComplete();
                                this.targetLayout = a })) } unmount() { this.options.layoutId && this.willUpdate(), this.root.nodes.remove(this); const e = this.getStack();
                            e && e.remove(this), this.parent && this.parent.children.delete(this), this.instance = void 0, ot(this.updateProjection) } blockUpdate() { this.updateManuallyBlocked = !0 } unblockUpdate() { this.updateManuallyBlocked = !1 } isUpdateBlocked() { return this.updateManuallyBlocked || this.updateBlockedByResize } isTreeAnimationBlocked() { return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || !1 } startUpdate() { this.isUpdateBlocked() || (this.isUpdating = !0, this.nodes && this.nodes.forEach(vi), this.animationId++) } getTransformTemplate() { const { visualElement: e } = this.options; return e && e.getProps().transformTemplate } willUpdate() { let e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0]; if (this.root.hasTreeAnimated = !0, this.root.isUpdateBlocked()) return void(this.options.onExitComplete && this.options.onExitComplete()); if (window.MotionCancelOptimisedTransform && !this.hasCheckedOptimisedAppear && ri(this), !this.root.isUpdating && this.root.startUpdate(), this.isLayoutDirty) return;
                            this.isLayoutDirty = !0; for (let a = 0; a < this.path.length; a++) { const e = this.path[a];
                                e.shouldResetTransform = !0, e.updateScroll("snapshot"), e.options.layoutRoot && e.willUpdate(!1) } const { layoutId: t, layout: n } = this.options; if (void 0 === t && !n) return; const r = this.getTransformTemplate();
                            this.prevTransformTemplateValue = r ? r(this.latestValues, "") : void 0, this.updateSnapshot(), e && this.notifyListeners("willUpdate") } update() { this.updateScheduled = !1; if (this.isUpdateBlocked()) return this.unblockUpdate(), this.clearAllSnapshots(), void this.nodes.forEach(di);
                            this.isUpdating || this.nodes.forEach(ui), this.isUpdating = !1, this.nodes.forEach(hi), this.nodes.forEach(oi), this.nodes.forEach(ii), this.clearAllSnapshots(); const e = Wn.now();
                            it.delta = Y(0, 1e3 / 60, e - it.timestamp), it.timestamp = e, it.isProcessing = !0, lt.update.process(it), lt.preRender.process(it), lt.render.process(it), it.isProcessing = !1 } didUpdate() { this.updateScheduled || (this.updateScheduled = !0, v.read(this.scheduleUpdate)) } clearAllSnapshots() { this.nodes.forEach(ci), this.sharedNodes.forEach(gi) } scheduleUpdateProjection() { this.projectionUpdateScheduled || (this.projectionUpdateScheduled = !0, at.preRender(this.updateProjection, !1, !0)) } scheduleCheckAfterUnmount() { at.postRender((() => { this.isLayoutDirty ? this.root.didUpdate() : this.root.checkUpdateFailed() })) } updateSnapshot() {!this.snapshot && this.instance && (this.snapshot = this.measure()) } updateLayout() { if (!this.instance) return; if (this.updateScroll(), (!this.options.alwaysMeasureLayout || !this.isLead()) && !this.isLayoutDirty) return; if (this.resumeFrom && !this.resumeFrom.instance)
                                for (let n = 0; n < this.path.length; n++) { this.path[n].updateScroll() }
                            const e = this.layout;
                            this.layout = this.measure(!1), this.layoutCorrected = Za(), this.isLayoutDirty = !1, this.projectionDelta = void 0, this.notifyListeners("measure", this.layout.layoutBox); const { visualElement: t } = this.options;
                            t && t.notify("LayoutMeasure", this.layout.layoutBox, e ? e.layoutBox : void 0) } updateScroll() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "measure",
                                t = Boolean(this.options.layoutScroll && this.instance); if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === e && (t = !1), t) { const t = a(this.instance);
                                this.scroll = { animationId: this.root.animationId, phase: e, isRoot: t, offset: r(this.instance), wasRoot: this.scroll ? this.scroll.isRoot : t } } } resetTransform() { if (!o) return; const e = this.isLayoutDirty || this.shouldResetTransform || this.options.alwaysMeasureLayout,
                                t = this.projectionDelta && !Bo(this.projectionDelta),
                                n = this.getTransformTemplate(),
                                r = n ? n(this.latestValues, "") : void 0,
                                a = r !== this.prevTransformTemplateValue;
                            e && (t || Ja(this.latestValues) || a) && (o(this.instance, r), this.shouldResetTransform = !1, this.scheduleRender()) } measure() { let e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0]; const t = this.measurePageBox(); let n = this.removeElementScroll(t); var r; return e && (n = this.removeTransform(n)), ki((r = n).x), ki(r.y), { animationId: this.root.animationId, measuredBox: t, layoutBox: n, latestValues: {}, source: this.id } } measurePageBox() { var e; const { visualElement: t } = this.options; if (!t) return Za(); const n = t.measureViewportBox(); if (!((null === (e = this.scroll) || void 0 === e ? void 0 : e.wasRoot) || this.path.some(Mi))) { const { scroll: e } = this.root;
                                e && (so(n.x, e.offset.x), so(n.y, e.offset.y)) } return n } removeElementScroll(e) { var t; const n = Za(); if (Vo(n, e), null === (t = this.scroll) || void 0 === t ? void 0 : t.wasRoot) return n; for (let r = 0; r < this.path.length; r++) { const t = this.path[r],
                                    { scroll: a, options: o } = t;
                                t !== this.root && a && o.layoutScroll && (a.wasRoot && Vo(n, e), so(n.x, a.offset.x), so(n.y, a.offset.y)) } return n } applyTransform(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const n = Za();
                            Vo(n, e); for (let r = 0; r < this.path.length; r++) { const e = this.path[r];!t && e.options.layoutScroll && e.scroll && e !== e.root && uo(n, { x: -e.scroll.offset.x, y: -e.scroll.offset.y }), Ja(e.latestValues) && uo(n, e.latestValues) } return Ja(this.latestValues) && uo(n, this.latestValues), n } removeTransform(e) { const t = Za();
                            Vo(t, e); for (let n = 0; n < this.path.length; n++) { const e = this.path[n]; if (!e.instance) continue; if (!Ja(e.latestValues)) continue;
                                Qa(e.latestValues) && e.updateSnapshot(); const r = Za();
                                Vo(r, e.measurePageBox()), No(t, e.latestValues, e.snapshot ? e.snapshot.layoutBox : void 0, r) } return Ja(this.latestValues) && No(t, this.latestValues), t } setTargetDelta(e) { this.targetDelta = e, this.root.scheduleUpdateProjection(), this.isProjectionDirty = !0 } setOptions(e) { this.options = { ...this.options, ...e, crossfade: void 0 === e.crossfade || e.crossfade } } clearMeasurements() { this.scroll = void 0, this.layout = void 0, this.snapshot = void 0, this.prevTransformTemplateValue = void 0, this.targetDelta = void 0, this.target = void 0, this.isLayoutDirty = !1 } forceRelativeParentToResolveTarget() { this.relativeParent && this.relativeParent.resolvedRelativeTargetAt !== it.timestamp && this.relativeParent.resolveTargetDelta(!0) } resolveTargetDelta() { let e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; var t; const n = this.getLead();
                            this.isProjectionDirty || (this.isProjectionDirty = n.isProjectionDirty), this.isTransformDirty || (this.isTransformDirty = n.isTransformDirty), this.isSharedProjectionDirty || (this.isSharedProjectionDirty = n.isSharedProjectionDirty); const r = Boolean(this.resumingFrom) || this !== n; if (!(e || r && this.isSharedProjectionDirty || this.isProjectionDirty || (null === (t = this.parent) || void 0 === t ? void 0 : t.isProjectionDirty) || this.attemptToResolveRelativeTarget || this.root.updateBlockedByResize)) return; const { layout: a, layoutId: o } = this.options; if (this.layout && (a || o)) { if (this.resolvedRelativeTargetAt = it.timestamp, !this.targetDelta && !this.relativeTarget) { const e = this.getClosestProjectingParent();
                                    e && e.layout && 1 !== this.animationProgress ? (this.relativeParent = e, this.forceRelativeParentToResolveTarget(), this.relativeTarget = Za(), this.relativeTargetOrigin = Za(), _a(this.relativeTargetOrigin, this.layout.layoutBox, e.layout.layoutBox), Vo(this.relativeTarget, this.relativeTargetOrigin)) : this.relativeParent = this.relativeTarget = void 0 } if (this.relativeTarget || this.targetDelta) { var i, l, s; if (this.target || (this.target = Za(), this.targetWithTransforms = Za()), this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target ? (this.forceRelativeParentToResolveTarget(), i = this.target, l = this.relativeTarget, s = this.relativeParent.target, Fa(i.x, l.x, s.x), Fa(i.y, l.y, s.y)) : this.targetDelta ? (Boolean(this.resumingFrom) ? this.target = this.applyTransform(this.layout.layoutBox) : Vo(this.target, this.layout.layoutBox), oo(this.target, this.targetDelta)) : Vo(this.target, this.layout.layoutBox), this.attemptToResolveRelativeTarget) { this.attemptToResolveRelativeTarget = !1; const e = this.getClosestProjectingParent();
                                        e && Boolean(e.resumingFrom) === Boolean(this.resumingFrom) && !e.options.layoutScroll && e.target && 1 !== this.animationProgress ? (this.relativeParent = e, this.forceRelativeParentToResolveTarget(), this.relativeTarget = Za(), this.relativeTargetOrigin = Za(), _a(this.relativeTargetOrigin, this.target, e.target), Vo(this.relativeTarget, this.relativeTargetOrigin)) : this.relativeParent = this.relativeTarget = void 0 } Qo && $o.resolvedTargetDeltas++ } } } getClosestProjectingParent() { if (this.parent && !Qa(this.parent.latestValues) && !eo(this.parent.latestValues)) return this.parent.isProjecting() ? this.parent : this.parent.getClosestProjectingParent() } isProjecting() { return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout) } calcProjection() { var e; const t = this.getLead(),
                                n = Boolean(this.resumingFrom) || this !== t; let r = !0; if ((this.isProjectionDirty || (null === (e = this.parent) || void 0 === e ? void 0 : e.isProjectionDirty)) && (r = !1), n && (this.isSharedProjectionDirty || this.isTransformDirty) && (r = !1), this.resolvedRelativeTargetAt === it.timestamp && (r = !1), r) return; const { layout: a, layoutId: o } = this.options; if (this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation), this.isTreeAnimating || (this.targetDelta = this.relativeTarget = void 0), !this.layout || !a && !o) return;
                            Vo(this.layoutCorrected, this.layout.layoutBox); const i = this.treeScale.x,
                                l = this.treeScale.y;! function(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; const a = n.length; if (!a) return; let o, i;
                                t.x = t.y = 1; for (let l = 0; l < a; l++) { o = n[l], i = o.projectionDelta; const { visualElement: a } = o.options;
                                    a && a.props.style && "contents" === a.props.style.display || (r && o.options.layoutScroll && o.scroll && o !== o.root && uo(e, { x: -o.scroll.offset.x, y: -o.scroll.offset.y }), i && (t.x *= i.x.scale, t.y *= i.y.scale, oo(e, i)), r && Ja(o.latestValues) && uo(e, o.latestValues)) } t.x < lo && t.x > io && (t.x = 1), t.y < lo && t.y > io && (t.y = 1) }(this.layoutCorrected, this.treeScale, this.path, n), !t.layout || t.target || 1 === this.treeScale.x && 1 === this.treeScale.y || (t.target = t.layout.layoutBox, t.targetWithTransforms = Za()); const { target: s } = t;
                            s ? (this.projectionDelta && this.prevProjectionDelta ? (Oo(this.prevProjectionDelta.x, this.projectionDelta.x), Oo(this.prevProjectionDelta.y, this.projectionDelta.y)) : this.createProjectionDeltas(), Da(this.projectionDelta, this.layoutCorrected, s, this.latestValues), this.treeScale.x === i && this.treeScale.y === l && Ko(this.projectionDelta.x, this.prevProjectionDelta.x) && Ko(this.projectionDelta.y, this.prevProjectionDelta.y) || (this.hasProjected = !0, this.scheduleRender(), this.notifyListeners("projectionUpdate", s)), Qo && $o.recalculatedProjection++) : this.prevProjectionDelta && (this.createProjectionDeltas(), this.scheduleRender()) } hide() { this.isVisible = !1 } show() { this.isVisible = !0 } scheduleRender() { let e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0]; var t; if (null === (t = this.options.visualElement) || void 0 === t || t.scheduleRender(), e) { const e = this.getStack();
                                e && e.scheduleRender() } this.resumingFrom && !this.resumingFrom.instance && (this.resumingFrom = void 0) } createProjectionDeltas() { this.prevProjectionDelta = Ka(), this.projectionDelta = Ka(), this.projectionDeltaWithTransform = Ka() } setAnimationOrigin(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const n = this.snapshot,
                                r = n ? n.latestValues : {},
                                a = { ...this.latestValues },
                                o = Ka();
                            this.relativeParent && this.relativeParent.options.layoutRoot || (this.relativeTarget = this.relativeTargetOrigin = void 0), this.attemptToResolveRelativeTarget = !t; const i = Za(),
                                l = (n ? n.source : void 0) !== (this.layout ? this.layout.source : void 0),
                                s = this.getStack(),
                                c = !s || s.members.length <= 1,
                                d = Boolean(l && !c && !0 === this.options.crossfade && !this.path.some(wi)); let u;
                            this.animationProgress = 0, this.mixTargetDelta = t => { const n = t / 1e3; var s, h;
                                yi(o.x, e.x, n), yi(o.y, e.y, n), this.setTargetDelta(o), this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout && (_a(i, this.layout.layoutBox, this.relativeParent.layout.layoutBox), function(e, t, n, r) { bi(e.x, t.x, n.x, r), bi(e.y, t.y, n.y, r) }(this.relativeTarget, this.relativeTargetOrigin, i, n), u && (s = this.relativeTarget, h = u, Wo(s.x, h.x) && Wo(s.y, h.y)) && (this.isProjectionDirty = !1), u || (u = Za()), Vo(u, this.relativeTarget)), l && (this.animationValues = a, function(e, t, n, r, a, o) { a ? (e.opacity = Mr(0, void 0 !== n.opacity ? n.opacity : 1, Ho(r)), e.opacityExit = Mr(void 0 !== t.opacity ? t.opacity : 1, 0, Lo(r))) : o && (e.opacity = Mr(void 0 !== t.opacity ? t.opacity : 1, void 0 !== n.opacity ? n.opacity : 1, r)); for (let i = 0; i < Mo; i++) { const a = "border".concat(So[i], "Radius"); let o = To(t, a),
                                            l = To(n, a);
                                        void 0 === o && void 0 === l || (o || (o = 0), l || (l = 0), 0 === o || 0 === l || Co(o) === Co(l) ? (e[a] = Math.max(Mr(Eo(o), Eo(l), r), 0), (ie.test(l) || ie.test(o)) && (e[a] += "%")) : e[a] = l) }(t.rotate || n.rotate) && (e.rotate = Mr(t.rotate || 0, n.rotate || 0, r)) }(a, r, this.latestValues, n, d, c)), this.root.scheduleUpdateProjection(), this.scheduleRender(), this.animationProgress = n }, this.mixTargetDelta(this.options.layoutRoot ? 1e3 : 0) } startAnimation(e) { this.notifyListeners("animationStart"), this.currentAnimation && this.currentAnimation.stop(), this.resumingFrom && this.resumingFrom.currentAnimation && this.resumingFrom.currentAnimation.stop(), this.pendingAnimation && (ot(this.pendingAnimation), this.pendingAnimation = void 0), this.pendingAnimation = at.update((() => { yo.hasAnimatedSinceResize = !0, this.currentAnimation = function(e, t, n) { const r = K(e) ? e : la(e); return r.start(ra("", r, t, n)), r.animation }(0, 1e3, { ...e, onUpdate: t => { this.mixTargetDelta(t), e.onUpdate && e.onUpdate(t) }, onComplete: () => { e.onComplete && e.onComplete(), this.completeAnimation() } }), this.resumingFrom && (this.resumingFrom.currentAnimation = this.currentAnimation), this.pendingAnimation = void 0 })) } completeAnimation() { this.resumingFrom && (this.resumingFrom.currentAnimation = void 0, this.resumingFrom.preserveOpacity = void 0); const e = this.getStack();
                            e && e.exitAnimationComplete(), this.resumingFrom = this.currentAnimation = this.animationValues = void 0, this.notifyListeners("animationComplete") } finishAnimation() { this.currentAnimation && (this.mixTargetDelta && this.mixTargetDelta(1e3), this.currentAnimation.stop()), this.completeAnimation() } applyTransformsToTarget() { const e = this.getLead(); let { targetWithTransforms: t, target: n, layout: r, latestValues: a } = e; if (t && n && r) { if (this !== e && this.layout && r && Si(this.options.animationType, this.layout.layoutBox, r.layoutBox)) { n = this.target || Za(); const t = Ra(this.layout.layoutBox.x);
                                    n.x.min = e.target.x.min, n.x.max = n.x.min + t; const r = Ra(this.layout.layoutBox.y);
                                    n.y.min = e.target.y.min, n.y.max = n.y.min + r } Vo(t, n), uo(t, a), Da(this.projectionDeltaWithTransform, this.layoutCorrected, t, a) } } registerSharedNode(e, t) { this.sharedNodes.has(e) || this.sharedNodes.set(e, new Zo);
                            this.sharedNodes.get(e).add(t); const n = t.options.initialPromotionConfig;
                            t.promote({ transition: n ? n.transition : void 0, preserveFollowOpacity: n && n.shouldPreserveFollowOpacity ? n.shouldPreserveFollowOpacity(t) : void 0 }) } isLead() { const e = this.getStack(); return !e || e.lead === this } getLead() { var e; const { layoutId: t } = this.options; return t && (null === (e = this.getStack()) || void 0 === e ? void 0 : e.lead) || this } getPrevLead() { var e; const { layoutId: t } = this.options; return t ? null === (e = this.getStack()) || void 0 === e ? void 0 : e.prevLead : void 0 } getStack() { const { layoutId: e } = this.options; if (e) return this.root.sharedNodes.get(e) } promote() { let { needsReset: e, transition: t, preserveFollowOpacity: n } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const r = this.getStack();
                            r && r.promote(this, n), e && (this.projectionDelta = void 0, this.needsReset = !0), t && this.setOptions({ transition: t }) } relegate() { const e = this.getStack(); return !!e && e.relegate(this) } resetSkewAndRotation() { const { visualElement: e } = this.options; if (!e) return; let t = !1; const { latestValues: n } = e; if ((n.z || n.rotate || n.rotateX || n.rotateY || n.rotateZ || n.skewX || n.skewY) && (t = !0), !t) return; const r = {};
                            n.z && ni("z", e, r, this.animationValues); for (let a = 0; a < Jo.length; a++) ni("rotate".concat(Jo[a]), e, r, this.animationValues), ni("skew".concat(Jo[a]), e, r, this.animationValues);
                            e.render(); for (const a in r) e.setStaticValue(a, r[a]), this.animationValues && (this.animationValues[a] = r[a]);
                            e.scheduleRender() } getProjectionStyles(e) { var t, n; if (!this.instance || this.isSVG) return; if (!this.isVisible) return ei; const r = { visibility: "" },
                                a = this.getTransformTemplate(); if (this.needsReset) return this.needsReset = !1, r.opacity = "", r.pointerEvents = Ze(null === e || void 0 === e ? void 0 : e.pointerEvents) || "", r.transform = a ? a(this.latestValues, "") : "none", r; const o = this.getLead(); if (!this.projectionDelta || !this.layout || !o.target) { const t = {}; return this.options.layoutId && (t.opacity = void 0 !== this.latestValues.opacity ? this.latestValues.opacity : 1, t.pointerEvents = Ze(null === e || void 0 === e ? void 0 : e.pointerEvents) || ""), this.hasProjected && !Ja(this.latestValues) && (t.transform = a ? a({}, "") : "none", this.hasProjected = !1), t } const i = o.animationValues || o.latestValues;
                            this.applyTransformsToTarget(), r.transform = function(e, t, n) { let r = ""; const a = e.x.translate / t.x,
                                    o = e.y.translate / t.y,
                                    i = (null === n || void 0 === n ? void 0 : n.z) || 0; if ((a || o || i) && (r = "translate3d(".concat(a, "px, ").concat(o, "px, ").concat(i, "px) ")), 1 === t.x && 1 === t.y || (r += "scale(".concat(1 / t.x, ", ").concat(1 / t.y, ") ")), n) { const { transformPerspective: e, rotate: t, rotateX: a, rotateY: o, skewX: i, skewY: l } = n;
                                    e && (r = "perspective(".concat(e, "px) ").concat(r)), t && (r += "rotate(".concat(t, "deg) ")), a && (r += "rotateX(".concat(a, "deg) ")), o && (r += "rotateY(".concat(o, "deg) ")), i && (r += "skewX(".concat(i, "deg) ")), l && (r += "skewY(".concat(l, "deg) ")) } const l = e.x.scale * t.x,
                                    s = e.y.scale * t.y; return 1 === l && 1 === s || (r += "scale(".concat(l, ", ").concat(s, ")")), r || "none" }(this.projectionDeltaWithTransform, this.treeScale, i), a && (r.transform = a(i, r.transform)); const { x: l, y: s } = this.projectionDelta;
                            r.transformOrigin = "".concat(100 * l.origin, "% ").concat(100 * s.origin, "% 0"), o.animationValues ? r.opacity = o === this ? null !== (n = null !== (t = i.opacity) && void 0 !== t ? t : this.latestValues.opacity) && void 0 !== n ? n : 1 : this.preserveOpacity ? this.latestValues.opacity : i.opacityExit : r.opacity = o === this ? void 0 !== i.opacity ? i.opacity : "" : void 0 !== i.opacityExit ? i.opacityExit : 0; for (const c in W) { if (void 0 === i[c]) continue; const { correct: e, applyTo: t } = W[c], n = "none" === r.transform ? i[c] : e(i[c], o); if (t) { const e = t.length; for (let a = 0; a < e; a++) r[t[a]] = n } else r[c] = n } return this.options.layoutId && (r.pointerEvents = o === this ? Ze(null === e || void 0 === e ? void 0 : e.pointerEvents) || "" : "none"), r } clearSnapshot() { this.resumeFrom = this.snapshot = void 0 } resetTree() { this.root.nodes.forEach((e => { var t; return null === (t = e.currentAnimation) || void 0 === t ? void 0 : t.stop() })), this.root.nodes.forEach(di), this.root.sharedNodes.clear() } } }

                function oi(e) { e.updateLayout() }

                function ii(e) { var t; const n = (null === (t = e.resumeFrom) || void 0 === t ? void 0 : t.snapshot) || e.snapshot; if (e.isLead() && e.layout && n && e.hasListeners("didUpdate")) { const { layoutBox: t, measuredBox: r } = e.layout, { animationType: a } = e.options, o = n.source !== e.layout.source; "size" === a ? Ya((e => { const r = o ? n.measuredBox[e] : n.layoutBox[e],
                                a = Ra(r);
                            r.min = t[e].min, r.max = r.min + a })) : Si(a, n.layoutBox, t) && Ya((r => { const a = o ? n.measuredBox[r] : n.layoutBox[r],
                                i = Ra(t[r]);
                            a.max = a.min + i, e.relativeTarget && !e.currentAnimation && (e.isProjectionDirty = !0, e.relativeTarget[r].max = e.relativeTarget[r].min + i) })); const i = Ka();
                        Da(i, t, n.layoutBox); const l = Ka();
                        o ? Da(l, e.applyTransform(r, !0), n.measuredBox) : Da(l, t, n.layoutBox); const s = !Bo(i); let c = !1; if (!e.resumeFrom) { const r = e.getClosestProjectingParent(); if (r && !r.resumeFrom) { const { snapshot: a, layout: o } = r; if (a && o) { const i = Za();
                                    _a(i, n.layoutBox, a.layoutBox); const l = Za();
                                    _a(l, t, o.layoutBox), qo(i, l) || (c = !0), r.options.layoutRoot && (e.relativeTarget = l, e.relativeTargetOrigin = i, e.relativeParent = r) } } } e.notifyListeners("didUpdate", { layout: t, snapshot: n, delta: l, layoutDelta: i, hasLayoutChanged: s, hasRelativeTargetChanged: c }) } else if (e.isLead()) { const { onExitComplete: t } = e.options;
                        t && t() } e.options.transition = void 0 }

                function li(e) { Qo && $o.totalNodes++, e.parent && (e.isProjecting() || (e.isProjectionDirty = e.parent.isProjectionDirty), e.isSharedProjectionDirty || (e.isSharedProjectionDirty = Boolean(e.isProjectionDirty || e.parent.isProjectionDirty || e.parent.isSharedProjectionDirty)), e.isTransformDirty || (e.isTransformDirty = e.parent.isTransformDirty)) }

                function si(e) { e.isProjectionDirty = e.isSharedProjectionDirty = e.isTransformDirty = !1 }

                function ci(e) { e.clearSnapshot() }

                function di(e) { e.clearMeasurements() }

                function ui(e) { e.isLayoutDirty = !1 }

                function hi(e) { const { visualElement: t } = e.options;
                    t && t.getProps().onBeforeLayoutMeasure && t.notify("BeforeLayoutMeasure"), e.resetTransform() }

                function mi(e) { e.finishAnimation(), e.targetDelta = e.relativeTarget = e.target = void 0, e.isProjectionDirty = !0 }

                function pi(e) { e.resolveTargetDelta() }

                function fi(e) { e.calcProjection() }

                function vi(e) { e.resetSkewAndRotation() }

                function gi(e) { e.removeLeadSnapshot() }

                function yi(e, t, n) { e.translate = Mr(t.translate, 0, n), e.scale = Mr(t.scale, 1, n), e.origin = t.origin, e.originPoint = t.originPoint }

                function bi(e, t, n, r) { e.min = Mr(t.min, n.min, r), e.max = Mr(t.max, n.max, r) }

                function wi(e) { return e.animationValues && void 0 !== e.animationValues.opacityExit } const zi = { duration: .45, ease: [.4, 0, .1, 1] },
                    xi = e => "undefined" !== typeof navigator && navigator.userAgent && navigator.userAgent.toLowerCase().includes(e),
                    Ai = xi("applewebkit/") && !xi("chrome/") ? Math.round : rt.l;

                function ki(e) { e.min = Ai(e.min), e.max = Ai(e.max) }

                function Si(e, t, n) { return "position" === e || "preserve-aspect" === e && (r = Go(t), a = Go(n), o = .2, !(Math.abs(r - a) <= o)); var r, a, o }

                function Mi(e) { var t; return e !== e.root && (null === (t = e.scroll) || void 0 === t ? void 0 : t.wasRoot) } const Ei = ai({ attachResizeListener: (e, t) => dt(e, "resize", t), measureScroll: () => ({ x: document.documentElement.scrollLeft || document.body.scrollLeft, y: document.documentElement.scrollTop || document.body.scrollTop }), checkIsScrollRoot: () => !0 }),
                    Ci = { current: void 0 },
                    Ti = ai({ measureScroll: e => ({ x: e.scrollLeft, y: e.scrollTop }), defaultParent: () => { if (!Ci.current) { const e = new Ei({});
                                e.mount(window), e.setOptions({ layoutScroll: !0 }), Ci.current = e } return Ci.current }, resetTransform: (e, t) => { e.style.transform = void 0 !== t ? t : "none" }, checkIsScrollRoot: e => Boolean("fixed" === window.getComputedStyle(e).position) }),
                    Hi = { pan: { Feature: class extends xt { constructor() { super(...arguments), this.removePointerDownListener = rt.l } onPointerDown(e) { this.session = new Sa(e, this.createPanHandlers(), { transformPagePoint: this.node.getTransformPagePoint(), contextWindow: mo(this.node) }) } createPanHandlers() { const { onPanSessionStart: e, onPanStart: t, onPan: n, onPanEnd: r } = this.node.getProps(); return { onSessionStart: go(e), onStart: go(t), onMove: n, onEnd: (e, t) => { delete this.session, r && at.postRender((() => r(e, t))) } } } mount() { this.removePointerDownListener = pt(this.node.current, "pointerdown", (e => this.onPointerDown(e))) } update() { this.session && this.session.updateHandlers(this.createPanHandlers()) } unmount() { this.removePointerDownListener(), this.session && this.session.end() } } }, drag: { Feature: class extends xt { constructor(e) { super(e), this.removeGroupControls = rt.l, this.removeListeners = rt.l, this.controls = new fo(e) } mount() { const { dragControls: e } = this.node.getProps();
                                    e && (this.removeGroupControls = e.subscribe(this.controls)), this.removeListeners = this.controls.addListeners() || rt.l } unmount() { this.removeGroupControls(), this.removeListeners() } }, ProjectionNode: Ti, MeasureLayout: Ao } },
                    Li = { current: null },
                    Ii = { current: !1 }; const ji = new WeakMap,
                    Vi = [...an, bn, Hn],
                    Oi = ["AnimationStart", "AnimationComplete", "Update", "BeforeLayoutMeasure", "LayoutMeasure", "LayoutAnimationStart", "LayoutAnimationComplete"],
                    Ri = C.length;
                class Pi { scrapeMotionValuesFromProps(e, t, n) { return {} } constructor(e) { let { parent: t, props: n, presenceContext: r, reducedMotionConfig: a, blockInitialAnimation: o, visualState: i } = e, l = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                        this.applyWillChange = !1, this.current = null, this.children = new Set, this.isVariantNode = !1, this.isControllingVariants = !1, this.shouldReduceMotion = null, this.values = new Map, this.KeyframeResolver = hn, this.features = {}, this.valueSubscriptions = new Map, this.prevMotionValues = {}, this.events = {}, this.propEventSubscriptions = {}, this.notifyUpdate = () => this.notify("Update", this.latestValues), this.render = () => { this.isRenderScheduled = !1, this.current && (this.triggerBuild(), this.renderInstance(this.current, this.renderState, this.props.style, this.projection)) }, this.isRenderScheduled = !1, this.scheduleRender = () => { this.isRenderScheduled || (this.isRenderScheduled = !0, at.render(this.render, !1, !0)) }; const { latestValues: s, renderState: c } = i;
                        this.latestValues = s, this.baseTarget = { ...s }, this.initialValues = n.initial ? { ...s } : {}, this.renderState = c, this.parent = t, this.props = n, this.presenceContext = r, this.depth = t ? t.depth + 1 : 0, this.reducedMotionConfig = a, this.options = l, this.blockInitialAnimation = Boolean(o), this.isControllingVariants = T(n), this.isVariantNode = H(n), this.isVariantNode && (this.variantChildren = new Set), this.manuallyAnimateOnMount = Boolean(t && t.current); const { willChange: d, ...u } = this.scrapeMotionValuesFromProps(n, {}, this); for (const h in u) { const e = u[h];
                            void 0 !== s[h] && K(e) && e.set(s[h], !1) } } mount(e) { this.current = e, ji.set(e, this), this.projection && !this.projection.instance && this.projection.mount(e), this.parent && this.isVariantNode && !this.isControllingVariants && (this.removeFromVariantTree = this.parent.addVariantChild(this)), this.values.forEach(((e, t) => this.bindToMotionValue(t, e))), Ii.current || function() { if (Ii.current = !0, O.B)
                                if (window.matchMedia) { const e = window.matchMedia("(prefers-reduced-motion)"),
                                        t = () => Li.current = e.matches;
                                    e.addListener(t), t() } else Li.current = !1 }(), this.shouldReduceMotion = "never" !== this.reducedMotionConfig && ("always" === this.reducedMotionConfig || Li.current), this.parent && this.parent.children.add(this), this.update(this.props, this.presenceContext) } unmount() { ji.delete(this.current), this.projection && this.projection.unmount(), ot(this.notifyUpdate), ot(this.render), this.valueSubscriptions.forEach((e => e())), this.removeFromVariantTree && this.removeFromVariantTree(), this.parent && this.parent.children.delete(this); for (const e in this.events) this.events[e].clear(); for (const e in this.features) { const t = this.features[e];
                            t && (t.unmount(), t.isMounted = !1) } this.current = null } bindToMotionValue(e, t) { const n = q.has(e),
                            r = t.on("change", (t => { this.latestValues[e] = t, this.props.onUpdate && at.preRender(this.notifyUpdate), n && this.projection && (this.projection.isTransformDirty = !0) })),
                            a = t.on("renderRequest", this.scheduleRender);
                        this.valueSubscriptions.set(e, (() => { r(), a(), t.owner && t.stop() })) } sortNodePosition(e) { return this.current && this.sortInstanceNodePosition && this.type === e.type ? this.sortInstanceNodePosition(this.current, e.current) : 0 } updateFeatures() { let e = "animation"; for (e in V) { const t = V[e]; if (!t) continue; const { isEnabled: n, Feature: r } = t; if (!this.features[e] && r && n(this.props) && (this.features[e] = new r(this)), this.features[e]) { const t = this.features[e];
                                t.isMounted ? t.update() : (t.mount(), t.isMounted = !0) } } } triggerBuild() { this.build(this.renderState, this.latestValues, this.props) } measureViewportBox() { return this.current ? this.measureInstanceViewportBox(this.current, this.props) : Za() } getStaticValue(e) { return this.latestValues[e] } setStaticValue(e, t) { this.latestValues[e] = t } update(e, t) {
                        (e.transformTemplate || this.props.transformTemplate) && this.scheduleRender(), this.prevProps = this.props, this.props = e, this.prevPresenceContext = this.presenceContext, this.presenceContext = t; for (let n = 0; n < Oi.length; n++) { const t = Oi[n];
                            this.propEventSubscriptions[t] && (this.propEventSubscriptions[t](), delete this.propEventSubscriptions[t]); const r = e["on" + t];
                            r && (this.propEventSubscriptions[t] = this.on(t, r)) } this.prevMotionValues = function(e, t, n) { for (const r in t) { const a = t[r],
                                    o = n[r]; if (K(a)) e.addValue(r, a);
                                else if (K(o)) e.addValue(r, la(a, { owner: e }));
                                else if (o !== a)
                                    if (e.hasValue(r)) { const t = e.getValue(r);!0 === t.liveStyle ? t.jump(a) : t.hasAnimated || t.set(a) } else { const t = e.getStaticValue(r);
                                        e.addValue(r, la(void 0 !== t ? t : a, { owner: e })) } } for (const r in n) void 0 === t[r] && e.removeValue(r); return t }(this, this.scrapeMotionValuesFromProps(e, this.prevProps, this), this.prevMotionValues), this.handleChildMotionValue && this.handleChildMotionValue() } getProps() { return this.props } getVariant(e) { return this.props.variants ? this.props.variants[e] : void 0 } getDefaultTransition() { return this.props.transition } getTransformPagePoint() { return this.props.transformPagePoint } getClosestVariantNode() { return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : void 0 } getVariantContext() { if (arguments.length > 0 && void 0 !== arguments[0] && arguments[0]) return this.parent ? this.parent.getVariantContext() : void 0; if (!this.isControllingVariants) { const e = this.parent && this.parent.getVariantContext() || {}; return void 0 !== this.props.initial && (e.initial = this.props.initial), e } const e = {}; for (let t = 0; t < Ri; t++) { const n = C[t],
                                r = this.props[n];
                            (S(r) || !1 === r) && (e[n] = r) } return e } addVariantChild(e) { const t = this.getClosestVariantNode(); if (t) return t.variantChildren && t.variantChildren.add(e), () => t.variantChildren.delete(e) } addValue(e, t) { const n = this.values.get(e);
                        t !== n && (n && this.removeValue(e), this.bindToMotionValue(e, t), this.values.set(e, t), this.latestValues[e] = t.get()) } removeValue(e) { this.values.delete(e); const t = this.valueSubscriptions.get(e);
                        t && (t(), this.valueSubscriptions.delete(e)), delete this.latestValues[e], this.removeValueFromRenderState(e, this.renderState) } hasValue(e) { return this.values.has(e) } getValue(e, t) { if (this.props.values && this.props.values[e]) return this.props.values[e]; let n = this.values.get(e); return void 0 === n && void 0 !== t && (n = la(null === t ? void 0 : t, { owner: this }), this.addValue(e, n)), n } readValue(e, t) { var n; let r = void 0 === this.latestValues[e] && this.current ? null !== (n = this.getBaseTargetFromProps(this.props, e)) && void 0 !== n ? n : this.readValueFromInstance(this.current, e, this.options) : this.latestValues[e]; var a; return void 0 !== r && null !== r && ("string" === typeof r && (Kt(r) || qt(r)) ? r = parseFloat(r) : (a = r, !Vi.find(rn(a)) && Hn.test(t) && (r = Pn(e, t))), this.setBaseTarget(e, K(r) ? r.get() : r)), K(r) ? r.get() : r } setBaseTarget(e, t) { this.baseTarget[e] = t } getBaseTarget(e) { var t; const { initial: n } = this.props; let r; if ("string" === typeof n || "object" === typeof n) { const a = We(this.props, n, null === (t = this.presenceContext) || void 0 === t ? void 0 : t.custom);
                            a && (r = a[e]) } if (n && void 0 !== r) return r; const a = this.getBaseTargetFromProps(this.props, e); return void 0 === a || K(a) ? void 0 !== this.initialValues[e] && void 0 === r ? void 0 : this.baseTarget[e] : a } on(e, t) { return this.events[e] || (this.events[e] = new aa), this.events[e].add(t) } notify(e) { if (this.events[e]) { for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
                            this.events[e].notify(...n) } } } class Di extends Pi { constructor() { super(...arguments), this.KeyframeResolver = Fn } sortInstanceNodePosition(e, t) { return 2 & e.compareDocumentPosition(t) ? 1 : -1 } getBaseTargetFromProps(e, t) { return e.style ? e.style[t] : void 0 } removeValueFromRenderState(e, t) { let { vars: n, style: r } = t;
                        delete n[e], delete r[e] } } class Fi extends Di { constructor() { super(...arguments), this.type = "html", this.applyWillChange = !0, this.renderInstance = Pe } readValueFromInstance(e, t) { if (q.has(t)) { const e = Rn(t); return e && e.default || 0 } { const r = (n = e, window.getComputedStyle(n)),
                                a = (ve(t) ? r.getPropertyValue(t) : r[t]) || 0; return "string" === typeof a ? a.trim() : a } var n } measureInstanceViewportBox(e, t) { let { transformPagePoint: n } = t; return ho(e, n) } build(e, t, n) { we(e, t, n.transformTemplate) } scrapeMotionValuesFromProps(e, t, n) { return Ne(e, t, n) } handleChildMotionValue() { this.childSubscription && (this.childSubscription(), delete this.childSubscription); const { children: e } = this.props;
                        K(e) && (this.childSubscription = e.on("change", (e => { this.current && (this.current.textContent = "".concat(e)) }))) } } class Ni extends Di { constructor() { super(...arguments), this.type = "svg", this.isSVGTag = !1, this.measureInstanceViewportBox = Za } getBaseTargetFromProps(e, t) { return e[t] } readValueFromInstance(e, t) { if (q.has(t)) { const e = Rn(t); return e && e.default || 0 } return t = De.has(t) ? t : d(t), e.getAttribute(t) } scrapeMotionValuesFromProps(e, t, n) { return _e(e, t, n) } build(e, t, n) { Ie(e, t, this.isSVGTag, n.transformTemplate) } renderInstance(e, t, n, r) { Fe(e, t, 0, r) } mount(e) { this.isSVGTag = Ve(e.tagName), super.mount(e) } } const _i = (e, t) => B(e) ? new Ni(t) : new Fi(t, { allowProjection: e !== a.Fragment }),
                    Bi = { ...Aa, ...It, ...Hi, ...{ layout: { ProjectionNode: Ti, MeasureLayout: Ao } } },
                    Wi = N(((e, t) => function(e, t, n, r) { let { forwardMotionProps: a = !1 } = t; return { ...B(e) ? st : ct, preloadedFeatures: n, useRender: Re(a), createVisualElement: r, Component: e } }(e, t, Bi, _i))) }, 58129: (e, t, n) => { "use strict";
                n.d(t, { $: () => a, V: () => o }); var r = n(51892); let a = r.l,
                    o = r.l }, 14735: (e, t, n) => { "use strict";
                n.d(t, { B: () => r }); const r = "undefined" !== typeof window }, 51892: (e, t, n) => { "use strict";
                n.d(t, { l: () => r }); const r = e => e }, 34930: (e, t, n) => { "use strict";
                n.d(t, { M: () => a }); var r = n(65043);

                function a(e) { const t = (0, r.useRef)(null); return null === t.current && (t.current = e()), t.current } }, 40293: (e, t, n) => { "use strict";
                n.d(t, { E: () => a }); var r = n(65043); const a = n(14735).B ? r.useLayoutEffect : r.useEffect }, 24241: (e, t, n) => { "use strict";
                n.d(t, { c9: () => pr, R2: () => xn });
                class r extends Error {} class a extends r { constructor(e) { super("Invalid DateTime: ".concat(e.toMessage())) } } class o extends r { constructor(e) { super("Invalid Interval: ".concat(e.toMessage())) } } class i extends r { constructor(e) { super("Invalid Duration: ".concat(e.toMessage())) } } class l extends r {} class s extends r { constructor(e) { super("Invalid unit ".concat(e)) } } class c extends r {} class d extends r { constructor() { super("Zone is an abstract class") } } const u = "numeric",
                    h = "short",
                    m = "long",
                    p = { year: u, month: u, day: u },
                    f = { year: u, month: h, day: u },
                    v = { year: u, month: h, day: u, weekday: h },
                    g = { year: u, month: m, day: u },
                    y = { year: u, month: m, day: u, weekday: m },
                    b = { hour: u, minute: u },
                    w = { hour: u, minute: u, second: u },
                    z = { hour: u, minute: u, second: u, timeZoneName: h },
                    x = { hour: u, minute: u, second: u, timeZoneName: m },
                    A = { hour: u, minute: u, hourCycle: "h23" },
                    k = { hour: u, minute: u, second: u, hourCycle: "h23" },
                    S = { hour: u, minute: u, second: u, hourCycle: "h23", timeZoneName: h },
                    M = { hour: u, minute: u, second: u, hourCycle: "h23", timeZoneName: m },
                    E = { year: u, month: u, day: u, hour: u, minute: u },
                    C = { year: u, month: u, day: u, hour: u, minute: u, second: u },
                    T = { year: u, month: h, day: u, hour: u, minute: u },
                    H = { year: u, month: h, day: u, hour: u, minute: u, second: u },
                    L = { year: u, month: h, day: u, weekday: h, hour: u, minute: u },
                    I = { year: u, month: m, day: u, hour: u, minute: u, timeZoneName: h },
                    j = { year: u, month: m, day: u, hour: u, minute: u, second: u, timeZoneName: h },
                    V = { year: u, month: m, day: u, weekday: m, hour: u, minute: u, timeZoneName: m },
                    O = { year: u, month: m, day: u, weekday: m, hour: u, minute: u, second: u, timeZoneName: m };
                class R { get type() { throw new d } get name() { throw new d } get ianaName() { return this.name } get isUniversal() { throw new d } offsetName(e, t) { throw new d } formatOffset(e, t) { throw new d } offset(e) { throw new d } equals(e) { throw new d } get isValid() { throw new d } } let P = null;
                class D extends R { static get instance() { return null === P && (P = new D), P } get type() { return "system" } get name() { return (new Intl.DateTimeFormat).resolvedOptions().timeZone } get isUniversal() { return !1 } offsetName(e, t) { let { format: n, locale: r } = t; return $e(e, n, r) } formatOffset(e, t) { return tt(this.offset(e), t) } offset(e) { return -new Date(e).getTimezoneOffset() } equals(e) { return "system" === e.type } get isValid() { return !0 } } let F = {}; const N = { year: 0, month: 1, day: 2, era: 3, hour: 4, minute: 5, second: 6 }; let _ = {};
                class B extends R { static create(e) { return _[e] || (_[e] = new B(e)), _[e] } static resetCache() { _ = {}, F = {} } static isValidSpecifier(e) { return this.isValidZone(e) } static isValidZone(e) { if (!e) return !1; try { return new Intl.DateTimeFormat("en-US", { timeZone: e }).format(), !0 } catch (t) { return !1 } } constructor(e) { super(), this.zoneName = e, this.valid = B.isValidZone(e) } get type() { return "iana" } get name() { return this.zoneName } get isUniversal() { return !1 } offsetName(e, t) { let { format: n, locale: r } = t; return $e(e, n, r, this.name) } formatOffset(e, t) { return tt(this.offset(e), t) } offset(e) { const t = new Date(e); if (isNaN(t)) return NaN; const n = (r = this.name, F[r] || (F[r] = new Intl.DateTimeFormat("en-US", { hour12: !1, timeZone: r, year: "numeric", month: "2-digit", day: "2-digit", hour: "2-digit", minute: "2-digit", second: "2-digit", era: "short" })), F[r]); var r; let [a, o, i, l, s, c, d] = n.formatToParts ? function(e, t) { const n = e.formatToParts(t),
                                r = []; for (let a = 0; a < n.length; a++) { const { type: e, value: t } = n[a], o = N[e]; "era" === e ? r[o] = t : He(o) || (r[o] = parseInt(t, 10)) } return r }(n, t) : function(e, t) { const n = e.format(t).replace(/\u200E/g, ""),
                                r = /(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),
                                [, a, o, i, l, s, c, d] = r; return [i, a, o, l, s, c, d] }(n, t); "BC" === l && (a = 1 - Math.abs(a)); let u = +t; const h = u % 1e3; return u -= h >= 0 ? h : 1e3 + h, (Ke({ year: a, month: o, day: i, hour: 24 === s ? 0 : s, minute: c, second: d, millisecond: 0 }) - u) / 6e4 } equals(e) { return "iana" === e.type && e.name === this.name } get isValid() { return this.valid } } let W = {}; let U = {};

                function q(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const n = JSON.stringify([e, t]); let r = U[n]; return r || (r = new Intl.DateTimeFormat(e, t), U[n] = r), r } let G = {}; let K = {}; let Z = null; let Y = {};

                function X(e, t, n, r) { const a = e.listingMode(); return "error" === a ? null : "en" === a ? n(t) : r(t) } class $ { constructor(e, t, n) { this.padTo = n.padTo || 0, this.floor = n.floor || !1; const { padTo: r, floor: a, ...o } = n; if (!t || Object.keys(o).length > 0) { const t = { useGrouping: !1, ...n };
                            n.padTo > 0 && (t.minimumIntegerDigits = n.padTo), this.inf = function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const n = JSON.stringify([e, t]); let r = G[n]; return r || (r = new Intl.NumberFormat(e, t), G[n] = r), r }(e, t) } } format(e) { if (this.inf) { const t = this.floor ? Math.floor(e) : e; return this.inf.format(t) } return Fe(this.floor ? Math.floor(e) : We(e, 3), this.padTo) } } class Q { constructor(e, t, n) { let r; if (this.opts = n, this.originalZone = void 0, this.opts.timeZone) this.dt = e;
                        else if ("fixed" === e.zone.type) { const t = e.offset / 60 * -1,
                                n = t >= 0 ? "Etc/GMT+".concat(t) : "Etc/GMT".concat(t);
                            0 !== e.offset && B.create(n).valid ? (r = n, this.dt = e) : (r = "UTC", this.dt = 0 === e.offset ? e : e.setZone("UTC").plus({ minutes: e.offset }), this.originalZone = e.zone) } else "system" === e.zone.type ? this.dt = e : "iana" === e.zone.type ? (this.dt = e, r = e.zone.name) : (r = "UTC", this.dt = e.setZone("UTC").plus({ minutes: e.offset }), this.originalZone = e.zone); const a = { ...this.opts };
                        a.timeZone = a.timeZone || r, this.dtf = q(t, a) } format() { return this.originalZone ? this.formatToParts().map((e => { let { value: t } = e; return t })).join("") : this.dtf.format(this.dt.toJSDate()) } formatToParts() { const e = this.dtf.formatToParts(this.dt.toJSDate()); return this.originalZone ? e.map((e => { if ("timeZoneName" === e.type) { const t = this.originalZone.offsetName(this.dt.ts, { locale: this.dt.locale, format: this.opts.timeZoneName }); return { ...e, value: t } } return e })) : e } resolvedOptions() { return this.dtf.resolvedOptions() } } class J { constructor(e, t, n) { this.opts = { style: "long", ...n }, !t && je() && (this.rtf = function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const { base: n, ...r } = t, a = JSON.stringify([e, r]); let o = K[a]; return o || (o = new Intl.RelativeTimeFormat(e, t), K[a] = o), o }(e, n)) } format(e, t) { return this.rtf ? this.rtf.format(e, t) : function(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "always",
                                r = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; const a = { years: ["year", "yr."], quarters: ["quarter", "qtr."], months: ["month", "mo."], weeks: ["week", "wk."], days: ["day", "day", "days"], hours: ["hour", "hr."], minutes: ["minute", "min."], seconds: ["second", "sec."] },
                                o = -1 === ["hours", "minutes", "seconds"].indexOf(e); if ("auto" === n && o) { const n = "days" === e; switch (t) {
                                    case 1:
                                        return n ? "tomorrow" : "next ".concat(a[e][0]);
                                    case -1:
                                        return n ? "yesterday" : "last ".concat(a[e][0]);
                                    case 0:
                                        return n ? "today" : "this ".concat(a[e][0]) } } const i = Object.is(t, -0) || t < 0,
                                l = Math.abs(t),
                                s = 1 === l,
                                c = a[e],
                                d = r ? s ? c[1] : c[2] || c[1] : s ? a[e][0] : e; return i ? "".concat(l, " ").concat(d, " ago") : "in ".concat(l, " ").concat(d) }(t, e, this.opts.numeric, "long" !== this.opts.style) } formatToParts(e, t) { return this.rtf ? this.rtf.formatToParts(e, t) : [] } } const ee = { firstDay: 1, minimalDays: 4, weekend: [6, 7] };
                class te { static fromOpts(e) { return te.create(e.locale, e.numberingSystem, e.outputCalendar, e.weekSettings, e.defaultToEN) } static create(e, t, n, r) { let a = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; const o = e || pe.defaultLocale,
                            i = o || (a ? "en-US" : Z || (Z = (new Intl.DateTimeFormat).resolvedOptions().locale, Z)),
                            l = t || pe.defaultNumberingSystem,
                            s = n || pe.defaultOutputCalendar,
                            c = Pe(r) || pe.defaultWeekSettings; return new te(i, l, s, c, o) } static resetCache() { Z = null, U = {}, G = {}, K = {} } static fromObject() { let { locale: e, numberingSystem: t, outputCalendar: n, weekSettings: r } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return te.create(e, t, n, r) } constructor(e, t, n, r, a) { const [o, i, l] = function(e) { const t = e.indexOf("-x-"); - 1 !== t && (e = e.substring(0, t)); const n = e.indexOf("-u-"); if (-1 === n) return [e]; { let t, a; try { t = q(e).resolvedOptions(), a = e } catch (r) { const o = e.substring(0, n);
                                    t = q(o).resolvedOptions(), a = o } const { numberingSystem: o, calendar: i } = t; return [a, o, i] } }(e);
                        this.locale = o, this.numberingSystem = t || i || null, this.outputCalendar = n || l || null, this.weekSettings = r, this.intl = function(e, t, n) { return n || t ? (e.includes("-u-") || (e += "-u"), n && (e += "-ca-".concat(n)), t && (e += "-nu-".concat(t)), e) : e }(this.locale, this.numberingSystem, this.outputCalendar), this.weekdaysCache = { format: {}, standalone: {} }, this.monthsCache = { format: {}, standalone: {} }, this.meridiemCache = null, this.eraCache = {}, this.specifiedLocale = a, this.fastNumbersCached = null } get fastNumbers() { var e; return null == this.fastNumbersCached && (this.fastNumbersCached = (!(e = this).numberingSystem || "latn" === e.numberingSystem) && ("latn" === e.numberingSystem || !e.locale || e.locale.startsWith("en") || "latn" === new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem)), this.fastNumbersCached } listingMode() { const e = this.isEnglish(),
                            t = (null === this.numberingSystem || "latn" === this.numberingSystem) && (null === this.outputCalendar || "gregory" === this.outputCalendar); return e && t ? "en" : "intl" } clone(e) { return e && 0 !== Object.getOwnPropertyNames(e).length ? te.create(e.locale || this.specifiedLocale, e.numberingSystem || this.numberingSystem, e.outputCalendar || this.outputCalendar, Pe(e.weekSettings) || this.weekSettings, e.defaultToEN || !1) : this } redefaultToEN() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return this.clone({ ...e, defaultToEN: !0 }) } redefaultToSystem() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return this.clone({ ...e, defaultToEN: !1 }) } months(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return X(this, e, it, (() => { const n = t ? { month: e, day: "numeric" } : { month: e },
                                r = t ? "format" : "standalone"; return this.monthsCache[r][e] || (this.monthsCache[r][e] = function(e) { const t = []; for (let n = 1; n <= 12; n++) { const r = pr.utc(2009, n, 1);
                                    t.push(e(r)) } return t }((e => this.extract(e, n, "month")))), this.monthsCache[r][e] })) } weekdays(e) { let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return X(this, e, dt, (() => { const n = t ? { weekday: e, year: "numeric", month: "long", day: "numeric" } : { weekday: e },
                                r = t ? "format" : "standalone"; return this.weekdaysCache[r][e] || (this.weekdaysCache[r][e] = function(e) { const t = []; for (let n = 1; n <= 7; n++) { const r = pr.utc(2016, 11, 13 + n);
                                    t.push(e(r)) } return t }((e => this.extract(e, n, "weekday")))), this.weekdaysCache[r][e] })) } meridiems() { return X(this, void 0, (() => ut), (() => { if (!this.meridiemCache) { const e = { hour: "numeric", hourCycle: "h12" };
                                this.meridiemCache = [pr.utc(2016, 11, 13, 9), pr.utc(2016, 11, 13, 19)].map((t => this.extract(t, e, "dayperiod"))) } return this.meridiemCache })) } eras(e) { return X(this, e, ft, (() => { const t = { era: e }; return this.eraCache[e] || (this.eraCache[e] = [pr.utc(-40, 1, 1), pr.utc(2017, 1, 1)].map((e => this.extract(e, t, "era")))), this.eraCache[e] })) } extract(e, t, n) { const r = this.dtFormatter(e, t).formatToParts().find((e => e.type.toLowerCase() === n)); return r ? r.value : null } numberFormatter() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return new $(this.intl, e.forceSimple || this.fastNumbers, e) } dtFormatter(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return new Q(e, this.intl, t) } relFormatter() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return new J(this.intl, this.isEnglish(), e) } listFormatter() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; const n = JSON.stringify([e, t]); let r = W[n]; return r || (r = new Intl.ListFormat(e, t), W[n] = r), r }(this.intl, e) } isEnglish() { return "en" === this.locale || "en-us" === this.locale.toLowerCase() || new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us") } getWeekSettings() { return this.weekSettings ? this.weekSettings : Ve() ? function(e) { let t = Y[e]; if (!t) { const n = new Intl.Locale(e);
                                t = "getWeekInfo" in n ? n.getWeekInfo() : n.weekInfo, Y[e] = t } return t }(this.locale) : ee } getStartOfWeek() { return this.getWeekSettings().firstDay } getMinDaysInFirstWeek() { return this.getWeekSettings().minimalDays } getWeekendDays() { return this.getWeekSettings().weekend } equals(e) { return this.locale === e.locale && this.numberingSystem === e.numberingSystem && this.outputCalendar === e.outputCalendar } } let ne = null;
                class re extends R { static get utcInstance() { return null === ne && (ne = new re(0)), ne } static instance(e) { return 0 === e ? re.utcInstance : new re(e) } static parseSpecifier(e) { if (e) { const t = e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i); if (t) return new re(Qe(t[1], t[2])) } return null } constructor(e) { super(), this.fixed = e } get type() { return "fixed" } get name() { return 0 === this.fixed ? "UTC" : "UTC".concat(tt(this.fixed, "narrow")) } get ianaName() { return 0 === this.fixed ? "Etc/UTC" : "Etc/GMT".concat(tt(-this.fixed, "narrow")) } offsetName() { return this.name } formatOffset(e, t) { return tt(this.fixed, t) } get isUniversal() { return !0 } offset() { return this.fixed } equals(e) { return "fixed" === e.type && e.fixed === this.fixed } get isValid() { return !0 } } class ae extends R { constructor(e) { super(), this.zoneName = e } get type() { return "invalid" } get name() { return this.zoneName } get isUniversal() { return !1 } offsetName() { return null } formatOffset() { return "" } offset() { return NaN } equals() { return !1 } get isValid() { return !1 } }

                function oe(e, t) { if (He(e) || null === e) return t; if (e instanceof R) return e; if ("string" === typeof e) { const n = e.toLowerCase(); return "default" === n ? t : "local" === n || "system" === n ? D.instance : "utc" === n || "gmt" === n ? re.utcInstance : re.parseSpecifier(n) || B.create(e) } return Le(e) ? re.instance(e) : "object" === typeof e && "offset" in e && "function" === typeof e.offset ? e : new ae(e) } let ie, le = () => Date.now(),
                    se = "system",
                    ce = null,
                    de = null,
                    ue = null,
                    he = 60,
                    me = null;
                class pe { static get now() { return le } static set now(e) { le = e } static set defaultZone(e) { se = e } static get defaultZone() { return oe(se, D.instance) } static get defaultLocale() { return ce } static set defaultLocale(e) { ce = e } static get defaultNumberingSystem() { return de } static set defaultNumberingSystem(e) { de = e } static get defaultOutputCalendar() { return ue } static set defaultOutputCalendar(e) { ue = e } static get defaultWeekSettings() { return me } static set defaultWeekSettings(e) { me = Pe(e) } static get twoDigitCutoffYear() { return he } static set twoDigitCutoffYear(e) { he = e % 100 } static get throwOnInvalid() { return ie } static set throwOnInvalid(e) { ie = e } static resetCaches() { te.resetCache(), B.resetCache() } } class fe { constructor(e, t) { this.reason = e, this.explanation = t } toMessage() { return this.explanation ? "".concat(this.reason, ": ").concat(this.explanation) : this.reason } } const ve = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],
                    ge = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];

                function ye(e, t) { return new fe("unit out of range", "you specified ".concat(t, " (of type ").concat(typeof t, ") as a ").concat(e, ", which is invalid")) }

                function be(e, t, n) { const r = new Date(Date.UTC(e, t - 1, n));
                    e < 100 && e >= 0 && r.setUTCFullYear(r.getUTCFullYear() - 1900); const a = r.getUTCDay(); return 0 === a ? 7 : a }

                function we(e, t, n) { return n + (Ue(e) ? ge : ve)[t - 1] }

                function ze(e, t) { const n = Ue(e) ? ge : ve,
                        r = n.findIndex((e => e < t)); return { month: r + 1, day: t - n[r] } }

                function xe(e, t) { return (e - t + 7) % 7 + 1 }

                function Ae(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 4,
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1; const { year: r, month: a, day: o } = e, i = we(r, a, o), l = xe(be(r, a, o), n); let s, c = Math.floor((i - l + 14 - t) / 7); return c < 1 ? (s = r - 1, c = Ye(s, t, n)) : c > Ye(r, t, n) ? (s = r + 1, c = 1) : s = r, { weekYear: s, weekNumber: c, weekday: l, ...nt(e) } }

                function ke(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 4,
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1; const { weekYear: r, weekNumber: a, weekday: o } = e, i = xe(be(r, 1, t), n), l = qe(r); let s, c = 7 * a + o - i - 7 + t;
                    c < 1 ? (s = r - 1, c += qe(s)) : c > l ? (s = r + 1, c -= qe(r)) : s = r; const { month: d, day: u } = ze(s, c); return { year: s, month: d, day: u, ...nt(e) } }

                function Se(e) { const { year: t, month: n, day: r } = e; return { year: t, ordinal: we(t, n, r), ...nt(e) } }

                function Me(e) { const { year: t, ordinal: n } = e, { month: r, day: a } = ze(t, n); return { year: t, month: r, day: a, ...nt(e) } }

                function Ee(e, t) { if (!He(e.localWeekday) || !He(e.localWeekNumber) || !He(e.localWeekYear)) { if (!He(e.weekday) || !He(e.weekNumber) || !He(e.weekYear)) throw new l("Cannot mix locale-based week fields with ISO-based week fields"); return He(e.localWeekday) || (e.weekday = e.localWeekday), He(e.localWeekNumber) || (e.weekNumber = e.localWeekNumber), He(e.localWeekYear) || (e.weekYear = e.localWeekYear), delete e.localWeekday, delete e.localWeekNumber, delete e.localWeekYear, { minDaysInFirstWeek: t.getMinDaysInFirstWeek(), startOfWeek: t.getStartOfWeek() } } return { minDaysInFirstWeek: 4, startOfWeek: 1 } }

                function Ce(e) { const t = Ie(e.year),
                        n = De(e.month, 1, 12),
                        r = De(e.day, 1, Ge(e.year, e.month)); return t ? n ? !r && ye("day", e.day) : ye("month", e.month) : ye("year", e.year) }

                function Te(e) { const { hour: t, minute: n, second: r, millisecond: a } = e, o = De(t, 0, 23) || 24 === t && 0 === n && 0 === r && 0 === a, i = De(n, 0, 59), l = De(r, 0, 59), s = De(a, 0, 999); return o ? i ? l ? !s && ye("millisecond", a) : ye("second", r) : ye("minute", n) : ye("hour", t) }

                function He(e) { return "undefined" === typeof e }

                function Le(e) { return "number" === typeof e }

                function Ie(e) { return "number" === typeof e && e % 1 === 0 }

                function je() { try { return "undefined" !== typeof Intl && !!Intl.RelativeTimeFormat } catch (e) { return !1 } }

                function Ve() { try { return "undefined" !== typeof Intl && !!Intl.Locale && ("weekInfo" in Intl.Locale.prototype || "getWeekInfo" in Intl.Locale.prototype) } catch (e) { return !1 } }

                function Oe(e, t, n) { if (0 !== e.length) return e.reduce(((e, r) => { const a = [t(r), r]; return e && n(e[0], a[0]) === e[0] ? e : a }), null)[1] }

                function Re(e, t) { return Object.prototype.hasOwnProperty.call(e, t) }

                function Pe(e) { if (null == e) return null; if ("object" !== typeof e) throw new c("Week settings must be an object"); if (!De(e.firstDay, 1, 7) || !De(e.minimalDays, 1, 7) || !Array.isArray(e.weekend) || e.weekend.some((e => !De(e, 1, 7)))) throw new c("Invalid week settings"); return { firstDay: e.firstDay, minimalDays: e.minimalDays, weekend: Array.from(e.weekend) } }

                function De(e, t, n) { return Ie(e) && e >= t && e <= n }

                function Fe(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2; let n; return n = e < 0 ? "-" + ("" + -e).padStart(t, "0") : ("" + e).padStart(t, "0"), n }

                function Ne(e) { return He(e) || null === e || "" === e ? void 0 : parseInt(e, 10) }

                function _e(e) { return He(e) || null === e || "" === e ? void 0 : parseFloat(e) }

                function Be(e) { if (!He(e) && null !== e && "" !== e) { const t = 1e3 * parseFloat("0." + e); return Math.floor(t) } }

                function We(e, t) { const n = 10 ** t; return (arguments.length > 2 && void 0 !== arguments[2] && arguments[2] ? Math.trunc : Math.round)(e * n) / n }

                function Ue(e) { return e % 4 === 0 && (e % 100 !== 0 || e % 400 === 0) }

                function qe(e) { return Ue(e) ? 366 : 365 }

                function Ge(e, t) { const n = function(e, t) { return e - t * Math.floor(e / t) }(t - 1, 12) + 1; return 2 === n ? Ue(e + (t - n) / 12) ? 29 : 28 : [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][n - 1] }

                function Ke(e) { let t = Date.UTC(e.year, e.month - 1, e.day, e.hour, e.minute, e.second, e.millisecond); return e.year < 100 && e.year >= 0 && (t = new Date(t), t.setUTCFullYear(e.year, e.month - 1, e.day)), +t }

                function Ze(e, t, n) { return -xe(be(e, 1, t), n) + t - 1 }

                function Ye(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 4,
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1; const r = Ze(e, t, n),
                        a = Ze(e + 1, t, n); return (qe(e) - r + a) / 7 }

                function Xe(e) { return e > 99 ? e : e > pe.twoDigitCutoffYear ? 1900 + e : 2e3 + e }

                function $e(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : null; const a = new Date(e),
                        o = { hourCycle: "h23", year: "numeric", month: "2-digit", day: "2-digit", hour: "2-digit", minute: "2-digit" };
                    r && (o.timeZone = r); const i = { timeZoneName: t, ...o },
                        l = new Intl.DateTimeFormat(n, i).formatToParts(a).find((e => "timezonename" === e.type.toLowerCase())); return l ? l.value : null }

                function Qe(e, t) { let n = parseInt(e, 10);
                    Number.isNaN(n) && (n = 0); const r = parseInt(t, 10) || 0; return 60 * n + (n < 0 || Object.is(n, -0) ? -r : r) }

                function Je(e) { const t = Number(e); if ("boolean" === typeof e || "" === e || Number.isNaN(t)) throw new c("Invalid unit value ".concat(e)); return t }

                function et(e, t) { const n = {}; for (const r in e)
                        if (Re(e, r)) { const a = e[r]; if (void 0 === a || null === a) continue;
                            n[t(r)] = Je(a) } return n }

                function tt(e, t) { const n = Math.trunc(Math.abs(e / 60)),
                        r = Math.trunc(Math.abs(e % 60)),
                        a = e >= 0 ? "+" : "-"; switch (t) {
                        case "short":
                            return "".concat(a).concat(Fe(n, 2), ":").concat(Fe(r, 2));
                        case "narrow":
                            return "".concat(a).concat(n).concat(r > 0 ? ":".concat(r) : "");
                        case "techie":
                            return "".concat(a).concat(Fe(n, 2)).concat(Fe(r, 2));
                        default:
                            throw new RangeError("Value format ".concat(t, " is out of range for property format")) } }

                function nt(e) { return function(e, t) { return t.reduce(((t, n) => (t[n] = e[n], t)), {}) }(e, ["hour", "minute", "second", "millisecond"]) } const rt = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
                    at = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                    ot = ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"];

                function it(e) { switch (e) {
                        case "narrow":
                            return [...ot];
                        case "short":
                            return [...at];
                        case "long":
                            return [...rt];
                        case "numeric":
                            return ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];
                        case "2-digit":
                            return ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"];
                        default:
                            return null } } const lt = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                    st = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                    ct = ["M", "T", "W", "T", "F", "S", "S"];

                function dt(e) { switch (e) {
                        case "narrow":
                            return [...ct];
                        case "short":
                            return [...st];
                        case "long":
                            return [...lt];
                        case "numeric":
                            return ["1", "2", "3", "4", "5", "6", "7"];
                        default:
                            return null } } const ut = ["AM", "PM"],
                    ht = ["Before Christ", "Anno Domini"],
                    mt = ["BC", "AD"],
                    pt = ["B", "A"];

                function ft(e) { switch (e) {
                        case "narrow":
                            return [...pt];
                        case "short":
                            return [...mt];
                        case "long":
                            return [...ht];
                        default:
                            return null } }

                function vt(e, t) { let n = ""; for (const r of e) r.literal ? n += r.val : n += t(r.val); return n } const gt = { D: p, DD: f, DDD: g, DDDD: y, t: b, tt: w, ttt: z, tttt: x, T: A, TT: k, TTT: S, TTTT: M, f: E, ff: T, fff: I, ffff: V, F: C, FF: H, FFF: j, FFFF: O };
                class yt { static create(e) { return new yt(e, arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}) } static parseFormat(e) { let t = null,
                            n = "",
                            r = !1; const a = []; for (let o = 0; o < e.length; o++) { const i = e.charAt(o); "'" === i ? (n.length > 0 && a.push({ literal: r || /^\s+$/.test(n), val: n }), t = null, n = "", r = !r) : r || i === t ? n += i : (n.length > 0 && a.push({ literal: /^\s+$/.test(n), val: n }), n = i, t = i) } return n.length > 0 && a.push({ literal: r || /^\s+$/.test(n), val: n }), a } static macroTokenToFormatOpts(e) { return gt[e] } constructor(e, t) { this.opts = t, this.loc = e, this.systemLoc = null } formatWithSystemDefault(e, t) { null === this.systemLoc && (this.systemLoc = this.loc.redefaultToSystem()); return this.systemLoc.dtFormatter(e, { ...this.opts, ...t }).format() } dtFormatter(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; return this.loc.dtFormatter(e, { ...this.opts, ...t }) } formatDateTime(e, t) { return this.dtFormatter(e, t).format() } formatDateTimeParts(e, t) { return this.dtFormatter(e, t).formatToParts() } formatInterval(e, t) { return this.dtFormatter(e.start, t).dtf.formatRange(e.start.toJSDate(), e.end.toJSDate()) } resolvedOptions(e, t) { return this.dtFormatter(e, t).resolvedOptions() } num(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0; if (this.opts.forceSimple) return Fe(e, t); const n = { ...this.opts }; return t > 0 && (n.padTo = t), this.loc.numberFormatter(n).format(e) } formatDateTimeFromString(e, t) { const n = "en" === this.loc.listingMode(),
                            r = this.loc.outputCalendar && "gregory" !== this.loc.outputCalendar,
                            a = (t, n) => this.loc.extract(e, t, n),
                            o = t => e.isOffsetFixed && 0 === e.offset && t.allowZ ? "Z" : e.isValid ? e.zone.formatOffset(e.ts, t.format) : "",
                            i = () => n ? function(e) { return ut[e.hour < 12 ? 0 : 1] }(e) : a({ hour: "numeric", hourCycle: "h12" }, "dayperiod"),
                            l = (t, r) => n ? function(e, t) { return it(t)[e.month - 1] }(e, t) : a(r ? { month: t } : { month: t, day: "numeric" }, "month"),
                            s = (t, r) => n ? function(e, t) { return dt(t)[e.weekday - 1] }(e, t) : a(r ? { weekday: t } : { weekday: t, month: "long", day: "numeric" }, "weekday"),
                            c = t => { const n = yt.macroTokenToFormatOpts(t); return n ? this.formatWithSystemDefault(e, n) : t },
                            d = t => n ? function(e, t) { return ft(t)[e.year < 0 ? 0 : 1] }(e, t) : a({ era: t }, "era"); return vt(yt.parseFormat(t), (t => { switch (t) {
                                case "S":
                                    return this.num(e.millisecond);
                                case "u":
                                case "SSS":
                                    return this.num(e.millisecond, 3);
                                case "s":
                                    return this.num(e.second);
                                case "ss":
                                    return this.num(e.second, 2);
                                case "uu":
                                    return this.num(Math.floor(e.millisecond / 10), 2);
                                case "uuu":
                                    return this.num(Math.floor(e.millisecond / 100));
                                case "m":
                                    return this.num(e.minute);
                                case "mm":
                                    return this.num(e.minute, 2);
                                case "h":
                                    return this.num(e.hour % 12 === 0 ? 12 : e.hour % 12);
                                case "hh":
                                    return this.num(e.hour % 12 === 0 ? 12 : e.hour % 12, 2);
                                case "H":
                                    return this.num(e.hour);
                                case "HH":
                                    return this.num(e.hour, 2);
                                case "Z":
                                    return o({ format: "narrow", allowZ: this.opts.allowZ });
                                case "ZZ":
                                    return o({ format: "short", allowZ: this.opts.allowZ });
                                case "ZZZ":
                                    return o({ format: "techie", allowZ: this.opts.allowZ });
                                case "ZZZZ":
                                    return e.zone.offsetName(e.ts, { format: "short", locale: this.loc.locale });
                                case "ZZZZZ":
                                    return e.zone.offsetName(e.ts, { format: "long", locale: this.loc.locale });
                                case "z":
                                    return e.zoneName;
                                case "a":
                                    return i();
                                case "d":
                                    return r ? a({ day: "numeric" }, "day") : this.num(e.day);
                                case "dd":
                                    return r ? a({ day: "2-digit" }, "day") : this.num(e.day, 2);
                                case "c":
                                case "E":
                                    return this.num(e.weekday);
                                case "ccc":
                                    return s("short", !0);
                                case "cccc":
                                    return s("long", !0);
                                case "ccccc":
                                    return s("narrow", !0);
                                case "EEE":
                                    return s("short", !1);
                                case "EEEE":
                                    return s("long", !1);
                                case "EEEEE":
                                    return s("narrow", !1);
                                case "L":
                                    return r ? a({ month: "numeric", day: "numeric" }, "month") : this.num(e.month);
                                case "LL":
                                    return r ? a({ month: "2-digit", day: "numeric" }, "month") : this.num(e.month, 2);
                                case "LLL":
                                    return l("short", !0);
                                case "LLLL":
                                    return l("long", !0);
                                case "LLLLL":
                                    return l("narrow", !0);
                                case "M":
                                    return r ? a({ month: "numeric" }, "month") : this.num(e.month);
                                case "MM":
                                    return r ? a({ month: "2-digit" }, "month") : this.num(e.month, 2);
                                case "MMM":
                                    return l("short", !1);
                                case "MMMM":
                                    return l("long", !1);
                                case "MMMMM":
                                    return l("narrow", !1);
                                case "y":
                                    return r ? a({ year: "numeric" }, "year") : this.num(e.year);
                                case "yy":
                                    return r ? a({ year: "2-digit" }, "year") : this.num(e.year.toString().slice(-2), 2);
                                case "yyyy":
                                    return r ? a({ year: "numeric" }, "year") : this.num(e.year, 4);
                                case "yyyyyy":
                                    return r ? a({ year: "numeric" }, "year") : this.num(e.year, 6);
                                case "G":
                                    return d("short");
                                case "GG":
                                    return d("long");
                                case "GGGGG":
                                    return d("narrow");
                                case "kk":
                                    return this.num(e.weekYear.toString().slice(-2), 2);
                                case "kkkk":
                                    return this.num(e.weekYear, 4);
                                case "W":
                                    return this.num(e.weekNumber);
                                case "WW":
                                    return this.num(e.weekNumber, 2);
                                case "n":
                                    return this.num(e.localWeekNumber);
                                case "nn":
                                    return this.num(e.localWeekNumber, 2);
                                case "ii":
                                    return this.num(e.localWeekYear.toString().slice(-2), 2);
                                case "iiii":
                                    return this.num(e.localWeekYear, 4);
                                case "o":
                                    return this.num(e.ordinal);
                                case "ooo":
                                    return this.num(e.ordinal, 3);
                                case "q":
                                    return this.num(e.quarter);
                                case "qq":
                                    return this.num(e.quarter, 2);
                                case "X":
                                    return this.num(Math.floor(e.ts / 1e3));
                                case "x":
                                    return this.num(e.ts);
                                default:
                                    return c(t) } })) } formatDurationFromString(e, t) { const n = e => { switch (e[0]) {
                                    case "S":
                                        return "millisecond";
                                    case "s":
                                        return "second";
                                    case "m":
                                        return "minute";
                                    case "h":
                                        return "hour";
                                    case "d":
                                        return "day";
                                    case "w":
                                        return "week";
                                    case "M":
                                        return "month";
                                    case "y":
                                        return "year";
                                    default:
                                        return null } },
                            r = yt.parseFormat(t),
                            a = r.reduce(((e, t) => { let { literal: n, val: r } = t; return n ? e : e.concat(r) }), []); return vt(r, (e => t => { const r = n(t); return r ? this.num(e.get(r), t.length) : t })(e.shiftTo(...a.map(n).filter((e => e))))) } } const bt = /[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;

                function wt() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; const r = t.reduce(((e, t) => e + t.source), ""); return RegExp("^".concat(r, "$")) }

                function zt() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return e => t.reduce(((t, n) => { let [r, a, o] = t; const [i, l, s] = n(e, o); return [{ ...r, ...i }, l || a, s] }), [{}, null, 1]).slice(0, 2) }

                function xt(e) { if (null == e) return [null, null]; for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r]; for (const [a, o] of n) { const t = a.exec(e); if (t) return o(t) } return [null, null] }

                function At() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return (e, n) => { const r = {}; let a; for (a = 0; a < t.length; a++) r[t[a]] = Ne(e[n + a]); return [r, null, n + a] } } const kt = /(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,
                    St = "(?:".concat(kt.source, "?(?:\\[(").concat(bt.source, ")\\])?)?"),
                    Mt = /(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,
                    Et = RegExp("".concat(Mt.source).concat(St)),
                    Ct = RegExp("(?:T".concat(Et.source, ")?")),
                    Tt = At("weekYear", "weekNumber", "weekDay"),
                    Ht = At("year", "ordinal"),
                    Lt = RegExp("".concat(Mt.source, " ?(?:").concat(kt.source, "|(").concat(bt.source, "))?")),
                    It = RegExp("(?: ".concat(Lt.source, ")?"));

                function jt(e, t, n) { const r = e[t]; return He(r) ? n : Ne(r) }

                function Vt(e, t) { return [{ hours: jt(e, t, 0), minutes: jt(e, t + 1, 0), seconds: jt(e, t + 2, 0), milliseconds: Be(e[t + 3]) }, null, t + 4] }

                function Ot(e, t) { const n = !e[t] && !e[t + 1],
                        r = Qe(e[t + 1], e[t + 2]); return [{}, n ? null : re.instance(r), t + 3] }

                function Rt(e, t) { return [{}, e[t] ? B.create(e[t]) : null, t + 1] } const Pt = RegExp("^T?".concat(Mt.source, "$")),
                    Dt = /^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;

                function Ft(e) { const [t, n, r, a, o, i, l, s, c] = e, d = "-" === t[0], u = s && "-" === s[0], h = function(e) { return void 0 !== e && (arguments.length > 1 && void 0 !== arguments[1] && arguments[1] || e && d) ? -e : e }; return [{ years: h(_e(n)), months: h(_e(r)), weeks: h(_e(a)), days: h(_e(o)), hours: h(_e(i)), minutes: h(_e(l)), seconds: h(_e(s), "-0" === s), milliseconds: h(Be(c), u) }] } const Nt = { GMT: 0, EDT: -240, EST: -300, CDT: -300, CST: -360, MDT: -360, MST: -420, PDT: -420, PST: -480 };

                function _t(e, t, n, r, a, o, i) { const l = { year: 2 === t.length ? Xe(Ne(t)) : Ne(t), month: at.indexOf(n) + 1, day: Ne(r), hour: Ne(a), minute: Ne(o) }; return i && (l.second = Ne(i)), e && (l.weekday = e.length > 3 ? lt.indexOf(e) + 1 : st.indexOf(e) + 1), l } const Bt = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;

                function Wt(e) { const [, t, n, r, a, o, i, l, s, c, d, u] = e, h = _t(t, a, r, n, o, i, l); let m; return m = s ? Nt[s] : c ? 0 : Qe(d, u), [h, new re(m)] } const Ut = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,
                    qt = /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,
                    Gt = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;

                function Kt(e) { const [, t, n, r, a, o, i, l] = e; return [_t(t, a, r, n, o, i, l), re.utcInstance] }

                function Zt(e) { const [, t, n, r, a, o, i, l] = e; return [_t(t, l, n, r, a, o, i), re.utcInstance] } const Yt = wt(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/, Ct),
                    Xt = wt(/(\d{4})-?W(\d\d)(?:-?(\d))?/, Ct),
                    $t = wt(/(\d{4})-?(\d{3})/, Ct),
                    Qt = wt(Et),
                    Jt = zt((function(e, t) { return [{ year: jt(e, t), month: jt(e, t + 1, 1), day: jt(e, t + 2, 1) }, null, t + 3] }), Vt, Ot, Rt),
                    en = zt(Tt, Vt, Ot, Rt),
                    tn = zt(Ht, Vt, Ot, Rt),
                    nn = zt(Vt, Ot, Rt); const rn = zt(Vt); const an = wt(/(\d{4})-(\d\d)-(\d\d)/, It),
                    on = wt(Lt),
                    ln = zt(Vt, Ot, Rt); const sn = "Invalid Duration",
                    cn = { weeks: { days: 7, hours: 168, minutes: 10080, seconds: 604800, milliseconds: 6048e5 }, days: { hours: 24, minutes: 1440, seconds: 86400, milliseconds: 864e5 }, hours: { minutes: 60, seconds: 3600, milliseconds: 36e5 }, minutes: { seconds: 60, milliseconds: 6e4 }, seconds: { milliseconds: 1e3 } },
                    dn = { years: { quarters: 4, months: 12, weeks: 52, days: 365, hours: 8760, minutes: 525600, seconds: 31536e3, milliseconds: 31536e6 }, quarters: { months: 3, weeks: 13, days: 91, hours: 2184, minutes: 131040, seconds: 7862400, milliseconds: 78624e5 }, months: { weeks: 4, days: 30, hours: 720, minutes: 43200, seconds: 2592e3, milliseconds: 2592e6 }, ...cn },
                    un = 365.2425,
                    hn = 30.436875,
                    mn = { years: { quarters: 4, months: 12, weeks: 52.1775, days: un, hours: 8765.82, minutes: 525949.2, seconds: 525949.2 * 60, milliseconds: 525949.2 * 60 * 1e3 }, quarters: { months: 3, weeks: 13.044375, days: 91.310625, hours: 2191.455, minutes: 131487.3, seconds: 525949.2 * 60 / 4, milliseconds: 7889237999.999999 }, months: { weeks: 4.3481250000000005, days: hn, hours: 730.485, minutes: 43829.1, seconds: 2629746, milliseconds: 2629746e3 }, ...cn },
