                                        t = x.data.detailsPane ? x.data.detailsPane : { layout: { name: "layout-1", banner: { fields: [], color: "#00ACC0", showLegends: e, showRoleColorAsBannerColor: !0 } } }, await T(L.ZO.update({ orgId: H, themeId: x.id, data: { ...x, data: { ...x.data, detailsPane: { ...t, layout: { ...t.layout, banner: { ...t.layout.banner, showLegends: e } } } } } })) })(!(null !== C && void 0 !== C && C.showLegends)), name: "showLegend" }) }), (0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsxs)(i.A, { my: 2, display: "flex", flexDirection: "column", gap: 2, children: [(0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === N || void 0 === N || null === (s = N.inTheme) || void 0 === s || null === (c = s.mainSection) || void 0 === c ? void 0 : c.length) > 0 && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { mb: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Primary Fields" }) }), null === N || void 0 === N || null === (d = N.inTheme) || void 0 === d ? void 0 : d.mainSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { $active: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B, style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { p: 1, display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", gap: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => te(e.target.value, "main-section", t), singleLine: !0, options: j.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: ee(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B ? "minus" : "gear", size: "sm", variant: "light" }) }, "icon-primaryfield-".concat(U, "-").concat(B, "-").concat(t)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B && (0, D.jsx)(hn, { inTheme: !0, content: z, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, open: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B && !(null !== z && void 0 !== z && z.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: ee(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => ne({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: t }) }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) }))] }) }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === N || void 0 === N || null === (u = N.inTheme) || void 0 === u || null === (h = u.iconSection) || void 0 === h ? void 0 : h.length) > 0 && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { mb: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.bodyMD, color: P.Qs.Neutrals[900], children: "Icon Fields" }) }), null === N || void 0 === N || null === (v = N.inTheme) || void 0 === v ? void 0 : v.iconSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { $active: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B, style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { p: 1, display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", gap: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => te(e.target.value, "icon-section", t), singleLine: !0, options: O.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: ee(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B ? "minus" : "gear", size: "sm", variant: "light" }) }, "icon-iconfield-".concat(U, "-").concat(B, "-").concat(t)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B && (0, D.jsx)(hn, { inTheme: !0, content: z, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, open: t === U && (null === e || void 0 === e ? void 0 : e.fieldKey) === B && !(null !== z && void 0 !== z && z.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: ee(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => { var a, o; return ne({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: (null === N || void 0 === N || null === (a = N.inTheme) || void 0 === a || null === (o = a.mainSection) || void 0 === o ? void 0 : o.length) + t }) } }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) }))] }) })] })] }) },
                    fn = e => { var t, n, r, a, l, s, c; let { innerRef: d, isOverrideMode: h = !1, overrideRoleType: f = "single", showVancantPhotoSettings: v = !0, showNoPhotoSettings: g = !0, showAbovePositionSetting: y = !0 } = e; const b = (0, m.d4)(p.P0); let z = h ? { ...null === b || void 0 === b || null === (t = b.roleTypeOverrides) || void 0 === t || null === (n = t[f]) || void 0 === n ? void 0 : n.photos } : { ...null === b || void 0 === b || null === (r = b.data) || void 0 === r || null === (a = r.detailsPane) || void 0 === a ? void 0 : a.photos }; if (!z || 0 === Object.keys(z).length) { var x, A, k; const e = null === b || void 0 === b || null === (x = b.data) || void 0 === x || null === (A = x.detailsPane) || void 0 === A || null === (k = A.layout) || void 0 === k ? void 0 : k.name,
                                t = dn.A.find((t => t.id === e));
                            z = t.photos } const S = { ...z },
                            { orgId: M } = (0, u.useParams)(),
                            E = (0, m.wA)(),
                            C = (0, o.useRef)(),
                            [T, H] = (0, o.useState)(null);
                        y && [{ label: "Left", value: "left" }, { label: "Right", value: "right" }, { label: "Center", value: "center" }].push({ label: "Above", value: "above" }); const I = [{ label: "Circle", value: "circle", variant: "circular" }, { label: "Rounded", value: "oval", variant: "rounded" }, { label: "Square", value: "square", variant: "square" }],
                            j = e => h ? { ...b, roleTypeOverrides: { ...b.roleTypeOverrides, [f]: { ...b.roleTypeOverrides[f], photos: e } } } : { ...b, data: { ...b.data, detailsPane: { ...b.data.detailsPane, photos: e } } },
                            V = async e => { let t; switch (e.target.name) {
                                    case "standard":
                                    case "noPhoto":
                                    case "vacant":
                                        t = { ...z, [e.target.name]: { ...z[e.target.name], visible: e.target.checked } }; break;
                                    default:
                                        t = { ...z, [e.target.name]: e.target.checked } } await E(L.ZO.update({ orgId: M, themeId: b.id, data: j(t) })) }, O = e => { e && e.stopPropagation(), H("noPhoto"), C.current.click() }, R = e => async t => { t && t.stopPropagation(), await E(L.ZO.update({ orgId: M, themeId: b.id, data: j({ ...z, noPhoto: { ...z.noPhoto, imageAvatarId: e } }) })) }, F = [{ id: "initials", image: (0, lt.Or)("initials") }, { id: "avatar1", image: (0, lt.Or)("avatar1") }, { id: "avatar2", image: (0, lt.Or)("avatar2") }, { id: "avatar3", image: (0, lt.Or)("avatar3") }], N = [{ id: "vacant1", image: (0, lt.Or)("vacant1") }, { id: "vacant2", image: (0, lt.Or)("vacant2") }, { id: "vacant3", image: (0, lt.Or)("vacant3") }, { id: "vacant4", image: (0, lt.Or)("vacant4") }], _ = e => { e && e.stopPropagation(), H("vacant"), C.current.click() }, W = e => async t => { t && t.stopPropagation(), N.find((t => t.id === e)).isNew && (H("vacant"), C.current.click()), await E(L.ZO.update({ orgId: M, themeId: b.id, data: j({ ...z, vacant: { ...z.vacant, imageAvatarId: e } }) })) }, U = async (e, t) => { let n; switch (e) {
                                    case "position":
                                    case "shape":
                                        n = { ...z, [e]: t }; break;
                                    default:
                                        n = { ...z, standard: { ...z.standard, size: t }, noPhoto: { ...z.noPhoto, size: t }, vacant: { ...z.vacant, size: t } } } await E(L.ZO.update({ orgId: M, themeId: b.id, data: j(n) })) }, q = I.find((e => e.value === S.shape)), G = (null === q || void 0 === q ? void 0 : q.variant) || "circular"; return (0, D.jsx)("div", { ref: d, children: (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(B, { label: "Photo Shape", data: I, value: null === S || void 0 === S ? void 0 : S.shape, name: "shape", onChange: U }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Size", data: [{ label: "Small", value: 100 }, { label: "Medium", value: 120 }, { label: "Large", value: 140 }], value: null === S || void 0 === S || null === (l = S.standard) || void 0 === l ? void 0 : l.size, name: "size", onChange: U }) }), g && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show when no photo is available", checked: null === S || void 0 === S || null === (s = S.noPhoto) || void 0 === s ? void 0 : s.visible, onChange: V, name: "noPhoto" }) }), (null === S || void 0 === S || null === (c = S.noPhoto) || void 0 === c ? void 0 : c.visible) && (0, D.jsx)(i.A, { display: "flex", gap: 1, flexWrap: "wrap", children: F.map((e => (0, D.jsx)(it.A, { selected: e.id === S.noPhoto.imageAvatarId, id: e.id, image: "".concat(e.image), handleClick: R, handleEdit: O, customImageUrl: S.noPhoto.customImageUrl, isNew: e.isNew, variant: G }))) })] }), v && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(Ce, { label: "Show photo for vacant roles", checked: S.vacant.visible, onChange: V, name: "vacant" }) }), S.vacant.visible && (0, D.jsx)(i.A, { display: "flex", gap: 1, flexWrap: "wrap", children: N.map((e => (0, D.jsx)(it.A, { selected: e.id === S.vacant.imageAvatarId, id: e.id, image: e.image, customImageUrl: S.vacant.customImageUrl, handleClick: W, handleEdit: _, isNew: e.isNew }))) })] })] }) }) }; var vn = n(84373); const gn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsx)(vn.A, { variant: "text", width: 36, height: 4, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 5, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1 }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1 }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1 })] }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 25, animation: !1 })] }),
                    yn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsx)(vn.A, { variant: "text", width: 64, height: 2, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 2, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 2, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 4, ml: 1, alignItems: "center", children: [(0, D.jsx)(vn.A, { variant: "text", width: 108, height: 20, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 })] })] }),
                    bn = () => (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", children: (0, D.jsxs)(i.A, { display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", children: [(0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", justifyContent: "center", gap: 1, children: [(0, D.jsx)(vn.A, { variant: "text", width: 40, height: 2, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 40, height: 2, animation: !1, style: { backgroundColor: "#D2E2FC" } })] })] }), (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", ml: 1, alignItems: "center", children: [(0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 })] })] }) }),
                    wn = () => (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", children: (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", children: [(0, D.jsxs)(i.A, { display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", justifyContent: "center", gap: 1, children: [(0, D.jsx)(vn.A, { variant: "text", width: 40, height: 2, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 40, height: 2, animation: !1, style: { backgroundColor: "#D2E2FC" } })] })] }), (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", ml: 1, alignItems: "center", children: [(0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 })] })] }) }); var zn; const xn = S.Ay.div(zn || (zn = (0, a.A)(["\n  ", "\n"])), (e => { let { selected: t } = e; return "\n    position:relative;\n    align-items:center;\n    justify-content:space-between;\n    cursor:pointer;\n    border:2px solid transparent;\n    padding: 16px;\n    display: flex;\n    flex-direction: column;\n    grid-gap: 24px;\n    border: solid 1px #ccc;\n    border-radius: 10px;\n  ".concat(t ? "\n    border:1px solid #5C2DBF;\n    border-radius: 10px;\n    margin-bottom: 5px;\n  " : "", "\n  :hover{\n    border-radius: 10px;\n    border: solid 1px #5C2DBF;\n  }\n  .Button-root{\n    position:absolute;\n    right:0px;\n    top:0px;\n  }\n  ") })),
                    An = { "layout-1": (0, D.jsx)(gn, {}), "layout-2": (0, D.jsx)(yn, {}), "layout-3": (0, D.jsx)(bn, {}), "layout-4": (0, D.jsx)(wn, {}) },
                    kn = e => { var t, n, r; let { theme: a } = e; const o = null === a || void 0 === a || null === (t = a.data) || void 0 === t || null === (n = t.detailsPane) || void 0 === n || null === (r = n.layout) || void 0 === r ? void 0 : r.name,
                            s = (0, m.wA)(),
                            { orgId: c } = (0, u.useParams)(); return (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[900], children: "Choose a layout to arrange your data" }) }), (0, D.jsx)(i.A, { p: 1, display: "flex", flexDirection: "column", gridGap: 16, alignItems: "stretch", children: dn.A.map((e => (0, D.jsx)(i.A, { py: 1, display: "flex", flexDirection: "column", gap: 1, children: (0, D.jsxs)(xn, { onClick: () => (async e => { const t = dn.A.find((t => t.id === e));
                                            await s(L.ZO.update({ orgId: c, themeId: a.id, data: { ...a, data: { ...a.data, detailsPane: { ...a.data.detailsPane, layout: { name: e, banner: { color: t.bannerColor, fields: [] } }, fields: [], photos: null === t || void 0 === t ? void 0 : t.photos } } } })) })(e.id), selected: o === e.id, children: [(0, D.jsxs)(i.A, { textAlign: "center", children: [(0, D.jsx)(l.A, { variant: "subtitle2", color: "#5C2DBF", children: e.name }), (0, D.jsx)(l.A, { variant: "caption", children: (e.descriptions || []).join(" + ") })] }), (0, D.jsx)(i.A, { width: "100%", alignItems: "center", children: An[e.id] || An["layout-1"] })] }, e.id) }))) })] }) },
                    Sn = e => { let { settings: t, selectedSetting: n = "table", handleSettingClick: r } = e; return (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", gap: 1, py: 1, mx: 1, children: t.filter((e => !e.hidden)).map((e => (0, D.jsxs)(Gt, { button: !0, id: "theme_tooltip_".concat(e.slug), selected: e.slug === n, onClick: r(e.slug), "data-tour-anchor": null === e || void 0 === e ? void 0 : e.anchorDataAttribute, children: [(0, D.jsx)(I.me, { name: e.icon || "user", variant: "light", color: P.Qs.Neutrals[900] }), (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[600], children: e.name })] }, e.id))) }) },
                    Mn = e => { let { settings: t, selectedSetting: n = "table", handleSettingClick: r } = e; return (0, D.jsx)(i.A, { display: "flex", flexDirection: "column", gap: 1, py: 1, mx: 1, children: t.filter((e => !e.hidden)).map((e => (0, D.jsxs)(Gt, { id: "theme_tooltip_".concat(e.slug), selected: e.slug === n, onClick: r(e.slug), "data-tour-anchor": null === e || void 0 === e ? void 0 : e.anchorDataAttribute, children: [(0, D.jsx)(I.me, { name: e.icon || "user", variant: "light", color: P.Qs.Neutrals[900] }), (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[600], children: e.name })] }, e.id))) }) }; var En = n(63580); const Cn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "square", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "square", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "square", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] }),
                    Tn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }); var Hn; const Ln = (0, S.Ay)(W.A)(Hn || (Hn = (0, a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid #0000001c;\n  width: 72px;\n  height: 48px;\n"]))),
                    In = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(Ln, { children: (0, D.jsx)(I.Ay, { icon: "UserRectangle", size: "x2", color: "#0000001c", style: { position: "absolute" } }) }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 })] }); var jn; const Vn = (0, S.Ay)(W.A)(jn || (jn = (0, a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid #0000001c;\n  width: 56px;\n  height: 48px;\n"]))),
                    On = () => (0, D.jsx)(W.A, { display: "flex", flexDirection: "column", children: [0, 1].map((e => (0, D.jsxs)(W.A, { display: "flex", flexDirection: e % 2 ? "row-reverse" : "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(Vn, { children: (0, D.jsx)(I.Ay, { icon: "UserRectangle", size: "x2", color: "#0000001c", style: { position: "absolute" } }) }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 4, alignItems: e % 2 ? "flex-end" : "flex-start", children: [(0, D.jsx)(vn.A, { variant: "text", width: 36, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 48, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 8, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 8, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 8, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }, "alternate_".concat(e)))) }),
                    Rn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(I.Ay, { icon: "UserCircle", size: "x3", color: "#0000001c" }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 4, ml: 1, alignItems: "center", children: [(0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 108, height: 8, animation: !1 })] })] }),
                    Pn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(vn.A, { variant: "circle", width: 36, height: 36, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }); var Dn; const Fn = (0, S.Ay)(W.A)(Dn || (Dn = (0, a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid #0000001c;\n  width: 36px;\n  height: 48px;\n"]))),
                    Nn = () => (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", alignItems: "center", children: [(0, D.jsx)(Fn, { children: (0, D.jsx)(I.Ay, { icon: "UserRectangle", size: "x2", color: "#0000001c", style: { position: "absolute" } }) }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "column", gridGap: 8, children: [(0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1, style: { backgroundColor: "#D2E2FC" } }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsx)(vn.A, { variant: "text", width: 64, height: 8, animation: !1 }), (0, D.jsxs)(W.A, { display: "flex", flexDirection: "row", gridGap: 8, justifyContent: "center", children: [(0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } }), (0, D.jsx)(vn.A, { variant: "rect", width: 16, height: 4, animation: !1, style: { backgroundColor: "#E3DAF6" } })] })] })] }); var _n; const Bn = S.Ay.div(_n || (_n = (0, a.A)(["\n  ", "\n"])), (e => { let { selected: t } = e; return "\n    position:relative;\n    align-items:center;\n    justify-content:space-between;\n    cursor:pointer;\n    border:2px solid transparent;\n    padding: 16px;\n    display: flex;\n    flex-direction: row;\n    grid-gap: 24px;\n    border: solid 1px #ccc;\n    border-radius: 6px;\n\n    flex-direction: column;\n  ".concat(t ? "\n    border:1px solid #5C2DBF;\n    border-radius: 6px;\n    margin-bottom: 5px;\n  " : "", "\n  :hover{\n    border:1px solid #5C2DBF;\n    border-radius: 6px;\n  }\n  .Button-root{\n    position:absolute;\n    right:0px;\n    top:0px;\n  }\n  ") })),
                    Wn = { "layout-0": (0, D.jsx)(Cn, {}), "layout-1": (0, D.jsx)(Tn, {}), "layout-2": (0, D.jsx)(In, {}), "layout-3": (0, D.jsx)(On, {}), "layout-4": (0, D.jsx)(Rn, {}), "layout-5": (0, D.jsx)(Pn, {}), "layout-8": (0, D.jsx)(Nn, {}) },
                    Un = e => { var t, n; let { theme: r } = e; const a = (null === r || void 0 === r || null === (t = r.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.layout) || "layout-0",
                            o = (0, m.wA)(),
                            { orgId: s } = (0, u.useParams)(); return (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(i.A, { children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, color: P.Qs.Neutrals[900], children: "Choose a layout to arrange your data" }) }), (0, D.jsx)(i.A, { p: 1, display: "flex", flexDirection: "column", gridGap: 16, alignItems: "stretch", children: En.A.map((e => (0, D.jsx)(i.A, { py: 1, display: "flex", flexDirection: "column", gap: 1, children: (0, D.jsxs)(Bn, { onClick: () => (async e => { const t = En.A.find((t => t.id === e));
                                            await o(L.ZO.update({ orgId: s, themeId: r.id, data: { ...r, data: { ...r.data, photoboard: { ...r.data.photoboard, layout: e, fields: null === t || void 0 === t ? void 0 : t.fields, photos: null === t || void 0 === t ? void 0 : t.photos } } } })) })(e.id), selected: a === e.id, children: [(0, D.jsxs)(i.A, { textAlign: "center", children: [(0, D.jsx)(l.A, { variant: "subtitle2", color: "#5C2DBF", children: e.name }), (0, D.jsx)(l.A, { variant: "caption", children: e.descriptions })] }), (0, D.jsx)(i.A, { children: Wn[e.id] || Wn["layout-0"] })] }, e.id) }))) })] }) }; var qn, Gn, Kn, Zn, Yn, Xn, $n, Qn;
                (0, S.Ay)(W.A)(qn || (qn = (0, a.A)(["\n  background: #f8f8f8;\n  padding: 20px;\n  border-radius: 10px;\n"]))), (0, S.Ay)(we.A)(Gn || (Gn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  box-shadow: 1px 0px 5px 0px rgba(0,0,0,0.1);\n  background: ".concat(t.palette.grey[50], ";\n  overflow: auto;\n  ") })), (0, S.Ay)(we.A)(Kn || (Kn = (0, a.A)(["\n  ", "\n"])), (e => { let { width: t } = e; return "\n  background: #fdfdfd;\n  box-shadow: 1px 0px 5px 0px rgba(0,0,0,0.1);\n  height: 100%;\n  width:".concat(t, "px;\n  max-width:").concat(t, "px;\n  ") })), (0, S.Ay)(ze.A)(Zn || (Zn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    background: none;\n    border-radius: 16px;\n    border: 2px dashed ".concat(t.palette.grey[600], ";\n    color: ").concat(t.palette.grey[600], ";\n    box-shadow: none;\n    height: 90%;\n    padding: 55px 0;\n\n    span {\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n\n      svg{\n        margin-bottom: 10px;\n      }\n    }\n\n    &:hover {\n      box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);\n      background: none;\n      border: 2px dashed ").concat(t.palette.grey[700], ";\n      color: ").concat(t.palette.grey[700], ";\n    }\n") })), (0, S.Ay)(xe.A)(Yn || (Yn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, dashboardMode: n = !1 } = e; return "\nposition: absolute;\nleft: ".concat(.7 * Ae.Ay.dialogs.themeSettingsWidth, "px;\nz-index: 2;\nwidth: max-content;\nbox-shadow: none;\npadding: 30px 40px;\nwidth: calc(100vw - ").concat(.7 * Ae.Ay.dialogs.themeSettingsWidth + (n ? Ae.Ay.dashboard.navWidth : Ae.Ay.chart.navWidth), "px);\nbackground: #fdfdfd;\nborder-bottom: 1px solid ").concat(t.palette.grey[300], ";\nborder-left: 1px solid ").concat(t.palette.grey[300], ";\nborder-radius: 0;\n") })), (0, S.Ay)(V.A)(Xn || (Xn = (0, a.A)(["\n  height: 50px;\n  width: 260px;\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n"]))), (0, S.Ay)(xe.A)($n || ($n = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t, width: n } = e; return "\n  position: absolute;\n  height: calc(100% - 82px);\n  box-shadow: none;\n  width: ".concat(n, "px;\n  z-index:1;\n  .Mui-selected{\n    color:").concat(t.palette.primary.main, "\n  }\n  ") })); const Jn = (0, S.Ay)(xe.A)(Qn || (Qn = (0, a.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  position: relative;\n  height: 100%;\n  border: solid 1px ".concat(t.palette.grey[200], ";\n  border-radius: 0px;\n  padding: 0px ").concat(t.spacing(1), "px;\n  width: 100%;\n  .MuiSelect-root{\n    background:white;\n  }\n  ") })),
                    er = e => { let { inTheme: t, content: n, font: r, open: a, section: o, handleFontChange: i } = e; const l = { cursor: t ? "move" : "default", width: "100%" },
                            s = r && Object.keys(r).reduce(((e, t) => { switch (t) {
                                    case "weight":
                                        r[t] > 400 && e.push("bold"); break;
                                    case "italic":
                                        r[t] && e.push("italic"); break;
                                    case "underline":
                                        r[t] && e.push("underline"); break;
                                    case "strikethrough":
                                        r[t] && e.push("strikethrough") } return e }), []); return (0, D.jsx)("div", { style: l, children: t && (0, D.jsx)(rn.A, { in: a && !(null !== n && void 0 !== n && n.commonStyle), timeout: "auto", unmountOnExit: !0, children: (0, D.jsxs)(Jn, { elevation: 0, children: [(0, D.jsx)(W.A, { my: 1, children: (0, D.jsx)(q, { label: "Font Color", color: (null === r || void 0 === r ? void 0 : r.color) || "#151515", handleChangeBackground: e => i("color", e) }) }), "icon-section" !== o && "hover-section" !== o && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(W.A, { my: 2, children: (0, D.jsx)(K, { label: "Font size", value: null === r || void 0 === r ? void 0 : r.size, color: "primary", onChange: e => i("size", e.target.value), singleLine: !0, options: [12, 13, 14, 15, 16, 17, 18, 20, 22].map((e => ({ value: e, label: e }))) }) }), (0, D.jsx)(W.A, { my: 2, children: (0, D.jsx)(W.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Style", fullwidth: !0, value: s, onChange: async (e, t) => { i("format", t) }, multi: !0, singleLine: !0, data: [{ label: (0, D.jsx)(I.Ay, { icon: "Bold" }), value: "bold" }, { label: (0, D.jsx)(I.Ay, { icon: "Italic" }), value: "italic" }, { label: (0, D.jsx)(I.Ay, { icon: "Underline" }), value: "underline" }, { label: (0, D.jsx)(I.Ay, { icon: "StrikeThrough" }), value: "strikethrough" }] }) }) }), (0, D.jsx)(W.A, { my: 2, children: (0, D.jsx)(W.A, { display: "flex", justifyContent: "space-between", gap: 1, alignItems: "center", children: (0, D.jsx)(B, { label: "Case", value: (null === r || void 0 === r ? void 0 : r.case) || "none", onChange: (e, t) => { i("case", t) }, singleLine: !0, data: He.ML }) }) })] })] }) }) }) },
                    tr = e => { var t, n, r, a, s, c, d, u, h, v, g, y, b, w; let { innerRef: z, theme: x, overrideRoleType: A = "single" } = e; const { base: { content: k } } = x, S = (0, m.d4)(f.V1), E = (0, m.d4)(f.ZI), C = (0, m.d4)(p.P0), T = (null === x || void 0 === x || null === (t = x.data) || void 0 === t || null === (n = t.photoboard) || void 0 === n ? void 0 : n.layout) || "layout-0", j = En.A.find((e => e.id === T)), V = (0, o.useMemo)((() => E(A, null === j || void 0 === j ? void 0 : j.fields)), [A, E]), [O, F] = (0, o.useState)(V), [N, _] = (0, o.useState)([]), [B, W] = (0, o.useState)([]), [U, q] = (0, o.useState)(), [G, Z] = (0, o.useState)(), [Y, X] = (0, o.useState)(!1), $ = En.A.find((e => e.id === T)), Q = null === $ || void 0 === $ ? void 0 : $.mainSectionFieldsCount, J = null === $ || void 0 === $ ? void 0 : $.subSectionFieldsCount, ee = null === $ || void 0 === $ ? void 0 : $.iconSectionFieldsCount, te = null === $ || void 0 === $ ? void 0 : $.hoverableSectionFieldsCount, ne = (0, m.wA)();
                        (0, o.useEffect)((() => { F(V); const e = { fieldInfo: { id: "none", name: "none", label: "None", type: "string" }, fieldKey: "none", fieldStyle: {} }; let t = [{ ...e }, ...V.inTheme, ...V.notInTheme]; const n = [...new Map(t.map((e => [e.fieldKey, e]))).values()];
                            _(n); let r = n.filter((e => e.fieldInfo.name === mn.x2.EMAIL || e.fieldInfo.name === mn.x2.LINKEDIN || e.fieldInfo.name === mn.x2.PHONE));
                            W([{ ...e }, ...r]) }), [V]); const re = e => ({ ...C, data: { ...C.data, photoboard: { ...C.data.photoboard, fields: e } } }),
                            ae = (0, o.useCallback)(((e, t) => { const n = O.inTheme[e],
                                    r = H()(O.inTheme, { $splice: [
                                            [e, 1],
                                            [t, 0, n]
                                        ] });
                                F({ ...O, inTheme: [...r] }), (async e => { const t = e.map((e => ({ field: { ...null === e || void 0 === e ? void 0 : e.fieldInfo, section: null === e || void 0 === e ? void 0 : e.fieldSection }, fontDetails: e.fieldStyle })));
                                    await ne(L.ZO.update({ orgId: S, themeId: null === C || void 0 === C ? void 0 : C.id, data: re(t) })) })(r) }), [O.inTheme]),
                            oe = (e, t) => () => { q(e === U ? null : e), Z(t === G ? null : t) },
                            ie = async (e, t, n) => { var r, a, o, i;
                                q(null), Z(null); let l = {};
                                l = O.inTheme.filter((e => !e.hidden)).map((e => ({ field: { ...null === e || void 0 === e ? void 0 : e.fieldInfo, section: null === e || void 0 === e ? void 0 : e.fieldSection }, fontDetails: e.fieldStyle }))); const s = N.find((t => t.fieldKey === e && !t.hidden)); switch (t) {
                                    case "main-section":
                                        l[n] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: null === (r = $.fields[n]) || void 0 === r ? void 0 : r.fontDetails }; break;
                                    case "sub-section":
                                        l[n + Q] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: null === (a = $.fields[n + Q]) || void 0 === a ? void 0 : a.fontDetails }; break;
                                    case "icon-section":
                                        l[n + Q + J] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: null === (o = $.fields[n + Q + J]) || void 0 === o ? void 0 : o.fontDetails }; break;
                                    case "hover-section":
                                        l[n + Q + J + ee] = { field: { ...null === s || void 0 === s ? void 0 : s.fieldInfo, section: t }, fontDetails: null === (i = $.fields[n + Q + J + ee]) || void 0 === i ? void 0 : i.fontDetails } } await ne(L.ZO.update({ orgId: S, themeId: null === C || void 0 === C ? void 0 : C.id, data: re(l) })) }, le = async e => { const t = O.inTheme.find(((t, n) => t.fieldKey === e.fieldKey && n === e.index)); let n = []; switch (e.prop) {
                                    case "color":
                                    case "align":
                                    case "size":
                                    case "case":
                                    case "dateFormat":
                                        n = O.inTheme.map(((n, r) => { var a, o; return n.fieldInfo.name === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.model) && r === e.index ? { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: { ...n.fieldStyle, [e.prop]: e.value } } : { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: n.fieldStyle } })); break;
                                    case "format":
                                        n = O.inTheme.map(((n, r) => { var a, o; return n.fieldInfo.name === (null === t || void 0 === t || null === (a = t.fieldInfo) || void 0 === a ? void 0 : a.name) && n.fieldInfo.model === (null === t || void 0 === t || null === (o = t.fieldInfo) || void 0 === o ? void 0 : o.model) && r === e.index ? { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: { ...n.fieldStyle, weight: e.value.includes("bold") ? 700 : 400, italic: !!e.value.includes("italic"), underline: !!e.value.includes("underline"), strikethrough: !!e.value.includes("strikethrough") } } : { field: { ...null === n || void 0 === n ? void 0 : n.fieldInfo, section: null === n || void 0 === n ? void 0 : n.fieldSection }, fontDetails: n.fieldStyle } })) } await ne(L.ZO.update({ orgId: S, themeId: null === C || void 0 === C ? void 0 : C.id, data: re(n) })) }; return (0, D.jsxs)("div", { ref: z, children: [Y && (0, D.jsx)(l.A, { variant: R.Eq.caption, color: P.Qs.Error[500], children: "Maximum fields selected for the section" }), Q > 0 && (0, D.jsxs)(i.A, { pb: 1, children: [(0, D.jsx)(i.A, { py: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: "Main Fields" }) }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === O || void 0 === O || null === (r = O.inTheme) || void 0 === r || null === (a = r.mainSection) || void 0 === a ? void 0 : a.length) > 0 && (0, D.jsx)(D.Fragment, { children: null === O || void 0 === O || null === (s = O.inTheme) || void 0 === s ? void 0 : s.mainSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { $active: (null === e || void 0 === e ? void 0 : e.fieldKey) === U, style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", alignItems: "center", gap: 1, width: "100%", p: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => ie(e.target.value, "main-section", t), singleLine: !0, options: N.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: (null === e || void 0 === e ? void 0 : e.fieldKey) === U ? "minus" : "gear", size: "xs", variant: "light" }) }, "icon-".concat(G, "-").concat(U)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && (0, D.jsx)(er, { inTheme: !0, content: k, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, moveField: ae, open: t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && !(null !== k && void 0 !== k && k.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => le({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: t }) }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) })) }) })] }), J > 0 && (0, D.jsxs)(i.A, { py: 1, children: [(0, D.jsx)(i.A, { py: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: "Sub Fields" }) }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === O || void 0 === O || null === (c = O.inTheme) || void 0 === c || null === (d = c.subSection) || void 0 === d ? void 0 : d.length) > 0 && (null === O || void 0 === O || null === (u = O.inTheme) || void 0 === u ? void 0 : u.subSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { $active: (null === e || void 0 === e ? void 0 : e.fieldKey) === U, style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", p: 1, gap: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => ie(e.target.value, "sub-section", t), singleLine: !0, options: N.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: (null === e || void 0 === e ? void 0 : e.fieldKey) === U ? "minus" : "gear", size: "xs", variant: "light" }) }, "icon-".concat(G, "-").concat(U)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && (0, D.jsx)(er, { inTheme: !0, content: k, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, moveField: ae, open: t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && !(null !== k && void 0 !== k && k.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => { var a, o; return le({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: (null === O || void 0 === O || null === (a = O.inTheme) || void 0 === a || null === (o = a.mainSection) || void 0 === o ? void 0 : o.length) + t }) } }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) }))) })] }), ee > 0 && (0, D.jsxs)(i.A, { py: 1, children: [(0, D.jsx)(i.A, { py: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: "Icon Fields" }) }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === O || void 0 === O || null === (h = O.inTheme) || void 0 === h || null === (v = h.iconSection) || void 0 === v ? void 0 : v.length) > 0 && (null === O || void 0 === O || null === (g = O.inTheme) || void 0 === g ? void 0 : g.iconSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", p: 1, gap: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => ie(e.target.value, "icon-section", t), singleLine: !0, options: B.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: (null === e || void 0 === e ? void 0 : e.fieldKey) === U ? "minus" : "gear", size: "xs", variant: "light" }) }, "icon-".concat(G, "-").concat(U)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && (0, D.jsx)(er, { inTheme: !0, content: k, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, moveField: ae, open: t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && !(null !== k && void 0 !== k && k.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => { var a, o, i, l; return le({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: (null === O || void 0 === O || null === (a = O.inTheme) || void 0 === a || null === (o = a.mainSection) || void 0 === o ? void 0 : o.length) + (null === O || void 0 === O || null === (i = O.inTheme) || void 0 === i || null === (l = i.subSection) || void 0 === l ? void 0 : l.length) + t }) } }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) }))) })] }), te > 0 && (0, D.jsxs)(i.A, { py: 1, children: [(0, D.jsx)(i.A, { py: 2, display: "flex", justifyContent: "space-between", alignItems: "center", children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[900], children: "Hoverable Fields" }) }), (0, D.jsx)(M, { sortable: "true", component: "div", disablePadding: !0, children: (null === O || void 0 === O || null === (y = O.inTheme) || void 0 === y || null === (b = y.hoverSection) || void 0 === b ? void 0 : b.length) > 0 && (null === O || void 0 === O || null === (w = O.inTheme) || void 0 === w ? void 0 : w.hoverSection.map(((e, t) => { var n; return (0, D.jsxs)(Le, { style: { display: "flex", flexDirection: "column" }, children: [(0, D.jsxs)(i.A, { p: 1, display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%", gap: 1, children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(K, { label: "Field ".concat(t + 1), value: null === e || void 0 === e ? void 0 : e.fieldKey, color: "primary", onChange: e => ie(e.target.value, "hover-section", t), singleLine: !0, options: B.map((e => { let { fieldKey: t, fieldInfo: n } = e; return { value: t, label: n.label } })) }) }), (0, D.jsx)(i.A, { children: (0, D.jsx)(Xe.A, { onClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), size: "small", children: (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: (null === e || void 0 === e ? void 0 : e.fieldKey) === U ? "minus" : "gear", size: "xs", variant: "light" }) }, "icon-".concat(G, "-").concat(U)) }) })] }, null === e || void 0 === e ? void 0 : e.fieldKey), t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && (0, D.jsx)(er, { inTheme: !0, content: k, checked: !0, index: t, fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, fieldLabel: e.fieldInfo.label, fieldType: null === (n = e.fieldInfo) || void 0 === n ? void 0 : n.type, font: e.fieldStyle, moveField: ae, open: t === G && (null === e || void 0 === e ? void 0 : e.fieldKey) === U && !(null !== k && void 0 !== k && k.commonStyle), section: null === e || void 0 === e ? void 0 : e.fieldSection, handleFieldClick: oe(null === e || void 0 === e ? void 0 : e.fieldKey, t), handleFontChange: (n, r) => { var a, o, i, l, s, c; return le({ fieldKey: null === e || void 0 === e ? void 0 : e.fieldKey, prop: n, value: r, index: (null === O || void 0 === O || null === (a = O.inTheme) || void 0 === a || null === (o = a.mainSection) || void 0 === o ? void 0 : o.length) + (null === O || void 0 === O || null === (i = O.inTheme) || void 0 === i || null === (l = i.subSection) || void 0 === l ? void 0 : l.length) + (null === O || void 0 === O || null === (s = O.inTheme) || void 0 === s || null === (c = s.iconSection) || void 0 === c ? void 0 : c.length) + t }) } }, null === e || void 0 === e ? void 0 : e.fieldKey)] }) }))) })] })] }) },
                    nr = e => { var t, n, r, a, l, s, c, d, h; let { innerRef: f, isOverrideMode: v = !1, overrideRoleType: g = "single", showNoPhotoSettings: y = !0 } = e; const b = (0, m.d4)(p.P0),
                            z = v ? { ...null === b || void 0 === b || null === (t = b.roleTypeOverrides) || void 0 === t || null === (n = t[g]) || void 0 === n ? void 0 : n.photos } : { ...null === b || void 0 === b || null === (r = b.data) || void 0 === r || null === (a = r.photoboard) || void 0 === a ? void 0 : a.photos },
                            x = { ...z },
                            { orgId: A } = (0, u.useParams)(),
                            k = (0, m.wA)(),
                            S = (0, o.useRef)(),
                            [, M] = (0, o.useState)(null),
                            E = null === b || void 0 === b || null === (l = b.data) || void 0 === l || null === (s = l.photoboard) || void 0 === s ? void 0 : s.layout,
                            C = [{ label: "Circle", value: "circle", variant: "circular" }, { label: "Rounded", value: "oval", variant: "rounded" }, { label: "Square", value: "square", variant: "square" }],
                            T = e => v ? { ...b, roleTypeOverrides: { ...b.roleTypeOverrides, [g]: { ...b.roleTypeOverrides[g], data: { ...b.data, photoboard: { ...b.data.photoboard, photos: e } } } } } : { ...b, data: { ...b.data, photoboard: { ...b.data.photoboard, photos: e } } },
                            H = e => { e && e.stopPropagation(), M("noPhoto"), S.current.click() },
                            I = e => async t => { t && t.stopPropagation(), await k(L.ZO.update({ orgId: A, themeId: b.id, data: T({ ...z, noPhoto: { ...z.noPhoto, imageAvatarId: e } }) })) }, j = [{ id: "initials", image: (0, lt.Or)("initials") }, { id: "avatar1", image: (0, lt.Or)("avatar1") }, { id: "avatar2", image: (0, lt.Or)("avatar2") }, { id: "avatar3", image: (0, lt.Or)("avatar3") }], V = async (e, t) => { let n; switch (e) {
                                    case "position":
                                    case "shape":
                                        n = { ...z, [e]: t }; break;
                                    default:
                                        n = { ...z, standard: { ...z.standard, size: t }, noPhoto: { ...z.noPhoto, size: t } } } await k(L.ZO.update({ orgId: A, themeId: b.id, data: T(n) })) }, O = C.find((e => e.value === x.shape)), R = (null === O || void 0 === O ? void 0 : O.variant) || "circular"; return (0, D.jsx)("div", { ref: f, children: (0, D.jsxs)(D.Fragment, { children: ["layout-3" !== E && (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Shape", data: C, value: null === x || void 0 === x ? void 0 : x.shape, name: "shape", onChange: V }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(B, { label: "Photo Size", data: (() => { switch (E) {
                                                case "layout-1":
                                                    return [{ label: "Small", value: 80 }, { label: "Medium", value: 100 }, { label: "Large", value: 120 }];
                                                case "layout-2":
                                                case "layout-3":
                                                    return [{ label: "Small", value: 180 }, { label: "Medium", value: 210 }, { label: "Large", value: 240 }];
                                                case "layout-8":
                                                    return [{ label: "Small", value: 180 }, { label: "Medium", value: 210 }, { label: "Large", value: 250 }];
                                                default:
                                                    return [{ label: "Small", value: 80 }, { label: "Medium", value: 150 }, { label: "Large", value: 210 }] } })(), value: null === x || void 0 === x || null === (c = x.standard) || void 0 === c ? void 0 : c.size, name: "size", onChange: V }) }), y && (0, D.jsxs)(D.Fragment, { children: [(0, D.jsx)(w.A, { sx: { my: 3, borderColor: P.Qs.Neutrals[200] } }), (0, D.jsx)(i.A, { mb: 3, children: (0, D.jsx)(Ce, { label: "Show when no photo is available", checked: null === x || void 0 === x || null === (d = x.noPhoto) || void 0 === d ? void 0 : d.visible, onChange: async e => { let t; switch (e.target.name) {
                                                    case "standard":
                                                    case "noPhoto":
                                                        t = { ...z, [e.target.name]: { ...z[e.target.name], visible: e.target.checked } }; break;
                                                    default:
                                                        t = { ...z, [e.target.name]: e.target.checked } } await k(L.ZO.update({ orgId: A, themeId: b.id, data: T(t) })) }, name: "noPhoto" }) }), (null === x || void 0 === x || null === (h = x.noPhoto) || void 0 === h ? void 0 : h.visible) && (0, D.jsx)(i.A, { display: "flex", gap: 1, flexWrap: "wrap", children: j.map((e => { var t, n; return (0, D.jsx)(it.A, { selected: e.id === (null === x || void 0 === x || null === (t = x.noPhoto) || void 0 === t ? void 0 : t.imageAvatarId), id: e.id, image: "".concat(e.image), handleClick: I, handleEdit: H, customImageUrl: null === x || void 0 === x || null === (n = x.noPhoto) || void 0 === n ? void 0 : n.customImageUrl, isNew: e.isNew, variant: R }) })) })] })] }) }) },
                    rr = e => { var t, n, r, a; let { innerRef: o, theme: s } = e; const { data: { photoboard: c }, base: { table: d } } = s, h = (0, m.wA)(), { orgId: p } = (0, u.useParams)(), f = [{ name: "", model: "member", label: "None", attr: "", order: "asc" }, { name: "", model: "role", label: "Department Role", attr: "department", isDefault: !0, order: "asc" }, { name: "", model: "role", label: "Location Role", attr: "location", isDefault: !0, order: "asc" }, ...(0, m.d4)(Nt.kA).map((e => ({ ...e, attr: e.id, order: "asc" })))], v = (0, m.d4)(Nt.Xi), { handleGroupByChange: g } = (0, _t.A)(); return (0, D.jsxs)("div", { ref: o, children: [(0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(K, { defaultValue: (null === c || void 0 === c || null === (t = c.groupBy) || void 0 === t ? void 0 : t.label) || "None", name: "groupBy", label: "Default Group By", singleLine: !0, labelFlex: 2, children: v.map(((e, t) => (0, D.jsx)(x.A, { value: e.label, onClick: t => (async (e, t) => { "None" === t.label && (t = null), g(t), await h(L.ZO.update({ orgId: p, themeId: s.id, data: { ...s, data: { ...s.data, photoboard: { ...s.data.photoboard, groupBy: t } } } })) })(0, e), children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: e.label }) }, t))) }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(K, { defaultValue: (null === c || void 0 === c || null === (n = c.orderBy) || void 0 === n ? void 0 : n.label) || "None", name: "orderBy", label: "Default Sort By", singleLine: !0, labelFlex: 2, children: f.map(((e, t) => (0, D.jsx)(x.A, { value: e.label, onClick: t => (async (e, t) => { var n; "None" === t.label && (t = null), await h(L.ZO.update({ orgId: p, themeId: s.id, data: { ...s, data: { ...s.data, photoboard: { ...null === s || void 0 === s || null === (n = s.data) || void 0 === n ? void 0 : n.photoboard, orderBy: t } } } })) })(0, e), children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: e.label }) }, t))) }) }), (null === c || void 0 === c || null === (r = c.orderBy) || void 0 === r ? void 0 : r.label) && (0, D.jsx)(i.A, { my: 3, children: (0, D.jsxs)(K, { label: "Default Sort Order", defaultValue: null === c || void 0 === c || null === (a = c.orderBy) || void 0 === a ? void 0 : a.order, name: "sortOrder", singleLine: !0, labelFlex: 2, onChange: e => {
                                        (async e => { let t = { ...c.orderBy, order: e.target.value };
                                            await h(L.ZO.update({ orgId: p, themeId: s.id, data: { ...s, data: { ...s.data, photoboard: { ...s.data.photoboard, orderBy: t } } } })) })(e) }, children: [(0, D.jsx)(x.A, { value: "asc", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: "Ascending" }) }, "asc"), (0, D.jsx)(x.A, { value: "desc", children: (0, D.jsx)(l.A, { variant: R.Eq.bodySM, children: "Descending" }) }, "desc")] }) }), (0, D.jsx)(i.A, { my: 3, children: (0, D.jsx)(Ce, { label: "Unique By People", checked: null === c || void 0 === c ? void 0 : c.showPeople, onChange: () => (async e => { g(null), await h(L.ZO.update({ orgId: p, themeId: s.id, data: { ...s, data: { ...s.data, photoboard: { ...s.data.photoboard, showPeople: e } } } })) })(!(null !== c && void 0 !== c && c.showPeople)), name: "showPeople" }) })] }) }; var ar = n(43940),
                    or = n(86674); const ir = e => { let { handleSuccess: t, handleClose: n, pending: r, defaultValue: a } = e; return (0, D.jsx)(or.A, { maxWidth: "sm", handleClose: n, handleSuccess: t, title: "Change Theme Name", btnText: { submit: "Save", cancel: "Cancel" }, pending: r, render: e => { var t; let { register: n, formState: { errors: r } } = e; return (0, D.jsxs)(i.A, { p: 3, display: "flex", flexDirection: "column", gap: 2, children: [(0, D.jsx)(l.A, { variant: R.Eq.bodyMD, children: "Update your theme name to make it more easily selectable" }), (0, D.jsx)(G.A, { required: !0, fullWidth: !0, name: "name", label: "Theme name", defaultValue: a, inputRef: n({ required: "Theme name is required" }), error: r.name, helperText: null === r || void 0 === r || null === (t = r.name) || void 0 === t ? void 0 : t.message })] }) } }) }; var lr = n(16930); const sr = e => { let { innerRef: t, theme: n } = e; const r = (0, u.useParams)(),
                        { orgId: a } = r,
                        o = (0, m.wA)(),
                        { cards: s } = n.base,
                        c = (0, lr.$)(s.cardFrame.colorPosition),
                        d = [{ value: pt.U$.Top, label: "Top" }, { value: pt.U$.Left, label: "Left" }, { value: pt.U$.Cover, label: "Full Card" }]; return (0, D.jsxs)("div", { ref: t, children: [(0, D.jsx)(i.A, { mb: 2, children: (0, D.jsx)(l.A, { variant: "bodyMD", color: "#818181", children: "Set role colors in each role or apply smart color rules on the legend." }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(K, { label: "Role Color Position", name: "colorPosition", onChange: async e => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, cards: { ...n.base.cards, cardFrame: { ...n.base.cards.cardFrame, [e.target.name]: e.target.value } } } } })) }, value: c, options: d, defaultValue: "" }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Card Background Color", color: s.cardFrame.backgroundColor, handleChangeBackground: async e => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, cards: { ...n.base.cards, cardFrame: { ...n.base.cards.cardFrame, backgroundColor: e } } } } })) } }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Card Frame Color", color: s.cardFrame.color, handleChangeBackground: async e => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, base: { ...n.base, cards: { ...n.base.cards, cardFrame: { ...n.base.cards.cardFrame, color: e } } } } })) } }) }), (0, D.jsx)(i.A, { my: 2, children: (0, D.jsx)(q, { label: "Chart Background Color", color: n.chartOptions.backgroundColor || "#FFFFFF", handleChangeBackground: async e => { await o(L.ZO.update({ orgId: a, themeId: n.id, data: { ...n, chartOptions: { ...n.chartOptions, backgroundColor: e } } })) } }) })] }) }; var cr, dr;
                n(649), n(14279); const ur = S.Ay.div(cr || (cr = (0, a.A)(["\n  align-items: center;\n  border-bottom: 1px solid rgb(167 167 167 / 12%);\n"]))),
                    hr = (0, S.Ay)(i.A)(dr || (dr = (0, a.A)(["\n  cursor: pointer;\n"]))),
                    mr = e => { let { sample: t } = e; const n = (0, o.useRef)(null),
                            r = (0, m.d4)(p.P0),
                            a = (0, m.d4)(f.nh),
                            w = r;
                        (0, ar.M)({ featureTours: ["themesTour"] }); const z = (0, m.wA)(),
                            { chartId: x, resource: A } = (0, u.useParams)(),
                            k = (0, u.useHistory)(),
                            S = (0, h.A)(),
                            M = (0, m.d4)(f.V1),
                            E = A || "chart",
                            [C, T] = (0, o.useState)(E),
                            [H, L] = (0, o.useState)(!1),
                            { userHasMinAccess: j } = (0, Lt.A)(),
                            V = j(Ae.td.ADMIN),
                            { handleSettingClick: O, handleCloseClick: F, selectedSetting: N, setSelectedSetting: _, chartSettings: B, settingTypes: W, chartSettingAreas: U, directorySettings: q, detailsPaneSettings: G, photoboardSettings: K, handleThemeDelete: Z, onTitleUpdate: Y } = (0, v.A)({ sample: t }),
                            X = (0, o.useRef)();
                        (0, o.useEffect)((() => { if ("setup" !== N) { const e = "detailsPane" === C ? (z((0, y.gN)({ activePersonId: null, selectedRoleId: null, mode: "view", isSample: !0 })), "layout") : "chart" === A ? "cards" : "directory" === A ? "table" : "photoboard" === A ? "layout" : "";
                                e && _(e) } }), []), (0, o.useEffect)((() => (z((0, g.Qs)()), z((0, b.zi)()), z((0, y.Ch)()), () => { z((0, y.Ch)()) })), []);
                        (0, o.useEffect)((() => { n.current = w.id }), [w.id]); const $ = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const t = (null === e || void 0 === e ? void 0 : e.forceClose) || !1; "setup" === N || t ? F() : O("setup")(), z((0, y.Ch)()) },
                            Q = (e, t) => e === U.roleSpecific && a[t],
                            J = Object.keys(W),
                            [ee, te] = (0, Mt.A)((async e => { const { error: t } = await Y(null === e || void 0 === e ? void 0 : e.name);
                                t ? console.error({ error: t }) : L(!1) })); return (0, D.jsxs)(D.Fragment, { children: [H && (0, D.jsx)(ir, { handleClose: () => { L(!1) }, handleSuccess: te, defaultValue: r.name, pending: ee }), (0, D.jsxs)(i.A, { "data-tour-anchor": "themes-sidebar", display: "flex", flexDirection: "column", width: Ae.Ay.dialogs.themeSettingsWidth, borderRight: "1px solid ".concat(P.Qs.Neutrals[300]), overflow: "auto", position: "relative", flexShrink: 0, zIndex: 1, bgcolor: P.Qs.Neutrals[0], height: "100%", children: [(0, D.jsxs)(i.A, { bgcolor: P.Qs.Neutrals[800], color: P.Qs.Neutrals[0], p: 1, display: "flex", justifyContent: "space-between", alignItems: "center", children: [(0, D.jsx)(i.A, { px: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.bodyLG, children: r.name }) }), (0, D.jsxs)(i.A, { children: [V && (0, D.jsx)(s.A, { size: "small", onClick: () => (async () => { L(!0) })(), color: "secondary", children: (0, D.jsx)(I.me, { name: "pencil", color: "#ffffff", variant: "solid" }) }), !t && (0, D.jsx)(s.A, { size: "medium", onClick: () => $({ forceClose: !0 }), color: "secondary", children: (0, D.jsx)(I.me, { name: "times", color: "#ffffff", variant: "solid" }) })] })] }), (0, D.jsx)(d.A, { container: !0, xs: !0, style: { position: "relative" }, children: (0, D.jsx)(i.A, { position: "absolute", left: 0, right: 0, top: 0, bottom: 0, children: (0, D.jsxs)(i.A, { display: "flex", position: "relative", height: "100%", children: ["setup" !== N && (0, D.jsx)(Se, { item: !0, children: (0, D.jsxs)(i.A, { display: "flex", flexDirection: "column", height: "100%", children: [J.map(((e, t) => (0, D.jsxs)(i.A, { width: 144, px: 2, py: 2, bgcolor: P.Qs.Neutrals[200], flexGrow: J.length - 1 === t ? 1 : "unset", borderBottom: "solid 1px #cccccc", children: [(0, D.jsxs)(hr, { display: "flex", justifyContent: "space-between", alignItems: "center", onClick: () => (e => { z((0, y.Ch)()), C !== e && ("chart" === e && (T("chart"), A ? k.push((0, an.iw)({ orgId: M, chartId: x, resource: "chart" })) : k.push({ search: "editThemeMode=true&resource=chart" }), _("cards")), "directory" === e && (on.A.trackEvent({ eventName: "DIRECTORY_VIEW_THEME_SETTINGS" }), T("directory"), A ? k.push((0, an.RI)({ orgId: M, chartId: x, resource: "directory" })) : k.push({ search: "editThemeMode=true&resource=directory" }), _("table")), "detailsPane" === e && (on.A.trackEvent({ eventName: "DETAILS_PANE_THEME_SETTINGS" }), T("detailsPane"), A || k.push({ search: "editThemeMode=true&resource=" + (S.resource || "chart") }), z((0, y.gN)({ activePersonId: null, selectedRoleId: null, mode: "view", isSample: !0 })), _("layout")), "photoboard" === e && (on.A.trackEvent({ eventName: "PHOTOBOARD_VIEW_THEME_SETTINGS" }), T("photoboard"), A ? k.push((0, an.K5)({ orgId: M, chartId: x, resource: "photoboard" })) : k.push({ search: "editThemeMode=true&resource=photoboard" }), _("layout"))) })(e), children: [(0, D.jsx)(l.A, { variant: R.Eq.subheadingSM, color: P.Qs.Neutrals[800], children: W[e] }), (0, D.jsx)(i.A, { children: (0, D.jsx)(I.me, { name: C === e ? "minus" : "plus", variant: "solid", size: "xs" }) }, "section_open_".concat(String(C !== e)))] }), (0, D.jsx)(rn.A, { in: C === e, unmountOnExit: !0, children: (0, D.jsxs)(ur, { children: ["chart" === e && (0, D.jsx)(i.A, { ml: -1, mr: -1, children: (0, D.jsx)(Kt, { settingAreas: U, settings: B, selectedSetting: N, handleSettingClick: O, isOverrideEnabled: Q }) }), "directory" === e && (0, D.jsx)(i.A, { ml: -1, mr: -1, children: (0, D.jsx)(Zt, { settings: q, selectedSetting: N, handleSettingClick: O, isOverrideEnabled: Q }) }), "photoboard" === e && (0, D.jsx)(i.A, { ml: -1, mr: -1, children: (0, D.jsx)(Mn, { settings: K, selectedSetting: N, handleSettingClick: O, isOverrideEnabled: Q }) }), "detailsPane" === e && (0, D.jsx)(i.A, { ml: -1, mr: -1, children: (0, D.jsx)(Sn, { settings: G, selectedSetting: N, handleSettingClick: O, isOverrideEnabled: Q }) })] }) })] }, t))), "organimi" !== (null === w || void 0 === w ? void 0 : w.type) && (0, D.jsx)(i.A, { p: 1, bgcolor: P.Qs.Neutrals[200], children: (0, D.jsxs)(c.A, { size: "small", color: "error", variant: "text", onClick: Z(w.id), children: [(0, D.jsx)(I.me, { name: "trash-alt", color: P.Qs.Error[500] }), " Delete Theme"] }) })] }) }), (0, D.jsxs)(i.A, { flex: 1, position: "relative", height: "100%", oveflow: "hidden", display: "flex", flexDirection: "column", children: ["setup" !== N && (0, D.jsxs)(i.A, { display: "flex", justifyContent: "flex-end", alignItems: "center", px: 2, py: 1, borderBottom: "solid 1px #ccc", children: [(0, D.jsx)(i.A, { flex: 1, children: (0, D.jsx)(l.A, { variant: R.Eq.subheadingMD, color: P.Qs.Neutrals[900], children: ["departmentRole", "sharedRole", "assistantRole", "locationRole"].includes(N) ? "Role Type Overrides" : (0, Ze.Sn)(N) }) }), (0, D.jsx)(s.A, { size: "medium", onClick: $, children: (0, D.jsx)(I.me, { name: "arrow-left", size: "sm", variant: "light" }) })] }), (0, D.jsx)(i.A, { px: 2, py: 2, height: "100%", overflow: "auto", width: "100%", children: (ne = N, { chart: { setup: (0, D.jsx)(Vt, { innerRef: X, dashboardMode: t }), colors: (0, D.jsx)(sr, { innerRef: X, theme: w }), cards: (0, D.jsx)(ht, { innerRef: X, theme: w }), fields: (0, D.jsx)(ot, { innerRef: X, theme: w }), lines: (0, D.jsx)(zt, { innerRef: X, theme: w }), photos: (0, D.jsx)(st, { innerRef: X, theme: w }), legend: (0, D.jsx)(wt, { innerRef: X, theme: w }), positions: (0, D.jsx)(Pt, { innerRef: X, theme: w }), counts: (0, D.jsx)(gt, { innerRef: X, theme: w }), departmentRole: (0, D.jsx)(bt, { innerRef: X, theme: w }), sharedRole: (0, D.jsx)(Ot, { innerRef: X, theme: w }), assistantRole: (0, D.jsx)(ct, { innerRef: X, theme: w }), locationRole: (0, D.jsx)(xt, { innerRef: X, theme: w }) }, directory: { setup: (0, D.jsx)(Vt, { innerRef: X, theme: w, dashboardMode: t }), table: (0, D.jsx)(Ft, { innerRef: X, theme: w }), columns: (0, D.jsx)(Wt, { innerRef: X, theme: w }), photos: (0, D.jsx)(Ut, { innerRef: X, theme: w }), data: (0, D.jsx)(Bt, { innerRef: X, theme: w }) }, photoboard: { setup: (0, D.jsx)(Vt, { innerRef: X, theme: w, dashboardMode: t }), layout: (0, D.jsx)(Un, { innerRef: X, theme: w }), fields: (0, D.jsx)(tr, { innerRef: X, theme: w }), photos: (0, D.jsx)(nr, { innerRef: X, theme: w }), data: (0, D.jsx)(rr, { innerRef: X, theme: w }) }, detailsPane: { setup: (0, D.jsx)(Vt, { innerRef: X, theme: w, dashboardMode: t }), layout: (0, D.jsx)(kn, { innerRef: X, theme: w }), banner: (0, D.jsx)(pn, { innerRef: X, theme: w }), data: (0, D.jsx)(un, { innerRef: X, theme: w }), photos: (0, D.jsx)(fn, { innerRef: X, theme: w }) } } [C][ne]) })] })] }) }) })] })] }); var ne } }, 24738: (e, t, n) => { "use strict";
                n.d(t, { Dy: () => u, IV: () => s, V1: () => h, ZI: () => c, nh: () => i, rj: () => l, wB: () => d }); var r = n(80192),
                    a = n(89656),
                    o = n(7743);
                n(15622); const i = (0, r.Mz)(a.P0, (e => Object.keys((null === e || void 0 === e ? void 0 : e.roleTypeOverrides) || {}).reduce(((t, n) => { var r, a; return t[n] = null === e || void 0 === e || null === (r = e.roleTypeOverrides) || void 0 === r || null === (a = r[n]) || void 0 === a ? void 0 : a.enabled, t }), {}))),
                    l = ((0, r.Mz)((e => e.organization.charts), (e => e.filter((e => e.theme)))), (0, r.Mz)((e => e.organization.charts), (e => t => e.filter((e => e.theme === t)))), (0, r.Mz)((e => e), (e => e.organization.theme)), (0, r.Mz)(o.Z7, a.P0, ((e, t) => n => { var r, a, o; let i = ("single" !== n ? null === t || void 0 === t || null === (r = t.roleTypeOverrides) || void 0 === r || null === (a = r[n]) || void 0 === a ? void 0 : a.fields : null === t || void 0 === t || null === (o = t.data) || void 0 === o ? void 0 : o.chart) || []; const l = e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]))).reduce(((e, t) => { const n = t.id,
                                    r = i.findIndex((e => { var t; return null !== e && (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.id) === n })); return r > -1 ? e.inTheme[r] = { fieldKey: n, fieldInfo: t, fieldStyle: i[r].fontDetails } : e.notInTheme.push({ fieldKey: n, fieldInfo: t }), e }), { inTheme: Array.from(Array(i.length)), notInTheme: [], otherOrgFields: [] }),
                            s = e.map((e => e.id)); return i.forEach(((e, t) => { var n, r;
                            s.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || l.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), l.inTheme = l.inTheme.filter((e => e)), l }))),
                    s = (0, r.Mz)(o.m1, a.P0, ((e, t) => n => { var r, a, o, i, l, s; let c = "single" !== n ? null === t || void 0 === t || null === (r = t.roleTypeOverrides) || void 0 === r || null === (a = r[n]) || void 0 === a ? void 0 : a.fields : null === t || void 0 === t || null === (o = t.data) || void 0 === o || null === (i = o.directory) || void 0 === i ? void 0 : i.columns; const d = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]))).reduce(((e, t) => { const n = t.id;
                                c = c.map((e => (e.field.id || e.field.name === t.name && e.field.model === t.model && (e.field.id = t.id), e))); const r = c.findIndex((e => { var t; return null !== e && (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.id) === n })); return r > -1 ? e.inTheme[r] = { fieldKey: n, fieldInfo: t, fieldStyle: c[r].fontDetails } : e.notInTheme.push({ fieldKey: n, fieldInfo: t }), e }), { inTheme: Array.from(Array(c.length)), notInTheme: [], otherOrgFields: [] }),
                            u = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (l = c) || void 0 === l || l.forEach(((e, t) => { var n, r;
                            u.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || d.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), d.inTheme = null === (s = d.inTheme) || void 0 === s ? void 0 : s.filter((e => e)), d })),
                    c = (0, r.Mz)(o.PE, a.P0, ((e, t) => (n, r) => { var a, o, i, l, s, c, d, u, h, m, p, f; let v = "single" !== n ? null === t || void 0 === t || null === (a = t.roleTypeOverrides) || void 0 === a || null === (o = a[n]) || void 0 === o ? void 0 : o.fields : null === t || void 0 === t || null === (i = t.data) || void 0 === i || null === (l = i.photoboard) || void 0 === l ? void 0 : l.fields;
                        v = (!v || 0 === (null === (s = v) || void 0 === s ? void 0 : s.length)) && r || v; const g = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]) && (null === e || void 0 === e ? void 0 : e.isDefault))).reduce(((e, t) => { var n; const r = t.id;
                                v = null === (n = v) || void 0 === n ? void 0 : n.map((e => { var n; return (null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.name) === (null === t || void 0 === t ? void 0 : t.name) && e.field.model === t.model && (e.field.id = null === t || void 0 === t ? void 0 : t.id), e })); const a = [...v.keys()].filter((e => { var t, n; return null !== v[e] && (null === (t = v[e]) || void 0 === t || null === (n = t.field) || void 0 === n ? void 0 : n.id) === r })); return a.length > 0 ? null === a || void 0 === a || a.forEach((n => { e.inTheme[n] = { fieldKey: r, fieldInfo: t, fieldSection: v[n].field.section, fieldStyle: v[n].fontDetails } })) : e.notInTheme.push({ fieldKey: r, fieldInfo: t }), e }), { inTheme: Array.from(Array(null === (c = v) || void 0 === c ? void 0 : c.length)), notInTheme: [], otherOrgFields: [] }),
                            y = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (d = v) || void 0 === d || d.forEach(((e, t) => { var n, r;
                            y.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || g.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), g.inTheme = null === (u = g.inTheme) || void 0 === u ? void 0 : u.filter((e => e)), g.inTheme.mainSection = null === (h = g.inTheme) || void 0 === h ? void 0 : h.filter((e => "main-section" === e.fieldSection)), g.inTheme.subSection = null === (m = g.inTheme) || void 0 === m ? void 0 : m.filter((e => "sub-section" === e.fieldSection)), g.inTheme.iconSection = null === (p = g.inTheme) || void 0 === p ? void 0 : p.filter((e => "icon-section" === e.fieldSection)), g.inTheme.hoverSection = null === (f = g.inTheme) || void 0 === f ? void 0 : f.filter((e => "hover-section" === e.fieldSection)), g })),
                    d = (0, r.Mz)(o.bH, a.P0, ((e, t) => (n, r) => { var a, o, i, l, s, c, d, u, h, m, p, f; let v = "single" !== n ? null === t || void 0 === t || null === (a = t.roleTypeOverrides) || void 0 === a || null === (o = a[n]) || void 0 === o ? void 0 : o.fields : null === t || void 0 === t || null === (i = t.data) || void 0 === i || null === (l = i.detailsPane) || void 0 === l || null === (s = l.layout) || void 0 === s || null === (c = s.banner) || void 0 === c ? void 0 : c.fields;
                        v = (!v || 0 === (null === (d = v) || void 0 === d ? void 0 : d.length)) && r || v; const g = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]))).reduce(((e, t) => { var n; const r = t.id; if (v = null === (n = v) || void 0 === n ? void 0 : n.map((e => { var n, r; return (null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.name) === (null === t || void 0 === t ? void 0 : t.name) && (null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.model) === (null === t || void 0 === t ? void 0 : t.model) && (e.field.id = null === t || void 0 === t ? void 0 : t.id), e })), v) { const n = [...v.keys()].filter((e => { var t, n; return null !== v[e] && (null === (t = v[e]) || void 0 === t || null === (n = t.field) || void 0 === n ? void 0 : n.id) === r }));
                                    n.length > 0 ? null === n || void 0 === n || n.forEach((n => { e.inTheme[n] = { fieldKey: r, fieldInfo: t, fieldSection: v[n].field.section, fieldStyle: v[n].fontDetails } })) : e.notInTheme.push({ fieldKey: r, fieldInfo: t }) } else e.notInTheme.push({ fieldKey: r, fieldInfo: t }); return e }), { inTheme: Array.from(Array(null === (u = v) || void 0 === u ? void 0 : u.length)), notInTheme: [], otherOrgFields: [] }),
                            y = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (h = v) || void 0 === h || h.forEach(((e, t) => { var n, r;
                            y.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || g.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), g.inTheme = null === (m = g.inTheme) || void 0 === m ? void 0 : m.filter((e => e)), g.inTheme.mainSection = null === (p = g.inTheme) || void 0 === p ? void 0 : p.filter((e => "main-section" === e.fieldSection)), g.inTheme.iconSection = null === (f = g.inTheme) || void 0 === f ? void 0 : f.filter((e => "icon-section" === e.fieldSection)), g })),
                    u = (0, r.Mz)(o.$B, a.P0, ((e, t) => n => { var r, a, o, i, l, s, c; let d = "single" !== n ? null === t || void 0 === t || null === (r = t.roleTypeOverrides) || void 0 === r || null === (a = r[n]) || void 0 === a ? void 0 : a.fields : null === t || void 0 === t || null === (o = t.data) || void 0 === o || null === (i = o.detailsPane) || void 0 === i ? void 0 : i.fields;
                        d && 0 !== (null === (l = d) || void 0 === l ? void 0 : l.length) || (d = null === e || void 0 === e ? void 0 : e.map((e => ({ field: e, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, case: "none", color: "#828282", hideEmptyField: !0 } })))); const u = null === e || void 0 === e ? void 0 : e.filter((e => !e.themeHidden && !(null !== e && void 0 !== e && e.themeHiddenOnRole[n]))).reduce(((e, n) => { var r, a; const o = n.id;
                                d = d.map((e => { var t, r; return (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.name) === (null === n || void 0 === n ? void 0 : n.name) && (null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.model) === (null === n || void 0 === n ? void 0 : n.model) && (e.field.id = n.id), e })); const i = d.findIndex((e => { var t; return null !== e && (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.id) === o })); return i > -1 ? e.inTheme[i] = { fieldKey: o, fieldInfo: n, fieldStyle: d[i].fontDetails } : 0 !== (null === t || void 0 === t || null === (r = t.data) || void 0 === r || null === (a = r.detailsPane) || void 0 === a ? void 0 : a.fields.length) && e.notInTheme.push({ fieldKey: o, fieldInfo: n }), e }), { inTheme: Array.from(Array(d.length)), notInTheme: [], otherOrgFields: [] }),
                            h = null === e || void 0 === e ? void 0 : e.map((e => e.id)); return null === (s = d) || void 0 === s || s.forEach(((e, t) => { var n, r;
                            h.includes(null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.id) || u.inTheme.splice(t, 0, { fieldKey: null === e || void 0 === e || null === (r = e.field) || void 0 === r ? void 0 : r.id, fieldInfo: null === e || void 0 === e ? void 0 : e.field, fieldStyle: null === e || void 0 === e ? void 0 : e.fontDetails, hidden: !0 }) })), u.inTheme = null === (c = u.inTheme) || void 0 === c ? void 0 : c.filter((e => e)), u })),
                    h = e => e.organization.id }, 8924: (e, t, n) => { "use strict";
                n.d(t, { EK: () => l, Jk: () => s, PS: () => d, SQ: () => c, hO: () => i }); var r = n(80192),
                    a = n(89656),
                    o = n(30752); const i = (0, r.Mz)(a.xu, (e => e.map((e => ({ name: e.name, id: e.id, type: e.type }))).reduce(((e, t) => ("organimi" === t.type ? e.defaultThemes.push(t) : e.customThemes.push(t), e)), { defaultThemes: [], customThemes: [] }))),
                    l = (0, r.Mz)(o.A, (e => e.id)),
                    s = e => { var t; return (null === (t = e.organization) || void 0 === t ? void 0 : t.theme) || "modern" },
                    c = (0, r.Mz)((e => e.organization.charts || []), (e => t => (e || []).filter((e => e.theme === t)))),
                    d = e => { var t; return !(null === (t = e.organization) || void 0 === t || !t.id) } }, 74772: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = [{ id: "layout-1", name: "Modern", descriptions: ["Tabbed data", "Large header"], organimiThemeName: "Modern", mainSectionFieldsCount: 3, iconSectionFieldsCount: 3, bannerColor: "#00ACC0", templateName: "layout-2", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 22, weight: 500, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Location Address", model: "role", name: "locationAddress", type: "location", section: "main-section" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#909090", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 15, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#005dc9", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#AEAEAE", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#AEAEAE", align: "center", hideEmptyField: !0, border: "none" } }], photos: { standard: { visible: !0, size: 120 }, noPhoto: { visible: !0, size: 120, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 120, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-2", name: "Classic", organimiThemeName: "Classic", descriptions: ["Expansion panels", "Business card"], mainSectionFieldsCount: 3, iconSectionFieldsCount: 3, bannerColor: "#00ACC0", templateName: "layout-1", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 22, weight: 500, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Location Address", model: "role", name: "locationAddress", type: "location", section: "main-section" }, fontDetails: { size: 12, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#909090", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 15, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#005dc9", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#AEAEAE", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#AEAEAE", align: "center", hideEmptyField: !0, border: "none" } }], photos: { standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-3", name: "Luxe Flip", organimiThemeName: "Luxe Flip", descriptions: ["Photo Banner", "Tabbed data"], mainSectionFieldsCount: 2, iconSectionFieldsCount: 0, bannerColor: "#00ACC0", templateName: "layout-3", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 22, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 15, weight: 700, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }], photos: { standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-4", name: "Minimalist Flip", organimiThemeName: "Minimalist Flip", descriptions: ["Flipped card", "Tabbed Data"], mainSectionFieldsCount: 2, iconSectionFieldsCount: 0, bannerColor: "#00ACC0", templateName: "layout-4", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 22, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 15, weight: 700, underline: !1, italic: !1, strikethrough: !1, color: "#000", align: "center", hideEmptyField: !0, border: "none" } }], photos: { standard: { visible: !0, size: 100 }, noPhoto: { visible: !0, size: 100, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 100, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }] }, 63580: (e, t, n) => { "use strict";
                n.d(t, { A: () => r }); const r = [{ id: "layout-0", name: "Modern Centered", organimiThemeName: "Modern Centered", mainSectionFieldsCount: 2, subSectionFieldsCount: 0, iconSectionFieldsCount: 3, hoverableSectionFieldsCount: 0, descriptions: "2 fields + social icons", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 16, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-1", name: "Modern Left", organimiThemeName: "Modern Left", mainSectionFieldsCount: 2, subSectionFieldsCount: 0, iconSectionFieldsCount: 3, hoverableSectionFieldsCount: 0, descriptions: "2 fields + social icons", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 16, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 80 }, noPhoto: { visible: !0, size: 80, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 80, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-4", name: "Modern Large", organimiThemeName: "Modern Large", mainSectionFieldsCount: 2, subSectionFieldsCount: 1, iconSectionFieldsCount: 3, hoverableSectionFieldsCount: 0, descriptions: "3 fields + large photo", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Description", model: "role", name: "description", type: "string", section: "sub-section" }, fontDetails: { size: 15, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#444444", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 150 }, noPhoto: { visible: !0, size: 150, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 150, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "circle", shadow: !1 } }, { id: "layout-3", name: "Classic Grid", organimiThemeName: "Classic Grid", mainSectionFieldsCount: 2, subSectionFieldsCount: 0, iconSectionFieldsCount: 3, hoverableSectionFieldsCount: 0, descriptions: "2 fields, square photo + social icons", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 210 }, noPhoto: { visible: !0, size: 210, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 210, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "square", shadow: !1 } }, { id: "layout-2", name: "Classic Centered", organimiThemeName: "Classic Centered", mainSectionFieldsCount: 2, subSectionFieldsCount: 0, iconSectionFieldsCount: 0, hoverableSectionFieldsCount: 3, descriptions: "2 fields, large photo + social icons on hover", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 16, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "hover-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#fff", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "hover-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#fff", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "hover-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#fff", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 210 }, noPhoto: { visible: !0, size: 210, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 210, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "square", shadow: !1 } }, { id: "layout-8", name: "Classic Large", organimiThemeName: "Classic Large", mainSectionFieldsCount: 2, subSectionFieldsCount: 1, iconSectionFieldsCount: 3, hoverableSectionFieldsCount: 0, descriptions: "3 fields, large photo + single column", fields: [{ field: { id: "name", name: "name", label: "Name", model: "member", type: "string", isDefault: !0, section: "main-section" }, fontDetails: { size: 18, weight: 600, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Title", model: "role", name: "name", type: "string", section: "main-section" }, fontDetails: { size: 16, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Job Description", model: "role", name: "description", type: "string", section: "sub-section" }, fontDetails: { size: 15, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#444444", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "LinkedIn", model: "member", name: "linkedIn", type: "url", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Phone", model: "member", name: "phone", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }, { field: { isDefault: !0, label: "Email", model: "member", name: "email", type: "string", section: "icon-section" }, fontDetails: { size: 14, weight: 400, underline: !1, italic: !1, strikethrough: !1, color: "#151515", align: "center", showLabel: !1, border: "none" } }], photos: { standard: { visible: !0, size: 210 }, noPhoto: { visible: !0, size: 210, imageAvatarId: "avatar3", customImageUrl: null }, vacant: { visible: !0, size: 210, imageAvatarId: "vacant1", customImageUrl: "" }, position: "above", shape: "oval", shadow: !1 } }] }, 94234: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => h, Ww: () => c, ZO: () => s, cD: () => l, y0: () => u }); var r = n(80907),
                    a = n(9787),
                    o = n(47730); const i = "themes",
                    l = { applyToChart: "applyToChart", applyDefaultToChart: "applyDefaultToChart", resetAllToDefault: "resetAllToDefault", makeDefault: "makeDefault", applyToChartAndMakeDefault: "applyToChartAndMakeDefault", applyToNothing: "inspectOnly" },
                    s = (0, a.a)({ slice: i, scope: "themes" }),
                    c = (0, o.B)({ slice: i, scope: "themes" }),
                    d = (0, r.Z0)({ name: i, initialState: { isDirty: !1, customThemes: [], activeTheme: null }, reducers: { resetDirty: e => { e.isDirty = !1 }, "setActive/fulfilled": e => { e.isDirty = !1 }, "getLicenseThemes/fulfilled": (e, t) => { e.customThemes = t.payload.themes, e.isDirty = !1 }, "getPrintThemes/fulfilled": (e, t) => { e.customThemes = t.payload.customThemes, e.isDirty = !1 }, "getPublicChartTheme/fulfilled": (e, t) => { e.shareTheme = t.payload.theme, e.isDirty = !1 }, "create/fulfilled": (e, t) => { e.customThemes.push(t.payload.theme), e.isDirty = !0 }, "update/fulfilled": (e, t) => { e.customThemes = e.customThemes.map((e => { var n; return (null === e || void 0 === e ? void 0 : e.id) === (null === (n = t.payload.theme) || void 0 === n ? void 0 : n.id) ? t.payload.theme : e })), e.isDirty = !0 }, "delete/fulfilled": (e, t) => { e.customThemes = e.customThemes.filter((e => e.id !== t.payload.themeId)), e.isDirty = !0 } }, extraReducers: { "chart/migrateFormat/fulfilled": (e, t) => { const { theme: n } = t.payload;
                                n && e.customThemes.push(n) }, "chart/get/fulfilled": (e, t) => { const { chart: n, theme: r } = t.payload, { shareTheme: a } = n || {};
                                a && (e.shareTheme = a), null !== r && void 0 !== r && r.id && null !== e && void 0 !== e && e.customThemes && e.customThemes.findIndex((e => (null === r || void 0 === r ? void 0 : r.id) === (null === e || void 0 === e ? void 0 : e.id))) < 0 && e.customThemes.push(r) } } }),
                    { resetDirty: u } = d.actions,
                    h = d.reducer }, 31777: (e, t, n) => { "use strict";
                n.d(t, { A: () => B }); var r, a = n(65043),
                    o = n(57528); const i = (0, n(72119).Ay)("div")(r || (r = (0, o.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n\tposition: relative;\n\tbackground-color: ".concat(t.palette.common.white, ";\n\tdisplay:flex;\n\theight: 20px;\n\tflex-direction: row;\n\talign-items: center;\n\tjustify-content: flex-start;\n\tpadding: ").concat(t.spacing(4), "px ").concat(t.spacing(0), "px;\n\tcolor: ").concat(t.palette.grey[600], ";\n\tgrid-gap: 12px;\n\n  ") })); var l = n(75156),
                    s = n(72835),
                    c = n(49092); const d = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit", selectedOneRequired: !0 }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedOneRequired: !0 }],
                    u = [{ type: "button", tooltip: "Restore", icon: "Undo", name: "restore", selectedOneRequired: !0, tooltipMsg: "Restore organization data from the selected backup snapshot" }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0, tooltipMsg: "Delete selected backup snapshots" }],
                    h = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit", selectedOneRequired: !0 }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0 }, { type: "button", tooltip: "Duplicate", icon: "FileCopy", name: "fileCopy", selectedOneRequired: !0, separate: !0 }, { type: "button", tooltip: "Import", icon: "GetApp", name: "import" }, { type: "button", tooltip: "Export", icon: "Publish", separate: !1, name: "export", selectedMaxOneRequired: !0 }, { type: "button", tooltip: "Print", icon: "PrintOutlined", separate: !1, name: "print", selectedOneRequired: !0 }],
                    m = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit", selectedOneRequired: !0 }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0 }, { type: "button", tooltip: "CleanUp", icon: "Broom", name: "cleanup", separate: !0 }, { type: "button", tooltip: "Import", icon: "GetApp", name: "import" }, { type: "button", tooltip: "Export", icon: "Publish", name: "export" }],
                    p = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit", selectedOneRequired: !0 }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0 }, { type: "button", tooltip: "ToggleAccess", icon: "Filter", name: "filter" }],
                    f = [{ type: "button", tooltip: "Group", icon: "Group", name: "Group" }, { type: "button", tooltip: "Sort", icon: "Sort", separate: !0, name: "Sort" }, { type: "button", tooltip: "Print", icon: "PrintOutlined", name: "Print" }],
                    v = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit" }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete" }, { type: "button", tooltip: "Chart", icon: "Chart", name: "chart" }]; var g = n(9579),
                    y = n(61531),
                    b = n(58168),
                    w = n(80045),
                    z = n(65173),
                    x = n.n(z),
                    A = n(11978),
                    k = n(80219),
                    S = n.n(k),
                    M = n(70567),
                    E = n(45195),
                    C = n(86691),
                    T = function(e, t) { return !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2] ? E.H.indexOf(e) <= E.H.indexOf(t) : E.H.indexOf(e) < E.H.indexOf(t) },
                    H = function(e, t) { return !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2] ? E.H.indexOf(t) <= E.H.indexOf(e) : E.H.indexOf(t) < E.H.indexOf(e) },
                    L = "undefined" === typeof window ? a.useEffect : a.useLayoutEffect; const I = function() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return function(t) { var n = e.withTheme,
                            r = void 0 !== n && n,
                            o = e.noSSR,
                            i = void 0 !== o && o,
                            l = e.initialWidth;

                        function s(e) { var n = (0, M.A)(),
                                o = e.theme || n,
                                s = (0, A.A)({ theme: o, name: "MuiWithWidth", props: (0, b.default)({}, e) }),
                                c = s.initialWidth,
                                d = s.width,
                                u = (0, w.A)(s, ["initialWidth", "width"]),
                                h = a.useState(!1),
                                m = h[0],
                                p = h[1];
                            L((function() { p(!0) }), []); var f = o.breakpoints.keys.slice().reverse().reduce((function(e, t) { var n = (0, C.A)(o.breakpoints.up(t)); return !e && n ? t : e }), null),
                                v = (0, b.default)({ width: d || (m || i ? f : void 0) || c || l }, r ? { theme: o } : {}, u); return void 0 === v.width ? null : a.createElement(t, v) } return S()(s, t), s } };

                function j(e) { var t = e.children,
                        n = e.only,
                        r = e.width,
                        a = (0, M.A)(),
                        o = !0; if (n)
                        if (Array.isArray(n))
                            for (var i = 0; i < n.length; i += 1) { if (r === n[i]) { o = !1; break } } else n && r === n && (o = !1); if (o)
                        for (var l = 0; l < a.breakpoints.keys.length; l += 1) { var s = a.breakpoints.keys[l],
                                c = e["".concat(s, "Up")],
                                d = e["".concat(s, "Down")]; if (c && T(s, r) || d && H(s, r)) { o = !1; break } }
                    return o ? t : null } j.propTypes = { children: x().node, className: x().string, implementation: x().oneOf(["js", "css"]), initialWidth: x().oneOf(["xs", "sm", "md", "lg", "xl"]), lgDown: x().bool, lgUp: x().bool, mdDown: x().bool, mdUp: x().bool, only: x().oneOfType([x().oneOf(["xs", "sm", "md", "lg", "xl"]), x().arrayOf(x().oneOf(["xs", "sm", "md", "lg", "xl"]))]), smDown: x().bool, smUp: x().bool, width: x().string.isRequired, xlDown: x().bool, xlUp: x().bool, xsDown: x().bool, xsUp: x().bool }; const V = I()(j); var O = n(64467),
                    R = n(74822); const P = (0, n(71745).A)((function(e) { var t = { display: "none" }; return e.breakpoints.keys.reduce((function(n, r) { return n["only".concat((0, R.A)(r))] = (0, O.A)({}, e.breakpoints.only(r), t), n["".concat(r, "Up")] = (0, O.A)({}, e.breakpoints.up(r), t), n["".concat(r, "Down")] = (0, O.A)({}, e.breakpoints.down(r), t), n }), {}) }), { name: "PrivateHiddenCss" })((function(e) { var t = e.children,
                        n = e.classes,
                        r = e.className,
                        o = e.only,
                        i = ((0, w.A)(e, ["children", "classes", "className", "only"]), (0, M.A)()),
                        l = [];
                    r && l.push(r); for (var s = 0; s < i.breakpoints.keys.length; s += 1) { var c = i.breakpoints.keys[s],
                            d = e["".concat(c, "Up")],
                            u = e["".concat(c, "Down")];
                        d && l.push(n["".concat(c, "Up")]), u && l.push(n["".concat(c, "Down")]) } return o && (Array.isArray(o) ? o : [o]).forEach((function(e) { l.push(n["only".concat((0, R.A)(e))]) })), a.createElement("div", { className: l.join(" ") }, t) })); const D = function(e) { var t = e.implementation,
                            n = void 0 === t ? "js" : t,
                            r = e.lgDown,
                            o = void 0 !== r && r,
                            i = e.lgUp,
                            l = void 0 !== i && i,
                            s = e.mdDown,
                            c = void 0 !== s && s,
                            d = e.mdUp,
                            u = void 0 !== d && d,
                            h = e.smDown,
                            m = void 0 !== h && h,
                            p = e.smUp,
                            f = void 0 !== p && p,
                            v = e.xlDown,
                            g = void 0 !== v && v,
                            y = e.xlUp,
                            z = void 0 !== y && y,
                            x = e.xsDown,
                            A = void 0 !== x && x,
                            k = e.xsUp,
                            S = void 0 !== k && k,
                            M = (0, w.A)(e, ["implementation", "lgDown", "lgUp", "mdDown", "mdUp", "smDown", "smUp", "xlDown", "xlUp", "xsDown", "xsUp"]); return "js" === n ? a.createElement(V, (0, b.default)({ lgDown: o, lgUp: l, mdDown: c, mdUp: u, smDown: m, smUp: f, xlDown: g, xlUp: z, xsDown: A, xsUp: S }, M)) : a.createElement(P, (0, b.default)({ lgDown: o, lgUp: l, mdDown: c, mdUp: u, smDown: m, smUp: f, xlDown: g, xlUp: z, xsDown: A, xsUp: S }, M)) },
                    F = [{ type: "button", tooltip: "Sort", icon: "Sort", name: "sort" }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0 }],
                    N = [{ type: "button", tooltip: "Edit", icon: "Edit", name: "edit", selectedOneRequired: !0 }, { type: "button", tooltip: "Delete", icon: "Delete", name: "delete", selectedRequired: !0 }]; var _ = n(70579); const B = e => { let { buttonGroup: t, selectedCount: n, getIsButtonValid: r, handleActionClick: o, userType: b } = e; const { t: w } = (0, c.B)(), z = (0, a.useMemo)((() => { switch (t) {
                            case "organization_customfieldslist":
                                return d;
                            case "organization_snapshots":
                                return u;
                            case "organization_chartlist":
                                return b && "viewer" === b ? h.filter((e => !["import", "edit", "delete", "fileCopy"].includes(e.name))) : h;
                            case "organization_peoplelist":
                                return b && "editor" === b ? m.filter((e => !("import" === e.name || "export" === e.name))) : m;
                            case "chart_userlist":
                                return N;
                            case "organization_userlist":
                                return p;
                            case "chart_photoboard":
                                return f;
                            case "roles_connections":
                                return b && "viewer" === b ? v.filter((e => "chart" === e.name)) : v;
                            case "talent_pool":
                                return b && "viewer" === b ? F.filter((e => !["import", "edit", "delete", "fileCopy"].includes(e.name))) : F;
                            default:
                                throw Error("Button group not yet handled") } }), [t, b]), x = e => "function" === typeof r && !r(e.name) || e.selectedRequired && !n || e.selectedOneRequired && 1 !== n || e.selectedMaxOneRequired && n > 1; return (0, _.jsx)(i, { elevation: 1, children: z.map((e => (0, _.jsxs)(a.Fragment, { children: [(null === e || void 0 === e ? void 0 : e.tooltipMsg) && (0, _.jsx)(g.Ay, { title: e.tooltipMsg, placement: "bottom", arrow: !0, children: (0, _.jsx)(y.A, { children: (0, _.jsx)(s.A, { size: "small", disabled: x(e), variant: "outlined", color: "default", onClick: o(e.name), startIcon: (0, _.jsx)(l.Ay, { color: "inherit", icon: e.icon, fontSize: "large" }), children: (0, _.jsxs)(D, { smDown: !0, children: [w("Toolbar.Tooltip.".concat(e.tooltip)), "talent_pool" === t && e.selectedRequired && !!n && "(".concat(n, ")")] }) }) }) }), !(null !== e && void 0 !== e && e.tooltipMsg) && (0, _.jsx)(s.A, { size: "small", disabled: x(e), variant: "outlined", color: "default", onClick: o(e.name), startIcon: (0, _.jsx)(l.Ay, { color: "inherit", icon: e.icon, fontSize: "large" }), children: (0, _.jsxs)(D, { smDown: !0, children: [w("Toolbar.Tooltip.".concat(e.tooltip)), "talent_pool" === t && e.selectedRequired && !!n && "(".concat(n, ")")] }) })] }, "toolbar-button-".concat(e.name)))) }) } }, 49015: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => f, F3: () => m, Uu: () => h, aD: () => p, ip: () => s, lf: () => c }); var r = n(80907),
                    a = n(9787),
                    o = n(47730); const i = "user",
                    l = { organizations: [], user: null },
                    s = (0, a.a)({ slice: i, scope: "users" }),
                    c = (0, o.B)({ slice: i, scope: "users" }),
                    d = (e, t) => { var n, r; const a = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.licenseId,
                            o = null === t || void 0 === t || null === (r = t.payload) || void 0 === r ? void 0 : r.usage; if (a && o) { const t = e.licenses.findIndex((e => e.id === a)); - 1 !== t && (e.licenses[t].usage = o) } },
                    u = (0, r.Z0)({ name: i, initialState: l, reducers: { addOrganization: (e, t) => { var n, r; const a = null === t || void 0 === t || null === (n = t.payload) || void 0 === n || null === (r = n.organization) || void 0 === r ? void 0 : r.id;
                                a && !e.organizations.find((e => e.id === a)) && e.organizations.push({ ...t.payload.organization }) }, "register/fulfilled": (e, t) => ({ ...l, ...t.payload.user }), "trackGoal/fulfilled": (e, t) => { let { payload: { goals: n } } = t;
                                e.goals = n || {} }, "getMe/fulfilled": (e, t) => ({ ...l, ...t.payload.user }), "getMeLite/fulfilled": (e, t) => ({ ...l, ...t.payload.user }), "updateProfile/fulfilled": (e, t) => ({ ...e, ...t.payload.user }), "getMe/rejected": () => ({ ...l }), "getMeLite/rejected": () => ({ ...l }), "logout/fulfilled": () => ({ ...l }), "getOrganizations/fulfilled": (e, t) => { e.organizations = t.payload.organizations }, "getOrganizations/rejected": e => { e.organizations = [] }, "getLicenses/fulfilled": (e, t) => { e.licenses = t.payload.licenses }, "getPublicLicense/fulfilled": (e, t) => { e.licenses = [t.payload.license] }, removeOrganization: (e, t) => { e.organizations = e.organizations.filter((e => e.id !== t.payload.id)) }, "activateFree/fulfilled": (e, t) => { let { payload: { license: n, userProfile: r } } = t;
                                e.licenses = [n], e.userProfile = r }, "createNewTrial/fulfilled": (e, t) => { let { payload: { userProfile: n, license: r, organizations: a } } = t;
                                e.licenses = [r], e.userProfile = n, e.organizations = a }, setSamlConfigured: (e, t) => { var n, r; const a = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.licenseId) || "",
                                    o = (null === t || void 0 === t || null === (r = t.payload) || void 0 === r ? void 0 : r.isSamlConfigured) || !1; if (a) { const t = e.licenses.findIndex((e => e.id === a)); - 1 !== t && (e.licenses[t].isSamlConfigured = o) } } }, extraReducers: { "organization/remove/fulfilled": (e, t) => { var n; const { id: r } = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.organization) || {}, a = e.organizations.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === r)); - 1 !== a && e.organizations.splice(a, 1) }, "organization/create/fulfilled": (e, t) => { e.organizations.push({ ...t.payload.organization }) }, "organization/update/fulfilled": (e, t) => { const { organization: n } = t.payload, { id: r } = n;
                                e.organizations = e.organizations.map((e => r === e.id ? { ...e, ...n } : e)) }, "cleanup/mergeDuplicatePeople/fulfilled": (e, t) => { d(e, t) }, "cleanup/deleteUnassignedPeople/fulfilled": (e, t) => { d(e, t) }, "cleanup/deleteCharts/fulfilled": (e, t) => { d(e, t) }, "cleanup/deleteFields/fulfilled": (e, t) => { d(e, t) }, "cleanup/deleteOwnersAndAdmins/fulfilled": (e, t) => { d(e, t) }, "license/getUsage/fulfilled": (e, t) => { var n, r; const a = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.id,
                                    o = null === t || void 0 === t || null === (r = t.payload) || void 0 === r ? void 0 : r.usage; if (a && o) { const t = e.licenses.findIndex((e => e.id === a)); - 1 !== t && (e.licenses[t].usage = o) } }, "license/updateMetadata/fulfilled": (e, t) => { var n, r; const a = ["companyName"],
                                    o = null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.licenseId,
                                    i = t.payload.license || {},
                                    l = (e.licenses || []).find((e => e.id === o)); if (l)
                                    for (let s of a) l[s] = i[s] }, "license/endTrial/fulfilled": (e, t) => { var n; const r = null === (n = t.payload) || void 0 === n ? void 0 : n.license; if (r && r.id && Array.isArray(e.licenses)) { const t = e.licenses.findIndex((e => e.id === r.id)); - 1 !== t && (e.licenses[t] = r) } }, "license/getSubscriptionInfo/fulfilled": (e, t) => { var n, r, a; const o = null === t || void 0 === t || null === (n = t.meta) || void 0 === n || null === (r = n.arg) || void 0 === r ? void 0 : r.licenseId,
                                    i = null === t || void 0 === t || null === (a = t.payload) || void 0 === a ? void 0 : a.subscriptionInfo; if (o && i) { const t = e.licenses.findIndex((e => e.id === o)); - 1 !== t && (e.licenses[t].subscriptionInfo = i) } }, "license/updateAllowedLoginMethods/fulfilled": (e, t) => { var n, r; const a = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.id,
                                    o = null === t || void 0 === t || null === (r = t.payload) || void 0 === r ? void 0 : r.allowedLoginMethods; if (a) { const t = e.licenses.findIndex((e => e.id === a)); - 1 !== t && (e.licenses[t].allowedLoginMethods = o) } }, "license/createNewPlanCheckout/fulfilled": (e, t) => { let { payload: { license: n, userProfile: r } } = t;
                                e.licenses = [n], e.userProfile = r }, "chart/createDemo/fulfilled": (e, t) => { var n; const r = null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.organization;
                                null !== r && void 0 !== r && r.id && e.organizations.unshift(r) } } }),
                    { removeOrganization: h, addOrganization: m, setSamlConfigured: p } = u.actions,
                    f = u.reducer }, 75490: (e, t, n) => { "use strict";
                n.d(t, { A: () => X }); var r = n(65043),
                    a = n(393),
                    o = n(84),
                    i = n(90318),
                    l = n(40454),
                    s = n(55357),
                    c = n(20495),
                    d = n(33843),
                    u = n(96364),
                    h = n(70318),
                    m = n(41583),
                    p = n(86852),
                    f = n(84866),
                    v = n(72835),
                    g = n(83734),
                    y = n(9989),
                    b = n(87603),
                    w = n(28269),
                    z = n(70512),
                    x = n(75156),
                    A = n(70579); const k = e => { let { updateData: t, emails: n, message: a } = e; const [o, i] = (0, r.useState)(null), [s, c] = (0, r.useState)(null), d = (0, r.useRef)(), h = (0, r.useRef)(), m = () => { i(null); let e = [],
                                r = [];
                            d.current.value.split(",").forEach((t => { let a = t.trim();
                                a.match(z.eT) ? n.includes(a) || r.push(a) : a.length && e.push(a) })), t({ emails: [...n, ...r] }), d.current.value = "", e.length > 0 && (i(!0), console.log(e), d.current.value = e.join()) }, p = () => { let e = h.current.value.trim();
                            c("" === e || null), t({ message: e }) }; return (0, A.jsx)(A.Fragment, { children: (0, A.jsxs)(l.A, { container: !0, direction: "column", justifyContent: "flex-start", children: [(0, A.jsx)(u.A, { variant: "subtitle1", children: "Users to Invite:" }), (0, A.jsx)(u.A, { variant: "body2", children: "Ex: <EMAIL>, <EMAIL>" }), (0, A.jsx)(f.A, { id: "email-invites", label: "Emails", inputRef: d, multiline: !0, error: o, rows: 4, defaultValue: "", fullWidth: !0, onBlur: m }), (0, A.jsx)(l.A, { container: !0, direction: "row", justifyContent: "center", spacing: 2, children: (0, A.jsx)(l.A, { item: !0, xs: 6, children: (0, A.jsx)(v.A, { variant: "outlined", color: "default", startIcon: (0, A.jsx)(x.Ay, { icon: "Add" }), fullWidth: !0, onClick: m, children: "Add Email(s)" }) }) }), n.length > 0 && (0, A.jsxs)(A.Fragment, { children: [(0, A.jsx)(u.A, { variant: "subtitle1", children: "Include a custom message:" }), (0, A.jsx)(f.A, { id: "invitation-message", label: "Message", multiline: !0, error: s, inputRef: h, rows: 4, defaultValue: a, fullWidth: !0, onBlur: p, onChange: p }), (0, A.jsxs)(u.A, { variant: "subtitle1", children: ["To be invited (", n.length, "):"] }), (0, A.jsx)(y.A, { component: "div", "aria-label": "users to invite", children: n.map((e => (0, A.jsxs)(b.A, { children: [(0, A.jsx)(w.A, { children: (0, A.jsx)(g.A, {}) }), (0, A.jsx)(u.A, { variant: "body2", children: e })] }, e))) })] })] }) }) },
                    S = e => { let { updateData: t, message: n } = e; const [a, o] = (0, r.useState)(null), i = (0, r.useRef)(); return (0, A.jsx)(A.Fragment, { children: (0, A.jsx)(l.A, { container: !0, direction: "column", justifyContent: "flex-start", children: (0, A.jsxs)(A.Fragment, { children: [(0, A.jsx)(u.A, { variant: "subtitle1", children: "Include a custom message:" }), (0, A.jsx)(f.A, { id: "invitation-message", label: "Message", multiline: !0, error: a, inputRef: i, rows: 4, defaultValue: n, fullWidth: !0, onChange: () => { let e = i.current.value.trim();
                                            o("" === e || null), t({ message: e }) } })] }) }) }) }; var M = n(67467),
                    E = n(61531),
                    C = n(16853),
                    T = n(31287),
                    H = n(49147),
                    L = n(8289); const I = e => { let { updateData: t, updateOrgData: n, mode: a, data: { organizations: o, emails: i, message: l }, userEdited: s } = e; const [c, d] = (0, r.useState)(!0), h = () => o.length <= 10 ? 10 : 6, m = (0, r.useMemo)((() => o.filter((e => e.checked)).length), [o]), f = o.length && m === o.length; return (0, A.jsxs)(A.Fragment, { children: [(0, A.jsxs)(u.A, { variant: "subtitle1", children: ["Organization Access (", m, ")"] }), (0, A.jsx)(M.A, { children: (0, A.jsxs)(E.A, { display: "flex", alignItems: "center", gridGap: 8, children: [(0, A.jsx)(C.A, { checked: f, onChange: e => { let t = o.map((t => ({ ...t, checked: e.target.checked })));
                                            n(t) }, name: "all" }), (0, A.jsx)(T.A, { children: " - Select All Organizations(".concat(o.length, ") -") })] }) }), (0, A.jsx)(H.A, { children: o.slice(0, c ? h() : o.length).map((e => { return (0, A.jsx)(M.A, { children: (0, A.jsxs)(E.A, { display: "flex", alignItems: "center", gridGap: 8, children: [(0, A.jsx)(C.A, { checked: e.checked, onChange: (t = e.id, e => { let r = o.map((n => n.id === t ? { ...n, checked: e.target.checked } : n));
                                                    n(r) }), name: e.id }), (0, A.jsx)(T.A, { children: e.name })] }) }); var t })) }), o.length > h() && (0, A.jsx)(p.A, { variant: "span", spacing: { all: 0 }, children: (0, A.jsx)(L.A, { component: "button", variant: "body1", color: "primary", className: "more-button", onClick: () => d(!c), children: c ? "..More" : "<< Less" }) }), "add" === a && (0, A.jsx)(k, { updateData: t, emails: i, message: l }), "edit" === a && s && (0, A.jsx)(S, { updateData: t, emails: i, message: l })] }) },
                    j = e => { let { updateData: t, mode: n, data: { emails: r, message: a }, userEdited: o } = e; return (0, A.jsxs)(A.Fragment, { children: ["add" === n && (0, A.jsx)(k, { updateData: t, emails: r, message: a }), "edit" === n && o && (0, A.jsx)(S, { updateData: t, emails: r, message: a })] }) }; var V = n(73083); const O = e => { let { updateData: t, updateChartData: n, mode: a, data: { charts: o, emails: i, message: l }, userEdited: s } = e; const [c, d] = (0, r.useState)(!0), h = () => o.length <= 10 ? 10 : 6, m = (0, r.useMemo)((() => o.filter((e => e.checked)).length), [o]), f = o.length && m === o.length; return (0, A.jsxs)(A.Fragment, { children: [(0, A.jsxs)(u.A, { variant: "subtitle1", children: ["Chart Access (", o.filter((e => e.checked)).length, ")"] }), (0, A.jsx)(V.A, { control: (0, A.jsx)(C.A, { checked: f, onChange: e => { let t = o.map((t => ({ ...t, checked: e.target.checked })));
                                    n(t) }, name: "all" }), label: " - Select All Charts(".concat(o.length, ") -") }), (0, A.jsx)(H.A, { children: o.slice(0, c ? h() : o.length).map((e => { return (0, A.jsx)(V.A, { control: (0, A.jsx)(C.A, { checked: e.checked, onChange: (t = e.id, e => { let r = o.map((n => n.id === t ? { ...n, checked: e.target.checked } : n));
                                            n(r) }), name: e.id }), label: e.name }, e.id); var t })) }), o.length > h() && (0, A.jsx)(p.A, { variant: "span", spacing: { all: 0 }, children: (0, A.jsx)(L.A, { component: "button", variant: "body1", color: "primary", className: "more-button", onClick: () => d(!c), children: c ? "..More" : "<< Less" }) }), "add" === a && (0, A.jsx)(k, { updateData: t, emails: i, message: l }), "edit" === a && s && (0, A.jsx)(S, { updateData: t, emails: i, message: l })] }) }; var R = n(49092); const P = e => { let { text: t } = e; const { t: n } = (0, R.B)(), r = { none: "\n<p>".concat(n("Text.Permissions.Selectan"), " <strong>").concat(n("Text.Permissions.AccessType"), "</strong> ").concat(n("Text.Permissions.abovetoseethelistofpermissionsprovidedforeachtype"), "</p>\n<strong>").concat(n("Text.Permissions.AvailaibleTypes"), "</strong>\n<ul>\n<li>").concat(n("Text.Permissions.AccountOwner"), "</li>\n<li>").concat(n("Text.Permissions.Administrator"), "</li>\n<li>").concat(n("Text.Permissions.Editor"), "</li>\n<li>").concat(n("Text.Permissions.Viewer"), "</li>\n</ul>\n"), admin: "\n<strong>ADMINS</strong>\n<br/><br/>\n<p>Permissions only for organizations where the admin has Admin access.</p>\n\n<p>Organizations</p>\n<ul>\n<li>Update Organization Profile</li>\n<li>Can only access the Organizations specified</li>\n</ul>\n\n<p>People</p>\n<ul>\n<li>Add, Edit, Import & Delete People</li>\n<li>Import People</li>\n</ul>\n\n<p>Charts</p>\n<ul>\n<li>Add, Edit, Import & Delete Charts</li>\n<li>Search Charts and Lists</li>\n<li>Setup Chart Legends</li>\n<li>Setup Integrations</li>\n</ul>\n\n<p>Sharing</p>\n<ul>\n<li>Share Charts, Lists and Reports</li>\n<li>Export to Excel/ CSV</li>\n<li>Print and Save to PDF, PPT, PNG</li>\n</ul>\n\n<p>Themes</p>\n<ul>\n<li>Create and edit standard themes</li>\n<li>Create and edit custom themes</li>\n</ul>\n\n<p>Reports</p>\n<ul>\n<li>Enable and access reports</li>\n</ul>\n", owner: "\n<strong>OWNERS</strong>\n<br/><br/>\n<p>Organizations</p>\n<ul>\n<li>Access All Organizations</li>\n<li>Add, Edit, Delete Organizations</li>\n</ul>\n\n<p>User Rights</p>\n<ul>\n<li>Add, Edit, Delete Users including Viewers and Editors</li>\n</ul>\n\n<p>People</p>\n<ul>\n<li>Add, Edit, Import & Delete People</li>\n<li>Import People</li>\n</ul>\n\n<p>Charts</p>\n<ul>\n<li>Add, Edit, Import & Delete Charts</li>\n<li>Search Charts and Lists</li>\n<li>Setup Chart Legends</li>\n<li>Setup Integrations</li>\n</ul>\n\n<p>Account</p>\n<ul>\n<li>Manage Plan and Billing Information</li>\n<li>View and Download Invoices</li>\n</ul>\n\n<p>Sharing</p>\n<ul>\n<li>Share Charts, Lists and Reports</li>\n<li>Export to Excel/ CSV</li>\n<li>Print and Save to PDF, PPT, PNG</li>\n</ul>\n\n<p>Themes</p>\n<ul>\n<li>Create and edit standard themes</li>\n<li>Create and edit custom themes</li>\n</ul>\n\n<p>Reports</p>\n<ul>\n<li>Enable and access reports</li>\n</ul>\n", viewer: "\n<strong>VIEWERS</strong>\n<br/><br/>\n<p>Access is Read-only.</p>\n<p>People</p>\n<ul>\n<li>View profiles of people in org charts</li>\n</ul>\n<p>Charts</p>\n<ul>\n<li>View charts shared with them</li>\n<li>View chart legends shared with them</li>\n<li>Search and view charts, directories & photo boards</li>\n<li>Cannot:\n    <ul>\n    <li>View themes</li>\n    <li>View private custom fields</li>\n    <li>Create custom fields</li>\n    </ul>\n</li>\n</ul>\n\n<p>Sharing</p>\n<ul>\n<li>Print and save to PDF, PPT, & PNG</li>\n<li>Cannot:\n  <ul>\n  <li>View private custom fields</li>\n  </ul>\n</li></ul>\n", editor: "\n<strong>EDITORS</strong>\n<br/><br/>\n<p>People</p>\n<ul>\n<li>Edit role details of people in org charts</li>\n<li>View profiles of people in org charts</li>\n</ul>\n<p>Charts</p>\n<ul>\n<li>Edit and view charts</li>\n<li>View chart legends</li>\n<li>Search charts, directories & photo boards</li>\n<li>Cannot:\n    <ul>\n    <li>View themes</li>\n    <li>View private custom fields</li>\n    <li>Create custom fields</li>\n    </ul>\n</li>\n</ul>\n\n<p>Sharing</p>\n<ul>\n<li>Print and save to PDF, PPT, & PNG</li>\n<li>Cannot:\n  <ul>\n  <li>View private custom fields</li>\n  </ul>\n</li>\n</ul>\n" }; return (0, A.jsx)(A.Fragment, { children: (0, A.jsx)("div", { dangerouslySetInnerHTML: { __html: r[t] } }) }) }; var D = n(2173),
                    F = n(66588),
                    N = n(14556),
                    _ = n(78300),
                    B = n(97105),
                    W = n(43331),
                    U = n(42006),
                    q = n(67479),
                    G = n(59177),
                    K = n(48853),
                    Z = n(78396),
                    Y = n(24115); const X = () => { const e = { emails: [], message: "", organizations: [], charts: [] },
                        { toggleDialog: t, props: { mode: n, email: g, callback: y } } = (0, o.A)("userCard"),
                        [b, w] = (0, r.useState)("none"),
                        [z, k] = (0, r.useState)(null),
                        [S, M] = (0, r.useState)(null),
                        [E, C] = (0, r.useState)(e),
                        T = (0, N.wA)(),
                        { show: H } = (0, F.A)(),
                        { license: L, id: V } = (0, N.d4)(W.BF),
                        { firstName: R } = (0, N.d4)(U.VW),
                        X = (0, N.d4)(W.Ot),
                        $ = (0, N.d4)(U.zl),
                        Q = (0, r.useMemo)((() => (0, q.cb)(g)), [g]),
                        J = (0, N.d4)(Q),
                        [ee, te] = (0, r.useState)(!1),
                        { userHasMaxAccess: ne } = (0, K.A)(),
                        re = () => { M(null) },
                        ae = e => { C({ ...E, ...e }), te(!0) },
                        oe = e => { C({ ...E, charts: e }), te(!0) },
                        ie = { owner: (0, A.jsx)(j, { updateData: ae, mode: n, user: z, data: E, userEdited: ee }), admin: (0, A.jsx)(I, { updateData: ae, updateOrgData: e => { C({ ...E, organizations: e }), te(!0) }, mode: n, user: z, data: E, userEdited: ee }), editor: (0, A.jsx)(O, { updateData: ae, updateChartData: oe, mode: n, user: z, data: E, userEdited: ee }), viewer: (0, A.jsx)(O, { updateData: ae, updateChartData: oe, mode: n, user: z, data: E, userEdited: ee }) },
                        [le, se] = (0, D.A)((async () => { const e = b,
                                r = E.emails.map((e => ({ email: e }))),
                                a = E.organizations.reduce(((e, t) => (t.checked && e.push({ id: t.id }), e)), []),
                                o = E.charts.reduce(((e, t) => (t.checked && e.push({ id: t.id }), e)), []),
                                i = E.message; if ("add" === n) { const { payload: n } = await T(B.Bf.createUser({ type: e, users: r, orgId: V, organizations: a, charts: o, message: i, licenseId: "owner" === e ? L : null })), { errors: l } = n;
                                null !== l && void 0 !== l && l.length ? l.forEach((e => { H(e, "error", 6e3) })) : (y && y(n.users), t(), H("User added successfully", "success", 5e3)) } else { const { payload: n } = await T(B.Bf.updateUser({ type: e, users: r, orgId: V, organizations: a, charts: o, message: i, email: g, licenseId: L })), { errors: l } = n;
                                null !== l && void 0 !== l && l.length ? l.forEach((e => { H(e, "error", 6e3) })) : (y && y(n.user), t(), H("Changes saved successfully", "success", 5e3)) } }));
                    (0, r.useEffect)((() => { "add" === n ? (w("none"), k(null)) : J && (k(J), w(J.type), te(!1)) }), [J, n]), (0, r.useEffect)((() => {
                        function t(e) { return !!J && J.organizations.findIndex((t => t.id === e)) > -1 } C({ ...e, organizations: $.map((e => ({ id: e.id, checked: !!J && t(e.id), name: e.name }))).sort((e => J && e.checked ? -1 : 0)), charts: X.map((e => { return { id: e.id, checked: !!J && (t = e.id, !!J && J.charts.findIndex((e => e.id === t)) > -1), name: e.name }; var t })).sort((e => J && e.checked ? -1 : 0)), message: "add" === n ? "" : "".concat(R, " has modified your permissions.") }) }), [b, n, J, $]); const ce = (0, r.useMemo)((() => E.organizations.filter((e => e.checked)).length), [E]),
                        de = (0, r.useMemo)((() => E.charts.filter((e => e.checked)).length), [E]); return (0, A.jsxs)(A.Fragment, { children: [(0, A.jsx)(a.Ay, { children: (0, A.jsx)(Y.A, { loading: le, transparent: !0, className: "fullHeight", children: (0, A.jsx)(i.A, { variant: "div", spacing: { all: 1 }, className: "fullHeight", children: (0, A.jsxs)(l.A, { container: !0, direction: "column", justifyContent: "space-between", className: "fullHeight", children: [(0, A.jsxs)(l.A, { container: !0, justifyContent: "space-between", children: [(0, A.jsxs)(m.W, { container: !0, justifyContent: "flex-start", children: ["edit" === n && (0, A.jsxs)(A.Fragment, { children: [(0, A.jsx)(p.A, { variant: "div", spacing: { left: 1, right: 2, top: 1 }, children: (0, A.jsx)(_.A, { width: 100, height: 100 }) }), (0, A.jsxs)(p.A, { variant: "div", spacing: { top: 3 }, children: [(0, A.jsx)(u.A, { variant: "h3", children: z && z.typeDescription }), (0, A.jsx)(u.A, { variant: "h4", weight: "light", children: z && (0, G.RP)(z.name, 20) }), (0, A.jsx)(u.A, { variant: "body1", children: z && (0, G.RP)(z.email, 20) })] })] }), (0, A.jsxs)(p.A, { variant: "div", spacing: { left: 3, top: 3 }, className: "wrapper", children: [(0, A.jsx)(p.A, { spacing: { bottom: 2 }, children: (0, A.jsx)(u.A, { variant: "h3", children: "add" === n ? "Add a New User" : "Edit User" }) }), (0, A.jsxs)(l.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, A.jsxs)(f.A, { name: "access", select: !0, className: "access-select", id: "access-type", color: "primary", label: "Access Type", disabled: ne(Z.td.ADMIN) && "owner" === b, value: b, onChange: e => { ne(Z.td.ADMIN) && "owner" === b && "owner" !== e.target.value || ne(Z.td.ADMIN) && "owner" !== b && "owner" === e.target.value ? H("You don't have sufficient permissions to do this", "error", 5e3) : (w(e.target.value), "none" !== e.target.value && te(!0)) }, children: [(0, A.jsx)(s.A, { value: "none", children: "- Select User Access -" }), (0, A.jsx)(s.A, { value: "owner", children: "Account Owners" }), (0, A.jsx)(s.A, { value: "admin", children: "Organization Administrators" }), (0, A.jsx)(s.A, { value: "editor", children: "Editors" }), (0, A.jsx)(s.A, { value: "viewer", children: "Viewers" })] }), "none" !== b && (0, A.jsx)(p.A, { variant: "span", spacing: { all: 1 }, children: (0, A.jsx)(h.A, { fontSize: "large", onClick: e => { M(e.currentTarget) }, children: (0, A.jsx)(x.gF, { icon: "Help", size: "lg" }) }) })] })] })] }), (0, A.jsx)(l.A, { item: !0, children: (0, A.jsx)(h.A, { onClick: () => t(), children: (0, A.jsx)(x.gF, { icon: "Close", size: "lg" }) }) })] }), (0, A.jsx)(m.D, { children: (0, A.jsxs)(p.A, { variant: "div", spacing: { left: 3, right: 3, bottom: 0 }, className: "content-wrapper fullHeight", children: ["none" === b && (0, A.jsx)(c.A, { elevation: 0, children: (0, A.jsxs)(l.A, { container: !0, direction: "column", justifyContent: "flex-start", alignItems: "center", children: [(0, A.jsx)(p.A, { variant: "div", spacing: { all: 3 }, children: (0, A.jsx)(x.Ay, { icon: "Directory", size: "x3" }) }), (0, A.jsx)(P, { text: "none" })] }) }), "none" !== b && (ue = b, ie[ue])] }) }), (0, A.jsxs)(l.A, { container: !0, justifyContent: "space-around", spacing: 2, children: [(0, A.jsx)(l.A, { item: !0, xs: 6, children: (0, A.jsx)(v.A, { variant: "outlined", color: "default", fullWidth: !0, disabled: le, onClick: () => t(), children: "Cancel" }) }), (0, A.jsx)(l.A, { item: !0, xs: 6, children: (0, A.jsx)(v.A, { variant: "contained", color: "primary", fullWidth: !0, disabled: E && ("add" === n && (0 === E.emails.length || "admin" === b && 0 === ce || ("editor" === b || "viewer" === b) && 0 === de) || "edit" === n && ("none" === b || "admin" === b && 0 === ce || ("editor" === b || "viewer" === b) && 0 === de || !ee) || le), onClick: se, children: "add" === n ? "Send Invitation(s)" : "Update Permissions" }) })] })] }) }) }) }), (0, A.jsx)(d.Ay, { id: "user-access-tooltip", anchorEl: S, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "center" }, keepMounted: !0, open: Boolean(S), onClose: re, children: (0, A.jsxs)(i.A, { variant: "div", spacing: { all: 2 }, children: [(0, A.jsx)(l.A, { container: !0, justifyContent: "flex-end", children: (0, A.jsx)(l.A, { item: !0, children: (0, A.jsx)(h.A, { onClick: () => re(), children: (0, A.jsx)(x.gF, { icon: "Close", size: "lg" }) }) }) }), (0, A.jsx)(l.A, { container: !0, alignItems: "center", justifyContent: "center", children: (0, A.jsx)(l.A, { item: !0, children: (0, A.jsx)(x.Ay, { icon: "Lock", size: "x3" }) }) }), (0, A.jsx)(P, { text: b })] }) })] }); var ue } }, 97105: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => c, Bf: () => l, CA: () => i }); var r = n(80907),
                    a = n(9787); const o = "userManagement",
                    i = (0, r.pU)({ selectId: e => e.email, sortComparer: (e, t) => e.email.localeCompare(t.email) }),
                    l = (0, a.a)({ slice: o, scope: "userManagement" }),
                    s = (e, t) => { let { payload: { users: n = [] } } = t; const r = []; for (let o of n)
                            if (o) { var a; switch (o.type) {
                                    case "owner":
                                        o.permissionLevel = 0, o.permissionDetails = "All"; break;
                                    case "admin":
                                        o.permissionLevel = 1, o.permissionDetails = "All"; break;
                                    case "editor":
                                        o.permissionLevel = 2, o.permissionDetails = "Edit charts and reports only"; break;
                                    case "viewer":
                                        o.permissionLevel = 3, o.permissionDetails = "View charts and people only" } o.email = null === o || void 0 === o || null === (a = o.email) || void 0 === a ? void 0 : a.toLowerCase(), o.charts = (null === o || void 0 === o ? void 0 : o.charts) || [], o.organizations = (null === o || void 0 === o ? void 0 : o.organizations) || [], null !== o && void 0 !== o && o.email && r.push(o) } i.upsertMany(e, r) },
                    c = (0, r.Z0)({ name: o, initialState: i.getInitialState({ orgIndexMap: {}, chartIndexMap: {}, licenseIndexMap: {}, userTypeMap: {} }), reducers: { "getChartUsers/fulfilled": s, "getOrganizationUsers/fulfilled": s, "createUser/fulfilled": s, "getUserWithPerms/fulfilled": (e, t) => { var n; let { payload: r } = t; if (null !== r && void 0 !== r && r.user && null !== r && void 0 !== r && null !== (n = r.user) && void 0 !== n && n.email) { var a, o; const t = { ...r.user, email: null === (a = r.user) || void 0 === a || null === (o = a.email) || void 0 === o ? void 0 : o.toLowerCase() };
                                    i.upsertOne(e, t) } }, "updateUser/fulfilled": (e, t) => { let { payload: { user: n } } = t; return s(e, { payload: { users: [n] } }) }, "deleteChartUsers/fulfilled": (e, t) => { let { payload: { users: n }, meta: { arg: r } } = t; const a = null === r || void 0 === r ? void 0 : r.chartId,
                                    o = n.map((e => { var t; return null === (t = e.email) || void 0 === t ? void 0 : t.toLowerCase() })).filter((e => e)); for (let i of o) { let t = e.entities[i];
                                    t.charts = (t.charts || []).filter((e => (null === e || void 0 === e ? void 0 : e.id) !== a)) } }, "deleteUsers/fulfilled": (e, t) => { let { payload: { users: n } } = t, r = n.map((e => { var t; return null === (t = e.email) || void 0 === t ? void 0 : t.toLowerCase() })).filter((e => e));
                                i.removeMany(e, r) } } }).reducer }, 41: (e, t, n) => { "use strict";
                n.d(t, { Q6: () => c, TU: () => i, Zg: () => s, yO: () => l }); var r = n(59177),
                    a = n(70512),
                    o = n(78396);

                function i(e) { return e.owners.map((e => e.username)).filter((e => e)).join(",") || "" }

                function l(e) { const t = ["admins", "organizations", "charts"]; let n = []; for (let o of t) { var a; let t = (null === e || void 0 === e || null === (a = e.usage) || void 0 === a ? void 0 : a[o]) || 0;!t || t > 1 ? n.push("".concat(t, " ").concat(o)) : 1 === t && n.push("".concat(t, " ").concat((0, r.NY)(o))) } return 0 === n.length ? "empty license" : n.join(", ") }

                function s(e, t) { if (!e) return ""; const n = null === t || void 0 === t ? void 0 : t.username,
                        i = (0, a.Vf)(n); let l = e.companyName; return l || (l = i && e.accessLevel === o.td.OWNER ? (0, a.NC)(n) : function(e) { let t = "".concat(e.type, " ").concat(e.plan.frequency); return "trialing" !== e.status || e.isExpired ? "free" === e.type && (t = "Limited Free Tier") : t += " trial", (0, r.ZH)(t) }(e)), l }

                function c(e) { return e ? e.accessLevel === o.td.OWNER ? "Account Owner" : e.accessLevel === o.td.ADMIN ? "Organization Administrator" : e.accessLevel === o.td.EDITOR ? "Chart Editor" : e.accessLevel === o.td.VIEWER ? "Chart Viewer" : "Basic Access" : "Basic Access" } }, 78396: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => h, G6: () => c, Uz: () => o, XD: () => l, ZE: () => u, dd: () => s, em: () => d, mv: () => i, td: () => r, uI: () => a }); const r = { PUBLIC: "public", VIEWER: "viewer", EDITOR: "editor", ADMIN: "admin", OWNER: "owner" },
                    a = { PUBLIC: "public", PRINT_PREVIEW: "printPreview", PRINT: "print", DEFAULT: "view", THEME: "theme", AI_INSIGHTS: "aiInsights", SETTINGS: "settings" },
                    o = "root",
                    i = { SINGLE: "single", ASSISTANT: "assistant", SHARED: "shared", EMBEDDED: "embedded", DEPARTMENT: "department", LOCATION: "location", FUNCTION: "function", TEAM: "team", HIDDEN: "hidden", MATRIX_ROOT_ROLE: "matrix_root" },
                    l = { MATRIX: "matrix", TRADITIONAL: "traditional", BOARD_OF_DIRECTORS: "board of directors" },
                    s = { FUNCTION: "Function", TEAM: "Team" },
                    c = { PUBLIC: "publicurl", EMBED: "iframe", PASSWORD: "password-link" },
                    d = { PASSWORD: "password-link", SESSION: "session", PUBLIC: "public" },
