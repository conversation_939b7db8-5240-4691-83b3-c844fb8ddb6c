        function qy() { return qy = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, qy.apply(this, arguments) }

        function Gy(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Ky(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Gy(Object(n), !0).forEach((function(t) { Zy(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Gy(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Zy(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Ny(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Ny(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Ny(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Yy(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a } var Xy = function(e) { return Array.isArray(e.value) ? Fy()(e.value) : e.value };

        function $y(e) { var t = e.valueAccessor,
                n = void 0 === t ? Xy : t,
                r = Yy(e, _y),
                o = r.data,
                i = r.dataKey,
                l = r.clockWise,
                s = r.id,
                c = r.textBreakAll,
                d = Yy(r, By); return o && o.length ? a.createElement(Po, { className: "recharts-label-list" }, o.map((function(e, t) { var r = Na()(i) ? n(e, t) : hg(e && e.payload, i),
                    o = Na()(s) ? {} : { id: "".concat(s, "-").concat(t) }; return a.createElement(Hy, qy({}, po(e, !0), d, o, { parentViewBox: e.parentViewBox, value: r, textBreakAll: c, viewBox: Hy.parseViewBox(Na()(l) ? e : Ky(Ky({}, e), {}, { clockWise: l })), key: "label-".concat(t), index: t })) }))) : null } $y.displayName = "LabelList", $y.renderCallByParent = function(e, t) { var n = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2]; if (!e || !e.children && n && !e.label) return null; var r = so(e.children, $y).map((function(e, n) { return (0, a.cloneElement)(e, { data: t, key: "labelList-".concat(n) }) })); return n ? [function(e, t) { return e ? !0 === e ? a.createElement($y, { key: "labelList-implicit", data: t }) : a.isValidElement(e) || Ba()(e) ? a.createElement($y, { key: "labelList-implicit", data: t, content: e }) : Ua()(e) ? a.createElement($y, qy({ data: t }, e, { key: "labelList-implicit" })) : null : null }(e.label, t)].concat(Wy(r)) : r }; var Qy = n(12322),
            Jy = n.n(Qy),
            eb = n(96361),
            tb = n.n(eb);

        function nb(e) { return nb = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, nb(e) }

        function rb() { return rb = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, rb.apply(this, arguments) }

        function ab(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return ob(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ob(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function ob(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function ib(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function lb(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? ib(Object(n), !0).forEach((function(t) { sb(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ib(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function sb(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != nb(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != nb(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == nb(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var cb = function(e, t, n, r, a) { var o, i = n - r; return o = "M ".concat(e, ",").concat(t), o += "L ".concat(e + n, ",").concat(t), o += "L ".concat(e + n - i / 2, ",").concat(t + a), o += "L ".concat(e + n - i / 2 - r, ",").concat(t + a), o += "L ".concat(e, ",").concat(t, " Z") },
            db = { x: 0, y: 0, upperWidth: 0, lowerWidth: 0, height: 0, isUpdateAnimationActive: !1, animationBegin: 0, animationDuration: 1500, animationEasing: "ease" },
            ub = function(e) { var t = lb(lb({}, db), e),
                    n = (0, a.useRef)(),
                    r = ab((0, a.useState)(-1), 2),
                    o = r[0],
                    i = r[1];
                (0, a.useEffect)((function() { if (n.current && n.current.getTotalLength) try { var e = n.current.getTotalLength();
                        e && i(e) } catch (t) {} }), []); var l = t.x,
                    s = t.y,
                    c = t.upperWidth,
                    d = t.lowerWidth,
                    u = t.height,
                    h = t.className,
                    m = t.animationEasing,
                    p = t.animationDuration,
                    f = t.animationBegin,
                    v = t.isUpdateAnimationActive; if (l !== +l || s !== +s || c !== +c || d !== +d || u !== +u || 0 === c && 0 === d || 0 === u) return null; var g = va("recharts-trapezoid", h); return v ? a.createElement(rd, { canBegin: o > 0, from: { upperWidth: 0, lowerWidth: 0, height: u, x: l, y: s }, to: { upperWidth: c, lowerWidth: d, height: u, x: l, y: s }, duration: p, animationEasing: m, isActive: v }, (function(e) { var r = e.upperWidth,
                        i = e.lowerWidth,
                        l = e.height,
                        s = e.x,
                        c = e.y; return a.createElement(rd, { canBegin: o > 0, from: "0px ".concat(-1 === o ? 1 : o, "px"), to: "".concat(o, "px 0px"), attributeName: "strokeDasharray", begin: f, duration: p, easing: m }, a.createElement("path", rb({}, po(t, !0), { className: g, d: cb(s, c, r, i, l), ref: n }))) })) : a.createElement("g", null, a.createElement("path", rb({}, po(t, !0), { className: g, d: cb(l, s, c, d, u) }))) };

        function hb(e) { return hb = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, hb(e) }

        function mb() { return mb = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, mb.apply(this, arguments) }

        function pb(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function fb(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? pb(Object(n), !0).forEach((function(t) { vb(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : pb(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function vb(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != hb(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != hb(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == hb(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var gb = function(e) { var t = e.cx,
                    n = e.cy,
                    r = e.radius,
                    a = e.angle,
                    o = e.sign,
                    i = e.isExternal,
                    l = e.cornerRadius,
                    s = e.cornerIsExternal,
                    c = l * (i ? 1 : -1) + r,
                    d = Math.asin(l / c) / sy,
                    u = s ? a : a + o * d,
                    h = s ? a - o * d : a; return { center: dy(t, n, c, u), circleTangency: dy(t, n, r, u), lineTangency: dy(t, n, c * Math.cos(d * sy), h), theta: d } },
            yb = function(e) { var t = e.cx,
                    n = e.cy,
                    r = e.innerRadius,
                    a = e.outerRadius,
                    o = e.startAngle,
                    i = function(e, t) { return Ca(t - e) * Math.min(Math.abs(t - e), 359.999) }(o, e.endAngle),
                    l = o + i,
                    s = dy(t, n, a, o),
                    c = dy(t, n, a, l),
                    d = "M ".concat(s.x, ",").concat(s.y, "\n    A ").concat(a, ",").concat(a, ",0,\n    ").concat(+(Math.abs(i) > 180), ",").concat(+(o > l), ",\n    ").concat(c.x, ",").concat(c.y, "\n  "); if (r > 0) { var u = dy(t, n, r, o),
                        h = dy(t, n, r, l);
                    d += "L ".concat(h.x, ",").concat(h.y, "\n            A ").concat(r, ",").concat(r, ",0,\n            ").concat(+(Math.abs(i) > 180), ",").concat(+(o <= l), ",\n            ").concat(u.x, ",").concat(u.y, " Z") } else d += "L ".concat(t, ",").concat(n, " Z"); return d },
            bb = { cx: 0, cy: 0, innerRadius: 0, outerRadius: 0, startAngle: 0, endAngle: 0, cornerRadius: 0, forceCornerRadius: !1, cornerIsExternal: !1 },
            wb = function(e) { var t = fb(fb({}, bb), e),
                    n = t.cx,
                    r = t.cy,
                    o = t.innerRadius,
                    i = t.outerRadius,
                    l = t.cornerRadius,
                    s = t.forceCornerRadius,
                    c = t.cornerIsExternal,
                    d = t.startAngle,
                    u = t.endAngle,
                    h = t.className; if (i < o || d === u) return null; var m, p = va("recharts-sector", h),
                    f = i - o,
                    v = Va(l, f, 0, !0); return m = v > 0 && Math.abs(d - u) < 360 ? function(e) { var t = e.cx,
                        n = e.cy,
                        r = e.innerRadius,
                        a = e.outerRadius,
                        o = e.cornerRadius,
                        i = e.forceCornerRadius,
                        l = e.cornerIsExternal,
                        s = e.startAngle,
                        c = e.endAngle,
                        d = Ca(c - s),
                        u = gb({ cx: t, cy: n, radius: a, angle: s, sign: d, cornerRadius: o, cornerIsExternal: l }),
                        h = u.circleTangency,
                        m = u.lineTangency,
                        p = u.theta,
                        f = gb({ cx: t, cy: n, radius: a, angle: c, sign: -d, cornerRadius: o, cornerIsExternal: l }),
                        v = f.circleTangency,
                        g = f.lineTangency,
                        y = f.theta,
                        b = l ? Math.abs(s - c) : Math.abs(s - c) - p - y; if (b < 0) return i ? "M ".concat(m.x, ",").concat(m.y, "\n        a").concat(o, ",").concat(o, ",0,0,1,").concat(2 * o, ",0\n        a").concat(o, ",").concat(o, ",0,0,1,").concat(2 * -o, ",0\n      ") : yb({ cx: t, cy: n, innerRadius: r, outerRadius: a, startAngle: s, endAngle: c }); var w = "M ".concat(m.x, ",").concat(m.y, "\n    A").concat(o, ",").concat(o, ",0,0,").concat(+(d < 0), ",").concat(h.x, ",").concat(h.y, "\n    A").concat(a, ",").concat(a, ",0,").concat(+(b > 180), ",").concat(+(d < 0), ",").concat(v.x, ",").concat(v.y, "\n    A").concat(o, ",").concat(o, ",0,0,").concat(+(d < 0), ",").concat(g.x, ",").concat(g.y, "\n  "); if (r > 0) { var z = gb({ cx: t, cy: n, radius: r, angle: s, sign: d, isExternal: !0, cornerRadius: o, cornerIsExternal: l }),
                            x = z.circleTangency,
                            A = z.lineTangency,
                            k = z.theta,
                            S = gb({ cx: t, cy: n, radius: r, angle: c, sign: -d, isExternal: !0, cornerRadius: o, cornerIsExternal: l }),
                            M = S.circleTangency,
                            E = S.lineTangency,
                            C = S.theta,
                            T = l ? Math.abs(s - c) : Math.abs(s - c) - k - C; if (T < 0 && 0 === o) return "".concat(w, "L").concat(t, ",").concat(n, "Z");
                        w += "L".concat(E.x, ",").concat(E.y, "\n      A").concat(o, ",").concat(o, ",0,0,").concat(+(d < 0), ",").concat(M.x, ",").concat(M.y, "\n      A").concat(r, ",").concat(r, ",0,").concat(+(T > 180), ",").concat(+(d > 0), ",").concat(x.x, ",").concat(x.y, "\n      A").concat(o, ",").concat(o, ",0,0,").concat(+(d < 0), ",").concat(A.x, ",").concat(A.y, "Z") } else w += "L".concat(t, ",").concat(n, "Z"); return w }({ cx: n, cy: r, innerRadius: o, outerRadius: i, cornerRadius: Math.min(v, f / 2), forceCornerRadius: s, cornerIsExternal: c, startAngle: d, endAngle: u }) : yb({ cx: n, cy: r, innerRadius: o, outerRadius: i, startAngle: d, endAngle: u }), a.createElement("path", mb({}, po(t, !0), { className: p, d: m, role: "img" })) },
            zb = ["option", "shapeType", "propTransformer", "activeClassName", "isActive"];

        function xb(e) { return xb = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, xb(e) }

        function Ab(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function kb(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Sb(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? kb(Object(n), !0).forEach((function(t) { Mb(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : kb(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Mb(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != xb(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != xb(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == xb(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Eb(e, t) { return Sb(Sb({}, t), e) }

        function Cb(e) { var t = e.shapeType,
                n = e.elementProps; switch (t) {
                case "rectangle":
                    return a.createElement(pd, n);
                case "trapezoid":
                    return a.createElement(ub, n);
                case "sector":
                    return a.createElement(wb, n);
                case "symbols":
                    if (function(e, t) { return "symbols" === e }(t)) return a.createElement(Cl, n); break;
                default:
                    return null } }

        function Tb(e) { var t, n = e.option,
                r = e.shapeType,
                o = e.propTransformer,
                i = void 0 === o ? Eb : o,
                l = e.activeClassName,
                s = void 0 === l ? "recharts-active-shape" : l,
                c = e.isActive,
                d = Ab(e, zb); if ((0, a.isValidElement)(n)) t = (0, a.cloneElement)(n, Sb(Sb({}, d), function(e) { return (0, a.isValidElement)(e) ? e.props : e }(n)));
            else if (Ba()(n)) t = n(d);
            else if (Jy()(n) && !tb()(n)) { var u = i(n, d);
                t = a.createElement(Cb, { shapeType: r, elementProps: u }) } else { var h = d;
                t = a.createElement(Cb, { shapeType: r, elementProps: h }) } return c ? a.createElement(Po, { className: s }, t) : t }

        function Hb(e, t) { return null != t && "trapezoids" in e.props }

        function Lb(e, t) { return null != t && "sectors" in e.props }

        function Ib(e, t) { return null != t && "points" in e.props }

        function jb(e, t) { var n, r, a = e.x === (null === t || void 0 === t || null === (n = t.labelViewBox) || void 0 === n ? void 0 : n.x) || e.x === t.x,
                o = e.y === (null === t || void 0 === t || null === (r = t.labelViewBox) || void 0 === r ? void 0 : r.y) || e.y === t.y; return a && o }

        function Vb(e, t) { var n = e.endAngle === t.endAngle,
                r = e.startAngle === t.startAngle; return n && r }

        function Ob(e, t) { var n = e.x === t.x,
                r = e.y === t.y,
                a = e.z === t.z; return n && r && a }

        function Rb(e) { var t = e.activeTooltipItem,
                n = e.graphicalItem,
                r = e.itemData,
                a = function(e, t) { var n; return Hb(e, t) ? n = "trapezoids" : Lb(e, t) ? n = "sectors" : Ib(e, t) && (n = "points"), n }(n, t),
                o = function(e, t) { var n, r; return Hb(e, t) ? null === (n = t.tooltipPayload) || void 0 === n || null === (n = n[0]) || void 0 === n || null === (n = n.payload) || void 0 === n ? void 0 : n.payload : Lb(e, t) ? null === (r = t.tooltipPayload) || void 0 === r || null === (r = r[0]) || void 0 === r || null === (r = r.payload) || void 0 === r ? void 0 : r.payload : Ib(e, t) ? t.payload : {} }(n, t),
                i = r.filter((function(e, r) { var i = bv()(o, e),
                        l = n.props[a].filter((function(e) { var r = function(e, t) { var n; return Hb(e, t) ? n = jb : Lb(e, t) ? n = Vb : Ib(e, t) && (n = Ob), n }(n, t); return r(e, t) })),
                        s = n.props[a].indexOf(l[l.length - 1]); return i && r === s })); return r.indexOf(i[i.length - 1]) } var Pb = ["x", "y"];

        function Db(e) { return Db = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Db(e) }

        function Fb() { return Fb = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Fb.apply(this, arguments) }

        function Nb(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function _b(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Nb(Object(n), !0).forEach((function(t) { Bb(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Nb(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Bb(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Db(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Db(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Db(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Wb(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function Ub(e, t) { var n = e.x,
                r = e.y,
                a = Wb(e, Pb),
                o = "".concat(n),
                i = parseInt(o, 10),
                l = "".concat(r),
                s = parseInt(l, 10),
                c = "".concat(t.height || a.height),
                d = parseInt(c, 10),
                u = "".concat(t.width || a.width),
                h = parseInt(u, 10); return _b(_b(_b(_b(_b({}, t), a), i ? { x: i } : {}), s ? { y: s } : {}), {}, { height: d, width: h, name: t.name, radius: t.radius }) }

        function qb(e) { return a.createElement(Tb, Fb({ shapeType: "rectangle", propTransformer: Ub, activeClassName: "recharts-active-bar" }, e)) } var Gb, Kb = ["value", "background"];

        function Zb(e) { return Zb = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Zb(e) }

        function Yb(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function Xb() { return Xb = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Xb.apply(this, arguments) }

        function $b(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Qb(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? $b(Object(n), !0).forEach((function(t) { ow(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : $b(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Jb(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, iw(r.key), r) } }

        function ew(e, t, n) { return t = nw(t),
                function(e, t) { if (t && ("object" === Zb(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return rw(e) }(e, tw() ? Reflect.construct(t, n || [], nw(e).constructor) : t.apply(e, n)) }

        function tw() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (tw = function() { return !!e })() }

        function nw(e) { return nw = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, nw(e) }

        function rw(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }

        function aw(e, t) { return aw = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, aw(e, t) }

        function ow(e, t, n) { return (t = iw(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function iw(e) { var t = function(e, t) { if ("object" != Zb(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Zb(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Zb(t) ? t : String(t) } var lw = function(e) {
            function t() { var e;! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t); for (var n = arguments.length, r = new Array(n), a = 0; a < n; a++) r[a] = arguments[a]; return ow(rw(e = ew(this, t, [].concat(r))), "state", { isAnimationFinished: !1 }), ow(rw(e), "id", ja("recharts-bar-")), ow(rw(e), "handleAnimationEnd", (function() { var t = e.props.onAnimationEnd;
                    e.setState({ isAnimationFinished: !0 }), t && t() })), ow(rw(e), "handleAnimationStart", (function() { var t = e.props.onAnimationStart;
                    e.setState({ isAnimationFinished: !1 }), t && t() })), e } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && aw(e, t) }(t, e), n = t, r = [{ key: "renderRectanglesStatically", value: function(e) { var t = this,
                        n = this.props,
                        r = n.shape,
                        o = n.dataKey,
                        i = n.activeIndex,
                        l = n.activeBar,
                        s = po(this.props, !1); return e && e.map((function(e, n) { var c = n === i,
                            d = c ? l : r,
                            u = Qb(Qb(Qb({}, s), e), {}, { isActive: c, option: d, index: n, dataKey: o, onAnimationStart: t.handleAnimationStart, onAnimationEnd: t.handleAnimationEnd }); return a.createElement(Po, Xb({ className: "recharts-bar-rectangle" }, Qa(t.props, e, n), { key: "rectangle-".concat(null === e || void 0 === e ? void 0 : e.x, "-").concat(null === e || void 0 === e ? void 0 : e.y, "-").concat(null === e || void 0 === e ? void 0 : e.value) }), a.createElement(qb, u)) })) } }, { key: "renderRectanglesWithAnimation", value: function() { var e = this,
                        t = this.props,
                        n = t.data,
                        r = t.layout,
                        o = t.isAnimationActive,
                        i = t.animationBegin,
                        l = t.animationDuration,
                        s = t.animationEasing,
                        c = t.animationId,
                        d = this.state.prevData; return a.createElement(rd, { begin: i, duration: l, isActive: o, easing: s, from: { t: 0 }, to: { t: 1 }, key: "bar-".concat(c), onAnimationEnd: this.handleAnimationEnd, onAnimationStart: this.handleAnimationStart }, (function(t) { var o = t.t,
                            i = n.map((function(e, t) { var n = d && d[t]; if (n) { var a = Ra(n.x, e.x),
                                        i = Ra(n.y, e.y),
                                        l = Ra(n.width, e.width),
                                        s = Ra(n.height, e.height); return Qb(Qb({}, e), {}, { x: a(o), y: i(o), width: l(o), height: s(o) }) } if ("horizontal" === r) { var c = Ra(0, e.height)(o); return Qb(Qb({}, e), {}, { y: e.y + e.height - c, height: c }) } var u = Ra(0, e.width)(o); return Qb(Qb({}, e), {}, { width: u }) })); return a.createElement(Po, null, e.renderRectanglesStatically(i)) })) } }, { key: "renderRectangles", value: function() { var e = this.props,
                        t = e.data,
                        n = e.isAnimationActive,
                        r = this.state.prevData; return !(n && t && t.length) || r && bv()(r, t) ? this.renderRectanglesStatically(t) : this.renderRectanglesWithAnimation() } }, { key: "renderBackground", value: function() { var e = this,
                        t = this.props,
                        n = t.data,
                        r = t.dataKey,
                        o = t.activeIndex,
                        i = po(this.props.background, !1); return n.map((function(t, n) { t.value; var l = t.background,
                            s = Yb(t, Kb); if (!l) return null; var c = Qb(Qb(Qb(Qb(Qb({}, s), {}, { fill: "#eee" }, l), i), Qa(e.props, t, n)), {}, { onAnimationStart: e.handleAnimationStart, onAnimationEnd: e.handleAnimationEnd, dataKey: r, index: n, key: "background-bar-".concat(n), className: "recharts-bar-background-rectangle" }); return a.createElement(qb, Xb({ option: e.props.background, isActive: n === o }, c)) })) } }, { key: "renderErrorBar", value: function(e, t) { if (this.props.isAnimationActive && !this.state.isAnimationFinished) return null; var n = this.props,
                        r = n.data,
                        o = n.xAxis,
                        i = n.yAxis,
                        l = n.layout,
                        s = so(n.children, eg); if (!s) return null; var c = "vertical" === l ? r[0].height / 2 : r[0].width / 2,
                        d = function(e, t) { var n = Array.isArray(e.value) ? e.value[1] : e.value; return { x: e.x, y: e.y, value: n, errorVal: hg(e, t) } },
                        u = { clipPath: e ? "url(#clipPath-".concat(t, ")") : null }; return a.createElement(Po, u, s.map((function(e) { return a.cloneElement(e, { key: "error-bar-".concat(t, "-").concat(e.props.dataKey), data: r, xAxis: o, yAxis: i, layout: l, offset: c, dataPointFormatter: d }) }))) } }, { key: "render", value: function() { var e = this.props,
                        t = e.hide,
                        n = e.data,
                        r = e.className,
                        o = e.xAxis,
                        i = e.yAxis,
                        l = e.left,
                        s = e.top,
                        c = e.width,
                        d = e.height,
                        u = e.isAnimationActive,
                        h = e.background,
                        m = e.id; if (t || !n || !n.length) return null; var p = this.state.isAnimationFinished,
                        f = va("recharts-bar", r),
                        v = o && o.allowDataOverflow,
                        g = i && i.allowDataOverflow,
                        y = v || g,
                        b = Na()(m) ? this.id : m; return a.createElement(Po, { className: f }, v || g ? a.createElement("defs", null, a.createElement("clipPath", { id: "clipPath-".concat(b) }, a.createElement("rect", { x: v ? l : l - c / 2, y: g ? s : s - d / 2, width: v ? c : 2 * c, height: g ? d : 2 * d }))) : null, a.createElement(Po, { className: "recharts-bar-rectangles", clipPath: y ? "url(#clipPath-".concat(b, ")") : null }, h ? this.renderBackground() : null, this.renderRectangles()), this.renderErrorBar(y, b), (!u || p) && $y.renderCallByParent(this.props, n)) } }], o = [{ key: "getDerivedStateFromProps", value: function(e, t) { return e.animationId !== t.prevAnimationId ? { prevAnimationId: e.animationId, curData: e.data, prevData: t.curData } : e.data !== t.curData ? { curData: e.data } : null } }], r && Jb(n.prototype, r), o && Jb(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);

        function sw(e) { return sw = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, sw(e) }

        function cw(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, mw(r.key), r) } }

        function dw(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function uw(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? dw(Object(n), !0).forEach((function(t) { hw(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : dw(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function hw(e, t, n) { return (t = mw(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function mw(e) { var t = function(e, t) { if ("object" != sw(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != sw(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == sw(t) ? t : String(t) } Gb = lw, ow(lw, "displayName", "Bar"), ow(lw, "defaultProps", { xAxisId: 0, yAxisId: 0, legendType: "rect", minPointSize: 0, hide: !1, data: [], layout: "vertical", activeBar: !1, isAnimationActive: !ui.isSsr, animationBegin: 0, animationDuration: 400, animationEasing: "ease" }), ow(lw, "getComposedData", (function(e) { var t = e.props,
                n = e.item,
                r = e.barPosition,
                a = e.bandSize,
                o = e.xAxis,
                i = e.yAxis,
                l = e.xAxisTicks,
                s = e.yAxisTicks,
                c = e.stackedData,
                d = e.dataStartIndex,
                u = e.displayedData,
                h = e.offset,
                m = function(e, t) { if (!e) return null; for (var n = 0, r = e.length; n < r; n++)
                        if (e[n].item === t) return e[n].position; return null }(r, n); if (!m) return null; var p = t.layout,
                f = n.props,
                v = f.dataKey,
                g = f.children,
                y = f.minPointSize,
                b = "horizontal" === p ? i : o,
                w = c ? b.scale.domain() : null,
                z = function(e) { var t = e.numericAxis,
                        n = t.scale.domain(); if ("number" === t.type) { var r = Math.min(n[0], n[1]),
                            a = Math.max(n[0], n[1]); return r <= 0 && a >= 0 ? 0 : a < 0 ? a : r } return n[0] }({ numericAxis: b }),
                x = so(g, Py),
                A = u.map((function(e, t) { var r, u, h, f, g, b;
                    c ? r = function(e, t) { if (!t || 2 !== t.length || !Ha(t[0]) || !Ha(t[1])) return e; var n = Math.min(t[0], t[1]),
                            r = Math.max(t[0], t[1]),
                            a = [e[0], e[1]]; return (!Ha(e[0]) || e[0] < n) && (a[0] = n), (!Ha(e[1]) || e[1] > r) && (a[1] = r), a[0] > r && (a[0] = r), a[1] < n && (a[1] = n), a }(c[d + t], w) : (r = hg(e, v), Array.isArray(r) || (r = [z, r])); var A = function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0; return function(n, r) { if ("number" === typeof e) return e; var a = "number" === typeof n; return a ? e(n, r) : (a || (0, To.A)(!1), t) } }(y, Gb.defaultProps.minPointSize)(r[1], t); if ("horizontal" === p) { var k, S = [i.scale(r[0]), i.scale(r[1])],
                            M = S[0],
                            E = S[1];
                        u = Tg({ axis: o, ticks: l, bandSize: a, offset: m.offset, entry: e, index: t }), h = null !== (k = null !== E && void 0 !== E ? E : M) && void 0 !== k ? k : void 0, f = m.size; var C = M - E; if (g = Number.isNaN(C) ? 0 : C, b = { x: u, y: i.y, width: f, height: i.height }, Math.abs(A) > 0 && Math.abs(g) < Math.abs(A)) { var T = Ca(g || A) * (Math.abs(A) - Math.abs(g));
                            h -= T, g += T } } else { var H = [o.scale(r[0]), o.scale(r[1])],
                            L = H[0],
                            I = H[1]; if (u = L, h = Tg({ axis: i, ticks: s, bandSize: a, offset: m.offset, entry: e, index: t }), f = I - L, g = m.size, b = { x: o.x, y: h, width: o.width, height: g }, Math.abs(A) > 0 && Math.abs(f) < Math.abs(A)) f += Ca(f || A) * (Math.abs(A) - Math.abs(f)) } return Qb(Qb(Qb({}, e), {}, { x: u, y: h, width: f, height: g, value: c ? r : r[1], payload: e, background: b }, x && x[t] && x[t].props), {}, { tooltipPayload: [Rg(n, e)], tooltipPosition: { x: u + f / 2, y: h + g / 2 } }) })); return Qb({ data: A, layout: p }, h) })); var pw = function(e, t, n, r, a) { var o = e.width,
                    i = e.height,
                    l = e.layout,
                    s = e.children,
                    c = Object.keys(t),
                    d = { left: n.left, leftMirror: n.left, right: o - n.right, rightMirror: o - n.right, top: n.top, topMirror: n.top, bottom: i - n.bottom, bottomMirror: i - n.bottom },
                    u = !!co(s, lw); return c.reduce((function(o, i) { var s, c, h, m, p, f = t[i],
                        v = f.orientation,
                        g = f.domain,
                        y = f.padding,
                        b = void 0 === y ? {} : y,
                        w = f.mirror,
                        z = f.reversed,
                        x = "".concat(v).concat(w ? "Mirror" : ""); if ("number" === f.type && ("gap" === f.padding || "no-gap" === f.padding)) { var A = g[1] - g[0],
                            k = 1 / 0,
                            S = f.categoricalDomain.sort(); if (S.forEach((function(e, t) { t > 0 && (k = Math.min((e || 0) - (S[t - 1] || 0), k)) })), Number.isFinite(k)) { var M = k / A,
                                E = "vertical" === f.layout ? n.height : n.width; if ("gap" === f.padding && (s = M * E / 2), "no-gap" === f.padding) { var C = Va(e.barCategoryGap, M * E),
                                    T = M * E / 2;
                                s = T - C - (T - C) / E * C } } } c = "xAxis" === r ? [n.left + (b.left || 0) + (s || 0), n.left + n.width - (b.right || 0) - (s || 0)] : "yAxis" === r ? "horizontal" === l ? [n.top + n.height - (b.bottom || 0), n.top + (b.top || 0)] : [n.top + (b.top || 0) + (s || 0), n.top + n.height - (b.bottom || 0) - (s || 0)] : f.range, z && (c = [c[1], c[0]]); var H = xg(f, a, u),
                        L = H.scale,
                        I = H.realScaleType;
                    L.domain(g).range(c), kg(L); var j = Eg(L, uw(uw({}, f), {}, { realScaleType: I })); "xAxis" === r ? (p = "top" === v && !w || "bottom" === v && w, h = n.left, m = d[x] - p * f.height) : "yAxis" === r && (p = "left" === v && !w || "right" === v && w, h = d[x] - p * f.width, m = n.top); var V = uw(uw(uw({}, f), j), {}, { realScaleType: I, x: h, y: m, scale: L, width: "xAxis" === r ? n.width : f.width, height: "yAxis" === r ? n.height : f.height }); return V.bandSize = Vg(V, j), f.hide || "xAxis" !== r ? f.hide || (d[x] += (p ? -1 : 1) * V.width) : d[x] += (p ? -1 : 1) * V.height, uw(uw({}, o), {}, hw({}, i, V)) }), {}) },
            fw = function(e, t) { var n = e.x,
                    r = e.y,
                    a = t.x,
                    o = t.y; return { x: Math.min(n, a), y: Math.min(r, o), width: Math.abs(a - n), height: Math.abs(o - r) } },
            vw = function() {
                function e(t) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.scale = t } var t, n, r; return t = e, n = [{ key: "domain", get: function() { return this.scale.domain } }, { key: "range", get: function() { return this.scale.range } }, { key: "rangeMin", get: function() { return this.range()[0] } }, { key: "rangeMax", get: function() { return this.range()[1] } }, { key: "bandwidth", get: function() { return this.scale.bandwidth } }, { key: "apply", value: function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                            n = t.bandAware,
                            r = t.position; if (void 0 !== e) { if (r) switch (r) {
                                case "start":
                                default:
                                    return this.scale(e);
                                case "middle":
                                    var a = this.bandwidth ? this.bandwidth() / 2 : 0; return this.scale(e) + a;
                                case "end":
                                    var o = this.bandwidth ? this.bandwidth() : 0; return this.scale(e) + o }
                            if (n) { var i = this.bandwidth ? this.bandwidth() / 2 : 0; return this.scale(e) + i } return this.scale(e) } } }, { key: "isInRange", value: function(e) { var t = this.range(),
                            n = t[0],
                            r = t[t.length - 1]; return n <= r ? e >= n && e <= r : e >= r && e <= n } }], r = [{ key: "create", value: function(t) { return new e(t) } }], n && cw(t.prototype, n), r && cw(t, r), Object.defineProperty(t, "prototype", { writable: !1 }), e }();
        hw(vw, "EPS", 1e-4); var gw = function(e) { var t = Object.keys(e).reduce((function(t, n) { return uw(uw({}, t), {}, hw({}, n, vw.create(e[n]))) }), {}); return uw(uw({}, t), {}, { apply: function(e) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                        r = n.bandAware,
                        a = n.position; return Vy()(e, (function(e, n) { return t[n].apply(e, { bandAware: r, position: a }) })) }, isInRange: function(e) { return Ry()(e, (function(e, n) { return t[n].isInRange(e) })) } }) }; var yw = function(e) { var t = e.width,
                n = e.height,
                r = function(e) { return (e % 180 + 180) % 180 }(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0),
                a = r * Math.PI / 180,
                o = Math.atan(n / t),
                i = a > o && a < Math.PI - o ? n / Math.sin(a) : t / Math.cos(a); return Math.abs(i) };

        function bw(e) { return bw = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, bw(e) }

        function ww() { return ww = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, ww.apply(this, arguments) }

        function zw(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function xw(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? zw(Object(n), !0).forEach((function(t) { Aw(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : zw(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Aw(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != bw(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != bw(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == bw(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function kw(e) { var t = e.x,
                n = e.y,
                r = e.r,
                o = e.alwaysShow,
                i = e.clipPathId,
                l = La(t),
                s = La(n); if (Da(void 0 === o, 'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'), !l || !s) return null; var c = function(e) { var t = e.x,
                    n = e.y,
                    r = e.xAxis,
                    a = e.yAxis,
                    o = gw({ x: r.scale, y: a.scale }),
                    i = o.apply({ x: t, y: n }, { bandAware: !0 }); return Iy(e, "discard") && !o.isInRange(i) ? null : i }(e); if (!c) return null; var d = c.x,
                u = c.y,
                h = e.shape,
                m = e.className,
                p = xw(xw({ clipPath: Iy(e, "hidden") ? "url(#".concat(i, ")") : void 0 }, po(e, !0)), {}, { cx: d, cy: u }); return a.createElement(Po, { className: va("recharts-reference-dot", m) }, kw.renderDot(h, p), Hy.renderCallByParent(e, { x: d - r, y: u - r, width: 2 * r, height: 2 * r })) } kw.displayName = "ReferenceDot", kw.defaultProps = { isFront: !1, ifOverflow: "discard", xAxisId: 0, yAxisId: 0, r: 10, fill: "#fff", stroke: "#ccc", fillOpacity: 1, strokeWidth: 1 }, kw.renderDot = function(e, t) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement(rs, ww({}, t, { cx: t.cx, cy: t.cy, className: "recharts-reference-dot-dot" })) }; var Sw = n(24597),
            Mw = n.n(Sw),
            Ew = n(98990),
            Cw = n.n(Ew),
            Tw = n(15797),
            Hw = n.n(Tw)()((function(e) { return { x: e.left, y: e.top, width: e.width, height: e.height } }), (function(e) { return ["l", e.left, "t", e.top, "w", e.width, "h", e.height].join("") })); var Lw = (0, a.createContext)(void 0),
            Iw = (0, a.createContext)(void 0),
            jw = (0, a.createContext)(void 0),
            Vw = (0, a.createContext)({}),
            Ow = (0, a.createContext)(void 0),
            Rw = (0, a.createContext)(0),
            Pw = (0, a.createContext)(0),
            Dw = function(e) { var t = e.state,
                    n = t.xAxisMap,
                    r = t.yAxisMap,
                    o = t.offset,
                    i = e.clipPathId,
                    l = e.children,
                    s = e.width,
                    c = e.height,
                    d = Hw(o); return a.createElement(Lw.Provider, { value: n }, a.createElement(Iw.Provider, { value: r }, a.createElement(Vw.Provider, { value: o }, a.createElement(jw.Provider, { value: d }, a.createElement(Ow.Provider, { value: i }, a.createElement(Rw.Provider, { value: c }, a.createElement(Pw.Provider, { value: s }, l))))))) }; var Fw = function(e) { var t = (0, a.useContext)(Lw);
                null == t && (0, To.A)(!1); var n = t[e]; return null == n && (0, To.A)(!1), n },
            Nw = function(e) { var t = (0, a.useContext)(Iw);
                null == t && (0, To.A)(!1); var n = t[e]; return null == n && (0, To.A)(!1), n },
            _w = function() { return (0, a.useContext)(Pw) },
            Bw = function() { return (0, a.useContext)(Rw) };

        function Ww(e) { return Ww = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Ww(e) }

        function Uw(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function qw(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Uw(Object(n), !0).forEach((function(t) { Gw(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Uw(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Gw(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Ww(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Ww(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Ww(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Kw(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return Zw(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Zw(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Zw(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function Yw() { return Yw = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Yw.apply(this, arguments) }

        function Xw(e) { var t = e.x,
                n = e.y,
                r = e.segment,
                o = e.xAxisId,
                i = e.yAxisId,
                l = e.shape,
                s = e.className,
                c = e.alwaysShow,
                d = (0, a.useContext)(Ow),
                u = Fw(o),
                h = Nw(i),
                m = (0, a.useContext)(jw); if (!d || !m) return null;
            Da(void 0 === c, 'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'); var p = function(e, t, n, r, a, o, i, l, s) { var c = a.x,
                    d = a.y,
                    u = a.width,
                    h = a.height; if (n) { var m = s.y,
                        p = e.y.apply(m, { position: o }); if (Iy(s, "discard") && !e.y.isInRange(p)) return null; var f = [{ x: c + u, y: p }, { x: c, y: p }]; return "left" === l ? f.reverse() : f } if (t) { var v = s.x,
                        g = e.x.apply(v, { position: o }); if (Iy(s, "discard") && !e.x.isInRange(g)) return null; var y = [{ x: g, y: d + h }, { x: g, y: d }]; return "top" === i ? y.reverse() : y } if (r) { var b = s.segment.map((function(t) { return e.apply(t, { position: o }) })); return Iy(s, "discard") && Mw()(b, (function(t) { return !e.isInRange(t) })) ? null : b } return null }(gw({ x: u.scale, y: h.scale }), La(t), La(n), r && 2 === r.length, m, e.position, u.orientation, h.orientation, e); if (!p) return null; var f = Kw(p, 2),
                v = f[0],
                g = v.x,
                y = v.y,
                b = f[1],
                w = b.x,
                z = b.y,
                x = qw(qw({ clipPath: Iy(e, "hidden") ? "url(#".concat(d, ")") : void 0 }, po(e, !0)), {}, { x1: g, y1: y, x2: w, y2: z }); return a.createElement(Po, { className: va("recharts-reference-line", s) }, function(e, t) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement("line", Yw({}, t, { className: "recharts-reference-line-line" })) }(l, x), Hy.renderCallByParent(e, function(e) { var t = e.x1,
                    n = e.y1,
                    r = e.x2,
                    a = e.y2; return fw({ x: t, y: n }, { x: r, y: a }) }({ x1: g, y1: y, x2: w, y2: z }))) }

        function $w(e) { return $w = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, $w(e) }

        function Qw() { return Qw = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Qw.apply(this, arguments) }

        function Jw(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function ez(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Jw(Object(n), !0).forEach((function(t) { tz(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Jw(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function tz(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != $w(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != $w(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == $w(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } Xw.displayName = "ReferenceLine", Xw.defaultProps = { isFront: !1, ifOverflow: "discard", xAxisId: 0, yAxisId: 0, fill: "none", stroke: "#ccc", fillOpacity: 1, strokeWidth: 1, position: "middle" };

        function nz(e) { var t = e.x1,
                n = e.x2,
                r = e.y1,
                o = e.y2,
                i = e.className,
                l = e.alwaysShow,
                s = e.clipPathId;
            Da(void 0 === l, 'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'); var c = La(t),
                d = La(n),
                u = La(r),
                h = La(o),
                m = e.shape; if (!c && !d && !u && !h && !m) return null; var p = function(e, t, n, r, a) { var o = a.x1,
                    i = a.x2,
                    l = a.y1,
                    s = a.y2,
                    c = a.xAxis,
                    d = a.yAxis; if (!c || !d) return null; var u = gw({ x: c.scale, y: d.scale }),
                    h = { x: e ? u.x.apply(o, { position: "start" }) : u.x.rangeMin, y: n ? u.y.apply(l, { position: "start" }) : u.y.rangeMin },
                    m = { x: t ? u.x.apply(i, { position: "end" }) : u.x.rangeMax, y: r ? u.y.apply(s, { position: "end" }) : u.y.rangeMax }; return !Iy(a, "discard") || u.isInRange(h) && u.isInRange(m) ? fw(h, m) : null }(c, d, u, h, e); if (!p && !m) return null; var f = Iy(e, "hidden") ? "url(#".concat(s, ")") : void 0; return a.createElement(Po, { className: va("recharts-reference-area", i) }, nz.renderRect(m, ez(ez({ clipPath: f }, po(e, !0)), p)), Hy.renderCallByParent(e, p)) }

        function rz(e) { return function(e) { if (Array.isArray(e)) return az(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return az(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return az(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function az(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } nz.displayName = "ReferenceArea", nz.defaultProps = { isFront: !1, ifOverflow: "discard", xAxisId: 0, yAxisId: 0, r: 10, fill: "#ccc", fillOpacity: .5, stroke: "none", strokeWidth: 1 }, nz.renderRect = function(e, t) { return a.isValidElement(e) ? a.cloneElement(e, t) : Ba()(e) ? e(t) : a.createElement(pd, Qw({}, t, { className: "recharts-reference-area-rect" })) }; var oz = function(e, t, n, r, a) { var o = so(e, Xw),
                    i = so(e, kw),
                    l = [].concat(rz(o), rz(i)),
                    s = so(e, nz),
                    c = "".concat(r, "Id"),
                    d = r[0],
                    u = t; if (l.length && (u = l.reduce((function(e, t) { if (t.props[c] === n && Iy(t.props, "extendDomain") && Ha(t.props[d])) { var r = t.props[d]; return [Math.min(e[0], r), Math.max(e[1], r)] } return e }), u)), s.length) { var h = "".concat(d, "1"),
                        m = "".concat(d, "2");
                    u = s.reduce((function(e, t) { if (t.props[c] === n && Iy(t.props, "extendDomain") && Ha(t.props[h]) && Ha(t.props[m])) { var r = t.props[h],
                                a = t.props[m]; return [Math.min(e[0], r, a), Math.max(e[1], r, a)] } return e }), u) } return a && a.length && (u = a.reduce((function(e, t) { return Ha(t) ? [Math.min(e[0], t), Math.max(e[1], t)] : e }), u)), u },
            iz = n(17283),
            lz = new(n.n(iz)()),
            sz = "recharts.syncMouseEvents";

        function cz(e) { return cz = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, cz(e) }

        function dz(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, hz(r.key), r) } }

        function uz(e, t, n) { return (t = hz(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function hz(e) { var t = function(e, t) { if ("object" != cz(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != cz(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == cz(t) ? t : String(t) } var mz = function() {
            function e() {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), uz(this, "activeIndex", 0), uz(this, "coordinateList", []), uz(this, "layout", "horizontal") } var t, n, r; return t = e, n = [{ key: "setDetails", value: function(e) { var t, n = e.coordinateList,
                        r = void 0 === n ? null : n,
                        a = e.container,
                        o = void 0 === a ? null : a,
                        i = e.layout,
                        l = void 0 === i ? null : i,
                        s = e.offset,
                        c = void 0 === s ? null : s,
                        d = e.mouseHandlerCallback,
                        u = void 0 === d ? null : d;
                    this.coordinateList = null !== (t = null !== r && void 0 !== r ? r : this.coordinateList) && void 0 !== t ? t : [], this.container = null !== o && void 0 !== o ? o : this.container, this.layout = null !== l && void 0 !== l ? l : this.layout, this.offset = null !== c && void 0 !== c ? c : this.offset, this.mouseHandlerCallback = null !== u && void 0 !== u ? u : this.mouseHandlerCallback, this.activeIndex = Math.min(Math.max(this.activeIndex, 0), this.coordinateList.length - 1) } }, { key: "focus", value: function() { this.spoofMouse() } }, { key: "keyboardEvent", value: function(e) { if (0 !== this.coordinateList.length) switch (e.key) {
                        case "ArrowRight":
                            if ("horizontal" !== this.layout) return;
                            this.activeIndex = Math.min(this.activeIndex + 1, this.coordinateList.length - 1), this.spoofMouse(); break;
                        case "ArrowLeft":
                            if ("horizontal" !== this.layout) return;
                            this.activeIndex = Math.max(this.activeIndex - 1, 0), this.spoofMouse() } } }, { key: "setIndex", value: function(e) { this.activeIndex = e } }, { key: "spoofMouse", value: function() { var e, t; if ("horizontal" === this.layout && 0 !== this.coordinateList.length) { var n = this.container.getBoundingClientRect(),
                            r = n.x,
                            a = n.y,
                            o = n.height,
                            i = this.coordinateList[this.activeIndex].coordinate,
                            l = (null === (e = window) || void 0 === e ? void 0 : e.scrollX) || 0,
                            s = (null === (t = window) || void 0 === t ? void 0 : t.scrollY) || 0,
                            c = r + i + l,
                            d = a + this.offset.top + o / 2 + s;
                        this.mouseHandlerCallback({ pageX: c, pageY: d }) } } }], n && dz(t.prototype, n), r && dz(t, r), Object.defineProperty(t, "prototype", { writable: !1 }), e }();

        function pz() {}

        function fz(e, t, n) { e._context.bezierCurveTo((2 * e._x0 + e._x1) / 3, (2 * e._y0 + e._y1) / 3, (e._x0 + 2 * e._x1) / 3, (e._y0 + 2 * e._y1) / 3, (e._x0 + 4 * e._x1 + t) / 6, (e._y0 + 4 * e._y1 + n) / 6) }

        function vz(e) { this._context = e }

        function gz(e) { this._context = e }

        function yz(e) { this._context = e } vz.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._x0 = this._x1 = this._y0 = this._y1 = NaN, this._point = 0 }, lineEnd: function() { switch (this._point) {
                    case 3:
                        fz(this, this._x1, this._y1);
                    case 2:
                        this._context.lineTo(this._x1, this._y1) }(this._line || 0 !== this._line && 1 === this._point) && this._context.closePath(), this._line = 1 - this._line }, point: function(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1, this._line ? this._context.lineTo(e, t) : this._context.moveTo(e, t); break;
                    case 1:
                        this._point = 2; break;
                    case 2:
                        this._point = 3, this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);
                    default:
                        fz(this, e, t) } this._x0 = this._x1, this._x1 = e, this._y0 = this._y1, this._y1 = t } }, gz.prototype = { areaStart: pz, areaEnd: pz, lineStart: function() { this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN, this._point = 0 }, lineEnd: function() { switch (this._point) {
                    case 1:
                        this._context.moveTo(this._x2, this._y2), this._context.closePath(); break;
                    case 2:
                        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3), this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3), this._context.closePath(); break;
                    case 3:
                        this.point(this._x2, this._y2), this.point(this._x3, this._y3), this.point(this._x4, this._y4) } }, point: function(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1, this._x2 = e, this._y2 = t; break;
                    case 1:
                        this._point = 2, this._x3 = e, this._y3 = t; break;
                    case 2:
                        this._point = 3, this._x4 = e, this._y4 = t, this._context.moveTo((this._x0 + 4 * this._x1 + e) / 6, (this._y0 + 4 * this._y1 + t) / 6); break;
                    default:
                        fz(this, e, t) } this._x0 = this._x1, this._x1 = e, this._y0 = this._y1, this._y1 = t } }, yz.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._x0 = this._x1 = this._y0 = this._y1 = NaN, this._point = 0 }, lineEnd: function() {
                (this._line || 0 !== this._line && 3 === this._point) && this._context.closePath(), this._line = 1 - this._line }, point: function(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1; break;
                    case 1:
                        this._point = 2; break;
                    case 2:
                        this._point = 3; var n = (this._x0 + 4 * this._x1 + e) / 6,
                            r = (this._y0 + 4 * this._y1 + t) / 6;
                        this._line ? this._context.lineTo(n, r) : this._context.moveTo(n, r); break;
                    case 3:
                        this._point = 4;
                    default:
                        fz(this, e, t) } this._x0 = this._x1, this._x1 = e, this._y0 = this._y1, this._y1 = t } };
        class bz { constructor(e, t) { this._context = e, this._x = t } areaStart() { this._line = 0 } areaEnd() { this._line = NaN } lineStart() { this._point = 0 } lineEnd() {
                (this._line || 0 !== this._line && 1 === this._point) && this._context.closePath(), this._line = 1 - this._line } point(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1, this._line ? this._context.lineTo(e, t) : this._context.moveTo(e, t); break;
                    case 1:
                        this._point = 2;
                    default:
                        this._x ? this._context.bezierCurveTo(this._x0 = (this._x0 + e) / 2, this._y0, this._x0, t, e, t) : this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + t) / 2, e, this._y0, e, t) } this._x0 = e, this._y0 = t } }

        function wz(e) { this._context = e }

        function zz(e) { this._context = e }

        function xz(e) { return new zz(e) }

        function Az(e) { return e < 0 ? -1 : 1 }

        function kz(e, t, n) { var r = e._x1 - e._x0,
                a = t - e._x1,
                o = (e._y1 - e._y0) / (r || a < 0 && -0),
                i = (n - e._y1) / (a || r < 0 && -0),
                l = (o * a + i * r) / (r + a); return (Az(o) + Az(i)) * Math.min(Math.abs(o), Math.abs(i), .5 * Math.abs(l)) || 0 }

        function Sz(e, t) { var n = e._x1 - e._x0; return n ? (3 * (e._y1 - e._y0) / n - t) / 2 : t }

        function Mz(e, t, n) { var r = e._x0,
                a = e._y0,
                o = e._x1,
                i = e._y1,
                l = (o - r) / 3;
            e._context.bezierCurveTo(r + l, a + l * t, o - l, i - l * n, o, i) }

        function Ez(e) { this._context = e }

        function Cz(e) { this._context = new Tz(e) }

        function Tz(e) { this._context = e }

        function Hz(e) { this._context = e }

        function Lz(e) { var t, n, r = e.length - 1,
                a = new Array(r),
                o = new Array(r),
                i = new Array(r); for (a[0] = 0, o[0] = 2, i[0] = e[0] + 2 * e[1], t = 1; t < r - 1; ++t) a[t] = 1, o[t] = 4, i[t] = 4 * e[t] + 2 * e[t + 1]; for (a[r - 1] = 2, o[r - 1] = 7, i[r - 1] = 8 * e[r - 1] + e[r], t = 1; t < r; ++t) n = a[t] / o[t - 1], o[t] -= n, i[t] -= n * i[t - 1]; for (a[r - 1] = i[r - 1] / o[r - 1], t = r - 2; t >= 0; --t) a[t] = (i[t] - a[t + 1]) / o[t]; for (o[r - 1] = (e[r] + a[r - 1]) / 2, t = 0; t < r - 1; ++t) o[t] = 2 * e[t + 1] - a[t + 1]; return [a, o] }

        function Iz(e, t) { this._context = e, this._t = t }

        function jz(e) { return e[0] }

        function Vz(e) { return e[1] }

        function Oz(e, t) { var n = $i(!0),
                r = null,
                a = xz,
                o = null,
                i = yl(l);

            function l(l) { var s, c, d, u = (l = sv(l)).length,
                    h = !1; for (null == r && (o = a(d = i())), s = 0; s <= u; ++s) !(s < u && n(c = l[s], s, l)) === h && ((h = !h) ? o.lineStart() : o.lineEnd()), h && o.point(+e(c, s, l), +t(c, s, l)); if (d) return o = null, d + "" || null } return e = "function" === typeof e ? e : void 0 === e ? jz : $i(e), t = "function" === typeof t ? t : void 0 === t ? Vz : $i(t), l.x = function(t) { return arguments.length ? (e = "function" === typeof t ? t : $i(+t), l) : e }, l.y = function(e) { return arguments.length ? (t = "function" === typeof e ? e : $i(+e), l) : t }, l.defined = function(e) { return arguments.length ? (n = "function" === typeof e ? e : $i(!!e), l) : n }, l.curve = function(e) { return arguments.length ? (a = e, null != r && (o = a(r)), l) : a }, l.context = function(e) { return arguments.length ? (null == e ? r = o = null : o = a(r = e), l) : r }, l }

        function Rz(e, t, n) { var r = null,
                a = $i(!0),
                o = null,
                i = xz,
                l = null,
                s = yl(c);

            function c(c) { var d, u, h, m, p, f = (c = sv(c)).length,
                    v = !1,
                    g = new Array(f),
                    y = new Array(f); for (null == o && (l = i(p = s())), d = 0; d <= f; ++d) { if (!(d < f && a(m = c[d], d, c)) === v)
                        if (v = !v) u = d, l.areaStart(), l.lineStart();
                        else { for (l.lineEnd(), l.lineStart(), h = d - 1; h >= u; --h) l.point(g[h], y[h]);
                            l.lineEnd(), l.areaEnd() } v && (g[d] = +e(m, d, c), y[d] = +t(m, d, c), l.point(r ? +r(m, d, c) : g[d], n ? +n(m, d, c) : y[d])) } if (p) return l = null, p + "" || null }

            function d() { return Oz().defined(a).curve(i).context(o) } return e = "function" === typeof e ? e : void 0 === e ? jz : $i(+e), t = "function" === typeof t ? t : $i(void 0 === t ? 0 : +t), n = "function" === typeof n ? n : void 0 === n ? Vz : $i(+n), c.x = function(t) { return arguments.length ? (e = "function" === typeof t ? t : $i(+t), r = null, c) : e }, c.x0 = function(t) { return arguments.length ? (e = "function" === typeof t ? t : $i(+t), c) : e }, c.x1 = function(e) { return arguments.length ? (r = null == e ? null : "function" === typeof e ? e : $i(+e), c) : r }, c.y = function(e) { return arguments.length ? (t = "function" === typeof e ? e : $i(+e), n = null, c) : t }, c.y0 = function(e) { return arguments.length ? (t = "function" === typeof e ? e : $i(+e), c) : t }, c.y1 = function(e) { return arguments.length ? (n = null == e ? null : "function" === typeof e ? e : $i(+e), c) : n }, c.lineX0 = c.lineY0 = function() { return d().x(e).y(t) }, c.lineY1 = function() { return d().x(e).y(n) }, c.lineX1 = function() { return d().x(r).y(t) }, c.defined = function(e) { return arguments.length ? (a = "function" === typeof e ? e : $i(!!e), c) : a }, c.curve = function(e) { return arguments.length ? (i = e, null != o && (l = i(o)), c) : i }, c.context = function(e) { return arguments.length ? (null == e ? o = l = null : l = i(o = e), c) : o }, c }

        function Pz(e) { return Pz = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Pz(e) }

        function Dz() { return Dz = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Dz.apply(this, arguments) }

        function Fz(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Nz(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Fz(Object(n), !0).forEach((function(t) { _z(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Fz(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function _z(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Pz(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Pz(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Pz(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } wz.prototype = { areaStart: pz, areaEnd: pz, lineStart: function() { this._point = 0 }, lineEnd: function() { this._point && this._context.closePath() }, point: function(e, t) { e = +e, t = +t, this._point ? this._context.lineTo(e, t) : (this._point = 1, this._context.moveTo(e, t)) } }, zz.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._point = 0 }, lineEnd: function() {
                (this._line || 0 !== this._line && 1 === this._point) && this._context.closePath(), this._line = 1 - this._line }, point: function(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1, this._line ? this._context.lineTo(e, t) : this._context.moveTo(e, t); break;
                    case 1:
                        this._point = 2;
                    default:
                        this._context.lineTo(e, t) } } }, Ez.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN, this._point = 0 }, lineEnd: function() { switch (this._point) {
                    case 2:
                        this._context.lineTo(this._x1, this._y1); break;
                    case 3:
                        Mz(this, this._t0, Sz(this, this._t0)) }(this._line || 0 !== this._line && 1 === this._point) && this._context.closePath(), this._line = 1 - this._line }, point: function(e, t) { var n = NaN; if (t = +t, (e = +e) !== this._x1 || t !== this._y1) { switch (this._point) {
                        case 0:
                            this._point = 1, this._line ? this._context.lineTo(e, t) : this._context.moveTo(e, t); break;
                        case 1:
                            this._point = 2; break;
                        case 2:
                            this._point = 3, Mz(this, Sz(this, n = kz(this, e, t)), n); break;
                        default:
                            Mz(this, this._t0, n = kz(this, e, t)) } this._x0 = this._x1, this._x1 = e, this._y0 = this._y1, this._y1 = t, this._t0 = n } } }, (Cz.prototype = Object.create(Ez.prototype)).point = function(e, t) { Ez.prototype.point.call(this, t, e) }, Tz.prototype = { moveTo: function(e, t) { this._context.moveTo(t, e) }, closePath: function() { this._context.closePath() }, lineTo: function(e, t) { this._context.lineTo(t, e) }, bezierCurveTo: function(e, t, n, r, a, o) { this._context.bezierCurveTo(t, e, r, n, o, a) } }, Hz.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._x = [], this._y = [] }, lineEnd: function() { var e = this._x,
                    t = this._y,
                    n = e.length; if (n)
                    if (this._line ? this._context.lineTo(e[0], t[0]) : this._context.moveTo(e[0], t[0]), 2 === n) this._context.lineTo(e[1], t[1]);
                    else
                        for (var r = Lz(e), a = Lz(t), o = 0, i = 1; i < n; ++o, ++i) this._context.bezierCurveTo(r[0][o], a[0][o], r[1][o], a[1][o], e[i], t[i]);
                (this._line || 0 !== this._line && 1 === n) && this._context.closePath(), this._line = 1 - this._line, this._x = this._y = null }, point: function(e, t) { this._x.push(+e), this._y.push(+t) } }, Iz.prototype = { areaStart: function() { this._line = 0 }, areaEnd: function() { this._line = NaN }, lineStart: function() { this._x = this._y = NaN, this._point = 0 }, lineEnd: function() { 0 < this._t && this._t < 1 && 2 === this._point && this._context.lineTo(this._x, this._y), (this._line || 0 !== this._line && 1 === this._point) && this._context.closePath(), this._line >= 0 && (this._t = 1 - this._t, this._line = 1 - this._line) }, point: function(e, t) { switch (e = +e, t = +t, this._point) {
                    case 0:
                        this._point = 1, this._line ? this._context.lineTo(e, t) : this._context.moveTo(e, t); break;
                    case 1:
                        this._point = 2;
                    default:
                        if (this._t <= 0) this._context.lineTo(this._x, t), this._context.lineTo(e, t);
                        else { var n = this._x * (1 - this._t) + e * this._t;
                            this._context.lineTo(n, this._y), this._context.lineTo(n, t) } } this._x = e, this._y = t } }; var Bz = { curveBasisClosed: function(e) { return new gz(e) }, curveBasisOpen: function(e) { return new yz(e) }, curveBasis: function(e) { return new vz(e) }, curveBumpX: function(e) { return new bz(e, !0) }, curveBumpY: function(e) { return new bz(e, !1) }, curveLinearClosed: function(e) { return new wz(e) }, curveLinear: xz, curveMonotoneX: function(e) { return new Ez(e) }, curveMonotoneY: function(e) { return new Cz(e) }, curveNatural: function(e) { return new Hz(e) }, curveStep: function(e) { return new Iz(e, .5) }, curveStepAfter: function(e) { return new Iz(e, 1) }, curveStepBefore: function(e) { return new Iz(e, 0) } },
            Wz = function(e) { return e.x === +e.x && e.y === +e.y },
            Uz = function(e) { return e.x },
            qz = function(e) { return e.y },
            Gz = function(e) { var t, n = e.type,
                    r = void 0 === n ? "linear" : n,
                    a = e.points,
                    o = void 0 === a ? [] : a,
                    i = e.baseLine,
                    l = e.layout,
                    s = e.connectNulls,
                    c = void 0 !== s && s,
                    d = function(e, t) { if (Ba()(e)) return e; var n = "curve".concat(Ci()(e)); return "curveMonotone" !== n && "curveBump" !== n || !t ? Bz[n] || xz : Bz["".concat(n).concat("vertical" === t ? "Y" : "X")] }(r, l),
                    u = c ? o.filter((function(e) { return Wz(e) })) : o; if (Array.isArray(i)) { var h = c ? i.filter((function(e) { return Wz(e) })) : i,
                        m = u.map((function(e, t) { return Nz(Nz({}, e), {}, { base: h[t] }) })); return (t = "vertical" === l ? Rz().y(qz).x1(Uz).x0((function(e) { return e.base.x })) : Rz().x(Uz).y1(qz).y0((function(e) { return e.base.y }))).defined(Wz).curve(d), t(m) } return (t = "vertical" === l && Ha(i) ? Rz().y(qz).x1(Uz).x0(i) : Ha(i) ? Rz().x(Uz).y1(qz).y0(i) : Oz().x(Uz).y(qz)).defined(Wz).curve(d), t(u) },
            Kz = function(e) { var t = e.className,
                    n = e.points,
                    r = e.path,
                    o = e.pathRef; if ((!n || !n.length) && !r) return null; var i = n && n.length ? Gz(e) : r; return a.createElement("path", Dz({}, po(e, !1), $a(e), { className: va("recharts-curve", t), d: i, ref: o })) };

        function Zz(e) { return Zz = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Zz(e) } var Yz = ["x", "y", "top", "left", "width", "height", "className"];

        function Xz() { return Xz = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Xz.apply(this, arguments) }

        function $z(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Qz(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Zz(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Zz(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Zz(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Jz(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a } var ex = function(e, t, n, r, a, o) { return "M".concat(e, ",").concat(a, "v").concat(r, "M").concat(o, ",").concat(t, "h").concat(n) },
            tx = function(e) { var t = e.x,
                    n = void 0 === t ? 0 : t,
                    r = e.y,
                    o = void 0 === r ? 0 : r,
                    i = e.top,
                    l = void 0 === i ? 0 : i,
                    s = e.left,
                    c = void 0 === s ? 0 : s,
                    d = e.width,
                    u = void 0 === d ? 0 : d,
                    h = e.height,
                    m = void 0 === h ? 0 : h,
                    p = e.className,
                    f = function(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                            t % 2 ? $z(Object(n), !0).forEach((function(t) { Qz(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : $z(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }({ x: n, y: o, top: l, left: c, width: u, height: m }, Jz(e, Yz)); return Ha(n) && Ha(o) && Ha(u) && Ha(m) && Ha(l) && Ha(c) ? a.createElement("path", Xz({}, po(f, !0), { className: va("recharts-cross", p), d: ex(n, o, u, m, l, c) })) : null };

        function nx(e) { var t = e.cx,
                n = e.cy,
                r = e.radius,
                a = e.startAngle,
                o = e.endAngle; return { points: [dy(t, n, r, a), dy(t, n, r, o)], cx: t, cy: n, radius: r, startAngle: a, endAngle: o } }

        function rx(e, t, n) { var r, a, o, i; if ("horizontal" === e) o = r = t.x, a = n.top, i = n.top + n.height;
            else if ("vertical" === e) i = a = t.y, r = n.left, o = n.left + n.width;
            else if (null != t.cx && null != t.cy) { if ("centric" !== e) return nx(t); var l = t.cx,
                    s = t.cy,
                    c = t.innerRadius,
                    d = t.outerRadius,
                    u = t.angle,
                    h = dy(l, s, c, u),
                    m = dy(l, s, d, u);
                r = h.x, a = h.y, o = m.x, i = m.y } return [{ x: r, y: a }, { x: o, y: i }] }

        function ax(e) { return ax = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, ax(e) }

        function ox(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function ix(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? ox(Object(n), !0).forEach((function(t) { lx(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ox(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function lx(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != ax(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != ax(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == ax(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function sx(e) { var t, n = e.element,
                r = e.tooltipEventType,
                o = e.isActive,
                i = e.activeCoordinate,
                l = e.activePayload,
                s = e.offset,
                c = e.activeTooltipIndex,
                d = e.tooltipAxisBandSize,
                u = e.layout,
                h = e.chartName; if (!n || !n.props.cursor || !o || !i || "ScatterChart" !== h && "axis" !== r) return null; var m = Kz; if ("ScatterChart" === h) t = i, m = tx;
            else if ("BarChart" === h) t = function(e, t, n, r) { var a = r / 2; return { stroke: "none", fill: "#ccc", x: "horizontal" === e ? t.x - a : n.left + .5, y: "horizontal" === e ? n.top + .5 : t.y - a, width: "horizontal" === e ? r : n.width - 1, height: "horizontal" === e ? n.height - 1 : r } }(u, i, s, d), m = pd;
            else if ("radial" === u) { var p = nx(i),
                    f = p.cx,
                    v = p.cy,
                    g = p.radius;
                t = { cx: f, cy: v, startAngle: p.startAngle, endAngle: p.endAngle, innerRadius: g, outerRadius: g }, m = wb } else t = { points: rx(u, i, s) }, m = Kz; var y = ix(ix(ix(ix({ stroke: "#ccc", pointerEvents: "none" }, s), t), po(n.props.cursor, !1)), {}, { payload: l, payloadIndex: c, className: va("recharts-tooltip-cursor", n.props.cursor.className) }); return (0, a.isValidElement)(n.props.cursor) ? (0, a.cloneElement)(n.props.cursor, y) : (0, a.createElement)(m, y) } var cx = ["item"],
            dx = ["children", "className", "width", "height", "style", "compact", "title", "desc"];

        function ux(e) { return ux = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, ux(e) }

        function hx() { return hx = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, hx.apply(this, arguments) }

        function mx(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || xx(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function px(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function fx(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, Ex(r.key), r) } }

        function vx(e, t, n) { return t = yx(t),
                function(e, t) { if (t && ("object" === ux(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return bx(e) }(e, gx() ? Reflect.construct(t, n || [], yx(e).constructor) : t.apply(e, n)) }

        function gx() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (gx = function() { return !!e })() }

        function yx(e) { return yx = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, yx(e) }

        function bx(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }

        function wx(e, t) { return wx = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, wx(e, t) }

        function zx(e) { return function(e) { if (Array.isArray(e)) return Ax(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || xx(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function xx(e, t) { if (e) { if ("string" === typeof e) return Ax(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Ax(e, t) : void 0 } }

        function Ax(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function kx(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Sx(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? kx(Object(n), !0).forEach((function(t) { Mx(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : kx(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Mx(e, t, n) { return (t = Ex(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Ex(e) { var t = function(e, t) { if ("object" != ux(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != ux(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == ux(t) ? t : String(t) } var Cx = { xAxis: ["bottom", "top"], yAxis: ["left", "right"] },
            Tx = { width: "100%", height: "100%" },
            Hx = { x: 0, y: 0 };

        function Lx(e) { return e } var Ix = function(e, t) { var n = t.graphicalItems,
                r = t.dataStartIndex,
                a = t.dataEndIndex,
                o = (null !== n && void 0 !== n ? n : []).reduce((function(e, t) { var n = t.props.data; return n && n.length ? [].concat(zx(e), zx(n)) : e }), []); return o.length > 0 ? o : e && e.length && Ha(r) && Ha(a) ? e.slice(r, a + 1) : [] };

        function jx(e) { return "number" === e ? [0, "auto"] : void 0 } var Vx = function(e, t, n, r) { var a = e.graphicalItems,
                    o = e.tooltipAxis,
                    i = Ix(t, e); return n < 0 || !a || !a.length || n >= i.length ? null : a.reduce((function(a, l) { var s, c, d = null !== (s = l.props.data) && void 0 !== s ? s : t;
                    (d && e.dataStartIndex + e.dataEndIndex !== 0 && (d = d.slice(e.dataStartIndex, e.dataEndIndex + 1)), o.dataKey && !o.allowDuplicatedCategory) ? c = Pa(void 0 === d ? i : d, o.dataKey, r): c = d && d[n] || i[n]; return c ? [].concat(zx(a), [Rg(l, c)]) : a }), []) },
            Ox = function(e, t, n, r) { var a = r || { x: e.chartX, y: e.chartY },
                    o = function(e, t) { return "horizontal" === t ? e.x : "vertical" === t ? e.y : "centric" === t ? e.angle : e.radius }(a, n),
                    i = e.orderedTooltipTicks,
                    l = e.tooltipAxis,
                    s = e.tooltipTicks,
                    c = function(e) { var t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [],
                            r = arguments.length > 2 ? arguments[2] : void 0,
                            a = arguments.length > 3 ? arguments[3] : void 0,
                            o = -1,
                            i = null !== (t = null === n || void 0 === n ? void 0 : n.length) && void 0 !== t ? t : 0; if (i <= 1) return 0; if (a && "angleAxis" === a.axisType && Math.abs(Math.abs(a.range[1] - a.range[0]) - 360) <= 1e-6)
                            for (var l = a.range, s = 0; s < i; s++) { var c = s > 0 ? r[s - 1].coordinate : r[i - 1].coordinate,
                                    d = r[s].coordinate,
                                    u = s >= i - 1 ? r[0].coordinate : r[s + 1].coordinate,
                                    h = void 0; if (Ca(d - c) !== Ca(u - d)) { var m = []; if (Ca(u - d) === Ca(l[1] - l[0])) { h = u; var p = d + l[1] - l[0];
                                        m[0] = Math.min(p, (p + c) / 2), m[1] = Math.max(p, (p + c) / 2) } else { h = c; var f = u + l[1] - l[0];
                                        m[0] = Math.min(d, (f + d) / 2), m[1] = Math.max(d, (f + d) / 2) } var v = [Math.min(d, (h + d) / 2), Math.max(d, (h + d) / 2)]; if (e > v[0] && e <= v[1] || e >= m[0] && e <= m[1]) { o = r[s].index; break } } else { var g = Math.min(c, u),
                                        y = Math.max(c, u); if (e > (g + d) / 2 && e <= (y + d) / 2) { o = r[s].index; break } } } else
                                for (var b = 0; b < i; b++)
                                    if (0 === b && e <= (n[b].coordinate + n[b + 1].coordinate) / 2 || b > 0 && b < i - 1 && e > (n[b].coordinate + n[b - 1].coordinate) / 2 && e <= (n[b].coordinate + n[b + 1].coordinate) / 2 || b === i - 1 && e > (n[b].coordinate + n[b - 1].coordinate) / 2) { o = n[b].index; break } return o }(o, i, s, l); if (c >= 0 && s) { var d = s[c] && s[c].value,
                        u = Vx(e, t, c, d),
                        h = function(e, t, n, r) { var a = t.find((function(e) { return e && e.index === n })); if (a) { if ("horizontal" === e) return { x: a.coordinate, y: r.y }; if ("vertical" === e) return { x: r.x, y: a.coordinate }; if ("centric" === e) { var o = a.coordinate,
                                        i = r.radius; return Sx(Sx(Sx({}, r), dy(r.cx, r.cy, i, o)), {}, { angle: o, radius: i }) } var l = a.coordinate,
                                    s = r.angle; return Sx(Sx(Sx({}, r), dy(r.cx, r.cy, l, s)), {}, { angle: s, radius: l }) } return Hx }(n, i, c, a); return { activeTooltipIndex: c, activeLabel: d, activePayload: u, activeCoordinate: h } } return null },
            Rx = function(e, t) { var n = t.axes,
                    r = t.graphicalItems,
                    a = t.axisType,
                    o = t.axisIdKey,
                    i = t.stackGroups,
                    l = t.dataStartIndex,
                    s = t.dataEndIndex,
                    c = e.layout,
                    d = e.children,
                    u = e.stackOffset,
                    h = gg(c, a); return n.reduce((function(t, n) { var m, p = n.props,
                        f = p.type,
                        v = p.dataKey,
                        g = p.allowDataOverflow,
                        y = p.allowDuplicatedCategory,
                        b = p.scale,
                        w = p.ticks,
                        z = p.includeHidden,
                        x = n.props[o]; if (t[x]) return t; var A, k, S, M = Ix(e.data, { graphicalItems: r.filter((function(e) { return e.props[o] === x })), dataStartIndex: l, dataEndIndex: s }),
                        E = M.length;
                    (function(e, t, n) { if ("number" === n && !0 === t && Array.isArray(e)) { var r = null === e || void 0 === e ? void 0 : e[0],
                                a = null === e || void 0 === e ? void 0 : e[1]; if (r && a && Ha(r) && Ha(a)) return !0 } return !1 })(n.props.domain, g, f) && (A = jg(n.props.domain, null, g), !h || "number" !== f && "auto" === b || (S = mg(M, v, "category"))); var C = jx(f); if (!A || 0 === A.length) { var T, H = null !== (T = n.props.domain) && void 0 !== T ? T : C; if (v) { if (A = mg(M, v, f), "category" === f && h) { var L = function(e) { if (!Array.isArray(e)) return !1; for (var t = e.length, n = {}, r = 0; r < t; r++) { if (n[e[r]]) return !0;
                                        n[e[r]] = !0 } return !1 }(A);
                                y && L ? (k = A, A = Mo()(0, E)) : y || (A = Og(H, A, n).reduce((function(e, t) { return e.indexOf(t) >= 0 ? e : [].concat(zx(e), [t]) }), [])) } else if ("category" === f) A = y ? A.filter((function(e) { return "" !== e && !Na()(e) })) : Og(H, A, n).reduce((function(e, t) { return e.indexOf(t) >= 0 || "" === t || Na()(t) ? e : [].concat(zx(e), [t]) }), []);
                            else if ("number" === f) { var I = function(e, t, n, r, a) { var o = t.map((function(t) { return fg(e, t, n, a, r) })).filter((function(e) { return !Na()(e) })); return o && o.length ? o.reduce((function(e, t) { return [Math.min(e[0], t[0]), Math.max(e[1], t[1])] }), [1 / 0, -1 / 0]) : null }(M, r.filter((function(e) { return e.props[o] === x && (z || !e.props.hide) })), v, a, c);
                                I && (A = I) }!h || "number" !== f && "auto" === b || (S = mg(M, v, "category")) } else A = h ? Mo()(0, E) : i && i[x] && i[x].hasStack && "number" === f ? "expand" === u ? [0, 1] : Hg(i[x].stackGroups, l, s) : vg(M, r.filter((function(e) { return e.props[o] === x && (z || !e.props.hide) })), f, c, !0); if ("number" === f) A = oz(d, A, x, a, w), H && (A = jg(H, A, g));
                        else if ("category" === f && H) { var j = H;
                            A.every((function(e) { return j.indexOf(e) >= 0 })) && (A = j) } } return Sx(Sx({}, t), {}, Mx({}, x, Sx(Sx({}, n.props), {}, { axisType: a, domain: A, categoricalDomain: S, duplicateDomain: k, originalDomain: null !== (m = n.props.domain) && void 0 !== m ? m : C, isCategorical: h, layout: c }))) }), {}) },
            Px = function(e, t) { var n = t.axisType,
                    r = void 0 === n ? "xAxis" : n,
                    a = t.AxisComp,
                    o = t.graphicalItems,
                    i = t.stackGroups,
                    l = t.dataStartIndex,
                    s = t.dataEndIndex,
                    c = e.children,
                    d = "".concat(r, "Id"),
                    u = so(c, a),
                    h = {}; return u && u.length ? h = Rx(e, { axes: u, graphicalItems: o, axisType: r, axisIdKey: d, stackGroups: i, dataStartIndex: l, dataEndIndex: s }) : o && o.length && (h = function(e, t) { var n = t.graphicalItems,
                        r = t.Axis,
                        a = t.axisType,
                        o = t.axisIdKey,
                        i = t.stackGroups,
                        l = t.dataStartIndex,
                        s = t.dataEndIndex,
                        c = e.layout,
                        d = e.children,
                        u = Ix(e.data, { graphicalItems: n, dataStartIndex: l, dataEndIndex: s }),
                        h = u.length,
                        m = gg(c, a),
                        p = -1; return n.reduce((function(e, t) { var f, v = t.props[o],
                            g = jx("number"); return e[v] ? e : (p++, m ? f = Mo()(0, h) : i && i[v] && i[v].hasStack ? (f = Hg(i[v].stackGroups, l, s), f = oz(d, f, v, a)) : (f = jg(g, vg(u, n.filter((function(e) { return e.props[o] === v && !e.props.hide })), "number", c), r.defaultProps.allowDataOverflow), f = oz(d, f, v, a)), Sx(Sx({}, e), {}, Mx({}, v, Sx(Sx({ axisType: a }, r.defaultProps), {}, { hide: !0, orientation: Sa()(Cx, "".concat(a, ".").concat(p % 2), null), domain: f, originalDomain: g, isCategorical: m, layout: c })))) }), {}) }(e, { Axis: a, graphicalItems: o, axisType: r, axisIdKey: d, stackGroups: i, dataStartIndex: l, dataEndIndex: s })), h },
            Dx = function(e) { var t = e.children,
                    n = e.defaultShowTooltip,
                    r = co(t, ty),
                    a = 0,
                    o = 0; return e.data && 0 !== e.data.length && (o = e.data.length - 1), r && r.props && (r.props.startIndex >= 0 && (a = r.props.startIndex), r.props.endIndex >= 0 && (o = r.props.endIndex)), { chartX: 0, chartY: 0, dataStartIndex: a, dataEndIndex: o, activeTooltipIndex: -1, isTooltipActive: Boolean(n) } },
            Fx = function(e) { return "horizontal" === e ? { numericAxisName: "yAxis", cateAxisName: "xAxis" } : "vertical" === e ? { numericAxisName: "xAxis", cateAxisName: "yAxis" } : "centric" === e ? { numericAxisName: "radiusAxis", cateAxisName: "angleAxis" } : { numericAxisName: "angleAxis", cateAxisName: "radiusAxis" } },
            Nx = function(e, t) { var n = e.props,
                    r = (e.graphicalItems, e.xAxisMap),
                    a = void 0 === r ? {} : r,
                    o = e.yAxisMap,
                    i = void 0 === o ? {} : o,
                    l = n.width,
                    s = n.height,
                    c = n.children,
                    d = n.margin || {},
                    u = co(c, ty),
                    h = co(c, ts),
                    m = Object.keys(i).reduce((function(e, t) { var n = i[t],
                            r = n.orientation; return n.mirror || n.hide ? e : Sx(Sx({}, e), {}, Mx({}, r, e[r] + n.width)) }), { left: d.left || 0, right: d.right || 0 }),
                    p = Object.keys(a).reduce((function(e, t) { var n = a[t],
                            r = n.orientation; return n.mirror || n.hide ? e : Sx(Sx({}, e), {}, Mx({}, r, Sa()(e, "".concat(r)) + n.height)) }), { top: d.top || 0, bottom: d.bottom || 0 }),
                    f = Sx(Sx({}, p), m),
                    v = f.bottom;
                u && (f.bottom += u.props.height || ty.defaultProps.height), h && t && (f = function(e, t, n, r) { var a = n.children,
                        o = n.width,
                        i = n.margin,
                        l = o - (i.left || 0) - (i.right || 0),
                        s = og({ children: a, legendWidth: l }); if (s) { var c = r || {},
                            d = c.width,
                            u = c.height,
                            h = s.align,
                            m = s.verticalAlign,
                            p = s.layout; if (("vertical" === p || "horizontal" === p && "middle" === m) && "center" !== h && Ha(e[h])) return sg(sg({}, e), {}, cg({}, h, e[h] + (d || 0))); if (("horizontal" === p || "vertical" === p && "center" === h) && "middle" !== m && Ha(e[m])) return sg(sg({}, e), {}, cg({}, m, e[m] + (u || 0))) } return e }(f, 0, n, t)); var g = l - f.left - f.right,
                    y = s - f.top - f.bottom; return Sx(Sx({ brushBottom: v }, f), {}, { width: Math.max(g, 0), height: Math.max(y, 0) }) },
            _x = function(e, t) { return "xAxis" === t ? e[t].width : "yAxis" === t ? e[t].height : void 0 },
            Bx = function(e) { var t, n = e.chartName,
                    r = e.GraphicalChild,
                    o = e.defaultTooltipEventType,
                    i = void 0 === o ? "axis" : o,
                    l = e.validateTooltipEventTypes,
                    s = void 0 === l ? ["axis"] : l,
                    c = e.axisComponents,
                    d = e.legendContent,
                    u = e.formatAxisMap,
                    h = e.defaultProps,
                    m = function(e, t) { var n = t.graphicalItems,
                            r = t.stackGroups,
                            a = t.offset,
                            o = t.updateId,
                            i = t.dataStartIndex,
                            l = t.dataEndIndex,
                            s = e.barSize,
                            d = e.layout,
                            u = e.barGap,
                            h = e.barCategoryGap,
                            m = e.maxBarSize,
                            p = Fx(d),
                            f = p.numericAxisName,
                            v = p.cateAxisName,
                            g = function(e) { return !(!e || !e.length) && e.some((function(e) { var t = ao(e && e.type); return t && t.indexOf("Bar") >= 0 })) }(n),
                            y = []; return n.forEach((function(n, p) { var b = Ix(e.data, { graphicalItems: [n], dataStartIndex: i, dataEndIndex: l }),
                                w = n.props,
                                z = w.dataKey,
                                x = w.maxBarSize,
                                A = n.props["".concat(f, "Id")],
                                k = n.props["".concat(v, "Id")],
                                S = c.reduce((function(e, r) { var a = t["".concat(r.axisType, "Map")],
                                        o = n.props["".concat(r.axisType, "Id")];
                                    a && a[o] || "zAxis" === r.axisType || (0, To.A)(!1); var i = a[o]; return Sx(Sx({}, e), {}, Mx(Mx({}, r.axisType, i), "".concat(r.axisType, "Ticks"), bg(i))) }), {}),
                                M = S[v],
                                E = S["".concat(v, "Ticks")],
                                C = r && r[A] && r[A].hasStack && function(e, t) { var n = e.props.stackId; if (La(n)) { var r = t[n]; if (r) { var a = r.items.indexOf(e); return a >= 0 ? r.stackedData[a] : null } } return null }(n, r[A].stackGroups),
                                T = ao(n.type).indexOf("Bar") >= 0,
                                H = Vg(M, E),
                                L = [],
                                I = g && function(e) { var t = e.barSize,
                                        n = e.totalSize,
                                        r = e.stackGroups,
                                        a = void 0 === r ? {} : r; if (!a) return {}; for (var o = {}, i = Object.keys(a), l = 0, s = i.length; l < s; l++)
                                        for (var c = a[i[l]].stackGroups, d = Object.keys(c), u = 0, h = d.length; u < h; u++) { var m = c[d[u]],
                                                p = m.items,
                                                f = m.cateAxisId,
                                                v = p.filter((function(e) { return ao(e.type).indexOf("Bar") >= 0 })); if (v && v.length) { var g = v[0].props.barSize,
                                                    y = v[0].props[f];
                                                o[y] || (o[y] = []); var b = Na()(g) ? t : g;
                                                o[y].push({ item: v[0], stackList: v.slice(1), barSize: Na()(b) ? void 0 : Va(b, n, 0) }) } }
                                    return o }({ barSize: s, stackGroups: r, totalSize: _x(S, v) }); if (T) { var j, V, O = Na()(x) ? m : x,
                                    R = null !== (j = null !== (V = Vg(M, E, !0)) && void 0 !== V ? V : O) && void 0 !== j ? j : 0;
                                L = function(e) { var t = e.barGap,
                                        n = e.barCategoryGap,
                                        r = e.bandSize,
                                        a = e.sizeList,
                                        o = void 0 === a ? [] : a,
                                        i = e.maxBarSize,
                                        l = o.length; if (l < 1) return null; var s, c = Va(t, r, 0, !0),
                                        d = []; if (o[0].barSize === +o[0].barSize) { var u = !1,
                                            h = r / l,
                                            m = o.reduce((function(e, t) { return e + t.barSize || 0 }), 0);
                                        (m += (l - 1) * c) >= r && (m -= (l - 1) * c, c = 0), m >= r && h > 0 && (u = !0, m = l * (h *= .9)); var p = { offset: ((r - m) / 2 | 0) - c, size: 0 };
                                        s = o.reduce((function(e, t) { var n = { item: t.item, position: { offset: p.offset + p.size + c, size: u ? h : t.barSize } },
                                                r = [].concat(dg(e), [n]); return p = r[r.length - 1].position, t.stackList && t.stackList.length && t.stackList.forEach((function(e) { r.push({ item: e, position: p }) })), r }), d) } else { var f = Va(n, r, 0, !0);
                                        r - 2 * f - (l - 1) * c <= 0 && (c = 0); var v = (r - 2 * f - (l - 1) * c) / l;
                                        v > 1 && (v >>= 0); var g = i === +i ? Math.min(v, i) : v;
                                        s = o.reduce((function(e, t, n) { var r = [].concat(dg(e), [{ item: t.item, position: { offset: f + (v + c) * n + (v - g) / 2, size: g } }]); return t.stackList && t.stackList.length && t.stackList.forEach((function(e) { r.push({ item: e, position: r[r.length - 1].position }) })), r }), d) } return s }({ barGap: u, barCategoryGap: h, bandSize: R !== H ? R : H, sizeList: I[k], maxBarSize: O }), R !== H && (L = L.map((function(e) { return Sx(Sx({}, e), {}, { position: Sx(Sx({}, e.position), {}, { offset: e.position.offset - R / 2 }) }) }))) } var P, D, F = n && n.type && n.type.getComposedData;
                            F && y.push({ props: Sx(Sx({}, F(Sx(Sx({}, S), {}, { displayedData: b, props: e, dataKey: z, item: n, bandSize: H, barPosition: L, offset: a, stackedData: C, layout: d, dataStartIndex: i, dataEndIndex: l }))), {}, Mx(Mx(Mx({ key: n.key || "item-".concat(p) }, f, S[f]), v, S[v]), "animationId", o)), childIndex: (P = n, D = e.children, lo(D).indexOf(P)), item: n }) })), y },
                    p = function(e, t) { var a = e.props,
                            o = e.dataStartIndex,
                            i = e.dataEndIndex,
                            l = e.updateId; if (!uo({ props: a })) return null; var s = a.children,
                            d = a.layout,
                            h = a.stackOffset,
                            p = a.data,
                            f = a.reverseStackOrder,
                            v = Fx(d),
                            g = v.numericAxisName,
                            y = v.cateAxisName,
                            b = so(s, r),
                            w = function(e, t, n, r, a, o) { if (!e) return null; var i = (o ? t.reverse() : t).reduce((function(e, t) { var a = t.props,
                                        o = a.stackId; if (a.hide) return e; var i = t.props[n],
                                        l = e[i] || { hasStack: !1, stackGroups: {} }; if (La(o)) { var s = l.stackGroups[o] || { numericAxisId: n, cateAxisId: r, items: [] };
                                        s.items.push(t), l.hasStack = !0, l.stackGroups[o] = s } else l.stackGroups[ja("_stackId_")] = { numericAxisId: n, cateAxisId: r, items: [t] }; return sg(sg({}, e), {}, cg({}, i, l)) }), {}); return Object.keys(i).reduce((function(t, o) { var l = i[o]; return l.hasStack && (l.stackGroups = Object.keys(l.stackGroups).reduce((function(t, o) { var i = l.stackGroups[o]; return sg(sg({}, t), {}, cg({}, o, { numericAxisId: n, cateAxisId: r, items: i.items, stackedData: Mg(e, i.items, a) })) }), {})), sg(sg({}, t), {}, cg({}, o, l)) }), {}) }(p, b, "".concat(g, "Id"), "".concat(y, "Id"), h, f),
                            z = c.reduce((function(e, t) { var n = "".concat(t.axisType, "Map"); return Sx(Sx({}, e), {}, Mx({}, n, Px(a, Sx(Sx({}, t), {}, { graphicalItems: b, stackGroups: t.axisType === g && w, dataStartIndex: o, dataEndIndex: i })))) }), {}),
                            x = Nx(Sx(Sx({}, z), {}, { props: a, graphicalItems: b }), null === t || void 0 === t ? void 0 : t.legendBBox);
                        Object.keys(z).forEach((function(e) { z[e] = u(a, z[e], x, e.replace("Map", ""), n) })); var A = function(e) { var t = Oa(e),
                                    n = bg(t, !1, !0); return { tooltipTicks: n, orderedTooltipTicks: Co()(n, (function(e) { return e.coordinate })), tooltipAxis: t, tooltipAxisBandSize: Vg(t, n) } }(z["".concat(y, "Map")]),
                            k = m(a, Sx(Sx({}, z), {}, { dataStartIndex: o, dataEndIndex: i, updateId: l, graphicalItems: b, stackGroups: w, offset: x })); return Sx(Sx({ formattedGraphicalItems: k, graphicalItems: b, offset: x, stackGroups: w }, A), z) }; return t = function(e) {
                    function t(e) { var r, o, i; return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), Mx(bx(i = vx(this, t, [e])), "eventEmitterSymbol", Symbol("rechartsEventEmitter")), Mx(bx(i), "accessibilityManager", new mz), Mx(bx(i), "handleLegendBBoxUpdate", (function(e) { if (e) { var t = i.state,
                                    n = t.dataStartIndex,
                                    r = t.dataEndIndex,
                                    a = t.updateId;
                                i.setState(Sx({ legendBBox: e }, p({ props: i.props, dataStartIndex: n, dataEndIndex: r, updateId: a }, Sx(Sx({}, i.state), {}, { legendBBox: e })))) } })), Mx(bx(i), "handleReceiveSyncEvent", (function(e, t, n) { if (i.props.syncId === e) { if (n === i.eventEmitterSymbol && "function" !== typeof i.props.syncMethod) return;
                                i.applySyncEvent(t) } })), Mx(bx(i), "handleBrushChange", (function(e) { var t = e.startIndex,
                                n = e.endIndex; if (t !== i.state.dataStartIndex || n !== i.state.dataEndIndex) { var r = i.state.updateId;
                                i.setState((function() { return Sx({ dataStartIndex: t, dataEndIndex: n }, p({ props: i.props, dataStartIndex: t, dataEndIndex: n, updateId: r }, i.state)) })), i.triggerSyncEvent({ dataStartIndex: t, dataEndIndex: n }) } })), Mx(bx(i), "handleMouseEnter", (function(e) { var t = i.getMouseInfo(e); if (t) { var n = Sx(Sx({}, t), {}, { isTooltipActive: !0 });
                                i.setState(n), i.triggerSyncEvent(n); var r = i.props.onMouseEnter;
                                Ba()(r) && r(n, e) } })), Mx(bx(i), "triggeredAfterMouseMove", (function(e) { var t = i.getMouseInfo(e),
                                n = t ? Sx(Sx({}, t), {}, { isTooltipActive: !0 }) : { isTooltipActive: !1 };
                            i.setState(n), i.triggerSyncEvent(n); var r = i.props.onMouseMove;
                            Ba()(r) && r(n, e) })), Mx(bx(i), "handleItemMouseEnter", (function(e) { i.setState((function() { return { isTooltipActive: !0, activeItem: e, activePayload: e.tooltipPayload, activeCoordinate: e.tooltipPosition || { x: e.cx, y: e.cy } } })) })), Mx(bx(i), "handleItemMouseLeave", (function() { i.setState((function() { return { isTooltipActive: !1 } })) })), Mx(bx(i), "handleMouseMove", (function(e) { e.persist(), i.throttleTriggeredAfterMouseMove(e) })), Mx(bx(i), "handleMouseLeave", (function(e) { i.throttleTriggeredAfterMouseMove.cancel(); var t = { isTooltipActive: !1 };
                            i.setState(t), i.triggerSyncEvent(t); var n = i.props.onMouseLeave;
                            Ba()(n) && n(t, e) })), Mx(bx(i), "handleOuterEvent", (function(e) { var t, n = function(e) { var t = e && e.type; return t && ro[t] ? ro[t] : null }(e),
                                r = Sa()(i.props, "".concat(n));
                            n && Ba()(r) && r(null !== (t = /.*touch.*/i.test(n) ? i.getMouseInfo(e.changedTouches[0]) : i.getMouseInfo(e)) && void 0 !== t ? t : {}, e) })), Mx(bx(i), "handleClick", (function(e) { var t = i.getMouseInfo(e); if (t) { var n = Sx(Sx({}, t), {}, { isTooltipActive: !0 });
                                i.setState(n), i.triggerSyncEvent(n); var r = i.props.onClick;
                                Ba()(r) && r(n, e) } })), Mx(bx(i), "handleMouseDown", (function(e) { var t = i.props.onMouseDown;
                            Ba()(t) && t(i.getMouseInfo(e), e) })), Mx(bx(i), "handleMouseUp", (function(e) { var t = i.props.onMouseUp;
                            Ba()(t) && t(i.getMouseInfo(e), e) })), Mx(bx(i), "handleTouchMove", (function(e) { null != e.changedTouches && e.changedTouches.length > 0 && i.throttleTriggeredAfterMouseMove(e.changedTouches[0]) })), Mx(bx(i), "handleTouchStart", (function(e) { null != e.changedTouches && e.changedTouches.length > 0 && i.handleMouseDown(e.changedTouches[0]) })), Mx(bx(i), "handleTouchEnd", (function(e) { null != e.changedTouches && e.changedTouches.length > 0 && i.handleMouseUp(e.changedTouches[0]) })), Mx(bx(i), "triggerSyncEvent", (function(e) { void 0 !== i.props.syncId && lz.emit(sz, i.props.syncId, e, i.eventEmitterSymbol) })), Mx(bx(i), "applySyncEvent", (function(e) { var t = i.props,
                                n = t.layout,
                                r = t.syncMethod,
                                a = i.state.updateId,
                                o = e.dataStartIndex,
                                l = e.dataEndIndex; if (void 0 !== e.dataStartIndex || void 0 !== e.dataEndIndex) i.setState(Sx({ dataStartIndex: o, dataEndIndex: l }, p({ props: i.props, dataStartIndex: o, dataEndIndex: l, updateId: a }, i.state)));
                            else if (void 0 !== e.activeTooltipIndex) { var s = e.chartX,
                                    c = e.chartY,
                                    d = e.activeTooltipIndex,
                                    u = i.state,
                                    h = u.offset,
                                    m = u.tooltipTicks; if (!h) return; if ("function" === typeof r) d = r(m, e);
                                else if ("value" === r) { d = -1; for (var f = 0; f < m.length; f++)
                                        if (m[f].value === e.activeLabel) { d = f; break } } var v = Sx(Sx({}, h), {}, { x: h.left, y: h.top }),
                                    g = Math.min(s, v.x + v.width),
                                    y = Math.min(c, v.y + v.height),
                                    b = m[d] && m[d].value,
                                    w = Vx(i.state, i.props.data, d),
                                    z = m[d] ? { x: "horizontal" === n ? m[d].coordinate : g, y: "horizontal" === n ? y : m[d].coordinate } : Hx;
                                i.setState(Sx(Sx({}, e), {}, { activeLabel: b, activeCoordinate: z, activePayload: w, activeTooltipIndex: d })) } else i.setState(e) })), Mx(bx(i), "renderCursor", (function(e) { var t, r = i.state,
                                o = r.isTooltipActive,
                                l = r.activeCoordinate,
                                s = r.activePayload,
                                c = r.offset,
                                d = r.activeTooltipIndex,
                                u = r.tooltipAxisBandSize,
                                h = i.getTooltipEventType(),
                                m = null !== (t = e.props.active) && void 0 !== t ? t : o,
                                p = i.props.layout,
                                f = e.key || "_recharts-cursor"; return a.createElement(sx, { key: f, activeCoordinate: l, activePayload: s, activeTooltipIndex: d, chartName: n, element: e, isActive: m, layout: p, offset: c, tooltipAxisBandSize: u, tooltipEventType: h }) })), Mx(bx(i), "renderPolarAxis", (function(e, t, n) { var r = Sa()(e, "type.axisType"),
                                o = Sa()(i.state, "".concat(r, "Map")),
                                l = o && o[e.props["".concat(r, "Id")]]; return (0, a.cloneElement)(e, Sx(Sx({}, l), {}, { className: va(r, l.className), key: e.key || "".concat(t, "-").concat(n), ticks: bg(l, !0) })) })), Mx(bx(i), "renderPolarGrid", (function(e) { var t = e.props,
                                n = t.radialLines,
                                r = t.polarAngles,
                                o = t.polarRadius,
                                l = i.state,
                                s = l.radiusAxisMap,
                                c = l.angleAxisMap,
                                d = Oa(s),
                                u = Oa(c),
                                h = u.cx,
                                m = u.cy,
                                p = u.innerRadius,
                                f = u.outerRadius; return (0, a.cloneElement)(e, { polarAngles: Array.isArray(r) ? r : bg(u, !0).map((function(e) { return e.coordinate })), polarRadius: Array.isArray(o) ? o : bg(d, !0).map((function(e) { return e.coordinate })), cx: h, cy: m, innerRadius: p, outerRadius: f, key: e.key || "polar-grid", radialLines: n }) })), Mx(bx(i), "renderLegend", (function() { var e = i.state.formattedGraphicalItems,
                                t = i.props,
                                n = t.children,
                                r = t.width,
                                o = t.height,
                                l = i.props.margin || {},
                                s = r - (l.left || 0) - (l.right || 0),
                                c = og({ children: n, formattedGraphicalItems: e, legendWidth: s, legendContent: d }); if (!c) return null; var u = c.item,
                                h = px(c, cx); return (0, a.cloneElement)(u, Sx(Sx({}, h), {}, { chartWidth: r, chartHeight: o, margin: l, onBBoxUpdate: i.handleLegendBBoxUpdate })) })), Mx(bx(i), "renderTooltip", (function() { var e, t = i.props,
                                n = t.children,
                                r = t.accessibilityLayer,
                                o = co(n, Mi); if (!o) return null; var l = i.state,
                                s = l.isTooltipActive,
                                c = l.activeCoordinate,
                                d = l.activePayload,
                                u = l.activeLabel,
                                h = l.offset,
                                m = null !== (e = o.props.active) && void 0 !== e ? e : s; return (0, a.cloneElement)(o, { viewBox: Sx(Sx({}, h), {}, { x: h.left, y: h.top }), active: m, label: u, payload: m ? d : [], coordinate: c, accessibilityLayer: r }) })), Mx(bx(i), "renderBrush", (function(e) { var t = i.props,
                                n = t.margin,
                                r = t.data,
                                o = i.state,
                                l = o.offset,
                                s = o.dataStartIndex,
                                c = o.dataEndIndex,
                                d = o.updateId; return (0, a.cloneElement)(e, { key: e.key || "_recharts-brush", onChange: zg(i.handleBrushChange, e.props.onChange), data: r, x: Ha(e.props.x) ? e.props.x : l.left, y: Ha(e.props.y) ? e.props.y : l.top + l.height + l.brushBottom - (n.bottom || 0), width: Ha(e.props.width) ? e.props.width : l.width, startIndex: s, endIndex: c, updateId: "brush-".concat(d) }) })), Mx(bx(i), "renderReferenceElement", (function(e, t, n) { if (!e) return null; var r = bx(i).clipPathId,
                                o = i.state,
                                l = o.xAxisMap,
                                s = o.yAxisMap,
                                c = o.offset,
                                d = e.props,
                                u = d.xAxisId,
                                h = d.yAxisId; return (0, a.cloneElement)(e, { key: e.key || "".concat(t, "-").concat(n), xAxis: l[u], yAxis: s[h], viewBox: { x: c.left, y: c.top, width: c.width, height: c.height }, clipPathId: r }) })), Mx(bx(i), "renderActivePoints", (function(e) { var n = e.item,
                                r = e.activePoint,
                                a = e.basePoint,
                                o = e.childIndex,
                                i = e.isRange,
                                l = [],
                                s = n.props.key,
                                c = n.item.props,
                                d = c.activeDot,
                                u = Sx(Sx({ index: o, dataKey: c.dataKey, cx: r.x, cy: r.y, r: 4, fill: pg(n.item), strokeWidth: 2, stroke: "#fff", payload: r.payload, value: r.value, key: "".concat(s, "-activePoint-").concat(o) }, po(d, !1)), $a(d)); return l.push(t.renderActiveDot(d, u)), a ? l.push(t.renderActiveDot(d, Sx(Sx({}, u), {}, { cx: a.x, cy: a.y, key: "".concat(s, "-basePoint-").concat(o) }))) : i && l.push(null), l })), Mx(bx(i), "renderGraphicChild", (function(e, t, n) { var r = i.filterFormatItem(e, t, n); if (!r) return null; var o = i.getTooltipEventType(),
                                l = i.state,
                                s = l.isTooltipActive,
                                c = l.tooltipAxis,
                                d = l.activeTooltipIndex,
                                u = l.activeLabel,
                                h = co(i.props.children, Mi),
                                m = r.props,
                                p = m.points,
                                f = m.isRange,
                                v = m.baseLine,
                                g = r.item.props,
                                y = g.activeDot,
                                b = g.hide,
                                w = g.activeBar,
                                z = g.activeShape,
                                x = Boolean(!b && s && h && (y || w || z)),
                                A = {}; "axis" !== o && h && "click" === h.props.trigger ? A = { onClick: zg(i.handleItemMouseEnter, e.props.onClick) } : "axis" !== o && (A = { onMouseLeave: zg(i.handleItemMouseLeave, e.props.onMouseLeave), onMouseEnter: zg(i.handleItemMouseEnter, e.props.onMouseEnter) }); var k = (0, a.cloneElement)(e, Sx(Sx({}, r.props), A)); if (x) { if (!(d >= 0)) { var S, M = (null !== (S = i.getItemByXY(i.state.activeCoordinate)) && void 0 !== S ? S : { graphicalItem: k }).graphicalItem,
                                        E = M.item,
                                        C = void 0 === E ? e : E,
                                        T = M.childIndex,
                                        H = Sx(Sx(Sx({}, r.props), A), {}, { activeIndex: T }); return [(0, a.cloneElement)(C, H), null, null] } var L, I; if (c.dataKey && !c.allowDuplicatedCategory) { var j = "function" === typeof c.dataKey ? function(e) { return "function" === typeof c.dataKey ? c.dataKey(e.payload) : null } : "payload.".concat(c.dataKey.toString());
                                    L = Pa(p, j, u), I = f && v && Pa(v, j, u) } else L = null === p || void 0 === p ? void 0 : p[d], I = f && v && v[d]; if (z || w) { var V = void 0 !== e.props.activeIndex ? e.props.activeIndex : d; return [(0, a.cloneElement)(e, Sx(Sx(Sx({}, r.props), A), {}, { activeIndex: V })), null, null] } if (!Na()(L)) return [k].concat(zx(i.renderActivePoints({ item: r, activePoint: L, basePoint: I, childIndex: d, isRange: f }))) } return f ? [k, null, null] : [k, null] })), Mx(bx(i), "renderCustomized", (function(e, t, n) { return (0, a.cloneElement)(e, Sx(Sx({ key: "recharts-customized-".concat(n) }, i.props), i.state)) })), Mx(bx(i), "renderMap", { CartesianGrid: { handler: Lx, once: !0 }, ReferenceArea: { handler: i.renderReferenceElement }, ReferenceLine: { handler: Lx }, ReferenceDot: { handler: i.renderReferenceElement }, XAxis: { handler: Lx }, YAxis: { handler: Lx }, Brush: { handler: i.renderBrush, once: !0 }, Bar: { handler: i.renderGraphicChild }, Line: { handler: i.renderGraphicChild }, Area: { handler: i.renderGraphicChild }, Radar: { handler: i.renderGraphicChild }, RadialBar: { handler: i.renderGraphicChild }, Scatter: { handler: i.renderGraphicChild }, Pie: { handler: i.renderGraphicChild }, Funnel: { handler: i.renderGraphicChild }, Tooltip: { handler: i.renderCursor, once: !0 }, PolarGrid: { handler: i.renderPolarGrid, once: !0 }, PolarAngleAxis: { handler: i.renderPolarAxis }, PolarRadiusAxis: { handler: i.renderPolarAxis }, Customized: { handler: i.renderCustomized } }), i.clipPathId = "".concat(null !== (r = e.id) && void 0 !== r ? r : ja("recharts"), "-clip"), i.throttleTriggeredAfterMouseMove = ya()(i.triggeredAfterMouseMove, null !== (o = e.throttleDelay) && void 0 !== o ? o : 1e3 / 60), i.state = {}, i } var r, o, l; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                        e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && wx(e, t) }(t, e), r = t, o = [{ key: "componentDidMount", value: function() { var e, t;
                            this.addListener(), this.accessibilityManager.setDetails({ container: this.container, offset: { left: null !== (e = this.props.margin.left) && void 0 !== e ? e : 0, top: null !== (t = this.props.margin.top) && void 0 !== t ? t : 0 }, coordinateList: this.state.tooltipTicks, mouseHandlerCallback: this.triggeredAfterMouseMove, layout: this.props.layout }), this.displayDefaultTooltip() } }, { key: "displayDefaultTooltip", value: function() { var e = this.props,
                                t = e.children,
                                n = e.data,
                                r = e.height,
                                a = e.layout,
                                o = co(t, Mi); if (o) { var i = o.props.defaultIndex; if (!("number" !== typeof i || i < 0 || i > this.state.tooltipTicks.length)) { var l = this.state.tooltipTicks[i] && this.state.tooltipTicks[i].value,
                                        s = Vx(this.state, n, i, l),
                                        c = this.state.tooltipTicks[i].coordinate,
                                        d = (this.state.offset.top + r) / 2,
                                        u = "horizontal" === a ? { x: c, y: d } : { y: c, x: d },
                                        h = this.state.formattedGraphicalItems.find((function(e) { return "Scatter" === e.item.type.name }));
                                    h && (u = Sx(Sx({}, u), h.props.points[i].tooltipPosition), s = h.props.points[i].tooltipPayload); var m = { activeTooltipIndex: i, isTooltipActive: !0, activeLabel: l, activePayload: s, activeCoordinate: u };
                                    this.setState(m), this.renderCursor(o), this.accessibilityManager.setIndex(i) } } } }, { key: "getSnapshotBeforeUpdate", value: function(e, t) { return this.props.accessibilityLayer ? (this.state.tooltipTicks !== t.tooltipTicks && this.accessibilityManager.setDetails({ coordinateList: this.state.tooltipTicks }), this.props.layout !== e.layout && this.accessibilityManager.setDetails({ layout: this.props.layout }), this.props.margin !== e.margin && this.accessibilityManager.setDetails({ offset: { left: null !== (n = this.props.margin.left) && void 0 !== n ? n : 0, top: null !== (r = this.props.margin.top) && void 0 !== r ? r : 0 } }), null) : null; var n, r } }, { key: "componentDidUpdate", value: function(e) { fo([co(e.children, Mi)], [co(this.props.children, Mi)]) || this.displayDefaultTooltip() } }, { key: "componentWillUnmount", value: function() { this.removeListener(), this.throttleTriggeredAfterMouseMove.cancel() } }, { key: "getTooltipEventType", value: function() { var e = co(this.props.children, Mi); if (e && "boolean" === typeof e.props.shared) { var t = e.props.shared ? "axis" : "item"; return s.indexOf(t) >= 0 ? t : i } return i } }, { key: "getMouseInfo", value: function(e) { if (!this.container) return null; var t, n = this.container,
                                r = n.getBoundingClientRect(),
                                a = { top: (t = r).top + window.scrollY - document.documentElement.clientTop, left: t.left + window.scrollX - document.documentElement.clientLeft },
                                o = { chartX: Math.round(e.pageX - a.left), chartY: Math.round(e.pageY - a.top) },
                                i = r.width / n.offsetWidth || 1,
                                l = this.inRange(o.chartX, o.chartY, i); if (!l) return null; var s = this.state,
                                c = s.xAxisMap,
                                d = s.yAxisMap; if ("axis" !== this.getTooltipEventType() && c && d) { var u = Oa(c).scale,
                                    h = Oa(d).scale,
                                    m = u && u.invert ? u.invert(o.chartX) : null,
                                    p = h && h.invert ? h.invert(o.chartY) : null; return Sx(Sx({}, o), {}, { xValue: m, yValue: p }) } var f = Ox(this.state, this.props.data, this.props.layout, l); return f ? Sx(Sx({}, o), f) : null } }, { key: "inRange", value: function(e, t) { var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1,
                                r = this.props.layout,
                                a = e / n,
                                o = t / n; if ("horizontal" === r || "vertical" === r) { var i = this.state.offset; return a >= i.left && a <= i.left + i.width && o >= i.top && o <= i.top + i.height ? { x: a, y: o } : null } var l = this.state,
                                s = l.angleAxisMap,
                                c = l.radiusAxisMap; if (s && c) { var d = Oa(s); return py({ x: a, y: o }, d) } return null } }, { key: "parseEventsOfWrapper", value: function() { var e = this.props.children,
                                t = this.getTooltipEventType(),
                                n = co(e, Mi),
                                r = {}; return n && "axis" === t && (r = "click" === n.props.trigger ? { onClick: this.handleClick } : { onMouseEnter: this.handleMouseEnter, onMouseMove: this.handleMouseMove, onMouseLeave: this.handleMouseLeave, onTouchMove: this.handleTouchMove, onTouchStart: this.handleTouchStart, onTouchEnd: this.handleTouchEnd }), Sx(Sx({}, $a(this.props, this.handleOuterEvent)), r) } }, { key: "addListener", value: function() { lz.on(sz, this.handleReceiveSyncEvent) } }, { key: "removeListener", value: function() { lz.removeListener(sz, this.handleReceiveSyncEvent) } }, { key: "filterFormatItem", value: function(e, t, n) { for (var r = this.state.formattedGraphicalItems, a = 0, o = r.length; a < o; a++) { var i = r[a]; if (i.item === e || i.props.key === e.key || t === ao(i.item.type) && n === i.childIndex) return i } return null } }, { key: "renderClipPath", value: function() { var e = this.clipPathId,
                                t = this.state.offset,
                                n = t.left,
                                r = t.top,
                                o = t.height,
                                i = t.width; return a.createElement("defs", null, a.createElement("clipPath", { id: e }, a.createElement("rect", { x: n, y: r, height: o, width: i }))) } }, { key: "getXScales", value: function() { var e = this.state.xAxisMap; return e ? Object.entries(e).reduce((function(e, t) { var n = mx(t, 2),
                                    r = n[0],
                                    a = n[1]; return Sx(Sx({}, e), {}, Mx({}, r, a.scale)) }), {}) : null } }, { key: "getYScales", value: function() { var e = this.state.yAxisMap; return e ? Object.entries(e).reduce((function(e, t) { var n = mx(t, 2),
                                    r = n[0],
                                    a = n[1]; return Sx(Sx({}, e), {}, Mx({}, r, a.scale)) }), {}) : null } }, { key: "getXScaleByAxisId", value: function(e) { var t; return null === (t = this.state.xAxisMap) || void 0 === t || null === (t = t[e]) || void 0 === t ? void 0 : t.scale } }, { key: "getYScaleByAxisId", value: function(e) { var t; return null === (t = this.state.yAxisMap) || void 0 === t || null === (t = t[e]) || void 0 === t ? void 0 : t.scale } }, { key: "getItemByXY", value: function(e) { var t = this.state,
                                n = t.formattedGraphicalItems,
                                r = t.activeItem; if (n && n.length)
                                for (var a = 0, o = n.length; a < o; a++) { var i = n[a],
                                        l = i.props,
                                        s = i.item,
                                        c = ao(s.type); if ("Bar" === c) { var d = (l.data || []).find((function(t) { return hd(e, t) })); if (d) return { graphicalItem: i, payload: d } } else if ("RadialBar" === c) { var u = (l.data || []).find((function(t) { return py(e, t) })); if (u) return { graphicalItem: i, payload: u } } else if (Hb(i, r) || Lb(i, r) || Ib(i, r)) { var h = Rb({ graphicalItem: i, activeTooltipItem: r, itemData: s.props.data }),
                                            m = void 0 === s.props.activeIndex ? h : s.props.activeIndex; return { graphicalItem: Sx(Sx({}, i), {}, { childIndex: m }), payload: Ib(i, r) ? s.props.data[h] : i.props.data[h] } } }
                            return null } }, { key: "render", value: function() { var e = this; if (!uo(this)) return null; var t, n, r = this.props,
                                o = r.children,
                                i = r.className,
                                l = r.width,
                                s = r.height,
                                c = r.style,
                                d = r.compact,
                                u = r.title,
                                h = r.desc,
                                m = px(r, dx),
                                p = po(m, !1); if (d) return a.createElement(Dw, { state: this.state, width: this.props.width, height: this.props.height, clipPathId: this.clipPathId }, a.createElement(jo, hx({}, p, { width: l, height: s, title: u, desc: h }), this.renderClipPath(), go(o, this.renderMap)));
                            this.props.accessibilityLayer && (p.tabIndex = null !== (t = this.props.tabIndex) && void 0 !== t ? t : 0, p.role = null !== (n = this.props.role) && void 0 !== n ? n : "application", p.onKeyDown = function(t) { e.accessibilityManager.keyboardEvent(t) }, p.onFocus = function() { e.accessibilityManager.focus() }); var f = this.parseEventsOfWrapper(); return a.createElement(Dw, { state: this.state, width: this.props.width, height: this.props.height, clipPathId: this.clipPathId }, a.createElement("div", hx({ className: va("recharts-wrapper", i), style: Sx({ position: "relative", cursor: "default", width: l, height: s }, c) }, f, { ref: function(t) { e.container = t } }), a.createElement(jo, hx({}, p, { width: l, height: s, title: u, desc: h, style: Tx }), this.renderClipPath(), go(o, this.renderMap)), this.renderLegend(), this.renderTooltip())) } }], o && fx(r.prototype, o), l && fx(r, l), Object.defineProperty(r, "prototype", { writable: !1 }), t }(a.Component), Mx(t, "displayName", n), Mx(t, "defaultProps", Sx({ layout: "horizontal", stackOffset: "none", barCategoryGap: "10%", barGap: 4, margin: { top: 5, right: 5, bottom: 5, left: 5 }, reverseStackOrder: !1, syncMethod: "index" }, h)), Mx(t, "getDerivedStateFromProps", (function(e, t) { var n = e.dataKey,
                        r = e.data,
                        a = e.children,
                        o = e.width,
                        i = e.height,
                        l = e.layout,
                        s = e.stackOffset,
                        c = e.margin,
                        d = t.dataStartIndex,
                        u = t.dataEndIndex; if (void 0 === t.updateId) { var h = Dx(e); return Sx(Sx(Sx({}, h), {}, { updateId: 0 }, p(Sx(Sx({ props: e }, h), {}, { updateId: 0 }), t)), {}, { prevDataKey: n, prevData: r, prevWidth: o, prevHeight: i, prevLayout: l, prevStackOffset: s, prevMargin: c, prevChildren: a }) } if (n !== t.prevDataKey || r !== t.prevData || o !== t.prevWidth || i !== t.prevHeight || l !== t.prevLayout || s !== t.prevStackOffset || !qa(c, t.prevMargin)) { var m = Dx(e),
                            f = { chartX: t.chartX, chartY: t.chartY, isTooltipActive: t.isTooltipActive },
                            v = Sx(Sx({}, Ox(t, r, l)), {}, { updateId: t.updateId + 1 }),
                            g = Sx(Sx(Sx({}, m), f), v); return Sx(Sx(Sx({}, g), p(Sx({ props: e }, g), t)), {}, { prevDataKey: n, prevData: r, prevWidth: o, prevHeight: i, prevLayout: l, prevStackOffset: s, prevMargin: c, prevChildren: a }) } if (!fo(a, t.prevChildren)) { var y, b, w, z, x = co(a, ty),
                            A = x && null !== (y = null === (b = x.props) || void 0 === b ? void 0 : b.startIndex) && void 0 !== y ? y : d,
                            k = x && null !== (w = null === (z = x.props) || void 0 === z ? void 0 : z.endIndex) && void 0 !== w ? w : u,
                            S = A !== d || k !== u,
                            M = !Na()(r) && !S ? t.updateId : t.updateId + 1; return Sx(Sx({ updateId: M }, p(Sx(Sx({ props: e }, t), {}, { updateId: M, dataStartIndex: A, dataEndIndex: k }), t)), {}, { prevChildren: a, dataStartIndex: A, dataEndIndex: k }) } return null })), Mx(t, "renderActiveDot", (function(e, t) { var n; return n = (0, a.isValidElement)(e) ? (0, a.cloneElement)(e, t) : Ba()(e) ? e(t) : a.createElement(rs, t), a.createElement(Po, { className: "recharts-active-dot", key: t.key }, n) })), t };

        function Wx(e, t, n) { if (t < 1) return []; if (1 === t && void 0 === n) return e; for (var r = [], a = 0; a < e.length; a += t) { if (void 0 !== n && !0 !== n(e[a])) return;
                r.push(e[a]) } return r }

        function Ux(e, t, n, r, a) { if (e * t < e * r || e * t > e * a) return !1; var o = n(); return e * (t - e * o / 2 - r) >= 0 && e * (t + e * o / 2 - a) <= 0 }

        function qx(e) { return qx = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, qx(e) }

        function Gx(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Kx(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Gx(Object(n), !0).forEach((function(t) { Zx(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Gx(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Zx(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != qx(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != qx(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == qx(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Yx(e, t, n) { var r = e.tick,
                a = e.ticks,
                o = e.viewBox,
                i = e.minTickGap,
                l = e.orientation,
                s = e.interval,
