                e = (m - h) / Math.max(1, n - s + 2 * c), l && (e = Math.floor(e)), h += (m - h - e * (n - s)) * d, t = e * (1 - s), l && (h = Math.round(h), t = Math.round(t)); var p = function(e, t, n) { e = +e, t = +t, n = (a = arguments.length) < 2 ? (t = e, e = 0, 1) : a < 3 ? 1 : +n; for (var r = -1, a = 0 | Math.max(0, Math.ceil((t - e) / n)), o = new Array(a); ++r < a;) o[r] = e + r * n; return o }(n).map((function(t) { return h + e * t })); return a(u ? p.reverse() : p) } return delete n.unknown, n.domain = function(e) { return arguments.length ? (r(e), u()) : r() }, n.range = function(e) { return arguments.length ? ([o, i] = e, o = +o, i = +i, u()) : [o, i] }, n.rangeRound = function(e) { return [o, i] = e, o = +o, i = +i, l = !0, u() }, n.bandwidth = function() { return t }, n.step = function() { return e }, n.round = function(e) { return arguments.length ? (l = !!e, u()) : l }, n.padding = function(e) { return arguments.length ? (s = Math.min(1, c = +e), u()) : s }, n.paddingInner = function(e) { return arguments.length ? (s = Math.min(1, e), u()) : s }, n.paddingOuter = function(e) { return arguments.length ? (c = +e, u()) : c }, n.align = function(e) { return arguments.length ? (d = Math.max(0, Math.min(1, e)), u()) : d }, n.copy = function() { return kd(r(), [o, i]).round(l).paddingInner(s).paddingOuter(c).align(d) }, fd.apply(u(), arguments) }

        function Sd(e) { var t = e.copy; return e.padding = e.paddingOuter, delete e.paddingInner, delete e.paddingOuter, e.copy = function() { return Sd(t()) }, e }

        function Md() { return Sd(kd.apply(null, arguments).paddingInner(1)) }

        function Ed(e) { return Ed = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Ed(e) }

        function Cd(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Td(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Cd(Object(n), !0).forEach((function(t) { Hd(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Cd(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Hd(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Ed(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Ed(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Ed(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Ld = { widthCache: {}, cacheCount: 0 },
            Id = { position: "absolute", top: "-20000px", left: 0, padding: 0, margin: 0, border: "none", whiteSpace: "pre" },
            jd = "recharts_measurement_span"; var Vd = function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; if (void 0 === e || null === e || ui.isSsr) return { width: 0, height: 0 }; var n = function(e) { var t = Td({}, e); return Object.keys(t).forEach((function(e) { t[e] || delete t[e] })), t }(t),
                r = JSON.stringify({ text: e, copyStyle: n }); if (Ld.widthCache[r]) return Ld.widthCache[r]; try { var a = document.getElementById(jd);
                a || ((a = document.createElement("span")).setAttribute("id", jd), a.setAttribute("aria-hidden", "true"), document.body.appendChild(a)); var o = Td(Td({}, Id), n);
                Object.assign(a.style, o), a.textContent = "".concat(e); var i = a.getBoundingClientRect(),
                    l = { width: i.width, height: i.height }; return Ld.widthCache[r] = l, ++Ld.cacheCount > 2e3 && (Ld.cacheCount = 0, Ld.widthCache = {}), l } catch (s) { return { width: 0, height: 0 } } };

        function Od(e) { return Od = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Od(e) }

        function Rd(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return Pd(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Pd(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Pd(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function Dd(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, Fd(r.key), r) } }

        function Fd(e) { var t = function(e, t) { if ("object" != Od(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Od(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Od(t) ? t : String(t) } var Nd = /(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,
            _d = /(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,
            Bd = /^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,
            Wd = /(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,
            Ud = { cm: 96 / 2.54, mm: 96 / 25.4, pt: 96 / 72, pc: 16, in: 96, Q: 96 / 101.6, px: 1 },
            qd = Object.keys(Ud),
            Gd = "NaN"; var Kd = function() {
            function e(t, n) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.num = t, this.unit = n, this.num = t, this.unit = n, Number.isNaN(t) && (this.unit = ""), "" === n || Bd.test(n) || (this.num = NaN, this.unit = ""), qd.includes(n) && (this.num = function(e, t) { return e * Ud[t] }(t, n), this.unit = "px") } var t, n, r; return t = e, r = [{ key: "parse", value: function(t) { var n, r = Rd(null !== (n = Wd.exec(t)) && void 0 !== n ? n : [], 3),
                        a = r[1],
                        o = r[2]; return new e(parseFloat(a), null !== o && void 0 !== o ? o : "") } }], (n = [{ key: "add", value: function(t) { return this.unit !== t.unit ? new e(NaN, "") : new e(this.num + t.num, this.unit) } }, { key: "subtract", value: function(t) { return this.unit !== t.unit ? new e(NaN, "") : new e(this.num - t.num, this.unit) } }, { key: "multiply", value: function(t) { return "" !== this.unit && "" !== t.unit && this.unit !== t.unit ? new e(NaN, "") : new e(this.num * t.num, this.unit || t.unit) } }, { key: "divide", value: function(t) { return "" !== this.unit && "" !== t.unit && this.unit !== t.unit ? new e(NaN, "") : new e(this.num / t.num, this.unit || t.unit) } }, { key: "toString", value: function() { return "".concat(this.num).concat(this.unit) } }, { key: "isNaN", value: function() { return Number.isNaN(this.num) } }]) && Dd(t.prototype, n), r && Dd(t, r), Object.defineProperty(t, "prototype", { writable: !1 }), e }();

        function Zd(e) { if (e.includes(Gd)) return Gd; for (var t = e; t.includes("*") || t.includes("/");) { var n, r = Rd(null !== (n = Nd.exec(t)) && void 0 !== n ? n : [], 4),
                    a = r[1],
                    o = r[2],
                    i = r[3],
                    l = Kd.parse(null !== a && void 0 !== a ? a : ""),
                    s = Kd.parse(null !== i && void 0 !== i ? i : ""),
                    c = "*" === o ? l.multiply(s) : l.divide(s); if (c.isNaN()) return Gd;
                t = t.replace(Nd, c.toString()) } for (; t.includes("+") || /.-\d+(?:\.\d+)?/.test(t);) { var d, u = Rd(null !== (d = _d.exec(t)) && void 0 !== d ? d : [], 4),
                    h = u[1],
                    m = u[2],
                    p = u[3],
                    f = Kd.parse(null !== h && void 0 !== h ? h : ""),
                    v = Kd.parse(null !== p && void 0 !== p ? p : ""),
                    g = "+" === m ? f.add(v) : f.subtract(v); if (g.isNaN()) return Gd;
                t = t.replace(_d, g.toString()) } return t } var Yd = /\(([^()]*)\)/;

        function Xd(e) { var t = e.replace(/\s+/g, ""); return t = function(e) { for (var t = e; t.includes("(");) { var n = Rd(Yd.exec(t), 2)[1];
                    t = t.replace(Yd, Zd(n)) } return t }(t), t = Zd(t) }

        function $d(e) { var t = function(e) { try { return Xd(e) } catch (t) { return Gd } }(e.slice(5, -1)); return t === Gd ? "" : t } var Qd = ["x", "y", "lineHeight", "capHeight", "scaleToFit", "textAnchor", "verticalAnchor", "fill"],
            Jd = ["dx", "dy", "angle", "className", "breakAll"];

        function eu() { return eu = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, eu.apply(this, arguments) }

        function tu(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function nu(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return ru(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ru(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function ru(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } var au = /[ \f\n\r\t\v\u2028\u2029]+/,
            ou = function(e) { var t = e.children,
                    n = e.breakAll,
                    r = e.style; try { var a = []; return Na()(t) || (a = n ? t.toString().split("") : t.toString().split(au)), { wordsWithComputedWidth: a.map((function(e) { return { word: e, width: Vd(e, r).width } })), spaceWidth: n ? 0 : Vd("\xa0", r).width } } catch (o) { return null } },
            iu = function(e) { return [{ words: Na()(e) ? [] : e.toString().split(au) }] },
            lu = function(e) { var t = e.width,
                    n = e.scaleToFit,
                    r = e.children,
                    a = e.style,
                    o = e.breakAll,
                    i = e.maxLines; if ((t || n) && !ui.isSsr) { var l = ou({ breakAll: o, children: r, style: a }); return l ? function(e, t, n, r, a) { var o = e.maxLines,
                            i = e.children,
                            l = e.style,
                            s = e.breakAll,
                            c = Ha(o),
                            d = i,
                            u = function() { return (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []).reduce((function(e, t) { var o = t.word,
                                        i = t.width,
                                        l = e[e.length - 1]; if (l && (null == r || a || l.width + i + n < Number(r))) l.words.push(o), l.width += i + n;
                                    else { var s = { words: [o], width: i };
                                        e.push(s) } return e }), []) },
                            h = u(t); if (!c) return h; for (var m, p = function(e) { var t = d.slice(0, e),
                                    n = ou({ breakAll: s, style: l, children: t + "\u2026" }).wordsWithComputedWidth,
                                    a = u(n),
                                    i = a.length > o || function(e) { return e.reduce((function(e, t) { return e.width > t.width ? e : t })) }(a).width > Number(r); return [i, a] }, f = 0, v = d.length - 1, g = 0; f <= v && g <= d.length - 1;) { var y = Math.floor((f + v) / 2),
                                b = nu(p(y - 1), 2),
                                w = b[0],
                                z = b[1],
                                x = nu(p(y), 1)[0]; if (w || x || (f = y + 1), w && x && (v = y - 1), !w && x) { m = z; break } g++ } return m || h }({ breakAll: o, children: r, maxLines: i, style: a }, l.wordsWithComputedWidth, l.spaceWidth, t, n) : iu(r) } return iu(r) },
            su = "#808080",
            cu = function(e) { var t = e.x,
                    n = void 0 === t ? 0 : t,
                    r = e.y,
                    o = void 0 === r ? 0 : r,
                    i = e.lineHeight,
                    l = void 0 === i ? "1em" : i,
                    s = e.capHeight,
                    c = void 0 === s ? "0.71em" : s,
                    d = e.scaleToFit,
                    u = void 0 !== d && d,
                    h = e.textAnchor,
                    m = void 0 === h ? "start" : h,
                    p = e.verticalAnchor,
                    f = void 0 === p ? "end" : p,
                    v = e.fill,
                    g = void 0 === v ? su : v,
                    y = tu(e, Qd),
                    b = (0, a.useMemo)((function() { return lu({ breakAll: y.breakAll, children: y.children, maxLines: y.maxLines, scaleToFit: u, style: y.style, width: y.width }) }), [y.breakAll, y.children, y.maxLines, u, y.style, y.width]),
                    w = y.dx,
                    z = y.dy,
                    x = y.angle,
                    A = y.className,
                    k = y.breakAll,
                    S = tu(y, Jd); if (!La(n) || !La(o)) return null; var M, E = n + (Ha(w) ? w : 0),
                    C = o + (Ha(z) ? z : 0); switch (f) {
                    case "start":
                        M = $d("calc(".concat(c, ")")); break;
                    case "middle":
                        M = $d("calc(".concat((b.length - 1) / 2, " * -").concat(l, " + (").concat(c, " / 2))")); break;
                    default:
                        M = $d("calc(".concat(b.length - 1, " * -").concat(l, ")")) } var T = []; if (u) { var H = b[0].width,
                        L = y.width;
                    T.push("scale(".concat((Ha(L) ? L / H : 1) / H, ")")) } return x && T.push("rotate(".concat(x, ", ").concat(E, ", ").concat(C, ")")), T.length && (S.transform = T.join(" ")), a.createElement("text", eu({}, po(S, !0), { x: E, y: C, className: va("recharts-text", A), textAnchor: m, fill: g.includes("url") ? su : g }), b.map((function(e, t) { var n = e.words.join(k ? "" : " "); return a.createElement("tspan", { x: E, dy: 0 === t ? M : l, key: n }, n) }))) }; const du = Math.sqrt(50),
            uu = Math.sqrt(10),
            hu = Math.sqrt(2);

        function mu(e, t, n) { const r = (t - e) / Math.max(0, n),
                a = Math.floor(Math.log10(r)),
                o = r / Math.pow(10, a),
                i = o >= du ? 10 : o >= uu ? 5 : o >= hu ? 2 : 1; let l, s, c; return a < 0 ? (c = Math.pow(10, -a) / i, l = Math.round(e * c), s = Math.round(t * c), l / c < e && ++l, s / c > t && --s, c = -c) : (c = Math.pow(10, a) * i, l = Math.round(e / c), s = Math.round(t / c), l * c < e && ++l, s * c > t && --s), s < l && .5 <= n && n < 2 ? mu(e, t, 2 * n) : [l, s, c] }

        function pu(e, t, n) { if (!((n = +n) > 0)) return []; if ((e = +e) === (t = +t)) return [e]; const r = t < e,
                [a, o, i] = r ? mu(t, e, n) : mu(e, t, n); if (!(o >= a)) return []; const l = o - a + 1,
                s = new Array(l); if (r)
                if (i < 0)
                    for (let c = 0; c < l; ++c) s[c] = (o - c) / -i;
                else
                    for (let c = 0; c < l; ++c) s[c] = (o - c) * i;
            else if (i < 0)
                for (let c = 0; c < l; ++c) s[c] = (a + c) / -i;
            else
                for (let c = 0; c < l; ++c) s[c] = (a + c) * i; return s }

        function fu(e, t, n) { return mu(e = +e, t = +t, n = +n)[2] }

        function vu(e, t, n) { n = +n; const r = (t = +t) < (e = +e),
                a = r ? fu(t, e, n) : fu(e, t, n); return (r ? -1 : 1) * (a < 0 ? 1 / -a : a) }

        function gu(e, t) { return null == e || null == t ? NaN : e < t ? -1 : e > t ? 1 : e >= t ? 0 : NaN }

        function yu(e, t) { return null == e || null == t ? NaN : t < e ? -1 : t > e ? 1 : t >= e ? 0 : NaN }

        function bu(e) { let t, n, r;

            function a(e, r) { let a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                    o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : e.length; if (a < o) { if (0 !== t(r, r)) return o;
                    do { const t = a + o >>> 1;
                        n(e[t], r) < 0 ? a = t + 1 : o = t } while (a < o) } return a } return 2 !== e.length ? (t = gu, n = (t, n) => gu(e(t), n), r = (t, n) => e(t) - n) : (t = e === gu || e === yu ? e : wu, n = e, r = e), { left: a, center: function(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0; const o = a(e, t, n, (arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : e.length) - 1); return o > n && r(e[o - 1], t) > -r(e[o], t) ? o - 1 : o }, right: function(e, r) { let a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                        o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : e.length; if (a < o) { if (0 !== t(r, r)) return o;
                        do { const t = a + o >>> 1;
                            n(e[t], r) <= 0 ? a = t + 1 : o = t } while (a < o) } return a } } }

        function wu() { return 0 }

        function zu(e) { return null === e ? NaN : +e } const xu = bu(gu),
            Au = xu.right,
            ku = (xu.left, bu(zu).center, Au);

        function Su(e, t, n) { e.prototype = t.prototype = n, n.constructor = e }

        function Mu(e, t) { var n = Object.create(e.prototype); for (var r in t) n[r] = t[r]; return n }

        function Eu() {} var Cu = .7,
            Tu = 1 / Cu,
            Hu = "\\s*([+-]?\\d+)\\s*",
            Lu = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",
            Iu = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",
            ju = /^#([0-9a-f]{3,8})$/,
            Vu = new RegExp("^rgb\\(".concat(Hu, ",").concat(Hu, ",").concat(Hu, "\\)$")),
            Ou = new RegExp("^rgb\\(".concat(Iu, ",").concat(Iu, ",").concat(Iu, "\\)$")),
            Ru = new RegExp("^rgba\\(".concat(Hu, ",").concat(Hu, ",").concat(Hu, ",").concat(Lu, "\\)$")),
            Pu = new RegExp("^rgba\\(".concat(Iu, ",").concat(Iu, ",").concat(Iu, ",").concat(Lu, "\\)$")),
            Du = new RegExp("^hsl\\(".concat(Lu, ",").concat(Iu, ",").concat(Iu, "\\)$")),
            Fu = new RegExp("^hsla\\(".concat(Lu, ",").concat(Iu, ",").concat(Iu, ",").concat(Lu, "\\)$")),
            Nu = { aliceblue: 15792383, antiquewhite: 16444375, aqua: 65535, aquamarine: 8388564, azure: 15794175, beige: 16119260, bisque: 16770244, black: 0, blanchedalmond: 16772045, blue: 255, blueviolet: 9055202, brown: 10824234, burlywood: 14596231, cadetblue: 6266528, chartreuse: 8388352, chocolate: 13789470, coral: 16744272, cornflowerblue: 6591981, cornsilk: 16775388, crimson: 14423100, cyan: 65535, darkblue: 139, darkcyan: 35723, darkgoldenrod: 12092939, darkgray: 11119017, darkgreen: 25600, darkgrey: 11119017, darkkhaki: 12433259, darkmagenta: 9109643, darkolivegreen: 5597999, darkorange: 16747520, darkorchid: 10040012, darkred: 9109504, darksalmon: 15308410, darkseagreen: 9419919, darkslateblue: 4734347, darkslategray: 3100495, darkslategrey: 3100495, darkturquoise: 52945, darkviolet: 9699539, deeppink: 16716947, deepskyblue: 49151, dimgray: 6908265, dimgrey: 6908265, dodgerblue: 2003199, firebrick: 11674146, floralwhite: 16775920, forestgreen: 2263842, fuchsia: 16711935, gainsboro: 14474460, ghostwhite: 16316671, gold: 16766720, goldenrod: 14329120, gray: 8421504, green: 32768, greenyellow: 11403055, grey: 8421504, honeydew: 15794160, hotpink: 16738740, indianred: 13458524, indigo: 4915330, ivory: 16777200, khaki: 15787660, lavender: 15132410, lavenderblush: 16773365, lawngreen: 8190976, lemonchiffon: 16775885, lightblue: 11393254, lightcoral: 15761536, lightcyan: 14745599, lightgoldenrodyellow: 16448210, lightgray: 13882323, lightgreen: 9498256, lightgrey: 13882323, lightpink: 16758465, lightsalmon: 16752762, lightseagreen: 2142890, lightskyblue: 8900346, lightslategray: 7833753, lightslategrey: 7833753, lightsteelblue: 11584734, lightyellow: 16777184, lime: 65280, limegreen: 3329330, linen: 16445670, magenta: 16711935, maroon: 8388608, mediumaquamarine: 6737322, mediumblue: 205, mediumorchid: 12211667, mediumpurple: 9662683, mediumseagreen: 3978097, mediumslateblue: 8087790, mediumspringgreen: 64154, mediumturquoise: 4772300, mediumvioletred: 13047173, midnightblue: 1644912, mintcream: 16121850, mistyrose: 16770273, moccasin: 16770229, navajowhite: 16768685, navy: 128, oldlace: 16643558, olive: 8421376, olivedrab: 7048739, orange: 16753920, orangered: 16729344, orchid: 14315734, palegoldenrod: 15657130, palegreen: 10025880, paleturquoise: 11529966, palevioletred: 14381203, papayawhip: 16773077, peachpuff: 16767673, peru: 13468991, pink: 16761035, plum: 14524637, powderblue: 11591910, purple: 8388736, rebeccapurple: 6697881, red: 16711680, rosybrown: 12357519, royalblue: 4286945, saddlebrown: 9127187, salmon: 16416882, sandybrown: 16032864, seagreen: 3050327, seashell: 16774638, sienna: 10506797, silver: 12632256, skyblue: 8900331, slateblue: 6970061, slategray: 7372944, slategrey: 7372944, snow: 16775930, springgreen: 65407, steelblue: 4620980, tan: 13808780, teal: 32896, thistle: 14204888, tomato: 16737095, turquoise: 4251856, violet: 15631086, wheat: 16113331, white: 16777215, whitesmoke: 16119285, yellow: 16776960, yellowgreen: 10145074 };

        function _u() { return this.rgb().formatHex() }

        function Bu() { return this.rgb().formatRgb() }

        function Wu(e) { var t, n; return e = (e + "").trim().toLowerCase(), (t = ju.exec(e)) ? (n = t[1].length, t = parseInt(t[1], 16), 6 === n ? Uu(t) : 3 === n ? new Ku(t >> 8 & 15 | t >> 4 & 240, t >> 4 & 15 | 240 & t, (15 & t) << 4 | 15 & t, 1) : 8 === n ? qu(t >> 24 & 255, t >> 16 & 255, t >> 8 & 255, (255 & t) / 255) : 4 === n ? qu(t >> 12 & 15 | t >> 8 & 240, t >> 8 & 15 | t >> 4 & 240, t >> 4 & 15 | 240 & t, ((15 & t) << 4 | 15 & t) / 255) : null) : (t = Vu.exec(e)) ? new Ku(t[1], t[2], t[3], 1) : (t = Ou.exec(e)) ? new Ku(255 * t[1] / 100, 255 * t[2] / 100, 255 * t[3] / 100, 1) : (t = Ru.exec(e)) ? qu(t[1], t[2], t[3], t[4]) : (t = Pu.exec(e)) ? qu(255 * t[1] / 100, 255 * t[2] / 100, 255 * t[3] / 100, t[4]) : (t = Du.exec(e)) ? Ju(t[1], t[2] / 100, t[3] / 100, 1) : (t = Fu.exec(e)) ? Ju(t[1], t[2] / 100, t[3] / 100, t[4]) : Nu.hasOwnProperty(e) ? Uu(Nu[e]) : "transparent" === e ? new Ku(NaN, NaN, NaN, 0) : null }

        function Uu(e) { return new Ku(e >> 16 & 255, e >> 8 & 255, 255 & e, 1) }

        function qu(e, t, n, r) { return r <= 0 && (e = t = n = NaN), new Ku(e, t, n, r) }

        function Gu(e, t, n, r) { return 1 === arguments.length ? ((a = e) instanceof Eu || (a = Wu(a)), a ? new Ku((a = a.rgb()).r, a.g, a.b, a.opacity) : new Ku) : new Ku(e, t, n, null == r ? 1 : r); var a }

        function Ku(e, t, n, r) { this.r = +e, this.g = +t, this.b = +n, this.opacity = +r }

        function Zu() { return "#".concat(Qu(this.r)).concat(Qu(this.g)).concat(Qu(this.b)) }

        function Yu() { const e = Xu(this.opacity); return "".concat(1 === e ? "rgb(" : "rgba(").concat($u(this.r), ", ").concat($u(this.g), ", ").concat($u(this.b)).concat(1 === e ? ")" : ", ".concat(e, ")")) }

        function Xu(e) { return isNaN(e) ? 1 : Math.max(0, Math.min(1, e)) }

        function $u(e) { return Math.max(0, Math.min(255, Math.round(e) || 0)) }

        function Qu(e) { return ((e = $u(e)) < 16 ? "0" : "") + e.toString(16) }

        function Ju(e, t, n, r) { return r <= 0 ? e = t = n = NaN : n <= 0 || n >= 1 ? e = t = NaN : t <= 0 && (e = NaN), new th(e, t, n, r) }

        function eh(e) { if (e instanceof th) return new th(e.h, e.s, e.l, e.opacity); if (e instanceof Eu || (e = Wu(e)), !e) return new th; if (e instanceof th) return e; var t = (e = e.rgb()).r / 255,
                n = e.g / 255,
                r = e.b / 255,
                a = Math.min(t, n, r),
                o = Math.max(t, n, r),
                i = NaN,
                l = o - a,
                s = (o + a) / 2; return l ? (i = t === o ? (n - r) / l + 6 * (n < r) : n === o ? (r - t) / l + 2 : (t - n) / l + 4, l /= s < .5 ? o + a : 2 - o - a, i *= 60) : l = s > 0 && s < 1 ? 0 : i, new th(i, l, s, e.opacity) }

        function th(e, t, n, r) { this.h = +e, this.s = +t, this.l = +n, this.opacity = +r }

        function nh(e) { return (e = (e || 0) % 360) < 0 ? e + 360 : e }

        function rh(e) { return Math.max(0, Math.min(1, e || 0)) }

        function ah(e, t, n) { return 255 * (e < 60 ? t + (n - t) * e / 60 : e < 180 ? n : e < 240 ? t + (n - t) * (240 - e) / 60 : t) }

        function oh(e, t, n, r, a) { var o = e * e,
                i = o * e; return ((1 - 3 * e + 3 * o - i) * t + (4 - 6 * o + 3 * i) * n + (1 + 3 * e + 3 * o - 3 * i) * r + i * a) / 6 } Su(Eu, Wu, { copy(e) { return Object.assign(new this.constructor, this, e) }, displayable() { return this.rgb().displayable() }, hex: _u, formatHex: _u, formatHex8: function() { return this.rgb().formatHex8() }, formatHsl: function() { return eh(this).formatHsl() }, formatRgb: Bu, toString: Bu }), Su(Ku, Gu, Mu(Eu, { brighter(e) { return e = null == e ? Tu : Math.pow(Tu, e), new Ku(this.r * e, this.g * e, this.b * e, this.opacity) }, darker(e) { return e = null == e ? Cu : Math.pow(Cu, e), new Ku(this.r * e, this.g * e, this.b * e, this.opacity) }, rgb() { return this }, clamp() { return new Ku($u(this.r), $u(this.g), $u(this.b), Xu(this.opacity)) }, displayable() { return -.5 <= this.r && this.r < 255.5 && -.5 <= this.g && this.g < 255.5 && -.5 <= this.b && this.b < 255.5 && 0 <= this.opacity && this.opacity <= 1 }, hex: Zu, formatHex: Zu, formatHex8: function() { return "#".concat(Qu(this.r)).concat(Qu(this.g)).concat(Qu(this.b)).concat(Qu(255 * (isNaN(this.opacity) ? 1 : this.opacity))) }, formatRgb: Yu, toString: Yu })), Su(th, (function(e, t, n, r) { return 1 === arguments.length ? eh(e) : new th(e, t, n, null == r ? 1 : r) }), Mu(Eu, { brighter(e) { return e = null == e ? Tu : Math.pow(Tu, e), new th(this.h, this.s, this.l * e, this.opacity) }, darker(e) { return e = null == e ? Cu : Math.pow(Cu, e), new th(this.h, this.s, this.l * e, this.opacity) }, rgb() { var e = this.h % 360 + 360 * (this.h < 0),
                    t = isNaN(e) || isNaN(this.s) ? 0 : this.s,
                    n = this.l,
                    r = n + (n < .5 ? n : 1 - n) * t,
                    a = 2 * n - r; return new Ku(ah(e >= 240 ? e - 240 : e + 120, a, r), ah(e, a, r), ah(e < 120 ? e + 240 : e - 120, a, r), this.opacity) }, clamp() { return new th(nh(this.h), rh(this.s), rh(this.l), Xu(this.opacity)) }, displayable() { return (0 <= this.s && this.s <= 1 || isNaN(this.s)) && 0 <= this.l && this.l <= 1 && 0 <= this.opacity && this.opacity <= 1 }, formatHsl() { const e = Xu(this.opacity); return "".concat(1 === e ? "hsl(" : "hsla(").concat(nh(this.h), ", ").concat(100 * rh(this.s), "%, ").concat(100 * rh(this.l), "%").concat(1 === e ? ")" : ", ".concat(e, ")")) } })); const ih = e => () => e;

        function lh(e, t) { return function(n) { return e + n * t } }

        function sh(e) { return 1 === (e = +e) ? ch : function(t, n) { return n - t ? function(e, t, n) { return e = Math.pow(e, n), t = Math.pow(t, n) - e, n = 1 / n,
                        function(r) { return Math.pow(e + r * t, n) } }(t, n, e) : ih(isNaN(t) ? n : t) } }

        function ch(e, t) { var n = t - e; return n ? lh(e, n) : ih(isNaN(e) ? t : e) } const dh = function e(t) { var n = sh(t);

            function r(e, t) { var r = n((e = Gu(e)).r, (t = Gu(t)).r),
                    a = n(e.g, t.g),
                    o = n(e.b, t.b),
                    i = ch(e.opacity, t.opacity); return function(t) { return e.r = r(t), e.g = a(t), e.b = o(t), e.opacity = i(t), e + "" } } return r.gamma = e, r }(1);

        function uh(e) { return function(t) { var n, r, a = t.length,
                    o = new Array(a),
                    i = new Array(a),
                    l = new Array(a); for (n = 0; n < a; ++n) r = Gu(t[n]), o[n] = r.r || 0, i[n] = r.g || 0, l[n] = r.b || 0; return o = e(o), i = e(i), l = e(l), r.opacity = 1,
                    function(e) { return r.r = o(e), r.g = i(e), r.b = l(e), r + "" } } } uh((function(e) { var t = e.length - 1; return function(n) { var r = n <= 0 ? n = 0 : n >= 1 ? (n = 1, t - 1) : Math.floor(n * t),
                    a = e[r],
                    o = e[r + 1],
                    i = r > 0 ? e[r - 1] : 2 * a - o,
                    l = r < t - 1 ? e[r + 2] : 2 * o - a; return oh((n - r / t) * t, i, a, o, l) } })), uh((function(e) { var t = e.length; return function(n) { var r = Math.floor(((n %= 1) < 0 ? ++n : n) * t),
                    a = e[(r + t - 1) % t],
                    o = e[r % t],
                    i = e[(r + 1) % t],
                    l = e[(r + 2) % t]; return oh((n - r / t) * t, a, o, i, l) } }));

        function hh(e, t) { var n, r = t ? t.length : 0,
                a = e ? Math.min(r, e.length) : 0,
                o = new Array(a),
                i = new Array(r); for (n = 0; n < a; ++n) o[n] = wh(e[n], t[n]); for (; n < r; ++n) i[n] = t[n]; return function(e) { for (n = 0; n < a; ++n) i[n] = o[n](e); return i } }

        function mh(e, t) { var n = new Date; return e = +e, t = +t,
                function(r) { return n.setTime(e * (1 - r) + t * r), n } }

        function ph(e, t) { return e = +e, t = +t,
                function(n) { return e * (1 - n) + t * n } }

        function fh(e, t) { var n, r = {},
                a = {}; for (n in null !== e && "object" === typeof e || (e = {}), null !== t && "object" === typeof t || (t = {}), t) n in e ? r[n] = wh(e[n], t[n]) : a[n] = t[n]; return function(e) { for (n in r) a[n] = r[n](e); return a } } var vh = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,
            gh = new RegExp(vh.source, "g");

        function yh(e, t) { var n, r, a, o = vh.lastIndex = gh.lastIndex = 0,
                i = -1,
                l = [],
                s = []; for (e += "", t += "";
                (n = vh.exec(e)) && (r = gh.exec(t));)(a = r.index) > o && (a = t.slice(o, a), l[i] ? l[i] += a : l[++i] = a), (n = n[0]) === (r = r[0]) ? l[i] ? l[i] += r : l[++i] = r : (l[++i] = null, s.push({ i: i, x: ph(n, r) })), o = gh.lastIndex; return o < t.length && (a = t.slice(o), l[i] ? l[i] += a : l[++i] = a), l.length < 2 ? s[0] ? function(e) { return function(t) { return e(t) + "" } }(s[0].x) : function(e) { return function() { return e } }(t) : (t = s.length, function(e) { for (var n, r = 0; r < t; ++r) l[(n = s[r]).i] = n.x(e); return l.join("") }) }

        function bh(e, t) { t || (t = []); var n, r = e ? Math.min(t.length, e.length) : 0,
                a = t.slice(); return function(o) { for (n = 0; n < r; ++n) a[n] = e[n] * (1 - o) + t[n] * o; return a } }

        function wh(e, t) { var n, r, a = typeof t; return null == t || "boolean" === a ? ih(t) : ("number" === a ? ph : "string" === a ? (n = Wu(t)) ? (t = n, dh) : yh : t instanceof Wu ? dh : t instanceof Date ? mh : (r = t, !ArrayBuffer.isView(r) || r instanceof DataView ? Array.isArray(t) ? hh : "function" !== typeof t.valueOf && "function" !== typeof t.toString || isNaN(t) ? fh : ph : bh))(e, t) }

        function zh(e, t) { return e = +e, t = +t,
                function(n) { return Math.round(e * (1 - n) + t * n) } }

        function xh(e) { return +e } var Ah = [0, 1];

        function kh(e) { return e }

        function Sh(e, t) { return (t -= e = +e) ? function(n) { return (n - e) / t } : (n = isNaN(t) ? NaN : .5, function() { return n }); var n }

        function Mh(e, t, n) { var r = e[0],
                a = e[1],
                o = t[0],
                i = t[1]; return a < r ? (r = Sh(a, r), o = n(i, o)) : (r = Sh(r, a), o = n(o, i)),
                function(e) { return o(r(e)) } }

        function Eh(e, t, n) { var r = Math.min(e.length, t.length) - 1,
                a = new Array(r),
                o = new Array(r),
                i = -1; for (e[r] < e[0] && (e = e.slice().reverse(), t = t.slice().reverse()); ++i < r;) a[i] = Sh(e[i], e[i + 1]), o[i] = n(t[i], t[i + 1]); return function(t) { var n = ku(e, t, 1, r) - 1; return o[n](a[n](t)) } }

        function Ch(e, t) { return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown()) }

        function Th() { var e, t, n, r, a, o, i = Ah,
                l = Ah,
                s = wh,
                c = kh;

            function d() { var e = Math.min(i.length, l.length); return c !== kh && (c = function(e, t) { var n; return e > t && (n = e, e = t, t = n),
                        function(n) { return Math.max(e, Math.min(t, n)) } }(i[0], i[e - 1])), r = e > 2 ? Eh : Mh, a = o = null, u }

            function u(t) { return null == t || isNaN(t = +t) ? n : (a || (a = r(i.map(e), l, s)))(e(c(t))) } return u.invert = function(n) { return c(t((o || (o = r(l, i.map(e), ph)))(n))) }, u.domain = function(e) { return arguments.length ? (i = Array.from(e, xh), d()) : i.slice() }, u.range = function(e) { return arguments.length ? (l = Array.from(e), d()) : l.slice() }, u.rangeRound = function(e) { return l = Array.from(e), s = zh, d() }, u.clamp = function(e) { return arguments.length ? (c = !!e || kh, d()) : c !== kh }, u.interpolate = function(e) { return arguments.length ? (s = e, d()) : s }, u.unknown = function(e) { return arguments.length ? (n = e, u) : n },
                function(n, r) { return e = n, t = r, d() } }

        function Hh() { return Th()(kh, kh) } var Lh, Ih = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;

        function jh(e) { if (!(t = Ih.exec(e))) throw new Error("invalid format: " + e); var t; return new Vh({ fill: t[1], align: t[2], sign: t[3], symbol: t[4], zero: t[5], width: t[6], comma: t[7], precision: t[8] && t[8].slice(1), trim: t[9], type: t[10] }) }

        function Vh(e) { this.fill = void 0 === e.fill ? " " : e.fill + "", this.align = void 0 === e.align ? ">" : e.align + "", this.sign = void 0 === e.sign ? "-" : e.sign + "", this.symbol = void 0 === e.symbol ? "" : e.symbol + "", this.zero = !!e.zero, this.width = void 0 === e.width ? void 0 : +e.width, this.comma = !!e.comma, this.precision = void 0 === e.precision ? void 0 : +e.precision, this.trim = !!e.trim, this.type = void 0 === e.type ? "" : e.type + "" }

        function Oh(e, t) { if ((n = (e = t ? e.toExponential(t - 1) : e.toExponential()).indexOf("e")) < 0) return null; var n, r = e.slice(0, n); return [r.length > 1 ? r[0] + r.slice(2) : r, +e.slice(n + 1)] }

        function Rh(e) { return (e = Oh(Math.abs(e))) ? e[1] : NaN }

        function Ph(e, t) { var n = Oh(e, t); if (!n) return e + ""; var r = n[0],
                a = n[1]; return a < 0 ? "0." + new Array(-a).join("0") + r : r.length > a + 1 ? r.slice(0, a + 1) + "." + r.slice(a + 1) : r + new Array(a - r.length + 2).join("0") } jh.prototype = Vh.prototype, Vh.prototype.toString = function() { return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (void 0 === this.width ? "" : Math.max(1, 0 | this.width)) + (this.comma ? "," : "") + (void 0 === this.precision ? "" : "." + Math.max(0, 0 | this.precision)) + (this.trim ? "~" : "") + this.type }; const Dh = { "%": (e, t) => (100 * e).toFixed(t), b: e => Math.round(e).toString(2), c: e => e + "", d: function(e) { return Math.abs(e = Math.round(e)) >= 1e21 ? e.toLocaleString("en").replace(/,/g, "") : e.toString(10) }, e: (e, t) => e.toExponential(t), f: (e, t) => e.toFixed(t), g: (e, t) => e.toPrecision(t), o: e => Math.round(e).toString(8), p: (e, t) => Ph(100 * e, t), r: Ph, s: function(e, t) { var n = Oh(e, t); if (!n) return e + ""; var r = n[0],
                    a = n[1],
                    o = a - (Lh = 3 * Math.max(-8, Math.min(8, Math.floor(a / 3)))) + 1,
                    i = r.length; return o === i ? r : o > i ? r + new Array(o - i + 1).join("0") : o > 0 ? r.slice(0, o) + "." + r.slice(o) : "0." + new Array(1 - o).join("0") + Oh(e, Math.max(0, t + o - 1))[0] }, X: e => Math.round(e).toString(16).toUpperCase(), x: e => Math.round(e).toString(16) };

        function Fh(e) { return e } var Nh, _h, Bh, Wh = Array.prototype.map,
            Uh = ["y", "z", "a", "f", "p", "n", "\xb5", "m", "", "k", "M", "G", "T", "P", "E", "Z", "Y"];

        function qh(e) { var t, n, r = void 0 === e.grouping || void 0 === e.thousands ? Fh : (t = Wh.call(e.grouping, Number), n = e.thousands + "", function(e, r) { for (var a = e.length, o = [], i = 0, l = t[0], s = 0; a > 0 && l > 0 && (s + l + 1 > r && (l = Math.max(1, r - s)), o.push(e.substring(a -= l, a + l)), !((s += l + 1) > r));) l = t[i = (i + 1) % t.length]; return o.reverse().join(n) }),
                a = void 0 === e.currency ? "" : e.currency[0] + "",
                o = void 0 === e.currency ? "" : e.currency[1] + "",
                i = void 0 === e.decimal ? "." : e.decimal + "",
                l = void 0 === e.numerals ? Fh : function(e) { return function(t) { return t.replace(/[0-9]/g, (function(t) { return e[+t] })) } }(Wh.call(e.numerals, String)),
                s = void 0 === e.percent ? "%" : e.percent + "",
                c = void 0 === e.minus ? "\u2212" : e.minus + "",
                d = void 0 === e.nan ? "NaN" : e.nan + "";

            function u(e) { var t = (e = jh(e)).fill,
                    n = e.align,
                    u = e.sign,
                    h = e.symbol,
                    m = e.zero,
                    p = e.width,
                    f = e.comma,
                    v = e.precision,
                    g = e.trim,
                    y = e.type; "n" === y ? (f = !0, y = "g") : Dh[y] || (void 0 === v && (v = 12), g = !0, y = "g"), (m || "0" === t && "=" === n) && (m = !0, t = "0", n = "="); var b = "$" === h ? a : "#" === h && /[boxX]/.test(y) ? "0" + y.toLowerCase() : "",
                    w = "$" === h ? o : /[%p]/.test(y) ? s : "",
                    z = Dh[y],
                    x = /[defgprs%]/.test(y);

                function A(e) { var a, o, s, h = b,
                        A = w; if ("c" === y) A = z(e) + A, e = "";
                    else { var k = (e = +e) < 0 || 1 / e < 0; if (e = isNaN(e) ? d : z(Math.abs(e), v), g && (e = function(e) { e: for (var t, n = e.length, r = 1, a = -1; r < n; ++r) switch (e[r]) {
                                    case ".":
                                        a = t = r; break;
                                    case "0":
                                        0 === a && (a = r), t = r; break;
                                    default:
                                        if (!+e[r]) break e;
                                        a > 0 && (a = 0) }
                                return a > 0 ? e.slice(0, a) + e.slice(t + 1) : e }(e)), k && 0 === +e && "+" !== u && (k = !1), h = (k ? "(" === u ? u : c : "-" === u || "(" === u ? "" : u) + h, A = ("s" === y ? Uh[8 + Lh / 3] : "") + A + (k && "(" === u ? ")" : ""), x)
                            for (a = -1, o = e.length; ++a < o;)
                                if (48 > (s = e.charCodeAt(a)) || s > 57) { A = (46 === s ? i + e.slice(a + 1) : e.slice(a)) + A, e = e.slice(0, a); break } } f && !m && (e = r(e, 1 / 0)); var S = h.length + e.length + A.length,
                        M = S < p ? new Array(p - S + 1).join(t) : ""; switch (f && m && (e = r(M + e, M.length ? p - A.length : 1 / 0), M = ""), n) {
                        case "<":
                            e = h + e + A + M; break;
                        case "=":
                            e = h + M + e + A; break;
                        case "^":
                            e = M.slice(0, S = M.length >> 1) + h + e + A + M.slice(S); break;
                        default:
                            e = M + h + e + A } return l(e) } return v = void 0 === v ? 6 : /[gprs]/.test(y) ? Math.max(1, Math.min(21, v)) : Math.max(0, Math.min(20, v)), A.toString = function() { return e + "" }, A } return { format: u, formatPrefix: function(e, t) { var n = u(((e = jh(e)).type = "f", e)),
                        r = 3 * Math.max(-8, Math.min(8, Math.floor(Rh(t) / 3))),
                        a = Math.pow(10, -r),
                        o = Uh[8 + r / 3]; return function(e) { return n(a * e) + o } } } }

        function Gh(e, t, n, r) { var a, o = vu(e, t, n); switch ((r = jh(null == r ? ",f" : r)).type) {
                case "s":
                    var i = Math.max(Math.abs(e), Math.abs(t)); return null != r.precision || isNaN(a = function(e, t) { return Math.max(0, 3 * Math.max(-8, Math.min(8, Math.floor(Rh(t) / 3))) - Rh(Math.abs(e))) }(o, i)) || (r.precision = a), Bh(r, i);
                case "":
                case "e":
                case "g":
                case "p":
                case "r":
                    null != r.precision || isNaN(a = function(e, t) { return e = Math.abs(e), t = Math.abs(t) - e, Math.max(0, Rh(t) - Rh(e)) + 1 }(o, Math.max(Math.abs(e), Math.abs(t)))) || (r.precision = a - ("e" === r.type)); break;
                case "f":
                case "%":
                    null != r.precision || isNaN(a = function(e) { return Math.max(0, -Rh(Math.abs(e))) }(o)) || (r.precision = a - 2 * ("%" === r.type)) } return _h(r) }

        function Kh(e) { var t = e.domain; return e.ticks = function(e) { var n = t(); return pu(n[0], n[n.length - 1], null == e ? 10 : e) }, e.tickFormat = function(e, n) { var r = t(); return Gh(r[0], r[r.length - 1], null == e ? 10 : e, n) }, e.nice = function(n) { null == n && (n = 10); var r, a, o = t(),
                    i = 0,
                    l = o.length - 1,
                    s = o[i],
                    c = o[l],
                    d = 10; for (c < s && (a = s, s = c, c = a, a = i, i = l, l = a); d-- > 0;) { if ((a = fu(s, c, n)) === r) return o[i] = s, o[l] = c, t(o); if (a > 0) s = Math.floor(s / a) * a, c = Math.ceil(c / a) * a;
                    else { if (!(a < 0)) break;
                        s = Math.ceil(s * a) / a, c = Math.floor(c * a) / a } r = a } return e }, e }

        function Zh() { var e = Hh(); return e.copy = function() { return Ch(e, Zh()) }, fd.apply(e, arguments), Kh(e) }

        function Yh(e) { var t;

            function n(e) { return null == e || isNaN(e = +e) ? t : e } return n.invert = n, n.domain = n.range = function(t) { return arguments.length ? (e = Array.from(t, xh), n) : e.slice() }, n.unknown = function(e) { return arguments.length ? (t = e, n) : t }, n.copy = function() { return Yh(e).unknown(t) }, e = arguments.length ? Array.from(e, xh) : [0, 1], Kh(n) }

        function Xh(e, t) { var n, r = 0,
                a = (e = e.slice()).length - 1,
                o = e[r],
                i = e[a]; return i < o && (n = r, r = a, a = n, n = o, o = i, i = n), e[r] = t.floor(o), e[a] = t.ceil(i), e }

        function $h(e) { return Math.log(e) }

        function Qh(e) { return Math.exp(e) }

        function Jh(e) { return -Math.log(-e) }

        function em(e) { return -Math.exp(-e) }

        function tm(e) { return isFinite(e) ? +("1e" + e) : e < 0 ? 0 : e }

        function nm(e) { return (t, n) => -e(-t, n) }

        function rm(e) { const t = e($h, Qh),
                n = t.domain; let r, a, o = 10;

            function i() { return r = function(e) { return e === Math.E ? Math.log : 10 === e && Math.log10 || 2 === e && Math.log2 || (e = Math.log(e), t => Math.log(t) / e) }(o), a = function(e) { return 10 === e ? tm : e === Math.E ? Math.exp : t => Math.pow(e, t) }(o), n()[0] < 0 ? (r = nm(r), a = nm(a), e(Jh, em)) : e($h, Qh), t } return t.base = function(e) { return arguments.length ? (o = +e, i()) : o }, t.domain = function(e) { return arguments.length ? (n(e), i()) : n() }, t.ticks = e => { const t = n(); let i = t[0],
                    l = t[t.length - 1]; const s = l < i;
                s && ([i, l] = [l, i]); let c, d, u = r(i),
                    h = r(l); const m = null == e ? 10 : +e; let p = []; if (!(o % 1) && h - u < m) { if (u = Math.floor(u), h = Math.ceil(h), i > 0) { for (; u <= h; ++u)
                            for (c = 1; c < o; ++c)
                                if (d = u < 0 ? c / a(-u) : c * a(u), !(d < i)) { if (d > l) break;
                                    p.push(d) } } else
                        for (; u <= h; ++u)
                            for (c = o - 1; c >= 1; --c)
                                if (d = u > 0 ? c / a(-u) : c * a(u), !(d < i)) { if (d > l) break;
                                    p.push(d) } 2 * p.length < m && (p = pu(i, l, m)) } else p = pu(u, h, Math.min(h - u, m)).map(a); return s ? p.reverse() : p }, t.tickFormat = (e, n) => { if (null == e && (e = 10), null == n && (n = 10 === o ? "s" : ","), "function" !== typeof n && (o % 1 || null != (n = jh(n)).precision || (n.trim = !0), n = _h(n)), e === 1 / 0) return n; const i = Math.max(1, o * e / t.ticks().length); return e => { let t = e / a(Math.round(r(e))); return t * o < o - .5 && (t *= o), t <= i ? n(e) : "" } }, t.nice = () => n(Xh(n(), { floor: e => a(Math.floor(r(e))), ceil: e => a(Math.ceil(r(e))) })), t }

        function am() { const e = rm(Th()).domain([1, 10]); return e.copy = () => Ch(e, am()).base(e.base()), fd.apply(e, arguments), e }

        function om(e) { return function(t) { return Math.sign(t) * Math.log1p(Math.abs(t / e)) } }

        function im(e) { return function(t) { return Math.sign(t) * Math.expm1(Math.abs(t)) * e } }

        function lm(e) { var t = 1,
                n = e(om(t), im(t)); return n.constant = function(n) { return arguments.length ? e(om(t = +n), im(t)) : t }, Kh(n) }

        function sm() { var e = lm(Th()); return e.copy = function() { return Ch(e, sm()).constant(e.constant()) }, fd.apply(e, arguments) }

        function cm(e) { return function(t) { return t < 0 ? -Math.pow(-t, e) : Math.pow(t, e) } }

        function dm(e) { return e < 0 ? -Math.sqrt(-e) : Math.sqrt(e) }

        function um(e) { return e < 0 ? -e * e : e * e }

        function hm(e) { var t = e(kh, kh),
                n = 1; return t.exponent = function(t) { return arguments.length ? 1 === (n = +t) ? e(kh, kh) : .5 === n ? e(dm, um) : e(cm(n), cm(1 / n)) : n }, Kh(t) }

        function mm() { var e = hm(Th()); return e.copy = function() { return Ch(e, mm()).exponent(e.exponent()) }, fd.apply(e, arguments), e }

        function pm() { return mm.apply(null, arguments).exponent(.5) }

        function fm(e) { return Math.sign(e) * e * e }

        function vm() { var e, t = Hh(),
                n = [0, 1],
                r = !1;

            function a(n) { var a = function(e) { return Math.sign(e) * Math.sqrt(Math.abs(e)) }(t(n)); return isNaN(a) ? e : r ? Math.round(a) : a } return a.invert = function(e) { return t.invert(fm(e)) }, a.domain = function(e) { return arguments.length ? (t.domain(e), a) : t.domain() }, a.range = function(e) { return arguments.length ? (t.range((n = Array.from(e, xh)).map(fm)), a) : n.slice() }, a.rangeRound = function(e) { return a.range(e).round(!0) }, a.round = function(e) { return arguments.length ? (r = !!e, a) : r }, a.clamp = function(e) { return arguments.length ? (t.clamp(e), a) : t.clamp() }, a.unknown = function(t) { return arguments.length ? (e = t, a) : e }, a.copy = function() { return vm(t.domain(), n).round(r).clamp(t.clamp()).unknown(e) }, fd.apply(a, arguments), Kh(a) }

        function gm(e, t) { let n; if (void 0 === t)
                for (const r of e) null != r && (n < r || void 0 === n && r >= r) && (n = r);
            else { let r = -1; for (let a of e) null != (a = t(a, ++r, e)) && (n < a || void 0 === n && a >= a) && (n = a) } return n }

        function ym(e, t) { let n; if (void 0 === t)
                for (const r of e) null != r && (n > r || void 0 === n && r >= r) && (n = r);
            else { let r = -1; for (let a of e) null != (a = t(a, ++r, e)) && (n > a || void 0 === n && a >= a) && (n = a) } return n }

        function bm() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : gu; if (e === gu) return wm; if ("function" !== typeof e) throw new TypeError("compare is not a function"); return (t, n) => { const r = e(t, n); return r || 0 === r ? r : (0 === e(n, n)) - (0 === e(t, t)) } }

        function wm(e, t) { return (null == e || !(e >= e)) - (null == t || !(t >= t)) || (e < t ? -1 : e > t ? 1 : 0) }

        function zm(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 1 / 0,
                a = arguments.length > 4 ? arguments[4] : void 0; if (t = Math.floor(t), n = Math.floor(Math.max(0, n)), r = Math.floor(Math.min(e.length - 1, r)), !(n <= t && t <= r)) return e; for (a = void 0 === a ? wm : bm(a); r > n;) { if (r - n > 600) { const o = r - n + 1,
                        i = t - n + 1,
                        l = Math.log(o),
                        s = .5 * Math.exp(2 * l / 3),
                        c = .5 * Math.sqrt(l * s * (o - s) / o) * (i - o / 2 < 0 ? -1 : 1);
                    zm(e, t, Math.max(n, Math.floor(t - i * s / o + c)), Math.min(r, Math.floor(t + (o - i) * s / o + c)), a) } const o = e[t]; let i = n,
                    l = r; for (xm(e, n, t), a(e[r], o) > 0 && xm(e, n, r); i < l;) { for (xm(e, i, l), ++i, --l; a(e[i], o) < 0;) ++i; for (; a(e[l], o) > 0;) --l } 0 === a(e[n], o) ? xm(e, n, l) : (++l, xm(e, l, r)), l <= t && (n = l + 1), t <= l && (r = l - 1) } return e }

        function xm(e, t, n) { const r = e[t];
            e[t] = e[n], e[n] = r }

        function Am(e, t, n) { if (e = Float64Array.from(function*(e, t) { if (void 0 === t)
                        for (let n of e) null != n && (n = +n) >= n && (yield n);
                    else { let n = -1; for (let r of e) null != (r = t(r, ++n, e)) && (r = +r) >= r && (yield r) } }(e, n)), (r = e.length) && !isNaN(t = +t)) { if (t <= 0 || r < 2) return ym(e); if (t >= 1) return gm(e); var r, a = (r - 1) * t,
                    o = Math.floor(a),
                    i = gm(zm(e, o).subarray(0, o + 1)); return i + (ym(e.subarray(o + 1)) - i) * (a - o) } }

        function km(e, t) { let n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : zu; if ((r = e.length) && !isNaN(t = +t)) { if (t <= 0 || r < 2) return +n(e[0], 0, e); if (t >= 1) return +n(e[r - 1], r - 1, e); var r, a = (r - 1) * t,
                    o = Math.floor(a),
                    i = +n(e[o], o, e); return i + (+n(e[o + 1], o + 1, e) - i) * (a - o) } }

        function Sm() { var e, t = [],
                n = [],
                r = [];

            function a() { var e = 0,
                    a = Math.max(1, n.length); for (r = new Array(a - 1); ++e < a;) r[e - 1] = km(t, e / a); return o }

            function o(t) { return null == t || isNaN(t = +t) ? e : n[ku(r, t)] } return o.invertExtent = function(e) { var a = n.indexOf(e); return a < 0 ? [NaN, NaN] : [a > 0 ? r[a - 1] : t[0], a < r.length ? r[a] : t[t.length - 1]] }, o.domain = function(e) { if (!arguments.length) return t.slice();
                t = []; for (let n of e) null == n || isNaN(n = +n) || t.push(n); return t.sort(gu), a() }, o.range = function(e) { return arguments.length ? (n = Array.from(e), a()) : n.slice() }, o.unknown = function(t) { return arguments.length ? (e = t, o) : e }, o.quantiles = function() { return r.slice() }, o.copy = function() { return Sm().domain(t).range(n).unknown(e) }, fd.apply(o, arguments) }

        function Mm() { var e, t = 0,
                n = 1,
                r = 1,
                a = [.5],
                o = [0, 1];

            function i(t) { return null != t && t <= t ? o[ku(a, t, 0, r)] : e }

            function l() { var e = -1; for (a = new Array(r); ++e < r;) a[e] = ((e + 1) * n - (e - r) * t) / (r + 1); return i } return i.domain = function(e) { return arguments.length ? ([t, n] = e, t = +t, n = +n, l()) : [t, n] }, i.range = function(e) { return arguments.length ? (r = (o = Array.from(e)).length - 1, l()) : o.slice() }, i.invertExtent = function(e) { var i = o.indexOf(e); return i < 0 ? [NaN, NaN] : i < 1 ? [t, a[0]] : i >= r ? [a[r - 1], n] : [a[i - 1], a[i]] }, i.unknown = function(t) { return arguments.length ? (e = t, i) : i }, i.thresholds = function() { return a.slice() }, i.copy = function() { return Mm().domain([t, n]).range(o).unknown(e) }, fd.apply(Kh(i), arguments) }

        function Em() { var e, t = [.5],
                n = [0, 1],
                r = 1;

            function a(a) { return null != a && a <= a ? n[ku(t, a, 0, r)] : e } return a.domain = function(e) { return arguments.length ? (t = Array.from(e), r = Math.min(t.length, n.length - 1), a) : t.slice() }, a.range = function(e) { return arguments.length ? (n = Array.from(e), r = Math.min(t.length, n.length - 1), a) : n.slice() }, a.invertExtent = function(e) { var r = n.indexOf(e); return [t[r - 1], t[r]] }, a.unknown = function(t) { return arguments.length ? (e = t, a) : e }, a.copy = function() { return Em().domain(t).range(n).unknown(e) }, fd.apply(a, arguments) } Nh = qh({ thousands: ",", grouping: [3], currency: ["$", ""] }), _h = Nh.format, Bh = Nh.formatPrefix; const Cm = 1e3,
            Tm = 6e4,
            Hm = 36e5,
            Lm = 864e5,
            Im = 6048e5,
            jm = 2592e6,
            Vm = 31536e6,
            Om = new Date,
            Rm = new Date;

        function Pm(e, t, n, r) {
            function a(t) { return e(t = 0 === arguments.length ? new Date : new Date(+t)), t } return a.floor = t => (e(t = new Date(+t)), t), a.ceil = n => (e(n = new Date(n - 1)), t(n, 1), e(n), n), a.round = e => { const t = a(e),
                    n = a.ceil(e); return e - t < n - e ? t : n }, a.offset = (e, n) => (t(e = new Date(+e), null == n ? 1 : Math.floor(n)), e), a.range = (n, r, o) => { const i = []; if (n = a.ceil(n), o = null == o ? 1 : Math.floor(o), !(n < r) || !(o > 0)) return i; let l;
                do { i.push(l = new Date(+n)), t(n, o), e(n) } while (l < n && n < r); return i }, a.filter = n => Pm((t => { if (t >= t)
                    for (; e(t), !n(t);) t.setTime(t - 1) }), ((e, r) => { if (e >= e)
                    if (r < 0)
                        for (; ++r <= 0;)
                            for (; t(e, -1), !n(e););
                    else
                        for (; --r >= 0;)
                            for (; t(e, 1), !n(e);); })), n && (a.count = (t, r) => (Om.setTime(+t), Rm.setTime(+r), e(Om), e(Rm), Math.floor(n(Om, Rm))), a.every = e => (e = Math.floor(e), isFinite(e) && e > 0 ? e > 1 ? a.filter(r ? t => r(t) % e === 0 : t => a.count(0, t) % e === 0) : a : null)), a } const Dm = Pm((() => {}), ((e, t) => { e.setTime(+e + t) }), ((e, t) => t - e));
        Dm.every = e => (e = Math.floor(e), isFinite(e) && e > 0 ? e > 1 ? Pm((t => { t.setTime(Math.floor(t / e) * e) }), ((t, n) => { t.setTime(+t + n * e) }), ((t, n) => (n - t) / e)) : Dm : null);
        Dm.range; const Fm = Pm((e => { e.setTime(e - e.getMilliseconds()) }), ((e, t) => { e.setTime(+e + t * Cm) }), ((e, t) => (t - e) / Cm), (e => e.getUTCSeconds())),
            Nm = (Fm.range, Pm((e => { e.setTime(e - e.getMilliseconds() - e.getSeconds() * Cm) }), ((e, t) => { e.setTime(+e + t * Tm) }), ((e, t) => (t - e) / Tm), (e => e.getMinutes()))),
            _m = (Nm.range, Pm((e => { e.setUTCSeconds(0, 0) }), ((e, t) => { e.setTime(+e + t * Tm) }), ((e, t) => (t - e) / Tm), (e => e.getUTCMinutes()))),
            Bm = (_m.range, Pm((e => { e.setTime(e - e.getMilliseconds() - e.getSeconds() * Cm - e.getMinutes() * Tm) }), ((e, t) => { e.setTime(+e + t * Hm) }), ((e, t) => (t - e) / Hm), (e => e.getHours()))),
            Wm = (Bm.range, Pm((e => { e.setUTCMinutes(0, 0, 0) }), ((e, t) => { e.setTime(+e + t * Hm) }), ((e, t) => (t - e) / Hm), (e => e.getUTCHours()))),
            Um = (Wm.range, Pm((e => e.setHours(0, 0, 0, 0)), ((e, t) => e.setDate(e.getDate() + t)), ((e, t) => (t - e - (t.getTimezoneOffset() - e.getTimezoneOffset()) * Tm) / Lm), (e => e.getDate() - 1))),
            qm = (Um.range, Pm((e => { e.setUTCHours(0, 0, 0, 0) }), ((e, t) => { e.setUTCDate(e.getUTCDate() + t) }), ((e, t) => (t - e) / Lm), (e => e.getUTCDate() - 1))),
            Gm = (qm.range, Pm((e => { e.setUTCHours(0, 0, 0, 0) }), ((e, t) => { e.setUTCDate(e.getUTCDate() + t) }), ((e, t) => (t - e) / Lm), (e => Math.floor(e / Lm))));
        Gm.range;

        function Km(e) { return Pm((t => { t.setDate(t.getDate() - (t.getDay() + 7 - e) % 7), t.setHours(0, 0, 0, 0) }), ((e, t) => { e.setDate(e.getDate() + 7 * t) }), ((e, t) => (t - e - (t.getTimezoneOffset() - e.getTimezoneOffset()) * Tm) / Im)) } const Zm = Km(0),
            Ym = Km(1),
            Xm = Km(2),
            $m = Km(3),
            Qm = Km(4),
            Jm = Km(5),
            ep = Km(6);
        Zm.range, Ym.range, Xm.range, $m.range, Qm.range, Jm.range, ep.range;

        function tp(e) { return Pm((t => { t.setUTCDate(t.getUTCDate() - (t.getUTCDay() + 7 - e) % 7), t.setUTCHours(0, 0, 0, 0) }), ((e, t) => { e.setUTCDate(e.getUTCDate() + 7 * t) }), ((e, t) => (t - e) / Im)) } const np = tp(0),
            rp = tp(1),
            ap = tp(2),
            op = tp(3),
            ip = tp(4),
            lp = tp(5),
            sp = tp(6),
            cp = (np.range, rp.range, ap.range, op.range, ip.range, lp.range, sp.range, Pm((e => { e.setDate(1), e.setHours(0, 0, 0, 0) }), ((e, t) => { e.setMonth(e.getMonth() + t) }), ((e, t) => t.getMonth() - e.getMonth() + 12 * (t.getFullYear() - e.getFullYear())), (e => e.getMonth()))),
            dp = (cp.range, Pm((e => { e.setUTCDate(1), e.setUTCHours(0, 0, 0, 0) }), ((e, t) => { e.setUTCMonth(e.getUTCMonth() + t) }), ((e, t) => t.getUTCMonth() - e.getUTCMonth() + 12 * (t.getUTCFullYear() - e.getUTCFullYear())), (e => e.getUTCMonth()))),
            up = (dp.range, Pm((e => { e.setMonth(0, 1), e.setHours(0, 0, 0, 0) }), ((e, t) => { e.setFullYear(e.getFullYear() + t) }), ((e, t) => t.getFullYear() - e.getFullYear()), (e => e.getFullYear())));
        up.every = e => isFinite(e = Math.floor(e)) && e > 0 ? Pm((t => { t.setFullYear(Math.floor(t.getFullYear() / e) * e), t.setMonth(0, 1), t.setHours(0, 0, 0, 0) }), ((t, n) => { t.setFullYear(t.getFullYear() + n * e) })) : null;
        up.range; const hp = Pm((e => { e.setUTCMonth(0, 1), e.setUTCHours(0, 0, 0, 0) }), ((e, t) => { e.setUTCFullYear(e.getUTCFullYear() + t) }), ((e, t) => t.getUTCFullYear() - e.getUTCFullYear()), (e => e.getUTCFullYear()));
        hp.every = e => isFinite(e = Math.floor(e)) && e > 0 ? Pm((t => { t.setUTCFullYear(Math.floor(t.getUTCFullYear() / e) * e), t.setUTCMonth(0, 1), t.setUTCHours(0, 0, 0, 0) }), ((t, n) => { t.setUTCFullYear(t.getUTCFullYear() + n * e) })) : null;
        hp.range;

        function mp(e, t, n, r, a, o) { const i = [
                [Fm, 1, Cm],
                [Fm, 5, 5e3],
                [Fm, 15, 15e3],
                [Fm, 30, 3e4],
                [o, 1, Tm],
                [o, 5, 3e5],
                [o, 15, 9e5],
                [o, 30, 18e5],
                [a, 1, Hm],
                [a, 3, 108e5],
                [a, 6, 216e5],
                [a, 12, 432e5],
                [r, 1, Lm],
                [r, 2, 1728e5],
                [n, 1, Im],
                [t, 1, jm],
                [t, 3, 7776e6],
                [e, 1, Vm]
            ];

            function l(t, n, r) { const a = Math.abs(n - t) / r,
                    o = bu((e => { let [, , t] = e; return t })).right(i, a); if (o === i.length) return e.every(vu(t / Vm, n / Vm, r)); if (0 === o) return Dm.every(Math.max(vu(t, n, r), 1)); const [l, s] = i[a / i[o - 1][2] < i[o][2] / a ? o - 1 : o]; return l.every(s) } return [function(e, t, n) { const r = t < e;
                r && ([e, t] = [t, e]); const a = n && "function" === typeof n.range ? n : l(e, t, n),
                    o = a ? a.range(e, +t + 1) : []; return r ? o.reverse() : o }, l] } const [pp, fp] = mp(hp, dp, np, Gm, Wm, _m), [vp, gp] = mp(up, cp, Zm, Um, Bm, Nm);

        function yp(e) { if (0 <= e.y && e.y < 100) { var t = new Date(-1, e.m, e.d, e.H, e.M, e.S, e.L); return t.setFullYear(e.y), t } return new Date(e.y, e.m, e.d, e.H, e.M, e.S, e.L) }

        function bp(e) { if (0 <= e.y && e.y < 100) { var t = new Date(Date.UTC(-1, e.m, e.d, e.H, e.M, e.S, e.L)); return t.setUTCFullYear(e.y), t } return new Date(Date.UTC(e.y, e.m, e.d, e.H, e.M, e.S, e.L)) }

        function wp(e, t, n) { return { y: e, m: t, d: n, H: 0, M: 0, S: 0, L: 0 } } var zp, xp, Ap, kp = { "-": "", _: " ", 0: "0" },
            Sp = /^\s*\d+/,
            Mp = /^%/,
            Ep = /[\\^$*+?|[\]().{}]/g;

        function Cp(e, t, n) { var r = e < 0 ? "-" : "",
                a = (r ? -e : e) + "",
                o = a.length; return r + (o < n ? new Array(n - o + 1).join(t) + a : a) }

        function Tp(e) { return e.replace(Ep, "\\$&") }

        function Hp(e) { return new RegExp("^(?:" + e.map(Tp).join("|") + ")", "i") }

        function Lp(e) { return new Map(e.map(((e, t) => [e.toLowerCase(), t]))) }

        function Ip(e, t, n) { var r = Sp.exec(t.slice(n, n + 1)); return r ? (e.w = +r[0], n + r[0].length) : -1 }

        function jp(e, t, n) { var r = Sp.exec(t.slice(n, n + 1)); return r ? (e.u = +r[0], n + r[0].length) : -1 }

        function Vp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.U = +r[0], n + r[0].length) : -1 }

        function Op(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.V = +r[0], n + r[0].length) : -1 }

        function Rp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.W = +r[0], n + r[0].length) : -1 }

        function Pp(e, t, n) { var r = Sp.exec(t.slice(n, n + 4)); return r ? (e.y = +r[0], n + r[0].length) : -1 }

        function Dp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.y = +r[0] + (+r[0] > 68 ? 1900 : 2e3), n + r[0].length) : -1 }

        function Fp(e, t, n) { var r = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n, n + 6)); return r ? (e.Z = r[1] ? 0 : -(r[2] + (r[3] || "00")), n + r[0].length) : -1 }

        function Np(e, t, n) { var r = Sp.exec(t.slice(n, n + 1)); return r ? (e.q = 3 * r[0] - 3, n + r[0].length) : -1 }

        function _p(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.m = r[0] - 1, n + r[0].length) : -1 }

        function Bp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.d = +r[0], n + r[0].length) : -1 }

        function Wp(e, t, n) { var r = Sp.exec(t.slice(n, n + 3)); return r ? (e.m = 0, e.d = +r[0], n + r[0].length) : -1 }

        function Up(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.H = +r[0], n + r[0].length) : -1 }

        function qp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.M = +r[0], n + r[0].length) : -1 }

        function Gp(e, t, n) { var r = Sp.exec(t.slice(n, n + 2)); return r ? (e.S = +r[0], n + r[0].length) : -1 }

        function Kp(e, t, n) { var r = Sp.exec(t.slice(n, n + 3)); return r ? (e.L = +r[0], n + r[0].length) : -1 }

        function Zp(e, t, n) { var r = Sp.exec(t.slice(n, n + 6)); return r ? (e.L = Math.floor(r[0] / 1e3), n + r[0].length) : -1 }

        function Yp(e, t, n) { var r = Mp.exec(t.slice(n, n + 1)); return r ? n + r[0].length : -1 }

        function Xp(e, t, n) { var r = Sp.exec(t.slice(n)); return r ? (e.Q = +r[0], n + r[0].length) : -1 }

        function $p(e, t, n) { var r = Sp.exec(t.slice(n)); return r ? (e.s = +r[0], n + r[0].length) : -1 }

        function Qp(e, t) { return Cp(e.getDate(), t, 2) }

        function Jp(e, t) { return Cp(e.getHours(), t, 2) }

        function ef(e, t) { return Cp(e.getHours() % 12 || 12, t, 2) }

        function tf(e, t) { return Cp(1 + Um.count(up(e), e), t, 3) }

        function nf(e, t) { return Cp(e.getMilliseconds(), t, 3) }

        function rf(e, t) { return nf(e, t) + "000" }

        function af(e, t) { return Cp(e.getMonth() + 1, t, 2) }

        function of (e, t) { return Cp(e.getMinutes(), t, 2) }

        function lf(e, t) { return Cp(e.getSeconds(), t, 2) }

        function sf(e) { var t = e.getDay(); return 0 === t ? 7 : t }

        function cf(e, t) { return Cp(Zm.count(up(e) - 1, e), t, 2) }

        function df(e) { var t = e.getDay(); return t >= 4 || 0 === t ? Qm(e) : Qm.ceil(e) }

        function uf(e, t) { return e = df(e), Cp(Qm.count(up(e), e) + (4 === up(e).getDay()), t, 2) }

        function hf(e) { return e.getDay() }

        function mf(e, t) { return Cp(Ym.count(up(e) - 1, e), t, 2) }

        function pf(e, t) { return Cp(e.getFullYear() % 100, t, 2) }

        function ff(e, t) { return Cp((e = df(e)).getFullYear() % 100, t, 2) }

        function vf(e, t) { return Cp(e.getFullYear() % 1e4, t, 4) }

        function gf(e, t) { var n = e.getDay(); return Cp((e = n >= 4 || 0 === n ? Qm(e) : Qm.ceil(e)).getFullYear() % 1e4, t, 4) }

        function yf(e) { var t = e.getTimezoneOffset(); return (t > 0 ? "-" : (t *= -1, "+")) + Cp(t / 60 | 0, "0", 2) + Cp(t % 60, "0", 2) }

        function bf(e, t) { return Cp(e.getUTCDate(), t, 2) }

        function wf(e, t) { return Cp(e.getUTCHours(), t, 2) }

        function zf(e, t) { return Cp(e.getUTCHours() % 12 || 12, t, 2) }

        function xf(e, t) { return Cp(1 + qm.count(hp(e), e), t, 3) }

        function Af(e, t) { return Cp(e.getUTCMilliseconds(), t, 3) }

        function kf(e, t) { return Af(e, t) + "000" }

        function Sf(e, t) { return Cp(e.getUTCMonth() + 1, t, 2) }

        function Mf(e, t) { return Cp(e.getUTCMinutes(), t, 2) }

        function Ef(e, t) { return Cp(e.getUTCSeconds(), t, 2) }

        function Cf(e) { var t = e.getUTCDay(); return 0 === t ? 7 : t }

        function Tf(e, t) { return Cp(np.count(hp(e) - 1, e), t, 2) }

        function Hf(e) { var t = e.getUTCDay(); return t >= 4 || 0 === t ? ip(e) : ip.ceil(e) }

        function Lf(e, t) { return e = Hf(e), Cp(ip.count(hp(e), e) + (4 === hp(e).getUTCDay()), t, 2) }

        function If(e) { return e.getUTCDay() }

        function jf(e, t) { return Cp(rp.count(hp(e) - 1, e), t, 2) }

        function Vf(e, t) { return Cp(e.getUTCFullYear() % 100, t, 2) }

        function Of(e, t) { return Cp((e = Hf(e)).getUTCFullYear() % 100, t, 2) }

        function Rf(e, t) { return Cp(e.getUTCFullYear() % 1e4, t, 4) }

        function Pf(e, t) { var n = e.getUTCDay(); return Cp((e = n >= 4 || 0 === n ? ip(e) : ip.ceil(e)).getUTCFullYear() % 1e4, t, 4) }

        function Df() { return "+0000" }

        function Ff() { return "%" }

        function Nf(e) { return +e }

        function _f(e) { return Math.floor(+e / 1e3) }

        function Bf(e) { return new Date(e) }

        function Wf(e) { return e instanceof Date ? +e : +new Date(+e) }

        function Uf(e, t, n, r, a, o, i, l, s, c) { var d = Hh(),
                u = d.invert,
                h = d.domain,
                m = c(".%L"),
                p = c(":%S"),
                f = c("%I:%M"),
                v = c("%I %p"),
                g = c("%a %d"),
                y = c("%b %d"),
                b = c("%B"),
                w = c("%Y");

            function z(e) { return (s(e) < e ? m : l(e) < e ? p : i(e) < e ? f : o(e) < e ? v : r(e) < e ? a(e) < e ? g : y : n(e) < e ? b : w)(e) } return d.invert = function(e) { return new Date(u(e)) }, d.domain = function(e) { return arguments.length ? h(Array.from(e, Wf)) : h().map(Bf) }, d.ticks = function(t) { var n = h(); return e(n[0], n[n.length - 1], null == t ? 10 : t) }, d.tickFormat = function(e, t) { return null == t ? z : c(t) }, d.nice = function(e) { var n = h(); return e && "function" === typeof e.range || (e = t(n[0], n[n.length - 1], null == e ? 10 : e)), e ? h(Xh(n, e)) : d }, d.copy = function() { return Ch(d, Uf(e, t, n, r, a, o, i, l, s, c)) }, d }

        function qf() { return fd.apply(Uf(vp, gp, up, cp, Zm, Um, Bm, Nm, Fm, xp).domain([new Date(2e3, 0, 1), new Date(2e3, 0, 2)]), arguments) }

        function Gf() { return fd.apply(Uf(pp, fp, hp, dp, np, qm, Wm, _m, Fm, Ap).domain([Date.UTC(2e3, 0, 1), Date.UTC(2e3, 0, 2)]), arguments) }

        function Kf() { var e, t, n, r, a, o = 0,
                i = 1,
                l = kh,
                s = !1;

            function c(t) { return null == t || isNaN(t = +t) ? a : l(0 === n ? .5 : (t = (r(t) - e) * n, s ? Math.max(0, Math.min(1, t)) : t)) }

            function d(e) { return function(t) { var n, r; return arguments.length ? ([n, r] = t, l = e(n, r), c) : [l(0), l(1)] } } return c.domain = function(a) { return arguments.length ? ([o, i] = a, e = r(o = +o), t = r(i = +i), n = e === t ? 0 : 1 / (t - e), c) : [o, i] }, c.clamp = function(e) { return arguments.length ? (s = !!e, c) : s }, c.interpolator = function(e) { return arguments.length ? (l = e, c) : l }, c.range = d(wh), c.rangeRound = d(zh), c.unknown = function(e) { return arguments.length ? (a = e, c) : a },
                function(a) { return r = a, e = a(o), t = a(i), n = e === t ? 0 : 1 / (t - e), c } }

        function Zf(e, t) { return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown()) }

        function Yf() { var e = Kh(Kf()(kh)); return e.copy = function() { return Zf(e, Yf()) }, vd.apply(e, arguments) }

        function Xf() { var e = rm(Kf()).domain([1, 10]); return e.copy = function() { return Zf(e, Xf()).base(e.base()) }, vd.apply(e, arguments) }

        function $f() { var e = lm(Kf()); return e.copy = function() { return Zf(e, $f()).constant(e.constant()) }, vd.apply(e, arguments) }

        function Qf() { var e = hm(Kf()); return e.copy = function() { return Zf(e, Qf()).exponent(e.exponent()) }, vd.apply(e, arguments) }

        function Jf() { return Qf.apply(null, arguments).exponent(.5) }

        function ev() { var e = [],
                t = kh;

            function n(n) { if (null != n && !isNaN(n = +n)) return t((ku(e, n, 1) - 1) / (e.length - 1)) } return n.domain = function(t) { if (!arguments.length) return e.slice();
                e = []; for (let n of t) null == n || isNaN(n = +n) || e.push(n); return e.sort(gu), n }, n.interpolator = function(e) { return arguments.length ? (t = e, n) : t }, n.range = function() { return e.map(((n, r) => t(r / (e.length - 1)))) }, n.quantiles = function(t) { return Array.from({ length: t + 1 }, ((n, r) => Am(e, r / t))) }, n.copy = function() { return ev(t).domain(e) }, vd.apply(n, arguments) }

        function tv() { var e, t, n, r, a, o, i, l = 0,
                s = .5,
                c = 1,
                d = 1,
                u = kh,
                h = !1;

            function m(e) { return isNaN(e = +e) ? i : (e = .5 + ((e = +o(e)) - t) * (d * e < d * t ? r : a), u(h ? Math.max(0, Math.min(1, e)) : e)) }

            function p(e) { return function(t) { var n, r, a; return arguments.length ? ([n, r, a] = t, u = function(e, t) { void 0 === t && (t = e, e = wh); for (var n = 0, r = t.length - 1, a = t[0], o = new Array(r < 0 ? 0 : r); n < r;) o[n] = e(a, a = t[++n]); return function(e) { var t = Math.max(0, Math.min(r - 1, Math.floor(e *= r))); return o[t](e - t) } }(e, [n, r, a]), m) : [u(0), u(.5), u(1)] } } return m.domain = function(i) { return arguments.length ? ([l, s, c] = i, e = o(l = +l), t = o(s = +s), n = o(c = +c), r = e === t ? 0 : .5 / (t - e), a = t === n ? 0 : .5 / (n - t), d = t < e ? -1 : 1, m) : [l, s, c] }, m.clamp = function(e) { return arguments.length ? (h = !!e, m) : h }, m.interpolator = function(e) { return arguments.length ? (u = e, m) : u }, m.range = p(wh), m.rangeRound = p(zh), m.unknown = function(e) { return arguments.length ? (i = e, m) : i },
                function(i) { return o = i, e = i(l), t = i(s), n = i(c), r = e === t ? 0 : .5 / (t - e), a = t === n ? 0 : .5 / (n - t), d = t < e ? -1 : 1, m } }

        function nv() { var e = Kh(tv()(kh)); return e.copy = function() { return Zf(e, nv()) }, vd.apply(e, arguments) }

        function rv() { var e = rm(tv()).domain([.1, 1, 10]); return e.copy = function() { return Zf(e, rv()).base(e.base()) }, vd.apply(e, arguments) }

        function av() { var e = lm(tv()); return e.copy = function() { return Zf(e, av()).constant(e.constant()) }, vd.apply(e, arguments) }

        function ov() { var e = hm(tv()); return e.copy = function() { return Zf(e, ov()).exponent(e.exponent()) }, vd.apply(e, arguments) }

        function iv() { return ov.apply(null, arguments).exponent(.5) }

        function lv(e, t) { if ((a = e.length) > 1)
                for (var n, r, a, o = 1, i = e[t[0]], l = i.length; o < a; ++o)
                    for (r = i, i = e[t[o]], n = 0; n < l; ++n) i[n][1] += i[n][0] = isNaN(r[n][1]) ? r[n][0] : r[n][1] }! function(e) { zp = function(e) { var t = e.dateTime,
                    n = e.date,
                    r = e.time,
                    a = e.periods,
                    o = e.days,
                    i = e.shortDays,
                    l = e.months,
                    s = e.shortMonths,
                    c = Hp(a),
                    d = Lp(a),
                    u = Hp(o),
                    h = Lp(o),
                    m = Hp(i),
                    p = Lp(i),
                    f = Hp(l),
                    v = Lp(l),
                    g = Hp(s),
                    y = Lp(s),
                    b = { a: function(e) { return i[e.getDay()] }, A: function(e) { return o[e.getDay()] }, b: function(e) { return s[e.getMonth()] }, B: function(e) { return l[e.getMonth()] }, c: null, d: Qp, e: Qp, f: rf, g: ff, G: gf, H: Jp, I: ef, j: tf, L: nf, m: af, M: of , p: function(e) { return a[+(e.getHours() >= 12)] }, q: function(e) { return 1 + ~~(e.getMonth() / 3) }, Q: Nf, s: _f, S: lf, u: sf, U: cf, V: uf, w: hf, W: mf, x: null, X: null, y: pf, Y: vf, Z: yf, "%": Ff },
                    w = { a: function(e) { return i[e.getUTCDay()] }, A: function(e) { return o[e.getUTCDay()] }, b: function(e) { return s[e.getUTCMonth()] }, B: function(e) { return l[e.getUTCMonth()] }, c: null, d: bf, e: bf, f: kf, g: Of, G: Pf, H: wf, I: zf, j: xf, L: Af, m: Sf, M: Mf, p: function(e) { return a[+(e.getUTCHours() >= 12)] }, q: function(e) { return 1 + ~~(e.getUTCMonth() / 3) }, Q: Nf, s: _f, S: Ef, u: Cf, U: Tf, V: Lf, w: If, W: jf, x: null, X: null, y: Vf, Y: Rf, Z: Df, "%": Ff },
                    z = { a: function(e, t, n) { var r = m.exec(t.slice(n)); return r ? (e.w = p.get(r[0].toLowerCase()), n + r[0].length) : -1 }, A: function(e, t, n) { var r = u.exec(t.slice(n)); return r ? (e.w = h.get(r[0].toLowerCase()), n + r[0].length) : -1 }, b: function(e, t, n) { var r = g.exec(t.slice(n)); return r ? (e.m = y.get(r[0].toLowerCase()), n + r[0].length) : -1 }, B: function(e, t, n) { var r = f.exec(t.slice(n)); return r ? (e.m = v.get(r[0].toLowerCase()), n + r[0].length) : -1 }, c: function(e, n, r) { return k(e, t, n, r) }, d: Bp, e: Bp, f: Zp, g: Dp, G: Pp, H: Up, I: Up, j: Wp, L: Kp, m: _p, M: qp, p: function(e, t, n) { var r = c.exec(t.slice(n)); return r ? (e.p = d.get(r[0].toLowerCase()), n + r[0].length) : -1 }, q: Np, Q: Xp, s: $p, S: Gp, u: jp, U: Vp, V: Op, w: Ip, W: Rp, x: function(e, t, r) { return k(e, n, t, r) }, X: function(e, t, n) { return k(e, r, t, n) }, y: Dp, Y: Pp, Z: Fp, "%": Yp };

                function x(e, t) { return function(n) { var r, a, o, i = [],
                            l = -1,
                            s = 0,
                            c = e.length; for (n instanceof Date || (n = new Date(+n)); ++l < c;) 37 === e.charCodeAt(l) && (i.push(e.slice(s, l)), null != (a = kp[r = e.charAt(++l)]) ? r = e.charAt(++l) : a = "e" === r ? " " : "0", (o = t[r]) && (r = o(n, a)), i.push(r), s = l + 1); return i.push(e.slice(s, l)), i.join("") } }

                function A(e, t) { return function(n) { var r, a, o = wp(1900, void 0, 1); if (k(o, e, n += "", 0) != n.length) return null; if ("Q" in o) return new Date(o.Q); if ("s" in o) return new Date(1e3 * o.s + ("L" in o ? o.L : 0)); if (t && !("Z" in o) && (o.Z = 0), "p" in o && (o.H = o.H % 12 + 12 * o.p), void 0 === o.m && (o.m = "q" in o ? o.q : 0), "V" in o) { if (o.V < 1 || o.V > 53) return null; "w" in o || (o.w = 1), "Z" in o ? (a = (r = bp(wp(o.y, 0, 1))).getUTCDay(), r = a > 4 || 0 === a ? rp.ceil(r) : rp(r), r = qm.offset(r, 7 * (o.V - 1)), o.y = r.getUTCFullYear(), o.m = r.getUTCMonth(), o.d = r.getUTCDate() + (o.w + 6) % 7) : (a = (r = yp(wp(o.y, 0, 1))).getDay(), r = a > 4 || 0 === a ? Ym.ceil(r) : Ym(r), r = Um.offset(r, 7 * (o.V - 1)), o.y = r.getFullYear(), o.m = r.getMonth(), o.d = r.getDate() + (o.w + 6) % 7) } else("W" in o || "U" in o) && ("w" in o || (o.w = "u" in o ? o.u % 7 : "W" in o ? 1 : 0), a = "Z" in o ? bp(wp(o.y, 0, 1)).getUTCDay() : yp(wp(o.y, 0, 1)).getDay(), o.m = 0, o.d = "W" in o ? (o.w + 6) % 7 + 7 * o.W - (a + 5) % 7 : o.w + 7 * o.U - (a + 6) % 7); return "Z" in o ? (o.H += o.Z / 100 | 0, o.M += o.Z % 100, bp(o)) : yp(o) } }

                function k(e, t, n, r) { for (var a, o, i = 0, l = t.length, s = n.length; i < l;) { if (r >= s) return -1; if (37 === (a = t.charCodeAt(i++))) { if (a = t.charAt(i++), !(o = z[a in kp ? t.charAt(i++) : a]) || (r = o(e, n, r)) < 0) return -1 } else if (a != n.charCodeAt(r++)) return -1 } return r } return b.x = x(n, b), b.X = x(r, b), b.c = x(t, b), w.x = x(n, w), w.X = x(r, w), w.c = x(t, w), { format: function(e) { var t = x(e += "", b); return t.toString = function() { return e }, t }, parse: function(e) { var t = A(e += "", !1); return t.toString = function() { return e }, t }, utcFormat: function(e) { var t = x(e += "", w); return t.toString = function() { return e }, t }, utcParse: function(e) { var t = A(e += "", !0); return t.toString = function() { return e }, t } } }(e), xp = zp.format, zp.parse, Ap = zp.utcFormat, zp.utcParse }({ dateTime: "%x, %X", date: "%-m/%-d/%Y", time: "%-I:%M:%S %p", periods: ["AM", "PM"], days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], shortDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], shortMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"] });
        Array.prototype.slice;

        function sv(e) { return "object" === typeof e && "length" in e ? e : Array.from(e) }

        function cv(e) { for (var t = e.length, n = new Array(t); --t >= 0;) n[t] = t; return n }

        function dv(e, t) { return e[t] }

        function uv(e) { const t = []; return t.key = e, t } var hv = n(50539),
            mv = n.n(hv),
            pv = n(76745),
            fv = n.n(pv),
            vv = n(63538),
            gv = n.n(vv),
            yv = n(19853),
            bv = n.n(yv),
            wv = n(98210),
            zv = n.n(wv);

        function xv(e) { return function(e) { if (Array.isArray(e)) return Av(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && Symbol.iterator in Object(e)) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return Av(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Av(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Av(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } var kv = function(e) { return e },
            Sv = { "@@functional/placeholder": !0 },
            Mv = function(e) { return e === Sv },
            Ev = function(e) { return function t() { return 0 === arguments.length || 1 === arguments.length && Mv(arguments.length <= 0 ? void 0 : arguments[0]) ? t : e.apply(void 0, arguments) } },
            Cv = function e(t, n) { return 1 === t ? n : Ev((function() { for (var r = arguments.length, a = new Array(r), o = 0; o < r; o++) a[o] = arguments[o]; var i = a.filter((function(e) { return e !== Sv })).length; return i >= t ? n.apply(void 0, a) : e(t - i, Ev((function() { for (var e = arguments.length, t = new Array(e), r = 0; r < e; r++) t[r] = arguments[r]; var o = a.map((function(e) { return Mv(e) ? t.shift() : e })); return n.apply(void 0, xv(o).concat(t)) }))) })) },
            Tv = function(e) { return Cv(e.length, e) },
            Hv = function(e, t) { for (var n = [], r = e; r < t; ++r) n[r - e] = r; return n },
            Lv = Tv((function(e, t) { return Array.isArray(t) ? t.map(e) : Object.keys(t).map((function(e) { return t[e] })).map(e) })),
            Iv = function() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; if (!t.length) return kv; var r = t.reverse(),
                    a = r[0],
                    o = r.slice(1); return function() { return o.reduce((function(e, t) { return t(e) }), a.apply(void 0, arguments)) } },
            jv = function(e) { return Array.isArray(e) ? e.reverse() : e.split("").reverse.join("") },
            Vv = function(e) { var t = null,
                    n = null; return function() { for (var r = arguments.length, a = new Array(r), o = 0; o < r; o++) a[o] = arguments[o]; return t && a.every((function(e, n) { return e === t[n] })) ? n : (t = a, n = e.apply(void 0, a)) } }; var Ov = Tv((function(e, t, n) { var r = +e; return r + n * (+t - r) })),
            Rv = Tv((function(e, t, n) { var r = t - +e; return (n - e) / (r = r || 1 / 0) })),
            Pv = Tv((function(e, t, n) { var r = t - +e; return r = r || 1 / 0, Math.max(0, Math.min(1, (n - e) / r)) })); const Dv = { rangeStep: function(e, t, n) { for (var r = new(zv())(e), a = 0, o = []; r.lt(t) && a < 1e5;) o.push(r.toNumber()), r = r.add(n), a++; return o }, getDigitCount: function(e) { return 0 === e ? 1 : Math.floor(new(zv())(e).abs().log(10).toNumber()) + 1 }, interpolateNumber: Ov, uninterpolateNumber: Rv, uninterpolateTruncation: Pv };

        function Fv(e) { return function(e) { if (Array.isArray(e)) return Bv(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && Symbol.iterator in Object(e)) return Array.from(e) }(e) || _v(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Nv(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { if ("undefined" === typeof Symbol || !(Symbol.iterator in Object(e))) return; var n = [],
                    r = !0,
                    a = !1,
                    o = void 0; try { for (var i, l = e[Symbol.iterator](); !(r = (i = l.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0); } catch (s) { a = !0, o = s } finally { try { r || null == l.return || l.return() } finally { if (a) throw o } } return n }(e, t) || _v(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function _v(e, t) { if (e) { if ("string" === typeof e) return Bv(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Bv(e, t) : void 0 } }

        function Bv(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function Wv(e) { var t = Nv(e, 2),
                n = t[0],
                r = t[1],
                a = n,
                o = r; return n > r && (a = r, o = n), [a, o] }

        function Uv(e, t, n) { if (e.lte(0)) return new(zv())(0); var r = Dv.getDigitCount(e.toNumber()),
                a = new(zv())(10).pow(r),
                o = e.div(a),
                i = 1 !== r ? .05 : .1,
                l = new(zv())(Math.ceil(o.div(i).toNumber())).add(n).mul(i).mul(a); return t ? l : new(zv())(Math.ceil(l)) }

        function qv(e, t, n) { var r = 1,
                a = new(zv())(e); if (!a.isint() && n) { var o = Math.abs(e);
                o < 1 ? (r = new(zv())(10).pow(Dv.getDigitCount(e) - 1), a = new(zv())(Math.floor(a.div(r).toNumber())).mul(r)) : o > 1 && (a = new(zv())(Math.floor(e))) } else 0 === e ? a = new(zv())(Math.floor((t - 1) / 2)) : n || (a = new(zv())(Math.floor(e))); var i = Math.floor((t - 1) / 2); return Iv(Lv((function(e) { return a.add(new(zv())(e - i).mul(r)).toNumber() })), Hv)(0, t) }

        function Gv(e, t, n, r) { var a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 0; if (!Number.isFinite((t - e) / (n - 1))) return { step: new(zv())(0), tickMin: new(zv())(0), tickMax: new(zv())(0) }; var o, i = Uv(new(zv())(t).sub(e).div(n - 1), r, a);
            o = e <= 0 && t >= 0 ? new(zv())(0) : (o = new(zv())(e).add(t).div(2)).sub(new(zv())(o).mod(i)); var l = Math.ceil(o.sub(e).div(i).toNumber()),
                s = Math.ceil(new(zv())(t).sub(o).div(i).toNumber()),
                c = l + s + 1; return c > n ? Gv(e, t, n, r, a + 1) : (c < n && (s = t > 0 ? s + (n - c) : s, l = t > 0 ? l : l + (n - c)), { step: i, tickMin: o.sub(new(zv())(l).mul(i)), tickMax: o.add(new(zv())(s).mul(i)) }) } var Kv = Vv((function(e) { var t = Nv(e, 2),
                    n = t[0],
                    r = t[1],
                    a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 6,
                    o = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2],
                    i = Math.max(a, 2),
                    l = Nv(Wv([n, r]), 2),
                    s = l[0],
                    c = l[1]; if (s === -1 / 0 || c === 1 / 0) { var d = c === 1 / 0 ? [s].concat(Fv(Hv(0, a - 1).map((function() { return 1 / 0 })))) : [].concat(Fv(Hv(0, a - 1).map((function() { return -1 / 0 }))), [c]); return n > r ? jv(d) : d } if (s === c) return qv(s, a, o); var u = Gv(s, c, i, o),
                    h = u.step,
                    m = u.tickMin,
                    p = u.tickMax,
                    f = Dv.rangeStep(m, p.add(new(zv())(.1).mul(h)), h); return n > r ? jv(f) : f })),
            Zv = (Vv((function(e) { var t = Nv(e, 2),
                    n = t[0],
                    r = t[1],
                    a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 6,
                    o = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2],
                    i = Math.max(a, 2),
                    l = Nv(Wv([n, r]), 2),
                    s = l[0],
                    c = l[1]; if (s === -1 / 0 || c === 1 / 0) return [n, r]; if (s === c) return qv(s, a, o); var d = Uv(new(zv())(c).sub(s).div(i - 1), o, 0),
                    u = Iv(Lv((function(e) { return new(zv())(s).add(new(zv())(e).mul(d)).toNumber() })), Hv)(0, i).filter((function(e) { return e >= s && e <= c })); return n > r ? jv(u) : u })), Vv((function(e, t) { var n = Nv(e, 2),
                    r = n[0],
                    a = n[1],
                    o = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2],
                    i = Nv(Wv([r, a]), 2),
                    l = i[0],
                    s = i[1]; if (l === -1 / 0 || s === 1 / 0) return [r, a]; if (l === s) return [l]; var c = Math.max(t, 2),
                    d = Uv(new(zv())(s).sub(l).div(c - 1), o, 0),
                    u = [].concat(Fv(Dv.rangeStep(new(zv())(l), new(zv())(s).sub(new(zv())(.99).mul(d)), d)), [s]); return r > a ? jv(u) : u }))),
            Yv = ["offset", "layout", "width", "dataKey", "data", "dataPointFormatter", "xAxis", "yAxis"];

        function Xv() { return Xv = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Xv.apply(this, arguments) }

        function $v(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return Qv(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Qv(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Qv(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function Jv(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function eg(e) { var t = e.offset,
                n = e.layout,
                r = e.width,
                o = e.dataKey,
                i = e.data,
                l = e.dataPointFormatter,
                s = e.xAxis,
                c = e.yAxis,
                d = Jv(e, Yv),
                u = po(d, !1); "x" === e.direction && "number" !== s.type && (0, To.A)(!1); var h = i.map((function(e) { var i = l(e, o),
                    d = i.x,
                    h = i.y,
                    m = i.value,
                    p = i.errorVal; if (!p) return null; var f, v, g = []; if (Array.isArray(p)) { var y = $v(p, 2);
                    f = y[0], v = y[1] } else f = v = p; if ("vertical" === n) { var b = s.scale,
                        w = h + t,
                        z = w + r,
                        x = w - r,
                        A = b(m - f),
                        k = b(m + v);
                    g.push({ x1: k, y1: z, x2: k, y2: x }), g.push({ x1: A, y1: w, x2: k, y2: w }), g.push({ x1: A, y1: z, x2: A, y2: x }) } else if ("horizontal" === n) { var S = c.scale,
                        M = d + t,
                        E = M - r,
                        C = M + r,
                        T = S(m - f),
                        H = S(m + v);
                    g.push({ x1: E, y1: H, x2: C, y2: H }), g.push({ x1: M, y1: T, x2: M, y2: H }), g.push({ x1: E, y1: T, x2: C, y2: T }) } return a.createElement(Po, Xv({ className: "recharts-errorBar", key: "bar-".concat(g.map((function(e) { return "".concat(e.x1, "-").concat(e.x2, "-").concat(e.y1, "-").concat(e.y2) }))) }, u), g.map((function(e) { return a.createElement("line", Xv({}, e, { key: "line-".concat(e.x1, "-").concat(e.x2, "-").concat(e.y1, "-").concat(e.y2) })) }))) })); return a.createElement(Po, { className: "recharts-errorBars" }, h) }

        function tg(e) { return tg = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, tg(e) }

        function ng(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function rg(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? ng(Object(n), !0).forEach((function(t) { ag(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ng(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function ag(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != tg(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != tg(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == tg(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } eg.defaultProps = { stroke: "black", strokeWidth: 1.5, width: 5, offset: 0, layout: "horizontal" }, eg.displayName = "ErrorBar"; var og = function(e) { var t, n = e.children,
                r = e.formattedGraphicalItems,
                a = e.legendWidth,
                o = e.legendContent,
                i = co(n, ts); return i ? (t = i.props && i.props.payload ? i.props && i.props.payload : "children" === o ? (r || []).reduce((function(e, t) { var n = t.item,
                    r = t.props,
                    a = r.sectors || r.data || []; return e.concat(a.map((function(e) { return { type: i.props.iconType || n.props.legendType, value: e.name, color: e.fill, payload: e } }))) }), []) : (r || []).map((function(e) { var t = e.item,
                    n = t.props,
                    r = n.dataKey,
                    a = n.name,
                    o = n.legendType; return { inactive: n.hide, dataKey: r, type: i.props.iconType || o || "square", color: pg(t), value: a || r, payload: t.props } })), rg(rg(rg({}, i.props), ts.getWithHeight(i, a)), {}, { payload: t, item: i })) : null };

        function ig(e) { return ig = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, ig(e) }

        function lg(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function sg(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? lg(Object(n), !0).forEach((function(t) { cg(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : lg(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function cg(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != ig(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != ig(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == ig(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function dg(e) { return function(e) { if (Array.isArray(e)) return ug(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return ug(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ug(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function ug(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function hg(e, t, n) { return Na()(e) || Na()(t) ? n : La(t) ? Sa()(e, t, n) : Ba()(t) ? t(e) : n }

        function mg(e, t, n, r) { var a = gv()(e, (function(e) { return hg(e, t) })); if ("number" === n) { var o = a.filter((function(e) { return Ha(e) || parseFloat(e) })); return o.length ? [fv()(o), mv()(o)] : [1 / 0, -1 / 0] } return (r ? a.filter((function(e) { return !Na()(e) })) : a).map((function(e) { return La(e) || e instanceof Date ? e : "" })) } var pg = function(e) { var t, n = e.type.displayName,
                    r = e.props,
                    a = r.stroke,
                    o = r.fill; switch (n) {
                    case "Line":
                        t = a; break;
                    case "Area":
                    case "Radar":
                        t = a && "none" !== a ? a : o; break;
                    default:
                        t = o } return t },
            fg = function(e, t, n, r, a) { var o = so(t.props.children, eg).filter((function(e) { return function(e, t, n) { return !!Na()(t) || ("horizontal" === e ? "yAxis" === t : "vertical" === e || "x" === n ? "xAxis" === t : "y" !== n || "yAxis" === t) }(r, a, e.props.direction) })); if (o && o.length) { var i = o.map((function(e) { return e.props.dataKey })); return e.reduce((function(e, t) { var r = hg(t, n); if (Na()(r)) return e; var a = Array.isArray(r) ? [fv()(r), mv()(r)] : [r, r],
                            o = i.reduce((function(e, n) { var r = hg(t, n, 0),
                                    o = a[0] - Math.abs(Array.isArray(r) ? r[0] : r),
                                    i = a[1] + Math.abs(Array.isArray(r) ? r[1] : r); return [Math.min(o, e[0]), Math.max(i, e[1])] }), [1 / 0, -1 / 0]); return [Math.min(o[0], e[0]), Math.max(o[1], e[1])] }), [1 / 0, -1 / 0]) } return null },
            vg = function(e, t, n, r, a) { var o = t.map((function(t) { var o = t.props.dataKey; return "number" === n && o && fg(e, t, o, r) || mg(e, o, n, a) })); if ("number" === n) return o.reduce((function(e, t) { return [Math.min(e[0], t[0]), Math.max(e[1], t[1])] }), [1 / 0, -1 / 0]); var i = {}; return o.reduce((function(e, t) { for (var n = 0, r = t.length; n < r; n++) i[t[n]] || (i[t[n]] = !0, e.push(t[n])); return e }), []) },
            gg = function(e, t) { return "horizontal" === e && "xAxis" === t || "vertical" === e && "yAxis" === t || "centric" === e && "angleAxis" === t || "radial" === e && "radiusAxis" === t },
            yg = function(e, t, n, r) { if (r) return e.map((function(e) { return e.coordinate })); var a, o, i = e.map((function(e) { return e.coordinate === t && (a = !0), e.coordinate === n && (o = !0), e.coordinate })); return a || i.push(t), o || i.push(n), i },
            bg = function(e, t, n) { if (!e) return null; var r = e.scale,
                    a = e.duplicateDomain,
                    o = e.type,
                    i = e.range,
                    l = "scaleBand" === e.realScaleType ? r.bandwidth() / 2 : 2,
                    s = (t || n) && "category" === o && r.bandwidth ? r.bandwidth() / l : 0; return s = "angleAxis" === e.axisType && (null === i || void 0 === i ? void 0 : i.length) >= 2 ? 2 * Ca(i[0] - i[1]) * s : s, t && (e.ticks || e.niceTicks) ? (e.ticks || e.niceTicks).map((function(e) { var t = a ? a.indexOf(e) : e; return { coordinate: r(t) + s, value: e, offset: s } })).filter((function(e) { return !Aa()(e.coordinate) })) : e.isCategorical && e.categoricalDomain ? e.categoricalDomain.map((function(e, t) { return { coordinate: r(e) + s, value: e, index: t, offset: s } })) : r.ticks && !n ? r.ticks(e.tickCount).map((function(e) { return { coordinate: r(e) + s, value: e, offset: s } })) : r.domain().map((function(e, t) { return { coordinate: r(e) + s, value: a ? a[e] : e, index: t, offset: s } })) },
            wg = new WeakMap,
            zg = function(e, t) { if ("function" !== typeof t) return e;
                wg.has(e) || wg.set(e, new WeakMap); var n = wg.get(e); if (n.has(t)) return n.get(t); var r = function() { e.apply(void 0, arguments), t.apply(void 0, arguments) }; return n.set(t, r), r },
            xg = function(e, n, r) { var a = e.scale,
                    o = e.type,
                    i = e.layout,
                    l = e.axisType; if ("auto" === a) return "radial" === i && "radiusAxis" === l ? { scale: kd(), realScaleType: "band" } : "radial" === i && "angleAxis" === l ? { scale: Zh(), realScaleType: "linear" } : "category" === o && n && (n.indexOf("LineChart") >= 0 || n.indexOf("AreaChart") >= 0 || n.indexOf("ComposedChart") >= 0 && !r) ? { scale: Md(), realScaleType: "point" } : "category" === o ? { scale: kd(), realScaleType: "band" } : { scale: Zh(), realScaleType: "linear" }; if (za()(a)) { var s = "scale".concat(Ci()(a)); return { scale: (t[s] || Md)(), realScaleType: t[s] ? s : "point" } } return Ba()(a) ? { scale: a } : { scale: Md(), realScaleType: "point" } },
            Ag = 1e-4,
            kg = function(e) { var t = e.domain(); if (t && !(t.length <= 2)) { var n = t.length,
                        r = e.range(),
                        a = Math.min(r[0], r[1]) - Ag,
                        o = Math.max(r[0], r[1]) + Ag,
                        i = e(t[0]),
                        l = e(t[n - 1]);
                    (i < a || i > o || l < a || l > o) && e.domain([t[0], t[n - 1]]) } },
            Sg = { sign: function(e) { var t = e.length; if (!(t <= 0))
                        for (var n = 0, r = e[0].length; n < r; ++n)
                            for (var a = 0, o = 0, i = 0; i < t; ++i) { var l = Aa()(e[i][n][1]) ? e[i][n][0] : e[i][n][1];
                                l >= 0 ? (e[i][n][0] = a, e[i][n][1] = a + l, a = e[i][n][1]) : (e[i][n][0] = o, e[i][n][1] = o + l, o = e[i][n][1]) } }, expand: function(e, t) { if ((r = e.length) > 0) { for (var n, r, a, o = 0, i = e[0].length; o < i; ++o) { for (a = n = 0; n < r; ++n) a += e[n][o][1] || 0; if (a)
                                for (n = 0; n < r; ++n) e[n][o][1] /= a } lv(e, t) } }, none: lv, silhouette: function(e, t) { if ((n = e.length) > 0) { for (var n, r = 0, a = e[t[0]], o = a.length; r < o; ++r) { for (var i = 0, l = 0; i < n; ++i) l += e[i][r][1] || 0;
                            a[r][1] += a[r][0] = -l / 2 } lv(e, t) } }, wiggle: function(e, t) { if ((a = e.length) > 0 && (r = (n = e[t[0]]).length) > 0) { for (var n, r, a, o = 0, i = 1; i < r; ++i) { for (var l = 0, s = 0, c = 0; l < a; ++l) { for (var d = e[t[l]], u = d[i][1] || 0, h = (u - (d[i - 1][1] || 0)) / 2, m = 0; m < l; ++m) { var p = e[t[m]];
                                    h += (p[i][1] || 0) - (p[i - 1][1] || 0) } s += u, c += h * u } n[i - 1][1] += n[i - 1][0] = o, s && (o -= c / s) } n[i - 1][1] += n[i - 1][0] = o, lv(e, t) } }, positive: function(e) { var t = e.length; if (!(t <= 0))
                        for (var n = 0, r = e[0].length; n < r; ++n)
                            for (var a = 0, o = 0; o < t; ++o) { var i = Aa()(e[o][n][1]) ? e[o][n][0] : e[o][n][1];
                                i >= 0 ? (e[o][n][0] = a, e[o][n][1] = a + i, a = e[o][n][1]) : (e[o][n][0] = 0, e[o][n][1] = 0) } } },
            Mg = function(e, t, n) { var r = t.map((function(e) { return e.props.dataKey })),
                    a = Sg[n],
                    o = function() { var e = $i([]),
                            t = cv,
                            n = lv,
                            r = dv;

                        function a(a) { var o, i, l = Array.from(e.apply(this, arguments), uv),
                                s = l.length,
                                c = -1; for (const e of a)
                                for (o = 0, ++c; o < s; ++o)(l[o][c] = [0, +r(e, l[o].key, c, a)]).data = e; for (o = 0, i = sv(t(l)); o < s; ++o) l[i[o]].index = o; return n(l, i), l } return a.keys = function(t) { return arguments.length ? (e = "function" === typeof t ? t : $i(Array.from(t)), a) : e }, a.value = function(e) { return arguments.length ? (r = "function" === typeof e ? e : $i(+e), a) : r }, a.order = function(e) { return arguments.length ? (t = null == e ? cv : "function" === typeof e ? e : $i(Array.from(e)), a) : t }, a.offset = function(e) { return arguments.length ? (n = null == e ? lv : e, a) : n }, a }().keys(r).value((function(e, t) { return +hg(e, t, 0) })).order(cv).offset(a); return o(e) },
            Eg = function(e, t) { var n = t.realScaleType,
                    r = t.type,
                    a = t.tickCount,
                    o = t.originalDomain,
                    i = t.allowDecimals,
                    l = n || t.scale; if ("auto" !== l && "linear" !== l) return null; if (a && "number" === r && o && ("auto" === o[0] || "auto" === o[1])) { var s = e.domain(); if (!s.length) return null; var c = Kv(s, a, i); return e.domain([fv()(c), mv()(c)]), { niceTicks: c } } if (a && "number" === r) { var d = e.domain(); return { niceTicks: Zv(d, a, i) } } return null };

        function Cg(e) { var t = e.axis,
                n = e.ticks,
                r = e.bandSize,
                a = e.entry,
                o = e.index,
                i = e.dataKey; if ("category" === t.type) { if (!t.allowDuplicatedCategory && t.dataKey && !Na()(a[t.dataKey])) { var l = Pa(n, "value", a[t.dataKey]); if (l) return l.coordinate + r / 2 } return n[o] ? n[o].coordinate + r / 2 : null } var s = hg(a, Na()(i) ? t.dataKey : i); return Na()(s) ? null : t.scale(s) } var Tg = function(e) { var t = e.axis,
                    n = e.ticks,
                    r = e.offset,
                    a = e.bandSize,
                    o = e.entry,
                    i = e.index; if ("category" === t.type) return n[i] ? n[i].coordinate + r : null; var l = hg(o, t.dataKey, t.domain[i]); return Na()(l) ? null : t.scale(l) - a / 2 + r },
            Hg = function(e, t, n) { return Object.keys(e).reduce((function(r, a) { var o = e[a].stackedData.reduce((function(e, r) { var a = r.slice(t, n + 1).reduce((function(e, t) { return [fv()(t.concat([e[0]]).filter(Ha)), mv()(t.concat([e[1]]).filter(Ha))] }), [1 / 0, -1 / 0]); return [Math.min(e[0], a[0]), Math.max(e[1], a[1])] }), [1 / 0, -1 / 0]); return [Math.min(o[0], r[0]), Math.max(o[1], r[1])] }), [1 / 0, -1 / 0]).map((function(e) { return e === 1 / 0 || e === -1 / 0 ? 0 : e })) },
            Lg = /^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,
            Ig = /^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,
            jg = function(e, t, n) { if (Ba()(e)) return e(t, n); if (!Array.isArray(e)) return t; var r = []; if (Ha(e[0])) r[0] = n ? e[0] : Math.min(e[0], t[0]);
                else if (Lg.test(e[0])) { var a = +Lg.exec(e[0])[1];
                    r[0] = t[0] - a } else Ba()(e[0]) ? r[0] = e[0](t[0]) : r[0] = t[0]; if (Ha(e[1])) r[1] = n ? e[1] : Math.max(e[1], t[1]);
                else if (Ig.test(e[1])) { var o = +Ig.exec(e[1])[1];
                    r[1] = t[1] + o } else Ba()(e[1]) ? r[1] = e[1](t[1]) : r[1] = t[1]; return r },
            Vg = function(e, t, n) { if (e && e.scale && e.scale.bandwidth) { var r = e.scale.bandwidth(); if (!n || r > 0) return r } if (e && t && t.length >= 2) { for (var a = Co()(t, (function(e) { return e.coordinate })), o = 1 / 0, i = 1, l = a.length; i < l; i++) { var s = a[i],
                            c = a[i - 1];
                        o = Math.min((s.coordinate || 0) - (c.coordinate || 0), o) } return o === 1 / 0 ? 0 : o } return n ? void 0 : 0 },
            Og = function(e, t, n) { return e && e.length ? bv()(e, Sa()(n, "type.defaultProps.domain")) ? t : e : t },
            Rg = function(e, t) { var n = e.props,
                    r = n.dataKey,
                    a = n.name,
                    o = n.unit,
                    i = n.formatter,
                    l = n.tooltipType,
                    s = n.chartType,
                    c = n.hide; return sg(sg({}, po(e, !1)), {}, { dataKey: r, unit: o, formatter: i, name: a || r, color: pg(e), value: hg(t, r), type: l, payload: t, chartType: s, hide: c }) };

        function Pg(e) { return Pg = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Pg(e) }

        function Dg(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function Fg(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Dg(Object(n), !0).forEach((function(t) { Ng(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Dg(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Ng(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != Pg(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Pg(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Pg(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var _g = ["Webkit", "Moz", "O", "ms"];

        function Bg(e) { return Bg = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Bg(e) }

        function Wg() { return Wg = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Wg.apply(this, arguments) }

        function Ug(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function qg(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Ug(Object(n), !0).forEach((function(t) { Qg(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ug(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Gg(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, Jg(r.key), r) } }

        function Kg(e, t, n) { return t = Yg(t),
                function(e, t) { if (t && ("object" === Bg(t) || "function" === typeof t)) return t; if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined"); return Xg(e) }(e, Zg() ? Reflect.construct(t, n || [], Yg(e).constructor) : t.apply(e, n)) }

        function Zg() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))) } catch (e) {} return (Zg = function() { return !!e })() }

        function Yg(e) { return Yg = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, Yg(e) }

        function Xg(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e }

        function $g(e, t) { return $g = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) { return e.__proto__ = t, e }, $g(e, t) }

        function Qg(e, t, n) { return (t = Jg(t)) in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function Jg(e) { var t = function(e, t) { if ("object" != Bg(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != Bg(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == Bg(t) ? t : String(t) } var ey = function(e) { return e.changedTouches && !!e.changedTouches.length },
            ty = function(e) {
                function t(e) { var n; return function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), Qg(Xg(n = Kg(this, t, [e])), "handleDrag", (function(e) { n.leaveTimer && (clearTimeout(n.leaveTimer), n.leaveTimer = null), n.state.isTravellerMoving ? n.handleTravellerMove(e) : n.state.isSlideMoving && n.handleSlideDrag(e) })), Qg(Xg(n), "handleTouchMove", (function(e) { null != e.changedTouches && e.changedTouches.length > 0 && n.handleDrag(e.changedTouches[0]) })), Qg(Xg(n), "handleDragEnd", (function() { n.setState({ isTravellerMoving: !1, isSlideMoving: !1 }, (function() { var e = n.props,
                                t = e.endIndex,
                                r = e.onDragEnd,
                                a = e.startIndex;
                            null === r || void 0 === r || r({ endIndex: t, startIndex: a }) })), n.detachDragEndListener() })), Qg(Xg(n), "handleLeaveWrapper", (function() {
                        (n.state.isTravellerMoving || n.state.isSlideMoving) && (n.leaveTimer = window.setTimeout(n.handleDragEnd, n.props.leaveTimeOut)) })), Qg(Xg(n), "handleEnterSlideOrTraveller", (function() { n.setState({ isTextActive: !0 }) })), Qg(Xg(n), "handleLeaveSlideOrTraveller", (function() { n.setState({ isTextActive: !1 }) })), Qg(Xg(n), "handleSlideDragStart", (function(e) { var t = ey(e) ? e.changedTouches[0] : e;
                        n.setState({ isTravellerMoving: !1, isSlideMoving: !0, slideMoveStartX: t.pageX }), n.attachDragEndListener() })), n.travellerDragStartHandlers = { startX: n.handleTravellerDragStart.bind(Xg(n), "startX"), endX: n.handleTravellerDragStart.bind(Xg(n), "endX") }, n.state = {}, n } var n, r, o; return function(e, t) { if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
                    e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, "prototype", { writable: !1 }), t && $g(e, t) }(t, e), n = t, r = [{ key: "componentWillUnmount", value: function() { this.leaveTimer && (clearTimeout(this.leaveTimer), this.leaveTimer = null), this.detachDragEndListener() } }, { key: "getIndex", value: function(e) { var n = e.startX,
                            r = e.endX,
                            a = this.state.scaleValues,
                            o = this.props,
                            i = o.gap,
                            l = o.data.length - 1,
                            s = Math.min(n, r),
                            c = Math.max(n, r),
                            d = t.getIndexInRange(a, s),
                            u = t.getIndexInRange(a, c); return { startIndex: d - d % i, endIndex: u === l ? l : u - u % i } } }, { key: "getTextOfTick", value: function(e) { var t = this.props,
                            n = t.data,
                            r = t.tickFormatter,
                            a = t.dataKey,
                            o = hg(n[e], a, e); return Ba()(r) ? r(o, e) : o } }, { key: "attachDragEndListener", value: function() { window.addEventListener("mouseup", this.handleDragEnd, !0), window.addEventListener("touchend", this.handleDragEnd, !0), window.addEventListener("mousemove", this.handleDrag, !0) } }, { key: "detachDragEndListener", value: function() { window.removeEventListener("mouseup", this.handleDragEnd, !0), window.removeEventListener("touchend", this.handleDragEnd, !0), window.removeEventListener("mousemove", this.handleDrag, !0) } }, { key: "handleSlideDrag", value: function(e) { var t = this.state,
                            n = t.slideMoveStartX,
                            r = t.startX,
                            a = t.endX,
                            o = this.props,
                            i = o.x,
                            l = o.width,
                            s = o.travellerWidth,
                            c = o.startIndex,
                            d = o.endIndex,
                            u = o.onChange,
                            h = e.pageX - n;
                        h > 0 ? h = Math.min(h, i + l - s - a, i + l - s - r) : h < 0 && (h = Math.max(h, i - r, i - a)); var m = this.getIndex({ startX: r + h, endX: a + h });
                        m.startIndex === c && m.endIndex === d || !u || u(m), this.setState({ startX: r + h, endX: a + h, slideMoveStartX: e.pageX }) } }, { key: "handleTravellerDragStart", value: function(e, t) { var n = ey(t) ? t.changedTouches[0] : t;
                        this.setState({ isSlideMoving: !1, isTravellerMoving: !0, movingTravellerId: e, brushMoveStartX: n.pageX }), this.attachDragEndListener() } }, { key: "handleTravellerMove", value: function(e) { var t = this.state,
                            n = t.brushMoveStartX,
                            r = t.movingTravellerId,
                            a = t.endX,
                            o = t.startX,
                            i = this.state[r],
                            l = this.props,
                            s = l.x,
                            c = l.width,
                            d = l.travellerWidth,
                            u = l.onChange,
                            h = l.gap,
                            m = l.data,
                            p = { startX: this.state.startX, endX: this.state.endX },
                            f = e.pageX - n;
                        f > 0 ? f = Math.min(f, s + c - d - i) : f < 0 && (f = Math.max(f, s - i)), p[r] = i + f; var v = this.getIndex(p),
                            g = v.startIndex,
                            y = v.endIndex;
                        this.setState(Qg(Qg({}, r, i + f), "brushMoveStartX", e.pageX), (function() { u && function() { var e = m.length - 1; return "startX" === r && (a > o ? g % h === 0 : y % h === 0) || a < o && y === e || "endX" === r && (a > o ? y % h === 0 : g % h === 0) || a > o && y === e }() && u(v) })) } }, { key: "handleTravellerMoveKeyboard", value: function(e, t) { var n = this,
                            r = this.state,
                            a = r.scaleValues,
                            o = r.startX,
                            i = r.endX,
                            l = this.state[t],
                            s = a.indexOf(l); if (-1 !== s) { var c = s + e; if (!(-1 === c || c >= a.length)) { var d = a[c]; "startX" === t && d >= i || "endX" === t && d <= o || this.setState(Qg({}, t, d), (function() { n.props.onChange(n.getIndex({ startX: n.state.startX, endX: n.state.endX })) })) } } } }, { key: "renderBackground", value: function() { var e = this.props,
                            t = e.x,
                            n = e.y,
                            r = e.width,
                            o = e.height,
                            i = e.fill,
                            l = e.stroke; return a.createElement("rect", { stroke: l, fill: i, x: t, y: n, width: r, height: o }) } }, { key: "renderPanorama", value: function() { var e = this.props,
                            t = e.x,
                            n = e.y,
                            r = e.width,
                            o = e.height,
                            i = e.data,
                            l = e.children,
                            s = e.padding,
                            c = a.Children.only(l); return c ? a.cloneElement(c, { x: t, y: n, width: r, height: o, margin: s, compact: !0, data: i }) : null } }, { key: "renderTravellerLayer", value: function(e, n) { var r = this,
                            o = this.props,
                            i = o.y,
                            l = o.travellerWidth,
                            s = o.height,
                            c = o.traveller,
                            d = o.ariaLabel,
                            u = o.data,
                            h = o.startIndex,
                            m = o.endIndex,
                            p = Math.max(e, this.props.x),
                            f = qg(qg({}, po(this.props, !1)), {}, { x: p, y: i, width: l, height: s }),
                            v = d || "Min value: ".concat(u[h].name, ", Max value: ").concat(u[m].name); return a.createElement(Po, { tabIndex: 0, role: "slider", "aria-label": v, "aria-valuenow": e, className: "recharts-brush-traveller", onMouseEnter: this.handleEnterSlideOrTraveller, onMouseLeave: this.handleLeaveSlideOrTraveller, onMouseDown: this.travellerDragStartHandlers[n], onTouchStart: this.travellerDragStartHandlers[n], onKeyDown: function(e) {
                                ["ArrowLeft", "ArrowRight"].includes(e.key) && (e.preventDefault(), e.stopPropagation(), r.handleTravellerMoveKeyboard("ArrowRight" === e.key ? 1 : -1, n)) }, onFocus: function() { r.setState({ isTravellerFocused: !0 }) }, onBlur: function() { r.setState({ isTravellerFocused: !1 }) }, style: { cursor: "col-resize" } }, t.renderTraveller(c, f)) } }, { key: "renderSlide", value: function(e, t) { var n = this.props,
                            r = n.y,
                            o = n.height,
                            i = n.stroke,
                            l = n.travellerWidth,
                            s = Math.min(e, t) + l,
                            c = Math.max(Math.abs(t - e) - l, 0); return a.createElement("rect", { className: "recharts-brush-slide", onMouseEnter: this.handleEnterSlideOrTraveller, onMouseLeave: this.handleLeaveSlideOrTraveller, onMouseDown: this.handleSlideDragStart, onTouchStart: this.handleSlideDragStart, style: { cursor: "move" }, stroke: "none", fill: i, fillOpacity: .2, x: s, y: r, width: c, height: o }) } }, { key: "renderText", value: function() { var e = this.props,
                            t = e.startIndex,
                            n = e.endIndex,
                            r = e.y,
                            o = e.height,
                            i = e.travellerWidth,
                            l = e.stroke,
                            s = this.state,
                            c = s.startX,
                            d = s.endX,
                            u = { pointerEvents: "none", fill: l }; return a.createElement(Po, { className: "recharts-brush-texts" }, a.createElement(cu, Wg({ textAnchor: "end", verticalAnchor: "middle", x: Math.min(c, d) - 5, y: r + o / 2 }, u), this.getTextOfTick(t)), a.createElement(cu, Wg({ textAnchor: "start", verticalAnchor: "middle", x: Math.max(c, d) + i + 5, y: r + o / 2 }, u), this.getTextOfTick(n))) } }, { key: "render", value: function() { var e = this.props,
                            t = e.data,
                            n = e.className,
                            r = e.children,
                            o = e.x,
                            i = e.y,
                            l = e.width,
                            s = e.height,
                            c = e.alwaysShowText,
                            d = this.state,
                            u = d.startX,
                            h = d.endX,
                            m = d.isTextActive,
                            p = d.isSlideMoving,
                            f = d.isTravellerMoving,
                            v = d.isTravellerFocused; if (!t || !t.length || !Ha(o) || !Ha(i) || !Ha(l) || !Ha(s) || l <= 0 || s <= 0) return null; var g = va("recharts-brush", n),
                            y = 1 === a.Children.count(r),
                            b = function(e, t) { if (!e) return null; var n = e.replace(/(\w)/, (function(e) { return e.toUpperCase() })),
                                    r = _g.reduce((function(e, r) { return Fg(Fg({}, e), {}, Ng({}, r + n, t)) }), {}); return r[e] = t, r }("userSelect", "none"); return a.createElement(Po, { className: g, onMouseLeave: this.handleLeaveWrapper, onTouchMove: this.handleTouchMove, style: b }, this.renderBackground(), y && this.renderPanorama(), this.renderSlide(u, h), this.renderTravellerLayer(u, "startX"), this.renderTravellerLayer(h, "endX"), (m || p || f || v || c) && this.renderText()) } }], o = [{ key: "renderDefaultTraveller", value: function(e) { var t = e.x,
                            n = e.y,
                            r = e.width,
                            o = e.height,
                            i = e.stroke,
                            l = Math.floor(n + o / 2) - 1; return a.createElement(a.Fragment, null, a.createElement("rect", { x: t, y: n, width: r, height: o, fill: i, stroke: "none" }), a.createElement("line", { x1: t + 1, y1: l, x2: t + r - 1, y2: l, fill: "none", stroke: "#fff" }), a.createElement("line", { x1: t + 1, y1: l + 2, x2: t + r - 1, y2: l + 2, fill: "none", stroke: "#fff" })) } }, { key: "renderTraveller", value: function(e, n) { return a.isValidElement(e) ? a.cloneElement(e, n) : Ba()(e) ? e(n) : t.renderDefaultTraveller(n) } }, { key: "getDerivedStateFromProps", value: function(e, t) { var n = e.data,
                            r = e.width,
                            a = e.x,
                            o = e.travellerWidth,
                            i = e.updateId,
                            l = e.startIndex,
                            s = e.endIndex; if (n !== t.prevData || i !== t.prevUpdateId) return qg({ prevData: n, prevTravellerWidth: o, prevUpdateId: i, prevX: a, prevWidth: r }, n && n.length ? function(e) { var t = e.data,
                                n = e.startIndex,
                                r = e.endIndex,
                                a = e.x,
                                o = e.width,
                                i = e.travellerWidth; if (!t || !t.length) return {}; var l = t.length,
                                s = Md().domain(Mo()(0, l)).range([a, a + o - i]),
                                c = s.domain().map((function(e) { return s(e) })); return { isTextActive: !1, isSlideMoving: !1, isTravellerMoving: !1, isTravellerFocused: !1, startX: s(n), endX: s(r), scale: s, scaleValues: c } }({ data: n, width: r, x: a, travellerWidth: o, startIndex: l, endIndex: s }) : { scale: null, scaleValues: null }); if (t.scale && (r !== t.prevWidth || a !== t.prevX || o !== t.prevTravellerWidth)) { t.scale.range([a, a + r - o]); var c = t.scale.domain().map((function(e) { return t.scale(e) })); return { prevData: n, prevTravellerWidth: o, prevUpdateId: i, prevX: a, prevWidth: r, startX: t.scale(e.startIndex), endX: t.scale(e.endIndex), scaleValues: c } } return null } }, { key: "getIndexInRange", value: function(e, t) { for (var n = 0, r = e.length - 1; r - n > 1;) { var a = Math.floor((n + r) / 2);
                            e[a] > t ? r = a : n = a } return t >= e[r] ? r : n } }], r && Gg(n.prototype, r), o && Gg(n, o), Object.defineProperty(n, "prototype", { writable: !1 }), t }(a.PureComponent);

        function ny(e) { return ny = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, ny(e) }

        function ry(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function ay(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? ry(Object(n), !0).forEach((function(t) { oy(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ry(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function oy(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != ny(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != ny(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == ny(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function iy(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var r, a, o, i, l = [],
                        s = !0,
                        c = !1; try { if (o = (n = n.call(e)).next, 0 === t) { if (Object(n) !== n) return;
                            s = !1 } else
                            for (; !(s = (r = o.call(n)).done) && (l.push(r.value), l.length !== t); s = !0); } catch (e) { c = !0, a = e } finally { try { if (!s && null != n.return && (i = n.return(), Object(i) !== i)) return } finally { if (c) throw a } } return l } }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return ly(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return ly(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function ly(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } Qg(ty, "displayName", "Brush"), Qg(ty, "defaultProps", { height: 40, travellerWidth: 5, gap: 1, fill: "#fff", stroke: "#666", padding: { top: 1, right: 1, bottom: 1, left: 1 }, leaveTimeOut: 1e3, alwaysShowText: !1 }); var sy = Math.PI / 180,
            cy = function(e) { return 180 * e / Math.PI },
            dy = function(e, t, n, r) { return { x: e + Math.cos(-sy * r) * n, y: t + Math.sin(-sy * r) * n } },
            uy = function(e, t) { var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : { top: 0, right: 0, bottom: 0, left: 0 }; return Math.min(Math.abs(e - (n.left || 0) - (n.right || 0)), Math.abs(t - (n.top || 0) - (n.bottom || 0))) / 2 },
            hy = function(e, t) { var n = e.x,
                    r = e.y,
                    a = t.cx,
                    o = t.cy,
                    i = function(e, t) { var n = e.x,
                            r = e.y,
                            a = t.x,
                            o = t.y; return Math.sqrt(Math.pow(n - a, 2) + Math.pow(r - o, 2)) }({ x: n, y: r }, { x: a, y: o }); if (i <= 0) return { radius: i }; var l = (n - a) / i,
                    s = Math.acos(l); return r > o && (s = 2 * Math.PI - s), { radius: i, angle: cy(s), angleInRadian: s } },
            my = function(e, t) { var n = t.startAngle,
                    r = t.endAngle,
                    a = Math.floor(n / 360),
                    o = Math.floor(r / 360); return e + 360 * Math.min(a, o) },
            py = function(e, t) { var n = e.x,
                    r = e.y,
                    a = hy({ x: n, y: r }, t),
                    o = a.radius,
                    i = a.angle,
                    l = t.innerRadius,
                    s = t.outerRadius; if (o < l || o > s) return !1; if (0 === o) return !0; var c, d = function(e) { var t = e.startAngle,
                            n = e.endAngle,
                            r = Math.floor(t / 360),
                            a = Math.floor(n / 360),
                            o = Math.min(r, a); return { startAngle: t - 360 * o, endAngle: n - 360 * o } }(t),
                    u = d.startAngle,
                    h = d.endAngle,
                    m = i; if (u <= h) { for (; m > h;) m -= 360; for (; m < u;) m += 360;
                    c = m >= u && m <= h } else { for (; m > u;) m -= 360; for (; m < h;) m += 360;
                    c = m >= h && m <= u } return c ? ay(ay({}, t), {}, { radius: o, angle: my(m, t) }) : null },
            fy = function(e) { return (0, a.isValidElement)(e) || Ba()(e) || "boolean" === typeof e ? "" : e.className };

        function vy(e) { return vy = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, vy(e) } var gy = ["offset"];

        function yy(e) { return function(e) { if (Array.isArray(e)) return by(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return by(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return by(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function by(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function wy(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a }

        function zy(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function xy(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? zy(Object(n), !0).forEach((function(t) { Ay(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : zy(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function Ay(e, t, n) { return t = function(e) { var t = function(e, t) { if ("object" != vy(e) || !e) return e; var n = e[Symbol.toPrimitive]; if (void 0 !== n) { var r = n.call(e, t || "default"); if ("object" != vy(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === t ? String : Number)(e) }(e, "string"); return "symbol" == vy(t) ? t : String(t) }(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function ky() { return ky = Object.assign ? Object.assign.bind() : function(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, ky.apply(this, arguments) } var Sy = function(e) { var t = e.value,
                    n = e.formatter,
                    r = Na()(e.children) ? t : e.children; return Ba()(n) ? n(r) : r },
            My = function(e, t, n) { var r, o, i = e.position,
                    l = e.viewBox,
                    s = e.offset,
                    c = e.className,
                    d = l,
                    u = d.cx,
                    h = d.cy,
                    m = d.innerRadius,
                    p = d.outerRadius,
                    f = d.startAngle,
                    v = d.endAngle,
                    g = d.clockWise,
                    y = (m + p) / 2,
                    b = function(e, t) { return Ca(t - e) * Math.min(Math.abs(t - e), 360) }(f, v),
                    w = b >= 0 ? 1 : -1; "insideStart" === i ? (r = f + w * s, o = g) : "insideEnd" === i ? (r = v - w * s, o = !g) : "end" === i && (r = v + w * s, o = g), o = b <= 0 ? o : !o; var z = dy(u, h, y, r),
                    x = dy(u, h, y, r + 359 * (o ? 1 : -1)),
                    A = "M".concat(z.x, ",").concat(z.y, "\n    A").concat(y, ",").concat(y, ",0,1,").concat(o ? 0 : 1, ",\n    ").concat(x.x, ",").concat(x.y),
                    k = Na()(e.id) ? ja("recharts-radial-line-") : e.id; return a.createElement("text", ky({}, n, { dominantBaseline: "central", className: va("recharts-radial-bar-label", c) }), a.createElement("defs", null, a.createElement("path", { id: k, d: A })), a.createElement("textPath", { xlinkHref: "#".concat(k) }, t)) },
            Ey = function(e) { var t = e.viewBox,
                    n = e.offset,
                    r = e.position,
                    a = t,
                    o = a.cx,
                    i = a.cy,
                    l = a.innerRadius,
                    s = a.outerRadius,
                    c = (a.startAngle + a.endAngle) / 2; if ("outside" === r) { var d = dy(o, i, s + n, c),
                        u = d.x; return { x: u, y: d.y, textAnchor: u >= o ? "start" : "end", verticalAnchor: "middle" } } if ("center" === r) return { x: o, y: i, textAnchor: "middle", verticalAnchor: "middle" }; if ("centerTop" === r) return { x: o, y: i, textAnchor: "middle", verticalAnchor: "start" }; if ("centerBottom" === r) return { x: o, y: i, textAnchor: "middle", verticalAnchor: "end" }; var h = dy(o, i, (l + s) / 2, c); return { x: h.x, y: h.y, textAnchor: "middle", verticalAnchor: "middle" } },
            Cy = function(e) { var t = e.viewBox,
                    n = e.parentViewBox,
                    r = e.offset,
                    a = e.position,
                    o = t,
                    i = o.x,
                    l = o.y,
                    s = o.width,
                    c = o.height,
                    d = c >= 0 ? 1 : -1,
                    u = d * r,
                    h = d > 0 ? "end" : "start",
                    m = d > 0 ? "start" : "end",
                    p = s >= 0 ? 1 : -1,
                    f = p * r,
                    v = p > 0 ? "end" : "start",
                    g = p > 0 ? "start" : "end"; if ("top" === a) return xy(xy({}, { x: i + s / 2, y: l - d * r, textAnchor: "middle", verticalAnchor: h }), n ? { height: Math.max(l - n.y, 0), width: s } : {}); if ("bottom" === a) return xy(xy({}, { x: i + s / 2, y: l + c + u, textAnchor: "middle", verticalAnchor: m }), n ? { height: Math.max(n.y + n.height - (l + c), 0), width: s } : {}); if ("left" === a) { var y = { x: i - f, y: l + c / 2, textAnchor: v, verticalAnchor: "middle" }; return xy(xy({}, y), n ? { width: Math.max(y.x - n.x, 0), height: c } : {}) } if ("right" === a) { var b = { x: i + s + f, y: l + c / 2, textAnchor: g, verticalAnchor: "middle" }; return xy(xy({}, b), n ? { width: Math.max(n.x + n.width - b.x, 0), height: c } : {}) } var w = n ? { width: s, height: c } : {}; return "insideLeft" === a ? xy({ x: i + f, y: l + c / 2, textAnchor: g, verticalAnchor: "middle" }, w) : "insideRight" === a ? xy({ x: i + s - f, y: l + c / 2, textAnchor: v, verticalAnchor: "middle" }, w) : "insideTop" === a ? xy({ x: i + s / 2, y: l + u, textAnchor: "middle", verticalAnchor: m }, w) : "insideBottom" === a ? xy({ x: i + s / 2, y: l + c - u, textAnchor: "middle", verticalAnchor: h }, w) : "insideTopLeft" === a ? xy({ x: i + f, y: l + u, textAnchor: g, verticalAnchor: m }, w) : "insideTopRight" === a ? xy({ x: i + s - f, y: l + u, textAnchor: v, verticalAnchor: m }, w) : "insideBottomLeft" === a ? xy({ x: i + f, y: l + c - u, textAnchor: g, verticalAnchor: h }, w) : "insideBottomRight" === a ? xy({ x: i + s - f, y: l + c - u, textAnchor: v, verticalAnchor: h }, w) : Ua()(a) && (Ha(a.x) || Ta(a.x)) && (Ha(a.y) || Ta(a.y)) ? xy({ x: i + Va(a.x, s), y: l + Va(a.y, c), textAnchor: "end", verticalAnchor: "end" }, w) : xy({ x: i + s / 2, y: l + c / 2, textAnchor: "middle", verticalAnchor: "middle" }, w) },
            Ty = function(e) { return "cx" in e && Ha(e.cx) };

        function Hy(e) { var t, n = e.offset,
                r = xy({ offset: void 0 === n ? 5 : n }, wy(e, gy)),
                o = r.viewBox,
                i = r.position,
                l = r.value,
                s = r.children,
                c = r.content,
                d = r.className,
                u = void 0 === d ? "" : d,
                h = r.textBreakAll; if (!o || Na()(l) && Na()(s) && !(0, a.isValidElement)(c) && !Ba()(c)) return null; if ((0, a.isValidElement)(c)) return (0, a.cloneElement)(c, r); if (Ba()(c)) { if (t = (0, a.createElement)(c, r), (0, a.isValidElement)(t)) return t } else t = Sy(r); var m = Ty(o),
                p = po(r, !0); if (m && ("insideStart" === i || "insideEnd" === i || "end" === i)) return My(r, t, p); var f = m ? Ey(r) : Cy(r); return a.createElement(cu, ky({ className: va("recharts-label", u) }, p, f, { breakAll: h }), t) } Hy.displayName = "Label"; var Ly = function(e) { var t = e.cx,
                n = e.cy,
                r = e.angle,
                a = e.startAngle,
                o = e.endAngle,
                i = e.r,
                l = e.radius,
                s = e.innerRadius,
                c = e.outerRadius,
                d = e.x,
                u = e.y,
                h = e.top,
                m = e.left,
                p = e.width,
                f = e.height,
                v = e.clockWise,
                g = e.labelViewBox; if (g) return g; if (Ha(p) && Ha(f)) { if (Ha(d) && Ha(u)) return { x: d, y: u, width: p, height: f }; if (Ha(h) && Ha(m)) return { x: h, y: m, width: p, height: f } } return Ha(d) && Ha(u) ? { x: d, y: u, width: 0, height: 0 } : Ha(t) && Ha(n) ? { cx: t, cy: n, startAngle: a || r || 0, endAngle: o || r || 0, innerRadius: s || 0, outerRadius: c || l || i || 0, clockWise: v } : e.viewBox ? e.viewBox : {} };
        Hy.parseViewBox = Ly, Hy.renderCallByParent = function(e, t) { var n = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2]; if (!e || !e.children && n && !e.label) return null; var r = e.children,
                o = Ly(e),
                i = so(r, Hy).map((function(e, n) { return (0, a.cloneElement)(e, { viewBox: t || o, key: "label-".concat(n) }) })); if (!n) return i; var l = function(e, t) { return e ? !0 === e ? a.createElement(Hy, { key: "label-implicit", viewBox: t }) : La(e) ? a.createElement(Hy, { key: "label-implicit", viewBox: t, value: e }) : (0, a.isValidElement)(e) ? e.type === Hy ? (0, a.cloneElement)(e, { key: "label-implicit", viewBox: t }) : a.createElement(Hy, { key: "label-implicit", content: e, viewBox: t }) : Ba()(e) ? a.createElement(Hy, { key: "label-implicit", content: e, viewBox: t }) : Ua()(e) ? a.createElement(Hy, ky({ viewBox: t }, e, { key: "label-implicit" })) : null : null }(e.label, t || o); return [l].concat(yy(i)) }; var Iy = function(e, t) { var n = e.alwaysShow,
                    r = e.ifOverflow; return n && (r = "extendDomain"), r === t },
            jy = n(91733),
            Vy = n.n(jy),
            Oy = n(17002),
            Ry = n.n(Oy),
            Py = function(e) { return null };
        Py.displayName = "Cell"; var Dy = n(74065),
            Fy = n.n(Dy);

        function Ny(e) { return Ny = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) { return typeof e } : function(e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Ny(e) } var _y = ["valueAccessor"],
            By = ["data", "dataKey", "clockWise", "id", "textBreakAll"];

        function Wy(e) { return function(e) { if (Array.isArray(e)) return Uy(e) }(e) || function(e) { if ("undefined" !== typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) }(e) || function(e, t) { if (!e) return; if ("string" === typeof e) return Uy(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return Uy(e, t) }(e) || function() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function Uy(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

