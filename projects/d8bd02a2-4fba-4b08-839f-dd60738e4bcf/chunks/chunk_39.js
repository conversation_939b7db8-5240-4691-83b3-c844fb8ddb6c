                    P = (0, r.Mz)(b, c.kA, ((e, t) => { var n, r, a; const o = { type: "photo", name: "avatar", attr: "avatar", label: "", align: "center", sortable: !1 }; if ((null === e || void 0 === e || null === (n = e.data) || void 0 === n || null === (r = n.directory) || void 0 === r || null === (a = r.columns) || void 0 === a ? void 0 : a.length) > 0) { const n = null === t || void 0 === t ? void 0 : t.map((e => e.id)); let r = e.data.directory.columns.filter((e => n.includes(e.field.id) || !e.field.id)); if ((null === r || void 0 === r ? void 0 : r.length) > 0) { var i; let n = null === r || void 0 === r ? void 0 : r.map((e => { var n, r; let a = t.find((t => t.name === e.field.name && t.model === e.field.model)); return { ...e.field, id: (null === (n = e.field) || void 0 === n ? void 0 : n.id) || (null === a || void 0 === a ? void 0 : a.id) || e.field.name, attr: (null === (r = e.field) || void 0 === r ? void 0 : r.id) || (null === a || void 0 === a ? void 0 : a.id) || e.field.name, align: "left", sortable: !0, fontDetails: e.fontDetails, typeMetadata: (null === a || void 0 === a ? void 0 : a.typeMetadata) || null } })); return null !== e && void 0 !== e && null !== (i = e.base) && void 0 !== i && i.directoryPhotos ? [o, ...n] : [...n] } } return [o, ...t.map((e => ({ ...e, sortable: !0, align: "left", attr: e.id })))] })),
                    D = (0, r.Mz)(b, (e => null === e || void 0 === e ? void 0 : e.chartOptions)),
                    F = (0, r.Mz)(b, (e => null === e || void 0 === e ? void 0 : e.chartOptions.legend)),
                    N = ((0, r.Mz)(b, (e => ((null === e || void 0 === e ? void 0 : e.data) || []).reduce(((e, t) => (e[t.fieldKey] = t, e)), []))), (0, r.Mz)(b, (e => { var t, n; return (null === e || void 0 === e || null === (t = e.orgStructure) || void 0 === t || null === (n = t.roleCounts) || void 0 === n ? void 0 : n.visible) || !1 }))),
                    _ = ((0, r.Mz)(b, (e => { var t; return "condensed" === (null === e || void 0 === e || null === (t = e.chartOptions) || void 0 === t ? void 0 : t.stackDirectionShared) })), e => { let { roleId: t, inChain: n, roleType: a } = e; return (0, r.Mz)(v.A, d.xt, c.Op, c.C1, b, i.mn, o.uo, c.gJ, ((e, r, o, i, l, c, d, h) => { var m, p, v, y, b, w, z, x, A, k, S, M, E; const C = [f.T.Assistant, f.T.Single, f.T.Shared, f.T.Location],
                                T = null !== l && void 0 !== l && null !== (m = l.roleTypeOverrides) && void 0 !== m && null !== (p = m[a]) && void 0 !== p && p.enabled ? null === l || void 0 === l || null === (v = l.roleTypeOverrides) || void 0 === v ? void 0 : v[a] : {},
                                H = { ...l.base, ...T }; let L = (null === T || void 0 === T ? void 0 : T.textAlign) || (null === H || void 0 === H || null === (y = H.cards) || void 0 === y ? void 0 : y.textAlign); const { cards: { fontFamily: I, cardFrame: { color: j } }, photos: V } = H, O = C.includes(a), R = !(null === V || void 0 === V || null === (b = V.standard) || void 0 === b || !b.visible), P = null === V || void 0 === V ? void 0 : V.position;
                            L = O && R ? g[P] : L; const D = c ? i : o,
                                F = "single" === a ? (null === l || void 0 === l || null === (w = l.data) || void 0 === w ? void 0 : w.chart) || [] : null !== l && void 0 !== l && null !== (z = l.roleTypeOverrides) && void 0 !== z && null !== (x = z[a]) && void 0 !== x && x.enabled ? (null === l || void 0 === l || null === (A = l.roleTypeOverrides) || void 0 === A || null === (k = A[a]) || void 0 === k ? void 0 : k.fields) || [] : (null === l || void 0 === l || null === (S = l.data) || void 0 === S ? void 0 : S.chart) || [],
                                N = e[t]; if (!N) return { topData: null, cardData: [] }; const { chartOptions: { stackDirectionShared: _ } } = l, W = null === N || void 0 === N || null === (M = N.sharedStackOverrides) || void 0 === M ? void 0 : M.direction, U = W && "inherit" !== W ? W : _, q = { role: N, members: (N.members || []).map((e => r[e])) }, { role: { type: G } } = q, K = null === l || void 0 === l || null === (E = l.chartOptions) || void 0 === E ? void 0 : E.vacantText, Z = "condensed" === U && "shared" === G, Y = !I || I.toLowerCase && "poppins" === I.toLowerCase() ? "arial" : I, X = [u.uI.PRINT, u.uI.PRINT_PREVIEW].includes(d) ? Y : I || "poppins", $ = function(e) { var t; let { stackDirectionShared: n, textAlign: r, frameColor: a, fontFamily: o, data: i, fields: l = [], photoFormat: c = {}, type: d = "single", emptyLabel: h = "[ empty ]", availableFields: m, orgId: p, defaultFieldsMap: f = {} } = e; const { noPhoto: v, standard: g, vacant: y } = c, b = "condensed" === n && "shared" === d; let w, z;

                                function x(e) { let { objectId: t, model: n, attr: r, type: a, value: o, typeMetadata: i } = e; return "attachment" === a.toLowerCase() && o && o.code ? "/organizations/".concat(p, "/attachments/").concat(n.toLowerCase(), "s/").concat(t, "/").concat(r) : "currency" === a.toLowerCase() && o ? (0, s.Jw)(o, null === i || void 0 === i ? void 0 : i.currency) : "rollup" === a.toLowerCase() && o ? (0, s.$)(o, { typeMetadata: i }) : o } const A = { separatorColor: "shared" === d && "condensed" === n && a || null };

                                function k(e, t) { var n, a; const { skipRole: c, skipMember: p } = t || {}; let w = {},
                                        z = {},
                                        A = {}; const k = null === (n = f[s.x2.LINKEDIN]) || void 0 === n ? void 0 : n.id;
                                    Object.keys(e.member).includes(k) && (A.linkedIn = e.member[k]); const S = /single|shared|assistant/.test(d) && !p,
                                        M = !(null === (a = e.member) || void 0 === a || !a.id),
                                        E = d === u.mv.TEAM || d === u.mv.FUNCTION; let C = l.filter((e => { var t; return !!e && !!m[null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.id] && !!e.field })); if (E) { const e = C.map((e => { var t; return null === (t = e.field) || void 0 === t ? void 0 : t.id })),
                                            t = f.roleName;
                                        null !== t && void 0 !== t && t.id && !e.includes(t.id) && C.push({ field: t }) } for (let i of C) { var T, H, L; let t = i.field.model,
                                            n = i.field.id,
                                            a = x({ objectId: e[t.toLowerCase()].id, model: t, attr: n, type: i.field.type, typeMetadata: null === (T = m[n]) || void 0 === T ? void 0 : T.typeMetadata, value: e[t.toLowerCase()][n] }); if (("member" !== t || S) && ("role" !== t || !c))
                                            if (w[n] = { model: t.toLowerCase(), fieldName: n, modelId: e[t.toLowerCase()].id, value: a, type: m[n] ? m[n].type : "string", display: m[n] ? (null === (H = m[n]) || void 0 === H || null === (L = H.labelOverride) || void 0 === L ? void 0 : L[d]) || m[n].label : t + " " + n, fontDetails: { ...i.fontDetails, align: b ? "left" : r || "center", fontFamily: o || "poppins" } }, S && "member" === i.field.model && "name" === n && !M && h && (w[n].value = h), "embedded.roleCount" === n) w[n] && parseInt(w[n]) > 0 ? w[n].value += " positions" : w[n].value = null;
                                            else if ("shared" === e.role.type && "role" === i.field.model && "title" === n && e.member.roleTitles && e.member.roleTitles.length) { let n = (e.member.roleTitles || []).find((t => t.roleId === e.role.id));
                                            w["role.title"] = { value: n && n.title || e[t.toLowerCase()].name } } } var I; if (z.fallbackPhotoAvatar = v.imageAvatarId, ["team", "function"].includes(d)) z.hidden = !0;
                                    else if (null !== g && void 0 !== g && g.visible && "location" === d) z.value = B(i.role[null === (I = f[s.dj.LOCATIONADDRESS]) || void 0 === I ? void 0 : I.id]);
                                    else if ("embedded" === d) z.value = "".concat("https://app.organimi.com", "/images/logos/organimi_chartlogo_small.png");
                                    else if ("location" !== d && "department" !== d) { var j, V;
                                        null !== (j = e.member) && void 0 !== j && j.id || !g.visible || !y.visible ? null !== (V = e.member) && void 0 !== V && V.id && !e.member.photo && g.visible && v.visible ? (z.avatarId = v.imageAvatarId, "initials" === v.imageAvatarId && (z.name = e.member.name)) : e.member.photo && g.visible ? (z.value = e.member.photo, z.name = e.member.name) : z.hidden = !0 : z.avatarId = y.imageAvatarId } else z.hidden = !0; return "shared" === d && (w["member.id"] = { value: e.member.id, type: "hidden" }), { mappedData: w, mappedPhoto: z, socialData: A } } let S = [...i.members]; if ("shared" === d && ((null === S || void 0 === S ? void 0 : S.length) > 0 || (null === (t = i.role) || void 0 === t ? void 0 : t.expectedMemberCount) > 0)) { var M; const e = []; if (w = [], z = [], "condensed" === n) { const { mappedData: t, mappedPhoto: n, socialData: r } = k({ role: i.role, member: {} }, { skipMember: !0 });
                                        e.push({ fieldData: t, photoData: n, socialData: r }) } for (let r of S) { const { mappedData: t, mappedPhoto: a, socialData: o } = k({ role: i.role, member: r || {} }, { skipRole: "condensed" === n });
                                        e.push({ fieldData: t, photoData: a, socialData: o, cardDisplay: A }) } let t = Math.max(((null === i || void 0 === i || null === (M = i.role) || void 0 === M ? void 0 : M.expectedMemberCount) || 0) - (null === S || void 0 === S ? void 0 : S.length), 0); for (let r = 0; r < t; r++) { const { mappedData: t, mappedPhoto: r, socialData: a } = k({ role: null === i || void 0 === i ? void 0 : i.role, member: { firstName: h } }, { skipRole: "condensed" === n });
                                        e.push({ fieldData: t, photoData: r, socialData: a, cardDisplay: A }) } return e } { i.member = S[0] || {}; const { mappedData: e, mappedPhoto: t, socialData: n } = k(i); return w = e, z = t, [{ fieldData: w, photoData: z, socialData: n, cardDisplay: A }] } }({ stackDirectionShared: U, textAlign: ["embedded", "function", "team"].includes(G) ? "center" : L, frameColor: j, fontFamily: X, data: q, fields: F, photoFormat: V, type: n && "shared" === G ? "single" : G, availableFields: D, emptyLabel: K, orgId: c, defaultFieldsMap: h }); return { topData: Z ? $[0] : null, cardData: Z ? $.slice(1) : $ } })) }),
                    B = e => "https://maps.googleapis.com/maps/api/staticmap?center=" + (e || "Earth") + "&zoom=" + (e ? 10 : 1) + "&style=feature:all|element:labels|visibility:off&size=100x100&maptype=roadmap&key=AIzaSyAzfw1Br-kTzMlKIeCkwn6h8SRjf2wdWW4";
                (0, r.Mz)(D, (e => (null === e || void 0 === e ? void 0 : e.vacantText) || "Vacant Role")); const W = (0, r.Mz)(D, (e => null === e || void 0 === e ? void 0 : e.orderBy)),
                    U = (0, r.Mz)(D, (e => null === e || void 0 === e ? void 0 : e.orderAutomatically));
                (0, r.Mz)(W, U, ((e, t) => t && ["department", "firstName", "lastName", "name"].includes(e))) }, 23993: (e, t, n) => { "use strict";
                n.d(t, { yF: () => $t, CA: () => Qt, dh: () => Mt, jS: () => en, Cq: () => an, q8: () => yt, v7: () => gt, P_: () => kt, Xp: () => De, WD: () => Ct, Ty: () => ee, BU: () => Rt, Vs: () => ot, Es: () => rt, S0: () => Ft, of: () => Te, fv: () => pt, sz: () => ft, Zo: () => Ze, iR: () => qe, zZ: () => Ot, lA: () => Ue, Qu: () => te, DK: () => Pt, jc: () => ke, mw: () => zt, k3: () => St, J1: () => We, AB: () => Vt, x1: () => je, Gr: () => nn, Z6: () => Kt, KZ: () => tn, aC: () => rn, GJ: () => ve, KO: () => ge, VL: () => ye, vr: () => Ne, vZ: () => Je, YG: () => ne, Fk: () => Ht, pQ: () => Nt, Fg: () => me, LH: () => Bt, d7: () => tt, yN: () => Ut, MM: () => Gt, N1: () => ue, Zd: () => Ie, xC: () => Et, c5: () => Ve, GB: () => Xe, vv: () => Be, tG: () => Ee, bO: () => it, $V: () => vt, AO: () => Ke, Lj: () => Ge, ox: () => ct, eK: () => ut, P1: () => ht, ap: () => qt, $o: () => _e, YI: () => Ye, Xz: () => jt, a2: () => be, ei: () => Oe, eP: () => Re, Js: () => Jt, is: () => pe, ui: () => Yt, A0: () => Xt, uN: () => fe, gn: () => J, qs: () => X, tc: () => wt, UX: () => bt }); var r = n(80192),
                    a = n(43862),
                    o = n(81780),
                    i = n(46623),
                    l = n(78396);

                function s() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "horizontal",
                        t = arguments.length > 1 ? arguments[1] : void 0,
                        n = arguments.length > 2 ? arguments[2] : void 0,
                        r = arguments.length > 3 ? arguments[3] : void 0; if ("vertical" === e) return [r]; if ("horizontal" === e) return [...r.map((e => [e]))]; const a = [],
                        o = Math.max(t, Math.ceil(r.length / n)); for (let i = 0, l = 0; i < r.length; i++, l = i % o) a[l] = a[l] || [], a[l].push(r[i]); return a }

                function c(e, t, n, r, a, o, i, l) { return "leftright" === l ? d.apply(this, arguments) : u.apply(this, arguments) }

                function d(e, t, n, r, a, o, i, l) { let s = !1; for (let u = 0; u < e.length; u++) { const m = e[u],
                            p = "leaf" === (e[u + 1] || {}).kind; if ("root" !== o || e.length > 1) { const { kind: o, nodes: u } = m || { kind: "branch", nodes: [] }; if ("leaf" !== o || 1 === e.length && 1 === u.length)
                                for (const [e, d] of (m.nodes || []).entries()) { var c; const o = n ? a[d] : r[d]; if (!o) continue; const u = a[m.nodes[0]];
                                    h.call(this, t, o, u, m, e, (null === (c = i[d]) || void 0 === c ? void 0 : c.height) || 0, l), s = !1 } else { var d; const o = m.nodes[0],
                                        l = n ? a[o] : r[o]; if (!l) continue; const c = a[m.nodes[0]]; let u = 1,
                                        h = 16,
                                        f = 16,
                                        v = c.left - (t.left + t.w1) - f - ((null === (d = i[o]) || void 0 === d ? void 0 : d.height) || 0),
                                        g = !1; const y = () => ({ parentId: t.id, childId: l.id, percent: 1, source: { x: t.left + t.w1, y: t.top + t.h1 / 2 }, target: { x: t.left + t.w1 + v, y: t.top + t.h1 / 2 } }),
                                        b = () => ({ parentId: t.id, childId: l.id, percent: u, source: { x: t.left + t.w1 + v, y: t.top + t.h1 / 2 }, target: { x: l.left, y: l.top - h / 2 }, type: "cluster" }),
                                        w = () => ({ percent: u, parentId: t.id, childId: l.id, source: { x: t.left + t.w1 + v, y: t.top + t.h1 / 2 }, target: { x: l.left, y: l.top + l.h1 + h / 2 }, type: "cluster" }); if (g || (this.push(y()), g = !0), 1 === (null === e || void 0 === e ? void 0 : e.length)) { const e = b(),
                                            t = w();
                                        this.push(e, t) } else if (p) { const e = b();
                                        s || (this.push(e), s = !0) } else { const e = w(u, l, h);
                                        this.push(e), s = !1 } } } } }

                function u(e, t, n, r, a, o, i, l) { let s = !1,
                        c = "bottomup" === l; for (let m = 0; m < e.length; m++) { const p = e[m],
                            f = "leaf" === (e[m + 1] || {}).kind; if ("root" !== o || e.length > 1) { const { kind: o, nodes: m } = p || { kind: "branch", nodes: [] }; if ("leaf" !== o || 1 === e.length && 1 === m.length)
                                for (const [e, c] of (p.nodes || []).entries()) { var d; const o = n ? a[c] : r[c]; if (!o) continue; const u = a[p.nodes[0]];
                                    h.call(this, t, o, u, p, e, (null === (d = i[c]) || void 0 === d ? void 0 : d.height) || 0, l), s = !1 } else { var u; const o = p.nodes[0],
                                        l = n ? a[o] : r[o]; if (!l) continue; const d = a[p.nodes[0]]; let h = .7,
                                        m = 16,
                                        v = c ? -16 : 16,
                                        g = d.top - (t.top + t.h1),
                                        y = g - v - ((null === (u = i[o]) || void 0 === u ? void 0 : u.height) || 0);
                                    h = y / g; const b = () => ({ parentId: t.id, childId: l.id, percent: h, source: { x: t.left + t.w1 / 2, y: t.top + t.h1 }, target: { x: d.left - m / 4, y: l.top + (c ? l.h1 : 0) }, type: "cluster" }),
                                        w = () => ({ parentId: t.id, childId: l.id, percent: h, source: { x: t.left + t.w1 / 2, y: t.top + t.h1 }, target: { x: l.left + l.w1 + m / 4, y: l.top + (c ? l.h1 : 0) }, type: "cluster" }); if (1 === (null === e || void 0 === e ? void 0 : e.length)) { const e = b(),
                                            t = w();
                                        this.push(e, t) } else if (f) { const e = b();
                                        s || (this.push(e), s = !0) } else { const e = w(h, l, m);
                                        this.push(e), s = !1 } } } } }

                function h(e) { return "leftright" === e ? m.apply(this, arguments) : p.apply(this, arguments) }

                function m(e, t, n, r, a) { let o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 0,
                        i = arguments.length > 7 ? arguments[7] : void 0; const { kind: l, nodes: s } = r || { kind: "branch", nodes: [] }, c = "assistant" === l; if ("leaf" !== l || 1 === (null === s || void 0 === s ? void 0 : s.length)) { let n = .7,
                            r = 16,
                            l = t.left - (e.left + e.w1) - r - o;
                        n = c ? 0 : 1, (0 === e.left || i) && (n = 1); let s = e.left + e.w1 + l,
                            d = t.left + (c ? t.w1 / 2 : 0),
                            u = e.top + e.h1 / 2,
                            h = t.top + t.h1 / 2;
                        this.push({ parentId: e.id, childId: t.id, percent: n, source: { x: s, y: u }, target: { x: d, y: h } }), 0 === a && this.push({ parentId: e.id, childId: t.id, percent: 1, source: { x: e.left + e.w1, y: e.top + e.h1 / 2 }, target: { x: e.left + e.w1 + l, y: e.top + e.h1 / 2 } }) } else { let r = c ? 0 : 1,
                            i = 8,
                            l = 16,
                            s = n.left - (e.left + e.w1) - l - o; const d = { parentId: e.id, childId: t.id, percent: r, source: { x: e.left + e.w1 + s, y: e.top + e.h1 / 2 }, target: { x: n.left - i, y: n.top - i } };
                        0 === a && (this.push({ parentId: e.id, childId: t.id, percent: 1, source: { x: e.left + e.w1, y: e.top + e.h1 / 2 }, target: { x: e.left + e.w1 + s, y: e.top + e.h1 / 2 } }), this.push(d)), this.push({ parentId: e.id, childId: t.id, percent: 0, source: { x: d.target.x, y: d.target.y }, target: { x: t.left + t.w1 / 2, y: t.top } }) } }

                function p(e, t, n, r, a) { let o = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 0,
                        i = arguments.length > 6 ? arguments[6] : void 0,
                        l = arguments.length > 7 ? arguments[7] : void 0; const s = "bottomup" === i,
                        c = "leftright" === i,
                        { kind: d, nodes: u } = r || { kind: "branch", nodes: [] },
                        h = "assistant" === d; if ("leaf" !== d || 1 === (null === u || void 0 === u ? void 0 : u.length)) { let n = .7,
                            r = 16,
                            a = s ? -16 : 16,
                            i = c ? t.left - (e.left + e.w1) : t.top - (e.top + e.h1);
                        n = (i - a - o) / i, h && (n = 1), (0 === e.top || l) && (n = 0), c && (n = h ? 0 : 1); let d = e.left,
                            u = t.left;
                        c ? (d += e.w1 + 16, u += h ? t.w1 / 2 : 0) : (d += e.w1 / 2, u += t.w1 / 2), this.push({ parentId: e.id, childId: t.id, percent: n, source: { x: d, y: e.top + (c ? e.h1 / 2 : h ? r + e.h1 : e.h1) }, markerPoint: { x: u, y: h ? t.top + t.h1 / 2 : t.top - a + (s ? t.h1 : 0) + (c ? t.h1 / 2 : 0) }, target: { x: u, y: h ? t.top + t.h1 / 2 : t.top + (s ? t.h1 : 0) + (c ? t.h1 / 2 : 0) } }), c && this.push({ parentId: e.id, childId: t.id, percent: 1, markerPoint: { x: e.left + e.w1 + r, y: e.top + e.h1 / 2, color: "blue" }, source: { x: e.left + e.w1, y: e.top + e.h1 / 2 }, target: { x: e.left + e.w1 + r, y: e.top + e.h1 / 2 } }) } else { let r = .7,
                            i = 16,
                            l = s ? -16 : 16,
                            d = n.top - (e.top + e.h1);
                        r = (d - l - o) / d, c && (r = h ? 0 : 1); const u = { parentId: e.id, childId: t.id, partialLine: !0, percent: r, source: { x: e.left + e.w1 / 2, y: e.top + e.h1 }, target: { x: n.left - i, y: n.top + (s ? n.h1 : 0) } };
                        0 === a && this.push(u), this.push({ parentId: e.id, childId: t.id, percent: 1, markerPoint: { x: t.left + 0 - i, y: t.top + t.h1 / 2 }, source: { x: u.target.x, y: u.target.y }, target: { x: t.left + 0, y: t.top + t.h1 / 2 } }) } }

                function f(e) { let { startId: t, endPoint: n, dims: r, tension: a, radius: o, n: l } = e; const s = "top",
                        c = "left",
                        d = "w1",
                        u = "h1"; let { color: h, roleId: m } = n, p = m; var f = r[t],
                        v = f[c],
                        g = f[s],
                        y = r[p],
                        b = y[c],
                        w = y[s]; return f[s] === y[s] ? (f[c] > y[c] ? (v = f[c], b = y[c] + y[d], g = f[s] + f[u], w = y[s] + y[u]) : (v = y[c], b = f[c] + f[d], w = f[s] + f[u], g = y[s] + y[u]), a = .2) : f[s] <= y[s] ? f[c] > y[c] ? f[s] <= y[s] + y[u] ? (v = f[c], g = f[s] + 3 * f[u] / 4 - 5 * l, b = y[c] + y[d], w = y[s] + y[u] / 4) : (v = f[c] + f[d] / 4 + 5 * l, g = f[s] + f[u], b = y[c] + 3 * y[d] / 4, w = y[s]) : f[c] < y[c] ? f[s] <= y[s] + y[u] ? (v = f[c] + f[d], g = f[s] + 3 * f[u] / 4 - 5 * l, b = y[c], w = y[s] + y[u] / 4) : (v = f[c] + 3 * f[d] / 4 - 5 * l, g = f[s] + f[u], b = y[c] + y[d] / 2 - y[d] / 4, w = y[s]) : (v = f[c] + f[d] / 4 + 5 * l, g = f[s] + f[u], b = y[c] + y[d] / 2, w = y[s]) : f[c] > y[c] ? (v = f[c] + f[d] / 4 + 5 * l, g = f[s], b = y[c] + .75 * y[d], w = y[s] + y[u]) : f[c] < y[c] ? (v = f[c] + 3 * f[d] / 4 - 5 * l, g = f[s], b = y[c] + y[d] / 4, w = y[s] + y[u]) : (v = f[c] + f[d] / 4 + 5 * l, g = f[s], b = y[c] + y[d] / 2, w = y[s] + y[u]),
                        function(e, t, n, r) { let a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 1,
                                o = arguments.length > 5 ? arguments[5] : void 0,
                                l = arguments.length > 6 ? arguments[6] : void 0; var s = (n - e) * a,
                                c = e + s,
                                d = t,
                                u = n - s,
                                h = r,
                                m = "M " + e + " " + t + " C " + c + " " + d + " " + u + " " + h + " " + n + " " + r; let p = i.A.fromCSS(l).darken(20); var f = { p1: { cx: e, cy: t, r: o, fill: p }, p2: { cx: n, cy: r, r: o, fill: p } }; return { path: m, points: f } }(v, g, b, w, a, o, h) } const v = { displayData: function(e) { let { tree: t, collapseMap: n, stackSettingsMap: r, filter: a, searchResults: o, compact: i = !1, roleKindMap: l, pageMap: c } = e; const d = t.children.map((e => e.id)),
                            u = {},
                            h = o.map((e => { var t; return null === e || void 0 === e || null === (t = e.role) || void 0 === t ? void 0 : t.id })); return function e(t) { let o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
                                m = { left: [], right: [] },
                                p = []; const { direct: [f, v, g] } = r[t.id] || { direct: [] };
                            u[t.id] = { type: t.type, clusters: [] }; let y = null === t || void 0 === t ? void 0 : t.children.find((e => { var t; return (null === e || void 0 === e || null === (t = e.children) || void 0 === t ? void 0 : t.length) > 0 })); for (let E = 0; E < (null === t || void 0 === t || null === (b = t.children) || void 0 === b ? void 0 : b.length); E++) { var b; let C = t.children[E]; if (!C) return;
                                u[C.id] = { type: C.type, children: [], clusters: [] }; let T = l[null === C || void 0 === C ? void 0 : C.id],
                                    H = p[p.length - 1]; if (a && !h.includes(C.id)) p.push({ kind: "branch", nodes: [C.id], filterOut: !0, type: C.type, memberIds: C.members || [] });
                                else { var w; let e = "leaf" === T || "branch" === T && !!(c || {})[null === C || void 0 === C ? void 0 : C.id] && "shared" !== (null === C || void 0 === C ? void 0 : C.type) && !d.includes(C.id); var z; if ("leaf" === T || e) !i && y || !p.length || "leaf" !== (null === (z = H) || void 0 === z ? void 0 : z.kind) ? p.push({ nodes: [C.id], kind: e ? "leaf" : T }) : H.nodes.push(C.id); if (H = p[p.length - 1], ("leaf" !== T && !e || E === t.children.length - 1) && "leaf" === (null === (w = H) || void 0 === w ? void 0 : w.kind)) { const [e, ...t] = s(f, v, g, [...H.nodes]);
                                        H.nodes = e, p = [...p, ...t.map((e => ({ collapsed: !1, nodes: e, kind: "leaf" })))] } if ("assistant" === T) { var x;
                                        u[t.id].hasAssistant = !0; let [e] = p; var A; if ("assistant" !== (null === (x = e) || void 0 === x ? void 0 : x.kind)) p.unshift(e = { nodes: [], memberCount: (null === (A = C.members) || void 0 === A ? void 0 : A.length) || 0, kind: T });
                                        C.orientation && "left" !== C.orientation ? e.nodes.splice(m.left.length + m.right.length, 0, C.id) : e.nodes.splice(m.left.length, 0, C.id), m[C.orientation || "left"].push(C.id), e.leftNodeCount = m.left.length, e.rightNodeCount = m.right.length } else if ("leaf" !== T && !e) { var k; let e = (null === (k = C.members) || void 0 === k ? void 0 : k.length) || 0; const t = { collapsed: n[C.id], nodes: [C.id], memberCount: e, kind: T, type: C.type }; if ("shared" === C.type) { var S, M; let n = 0,
                                                a = 0;
                                            e = Math.max((null === C || void 0 === C ? void 0 : C.expectedMemberCount) || 0, null === C || void 0 === C || null === (S = C.members) || void 0 === S ? void 0 : S.length); let o = Math.max(((null === C || void 0 === C ? void 0 : C.expectedMemberCount) || 0) - (null === C || void 0 === C || null === (M = C.members) || void 0 === M ? void 0 : M.length), 0); const { shared: [i, l, s] } = r[C.id] || { shared: [] }; "vertical" === i || "condensed" === i ? (a = e, n = 1) : "horizontal" === i || e <= l ? (a = 1, n = e) : e <= l * s ? (a = Math.ceil(e / l), n = l) : (a = s, n = Math.ceil(e / s)), t.visibleSharedRows = a, t.visibleSharedCols = n, t.memberIds = C.members || []; for (let e = 0; e < o; e++) t.memberIds.push("expectedMember_".concat(e)) } p.push(t) } } n[null === C || void 0 === C ? void 0 : C.id] || e(C, o + 1) } u[t.id].id = t.id, u[t.id].type = t.type, u[t.id].clusters = p, u[t.id].children = t.children.map((e => e.id)) }(t), a && function e(t) { let n = 0,
                                r = !0; if (!t) return 0; const a = u[t].clusters; for (let o = 0; o < a.length; o++) { const t = a[o]; if (t.recursiveFilterCount = 0, t.filterOut) { if ("branch" === t.kind) { const a = e(t.nodes[0]);
                                        t.recursiveFilterCount += a.filterChained ? a.filteredCount : 0, n = a.filterChained ? n + (t.recursiveFilterCount + t.nodes.length) : 0, r = r && a.filterChained, n > 1 && u[t.nodes[0]] && (u[t.nodes[0]].clusters = []) } } else { for (let n = 0; n < t.nodes.length; n++) e(t.nodes[n]);
                                    t.recursiveFilterCount = 0, r = !1 } } return { filteredCount: n, filterChained: r } }(t.id), { roleData: u } }, calculateTree: function(e) { let { data: t, topRoleId: n, roleSizes: r, dropLevelMap: a, clustersSpacingMap: o, layout: i = "topdown", pageMap: s } = e; const c = "bottomup" === i,
                            d = "leftright" === i,
                            u = {};

                        function h(e) { return r[e].height || 0 }

                        function m(e) { return r[e].width || 0 }

                        function p(e, t, n) { if ("hidden" === e.type) return 0; if ("shared" === e.type) { var o, i, l; const { nodes: [t] } = e, n = e.memberIds.reduce((e => { var n; return Math.max(e, (null === (n = r[t]) || void 0 === n ? void 0 : n.height) || 0) }), 0) || (null === (o = r[t]) || void 0 === o ? void 0 : o.height) || (null === (i = r.shared) || void 0 === i ? void 0 : i.height) || 0; return (e.visibleSharedRows || 1) * n + ((null === (l = r[t]) || void 0 === l ? void 0 : l.topHeight) || 0) } return (e.nodes || []).reduce(((e, r) => h(r) + (a[r].height || 0) + e + 2 * t.clusterVers + (n[r] || 0)), 0) }

                        function f(e) { return "hidden" === e.type ? 0 : "shared" === e.type ? (e.visibleSharedCols || 1) * m(e.nodes[0]) : (e.nodes || []).reduce(((e, t) => Math.max(m(t), e)), 0) }

                        function v(e) { const t = [],
                                [n] = (null === e || void 0 === e ? void 0 : e.nodes) || [],
                                { visibleSharedCols: r, memberIds: a } = e,
                                o = m(n),
                                i = h(n); for (let l = 0, s = 0, c = 0; c < (null === a || void 0 === a ? void 0 : a.length); c++) l = c % r * o, s = Math.floor(c / (r || 1)) * i, t.push([l, s]); return t }! function e(n, r, i) { let c, { id: d, x: g, y: y, cluster: b, pid: w } = n,
                                z = 0,
                                x = 0,
                                A = 0,
                                k = 0,
                                S = 0,
                                M = 0,
                                E = 0,
                                C = 0; const T = t[d],
                                H = o[d]; let L = d === l.Uz ? 0 : h(d),
                                I = d === l.Uz ? 0 : m(d),
                                j = null; "shared" === (null === b || void 0 === b ? void 0 : b.type) && (L = p(b, H), I = f(b), j = v(b)), u[d] = { top: y + (w === l.Uz ? 0 : a[d].height), h1: L, w1: I, innerCoords: j }, C = y + u[d].h1 + (w === l.Uz ? 0 : a[d].height); const V = g,
                                O = d !== l.Uz || (null === T || void 0 === T || null === (r = T.clusters) || void 0 === r ? void 0 : r.length) > 0 ? H.vers + H.branch : 0; for (let t = 0; t < (null === T || void 0 === T || null === (R = T.clusters) || void 0 === R ? void 0 : R.length); t++) { var R, P, D;
                                c = t > 0 ? T.clusters[t - 1].kind : null; const n = T.clusters[t],
                                    r = (null === (P = n.nodes) || void 0 === P ? void 0 : P.length) || 0,
                                    o = c && "leaf" === c ? H.clusterHors : "leaf" === (null === n || void 0 === n ? void 0 : n.kind) && (null === n || void 0 === n || null === (D = n.nodes) || void 0 === D ? void 0 : D.length) > 1 ? 2 * H.hors : H.hors; if ("leaf" === n.kind) { var F; let e, t = C + O,
                                        i = 0,
                                        l = (a[n.nodes[0]] || {}).height || 0;
                                    1 === (null === (F = n.nodes) || void 0 === F ? void 0 : F.length) && l && (i = l); let c = {}; for (let a = 0; a < r; a++) { const r = n.nodes[a];
                                        a > 0 && (t += H.clusterVers, (s[r] || null !== s && void 0 !== s && s[e]) && (t += 20, c[r] = 20)), e = n.nodes[a]; const l = h(r),
                                            d = m(r);
                                        u[r] = { top: t + i, left: V + o + z, w1: d, h1: l }, t += l + H.clusterVers } const d = p(n, H, c);
                                    z += f(n) + o, x = Math.max(x, d + O + i) } else if ("branch" === n.kind) { let t; for (let r = 0; r < n.nodes.length; r++) { t = e({ id: n.nodes[r], x: V + z, y: C + O, pid: d, cluster: n }), z += Math.max(t.w1 + H.hors, t.w0), x = Math.max(x, t.h0 + O) } } else if ("shared" === n.kind) { const e = n.nodes[0],
                                        t = f(n),
                                        r = p(n, H),
                                        a = v(n);
                                    u[e] = { top: C + O, left: V + z + o, h1: r, w1: t, innerCoords: a }, z += t + o, x = Math.max(x, r + O) } else if ("assistant" === n.kind) { const { leftNodeCount: e, rightNodeCount: t } = n, a = n.nodes.length, o = Math.max(e, t), i = m("assistant"); let l = 0; for (let s = 0; s < r; s++) { const e = h(n.nodes[s]);
                                        l = Math.max(e, l) } C += l + H.vers, A += 2 * (i + 2 * H.assistant.hors) * o, k = 2 * (i + 2 * H.assistant.hors) * e, S += 2 * (i + 2 * H.assistant.hors) * t, M = (i + 2 * H.assistant.hors) * a, t || (M = 0), k < S && (M = A), T.has_assistant = !0 } } if (u[d].h0 = C - y + (u[d].h2 = x), u[d].w0 = Math.max(u[d].w1, u[d].w2 = z, A), u[d].left = g + H.hors / 2 + (null !== T && void 0 !== T && null !== (i = T.clusters) && void 0 !== i && i.length ? 0 : H.hors / 2) + Math.max((Math.max(u[d].w2, A) - u[d].w1) / 2, 0), A) { let e = T.clusters[0],
                                    { leftNodeCount: t, rightNodeCount: n } = e,
                                    r = e.nodes.length,
                                    o = (M - u[d].w2) / 2,
                                    i = 0;
                                E = y + u[d].h1 + H.assistant.vers, u[d].w1 > A && (u[d].w2 < Math.max(u[d].w1, A) && u[d].w2 > A ? i = (Math.max(u[d].w1, A) - u[d].w2) / 2 : u[d].w2 < Math.max(u[d].w1, A) && (i = (Math.max(u[d].w1, u[d].w2) - A) / 2)), o > 0 && k > S && (u[d].w0 = A - o), e.nodes.forEach((function(o, s) { let c = h(o),
                                        p = m(o),
                                        f = V + s * (p + 2 * H.assistant.hors) + H.assistant.hors + Math.max((u[d].w2 - A) / 2, 0);
                                    t < r && s >= t && (f += 2 * H.assistant.hors), n && "assistant" === e.kind && (f += Math.max(n - t, 0) * (p + 2 * H.assistant.hors)), u[o] = { top: E + (w === l.Uz ? 0 : a[d].height), left: f + i, w1: p, h1: c } })) } if (u[d].w2 < Math.max(u[d].w1, A)) { let e = (Math.max(u[d].w1, A) - u[d].w2) / 2;! function n(r, a) { a && u[r] && (u[r].left += e); for (let e of (null === (o = t[r]) || void 0 === o ? void 0 : o.children) || []) { var o;
                                        t[e] && (a || "assistant" !== t[e].type) && n(e, r) } }(T.id) } return u[d] }({ id: n, x: 0, y: 0 }); let g = u[n]; if (c) { let e = g.h0,
                                t = 0; for (let n in u) u[n].top = -(u[n].top - t) + e - u[n].h1 } if (d)
                            for (let l in u) { let e = u[l].top,
                                    t = u[l].w0;
                                u[l].top = u[l].left, u[l].left = e, u[l].w1 || (u[l].w1 = t), u[l].w0 = u[l].h0, u[l].w0 = u[l].h0, u[l].h0 = t }
                        return { dims: u } }, calculateHorizontalTree: function(e) { let { data: t, topRoleId: n, roleSizes: r, dropLevelMap: a, clustersSpacingMap: o } = e; const i = {};

                        function s(e) { return r[e].height || 0 }

                        function c(e) { return r[e].width || 0 }

                        function d(e, t) { return "hidden" === e.type ? 0 : "shared" === e.type ? (e.visibleSharedCols || 1) * c(e.nodes[0]) : (e.nodes || []).reduce(((e, n) => c(n) + (a[n].height || 0) + e + 2 * t.clusterHors), 0) }

                        function u(e) { if ("hidden" === e.type) return 0; if ("shared" === e.type) { var t, n, a; const { nodes: [o] } = e, i = e.memberIds.reduce((e => { var t; return Math.max(e, (null === (t = r[o]) || void 0 === t ? void 0 : t.height) || 0) }), 0) || (null === (t = r[o]) || void 0 === t ? void 0 : t.height) || (null === (n = r.shared) || void 0 === n ? void 0 : n.height) || 0; return (e.visibleSharedRows || 1) * i + ((null === (a = r[o]) || void 0 === a ? void 0 : a.topHeight) || 0) } return (e.nodes || []).reduce(((e, t) => Math.max(s(t), e)), 0) }

                        function h(e) { const t = [],
                                [n] = (null === e || void 0 === e ? void 0 : e.nodes) || [],
                                { visibleSharedCols: r, memberIds: a } = e,
                                o = c(n),
                                i = s(n); for (let l = 0, s = 0, c = 0; c < (null === a || void 0 === a ? void 0 : a.length); c++) l = c % r * o, s = Math.floor(c / (r || 1)) * i, t.push([l, s]); return t } return function e(n, r) { let { id: m, x: p, y: f = 0, cluster: v, pid: g } = n, y = 0, b = 0, w = 0, z = 0, x = 0, A = 0, k = 0, S = 0; const M = f,
                                E = t[m],
                                C = o[m]; let T = m === l.Uz ? 0 : s(m),
                                H = m === l.Uz ? 0 : c(m),
                                L = null; "shared" === (null === v || void 0 === v ? void 0 : v.type) && (T = u(v), H = d(v, C), L = h(v)), i[m] = { left: p + (g === l.Uz ? 0 : a[m].height), h1: T, w1: H, innerCoords: L }, S = p + i[m].w1 + (g === l.Uz ? 0 : a[m].height); const I = m !== l.Uz || (null === E || void 0 === E || null === (r = E.clusters) || void 0 === r ? void 0 : r.length) > 0 ? 2 * C.hors : 0; for (let t = 0; t < (null === E || void 0 === E || null === (j = E.clusters) || void 0 === j ? void 0 : j.length); t++) { var j, V, O; const n = E.clusters[t],
                                    r = (null === (V = n.nodes) || void 0 === V ? void 0 : V.length) || 0; if ("leaf" === n.kind && (null === E || void 0 === E || null === (O = E.clusters) || void 0 === O ? void 0 : O.length) > 1) { var R; const e = u(n),
                                        t = d(n, C); let o = 0,
                                        l = (a[n.nodes[0]] || {}).height || 0;
                                    1 === (null === (R = n.nodes) || void 0 === R ? void 0 : R.length) && l && (o = l); let h = S + I; for (let a = 0; a < r; a++) { a > 0 && (h += C.clusterHors / 2); const e = n.nodes[a],
                                            t = s(e),
                                            r = c(e);
                                        i[e] = { left: h, top: M + b + C.clusterVers / 2, w1: r, h1: t }, h += r + C.clusterHors } b += e + C.clusterVers, y = Math.max(y, t + I + o) } else if ("branch" === n.kind || "leaf" === n.kind) { let t; for (let r = 0; r < n.nodes.length; r++) { t = e({ id: n.nodes[r], y: M + b, x: S + I, pid: m, cluster: n }), b += Math.max(t.h0, t.h1 + ("shared" === n.type && t.h2 > 0 ? 0 : 2 * C.vers)), y = Math.max(y, t.w0 + I) } } else if ("shared" === n.kind) { const e = n.nodes[0],
                                        t = d(n),
                                        r = u(n),
                                        a = h(n);
                                    i[e] = { top: M + b + C.vers / 2, left: S + I, h1: r, w1: t, innerCoords: a }, b += r + C.vers / 2, y = Math.max(y, t + I) } else if ("assistant" === n.kind) { const { leftNodeCount: e, rightNodeCount: t } = n, a = n.nodes.length, o = Math.max(e, t), i = c("assistant"); let l = 0; for (let c = 0; c < r; c++) { const e = s(n.nodes[c]);
                                        l = Math.max(e, l) } S += i + 2 * C.hors, w += 2 * (l + 2 * C.assistant.vers) * o, z = 2 * (l + 2 * C.assistant.vers) * e, x += 2 * (l + 2 * C.assistant.vers) * t, A = (l + 2 * C.assistant.vers) * a, t || (A = 0), z < x && (A = w), E.has_assistant = !0 } } if (i[m].h0 = Math.max(i[m].h1, i[m].h2 = b, w), i[m].w0 = S - p + (i[m].w2 = y), i[m].top = f + (E.clusters.length ? 0 : C.vers) + Math.max((Math.max(i[m].h2, w) - i[m].h1) / 2, 0), w) { let e = E.clusters[0],
                                    { leftNodeCount: t, rightNodeCount: n } = e,
                                    r = t + n,
                                    o = (A - i[m].h2) / 2,
                                    d = 0;
                                k = p + i[m].w1 + C.assistant.hors / 2, i[m].h1 > w && (i[m].h2 < Math.max(i[m].h1, w) && i[m].h2 > w ? d = (Math.max(i[m].h1, w) - i[m].h2) / 2 : i[m].h2 < Math.max(i[m].h1, w) && (d = (Math.max(i[m].h1, i[m].h2) - w) / 2)), o > 0 && x > z && (i[m].h0 = w - o), e.nodes.forEach((function(o, u) { let h = s(o),
                                        p = c(o),
                                        f = M + u * (h + 2 * C.assistant.vers) + Math.max((i[m].h2 - w) / 2, 0);
                                    t < r && u >= t && (f += 2 * C.assistant.vers), n && "assistant" === e.kind && (f += Math.max(n - t, 0) * (h + 2 * C.assistant.vers)), i[o] = { left: k + (g === l.Uz ? 0 : a[m].height), top: f + d, w1: p, h1: h } })) } if (i[m].h2 < Math.max(i[m].h1, w)) { let e = (Math.max(i[m].h1, w) - i[m].h2) / 2;! function n(r, a) { a && i[r] && (i[r].top += e); for (let e of (null === (o = t[r]) || void 0 === o ? void 0 : o.children) || []) { var o;
                                        t[e] && (a || "assistant" !== t[e].type) && n(e, r) } }(E.id) } return i[m] }({ id: n, x: 0, y: 0 }), { dims: i } }, calculateLines: e => { let { visibleNodeIds: t, dims: n, data: r = {}, dropLevelMap: a, stackSettingsMap: o, layout: i = "topdown" } = e; const l = Object.keys(n),
                            s = [],
                            d = t.reduce(((e, t) => (e[t] = n[t], e)), {}); for (let p of l) { var u; let e = !!d[p]; const { clusters: t } = r[p]; let l = n[p]; if (!l) continue;
                            l.id = p; const f = (null === (u = o[p] || {}) || void 0 === u ? void 0 : u.direct) || ["horizontal", 3, 3],
                                [v] = f; if (t) { const r = !t.filter((e => "branch" === (null === e || void 0 === e ? void 0 : e.kind))).length; if ("cluster" === v && r) c.call(s, t, l, e, d, n, p, a, i);
                                else
                                    for (let o = 0; o < t.length; o++) { const r = t[o]; for (const [o, c] of (r.nodes || []).entries()) { const u = e ? n[c] : d[c]; if (!u) continue;
                                            u.id = c; const f = n[r.nodes[0]]; var m; if (u)
                                                if ("root" !== p || t.length > 1) h.call(s, l, u, f, r, o, (null === (m = a[c]) || void 0 === m ? void 0 : m.height) || 0, i, "root" === p) } } } } return { lines: s } }, calculateQuadrants: e => { let { dims: t, chartWidth: n = 0 } = e; const r = n / 2,
                            a = [],
                            o = Object.keys(t).sort(((e, n) => { const r = t[e],
                                    a = t[n]; return r.left < a.left ? -1 : r.left > a.left ? 1 : 0 })); for (let i = 0, l = 0; i < o.length; i++) { const e = o[i];
                            t[e].left > (l + 1) * r && l++, a[l] || (a[l] = []), a[l].push(e) } return a }, calculateDotted: (e, t, n, r) => { let a = []; if (e)
                            for (let o of Object.keys(e)) { if (!t[o]) continue; let i = 0; for (let l of e[o].slice().sort((function(e, n) { return t[e] && t[n] ? t[e].left < t[n].left ? 1 : t[e].left > t[n].left ? -1 : 0 : 1 }))) { if (!t[l.roleId] || !t[o]) continue;
                                    i++; let e = f({ startId: o, endPoint: l, dims: t, tension: .6, radius: 3, n: i - 1, layout: r }),
                                        s = { startId: o, endId: l.roleId, d: e.path, p1: e.points.p1, p2: e.points.p2, color: l.color, style: (null === l || void 0 === l ? void 0 : l.style) || (null === n || void 0 === n ? void 0 : n.style) || "dashed", thickness: (null === n || void 0 === n ? void 0 : n.thickness) || 2 };
                                    a.push(s) } }
                        return a }, getBoxSizing: function(e) { let { roleTypeMap: t, roleTypeHeights: n } = e; return Object.keys(t).reduce(((e, t) => (e[t] = n[t], e)), {}) } }; var g = n(45418),
                    y = n(19367),
                    b = n(279),
                    w = n(1619),
                    z = n(13888),
                    x = n(7743),
                    A = n(69219),
                    k = n(81635),
                    S = n(79091),
                    M = n(70669),
                    E = n(14241),
                    C = n(85510),
                    T = n(97067),
                    H = n(24657),
                    L = n(39659),
                    I = n(93949),
                    j = n(42006),
                    V = n(10621),
                    O = n(47088),
                    R = n(98433),
                    P = n(15813),
                    D = n(30752),
                    F = n(74079),
                    N = n(34944),
                    _ = n(31041),
                    B = n(65491),
                    W = n(95695),
                    U = n(37091),
                    q = n(59445),
                    G = n(55005),
                    K = n(59177),
                    Z = n(36735); const Y = { id: l.Uz, name: "No department" },
                    { selectIds: X, selectAll: $, selectEntities: Q, selectById: J } = a.k9.getSelectors(),
                    ee = e => e.roles.couldTransition,
                    te = e => e.roles.lastExpanded,
                    ne = e => e.roles.reFocusId,
                    re = (0, r.Mz)(D.A, (e => e.base)),
                    ae = (0, r.Mz)(re, (e => { var t, n, r; return (null === e || void 0 === e || null === (t = e.cards) || void 0 === t || null === (n = t.cardFrame) || void 0 === n || null === (r = n.size) || void 0 === r ? void 0 : r.width) || 200 })),
                    oe = (0, r.Mz)(D.A, (e => null === e || void 0 === e ? void 0 : e.chartOptions)),
                    ie = (0, r.Mz)(oe, (e => (null === e || void 0 === e ? void 0 : e.compactMode) || !1)),
                    le = (0, r.Mz)(oe, (e => null === e || void 0 === e ? void 0 : e.orderAutomatically)),
                    se = (0, r.Mz)(oe, (e => null === e || void 0 === e ? void 0 : e.orderBy)),
                    ce = (0, r.Mz)(re, (e => { var t; return null === e || void 0 === e || null === (t = e.lines) || void 0 === t ? void 0 : t.primary })),
                    de = (0, r.Mz)(re, (e => { var t; return null === e || void 0 === e || null === (t = e.lines) || void 0 === t ? void 0 : t.secondary })),
                    ue = (0, r.Mz)(F.A, (e => Object.keys(e).reduce(((t, n) => { var r, a; let o = { ...e[n] }; return o.parent = e[o.parent], o.children = null === (r = e[n]) || void 0 === r || null === (a = r.children) || void 0 === a ? void 0 : a.map((t => e[t])), t[n] = o, t }), {}))),
                    he = ((0, r.Mz)(F.A, (e => t => { for (let n of Object.keys(e)) { if (e[n].type === t) return !0 } })), (0, r.Mz)((e => e.roles.collapseMap), _.A, A.uo, E.s1, ((e, t, n, r) => "print" === n || "printPreview" === n || r ? t.map((e => ({
                        [e]: !1 }))) : e))),
                    me = (0, r.Mz)(he, (e => t => e[t] || !1)),
                    pe = (e, t) => e.roles.entities[t],
                    fe = (e, t) => { let { roleIds: n } = t; return n.map((t => e.roles.entities[t])).filter((e => e)) },
                    ve = (0, r.Mz)(N.A, (e => e.reduce(((e, t) => { let { members: n } = t; return [...new Set([...e, ...n || []])] }), []))),
                    ge = ((0, r.Mz)(N.A, g.xt, ((e, t) => e.reduce(((e, n) => { let { members: r } = n; return [...new Set([...e, ...r.map((e => t[e]))])] }), []))), (0, r.Mz)(N.A, (e => (e || []).reduce(((e, t) => { const { members: n = [] } = t || {}, r = (0, V.mA)(t); for (let a of n || []) e[a] ? e[a].push(r) : e[a] = [r]; return e }), {})))),
                    ye = (0, r.Mz)(N.A, (e => e.reduce((function(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                            { members: n } = t; for (let r of n || []) e[r] ? e[r].push(t) : e[r] = [t]; return e }), {}))),
                    be = (0, r.Mz)(((e, t) => { let { roleId: n } = t; return n }), B.A, ((e, t) => { let n = !1,
                            r = t[e]; for (let a of r || []) { let e = t[a]; if (!e || e && 0 === e.length) { n = !0; break } } return n })),
                    we = (0, r.Mz)(B.A, (e => function t(n, r) { const a = this,
                            o = e[n] || []; if (null !== o && void 0 !== o && o.length) { a[r] = a[r] || [], a[r].push(...o); for (let e of o) e && t.apply(a, [e, r + 1]) } return a }.apply([], [l.Uz, 1]))),
                    ze = (0, r.Mz)(we, (function() { return (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []).reduce(((e, t, n) => (t.forEach((t => { e[t] = n })), e)), {}) })),
                    xe = (0, r.Mz)(ze, F.A, (function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
                            t = arguments.length > 1 ? arguments[1] : void 0; return Object.keys(e).reduce(((n, r) => { const a = t[r],
                                o = ((null === a || void 0 === a ? void 0 : a.fields) || []).find((e => "name" === (null === e || void 0 === e ? void 0 : e.name))),
                                i = ((null === o || void 0 === o ? void 0 : o.value) || "").toLowerCase(); var l;
                            i && (Object.keys(n).includes(i) && null !== (l = n[i]) && void 0 !== l && l.levels ? (n[i].levels.push(e[r]), n[i].roleIds.push(r)) : n[i] = { levels: [e[r]], roleIds: [r] }); return n }), {}) })),
                    Ae = ((0, r.Mz)(we, (e => function t(n) { let r = this; return r += e[n].length, r < 20 ? t.call(r, n + 1) : r - 4 < 20 ? n : Math.max(n - 1, 1) }.call(0, 1))), (0, r.Mz)(N.A, B.A, ((e, t) => { const n = {}; for (let a = 0; a < e.length; a++) { const { type: t, id: r } = e[a]; "department" === t && (n[r] = []) } const r = [...Object.keys(n)]; for (let a = 0; a < r.length; a++) { const e = r[a];! function r() { let a = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; for (let o = 0; o < a.length; o++) { const i = a[o];
                                    n[i] || (n[e].push(i), r(t[i])) } }(t[e]) } return n }))),
                    ke = (0, r.Mz)(N.A, B.A, ((e, t) => { const n = {}; for (let a = 0; a < e.length; a++) { const { type: t, id: r } = e[a]; "location" === t && (n[r] = []) } const r = [...Object.keys(n)]; for (let a = 0; a < r.length; a++) { const e = r[a];! function r() { let a = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; for (let o = 0; o < a.length; o++) { const i = a[o];
                                    n[i] || (n[e].push(i), r(t[i])) } }(t[e]) } return n })),
                    Se = ((0, r.Mz)(Ae, F.A, ((e, t) => t), ((e, t, n) => Object.keys(e).map((r => { const a = e[r].reduce(((e, n) => { const r = t[n]; return r ? e.concat(r.members) : e }), []).filter((e => e)); return r !== l.Uz ? { ...t[r], inDepartment: a.includes(n) } : { ...Y, inDepartment: a.includes(n) } })).filter((e => e.inDepartment)))), (0, r.Mz)(F.A, Ae, ((e, t) => Object.keys(t).map((n => { const r = t[n].reduce(((t, n) => { const r = e[n]; return r ? t.concat(r.members) : t }), []).filter((e => e)); return n !== l.Uz ? { ...e[n], peopleCount: r.length } : { ...Y, peopleCount: r.length } }))))),
                    Me = ((0, r.Mz)(F.A, ke, ((e, t) => Object.keys(t).map((n => { const r = t[n].reduce(((t, n) => { const r = e[n]; return r ? t.concat(r.members) : t }), []).filter((e => e)); return n !== l.Uz ? { ...e[n], peopleCount: r.length } : { ...Y, peopleCount: r.length } })))), (0, r.Mz)(N.A, (e => { const t = { root: ["horizontal", 1, 1] }; for (let n = 0; n < e.length; n++) { const r = e[n],
                                { stackOverrides: a, sharedStackOverrides: o } = r; let { direction: i, minColumns: l = 1, maxRows: s = 3 } = a || { direction: "inherit", minColumns: 1, maxRows: 3 }; const { direction: c, minColumns: d, maxRows: u } = o || { direction: "inherit", minColumns: 1, maxRows: 3 };
                            t[r.id] = { direct: [i, l, s], shared: [c, d, u] } } return t }))),
                    Ee = (0, r.Mz)((e => X(e.roles)), Me, D.A, A.uo, z.A4, ((e, t, n, r, a) => { if (!n) return {}; const { chartOptions: { stackDirection: o, stackColumns: i = 3, stackRows: s = 3, stackDirectionShared: c, stackColumnsShared: d, stackRowsShared: u } } = n; return e.reduce(((e, n) => { const [h, m, p] = t[n].direct, [f, v, g] = t[n].shared; if ([l.uI.PRINT, l.uI.PRINT_PREVIEW].includes(r) && a[n]) { const { directReportDirection: t, directReportColumns: r = 2, directReportRows: o = 2 } = a[n];
                                e[n] = { direct: [t, r, o], shared: "inherit" === f ? [c, d, u] : [f, v, g] } } else e[n] = { direct: "inherit" === h ? [o, i, s] : [h, m, p], shared: "inherit" === f ? [c, d, u] : [f, v, g] }; return e }), {}) })),
                    Ce = (0, r.Mz)(N.A, (e => e.reduce(((e, t) => { let { id: n, type: r } = t; return e[r] || (e[r] = []), e[r].push(n), e }), { single: [], assistant: [], shared: [], department: [], embedded: [], location: [] }))),
                    Te = (0, r.Mz)(E.mE, _.A, E.s1, ((e, t, n) => { if (!n) return []; const r = e.map((e => { var t; return null === e || void 0 === e || null === (t = e.role) || void 0 === t ? void 0 : t.id })).filter((e => e)); return t.filter((e => !r.includes(e))) })),
                    He = (0, r.Mz)((e => e.roles.autoSizes), Te, ((e, t) => ({ ...e, ...t.reduce(((e, t) => (e[t] = l.Ay.cards.sizes.filteredRole, e)), {}) }))),
                    Le = (0, r.Mz)(D.A, Te, ((e, t) => ({ ...["single", "assistant", "shared", "department", "location", "embedded", "hidden", "team", "function"].reduce(((t, n) => { const r = e.base,
                                { cards: { cardFrame: { size: a } } } = r; return ["function", "team"].includes(n) ? t[n] = { ...a, height: 60, width: 240 } : t[n] = "hidden" === n ? { ...a, height: 0, width: 0 } : a, t }), {}), ...t.reduce(((e, t) => (e[t] = { height: l.Ay.cards.sizes.filteredRole, width: l.Ay.cards.sizes.filteredRole }, e)), {}) }))); const Ie = (0, r.Mz)(_.A, F.A, He, Le, B.A, Ce, D.A, E.s1, W.yP, G.A, (function(e, t, n, r, a, o) { let i = arguments.length > 6 && void 0 !== arguments[6] ? arguments[6] : {},
                            l = arguments.length > 7 ? arguments[7] : void 0,
                            s = arguments.length > 8 ? arguments[8] : void 0,
                            c = arguments.length > 9 ? arguments[9] : void 0; const d = ["single", "assistant", "shared", "department", "location", "embedded"]; let u = ["single", "department", "location", "assistant", "embedded"]; const { cards: { cardFrame: { size: { autoStrategy: h, auto: m } } } } = i.base; if (m) { let i = { all: 0, single: 0, department: 0, location: 0, assistant: 0, embedded: 0, hidden: 0 }; if ("all" === h) { const e = Object.values(n).filter((e => e));
                                i.all = Math.max.apply(null, e) } else if ("type" === h)
                                for (let e of u) { const t = o[e].map((e => n[e] || 0));
                                    t.length ? i[e] = Math.max.apply(null, t) || 0 : i[e] = 0 }
                            const p = e.reduce(((e, o) => { let d = t[o] || {}; if (!d) return e; const { id: u, parent: p, type: f } = d, v = function(e, t, n, r) { return Array.isArray(e) ? e.filter((e => { var a; if (e.type !== U.hC.ROLE) return !1; const o = (0, q.cf)(e, t),
                                            i = n[o.id]; return null === i || void 0 === i || null === (a = i.children) || void 0 === a ? void 0 : a.includes(r) })).map((e => (0, q.cf)(e, t))) : [] }(s, c, t, u), g = null !== v && void 0 !== v && v.length ? [u] : a[p] || [u]; let y; if (l) y = n[o];
                                else if (m)
                                    if ("compact" === h || "shared" === f) { let e = 0; "assistant" !== f && "shared" !== f ? e = g.filter((e => { var n, r; return e === u || "shared" !== (null === (n = t[e]) || void 0 === n ? void 0 : n.type) && "assistant" !== (null === (r = t[e]) || void 0 === r ? void 0 : r.type) })).map((e => n[e] || 0)) : "assistant" === f ? e = g.filter((e => { var n; return e === u || "assistant" === (null === (n = t[e]) || void 0 === n ? void 0 : n.type) })).map((e => n[e] || 0)) : "shared" === f && (e = [n[o]]), y = Math.max.apply(null, e) } else "type" === h ? y = i[f] || n[o] : "all" === h && (y = i.all);
                                else y = n[o]; var b, w;
                                y ? e[o] = { ...r[o] || r[null === (b = t[o]) || void 0 === b ? void 0 : b.type], height: y, ownHeight: n[o], topHeight: n["".concat(o, "_top")] || 0 } : e[o] = { ...r[o] || r[null === (w = t[o]) || void 0 === w ? void 0 : w.type], topHeight: n["".concat(o, "_top")] || 0 }; return e }), {}); for (let e of d) p[e] = { ...r[e] }; return p } const p = e.reduce(((e, a) => { var o; return e[a] = r[a] || r[null === (o = t[a]) || void 0 === o ? void 0 : o.type] || r.single, { ...e, ownHeight: e[a].height, topHeight: n["".concat(a, "_top")] || 0 } }), {}); for (let f of d) p[f] = { ...r[f] }; return p })),
                    je = (e, t, n) => { if (null !== t && void 0 !== t && t.includes(e)) return t.indexOf(e) + 2; { const r = n(e).map((e => null === e || void 0 === e ? void 0 : e.id)); let a = r[0]; for (let e of r) t.indexOf(e) >= 0 && t.indexOf(e) > t.indexOf(a) && (a = e); return t.indexOf(a) + 2 } },
                    Ve = (0, r.Mz)(F.A, (e => t => { if (!t) return []; const n = [t],
                            r = [],
                            a = e[t]; return a ? function t(a) { const o = e[a]; return o && (r.push(o), n.push(a)), null !== o && void 0 !== o && o.parent && !n.includes(null === o || void 0 === o ? void 0 : o.parent) ? t(o.parent) : r }(a.parent) : [] })),
                    Oe = (0, r.Mz)(F.A, (e => e ? t => { let { parentId: n, descendentId: r } = t; return function t(r) { return !!r && ((null === r || void 0 === r ? void 0 : r.id) === n || t(e[null === r || void 0 === r ? void 0 : r.parent])) }(e[r]) } : () => !1)),
                    Re = ((0, r.Mz)(y.Mk, (e => { var t, n, r; return (null === e || void 0 === e || null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.pageSetup) || void 0 === r ? void 0 : r.pageBreaks) || {} }), ((e, t) => t[null === e || void 0 === e ? void 0 : e.id] || [])), (0, r.Mz)(xe, ue, ((e, t) => function(n) { let { roleName: r, firstRoleMember: a, firstParentRoleName: o = "", excludedRoleIds: i = [] } = n; const l = e[r.toLowerCase()]; if ((null === l || void 0 === l ? void 0 : l.roleIds.length) > 1) { const e = ((null === l || void 0 === l ? void 0 : l.roleIds) || []).map((e => t[e])),
                                n = e.filter((e => !(i || []).includes(null === e || void 0 === e ? void 0 : e.id))); if (1 === n.length) return n[0]; if (n.length > 1) { let e = n.find((e => (null === e || void 0 === e ? void 0 : e.members[0]) === a)); if (e) return e; let t = n.find((e => { var t; return (null === e || void 0 === e || null === (t = e.parent) || void 0 === t ? void 0 : t.name) === o })); return null !== t && void 0 !== t && t.length ? t : n[0] } return e[0] } return t[null === l || void 0 === l ? void 0 : l.roleIds[0]] }))),
                    Pe = (0, r.Mz)(y.Mk, z.zk, Oe, (e => { var t, n, r; return (null === e || void 0 === e || null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.pageSetup) || void 0 === r ? void 0 : r.pageBreaks) || {} }), F.A, Ve, Re, ((e, t, n, r, a, o, i) => { var l; const s = (null === (l = r[null === e || void 0 === e ? void 0 : e.id]) || void 0 === l ? void 0 : l.map((e => ({ ...e })))) || [],
                            c = e => { const t = e.map((e => null === e || void 0 === e ? void 0 : e.id)); return e.map((e => { var n; const r = o(null === e || void 0 === e ? void 0 : e.id),
                                        a = r.find((e => t.includes(null === e || void 0 === e ? void 0 : e.id))); return { ...e, prevId: (null === a || void 0 === a ? void 0 : a.id) || (null === (n = r[(null === r || void 0 === r ? void 0 : r.length) - 1]) || void 0 === n ? void 0 : n.id) || "root" } })) };

                        function d(e) { const t = Object.keys(e); return a[null === e || void 0 === e ? void 0 : e.id] || t.includes("name") || "root" === (null === e || void 0 === e ? void 0 : e.id) } const u = e => { const t = e.map((e => null === e || void 0 === e ? void 0 : e.id)).filter((e => e)); return e.map((e => { var n, r, o, l; let s = a[null === e || void 0 === e ? void 0 : e.id]; if (!s) { let n = i({ roleName: e.name, firstRoleMember: e.firstRoleMember, firstParentRoleName: e.firstParentRoleName, excludedRoleIds: t }); if (!n) return null;
                                    s = { ...n, storeId: null === e || void 0 === e ? void 0 : e.id }, t.push(null === n || void 0 === n ? void 0 : n.id), e.id = null === n || void 0 === n ? void 0 : n.id } const c = (0, V.mA)(s),
                                    d = null !== (n = s) && void 0 !== n && n.members && null !== (r = s) && void 0 !== r && r.members[0] && (null === (o = s) || void 0 === o ? void 0 : o.members[0]) || ""; return { ...e, name: c, firstRoleMember: d, storeId: null === (l = s) || void 0 === l ? void 0 : l.storeId } })).filter((e => e)) }; return null !== t && void 0 !== t && t.length ? c(u(s.filter((e => { var r; return (null === e || void 0 === e ? void 0 : e.id) && d(e) && n({ parentId: null === (r = t[0]) || void 0 === r ? void 0 : r.id, descendentId: null === e || void 0 === e ? void 0 : e.id }) })) || [])) : c(u(s.filter((e => (null === e || void 0 === e ? void 0 : e.id) && d(e))))) })),
                    De = (0, r.Mz)(B.A, g.Kj, F.A, N.A, z.m7, z.Ic, Pe, Ve, A.uo, R.db, R.DD, R.mU, ((e, t, n, r, a, i, s, c, d, u, h, m) => { if (!a) return null; const { topRoles: p, nLevels: f, nTopLevels: v, breakBy: g, pagination: y, pageFormat: { size: b, orientation: w }, range: z } = a, x = null === i || void 0 === i ? void 0 : i.tableOfContents.visible, { ownPage: A, visible: k } = h, S = "multi" === y; if (d === l.uI.DEFAULT) return {}; let M; if (S && g === P.N0.BESTFIT) { if (!m) return { pageCount: 1 };
                            M = JSON.parse(JSON.stringify(m)); const e = Object.values(m).flatMap((e => null === e || void 0 === e ? void 0 : e.partitions)).reduce(((e, t) => (t.pageNumber > e && (e = t.pageNumber), e)), 0); if (M.pageCount = e, x) { const t = P.ek[b] || 96,
                                    n = P.q2[b] || P.q2.letter,
                                    r = "landscape" === w ? null === n || void 0 === n ? void 0 : n.width : null === n || void 0 === n ? void 0 : n.height,
                                    a = r * t - 75 - 40 - ("landscape" === w ? null === n || void 0 === n ? void 0 : n.height : null === n || void 0 === n ? void 0 : n.width) * t * .2 - 56; if (0 !== r) { let t = 50; const n = Math.round(a / t),
                                        r = Object.keys(M).length - 1,
                                        o = Math.ceil(r / n);
                                    Object.keys(M).forEach((e => { "object" === typeof M[e] && (M[e].pageNumber += o, M[e].partitions.forEach((e => { e.pageNumber += o })), M[e].prevPageNumber += o) })), M.tableOfContents = { visible: x, totalToCPages: o, maxRowsPerPage: n }, M.pageCount = e + o } } } else M = (0, o.aH)({ parentChildMap: e, roleMap: n, roles: r, personMap: t }, { nLevels: f, nTopLevels: v || f, breakBy: g, topRoles: z === P.J_.ROLES ? p : [], multi: S, showToC: x, pageSize: b, orientation: w, pageMapTopIds: s, getPageForRoleFn: je, getRootPathSelector: c }); let E = 0; if (u.visible && E++, A && k && E++, E) { const e = ["tableOfContents", "pageCount"];
                            Object.keys(M).filter((t => !e.includes(t))).forEach((e => { M[e].pageNumber += E, M[e].prevPageNumber += E, M[e].partitions && M[e].partitions.forEach((e => { e.pageNumber += E })) })), M.pageCount += E } return M })),
                    Fe = (0, r.Mz)(De, z.lM, ((e, t) => (0, o.eO)(e, t))),
                    Ne = ((0, r.Mz)(De, (e => (null === e || void 0 === e ? void 0 : e.pageCount) || 1)), (0, r.Mz)((e => e.search.selectedResult), g.xt, ((e, t) => { var n; if (null === e || void 0 === e || !e.role) return null; const { role: r } = e; let a = ""; return (null === r || void 0 === r || null === (n = r.members) || void 0 === n ? void 0 : n.length) > 0 && (a = r.members.map((e => { let n = t[e] || {}; return null === n || void 0 === n ? void 0 : n.name })).filter((e => e)).join(", ")), { roleId: r.id, roleName: (0, V.mA)(r), personName: a } }))),
                    _e = (0, r.Mz)(y.Mk, z.zk, Fe, (e => e.search.selectedResult), A.uo, y.Jf, y.G0, ((e, t, n, r, a, o, i) => { var s, c; const d = null === e || void 0 === e || null === (s = e.alias) || void 0 === s ? void 0 : s.roleId; if (i === l.XD.MATRIX) { var u; return (null === r || void 0 === r || null === (u = r.role) || void 0 === u ? void 0 : u.id) || d || l.Uz } var h, m; return "print" === a || "printPreview" === a ? n || (null === (h = t[0]) || void 0 === h ? void 0 : h.id) || d || l.Uz : null !== r && void 0 !== r && null !== (c = r.role) && void 0 !== c && c.id ? (null === r || void 0 === r || null === (m = r.role) || void 0 === m ? void 0 : m.id) || d || l.Uz : null !== o && void 0 !== o && o.topRole ? (null === o || void 0 === o ? void 0 : o.topRole) || d || l.Uz : d || l.Uz })),
                    Be = (0, r.Mz)(_e, F.A, B.A, ((e, t, n) => { if (e && e !== l.Uz) return [t[e] || t[l.Uz]]; return (n[l.Uz] || []).map((e => t[e])).filter((e => e)).sort(((e, t) => (e.position || 0) <= (t.position || 0) ? -1 : 1)) })),
                    We = (0, r.Mz)(Be, (e => e.filter((function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return !(null !== e && void 0 !== e && e.parent && e.parent !== l.Uz || (null === e || void 0 === e ? void 0 : e.type) === l.mv.FUNCTION || (null === e || void 0 === e ? void 0 : e.type) === l.mv.TEAM || (null === e || void 0 === e ? void 0 : e.type) === l.mv.HIDDEN || null !== e && void 0 !== e && e.functionId || null !== e && void 0 !== e && e.teamId) }))[0])),
                    Ue = (0, r.Mz)(N.A, (e => e.filter((function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; return (null === e || void 0 === e ? void 0 : e.type) === l.mv.HIDDEN })))),
                    qe = (0, r.Mz)(F.A, N.A, We, B.A, De, z.lM, ((e, t, n, r, a, o) => { if (n) { let t = r[null === n || void 0 === n ? void 0 : n.id].map((t => e[t])).filter((e => (null === e || void 0 === e ? void 0 : e.type) === l.mv.FUNCTION)); if (o && a) { var i, s; const e = (null === a || void 0 === a || null === (i = a.root) || void 0 === i ? void 0 : i.partitions) && (null === a || void 0 === a || null === (s = a.root) || void 0 === s ? void 0 : s.partitions[o]); if (null !== e && void 0 !== e && e.length) return t.filter((t => null === e || void 0 === e ? void 0 : e.includes(null === t || void 0 === t ? void 0 : t.id))) } return t } let c = t.filter((e => (null === e || void 0 === e ? void 0 : e.type) === l.mv.FUNCTION)).sort(((e, t) => (null === e || void 0 === e ? void 0 : e.position) || 0 - (null === t || void 0 === t ? void 0 : t.position) || 0)); if (o && a) { var d, u; const e = (null === a || void 0 === a || null === (d = a.root) || void 0 === d ? void 0 : d.partitions) && (null === a || void 0 === a || null === (u = a.root) || void 0 === u ? void 0 : u.partitions[o]); if (null !== e && void 0 !== e && e.length) return c.filter((t => null === e || void 0 === e ? void 0 : e.includes(null === t || void 0 === t ? void 0 : t.id))) } return c })),
                    Ge = (0, r.Mz)(F.A, N.A, We, B.A, De, z.lM, ((e, t, n, r, a, o) => { if (n) { let t = r[null === n || void 0 === n ? void 0 : n.id].map((t => e[t])).filter((e => (null === e || void 0 === e ? void 0 : e.type) === l.mv.TEAM)); if (o && a) { var i, s; const e = (null === a || void 0 === a || null === (i = a.root) || void 0 === i ? void 0 : i.partitions) && (null === a || void 0 === a || null === (s = a.root) || void 0 === s ? void 0 : s.partitions[o]); if (null !== e && void 0 !== e && e.length) return t.filter((t => null === e || void 0 === e ? void 0 : e.includes(null === t || void 0 === t ? void 0 : t.id))) } return t } let c = t.filter((e => (null === e || void 0 === e ? void 0 : e.type) === l.mv.TEAM)).sort(((e, t) => (null === e || void 0 === e ? void 0 : e.position) || 0 - (null === t || void 0 === t ? void 0 : t.position) || 0)); if (o && a) { var d, u; const e = (null === a || void 0 === a || null === (d = a.root) || void 0 === d ? void 0 : d.partitions) && (null === a || void 0 === a || null === (u = a.root) || void 0 === u ? void 0 : u.partitions[o]); if (null !== e && void 0 !== e && e.length) return c.filter((t => null === e || void 0 === e ? void 0 : e.includes(null === t || void 0 === t ? void 0 : t.id))) } return c })),
                    Ke = (0, r.Mz)(Ge, (e => e.reduce(((e, t) => (e[null === t || void 0 === t ? void 0 : t.id] = t, e)), {}))),
                    Ze = (0, r.Mz)(qe, (e => e.reduce(((e, t) => (e[null === t || void 0 === t ? void 0 : t.id] = t, e)), {}))),
                    Ye = (0, r.Mz)(Ue, (e => e.reduce(((e, t) => { let n = t.teamId,
                            r = t.functionId; return e[r] || (e[r] = {}), e[r][n] = t, e }), {}))),
                    Xe = ((0, r.Mz)(Be, (e => e.filter((e => e.type === l.mv.HIDDEN && (e.children || []).length)).length)), (0, r.Mz)(Ge, he, ((e, t) => (e || []).reduce(((e, n) => (e[n.id] = !t[n.id], e)), {}) || {})), (0, r.Mz)(F.A, _.A, z.zk, y.Mk, ((e, t, n, r) => { var a; const o = null === r || void 0 === r || null === (a = r.alias) || void 0 === a ? void 0 : a.roleId; var i; if (null !== n && void 0 !== n && n.length) return e[null === (i = n[0]) || void 0 === i ? void 0 : i.id]; if (o) return e[o]; { let n = null; for (let r of t) { const t = e[r],
                                    a = null === t || void 0 === t ? void 0 : t.parent; if (!a || ["undefined", "null", void 0, null, ""].includes(a)) { n = t; break } } return n || {} } }))),
                    $e = (0, r.Mz)(B.A, (e => { const t = {}; return function n(r) { const a = e[r]; if (!a || !Array.isArray(a)) return t[r];
                            t[r] = (null === a || void 0 === a ? void 0 : a.length) || 0; for (let e of a || []) t[r] += n(e); return t[r] }(l.Uz), t })),
                    Qe = (0, r.Mz)(N.A, g.xt, $e, De, _e, se, le, z.lM, (function(e, t, n) { let r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {},
                            a = arguments.length > 4 ? arguments[4] : void 0,
                            i = arguments.length > 5 ? arguments[5] : void 0,
                            l = arguments.length > 6 ? arguments[6] : void 0,
                            s = arguments.length > 7 ? arguments[7] : void 0; return (0, o.Mv)(e, t, { pageMap: r, topRoleId: a, orderBy: i, orderAutomatically: l, numRolesInSubtreeMap: n, curPage: s }) })),
                    Je = (0, r.Mz)((e => e.search.selectedResult), (e => { var t; return null === e || void 0 === e || null === (t = e.role) || void 0 === t ? void 0 : t.id })),
                    et = (0, r.Mz)(Qe, F.A, Ie, y.Uq, ((e, t, n, r) => { const a = {}; return function e(i) { let l = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0; const s = (i.dropLevel || 0) * (function(e) { const a = "leftright" === r ? "width" : "height"; if ("shared" !== e.type) return n[e.id] && n[e.id][a] || 0; let o = t[e.parent]; return o && "shared" !== o.type ? n[e.parent] && n[e.parent][a] || 0 : n.single && n.single[a] || 0 }(i) + o.YK.vers + o.YK.branch);
                            a[i.id] = { level: i.dropLevel, height: s, recursiveDrop: l + s }; for (let t of i.children || []) e(t, l + s) }(e), a })),
                    tt = (0, r.Mz)((e => { var t, n; return null === (t = e.roles) || void 0 === t || null === (n = t.ids) || void 0 === n ? void 0 : n.length }), (e => e)),
                    nt = (0, r.Mz)(F.A, et, ((e, t) => { const n = Object.keys(e),
                            r = {}; for (let i of n) { var a, o; let n = ((null === (a = t[i]) || void 0 === a ? void 0 : a.level) || 0) > 0,
                                l = e[i],
                                s = null !== l && void 0 !== l && null !== (o = l.children) && void 0 !== o && o.length || n && "assistant" !== l.type ? "branch" : ("shared" === l.type ? "shared" : "assistant" === l.type && "assistant") || "leaf";
                            r[i] = s } return r })),
                    rt = (0, r.Mz)(Ee, Qe, he, et, E.s1, E.mE, ie, nt, y.Uq, De, ((e, t, n, r, a, o, i, l, s, c) => { const { roleData: d } = v.displayData({ tree: t, collapseMap: n, stackSettingsMap: e, dropLevelMap: r, filter: a, searchResults: o, compact: i, roleKindMap: l, layout: s, pageMap: c }); return d })),
                    at = (0, r.Mz)(rt, Ee, y.Uq, ie, ((e, t, n, r) => { const a = (0, o.fn)(n),
                            i = "leftright" === n,
                            l = i ? "hors" : "vers",
                            s = i ? "vers" : "hors",
                            c = i ? "clusterHors" : "clusterVers",
                            d = i ? "clusterVers" : "clusterHors"; return Object.keys(e).reduce(((n, o) => { const { clusters: i } = e[o]; if (!i.filter((e => "branch" === (null === e || void 0 === e ? void 0 : e.kind))).length || r) { var u; const [e = "horizontal"] = (null === (u = t[o]) || void 0 === u ? void 0 : u.direct) || [], r = "cluster" === e; return n[o] = { ...a, [l]: a.vers, [s]: a.hors, [d]: r ? 16 : 32, [c]: r ? 6 : 12, assistant: { ...a.assistant, [l]: a.assistant.vers, [s]: a.assistant.hors } }, n } return n[o] = { ...a, [l]: a.vers, [s]: a.hors, [d]: a.clusterHors, [c]: a.clusterVers, assistant: { ...a.assistant, [l]: a.assistant.vers, [s]: a.assistant.hors } }, n }), {}) })),
                    ot = (0, r.Mz)(rt, he, Ie, B.A, et, at, y.Uq, De, ((e, t, n, r, a, o, i, s) => { if ("leftright" === i) { const { dims: s } = v.calculateHorizontalTree({ data: e, topRoleId: l.Uz, leafOffset: 0, roleSizes: n, collapseMap: t, parentChildMap: r, dropLevelMap: a, clustersSpacingMap: o, layout: i }); return s } const { dims: c } = v.calculateTree({ data: e, topRoleId: l.Uz, leafOffset: 0, roleSizes: n, collapseMap: t, parentChildMap: r, dropLevelMap: a, clustersSpacingMap: o, layout: i, pageMap: s }); return c })),
                    it = (0, r.Mz)(ot, B.A, ((e, t) => n => { const r = e[l.Uz] || { w0: 0, h0: 0, left: 0, top: 0 }; if (n === l.Uz) return e; const a = e[n || l.Uz] || { w0: 0, h0: 0, left: 0, top: 0 },
                            o = a.top - r.top,
                            i = Math.max(a.left - a.w0 / 2, 0),
                            s = { ...a, top: a.top - o, left: a.left - i }; return function n(r) { let a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; if (r) return t[r].filter((t => e[t])).reduce(((t, r) => { const a = e[r] || { left: 0, top: 0 }; return t[r] = { ...a, top: a.top - o, left: a.left - i }, n(r, t) }), a) }(n, {
                            [n]: s }) })),
                    lt = (0, r.Mz)(it, We, ((e, t) => { if (t) { return e("root")[null === t || void 0 === t ? void 0 : t.id] } return { w0: 200, h0: 60 + l.Ay.chart.padding, w1: 200, h1: 60 + l.Ay.chart.padding, left: 0, top: 0 } })),
                    st = (0, r.Mz)(Ue, (e => e.reduce(((e, t) => (e[null === t || void 0 === t ? void 0 : t.id] = null === t || void 0 === t ? void 0 : t.teamId, e)), {}))),
                    ct = (0, r.Mz)(Ge, he, st, ((e, t, n) => { const r = Object.keys(t); let a = Object.keys(n || {}).reduce(((e, r) => (e[n[r]] = e[n[r]] || t[r], e)), {}); const o = Object.keys(a); return (e.filter((e => !o.includes(null === e || void 0 === e ? void 0 : e.id))).map((e => null === e || void 0 === e ? void 0 : e.id)) || []).forEach((e => { r.includes(e) ? a[e] = t[e] : a[e] = !1 })), a })),
                    dt = 140,
                    ut = (0, r.Mz)(Ge, qe, it, Ue, ae, lt, ct, ((e, t, n, r, a, o, i) => { const s = a,
                            c = t.map((e => null === e || void 0 === e ? void 0 : e.id)); return e.reduce(((t, a, d) => { var u, h, m, p; const f = null === a || void 0 === a ? void 0 : a.id,
                                v = r.filter((e => (null === e || void 0 === e ? void 0 : e.teamId) === a.id)).filter((e => c.includes(null === e || void 0 === e ? void 0 : e.functionId))).map((e => null === e || void 0 === e ? void 0 : e.id)).reduce(((e, t) => { const r = n(t)[t],
                                        a = null === r || void 0 === r ? void 0 : r.h0; return a > e ? a : e }), dt) + l.Ay.chart.padding; let g = d > 0 ? null === (u = t[null === (h = e[d - 1]) || void 0 === h ? void 0 : h.id]) || void 0 === u ? void 0 : u.top : dt,
                                y = d > 0 ? (null === (m = t[null === (p = e[d - 1]) || void 0 === p ? void 0 : p.id]) || void 0 === m ? void 0 : m.h0) + 40 : o.top + o.h1 + 2 * T.lw; return t[null === a || void 0 === a ? void 0 : a.id] = { left: 20 + L.Z, top: C.r + g + y, w0: s, h0: i[f] ? dt : (v || dt) + T.lw, w1: 240, h1: 140 }, t }), {}) })),
                    ht = (0, r.Mz)(ut, (e => { const t = Object.keys(e || {}) || []; let n = {}; for (let o of t) { var r, a;
                            n[o] = { ...e[o] }, n[o].top = n[o].top + C.r / 2 + ((null === (r = e[o]) || void 0 === r ? void 0 : r.h0) - (null === (a = e[o]) || void 0 === a ? void 0 : a.h1)) / 2 } return n })),
                    mt = (0, r.Mz)(ae, (e => e <= 200 ? 200 - e + 40 : 0)),
                    pt = (0, r.Mz)(qe, Ge, it, Ue, ae, lt, mt, ((e, t, n, r, a, o, i) => { const l = a,
                            s = t.map((e => null === e || void 0 === e ? void 0 : e.id)); return e.reduce(((t, a, c) => { var d, u, h, m; const p = r.filter((e => (null === e || void 0 === e ? void 0 : e.functionId) === a.id)).filter((e => s.includes(null === e || void 0 === e ? void 0 : e.teamId))).map((e => null === e || void 0 === e ? void 0 : e.id)).reduce(((e, t) => { const r = n(t)[t],
                                    a = null === r || void 0 === r ? void 0 : r.w0; return a > e ? a : e }), l); let f = c > 0 ? null === (d = t[null === (u = e[c - 1]) || void 0 === u ? void 0 : u.id]) || void 0 === d ? void 0 : d.left : l + i,
                                v = c > 0 ? null === (h = t[null === (m = e[c - 1]) || void 0 === m ? void 0 : m.id]) || void 0 === h ? void 0 : h.w0 : L.Z; return t[a.id] = { left: 20 + f + v, top: o.top + o.h1 + 2 * T.lw, w0: (p || l) + 2 * T.lw, h0: l + 2 * T.lw, w1: 240, h1: 140 }, t }), {}) })),
                    ft = (0, r.Mz)(pt, (e => { const t = Object.keys(e || {}) || []; let n = {}; for (let o of t) { var r, a;
                            n[o] = { ...e[o] }, n[o].left = n[o].left + T.lw + ((null === (r = e[o]) || void 0 === r ? void 0 : r.w0) - (null === (a = e[o]) || void 0 === a ? void 0 : a.w1)) / 2 } return n })),
                    vt = (0, r.Mz)(pt, ae, mt, ((e, t, n) => { const r = Object.keys(e); return t + n + T.lw + 20 + r.reduce(((t, n) => { var r; return t + (null === (r = e[n]) || void 0 === r ? void 0 : r.w0) + 20 }), 0) })),
                    gt = (0, r.Mz)(ut, Ge, lt, ae, ((e, t, n, r) => ({ top: n.h1 + n.top + 2 * T.lw + C.r * ((null === t || void 0 === t ? void 0 : t.length) + 1) + Object.keys(e).reduce(((t, n) => t + e[n].h0 + 40), dt), left: 0, width: r }))),
                    yt = (0, r.Mz)(pt, lt, ae, mt, ((e, t, n, r) => ({ left: 20 + L.Z + Object.keys(e).reduce(((t, n) => t + e[n].w0 + 20), n + T.lw + r), top: t.h1 + t.top + (2 * T.lw - H.uF) / 2, width: n }))),
                    bt = (0, r.Mz)(A.uo, j.eW, ((e, t) => { const n = (l.ZE[t] || 0) >= l.ZE[l.td.EDITOR]; return !(l.uI.DEFAULT !== e || !n) })),
                    wt = (0, r.Mz)(A.uo, j.eW, We, ((e, t, n) => { const r = (l.ZE[t] || 0) >= l.ZE[l.td.EDITOR]; return !(!(l.uI.DEFAULT === e && r || null === n || void 0 === n) && n.id) })),
                    zt = (0, r.Mz)(ut, lt, vt, wt, ((e, t, n, r) => { let a = { h0: (null === t || void 0 === t ? void 0 : t.h1) + l.Ay.chart.padding + 264 + t.top, w0: n + H.rU + L.Z }; return Object.keys(e).forEach((t => { var n;
                            a.h0 += (null === (n = e[t]) || void 0 === n ? void 0 : n.h0) + C.r + 40 })), a = { ...t, ...a, left: L.Z + (n - t.w1) / 2 }, a })),
                    xt = (0, r.Mz)(Ue, (e => e.reduce(((e, t) => { const n = null === t || void 0 === t ? void 0 : t.functionId; return Object.keys(e).includes(n) ? e[n].push(null === t || void 0 === t ? void 0 : t.id) : e[n] = [null === t || void 0 === t ? void 0 : t.id], e }), {}))),
                    At = (0, r.Mz)(xt, it, ((e, t) => { let n = Object.keys(e),
                            r = n.reduce(((e, t) => (e[t] = 0, e)), {}); for (let a of n) { let n = e[a]; for (let e of n) { let n = t(e)[e];
                                (null === n || void 0 === n ? void 0 : n.w0) > r[a] && (r[a] = null === n || void 0 === n ? void 0 : n.w0) } } return r })),
                    kt = (0, r.Mz)(Ue, it, y.G0, pt, ft, ut, ht, At, ((e, t, n, r, a, o, i, s) => { if (n === l.XD.MATRIX) { const n = Object.keys(r || {}),
                                l = Object.keys(o || {}); return e.filter((e => n.includes(null === e || void 0 === e ? void 0 : e.functionId))).filter((e => l.includes(null === e || void 0 === e ? void 0 : e.teamId))).reduce(((e, n) => { let a = t(null === n || void 0 === n ? void 0 : n.id); const i = a[null === n || void 0 === n ? void 0 : n.id]; return Object.keys(a || {}).forEach((e => { e = a[e]; const t = r[null === n || void 0 === n ? void 0 : n.functionId].w0,
                                        l = (null === i || void 0 === i ? void 0 : i.w0) < s[null === n || void 0 === n ? void 0 : n.functionId] ? (t - (null === i || void 0 === i ? void 0 : i.w0)) / 2 - T.lw : 0;
                                    e.left += 2 * T.lw + r[null === n || void 0 === n ? void 0 : n.functionId].left + l, e.top += o[null === n || void 0 === n ? void 0 : n.teamId].top + C.r })), { ...e, ...a } }), { ...i, ...a }) } return t(l.Uz) })),
                    St = (0, r.Mz)(ht, ft, gt, yt, zt, We, wt, ((e, t, n, r, a, o, i) => { const l = { x: H.rU / 2, y: r.top + H.uF / 2 },
                            s = []; for (let c of Object.keys(t)) { const e = t[c]; let n = { x: e.left + e.w1 / 2, y: e.top };
                            s.push({ source: l, target: n, percent: 0 }) } for (let c of Object.keys(e)) { const t = e[c]; let n = { x: t.left - T.lw / 2 - I.J / 2, y: t.top + t.h1 / 2 };
                            s.push({ source: l, target: n, percent: 1 }) } return s.push({ source: l, target: { x: n.left + H.rU / 2, y: n.top }, percent: 1 }), s.push({ source: l, target: { x: r.left, y: r.top + H.uF / 2 }, percent: 1 }), (i || null !== o && void 0 !== o && o.id) && s.push({ source: l, target: { x: a.left + a.w1 / 2, y: a.top + a.h1 }, percent: 0 }), s })),
                    Mt = e => (0, r.Mz)(ot, (t => t[e] || { w0: 0, h0: 0, left: 0, top: 0, w1: 0, h1: 0 })),
                    Et = (0, r.Mz)(ot, (e => e[l.Uz] || { w0: 0, h0: 0 })),
                    Ct = (0, r.Mz)((e => Q(e.roles)), rt, ((e, t) => n => { const { parent: r } = e[n] || {}, a = { roleClusterGroup: t[n] }; return r && t[r] && (a.roleCluster = (() => { const { clusters: e = [] } = t[r]; for (let t = 0; t < e.length; t++) { let r = e[t]; if (-1 !== r.nodes.indexOf(n)) return r } })()), a })),
                    Tt = (0, r.Mz)(B.A, he, ((e, t) => { const n = {}; return function r(a) { const o = e[a] || []; return (!o.length || !t[a]) && (null !== o && void 0 !== o && o.length ? n[a] = o.reduce(((e, t) => r(t) && e), !0) : !t[a]) }(l.Uz), n })),
                    Ht = (0, r.Mz)(Tt, (e => t => e[t] || !1)),
                    Lt = ((0, r.Mz)(((e, t) => t), A.uo, A.WJ, k.t0, S.tO, M.Q$, ((e, t, n, r, a, o) => { const [i, s] = e; let c = i,
                            d = s; return "dashboard" === t && (d -= l.Ay.toolbars.headerHeight, c -= l.Ay.chart.padding, c -= l.Ay.dashboard.navWidth, "themes" === n && (c -= l.Ay.dialogs.themeSettingsWidth), r && (c -= l.Ay.dialogs.profileCardWidth)), "view" === t && (d -= l.Ay.toolbars.headerHeight, d -= l.Ay.chart.topSectionHeight, c -= l.Ay.chart.navWidth, r ? c -= l.Ay.dialogs.profileCardWidth : a ? c -= l.Ay.dialogs.legendCardWidth : o && (c -= l.Ay.dialogs.talentPoolCardWidth), "theme" === n && (c -= l.Ay.dialogs.themeSettingsWidth), "settings" === n && (c -= l.Ay.dialogs.chartSettingsWidth)), "printPreview" === t && (d -= l.Ay.toolbars.headerHeight, d -= l.Ay.print.previewTopActionHeight, d -= 2 * l.Ay.print.previewPadding, d -= l.Ay.print.headerHeight, d -= l.Ay.print.footerHeight, c -= l.Ay.dialogs.printSettingsWidth, c -= 2 * l.Ay.print.previewPadding), [c, d] })), (0, r.Mz)(ot, y.jb, y.LL, (e => e.print.zoom), (e => { var t, n, r, a; return "auto" === (null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.pageSetup) || void 0 === r || null === (a = r.pageFormat) || void 0 === a ? void 0 : a.size) }), (e => { var t; return null === (t = e.print) || void 0 === t ? void 0 : t.fitScale }), Z.A, ((e, t, n, r, a, o, i) => e ? v.calculateQuadrants({ dims: e, chartWidth: t / Math.max(i === l.uI.PRINT_PREVIEW ? r * (a ? 1 : o) : n, .002) }) : []))),
                    It = (0, r.Mz)(Lt, y.iM, A.uo, ((e, t, n) => { if ("print" === n) return e; const r = Math.max(t - 1 - (n === l.uI.PRINT_PREVIEW ? 2 : 0), 0),
                            a = Math.min(t + 3 + (n === l.uI.PRINT_PREVIEW ? 3 : 0), e.length); return e.slice(r, a) })),
                    jt = (0, r.Mz)(It, ot, ne, A.uo, ((e, t, n, r) => {
                        function a(e) { return t[e] } if (r === l.uI.PRINT) return Object.keys(t); const o = e.reduce(((e, t) => e.concat(t || [])), [l.Uz]); return n ? -1 === o.indexOf(n) ? null !== o && [...o, n].filter(a) : o.filter(a) : null !== o && o.filter(a) })),
                    Vt = (0, r.Mz)(it, jt, Ge, qe, ((e, t, n, r) => a => { if (a) { const t = e(a); let o = Object.keys(t); for (let e of n) o.includes(null === e || void 0 === e ? void 0 : e.id) || o.push(null === e || void 0 === e ? void 0 : e.id); for (let e of r) o.includes(null === e || void 0 === e ? void 0 : e.id) || o.push(null === e || void 0 === e ? void 0 : e.id); return o } return t })),
                    Ot = (0, r.Mz)(y.iM, Lt, ot, A.uo, y.G0, ((e, t, n, r, a) => { if (a === l.XD.MATRIX) return []; if (r === l.uI.PRINT || r === l.uI.PRINT_PREVIEW) return []; const o = e - 2,
                            i = e + 3,
                            s = []; return o >= 0 && s.push.apply(s, t[o]), i < t.length && s.push.apply(s, t[i]), s.filter((function(e) { return e !== l.Uz && n[e] })) })),
                    Rt = (0, r.Mz)(Se, B.A, me, it, ((e, t) => t), y.Uq, ((e, t, n, r, a, o) => { const i = r(a || "root"),
                            l = "leftright" === o; return e.map((e => { const r = i[null === e || void 0 === e ? void 0 : e.id]; if (!r) return null; const a = t[null === e || void 0 === e ? void 0 : e.id],
                                o = n(null === e || void 0 === e ? void 0 : e.id),
                                s = r.h1 / 2; if (r && null !== a && void 0 !== a && a.length && !o) { let t = l ? r.left + (r.w1 || 0) : r.left + (r.w1 || 0) / 2 - (r.w0 || 0) / 2,
                                    n = l ? r.top + (r.h1 || 0) / 2 - (r.h0 || 0) / 2 : r.top + s,
                                    a = l ? r.w0 - (r.w1 || 0) : r.w0,
                                    o = r.h0 - s + 32; return { id: null === e || void 0 === e ? void 0 : e.id, left: t, width: a, height: o, top: n } } })).filter((e => e)) })),
                    Pt = (0, r.Mz)(((e, t) => t), it, rt, et, Ee, ce, y.Uq, ((e, t, n, r, a, o, i) => { const s = t(e); if (null === o || void 0 === o || !o.visible) return []; const { lines: c } = v.calculateLines({ visibleNodeIds: Object.keys(s || {}), dims: s, data: n, topRoleId: l.Uz, stackSettingsMap: a, dropLevelMap: r, layout: i }); return c })),
                    Dt = (0, r.Mz)(N.A, (e => e.reduce(((e, t) => (e[t.id] = (t.dottedReports || []).filter((e => e)), e)), {}))),
                    Ft = (0, r.Mz)(Dt, ot, de, y.G0, y.Uq, ((e, t, n, r, a) => r !== l.XD.MATRIX && null !== n && void 0 !== n && n.visible ? v.calculateDotted(e, t, n, a) : [])),
                    Nt = (0, r.Mz)(g.xt, N.A, y.Zx, x.C1, ((e, t, n, r) => { const a = {},
                            o = (n || {}).rules || [],
                            i = !!(n && n.id && n.visible) && o.reduce(((e, t) => { let n = []; return t.comparators && t.comparators.length && (e || (e = {}), n = t.comparators.map((e => { var t, n; let a = "string"; if (!e.field) return null; if ("unfilled" === (null === e || void 0 === e || null === (t = e.field) || void 0 === t ? void 0 : t.name) || "position" === (null === e || void 0 === e || null === (n = e.field) || void 0 === n ? void 0 : n.name)) a = "chart";
                                    else { var o; let t = r[null === e || void 0 === e || null === (o = e.field) || void 0 === o ? void 0 : o.id];
                                        a = ((null === t || void 0 === t ? void 0 : t.type) || "string").toLowerCase() } return { ...e, type: a } })).filter((e => e)), e[t.id] = { comparators: n, join: t.join || "AND" }), e }), null) || {}; return t.reduce(((t, n) => { const { id: a } = n, o = (0, b.b)({ role: n, ruleTests: i, roster: e, fieldMap: r }); return t[a] = o, t }), a), a })),
                    _t = ((0, r.Mz)(Nt, ((e, t) => t), ((e, t) => e[t] || [])), (0, r.Mz)(y.Zx, B.A, Nt, F.A, ((e, t, n, r) => { var a; const o = null === e || void 0 === e || null === (a = e.rules) || void 0 === a ? void 0 : a.filter((e => e.displayType === O.tI.COLORBAR)),
                            i = {},
                            s = []; return function e(a) { s.push(a); const l = t[a] || [],
                                c = n[a] || [];
                            null === o || void 0 === o || o.forEach((e => { var t;!0 !== c[e.id] && !0 !== (null === (t = c[e.id]) || void 0 === t ? void 0 : t[a]) || (i[a] = e.color) })); for (let t of l) { r[t] && (s.includes(t) || e(t)) } }(l.Uz), i }))),
                    Bt = (0, r.Mz)(F.A, _t, B.A, (e => { var t, n, r; return null === (t = e.print) || void 0 === t || null === (n = t.settings) || void 0 === n || null === (r = n.chartElements) || void 0 === r ? void 0 : r.roleColors }), A.uo, _e, ((e, t, n, r, a, o) => { if ((a === l.uI.PRINT_PREVIEW || a === l.uI.PRINT) && 1 != r) return {}; const i = { root: { bgcolor: null, isSmartColor: !1, inherited: null } },
                            s = o !== l.Uz && n[l.Uz].includes(o); if (s) { const { bgcolor: n, propagateBg: r } = e[o], a = n && "#ffffff" !== n.toLowerCase() ? n : null, l = t[o];
                            i[o] = { bgcolor: l || a, propBg: "on" === r, isSmartColor: !!l, inherited: null, roleColorWithoutSmartColor: a } } const c = []; return function r(a) { c.push(a); const o = n[a] || []; let l = i[a]; for (let n of o) { let a = e[n]; if (!a) continue; const { bgcolor: o, propagateBg: s } = a, d = o && "#ffffff" !== o.toLowerCase() ? o : null, u = t[n], h = (null === l || void 0 === l ? void 0 : l.propBg) && (null === l || void 0 === l ? void 0 : l.bgcolor) && (null !== l && void 0 !== l && l.isSmartColor ? (null === l || void 0 === l ? void 0 : l.roleColorWithoutSmartColor) || null : null === l || void 0 === l ? void 0 : l.bgcolor);
                                i[n] = { bgcolor: u || d || h, propBg: "on" === s, inherited: !d && !!l, isSmartColor: !!u, roleColorWithoutSmartColor: d || h }, c.includes(n) || r(n) } }(s ? o : l.Uz), i })),
                    Wt = ((0, r.Mz)(((e, t) => { let { roleId: n } = t; return n }), Bt, ((e, t) => t[e])), (0, r.Mz)(_e, B.A, F.A, D.A, ((e, t, n, r) => { const { orgStructure: { roleCounts: { rules: a } } } = r, o = { root: { size: 0, departments: 0, assistants: 0, locations: 0 } }; return function(e, t, n, r, a) { const o = {};! function a(i, l, s, c) { let d = n[i]; if (o[i]) return { recursive: 0, direct: 0, memberIds: [], recursiveMembers: [] };
                                o[i] = !0, e[i] = { recursive: 0, direct: 0, directmemberIds: [], recursiveMembersIds: [] }; let u = r[i] || []; for (let n of u)
                                    if (t.countByPeople) { let { memberIds: t, recursiveMembers: r } = a(n), o = [...e[i].directmemberIds || [], ...t || []];
                                        o = [...new Set(o)]; let l = [...r, ...e[i].recursiveMembersIds];
                                        l = [...new Set(l)], e[i].recursive = l.length, e[i].direct = o.length, e[i].directmemberIds = o, e[i].recursiveMembersIds = l } else { let { direct: t, recursive: r } = a(n);
                                        e[i].recursive += r, e[i].direct += t } let h = [];
                                d && (h = Zt(d, t, n)); let m = []; return m = h && h.memberId ? [...h.memberId, ...e[i].recursiveMembersIds] : [...e[i].recursiveMembersIds], { recursive: e[i].recursive + ((null === (l = h) || void 0 === l ? void 0 : l.nodeSize) || 0), direct: (null === (s = h) || void 0 === s ? void 0 : s.direct) || 0, memberIds: null === (c = h) || void 0 === c ? void 0 : c.memberId, recursiveMembers: m } }(a) }(o, a, n, t, e || l.Uz), o }))),
                    Ut = ((0, r.Mz)(Wt, ((e, t) => t), ((e, t) => e[t] || { direct: 0, recursive: 0 })), (0, r.Mz)(Wt, (e => t => e[t] || { direct: 0, recursive: 0 }))),
                    qt = (0, r.Mz)(D.A, (e => { var t; return null === e || void 0 === e || null === (t = e.orgStructure) || void 0 === t ? void 0 : t.roleCounts })),
                    Gt = (0, r.Mz)(A.uo, D.A, z.$x, ((e, t, n) => { var r, a, o, i; return "print" === e || "printPreview" === e ? (null === n || void 0 === n || null === (o = n.layout) || void 0 === o || null === (i = o.chart) || void 0 === i ? void 0 : i.roleCounts) || !1 : (null === t || void 0 === t || null === (r = t.orgStructure) || void 0 === r || null === (a = r.roleCounts) || void 0 === a ? void 0 : a.visible) || !1 })),
                    Kt = ((0, r.Mz)(A.uo, z.$x, ((e, t) => { var n, r; return "print" !== e && "printPreview" !== e || ((null === t || void 0 === t || null === (n = t.layout) || void 0 === n || null === (r = n.chart) || void 0 === r ? void 0 : r.linkedIn) || !1) })), (0, r.Mz)(A.uo, z.$x, ((e, t) => { var n, r, a; return ("print" === e || "printPreview" === e) && (null === t || void 0 === t || null === (n = t.setup) || void 0 === n || null === (r = n.page) || void 0 === r || null === (a = r.multiOptions) || void 0 === a ? void 0 : a.showPageIndicators) || !1 })));

                function Zt(e, t, n) { let r = 1,
                        a = 1,
                        o = []; switch (e.type) {
                        case "department":
                            if (t.inclDept && !t.countByPeople || (r = 0, a = 0), !t.inclDept) { const r = e.children.map((e => n[e]));
                                a = r.reduce(((e, r) => { if (r) { const { direct: a } = Zt(r, t, n);
                                        e += a } return e }), 0), o = r.reduce(((e, r) => { if (r) { const { memberId: a } = Zt(r, t, n);
                                        e = [...e, ...a] } return e }), []) } break;
                        case "embedded":
                            r = 0, a = 0; break;
                        case "location":
                            if (t.inclLocation && !t.countByPeople || (r = 0, a = 0), !t.inclLocation) { const r = e.children.map((e => n[e]));
                                a = r.reduce(((e, r) => { if (r) { const { direct: a } = Zt(r, t, n);
                                        e += a } return e }), 0), o = r.reduce(((e, r) => { if (r) { const { memberId: a } = Zt(r, t, n);
                                        e = [...e, ...a] } return e }), []) } break;
                        case "assistant":
                        case "single":
                            t.inclUnassigned && !t.countByPeople || e.members.length || (r = 0, a = 0), o = [...e.members]; break;
                        case "shared":
                            var i; if (t.inclUnassigned && !t.countByPeople || e.members.length || (r = 0, a = 0), t.inclSharedMembers || t.countByPeople) r = Math.max((null === e || void 0 === e || null === (i = e.members) || void 0 === i ? void 0 : i.length) || 0, (null === e || void 0 === e ? void 0 : e.expectedMemberCount) || 0), a = r;
                            o = [...e.members]; break;
                        default:
                            r = 1, a = 1 } return { memberId: o, nodeSize: r, direct: a } } const Yt = (0, r.Mz)(g.xt, N.A, ((e, t) => { let { query: n } = t; return n }), x.ge, x.gJ, D.A, ((e, t, n, r, a, o) => { var i; if (!n) return []; return w.A.queryTree({ roles: t, roster: e, query: n, searchableFields: r, defaultFieldsMap: a, vacantText: null === o || void 0 === o || null === (i = o.chartOptions) || void 0 === i ? void 0 : i.vacantText }) })),
                    Xt = (0, r.Mz)(g.xt, N.A, ((e, t) => { let { query: n } = t; return n }), x.ge, x.gJ, D.A, ((e, t, n, r, a, o) => { var i; if (!n) return []; const s = w.A.queryTree({ roles: t, roster: e, query: n, searchableFields: r, defaultFieldsMap: a, vacantText: null === o || void 0 === o || null === (i = o.chartOptions) || void 0 === i ? void 0 : i.vacantText }); let c = s; const d = []; return s.forEach((e => { var n, r, a; return null !== e && void 0 !== e && null !== (n = e.role) && void 0 !== n && n.id && (null === e || void 0 === e || null === (r = e.role) || void 0 === r ? void 0 : r.id) !== l.Uz && null !== e && void 0 !== e && e.role ? void
                            function n(r) { const a = t.find((e => e.id === r)); if (a && !d.includes(r)) return "department" === (null === a || void 0 === a ? void 0 : a.type) && c.push({ role: a, member: null, match: e.match, fieldWeight: e.fieldWeight }), d.push(r), n(a.parent) }(null === e || void 0 === e || null === (a = e.role) || void 0 === a ? void 0 : a.parent): [] })), c })),
                    $t = (0, r.Mz)(g.xt, N.A, ((e, t) => { let { advancedQuery: n } = t; return n }), x.J0, ((e, t, n, r) => { if (!n) return []; return w.A.queryTreeAdvanced({ roles: t, roster: e, advancedQuery: n, fieldMap: r }) })),
                    Qt = (0, r.Mz)(g.xt, N.A, ((e, t) => { let { advancedQuery: n } = t; return n }), x.J0, ((e, t, n, r) => { if (!n) return []; const a = w.A.queryTreeAdvanced({ roles: t, roster: e, advancedQuery: n, fieldMap: r }); let o = a; const i = []; return a.forEach((e => { var n, r, a; return null !== e && void 0 !== e && null !== (n = e.role) && void 0 !== n && n.id && (null === e || void 0 === e || null === (r = e.role) || void 0 === r ? void 0 : r.id) !== l.Uz && null !== e && void 0 !== e && e.role ? void
                            function n(r) { const a = t.find((e => e.id === r)); if (a && !i.includes(r)) return "department" === (null === a || void 0 === a ? void 0 : a.type) && o.push({ role: a, member: null, match: e.match, fieldWeight: e.fieldWeight }), i.push(r), n(a.parent) }(null === e || void 0 === e || null === (a = e.role) || void 0 === a ? void 0 : a.parent): [] })), o })),
                    Jt = (0, r.Mz)(B.A, (e => function t() { let n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
                            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0; return n.length ? n.reduce(((n, a) => { const o = e[a]; return Math.max(n, t(o, r + 1)) }), r) : r }(e[l.Uz] || []))),
                    en = ((0, r.Mz)(N.A, y.Zx, D.A, ((e, t, n) => { var r, a, o, i, l, s, c, d, u, h, m; const p = e.map((e => { let { bgcolor: t } = e; return t })),
                            f = (null === t || void 0 === t || null === (r = t.rules) || void 0 === r ? void 0 : r.map((e => { let { color: t } = e; return t }))) || [],
                            v = [null === (a = n.base.cards) || void 0 === a || null === (o = a.cardFrame) || void 0 === o ? void 0 : o.color, null === (i = n.base.cards) || void 0 === i || null === (l = i.cardFrame) || void 0 === l ? void 0 : l.backgroundColor, null === (s = n.base.cards) || void 0 === s || null === (c = s.lines) || void 0 === c || null === (d = c.primary) || void 0 === d ? void 0 : d.color, null === (u = n.base.cards) || void 0 === u || null === (h = u.lines) || void 0 === h || null === (m = h.secondary) || void 0 === m ? void 0 : m.color]; return Array.from(new Set([...p, ...f, ...v].filter((function(e) { return e && "#ffffff" !== e && "#FFFFFF" !== e })))) })), e => (0, r.Mz)(F.A, (t => { if (!e || e === l.Uz) return []; const n = [e],
                            r = [],
                            a = t[e]; return a ? function e(a) { const o = t[a]; return o && (r.push(o), n.push(a)), null !== o && void 0 !== o && o.parent && !n.includes(null === o || void 0 === o ? void 0 : o.parent) ? e(o.parent) : r }(a.parent) : [] }))),
                    tn = (0, r.Mz)(Pe, (e => (null === e || void 0 === e ? void 0 : e.map((e => e.id))) || [])),
                    nn = ((0, r.Mz)(Pe, F.A, ((e, t) => null === e || void 0 === e ? void 0 : e.map((e => { let { id: n, storeId: r } = e; return { role: { ...t[n], storeId: r } } })))), (0, r.Mz)(tn, Ve, De, ((e, t, n) => { var r; const a = null !== n && void 0 !== n && null !== (r = n.tableOfContents) && void 0 !== r && r.visible ? null === n || void 0 === n ? void 0 : n.tableOfContents.totalToCPages : 0; return n => je(n, e, t) + a }))),
                    rn = (0, r.Mz)(y.Mk, Oe, F.A, Ve, Re, R.rW, ((e, t, n, r, a, o) => { var i; const l = (null === o || void 0 === o ? void 0 : o.topRoles) || [],
                            s = (null === (i = ((null === o || void 0 === o ? void 0 : o.pageBreaks) || {})[(null === e || void 0 === e ? void 0 : e.id) || "default-chart-id"]) || void 0 === i ? void 0 : i.map((e => ({ ...e })))) || [],
                            c = e => { const t = e.map((e => null === e || void 0 === e ? void 0 : e.id)); return e.map((e => { var n; const a = r(null === e || void 0 === e ? void 0 : e.id),
                                        o = a.find((e => t.includes(null === e || void 0 === e ? void 0 : e.id))); return { ...e, prevId: (null === o || void 0 === o ? void 0 : o.id) || (null === (n = a[(null === a || void 0 === a ? void 0 : a.length) - 1]) || void 0 === n ? void 0 : n.id) || "root" } })) };

                        function d(e) { const t = Object.keys(e); return n[null === e || void 0 === e ? void 0 : e.id] || t.includes("name") || "root" === (null === e || void 0 === e ? void 0 : e.id) } const u = e => { const t = e.map((e => null === e || void 0 === e ? void 0 : e.id)).filter((e => e)); return e.map((e => { var r, o, i; let l = n[null === e || void 0 === e ? void 0 : e.id]; if (!l) { const n = a({ roleName: e.name, firstRoleMember: e.firstRoleMember, firstParentRoleName: e.firstParentRoleName }); if (!n) return null;
                                    l = { ...n }, t.push(null === n || void 0 === n ? void 0 : n.id), e.id = null === n || void 0 === n ? void 0 : n.id } const s = (0, V.mA)(l),
                                    c = null !== (r = l) && void 0 !== r && r.members && null !== (o = l) && void 0 !== o && o.members[0] && (null === (i = l) || void 0 === i ? void 0 : i.members[0]) || ""; return { ...e, name: s, firstRoleMember: c } })).filter((e => null !== e)) }; return null !== l && void 0 !== l && l.length ? { ...o, pageBreaks: c(u(s.filter((e => { var n; return (null === e || void 0 === e ? void 0 : e.id) && d(e) && t({ parentId: null === (n = l[0]) || void 0 === n ? void 0 : n.id, descendentId: null === e || void 0 === e ? void 0 : e.id }) })) || [])) } : { ...o, pageBreaks: c(u(s.filter((e => (null === e || void 0 === e ? void 0 : e.id) && d(e))))) } })),
                    an = (0, r.Mz)(((e, t) => t), N.A, g.xt, ((e, t, n) => { if (!e || !(0, K.B9)(e)) return null; if (0 === (null === t || void 0 === t ? void 0 : t.length)) return null; let r; const a = null === t || void 0 === t ? void 0 : t.find((t => { var a; if (null !== t && void 0 !== t && null !== (a = t.members) && void 0 !== a && a.length) return r = t.members.find((t => { const r = n[t]; return r && r.email === e })), !!r })); return { roleId: null === a || void 0 === a ? void 0 : a.id, member: r || null } })) }, 67479: (e, t, n) => { "use strict";
                n.d(t, { AZ: () => b, cb: () => w }); var r = n(97105),
                    a = n(80192),
                    o = n(43331),
                    i = n(19367),
                    l = n(45418),
                    s = n(7743),
                    c = n(10621); const { selectIds: d, selectAll: u, selectEntities: h, selectById: m } = r.CA.getSelectors(), p = e => h(e.userManagement), f = e => u(e.userManagement), v = (0, a.Mz)(f, (e => { const t = {}; for (let n = 0; n < e.length; n++) { const r = e[n],
                            a = (null === r || void 0 === r ? void 0 : r.organizations) || []; for (let e = 0; e < a.length; e++) { const { id: n } = a[e];
                            t[n] = t[n] || [], t[n].push(r.email) } } return t })), g = (0, a.Mz)(f, (e => { const t = {}; for (let n = 0; n < e.length; n++) { const r = e[n],
                            a = (null === r || void 0 === r ? void 0 : r.charts) || []; for (let e = 0; e < a.length; e++) { const { id: n, orgId: o, access: i } = a[e];
                            t[n] = t[n] || [], t[n].push({ email: r.email, orgId: o, type: null === i || void 0 === i ? void 0 : i.toLowerCase() }) } } return t })), y = (0, a.Mz)(f, (e => { const t = {}; for (let n = 0; n < e.length; n++) { const r = e[n],
                            a = (null === r || void 0 === r ? void 0 : r.licenses) || []; for (let e = 0; e < a.length; e++) { const { id: n } = a[e];
                            t[n] = t[n] || [], t[n].push(r.email) } } return t })), b = (0, a.Mz)(o.BF, p, v, y, g, ((e, t, n, r, a) => { const { id: o, license: i } = e, l = i && r[i] || [], s = n[o] || [], c = [], d = a ? Object.values(a) : []; return d && d.forEach((e => { c.push(...(e || []).filter((e => e.orgId === o)).map((e => e.email))) })), [...new Set([...l, ...s, ...c])].map((e => t[e])) })), w = e => (0, a.Mz)(p, (t => t[e]));
                (0, a.Mz)(l.wr, g, i.Pq, p, s.gJ, ((e, t, n, r, a) => { var o; let i = [...t[n] || []].filter((e => { var t; const n = null === (t = r[null === e || void 0 === e ? void 0 : e.email]) || void 0 === t ? void 0 : t.type; return "viewer" === n || "editor" === n })); const l = null === (o = a[c.x2.EMAIL]) || void 0 === o ? void 0 : o.id; for (let s = 0; s < i.length; s++) i[s] = { ...i[s], ...e.filter((e => e[l] === i[s].email))[0] } || i[s]; return i })) }, 42006: (e, t, n) => { "use strict";
                n.d(t, { AU: () => g, Ok: () => p, Sj: () => h, VF: () => y, VW: () => m, YM: () => b, eW: () => w, n8: () => v, zl: () => z }); var r = n(80192),
                    a = n(69219),
                    o = n(43331),
                    i = n(77604),
                    l = n(19367),
                    s = n(59177),
                    c = n(78396),
                    d = n(15622); const u = { owner: 3, admin: 2, editor: 1, viewer: 0 }; const h = e => e.user.id,
                    m = e => e.user,
                    p = e => e.user.organizations || [],
                    f = e => { var t, n; return (null === e || void 0 === e || null === (t = e.user) || void 0 === t || null === (n = t.licenses) || void 0 === n ? void 0 : n.map((e => { var t; let n = [],
                                r = []; return null !== e && void 0 !== e && null !== (t = e.owners) && void 0 !== t && t.length && e.owners.forEach((e => { "object" === typeof e ? (n.push(null === e || void 0 === e ? void 0 : e.id), r.push(e)) : r.push(e) })), { ...e, owners: n, ownersObjs: r } }))) || [] },
                    v = (0, r.Mz)(p, (function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; return null === e || void 0 === e ? void 0 : e.reduce(((e, t) => { const n = null === t || void 0 === t ? void 0 : t.license,
                                r = null === t || void 0 === t ? void 0 : t.accessLevel; if (Object.keys(e).includes(n)) { const t = e[n];
                                c.ZE[r] >= c.ZE[t] && (e[n] = r) } else e[n] = r; return e }), {}) })),
                    g = ((0, r.Mz)(f, h, p, (function() { let e = arguments.length > 1 ? arguments[1] : void 0,
                            t = arguments.length > 2 ? arguments[2] : void 0,
                            n = [],
                            r = []; const a = {}; return (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []).forEach((t => { var r, o, i, l, c, d, u, h, m, p, f, v, g; const y = "trialing" === (null === (r = t) || void 0 === r ? void 0 : r.status),
                                b = "standard" === (null === (o = t) || void 0 === o ? void 0 : o.type) ? "BASIC" : (null === (i = t) || void 0 === i ? void 0 : i.type.toUpperCase()) || ""; let w = y ? null === (l = t) || void 0 === l || null === (c = l.trialInfo) || void 0 === c ? void 0 : c.planName : "".concat((0, s.Sn)(b), " Plan") || 0; const z = "annual" === (null === (d = t) || void 0 === d || null === (u = d.plan) || void 0 === u ? void 0 : u.frequency),
                                x = t.user.id === e ? "You" : null === (h = t) || void 0 === h || null === (m = h.user) || void 0 === m ? void 0 : m.username,
                                A = null === (p = t) || void 0 === p ? void 0 : p.ownersObjs.map((t => (null === t || void 0 === t ? void 0 : t.id) === e ? "You" !== x ? "You" : void 0 : null === t || void 0 === t ? void 0 : t.username)).filter((e => e));
                            t = { ...t, planTitlePrimary: w, isAnnualPlan: z, createdBy: x, accountOwners: A, isLicenseTrialing: y }, a[null === (f = t) || void 0 === f ? void 0 : f.id] = t, null !== (v = t) && void 0 !== v && null !== (g = v.owners) && void 0 !== g && g.includes(e) && n.push({ ...t, isOwner: !0 }) })), t.forEach((e => { const t = r.map((e => null === e || void 0 === e ? void 0 : e.id)); "admin" !== e.accessLevel || t.includes(null === e || void 0 === e ? void 0 : e.license) || r.push(a[null === e || void 0 === e ? void 0 : e.license]) })), { licensesAsOwners: n, licensesAsAdmins: r } })), (0, r.Mz)(h, f, ((e, t) => !t || t.length <= 0 ? null : t.reduce(((t, n) => !t || (0, i.S5)(n, t, e) ? n : t || n), null)))),
                    y = d.A,
                    b = ((0, r.Mz)(y, (e => null === e || void 0 === e ? void 0 : e.id)), (0, r.Mz)(y, (e => e.organization), (e => e.user), ((e, t, n) => { if (!e) return !1; if ("free" === e.type) return t.user === n.id; { var r, a, o; let t = e && "free" !== e.type,
                                i = ((null === e || void 0 === e || null === (r = e.user) || void 0 === r ? void 0 : r.id) || (null === e || void 0 === e ? void 0 : e.user)) === n.id,
                                l = !(!e || !e.owners) && -1 !== (null === e || void 0 === e || null === (a = e.owners) || void 0 === a || null === (o = a.map((e => (null === e || void 0 === e ? void 0 : e.id) || e))) || void 0 === o ? void 0 : o.indexOf(n.id)); return t && (i || l) } }))),
                    w = ((0, r.Mz)(g, p, ((e, t) => { if (!e) return "public"; const n = e.id; return function(e) { let t = "viewer"; return e.forEach((e => { e.accessLevel && u[e.accessLevel] > u[t] && (t = e.accessLevel) })), t }(t.filter((e => e.license === n))) })), (0, r.Mz)(o.mn, a.uo, l.Jf, o.M3, y, ((e, t, n, r, a) => "public" === t || "embed" === t ? "public" : e && "r" === (null === n || void 0 === n ? void 0 : n.rw) ? "viewer" : r || ((null === a || void 0 === a ? void 0 : a.accessLevel) || "public")))),
                    z = ((0, r.Mz)(p, (e => e.filter((e => "owner" === (null === e || void 0 === e ? void 0 : e.accessLevel) || "admin" === (null === e || void 0 === e ? void 0 : e.accessLevel))) || [])), (0, r.Mz)(a.uo, y, ((e, t) => "public" === e || "embed" === e ? "viewer" : (null === t || void 0 === t ? void 0 : t.accessLevel) || "public")), (0, r.Mz)((e => e.user.organizations), (e => (e || []).filter((e => "owner" === e.accessLevel || "admin" === e.accessLevel))))) }, 35513: (e, t, n) => { "use strict";
                n.d(t, { A: () => ae }); var r = function() {
                        function e(e) { var t = this;
                            this._insertTag = function(e) { var n;
                                n = 0 === t.tags.length ? t.insertionPoint ? t.insertionPoint.nextSibling : t.prepend ? t.container.firstChild : t.before : t.tags[t.tags.length - 1].nextSibling, t.container.insertBefore(e, n), t.tags.push(e) }, this.isSpeedy = void 0 === e.speedy || e.speedy, this.tags = [], this.ctr = 0, this.nonce = e.nonce, this.key = e.key, this.container = e.container, this.prepend = e.prepend, this.insertionPoint = e.insertionPoint, this.before = null } var t = e.prototype; return t.hydrate = function(e) { e.forEach(this._insertTag) }, t.insert = function(e) { this.ctr % (this.isSpeedy ? 65e3 : 1) === 0 && this._insertTag(function(e) { var t = document.createElement("style"); return t.setAttribute("data-emotion", e.key), void 0 !== e.nonce && t.setAttribute("nonce", e.nonce), t.appendChild(document.createTextNode("")), t.setAttribute("data-s", ""), t }(this)); var t = this.tags[this.tags.length - 1]; if (this.isSpeedy) { var n = function(e) { if (e.sheet) return e.sheet; for (var t = 0; t < document.styleSheets.length; t++)
                                        if (document.styleSheets[t].ownerNode === e) return document.styleSheets[t] }(t); try { n.insertRule(e, n.cssRules.length) } catch (r) { 0 } } else t.appendChild(document.createTextNode(e));
                            this.ctr++ }, t.flush = function() { this.tags.forEach((function(e) { return e.parentNode && e.parentNode.removeChild(e) })), this.tags = [], this.ctr = 0 }, e }(),
                    a = Math.abs,
                    o = String.fromCharCode,
                    i = Object.assign;

                function l(e) { return e.trim() }

                function s(e, t, n) { return e.replace(t, n) }

                function c(e, t) { return e.indexOf(t) }

                function d(e, t) { return 0 | e.charCodeAt(t) }

                function u(e, t, n) { return e.slice(t, n) }

                function h(e) { return e.length }

                function m(e) { return e.length }

                function p(e, t) { return t.push(e), e } var f = 1,
                    v = 1,
                    g = 0,
                    y = 0,
                    b = 0,
                    w = "";

                function z(e, t, n, r, a, o, i) { return { value: e, root: t, parent: n, type: r, props: a, children: o, line: f, column: v, length: i, return: "" } }

                function x(e, t) { return i(z("", null, null, "", null, null, 0), e, { length: -e.length }, t) }

                function A() { return b = y > 0 ? d(w, --y) : 0, v--, 10 === b && (v = 1, f--), b }

                function k() { return b = y < g ? d(w, y++) : 0, v++, 10 === b && (v = 1, f++), b }

                function S() { return d(w, y) }

                function M() { return y }

                function E(e, t) { return u(w, e, t) }

                function C(e) { switch (e) {
                        case 0:
                        case 9:
                        case 10:
                        case 13:
                        case 32:
                            return 5;
                        case 33:
                        case 43:
                        case 44:
                        case 47:
                        case 62:
                        case 64:
                        case 126:
                        case 59:
                        case 123:
                        case 125:
                            return 4;
                        case 58:
                            return 3;
                        case 34:
                        case 39:
                        case 40:
                        case 91:
                            return 2;
                        case 41:
                        case 93:
                            return 1 } return 0 }

                function T(e) { return f = v = 1, g = h(w = e), y = 0, [] }

                function H(e) { return w = "", e }

                function L(e) { return l(E(y - 1, V(91 === e ? e + 2 : 40 === e ? e + 1 : e))) }

                function I(e) { for (;
                        (b = S()) && b < 33;) k(); return C(e) > 2 || C(b) > 3 ? "" : " " }

                function j(e, t) { for (; --t && k() && !(b < 48 || b > 102 || b > 57 && b < 65 || b > 70 && b < 97);); return E(e, M() + (t < 6 && 32 == S() && 32 == k())) }

                function V(e) { for (; k();) switch (b) {
                        case e:
                            return y;
                        case 34:
                        case 39:
                            34 !== e && 39 !== e && V(b); break;
                        case 40:
                            41 === e && V(e); break;
                        case 92:
                            k() }
                    return y }

                function O(e, t) { for (; k() && e + b !== 57 && (e + b !== 84 || 47 !== S());); return "/*" + E(t, y - 1) + "*" + o(47 === e ? e : k()) }

                function R(e) { for (; !C(S());) k(); return E(e, y) } var P = "-ms-",
                    D = "-moz-",
                    F = "-webkit-",
                    N = "comm",
                    _ = "rule",
                    B = "decl",
                    W = "@keyframes";

                function U(e, t) { for (var n = "", r = m(e), a = 0; a < r; a++) n += t(e[a], a, e, t) || ""; return n }

                function q(e, t, n, r) { switch (e.type) {
                        case "@layer":
                            if (e.children.length) break;
                        case "@import":
                        case B:
                            return e.return = e.return || e.value;
                        case N:
                            return "";
                        case W:
                            return e.return = e.value + "{" + U(e.children, r) + "}";
                        case _:
                            e.value = e.props.join(",") } return h(n = U(e.children, r)) ? e.return = e.value + "{" + n + "}" : "" }

                function G(e) { return H(K("", null, null, null, [""], e = T(e), 0, [0], e)) }

                function K(e, t, n, r, a, i, l, u, m) { for (var f = 0, v = 0, g = l, y = 0, b = 0, w = 0, z = 1, x = 1, E = 1, C = 0, T = "", H = a, V = i, P = r, D = T; x;) switch (w = C, C = k()) {
                        case 40:
                            if (108 != w && 58 == d(D, g - 1)) {-1 != c(D += s(L(C), "&", "&\f"), "&\f") && (E = -1); break }
                        case 34:
                        case 39:
                        case 91:
                            D += L(C); break;
                        case 9:
                        case 10:
                        case 13:
                        case 32:
                            D += I(w); break;
                        case 92:
                            D += j(M() - 1, 7); continue;
                        case 47:
                            switch (S()) {
                                case 42:
                                case 47:
                                    p(Y(O(k(), M()), t, n), m); break;
                                default:
                                    D += "/" } break;
                        case 123 * z:
                            u[f++] = h(D) * E;
                        case 125 * z:
                        case 59:
                        case 0:
                            switch (C) {
                                case 0:
                                case 125:
                                    x = 0;
                                case 59 + v:
                                    -1 == E && (D = s(D, /\f/g, "")), b > 0 && h(D) - g && p(b > 32 ? X(D + ";", r, n, g - 1) : X(s(D, " ", "") + ";", r, n, g - 2), m); break;
                                case 59:
                                    D += ";";
                                default:
                                    if (p(P = Z(D, t, n, f, v, a, u, T, H = [], V = [], g), i), 123 === C)
                                        if (0 === v) K(D, t, P, P, H, i, g, u, V);
                                        else switch (99 === y && 110 === d(D, 3) ? 100 : y) {
                                            case 100:
                                            case 108:
                                            case 109:
                                            case 115:
                                                K(e, P, P, r && p(Z(e, P, P, 0, 0, a, u, T, a, H = [], g), V), a, V, g, u, r ? H : V); break;
                                            default:
                                                K(D, P, P, P, [""], V, 0, u, V) } } f = v = b = 0, z = E = 1, T = D = "", g = l; break;
                        case 58:
                            g = 1 + h(D), b = w;
                        default:
                            if (z < 1)
                                if (123 == C) --z;
                                else if (125 == C && 0 == z++ && 125 == A()) continue; switch (D += o(C), C * z) {
                                case 38:
                                    E = v > 0 ? 1 : (D += "\f", -1); break;
                                case 44:
                                    u[f++] = (h(D) - 1) * E, E = 1; break;
                                case 64:
                                    45 === S() && (D += L(k())), y = S(), v = g = h(T = D += R(M())), C++; break;
                                case 45:
                                    45 === w && 2 == h(D) && (z = 0) } }
                    return i }

                function Z(e, t, n, r, o, i, c, d, h, p, f) { for (var v = o - 1, g = 0 === o ? i : [""], y = m(g), b = 0, w = 0, x = 0; b < r; ++b)
                        for (var A = 0, k = u(e, v + 1, v = a(w = c[b])), S = e; A < y; ++A)(S = l(w > 0 ? g[A] + " " + k : s(k, /&\f/g, g[A]))) && (h[x++] = S); return z(e, t, n, 0 === o ? _ : d, h, p, f) }

                function Y(e, t, n) { return z(e, t, n, N, o(b), u(e, 2, -2), 0) }

                function X(e, t, n, r) { return z(e, t, n, B, u(e, 0, r), u(e, r + 1, -1), r) } var $ = function(e, t, n) { for (var r = 0, a = 0; r = a, a = S(), 38 === r && 12 === a && (t[n] = 1), !C(a);) k(); return E(e, y) },
                    Q = function(e, t) { return H(function(e, t) { var n = -1,
                                r = 44;
                            do { switch (C(r)) {
                                    case 0:
                                        38 === r && 12 === S() && (t[n] = 1), e[n] += $(y - 1, t, n); break;
                                    case 2:
                                        e[n] += L(r); break;
                                    case 4:
                                        if (44 === r) { e[++n] = 58 === S() ? "&\f" : "", t[n] = e[n].length; break }
                                    default:
                                        e[n] += o(r) } } while (r = k()); return e }(T(e), t)) },
                    J = new WeakMap,
                    ee = function(e) { if ("rule" === e.type && e.parent && !(e.length < 1)) { for (var t = e.value, n = e.parent, r = e.column === n.column && e.line === n.line;
                                "rule" !== n.type;)
                                if (!(n = n.parent)) return; if ((1 !== e.props.length || 58 === t.charCodeAt(0) || J.get(n)) && !r) { J.set(e, !0); for (var a = [], o = Q(t, a), i = n.props, l = 0, s = 0; l < o.length; l++)
                                    for (var c = 0; c < i.length; c++, s++) e.props[s] = a[l] ? o[l].replace(/&\f/g, i[c]) : i[c] + " " + o[l] } } },
                    te = function(e) { if ("decl" === e.type) { var t = e.value;
                            108 === t.charCodeAt(0) && 98 === t.charCodeAt(2) && (e.return = "", e.value = "") } };

                function ne(e, t) { switch (function(e, t) { return 45 ^ d(e, 0) ? (((t << 2 ^ d(e, 0)) << 2 ^ d(e, 1)) << 2 ^ d(e, 2)) << 2 ^ d(e, 3) : 0 }(e, t)) {
                        case 5103:
                            return F + "print-" + e + e;
                        case 5737:
                        case 4201:
                        case 3177:
                        case 3433:
                        case 1641:
                        case 4457:
                        case 2921:
                        case 5572:
                        case 6356:
                        case 5844:
                        case 3191:
                        case 6645:
                        case 3005:
                        case 6391:
                        case 5879:
                        case 5623:
                        case 6135:
                        case 4599:
                        case 4855:
                        case 4215:
                        case 6389:
                        case 5109:
                        case 5365:
                        case 5621:
                        case 3829:
                            return F + e + e;
                        case 5349:
                        case 4246:
                        case 4810:
                        case 6968:
                        case 2756:
                            return F + e + D + e + P + e + e;
                        case 6828:
                        case 4268:
                            return F + e + P + e + e;
                        case 6165:
                            return F + e + P + "flex-" + e + e;
                        case 5187:
                            return F + e + s(e, /(\w+).+(:[^]+)/, F + "box-$1$2" + P + "flex-$1$2") + e;
                        case 5443:
                            return F + e + P + "flex-item-" + s(e, /flex-|-self/, "") + e;
                        case 4675:
                            return F + e + P + "flex-line-pack" + s(e, /align-content|flex-|-self/, "") + e;
                        case 5548:
                            return F + e + P + s(e, "shrink", "negative") + e;
                        case 5292:
                            return F + e + P + s(e, "basis", "preferred-size") + e;
                        case 6060:
                            return F + "box-" + s(e, "-grow", "") + F + e + P + s(e, "grow", "positive") + e;
                        case 4554:
                            return F + s(e, /([^-])(transform)/g, "$1" + F + "$2") + e;
                        case 6187:
                            return s(s(s(e, /(zoom-|grab)/, F + "$1"), /(image-set)/, F + "$1"), e, "") + e;
                        case 5495:
                        case 3959:
                            return s(e, /(image-set\([^]*)/, F + "$1$`$1");
                        case 4968:
                            return s(s(e, /(.+:)(flex-)?(.*)/, F + "box-pack:$3" + P + "flex-pack:$3"), /s.+-b[^;]+/, "justify") + F + e + e;
                        case 4095:
                        case 3583:
                        case 4068:
                        case 2532:
                            return s(e, /(.+)-inline(.+)/, F + "$1$2") + e;
                        case 8116:
                        case 7059:
                        case 5753:
                        case 5535:
                        case 5445:
                        case 5701:
                        case 4933:
                        case 4677:
                        case 5533:
                        case 5789:
                        case 5021:
                        case 4765:
                            if (h(e) - 1 - t > 6) switch (d(e, t + 1)) {
                                case 109:
                                    if (45 !== d(e, t + 4)) break;
                                case 102:
                                    return s(e, /(.+:)(.+)-([^]+)/, "$1" + F + "$2-$3$1" + D + (108 == d(e, t + 3) ? "$3" : "$2-$3")) + e;
                                case 115:
                                    return ~c(e, "stretch") ? ne(s(e, "stretch", "fill-available"), t) + e : e }
                            break;
                        case 4949:
                            if (115 !== d(e, t + 1)) break;
                        case 6444:
                            switch (d(e, h(e) - 3 - (~c(e, "!important") && 10))) {
                                case 107:
                                    return s(e, ":", ":" + F) + e;
                                case 101:
                                    return s(e, /(.+:)([^;!]+)(;|!.+)?/, "$1" + F + (45 === d(e, 14) ? "inline-" : "") + "box$3$1" + F + "$2$3$1" + P + "$2box$3") + e } break;
                        case 5936:
                            switch (d(e, t + 11)) {
                                case 114:
                                    return F + e + P + s(e, /[svh]\w+-[tblr]{2}/, "tb") + e;
                                case 108:
                                    return F + e + P + s(e, /[svh]\w+-[tblr]{2}/, "tb-rl") + e;
                                case 45:
                                    return F + e + P + s(e, /[svh]\w+-[tblr]{2}/, "lr") + e } return F + e + P + e + e } return e } var re = [function(e, t, n, r) { if (e.length > -1 && !e.return) switch (e.type) {
                            case B:
                                e.return = ne(e.value, e.length); break;
                            case W:
                                return U([x(e, { value: s(e.value, "@", "@" + F) })], r);
                            case _:
                                if (e.length) return function(e, t) { return e.map(t).join("") }(e.props, (function(t) { switch (function(e, t) { return (e = t.exec(e)) ? e[0] : e }(t, /(::plac\w+|:read-\w+)/)) {
                                        case ":read-only":
                                        case ":read-write":
                                            return U([x(e, { props: [s(t, /:(read-\w+)/, ":-moz-$1")] })], r);
                                        case "::placeholder":
                                            return U([x(e, { props: [s(t, /:(plac\w+)/, ":" + F + "input-$1")] }), x(e, { props: [s(t, /:(plac\w+)/, ":-moz-$1")] }), x(e, { props: [s(t, /:(plac\w+)/, P + "input-$1")] })], r) } return "" })) } }],
                    ae = function(e) { var t = e.key; if ("css" === t) { var n = document.querySelectorAll("style[data-emotion]:not([data-s])");
                            Array.prototype.forEach.call(n, (function(e) {-1 !== e.getAttribute("data-emotion").indexOf(" ") && (document.head.appendChild(e), e.setAttribute("data-s", "")) })) } var a = e.stylisPlugins || re; var o, i, l = {},
                            s = [];
                        o = e.container || document.head, Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="' + t + ' "]'), (function(e) { for (var t = e.getAttribute("data-emotion").split(" "), n = 1; n < t.length; n++) l[t[n]] = !0;
                            s.push(e) })); var c, d, u = [q, (d = function(e) { c.insert(e) }, function(e) { e.root || (e = e.return) && d(e) })],
                            h = function(e) { var t = m(e); return function(n, r, a, o) { for (var i = "", l = 0; l < t; l++) i += e[l](n, r, a, o) || ""; return i } }([ee, te].concat(a, u));
                        i = function(e, t, n, r) { c = n, U(G(e ? e + "{" + t.styles + "}" : t.styles), h), r && (p.inserted[t.name] = !0) }; var p = { key: t, sheet: new r({ key: t, container: o, nonce: e.nonce, speedy: e.speedy, prepend: e.prepend, insertionPoint: e.insertionPoint }), nonce: e.nonce, inserted: l, registered: {}, insert: i }; return p.sheet.hydrate(s), p } }, 11068: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(30918),
                    a = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,
                    o = (0, r.A)((function(e) { return a.test(e) || 111 === e.charCodeAt(0) && 110 === e.charCodeAt(1) && e.charCodeAt(2) < 91 })) }, 30918: (e, t, n) => { "use strict";

                function r(e) { var t = Object.create(null); return function(n) { return void 0 === t[n] && (t[n] = e(n)), t[n] } } n.d(t, { A: () => r }) }, 55756: (e, t, n) => { "use strict";
                n.d(t, { C: () => l, T: () => c, i: () => o, w: () => s }); var r = n(65043),
                    a = n(35513),
                    o = (n(12830), n(69436), !0),
                    i = r.createContext("undefined" !== typeof HTMLElement ? (0, a.A)({ key: "css" }) : null); var l = i.Provider,
                    s = function(e) { return (0, r.forwardRef)((function(t, n) { var a = (0, r.useContext)(i); return e(t, a, n) })) };
                o || (s = function(e) { return function(t) { var n = (0, r.useContext)(i); return null === n ? (n = (0, a.A)({ key: "css" }), r.createElement(i.Provider, { value: n }, e(t, n))) : e(t, n) } }); var c = r.createContext({}) }, 83290: (e, t, n) => { "use strict";
                n.d(t, { AH: () => c, i7: () => d, mL: () => s }); var r = n(55756),
                    a = n(65043),
                    o = n(81722),
                    i = n(69436),
                    l = n(12830),
                    s = (n(35513), n(80219), (0, r.w)((function(e, t) { var n = e.styles,
                            s = (0, l.J)([n], void 0, a.useContext(r.T)); if (!r.i) { for (var c, d = s.name, u = s.styles, h = s.next; void 0 !== h;) d += " " + h.name, u += h.styles, h = h.next; var m = !0 === t.compat,
                                p = t.insert("", { name: d, styles: u }, t.sheet, m); return m ? null : a.createElement("style", ((c = {})["data-emotion"] = t.key + "-global " + d, c.dangerouslySetInnerHTML = { __html: p }, c.nonce = t.sheet.nonce, c)) } var f = a.useRef(); return (0, i.i)((function() { var e = t.key + "-global",
                                n = new t.sheet.constructor({ key: e, nonce: t.sheet.nonce, container: t.sheet.container, speedy: t.sheet.isSpeedy }),
                                r = !1,
                                a = document.querySelector('style[data-emotion="' + e + " " + s.name + '"]'); return t.sheet.tags.length && (n.before = t.sheet.tags[0]), null !== a && (r = !0, a.setAttribute("data-emotion", e), n.hydrate([a])), f.current = [n, r],
                                function() { n.flush() } }), [t]), (0, i.i)((function() { var e = f.current,
                                n = e[0]; if (e[1]) e[1] = !1;
                            else { if (void 0 !== s.next && (0, o.sk)(t, s.next, !0), n.tags.length) { var r = n.tags[n.tags.length - 1].nextElementSibling;
                                    n.before = r, n.flush() } t.insert("", s, n, !1) } }), [t, s.name]), null })));

                function c() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n]; return (0, l.J)(t) } var d = function() { var e = c.apply(void 0, arguments),
                        t = "animation-" + e.name; return { name: t, styles: "@keyframes " + t + "{" + e.styles + "}", anim: 1, toString: function() { return "_EMO_" + this.name + "_" + this.styles + "_EMO_" } } } }, 12830: (e, t, n) => { "use strict";
                n.d(t, { J: () => p }); var r = { animationIterationCount: 1, aspectRatio: 1, borderImageOutset: 1, borderImageSlice: 1, borderImageWidth: 1, boxFlex: 1, boxFlexGroup: 1, boxOrdinalGroup: 1, columnCount: 1, columns: 1, flex: 1, flexGrow: 1, flexPositive: 1, flexShrink: 1, flexNegative: 1, flexOrder: 1, gridRow: 1, gridRowEnd: 1, gridRowSpan: 1, gridRowStart: 1, gridColumn: 1, gridColumnEnd: 1, gridColumnSpan: 1, gridColumnStart: 1, msGridRow: 1, msGridRowSpan: 1, msGridColumn: 1, msGridColumnSpan: 1, fontWeight: 1, lineHeight: 1, opacity: 1, order: 1, orphans: 1, tabSize: 1, widows: 1, zIndex: 1, zoom: 1, WebkitLineClamp: 1, fillOpacity: 1, floodOpacity: 1, stopOpacity: 1, strokeDasharray: 1, strokeDashoffset: 1, strokeMiterlimit: 1, strokeOpacity: 1, strokeWidth: 1 },
                    a = n(30918),
                    o = /[A-Z]|^ms/g,
                    i = /_EMO_([^_]+?)_([^]*?)_EMO_/g,
                    l = function(e) { return 45 === e.charCodeAt(1) },
                    s = function(e) { return null != e && "boolean" !== typeof e },
                    c = (0, a.A)((function(e) { return l(e) ? e : e.replace(o, "-$&").toLowerCase() })),
                    d = function(e, t) { switch (e) {
                            case "animation":
                            case "animationName":
                                if ("string" === typeof t) return t.replace(i, (function(e, t, n) { return h = { name: t, styles: n, next: h }, t })) } return 1 === r[e] || l(e) || "number" !== typeof t || 0 === t ? t : t + "px" };

                function u(e, t, n) { if (null == n) return ""; if (void 0 !== n.__emotion_styles) return n; switch (typeof n) {
                        case "boolean":
                            return "";
                        case "object":
                            if (1 === n.anim) return h = { name: n.name, styles: n.styles, next: h }, n.name; if (void 0 !== n.styles) { var r = n.next; if (void 0 !== r)
                                    for (; void 0 !== r;) h = { name: r.name, styles: r.styles, next: h }, r = r.next; return n.styles + ";" } return function(e, t, n) { var r = ""; if (Array.isArray(n))
                                    for (var a = 0; a < n.length; a++) r += u(e, t, n[a]) + ";";
                                else
                                    for (var o in n) { var i = n[o]; if ("object" !== typeof i) null != t && void 0 !== t[i] ? r += o + "{" + t[i] + "}" : s(i) && (r += c(o) + ":" + d(o, i) + ";");
                                        else if (!Array.isArray(i) || "string" !== typeof i[0] || null != t && void 0 !== t[i[0]]) { var l = u(e, t, i); switch (o) {
                                                case "animation":
                                                case "animationName":
                                                    r += c(o) + ":" + l + ";"; break;
                                                default:
                                                    r += o + "{" + l + "}" } } else
                                            for (var h = 0; h < i.length; h++) s(i[h]) && (r += c(o) + ":" + d(o, i[h]) + ";") }
                                return r }(e, t, n);
                        case "function":
                            if (void 0 !== e) { var a = h,
                                    o = n(e); return h = a, u(e, t, o) } } if (null == t) return n; var i = t[n]; return void 0 !== i ? i : n } var h, m = /label:\s*([^\s;\n{]+)\s*(;|$)/g; var p = function(e, t, n) { if (1 === e.length && "object" === typeof e[0] && null !== e[0] && void 0 !== e[0].styles) return e[0]; var r = !0,
                        a = "";
                    h = void 0; var o = e[0];
                    null == o || void 0 === o.raw ? (r = !1, a += u(n, t, o)) : a += o[0]; for (var i = 1; i < e.length; i++) a += u(n, t, e[i]), r && (a += o[i]);
                    m.lastIndex = 0; for (var l, s = ""; null !== (l = m.exec(a));) s += "-" + l[1]; var c = function(e) { for (var t, n = 0, r = 0, a = e.length; a >= 4; ++r, a -= 4) t = 1540483477 * (65535 & (t = 255 & e.charCodeAt(r) | (255 & e.charCodeAt(++r)) << 8 | (255 & e.charCodeAt(++r)) << 16 | (255 & e.charCodeAt(++r)) << 24)) + (59797 * (t >>> 16) << 16), n = 1540483477 * (65535 & (t ^= t >>> 24)) + (59797 * (t >>> 16) << 16) ^ 1540483477 * (65535 & n) + (59797 * (n >>> 16) << 16); switch (a) {
                            case 3:
                                n ^= (255 & e.charCodeAt(r + 2)) << 16;
                            case 2:
                                n ^= (255 & e.charCodeAt(r + 1)) << 8;
                            case 1:
                                n = 1540483477 * (65535 & (n ^= 255 & e.charCodeAt(r))) + (59797 * (n >>> 16) << 16) } return (((n = 1540483477 * (65535 & (n ^= n >>> 13)) + (59797 * (n >>> 16) << 16)) ^ n >>> 15) >>> 0).toString(36) }(a) + s; return { name: c, styles: a, next: h } } }, 69436: (e, t, n) => { "use strict"; var r;
                n.d(t, { i: () => l, s: () => i }); var a = n(65043),
                    o = !!(r || (r = n.t(a, 2))).useInsertionEffect && (r || (r = n.t(a, 2))).useInsertionEffect,
                    i = o || function(e) { return e() },
                    l = o || a.useLayoutEffect }, 81722: (e, t, n) => { "use strict";
                n.d(t, { Rk: () => r, SF: () => a, sk: () => o });

                function r(e, t, n) { var r = ""; return n.split(" ").forEach((function(n) { void 0 !== e[n] ? t.push(e[n] + ";") : r += n + " " })), r } var a = function(e, t, n) { var r = e.key + "-" + t.name;!1 === n && void 0 === e.registered[r] && (e.registered[r] = t.styles) },
                    o = function(e, t, n) { a(e, t, n); var r = e.key + "-" + t.name; if (void 0 === e.inserted[t.name]) { var o = t;
                            do { e.insert(t === o ? "." + r : "", o, e.sheet, !0), o = o.next } while (void 0 !== o) } } }, 59453: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#e3f2fd", 100: "#bbdefb", 200: "#90caf9", 300: "#64b5f6", 400: "#42a5f5", 500: "#2196f3", 600: "#1e88e5", 700: "#1976d2", 800: "#1565c0", 900: "#0d47a1", A100: "#82b1ff", A200: "#448aff", A400: "#2979ff", A700: "#2962ff" };
                t.default = n }, 74216: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { black: "#000", white: "#fff" };
                t.default = n }, 76302: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#e8f5e9", 100: "#c8e6c9", 200: "#a5d6a7", 300: "#81c784", 400: "#66bb6a", 500: "#4caf50", 600: "#43a047", 700: "#388e3c", 800: "#2e7d32", 900: "#1b5e20", A100: "#b9f6ca", A200: "#69f0ae", A400: "#00e676", A700: "#00c853" };
                t.default = n }, 28046: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#fafafa", 100: "#f5f5f5", 200: "#eeeeee", 300: "#e0e0e0", 400: "#bdbdbd", 500: "#9e9e9e", 600: "#757575", 700: "#616161", 800: "#424242", 900: "#212121", A100: "#d5d5d5", A200: "#aaaaaa", A400: "#303030", A700: "#616161" };
                t.default = n }, 1291: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#e8eaf6", 100: "#c5cae9", 200: "#9fa8da", 300: "#7986cb", 400: "#5c6bc0", 500: "#3f51b5", 600: "#3949ab", 700: "#303f9f", 800: "#283593", 900: "#1a237e", A100: "#8c9eff", A200: "#536dfe", A400: "#3d5afe", A700: "#304ffe" };
                t.default = n }, 91219: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#fff3e0", 100: "#ffe0b2", 200: "#ffcc80", 300: "#ffb74d", 400: "#ffa726", 500: "#ff9800", 600: "#fb8c00", 700: "#f57c00", 800: "#ef6c00", 900: "#e65100", A100: "#ffd180", A200: "#ffab40", A400: "#ff9100", A700: "#ff6d00" };
                t.default = n }, 25385: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#fce4ec", 100: "#f8bbd0", 200: "#f48fb1", 300: "#f06292", 400: "#ec407a", 500: "#e91e63", 600: "#d81b60", 700: "#c2185b", 800: "#ad1457", 900: "#880e4f", A100: "#ff80ab", A200: "#ff4081", A400: "#f50057", A700: "#c51162" };
                t.default = n }, 37794: (e, t) => { "use strict";
                Object.defineProperty(t, "__esModule", { value: !0 }), t.default = void 0; var n = { 50: "#ffebee", 100: "#ffcdd2", 200: "#ef9a9a", 300: "#e57373", 400: "#ef5350", 500: "#f44336", 600: "#e53935", 700: "#d32f2f", 800: "#c62828", 900: "#b71c1c", A100: "#ff8a80", A200: "#ff5252", A400: "#ff1744", A700: "#d50000" };
                t.default = n }, 96962: (e, t, n) => { "use strict";
                n.d(t, { A: () => d }); var r = n(58168),
                    a = n(80045),
                    o = n(65043),
                    i = n(43024),
                    l = n(71745); const s = (0, n(91917).A)(o.createElement("path", { d: "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" }), "Person"); var c = o.forwardRef((function(e, t) { var n = e.alt,
