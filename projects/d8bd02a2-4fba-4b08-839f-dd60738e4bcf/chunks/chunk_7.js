                    Kr = ["autoFocus", "children", "disabled", "selected", "value", "tabIndex", "onClick", "onKeyDown", "onFocus", "onBlur", "aria-current", "aria-label", "monthsPerRow"],
                    Zr = (0, ue.Ay)("div", { name: "MuiPickersMonth", slot: "Root", overridesResolver: (e, t) => [t.root] })({ display: "flex", alignItems: "center", justifyContent: "center", flexBasis: "33.3%", variants: [{ props: { monthsPerRow: 4 }, style: { flexBasis: "25%" } }] }),
                    Yr = (0, ue.Ay)("button", { name: "MuiPickersMonth", slot: "MonthButton", overridesResolver: (e, t) => [t.monthButton, {
                            ["&.".concat(Gr.disabled)]: t.disabled }, {
                            ["&.".concat(Gr.selected)]: t.selected }] })((e => { let { theme: t } = e; return (0, a.default)({ color: "unset", backgroundColor: "transparent", border: 0, outline: 0 }, t.typography.subtitle1, { margin: "8px 0", height: 36, width: 72, borderRadius: 18, cursor: "pointer", "&:focus": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.activeChannel, " / ").concat(t.vars.palette.action.hoverOpacity, ")") : (0, pr.X4)(t.palette.action.active, t.palette.action.hoverOpacity) }, "&:hover": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.activeChannel, " / ").concat(t.vars.palette.action.hoverOpacity, ")") : (0, pr.X4)(t.palette.action.active, t.palette.action.hoverOpacity) }, "&:disabled": { cursor: "auto", pointerEvents: "none" }, ["&.".concat(Gr.disabled)]: { color: (t.vars || t).palette.text.secondary }, ["&.".concat(Gr.selected)]: { color: (t.vars || t).palette.primary.contrastText, backgroundColor: (t.vars || t).palette.primary.main, "&:focus, &:hover": { backgroundColor: (t.vars || t).palette.primary.dark } } }) })),
                    Xr = r.memo((function(e) { const t = (0, i.A)({ props: e, name: "MuiPickersMonth" }),
                            { autoFocus: n, children: s, disabled: c, selected: d, value: u, tabIndex: h, onClick: m, onKeyDown: p, onFocus: f, onBlur: v, "aria-current": g, "aria-label": y } = t,
                            b = (0, o.default)(t, Kr),
                            w = r.useRef(null),
                            z = (e => { const { disabled: t, selected: n, classes: r } = e, a = { root: ["root"], monthButton: ["monthButton", t && "disabled", n && "selected"] }; return (0, he.A)(a, qr, r) })(t); return (0, rt.A)((() => { var e;
                            n && (null === (e = w.current) || void 0 === e || e.focus()) }), [n]), (0, l.jsx)(Zr, (0, a.default)({ className: z.root, ownerState: t }, b, { children: (0, l.jsx)(Yr, { ref: w, disabled: c, type: "button", role: "radio", tabIndex: c ? -1 : h, "aria-current": g, "aria-checked": d, "aria-label": y, onClick: e => m(e, u), onKeyDown: e => p(e, u), onFocus: e => f(e, u), onBlur: e => v(e, u), className: z.monthButton, ownerState: t, children: s }) })) }));

                function $r(e) { return (0, me.Ay)("MuiMonthCalendar", e) }(0, pe.A)("MuiMonthCalendar", ["root"]); const Qr = ["className", "value", "defaultValue", "referenceDate", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onChange", "shouldDisableMonth", "readOnly", "disableHighlightToday", "autoFocus", "onMonthFocus", "hasFocus", "onFocusedViewChange", "monthsPerRow", "timezone", "gridLabelId"]; const Jr = (0, ue.Ay)("div", { name: "MuiMonthCalendar", slot: "Root", overridesResolver: (e, t) => t.root })({ display: "flex", flexWrap: "wrap", alignContent: "stretch", padding: "0 4px", width: bt, boxSizing: "border-box" }),
                    ea = r.forwardRef((function(e, t) { const n = function(e, t) { const n = ae(),
                                    r = oe(),
                                    o = (0, i.A)({ props: e, name: t }); return (0, a.default)({ disableFuture: !1, disablePast: !1 }, o, { minDate: b(n, o.minDate, r.minDate), maxDate: b(n, o.maxDate, r.maxDate) }) }(e, "MuiMonthCalendar"),
                            { className: s, value: c, defaultValue: d, referenceDate: u, disabled: h, disableFuture: m, disablePast: p, maxDate: f, minDate: v, onChange: g, shouldDisableMonth: y, readOnly: z, autoFocus: x = !1, onMonthFocus: A, hasFocus: k, onFocusedViewChange: S, monthsPerRow: M = 3, timezone: E, gridLabelId: T } = n,
                            H = (0, o.default)(n, Qr),
                            { value: L, handleValueChange: I, timezone: j } = tt({ name: "MonthCalendar", timezone: E, value: c, defaultValue: d, onChange: g, valueManager: Q }),
                            V = le(j),
                            O = (0, Ur.A)(),
                            R = ae(),
                            P = r.useMemo((() => Q.getInitialReferenceValue({ value: L, utils: R, props: n, timezone: j, referenceDate: u, granularity: C.month })), []),
                            D = n,
                            F = (e => { const { classes: t } = e; return (0, he.A)({ root: ["root"] }, $r, t) })(D),
                            N = r.useMemo((() => R.getMonth(V)), [R, V]),
                            _ = r.useMemo((() => null != L ? R.getMonth(L) : null), [L, R]),
                            [B, W] = r.useState((() => _ || R.getMonth(P))),
                            [U, q] = (0, Je.A)({ name: "MonthCalendar", state: "hasFocus", controlled: k, default: null !== x && void 0 !== x && x }),
                            G = (0, Pe.A)((e => { q(e), S && S(e) })),
                            K = r.useCallback((e => { const t = R.startOfMonth(p && R.isAfter(V, v) ? V : v),
                                    n = R.startOfMonth(m && R.isBefore(V, f) ? V : f),
                                    r = R.startOfMonth(e); return !!R.isBefore(r, t) || (!!R.isAfter(r, n) || !!y && y(r)) }), [m, p, f, v, V, y, R]),
                            Z = (0, Pe.A)(((e, t) => { if (z) return; const n = R.setMonth(null !== L && void 0 !== L ? L : P, t);
                                I(n) })),
                            Y = (0, Pe.A)((e => { K(R.setMonth(null !== L && void 0 !== L ? L : P, e)) || (W(e), G(!0), A && A(e)) }));
                        r.useEffect((() => { W((e => null !== _ && e !== _ ? _ : e)) }), [_]); const X = (0, Pe.A)(((e, t) => { const n = 12; switch (e.key) {
                                    case "ArrowUp":
                                        Y((n + t - 3) % n), e.preventDefault(); break;
                                    case "ArrowDown":
                                        Y((n + t + 3) % n), e.preventDefault(); break;
                                    case "ArrowLeft":
                                        Y((n + t + ("ltr" === O.direction ? -1 : 1)) % n), e.preventDefault(); break;
                                    case "ArrowRight":
                                        Y((n + t + ("ltr" === O.direction ? 1 : -1)) % n), e.preventDefault() } })),
                            $ = (0, Pe.A)(((e, t) => { Y(t) })),
                            J = (0, Pe.A)(((e, t) => { B === t && G(!1) })); return (0, l.jsx)(Jr, (0, a.default)({ ref: t, className: ce(F.root, s), ownerState: D, role: "radiogroup", "aria-labelledby": T }, H, { children: w(R, null !== L && void 0 !== L ? L : P).map((e => { const t = R.getMonth(e),
                                    n = R.format(e, "monthShort"),
                                    r = R.format(e, "month"),
                                    a = t === _,
                                    o = h || K(e); return (0, l.jsx)(Xr, { selected: a, value: t, onClick: Z, onKeyDown: X, autoFocus: U && t === B, disabled: o, tabIndex: t === B ? 0 : -1, onFocus: $, onBlur: J, "aria-current": N === t ? "date" : void 0, "aria-label": r, monthsPerRow: M, children: n }, n) })) })) }));

                function ta(e) { return (0, me.Ay)("MuiPickersYear", e) } const na = (0, pe.A)("MuiPickersYear", ["root", "yearButton", "selected", "disabled"]),
                    ra = ["autoFocus", "className", "children", "disabled", "selected", "value", "tabIndex", "onClick", "onKeyDown", "onFocus", "onBlur", "aria-current", "yearsPerRow"],
                    aa = (0, ue.Ay)("div", { name: "MuiPickersYear", slot: "Root", overridesResolver: (e, t) => [t.root] })({ display: "flex", alignItems: "center", justifyContent: "center", flexBasis: "33.3%", variants: [{ props: { yearsPerRow: 4 }, style: { flexBasis: "25%" } }] }),
                    oa = (0, ue.Ay)("button", { name: "MuiPickersYear", slot: "YearButton", overridesResolver: (e, t) => [t.yearButton, {
                            ["&.".concat(na.disabled)]: t.disabled }, {
                            ["&.".concat(na.selected)]: t.selected }] })((e => { let { theme: t } = e; return (0, a.default)({ color: "unset", backgroundColor: "transparent", border: 0, outline: 0 }, t.typography.subtitle1, { margin: "6px 0", height: 36, width: 72, borderRadius: 18, cursor: "pointer", "&:focus": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.activeChannel, " / ").concat(t.vars.palette.action.focusOpacity, ")") : (0, pr.X4)(t.palette.action.active, t.palette.action.focusOpacity) }, "&:hover": { backgroundColor: t.vars ? "rgba(".concat(t.vars.palette.action.activeChannel, " / ").concat(t.vars.palette.action.hoverOpacity, ")") : (0, pr.X4)(t.palette.action.active, t.palette.action.hoverOpacity) }, "&:disabled": { cursor: "auto", pointerEvents: "none" }, ["&.".concat(na.disabled)]: { color: (t.vars || t).palette.text.secondary }, ["&.".concat(na.selected)]: { color: (t.vars || t).palette.primary.contrastText, backgroundColor: (t.vars || t).palette.primary.main, "&:focus, &:hover": { backgroundColor: (t.vars || t).palette.primary.dark } } }) })),
                    ia = r.memo((function(e) { const t = (0, i.A)({ props: e, name: "MuiPickersYear" }),
                            { autoFocus: n, className: s, children: c, disabled: d, selected: u, value: h, tabIndex: m, onClick: p, onKeyDown: f, onFocus: v, onBlur: g, "aria-current": y } = t,
                            b = (0, o.default)(t, ra),
                            w = r.useRef(null),
                            z = (e => { const { disabled: t, selected: n, classes: r } = e, a = { root: ["root"], yearButton: ["yearButton", t && "disabled", n && "selected"] }; return (0, he.A)(a, ta, r) })(t); return r.useEffect((() => { n && w.current.focus() }), [n]), (0, l.jsx)(aa, (0, a.default)({ className: ce(z.root, s), ownerState: t }, b, { children: (0, l.jsx)(oa, { ref: w, disabled: d, type: "button", role: "radio", tabIndex: d ? -1 : m, "aria-current": y, "aria-checked": u, onClick: e => p(e, h), onKeyDown: e => f(e, h), onFocus: e => v(e, h), onBlur: e => g(e, h), className: z.yearButton, ownerState: t, children: c }) })) }));

                function la(e) { return (0, me.Ay)("MuiYearCalendar", e) }(0, pe.A)("MuiYearCalendar", ["root"]); const sa = ["autoFocus", "className", "value", "defaultValue", "referenceDate", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onChange", "readOnly", "shouldDisableYear", "disableHighlightToday", "onYearFocus", "hasFocus", "onFocusedViewChange", "yearsPerRow", "timezone", "gridLabelId"]; const ca = (0, ue.Ay)("div", { name: "MuiYearCalendar", slot: "Root", overridesResolver: (e, t) => t.root })({ display: "flex", flexDirection: "row", flexWrap: "wrap", overflowY: "auto", height: "100%", padding: "0 4px", width: bt, maxHeight: 280, boxSizing: "border-box", position: "relative" }),
                    da = r.forwardRef((function(e, t) { const n = function(e, t) { var n; const r = ae(),
                                    o = oe(),
                                    l = (0, i.A)({ props: e, name: t }); return (0, a.default)({ disablePast: !1, disableFuture: !1 }, l, { yearsPerRow: null !== (n = l.yearsPerRow) && void 0 !== n ? n : 3, minDate: b(r, l.minDate, o.minDate), maxDate: b(r, l.maxDate, o.maxDate) }) }(e, "MuiYearCalendar"),
                            { autoFocus: s, className: c, value: d, defaultValue: u, referenceDate: h, disabled: m, disableFuture: p, disablePast: f, maxDate: v, minDate: g, onChange: y, readOnly: w, shouldDisableYear: z, onYearFocus: x, hasFocus: A, onFocusedViewChange: k, yearsPerRow: S, timezone: M, gridLabelId: E } = n,
                            T = (0, o.default)(n, sa),
                            { value: H, handleValueChange: L, timezone: I } = tt({ name: "YearCalendar", timezone: M, value: d, defaultValue: u, onChange: y, valueManager: Q }),
                            j = le(I),
                            V = (0, Ur.A)(),
                            O = ae(),
                            R = r.useMemo((() => Q.getInitialReferenceValue({ value: H, utils: O, props: n, timezone: I, referenceDate: h, granularity: C.year })), []),
                            P = n,
                            D = (e => { const { classes: t } = e; return (0, he.A)({ root: ["root"] }, la, t) })(P),
                            F = r.useMemo((() => O.getYear(j)), [O, j]),
                            N = r.useMemo((() => null != H ? O.getYear(H) : null), [H, O]),
                            [_, B] = r.useState((() => N || O.getYear(R))),
                            [W, U] = (0, Je.A)({ name: "YearCalendar", state: "hasFocus", controlled: A, default: null !== s && void 0 !== s && s }),
                            q = (0, Pe.A)((e => { U(e), k && k(e) })),
                            G = r.useCallback((e => { if (f && O.isBeforeYear(e, j)) return !0; if (p && O.isAfterYear(e, j)) return !0; if (g && O.isBeforeYear(e, g)) return !0; if (v && O.isAfterYear(e, v)) return !0; if (!z) return !1; const t = O.startOfYear(e); return z(t) }), [p, f, v, g, j, z, O]),
                            K = (0, Pe.A)(((e, t) => { if (w) return; const n = O.setYear(null !== H && void 0 !== H ? H : R, t);
                                L(n) })),
                            Z = (0, Pe.A)((e => { G(O.setYear(null !== H && void 0 !== H ? H : R, e)) || (B(e), q(!0), null === x || void 0 === x || x(e)) }));
                        r.useEffect((() => { B((e => null !== N && e !== N ? N : e)) }), [N]); const Y = (0, Pe.A)(((e, t) => { switch (e.key) {
                                    case "ArrowUp":
                                        Z(t - S), e.preventDefault(); break;
                                    case "ArrowDown":
                                        Z(t + S), e.preventDefault(); break;
                                    case "ArrowLeft":
                                        Z(t + ("ltr" === V.direction ? -1 : 1)), e.preventDefault(); break;
                                    case "ArrowRight":
                                        Z(t + ("ltr" === V.direction ? 1 : -1)), e.preventDefault() } })),
                            X = (0, Pe.A)(((e, t) => { Z(t) })),
                            $ = (0, Pe.A)(((e, t) => { _ === t && q(!1) })),
                            J = r.useRef(null),
                            ee = (0, He.A)(t, J); return r.useEffect((() => { if (s || null === J.current) return; const e = J.current.querySelector('[tabindex="0"]'); if (!e) return; const t = e.offsetHeight,
                                n = e.offsetTop,
                                r = J.current.clientHeight,
                                a = J.current.scrollTop,
                                o = n + t;
                            t > r || n < a || (J.current.scrollTop = o - r / 2 - t / 2) }), [s]), (0, l.jsx)(ca, (0, a.default)({ ref: ee, className: ce(D.root, c), ownerState: P, role: "radiogroup", "aria-labelledby": E }, T, { children: O.getYearRange([g, v]).map((e => { const t = O.getYear(e),
                                    n = t === N,
                                    r = m || G(e); return (0, l.jsx)(ia, { selected: n, value: t, onClick: K, onKeyDown: Y, autoFocus: W && t === _, disabled: r, tabIndex: t === _ ? 0 : -1, onFocus: X, onBlur: $, "aria-current": F === t ? "date" : void 0, yearsPerRow: S, children: O.format(e, "year") }, O.format(e, "year")) })) })) }));

                function ua(e) { return (0, me.Ay)("MuiPickersArrowSwitcher", e) }(0, pe.A)("MuiPickersArrowSwitcher", ["root", "spacer", "button"]); const ha = ["children", "className", "slots", "slotProps", "isNextDisabled", "isNextHidden", "onGoToNext", "nextLabel", "isPreviousDisabled", "isPreviousHidden", "onGoToPrevious", "previousLabel"],
                    ma = ["ownerState"],
                    pa = ["ownerState"],
                    fa = (0, ue.Ay)("div", { name: "MuiPickersArrowSwitcher", slot: "Root", overridesResolver: (e, t) => t.root })({ display: "flex" }),
                    va = (0, ue.Ay)("div", { name: "MuiPickersArrowSwitcher", slot: "Spacer", overridesResolver: (e, t) => t.spacer })((e => { let { theme: t } = e; return { width: t.spacing(3) } })),
                    ga = (0, ue.Ay)(Te.A, { name: "MuiPickersArrowSwitcher", slot: "Button", overridesResolver: (e, t) => t.button })({ variants: [{ props: { hidden: !0 }, style: { visibility: "hidden" } }] }),
                    ya = r.forwardRef((function(e, t) { var n, r, s, c; const d = "rtl" === (0, Pt.A)().direction,
                            u = (0, i.A)({ props: e, name: "MuiPickersArrowSwitcher" }),
                            { children: h, className: m, slots: p, slotProps: f, isNextDisabled: v, isNextHidden: g, onGoToNext: y, nextLabel: b, isPreviousDisabled: w, isPreviousHidden: z, onGoToPrevious: x, previousLabel: A } = u,
                            k = (0, o.default)(u, ha),
                            S = u,
                            M = (e => { const { classes: t } = e; return (0, he.A)({ root: ["root"], spacer: ["spacer"], button: ["button"] }, ua, t) })(S),
                            E = { isDisabled: v, isHidden: g, goTo: y, label: b },
                            C = { isDisabled: w, isHidden: z, goTo: x, label: A },
                            T = null !== (n = null === p || void 0 === p ? void 0 : p.previousIconButton) && void 0 !== n ? n : ga,
                            H = (0, Ee.Q)({ elementType: T, externalSlotProps: null === f || void 0 === f ? void 0 : f.previousIconButton, additionalProps: { size: "medium", title: C.label, "aria-label": C.label, disabled: C.isDisabled, edge: "end", onClick: C.goTo }, ownerState: (0, a.default)({}, S, { hidden: C.isHidden }), className: M.button }),
                            L = null !== (r = null === p || void 0 === p ? void 0 : p.nextIconButton) && void 0 !== r ? r : ga,
                            I = (0, Ee.Q)({ elementType: L, externalSlotProps: null === f || void 0 === f ? void 0 : f.nextIconButton, additionalProps: { size: "medium", title: E.label, "aria-label": E.label, disabled: E.isDisabled, edge: "start", onClick: E.goTo }, ownerState: (0, a.default)({}, S, { hidden: E.isHidden }), className: M.button }),
                            j = null !== (s = null === p || void 0 === p ? void 0 : p.leftArrowIcon) && void 0 !== s ? s : It,
                            V = (0, Ee.Q)({ elementType: j, externalSlotProps: null === f || void 0 === f ? void 0 : f.leftArrowIcon, additionalProps: { fontSize: "inherit" }, ownerState: void 0 }),
                            O = (0, o.default)(V, ma),
                            R = null !== (c = null === p || void 0 === p ? void 0 : p.rightArrowIcon) && void 0 !== c ? c : jt,
                            P = (0, Ee.Q)({ elementType: R, externalSlotProps: null === f || void 0 === f ? void 0 : f.rightArrowIcon, additionalProps: { fontSize: "inherit" }, ownerState: void 0 }),
                            D = (0, o.default)(P, pa); return (0, l.jsxs)(fa, (0, a.default)({ ref: t, className: ce(M.root, m), ownerState: S }, k, { children: [(0, l.jsx)(T, (0, a.default)({}, H, { children: d ? (0, l.jsx)(R, (0, a.default)({}, D)) : (0, l.jsx)(j, (0, a.default)({}, O)) })), h ? (0, l.jsx)(de.A, { variant: "subtitle1", component: "span", children: h }) : (0, l.jsx)(va, { className: M.spacer, ownerState: S }), (0, l.jsx)(L, (0, a.default)({}, I, { children: d ? (0, l.jsx)(j, (0, a.default)({}, O)) : (0, l.jsx)(R, (0, a.default)({}, D)) }))] })) })); const ba = e => (0, me.Ay)("MuiPickersCalendarHeader", e),
                    wa = (0, pe.A)("MuiPickersCalendarHeader", ["root", "labelContainer", "label", "switchViewButton", "switchViewIcon"]),
                    za = ["slots", "slotProps", "currentMonth", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "onViewChange", "view", "reduceAnimations", "views", "labelId", "className", "timezone", "format"],
                    xa = ["ownerState"],
                    Aa = (0, ue.Ay)("div", { name: "MuiPickersCalendarHeader", slot: "Root", overridesResolver: (e, t) => t.root })({ display: "flex", alignItems: "center", marginTop: 12, marginBottom: 4, paddingLeft: 24, paddingRight: 12, maxHeight: 40, minHeight: 40 }),
                    ka = (0, ue.Ay)("div", { name: "MuiPickersCalendarHeader", slot: "LabelContainer", overridesResolver: (e, t) => t.labelContainer })((e => { let { theme: t } = e; return (0, a.default)({ display: "flex", overflow: "hidden", alignItems: "center", cursor: "pointer", marginRight: "auto" }, t.typography.body1, { fontWeight: t.typography.fontWeightMedium }) })),
                    Sa = (0, ue.Ay)("div", { name: "MuiPickersCalendarHeader", slot: "Label", overridesResolver: (e, t) => t.label })({ marginRight: 6 }),
                    Ma = (0, ue.Ay)(Te.A, { name: "MuiPickersCalendarHeader", slot: "SwitchViewButton", overridesResolver: (e, t) => t.switchViewButton })({ marginRight: "auto", variants: [{ props: { view: "year" }, style: {
                                [".".concat(wa.switchViewIcon)]: { transform: "rotate(180deg)" } } }] }),
                    Ea = (0, ue.Ay)(Lt, { name: "MuiPickersCalendarHeader", slot: "SwitchViewIcon", overridesResolver: (e, t) => t.switchViewIcon })((e => { let { theme: t } = e; return { willChange: "transform", transition: t.transitions.create("transform"), transform: "rotate(0deg)" } })),
                    Ca = r.forwardRef((function(e, t) { var n, s; const c = ie(),
                            d = ae(),
                            u = (0, i.A)({ props: e, name: "MuiPickersCalendarHeader" }),
                            { slots: h, slotProps: m, currentMonth: p, disabled: f, disableFuture: v, disablePast: g, maxDate: y, minDate: b, onMonthChange: w, onViewChange: z, view: x, reduceAnimations: A, views: k, labelId: S, className: M, timezone: E, format: C = "".concat(d.formats.month, " ").concat(d.formats.year) } = u,
                            T = (0, o.default)(u, za),
                            H = u,
                            L = (e => { const { classes: t } = e; return (0, he.A)({ root: ["root"], labelContainer: ["labelContainer"], label: ["label"], switchViewButton: ["switchViewButton"], switchViewIcon: ["switchViewIcon"] }, ba, t) })(u),
                            I = null !== (n = null === h || void 0 === h ? void 0 : h.switchViewButton) && void 0 !== n ? n : Ma,
                            j = (0, Ee.Q)({ elementType: I, externalSlotProps: null === m || void 0 === m ? void 0 : m.switchViewButton, additionalProps: { size: "small", "aria-label": c.calendarViewSwitchingButtonAriaLabel(x) }, ownerState: H, className: L.switchViewButton }),
                            V = null !== (s = null === h || void 0 === h ? void 0 : h.switchViewIcon) && void 0 !== s ? s : Ea,
                            O = (0, Ee.Q)({ elementType: V, externalSlotProps: null === m || void 0 === m ? void 0 : m.switchViewIcon, ownerState: void 0, className: L.switchViewIcon }),
                            R = (0, o.default)(O, xa),
                            P = function(e, t) { let { disableFuture: n, maxDate: a, timezone: o } = t; const i = ae(); return r.useMemo((() => { const t = i.date(void 0, o),
                                        r = i.startOfMonth(n && i.isBefore(t, a) ? t : a); return !i.isAfter(r, e) }), [n, a, e, i, o]) }(p, { disableFuture: v, maxDate: y, timezone: E }),
                            D = function(e, t) { let { disablePast: n, minDate: a, timezone: o } = t; const i = ae(); return r.useMemo((() => { const t = i.date(void 0, o),
                                        r = i.startOfMonth(n && i.isAfter(t, a) ? t : a); return !i.isBefore(r, e) }), [n, a, e, i, o]) }(p, { disablePast: g, minDate: b, timezone: E }); if (1 === k.length && "year" === k[0]) return null; const F = d.formatByString(p, C); return (0, l.jsxs)(Aa, (0, a.default)({}, T, { ownerState: H, className: ce(M, L.root), ref: t, children: [(0, l.jsxs)(ka, { role: "presentation", onClick: () => { if (1 !== k.length && z && !f)
                                        if (2 === k.length) z(k.find((e => e !== x)) || k[0]);
                                        else { const e = 0 !== k.indexOf(x) ? 0 : 1;
                                            z(k[e]) } }, ownerState: H, "aria-live": "polite", className: L.labelContainer, children: [(0, l.jsx)(hr, { reduceAnimations: A, transKey: F, children: (0, l.jsx)(Sa, { id: S, ownerState: H, className: L.label, children: F }) }), k.length > 1 && !f && (0, l.jsx)(I, (0, a.default)({}, j, { children: (0, l.jsx)(V, (0, a.default)({}, R)) }))] }), (0, l.jsx)(je.A, { in: "day" === x, children: (0, l.jsx)(ya, { slots: h, slotProps: m, onGoToPrevious: () => w(d.addMonths(p, -1), "right"), isPreviousDisabled: D, previousLabel: c.previousMonth, onGoToNext: () => w(d.addMonths(p, 1), "left"), isNextDisabled: P, nextLabel: c.nextMonth }) })] })) })),
                    Ta = (0, ue.Ay)("div")({ overflow: "hidden", width: bt, maxHeight: 336, display: "flex", flexDirection: "column", margin: "0 auto" }),
                    Ha = e => (0, me.Ay)("MuiDateCalendar", e),
                    La = ((0, pe.A)("MuiDateCalendar", ["root", "viewTransitionContainer"]), ["autoFocus", "onViewChange", "value", "defaultValue", "referenceDate", "disableFuture", "disablePast", "onChange", "onYearChange", "onMonthChange", "reduceAnimations", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "view", "views", "openTo", "className", "disabled", "readOnly", "minDate", "maxDate", "disableHighlightToday", "focusedView", "onFocusedViewChange", "showDaysOutsideCurrentMonth", "fixedWeekNumber", "dayOfWeekFormatter", "slots", "slotProps", "loading", "renderLoading", "displayWeekNumber", "yearsPerRow", "monthsPerRow", "timezone"]); const Ia = (0, ue.Ay)(Ta, { name: "MuiDateCalendar", slot: "Root", overridesResolver: (e, t) => t.root })({ display: "flex", flexDirection: "column", height: 336 }),
                    ja = (0, ue.Ay)(hr, { name: "MuiDateCalendar", slot: "ViewTransitionContainer", overridesResolver: (e, t) => t.viewTransitionContainer })({}),
                    Va = r.forwardRef((function(e, t) { var n; const s = ae(),
                            c = (0, Le.A)(),
                            d = function(e, t) { var n, r, o, s, c, d, u; const h = ae(),
                                    m = oe(),
                                    p = Ge(),
                                    f = (0, i.A)({ props: e, name: t }); return (0, a.default)({}, f, { loading: null !== (n = f.loading) && void 0 !== n && n, disablePast: null !== (r = f.disablePast) && void 0 !== r && r, disableFuture: null !== (o = f.disableFuture) && void 0 !== o && o, openTo: null !== (s = f.openTo) && void 0 !== s ? s : "day", views: null !== (c = f.views) && void 0 !== c ? c : ["year", "day"], reduceAnimations: null !== (d = f.reduceAnimations) && void 0 !== d ? d : p, renderLoading: null !== (u = f.renderLoading) && void 0 !== u ? u : () => (0, l.jsx)("span", { children: "..." }), minDate: b(h, f.minDate, m.minDate), maxDate: b(h, f.maxDate, m.maxDate) }) }(e, "MuiDateCalendar"),
                            { autoFocus: u, onViewChange: h, value: m, defaultValue: p, referenceDate: f, disableFuture: v, disablePast: w, onChange: z, onYearChange: x, onMonthChange: A, reduceAnimations: k, shouldDisableDate: S, shouldDisableMonth: M, shouldDisableYear: E, view: C, views: T, openTo: H, className: L, disabled: I, readOnly: j, minDate: V, maxDate: O, disableHighlightToday: R, focusedView: P, onFocusedViewChange: D, showDaysOutsideCurrentMonth: F, fixedWeekNumber: N, dayOfWeekFormatter: _, slots: B, slotProps: W, loading: U, renderLoading: q, displayWeekNumber: G, yearsPerRow: K, monthsPerRow: Z, timezone: Y } = d,
                            X = (0, o.default)(d, La),
                            { value: $, handleValueChange: J, timezone: ee } = tt({ name: "DateCalendar", timezone: Y, value: m, defaultValue: p, onChange: z, valueManager: Q }),
                            { view: te, setView: ne, focusedView: re, setFocusedView: ie, goToNextView: le, setValueAndGoToNextView: se } = at({ view: C, views: T, openTo: H, onChange: J, onViewChange: h, autoFocus: u, focusedView: P, onFocusedViewChange: D }),
                            { referenceDate: de, calendarState: ue, changeFocusedDay: me, changeMonth: pe, handleChangeMonth: fe, isDateDisabled: ve, onMonthSwitchingAnimationEnd: ge } = sr({ value: $, referenceDate: f, reduceAnimations: k, onMonthChange: A, minDate: V, maxDate: O, shouldDisableDate: S, disablePast: w, disableFuture: v, timezone: ee }),
                            ye = I && $ || V,
                            be = I && $ || O,
                            we = "".concat(c, "-grid-label"),
                            ze = null !== re,
                            xe = null !== (n = null === B || void 0 === B ? void 0 : B.calendarHeader) && void 0 !== n ? n : Ca,
                            Ae = (0, Ee.Q)({ elementType: xe, externalSlotProps: null === W || void 0 === W ? void 0 : W.calendarHeader, additionalProps: { views: T, view: te, currentMonth: ue.currentMonth, onViewChange: ne, onMonthChange: (e, t) => fe({ newMonth: e, direction: t }), minDate: ye, maxDate: be, disabled: I, disablePast: w, disableFuture: v, reduceAnimations: k, timezone: ee, labelId: we, slots: B, slotProps: W }, ownerState: d }),
                            ke = (0, Pe.A)((e => { const t = s.startOfMonth(e),
                                    n = s.endOfMonth(e),
                                    r = ve(e) ? y({ utils: s, date: e, minDate: s.isBefore(V, t) ? t : V, maxDate: s.isAfter(O, n) ? n : O, disablePast: w, disableFuture: v, isDateDisabled: ve, timezone: ee }) : e;
                                r ? (se(r, "finish"), null === A || void 0 === A || A(t)) : (le(), pe(t)), me(r, !0) })),
                            Se = (0, Pe.A)((e => { const t = s.startOfYear(e),
                                    n = s.endOfYear(e),
                                    r = ve(e) ? y({ utils: s, date: e, minDate: s.isBefore(V, t) ? t : V, maxDate: s.isAfter(O, n) ? n : O, disablePast: w, disableFuture: v, isDateDisabled: ve, timezone: ee }) : e;
                                r ? (se(r, "finish"), null === x || void 0 === x || x(r)) : (le(), pe(t)), me(r, !0) })),
                            Me = (0, Pe.A)((e => J(e ? g(s, e, null !== $ && void 0 !== $ ? $ : de) : e, "finish", te)));
                        r.useEffect((() => { null != $ && s.isValid($) && pe($) }), [$]); const Ce = d,
                            Te = (e => { const { classes: t } = e; return (0, he.A)({ root: ["root"], viewTransitionContainer: ["viewTransitionContainer"] }, Ha, t) })(Ce),
                            He = { disablePast: w, disableFuture: v, maxDate: O, minDate: V },
                            Ie = { disableHighlightToday: R, readOnly: j, disabled: I, timezone: ee, gridLabelId: we },
                            je = r.useRef(te);
                        r.useEffect((() => { je.current !== te && (re === je.current && ie(te, !0), je.current = te) }), [re, ie, te]); const Ve = r.useMemo((() => [$]), [$]); return (0, l.jsxs)(Ia, (0, a.default)({ ref: t, className: ce(Te.root, L), ownerState: Ce }, X, { children: [(0, l.jsx)(xe, (0, a.default)({}, Ae)), (0, l.jsx)(ja, { reduceAnimations: k, className: Te.viewTransitionContainer, transKey: te, ownerState: Ce, children: (0, l.jsxs)("div", { children: ["year" === te && (0, l.jsx)(da, (0, a.default)({}, He, Ie, { value: $, onChange: Se, shouldDisableYear: E, hasFocus: ze, onFocusedViewChange: e => ie("year", e), yearsPerRow: K, referenceDate: de })), "month" === te && (0, l.jsx)(ea, (0, a.default)({}, He, Ie, { hasFocus: ze, className: L, value: $, onChange: ke, shouldDisableMonth: M, onFocusedViewChange: e => ie("month", e), monthsPerRow: Z, referenceDate: de })), "day" === te && (0, l.jsx)(Wr, (0, a.default)({}, ue, He, Ie, { onMonthSwitchingAnimationEnd: ge, onFocusedDayChange: me, reduceAnimations: k, selectedDays: Ve, onSelectedDaysChange: Me, shouldDisableDate: S, shouldDisableMonth: M, shouldDisableYear: E, hasFocus: ze, onFocusedViewChange: e => ie("day", e), showDaysOutsideCurrentMonth: F, fixedWeekNumber: N, dayOfWeekFormatter: _, displayWeekNumber: G, slots: B, slotProps: W, loading: U, renderLoading: q }))] }) })] })) })),
                    Oa = e => { let { view: t, onViewChange: n, views: r, focusedView: a, onFocusedViewChange: o, value: i, defaultValue: s, referenceDate: c, onChange: d, className: u, classes: h, disableFuture: m, disablePast: p, minDate: f, maxDate: v, shouldDisableDate: g, shouldDisableMonth: y, shouldDisableYear: b, reduceAnimations: w, onMonthChange: z, monthsPerRow: x, onYearChange: k, yearsPerRow: S, slots: M, slotProps: E, loading: C, renderLoading: T, disableHighlightToday: H, readOnly: L, disabled: I, showDaysOutsideCurrentMonth: j, dayOfWeekFormatter: V, sx: O, autoFocus: R, fixedWeekNumber: P, displayWeekNumber: D, timezone: F } = e; return (0, l.jsx)(Va, { view: t, onViewChange: n, views: r.filter(A), focusedView: a && A(a) ? a : null, onFocusedViewChange: o, value: i, defaultValue: s, referenceDate: c, onChange: d, className: u, classes: h, disableFuture: m, disablePast: p, minDate: f, maxDate: v, shouldDisableDate: g, shouldDisableMonth: y, shouldDisableYear: b, reduceAnimations: w, onMonthChange: z, monthsPerRow: x, onYearChange: k, yearsPerRow: S, slots: M, slotProps: E, loading: C, renderLoading: T, disableHighlightToday: H, readOnly: L, disabled: I, showDaysOutsideCurrentMonth: j, dayOfWeekFormatter: V, sx: O, autoFocus: R, fixedWeekNumber: P, displayWeekNumber: D, timezone: F }) },
                    Ra = r.forwardRef((function(e, t) { var n, i, s, c; const u = ie(),
                            h = ae(),
                            m = Se(e, "MuiDesktopDatePicker"),
                            f = (0, a.default)({ day: Oa, month: Oa, year: Oa }, m.viewRenderers),
                            v = (0, a.default)({}, m, { viewRenderers: f, format: k(h, m, !1), yearsPerRow: null !== (n = m.yearsPerRow) && void 0 !== n ? n : 4, slots: (0, a.default)({ openPickerIcon: Vt, field: ir }, m.slots), slotProps: (0, a.default)({}, m.slotProps, { field: e => { var n; return (0, a.default)({}, (0, p.Y)(null === (n = m.slotProps) || void 0 === n ? void 0 : n.field, e), $t(m), { ref: t }) }, toolbar: (0, a.default)({ hidden: !0 }, null === (i = m.slotProps) || void 0 === i ? void 0 : i.toolbar) }) }),
                            { renderPicker: g } = (e => { var t, n, i, s, c; let { props: u, getOpenDialogAriaText: h } = e, m = (0, o.default)(e, Et); const { slots: p, slotProps: f, className: v, sx: g, format: y, formatDensity: b, enableAccessibleFieldDOMStructure: w, selectedSections: z, onSelectedSectionsChange: x, timezone: A, name: k, label: S, inputRef: M, readOnly: E, disabled: C, autoFocus: T, localeText: H, reduceAnimations: L } = u, I = ae(), j = r.useRef(null), V = r.useRef(null), O = (0, Le.A)(), R = null !== (t = null === f || void 0 === f || null === (n = f.toolbar) || void 0 === n ? void 0 : n.hidden) && void 0 !== t && t, { open: P, actions: D, hasUIView: F, layoutProps: N, renderCurrentView: _, shouldRestoreFocus: B, fieldProps: W } = ct((0, a.default)({}, m, { props: u, fieldRef: V, autoFocusView: !0, additionalViewProps: {}, wrapperVariant: "desktop" })), U = null !== (i = p.inputAdornment) && void 0 !== i ? i : Ce.A, q = (0, Ee.Q)({ elementType: U, externalSlotProps: null === f || void 0 === f ? void 0 : f.inputAdornment, additionalProps: { position: "end" }, ownerState: u }), G = (0, o.default)(q, Ct), K = null !== (s = p.openPickerButton) && void 0 !== s ? s : Te.A, Z = (0, Ee.Q)({ elementType: K, externalSlotProps: null === f || void 0 === f ? void 0 : f.openPickerButton, additionalProps: { disabled: C || E, onClick: P ? D.onClose : D.onOpen, "aria-label": h(W.value, I), edge: G.position }, ownerState: u }), Y = (0, o.default)(Z, Tt), X = p.openPickerIcon, $ = p.field, Q = (0, Ee.Q)({ elementType: $, externalSlotProps: null === f || void 0 === f ? void 0 : f.field, additionalProps: (0, a.default)({}, W, R && { id: O }, { readOnly: E, disabled: C, className: v, sx: g, format: y, formatDensity: b, enableAccessibleFieldDOMStructure: w, selectedSections: z, onSelectedSectionsChange: x, timezone: A, label: S, name: k, autoFocus: T && !u.open, focused: !!P || void 0 }, M ? { inputRef: M } : {}), ownerState: u });
                                F && (Q.InputProps = (0, a.default)({}, Q.InputProps, { ref: j, ["".concat(G.position, "Adornment")]: (0, l.jsx)(U, (0, a.default)({}, G, { children: (0, l.jsx)(K, (0, a.default)({}, Y, { children: (0, l.jsx)(X, (0, a.default)({}, null === f || void 0 === f ? void 0 : f.openPickerIcon)) })) })) })); const J = (0, a.default)({ textField: p.textField, clearIcon: p.clearIcon, clearButton: p.clearButton }, Q.slots),
                                    ee = null !== (c = p.layout) && void 0 !== c ? c : Mt; let te = O;
                                R && (te = S ? "".concat(O, "-label") : void 0); const ne = (0, a.default)({}, f, { toolbar: (0, a.default)({}, null === f || void 0 === f ? void 0 : f.toolbar, { titleId: O }), popper: (0, a.default)({ "aria-labelledby": te }, null === f || void 0 === f ? void 0 : f.popper) }),
                                    re = (0, He.A)(V, Q.unstableFieldRef); return { renderPicker: () => (0, l.jsxs)(d, { localeText: H, children: [(0, l.jsx)($, (0, a.default)({}, Q, { slots: J, slotProps: ne, unstableFieldRef: re })), (0, l.jsx)($e, (0, a.default)({ role: "dialog", placement: "bottom-start", anchorEl: j.current }, D, { open: P, slots: p, slotProps: ne, shouldRestoreFocus: B, reduceAnimations: L, children: (0, l.jsx)(ee, (0, a.default)({}, N, null === ne || void 0 === ne ? void 0 : ne.layout, { slots: p, slotProps: ne, children: _() })) }))] }) } })({ props: v, valueManager: Q, valueType: "date", getOpenDialogAriaText: null !== (s = null === (c = v.localeText) || void 0 === c ? void 0 : c.openDatePickerDialogue) && void 0 !== s ? s : u.openDatePickerDialogue, validator: Me }); return g() }));
                Ra.propTypes = { autoFocus: m().bool, className: m().string, closeOnSelect: m().bool, dayOfWeekFormatter: m().func, defaultValue: m().object, disabled: m().bool, disableFuture: m().bool, disableHighlightToday: m().bool, disableOpenPicker: m().bool, disablePast: m().bool, displayWeekNumber: m().bool, enableAccessibleFieldDOMStructure: m().any, fixedWeekNumber: m().number, format: m().string, formatDensity: m().oneOf(["dense", "spacious"]), inputRef: f, label: m().node, loading: m().bool, localeText: m().object, maxDate: m().object, minDate: m().object, monthsPerRow: m().oneOf([3, 4]), name: m().string, onAccept: m().func, onChange: m().func, onClose: m().func, onError: m().func, onMonthChange: m().func, onOpen: m().func, onSelectedSectionsChange: m().func, onViewChange: m().func, onYearChange: m().func, open: m().bool, openTo: m().oneOf(["day", "month", "year"]), orientation: m().oneOf(["landscape", "portrait"]), readOnly: m().bool, reduceAnimations: m().bool, referenceDate: m().object, renderLoading: m().func, selectedSections: m().oneOfType([m().oneOf(["all", "day", "empty", "hours", "meridiem", "minutes", "month", "seconds", "weekDay", "year"]), m().number]), shouldDisableDate: m().func, shouldDisableMonth: m().func, shouldDisableYear: m().func, showDaysOutsideCurrentMonth: m().bool, slotProps: m().object, slots: m().object, sx: m().oneOfType([m().arrayOf(m().oneOfType([m().func, m().object, m().bool])), m().func, m().object]), timezone: m().string, value: m().object, view: m().oneOf(["day", "month", "year"]), viewRenderers: m().shape({ day: m().func, month: m().func, year: m().func }), views: m().arrayOf(m().oneOf(["day", "month", "year"]).isRequired), yearsPerRow: m().oneOf([3, 4]) }; var Pa = n(35316),
                    Da = n(83462),
                    Fa = n(93436); const Na = (0, ue.Ay)(Da.A)({
                        ["& .".concat(Fa.A.container)]: { outline: 0 }, ["& .".concat(Fa.A.paper)]: { outline: 0, minWidth: bt } }),
                    _a = (0, ue.Ay)(Pa.A)({ "&:first-of-type": { padding: 0 } });

                function Ba(e) { var t, n; const { children: r, onDismiss: o, open: i, slots: s, slotProps: c } = e, d = null !== (t = null === s || void 0 === s ? void 0 : s.dialog) && void 0 !== t ? t : Na, u = null !== (n = null === s || void 0 === s ? void 0 : s.mobileTransition) && void 0 !== n ? n : je.A; return (0, l.jsx)(d, (0, a.default)({ open: i, onClose: o }, null === c || void 0 === c ? void 0 : c.dialog, { TransitionComponent: u, TransitionProps: null === c || void 0 === c ? void 0 : c.mobileTransition, PaperComponent: null === s || void 0 === s ? void 0 : s.mobilePaper, PaperProps: null === c || void 0 === c ? void 0 : c.mobilePaper, children: (0, l.jsx)(_a, { children: r }) })) } const Wa = ["props", "getOpenDialogAriaText"],
                    Ua = r.forwardRef((function(e, t) { var n, i, s; const c = ie(),
                            u = ae(),
                            h = Se(e, "MuiMobileDatePicker"),
                            m = (0, a.default)({ day: Oa, month: Oa, year: Oa }, h.viewRenderers),
                            f = (0, a.default)({}, h, { viewRenderers: m, format: k(u, h, !1), slots: (0, a.default)({ field: ir }, h.slots), slotProps: (0, a.default)({}, h.slotProps, { field: e => { var n; return (0, a.default)({}, (0, p.Y)(null === (n = h.slotProps) || void 0 === n ? void 0 : n.field, e), $t(h), { ref: t }) }, toolbar: (0, a.default)({ hidden: !1 }, null === (n = h.slotProps) || void 0 === n ? void 0 : n.toolbar) }) }),
                            { renderPicker: v } = (e => { var t, n, i; let { props: s, getOpenDialogAriaText: c } = e, u = (0, o.default)(e, Wa); const { slots: h, slotProps: m, className: p, sx: f, format: v, formatDensity: g, enableAccessibleFieldDOMStructure: y, selectedSections: b, onSelectedSectionsChange: w, timezone: z, name: x, label: A, inputRef: k, readOnly: S, disabled: M, localeText: E } = s, C = ae(), T = r.useRef(null), H = (0, Le.A)(), L = null !== (t = null === m || void 0 === m || null === (n = m.toolbar) || void 0 === n ? void 0 : n.hidden) && void 0 !== t && t, { open: I, actions: j, layoutProps: V, renderCurrentView: O, fieldProps: R } = ct((0, a.default)({}, u, { props: s, fieldRef: T, autoFocusView: !0, additionalViewProps: {}, wrapperVariant: "mobile" })), P = h.field, D = (0, Ee.Q)({ elementType: P, externalSlotProps: null === m || void 0 === m ? void 0 : m.field, additionalProps: (0, a.default)({}, R, L && { id: H }, !(M || S) && { onClick: j.onOpen, onKeyDown: (F = j.onOpen, e => { "Enter" !== e.key && " " !== e.key || (F(e), e.preventDefault(), e.stopPropagation()), N && N(e) }) }, { readOnly: null === S || void 0 === S || S, disabled: M, className: p, sx: f, format: v, formatDensity: g, enableAccessibleFieldDOMStructure: y, selectedSections: b, onSelectedSectionsChange: w, timezone: z, label: A, name: x }, k ? { inputRef: k } : {}), ownerState: s }); var F, N;
                                D.inputProps = (0, a.default)({}, D.inputProps, { "aria-label": c(R.value, C) }); const _ = (0, a.default)({ textField: h.textField }, D.slots),
                                    B = null !== (i = h.layout) && void 0 !== i ? i : Mt; let W = H;
                                L && (W = A ? "".concat(H, "-label") : void 0); const U = (0, a.default)({}, m, { toolbar: (0, a.default)({}, null === m || void 0 === m ? void 0 : m.toolbar, { titleId: H }), mobilePaper: (0, a.default)({ "aria-labelledby": W }, null === m || void 0 === m ? void 0 : m.mobilePaper) }),
                                    q = (0, He.A)(T, D.unstableFieldRef); return { renderPicker: () => (0, l.jsxs)(d, { localeText: E, children: [(0, l.jsx)(P, (0, a.default)({}, D, { slots: _, slotProps: U, unstableFieldRef: q })), (0, l.jsx)(Ba, (0, a.default)({}, j, { open: I, slots: h, slotProps: U, children: (0, l.jsx)(B, (0, a.default)({}, V, null === U || void 0 === U ? void 0 : U.layout, { slots: h, slotProps: U, children: O() })) }))] }) } })({ props: f, valueManager: Q, valueType: "date", getOpenDialogAriaText: null !== (i = null === (s = f.localeText) || void 0 === s ? void 0 : s.openDatePickerDialogue) && void 0 !== i ? i : c.openDatePickerDialogue, validator: Me }); return v() }));
                Ua.propTypes = { autoFocus: m().bool, className: m().string, closeOnSelect: m().bool, dayOfWeekFormatter: m().func, defaultValue: m().object, disabled: m().bool, disableFuture: m().bool, disableHighlightToday: m().bool, disableOpenPicker: m().bool, disablePast: m().bool, displayWeekNumber: m().bool, enableAccessibleFieldDOMStructure: m().any, fixedWeekNumber: m().number, format: m().string, formatDensity: m().oneOf(["dense", "spacious"]), inputRef: f, label: m().node, loading: m().bool, localeText: m().object, maxDate: m().object, minDate: m().object, monthsPerRow: m().oneOf([3, 4]), name: m().string, onAccept: m().func, onChange: m().func, onClose: m().func, onError: m().func, onMonthChange: m().func, onOpen: m().func, onSelectedSectionsChange: m().func, onViewChange: m().func, onYearChange: m().func, open: m().bool, openTo: m().oneOf(["day", "month", "year"]), orientation: m().oneOf(["landscape", "portrait"]), readOnly: m().bool, reduceAnimations: m().bool, referenceDate: m().object, renderLoading: m().func, selectedSections: m().oneOfType([m().oneOf(["all", "day", "empty", "hours", "meridiem", "minutes", "month", "seconds", "weekDay", "year"]), m().number]), shouldDisableDate: m().func, shouldDisableMonth: m().func, shouldDisableYear: m().func, showDaysOutsideCurrentMonth: m().bool, slotProps: m().object, slots: m().object, sx: m().oneOfType([m().arrayOf(m().oneOfType([m().func, m().object, m().bool])), m().func, m().object]), timezone: m().string, value: m().object, view: m().oneOf(["day", "month", "year"]), viewRenderers: m().shape({ day: m().func, month: m().func, year: m().func }), views: m().arrayOf(m().oneOf(["day", "month", "year"]).isRequired), yearsPerRow: m().oneOf([3, 4]) }; const qa = ["desktopModeMediaQuery"],
                    Ga = r.forwardRef((function(e, t) { const n = (0, i.A)({ props: e, name: "MuiDatePicker" }),
                            { desktopModeMediaQuery: r = _e } = n,
                            s = (0, o.default)(n, qa); return (0, u.A)(r, { defaultMatches: !0 }) ? (0, l.jsx)(Ra, (0, a.default)({ ref: t }, s)) : (0, l.jsx)(Ua, (0, a.default)({ ref: t }, s)) })); var Ka = n(75156),
                    Za = n(24241); const Ya = { y: { sectionType: "year", contentType: "digit", maxLength: 4 }, yy: "year", yyyy: { sectionType: "year", contentType: "digit", maxLength: 4 }, L: { sectionType: "month", contentType: "digit", maxLength: 2 }, LL: "month", LLL: { sectionType: "month", contentType: "letter" }, LLLL: { sectionType: "month", contentType: "letter" }, M: { sectionType: "month", contentType: "digit", maxLength: 2 }, MM: "month", MMM: { sectionType: "month", contentType: "letter" }, MMMM: { sectionType: "month", contentType: "letter" }, d: { sectionType: "day", contentType: "digit", maxLength: 2 }, dd: "day", c: { sectionType: "weekDay", contentType: "digit", maxLength: 1 }, ccc: { sectionType: "weekDay", contentType: "letter" }, cccc: { sectionType: "weekDay", contentType: "letter" }, E: { sectionType: "weekDay", contentType: "digit", maxLength: 2 }, EEE: { sectionType: "weekDay", contentType: "letter" }, EEEE: { sectionType: "weekDay", contentType: "letter" }, a: "meridiem", H: { sectionType: "hours", contentType: "digit", maxLength: 2 }, HH: "hours", h: { sectionType: "hours", contentType: "digit", maxLength: 2 }, hh: "hours", m: { sectionType: "minutes", contentType: "digit", maxLength: 2 }, mm: "minutes", s: { sectionType: "seconds", contentType: "digit", maxLength: 2 }, ss: "seconds" },
                    Xa = { year: "yyyy", month: "LLLL", monthShort: "MMM", dayOfMonth: "d", dayOfMonthFull: "d", weekday: "cccc", weekdayShort: "ccccc", hours24h: "HH", hours12h: "hh", meridiem: "a", minutes: "mm", seconds: "ss", fullDate: "DD", keyboardDate: "D", shortDate: "MMM d", normalDate: "d MMMM", normalDateWithWeekday: "EEE, MMM d", fullTime: "t", fullTime12h: "hh:mm a", fullTime24h: "HH:mm", keyboardDateTime: "D t", keyboardDateTime12h: "D hh:mm a", keyboardDateTime24h: "D T" };
                class $a { constructor() { var e = this; let { locale: t, formats: n } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                        this.isMUIAdapter = !0, this.isTimezoneCompatible = !0, this.lib = "luxon", this.locale = void 0, this.formats = void 0, this.escapedCharacters = { start: "'", end: "'" }, this.formatTokenMap = Ya, this.setLocaleToValue = e => { const t = this.getCurrentLocaleCode(); return t === e.locale ? e : e.setLocale(t) }, this.date = function(t) { let n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "default"; return null === t ? null : "undefined" === typeof t ? Za.c9.fromJSDate(new Date, { locale: e.locale, zone: n }) : Za.c9.fromISO(t, { locale: e.locale, zone: n }) }, this.getInvalidDate = () => Za.c9.fromJSDate(new Date("Invalid Date")), this.getTimezone = e => "system" === e.zone.type ? "system" : e.zoneName, this.setTimezone = (e, t) => e.zone.equals(Za.R2.normalizeZone(t)) ? e : e.setZone(t), this.toJsDate = e => e.toJSDate(), this.parse = (e, t) => "" === e ? null : Za.c9.fromFormat(e, t, { locale: this.locale }), this.getCurrentLocaleCode = () => this.locale, this.is12HourCycleInCurrentLocale = () => { var e; return "undefined" === typeof Intl || "undefined" === typeof Intl.DateTimeFormat || Boolean(null === (e = new Intl.DateTimeFormat(this.locale, { hour: "numeric" })) || void 0 === e || null === (e = e.resolvedOptions()) || void 0 === e ? void 0 : e.hour12) }, this.expandFormat = e => { const t = [...Object.keys(this.formatTokenMap), "yyyyy"],
                                n = new RegExp("^(".concat(t.join("|"), ")+$")),
                                r = /(?:^|[^a-z])([a-z]+)(?:[^a-z]|$)|([a-z]+)/gi; return e.match(/''|'(''|[^'])+('|$)|[^']*/g).map((e => { if ("'" === e[0]) return e; return Za.c9.expandFormat(e, { locale: this.locale }).replace(r, ((e, t, r) => { const a = t || r; return n.test(a) ? e : "'".concat(e, "'") })) })).join("").replace("yyyyy", "yyyy") }, this.isValid = e => null !== e && e.isValid, this.format = (e, t) => this.formatByString(e, this.formats[t]), this.formatByString = (e, t) => e.setLocale(this.locale).toFormat(t), this.formatNumber = e => e, this.isEqual = (e, t) => null === e && null === t || null !== e && null !== t && +e === +t, this.isSameYear = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.hasSame(n, "year") }, this.isSameMonth = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.hasSame(n, "month") }, this.isSameDay = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.hasSame(n, "day") }, this.isSameHour = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.hasSame(n, "hour") }, this.isAfter = (e, t) => e > t, this.isAfterYear = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.diff(this.endOfYear(n), "years").toObject().years > 0 }, this.isAfterDay = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.diff(this.endOfDay(n), "days").toObject().days > 0 }, this.isBefore = (e, t) => e < t, this.isBeforeYear = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.diff(this.startOfYear(n), "years").toObject().years < 0 }, this.isBeforeDay = (e, t) => { const n = this.setTimezone(t, this.getTimezone(e)); return e.diff(this.startOfDay(n), "days").toObject().days < 0 }, this.isWithinRange = (e, t) => { let [n, r] = t; return this.isEqual(e, n) || this.isEqual(e, r) || this.isAfter(e, n) && this.isBefore(e, r) }, this.startOfYear = e => e.startOf("year"), this.startOfMonth = e => e.startOf("month"), this.startOfWeek = e => e.startOf("week", { useLocaleWeeks: !0 }), this.startOfDay = e => e.startOf("day"), this.endOfYear = e => e.endOf("year"), this.endOfMonth = e => e.endOf("month"), this.endOfWeek = e => e.endOf("week", { useLocaleWeeks: !0 }), this.endOfDay = e => e.endOf("day"), this.addYears = (e, t) => e.plus({ years: t }), this.addMonths = (e, t) => e.plus({ months: t }), this.addWeeks = (e, t) => e.plus({ weeks: t }), this.addDays = (e, t) => e.plus({ days: t }), this.addHours = (e, t) => e.plus({ hours: t }), this.addMinutes = (e, t) => e.plus({ minutes: t }), this.addSeconds = (e, t) => e.plus({ seconds: t }), this.getYear = e => e.get("year"), this.getMonth = e => e.get("month") - 1, this.getDate = e => e.get("day"), this.getHours = e => e.get("hour"), this.getMinutes = e => e.get("minute"), this.getSeconds = e => e.get("second"), this.getMilliseconds = e => e.get("millisecond"), this.setYear = (e, t) => e.set({ year: t }), this.setMonth = (e, t) => e.set({ month: t + 1 }), this.setDate = (e, t) => e.set({ day: t }), this.setHours = (e, t) => e.set({ hour: t }), this.setMinutes = (e, t) => e.set({ minute: t }), this.setSeconds = (e, t) => e.set({ second: t }), this.setMilliseconds = (e, t) => e.set({ millisecond: t }), this.getDaysInMonth = e => e.daysInMonth, this.getWeekArray = e => { const t = this.setLocaleToValue(e),
                                n = this.startOfWeek(this.startOfMonth(t)),
                                r = this.endOfWeek(this.endOfMonth(t)),
                                { days: a } = r.diff(n, "days").toObject(),
                                o = []; return new Array(Math.round(a)).fill(0).map(((e, t) => t)).map((e => n.plus({ days: e }))).forEach(((e, t) => { 0 === t || t % 7 === 0 && t > 6 ? o.push([e]) : o[o.length - 1].push(e) })), o }, this.getWeekNumber = e => { var t; return null !== (t = e.localWeekNumber) && void 0 !== t ? t : e.weekNumber }, this.getDayOfWeek = e => e.weekday, this.getYearRange = e => { let [t, n] = e; const r = this.startOfYear(t),
                                a = this.endOfYear(n),
                                o = []; let i = r; for (; this.isBefore(i, a);) o.push(i), i = this.addYears(i, 1); return o }, this.locale = t || "en-US", this.formats = (0, a.default)({}, Xa, n) } } const Qa = e => { let { onChange: t, name: n, value: a, size: o, label: i, disabled: s = !1, datePickerProps: c = {}, clearable: u = !1 } = e; const [h, m] = r.useState(!1); return (0, l.jsx)(d, { dateAdapter: $a, adapterLocale: "en", children: (0, l.jsx)(Ga, { ...c, disabled: s || !1, label: i, onChange: e => { const n = null === e || void 0 === e ? void 0 : e.toISO();
                                t(n), m(!1) }, name: n, slots: { textField: Rt.A }, value: a ? Za.c9.fromISO(a) : u ? null : Za.c9.now(), open: h, slotProps: { field: { clearable: u }, textField: { size: o, variant: "outlined", fullWidth: !0, InputProps: { endAdornment: (0, l.jsx)(Ce.A, { position: "end", onClick: () => { m(!h) }, children: (0, l.jsx)(Ka.gF, { icon: "Date", size: "xs" }) }) } } } }) }) } }, 32601: (e, t, n) => { "use strict";
                n.d(t, { A: () => d });
                n(65043); var r = n(86697),
                    a = n(96446),
                    o = n(85865),
                    i = n(67784),
                    l = n(32115),
                    s = n(76790),
                    c = n(70579); const d = e => { let { selected: t, label: n, name: d, handleSelect: u, options: h, freeSolo: m = !0, limitTags: p = 1, multiple: f = !0, error: v, size: g = "small", required: y = !1, fullWidth: b = !1, hideChips: w = !0, customPlaceHolder: z = "" } = e; return (0, c.jsx)(r.A, { multiple: f, id: "tags-outlined-".concat(d), onChange: u, value: t, options: h, disableCloseOnSelect: f, freeSolo: m, fullWidth: b, getOptionLabel: e => "object" === typeof e ? "" : e, renderOption: (e, t, n) => (0, c.jsxs)(a.A, { ...e, children: [f && (0, c.jsx)(s.A, { onClick: e.onClick, color: "primary", size: g, checked: n.selected }), (0, c.jsx)(o.A, { variant: l.Eq.bodySM, children: t })] }), limitTags: p, ChipProps: { size: g, ...w ? { style: { display: "none" } } : {} }, sx: { "& .MuiAutocomplete-option": { alignItems: "flex-start" }, "& .MuiInputBase-formControl": { padding: "10px" } }, renderInput: e => (0, c.jsx)(i.A, { ...e, fullWidth: !0, size: g, name: d, variant: "outlined", placeholder: z || "Search ".concat(n || d), error: !(null === v || void 0 === v || !v.message), helperText: null === v || void 0 === v ? void 0 : v.message, label: n, required: y }) }) } }, 24876: (e, t, n) => { "use strict";
                n.d(t, { A: () => g });
                n(65043); var r = n(61258),
                    a = n(47088),
                    o = n(67784),
                    i = n(96446),
                    l = n(85865),
                    s = n(4598),
                    c = n(77739),
                    d = n(32601),
                    u = n(32115),
                    h = n(20965),
                    m = n(76790),
                    p = n(90538),
                    f = n(24241),
                    v = n(70579); const g = e => { var t, n; let { onFieldChange: g, name: y, error: b, inputRef: w, label: z, field: x, value: A, autoFocus: k, required: S = !1, size: M = "small", fullWidth: E = !1, localSearchFieldProps: C = {} } = e; const { control: T } = (0, r.xW)(), H = x.type; let L = "";
                    H === a.yw.RichText && (L = (Array.isArray(A) ? A.join("\n") : A || "").toString()); const I = e => function(t) { for (var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++) r[a - 1] = arguments[a]; "function" === typeof g && g.call(null, (null === t || void 0 === t ? void 0 : t.target) instanceof HTMLInputElement ? t.target.value : r[0]), "function" === typeof e && e(t, ...r) },
                        j = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(o.A, { size: M, error: !(null === b || void 0 === b || !b.message), name: r, inputRef: w, defaultValue: n, fullWidth: E, label: z ? "".concat(z, " ").concat("computed" === H ? "(computed)" : "") : "", autoFocus: k || !1, onChange: I(t), helperText: null === b || void 0 === b ? void 0 : b.message, disabled: "computed" === H, required: S }) } }),
                        V = j,
                        O = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: L, render: e => { let { onChange: t, name: n } = e; return (0, v.jsx)(o.A, { size: M, multiline: !0, minRows: 3, error: !(null === b || void 0 === b || !b.message), name: n, defaultValue: L, fullWidth: E, label: z, autoFocus: k || !1, onChange: I(t), helperText: null === b || void 0 === b ? void 0 : b.message, required: S }) } }),
                        R = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(o.A, { size: M, error: !(null === b || void 0 === b || !b.message), type: "url", name: r, inputRef: w, defaultValue: n, fullWidth: E, label: z, onChange: I(t), helperText: null === b || void 0 === b ? void 0 : b.message, required: S }) } }),
                        P = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(o.A, { error: !(null === b || void 0 === b || !b.message), type: "number", name: r, inputRef: w, defaultValue: n, fullWidth: E, label: z || "", onChange: I(t), helperText: null === b || void 0 === b ? void 0 : b.message, size: M, required: S }) } }),
                        D = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { var t, n; let { onChange: r, value: a } = e; return (0, v.jsx)(d.A, { selected: N(x, a), label: z, handleSelect: I(((e, t) => { var n, a; const o = null !== x && void 0 !== x && null !== (n = x.typeMetadata) && void 0 !== n && null !== (a = n.location) && void 0 !== a && a.multiple ? t : null === t ? [] : [t];
                                        r(o) })), name: y, options: (null === x || void 0 === x || null === (t = x.typeMetadata) || void 0 === t || null === (n = t.location) || void 0 === n ? void 0 : n.choices.map((e => e.nickname))) || [], multiple: !1, error: b, required: S, fullWidth: E, size: M }) } }),
                        F = void 0 !== (null === C || void 0 === C ? void 0 : C.multiple) ? null === C || void 0 === C ? void 0 : C.multiple : (null === x || void 0 === x || null === (t = x.typeMetadata) || void 0 === t || null === (n = t.tags) || void 0 === n ? void 0 : n.multiple) || !1,
                        N = (e, t) => { var n, r, o, i; let l = null === e || void 0 === e || null === (n = e.typeMetadata) || void 0 === n ? void 0 : n.tags; switch (e.type) {
                                case a.yw.Location:
                                    l = null === e || void 0 === e || null === (r = e.typeMetadata) || void 0 === r ? void 0 : r.location; break;
                                case a.yw.Tags:
                                    l = null === e || void 0 === e || null === (o = e.typeMetadata) || void 0 === o ? void 0 : o.tags } return (void 0 !== (null === C || void 0 === C ? void 0 : C.multiple) ? F : null === (i = l) || void 0 === i ? void 0 : i.multiple) ? t ? Array.isArray(t) ? t : [t] : [] : t ? Array.isArray(t) ? null === t[0] ? null : t[0] : t : [] },
                        _ = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { var t, n, r, a; let { onChange: o, value: i, name: l } = e; return (0, v.jsx)(d.A, { size: M, selected: N(x, i), label: z, handleSelect: I(((e, t) => { o(F ? t : null === t ? [] : [t]) })), name: l, options: (null === x || void 0 === x || null === (t = x.typeMetadata) || void 0 === t || null === (n = t.tags) || void 0 === n ? void 0 : n.choices) || [], freeSolo: !(null !== x && void 0 !== x && null !== (r = x.typeMetadata) && void 0 !== r && null !== (a = r.tags) && void 0 !== a && a.isRestricted), multiple: F, error: b, required: S, limitTags: C.limitTags || 1, hideChips: void 0 === (null === C || void 0 === C ? void 0 : C.hideChips) || C.hideChips }) } }),
                        B = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsxs)(i.A, { display: "flex", flexDirection: "row", alignItems: "center", gap: (0, h.A)(12), children: [(0, v.jsx)(l.A, { variant: u.Eq.bodySM, children: z }), null !== x && void 0 !== x && x.labelLeft ? (0, v.jsx)(l.A, { variant: u.Eq.bodySM, children: null === x || void 0 === x ? void 0 : x.labelLeft }) : null, "role.orientation" === r ? (0, v.jsx)(s.A, { name: r, checked: "right" === n, onChange: I((e => { t(e.target.checked ? "right" : "left") })) }) : (0, v.jsx)(s.A, { name: r, checked: n, onChange: I((e => { t(e.target.checked) })) }), null !== x && void 0 !== x && x.labelLeft ? (0, v.jsx)(l.A, { variant: u.Eq.bodySM, children: null === x || void 0 === x ? void 0 : x.labelLeft }) : null] }) } }),
                        W = (0, v.jsx)(r.xI, { control: T, name: y, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsxs)(i.A, { display: "flex", flexDirection: "row", alignItems: "center", gap: (0, h.A)(12), children: [(0, v.jsx)(m.A, { name: r, checked: /1|true/i.test(n), onChange: I((e => { t(e.target.checked) })), required: S }), (0, v.jsx)(l.A, { variant: u.Eq.bodySM, children: z })] }) } }),
                        U = (0, v.jsx)(r.xI, { name: y, control: T, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(p.A, { onChange: I(t), value: n, name: r, size: M, label: z }) } }),
                        q = (0, v.jsx)(r.xI, { name: y, control: T, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(o.A, { size: M, error: !(null === b || void 0 === b || !b.message), type: "number", name: r, inputRef: w, defaultValue: n, fullWidth: E, label: z || "", onChange: I(t), helperText: null === b || void 0 === b ? void 0 : b.message, required: S }) } }),
                        G = f.c9.fromISO(A),
                        K = (0, v.jsx)(c.A, { title: (0, v.jsx)(i.A, { textAlign: "center", p: 1 }), placement: "bottom", arrow: !0, children: (0, v.jsx)(r.xI, { name: y, control: T, defaultValue: A, render: e => { let { onChange: t, value: n, name: r } = e; return (0, v.jsx)(o.A, { name: r, inputRef: w, defaultValue: null !== G && void 0 !== G && G.isValid ? G.toLocaleString() : n, fullWidth: E, size: M, label: z || "", autoFocus: k || !1, onChange: I(t), required: S }) } }) }); return {
                        [a.yw.String]: V, [a.yw.RichText]: O, [a.yw.Url]: R, [a.yw.Number]: P, [a.yw.Location]: D, [a.yw.Tags]: _, [a.yw.Switch]: B, [a.yw.Boolean]: W, [a.yw.Date]: U, [a.yw.Currency]: q, [a.yw.Computed]: K } [H] || j } }, 31056: (e, t, n) => { "use strict";
                n.d(t, { FV: () => d, qU: () => o.A, Dr: () => a.A, P8: () => i.A });
                n(65043), n(32115); var r = n(70579);
                n(75156); var a = n(47593),
                    o = n(49194),
                    i = n(24876),
                    l = n(80286),
                    s = n(96446),
                    c = n(16528); const d = e => { let { size: t = 24, icon: n, color: a, margin: o, shape: i = "circle" } = e; const d = t,
                        u = t; let h = "0"; "circle" === i && (h = "50%"); return !(!n || !l.Wb[n]) ? (0, r.jsx)(s.A, { width: d, height: u, margin: o, display: "flex", justifyContent: "center", alignItems: "center", children: (0, r.jsx)(c.r, { icon: n, iconColor: a, sx: { color: a, display: "flex" }, width: d, height: u }) }, "colorBadgeIconBox-".concat(n, "-").concat(a)) : (0, r.jsx)(s.A, { width: d, height: u, bgcolor: a, borderRadius: h, margin: o }) };
                n(38887) }, 16091: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); const r = JSON.parse('{"chart":{"id":"sampleChart","name":"Sample Chart","organization":"sampleOrganization"},"roles":[{"id":"sampleRole_0","name":"Sample Chart","type":"department","children":["sampleRole_1"],"parent":""},{"id":"sampleRole_1","name":"CEO","members":["samplePerson_1"],"type":"single","children":["sampleRole_2","sampleRole_3"],"parent":"sampleRole_0"},{"id":"sampleRole_2","name":"Project Manager","members":["samplePerson_2"],"type":"assistant","orientation":"left","parent":"sampleRole_1","children":[]},{"id":"sampleRole_3","name":"Project Manager","members":["samplePerson_3"],"type":"assistant","orientation":"right","parent":"sampleRole_1","children":[]}],"people":[{"id":"samplePerson_1","firstName":"John","lastName":"Doe","name":"John Doe","email":"<EMAIL>","phone":"************"},{"id":"samplePerson_2","firstName":"Jane","lastName":"Doe","name":"Jane Doe","email":"<EMAIL>","phone":"************"},{"id":"samplePerson_3","firstName":"Stefanie","lastName":"Jules","name":"Stefanie Jules","email":"<EMAIL>","phone":"************"}]}'),
                    a = JSON.parse('{"customizeRoleTypes":false,"data":{"chart":[{"fontDetails":{"size":14,"weight":400,"underline":false,"italic":false,"strikethrough":false,"color":"#151515","align":"center","showLabel":false,"border":"none"},"fieldId":null,"fieldKey":"member.name"},{"fontDetails":{"size":14,"weight":400,"underline":false,"italic":false,"strikethrough":false,"color":"#151515","align":"center","showLabel":false,"border":"none"},"fieldId":null,"fieldKey":"role.name"}]},"base":{"cards":{"cardFrame":{"size":{"width":240,"height":140,"sharedMemberHeight":50,"auto":true,"autoStrategy":"type"},"backgroundColor":"#ffffff","colorPosition":"top","colorDisplayStyle":"both","visible":true,"thickness":1,"color":"#B7B7B7","shape":"oval"},"textAlign":"center"},"lines":{"primary":{"visible":true,"style":"solid","thickness":0.5,"color":"#aaaaaa"},"secondary":{"visible":true,"style":"dotted","thickness":0.5,"color":"#aaaaaa"},"department":{"visible":false}},"photos":{"standard":{"visible":true,"size":120},"noPhoto":{"visible":true,"size":120,"imageAvatarId":"avatar3","customImageUrl":null},"vacant":{"visible":true,"size":120,"imageAvatarId":"vacant1","customImageUrl":""},"position":"above","shape":"circle","shadow":false}},"chartOptions":{"legend":{"title":"Legend","badgeShape":"circle"},"stackDirection":"horizontal","stackColumns":3,"stackRows":3,"stackDirectionShared":"horizontal","stackColumnsShared":3,"stackRowsShared":3,"minifyClusters":true,"compactMode":true,"orderAutomatically":false,"orderBy":"lastName","vacantText":"-vacant-","backgroundColor":"#F8F8F8"},"orgStructure":{"roleCounts":{"rules":{"countByPeople":false,"directReportsOnly":false,"inclDept":false,"inclLocation":false,"inclUnassigned":true,"inclSharedMembers":true},"visible":true}},"single":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"shared":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"assistant":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"department":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"chartLink":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"location":{"cards":{"cardFrame":{"size":{"width":200,"height":150,"sharedMemberHeight":50},"backgroundColor":"#FFFFFF","colorPosition":"top","colorDisplayStyle":"both"},"textAlign":"center","cardData":[]},"lines":{"department":{"visible":false}}},"name":"Modern","organimiThemeName":"modern","id":"modern","image":"modern"}'),
                    o = JSON.parse('{"role":{"id":"sampleRole_1","name":"CEO","members":["samplePerson_1"],"type":"single","children":[],"parent":"","locationAddress":"Los Angeles"},"member":{"id":"samplePerson_1","firstName":"John","lastName":"Doe","name":"John Doe","email":"<EMAIL>","phone":"************","linkedIn":"https://www.linkedin.com/feed/"}}');

                function i(e) { return "dashboardThemeChartData" === e ? r : "fallbackTheme" === e ? a : "detailsPaneThemeData" === e ? o : {} } }, 46105: (e, t, n) => { "use strict";
                n.d(t, { K: () => ne }); var r, a, o, i, l, s, c, d, u = n(57528),
                    h = n(65043),
                    m = n(76031),
                    p = n(25438),
                    f = n(96364),
                    v = n(77325),
                    g = n(61531),
                    y = n(30105),
                    b = n(55357),
                    w = n(9579),
                    z = n(40454),
                    x = n(9989),
                    A = n(87603),
                    k = n(28269),
                    S = n(56175),
                    M = n(5571),
                    E = n(40963),
                    C = n(52216),
                    T = n(63508),
                    H = n(72119),
                    L = n(59177),
                    I = n(84866),
                    j = n(61258),
                    V = n(70512),
                    O = n(14556),
                    R = n(83340),
                    P = n(64418),
                    D = n(5816),
                    F = n(78396),
                    N = n(58550),
                    _ = n(22440),
                    B = n(49015),
                    W = n(70579); const U = (0, H.Ay)(f.A)(r || (r = (0, u.A)(["\n  color: #999999;\n  font-size: 14px;\n"]))),
                    q = (0, H.Ay)(v.A)(a || (a = (0, u.A)(["\n  color: #999999;\n  .MuiListItemText-primary {\n    color: #888888;\n    font-size: 13px;\n  }\n  .MuiListItemText-secondary {\n    color: #999999;\n    font-size: 13px;\n  }\n"]))),
                    G = (0, H.Ay)(f.A)(o || (o = (0, u.A)(["\n  ", "\n"])), (e => { let { theme: { palette: t } } = e; return "\n    color: ".concat(t.primary.light, ";\n  ") })),
                    K = (0, H.Ay)(f.A)(i || (i = (0, u.A)(["\n  font-weight: 500;\n"]))),
                    Z = (0, H.Ay)(m.A)(l || (l = (0, u.A)(["\n  .MuiBackdrop-root {\n    background-color: rgba(222, 222, 222, 0.5);\n  }\n"]))),
                    Y = H.Ay.a(s || (s = (0, u.A)(["\n  font-size: 14px;\n  color: darkblue;\n"]))),
                    X = (0, H.Ay)(f.A)(c || (c = (0, u.A)(["\n  font-weight: 400;\n  font-size: 14px;\n  color: #888888;\n"]))),
                    $ = H.Ay.a(d || (d = (0, u.A)(["\n  font-size: 14px;\n  color: darkblue;\n    margin-left: 8px;\n"]))),
                    Q = 450,
                    J = e => { let { handleAccessRequested: t } = e; const n = (0, O.wA)(),
                            [r, a] = (0, h.useState)(!1),
                            { register: o, handleSubmit: i, errors: l } = (0, j.mN)(),
                            { confirmAction: s } = (0, P.A)(),
                            c = i((async e => { s({ title: "Request Organimi Access", message: "We will notify ".concat(e.email, " you would like access to their Organimi account, including multiple suggestions to share. Continue?"), cancelButtonText: "Cancel", confirmButtonText: "Send Request", execFunc: async () => { a(!0); const { error: r } = await n(R.hW.requestAccess({ email: e.email }));
                                        a(!1), r || t(e.email) } }) })); return (0, W.jsx)("form", { onSubmit: c, children: (0, W.jsxs)(g.A, { display: "flex", alignItems: "center", gridGap: 8, m: 2, children: [(0, W.jsx)(g.A, { display: "flex", flex: 3, children: (0, W.jsx)(I.A, { disabled: r, name: "email", label: "Owner Email", placeholder: "Account Owner Email", inputRef: o({ required: "This email is invalid", pattern: V.eT }), error: !!l.email, fullWidth: !0 }) }), (0, W.jsx)(y.A, { disabled: r, type: "submit", size: "small", color: "secondary", variant: "contained", children: "Request Access" })] }) }) },
                    ee = e => { let { suggestedName: t } = e; return (0, W.jsxs)(W.Fragment, { children: [(0, W.jsx)(g.A, { width: Q, display: "flex", flexDirection: "column", mb: 3, mt: 1, children: (0, W.jsx)(X, { children: "Your account is created to keep licensed resources in one space, including payment information, primary contact and multiple owner access" }) }), (0, W.jsx)(g.A, { width: Q, children: (0, W.jsxs)("form", { children: [(0, W.jsxs)(g.A, { display: "flex", flexDirection: "column", children: [(0, W.jsx)(I.A, { label: "Company Name", required: !0, fullWidth: !0, autoFocus: !0, defaultValue: t }), (0, W.jsx)(I.A, { label: "Plan Selection", select: !0, fullWidth: !0, children: (0, W.jsx)(b.A, { value: "", disabled: !0, children: "Select a plan to get started" }) })] }), (0, W.jsx)(g.A, { display: "flex", justifyContent: "center", gridGap: 16, my: 2, children: (0, W.jsx)(y.A, { variant: "contained", color: "primary", children: "Get Started" }) })] }) })] }) },
                    te = e => { let { license: t } = e; if (null !== t && void 0 !== t && t.requiresReLogin) { var n; let e = null === (n = ({ methodNotAllowed: "This account requires you to login with this company's SSO.", samlLogoutRequired: "You will have to logout from your company's SSO and login using any other methods to access this account.", incorrectIdp: "This account requires you to login with this company's SSO." } [null === t || void 0 === t ? void 0 : t.reLoginReason] || "") + " Selecting this account will log you out.") || void 0 === n ? void 0 : n.trim(); return (0, W.jsx)(w.Ay, { title: e, arrow: !0, children: (0, W.jsx)(C.A, {}) }) } return null !== t && void 0 !== t && t.isLoginMethodRestricted ? (0, W.jsx)(T.A, {}) : (0, W.jsx)(E.A, {}) },
                    ne = e => { let { handleAccountSelect: t, licenses: n, suggestedName: r, disableForceSSO: a, activeLicenseId: o, handleClose: i, fullScreen: l } = e; const [s] = (0, _.A)(), [c, d] = (0, h.useState)((() => 0 === (null === n || void 0 === n ? void 0 : n.length))), [u, m] = (0, h.useState)(!1), b = (0, O.wA)(), E = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; const { companyName: t, accessLevel: n } = e, r = s > 450 ? t : (0, L.RP)(t, 20); return (0, W.jsxs)(g.A, { display: "flex", gridGap: 8, children: [(0, W.jsx)(K, { children: (0, L.Sn)(n || "Owner") }), s > 450 && (0, W.jsx)(f.A, { children: " of " }), (0, W.jsx)(G, { children: r })] }) }, C = function() { let e, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                            e = t.accessLevel === F.td.ADMIN || t.accessLevel === F.td.OWNER ? t.usageDescription : t.ownerString; const n = (0, L.RP)(e, 40); return (0, W.jsx)(z.A, { container: !0, children: (0, W.jsx)(w.Ay, { title: e, arrow: !0, children: (0, W.jsx)(q, { secondary: n }) }) }) }, [T, H] = (0, h.useState)(!1), I = u ? "Already have an Account?" : "Is your account not listed above?", j = s < Q; return (0, W.jsxs)(Z, { open: !0, fullScreen: l, children: [(0, W.jsx)(D.A, { onClose: i, children: "Select Primary Account" }), (0, W.jsx)(p.A, { dividers: !0, children: (0, W.jsxs)(g.A, { width: Math.min(Q, s), children: [u && (0, W.jsx)(ee, { suggestedName: r, handleExistingAccount: () => { m(!1) } }), (0, W.jsxs)(z.A, { container: !0, justifyContent: "center", children: [(0, W.jsx)(N.A, { variant: "caption", color: "#9e9e9e" }), (0, W.jsxs)($, { color: "primary", onClick: async () => { await b(B.ip.logout()) }, children: [" ", "Logout?"] })] }), (0, W.jsxs)(x.A, { children: [!(null !== n && void 0 !== n && n.length) && (0, W.jsx)(A.A, { children: (0, W.jsx)(v.A, { primary: "No Accounts Found", secondary: "Were you invited to Organimi under another email?" }) }), n.map(((e, n) => (0, W.jsxs)(h.Fragment, { children: [(0, W.jsxs)(A.A, { button: !0, onClick: () => t(e), selected: e.id === o, children: [!j && (0, W.jsx)(k.A, { children: (0, W.jsx)(te, { license: e }) }), (0, W.jsx)(v.A, { primary: E(e), secondary: C(e) }), (0, W.jsx)(S.A, { children: (0, W.jsx)(y.A, { color: "primary", variant: "outlined", size: "small", onClick: n => { n.stopPropagation(), t(e) }, children: "Select" }) })] }), (null === e || void 0 === e ? void 0 : e.showDisableForceSSO) && (0, W.jsx)(z.A, { container: !0, justifyContent: "center", children: (0, W.jsx)(y.A, { color: "error", variant: "outlined", size: "small", onClick: t => { t.stopPropagation(), a(e) }, children: "Disable Force SSO" }) })] }, "".concat(e.id, "_").concat(n))))] }), (0, W.jsx)(M.A, {}), (0, W.jsx)(g.A, { display: "flex", justifyContent: "center", mt: 2, children: (0, W.jsx)(Y, { onClick: () => { c && H(!1), d((e => !e)) }, children: I }) }), c && !T && (0, W.jsxs)(g.A, { children: [(0, W.jsx)(g.A, { display: "flex", flexDirection: "column", p: 2, children: (0, W.jsx)(U, { children: "You may have been invited under a different email. If you know the account owner's contact, enter their email below and we can inform them you would like account access." }) }), (0, W.jsx)(J, { handleAccessRequested: () => { H(!0) } })] }), c && T && (0, W.jsx)(g.A, { children: (0, W.jsxs)(g.A, { display: "flex", flexDirection: "column", p: 2, children: [(0, W.jsx)(U, { children: "Access Request Complete. If the owner exists in Organimi, they will be notified of how to give you access into their account." }), (0, W.jsx)(g.A, { mb: 1, mt: 2, children: (0, W.jsx)(K, { align: "center", children: "Refresh may be required after access granted" }) })] }) })] }) })] }) } }, 70520: (e, t, n) => { "use strict";
                n.d(t, { A: () => f }); var r = n(14556),
                    a = n(91688),
                    o = n(83340),
                    i = n(49015),
                    l = n(94916),
                    s = n(66588),
                    c = n(18073),
                    d = n(46105),
                    u = n(53222),
                    h = n(66856),
                    m = n(356),
                    p = n(70579);

                function f(e) { let { handleClose: t, mode: n = "page" } = e; const [f, v] = (0, l.A)("activeLicenseId", null, !0), g = (0, r.d4)(u.H), y = (0, r.d4)(u.n), { show: b } = (0, s.A)(), w = (0, a.useHistory)(), z = (0, r.wA)(); return (0, p.jsxs)(p.Fragment, { children: ["page" === n && (0, p.jsx)(c.A, {}), (0, p.jsx)(d.K, { licenses: g, handleCreateNew: () => { b("create new account") }, handleAccountSelect: async e => { if (null !== e && void 0 !== e && e.requiresReLogin) { await z(i.ip.logout()); const t = "samlLogoutRequired" === (null === e || void 0 === e ? void 0 : e.reLoginReason) ? (0, h.iD)() : "".concat((0, h.iD)(), "?step=2");
                                    window.location.assign(t) } else v(e.id), w.push("/"); "function" === typeof t && t() }, suggestedName: y, disableForceSSO: async e => { const { error: t } = await z(o.hW.allowAllLoginMethods({ licenseId: null === e || void 0 === e ? void 0 : e.id })); var n;
                                t || m.A.trackEvent({ eventName: "SSO_FORCE_DISABLED_OVERRIDE", extraParams: { licenseId: null === e || void 0 === e ? void 0 : e.id, plan: (null === e || void 0 === e || null === (n = e.plan) || void 0 === n ? void 0 : n.id) || "" } });
                                window.location.assign("/") }, activeLicenseId: f, handleClose: t })] }) } }, 53222: (e, t, n) => { "use strict";
                n.d(t, { H: () => o, n: () => i }); var r = n(70512),
                    a = n(41); const o = e => { const t = e.user.licenses || [],
                            n = e.user; return null === t || void 0 === t ? void 0 : t.map((e => { const t = (0, a.yO)(e),
                                r = (0, a.Zg)(e, n),
                                o = (0, a.TU)(e, n); return { ...e, ownerString: o, companyName: r, usageDescription: t } })) },
                    i = e => { const t = e.user.username; return (0, r.Vf)(t) && (0, r.NC)(t) || "" } }, 69219: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => d, WJ: () => i, Wq: () => c, Z0: () => s, hR: () => l, oL: () => a, uo: () => o }); const r = (0, n(80907).Z0)({ name: "appInfo", initialState: {}, reducers: { openActiveError: (e, t) => { let { payload: n } = t;
                                e.activeError = n }, closeActiveError: e => { e.activeError = null }, updateAppMode: (e, t) => { let { payload: { primary: n, secondary: r } } = t;
                                e.mode = n, e.secondaryMode = r } } }),
                    a = e => e.appInfo.activeError,
                    o = e => e.appInfo.mode,
                    i = e => e.appInfo.secondaryMode,
                    { openActiveError: l, closeActiveError: s, updateAppMode: c } = r.actions,
                    d = r.reducer }, 86327: (e, t, n) => { "use strict";
                n.d(t, { U: () => a, y: () => o }); var r = n(24241); const a = { "chart-created": "Chart created", "chart-deleted": "Chart deleted", "chart-viewed": "Chart viewed", "chart-printed": "Chart printed", "chart-exported": "Chart exported", "chart-link-created": "Chart link created", "chart-link-deleted": "Chart link deleted", "data-imported": "Data imported", "role-created": "Role created", "role-updated": "Role updated", "role-deleted": "Role deleted", "people-created": "People created", "people-updated": "People updated", "people-deleted": "People deleted", "people-exported": "People exported", "field-created": "Custom field created", "field-updated": "Custom field updated", "field-deleted": "Custom field deleted", "permission-created": "Permissions created", "permission-updated": "Permissions updated", "permission-deleted": "Permissions deleted", "integration-updated": "Integration setup updated", "integration-deleted": "Integration setup deleted", "snapshot-created": "Backup snapshot manually created", "snapshot-deleted": "Backup snapshot deted", "snapshot-enabled": "Monthly auto snapshots enabled", "snapshot-disabled": "Monthly auto snapshots disabled", "snapshot-restored": "Backup snapshot restored" },
                    o = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ""; const t = r.c9.fromISO(e); return t.isValid ? t.toLocaleString(r.c9.DATETIME_MED_WITH_SECONDS) : e } }, 58550: (e, t, n) => { "use strict";
                n.d(t, { A: () => s });
                n(65043); var r = n(14556),
                    a = n(9579),
                    o = n(42006),
                    i = n(96364),
                    l = n(70579); const s = e => { const t = (0, r.d4)(o.VW),
                        n = null === t || void 0 === t ? void 0 : t.lastLoginMethod; return !!n && (0, l.jsx)(a.Ay, { title: n, arrow: !0, children: (0, l.jsxs)(i.A, { color: "primary", ...e, children: ["Logged in with ", n] }) }) } }, 10268: (e, t, n) => { "use strict";
                n.d(t, { n: () => g }); var r = n(2173),
                    a = n(45956),
                    o = n(14556),
                    i = n(53109),
                    l = n(84),
                    s = n(36138),
                    c = n(66856),
                    d = n(66588),
                    u = n(6463),
                    h = n(43862),
                    m = n(37091),
                    p = n(64418),
                    f = n(10621),
                    v = function(e) { return e.APPROVE = "approve", e.REJECT = "reject", e }(v || {});

                function g() { const e = (0, o.wA)(),
                        { show: t } = (0, d.A)(),
                        { confirmAction: n } = (0, p.A)(),
                        g = (0, o.d4)(u.ay),
                        y = (0, o.d4)(u.$T),
                        { openDialog: b } = (0, l.A)("verifySuggestion"),
                        { openDialog: w } = (0, l.A)("rejectSuggestion"),
                        { openDialog: z } = (0, l.A)("changeSuggestionManager"),
                        x = (0, s.u)((0, c.Yj)()).params,
                        A = e => e.id,
                        [k, S] = (0, r.A)((async n => { const { error: r } = await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: n.id, action: a.Bl.APPROVE })); if (!r) { if (n.suggestionType === m.hC.ROLE) { const { error: r } = await e(h.h7.create({ orgId: x.orgId, chartId: x.chartId, data: { role: { _id: n.suggestedRoleId, fields: [{ name: "name", model: "role", value: n.roleTitle }] }, rel: { id: "root", position: "below" } } })); return void(r ? console.error("Error creating role from suggestion:", r) : t("1 role suggestion approved", "success")) } r || t("1 role suggestion approved", "success") } }), A),
                        [M, E] = (0, r.A)((async n => { for (const t of n) await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: t, action: a.Bl.APPROVE }));
                            t("".concat(n.length, " role suggestions approved"), "success") }), (e => e)),
                        [C, T] = (0, r.A)((async (t, n, r) => { const o = await e(i.wz.updateChartSuggestion({ parentRoleId: n, chartId: x.chartId, suggestionId: t.id, action: a.Bl.REJECT })); return r ? await r() : o }), A),
                        H = "string" === typeof k ? k : "string" === typeof C ? C : null; return { handleChangeSuggestionManager: e => { z({ suggestion: e, requestedParentId: e.suggestedParentId }) }, handleChangeRoleManager: (e, n) => { const r = y[e.id];
                            r || t("No role suggestion found for this role", "error"), z({ suggestion: r, requestedParentId: n }) }, handleRejectRelatedSuggestions: async t => { for (const n of g[t.id] || []) await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: n.id, action: a.Bl.REJECT, parentRoleId: t.parent })); if (y[t.id]) { const n = y[t.id];
                                await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: n.id, action: a.Bl.REJECT, parentRoleId: "" })) } }, handleAcceptSuggestionFromRole: r => { const a = (0, f.mA)(r); return n({ execFunc: async () => { const n = r.suggestion,
                                        a = n.id,
                                        o = await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: a, action: v.APPROVE })); if (o.error) console.error("Error accepting suggestion:", o.error);
                                    else { const { error: a } = await e(h.h7.create({ orgId: x.orgId, chartId: x.chartId, data: { role: { ...r, children: [], parent: null, _id: n.role.id } } }));
                                        a ? console.error("Error creating role from suggestion:", a) : t("1 role suggestion approved", "success") } }, title: "Accept Suggested Position", message: 'Are you sure you want to accept "'.concat(a, '" to hold this position in the chart?'), cancelButtonText: "Cancel", confirmButtonText: "Approve Position" }) }, handleRejectSuggestionFromRole: async t => { const n = t.suggestion.id,
                                r = await e(i.wz.updateChartSuggestion({ chartId: x.chartId, suggestionId: n, action: v.REJECT }));
                            r.error ? console.error("Error accepting suggestion:", r.error) : console.log("Suggestion accepted successfully") }, handleApproveRequest: (e, t, n) => { b({ suggestion: e, rejectVisible: t, allowContinue: n }) }, handleRejectRequest: e => { w({ suggestion: e }) }, handleApproveSuggestion: S, handleRejectSuggestion: T, handleApproveBulkSuggestions: E, suggestionIdsPending: H ? [H] : Array.isArray(M) ? M : [] } } }, 6463: (e, t, n) => { "use strict";
                n.d(t, { $T: () => v, $r: () => h, I8: () => p, Xo: () => c, ay: () => f, bA: () => g, e2: () => u, oZ: () => d }); var r = n(80192),
                    a = n(37091),
                    o = n(74079),
                    i = n(95695),
                    l = n(59445),
                    s = n(34944); const c = (0, r.Mz)(i.yP, (e => e.length)),
                    d = (0, r.Mz)(i.yP, (e => e.filter((e => e.status === a.TK.PENDING)).length)),
                    u = (0, r.Mz)(i.RW, o.A, (e => { var t; return null === (t = e.chart.insights) || void 0 === t ? void 0 : t.filters }), ((e, t, n) => { const r = e.filter((e => e.type === a.hC.LINK && !(null !== n && void 0 !== n && n.status) && e.status === a.TK.PENDING || (null === n || void 0 === n ? void 0 : n.status) === e.status)),
                            o = []; for (let a = 0; a < r.length; a++) { var i; let e;
                            a > 0 && (e = o[a - 1]); const n = r[a],
                                s = null === (i = n.parent) || void 0 === i ? void 0 : i.id,
                                c = n.role.id,
                                d = s ? t[s] : null,
                                u = c ? t[c] : void 0;
                            o[a] = (0, l.Sp)(n, u, d), e && (e.nextSuggestion = o[a]) } return o })),
                    h = (0, r.Mz)(i.RW, o.A, ((e, t) => n => e.filter((e => e.type === n)).map((e => { var n; const r = null === (n = e.parent) || void 0 === n ? void 0 : n.id,
                            a = e.role.id,
                            o = r ? t[r] : null,
                            i = a ? t[a] : void 0; return (0, l.Sp)(e, i, o) })))),
                    m = ((0, r.Mz)(i.RW, (e => e.reduce(((t, n) => { if (n.type !== a.hC.ROLE) return t; const r = e.find((e => { var t, r; return e.type === a.hC.LINK && (null === (t = e.parent) || void 0 === t ? void 0 : t.id) === (null === (r = n.parent) || void 0 === r ? void 0 : r.id) })); return r && (t[n.id] = (0, l.Sp)(r)), t }), {}))), (0, r.Mz)(i.RW, (e => { const t = {}; return e.forEach((e => { var n; const r = null === (n = e.parent) || void 0 === n ? void 0 : n.id;
                            r && (t[r] || (t[r] = []), t[r].push(e.role.id)) })), t }))),
                    p = (0, r.Mz)(m, i.RW, ((e, t) => { const n = {}; return t.forEach((e => { n[e.role.id] = e })), r => { const a = [],
                                o = t.find((e => e.id === r)); return o ? (a.push({ suggestionId: o.id, id: o.role.id, parent: void 0, label: o.role.title }), function t(r) { const o = (e[r] || []).map((e => n[e])); for (const e of o) { var i; if (e) a.push({ suggestionId: e.id, id: e.role.id, parent: null === (i = e.parent) || void 0 === i ? void 0 : i.id, label: e.role.title }), t(e.role.id) } }(o.role.id), a) : [] } })),
                    f = (0, r.Mz)(i.RW, (e => e.reduce(((e, t) => { var n, r, o, i; if (t.type === a.hC.ROLE || null === (n = t.parent) || void 0 === n || !n.id) return e;
                        e[null === (r = t.parent) || void 0 === r ? void 0 : r.id] || (e[null === (i = t.parent) || void 0 === i ? void 0 : i.id] = []); return e[null === (o = t.parent) || void 0 === o ? void 0 : o.id].push((0, l.Sp)(t)), e }), {}))),
                    v = (0, r.Mz)(i.RW, (e => e.reduce(((e, t) => { var n; return t.type === a.hC.LINK && null !== (n = t.role) && void 0 !== n && n.id ? (e[t.role.id] = (0, l.Sp)(t), e) : e }), {}))),
                    g = s.A }, 45956: (e, t, n) => { "use strict";
                n.d(t, { Bl: () => a, F4: () => s, HD: () => i, pz: () => o, r_: () => l }); var r = n(37294); let a = function(e) { return e.APPROVE = "approve", e.REJECT = "reject", e }({}),
                    o = function(e) { return e[e.CONFIDENCE_LEVELS = 0] = "CONFIDENCE_LEVELS", e[e.IMPROVE_CHART = 1] = "IMPROVE_CHART", e[e.LOG = 2] = "LOG", e[e.INSIGHTS = 3] = "INSIGHTS", e }({}); const i = {
                    [o.CONFIDENCE_LEVELS]: "Confidence levels explained", [o.IMPROVE_CHART]: "Improve your chart", [o.LOG]: "Verification Log", [o.INSIGHTS]: "Insights" }; let l = function(e) { return e[e.LOW = 0] = "LOW", e[e.MEDIUM = .5] = "MEDIUM", e[e.HIGH = .79] = "HIGH", e }({}); const s = {
                    [l.LOW]: r.Qs.Error[500], [l.MEDIUM]: r.Qs.Warning[200], [l.HIGH]: r.Qs.Success[600] } }, 40364: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => s, F8: () => o, cO: () => l }); var r = n(80907); const a = "boardOfDirectors",
                    o = (0, n(9787).a)({ slice: a, scope: "boardOfDirectors" }),
                    i = (0, r.Z0)({ name: a, initialState: { governanceChart: null }, reducers: { reloadBoardOfDirectors: (e, t) => { let { payload: n } = t;
                                e.governanceChart = n.governanceChart }, "getGovernanceChart/fulfilled": (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), "addMemberToGovernanceChart/fulfilled": (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), "updateGovernanceChartObject/fulfilled": (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), "addNewCommittee/fulfilled": (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), "updateCommittee/fulfilled": (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), deleteMemberWithType: (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }), deleteCommittee: (e, t) => ({ ...e, governanceChart: { ...e.governanceChart || {}, ...t.payload.governanceChart } }) } }),
                    { reloadBoardOfDirectors: l } = i.actions,
                    s = i.reducer }, 58053: (e, t, n) => { "use strict";
                n.d(t, { A: () => Ee }); var r = n(65043),
                    a = n(22264),
                    o = n(12987),
                    i = n(10242),
                    l = n(14556),
                    s = n(69219),
                    c = n(24259),
                    d = n(30391),
                    u = n(89656),
                    h = n(64021),
                    m = n(37259),
                    p = n(2267),
                    f = n(39179),
                    v = n(52996),
                    g = n(72613); const y = e => { let t = document.getElementById(e); if (t) try { t.scrollIntoView({ behavior: "smooth", block: "center", inline: "center" }) } catch (n) { console.log("Problem in scrollIdIntoView, e="), console.log(n) } }; var b = n(23993),
                    w = n(55679),
                    z = n(84),
                    x = n(19367),
                    A = n(78396),
                    k = n(93865),
                    S = n(9727),
                    M = n(52527),
                    E = n(96364),
                    C = n(79071),
                    T = n(22818),
                    H = n(356),
                    L = n(97626),
                    I = n(48853),
                    j = n(91688),
                    V = n(70579); const O = () => { const { userHasMinAccess: e } = (0, I.A)(), [t, n] = (0, r.useState)(null), [o, i] = (0, r.useState)(!1), [s, c] = (0, r.useState)(null), d = A.Ay.chart.padding, { dropZoneCardRef: h, chartControlState: m, chartSpaceRef: p, newDropZoneCardRef: f } = (0, a.A)(), { resourceAction: v } = (0, j.useParams)(), g = v === A.uI.AI_INSIGHTS, [, y] = f || [], w = (0, l.d4)(b.Vs), O = (0, l.d4)(x.LL), R = (0, l.d4)((e => (0, b.gn)(e.roles, t))) || {}, { item: P, dragItemType: D } = m.current, F = (null === P || void 0 === P ? void 0 : P.id) !== (null === R || void 0 === R ? void 0 : R.id), { type: N } = R || {}, _ = L.b.includes(N), { cards: { cardFrame: B } } = (0, l.d4)((e => (0, u.FV)(e, N))), W = p.current || { h: 0, v: 0 }, { shape: U } = B, { openDialog: q } = (0, z.A)("newRole"), [{ canDrop: G, isOver: K }, Z] = (0, k.H)({ accept: [T.A.PERSON, T.A.ROLE], drop(e) { if (e.type === T.A.PERSON) { const { current: { itemClickOrDragCB: e } } = { ...m };
                                G && K && _ && (H.A.trackEvent({ eventName: "TALENT_POOL_ASSIGN_DRAG" }), "function" === typeof e && e({ role: R })) } }, collect: e => ({ isOver: e.isOver(), canDrop: e.canDrop() }) });
                    (0, r.useEffect)((() => { K || ("function" === typeof n && n(null), "function" === typeof y && y(null)) }), [K]); const Y = e => t => async n => { let { members: r } = n; if (D === T.A.PERSON) q({ rel: { id: t, position: e }, members: r, isTalentPoolDrop: !0 });
                        else if (D === T.A.ROLE) { const { current: { itemAssignOrCancelCB: n } } = m;
                            await n({ rel: { id: t, position: e }, draggedRole: P }) } };
                    (0, r.useEffect)((() => { h.current = [t, n] }), [t]); const X = w[t],
                        $ = t && X; if (!$) return null; const Q = O <= 1.2 ? "lg" : O <= 1.6 ? "x2" : "x3"; let J = O * X.w1,
                        ee = O * X.left + d + W.h / 2,
                        te = O * X.h1,
                        ne = O * X.top + d + W.v / 2,
                        re = !o;
                    J < 200 && (ee -= (200 - J) / 2, J = 200), te < 120 && (ne -= (120 - te) / 2, te = 120); let ae, oe = "5px"; if ("square" === U) oe = "0px";
                    else oe = "5px"; return D === T.A.ROLE ? (ae = "Drop over the plus button", re = !1) : ae = _ ? s ? "Drop to insert role ".concat(s) : "Drop to assign to role" : "Cannot assign to ".concat(N || "this role"), F || (ae = "Drop onto the plus button of the target role."), $ && (0, V.jsxs)(M.A, { width: J, height: te, left: ee, top: ne, borderRadius: oe, showBorder: re, isRoleTypeDroppable: _, ref: Z, children: [(0, V.jsx)(S.A, { height: te, children: (0, V.jsx)(E.A, { weight: "bold", color: "#fff", children: ae }) }), e(A.td.EDITOR) && F && (0, V.jsxs)(V.Fragment, { children: [!g && (0, V.jsx)(C.A, { size: Q, icon: "Add", align: "top", direction: "above", handleCreateRoleFromDrop: Y("above")(t), role: R, handleIsOver: i, handleAlign: c }), !g && (0, V.jsx)(C.A, { size: Q, icon: "Add", align: "left", direction: "on left", handleCreateRoleFromDrop: Y("left")(t), role: R, handleIsOver: i, handleAlign: c }), (0, V.jsx)(C.A, { size: Q, icon: "Add", align: "bottom", direction: "below", handleCreateRoleFromDrop: Y("below")(t), role: R, handleIsOver: i, handleAlign: c }), !g && (0, V.jsx)(C.A, { size: Q, icon: "Add", align: "right", direction: "on right", handleCreateRoleFromDrop: Y("right")(t), role: R, handleIsOver: i, handleAlign: c })] })] }) }; var R, P, D = n(57528),
                    F = n(61531),
                    N = n(72119),
                    _ = n(45904); const B = (0, N.Ay)(F.A).attrs({ position: "absolute", justifyContent: "center", alignItems: "center", flexDirection: "column" })(R || (R = (0, D.A)(["\n  background: rgba(245, 244, 246, 0.9);\n"]))),
                    W = (0, N.Ay)(E.A)(P || (P = (0, D.A)(["\n  margin-top: 8px;\n  font-weight: 500;\n"]))),
                    U = e => { let { message: t, ...n } = e; return (0, V.jsxs)(B, { display: "flex", ...n, children: [(0, V.jsx)(_.A, {}), (0, V.jsx)(W, { children: t || "updating..." })] }) },
                    q = () => { const e = A.Ay.chart.padding,
                            [t, n] = (0, r.useState)(null),
                            o = (0, l.d4)(b.Vs),
                            { loadingCardRef: i, chartSpaceRef: s } = (0, a.A)(),
                            c = (0, l.d4)(x.LL),
                            d = s.current || { h: 0, v: 0 }; if ((0, r.useEffect)((() => { i.current = [t, n] }), []), !t) return null; const u = o[t]; if (!(t && u)) return null; let h = c * u.w1,
                            m = c * u.left + e + d.h / 2,
                            p = c * u.h1,
                            f = c * u.top + e + d.v / 2; return (0, V.jsx)(U, { width: h, height: p, left: m, top: f }) }; var G, K = n(49092),
                    Z = n(57546),
                    Y = n(66856),
                    X = n(58481),
                    $ = n(65453),
                    Q = n(72629),
                    J = n(82372),
                    ee = n(61),
                    te = n(34336),
                    ne = n(75156),
                    re = n(52861); const ae = (0, N.Ay)(re.A)(G || (G = (0, D.A)(["\n  cursor: pointer;\n  &:hover rect {\n    fill: black;\n  }\n"])));

                function oe(e) { let { width: t, text: n = "Remove Pin", handleClick: r } = e; return (0, V.jsxs)(ae, { onClick: r, top: 0, children: [(0, V.jsx)("rect", { width: t, height: 30, fill: "#666666", rx: 5 }), (0, V.jsxs)(v.A, { top: 4, left: 4, children: [(0, V.jsx)(ne.me, { inlineSvg: !0, name: "thumbtack", variant: "light", fontSize: 20, color: "#ffffff" }), (0, V.jsx)(te.A, { y: 4, x: 36, textAnchor: "start", verticalAnchor: "start", fill: "#ffffff", children: n })] })] }) } var ie = n(89471),
                    le = n(81635),
                    se = n(14241); const ce = e => { let { mode: t } = e; const n = (0, l.d4)(b.xC),
                        { left: a, top: o, w1: i } = n,
                        s = (0, l.d4)(se.a4),
                        c = (0, l.d4)(ie.sm),
                        d = (0, j.useParams)(),
                        u = (0, j.useHistory)(),
                        { userHasMinAccess: h } = (0, I.A)(),
                        m = h(A.td.ADMIN),
                        p = (0, l.wA)(),
                        f = () => { s ? (p((0, ee.nZ)()), p((0, le.Ch)())) : c && u.push((0, Y.si)({ ...d, chartId: c.rootChartId })) },
                        v = (0, r.useMemo)((() => s ? "Remove Pin" : c ? "Visit Main" : ""), [s]); if (s || c && m && "print" !== t && "printPreview" !== t) { let e = 132,
                            t = 30; return (0, V.jsx)("svg", { id: "roleCard_root", width: e, height: t, x: a + (i || 0) / 2 - e / 2, y: n.top, overflow: "visible", children: (0, V.jsx)(oe, { width: e, height: t, handleClick: f, text: v }) }) } return (0, V.jsx)("svg", { id: "roleCard_root", x: a, y: o }) }; var de, ue, he = n(72835),
                    me = n(40454),
                    pe = n(42006); const fe = (0, N.Ay)("div")(de || (de = (0, D.A)(["\n  position:absolute;\n  top:3px;\n  left:0;\n  right:0;\n  display:flex;\n  align-items:center;\n  justify-content:center;\n  z-index:1;\n"]))),
                    ve = (0, N.Ay)(F.A)(ue || (ue = (0, D.A)(["\n  background:#EEEEEE;\n  padding:8px;\n  border-radius:10px;\n"]))),
                    ge = () => { const { openDialog: e } = (0, z.A)("upgradeDialog"), t = (0, l.d4)(pe.eW); return (0, V.jsx)(V.Fragment, { children: (0, V.jsx)(fe, { children: (0, V.jsx)(ve, { children: (0, V.jsxs)(me.A, { container: !0, spacing: 3, justifyContent: "center", alignItems: "baseline", children: [(0, V.jsx)(me.A, { item: !0, children: (0, V.jsx)(E.A, { variant: "body1", children: "Upgrade to a Paid Plan to remove watermark" }) }), (0, V.jsx)(me.A, { item: !0, children: (0, V.jsx)(he.A, { variant: "contained", color: "secondary", onClick: () => { t && "owner" === t && e() }, children: "Upgrade Now" }) })] }) }) }) }) }; var ye = n(53492); const be = n.p + "static/media/watermark-new.da6193254adceb2d6844.png"; var we; const ze = (0, N.Ay)("div")(we || (we = (0, D.A)(["\n  position: absolute;\n  top: 15px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1;\n  pointer-events: none;\n  ", " {\n    width: auto;\n    max-height: 90%;\n    max-width: 100%;\n  }\n"])), ye.A),
                    xe = () => (0, V.jsx)(V.Fragment, { children: (0, V.jsx)(ze, { children: (0, V.jsx)(ye.A, { src: be }) }) }); var Ae = n(24251),
                    ke = n(43940),
                    Se = n(76345),
                    Me = n(13888); const Ee = e => { let { readOnly: t = !1 } = e; const n = (0, j.useHistory)(),
                        x = (0, j.useParams)(),
                        { resourceAction: k } = x,
                        { t: S } = (0, K.B)(),
                        { openDialog: M } = (0, z.A)("newRole"),
                        { chartContainerRef: E, chartSvgRef: C, topGroupRef: T, newActionCardState: H, dropZoneCardRef: L } = (0, a.A)(),
                        [, R] = L.current,
                        [, P] = H,
                        D = (0, l.d4)(s.uo),
                        F = (0, l.d4)(u.w$),
                        N = (0, l.d4)(b.YG),
                        _ = (0, l.d4)(b.d7),
                        { userType: B } = (0, I.A)(),
                        { prevChartId: W } = (0, Z.A)(),
                        U = (0, l.d4)(pe.VF),
                        G = (0, l.d4)(ie.Pq),
                        ee = (0, l.d4)(Me.D9);
                    (0, ke.M)({ featureTours: ["chartTour", "chartViewTour"] }); const te = "trialing" === (null === U || void 0 === U ? void 0 : U.status) || "expired" === (null === U || void 0 === U ? void 0 : U.status) || "free" === (null === U || void 0 === U ? void 0 : U.paidPlanType);
                    (0, $.A)(), (0, c.A)(), (0, d.A)(), (0, Q.A)(), (0, J.A)();
                    (0, Ae.A)(D !== A.uI.PUBLIC ? ["print", "zoom"] : ["zoom"]), (0, r.useEffect)((() => () => { P(null) }), [D]), (0, r.useEffect)((() => { "print" === D || N || setTimeout((() => { y("roleCard_root") }), 400) }), []), (0, r.useEffect)((() => { "print" !== D && "printPreview" !== D && N && setTimeout((() => { y("roleCard_".concat(N)) }), 400) }), [N]); const ne = !_ && ((0, V.jsx)(h.A, { text: S("General.Text.NothingHere"), image: m.A, handleClick: G && (() => { M({ rel: { id: "root", position: "below" } }) }), buttonText: G && S("General.Text.CreateANewRole"), isButtonVisible: G && ("owner" === B || "admin" === B) && "view" === D }) || null); return (0, V.jsxs)(V.Fragment, { children: [te && "printPreview" === D && (0, V.jsx)(ge, {}), te && ("printPreview" === D || "print" === D) && (0, V.jsx)(xe, {}), (0, V.jsx)(i.A, { children: (0, V.jsxs)(o.A, { id: "chartExterior", ref: E, allowScroll: D !== A.uI.PRINT_PREVIEW || (null === ee || void 0 === ee ? void 0 : ee.auto), bgcolor: F, onClick: () => { "function" === typeof P && P(null), "function" === typeof R && R(null) }, children: [W && "theme" !== k && "settings" !== k && (0, V.jsx)(X.A, { variant: "outlined", color: "primary", onClick: () => n.push((0, Y.si)({ ...x, chartId: W })), children: "Back To Chart" }), (0, V.jsx)(p.A, { innerRef: C, className: "chart", id: "chartSVG", children: (0, V.jsxs)(v.A, { innerRef: T, id: "chartSvg", children: [(0, V.jsx)(ce, { mode: D }), (0, V.jsx)(f.A, {}), (0, V.jsx)(g.A, {})] }) }), ne, (0, V.jsx)(w.A, { readOnly: t }), (0, V.jsx)(O, {}), (0, V.jsx)(q, {}), (0, V.jsx)(Se.V, {})] }) })] }) } }, 39179: (e, t, n) => { "use strict";
                n.d(t, { A: () => b }); var r = n(89015),
                    a = n(44938),
                    o = n(14556),
                    i = n(23993),
                    l = n(89656),
                    s = n(70579); const c = e => { let { active: t, d: n, fill: r, color: a, p1: o, p2: i, style: l, width: c } = e; if (!t) return (0, s.jsx)(s.Fragment, {}); let d = "0,0"; return l && "solid" !== l ? "dashed" === l ? d = "20,5" : "dotted" === l && (d = 2 * c + "," + 4 * c) : d = "0,0", (0, s.jsxs)("svg", { style: { position: "absolute", height: "100%", width: "100%", display: "block", pointerEvents: "none", left: 0, right: 0, overflow: "visible" }, children: [(0, s.jsx)("defs", { children: (0, s.jsx)("marker", { id: "triangle", viewBox: "0 0 10 10", refX: "0", refY: "5", markerUnits: "strokeWidth", markerWidth: "10", markerHeight: "8", orient: "auto", children: (0, s.jsx)("path", { d: "M 0 0 L 10 5 L 0 10 z" }) }) }), (0, s.jsxs)("g", { transform: "translate(0.5,0.5)", strokeWidth: (c || 1).toString(), children: [(0, s.jsx)("path", { d: n, fill: r, stroke: a, strokeDasharray: d }), (0, s.jsx)("circle", { cx: o.cx, cy: o.cy, r: o.r, fill: o.fill }), (0, s.jsx)("circle", { cx: i.cx, cy: i.cy, r: i.r, fill: i.fill })] })] }) }; var d, u = n(57528),
                    h = n(52996); const m = n(72119).Ay.rect(d || (d = (0, u.A)(["\n  fill-opacity: 0;\n  stroke-dashoffset: 4px;\n  stroke-width: 1px;\n  stroke-dasharray: 3px 6px;\n"]))),
                    p = e => { let { left: t, top: n, width: r, height: a, stroke: o } = e; return (0, s.jsx)(h.A, { left: t, top: n, children: (0, s.jsx)(m, { stroke: o, width: r, height: a }) }) }; var f = n(78396),
                    v = n(89471),
                    g = n(10268),
                    y = n(59445);

                function b(e) { let { subchartId: t = f.Uz } = e; const n = (0, o.d4)((e => (0, i.DK)(e, t))),
                        d = (0, o.d4)(l.y2),
                        u = (0, o.d4)(i.S0),
                        h = (0, o.d4)(v.Ht),
                        m = (0, o.d4)(v.ro),
                        { handleApproveRequest: b } = (0, g.n)(),
                        w = (0, o.d4)((e => (0, i.BU)(e, t))),
                        z = (0, o.d4)(l.Nh); return (0, s.jsxs)(s.Fragment, { children: [n.map(((e, t) => { const n = e.parentId && (h[e.parentId] || []).includes(e.childId),
                                o = n && m(e.parentId, e.childId),
                                i = n ? (0, y.q$)(o) : null,
                                l = { ...d }; return n && (l.style = "dashed", l.color = i.line.color, l.thickness = i.line.thickness), (0, s.jsx)(a.A, { data: e, percent: e.percent, children: t => { let { path: n } = t; return (0, s.jsx)(s.Fragment, { children: (0, s.jsx)(r.A, { d: n(e), options: l }) }) } }, "chart-line-".concat(t, "-").concat(e.parentId, "-").concat(e.childId)) })), n.map((e => { const t = e.parentId && (h[e.parentId] || []).includes(e.childId),
                                n = t && m(e.parentId, e.childId); let r = n ? (0, y.q$)(n) : null; return t && !n ? null : t && !e.partialLine && e.markerPoint && (0, s.jsxs)("svg", { style: { pointerEvents: "all", cursor: "pointer" }, onClick: () => { b(n, !1) }, children: [(0, s.jsx)("rect", { x: e.markerPoint.x - 14, y: e.markerPoint.y - 12, width: 28, height: 18, rx: 4, fill: r.marker.fill, opacity: .85 }), (0, s.jsxs)("text", { x: e.markerPoint.x, y: e.markerPoint.y + r.marker.size / 2 - 5, textAnchor: "middle", alignmentBaseline: "middle", fontSize: "12", fontWeight: "bold", fill: "#fff", style: { userSelect: "none" }, children: [Math.round(100 * n.confidence), "%"] })] }, "marker-".concat(e.parentId, "-").concat(e.childId)) })), u.map(((e, t) => (0, s.jsx)(c, { active: !0, d: e.d, p1: e.p1, p2: e.p2, fill: "none", color: e.color, style: e.style, width: e.thickness }, "dotted-chart-line-".concat(t)))), z.borderVisible && w.map(((e, t) => (0, s.jsx)(p, { stroke: null === z || void 0 === z ? void 0 : z.borderColor, ...e || {} }, "depBox_".concat((null === e || void 0 === e ? void 0 : e.id) || t))))] }) } }, 72613: (e, t, n) => { "use strict";
                n.d(t, { A: () => FNe }); var r = {};
