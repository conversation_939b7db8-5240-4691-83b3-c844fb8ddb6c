                    _De = (0, mt.A)(a.createElement("path", { d: "M16.75 13.96c.25.13.41.2.46.3.06.11.04.61-.21 1.18-.2.56-1.24 1.1-1.7 1.12-.46.02-.47.36-2.96-.73-2.49-1.09-3.99-3.75-4.11-3.92-.12-.17-.96-1.38-.92-2.61.05-1.22.69-1.8.95-2.04.24-.26.51-.29.68-.26h.47c.15 0 .36-.06.55.45l.69 1.87c.06.13.1.28.01.44l-.27.41-.39.42c-.12.12-.26.25-.12.5.12.26.62 1.09 1.32 1.78.91.88 1.71 1.17 1.95 1.3.24.14.39.12.54-.04l.81-.94c.19-.25.35-.19.58-.11l1.67.88M12 2a10 10 0 0 1 10 10 10 10 0 0 1-10 10c-1.97 0-3.8-.57-5.35-1.55L2 22l1.55-4.65A9.969 9.969 0 0 1 2 12 10 10 0 0 1 12 2m0 2a8 8 0 0 0-8 8c0 1.72.54 3.31 1.46 4.61L4.5 19.5l2.89-.96A7.95 7.95 0 0 0 12 20a8 8 0 0 0 8-8 8 8 0 0 0-8-8z" }), "WhatsApp"),
                    BDe = (0, mt.A)(a.createElement("path", { d: "M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z" }), "Whatshot"),
                    WDe = (0, mt.A)(a.createElement("path", { d: "M11.57 13.16c-1.36.28-2.17 1.16-2.17 2.41 0 1.34 1.11 2.42 2.49 2.42 2.05 0 3.71-1.66 3.71-3.71 0-1.07-.15-2.12-.46-3.12-.79 1.07-2.2 1.72-3.57 2zM13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM12 20c-3.31 0-6-2.69-6-6 0-1.53.3-3.04.86-4.43 1.01 1.01 2.41 1.63 3.97 1.63 2.66 0 4.75-1.83 5.28-4.43C17.34 8.97 18 11.44 18 14c0 3.31-2.69 6-6 6z" }), "WhatshotOutlined"),
                    UDe = (0, mt.A)(a.createElement("path", { d: "M17.09 4.56c-.7-1.03-1.5-1.99-2.4-2.85-.35-.34-.94-.02-.84.46.19.94.39 2.18.39 3.29 0 2.06-1.35 3.73-3.41 3.73-1.54 0-2.8-.93-3.35-2.26-.1-.2-.14-.32-.2-.54-.11-.42-.66-.55-.9-.18-.18.27-.35.54-.51.83C4.68 9.08 4 11.46 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8c0-3.49-1.08-6.73-2.91-9.44zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.47-.3 2.98-.93 4.03-1.92.28-.26.74-.14.82.23.23 1.02.35 2.08.35 3.15.01 2.65-2.14 4.8-4.79 4.8z" }), "WhatshotRounded"),
                    qDe = (0, mt.A)(a.createElement("path", { d: "M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z" }), "WhatshotSharp"),
                    GDe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M16.11 6.77c-.53 2.6-2.62 4.43-5.28 4.43-1.56 0-2.96-.62-3.97-1.63C6.3 10.96 6 12.47 6 14c0 3.31 2.69 6 6 6s6-2.69 6-6c0-2.56-.66-5.03-1.89-7.23zm-4.22 11.22c-1.37 0-2.49-1.08-2.49-2.42 0-1.25.81-2.13 2.17-2.41 1.37-.28 2.78-.93 3.57-1.99.3 1 .46 2.05.46 3.12 0 2.04-1.66 3.7-3.71 3.7z", opacity: ".3" }), a.createElement("path", { d: "M11.57 13.16c-1.36.28-2.17 1.16-2.17 2.41 0 1.34 1.11 2.42 2.49 2.42 2.05 0 3.71-1.66 3.71-3.71 0-1.07-.15-2.12-.46-3.12-.79 1.07-2.2 1.72-3.57 2zM13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM12 20c-3.31 0-6-2.69-6-6 0-1.53.3-3.04.86-4.43 1.01 1.01 2.41 1.63 3.97 1.63 2.66 0 4.75-1.83 5.28-4.43C17.34 8.97 18 11.44 18 14c0 3.31-2.69 6-6 6z" })), "WhatshotTwoTone"),
                    KDe = (0, mt.A)(a.createElement("path", { d: "M12 2c3.86 0 7 3.14 7 7 0 5.25-7 13-7 13S5 14.25 5 9c0-3.86 3.14-7 7-7zm-1.53 12L17 7.41 15.6 6l-5.13 5.18L8.4 9.09 7 10.5l3.47 3.5z" }), "WhereToVote"),
                    ZDe = (0, mt.A)(a.createElement("path", { d: "M12 1C7.59 1 4 4.59 4 9c0 5.57 6.96 13.34 7.26 13.67l.74.82.74-.82C13.04 22.34 20 14.57 20 9c0-4.41-3.59-8-8-8zm0 19.47C9.82 17.86 6 12.54 6 9c0-3.31 2.69-6 6-6s6 2.69 6 6c0 3.83-4.25 9.36-6 11.47zm-1.53-9.3L8.71 9.4l-1.42 1.42L10.47 14l6.01-6.01-1.41-1.42z" }), "WhereToVoteOutlined"),
                    YDe = (0, mt.A)(a.createElement("path", { d: "M12 2C8.14 2 5 5.14 5 9c0 4.17 4.42 9.92 6.23 12.11.4.48 1.13.48 1.53 0C14.58 18.92 19 13.17 19 9c0-3.86-3.14-7-7-7zm4.31 6.16l-5.13 5.13c-.39.39-1.02.39-1.41 0L7.7 11.22c-.39-.39-.39-1.03 0-1.42.39-.39 1.03-.39 1.42 0l1.36 1.36 4.42-4.42c.39-.39 1.03-.39 1.42 0 .38.4.38 1.03-.01 1.42z" }), "WhereToVoteRounded"),
                    XDe = (0, mt.A)(a.createElement("path", { d: "M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7zm-1.53 12l-3.48-3.48L8.4 9.1l2.07 2.07 5.13-5.14 1.41 1.42L10.47 14z" }), "WhereToVoteSharp"),
                    $De = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M12 3C8.69 3 6 5.69 6 9c0 3.54 3.82 8.86 6 11.47 1.75-2.11 6-7.63 6-11.47 0-3.31-2.69-6-6-6zm-1.53 11l-3.18-3.18L8.71 9.4l1.77 1.77 4.6-4.6 1.41 1.41L10.47 14z", opacity: ".3" }), a.createElement("path", { d: "M12 1C7.59 1 4 4.59 4 9c0 5.57 6.96 13.34 7.26 13.67l.74.82.74-.82C13.04 22.34 20 14.57 20 9c0-4.41-3.59-8-8-8zm0 19.47C9.82 17.86 6 12.54 6 9c0-3.31 2.69-6 6-6s6 2.69 6 6c0 3.83-4.25 9.36-6 11.47zm3.07-13.9l-4.6 4.6L8.71 9.4l-1.42 1.42L10.47 14l6.01-6.01z" })), "WhereToVoteTwoTone"),
                    QDe = (0, mt.A)(a.createElement("path", { d: "M13 13v8h8v-8h-8zM3 21h8v-8H3v8zM3 3v8h8V3H3zm13.66-1.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65z" }), "Widgets"),
                    JDe = (0, mt.A)(a.createElement("path", { d: "M16.66 4.52l2.83 2.83-2.83 2.83-2.83-2.83 2.83-2.83M9 5v4H5V5h4m10 10v4h-4v-4h4M9 15v4H5v-4h4m7.66-13.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65zM11 3H3v8h8V3zm10 10h-8v8h8v-8zm-10 0H3v8h8v-8z" }), "WidgetsOutlined"),
                    eFe = (0, mt.A)(a.createElement("path", { d: "M13 14v6c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1zm-9 7h6c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1zM3 4v6c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1zm12.95-1.6L11.7 6.64c-.39.39-.39 1.02 0 1.41l4.25 4.25c.39.39 1.02.39 1.41 0l4.25-4.25c.39-.39.39-1.02 0-1.41L17.37 2.4c-.39-.39-1.03-.39-1.42 0z" }), "WidgetsRounded"),
                    tFe = (0, mt.A)(a.createElement("path", { d: "M13 13v8h8v-8h-8zM3 21h8v-8H3v8zM3 3v8h8V3H3zm13.66-1.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65z" }), "WidgetsSharp"),
                    nFe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M5 5h4v4H5zm10 10h4v4h-4zM5 15h4v4H5zM16.66 4.52l-2.83 2.82 2.83 2.83 2.83-2.83z", opacity: ".3" }), a.createElement("path", { d: "M16.66 1.69L11 7.34 16.66 13l5.66-5.66-5.66-5.65zm-2.83 5.65l2.83-2.83 2.83 2.83-2.83 2.83-2.83-2.83zM3 3v8h8V3H3zm6 6H5V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4zm8-2v8h8v-8h-8zm6 6h-4v-4h4v4z" })), "WidgetsTwoTone"),
                    rFe = (0, mt.A)(a.createElement("path", { d: "M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" }), "Wifi"),
                    aFe = (0, mt.A)(a.createElement("path", { d: "M20.5 9.5c.28 0 .55.04.81.08L24 6c-3.34-2.51-7.5-4-12-4S3.34 3.49 0 6l12 16 3.5-4.67V14.5c0-2.76 2.24-5 5-5zM23 16v-1.5c0-1.38-1.12-2.5-2.5-2.5S18 13.12 18 14.5V16c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1zm-1 0h-3v-1.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V16z" }), "WifiLock"),
                    oFe = (0, mt.A)(a.createElement("path", { d: "M21.31 9.58L24 6c-3.34-2.51-7.5-4-12-4S3.34 3.49 0 6l12 16 3.5-4.67V14.5c0-2.76 2.24-5 5-5 .28 0 .55.04.81.08zM23 16v-1.5c0-1.38-1.12-2.5-2.5-2.5S18 13.12 18 14.5V16c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1zm-1 0h-3v-1.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V16z" }), "WifiLockOutlined"),
                    iFe = (0, mt.A)(a.createElement("path", { d: "M21.31 9.58L24 6c-3.34-2.51-7.5-4-12-4S3.34 3.49 0 6l10.4 13.87c.8 1.07 2.4 1.07 3.2 0l1.9-2.53V14.5c0-2.76 2.24-5 5-5 .28 0 .55.04.81.08zM23 16v-1.5c0-1.38-1.12-2.5-2.5-2.5S18 13.12 18 14.5V16c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1zm-1 0h-3v-1.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V16z" }), "WifiLockRounded"),
                    lFe = (0, mt.A)(a.createElement("path", { d: "M23 14.64c0-1.31-.94-2.5-2.24-2.63-1.5-.15-2.76 1.02-2.76 2.49V16h-1v6h7v-6h-1v-1.36zM22 16h-3v-1.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V16zm-.69-6.42L24 6c-3.34-2.51-7.5-4-12-4S3.34 3.49 0 6l12 16 3.5-4.67V14.5c0-2.76 2.24-5 5-5 .28 0 .55.04.81.08z" }), "WifiLockSharp"),
                    sFe = (0, mt.A)(a.createElement("path", { d: "M21.31 9.58L24 6c-3.34-2.51-7.5-4-12-4S3.34 3.49 0 6l12 16 3.5-4.67V14.5c0-2.76 2.24-5 5-5 .28 0 .55.04.81.08zM23 16v-1.5c0-1.38-1.12-2.5-2.5-2.5S18 13.12 18 14.5V16c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1zm-1 0h-3v-1.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V16z" }), "WifiLockTwoTone"),
                    cFe = (0, mt.A)(a.createElement("path", { d: "M22.99 9C19.15 5.16 13.8 3.76 8.84 4.78l2.52 2.52c3.47-.17 6.99 1.05 9.63 3.7l2-2zm-4 4c-1.29-1.29-2.84-2.13-4.49-2.56l3.53 3.53.96-.97zM2 3.05L5.07 6.1C3.6 6.82 2.22 7.78 1 9l1.99 2c1.24-1.24 2.67-2.16 4.2-2.77l2.24 2.24C7.81 10.89 6.27 11.73 5 13v.01L6.99 15c1.36-1.36 3.14-2.04 4.92-2.06L18.98 20l1.27-1.26L3.29 1.79 2 3.05zM9 17l3 3 3-3c-1.65-1.66-4.34-1.66-6 0z" }), "WifiOff"),
                    dFe = (0, mt.A)(a.createElement("path", { d: "M21 11l2-2c-3.73-3.73-8.87-5.15-13.7-4.31l2.58 2.58c3.3-.02 6.61 1.22 9.12 3.73zm-2 2c-1.08-1.08-2.36-1.85-3.72-2.33l3.02 3.02.7-.69zM9 17l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zM3.41 1.64L2 3.05 5.05 6.1C3.59 6.83 2.22 7.79 1 9l2 2c1.23-1.23 2.65-2.16 4.17-2.78l2.24 2.24C7.79 10.89 6.27 11.74 5 13l2 2c1.35-1.35 3.11-2.04 4.89-2.06l7.08 7.08 1.41-1.41L3.41 1.64z" }), "WifiOffOutlined"),
                    uFe = (0, mt.A)(a.createElement("path", { d: "M20.06 10.14c.56.46 1.38.42 1.89-.09.59-.59.55-1.57-.1-2.1-3.59-2.94-8.2-4.03-12.55-3.26l2.59 2.59c2.89-.03 5.8.92 8.17 2.86zm-2.27 1.83c-.78-.57-1.63-1-2.52-1.3l2.95 2.95c.24-.58.1-1.27-.43-1.65zm-3.84 4.26c-1.22-.63-2.68-.63-3.91 0-.59.31-.7 1.12-.23 1.59l1.47 1.47c.39.39 1.02.39 1.41 0l1.47-1.47c.49-.47.39-1.28-.21-1.59zm5.73 1.67L4.12 2.34a.9959.9959 0 00-1.41 0c-.39.39-.39 1.02 0 1.41L5.05 6.1c-1.01.5-1.99 1.11-2.89 1.85-.65.53-.69 1.51-.1 2.1.51.51 1.32.56 1.87.1 1-.82 2.1-1.46 3.25-1.93l2.23 2.23c-1.13.3-2.21.8-3.19 1.51-.69.5-.73 1.51-.13 2.11l.01.01c.49.49 1.26.54 1.83.13 1.19-.84 2.58-1.26 3.97-1.29l6.37 6.37c.39.39 1.02.39 1.41 0 .39-.37.39-1 0-1.39z" }), "WifiOffRounded"),
                    hFe = (0, mt.A)(a.createElement("path", { d: "M21 11l2-2c-3.73-3.73-8.87-5.15-13.7-4.31l2.58 2.58c3.3-.02 6.61 1.22 9.12 3.73zM9 17l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm10-4c-1.08-1.08-2.36-1.85-3.72-2.33l3.02 3.02.7-.69zM3.41 1.64L2 3.05 5.05 6.1C3.59 6.83 2.22 7.79 1 9l2 2c1.23-1.23 2.65-2.16 4.17-2.78l2.24 2.24C7.79 10.89 6.27 11.74 5 13l2 2c1.35-1.35 3.11-2.04 4.89-2.06l7.08 7.08 1.41-1.41L3.41 1.64z" }), "WifiOffSharp"),
                    mFe = (0, mt.A)(a.createElement("path", { d: "M21 11l2-2c-3.73-3.73-8.87-5.15-13.7-4.31l2.58 2.58c3.3-.02 6.61 1.22 9.12 3.73zm-2 2c-1.08-1.08-2.36-1.85-3.72-2.33l3.02 3.02.7-.69zM9 17l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zM3.41 1.64L2 3.05 5.05 6.1C3.59 6.83 2.22 7.79 1 9l2 2c1.23-1.23 2.65-2.16 4.17-2.78l2.24 2.24C7.79 10.89 6.27 11.74 5 13l2 2c1.35-1.35 3.11-2.04 4.89-2.06l7.08 7.08 1.41-1.41L3.41 1.64z" }), "WifiOffTwoTone"),
                    pFe = (0, mt.A)(a.createElement("path", { d: "M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" }), "WifiOutlined"),
                    fFe = (0, mt.A)(a.createElement("path", { d: "M2.06 10.06c.51.51 1.32.56 1.87.1 4.67-3.84 11.45-3.84 16.13-.01.56.46 1.38.42 1.89-.09.59-.59.55-1.57-.1-2.1-5.71-4.67-13.97-4.67-19.69 0-.65.52-.7 1.5-.1 2.1zm7.76 7.76l1.47 1.47c.39.39 1.02.39 1.41 0l1.47-1.47c.47-.47.37-1.28-.23-1.59-1.22-.63-2.68-.63-3.91 0-.57.31-.68 1.12-.21 1.59zm-3.73-3.73c.49.49 1.26.54 1.83.13 2.44-1.73 5.72-1.73 8.16 0 .57.4 1.34.36 1.83-.13l.01-.01c.6-.6.56-1.62-.13-2.11-3.44-2.49-8.13-2.49-11.58 0-.69.5-.73 1.51-.12 2.12z" }), "WifiRounded"),
                    vFe = (0, mt.A)(a.createElement("path", { d: "M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" }), "WifiSharp"),
                    gFe = (0, mt.A)(a.createElement("path", { d: "M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.31-2.69-6-6-6s-6 2.69-6 6c0 2.22 1.21 4.15 3 5.19l1-1.74c-1.19-.7-2-1.97-2-3.45 0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.48-.81 2.75-2 3.45l1 1.74c1.79-1.04 3-2.97 3-5.19zM12 3C6.48 3 2 7.48 2 13c0 3.7 2.01 6.92 4.99 8.65l1-1.73C5.61 18.53 4 15.96 4 13c0-4.42 3.58-8 8-8s8 3.58 8 8c0 2.96-1.61 5.53-4 6.92l1 1.73c2.99-1.73 5-4.95 5-8.65 0-5.52-4.48-10-10-10z" }), "WifiTethering"),
                    yFe = (0, mt.A)(a.createElement("path", { d: "M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.31-2.69-6-6-6s-6 2.69-6 6c0 2.22 1.21 4.15 3 5.19l1-1.74c-1.19-.7-2-1.97-2-3.45 0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.48-.81 2.75-2 3.45l1 1.74c1.79-1.04 3-2.97 3-5.19zM12 3C6.48 3 2 7.48 2 13c0 3.7 2.01 6.92 4.99 8.65l1-1.73C5.61 18.53 4 15.96 4 13c0-4.42 3.58-8 8-8s8 3.58 8 8c0 2.96-1.61 5.53-4 6.92l1 1.73c2.99-1.73 5-4.95 5-8.65 0-5.52-4.48-10-10-10z" }), "WifiTetheringOutlined"),
                    bFe = (0, mt.A)(a.createElement("path", { d: "M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.56-3.11-6.4-6.75-5.95-2.62.32-4.78 2.41-5.18 5.02-.33 2.15.49 4.11 1.93 5.4.48.43 1.23.33 1.56-.23l.01-.01c.24-.42.14-.93-.22-1.26-1.03-.93-1.59-2.37-1.22-3.94.33-1.42 1.48-2.57 2.9-2.91C13.65 8.49 16 10.47 16 13c0 1.18-.52 2.23-1.33 2.96-.36.32-.47.84-.23 1.26l.01.01c.31.53 1.03.69 1.5.28C17.2 16.41 18 14.8 18 13zm-7.17-9.93c-4.62.52-8.35 4.33-8.78 8.96-.35 3.7 1.32 7.02 4.02 9.01.48.35 1.16.2 1.46-.31.25-.43.14-.99-.26-1.29-2.28-1.69-3.65-4.55-3.16-7.7.54-3.5 3.46-6.29 6.98-6.68C15.91 4.51 20 8.28 20 13c0 2.65-1.29 4.98-3.27 6.44-.4.3-.51.85-.26 1.29.3.52.98.66 1.46.31C20.4 19.22 22 16.3 22 13c0-5.91-5.13-10.62-11.17-9.93z" }), "WifiTetheringRounded"),
                    wFe = (0, mt.A)(a.createElement("path", { d: "M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.31-2.69-6-6-6s-6 2.69-6 6c0 2.22 1.21 4.15 3 5.19l1-1.74c-1.19-.7-2-1.97-2-3.45 0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.48-.81 2.75-2 3.45l1 1.74c1.79-1.04 3-2.97 3-5.19zM12 3C6.48 3 2 7.48 2 13c0 3.7 2.01 6.92 4.99 8.65l1-1.73C5.61 18.53 4 15.96 4 13c0-4.42 3.58-8 8-8s8 3.58 8 8c0 2.96-1.61 5.53-4 6.92l1 1.73c2.99-1.73 5-4.95 5-8.65 0-5.52-4.48-10-10-10z" }), "WifiTetheringSharp"),
                    zFe = (0, mt.A)(a.createElement("path", { d: "M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 2c0-3.31-2.69-6-6-6s-6 2.69-6 6c0 2.22 1.21 4.15 3 5.19l1-1.74c-1.19-.7-2-1.97-2-3.45 0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.48-.81 2.75-2 3.45l1 1.74c1.79-1.04 3-2.97 3-5.19zM12 3C6.48 3 2 7.48 2 13c0 3.7 2.01 6.92 4.99 8.65l1-1.73C5.61 18.53 4 15.96 4 13c0-4.42 3.58-8 8-8s8 3.58 8 8c0 2.96-1.61 5.53-4 6.92l1 1.73c2.99-1.73 5-4.95 5-8.65 0-5.52-4.48-10-10-10z" }), "WifiTetheringTwoTone"),
                    xFe = (0, mt.A)(a.createElement("path", { d: "M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" }), "WifiTwoTone"),
                    AFe = (0, mt.A)(a.createElement("path", { d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z" }), "Work"),
                    kFe = (0, mt.A)(a.createElement("path", { d: "M23 21.74l-1.46-1.46L7.21 5.95 3.25 1.99 1.99 3.25l2.7 2.7h-.64c-1.11 0-1.99.89-1.99 2l-.01 11c0 1.11.89 2 2 2h15.64L21.74 23 23 21.74zM22 7.95c.05-1.11-.84-2-1.95-1.95h-4V3.95c0-1.11-.89-2-2-1.95h-4c-1.11-.05-2 .84-2 1.95v.32l13.95 14V7.95zM14.05 6H10V3.95h4.05V6z" }), "WorkOff"),
                    SFe = (0, mt.A)(a.createElement("path", { d: "M10 4h4v2h-3.6l2 2H20v7.6l2 2V8c0-1.11-.89-2-2-2h-4V4c0-1.11-.89-2-2-2h-4c-.99 0-1.8.7-1.96 1.64L10 5.6V4zM3.4 1.84L1.99 3.25 4.74 6H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h15.74l2 2 1.41-1.41L3.4 1.84zM4 19V8h2.74l11 11H4z" }), "WorkOffOutlined"),
                    MFe = (0, mt.A)(a.createElement("path", { d: "M4.11 2.54a.9959.9959 0 00-1.41 0c-.39.39-.39 1.02 0 1.41L4.74 6H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h15.74l1.29 1.29c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L4.11 2.54zM10 4h4v2h-3.6L22 17.6V8c0-1.11-.89-2-2-2h-4V4c0-1.11-.89-2-2-2h-4c-.99 0-1.8.7-1.96 1.64L10 5.6V4z" }), "WorkOffRounded"),
                    EFe = (0, mt.A)(a.createElement("path", { d: "M10 4h4v2h-3.6L22 17.6V6h-6V4c0-1.1-.9-2-2-2h-4c-.98 0-1.79.71-1.96 1.64L10 5.6V4zM3.4 1.84L1.99 3.25 4.74 6H2.01L2 21h17.74l2 2 1.41-1.41z" }), "WorkOffSharp"),
                    CFe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M4 8v11h13.74l-11-11zm8.4 0l7.6 7.6V8z", opacity: ".3" }), a.createElement("path", { d: "M10 4h4v2h-3.6l2 2H20v7.6l2 2V8c0-1.11-.89-2-2-2h-4V4c0-1.11-.89-2-2-2h-4c-.99 0-1.8.7-1.96 1.64L10 5.6V4zM3.4 1.84L1.99 3.25 4.74 6H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h15.74l2 2 1.41-1.41L3.4 1.84zM4 19V8h2.74l11 11H4z" })), "WorkOffTwoTone"),
                    TFe = (0, mt.A)(a.createElement("path", { fillRule: "evenodd", d: "M14 6V4h-4v2h4zM4 8v11h16V8H4zm16-2c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z" }), "WorkOutline"),
                    HFe = (0, mt.A)(a.createElement("path", { d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z" }), "WorkOutlined"),
                    LFe = (0, mt.A)(a.createElement("path", { d: "M14 6V4h-4v2h4zM4 8v11h16V8H4zm16-2c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z" }), "WorkOutlineOutlined"),
                    IFe = (0, mt.A)(a.createElement("path", { d: "M14 6V4h-4v2h4zM4 9v9c0 .55.45 1 1 1h14c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1H5c-.55 0-1 .45-1 1zm16-3c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z" }), "WorkOutlineRounded"),
                    jFe = (0, mt.A)(a.createElement("path", { d: "M14 6V4h-4v2h4zM4 8v11h16V8H4zm18-2v15H2.01V6H8V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2v2h6z" }), "WorkOutlineSharp"),
                    VFe = (0, mt.A)(a.createElement("path", { d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM10 4h4v2h-4V4zm10 15H4V8h16v11z" }), "WorkOutlineTwoTone"),
                    OFe = (0, mt.A)(a.createElement("path", { d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z" }), "WorkRounded"),
                    RFe = (0, mt.A)(a.createElement("path", { d: "M22 6h-6V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H2v15h20V6zm-8 0h-4V4h4v2z" }), "WorkSharp"),
                    PFe = (0, mt.A)(a.createElement(a.Fragment, null, a.createElement("path", { d: "M4 8h16v11H4z", opacity: ".3" }), a.createElement("path", { d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM10 4h4v2h-4V4zm10 15H4V8h16v11z" })), "WorkTwoTone"),
                    DFe = (0, mt.A)(a.createElement("path", { d: "M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3 3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z" }), "WrapText"),
                    FFe = (0, mt.A)(a.createElement("path", { d: "M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3 3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z" }), "WrapTextOutlined"),
                    NFe = (0, mt.A)(a.createElement("path", { d: "M5 7h14c.55 0 1-.45 1-1s-.45-1-1-1H5c-.55 0-1 .45-1 1s.45 1 1 1zm11.83 4H5c-.55 0-1 .45-1 1s.45 1 1 1h12.13c1 0 1.93.67 2.09 1.66.21 1.25-.76 2.34-1.97 2.34H15v-.79c0-.45-.54-.67-.85-.35l-1.79 1.79c-.2.2-.2.51 0 .71l1.79 1.79c.32.32.85.09.85-.35V19h2c2.34 0 4.21-2.01 3.98-4.39-.2-2.08-2.06-3.61-4.15-3.61zM9 17H5c-.55 0-1 .45-1 1s.45 1 1 1h4c.55 0 1-.45 1-1s-.45-1-1-1z" }), "WrapTextRounded"),
                    _Fe = (0, mt.A)(a.createElement("path", { d: "M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3 3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z" }), "WrapTextSharp"),
                    BFe = (0, mt.A)(a.createElement("path", { d: "M4 17h6v2H4zm13-6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3 3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4zM4 5h16v2H4z" }), "WrapTextTwoTone"),
                    WFe = (0, mt.A)(a.createElement("path", { d: "M10 15l5.19-3L10 9v6m11.56-7.83c.13.47.22 1.1.28 1.9.07.8.1 1.49.1 2.09L22 12c0 2.19-.16 3.8-.44 4.83-.25.9-.83 1.48-1.73 1.73-.47.13-1.33.22-2.65.28-1.3.07-2.49.1-3.59.1L12 19c-4.19 0-6.8-.16-7.83-.44-.9-.25-1.48-.83-1.73-1.73-.13-.47-.22-1.1-.28-1.9-.07-.8-.1-1.49-.1-2.09L2 12c0-2.19.16-3.8.44-4.83.25-.9.83-1.48 1.73-1.73.47-.13 1.33-.22 2.65-.28 1.3-.07 2.49-.1 3.59-.1L12 5c4.19 0 6.8.16 7.83.44.9.25 1.48.83 1.73 1.73z" }), "YouTube"),
                    UFe = (0, mt.A)(a.createElement("path", { d: "M17.01 14h-.8l-.27-.27c.98-1.14 1.57-2.61 1.57-4.23 0-3.59-2.91-6.5-6.5-6.5s-6.5 3-6.5 6.5H2l3.84 4 4.16-4H6.51C6.51 7 8.53 5 11.01 5s4.5 2.01 4.5 4.5c0 2.48-2.02 4.5-4.5 4.5-.65 0-1.26-.14-1.82-.38L7.71 15.1c.97.57 2.09.9 3.3.9 1.61 0 3.08-.59 4.22-1.57l.27.27v.79l5.01 4.99L22 19l-4.99-5z" }), "YoutubeSearchedFor"),
                    qFe = (0, mt.A)(a.createElement("path", { d: "M17.01 14h-.8l-.27-.27c.98-1.14 1.57-2.61 1.57-4.23 0-3.59-2.91-6.5-6.5-6.5s-6.5 3-6.5 6.5H2l3.84 4 4.16-4H6.51C6.51 7 8.53 5 11.01 5s4.5 2.01 4.5 4.5c0 2.48-2.02 4.5-4.5 4.5-.65 0-1.26-.14-1.82-.38L7.71 15.1c.97.57 2.09.9 3.3.9 1.61 0 3.08-.59 4.22-1.57l.27.27v.79l5.01 4.99L22 19l-4.99-5z" }), "YoutubeSearchedForOutlined"),
                    GFe = (0, mt.A)(a.createElement("path", { d: "M17.01 14h-.8l-.27-.27c1.15-1.34 1.76-3.14 1.51-5.09C17.11 6 15.1 3.78 12.5 3.18 8.26 2.2 4.51 5.53 4.51 9.5h-2.1c-.47 0-.68.59-.31.89l3.4 2.75c.19.2.51.21.71.01l2.9-2.79c.32-.31.1-.86-.35-.86H6.51c0-2.49 2-4.48 4.46-4.5 2.44-.02 4.54 2.05 4.54 4.49 0 2.48-2.02 4.51-4.5 4.51-.45 0-.89-.07-1.3-.19-.34-.1-.71 0-.96.26-.53.53-.32 1.45.39 1.66.59.17 1.22.27 1.87.27 1.61 0 3.08-.59 4.22-1.57l.27.27v.79l4.27 4.25c.41.41 1.07.41 1.48 0 .41-.41.41-1.08 0-1.49L17.01 14z" }), "YoutubeSearchedForRounded"),
                    KFe = (0, mt.A)(a.createElement("path", { d: "M17.01 14h-.8l-.27-.27c.98-1.14 1.57-2.61 1.57-4.23 0-3.59-2.91-6.5-6.5-6.5s-6.5 3-6.5 6.5H2l3.84 4 4.16-4H6.51C6.51 7 8.53 5 11.01 5s4.5 2.01 4.5 4.5c0 2.48-2.02 4.5-4.5 4.5-.65 0-1.26-.14-1.82-.38L7.71 15.1c.97.57 2.09.9 3.3.9 1.61 0 3.08-.59 4.22-1.57l.27.27v.79l5.01 4.99L22 19l-4.99-5z" }), "YoutubeSearchedForSharp"),
                    ZFe = (0, mt.A)(a.createElement("path", { d: "M17.01 14h-.8l-.27-.27c.98-1.14 1.57-2.61 1.57-4.23 0-3.59-2.91-6.5-6.5-6.5s-6.5 3-6.5 6.5H2l3.84 4 4.16-4H6.51C6.51 7 8.53 5 11.01 5s4.5 2.01 4.5 4.5c0 2.48-2.02 4.5-4.5 4.5-.65 0-1.26-.14-1.82-.38L7.71 15.1c.97.57 2.09.9 3.3.9 1.61 0 3.08-.59 4.22-1.57l.27.27v.79l5.01 4.99L22 19l-4.99-5z" }), "YoutubeSearchedForTwoTone"); var YFe = n(53711); const XFe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zm.5-7H9v2H7v1h2v2h1v-2h2V9h-2z" }), "ZoomInOutlined"),
                    $Fe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27c1.2-1.4 1.82-3.31 1.48-5.34-.47-2.78-2.79-5-5.59-5.34-4.23-.52-7.78 3.04-7.27 7.27.34 2.8 2.56 5.12 5.34 5.59 2.03.34 3.94-.28 5.34-1.48l.27.28v.79l4.26 4.25c.41.41 1.07.41 1.48 0l.01-.01c.41-.41.41-1.07 0-1.48L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zm0-7c-.28 0-.5.22-.5.5V9H7.5c-.28 0-.5.22-.5.5s.22.5.5.5H9v1.5c0 .28.22.5.5.5s.5-.22.5-.5V10h1.5c.28 0 .5-.22.5-.5s-.22-.5-.5-.5H10V7.5c0-.28-.22-.5-.5-.5z" }), "ZoomInRounded"),
                    QFe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zm.5-7H9v2H7v1h2v2h1v-2h2V9h-2z" }), "ZoomInSharp"),
                    JFe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zm.5-7H9v2H7v1h2v2h1v-2h2V9h-2z" }), "ZoomInTwoTone"); var eNe = n(10990); const tNe = (0, mt.A)(a.createElement("path", { d: "M15 3l2.3 2.3-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3h-6zM3 9l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3 9 3H3v6zm6 12l-2.3-2.3 2.89-2.87-1.42-1.42L5.3 17.3 3 15v6h6zm12-6l-2.3 2.3-2.87-2.89-1.42 1.42 2.89 2.87L15 21h6v-6z" }), "ZoomOutMap"),
                    nNe = (0, mt.A)(a.createElement("path", { d: "M15 3l2.3 2.3-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3h-6zM3 9l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3 9 3H3v6zm6 12l-2.3-2.3 2.89-2.87-1.42-1.42L5.3 17.3 3 15v6h6zm12-6l-2.3 2.3-2.87-2.89-1.42 1.42 2.89 2.87L15 21h6v-6z" }), "ZoomOutMapOutlined"),
                    rNe = (0, mt.A)(a.createElement("path", { d: "M15.85 3.85L17.3 5.3l-2.18 2.16c-.39.39-.39 1.03 0 1.42.39.39 1.03.39 1.42 0L18.7 6.7l1.45 1.45c.31.31.85.09.85-.36V3.5c0-.28-.22-.5-.5-.5h-4.29c-.45 0-.67.54-.36.85zm-12 4.3L5.3 6.7l2.16 2.18c.39.39 1.03.39 1.42 0 .39-.39.39-1.03 0-1.42L6.7 5.3l1.45-1.45c.31-.31.09-.85-.36-.85H3.5c-.28 0-.5.22-.5.5v4.29c0 .45.54.67.85.36zm4.3 12L6.7 18.7l2.18-2.16c.39-.39.39-1.03 0-1.42-.39-.39-1.03-.39-1.42 0L5.3 17.3l-1.45-1.45c-.31-.31-.85-.09-.85.36v4.29c0 .28.22.5.5.5h4.29c.45 0 .67-.54.36-.85zm12-4.3L18.7 17.3l-2.16-2.18c-.39-.39-1.03-.39-1.42 0-.39.39-.39 1.03 0 1.42l2.18 2.16-1.45 1.45c-.31.31-.09.85.36.85h4.29c.28 0 .5-.22.5-.5v-4.29c0-.45-.54-.67-.85-.36z" }), "ZoomOutMapRounded"),
                    aNe = (0, mt.A)(a.createElement("path", { d: "M15 3l2.3 2.3-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3h-6zM3 9l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3 9 3H3v6zm6 12l-2.3-2.3 2.89-2.87-1.42-1.42L5.3 17.3 3 15v6h6zm12-6l-2.3 2.3-2.87-2.89-1.42 1.42 2.89 2.87L15 21h6v-6z" }), "ZoomOutMapSharp"),
                    oNe = (0, mt.A)(a.createElement("path", { d: "M17.3 5.3l-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3h-6zM9 3H3v6l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3zm-.83 11.41L5.3 17.3 3 15v6h6l-2.3-2.3 2.89-2.87zm7.66 0l-1.42 1.42 2.89 2.87L15 21h6v-6l-2.3 2.3z" }), "ZoomOutMapTwoTone"),
                    iNe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z" }), "ZoomOutOutlined"),
                    lNe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27c1.2-1.4 1.82-3.31 1.48-5.34-.47-2.78-2.79-5-5.59-5.34-4.23-.52-7.79 3.04-7.27 7.27.34 2.8 2.56 5.12 5.34 5.59 2.03.34 3.94-.28 5.34-1.48l.27.28v.79l4.26 4.25c.41.41 1.07.41 1.48 0l.01-.01c.41-.41.41-1.07 0-1.48L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zm-2-5h4c.28 0 .5.22.5.5s-.22.5-.5.5h-4c-.28 0-.5-.22-.5-.5s.22-.5.5-.5z" }), "ZoomOutRounded"),
                    sNe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7V9z" }), "ZoomOutSharp"),
                    cNe = (0, mt.A)(a.createElement("path", { d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z" }), "ZoomOutTwoTone"); const dNe = /(3d_rotation|4k|360)/,
                    uNe = e => { let { name: t = "", width: n = 24, height: o = 24, color: i = "#000000", children: l } = e; const s = (0, a.useRef)(); if (!t && l) return (0, c.jsx)(ht.A, { width: n, height: o, htmlColor: i, children: l }); const d = dNe.test(t) ? "3d_rotation" === (u = t) ? "ThreeDRotation" : "4k" === u ? "FourK" : "360" === u ? "ThreeSixty" : "" : function(e) { return e.replace(/([a-z])([a-z]+)(_|$)/g, (function(e, t, n) { return t.toUpperCase() + n })) }(t); var u; if (!d) return null; const h = r[d]; return (0, c.jsx)(ht.A, { width: n, height: o, htmlColor: i, ref: s, children: !l && h ? (0, c.jsx)(h, { fontSize: "small" }) : l }) }; var hNe = n(52861);

                function mNe(e) { let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
                        n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
                        r = arguments.length > 3 ? arguments[3] : void 0,
                        a = (r || {}).left || "left",
                        o = (r || {}).top || "top"; if (null !== r && void 0 !== r && r.invert) { const e = a;
                        a = o, o = e } return {
                        [a]: t, [o]: n } }

                function pNe(e, t, n, r) { let a; return a = "leftright" === e ? t ? "angle-right" : "angle-left" : t && !("bottomup" === e) ? "angle-down" : "angle-up", (0, c.jsx)(l.me, { inlineSvg: !0, name: a, variant: "light", color: n, fontSize: r }) }

                function fNe(e) { let { ExpandProps: t, CountProps: n, CardBoxProps: r, showRoleCount: o, handleExpandClick: l, handleExpandAllClick: s, handleAliasLinkClick: d, frame: u, mode: h, layout: m, isHovering: p, roleId: f } = e; const { collapsed: v, recursiveExpanded: g, expandStyle: y } = t, b = !g, { width: w, height: z } = r, { recursive: x, direct: A, aliasVisible: k } = n, S = "bottomup" === m, M = "leftright" === m, E = "print" === h || "printPreview" === h, C = u.color || "#909090", T = u.visible ? "".concat(u.thickness, "px") : "0px", H = p ? (0, te.e$)(C, .5) : C, L = (0, i.w5)(u.backgroundColor, "#ffffff", "#444444"), I = (0, i.w5)(u.backgroundColor, "#ffffff", "#444444"), j = p ? (0, te.e$)(I, 1) : I, V = u.backgroundColor, O = 12, R = 18, P = y === U.$.Visible || !y || E && n.position === U.d5.below || p && y === U.$.Hover, D = P && !E && x > 0 && x > A, F = !E && v && P, N = !E && !v && P, _ = !E && k, B = {}, W = 0 === A;
                    o = o && x > 0; let q = o ? 0 : 4; const G = (() => { let e = q;
                            _ && (q += 24, B.alias = q + 2); let t = mNe(M, e + 4 + 2, 2); return _ && (0, a.createElement)(hNe.A, { ...t, key: "aliasIcon_".concat(p, "_").concat(f) }, (0, c.jsx)(uNe, { width: O, height: O, color: j, children: (0, c.jsx)(OK, { fontSize: "small" }) })) })(),
                        K = (() => { if (x) { return (0, se.A)(x.toString(), { fontSize: 12, fontFamily: "Inter" }) } return 0 })(),
                        Z = (() => { if (A) { return (0, se.A)(A.toString(), { fontSize: 12, fontFamily: "Inter" }) } return 0 })(),
                        Y = (() => { let e = q,
                                t = 0; const n = o && x > 0 && x > A;
                            n && (t = K + 8, q += t, B.recursive = q + 2, q += E ? 4 : 0); let r = mNe(M, e + t / 2, 4, { left: "x", top: "y" }); return n && (0, c.jsx)(oe.A, { ...r, verticalAnchor: "start", textAnchor: "middle", fill: L, fontSize: 12, fontFamily: "Inter", children: x }) })(),
                        X = (() => { let e = q,
                                t = M ? 0 : -1,
                                n = pNe(m, b, j, O),
                                r = mNe(M, 0, 5, { invert: M }),
                                o = mNe(M, e, t); return D && (q += 20, B.recursive = q + 2), D && (0, a.createElement)(hNe.A, { ...o, key: "recursiveIcon_".concat(p, "_").concat(f) }, n, (0, c.jsx)(hNe.A, { ...r, children: n })) })();
                    S && (q += 0); const $ = (() => { let e = q,
                                t = 0;!W && o && (t = Z + 8, q += t, B.direct = q + 2); let n = mNe(M, e + t / 2, 4, { left: "x", top: "y" }); return !W && o && (0, c.jsx)(oe.A, { ...n, verticalAnchor: "start", textAnchor: "middle", fill: L, fontSize: 12, fontFamily: "Inter", children: A }) })(),
                        Q = (() => { let e = mNe(M, q, 2),
                                t = pNe(m, F, j, O); return (F || N) && (q += 16, B.direct = q - 2), (F || N) && (0, a.createElement)(hNe.A, { ...e, key: "expandIcon_".concat(p, "_").concat(f) }, t) })();
                    q += 4; let J = mNe(M, w - q - 8, z - u.thickness);

                    function ee(e) { let t = B[e]; return (0, c.jsx)("path", { d: "\n          M ".concat(t, ",0\n          L").concat(t, ",").concat(R, "  \n        "), stroke: H, strokeWidth: T }) } const ne = G && Y || $,
                        re = !(!Y || !$); return !!(Y || X || $ || Q || G) && (0, c.jsx)(c.Fragment, { children: (0, c.jsxs)("g", { transform: "translate(".concat(J.left, ", ").concat(J.top, ")"), onClick: l, children: [(0, c.jsx)("path", { d: "\n            M 0,0 \n            L 0,".concat(14, " \n            S 0,").concat(R, " ").concat(4, ",").concat(R, " \n            L ").concat(q - 4, ",").concat(R, " \n            S ").concat(q, ",").concat(R, " ").concat(q, ",").concat(14, " \n            L ").concat(q, ",0"), stroke: H, strokeWidth: T, fill: V }), ne && ee("alias"), re && ee("recursive"), (0, c.jsxs)("g", { transform: "translate(".concat(2, ",").concat(2, ")"), children: [(0, c.jsx)("g", { onClick: d, children: G }), (0, c.jsxs)("g", { onClick: s, children: [Y, X] }), (0, c.jsxs)("g", { children: [$, Q] })] })] }) }) } var vNe = n(66856); var gNe = n(9368);

                function yNe(e, t) { var n; const r = (0, A.d4)(k.Xp),
                        a = (0, A.d4)(I.lM),
                        o = r && r[e],
                        i = (0, A.d4)(k.Z6),
                        l = null === o || void 0 === o ? void 0 : o.pageNumber,
                        s = null === o || void 0 === o ? void 0 : o.prevPageNumber,
                        c = r && (null === (n = Object.values(r).find((e => { var t; return null === (t = e.partitions) || void 0 === t ? void 0 : t.some((e => e.pageNumber === a)) }))) || void 0 === n ? void 0 : n.partitions.map((e => e.pageNumber))); return { pageLinkVisible: t ? s && (c ? !c.includes(s) : s !== a) && i : l && l !== a && i, nextPageMessage: t ? "<< Page ".concat(s) : "Page ".concat(l, " >>"), nextPageNumber: l, prevPageNumber: s } } var bNe = n(98433); const wNe = e => { let { w1: t, h1: n, roleId: r, isBackLink: a, innerRef: i } = e; const l = (t - 100) / 2,
                        s = (0, A.d4)(j.FV),
                        { pageLinkVisible: d, nextPageMessage: u, nextPageNumber: h, prevPageNumber: m } = yNe(r, a),
                        p = (0, A.wA)(),
                        f = a ? -50 : n,
                        v = a ? -100 : n + 50; return d ? (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)("rect", { x: t / 2, y: f, height: 50, width: s.lines.primary.thickness, fill: s.lines.primary.color, ref: a ? i : null }), (0, c.jsxs)(o.A, { left: l, top: v, onClick: () => { p((0, bNe.IR)(a ? m : h || 1)) }, children: [(0, c.jsx)("rect", { height: 50, width: 100, stroke: s.cards.cardFrame.color, strokeWidth: s.cards.cardFrame.thickness, fill: s.cards.cardFrame.backgroundColor, rx: 10 }), (0, c.jsx)("text", { x: 50, y: 25, textAnchor: "middle", dominantBaseline: "middle", pointerEvents: "none", children: u })] })] }) : null }; var zNe; const xNe = (0, R.Ay)(o.A)(zNe || (zNe = (0, O.A)([""]))),
                    ANe = e => { let { href: t, children: n, top: r, width: a, frame: o = {} } = e; const l = 124,
                            s = 20,
                            d = o.color || "#444444",
                            u = (0, i.w5)(d, "#ffffff", "#000000"); return (0, c.jsx)(xNe, { left: a - l, top: r, width: l, children: (0, c.jsxs)("a", { href: t, children: [(0, c.jsx)("path", { d: "\n          M 0,0 \n          L 0,".concat(20, " \n          S 0,").concat(s, " ").concat(0, ",").concat(s, " \n          L ").concat(124, ",").concat(s, " \n          S ").concat(l, ",").concat(s, " ").concat(l, ",").concat(20, " \n          L ").concat(l, ",0"), stroke: d, strokeWidth: o.thickness, fill: o.backgroundColor, vectorEffect: "non-scaling-stroke" }), (0, c.jsx)(oe.A, { y: 4, x: 62, textAnchor: "middle", verticalAnchor: "start", stroke: u, strokeWidth: .2, children: n })] }) }) };

                function kNe(e) { let { width: t, top: n, chartId: r, embeddedChartId: a } = e; const o = (0, ie.useParams)(),
                        { userType: i } = (0, Je.A)(),
                        l = (0, A.d4)(j.hL),
                        s = i && i !== C.td.PUBLIC,
                        d = (0, vNe.si)({ ...o, chartId: a }); return s && (0, c.jsx)(ANe, { frame: l, href: "".concat(d, "?prevChartId=").concat(r), width: t, top: n, children: "Visit Chart" }) } var SNe; const MNe = 20,
                    ENe = (0, R.Ay)(o.A)(SNe || (SNe = (0, O.A)(["\n  * {\n    cursor: pointer;\n  }\n"]))),
                    CNe = e => { let { top: t, width: n, onClick: r, frame: a, size: s, count: d, showCount: u, message: h } = e; const m = a.color || "#444444",
                            p = (0, i.w5)((null === a || void 0 === a ? void 0 : a.backgroundColor) || "#ffffff"); let f = "sm" === s ? 64 : 136; const v = d > 0 && u,
                            g = (0, c.jsx)(c.Fragment, { children: (0, c.jsxs)(o.A, { left: 4, top: 2, children: [(0, c.jsx)(uNe, { width: 18, height: 18, color: p, children: (0, c.jsx)(l.me, { inlineSvg: !0, name: "user-group", variant: "light", color: p, fontSize: 18 }) }), (0, c.jsx)(oe.A, { x: 22, y: 2, verticalAnchor: "start", width: 30, textAnchor: "start", fill: p, children: d })] }) }); return (0, c.jsxs)(ENe, { top: t, left: n - f, onClick: r, width: f, children: [(0, c.jsx)("path", { d: "\n          M 0,0 \n          L 0,".concat(20, " \n          S 0,").concat(MNe, " ").concat(0, ",").concat(MNe, " \n          L ").concat(f - 0, ",").concat(MNe, " \n          S ").concat(f, ",").concat(MNe, " ").concat(f, ",").concat(20, " \n          L ").concat(f, ",0"), stroke: m, strokeWidth: a.thickness, fill: a.backgroundColor, vectorEffect: "non-scaling-stroke" }), v ? g : null, (0, c.jsx)(o.A, { left: 56, top: 4, children: (0, c.jsx)(oe.A, { verticalAnchor: "start", textAnchor: "start", fontFamily: "Inter", children: h }) })] }) };

                function TNe(e) { let { count: t, showCount: n, roleId: r, top: a, width: o } = e; const i = (0, A.wA)(),
                        l = (0, A.d4)(j.hL),
                        { pageLinkVisible: s, nextPageMessage: d, nextPageNumber: u } = yNe(r); return s ? (0, c.jsx)(CNe, { onClick: () => { i((0, bNe.IR)(u || 1)) }, top: a, width: o, frame: l, count: t, showCount: n, message: d }) : null } var HNe = n(37091),
                    LNe = n(59445); const INe = e => { let { role: t, CardBoxProps: n, handleAccept: r, handleReject: a } = e; const o = t.suggestion,
                        i = (0, LNe.q$)((0, LNe.Sp)(o)),
                        s = i.box.color,
                        d = i.box.thickness,
                        u = Math.max(n.height + 20, 110),
                        h = (0, J.dQ)({ x: -10, y: -10 }, { width: Math.max(n.width + 20, 200), height: Math.max(n.height + 20, 110) }, { tl: n.radius, tr: n.radius, br: n.radius, bl: n.radius }); return (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)("g", { id: "suggestion-".concat(t.id), children: (0, c.jsx)("path", { d: h, fill: "rgba(255, 255, 255, 0.9)", stroke: s, strokeWidth: d, strokeDasharray: "8 8" }) }), (0, c.jsx)(oe.A, { x: n.width / 2, y: u / 3, width: n.width, textAnchor: "middle", dominantBaseline: "middle", fill: "#444444", fontSize: "16", fontWeight: "normal", lineHeight: 24, children: (0, G.Sn)(o.role.title) }), (0, c.jsx)(oe.A, { x: n.width / 2, y: u / 2 + 6, textAnchor: "middle", dominantBaseline: "middle", fill: "#666666", fontSize: "10", fontWeight: "normal", children: "Keep this Suggested Role ?" }), (0, c.jsxs)("g", { transform: "translate(".concat(n.width / 2, ", ").concat(u / 2 + 24, ")"), children: [(0, c.jsxs)("g", { transform: "translate(-20, 0)", onClick: a, pointerEvents: "bounding-box", children: [(0, c.jsx)("rect", { dx: "0", dy: "0", rx: 4, ry: 4, width: "18", height: "18", fill: st.Qs.Neutrals[400], style: { filter: "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1))", transition: "filter 0.2s ease-in-out", cursor: "pointer" }, onMouseEnter: e => { e.currentTarget.style.filter = "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3))" }, onMouseLeave: e => { e.currentTarget.style.filter = "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1))" } }), (0, c.jsx)("g", { transform: "translate(2, 2)", pointerEvents: "none", children: (0, c.jsx)(l.me, { inlineSvg: !0, name: "remove", variant: "light", fontSize: 14, color: "#fff" }) })] }), (0, c.jsxs)("g", { transform: "translate(5, 0)", onClick: r, pointerEvents: "bounding-box", children: [(0, c.jsx)("rect", { dx: "0", dy: "0", rx: 4, ry: 4, width: "18", height: "18", fill: st.Qs.Success[500], style: { filter: "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1))", transition: "filter 0.2s ease-in-out", cursor: "pointer" }, onMouseEnter: e => { e.currentTarget.style.filter = "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3))" }, onMouseLeave: e => { e.currentTarget.style.filter = "drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1))" } }), (0, c.jsx)("g", { transform: "translate(2, 2)", pointerEvents: "none", children: (0, c.jsx)(l.me, { inlineSvg: !0, name: "check", variant: "light", fontSize: 14, color: "#fff" }) })] })] })] }) }; var jNe = n(10268);

                function VNe(e) { let { roleType: t, chartType: n, isMatrixRoot: r, isAiInsightsView: a = !1 } = e; if (a) return {
                        [Q.DQ.Top]: !1, [Q.DQ.Left]: !1, [Q.DQ.Right]: !1, [Q.DQ.Bottom]: !1 }; const o = {
                        [Q.DQ.Top]: !1, [Q.DQ.Left]: !0, [Q.DQ.Right]: !0, [Q.DQ.Bottom]: !0 }; return n === Fe.NB.Matrix && r && (o[Q.DQ.Top] = !1, o[Q.DQ.Bottom] = !1, o[Q.DQ.Left] = !1, o[Q.DQ.Right] = !1), t !== lt.T.Assistant && t !== lt.T.Embedded || (o[Q.DQ.Bottom] = !1), t === lt.T.Team && (o[Q.DQ.Left] = !1, o[Q.DQ.Right] = !1, o[Q.DQ.Top] = !0), t === lt.T.Function && (o[Q.DQ.Top] = !1, o[Q.DQ.Bottom] = !1), o } const ONe = e => { var t; let { id: n, hidePrintContextMenu: r = !1, innerCoords: i, role: l, width: s, height: d, inChain: u, isLeafCluster: h, clickToGotoPage: m = !1, onCLickGotoPage: p } = e, f = !1; "suggestion" in l && (f = l.suggestion.status !== HNe.TK.APPROVED); const { resourceAction: g } = (0, ie.useParams)(), { handleAcceptSuggestionFromRole: y, handleRejectSuggestionFromRole: w } = (0, jNe.n)(), z = (e => { const t = (0, A.d4)(k.yN),
                                { direct: n, recursive: r } = t(e),
                                a = (0, A.d4)(k.ap),
                                o = (0, A.d4)(k.MM),
                                i = (0, A.d4)(dt.A),
                                l = i === C.uI.PRINT || i === C.uI.PRINT_PREVIEW,
                                s = (0, A.d4)(ut.II),
                                c = (0, A.d4)(Ze),
                                d = s[e],
                                { newActionCardState: u } = (0, M.A)(),
                                [h] = u,
                                m = a.expandStyle,
                                p = a.position; return { direct: n, recursive: r, visible: o && (!m || m === U.$.Visible || p === U.d5.insideLeft || p === U.d5.insideRight || !p) || o && l || h === e && m === U.$.Hover && o, expandStyle: m, aliasVisible: d && (!c || c.roleId !== e) && (!m || m === U.$.Visible || h === e && m === U.$.Hover), position: a.position || U.d5.below } })(n), x = (e => { const t = (0, A.d4)(k.Fg),
                                n = (0, A.d4)(k.Fk),
                                r = (0, A.d4)(k.ap); return { collapsed: t(e), recursiveExpanded: n(e), expandStyle: r.expandStyle } })(n), { pageLinkVisible: S } = yNe(n), T = (null === l || void 0 === l || null === (t = l.children) || void 0 === t ? void 0 : t.length) || 0, { printSettings: { chartElements: { roleColors: H } } } = (0, it.A)(), { openDialog: L } = (0, v.A)("addMatrixHeaderDialog"), [O, R] = (0, a.useState)(!1), { onTourEventComplete: P } = (0, ot.M)({ featureTours: [] }), { openDialog: D } = (0, v.A)("newRole"), F = (0, a.useContext)(Ye.Y), { type: N, id: J } = l, te = (0, a.useRef)([]), oe = (0, a.useRef)(0), le = (0, A.wA)(), { cards: { spacing: se } } = C.Ay, ce = g === C.uI.AI_INSIGHTS, de = (0, A.d4)(k.J1), ue = (0, A.d4)(at.Jf), he = (0, gNe.A)(), me = !ce && (!he || (null === ue || void 0 === ue ? void 0 : ue.showRoleDetails)), pe = (0, A.d4)(Ke), fe = (0, A.d4)(qe), { openDialog: ge } = (0, v.A)("unsyncChart"), ye = (0, A.d4)(at.kw), be = ye && ye.syncEnabled, we = (0, A.d4)(Ge), ze = (0, A.d4)(I.oJ), xe = (0, A.d4)(j.NZ), { cards: { cardFrame: Ae } } = xe(N), { colorPosition: ke } = Ae, Se = (0, A.d4)(k.tG), Me = (0, A.d4)(Ne), Ee = (0, A.d4)((e => (0, Y.Yk)(e, { ids: (null === l || void 0 === l ? void 0 : l.members) || [] }))), Ce = (0, A.d4)(k.LH), Te = (0, A.d4)(_e), He = (0, A.d4)(j.y$), Le = (0, A.d4)(j.Nh), je = (0, A.d4)(Ue), { userHasMinAccess: Ve } = (0, Je.A)(), Oe = Ve(C.td.EDITOR), Re = Me === C.XD.MATRIX && !(null !== l && void 0 !== l && l.parent), Pe = (0, A.d4)(We), { openDialog: De } = (0, v.A)("newRoleSimple"), Fe = (0, j.K6)({ roleId: J, roleType: N, inChain: u }), Be = (0, A.d4)(k.YG), { cardData: Xe, topData: $e } = (0, A.d4)(Fe), tt = (0, A.d4)(k.Zd), nt = (0, A.d4)(E.uo), { handleAliasLinkClick: st, handleExpandNodeClick: ht, handleRecursiveExpandNodeClick: mt } = (e => { const t = (0, ie.useParams)(),
                                n = (0, ie.useHistory)(),
                                r = (0, A.wA)(),
                                a = (0, A.d4)(ut.II),
                                o = (0, A.d4)(k.Fk)(e); return { handleAliasLinkClick: r => { r && r.stopPropagation(); const o = a[e].id;
                                    n.push((0, vNe.si)({ ...t, chartId: o })) }, handleExpandNodeClick: t => { t && t.stopPropagation(), r((0, $.tE)({ roleId: e })) }, handleRecursiveExpandNodeClick: t => { t && t.stopPropagation(), r((0, $.Fc)({ roleId: e, collapsed: o })) } } })(J), pt = "printPreview" === nt || "print" === nt, { shared: [ft] } = Se[J] || { shared: [], direct: [] }, vt = N === lt.T.Shared, gt = ft === U.jo.Condensed && vt, yt = function() { let e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; const t = (0, A.d4)(E.uo),
                                n = !e && t !== C.uI.PRINT && t !== C.uI.PRINT_PREVIEW; return e => { if (n) return e } }(f), bt = (e => { let { roleType: t, roleId: n, members: r } = e; const { resourceAction: a } = (0, ie.useParams)(), o = (0, A.d4)(j.NZ), { photos: i } = o(t), l = (0, A.d4)(k.tG), { shared: [s] } = l[n] || { shared: [], direct: [] }, c = "condensed" === s, d = c && t === lt.T.Shared, { noPhoto: { visible: u }, vacant: { visible: h } } = i; let { position: m, standard: { visible: p, size: f }, shape: v } = i; return p ? (r.find((e => !(null === e || void 0 === e || !e.photo))) || (p = u), null !== r && void 0 !== r && r.length || (p = h), a === C.uI.AI_INSIGHTS && (p = !1, m === Q.yS.Above ? m = Q.yS.Center : m === Q.yS.AboveLeft ? m = Q.yS.Left : m === Q.yS.AboveRight && (m = Q.yS.Right)), t !== lt.T.Shared || c || (v === Q.qd.Fill && (v = Q.qd.Square), m === Q.yS.Above ? m = Q.yS.Center : m === Q.yS.AboveLeft ? m = Q.yS.Left : m === Q.yS.AboveRight && (m = Q.yS.Right)), d && (f = 30, m = Q.yS.Left, v = v === Q.qd.Fill ? Q.qd.Square : v), t === lt.T.Embedded && (p = !1), t === lt.T.Department && (p = !1), { size: f, placement: m, shape: v, visible: p }) : { size: f, visible: !1, shape: v, placement: m } })({ roleType: N, roleId: J, members: Ee }), wt = (e => { let { roleType: t, roleId: n } = e; const r = (0, A.d4)(k.tG),
                                { shared: [a] } = r[n] || { shared: [], direct: [] },
                                o = t === lt.T.Shared,
                                i = !(a === U.jo.Condensed) && o,
                                l = (0, A.d4)(E.uo),
                                s = "printPreview" === l || "print" === l,
                                c = (0, A.d4)(_e),
                                d = (0, A.d4)(j.y$),
                                u = (0, A.d4)(We),
                                h = (0, A.d4)(j.NZ),
                                { cards: { cardFrame: m } } = h(t),
                                { printSettings: { chartElements: { legend: { visible: p } } } } = (0, it.A)(),
                                f = ((null === c || void 0 === c ? void 0 : c.rules) || []).filter((e => { let { id: t } = e; const r = u[n]; if ("boolean" === typeof r[t]) return !0 === r[t]; const a = r[t]; return "object" === typeof a && !0 === a[n] })),
                                v = ((null === c || void 0 === c ? void 0 : c.rules) || []).filter((e => { const t = u[n]; if ("boolean" === typeof t[e.id]) return !1; const r = t[e.id]; return "object" === typeof r && Object.values(r).filter((e => !!e)).length > 0 })),
                                g = i && v.length,
                                y = f.length > v.length; return { placement: s && !p ? Q.aK.Hidden : f.length > 0 ? d.badgeBarPosition : Q.aK.Hidden, stroke: m.color, strokeWidth: m.thickness, badgeMap: u[n], badgeShape: d.badgeShape, legend: c, memberBadgeExists: g, roleBadgeExists: y } })({ roleType: N, roleId: J }), zt = Ce[J], xt = "#ffffff" === ((null === zt || void 0 === zt ? void 0 : zt.bgcolor) || "#ffffff").toLowerCase(), At = pt && !H || xt || xt ? U.U$.Hidden : (0, X.$)(ke), kt = wt.placement === Q.aK.Top && At === U.U$.Top, St = { placement: At, bgcolor: null === zt || void 0 === zt ? void 0 : zt.bgcolor, innerHeight: null !== zt && void 0 !== zt && zt.bgcolor && kt ? ee.z : 0 }, { chartControlState: Mt, dropZoneCardRef: Et, actionCardRef: Ct, newActionCardState: Tt, newDropZoneCardRef: Ht, visibleNodeRefs: Lt, backPageLinkRef: It } = (0, M.A)(), [, , jt] = null === Ct || void 0 === Ct ? void 0 : Ct.current, [Vt, Ot] = Tt, [, Rt] = Et.current, [, Pt] = Ht || [], { width: Dt } = tt[J] || { width: 100 }, { toggle: Ft } = (0, et.A)(Vt), [{ isOver: Nt }, _t] = (0, B.H)({ accept: [q.A.PERSON, q.A.ROLE], drop() { const { current: { itemClickOrDragCB: e } } = Mt; "function" === typeof e && e({ role: l }) }, collect: e => ({ isOver: e.isOver(), canDrop: e.canDrop() }) }), Bt = N === lt.T.Department && "label" === Le.displayType, Wt = ((null === Te || void 0 === Te ? void 0 : Te.rules) || []).filter((e => { let { id: t } = e; const n = Pe[J]; if ("boolean" === typeof n[t]) return !0 === n[t]; const r = n[t]; return "object" === typeof r && !0 === r[J] })), Ut = (() => { let e = 0; return St.placement === U.U$.Left && (e += ee.z), wt.placement === Q.aK.Left && (e += Wt.length > 0 ? ne.$ : 0), e })(), qt = (() => { let e = 0; return St.placement === U.U$.Top && (e += ee.z), wt.placement === Q.aK.Top && (e += ne.$), St.placement === U.U$.Top && wt.placement === Q.aK.Top && (e = ne.$), e })(), Gt = { backgroundColor: Ae.backgroundColor, stroke: Ae.color, strokeWidth: Ae.visible && Ae.thickness || 0, width: s - Ut, height: d - qt, radius: Ae.shape === re.Circle || Ae.shape === re.Oval ? 6 : 0, sharedDisplayDirection: ft }, Kt = [Q.yS.Above, Q.yS.AboveLeft, Q.yS.AboveRight, Q.yS.Center, Q.yS.TopLeft, Q.yS.TopRight].includes(bt.placement), Zt = Xe.reduce(((e, t) => { let { photoData: n } = t; const { value: r, avatarId: a, hidden: o } = n || {}, i = o || !r && !a ? 0 : bt.size; return e.push(i), e }), []).map((e => { const t = (() => { if (bt.shape === Q.qd.Fill) return kt && !xt && wt.roleBadgeExists ? ee.z : Gt.strokeWidth / 2; if (gt) return se.shared; if (!bt.visible || !e) return se.default; switch (bt.placement) {
                                        case Q.yS.AboveLeft:
                                        case Q.yS.AboveRight:
                                        case Q.yS.Above:
                                            return -se.photoAbove - qt;
                                        default:
                                            return se.default } })(),
                                n = (() => { switch (bt.placement) {
                                        case Q.yS.Left:
                                        case Q.yS.AboveLeft:
                                        case Q.yS.TopLeft:
                                            return bt.shape === Q.qd.Fill ? e / 2 : se.default+e / 2;
                                        case Q.yS.AboveRight:
                                        case Q.yS.TopRight:
                                        case Q.yS.Right:
                                            return bt.shape === Q.qd.Fill ? Dt - e / 2 - Ut : Dt - se.default-e / 2 - Ut;
                                        default:
                                            return (Dt - Ut) / 2 } })(),
                                r = bt.shape === Q.qd.Circle || bt.shape === Q.qd.Oval ? { cx: n, cy: t + e / 2, r: e / 2 } : null,
                                a = { offsetY: bt.shape !== Q.qd.Fill || Kt ? gt ? se.shared : bt.visible ? bt.visible && Kt ? t + e + se.default : bt.visible && Kt && t + e || t + se.text : se.default : se.text / 2, offsetX: (() => { if (!bt.visible) return se.default; if (bt.shape === Q.qd.Fill && bt.placement === Q.yS.Left) return se.photo + e; switch (bt.placement) {
                                            case Q.yS.AboveRight:
                                            case Q.yS.TopRight:
                                                return se.default;
                                            case Q.yS.Left:
                                                return e + se.default+se.photo;
                                            case Q.yS.AboveLeft:
                                            case Q.yS.TopLeft:
                                            case Q.yS.Right:
                                                return se.default;
                                            default:
                                                return Kt ? se.default : e + se.default+se.photo } })(), width: (() => { if (!bt.visible) return Dt - 2 * se.default-Ut; if (bt.shape === Q.qd.Fill && !Kt) return Dt - (se.photo + e + se.default) - Ut; if (bt.shape === Q.qd.Fill && Kt) return Dt - 2 * se.default-Ut; switch (bt.placement) {
                                            case Q.yS.AboveLeft:
                                            case Q.yS.TopLeft:
                                                return Dt - 2 * se.default-Ut;
                                            case Q.yS.Left:
                                                return Dt - e - 2 * se.default-se.photo - Ut;
                                            case Q.yS.AboveRight:
                                            case Q.yS.TopRight:
                                                return Dt - 2 * se.default-Ut;
                                            case Q.yS.Right:
                                                return Dt - e - 2 * se.default-se.photo - Ut;
                                            default:
                                                return !bt.visible || Kt ? Dt - 2 * se.default-Ut : Dt - e - 2 * se.default-se.photo } })() }; return { photoSize: e, cx: n, cy: t, r: e / 2, visible: bt.visible, clip: r, shape: bt.shape, dataParams: a } })), Yt = { memberIndex: 0, memberId: null, roleId: J, roleType: l.type }, Xt = gt ? { offsetY: se.default, offsetX: se.default, width: Dt - 2 * se.default-Ut } : { offsetY: 0, offsetX: 0, width: Dt }, $t = (0, a.useRef)(null);
                        (0, a.useLayoutEffect)((() => {
                            (() => { const { current: e } = te; let t = 0; for (const [n, r] of Zt.entries()) { const { photoSize: a, cy: o } = r; let i = vt ? se.shared : qt; const l = e[n] || 0;
                                    bt.visible ? (bt.shape === Q.qd.Fill ? [Q.yS.Left, Q.yS.Right].includes(bt.placement) ? i += Math.max(a + o, l + 2 * se.default) : (i += a, i += o + Gt.strokeWidth, i += l, i += 2 * se.default) : [Q.yS.Above, Q.yS.AboveLeft, Q.yS.AboveRight].includes(bt.placement) ? (i += 2 * se.default, i += a, i += o, i += l) : [Q.yS.Center, Q.yS.TopLeft, Q.yS.TopRight].includes(bt.placement) ? (i += a + se.default, i += 2 * se.default, i += l, gt && (i += se.shared + 2 * se.default)) : i += gt ? Math.max(a, l + 2 * se.default) : Math.max(2 * se.default+a, 2 * se.default+l), t = Math.max(i, t)) : (i += gt ? se.shared + se.default : 2 * se.default, i += l, t = Math.max(i, t)) } if (F(J, t), gt) { const e = oe.current + Xt.offsetY;
                                    F("".concat(J, "_top"), (vt ? qt : 0) + e) } else F("".concat(J, "_top"), (vt ? qt : 0) + (wt.memberBadgeExists ? se.default : 0)) })() }), [Xe, Te, je]), (0, a.useEffect)((() => () => { Lt.current[J] = 0, delete Lt[J] }), []); const Qt = Vt === J,
                            Jt = Qt && !f,
                            en = Oe && Qt,
                            [, tn] = (0, W.i)({ item: { type: q.A.ROLE, roleObj: l }, collect: e => ({ isDragging: e.isDragging(), handlerId: e.getHandlerId() }), end() { Mt.current = { type: "DEFAULT", dragItemType: "", item: null, itemClickOrDragCB: null, itemClickOrDragErrorCB: null, itemAssignOrCancelCB: null } } });
                        (0, a.useEffect)((() => { "function" === typeof Rt && Nt && Rt(J), "function" === typeof Pt && Nt && Pt(J) }), [Nt]), (0, a.useEffect)((() => { $t.current && clearTimeout($t.current), Be === l.id ? (R(!0), $t.current = setTimeout((() => { R(!1), le((0, $.Ic)()) }), 1e4)) : R(!1) }), [Be, l.id]); const nn = (e, t) => { if (e === lt.T.Team || e === lt.T.Function) { if (t === Q.DQ.Bottom) return Q.DQ.Right; if (t === Q.DQ.Top) return Q.DQ.Left } return t },
                            rn = [Q.yS.Above, Q.yS.AboveLeft, Q.yS.AboveRight].includes(bt.placement); return (0, c.jsxs)(c.Fragment, { children: [(0, c.jsx)(b, { role: l, additionalDisplayCondition: !r, children: (0, c.jsxs)(_, { innerRef: _t, onMouseOver: yt((() => { const { current: { item: e } } = Mt;
                                        null !== jt && void 0 !== jt && jt.current && clearTimeout(null === jt || void 0 === jt ? void 0 : jt.current), e || "function" === typeof Ot && Ot(null === l || void 0 === l ? void 0 : l.id) })), onMouseOut: yt((e => { const t = e.relatedTarget,
                                            n = null === t || void 0 === t ? void 0 : t.querySelectorAll("[data-id='moveRoleIcon']");
                                        null !== n && void 0 !== n && n.length || Ot(null) })), onClick: me ? m ? p : yt((e => { const { current: { type: t, item: n, itemClickOrDragCB: r, itemClickOrDragErrorCB: a } } = Mt; if ("TALENTPOOL" === t)
                                            if (K.b.includes(l.type)) V.A.trackEvent({ eventName: "TALENT_POOL_ASSIGN_CLICK" }), "function" === typeof r && r({ role: l, member: n });
                                            else { a({ message: "Cannot assign a person to the ".concat((0, G.Sn)((null === l || void 0 === l ? void 0 : l.type) || ""), ". You can assign people to role of type\n        ").concat(K.b.map((e => (0, G.Sn)(e))).join(","), ".") }) } else null === l || void 0 === l || !l.id || null !== l && void 0 !== l && l.id.includes("sampleRole") || [lt.T.Team, lt.T.Function].includes(null === l || void 0 === l ? void 0 : l.type) || (e && e.stopPropagation(), le((0, Z.gN)({ activePersonId: Ee[0], selectedRoleId: l.id, mode: "view" }))) })) : null, children: [ze && (0, c.jsx)(wNe, { w1: s, h1: d, roleId: l.id, isBackLink: !0, innerRef: It }), (0, c.jsxs)(X.o, { ColorBarProps: St, BadgeBarProps: wt, CardBoxProps: Gt, DataProps: Yt, PhotoProps: bt, children: [(0, c.jsx)(ae, { isLeafCluster: h && rn, width: s, height: d, hidden: Bt, id: "roleCard_".concat(null === l || void 0 === l ? void 0 : l.id), ColorBarProps: St, BadgeBarProps: wt, CardBoxProps: Gt, PhotoProps: bt, showEditActions: en, showActions: Qt, highlight: O || Nt, handleCreateRole: (an = null === l || void 0 === l ? void 0 : l.id, e => { e = (e => { switch (e) {
                                                        case Q.DQ.Left:
                                                            return "leftright" === pe ? Q.DQ.Top : e;
                                                        case Q.DQ.Right:
                                                            return "leftright" === pe ? Q.DQ.Bottom : e;
                                                        case Q.DQ.Top:
                                                            return "leftright" === pe ? Q.DQ.Right : e;
                                                        case Q.DQ.Bottom:
                                                            return "leftright" === pe ? Q.DQ.Left : e;
                                                        default:
                                                            return e } })(e), be ? ge({ chartId: we.id }) : [lt.T.Team, lt.T.Function].includes(null === l || void 0 === l ? void 0 : l.type) ? L({ itemType: null === l || void 0 === l ? void 0 : l.type, rel: { id: an, position: nn(l.type, e) } }) : (De({ rel: { id: an, position: e } }), P({ event: "role-card-new-role-btn--click" })) }), showAddButtons: VNe({ roleType: N, chartType: Me, isAiInsightsView: ce, isMatrixRoot: (null === l || void 0 === l ? void 0 : l.id) === (null === de || void 0 === de ? void 0 : de.id) }), children: (0, c.jsxs)(c.Fragment, { children: [Jt && (0, c.jsx)(rt, { showEditActions: en, CardBoxProps: Gt, PhotoProps: bt, handleEditClick: e => {
                                                        [lt.T.Team, lt.T.Function].includes(null === l || void 0 === l ? void 0 : l.type) ? L({ itemType: null === l || void 0 === l ? void 0 : l.type, role: l, mode: "edit" }) : D({ role: l, members: Ee }), e && e.stopPropagation() }, handleViewClick: e => { le((0, Z.gN)({ activePersonId: Ee[0], selectedRoleId: Vt, mode: "view" })), e && e.stopPropagation() }, handleMoreMenuClick: e => { Ft(e) }, innerRef: tn }), kt && (0, c.jsx)(ee.D, { CardBoxProps: Gt, BadgeBarProps: wt, ...St }), (0, c.jsx)(Qe, { data: null === $e || void 0 === $e ? void 0 : $e.fieldData, dataTopHeightRef: oe, dataParams: Xt, children: Xe.length ? (e, t) => Xe.map(((e, n) => { var r; let { photoData: l, fieldData: s, socialData: d, cardDisplay: u } = e; return (0, c.jsxs)(a.Fragment, { children: [(0, c.jsx)(o.A, { left: i && i[n] ? i[n][0] : 0, top: t + (i && i[n] ? i[n][1] : 0), children: (0, c.jsx)(Ie, { ColorBarProps: St, CardBoxProps: Gt, PhotoProps: bt, photoCoords: Zt[n], photoData: l, personId: null === (r = s["member.name"]) || void 0 === r ? void 0 : r.modelId, socialData: !gt && fe && (null === d || void 0 === d ? void 0 : d.linkedIn) && d, roleId: J, isShared: vt }) }), (0, c.jsx)(o.A, { top: t + (i && i[n] ? i[n][1] : 0), left: i && i[n] ? i[n][0] : 0, children: (0, c.jsx)(ve, { CardBoxProps: Gt, data: s, dataParams: Zt[n].dataParams, dataIndex: n, dataHeightRef: te, cardDisplay: u, isShared: vt, PhotoProps: bt, children: vt ? (e, t, r) => { var a; return (0, c.jsx)(ne.x, { ...wt, placement: He.badgeBarPosition, CardBoxProps: Gt, DataProps: { ...Yt, memberIndex: n, memberId: (null === (a = s["member.id"]) || void 0 === a ? void 0 : a.value) || "".concat(J, "_vacant_member_").concat(n) }, PhotoProps: bt, offsetX: e, offsetY: t, dataWidth: r, isNested: !0 }) } : () => null }) })] }, "basecardInner".concat(s["member.id"] || J, "-").concat(n)) })) : () => null }), (0, c.jsx)(ct, { CardBoxProps: Gt, ...z })] }) }), !Re && !S && T > 0 && (0, c.jsx)(fNe, { showRoleCount: z.visible && z.position === U.d5.below, mode: nt, frame: Ae, CountProps: z, CardBoxProps: Gt, ExpandProps: x, handleAliasLinkClick: yt(st), handleExpandClick: yt(ht), handleExpandAllClick: yt(mt), layout: pe, isHovering: Qt, roleId: J }), "embedded" === l.type && "print" !== nt && "printPreview" !== nt && (0, c.jsx)(kNe, { width: null === Gt || void 0 === Gt ? void 0 : Gt.width, top: null === Gt || void 0 === Gt ? void 0 : Gt.height, chartId: l.chart, embeddedChartId: l.embedded_chart }), (0, c.jsx)(TNe, { count: z.recursive, showCount: z.visible && z.position === U.d5.below, width: null === Gt || void 0 === Gt ? void 0 : Gt.width, top: null === Gt || void 0 === Gt ? void 0 : Gt.height, roleId: l.id })] })] }) }), f && (0, c.jsx)(INe, { role: l, CardBoxProps: Gt, handleAccept: e => { e.stopPropagation(), y(l) }, handleReject: e => { e.stopPropagation(), w(l) } })] }); var an },
                    RNe = () => { const e = (0, A.d4)(k.$o),
                            t = (0, a.useMemo)((() => (0, k.jS)(e)), [e]),
                            n = (0, A.d4)(t),
                            r = (0, A.d4)(k.Gr),
                            [i, l] = (0, a.useState)([]),
                            { managerPathRef: s, visibleNodeRefs: d } = (0, M.A)(),
                            u = (0, a.useMemo)((() => (0, k.dh)(e)), [e]),
                            h = (0, A.d4)(u),
                            m = (0, A.d4)(j.Qw),
                            { setPageNumber: p } = (0, it.A)(),
                            { left: f, h1: v } = h || {},
                            g = Math.max((i[0] || 100) - v, 0); let y = n.reduce(((e, t, r) => (e[t.id] = 0 !== r ? e[n[r - 1].id] - m - 16 : f - m - 16, r++, e)), {}); const b = n.map(((e, t) => { const n = i[t] || 0; let r = 48 - 16 * (t + 1) - g; return { w1: m, h1: n, left: y[e.id], top: r } })); return (0, a.useEffect)((() => { if (null !== n && void 0 !== n && n.length) { const e = (null === d || void 0 === d ? void 0 : d.current) || {},
                                    t = n.length ? n.length * (m + 16) + m / 2 : 0,
                                    r = b.reduce(((e, t) => { const n = (null === t || void 0 === t ? void 0 : t.top) || 0; return Math.min(n, e) }), 0),
                                    a = r < 0 ? Math.abs(r) + 96 : 0,
                                    o = n.map((t => e[null === t || void 0 === t ? void 0 : t.id] || 0));
                                l((e => { let t = !1; for (let [n, r] of (o || []).entries())
                                        if (e[n] !== r) { t = !0; break } return t ? o : e })), s.current = { topOffset: a, width: t } } })), (0, a.useEffect)((() => function() { s.current = { topOffset: 0, width: 0 } }), []), n.map(((e, t) => { let n = b[t]; return (0, c.jsx)(o.A, { left: n.left, top: n.top, children: (0, c.jsx)(ONe, { hidePrintContextMenu: !0, clickToGotoPage: !0, onCLickGotoPage: () => { p(r(null === e || void 0 === e ? void 0 : e.id)) }, inChain: !0, showPrintContextMenu: !1, width: n.w1, height: n.h1, role: e, isStacked: !1, topStackId: null }) }) })) }; var PNe = n(74079); const DNe = .15;

                function FNe(e) { var t; let { subchartId: n = C.Uz } = e; const r = (0, A.wA)(),
                        i = (0, A.d4)(k.Vs),
                        l = (0, A.d4)(E.uo),
                        s = (0, A.d4)(at.G0),
                        { chartContainerRef: d } = (0, M.A)(),
                        h = (0, A.d4)(PNe.A),
                        m = (0, A.d4)(k.bO),
                        p = (0, A.d4)(j.w$),
                        f = (0, A.d4)(I.Y1),
                        v = (0, A.d4)(k.AB),
                        g = (0, A.d4)(k.Xz),
                        y = (0, A.d4)(k.zZ),
                        b = (0, A.d4)(k.Es),
                        w = (0, A.d4)(k.J1),
                        z = (0, A.d4)(k.mw),
                        T = (0, A.d4)(k.sz),
                        L = (0, A.d4)(k.P1),
                        V = s === C.XD.MATRIX,
                        O = (0, A.d4)(bNe.kU),
                        R = (0, A.d4)(I.D9),
                        P = (0, a.useMemo)((() => { if (V && n === C.Uz) { let e = { ...L, ...T }; return null !== w && void 0 !== w && w.id && (e[null === w || void 0 === w ? void 0 : w.id] = z), e } return m(n) }), [m, n, L, T]),
                        D = (0, a.useMemo)((() => v(n)), [n, P]); let F = {},
                        N = [];
                    V ? (N = D, F = P) : s === C.XD.TRADITIONAL && (N = g, F = i); const _ = (null === (t = F) || void 0 === t ? void 0 : t.root) || { w0: 0, h0: 0 },
                        B = (0, A.d4)(k.Qu),
                        W = (0, A.d4)(k.Ty),
                        U = (0, A.d4)(k.of),
                        q = (0, A.d4)(k.WD),
                        G = (0, a.useMemo)((() => N.filter((e => !U.includes(e)))), [U, N]),
                        K = (0, a.useMemo)((() => U.filter((e => N.includes(e)))), [U, N]),
                        Z = e => { var t; const n = q(e); return (null === n || void 0 === n || null === (t = n.roleCluster) || void 0 === t ? void 0 : t.recursiveFilterCount) || 0 },
                        Y = (0, a.useCallback)((e => l !== C.uI.PRINT && l !== C.uI.PRINT_PREVIEW && (-1 !== B.indexOf(e) || -1 !== W.indexOf(e))), [l, W, B]),
                        X = e => (t, n) => { const r = F[t],
                                a = h[t] || {}; if (!r || "hidden" === (null === a || void 0 === a ? void 0 : a.type)) return null; return Y(t) ? (0, c.jsx)(H, { isLastExpanded: B.includes(t), parentDims: F[null === a || void 0 === a ? void 0 : a.parent] || _, roleDims: F[t], children: r => (0, c.jsx)(o.A, { left: r.x, top: r.y, children: e(t, n) }) }, "cardGroupTransitionContainer_".concat(t)) : (0, c.jsx)(o.A, { left: r.left, top: r.top, children: e(t, n) }, "cardGroupDimContainer_".concat(t, "_").concat(n)) }; return (0, c.jsxs)(c.Fragment, { children: [f && (0, c.jsx)(RNe, {}), K.map(X((e => (0, c.jsx)(u, { handleFilterExpandClick: () => (e => { r((0, S.w9)({ role: h[e] })) })(e), recursiveFilterCount: Z(e), bgColor: p }, "filter-node-".concat(e))))), G.map(X((e => { const t = F[e],
                                n = h[e]; if (t && n && (null === n || void 0 === n ? void 0 : n.type) !== C.mv.HIDDEN) return (e => { const t = d.current,
                                    { offsetWidth: n } = t || {},
                                    r = (n || 0) / (_.w0 || 1),
                                    a = F[e] || {},
                                    o = h[e],
                                    i = b[e] || {}; var s; return l !== C.uI.PRINT_PREVIEW || r > DNe || O >= DNe / r || l === C.uI.PRINT_PREVIEW && null !== R && void 0 !== R && R.auto && O > DNe ? (0, c.jsx)(ONe, { id: e, role: o, width: a.w1, height: a.h1, innerCoords: a.innerCoords, isLeafCluster: 0 === (null === i || void 0 === i || null === (s = i.clusters) || void 0 === s ? void 0 : s.length) }, "eventable-node-".concat(e)) : (0, c.jsx)(x, { role: o, width: a.w1, height: a.h1 }, "empty-node-".concat(e)) })(e) }))), y.map(X((e => { var t, n; return (0, c.jsx)(x, { width: null === (t = F[e]) || void 0 === t ? void 0 : t.w1, height: null === (n = F[e]) || void 0 === n ? void 0 : n.h1 }, "hidden-node-".concat(e)) })))] }) } }, 76345: (e, t, n) => { "use strict";
                n.d(t, { V: () => d }); var r = n(65043),
                    a = n(42531),
                    o = n(13130);

                function i(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { if ("undefined" === typeof Symbol || !(Symbol.iterator in Object(e))) return; var n = [],
                            r = !0,
                            a = !1,
                            o = void 0; try { for (var i, l = e[Symbol.iterator](); !(r = (i = l.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0); } catch (s) { a = !0, o = s } finally { try { r || null == l.return || l.return() } finally { if (a) throw o } } return n }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return l(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return l(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

                function l(e, t) {
                    (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r } var s = n(10621),
                    c = n(70579); const d = () => { const { isDragging: e, dragOffset: t, item: n } = function(e) { var t = (0, a.u)().getMonitor(),
                            n = i((0, o.F)(t, e), 2),
                            l = n[0],
                            s = n[1]; return (0, r.useEffect)((function() { return t.subscribeToOffsetChange(s) })), (0, r.useEffect)((function() { return t.subscribeToStateChange(s) })), l }((e => ({ isDragging: e.isDragging(), dragOffset: e.getSourceClientOffset(), item: e.getItem() }))); if ("role" !== (null === n || void 0 === n ? void 0 : n.type)) return null; if (!e || !t) return null; const l = (0, s.mA)(null === n || void 0 === n ? void 0 : n.roleObj); return (0, c.jsx)("div", { style: e ? { transform: "translate(".concat(null === t || void 0 === t ? void 0 : t.x, "px, ").concat(t.y, "px)"), position: "fixed", top: 0, left: 0, pointerEvents: "none", display: "flex", padding: 8, border: "solid 1px #ccc", background: "#ffffff", borderRadius: 4, maxWidth: 100 } : { display: "none", visibility: "hidden" }, children: l }) } }, 39659: (e, t, n) => { "use strict";
                n.d(t, { Z: () => ie, A: () => le }); var r, a = n(65043),
                    o = n(14556),
                    i = n(78396),
                    l = n(89656),
                    s = n(23993),
                    c = n(2267),
                    d = n(22264),
                    u = n(57528),
                    h = n(52996),
                    m = n(75156),
                    p = n(72119),
                    f = n(69219),
                    v = n(70579); const g = (0, p.Ay)(h.A)(r || (r = (0, u.A)(["\n  .borderedRect {\n  }\n  .addIcon {\n  }\n  :hover {\n    .borderedRect {\n      :hover {\n        stroke-width: 2px;\n        stroke-dasharray: 5, 5;\n      }\n    }\n  }\n"]))),
                    y = e => { let { handleClick: t, left: n, top: r, width: a = 100, height: l = 100 } = e; const s = (0, o.d4)(f.uo); return (0, v.jsxs)(g, { left: n, top: r, onClick: s === i.uI.DEFAULT ? t : () => {}, pointerEvent: "fill", cursor: "pointer", children: [(0, v.jsx)("rect", { width: a, height: l, stroke: "gray", fill: "white", strokeDasharray: "2,2", rx: 20, className: "borderedRect" }), (0, v.jsx)(h.A, { top: (l - 48) / 2, left: (a - 48) / 2, className: "addIcon", pointerEvent: "fill", cursor: "pointer", children: (0, v.jsx)(m.me, { inlineSvg: !0, name: "plus", color: "gray", variant: "light", fontSize: 48 }) })] }) }; var b = n(84),
                    w = n(12987),
                    z = n(43862),
                    x = n(10242),
                    A = n(65453),
                    k = n(24259),
                    S = n(30391),
                    M = n(72629),
                    E = n(82372),
                    C = n(24251),
                    T = n(91688),
                    H = n(39179),
                    L = n(34336),
                    I = n(42197),
                    j = n(48853); const V = e => { let { onClick: t, dims: n, textProps: r } = e; const { userHasMinAccess: a } = (0, j.A)(), o = a(i.td.EDITOR); return (0, v.jsxs)(h.A, { onClick: o ? t : () => {}, left: null === n || void 0 === n ? void 0 : n.left, top: null === n || void 0 === n ? void 0 : n.top, pointerEvents: "fill", cursor: "pointer", children: [(0, v.jsx)("rect", { width: n.w1, height: n.h1, stroke: I.A.palette.default.main, fill: "white", rx: 10 }), (0, v.jsx)(L.A, { ...r, width: n.w1, x: n.w1 / 2, y: n.h1 / 3, children: o ? "Add Matrix Leader" : "No Matrix Leader" }), o && (0, v.jsx)(h.A, { top: n.h1 / 2, left: (n.w1 - 16) / 2, children: (0, v.jsx)(m.me, { inlineSvg: !0, name: "plus", variant: "light", fontSize: 16 }) })] }) }; var O = n(85510),
                    R = n(72613),
                    P = n(24657),
                    D = n(19367); const F = () => { const e = (0, o.d4)(D.Qn),
                        t = (0, o.d4)(s.v7),
                        n = (0, o.d4)(s.q8),
                        r = (0, o.d4)(s.iR),
                        a = (0, o.d4)(s.Lj),
                        l = (0, o.d4)(s.J1),
                        c = null === l || void 0 === l ? void 0 : l.id,
                        { openDialog: d } = (0, b.A)("addMatrixHeaderDialog"),
                        u = e => () => { let t, n, o, l; if (e === i.mv.FUNCTION) { var s, u; if (n = r[r.length - 1], null !== (s = n) && void 0 !== s && s.id) o = null === (u = n) || void 0 === u ? void 0 : u.id, l = "right";
                                else c ? (o = c, l = "below") : (o = i.Uz, l = "below");
                                t = { id: o, position: l } } else if (e === i.mv.TEAM) { const e = a[a.length - 1];
                                null !== e && void 0 !== e && e.id ? (o = null === e || void 0 === e ? void 0 : e.id, l = "right") : c ? (o = c, l = "below") : (o = i.Uz, l = "below"), t = { id: o, position: l } } d({ itemType: e, rel: t }) }; return (0, v.jsxs)(v.Fragment, { children: [(0, v.jsx)(R.A, {}), (0, v.jsx)(P.Ay, { label: "".concat(e.matrixSideLabel), ...t, handleClick: u(i.mv.TEAM) }), (0, v.jsx)(P.Ay, { label: "".concat(e.matrixTopLabel), ...n, handleClick: u(i.mv.FUNCTION) })] }) }; var N = n(97067),
                    _ = n(93865),
                    B = n(9727),
                    W = n(52527),
                    U = n(96364),
                    q = n(79071),
                    G = n(22818),
                    K = n(356),
                    Z = n(97626); const Y = () => { const { userHasMinAccess: e } = (0, j.A)(), { resourceAction: t } = (0, T.useParams)(), n = t === i.uI.AI_INSIGHTS, [r, c] = (0, a.useState)(null), [u, h] = (0, a.useState)(null), [m, p] = (0, a.useState)(!1), { dropZoneCardRef: f, chartControlState: g, newDropZoneCardRef: y } = (0, d.A)(), [w, z] = y || [], x = (0, o.d4)(D.LL), A = (0, o.d4)(s.P_), k = (0, o.d4)(D.LL), S = (0, o.d4)((e => (0, s.gn)(e.roles, r))) || {}, { item: M, dragItemType: E } = g.current, C = (null === M || void 0 === M ? void 0 : M.id) !== (null === S || void 0 === S ? void 0 : S.id), { type: H } = S || {}, { type: L } = M || {}, I = Z.b.includes(H), V = ((e, t) => i.mv.FUNCTION === e ? i.mv.FUNCTION === t : i.mv.TEAM === e ? i.mv.TEAM === t : [i.mv.SINGLE, i.mv.ASSISTANT, i.mv.DEPARTMENT, i.mv.LOCATION, i.mv.SHARED].includes(t))(L, H), { cards: { cardFrame: O } } = (0, o.d4)((e => (0, l.FV)(e, H))), { shape: R } = O, { openDialog: P } = (0, b.A)("newRole"), [{ canDrop: F, isOver: Y }, X] = (0, _.H)({ accept: [G.A.PERSON, G.A.ROLE], drop(e) { if (e.type === G.A.PERSON) { const { current: { itemClickOrDragCB: e } } = { ...g };
                                F && Y && I && (K.A.trackEvent({ eventName: "TALENT_POOL_ASSIGN_DRAG" }), "function" === typeof e && e({ role: S })) } }, collect: e => ({ isOver: e.isOver(), canDrop: e.canDrop() }) });
                    (0, a.useEffect)((() => { Y || ("function" === typeof c && c(null), "function" === typeof z && z(null)) }), [Y]); const $ = e => t => async n => { let { members: r } = n; if (E === G.A.PERSON) P({ rel: { id: t, position: e }, members: r, isTalentPoolDrop: !0 });
                        else if (E === G.A.ROLE) { const { current: { itemAssignOrCancelCB: n } } = g;
                            await n({ rel: { id: t, position: e }, draggedRole: M }) } };
                    (0, a.useEffect)((() => { f.current = [r, c] }), [r]); const Q = (0, a.useMemo)((() => { let e = A[w]; return e ? { width: e.w1, height: e.h1, top: e.top, left: e.left } : {} })),
                        J = r && Q && V; if (!J) return null; const ee = x <= 1.2 ? "lg" : x <= 1.6 ? "x2" : "x3"; let te = !m; const ne = 200 - (null === Q || void 0 === Q ? void 0 : Q.width),
                        re = 120 - (null === Q || void 0 === Q ? void 0 : Q.height);
                    Q.width < 200 && (Q.left -= ne / 2, Q.width = 200), Q.height < 120 && (Q.top -= re / 2, Q.height = 120); let ae, oe = "5px"; if ("square" === R) oe = "0px";
                    else oe = "5px";
                    E === G.A.ROLE ? (ae = "Drop over the plus button", te = !1) : ae = I ? u ? "Drop to insert role ".concat(u) : "Drop to assign to role" : "Cannot assign to ".concat(H || "this role"), C || (ae = "Drop onto the plus button of the target role."); const ie = 15 * k > 8 ? 15 * k : 8; return J && (0, v.jsx)("foreignObject", { x: k * (null === Q || void 0 === Q ? void 0 : Q.left), y: k * (null === Q || void 0 === Q ? void 0 : Q.top), width: k * (null === Q || void 0 === Q ? void 0 : Q.width), height: k * (null === Q || void 0 === Q ? void 0 : Q.height) + 2 * N.lw, children: (0, v.jsxs)(W.A, { width: k * (null === Q || void 0 === Q ? void 0 : Q.width), height: k * (null === Q || void 0 === Q ? void 0 : Q.height), left: 0, top: 0, borderRadius: oe, showBorder: te, isRoleTypeDroppable: I, ref: X, children: [(0, v.jsx)(B.A, { height: null === Q || void 0 === Q ? void 0 : Q.height, children: (0, v.jsx)(U.A, { weight: "bold", color: "#fff", fontSize: ie, children: ae }) }), e(i.td.EDITOR) && C && (0, v.jsxs)(v.Fragment, { children: [!n && H !== i.mv.FUNCTION && (0, v.jsx)(q.A, { size: ee, icon: "Add", align: "top", direction: "above", handleCreateRoleFromDrop: $(H === i.mv.TEAM ? "left" : "above")(r), role: S, handleIsOver: p, handleAlign: h }), !n && H !== i.mv.TEAM && (0, v.jsx)(q.A, { size: ee, icon: "Add", align: "left", direction: "on left", handleCreateRoleFromDrop: $("left")(r), role: S, handleIsOver: p, handleAlign: h }), H !== i.mv.FUNCTION && (0, v.jsx)(q.A, { size: ee, icon: "Add", align: "bottom", direction: "below", handleCreateRoleFromDrop: $(H === i.mv.TEAM ? "right" : "below")(r), role: S, handleIsOver: p, handleAlign: h }), !n && H !== i.mv.TEAM && (0, v.jsx)(q.A, { size: ee, icon: "Add", align: "right", direction: "on right", handleCreateRoleFromDrop: $("right")(r), role: S, handleIsOver: p, handleAlign: h })] })] }) }) }; var X = n(93949),
                    $ = n(89015),
                    Q = n(44938); const J = () => { const e = (0, o.d4)(s.k3),
                        t = (0, o.d4)(l.y2); return e.map(((e, n) => (0, v.jsx)(Q.A, { data: e, percent: e.percent, children: n => { let { path: r } = n; return (0, v.jsx)($.A, { d: r(e), options: t }) } }, "chart-line-root-".concat(n)))) }; var ee = n(58481),
                    te = n(57546),
                    ne = n(66856),
                    re = n(10621),
                    ae = n(55679),
                    oe = n(76345); const ie = 140,
                    le = () => { const e = (0, T.useHistory)(),
                            t = (0, T.useParams)(),
                            { chartId: n, resourceAction: r } = t,
                            { openDialog: a } = (0, b.A)("newRole"),
                            { chartContainerRef: u, chartSvgRef: m, topGroupRef: p, newActionCardState: g, dropZoneCardRef: L } = (0, d.A)(),
                            [, I] = L.current,
                            [, P] = g,
                            D = (0, o.d4)(f.uo),
                            _ = (0, o.d4)(l.w$),
                            B = (0, o.d4)(s.bO),
                            W = (0, o.d4)(s.yN),
                            U = (0, o.wA)(),
                            q = (0, o.d4)(s.J1),
                            G = (0, o.d4)(s.lA),
                            K = (0, o.d4)(s.iR),
                            Z = (0, o.d4)(s.Lj),
                            $ = (0, o.d4)(l.Qw),
                            Q = (0, o.d4)(s.YI),
                            ie = (0, o.d4)(s.fv),
                            le = (0, o.d4)(s.eK),
                            se = (0, o.d4)(s.WD),
                            ce = (0, o.d4)(s.ox),
                            de = (0, o.d4)(s.tc),
                            { prevChartId: ue } = (0, te.A)(),
                            { userHasMinAccess: he } = (0, j.A)();
                        (0, A.A)(), (0, k.A)(), (0, S.A)(), (0, M.A)(), (0, E.A)();
                        (0, C.A)(D !== i.uI.PUBLIC ? ["print", "zoom"] : ["zoom"]); const me = $,
                            pe = e => { let t = ce[e],
                                    n = (G || []).filter((t => (null === t || void 0 === t ? void 0 : t.teamId) === e)); if (n && null !== n && void 0 !== n && n.length)
                                    for (let r of n || []) { const n = se(null === r || void 0 === r ? void 0 : r.id),
                                            a = null === n || void 0 === n ? void 0 : n.roleCluster,
                                            o = (null === a || void 0 === a ? void 0 : a.nodes) || [];
                                        (null === r || void 0 === r ? void 0 : r.teamId) === e && U((0, z.tE)({ roleId: null === r || void 0 === r ? void 0 : r.id, clusterNodes: o, forceExpand: t })) } else U((0, z.tE)({ roleId: e, clusterNodes: [] })) },
                            fe = (e, t, r) => (0, v.jsx)(v.Fragment, { children: (0, v.jsx)(h.A, { id: "".concat(e, "_").concat(t, "_col"), left: me + N.lw, top: 140, children: Z.map((t => { var o, l; let { id: s } = t; const c = (Q[e] || {})[s],
                                            d = ie[e].left - me,
                                            u = le[s].top - 140,
                                            m = ie[e].w0,
                                            p = le[s].h0; let f, g = 0; if (null !== c && void 0 !== c && c.id) { f = B(null === c || void 0 === c ? void 0 : c.id)[null === c || void 0 === c ? void 0 : c.id]; const { recursive: e } = W(null === c || void 0 === c ? void 0 : c.id);
                                            g = e || 0 } const b = (m - (null === (o = f) || void 0 === o ? void 0 : o.w0)) / 2,
                                            w = ce[s]; return (0, v.jsxs)(h.A, { id: "".concat(e, "_").concat(s, "_cell_CG"), left: d, top: u, children: [(0, v.jsx)(O.A, { cellWidth: m, cellHeight: p, isCellCollapsed: w, handleClick: () => { pe(s) }, label: (0, re.mA)(r), recursiveRoleCount: g }), !w && (0, v.jsxs)(h.A, { top: O.r, id: "".concat(e, "_").concat(s, "_chart_nodes_CG"), children: [c && (0, v.jsxs)(h.A, { left: b, children: [(0, v.jsx)(H.A, { subchartId: null === c || void 0 === c ? void 0 : c.id }), (0, v.jsx)(R.A, { subchartId: null === c || void 0 === c ? void 0 : c.id })] }), !(null !== c && void 0 !== c && null !== (l = c.children) && void 0 !== l && l.length) && !ce[s] && he(i.td.EDITOR) && (0, v.jsx)(y, { handleClick: () => {
                                                        (async e => { let t, { functionId: r, teamId: o, cellRoot: i } = e;
                                                            t = null !== i && void 0 !== i && i.id ? { id: (null === i || void 0 === i ? void 0 : i.id) || n, position: "below" } : { chart: n, position: "below" }, a({ role: { type: "single", functionId: r, teamId: o }, rel: t }) })({ functionId: e, teamId: s, cellRoot: c }) }, id: "".concat(e, "_").concat(s, "_empty_cell"), top: 0, left: 0, width: ie[e].w0, height: le[s].h0 - O.r })] })] }) })) }) }),
                            ve = (0, o.d4)(s.mw); return (0, v.jsx)(x.A, { children: (0, v.jsxs)(w.A, { id: "chartExterior", allowScroll: !0, ref: u, bgcolor: _, onClick: () => { "function" === typeof P && P(null), "function" === typeof I && I(null) }, children: [ue && "theme" !== r && "settings" !== r && (0, v.jsx)(ee.A, { variant: "outlined", color: "primary", onClick: () => e.push((0, ne.si)({ ...t, chartId: ue })), children: "Back To Chart" }), (0, v.jsxs)(c.A, { innerRef: m, id: "chartSvg", className: "chart", width: 2500, height: 2e3, children: [(0, v.jsxs)("g", { ref: p, children: [de && !(null !== q && void 0 !== q && q.id) && (0, v.jsx)(V, { onClick: () => { a({ rel: { id: "root", position: "level" } }) }, dims: ve, textProps: { verticalAnchor: "start", textAnchor: "middle", fill: "gray" } }), (0, v.jsx)(J, {}), (0, v.jsx)(X.A, { handleExpandNodeClick: pe, teamRowCollapseMap: ce }), (0, v.jsx)(F, {}), K.map(((e, t) => { let { id: n, ...r } = e; return fe(n, t, r) }))] }), (0, v.jsx)(Y, {})] }), (0, v.jsx)(ae.A, { readOnly: !1 }), (0, v.jsx)(oe.V, {})] }) }) } }, 93949: (e, t, n) => { "use strict";
                n.d(t, { A: () => f, J: () => p }); var r = n(85510),
                    a = n(42197),
                    o = n(39659),
                    i = n(14556),
                    l = n(23993),
                    s = n(75156),
                    c = n(52861),
                    d = n(89015),
                    u = n(44938),
                    h = n(89656),
                    m = n(70579); const p = 20,
                    f = e => { let { handleExpandNodeClick: t } = e; const n = (0, i.d4)(l.Lj),
                            f = (0, i.d4)(l.eK),
                            v = (0, i.d4)(l.$V),
                            g = (0, i.d4)(l.ox),
                            y = (0, i.d4)(h.y2); return n.map((e => { let { id: n } = e; const i = f[n].top,
                                l = f[n].h0,
                                h = g[n],
                                b = { source: { x: 0, y: l / 2 }, target: { x: v, y: l / 2 }, percent: 1 }; return (0, m.jsxs)(c.A, { left: o.Z, top: i + r.r / 2, pointerEvents: "auto", cursor: "pointer", children: [g[n] ? (0, m.jsx)(u.A, { data: b, percent: b.percent, children: e => { let { path: t } = e; return (0, m.jsx)(d.A, { d: t(b), options: { ...y, thickness: 2 } }) } }, "chart-line-root-".concat(n, "-").concat(h)) : (0, m.jsx)("rect", { x: 0, y: 0, width: v, height: l, fill: "transparent", stroke: a.A.palette.primary.main, strokeDasharray: "3,3", rx: "10px" }), (0, m.jsxs)(c.A, { top: (l - p) / 2, left: -p / 2, pointerEvents: "fill", cursor: "pointer", onClick: () => { t(n) }, children: [(0, m.jsx)("rect", { width: p, height: p, rx: 4, fill: a.A.palette.secondary.main }), (0, m.jsx)(s.me, { inlineSvg: !0, color: a.A.palette.secondary.contrastText, fontSize: p, name: g[n] ? "plus" : "minus", variant: "solid" }, "icon-".concat(n, "-").concat(g[n]))] })] }) })) } }, 79071: (e, t, n) => { "use strict";
                n.d(t, { A: () => p }); var r = n(65043),
                    a = n(75156),
                    o = n(93865),
                    i = n(22264),
                    l = n(35276),
                    s = n(22818),
                    c = n(14556),
                    d = n(19367),
                    u = n(84),
                    h = n(70579); const m = e => { let { align: t, size: n, direction: a, ...m } = e; const { chartControlState: p } = (0, i.A)(), f = (0, c.d4)(d.kw), v = (0, c.d4)(d.Mk), { openDialog: g } = (0, u.A)("unsyncChart"), y = f && f.syncEnabled, { item: b, itemAssignOrCancelCB: w } = p.current, [{ isOver: z }, x] = (0, o.H)({ accept: [s.A.PERSON, s.A.ROLE], drop(e) { m.handleCreateRoleFromDrop({ members: [b] }), e.type === s.A.PERSON && w() }, collect: e => ({ isOver: e.isOver() }), canDrop(e) { if (!y) return !0; const t = null === e || void 0 === e ? void 0 : e.roleObj,
                                    n = (null === m || void 0 === m ? void 0 : m.role) || {},
                                    r = "on right" === a,
                                    o = "on left" === a,
                                    i = t.parent === (null === n || void 0 === n ? void 0 : n.parent),
                                    l = t.parent === n.id,
                                    s = i && (r || o) || l && "below" === a; return s || (g({ chartId: null === v || void 0 === v ? void 0 : v.id }), !1) } }); return (0, r.useEffect)((() => { m.handleIsOver(z), z ? m.handleAlign(a) : m.handleAlign(null) }), [z]), (0, h.jsx)(l.e, { dropRef: x, align: t, size: n, ...m, isOver: z, position: "absolute" }) },
                    p = e => { let { size: t, icon: n, align: r, ...o } = e; return (0, h.jsx)(m, { size: t, align: r, ...o, children: (0, h.jsx)(a.Ay, { icon: n, size: t }) }, "".concat(n, "-").concat(t)) } }, 53109: (e, t, n) => { "use strict";
                n.d(t, { A: () => C, Ay: () => T, DX: () => A, ET: () => x, Kj: () => z, WZ: () => v, Ww: () => M, cK: () => k, jJ: () => s, jv: () => S, ke: () => b, nF: () => y, pW: () => w, te: () => h, wz: () => m, yX: () => g }); var r = n(80907),
                    a = n(9787),
                    o = n(47730),
                    i = n(37091); const l = "chart"; let s = function(e) { return e.all = "all", e.high = "high", e.medium = "medium", e.low = "low", e }({}); const c = { mode: "suggestions", suggestions: [], pageInfo: { totalDocs: 0, limit: 0, totalPages: 0, page: 0, pagingCounter: 0, hasPrevPage: !1, hasNextPage: !1, prevPage: null, nextPage: 0 } },
                    d = { status: i.TK.PENDING, confidence: "all" },
                    u = { zoom: 1, printPreviewZoom: 1, info: { refreshDynamicFieldValues: !1 }, chartTemplates: {}, visibleContainerWidth: 0, visibleQuadrant: 0 },
                    h = (0, a.a)({ slice: l, scope: "charts" }),
                    m = (0, o.B)({ slice: l, scope: "charts" }),
                    p = .2,
                    f = (0, r.Z0)({ name: l, initialState: u, reducers: { updateVisibleChartQuadrant: (e, t) => { e.visibleQuadrant = t.payload }, updateVisibleChartWidth: (e, t) => { e.visibleContainerWidth = t.payload }, printPreviewZoomExact: (e, t) => { e.printPreviewZoom = Math.min(Math.max(t.payload || 1, p), 2) }, printPreviewZoomIn: e => { e.printPreviewZoom = Math.min(e.printPreviewZoom + .2, 2) }, printPreviewZoomOut: e => { e.printPreviewZoom = Math.max(e.printPreviewZoom - .2, p) }, zoomIn: e => { e.zoom = Math.min(e.zoom + .1, 2) }, zoomOut: e => { e.zoom = Math.max(e.zoom - .1, p) }, zoomExact: (e, t) => { e.zoom = Math.min(Math.max(t.payload || 1, p), 2) }, setSampleTheme: (e, t) => { e.info.theme = t.payload }, loadSampleChart: (e, t) => ({ ...u, info: { ...t.payload.chart } }), updateLegendsWithNewFolder: (e, t) => { e.legendFolders = t.payload }, updateInsightFilters: (e, t) => { e.insights || (e.insights = { ...c }), e.insights.filters = { ...d, ...e.insights.filters || {}, ...t.payload } }, updateInsightsMode: (e, t) => { e.insights || (e.insights = { ...c }), e.insights.mode = t.payload }, "get/pending": () => ({ ...u }), "get/fulfilled": (e, t) => { const { chart: n } = t.payload || {}, { sharePermission: r, ...a } = n || {}; return { ...u, info: { ...a }, sharePermission: r } }, "update/fulfilled": (e, t) => { const n = { ...t.payload.chart },
                                    r = n.legend; var a, o;
                                r && "string" === typeof r && ((null === (a = e.info) || void 0 === a || null === (o = a.legend) || void 0 === o ? void 0 : o.id) === r && (n.legend = e.info.legend)); return { ...e, info: { ...n } } }, "create/fulfilled": (e, t) => ({ ...u, info: { ...t.payload.chart } }), "create/rejected": () => ({ ...u }), "getChartTemplates/fulfilled": (e, t) => ({ ...e, chartTemplates: t.payload.chartTemplates }), "getChartIntegration/fulfilled": (e, t) => ({ ...e, integration: t.payload.chartIntegration }), "migrateFormat/fulfilled": (e, t) => { const { theme: n } = t.payload; return { ...e, info: { ...e.info, theme: (null === n || void 0 === n ? void 0 : n.id) || e.info.theme, v6FormatMigrated: !0 } } }, "refreshDynamicFieldValues/fulfilled": (e, t) => { var n;
                                e.info.id === (null === (n = t.payload) || void 0 === n ? void 0 : n.chartId) && (e.info.refreshDynamicFieldValues = !1) }, "getChartInsights/fulfilled": (e, t) => { var n, r, a;
                                e.insights = { mode: (null === (n = e.insights) || void 0 === n ? void 0 : n.mode) || "suggestions", suggestions: (null === (r = t.payload) || void 0 === r ? void 0 : r.suggestions) || [], pageInfo: null === (a = t.payload) || void 0 === a ? void 0 : a.pageInfo } }, "runChartInsights/fulfilled": (e, t) => { var n, r;
                                e.insights = { mode: "suggestions", suggestions: (null === (n = t.payload) || void 0 === n ? void 0 : n.suggestions) || [], pageInfo: null === (r = t.payload) || void 0 === r ? void 0 : r.pageInfo } }, "updateChartSuggestion/fulfilled": (e, t) => { var n; const r = t.payload.suggestions; if (null !== (n = e.insights) && void 0 !== n && n.suggestions)
                                    for (let a = 0; a < r.length; a++) { const t = r[a],
                                            n = e.insights.suggestions.findIndex((e => (null === e || void 0 === e ? void 0 : e.id) === (null === t || void 0 === t ? void 0 : t.id)));
                                        n > -1 && (e.insights.suggestions[n] = t) } } }, extraReducers: { "organization/get/pending": () => ({ ...u }), "legend/createLegend/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/updateLegend/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/addUpdateRule/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/updateFolderName/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/deleteRule/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/updateLegendRulePositionAndFolderName/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/deleteFolderName/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "legend/deleteFolder/fulfilled": (e, t) => { e.info.legend = { ...t.payload.legend } }, "themes/setActive/fulfilled": (e, t) => { e.info.theme = t.payload.theme.id }, "themes/delete/fulfilled": (e, t) => { var n; const r = (null === t || void 0 === t || null === (n = t.payload) || void 0 === n ? void 0 : n.themeId) || "";
                                e.info.theme === r && (e.info.theme = null) }, "import/updateChartIntegration/fulfilled": (e, t) => { var n; const r = null === e || void 0 === e || null === (n = e.info) || void 0 === n ? void 0 : n.chartIntegration,
                                    a = null === e || void 0 === e ? void 0 : e.info; if (r && r.syncEnabled) { const n = t.payload.integration.charts.find((e => e.chart._id === (null === a || void 0 === a ? void 0 : a.id)));
                                    n && (e.info.chartIntegration = { syncEnabled: n.syncEnabled, chartIntegrationId: n.id, integrationType: t.payload.integration.type }) } }, "integration/updateChartIntegration/fulfilled": (e, t) => { const { meta: { arg: { name: n, value: r } } } = t; switch (n) {
                                    case "syncEnabled":
                                        { var a; const t = null === e || void 0 === e || null === (a = e.info) || void 0 === a ? void 0 : a.chartIntegration;t && (t.syncEnabled = r); break }
                                    default:
                                        return e } }, "roles/removeRole/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/removeRoleRecursive/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/dropPeople/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/assignPeople/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/update/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/bulkUpdate/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/moveRoleInChart/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/create/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/duplicateMatrixStructure/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 }, "roles/mergeChartLink/fulfilled": e => { e.info.refreshDynamicFieldValues = !0 } } }),
                    { updateVisibleChartWidth: v, updateVisibleChartQuadrant: g, zoomIn: y, zoomOut: b, zoomExact: w, setSampleTheme: z, printPreviewZoomIn: x, printPreviewZoomOut: A, printPreviewZoomExact: k, loadSampleChart: S, updateLegendsWithNewFolder: M, updateInsightFilters: E, updateInsightsMode: C } = f.actions,
                    T = f.reducer }, 30391: (e, t, n) => { "use strict";
                n.d(t, { A: () => p }); var r = n(65043),
                    a = n(14556),
                    o = n(69219),
                    i = n(53109),
                    l = n(78396),
                    s = n(22264),
                    c = n(19367),
                    d = n(13888),
                    u = n(23993),
                    h = n(6124),
                    m = n(98433);

                function p() { const [e, t] = (0, r.useState)(!1), n = (0, a.wA)(), p = (0, a.d4)(c.jb), f = (0, a.d4)(c.Jf) || {}, v = (0, a.d4)(c.G0), g = (0, a.d4)(v === l.XD.MATRIX ? u.J1 : u.$o), { pageReady: y } = (0, h.A)(), { fitToScreen: b } = f, w = (0, a.d4)(m.kU), { chartContainerRef: z, chartSvgRef: x, chartScaleRef: A, chartSpaceRef: k, topGroupRef: S, managerPathRef: M, backPageLinkRef: E } = (0, s.A)(), C = (0, a.d4)(o.uo), T = (0, a.d4)(d.D9), H = (0, a.d4)(c.LL), L = (0, a.d4)(v === l.XD.MATRIX ? u.mw : u.xC);
                    (0, r.useEffect)((() => { const { offsetWidth: e } = (null === z || void 0 === z ? void 0 : z.current) || {};
                        e > 0 && e !== p && n((0, i.WZ)(e)) })), (0, r.useEffect)((() => { var r, a; const o = l.Ay.chart.padding,
                            s = z.current,
                            c = x.current,
                            d = S.current; if (!s) return; if (!c) return; const { offsetWidth: u, offsetHeight: h } = s; let { w0: p, h0: f } = L || { w0: 0, h0: 0 }; const { width: v, topOffset: I } = M.current || { width: 0, height: 0 }, { y: j, height: V } = (null === (r = E.current) || void 0 === r || null === (a = r.getBBox) || void 0 === a ? void 0 : a.call(r)) || { x: 0, y: 0, width: 0, height: 0 }; let O = Math.abs(j) + V; const R = 2 * v - p;
                        R > 0 && (p += R / 2), I && (f += I), g && "root" !== g && (f += 32); const P = Math.min((u - 2 * o) / (p || 1), 1),
                            D = Math.min((h - 2 * o) / (f || 1), 1),
                            F = Math.min(P, D);
                        n((0, m.k1)(F)); let N = 1;
                        C === l.uI.PRINT_PREVIEW && null !== T && void 0 !== T && T.auto ? N = w : (C !== l.uI.PRINT || null !== T && void 0 !== T && T.auto) && C !== l.uI.PRINT_PREVIEW ? y && !e && b ? (N = F, n((0, i.pW)(F)), t(!0)) : N = H : N = F; const _ = Math.max(0, u - 2 * o - (p * N || 1)),
                            B = Math.max(0, h - 2 * o - (f * N || 1)),
                            W = o + _ / 2 + (R > 0 ? Math.max(R / 2, 0) * N : 0),
                            U = o + B / 2 + (I > 0 ? Math.max(I / 2, 0) * N : 0) + (O > 0 ? Math.max(O / 2, 0) * N : 0),
                            q = 2 * o + p * N,
                            G = 2 * o + f * N;
                        A.current = Math.floor(100 * N) / 100, k.current = { h: _, v: B }, d.setAttribute("transform", "scale(".concat(N, " ").concat(N, ")")), c.style.left = "".concat(W, "px"), c.style.top = "".concat(U, "px"), c.setAttribute("width", q), c.setAttribute("height", G) })) } }, 24259: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(65043),
                    a = n(22264),
                    o = n(6124);

                function i() { const e = (0, r.useRef)({}),
                        { chartContainerRef: t } = (0, a.A)(),
                        { pageReady: n } = (0, o.A)(),
                        i = n => { const r = t.current,
                                a = function(e) { const t = e.target || e.srcElement; let n; return t && (n = function(e, t) { if (t = t.toLowerCase(), e.id && e.id.toLowerCase() == t) return e; for (; e && e.parentNode;)
                                            if ((e = e.parentNode).id && e.id.toLowerCase() == t) return e; return null }(t, "chartSVG")), !!n }(n);
                            r && a && (e.current.scrollLeft = n.pageX + r.scrollLeft, e.current.scrollTop = n.pageY + r.scrollTop, e.current.hasMouseDown = !0, r.addEventListener("mousemove", l)) },
                        l = n => { const r = t.current; if (e.current.hasMouseDown) { let t = e.current.scrollLeft - n.pageX,
                                    a = e.current.scrollTop - n.pageY;
                                r.scrollLeft = t, r.scrollTop = a } },
                        s = () => { const n = t.current;
                            e.current.hasMouseDown = !1, n && n.removeEventListener("mousemove", l) };
                    (0, r.useEffect)((() => null !== t && void 0 !== t && t.current ? n ? (t.current.addEventListener("mousedown", i), window.addEventListener("mouseup", s), () => { window.removeEventListener("mouseup", s) }) : void 0 : () => {}), [n]) } }, 82372: (e, t, n) => { "use strict";
                n.d(t, { A: () => i }); var r = n(65043),
                    a = n(73179),
                    o = n(14556); const i = () => { const e = (0, o.wA)(null);
                    (0, r.useEffect)((() => (e((0, a.Wh)({ isTrackingEnabled: !0 })), () => { e((0, a.Wh)({ isTrackingEnabled: !1 })), e((0, a.g4)()) })), []) } }, 72629: (e, t, n) => { "use strict";
                n.d(t, { A: () => o }); var r = n(65043),
                    a = n(86255);

                function o() { const { eventDebounce: e } = (0, a.A)(), [, t] = (0, r.useState)(0);
                    (0, r.useEffect)((() => { const n = e((() => { t((e => e + 1)) }), 100); return window.addEventListener("resize", n), () => { window.removeEventListener("resize", n) } }), []) } }, 89471: (e, t, n) => { "use strict";
                n.d(t, { Ht: () => h, Pe: () => c, Pq: () => s, ro: () => u, sm: () => d }); var r = n(80192),
                    a = n(37091),
                    o = n(95695),
                    i = n(74079),
                    l = n(59445); const s = e => { const { info: t } = e.chart || {}; return null === t || void 0 === t ? void 0 : t.id },
                    c = e => { var t, n, r, a, o; return (null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n || null === (r = n.alias) || void 0 === r ? void 0 : r.rootChartId) || (null === (a = e.chart) || void 0 === a || null === (o = a.info) || void 0 === o ? void 0 : o.id) },
                    d = e => { var t, n; return null === (t = e.chart) || void 0 === t || null === (n = t.info) || void 0 === n ? void 0 : n.alias },
                    u = (0, r.Mz)(o.yP, i.A, ((e, t) => (n, r) => { const o = e.find((e => { var t; return (null === (t = e.parent) || void 0 === t ? void 0 : t.id) === n && e.role.id === r && e.type === a.hC.LINK })); if (o) { var i; const e = null === (i = o.parent) || void 0 === i ? void 0 : i.id,
                                n = o.role.id,
                                r = e ? t[e] : null,
                                a = n ? t[n] : void 0; return (0, l.Sp)(o, a, r) } return null })),
                    h = (0, r.Mz)(o.yP, (e => e.filter((e => e.type === a.hC.LINK)).filter((e => e.status === a.TK.PENDING)).map((e => { var t; return { parentId: null === (t = e.parent) || void 0 === t ? void 0 : t.id, roleId: e.role.id } })).reduce(((e, t) => { let { parentId: n, roleId: r } = t; return n ? (e[n] || (e[n] = []), e[n].push(r), e) : e }), {}))) }, 55679: (e, t, n) => { "use strict";
                n.d(t, { A: () => k }); var r, a = n(65043),
                    o = n(14556),
                    i = n(84),
                    l = n(19367),
                    s = n(23993),
                    c = n(78396),
                    d = n(22264),
                    u = n(18858),
                    h = n(22818),
                    m = n(36138),
                    p = n(66856),
                    f = n(43862),
                    v = n(356),
                    g = n(48853); var y = n(96446),
                    b = n(75156),
                    w = n(13895),
                    z = n(47088),
                    x = n(10268),
                    A = n(70579); const k = e => { let { readOnly: t = !1 } = e; const n = (0, o.wA)(),
                        [k, S] = (0, a.useState)({ left: 0, top: 0 }),
                        { chartControlState: M, newActionCardState: E } = (0, d.A)(),
                        [C, T] = E,
                        H = (0, o.d4)(l.LL),
                        L = (0, o.d4)(s.Vs),
                        I = (0, o.d4)(l.kw),
                        j = (0, o.d4)(s.J1),
                        V = (0, o.d4)(w.A),
                        O = I && I.syncEnabled,
                        R = (0, o.d4)((e => (0, s.gn)(e.roles, C))),
                        { userHasMinAccess: P } = (0, g.A)(),
                        { handleChangeRoleManager: D } = (0, x.n)(),
                        { params: { orgId: F, chartId: N, resourceAction: _ } } = (0, m.u)([(0, p.si)()]),
                        B = _ === c.uI.AI_INSIGHTS,
                        { openDialog: W } = (0, i.A)("alert"),
                        { openDialog: U } = (0, i.A)("moveRole"),
                        q = (0, o.d4)(s.ei),
                        G = V === z.NB.Matrix && (null === j || void 0 === j ? void 0 : j.id) === C,
                        K = L[C],
                        Z = C && K && !G && !t,
                        Y = () => { M.current = { type: "DEFAULT", dragItemType: "", item: null, itemClickOrDragCB: null, itemClickOrDragErrorCB: null, itemAssignOrCancelCB: null } },
                        X = async e => { let { rel: t, draggedRole: r } = e; if (B) D(r, t.id);
                            else if (!O && ((e, t) => { var n; return !(!e || !t) && 0 !== (null === e || void 0 === e || null === (n = e.children) || void 0 === n ? void 0 : n.length) && !q({ parentId: null === e || void 0 === e ? void 0 : e.id, descendentId: null === t || void 0 === t ? void 0 : t.id }) })(r, t)) { U({ orgId: F, chartId: N, role: r, rel: t, onSubmit: () => { Y() } }) } else v.A.trackEvent({ eventName: "ROLE_MOVE_DRAG" }), await n(f.h7.moveRoleInChart({ orgId: F, chartId: N, data: { role: { ...r }, moveHierarchy: O, moveOne: !O, rel: t } })) }, $ = e => { W({ title: "Failed to move", message: (null === e || void 0 === e ? void 0 : e.message) || "Something went wrong." }) }, [{ isDragging: Q }, J, ee] = (0, u.i)({ item: { type: h.A.ROLE, roleObj: R }, collect: e => ({ isDragging: e.isDragging(), handlerId: e.getHandlerId() }), end() { Y() } }); if ((0, a.useEffect)((() => { Q && (T(null), M.current = { type: "DEFAULT", dragItemType: h.A.ROLE, item: R, itemClickOrDragCB: Y, itemClickOrDragErrorCB: $, itemAssignOrCancelCB: X }) }), [Q]), (0, a.useEffect)((() => { ee((r || ((r = new Image).src = "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="), r)) }), []), (0, a.useLayoutEffect)((() => { if (C) { const e = document.getElementById("roleActionBar"),
                                    { left: t, top: n } = (null === e || void 0 === e ? void 0 : e.getBoundingClientRect()) || {},
                                    r = document.getElementById("chartExterior"),
                                    a = null === r || void 0 === r ? void 0 : r.getBoundingClientRect(),
                                    o = null === r || void 0 === r ? void 0 : r.scrollTop,
                                    i = null === r || void 0 === r ? void 0 : r.scrollLeft,
                                    l = 4 * H;
                                S({ left: t - ((null === a || void 0 === a ? void 0 : a.left) || 0) + i + l, top: n - ((null === a || void 0 === a ? void 0 : a.top) || 0) + o }) } }), [C]), !Z) return null; const te = 24 * H,
                        ne = 1 * H; return Z && P(c.td.EDITOR) && (0, A.jsx)("div", { id: "actionCard", style: { position: "absolute", width: te + 2 * ne, height: te + 2 * ne, left: k.left + ne, top: k.top + ne, visibility: Q ? "hidden" : "inherit", zIndex: 1, padding: ne, borderRadius: 2 }, onMouseEnter: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "visible"; const e = t.querySelector("path");
                                e && (e.style.transform = "scale(1.4)", e.style.transformOrigin = "50%") } }, onMouseLeave: e => { const t = e.currentTarget.querySelector("svg"); if (t) { t.style.overflow = "inherit"; const e = t.querySelector("path");
                                e && (e.style.transform = "scale(1)", e.style.transformOrigin = "50%") } }, ref: J, children: (0, A.jsx)(y.A, { sx: { cursor: "grab", width: te, height: te }, children: (0, A.jsx)(b.me, { dataId: "moveRoleIcon", name: "arrows-up-down-left-right", variant: "light", color: "#000000", fontSize: 14 * H }) }) }) } }, 97067: (e, t, n) => { "use strict";
                n.d(t, { lw: () => l });
                n(65043), n(14556), n(19367), n(22818), n(22264), n(23993), n(96364), n(84), n(89656), n(356), n(43862), n(66856), n(45418), n(81635), n(74656), n(78396), n(85725), n(35276), n(48853); var r, a = n(57528),
                    o = n(72119),
                    i = (n(75156), n(70579));
                (0, o.Ay)((e => { let { align: t, size: n, innerRef: r = null, isOver: a = null, ...o } = e; return (0, i.jsx)("div", { ref: r, ...o }) })).attrs((e => { let { size: t, cursor: n } = e; const r = "lg" === t ? 36 : "x2" === t ? 42 : 56; return { style: { width: r, height: r, ...{ position: "relative", cursor: n || "pointer" } } } }))(r || (r = (0, a.A)(["\n  pointer-events: auto;\n  margin-top: 2px;\n  margin-left: 4px;\n  margin-right: 4px;\n  border: solid 1px #ffffff;\n  border-radius: 50%;\n  padding: 6px 4px 4px 4px;\n  background: ", ";\n  color: ", ";\n  z-index: 10;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  :hover {\n    border-color: #000000;\n    background: #000000;\n    color: #ffffff;\n  }\n"])), (e => e.isOver ? "#000000" : "#3cd3c2"), (e => (e.isOver, "#ffffff")));
                n(10621); const l = 36 }, 19857: (e, t, n) => { "use strict";
                n.d(t, { $: () => s, x: () => c });
                n(65043); var r = n(47517),
                    a = n(55143),
                    o = n(18519),
                    i = n(16528),
                    l = n(70579); const s = 24,
                    c = e => { let { PhotoProps: t, CardBoxProps: n, DataProps: c, badgeMap: d, badgeShape: u, placement: h = a.aK.Left, stroke: m, strokeWidth: p, offset: f, legend: v, offsetX: g, offsetY: y, dataWidth: b, isNested: w } = e; if (a.aK.Hidden === h) return null;
                        w && (h = a.aK.Top); const { roleId: z, roleType: x, memberId: A } = c, { width: k, height: S, radius: M } = n, E = m, C = p, T = 14, H = (s - T) / 2, L = (s - T) / 2, I = f || 0, j = M; let V = 0,
                            O = 0,
                            R = 0,
                            P = 0; const D = { tl: 0, tr: 0, bl: 0, br: 0 };
                        h === a.aK.Left ? (V = s, O = S - 2 * I, R = 0, P = I, D.tl = j, D.bl = j) : h === a.aK.Top && (V = k - 2 * I, O = s, R = I, P = 0, D.tl = j, D.tr = j), w && b && (V = b, O = s); const F = (0, r.dQ)({ x: R, y: P }, { width: V, height: O }, D),
                            { rules: N = [] } = v || {},
                            _ = N.filter((e => { let { id: t } = e; if (!w) return x === o.T.Shared && "object" === typeof d[t] ? !0 === d[t][z] : !0 === d[t]; const n = d[t]; return A && "object" === typeof n && !0 === n[A] && !0 })),
                            B = u === a.LJ.Square ? 0 : 7; if (0 === _.length) return null; const W = [a.yS.Left, a.yS.AboveLeft, a.yS.TopLeft].includes(t.placement),
                            U = [a.yS.Center, a.yS.Above].includes(t.placement),
                            q = !w && h === a.aK.Left,
                            G = !w && t.placement !== a.yS.AboveLeft || w && W,
                            K = w && U; return (0, l.jsxs)("g", { transform: "translate(".concat(g || 0, ", ").concat(y || 0, ")"), children: [!w && (0, l.jsx)("path", { d: F, fill: "#F9F9F9", stroke: E, strokeWidth: C }), (0, l.jsx)("g", { transform: "translate(".concat(H, ", ").concat(P + L, ")"), children: _.map(((e, t) => { let n, r, { color: a, icon: o } = e; const s = q ? O - 2 * L : V - 2 * H; return r = q ? 8 * t + t * T : K ? 8 * t + t * T + (s - 22 * _.length) / 2 : G ? 8 * t + t * T : s - (8 * t + (t + 1) * T), r > s || r < 0 ? null : (n = q ? "translate(0, ".concat(r, ")") : "translate(".concat(r, ", 0)"), (0, l.jsx)("g", { transform: n, children: o ? (0, l.jsx)(i.r, { icon: o, iconColor: a, svg: !0, width: T, height: T, sx: { fontSize: "".concat(T, "px"), color: a } }) : (0, l.jsx)("rect", { width: T, height: T, rx: B, ry: B, fill: a }) }, "".concat(o, "-").concat(t))) })) })] }) } }, 1645: (e, t, n) => { "use strict";
                n.d(t, { D: () => c, z: () => s });
                n(65043); var r = n(47517),
                    a = n(55143),
                    o = n(19857),
                    i = n(72952),
                    l = n(70579); const s = 8,
                    c = e => { let { CardBoxProps: t, BadgeBarProps: n, placement: c = i.U$.Left, bgcolor: d } = e; if ("cover" === c || !d || "#ffffff" === d.toLowerCase()) return null; const { width: u, height: h } = t, m = d; let p = 6; const f = "number" === typeof t.radius ? t.radius : 0; let v = 0,
                            g = 0,
                            y = 0,
                            b = 0; const w = { tl: 0, tr: 0, bl: 0, br: 0 };
                        c === i.U$.Left ? (v = s, g = h - 2 * p, y = 0, b = p, w.tl = f, w.bl = f, n.placement === a.aK.Top && (g += o.$)) : c === i.U$.Top ? (n.placement === a.aK.Top ? (w.tl = 0, w.tr = 0, w.bl = 0, w.br = 0, p = 0) : (w.tl = f, w.tr = f), v = u - 2 * p, g = s, y = p, b = 0) : c === i.U$.Right && (v = s, g = h - 2 * p, y = u, b = p, w.tr = f, w.br = f); const z = (0, r.dQ)({ x: y, y: b }, { width: v, height: g }, w); return (0, l.jsx)(l.Fragment, { children: (0, l.jsx)("path", { d: z, fill: m }) }) } }, 16930: (e, t, n) => { "use strict";
                n.d(t, { $: () => s, o: () => c });
                n(65043); var r = n(1645),
                    a = n(19857),
                    o = n(55143),
                    i = n(72952),
                    l = n(70579);

                function s(e) { return e === i.U$.TopLeft || e === i.U$.BottomLeft ? i.U$.Left : e === i.U$.TopRight || e === i.U$.BottomRight || e === i.U$.Bottom ? i.U$.Top : e === i.U$.Right ? i.U$.Left : e } const c = e => { let { ColorBarProps: t, BadgeBarProps: n, CardBoxProps: c, PhotoProps: d, DataProps: u, children: h } = e; const m = s(t.placement),
                        p = n.placement; let f = !1,
                        v = 0,
                        g = 0,
                        y = 0,
                        b = 0,
                        w = 0,
                        z = 0; return m === i.U$.Left && (y = r.z, v += r.z), m === i.U$.Top && (p !== o.aK.Top ? b = r.z : z = a.$, g += r.z), p === o.aK.Left && (v += a.$, m !== i.U$.Left && (w = a.$)), p === o.aK.Top && (m !== i.U$.Top ? g += a.$ : (g = a.$, f = !0)), (0, l.jsxs)(l.Fragment, { children: [(0, l.jsx)("g", { transform: "translate(".concat(y, ", ").concat(b, ")"), children: (0, l.jsx)(a.x, { PhotoProps: d, CardBoxProps: c, DataProps: u, ...n, offset: 0, radius: c.radius }) }), !f && (0, l.jsx)("g", { transform: "translate(".concat(w, ", ").concat(z, ")"), children: (0, l.jsx)(r.D, { CardBoxProps: c, BadgeBarProps: n, ...t }) }), (0, l.jsx)("g", { transform: "translate(".concat(v, ", ").concat(g, ")"), children: h })] }) } }, 55143: (e, t, n) => { "use strict";
                n.d(t, { DQ: () => l, LJ: () => i, aK: () => o, qd: () => a, yS: () => r }); let r = function(e) { return e.Left = "left", e.Right = "right", e.Center = "center", e.TopLeft = "topLeft", e.TopRight = "topRight", e.Above = "above", e.AboveLeft = "aboveLeft", e.AboveRight = "aboveRight", e }({}),
                    a = function(e) { return e.Circle = "circle", e.Oval = "oval", e.Square = "square", e.Fill = "fill", e }({}),
                    o = function(e) { return e.Left = "left", e.Top = "top", e.Hidden = "hidden", e }({}),
                    i = function(e) { return e.Circle = "circle", e.Stretch = "stretch", e.Square = "square", e }({}),
                    l = function(e) { return e.Left = "left", e.Right = "right", e.Top = "above", e.Bottom = "below", e }({}) }, 47517: (e, t, n) => { "use strict";
                n.d(t, { dQ: () => o, mz: () => a }); var r = n(55143);

                function a(e, t, n, a) { const { width: o, height: i } = t;

                    function l(e) { switch (e) {
                            case r.DQ.Left:
                                return { x: -8, y: i / 2 };
                            case r.DQ.Top:
                                return { x: o / 2, y: -8 };
                            case r.DQ.Right:
                                return { x: o, y: i / 2 };
                            case r.DQ.Bottom:
                                return { x: o / 2 - n / 2, y: i + n / 2 };
                            default:
                                throw "Invalid position" } } return e.map((e => { const t = function(e) { switch (e) {
                                    case r.DQ.Left:
                                        return { x: 0, y: i / 2 };
                                    case r.DQ.Top:
                                        return { x: o / 2, y: 0 };
                                    case r.DQ.Right:
                                        return { x: o, y: i / 2 };
                                    case r.DQ.Bottom:
                                        return { x: o / 2, y: i + (a ? -n / 2 : 0) };
                                    default:
                                        throw "Invalid position" } }(e),
                            s = function(e, t, n) { const { x: a, y: o } = e, i = t / 4, l = t; switch (n) {
                                    case r.DQ.Bottom:
                                        return "\n        M ".concat(a - i - t, ",").concat(o, "\n        S ").concat(a - t, ",").concat(o - 0, " ").concat(a - t, ",").concat(o + i, "\n        A ").concat(t, " ").concat(l, " 0 0 0 ").concat(a + t, ",").concat(o + i, "\n        S ").concat(a + t, ",").concat(o, " ").concat(a + t + i, ",").concat(o, "\n      ");
                                    case r.DQ.Left:
                                        return "\n        M ".concat(a, ",").concat(o - i - t, "\n        S ").concat(a, ",").concat(o - t, " ").concat(a - i, ",").concat(o - t, "\n        A ").concat(l, " ").concat(t, " 0 0 0 ").concat(a - i, ",").concat(o + t, "\n        S ").concat(a, ",").concat(o + t, " ").concat(a, ",").concat(o + t + i, "\n      ");
                                    case r.DQ.Right:
                                        return "\n        M ".concat(a, ",").concat(o - i - t, "\n        S ").concat(a, ",").concat(o - t, " ").concat(a + i, ",").concat(o - t, "\n        A ").concat(l, " ").concat(t, " 0 0 1 ").concat(a + i, ",").concat(o + t, "\n        S ").concat(a, ",").concat(o + t, " ").concat(a, ",").concat(o + t + i, "\n      ");
                                    case r.DQ.Top:
                                        return "\n        M ".concat(a - i - t, ",").concat(o, "\n        S ").concat(a - t, ",").concat(o, " ").concat(a - t, ",").concat(o - i, "\n        A ").concat(t, " ").concat(l, " 0 0 1 ").concat(a + t, ",").concat(o - i, "\n        S ").concat(a + t, ",").concat(o, " ").concat(a + t + i, ",").concat(o, "\n      ");
                                    default:
                                        throw new Error("Invalid position. Expected 'left', 'right', 'top', or 'bottom'.") } }(t, 16, e); return { position: e, path: s, centerPoint: t, iconCenter: l(e) } })) }

                function o(e, t, n) { const { width: r, height: a } = t, { x: o, y: i } = e, { tl: l = 0, tr: s = 0, bl: c = 0, br: d = 0 } = n; return "\n    M ".concat(o + l, ",").concat(i, "\n    L ").concat(o + r - s, ",").concat(i, "\n    S ").concat(o + r, ",").concat(i, " ").concat(o + r, ",").concat(i + s, " \n    L ").concat(o + r, ",").concat(i + a - d, "\n    S ").concat(o + r, ",").concat(i + a, " ").concat(o + r - d, ",").concat(i + a, "\n    L ").concat(o + c, ",").concat(i + a, "\n    S ").concat(o, ",").concat(i + a, " ").concat(o, ",").concat(i + a - c, "\n    L ").concat(o, ",").concat(i + l, "\n    S ").concat(o, ",").concat(i, " ").concat(o + l, ",").concat(i, "\n    Z\n  ") } }, 80313: (e, t, n) => { "use strict";
                n.d(t, { n: () => h }); var r = n(61531),
                    a = (n(65043), n(14556)),
                    o = n(19367),
                    i = n(89656),
                    l = n(23993),
                    s = n(16528),
                    c = n(80286),
                    d = n(70579); const u = e => { let { size: t = 28, icon: n, color: a, shape: o = "circle" } = e; const i = t,
                            l = t; let u = "0"; "circle" === o && (u = "50%"); return !(!n || !c.Wb[n]) ? (0, d.jsx)(r.A, { width: i, height: l, display: "flex", justifyContent: "center", alignItems: "center", children: (0, d.jsx)(s.r, { icon: n, iconColor: a, sx: { color: a, display: "flex", fontSize: "".concat(t, "px") }, width: i, height: l }) }, "colorBadgeIconBox-".concat(n, "-").concat(a)) : (0, d.jsx)(r.A, { width: i, height: l, bgcolor: a, borderRadius: u }) },
                    h = e => { let { role: t, person: n } = e; const s = (0, a.d4)(l.pQ),
                            c = (0, a.d4)(o.Zx),
                            h = (0, a.d4)(i.y$),
                            { badgeShape: m } = h || {},
                            { rules: p = [] } = c || {},
                            f = p.filter((e => { let { id: r } = e; if (null !== t && void 0 !== t && t.id) { const e = s[null === t || void 0 === t ? void 0 : t.id] && s[null === t || void 0 === t ? void 0 : t.id][r]; return e && null !== n && void 0 !== n && n.id ? !0 === e || !0 === e[null === n || void 0 === n ? void 0 : n.id] : !0 === e } return !0 === s[r] })); return (0, d.jsx)(d.Fragment, { children: f.length > 0 && (0, d.jsx)(r.A, { display: "flex", gridGap: 8, children: f.map((e => (0, d.jsx)(u, { icon: (null === e || void 0 === e ? void 0 : e.icon) || "", color: (null === e || void 0 === e ? void 0 : e.color) || "", shape: m, size: 16 }))) }) }) } }, 55340: (e, t, n) => { "use strict";
                n.d(t, { A: () => g }); var r, a = n(57528),
                    o = n(75156),
                    i = n(72119),
                    l = n(61531),
                    s = n(59177),
                    c = n(14556),
                    d = n(81635),
                    u = n(91688),
                    h = n(66856),
                    m = n(36138),
                    p = n(85865),
                    f = n(70579); const v = (0, i.Ay)(l.A)(r || (r = (0, a.A)(["\n  cursor: pointer;\n  color: ", ";\n  &:hover {\n    font-weight: 500;\n  }\n  padding: 10px;\n  font-size: 14px;\n"])), (e => { let { active: t, theme: n } = e; return "".concat(t ? n.palette.textPrimary : n.palette.primary.main) })),
                    g = e => { let { name: t, chartId: n, chartName: r, active: a, id: i, orgId: g, isClickable: y } = e; const b = a ? "textPrimary" : "primary",
                            w = (0, c.wA)(),
                            z = (0, u.useHistory)(),
                            x = (0, m.u)([(0, h.Zm)(), (0, h.si)()]),
                            A = (null === x || void 0 === x ? void 0 : x.params) || { base: "protected" }; return (0, f.jsxs)(l.A, { children: [(0, f.jsx)(p.A, { variant: "subheadingSM", fontSize: 14, children: t }), (0, f.jsxs)(v, { active: a, display: "flex", alignItems: "center", gridGap: 8, my: 1, onClick: y ? () => { w((0, d.Ch)()); const e = {};
                                    i && (e.search = "?targetRoleId=".concat(i)), z.push({ pathname: (0, h.Wx)({ resource: "chart", chartId: n, orgId: g, targetRoleId: i, base: (null === A || void 0 === A ? void 0 : A.base) || "protected" }), ...e }) } : void 0, color: b, children: [(0, f.jsx)(o.Ay, { icon: "ChartSolid", size: "lg" }), (0, f.jsx)(l.A, { display: "flex", alignItems: "center", children: (0, f.jsxs)(p.A, { variant: "caption", color: b, fontSize: 14, children: ["\xa0 ", (0, s.RP)(r, 20)] }) })] })] }) } }, 41450: (e, t, n) => { "use strict";
                n.d(t, { A: () => m }); var r, a, o = n(57528),
                    i = n(72119),
                    l = n(61531),
                    s = n(75156),
                    c = n(70318),
                    d = n(70579); const u = (0, i.Ay)(c.A)(r || (r = (0, o.A)(["\n  margin:5px;\n  opacity: 75%;\n  padding: 5px 10px;\n"]))),
                    h = (0, i.Ay)(l.A).attrs({ position: "absolute", left: 0, right: 0, top: 0, bottom: 0, display: "flex", justifyContent: "space-between", alignItems: "center" })(a || (a = (0, o.A)(["\n  ", " \n"])), u),
                    m = e => { let { handleLeftClick: t, handleRightClick: n } = e; return (0, d.jsxs)(h, { children: [(0, d.jsx)(u, { onClick: t, children: (0, d.jsx)(s.Ay, { icon: "ArrowLeft", color: "#000" }) }), (0, d.jsx)(u, { onClick: n, children: (0, d.jsx)(s.Ay, { icon: "ArrowRight", color: "#000" }) })] }) } }, 74706: (e, t, n) => { "use strict";
                n.d(t, { A: () => y }); var r, a = n(57528),
                    o = n(65043),
                    i = n(96364),
                    l = n(10621),
                    s = n(42517),
                    c = n(75156),
                    d = n(72119),
                    u = n(59177),
                    h = n(61531),
                    m = n(24241),
                    p = n(80286),
                    f = n(37294),
                    v = n(70579); const g = (0, d.Ay)(i.A)(r || (r = (0, a.A)(["\n  ", "\n"])), (e => { let { fontDetails: t } = e; return "\n    a{\n      color: ".concat(null === t || void 0 === t ? void 0 : t.color, ";\n      font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n      font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n      font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n      text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", "\n      text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n    }\n    color: ").concat(null === t || void 0 === t ? void 0 : t.color, ";\n    font-size: ").concat(null === t || void 0 === t ? void 0 : t.size, "px;\n    font-weight: ").concat(null === t || void 0 === t ? void 0 : t.weight, ";\n    font-style: ").concat(null !== t && void 0 !== t && t.italic ? "italic" : "", ";\n    text-decoration: ").concat(null !== t && void 0 !== t && t.strikethrough ? "line-through" : "", " ").concat(null !== t && void 0 !== t && t.underline ? "underline" : "", ";\n    text-transform: ").concat((null === t || void 0 === t ? void 0 : t.case) || "none", ";\n  };\n  ") })),
                    y = e => { let { field: t, value: n, objectId: r, orgId: a, fontDetails: i, section: d } = e; const y = ((null === t || void 0 === t ? void 0 : t.displayType) || (null === t || void 0 === t ? void 0 : t.type) || "").toLowerCase(),
                            [b, w] = (0, o.useState)({ fieldId: null === t || void 0 === t ? void 0 : t.id, isReadMore: !0 }),
                            z = e => { e !== b.fieldId && w({ fieldId: e, isReadMore: !1 }), e === b.fieldId && w((t => ({ fieldId: e, isReadMore: !t.isReadMore }))) },
                            x = e => { null === e || void 0 === e || e.stopPropagation() }; if ("string" === y) { if ((null === t || void 0 === t ? void 0 : t.name) === l.x2.PHONE) { if ("icon-section" !== d) return (0, v.jsx)(g, { fontDetails: i, children: n ? (0, v.jsx)("a", { href: "tel:".concat(n), target: "_blank", rel: "noopener noreferrer", children: n }) : "------" }); if (n) return (0, v.jsx)("a", { href: "tel:".concat(n), target: "_blank", rel: "noopener noreferrer", children: (0, v.jsx)(c.Ay, { icon: "PhoneSolid", size: "lg", color: null === i || void 0 === i ? void 0 : i.color }) }) } if ((null === t || void 0 === t ? void 0 : t.name) === l.x2.EMAIL) { if ("icon-section" !== d) return (0, v.jsx)(g, { fontDetails: i, children: n ? (0, v.jsx)("a", { href: "mailto:".concat(n), target: "_blank", rel: "noopener noreferrer", children: (0, u.RP)(n, 30) }) : "------" }); if (n) return (0, v.jsx)("a", { href: "mailto:".concat(n), target: "_blank", rel: "noopener noreferrer", children: (0, v.jsx)(c.Ay, { icon: "Email", size: "lg", color: null === i || void 0 === i ? void 0 : i.color }) }) } if (n) return (0, l.vK)(n) && (0, v.jsxs)(g, { fontDetails: i, children: [b.isReadMore && b.fieldId === (null === t || void 0 === t ? void 0 : t.id) && "string" === typeof n ? n.slice(0, 75) : n, "string" === typeof n && n.length > 75 && (0, v.jsxs)("a", { onClick: () => z(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em", color: f.Qs.Violet[500] }, children: ["\xa0", b.isReadMore && b.fieldId === (null === t || void 0 === t ? void 0 : t.id) ? "...more" : " ...less"] })] }) } if ("number" === y) return (0, v.jsx)(g, { fontDetails: i, children: (0, l.vK)(n) ? n : "------" }); if ("iconpicklist" === y && n && p.Md[n]) return (0, v.jsxs)(h.A, { display: "flex", gridGap: 8, alignItems: "center", children: [(0, v.jsx)("img", { src: "".concat("https://assets-organimi.s3.amazonaws.com").concat(p.Md[n].path), width: 24 }), (0, v.jsx)(g, { fontDetails: i, children: p.Md[n].label })] }); if ("url" === y) return "icon-section" !== d ? (0, v.jsx)(g, { fontDetails: i, children: n ? (0, v.jsxs)("a", { onClick: x, href: n, target: "_blank", rel: "noopener noreferrer", children: [(0, u.RP)(n, 40), " "] }) : "------" }) : (0, l.x7)(n) && (null === t || void 0 === t ? void 0 : t.name) === l.x2.LINKEDIN && (0, v.jsx)("a", { onClick: x, href: n, target: "_blank", rel: "noopener noreferrer", children: (0, v.jsx)(c.Ay, { icon: "LinkedInInverse", size: "lg", color: null === i || void 0 === i ? void 0 : i.color }) }); if ("attachment" === y) return n ? (0, v.jsx)(s.A, { orgId: a, model: null === t || void 0 === t ? void 0 : t.model, name: null === t || void 0 === t ? void 0 : t.id, objectId: r, extension: null === n || void 0 === n ? void 0 : n.extension, label: null === t || void 0 === t ? void 0 : t.label }) : "------"; if ("boolean" === y) return (0, v.jsx)(h.A, { children: (0, v.jsx)(c.Ay, { icon: "Checkbox", size: "lg", color: n ? null === i || void 0 === i ? void 0 : i.color : "#eeeeee" }) }); if ("switch" === y) return (0, v.jsx)(h.A, { children: (0, v.jsx)(c.Ay, { icon: "Toggle", size: "lg", color: n ? null === i || void 0 === i ? void 0 : i.color : "#eeeeee", style: { fontSize: (null === i || void 0 === i ? void 0 : i.size) + "px" } }) }); if ("tags" === y) return (0, v.jsx)(g, { fontDetails: i, children: n ? (0, l.Hc)(n) : "------" }); if ("date" === y) return (0, v.jsx)(g, { fontDetails: i, children: n && "Invalid Date" !== n.toString() ? (0, l.Os)(n, null === i || void 0 === i ? void 0 : i.dateFormat) : "------" }); if ("computed" === y) { const e = m.c9.fromISO(n); return (0, v.jsx)(g, { fontDetails: i, children: null !== e && void 0 !== e && e.isValid ? (0, l.Os)(n, null === i || void 0 === i ? void 0 : i.dateFormat) : n }) } if ("location" === y) return (0, v.jsx)(g, { fontDetails: i, children: n ? (0, l.rS)(n, t) : "------" }); if ("richtext" === y) { if ("Sample value" === n) return (0, v.jsx)(g, { fontDetails: i, children: n }); if (!Array.isArray(n)) return (0, v.jsx)(g, { fontDetails: i, children: "------" }); if (Array.isArray(n)) { const e = !b.isReadMore && b.fieldId === (null === t || void 0 === t ? void 0 : t.id),
                                    r = n.filter((e => null !== e && void 0 !== e)),
                                    a = r.length,
                                    s = a > 1; return r.map(((n, r) => e || r < 1 ? (0, v.jsxs)(o.Fragment, { children: ["" === n ? (0, v.jsx)("br", {}) : (0, v.jsx)(g, { fontDetails: i, children: (0, l.vK)(n) && n }), !e && 0 === r && s && (0, v.jsx)("a", { onClick: () => z(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em" }, children: "\xa0...more" }), e && r === a - 1 && s && (0, v.jsx)("a", { onClick: () => z(null === t || void 0 === t ? void 0 : t.id), style: { fontSize: "0.8em" }, children: "\xa0...less" })] }, r) : null)) } } var A; return "currency" === y ? "Sample value" === n ? (0, v.jsx)(g, { fontDetails: i, children: n }) : (0, v.jsx)(g, { fontDetails: i, children: n ? (0, l.Jw)(n, null === t || void 0 === t || null === (A = t.typeMetadata) || void 0 === A ? void 0 : A.currency) : "------" }) : "rollup" === y ? (0, v.jsx)(g, { fontDetails: i, children: void 0 !== n && null !== n ? (0, l.$)(n, t) : "------" }) : n ? (0, v.jsx)(g, { fontDetails: i, children: n }) : "icon-section" !== d ? (0, v.jsx)(g, { fontDetails: i, children: "------" }) : (0, v.jsx)(v.Fragment, {}) } }, 66486: (e, t, n) => { "use strict";
                n.d(t, { A: () => b }); var r, a, o = n(57528),
                    i = (n(65043), n(78300)),
                    l = n(80539),
                    s = n(75156),
                    c = n(96382),
                    d = n(14556),
                    u = n(89656),
                    h = n(61531),
                    m = n(10621),
                    p = n(72119),
                    f = n(70579); const v = 90,
                    g = (0, p.Ay)(l.A)(r || (r = (0, o.A)(["\n  ", "\n"])), (e => { let { shape: t } = e; return "\n    border-radius: ".concat(t, ";\n") })),
                    y = (0, p.Ay)(i.A)(a || (a = (0, o.A)(["\n  ", "\n"])), (e => { let { shape: t } = e; return "\n    border-radius: ".concat(t, ";\n") })),
                    b = e => { let { size: t = v, shape: n = "circular", person: r, role: a } = e; const o = (0, d.d4)(u.o4),
                            i = (0, d.d4)(u.Od); let l, p, b; switch (n) {
                            case "circular":
                            default:
                                b = "9999px"; break;
                            case "oval":
                                b = "16px"; break;
                            case "square":
                                b = "0px" } if (r) { const e = (0, m.II)(r),
                                n = (null === r || void 0 === r ? void 0 : r.name) || (e ? "".concat(e, " ").concat((0, m.US)(r)) : "");
                            l = (0, f.jsx)(g, { shape: b, width: t, height: t, src: o, name: n, overrideColor: null === r || void 0 === r ? void 0 : r.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.currentTarget.setAttribute("processed", "true") }, onError: e => { e.currentTarget.setAttribute("processed", "true"), e.currentTarget.setAttribute("href", c.A) } } }), p = (0, f.jsx)(g, { shape: b, width: t, height: t, src: (null === r || void 0 === r ? void 0 : r.photo) || o, name: n, overrideColor: null === r || void 0 === r ? void 0 : r.memberPhotoColor, imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.currentTarget.setAttribute("processed", "true") } }, children: l }) } else if (a) { const { type: e } = a || {};
                            p = /single|assistant|shared/i.test(e) ? (0, f.jsx)(g, { shape: b, width: t, height: t, src: i, name: "va", imgProps: { className: "thumbnail MuiAvatar-img", onLoad: e => { e.currentTarget.setAttribute("processed", "true") } }, children: l }) : (0, f.jsx)(y, { shape: b, width: t, height: t, color: "default", imgProps: { onLoad: e => { e.target.setAttribute("processed", "true") } }, children: (0, f.jsx)(s.Ay, { icon: "Building", size: "x3" }) }) } return (0, f.jsx)(h.A, { position: "relative", display: "flex", justifyContent: "center", children: p }) } }, 82513: (e, t, n) => { "use strict";
                n.d(t, { Ay: () => W }); var r, a, o, i, l, s, c = n(57528),
                    d = n(61531),
                    u = n(40454),
                    h = n(8713),
                    m = n(77325),
                    p = n(72119),
                    f = n(96364),
                    v = n(172),
                    g = n(75156),
                    y = n(70318),
                    b = n(10621),
                    w = n(65043),
                    z = n(14556),
                    x = n(81635),
                    A = n(84),
                    k = n(43862),
                    S = n(43331),
                    M = n(37869),
                    E = n(64418),
                    C = n(19367),
                    T = n(22264),
                    H = n(78396),
                    L = n(48853),
                    I = n(45418),
                    j = n(26225),
                    V = n(70579); const O = (0, p.Ay)(f.A)(r || (r = (0, c.A)(["\n  font-size: 14px;\n  font-weight: 400;\n"]))),
                    R = (0, p.Ay)(v.A)(a || (a = (0, c.A)(["\n  cursor: pointer;\n  // border: solid 1px #ccc;\n  border-radius: 8px;\n  margin-top: 8px;\n"]))),
                    P = (0, p.Ay)(f.A)(o || (o = (0, c.A)(["\n  font-weight: 500;\n  font-size: 14px;\n"]))),
                    D = (0, p.Ay)(f.A)(i || (i = (0, c.A)(["\n  font-weight: 400;\n  font-size: 14px;\n"]))),
                    F = (0, p.Ay)(d.A)(l || (l = (0, c.A)(["\n  cursor: pointer;\n  color: ", ";\n  ", " {\n    font-size: 16px;\n  }\n"])), (e => { let { theme: t } = e; return "".concat(t.palette.primary.main) }), f.A),
                    N = (0, p.Ay)(f.A)(s || (s = (0, c.A)(["\n  line-height: 2em;\n"]))),
                    _ = e => { let { role: t, person: n, photoComponent: r, listActive: a, handleClick: o, handleRemoveItemClick: i } = e; const l = "single" === t.type || "shared" === t.type ? n ? n.name || "".concat((0, b.II)(n), " ").concat((0, b.US)(n)) : "" : (0, b.mA)(t); return (0, V.jsxs)(u.A, { container: !0, children: [(0, V.jsx)(u.A, { item: !0, xs: !0, children: (0, V.jsxs)(R, { button: !0, alignItems: "flex-start", ContainerComponent: "div", onClick: o, children: [(0, V.jsx)(h.A, { sizes: "medium", children: r }), (0, V.jsx)(m.A, { primary: (0, V.jsx)(P, { children: l }), secondary: n ? (0, V.jsx)(D, { children: (0, b.mA)(t) }) : void 0 })] }) }), a && (0, V.jsx)(u.A, { item: !0, children: (0, V.jsx)(y.A, { onClick: i, children: (0, V.jsx)(g.Ay, { icon: "Close" }) }) })] }) },
                    B = e => { let { role: t, listActive: n, handleClick: r, handleRemoveItemClick: a } = e; const o = (0, z.d4)((e => { var n; return (0, I.Ar)(e.people, null === t || void 0 === t || null === (n = t.members) || void 0 === n ? void 0 : n[0]) })),
                            i = "single" === t.type || "shared" === t.type ? o ? o.name || "".concat((0, b.II)(o), " ").concat((0, b.US)(o)) : "" : (0, b.mA)(t); return (0, V.jsxs)(u.A, { container: !0, children: [(0, V.jsx)(u.A, { item: !0, xs: !0, children: (0, V.jsxs)(R, { button: !0, alignItems: "flex-start", ContainerComponent: "div", onClick: r, children: [(0, V.jsx)(h.A, { sizes: "medium", children: (0, V.jsx)(M.A, { size: 48, person: o, role: t }) }), (0, V.jsx)(m.A, { primary: (0, V.jsx)(P, { children: i }), secondary: o ? (0, V.jsx)(D, { children: (0, b.mA)(t) }) : void 0 })] }) }), n && (0, V.jsx)(u.A, { item: !0, children: (0, V.jsx)(y.A, { onClick: a, children: (0, V.jsx)(g.Ay, { icon: "Close" }) }) })] }) },
                    W = e => { let { role: t, connections: n, isMobile: r } = e; const { indirect: a } = { indirect: [], ...n || {} }, o = (0, z.d4)((e => (0, j.kp)(e, null === t || void 0 === t ? void 0 : t.id))), i = (0, z.d4)((e => (0, j.p4)(e, null === t || void 0 === t ? void 0 : t.parent))), l = (0, z.d4)((e => { var t; return (0, I.Ar)(e.people, null === i || void 0 === i || null === (t = i.members) || void 0 === t ? void 0 : t[0]) })), s = l ? l.name || "".concat((0, b.II)(l), " ").concat((0, b.US)(l)) : "", c = (0, z.wA)(), [u, p] = (0, w.useState)(null), [v, y] = (0, w.useState)(!1), { confirmAction: W } = (0, E.A)(), U = (0, z.d4)(x.FM), { openDialog: q } = (0, A.A)("newRoleSimple"), { userHasMinAccess: G, userType: K } = (0, L.A)(), Z = (0, z.d4)(S.mn), Y = (0, z.d4)(C.Mk), X = (0, z.d4)(C.kw), { syncEnabled: $ } = X || {}, Q = (0, T.A)(), { newActionCardState: J } = Q || {}, [, ee] = J || [], te = !r && (null === Y || void 0 === Y ? void 0 : Y.type) !== H.XD.BOARD_OF_DIRECTORS && G(H.td.EDITOR) && !$ && !(K === H.td.EDITOR && !t), ne = e => { let { roleId: t, personId: n } = e; return () => { c((0, k.mO)({ roleId: t })), "function" === typeof ee && ee(t), c((0, x.gN)({ ...U, selectedRoleId: t, activePersonId: n })) } }, re = async (e, n) => { y(!0), await c(k.h7.update({ orgId: Z, chartId: t.chart, data: { role: { ...t, parent: e }, moveOne: !n, moveHierarchy: n } })), y(!1) }, { openDialog: ae } = (0, A.A)("changeManager", { onSelected: re }), oe = () => { p((e => "direct" !== e ? "direct" : "")) }, ie = e => { let { roleId: n, chartId: r } = e; return () => { W({ execFunc: async () => { y(!0), await c(k.h7.removeRole({ orgId: Z, chartId: r, roleId: n })), y(!1) }, title: "Remove role", message: "Remove role as a direct report to ".concat((0, b.mA)(t)) }) } }, le = o.length > 0 && o.filter((e => e)).map((e => { var t; return (0, V.jsx)(B, { role: e, listActive: "direct" === u, handleRemoveItemClick: ie({ roleId: e.id, chartId: e.chart }), handleClick: ne ? ne({ roleId: e.id, personId: (null === (t = e.members[0]) || void 0 === t ? void 0 : t.id) || e.members[0] }) : void 0 }, "directConnection".concat(e.id)) })), se = a.filter((e => e)).map((e => { var t; const n = e.members && e.members[0]; return (0, V.jsx)(_, { role: e, person: n, handleClick: ne ? ne({ roleId: e.id, personId: (null === (t = e.members[0]) || void 0 === t ? void 0 : t.id) || e.members[0] }) : void 0, photoComponent: (0, V.jsx)(M.A, { size: 48, person: n, role: e }) }, "indirectConnection".concat(e.id)) })); return (0, V.jsx)(V.Fragment, { children: t && (0, V.jsxs)(d.A, { children: [(0, V.jsxs)(d.A, { children: [(0, V.jsxs)(d.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: [(0, V.jsx)(O, { children: "Manager" }), te && (0, V.jsx)("a", { onClick: () => { ae({ handleChange: re, currentParentId: null === i || void 0 === i ? void 0 : i.id, roleId: t.id }) }, children: "Change" })] }), !i && (0, V.jsx)(N, { children: "None" }), i && (0, V.jsxs)(R, { button: !0, alignItems: "flex-start", ContainerComponent: "div", onClick: ne ? ne({ roleId: null === i || void 0 === i ? void 0 : i.id, personId: ((null === i || void 0 === i ? void 0 : i.members) || [])[0] }) : void 0, children: [(0, V.jsx)(h.A, { sizes: "medium", children: (0, V.jsx)(M.A, { role: i, person: l, size: 48 }) }), (0, V.jsx)(m.A, { primary: (0, V.jsx)(P, { children: s }), secondary: (0, V.jsx)(D, { children: (0, b.mA)(i || "manager") }) })] })] }), (0, V.jsxs)(d.A, { mt: 2, children: [(0, V.jsxs)(d.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: [(0, V.jsxs)(O, { children: ["Direct Reports (", o.length, ")"] }), te && 0 !== o.length && (0, V.jsxs)(V.Fragment, { children: ["direct" !== u && (0, V.jsx)("a", { onClick: oe, children: "Edit List" }), "direct" === u && (0, V.jsx)("a", { onClick: oe, children: "Done" })] })] }), 0 === o.length && (0, V.jsx)(N, { children: "None" }), le, te && (0, V.jsxs)(F, { display: "flex", alignItems: "center", mt: 1, pb: 2, children: [(0, V.jsx)(g.Ay, { icon: "Add", size: "lg" }), (0, V.jsx)("a", { onClick: () => { q({ rel: { id: null === t || void 0 === t ? void 0 : t.id, position: "below" } }) }, children: "\xa0 Add Person" })] })] }), (null === se || void 0 === se ? void 0 : se.length) > 0 && (0, V.jsxs)(d.A, { mt: 2, children: [te && (0, V.jsx)(d.A, { display: "flex", alignItems: "center", justifyContent: "space-between", children: (0, V.jsxs)(O, { children: ["Indirect Connections", " ", (0, V.jsx)(f.A, { variant: "body2", display: "inline", children: "(joined by dotted line)" })] }) }), se] })] }) }) } }, 77820: (e, t, n) => { "use strict";
                n.d(t, { A: () => oe }); var r, a, o, i, l, s, c, d, u, h, m, p, f, v, g = n(57528),
                    y = n(96446),
                    b = n(17392),
                    w = n(68903),
                    z = n(24056),
                    x = n(85865),
                    A = n(65043),
