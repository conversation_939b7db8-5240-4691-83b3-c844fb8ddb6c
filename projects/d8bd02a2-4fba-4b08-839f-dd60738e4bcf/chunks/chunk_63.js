                e.exports = JSON.parse('{"Yr":7,"ue":2,"cM":126,"$U":"prod7MA2MI126RBETA"}') } },
        t = {};

    function n(r) { var a = t[r]; if (void 0 !== a) return a.exports; var o = t[r] = { id: r, loaded: !1, exports: {} }; return e[r].call(o.exports, o, o.exports, n), o.loaded = !0, o.exports } n.m = e, n.n = e => { var t = e && e.__esModule ? () => e.default : () => e; return n.d(t, { a: t }), t }, (() => { var e, t = Object.getPrototypeOf ? e => Object.getPrototypeOf(e) : e => e.__proto__;
        n.t = function(r, a) { if (1 & a && (r = this(r)), 8 & a) return r; if ("object" === typeof r && r) { if (4 & a && r.__esModule) return r; if (16 & a && "function" === typeof r.then) return r } var o = Object.create(null);
            n.r(o); var i = {};
            e = e || [null, t({}), t([]), t(t)]; for (var l = 2 & a && r;
                "object" == typeof l && !~e.indexOf(l); l = t(l)) Object.getOwnPropertyNames(l).forEach((e => i[e] = () => r[e])); return i.default = () => r, n.d(o, i), o } })(), n.d = (e, t) => { for (var r in t) n.o(t, r) && !n.o(e, r) && Object.defineProperty(e, r, { enumerable: !0, get: t[r] }) }, n.f = {}, n.e = e => Promise.all(Object.keys(n.f).reduce(((t, r) => (n.f[r](e, t), t)), [])), n.u = e => "static/js/" + e + "." + { 248: "d4ae12d3", 353: "dd80e0ab", 491: "65a7d971", 526: "57bf6b38", 600: "4f50faaf", 766: "037ea6cb", 927: "ffef0aca", 955: "874d45bf", 1139: "68eea2fd", 1222: "113ede7d", 1229: "81e5f46e", 1249: "edb93cdf", 1407: "84ddb8a7", 1448: "5b0e1303", 1566: "ac97a062", 1620: "18e054b8", 1636: "01fcba16", 1649: "a3a6757b", 1704: "b4a95733", 1783: "4f133fa7", 1802: "854278ce", 1950: "4a02dd25", 2180: "f4381c22", 2298: "4cac58a7", 2315: "d1a64849", 2394: "e15606d5", 2420: "2d5dec1f", 2470: "22c24562", 2497: "79051277", 2518: "34d887b5", 2650: "655c5de4", 2723: "3df454be", 2884: "9ea0c34f", 2998: "d3fb2965", 3194: "ace36064", 3263: "1d652d5f", 3335: "30cd4b7a", 3517: "7e42f83a", 3649: "16b5630f", 3657: "7d33e090", 3679: "d85caeec", 3691: "639b045e", 3760: "92ca97a3", 4195: "80755b54", 4259: "73ba6d10", 4279: "f01bcf2c", 4352: "8570d837", 4381: "cf3caa40", 4430: "7c21591a", 4456: "572ec4e8", 4482: "7dfe101a", 4592: "f1cfbcb8", 4806: "f23b3c1b", 4883: "fca0f297", 5167: "27e1f968", 5250: "27152dc6", 5277: "db71b0ef", 5369: "df6eec5e", 5380: "f3dc43d0", 5647: "67c9c672", 5858: "24d95fc2", 5862: "ed1b4692", 5938: "2f7f7323", 6014: "622b59b6", 6142: "7940745e", 6222: "d68152a1", 6346: "15b96f6f", 6710: "0008315f", 6808: "c2b59113", 6863: "62a301fb", 6928: "cdf2ae50", 6940: "fbc4268d", 6996: "384f56ce", 7020: "500265da", 7132: "4f12a3a0", 7326: "1db51a06", 7512: "e058e2d4", 7725: "7092e0d9", 7824: "c8f66d4b", 7826: "700e72f4", 7909: "9ba9b29d", 7920: "95e9d5a8", 8041: "f2539acf", 8053: "23e1c4a9", 8076: "74159d11", 8132: "92f629e1", 8171: "0246224b", 8248: "e6f71649", 8274: "18f842e8", 8385: "7b4e101a", 8409: "41643044", 8444: "d787efad", 8658: "f568ef53", 8665: "7a3fc04b", 8705: "35ef6a73", 8780: "c070ebd6", 8786: "2855994c", 8855: "d67a5f04", 8858: "39a0179a", 8893: "379d5cf2", 8911: "3423064c", 8913: "600c09b6", 9040: "aa30fc04", 9051: "5598d3fb", 9080: "fbfb39d1", 9138: "ab7a4551", 9321: "02a30eb0", 9361: "03a6ae60", 9395: "6a7e26fa", 9419: "1a28dc34", 9454: "1da5939b", 9530: "c070bb25", 9584: "91a2f62f", 9587: "25221504", 9597: "2680275c", 9620: "a9f36491", 9755: "a2a5979d", 9868: "932092c2", 9895: "9e76bece", 9955: "42719251" } [e] + ".chunk.js", n.miniCssF = e => "static/css/" + e + "." + { 526: "4bbe3e65", 600: "4bbe3e65", 927: "c6a277c4", 2315: "c6a277c4", 3335: "c6a277c4", 4259: "c6a277c4", 4456: "d24ee907", 5858: "c6a277c4", 5862: "c6a277c4", 6222: "c6a277c4", 8248: "c6a277c4", 8658: "4bbe3e65", 8665: "c6a277c4", 9587: "d24ee907", 9620: "c6a277c4", 9895: "c6a277c4" } [e] + ".chunk.css", n.g = function() { if ("object" === typeof globalThis) return globalThis; try { return this || new Function("return this")() } catch (e) { if ("object" === typeof window) return window } }(), n.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t), (() => { var e = {},
            t = "organimi-app:";
        n.l = (r, a, o, i) => { if (e[r]) e[r].push(a);
            else { var l, s; if (void 0 !== o)
                    for (var c = document.getElementsByTagName("script"), d = 0; d < c.length; d++) { var u = c[d]; if (u.getAttribute("src") == r || u.getAttribute("data-webpack") == t + o) { l = u; break } } l || (s = !0, (l = document.createElement("script")).charset = "utf-8", l.timeout = 120, n.nc && l.setAttribute("nonce", n.nc), l.setAttribute("data-webpack", t + o), l.src = r, 0 !== l.src.indexOf(window.location.origin + "/") && (l.crossOrigin = "anonymous"), l.integrity = n.sriHashes[i], l.crossOrigin = "anonymous"), e[r] = [a]; var h = (t, n) => { l.onerror = l.onload = null, clearTimeout(m); var a = e[r]; if (delete e[r], l.parentNode && l.parentNode.removeChild(l), a && a.forEach((e => e(n))), t) return t(n) },
                    m = setTimeout(h.bind(null, void 0, { type: "timeout", target: l }), 12e4);
                l.onerror = h.bind(null, l.onerror), l.onload = h.bind(null, l.onload), s && document.head.appendChild(l) } } })(), n.r = e => { "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, n.nmd = e => (e.paths = [], e.children || (e.children = []), e), n.p = "https://client-us-prod.organimi.com/", n.sriHashes = { 248: "sha256-hE69Wq1f2+h4JipuLxAqFEHSFNs+XIc++v50jR5S/TY= sha384-1Wb77POoG4wWkCo4aW17LkLfzttX4KTP4enJOigQFp0tj6Vyu7xSwnbnYCUpR0aZ", 353: "sha256-9S30e10Im3drHuaaEIahu1VaUWU6cBZtUGijFQ1h49o= sha384-k4et0J6YOD4K0RQg8Cvq0uoj/bVyxhcTdT3Ti+7G96zxEmJyXHJYGG4GLM+Wjxze", 491: "sha256-o8mCT2rSHE3VttjCY/xp2ptp0vFz3qlTc/bP3/YwcKw= sha384-fpivMuQgQi008xSPHnbyicRxmpW0yniYhy92L6cFtEEgasRsXtTYA06wWykmdJ2z", 526: "sha256-j85ChPg1DQibs8cAchJbHsWYaRcAxneDBnootEXUyh8= sha384-UkQYumUOPBkmypcc8iI1sHp3Wfmv3I/n6fBfuGU826Oo+mHU0OsLwLIC1Qj162Ob", 600: "sha256-Y24OOFO81yuHYB/t2IztLKbIoxO4EbXkfQPWa6tGxHU= sha384-0FAoNkaV+Hz7r5Ds+rX9iyEKiDb88Od1jW7vEv9e5UULV/Qkpzaj79oVnSLC0uY7", 766: "sha256-rbJXG25hPmvWGUjRi4pUD3rUKRnkQd66hC0n7u3cnYY= sha384-c2pLYYnxDIEjIRn80faZiHsm67tWQSG69B3usLz/wXKwTsdEExuUE3GfIbaC0cSQ", 927: "sha256-qvkmIrFqhkQGEPbuHvbMsBklH1RBSEP6FCfoFhg9Iko= sha384-q5assuvNtSmO/3KWFuw7FDpB2p2TS0tPJvL/Z/R30EPdvzJLnSiA5ypqpRiXMdCZ", 955: "sha256-8vJuuUiJoWEa56eoVXRnyAIqzH/q/bskTwtyqDCnhDQ= sha384-9iRCp8eMY4fb1PrzMqr1+QcFkfkx17CB2TaCHlmEPh9E1vKY9nnqiXer+upmEHWl", 1139: "sha256-YUd5SzD/rGaHWTwGQT2vmqZy/6zgJYi//qimnQS3cM0= sha384-lO5UIyLDxGJ/aCm4Uv4ayQhb0InqzDDtElVP1KDdkHUQJ5+zuQe5SR+YwdWGkERP", 1222: "sha256-L8WbF3IrhXNB/mnX6UxABDA/Y4Wr02VycOemfpfrfUg= sha384-JYYtER+i5alXcO6rsln/a095dWVPq6V+cI/9iFhQNBoYljFG6Z7jDPF2v67uvEVj", 1229: "sha256-gUuZNJ6iadIVGFpPNqJoBm8eN36WwwNn8NM78oN8jZo= sha384-LVGsgc43wX/cdHKnwiD8mHCYDdalmPk4JU/Yw8vysEw745EbPGhjpUvFaV9ZaAxM", 1249: "sha256-W6ZIqIjt245a4sVqBHO4mBaCKlxVh7oalZ9c82AOBNs= sha384-W4+BIC1aFJk9oj3bcrkYVNbrjWCVt+fb5Yq/yLPbdA2hHqR2e4e1Gamzt3y2pbJt", 1407: "sha256-ri8kt8Kyk+hNcmKuDyhDYy6hgZz1pBjrfkTpvzpmHwM= sha384-rjAj+ByPXwkvep86XkozvUA9cCD9FXnqXinAwPFZ5BYLUFxRqsQuxgUtlLjT1GtB", 1448: "sha256-YvOz4fTZIV4RZ49XWc3Bl33+iP9czI5MroPMcyan+JQ= sha384-tx+iqRBFj5AzFEjmbdrnKi/ZCi6FeArbhHBBnSuiQJozDPlRQbEpcp04EEv6Niif", 1566: "sha256-3rAYRnIeH8gF932ngBsXARUqoqeEe92iCcH7r912+vo= sha384-35N9l9Cfy/h6pruFAzuYFqk3iHzLLf/5FRj9J8/grSl3kgA9IxgVeDgqa9CWahnH", 1620: "sha256-lDFvlrbIM9nTQJDQiF1UBhc0cc33IE1c9kRN+FCvBG8= sha384-Mm7caNHQKatM77lC8NFWVEiJjkRRq4ksWx89H0D91PXpZ43FQTrbnJ8KpIlyATsj", 1636: "sha256-Ao1zufv0a1X7VBzaCIBV5v7Ls8ErZib/G/Fg52NKQD4= sha384-B56I7PZcyiqEzzfcnzW4RiYfjUGnfTxpoXOz5DYHFsQYyKGgMILmfNj0GEdZKOA9", 1649: "sha256-++F4l5SmF7g1AUunwKpM4O6kcrcA0Ah8iTliRN0O2Jk= sha384-XqaHbOQOXpYn32a3mtJNdZ5ALZ0CIcPvWGVQ6d52JV48mEZHPrW9zRFZnqGvFTAc", 1704: "sha256-khUaNo/kS8eUNYkZe9BwUWRnHEr5Ri/z02IX5TkPStU= sha384-LEnEx3uZKH+9CJlHSIdome53I/TQ6WMrqBvz4IOD1XwLR79up600hApp7jbCQ0WQ", 1783: "sha256-mV/R6ZciW5Qt/iB/OfiYRziAfSrW1msLhBGIc51bEQA= sha384-CLAW1i8SXG92o3sX8xqcFjwHRsx9wOctTeNamMwZtoBxjU/WKYHgZYoDpQTMOaZ6", 1802: "sha256-W7MDG5Ve+JdVoyb7SBrKkX5G4HzyIeI+OVtyGpcvudA= sha384-lQc8vlU35PW3Rh2xk+YSLCqIcibSPTakpnWKBmvSxzHIJ9DocsgrJPZl9ZFgNsAA", 1950: "sha256-gkp6bMQtDOdyhk478PC0VLOjPmgFr14cG/KhoXeb5v0= sha384-HYzdGbxRhzZkhbgwAkCvnLioOrjZjJI3DEflQ7J8F04B0ooBVa++5ppGqak9Sd9E", 2180: "sha256-Y/aLxeCgKXovtRzyvce/jkeJmaxHm0JDpP4IYlU2vsk= sha384-CkRvTu5gacp8tRP3gqwKeNL5LFlV8HtCFBFZdznyWYxCoVR9UZuwz+jqCISlHg0W", 2298: "sha256-hjWfOg1xdE2eGi52Gy+O7AlD7ExsgNrRKO+xBVIeINI= sha384-EgpczYEGHDIjJ0bf5wD5LuZdU781SU4scZ4XNP/lYCWXpl7pi1i+L0KE16ECkf8R", 2315: "sha256-X6Cy77lhKL6yXZuYcTW7HNfLJVGxstAPD49Amlzrwa8= sha384-C885+w6USJKv+N6LEOpB38B0Bh10ujvQMm/0P4w/GJk+clMEszE2NMjFnyPkXU6s", 2394: "sha256-OqYIir59O3HUtiYjeE+k8CGPnFpaKgMGxL2hooEeUhE= sha384-pTm2hh13/Z4qT4Yfa9Uh3T6HyBTf3sjzLgnG3QuXyelpO54pKnegm5++c1iqOtAm", 2420: "sha256-nSAsFhIcJIMUE2S8WLToKzFi7hj358pJ+hvGoorGcT8= sha384-3RewrAAe00/Fxsbq/4L6TlcA2Cp3oY1OMOzU/HW9OJxp0+0agG4rgPwK9PHmlYpT", 2470: "sha256-apc45R00nNpCoy9kRg5ZkeYLJzmZGEv9su1gXhCUr04= sha384-5ucXY3cVVTtQ7a77sTvhlS1K2wmAKH7TTc9/d4OjAifKmp8iX7fRaB7KbekGQLej", 2497: "sha256-7umSHDJJF/7OOyGjvGYyoFsbVkaVpFw7rzjx62pw5O0= sha384-N/M5X4nooeY6JQudy6hvxOSQp/Rq+44SWDBwkDzH2Q8IfP7gwSki/rw2rw8bLq2M", 2518: "sha256-At0Yp4eh1kM1PP0YFey8bkIfTd52DqirdkRJsosqMGQ= sha384-2ors635xaFVQQE6mX9zfD093bGqXlbMLMgIS5WyKxCJNc3h/7YOUlzAweIGocc5t", 2650: "sha256-7LRtxNYwkSOHf8r8B7OsulLWP3n09NJd+f74P0v5+MQ= sha384-MhOtFtP7ojqitCg3O5s4z0jR1KfOpArCr5bNHMTE+uA4l9RFKquQbD5bspfLdElF", 2723: "sha256-j3YgDmy0UYnOYdZemGdH9uyeh9+f1VC78EPOoS9kHtU= sha384-TVNYjanlI1QN9bOsN5gNymjy3uPZcclDr2mPVpVmpFLwPNiggp5e8+JpcOsd2k52", 2884: "sha256-1F95krnPxrcUno3g+B95Zd4fKcI53Mki2EoO0f9re1w= sha384-cXJftAu4dnIOK6fvboEfjvd/QtpCZeYuauXTBJvZwSsvS/s+nyFtPEGDnl8ZxbUp", 2998: "sha256-rnKpoMrblCRNsiUArNsLju8qHZ6PCPlx/1qg2vWnkO0= sha384-Dq9FuCNx7Rl5YCU0+Ui/k49ltPfitoMo/rgPd2SMymlyZTUlrt1xgHgyEHKPT+nG", 3194: "sha256-lngtiswPKURpumoSyBJWTVWQYrvinM3O4ZUuqUlyTcE= sha384-Kfj1Cy13WqSV5qG8mRTH/sLzmBe515nOTj6izGIAYkwpsn3baI6x5i8vGl+zQckr", 3263: "sha256-tAkWZ9V61rgHv5gxYcllXl7iC9vRa9Q8fiOjMrEF9zw= sha384-EYJGS96okSc9/QmwdRA8pe0w8TkbXw7Gh/Rsw+uqTum8QAOJsmk9SrDaWfp0M/jw", 3335: "sha256-3RC/ZMNuZOjDc8kt/QyxducrOYh3PFMlR1k7tJ8OKbA= sha384-j/9Yt62DEMjUqMPS5ZSDVE/joOuV/mMoyLZTSzlCj0d/dqpeVEmOUgQZAhZhXg4P", 3517: "sha256-u73bFQ7Km9Aa+9JySXqbGXxGZqeafi87cuLZ5LgJjOU= sha384-RCbhTcWZ1xVNR/gvbMlJLhvdMKNij0/nYU+fre+JPZTJKNpVvSyoHmVeVH+q2fe1", 3649: "sha256-18HbIOX05ElLp1FelNi+ItbZqIPEvQXtQ0Z1VWHt0vk= sha384-y8gL99BvZin0jMnvnpqZjKQaa3YGmZRspGqm1ochyJhTKQAfHGed1fhJ+ktRk951", 3657: "sha256-1XHiZH56tLwnPmTgTvFu3ul59qaVlUgfKGLMpjZRBLo= sha384-GViFIaIWmWh7Ufl3zl6gHDJBL8MhKSj2aHrKu+TRl/U3mYV9aL5xvRToM36/JKEO", 3679: "sha256-NUUnGHDHdAUX5ZNTl/FR72iPLFxuq7i3x8UAlyB+gzw= sha384-Z5irdzvO1pKKaolhSTGd3ZetVqR55IgZcc6pQl/4G9C51xFpCwL0TMgKCWUMKOSk", 3691: "sha256-YTvf6y0drn50b/Qmxrclbax9ZSaqM6r579eg8zc3Iz0= sha384-nWrD7l7Wxsg0aA3+C9F9W7vW/UpaB9cCEBxX4495ig9udpmv3VwXhugDiH22D4Jh", 3760: "sha256-k9lx9LJtEDBuPH2YSNNM2C0hrQW1NPEDBXqiYXsTiXU= sha384-V8iKogEh7z57Dufw6hAvZBSdsCBEaVlzcTj0AGS9N7XyIefp9TsImfjwiUy2h9wX", 4195: "sha256-URvejfX53yYS252XHLX2S8wxT3HOh650egkFM0uqhgg= sha384-+lmIazjkVj62c7r+pQbT7dJNeEee/2gsuW6RawkrJPzOT1Axu9U93aOJ9KsR+lV/", 4259: "sha256-UP+04L1jiVH5NFmvYBCQ1rk2fKP6uCbpN1VPasttvzg= sha384-8KtGt4K0X4oVE0083qtn1nXaD5yWDGNVgRNM7aT5r1FShOQ1EYxFTiOGEBjiqXlG", 4279: "sha256-coxGqUUZYHZlaKXz6Gd7x6JS1je2dBYq3QMmnUVWEr4= sha384-qzU7MRRttliE1xnCNcpvMUXYn50I2LOrjbdb0MD6qvlAa5N3lxLRqXvggul2Of/o", 4352: "sha256-lVx8pZ1iRZl2lx/+uy913qHVV1Y7Pt5HdBrQXx8oYkg= sha384-LMQt+ODeEyuskDbogw2EQsIpRbqMpg1AO8PdmcQ6/OWUzu/P59dS3ZyG7Rb2cR38", 4381: "sha256-npo2THPWIgjnBSupbgYJUVnsy2Ppu42wxoa3z8Ca/Lo= sha384-TzyUD36d0mYv0LB+1MkcstRzq4LSLIqtOOnNWar6Ph6iNh4FPmp+NfsOvUfc8l6Z", 4430: "sha256-f8RyRb8DUPJfZxGtL08tptciju8U4dVd3ecn/1vvvfU= sha384-4kHqtZJ8WmIM4UMNpyYn03v8AdmbmBzEEH6wjgkghAUuFqsXY+Z9F30BgEtAgZj9", 4456: "sha256-ouheGVPI1CTNDvygl1KaSVoqk4oWsmx0j84Nw8mqkP8= sha384-iAMAtIuol3k3pL/Oy1ivKOlU9XklEHW0wgKmBiQlfLh9ryfm6pPryphQuu8EzUAL", 4482: "sha256-IksnIW9RUWOsNmbmhVMGkydXrtW+Z43Y0Lp8baEPD50= sha384-N4ySIPVIt2fQnMxznLZQs0IoL+OeE1A17HhBWcnxnFwmLDMn2X9SKneoF3s7XZRS", 4592: "sha256-zhEAlSNm3Ywwpqq6PA5+IinW9zINSAoO3wyWYN3E5OY= sha384-Hs+zsHiT3i3yHjOwI3gIgtEf6BoythuGnZYAZIHvsjIMxfXmgv5f1H1vK3HsbJi5", 4806: "sha256-AaYTzhWOatJkKYjQFJUqUv+CxLCJ8SIMblcsUs0sVP0= sha384-dpHqbIMx796sTDAP97ooz23gRDcUIug67Vkxc3Aj81SCgJjh3gy9bENLT/tT+nuG", 4883: "sha256-td0vifdKoBCsuM7qkXQVJ5UDXBMo1bOOciVPegYMb6w= sha384-iTqMVOSJp1K/1uS85AO00GeSbGABXsFGa+aBzhTNUfkcteFrxdfriGpMA1aFVD+J", 5167: "sha256-uerh3JdRMavmM65SienIa3Jvm7L+rSkYWXAcOyJFOXg= sha384-m42qPq13OCQgUJiPPJUY9mHSFKGRekpImksF17OrCK4Ee0oEwtervCu0iojBpmpW", 5250: "sha256-VOsnWtfkjPfuVZp1IGNLXYbNMvBBi3gjbxuAyjuz4zg= sha384-SYNQ1sFnhcRLUqFL3Q7EJ1MvCOvt3ftRk6JMc/2KsnBK3PqjR9pNLyL2/YPfGKoc", 5277: "sha256-1akwQDwYX9l8xPyxMrqaUquk/NZw0e1rqVAwcVPbALY= sha384-wOPpOGVWSPHjRx2v8dE3KAkA5zaDKtoVYAoeL732W7jdCBqc50SH8AuiJu/cfxcg", 5369: "sha256-7esOp2p0zflnijzaUGQ19XG0oCGiQTGmlhLkZEjPvMQ= sha384-3lfbsLtEBvkrpepQFpxD5m96/4RWUEA1M3PAHqABugkgyybkTJyOtjgWby+J0yRa", 5380: "sha256-nYqF8MD8qEnNq4en/UPHYtfzc+xXPY9FPfmR+Qpa96M= sha384-SqWoTOvKsOYKHnpLoQ0xwOfZ6WZ4UXPDw4v6AL5JWDJWGhQyTiJ5+bAgmIRxtB7x", 5647: "sha256-oBj2qSN/W3Ad8oeCKMgdzRd12Pwip74djFBzmm/75T0= sha384-yY+JA5GadN0As51AJXdpn5qUAAgOmBvmAwMEWYAdTfZmtwYPfVweoVahzuXIa1DS", 5858: "sha256-kahnDGPykUMC3ygNhQPL2kyDa7zb3tO7mv3Wi5+Tzk4= sha384-0gzDKOABz3tLlTLCaYp4UHJfOqDJkHHH+7BhJx6TeuhWr1KfE3ni3m4JKlhp50rL", 5862: "sha256-DYjq0zVw9NNSz+b0rW77IdiDzs+c2VhydDsep679m3E= sha384-eJfPQBB/tMjVb7VE64DTJ1lW8AJWYt6A08UO1XJnVV6XaNS9xoOuUSO8ist6FNkT", 5938: "sha256-p8S2nhTGRTMJG5KMeXnLvLD6/rKtTQKuJavI5mN8rDQ= sha384-rfgAMR0GEvNtBIxOjYYToEVOUOTf1sm4cG6OHWEy3E7A/pNwNTAzQE2dIkTNBeMJ", 6014: "sha256-pJDGZMwiJzYDKkaaxRzjrsbQ7gJgUn4uZXhemEIuu44= sha384-+otcSFccwpU4Kb+tIzSfMeth1Y2MbHt+Vacu111nCbPEO6m+n/Ehc1vJJuqY2k03", 6142: "sha256-ykbHsqfsMSRB6Wknl9e/xNl7Y6Hvk0/nlinFKaRXIMg= sha384-ZCkdYYh3q5T0OXb8M1zASNOd93sNpeRddaGC+mBQmFF0jMUn4LorrNWc538iIh2p", 6222: "sha256-DpF53ADebgg+reNSmLt9JDQHW6uUNxUWp/4M4lUu/F0= sha384-7iO9fdzFgCzbgzTVujAinHU/Tk6tJaFnUoqJJ+5Y1Ixzn2RI1tLcaAqzp8H7m67m", 6346: "sha256-OkEveoYxQyGHC8PQs0MTMtTTlhT4EzUv/twPxaDhtqA= sha384-rDu0aTF/WAzqARxw4oxT9jP/0pd8nn304O+8jD3H55/nlIimpgcVwoXLMsoAWnjm", 6710: "sha256-QssXaNSTkTdUIQstDbFfPWXLGoiQsn2PR3jtJOsqJ08= sha384-rGMFpWv42S1noOjWErRavKJFjRwU7RtbfPX4R3XOozTRanYNPaDTYmjAtc8Q30ii", 6808: "sha256-pNObU9yj/AkXnjDBosLZQCZRAJH35KqHCnW5TZaShxY= sha384-ypxbPsg3AqPGFCxVg0xW7CkxrZUEt5GnlIpEiypsppXnbtsY+V1MWSzN46sPyWw/", 6863: "sha256-X9eweLSAQwrgMfWVfq7I2kIHg4Qn747YzEz/DgFTkvA= sha384-irOqvbgZhgxOkpNqrvDp8go2hbGLPl0TY9bKM88tGv7oAxN0zco2eIsPwH4otswj", 6928: "sha256-GP2rtdfI5CYUNDOY7mZdBsLtCIS3zMWUs3Rk7sL6T4Y= sha384-jFOK03bssiOsXxWB1y2IEXQqQnfDrijP2JdRAhS8CLLg5cnkO6IGS1DGlI8Yjg52", 6940: "sha256-0+b8VqcZIRhtaOVoaCRepnbY87yy0EmkNYryR+l4qmA= sha384-W77e0oZ/OcmzWEpLPNcg2qySa4RadKiAnIgOSxa+tccQLPcYF9oaT7St5yCyAxGi", 6996: "sha256-9VbWA1DfYwSGFV3zFO62tgvLQtvVGtgWOen1fMVp+3c= sha384-ec5RZt9IvKma9IrIuIyQapXXNQnrf2vLFUq+ybBKw59IoddTy114DOo9ZYdGLVQk", 7020: "sha256-78SwtPpw22TtrsyXxlhLLL1iUp0G4n6WqQqHWQwU9sg= sha384-PbovtwdvBsJq/WIBjf4hU1K/Bupbf/6t4wpTkDG/E8V+i+WnmFkfhg3KpVrNtQRp", 7132: "sha256-Nc82ozLoLEyXVq2PZt/LVVC7WvChSsjlCJJP/FgAhLw= sha384-Xmho9dGzX+EwF9PWqQFjHXFzfo27SSLYzR8J3ZSUxoTQ6cWoNLoRKbAS1SXzu+Q4", 7326: "sha256-faCkHFjgTvMDncoyvcejrBXIkkHPVP5wSnwWli8oDII= sha384-qON8R7PsCkzg5hqHbTQeOVbeoigBkgzSgI6sg/zwlfbteKVPqC/L07+oYeVC8kDA", 7512: "sha256-eEXH4FNcCT/mGHxVNas+3GcsMTrxnvoqO4LKRmBeOWs= sha384-Wy5BNPgrc8el2VOe6HPLSYQAiCjjyQnMgwFqT6ZLU5e0dz8Xw6hy28Hsu9fWVhWA", 7725: "sha256-oKKWAyWf3NuF2RxighwwVLV7trxj/hTdp09mc3gWf5E= sha384-ckOOzcU/GYF+vBOJ0nqjkkpnlmxPZKLLDIoeoISrJrVM4c6z3/bkRyRtuMhhdlRD", 7824: "sha256-4ClPmLWyUV3ste4uzL6fyQVF5ecNLA2x7j8vYRe3seY= sha384-DAPvZZa2Omqjajj66MqvoIQSOToz+9b+Cnf9uB9kOkthiKUQ2CDGKksHdRHT6s2+", 7826: "sha256-+/n0lBM1Xj5apym4oW3Pt0seld2e1XGFFwzciAUQSho= sha384-lI5w5jx1pLX9nJRoNfOqhFO8UeD1wLyW9W8Rs/BIEOSlVEpMThj2h6J52LvshjhY", 7909: "sha256-Uime3IgYDnmcEeJPTgMZ0OsY/Y56Z6lEop8vttwPYXQ= sha384-Cr3ZVKwUj7K+y2gVJpFGd3QNXF6+XY15FBld58e20bdgBuxuEpepAdwWDy3kReCW", 7920: "sha256-YM7ZIgty3C6yK37t0cS6oKJe6QhLQMxE2KHgboTbMkc= sha384-kkr2jDF+0U8tUD261JpNUjyKv8LKH5pBCi53TpicDLq4en65vwqAwicipQQkAS1+", 8041: "sha256-GBadnxhkeHIPGXjX/lAeP7eR/htZbC8UB2jB1X/5dsU= sha384-pXDsuUBMkN1DXbk7kxz26FUVYGEKRF+anuMNp1fESMini0a9DkpoMR/dYmDY3yQZ", 8053: "sha256-lKrZ9IJogLoptD39LQedHsl8DBfM86aNrr6yI4pnomw= sha384-VO3USlwxz9FGt9GdqKKMwFG9uLGvig5DM0/E1L3rWDXBAkypvTYszyeetNXQPwH1", 8076: "sha256-+Nuzz0aBDe3u803CP5OZ0gVPXl+Wilr0LKVFXCIJL6c= sha384-MBo4JVkyOJa2xlvLat9b+BbjCchP5X0X2kcRfx9vX6OulzOz/PEkd/AsmWUEgO65", 8132: "sha256-1zv01xaVqftc0xLlA0VBtCxCriKrirkJKCwubhMC+f4= sha384-DBzvciiGoHnzheGzPnVeRCXxDghF/hNImaZrcCPI1+oUFLKTS3ed3DOlVJNMtVjH", 8171: "sha256-04HzWtNcmANYKDr8+b82ZQPb3KqqXu7wjiPu+waJySE= sha384-3GB40DEYonCnXQBoK5Ofdzwt9HipSjtdW1I5URC1ghB9di0KExwcSN+UcicP3vAu", 8248: "sha256-+QzsEcKoUlhTntD1ijlEyhSHIiYgKYg3Dq31zacQZBg= sha384-fxXOpnKLeINRKy4kDifhaF2YIkeVaTwM2Ts0kHK0mAFGm6zA0q3TOZjgVwupT1eY", 8274: "sha256-xAiRGA9eb5UwAIfhzoYIDQgzA+cNp6IktEzQTEpPGjE= sha384-DvHU11DJx+yc28h73wQQ9MYj3vJl3ZBJ99/WZvgaYzNfd/8vzx6zXwwu8naZL6/e", 8385: "sha256-AGk1CWceSbnxDhpseti60L3LBv6YwBaZplTRpU5TpU8= sha384-jUyj9WclzYuo4nVN+I/dk1Dj70wkXxTGOeO56c8slLxXlKooRlZgSan8mFbjKYuE", 8409: "sha256-EniI2hnDyiNFxOePpnoc2XLt8DJPMF0MXdeEnXyCeAE= sha384-5KzHGYfyAxFz8WSwkl+WQBTEdUq25Kdk/0QifRQrAKj7eRRNXTIfDu2Fk624PBNC", 8444: "sha256-OYvJurgXlMe1FccQzO/7PrUwfNQBml4Zyt6orV1MoJs= sha384-1m1y0NyFVXr8oM4aXH2f4o+7OVC071bLZ7pFQZzA7XulhoaOV5OkhnRNf0eCB1Ju", 8658: "sha256-AHoFV2E2qHZOhwBMsKRYmIui+OE3dwt/OCPDenaDi54= sha384-CCqEmQoqVLjWOdYWnFf/xaNPWoIeY1WltDvD6Xsk+f5BJWy5V/JTar7gMNlz5W5u", 8665: "sha256-HG4ZBbBJ0B6EB2l0PmKvpKrMosbWIv31dxmrFouCBq0= sha384-V5AWrbUrT0gwhZl6iK0WkPj/J91YwaLYfiXOl2HAqArzIwkd0nN7wYPYKxnMlb13", 8705: "sha256-Ljgb0FPSfWN0O9bArX0v+F8pFx0aAm7SDp519mVv4Uw= sha384-eSQLH9jl4F5sXY4Gc5fBGWzMjTdiDuYhGaqNB+DsTTnJJPhojRtJUPkoipSm3WxR", 8780: "sha256-fnfym3Dw0VoCXByf69d5aODskd99qUzekH1xXmLYe1g= sha384-tSS97JrC7a91fKFcHxok51/DgXlAoV5maaJBx8nYSDgJtZomlHKFj0ye2d3TuXe1", 8786: "sha256-9j9gpbRD98aLukdy4ahUe/x6ZpQh2FbCCsBAMRVnfz4= sha384-ersDhpb+cmsABomfDxhwHyCHZIb6KYuht5e2fBVDK6/t2X4fEjwplBsTi1i/p+Cg", 8855: "sha256-/sjvbxdsHJeftAUIQNSx7b6CuFvLRYbCuNdL0zbCqt4= sha384-lNT0oYpXm7ib9QaBrmNlTRAOgNnRdoN4GS41Zhwk2LdklInAbKxQEYp2UakTgi7V", 8858: "sha256-07BkRFQy4IEhi1bTc7ISpfXh7ZBNAiYtCoilZXLsRF4= sha384-1MOYPMAId8XyAyDbKFa7oroMWsryA9CArmuajE7RytwkbopbVm4x6utiPmUnGOSJ", 8893: "sha256-HuRPCYjJe0LZ+wQpisLCb+b45kwMXv9WsE1a2iUjwgE= sha384-dTco7zlMBNVCycDPU3OX2EdMmiBcJJXrweLubIxbwidjTJPw42+m9yX90w5R9U++", 8911: "sha256-IrE9ym+VyWxjDKoC+QbMTqClk+bdxSJVbXa7gfsgMIQ= sha384-bmAklrod6XulWclr8d/gzQ/AlMoDwATbRwmOEJJodYVeydGv742F+U3vpxbXwF0y", 8913: "sha256-IEoTHvKjkDhbSF9GGfUR8rdL0PCWjhWygfbhqEvry9Y= sha384-19Q9PPbZXgr3QRPQEmLg2PiV5s2k80wi0cvzq0XdKao+OgLXnIZwLJrjLOBNRisT", 9040: "sha256-f+fhZoOxSdJ/nntUza1IJNsyqoDN/zSDkHdwiwMDSs4= sha384-bmPDUJ4rk0lCt5+jrY3OLtgk0IiiUC74xtOHAAd/DKYu8GarJ8SmFAJbAJcTjOiL", 9051: "sha256-qL2gvlpvCYkAqoAazvRMWAc4Ajv/COm8oaglBjXa9ys= sha384-sxo2ScHjnHFuzvyOg1uELviwphl9sMrcAMCIU/L7mOuKFb8hjguqRu0srwZQ3WcA", 9080: "sha256-t8JI1KxQH23sGiEm1jfKeYJoL6Qf5Oj5Q2lcOu8Wc/g= sha384-dc+0TLV4j7c/xZupfXsMwXvmw2Nf9l9j4NjfgCMDuj1kFgzwHu0jU+HzHVJwlmGa", 9138: "sha256-4YRyYYnloF4wNPlsUF25awl45KK0Jg/zBHjQQxwhsXc= sha384-2kwh1tg0s+348QgpUhDMwWL5kY1pfxuW8s60nK+7iAtkzbvcnD7gbShP/0KqhP7f", 9321: "sha256-f4yhTIQNtkZ1oSB14Yol+PeMNjBq6ZTgHv1tH96TeuA= sha384-ZPoTRoeTs4eqYecsu8Rp4UmhAGcQ0gKRRibp1vFVdCcedMz8fbz2n/vFmU6Xa/p9", 9361: "sha256-egeH/2fokQNDZZbbME/c52JJKL4J5Otb3DXinHKRLS0= sha384-3IO/WGVq8WM7+AArXGkUOyGQO9P9tFh2gsJ3H+g1MohIpBwprn9IpRKdWxMLXZJu", 9395: "sha256-Y2cXxcciSOPqkx9gtHzrhivKAi18PoWRZDGj0ob75Io= sha384-3/GA9ayHu0DY6/PEoDssOEgG4a/m3fpmz+i0iS4JZFyu4CSycOrPhJ/9G5U83NG8", 9419: "sha256-2b/7Q7/hSI95aBKahEOCD+GE4AMWbZ3polA3l5RPPTg= sha384-kklM15zvYfdtSGqFa4xRWti+AFWWazFfylFeLCzlpC1+6W9VPryCrqJ3GAJt1zhh", 9454: "sha256-s9jzhm3+CZ91+oJWdNQ8iyBZsXmyLVkLGotNFyJ8HNk= sha384-2NvQ2l/HjUEc/A08BLi4hU5CDYyd9EWdPAqExCIWXMHQEuzkN4UtAAFZY2hWw8nq", 9530: "sha256-nyYWHrHEREDow9+IrF3iNynT3UiwMJ5SCIuv04q1cEg= sha384-3ufOZVYKtweD7H6apk0wLp6O8h+DTa537Kxvp0R1cWXPgazmrclv6uq/E5LNJl+5", 9584: "sha256-fML8yBfekCaaX0u+/leR6N5+FJCiFnOXLwUF4FuPuAI= sha384-w1H14+kMQJSbMe98baRKNdZUWtvXpXUHgVTebfeFpNJw31bMrdPDHLkWFZtniBwd", 9587: "sha256-3TcCekX49Zol5jm1M+BL4ogvgqjlMLuMDDf76iVcMQU= sha384-JjGnJskNYOE3vixbgPKmVCYqRv1rQECQLo8rjzG0pvT4Tdo+lNqBiErjnV3kyv3F", 9597: "sha256-li+ST5uQvXRf6xl6nKgLbPd7eMcfap49CpuMVU4bwGU= sha384-zNaQzWlePCHnVAitXJZBGZSb85tYLdabkqMfXl869+ID3UYDVCj4Dz7TlrkVhHKO", 9620: "sha256-xRozcvIeIaxP40BpO6S88rb1Dfeq3+scRlxWuSSF4no= sha384-P/W4VZBX2KBjUIaAI+WcZF1cssG4P8cbiRJoLzu/ItECvaZOq3/EyUFXIMVRdmyU", 9755: "sha256-dEyiFdFzmm8o9ZMcPEbfKTMELmZz0XkEmeISAaJCMew= sha384-aUCjU3n3qRoArCtnOmNNp4yl0y7QXKEaL4avurgC8mxFvo+DWD4AEUY3WzGo1aJE", 9868: "sha256-eWGr+ZPmeTrdeHl0sFjasoSL1JF1zyI+d0hFDdCBk9M= sha384-c95y/yahDYpTt+hxa9nNyMqSp9T67X2LvoNh1qhZs4nEF0XnlJDObJfBCo2w2Pbi", 9895: "sha256-GvDO/TxMYDd+qCjMAlJPkgqw1B+5Yh0g2BDQjHs/Xqk= sha384-1un2iaxhXgDUwnJRwzgt2PE49zOZIDI8KjWkweMGtRMcUdjlH+0goGnW6QYPI7Ol", 9955: "sha256-uzODsaGepvL+DPr8GZSc7E6tKZaVgyHr3k5rnLUIe9o= sha384-eH0bcJfdsYcYpuBw3G7ZTr9KvqIQHrp4Nie/OmUDlApHnC8qw8GqYWU3W3sCHha6" }, (() => { if ("undefined" !== typeof document) { var e = e => new Promise(((t, r) => { var a = n.miniCssF(e),
                        o = n.p + a; if (((e, t) => { for (var n = document.getElementsByTagName("link"), r = 0; r < n.length; r++) { var a = (i = n[r]).getAttribute("data-href") || i.getAttribute("href"); if ("stylesheet" === i.rel && (a === e || a === t)) return i } var o = document.getElementsByTagName("style"); for (r = 0; r < o.length; r++) { var i; if ((a = (i = o[r]).getAttribute("data-href")) === e || a === t) return i } })(a, o)) return t();
                    ((e, t, r, a, o) => { var i = document.createElement("link");
                        i.rel = "stylesheet", i.type = "text/css", n.nc && (i.nonce = n.nc), i.onerror = i.onload = n => { if (i.onerror = i.onload = null, "load" === n.type) a();
                            else { var r = n && n.type,
                                    l = n && n.target && n.target.href || t,
                                    s = new Error("Loading CSS chunk " + e + " failed.\n(" + r + ": " + l + ")");
                                s.name = "ChunkLoadError", s.code = "CSS_CHUNK_LOAD_FAILED", s.type = r, s.request = l, i.parentNode && i.parentNode.removeChild(i), o(s) } }, i.href = t, 0 !== i.href.indexOf(window.location.origin + "/") && (i.crossOrigin = "anonymous"), r ? r.parentNode.insertBefore(i, r.nextSibling) : document.head.appendChild(i) })(e, o, null, t, r) })),
                t = { 8792: 0 };
            n.f.miniCss = (n, r) => { t[n] ? r.push(t[n]) : 0 !== t[n] && { 526: 1, 600: 1, 927: 1, 2315: 1, 3335: 1, 4259: 1, 4456: 1, 5858: 1, 5862: 1, 6222: 1, 8248: 1, 8658: 1, 8665: 1, 9587: 1, 9620: 1, 9895: 1 } [n] && r.push(t[n] = e(n).then((() => { t[n] = 0 }), (e => { throw delete t[n], e }))) } } })(), (() => { var e = { 8792: 0 };
        n.f.j = (t, r) => { var a = n.o(e, t) ? e[t] : void 0; if (0 !== a)
                if (a) r.push(a[2]);
                else { var o = new Promise(((n, r) => a = e[t] = [n, r]));
                    r.push(a[2] = o); var i = n.p + n.u(t),
                        l = new Error;
                    n.l(i, (r => { if (n.o(e, t) && (0 !== (a = e[t]) && (e[t] = void 0), a)) { var o = r && ("load" === r.type ? "missing" : r.type),
                                i = r && r.target && r.target.src;
                            l.message = "Loading chunk " + t + " failed.\n(" + o + ": " + i + ")", l.name = "ChunkLoadError", l.type = o, l.request = i, a[1](l) } }), "chunk-" + t, t) } }; var t = (t, r) => { var a, o, i = r[0],
                    l = r[1],
                    s = r[2],
                    c = 0; if (i.some((t => 0 !== e[t]))) { for (a in l) n.o(l, a) && (n.m[a] = l[a]); if (s) s(n) } for (t && t(r); c < i.length; c++) o = i[c], n.o(e, o) && e[o] && e[o][0](), e[o] = 0 },
            r = self.webpackChunkorganimi_app = self.webpackChunkorganimi_app || [];
        r.forEach(t.bind(null, 0)), r.push = t.bind(null, r.push.bind(r)) })(), n.nc = void 0, (() => { "use strict"; var e = {};
        n.r(e), n.d(e, { FILE: () => I, TEXT: () => V, URL: () => j }); var t = {};
        n.r(t), n.d(t, { scaleBand: () => kd, scaleDiverging: () => nv, scaleDivergingLog: () => rv, scaleDivergingPow: () => ov, scaleDivergingSqrt: () => iv, scaleDivergingSymlog: () => av, scaleIdentity: () => Yh, scaleImplicit: () => xd, scaleLinear: () => Zh, scaleLog: () => am, scaleOrdinal: () => Ad, scalePoint: () => Md, scalePow: () => mm, scaleQuantile: () => Sm, scaleQuantize: () => Mm, scaleRadial: () => vm, scaleSequential: () => Yf, scaleSequentialLog: () => Xf, scaleSequentialPow: () => Qf, scaleSequentialQuantile: () => ev, scaleSequentialSqrt: () => Jf, scaleSequentialSymlog: () => $f, scaleSqrt: () => pm, scaleSymlog: () => sm, scaleThreshold: () => Em, scaleTime: () => qf, scaleUtc: () => Gf, tickFormat: () => Gh }); var r = n(84391),
            a = n(65043),
            o = n(22908),
            i = n.n(o),
            l = n(58168),
            s = n(71745),
            c = { WebkitFontSmoothing: "antialiased", MozOsxFontSmoothing: "grayscale", boxSizing: "border-box" },
            d = function(e) { return (0, l.default)({ color: e.palette.text.primary }, e.typography.body2, { backgroundColor: e.palette.background.default, "@media print": { backgroundColor: e.palette.common.white } }) }; const u = (0, s.A)((function(e) { return { "@global": { html: c, "*, *::before, *::after": { boxSizing: "inherit" }, "strong, b": { fontWeight: e.typography.fontWeightBold }, body: (0, l.default)({ margin: 0 }, d(e), { "&::backdrop": { backgroundColor: e.palette.background.default } }) } } }), { name: "MuiCssBaseline" })((function(e) { var t = e.children,
                n = void 0 === t ? null : t; return e.classes, a.createElement(a.Fragment, null, n) })); var h = n(34615);

        function m(e, t) { return function(e) { if (Array.isArray(e)) return e }(e) || function(e, t) { if ("undefined" === typeof Symbol || !(Symbol.iterator in Object(e))) return; var n = [],
                    r = !0,
                    a = !1,
                    o = void 0; try { for (var i, l = e[Symbol.iterator](); !(r = (i = l.next()).done) && (n.push(i.value), !t || n.length !== t); r = !0); } catch (s) { a = !0, o = s } finally { try { r || null == l.return || l.return() } finally { if (a) throw o } } return n }(e, t) || function(e, t) { if (!e) return; if ("string" === typeof e) return p(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); "Object" === n && e.constructor && (n = e.constructor.name); if ("Map" === n || "Set" === n) return Array.from(e); if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return p(e, t) }(e, t) || function() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") }() }

        function p(e, t) {
            (null == t || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n]; return r }

        function f(e, t) { if (null == e) return {}; var n, r, a = function(e, t) { if (null == e) return {}; var n, r, a = {},
                    o = Object.keys(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || (a[n] = e[n]); return a }(e, t); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); for (r = 0; r < o.length; r++) n = o[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (a[n] = e[n]) } return a } var v = 0,
            g = (0, a.memo)((function(e) { var t = e.children,
                    n = function(e) { if ("manager" in e) { return [{ dragDropManager: e.manager }, !1] } var t = function(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : b(),
                                    n = arguments.length > 2 ? arguments[2] : void 0,
                                    r = arguments.length > 3 ? arguments[3] : void 0,
                                    a = t;
                                a[y] || (a[y] = (0, h.s)(e, t, n, r)); return a[y] }(e.backend, e.context, e.options, e.debugMode),
                            n = !e.context; return [t, n] }(f(e, ["children"])),
                    r = m(n, 2),
                    o = r[0],
                    i = r[1]; return a.useEffect((function() { return i && v++,
                        function() { i && (0 === --v && (b()[y] = null)) } }), []), a.createElement(h.M.Provider, { value: o }, t) }));
        g.displayName = "DndProvider"; var y = Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");

        function b() { return "undefined" !== typeof n.g ? n.g : window }

        function w(e) { var t = null; return function() { return null == t && (t = e()), t } }

        function z(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var x = function() {
                function e(t) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.entered = [], this.isNodeInDocument = t } var t, n, r; return t = e, (n = [{ key: "enter", value: function(e) { var t = this,
                            n = this.entered.length; return this.entered = function(e, t) { var n = new Set,
                                r = function(e) { return n.add(e) };
                            e.forEach(r), t.forEach(r); var a = []; return n.forEach((function(e) { return a.push(e) })), a }(this.entered.filter((function(n) { return t.isNodeInDocument(n) && (!n.contains || n.contains(e)) })), [e]), 0 === n && this.entered.length > 0 } }, { key: "leave", value: function(e) { var t, n, r = this.entered.length; return this.entered = (t = this.entered.filter(this.isNodeInDocument), n = e, t.filter((function(e) { return e !== n }))), r > 0 && 0 === this.entered.length } }, { key: "reset", value: function() { this.entered = [] } }]) && z(t.prototype, n), r && z(t, r), e }(),
            A = w((function() { return /firefox/i.test(navigator.userAgent) })),
            k = w((function() { return Boolean(window.safari) }));

        function S(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var M = function() {
                function e(t, n) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e); for (var r = t.length, a = [], o = 0; o < r; o++) a.push(o);
                    a.sort((function(e, n) { return t[e] < t[n] ? -1 : 1 })); for (var i, l, s = [], c = [], d = [], u = 0; u < r - 1; u++) i = t[u + 1] - t[u], l = n[u + 1] - n[u], c.push(i), s.push(l), d.push(l / i); for (var h = [d[0]], m = 0; m < c.length - 1; m++) { var p = d[m],
                            f = d[m + 1]; if (p * f <= 0) h.push(0);
                        else { i = c[m]; var v = c[m + 1],
                                g = i + v;
                            h.push(3 * g / ((g + v) / p + (g + i) / f)) } } h.push(d[d.length - 1]); for (var y, b = [], w = [], z = 0; z < h.length - 1; z++) { y = d[z]; var x = h[z],
                            A = 1 / c[z],
                            k = x + h[z + 1] - y - y;
                        b.push((y - x - k) * A), w.push(k * A * A) } this.xs = t, this.ys = n, this.c1s = h, this.c2s = b, this.c3s = w } var t, n, r; return t = e, (n = [{ key: "interpolate", value: function(e) { var t = this.xs,
                            n = this.ys,
                            r = this.c1s,
                            a = this.c2s,
                            o = this.c3s,
                            i = t.length - 1; if (e === t[i]) return n[i]; for (var l, s = 0, c = o.length - 1; s <= c;) { var d = t[l = Math.floor(.5 * (s + c))]; if (d < e) s = l + 1;
                            else { if (!(d > e)) return n[l];
                                c = l - 1 } } var u = e - t[i = Math.max(0, c)],
                            h = u * u; return n[i] + r[i] * u + a[i] * h + o[i] * u * h } }]) && S(t.prototype, n), r && S(t, r), e }(),
            E = 1;

        function C(e) { var t = e.nodeType === E ? e : e.parentElement; if (!t) return null; var n = t.getBoundingClientRect(),
                r = n.top; return { x: n.left, y: r } }

        function T(e) { return { x: e.clientX, y: e.clientY } }

        function H(e, t, n, r, a) { var o = function(e) { var t; return "IMG" === e.nodeName && (A() || !(null === (t = document.documentElement) || void 0 === t ? void 0 : t.contains(e))) }(t),
                i = C(o ? e : t),
                l = { x: n.x - i.x, y: n.y - i.y },
                s = e.offsetWidth,
                c = e.offsetHeight,
                d = r.anchorX,
                u = r.anchorY,
                h = function(e, t, n, r) { var a = e ? t.width : n,
                        o = e ? t.height : r; return k() && e && (o /= window.devicePixelRatio, a /= window.devicePixelRatio), { dragPreviewWidth: a, dragPreviewHeight: o } }(o, t, s, c),
                m = h.dragPreviewWidth,
                p = h.dragPreviewHeight,
                f = a.offsetX,
                v = a.offsetY,
                g = 0 === v || v; return { x: 0 === f || f ? f : new M([0, .5, 1], [l.x, l.x / s * m, l.x + m - s]).interpolate(d), y: g ? v : function() { var e = new M([0, .5, 1], [l.y, l.y / c * p, l.y + p - c]).interpolate(u); return k() && o && (e += (window.devicePixelRatio - 1) * p), e }() } } var L, I = "__NATIVE_FILE__",
            j = "__NATIVE_URL__",
            V = "__NATIVE_TEXT__";

        function O(e, t, n) { var r = t.reduce((function(t, n) { return t || e.getData(n) }), ""); return null != r ? r : n }

        function R(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var P = (R(L = {}, I, { exposeProperties: { files: function(e) { return Array.prototype.slice.call(e.files) }, items: function(e) { return e.items } }, matchesTypes: ["Files"] }), R(L, j, { exposeProperties: { urls: function(e, t) { return O(e, t, "").split("\n") } }, matchesTypes: ["Url", "text/uri-list"] }), R(L, V, { exposeProperties: { text: function(e, t) { return O(e, t, "") } }, matchesTypes: ["Text", "text/plain"] }), L);

        function D(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var F = function() {
            function e(t) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.config = t, this.item = {}, this.initializeExposedProperties() } var t, n, r; return t = e, (n = [{ key: "initializeExposedProperties", value: function() { var e = this;
                    Object.keys(this.config.exposeProperties).forEach((function(t) { Object.defineProperty(e.item, t, { configurable: !0, enumerable: !0, get: function() { return console.warn("Browser doesn't allow reading \"".concat(t, '" until the drop event.')), null } }) })) } }, { key: "loadDataTransfer", value: function(e) { var t = this; if (e) { var n = {};
                        Object.keys(this.config.exposeProperties).forEach((function(r) { n[r] = { value: t.config.exposeProperties[r](e, t.config.matchesTypes), configurable: !0, enumerable: !0 } })), Object.defineProperties(this.item, n) } } }, { key: "canDrag", value: function() { return !0 } }, { key: "beginDrag", value: function() { return this.item } }, { key: "isDragging", value: function(e, t) { return t === e.getSourceId() } }, { key: "endDrag", value: function() {} }]) && D(t.prototype, n), r && D(t, r), e }();

        function N(e) { if (!e) return null; var t = Array.prototype.slice.call(e.types || []); return Object.keys(P).filter((function(e) { return P[e].matchesTypes.some((function(e) { return t.indexOf(e) > -1 })) }))[0] || null }

        function _(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var B = function() {
            function e(t) {! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, e), this.globalContext = t } var t, n, r; return t = e, (n = [{ key: "window", get: function() { return this.globalContext ? this.globalContext : "undefined" !== typeof window ? window : void 0 } }, { key: "document", get: function() { if (this.window) return this.window.document } }]) && _(t.prototype, n), r && _(t, r), e }();

        function W(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, r) } return n }

        function U(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? W(Object(n), !0).forEach((function(t) { q(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : W(Object(n)).forEach((function(t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e }

        function q(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e }

        function G(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n];
                r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } var K, Z = function() {
                function t(e, n) { var r = this;! function(e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }(this, t), this.sourcePreviewNodes = new Map, this.sourcePreviewNodeOptions = new Map, this.sourceNodes = new Map, this.sourceNodeOptions = new Map, this.dragStartSourceIds = null, this.dropTargetIds = [], this.dragEnterTargetIds = [], this.currentNativeSource = null, this.currentNativeHandle = null, this.currentDragSourceNode = null, this.altKeyPressed = !1, this.mouseMoveTimeoutTimer = null, this.asyncEndDragFrameId = null, this.dragOverTargetIds = null, this.getSourceClientOffset = function(e) { var t = r.sourceNodes.get(e); return t && C(t) || null }, this.endDragNativeItem = function() { r.isDraggingNativeItem() && (r.actions.endDrag(), r.currentNativeHandle && r.registry.removeSource(r.currentNativeHandle), r.currentNativeHandle = null, r.currentNativeSource = null) }, this.isNodeInDocument = function(e) { return Boolean(e && r.document && r.document.body && document.body.contains(e)) }, this.endDragIfSourceWasRemovedFromDOM = function() { var e = r.currentDragSourceNode;
                        r.isNodeInDocument(e) || r.clearCurrentDragSourceNode() && r.actions.endDrag() }, this.handleTopDragStartCapture = function() { r.clearCurrentDragSourceNode(), r.dragStartSourceIds = [] }, this.handleTopDragStart = function(e) { if (!e.defaultPrevented) { var t = r.dragStartSourceIds;
                            r.dragStartSourceIds = null; var n = T(e);
                            r.monitor.isDragging() && r.actions.endDrag(), r.actions.beginDrag(t || [], { publishSource: !1, getSourceClientOffset: r.getSourceClientOffset, clientOffset: n }); var a = e.dataTransfer,
                                o = N(a); if (r.monitor.isDragging()) { if (a && "function" === typeof a.setDragImage) { var i = r.monitor.getSourceId(),
                                        l = r.sourceNodes.get(i),
                                        s = r.sourcePreviewNodes.get(i) || l; if (s) { var c = r.getCurrentSourcePreviewNodeOptions(),
                                            d = H(l, s, n, { anchorX: c.anchorX, anchorY: c.anchorY }, { offsetX: c.offsetX, offsetY: c.offsetY });
                                        a.setDragImage(s, d.x, d.y) } } try { null === a || void 0 === a || a.setData("application/json", {}) } catch (u) {} r.setCurrentDragSourceNode(e.target), r.getCurrentSourcePreviewNodeOptions().captureDraggingState ? r.actions.publishDragSource() : setTimeout((function() { return r.actions.publishDragSource() }), 0) } else if (o) r.beginDragNativeItem(o);
                            else { if (a && !a.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute("draggable"))) return;
                                e.preventDefault() } } }, this.handleTopDragEndCapture = function() { r.clearCurrentDragSourceNode() && r.actions.endDrag() }, this.handleTopDragEnterCapture = function(e) { if (r.dragEnterTargetIds = [], r.enterLeaveCounter.enter(e.target) && !r.monitor.isDragging()) { var t = e.dataTransfer,
                                n = N(t);
                            n && r.beginDragNativeItem(n, t) } }, this.handleTopDragEnter = function(e) { var t = r.dragEnterTargetIds;
                        (r.dragEnterTargetIds = [], r.monitor.isDragging()) && (r.altKeyPressed = e.altKey, A() || r.actions.hover(t, { clientOffset: T(e) }), t.some((function(e) { return r.monitor.canDropOnTarget(e) })) && (e.preventDefault(), e.dataTransfer && (e.dataTransfer.dropEffect = r.getCurrentDropEffect()))) }, this.handleTopDragOverCapture = function() { r.dragOverTargetIds = [] }, this.handleTopDragOver = function(e) { var t = r.dragOverTargetIds; if (r.dragOverTargetIds = [], !r.monitor.isDragging()) return e.preventDefault(), void(e.dataTransfer && (e.dataTransfer.dropEffect = "none"));
                        r.altKeyPressed = e.altKey, r.actions.hover(t || [], { clientOffset: T(e) }), (t || []).some((function(e) { return r.monitor.canDropOnTarget(e) })) ? (e.preventDefault(), e.dataTransfer && (e.dataTransfer.dropEffect = r.getCurrentDropEffect())) : r.isDraggingNativeItem() ? e.preventDefault() : (e.preventDefault(), e.dataTransfer && (e.dataTransfer.dropEffect = "none")) }, this.handleTopDragLeaveCapture = function(e) { r.isDraggingNativeItem() && e.preventDefault(), r.enterLeaveCounter.leave(e.target) && r.isDraggingNativeItem() && r.endDragNativeItem() }, this.handleTopDropCapture = function(e) { var t;
                        (r.dropTargetIds = [], e.preventDefault(), r.isDraggingNativeItem()) && (null === (t = r.currentNativeSource) || void 0 === t || t.loadDataTransfer(e.dataTransfer));
                        r.enterLeaveCounter.reset() }, this.handleTopDrop = function(e) { var t = r.dropTargetIds;
                        r.dropTargetIds = [], r.actions.hover(t, { clientOffset: T(e) }), r.actions.drop({ dropEffect: r.getCurrentDropEffect() }), r.isDraggingNativeItem() ? r.endDragNativeItem() : r.endDragIfSourceWasRemovedFromDOM() }, this.handleSelectStart = function(e) { var t = e.target; "function" === typeof t.dragDrop && ("INPUT" === t.tagName || "SELECT" === t.tagName || "TEXTAREA" === t.tagName || t.isContentEditable || (e.preventDefault(), t.dragDrop())) }, this.options = new B(n), this.actions = e.getActions(), this.monitor = e.getMonitor(), this.registry = e.getRegistry(), this.enterLeaveCounter = new x(this.isNodeInDocument) } var n, r, a; return n = t, r = [{ key: "profile", value: function() { var e, t; return { sourcePreviewNodes: this.sourcePreviewNodes.size, sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size, sourceNodeOptions: this.sourceNodeOptions.size, sourceNodes: this.sourceNodes.size, dragStartSourceIds: (null === (e = this.dragStartSourceIds) || void 0 === e ? void 0 : e.length) || 0, dropTargetIds: this.dropTargetIds.length, dragEnterTargetIds: this.dragEnterTargetIds.length, dragOverTargetIds: (null === (t = this.dragOverTargetIds) || void 0 === t ? void 0 : t.length) || 0 } } }, { key: "setup", value: function() { if (void 0 !== this.window) { if (this.window.__isReactDndBackendSetUp) throw new Error("Cannot have two HTML5 backends at the same time.");
                            this.window.__isReactDndBackendSetUp = !0, this.addEventListeners(this.window) } } }, { key: "teardown", value: function() { void 0 !== this.window && (this.window.__isReactDndBackendSetUp = !1, this.removeEventListeners(this.window), this.clearCurrentDragSourceNode(), this.asyncEndDragFrameId && this.window.cancelAnimationFrame(this.asyncEndDragFrameId)) } }, { key: "connectDragPreview", value: function(e, t, n) { var r = this; return this.sourcePreviewNodeOptions.set(e, n), this.sourcePreviewNodes.set(e, t),
                            function() { r.sourcePreviewNodes.delete(e), r.sourcePreviewNodeOptions.delete(e) } } }, { key: "connectDragSource", value: function(e, t, n) { var r = this;
                        this.sourceNodes.set(e, t), this.sourceNodeOptions.set(e, n); var a = function(t) { return r.handleDragStart(t, e) },
                            o = function(e) { return r.handleSelectStart(e) }; return t.setAttribute("draggable", "true"), t.addEventListener("dragstart", a), t.addEventListener("selectstart", o),
                            function() { r.sourceNodes.delete(e), r.sourceNodeOptions.delete(e), t.removeEventListener("dragstart", a), t.removeEventListener("selectstart", o), t.setAttribute("draggable", "false") } } }, { key: "connectDropTarget", value: function(e, t) { var n = this,
                            r = function(t) { return n.handleDragEnter(t, e) },
                            a = function(t) { return n.handleDragOver(t, e) },
                            o = function(t) { return n.handleDrop(t, e) }; return t.addEventListener("dragenter", r), t.addEventListener("dragover", a), t.addEventListener("drop", o),
                            function() { t.removeEventListener("dragenter", r), t.removeEventListener("dragover", a), t.removeEventListener("drop", o) } } }, { key: "addEventListeners", value: function(e) { e.addEventListener && (e.addEventListener("dragstart", this.handleTopDragStart), e.addEventListener("dragstart", this.handleTopDragStartCapture, !0), e.addEventListener("dragend", this.handleTopDragEndCapture, !0), e.addEventListener("dragenter", this.handleTopDragEnter), e.addEventListener("dragenter", this.handleTopDragEnterCapture, !0), e.addEventListener("dragleave", this.handleTopDragLeaveCapture, !0), e.addEventListener("dragover", this.handleTopDragOver), e.addEventListener("dragover", this.handleTopDragOverCapture, !0), e.addEventListener("drop", this.handleTopDrop), e.addEventListener("drop", this.handleTopDropCapture, !0)) } }, { key: "removeEventListeners", value: function(e) { e.removeEventListener && (e.removeEventListener("dragstart", this.handleTopDragStart), e.removeEventListener("dragstart", this.handleTopDragStartCapture, !0), e.removeEventListener("dragend", this.handleTopDragEndCapture, !0), e.removeEventListener("dragenter", this.handleTopDragEnter), e.removeEventListener("dragenter", this.handleTopDragEnterCapture, !0), e.removeEventListener("dragleave", this.handleTopDragLeaveCapture, !0), e.removeEventListener("dragover", this.handleTopDragOver), e.removeEventListener("dragover", this.handleTopDragOverCapture, !0), e.removeEventListener("drop", this.handleTopDrop), e.removeEventListener("drop", this.handleTopDropCapture, !0)) } }, { key: "getCurrentSourceNodeOptions", value: function() { var e = this.monitor.getSourceId(),
                            t = this.sourceNodeOptions.get(e); return U({ dropEffect: this.altKeyPressed ? "copy" : "move" }, t || {}) } }, { key: "getCurrentDropEffect", value: function() { return this.isDraggingNativeItem() ? "copy" : this.getCurrentSourceNodeOptions().dropEffect } }, { key: "getCurrentSourcePreviewNodeOptions", value: function() { var e = this.monitor.getSourceId(); return U({ anchorX: .5, anchorY: .5, captureDraggingState: !1 }, this.sourcePreviewNodeOptions.get(e) || {}) } }, { key: "isDraggingNativeItem", value: function() { var t = this.monitor.getItemType(); return Object.keys(e).some((function(n) { return e[n] === t })) } }, { key: "beginDragNativeItem", value: function(e, t) { this.clearCurrentDragSourceNode(), this.currentNativeSource = function(e, t) { var n = new F(P[e]); return n.loadDataTransfer(t), n }(e, t), this.currentNativeHandle = this.registry.addSource(e, this.currentNativeSource), this.actions.beginDrag([this.currentNativeHandle]) } }, { key: "setCurrentDragSourceNode", value: function(e) { var t = this;
                        this.clearCurrentDragSourceNode(), this.currentDragSourceNode = e, this.mouseMoveTimeoutTimer = setTimeout((function() { return t.window && t.window.addEventListener("mousemove", t.endDragIfSourceWasRemovedFromDOM, !0) }), 1e3) } }, { key: "clearCurrentDragSourceNode", value: function() { return !!this.currentDragSourceNode && (this.currentDragSourceNode = null, this.window && (this.window.clearTimeout(this.mouseMoveTimeoutTimer || void 0), this.window.removeEventListener("mousemove", this.endDragIfSourceWasRemovedFromDOM, !0)), this.mouseMoveTimeoutTimer = null, !0) } }, { key: "handleDragStart", value: function(e, t) { e.defaultPrevented || (this.dragStartSourceIds || (this.dragStartSourceIds = []), this.dragStartSourceIds.unshift(t)) } }, { key: "handleDragEnter", value: function(e, t) { this.dragEnterTargetIds.unshift(t) } }, { key: "handleDragOver", value: function(e, t) { null === this.dragOverTargetIds && (this.dragOverTargetIds = []), this.dragOverTargetIds.unshift(t) } }, { key: "handleDrop", value: function(e, t) { this.dropTargetIds.unshift(t) } }, { key: "window", get: function() { return this.options.window } }, { key: "document", get: function() { return this.options.document } }], r && G(n.prototype, r), a && G(n, a), t }(),
            Y = function(e, t) { return new Z(e, t) },
            X = n(62582),
            $ = n(91688),
            Q = n(57546),
            J = n(57528),
            ee = n(72119); const te = ee.Ay.div(K || (K = (0, J.A)(["\n  max-height: 100%;\n  overflow: ", ";\n  height: 100%;\n"])), (e => { let { isPrintScreen: t } = e; return t ? "visible" : "auto" })); var ne = n(66856),
            re = n(36138),
            ae = n(14556),
            oe = n(61258),
            ie = n(49092),
            le = n(70567),
            se = n(61531),
            ce = n(5571); const de = () => (0, a.useMemo)((() => { let e = Math.floor(4 * Math.random()) + 1; return "".concat("https://assets-organimi.s3.amazonaws.com", "/auth-backgrounds/").concat(e, ".jpg") }), []); var ue, he = n(2173),
            me = n(49015),
            pe = n(60194),
            fe = n(70512),
            ve = n(356),
            ge = n(3342),
            ye = n(47287),
            be = n(28623),
            we = n(70579); const ze = e => { let { version: t, children: n } = e; return (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", margin: "auto", bgcolor: "#ffffff", p: 4, maxWidth: 600, children: [(0, we.jsxs)(se.A, { display: "flex", justifyContent: "center", position: "relative", textAlign: "center", children: [(0, we.jsx)("a", { href: "https://www.organimi.com", target: "_blank", rel: "noopener noreferrer", children: (0, we.jsx)("img", { width: 200, src: be, alt: "Organimi Logo" }) }), (0, we.jsx)(xe, { children: t })] }), (0, we.jsx)(se.A, { mt: 2, children: n })] }) },
            xe = ee.Ay.span(ue || (ue = (0, J.A)(["\n  color: #666666;\n  font-size: 11px;\n  width: 190px;\n  display: block;\n  margin-top: 40px;\n  position: absolute;\n  margin-left: 180px;\n"]))),
            Ae = "data:image/png;base64,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",
            ke = "data:image/png;base64,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",
            Se = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAACpF6WWAAAACXBIWXMAAAsSAAALEgHS3X78AAAAVElEQVQ4jWP8//8/A7UBC8i8z4HKBE3mXX+XsWE3I0F1Da7/GZmo7kwGBoZRQ0cNpTKgXY5iXPqBoMn/owUYGXYRzlEMbqM5atTQoWEo9XMUAwMDAGSOGCOVddy/AAAAAElFTkSuQmCC"; var Me, Ee = n(75156); const Ce = e => { ve.A.trackEvent({ eventName: "LOGINCLICK_".concat(e) }) },
            Te = e => { let { setCurrentStep: t } = e; const n = "https://app.organimi.com/api/v7"; return (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(se.A, { display: "flex", flexWrap: "wrap", children: [(0, we.jsxs)(He, { className: "iconBtnLink", href: "".concat(n, "/auth/login/google").concat(window.location.search || ""), onClick: () => Ce("GOOGLE"), children: [(0, we.jsx)("img", { src: Ae, alt: "Google Login" }), (0, we.jsx)("span", { children: "Sign In with Google" })] }), (0, we.jsxs)(He, { className: "iconBtnLink", href: "".concat(n, "/auth/login/linkedin").concat(window.location.search || ""), onClick: () => Ce("LINKEDIN"), children: [(0, we.jsx)("img", { src: ke, alt: "LinkedIn Login" }), (0, we.jsx)("span", { children: "Sign In with LinkedIn" })] }), (0, we.jsxs)(He, { className: "iconBtnLink", href: "".concat(n, "/auth/login/microsoft").concat(window.location.search || ""), onClick: () => Ce("MICROSOFT"), children: [(0, we.jsx)("img", { src: Se, alt: "Microsoft Login" }), (0, we.jsx)("span", { children: "Sign In with Microsoft" })] }), (0, we.jsxs)(He, { className: "iconBtnLink", onClick: () => t(2), children: [(0, we.jsx)(Ee.Ay, { icon: "SSO", size: "xl", style: { marginRight: "19px" } }), (0, we.jsx)("span", { children: "Sign In with SSO" })] })] }) }) },
            He = ee.Ay.a(Me || (Me = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\ndisplay: block;\nborder: 1px solid ".concat(t.palette.grey[200], ";\nwidth: 230px;\nheight: 45px;\nbackground: white;\ndisplay: flex;\nalign-items: center;\npadding: 0 10px;\njustify: flex-start;\ncolor: ").concat(t.palette.grey[700], ";\nfont-weight: 400;\nfont-size: 14px;\n&:hover {\n  text-decoration: none;\n}\nmargin: 4px auto;\nsvg {\n  margin-right: 18px;\n  height: 28px;\n  width: 28px !important;\n}\nimg {\n  margin-right: 18px;\n  height: 28px;\n  width: 28px\n}") })); var Le, Ie, je = n(96364),
            Ve = n(84866),
            Oe = n(8255),
            Re = n(74593);
        (0, ee.Ay)(se.A)(Le || (Le = (0, J.A)(["\n  position: fixed;\n  display: flex;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  z-index: 1;\n"]))), (0, ee.Ay)(se.A)(Ie || (Ie = (0, J.A)(["\n  width: 100%;\n  color: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.4);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  &:hover {\n    background: rgba(0, 0, 0, 0.1);\n  }\n"]))); var Pe, De, Fe, Ne, _e;
        (0, ee.Ay)(je.A)(Pe || (Pe = (0, J.A)(["\n  font-size: 16px;\n  color: #666666;\n  font-weight: 400;\n"]))), (0, ee.Ay)(je.A)(De || (De = (0, J.A)(["\n  font-size: 20px;\n  font-weight: 400;\n  color: #888888;\n"]))); const Be = (0, ee.Ay)(je.A)(Fe || (Fe = (0, J.A)(["\n  font-size: 14px;\n  font-weight: 400;\n  color: #888888;\n"])));
        (0, ee.Ay)(je.A)(Ne || (Ne = (0, J.A)(["\n  color: #000000;\n  font-size: 36px;\n  font-weight: 400;\n"]))), (0, ee.Ay)(je.A)(_e || (_e = (0, J.A)(["\n  font-size: inherit;\n  font-weight: 600;\n  color: ", ";\n"])), (e => { let { theme: { palette: t } } = e; return t.secondary.main })); var We, Ue = n(22440); const qe = (0, ee.Ay)(se.A)(We || (We = (0, J.A)(["\n  top: 0;\n  bottom: 0;\n  display: flex;\n  position: fixed;\n  left: 0;\n  right: 0;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: cover;\n  justify-content: center;\n  min-height: 100%;\n  align-items: center;\n  ", ";\n"])), (e => { let { bgimagesrc: t } = e; return "\n    background-image: url(".concat(t, ");\n    filter: blur(5px);\n    -webkit-filter: blur(5px);\n  ") })); var Ge = n(72835),
            Ke = n(86852),
            Ze = n(55357),
            Ye = n(56579); const Xe = e => { let { altLinkText: t, setCurrentStep: n, btnText: r } = e; const { t: o } = (0, ie.B)(), i = (0, Q.A)();
            r = r || o("Auth.Button.Login"); const l = ((null === i || void 0 === i ? void 0 : i.idps) || "").split(",").filter((e => e)) || [],
                s = "2" === (null === i || void 0 === i ? void 0 : i.step) && 1 === (null === l || void 0 === l ? void 0 : l.length),
                [c, d] = (0, a.useState)(("invalidCompany" === (null === i || void 0 === i ? void 0 : i.err) ? "Please enter a valid company name or contact your administrator for assistance" : null === i || void 0 === i ? void 0 : i.err) || ""),
                [u, h] = (0, a.useState)(l[0] || "");
            (0, a.useEffect)((() => { s && m({ companyAlias: l[0] }) }), []); const m = async () => { if (!u) return void d("Please enter a Company Alias provided by your administrator"); const e = window.location.search ? window.location.search : "",
                    t = e ? "&company=".concat(u) : "?company=".concat(u);
                ve.A.trackEvent({ eventName: "SSO_LOGIN", extraParams: { companyAlias: u, flow: "SP_INITIATED" } }), window.location.href = "".concat("https://app.organimi.com/api/v7", "/auth/login/saml").concat(e + t) }; return (0, we.jsxs)(Ke.A, { spacing: { all: 4 }, children: [s && (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(je.A, { variant: "caption", children: ["Redirecting to ", l[0]] }) }), !s && (0, we.jsxs)(we.Fragment, { children: [0 === (null === l || void 0 === l ? void 0 : l.length) && (0, we.jsx)(Ve.A, { label: "Company Name", id: "companyAlias", type: "text", autoFocus: !0, name: "companyAlias", fullWidth: !0, value: u, onChange: e => { var t; return h(null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value) } }), (null === l || void 0 === l ? void 0 : l.length) > 1 && (0, we.jsx)(Ye.A, { label: "Select IDP", id: "companyAlias", autoFocus: !0, name: "companyAlias", fullWidth: !0, value: u, onChange: e => { var t; return h(null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value) }, children: null === l || void 0 === l ? void 0 : l.map(((e, t) => (0, we.jsx)(Ze.A, { value: e, children: e }, "".concat(e, "_").concat(t)))) }), (0, we.jsx)(je.A, { variant: "caption", color: "error", children: c }), (0, we.jsx)(Ke.A, { spacing: { top: 2 }, children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", fullWidth: !0, "data-testid": "login-button", onClick: m, children: r }) }), (0, we.jsx)(Ke.A, { spacing: { top: 2 }, children: (0, we.jsx)(je.A, { variant: "body1", component: "a", onClick: () => n(1), target: "_blank", children: t || "Sign in without SSO" }) })] })] }) }; var $e = n(30105); const Qe = { features: [{ label: "Quick Organimi Setup", description: "Check out our quick overview setting up an org chart in minutes. The more automated you create your setup, the more seamless the updates!", type: "youtube", embedLink: "https://www.youtube-nocookie.com/embed/ZrOruX55j9k" }, { label: "Create a chart in ", labelInnerSuffix: "minutes.", description: "Choose between uploading your data, connecting to your existing HR system, utilizing direct integrations or building your chart manually using our best in class drag & drop interface!", imgSrc: "register/slides/Chart-min.png" }, { label: "Keep everyone in the ", labelInnerSuffix: "loop.", description: "Share your org charts privately with select individuals or company wide in one click. Enable direct invites with SSO. Showcase your team on your website with an iFrame Embed.", imgSrc: "register/slides/Sharing-min.png" }, { label: "Channel your inner ", labelInnerSuffix: "artist.", description: "Use Organimi\u2019s custom themes and unique SmartChart\u2122 Legend feature to help visualize information on your chart and attach conditions to custom fields.", imgSrc: "register/slides/ThemesLegend-min.png" }, { label: "Put a face to the ", labelInnerSuffix: "name.", description: "Foster meaningful connections by utilizing our Photoboards and Directory features, enabling you to engage with the individuals behind their professional titles.", imgSrc: "register/slides/Photoboard-min.png" }, { label: "Add your own ", labelInnerSuffix: "custom fields.", description: "Create any field you'd like and control privacy by deciding which custom fields should be displayed publicly (e.g. job title) and which ones you would rather keep private (e.g. salaries).", imgSrc: "register/slides/CustomFields-min.png" }, { label: "Get the insights you need with ", labelInnerSuffix: "reporting.", description: "Analyze your current people data, compare organizational changes between time periods, view your reporting history and share organization or chart level reports with your team!", imgSrc: "register/slides/Reporting-min.png" }] }; var Je, et, tt, nt, rt, at, ot, it, lt; const st = (0, ee.Ay)(se.A)(Je || (Je = (0, J.A)(["\n  width: 12px;\n  height: 12px;\n  border-radius: 6px;\n  cursor: pointer;\n  ", "\n"])), (e => { let { theme: t, active: n } = e; return "\n    background: ".concat(n ? "".concat(t.palette.primary.light) : "rgba(0, 0, 0, 0.3)", ";\n  ") })),
            ct = (0, ee.Ay)(se.A)(et || (et = (0, J.A)(["\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  flex: 1;\n  grid-gap: 24px;\n"]))),
            dt = (0, ee.Ay)(je.A)(tt || (tt = (0, J.A)(["\n  font-size: 36px;\n  color: #000000;\n"]))),
            ut = (0, ee.Ay)(je.A)(nt || (nt = (0, J.A)(["\n  font-size: inherit;\n  color: ", ";\n  font-weight: 600;\n"])), (e => { let { theme: { palette: t } } = e; return t.secondary.main })),
            ht = (0, ee.Ay)(je.A)(rt || (rt = (0, J.A)(["\n  font-size: 16px;\n  color: #444444;\n  font-weight: 300;\n  line-height: 1.6em;\n"]))),
            mt = (0, ee.Ay)(se.A)(at || (at = (0, J.A)(["\n  position: absolute;\n  left: 0;\n  z-index: 1;\n  border: solid 1px #666666;\n  border-radius: 4px;\n"]))),
            pt = (0, ee.Ay)(se.A)(ot || (ot = (0, J.A)(["\n  position: absolute;\n  right: 0;\n  z-index: 1;\n  border: solid 1px #666666;\n  border-radius: 4px;\n"]))),
            ft = (0, ee.Ay)(se.A)(it || (it = (0, J.A)(["\n  position: fixed;\n  display: flex;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  z-index: 1;\n"]))),
            vt = (0, ee.Ay)(se.A)(lt || (lt = (0, J.A)(["\n  width: 100%;\n  color: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.4);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  &:hover {\n    background: rgba(0, 0, 0, 0.1);\n  }\n"]))),
            gt = e => { let { children: t, description: n, label: r, labelInnerSuffix: a = "", handleActionClick: o, actionText: i } = e; return (0, we.jsx)(se.A, { position: "relative", px: 2, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", gridGap: 24, children: [(0, we.jsxs)(dt, { component: "h3", align: "center", children: [r, (0, we.jsx)(ut, { component: "span", children: a })] }), t, o && i && (0, we.jsx)(se.A, { display: "flex", justifyContent: "center", children: (0, we.jsx)($e.A, { color: "secondary", variant: "contained", size: "small", onClick: o, children: i }) }), (0, we.jsx)(ht, { children: n })] }) }) },
            yt = () => { const [e, t] = (0, a.useState)(0), { features: n } = Qe, [r, o] = (0, a.useState)(!1), i = n[e], l = () => { t((e => (e + 1) % n.length)) }, s = () => { t((e => (e - 1 + n.length) % n.length)) }; return (0, a.useEffect)((() => {
                    function e(e) { "Escape" === e.code && o(!1) } return document.addEventListener("keydown", e), () => document.removeEventListener("keydown", e) }), []), (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { display: "flex", flexDirection: "column", gridGap: 16, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", gridGap: 32, position: "relative", p: 4, flexWrap: "wrap", alignItems: "center", children: [(0, we.jsx)(se.A, { children: (() => { const e = {}; "youtube" === i.type && (e.handleActionClick = () => { o(!0) }, e.actionText = "See video"); const t = "youtube" === i.type ? (0, we.jsxs)(se.A, { position: "relative", children: [(0, we.jsx)(vt, { onClick: () => { o(!0) }, children: (0, we.jsx)(Ee.Ay, { icon: "PlaySolid", size: "x4" }) }), (0, we.jsx)("img", { width: "100%", src: "".concat("https://assets-organimi.s3.amazonaws.com", "/register/GoodThumbnail.png") })] }) : (0, we.jsx)(se.A, { bgcolor: "transparent", children: (0, we.jsx)("img", { src: "".concat("https://assets-organimi.s3.amazonaws.com", "/").concat(i.imgSrc), width: "100%" }) }); return (0, we.jsx)(gt, { ...i, handlePrev: s, handleNext: l, ...e, children: t }) })() }), (0, we.jsxs)(se.A, { position: "relative", display: "flex", justifyContent: "center", alignItems: "center", flex: 1, children: [(0, we.jsx)(ct, { my: 2, children: n.map(((r, a) => (0, we.jsx)(st, { active: a === e, onClick: () => { return e = a, void t(Math.min(Math.max(0, e), n.length - 1)); var e } }, "".concat(r.label, "_").concat(a)))) }), (0, we.jsx)(mt, { children: (0, we.jsx)($e.A, { onClick: s, children: "<" }) }), (0, we.jsx)(pt, { children: (0, we.jsx)($e.A, { onClick: l, children: ">" }) })] })] }) }), r && (0, we.jsx)(ft, { p: 8, onClick: () => { o(!1) }, children: (0, we.jsxs)(se.A, { display: "flex", justifyContent: "center", width: "100%", position: "relative", paddingBottom: "56.25%", height: 0, children: [(0, we.jsx)("iframe", { style: { position: "absolute", top: 0, left: 0, width: "100%", height: "100%" }, src: i.embedLink, title: "Organimi Help Walk Through", frameBorder: "0", allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share", allowfullscreen: !0 }), (0, we.jsx)(se.A, { position: "fixed", right: 16, top: 16, children: (0, we.jsx)($e.A, { variant: "contained", color: "primary", onClick: () => { o(!1) }, children: "Close Video" }) })] }) })] }) },
            { ERROR_MESSAGES: bt } = ge.A,
            wt = () => { var e, t, n, r; const [o] = (0, Ue.A)(), i = (0, le.A)(), l = "".concat(pe.Yr, ".").concat(pe.ue, ".").concat(pe.cM), [s, c] = (0, a.useState)(""), [d, u] = (0, a.useState)(""), [h, m] = (0, a.useState)(), p = (0, oe.mN)({ mode: "onChange" }), { register: f, handleSubmit: v, errors: g, watch: y, clearErrors: b } = p, w = (0, ae.wA)(), z = de(), { t: x } = (0, ie.B)(), A = (0, Q.A)(), k = A.username || "", S = A.token, M = k && S, E = (0, $.useHistory)(), C = e => { c(e.message || "There was a problem creating an account with that email") }, [T, H] = (0, he.A)(v((async e => { const { error: t, payload: n } = await w(me.ip.register({ ...e, onError: C, queryParams: A })); if (!t) { if (null === n || void 0 === n || !n.redirect) return E.push((0, ne.A$)()); let e = n.redirect,
                            t = ""; const r = n.redirect.indexOf("?");
                        r >= 0 && (t = n.redirect.substring(r), e = e.slice(0, r)), window.location.assign("".concat(e).concat(t)) } }), (() => {})));
                (0, a.useEffect)((() => { document.title = "Organimi | " + (M ? "Accept Invitation" : "Sign Up"), ve.A.trackEvent({ eventName: "REGISTER_OPEN" }), m((null === A || void 0 === A ? void 0 : A.step) || 1); try { window.dataLayer ? M ? window.dataLayer.push({ event: "register_open_fromInvite", category: "Account", action: "register_open_fromInvite", label: "Register Open From Invite" }) : window.dataLayer.push({ event: "register_open", category: "Account", action: "register_open", label: "Register Open", testVariableText: "123", testVariableNumber: 123 }) : console.log("DataLayer is not defined") } catch (e) { console.log("Problem logging register_open event in GA"), console.log(e) } }), []); const L = () => { var e, t;
                        g.password || b("password"), g.passwordConfirmation || b("passwordConfirmation"), "passwordConfirmation" === (null === g || void 0 === g || null === (e = g.password) || void 0 === e ? void 0 : e.type) && b("password"), "passwordConfirmation" === (null === g || void 0 === g || null === (t = g.passwordConfirmation) || void 0 === t ? void 0 : t.type) && b("passwordConfirmation") },
                    I = { firstName: { required: "Please enter a First Name" }, lastName: { required: "Please enter a Last Name" }, username: { required: "Please enter the email.", pattern: { value: fe.eT, message: "Invalid Email format." } }, password: { required: "Please enter password", validate: { predictable: e => ge.A.isPasswordPredictable({ password: e, firstName: y("firstName") }), passwordConfirmation: e => ge.A.checkPasswordMatch({ password: e, passwordConfirmation: y("passwordConfirmation"), cb: L }) } }, passwordConfirmation: { required: "Please confirm password", validate: { passwordConfirmation: e => ge.A.checkPasswordMatch({ password: y("password"), passwordConfirmation: e, cb: L }) } } },
                    j = { 1: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { p: o < 600 ? 0 : 4, bgcolor: "rgb(64, 31, 133)", minHeight: "100%", bgimagesrc: z }), (0, we.jsxs)(se.A, { zIndex: 1, position: "relative", display: o < 600 ? "block" : "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", children: [(0, we.jsx)(se.A, { flex: 6, display: "flex", justifyContent: "center", minWidth: 500, maxWidth: 750, bgcolor: "#ffffff", children: (0, we.jsxs)(ze, { version: "".concat(l), children: [(0, we.jsx)(se.A, { mt: 2, children: (0, we.jsx)(je.A, { align: "center", color: "textPrimary", children: "CREATE ACCOUNT" }) }), (0, we.jsxs)(se.A, { children: [(0, we.jsx)(se.A, { m: 4, children: (0, we.jsx)(oe.Op, { ...p, children: (0, we.jsxs)("form", { id: "registrationForm", onSubmit: H, children: [(0, we.jsx)(Ve.A, { label: x("Auth.Label.FirstName"), id: "registerFirstName", type: "text", inputRef: f(I.firstName), error: !!g.firstName, name: "firstName", autoFocus: !0, fullWidth: !0 }), g.firstName && (0, we.jsx)(Oe.A, { children: g.firstName.message }), (0, we.jsx)(Ve.A, { label: x("Auth.Label.LastName"), id: "registerLastName", type: "text", name: "lastName", error: !!g.lastName, inputRef: f(I.lastName), fullWidth: !0 }), g.lastName && (0, we.jsx)(Oe.A, { children: g.lastName.message }), (0, we.jsx)(Ve.A, { label: x("Auth.Register.EmailLabel"), id: "registerEmail", type: "email", name: "username", defaultValue: k, inputRef: f(I.username), error: !!g.username, fullWidth: !0 }), g.username && (0, we.jsx)(Oe.A, { children: g.username.message }), (0, we.jsx)(ye.A, { id: "registerPassword", name: "password", fullWidth: !0, label: x("Auth.Label.Password"), doValidation: !0, fieldRefParams: I.password, error: !!g.password }), g.password && (0, we.jsx)(Oe.A, { children: (null === (e = g.password) || void 0 === e ? void 0 : e.message) || bt[null === (t = g.password) || void 0 === t ? void 0 : t.type] }), (0, we.jsx)(ye.A, { label: x("Auth.Label.PasswordConfirmation"), id: "registerPasswordConfirmation", name: "passwordConfirmation", fullWidth: !0, fieldRefParams: I.passwordConfirmation, error: !!g.passwordConfirmation }), g.passwordConfirmation && (0, we.jsx)(Oe.A, { children: (null === (n = g.passwordConfirmation) || void 0 === n ? void 0 : n.message) || bt[null === (r = g.passwordConfirmation) || void 0 === r ? void 0 : r.type] }), (0, we.jsx)(je.A, { variant: "caption", color: "error", children: s }), (0, we.jsxs)(se.A, { my: 1, display: "flex", alignItems: "center", justifyContent: "space-around", children: [(0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: (0, ne.iD)(), children: x("Auth.Link.Login") }) }), (0, we.jsx)(Re.A, { fullWidth: !0, disabled: T, variant: "contained", color: "primary", type: "submit", form: "registrationForm", children: x(M ? "Auth.Button.AcceptInvitation" : "Auth.Button.Register") })] }), (0, we.jsx)(je.A, { weight: "bold", color: i.palette.success.main, children: d })] }) }) }), (0, we.jsx)(se.A, { pb: 4, children: (0, we.jsx)(ce.A, {}) }), (0, we.jsx)(Te, { setCurrentStep: m })] }), (0, we.jsxs)(se.A, { mt: 6, display: "flex", gridGap: 16, alignItems: "center", justifyContent: "space-around", children: [(0, we.jsxs)(Be, { variant: "body1", display: "inline", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", x("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "inline", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: x("Auth.Link.Privacy") }) })] })] }) }), (0, we.jsx)(se.A, { flex: 5, bgcolor: "#e9f1fd", display: "flex", flexDirection: "column", justifyContent: "center", minWidth: 500, maxWidth: 750, width: "100%", p: 4, gridGap: 32, children: (0, we.jsx)(yt, {}) })] })] }), 2: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: z }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsxs)(ze, { version: "".concat(l), children: [(0, we.jsx)(se.A, { my: 2, children: (0, we.jsx)(je.A, { color: "textPrimary", children: x("Auth.Login.Heading") }) }), (0, we.jsx)(Xe, { altLinkText: "Register without SSO", setCurrentStep: m, btnText: x(M ? "Auth.Button.AcceptInvitation" : "Auth.Button.Login") }), (0, we.jsxs)(se.A, { mt: 6, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", x("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: x("Auth.Link.Privacy") }) })] })] }) })] }) }; return (0, we.jsxs)(we.Fragment, { children: [" ", j[h], " "] }) },
            zt = () => { var e; const [t, n] = (0, a.useState)(""), r = (0, ae.wA)(), o = de(), { t: i } = (0, ie.B)(), l = (0, oe.mN)({ mode: "onChange" }), { register: s, handleSubmit: c, errors: d, setError: u } = l, h = (0, Q.A)(), m = "?".concat(null === (e = new URLSearchParams(h)) || void 0 === e ? void 0 : e.toString()) || 0, p = "".concat(pe.Yr, ".").concat(pe.ue, ".").concat(pe.cM), [f, v] = (0, a.useState)();
                (0, a.useEffect)((() => { document.title = "Organimi | Login", v((null === h || void 0 === h ? void 0 : h.step) || 1) }), []); const g = e => { 401 === e.responseCode ? u("password", { message: "Incorrect email or password" }) : n(e.message) },
                    [y, b] = (0, he.A)(c((async e => { ve.A.trackEvent({ eventName: "LOGINCLICK_LOCAL" }); const { error: t, payload: n } = await r(me.ip.login({ ...e, onError: g, queryParams: h }));
                        t || window.location.assign(n.redirect || (0, ne.ze)()) }))),
                    w = { username: { required: "Please enter the email.", pattern: { value: fe.eT, message: "Invalid Email format." } }, password: { required: "Please enter password" } },
                    z = { 1: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { m: 4, children: (0, we.jsx)(oe.Op, { ...l, children: (0, we.jsxs)("form", { onSubmit: b, children: [(0, we.jsx)(Ve.A, { label: i("Auth.Label.Email"), id: "loginEmail", type: "text", autoFocus: !0, name: "username", fullWidth: !0, defaultValue: (null === h || void 0 === h ? void 0 : h.username) || "", inputRef: s(w.username), error: !!d.username }), (0, we.jsx)(ye.A, { id: "loginPassword", name: "password", label: i("Auth.Label.Password"), fullWidth: !0, fieldRefParams: w.password, error: !!d.password }), (0, we.jsx)(je.A, { variant: "caption", color: "error", children: t }), (0, we.jsx)(Oe.A, { children: d.username && d.username.message }), (0, we.jsx)(Oe.A, { children: d.password && d.password.message }), (0, we.jsx)(se.A, { my: 1, children: (0, we.jsx)(Ge.A, { type: "submit", variant: "contained", color: "primary", fullWidth: !0, disabled: y, "data-testid": "login-button", children: i("Auth.Button.Login") }) })] }) }) }), (0, we.jsxs)(se.A, { my: 2, children: [(0, we.jsx)(se.A, { my: 1, children: (0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: (0, ne.yZ)(), children: i("Auth.Link.Forgot") }) }) }), (0, we.jsx)(se.A, { my: 1, children: (0, we.jsx)(je.A, { variant: "body1", children: (0, we.jsx)(X.N_, { to: "".concat((0, ne.kz)()).concat(m), children: i("Auth.Link.Register") }) }) })] }), (0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(ce.A, {}) }), (0, we.jsx)(Te, { setCurrentStep: v })] }), 2: (0, we.jsx)(Xe, { setCurrentStep: v }) }; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: o }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsxs)(ze, { version: "".concat(p), children: [(0, we.jsx)(se.A, { my: 2, children: (0, we.jsx)(je.A, { color: "textPrimary", children: i("Auth.Login.Heading") }) }), z[f], (0, we.jsxs)(se.A, { mt: 6, children: [(0, we.jsxs)(je.A, { variant: "body1", children: ["\xa9 ", (new Date).getUTCFullYear(), " Organimi Inc. ", i("Auth.Text.AllRightsReserved")] }), (0, we.jsx)(je.A, { variant: "body2", display: "block", children: (0, we.jsx)("a", { href: "https://www.organimi.com/privacy", target: "_blank", rel: "noopener noreferrer", children: i("Auth.Link.Privacy") }) })] })] }) })] }) },
            xt = () => { const [e, t] = (0, a.useState)(), { t: n } = (0, ie.B)(), r = (0, ae.wA)(), o = de(), i = (0, Q.A)().token, l = (0, $.useHistory)(), { register: s, handleSubmit: c, errors: d, watch: u } = (0, oe.mN)(), [h, m] = (0, he.A)(c((async e => { const { error: n, payload: a } = await r(me.ip.resetPassword({ ...e, token: i }));
                    n ? t((null === a || void 0 === a ? void 0 : a.message) || n.message) : l.push((0, ne.iD)()) }))); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: o }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsx)(ze, { children: (0, we.jsxs)(Ke.A, { spacing: { all: 4 }, children: [(0, we.jsx)(Ke.A, { spacing: { bottom: 4 }, children: (0, we.jsx)(je.A, { variant: "h3", children: n("Auth.SetPassword.Heading") }) }), (0, we.jsxs)("form", { onSubmit: m, children: [(0, we.jsx)(Ve.A, { variant: "outlined", name: "email", label: n("Auth.Label.Email"), inputRef: s({ required: "This email is invalid", pattern: fe.eT }), error: !!d.email, autoFocus: !0, fullWidth: !0 }), (0, we.jsx)(Ve.A, { variant: "outlined", name: "password", type: "password", label: n("Auth.Label.Password"), inputRef: s({ required: "Please enter a password", pattern: { value: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{10,}$/, message: "Password must contain at least 10 characters, including uppercase, lowercase letters, special characters and numbers" } }), error: !!d.password, fullWidth: !0 }), (0, we.jsx)(Ve.A, { variant: "outlined", name: "passwordConfirmation", type: "password", label: n("Auth.Label.PasswordConfirmation"), error: !!d.passwordConfirmation, inputRef: s({ required: "Your password doesn't match", validate: e => e === u("password") }), fullWidth: !0 }), (0, we.jsxs)(Ke.A, { spacing: { vertical: 2 }, children: [(0, we.jsx)(Ke.A, { spacing: { vertical: 1 }, children: (0, we.jsx)(je.A, { variant: "caption", color: "error", children: e }) }), (0, we.jsx)(Oe.A, { children: d.email && d.email.message }), (0, we.jsx)(Oe.A, { children: d.password && d.password.message }), (0, we.jsx)(Oe.A, { children: d.passwordConfirmation && d.passwordConfirmation.message }), (0, we.jsx)($e.A, { fullWidth: !0, type: "submit", variant: "contained", color: "primary", disabled: h, children: "Set Password" })] }), (0, we.jsx)(Ke.A, { spacing: { horizontal: 3 }, children: (0, we.jsx)(je.A, { variant: "subtitle2", children: (0, we.jsx)(X.N_, { to: "/login", children: n("Auth.Button.BackToLogin") }) }) })] })] }) }) })] }) },
            At = () => { const e = (0, le.A)(),
                    t = (0, ae.wA)(),
                    n = de(),
                    [r, o] = (0, a.useState)(""),
                    [i, l] = (0, a.useState)(""),
                    s = e => { o(e.message) },
                    [c, d] = (0, he.A)((async () => { const { error: e } = await t(me.ip.resendVerification({ onError: s }));
                        e || l("Email sent") })); return (0, a.useEffect)((() => { document.title = "Organimi | Verify Email" }), []), (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: n }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsx)(ze, { children: (0, we.jsxs)(Ke.A, { spacing: { all: 4 }, children: [(0, we.jsx)(Ke.A, { spacing: { vertical: 4 }, children: (0, we.jsx)(je.A, { variant: "h3", children: "Verify Your Email" }) }), (0, we.jsx)(Ke.A, { spacing: { bottom: 4 }, children: (0, we.jsx)(je.A, { variant: "body1", children: "Please click the link in the email we sent you to verify your account" }) }), (0, we.jsx)(Ke.A, { spacing: { bottom: 2 }, children: (0, we.jsx)(je.A, { variant: "body1", children: "Didn't receive the email? Click below to resend." }) }), (0, we.jsx)($e.A, { fullWidth: !0, type: "submit", variant: "contained", color: "primary", disabled: c, onClick: d, children: "Resend Email" }), (0, we.jsxs)(Ke.A, { spacing: { vertical: 2 }, children: [r && (0, we.jsx)(je.A, { variant: "subtitle2", color: e.palette.error.main, children: r }), i && (0, we.jsx)(je.A, { variant: "subtitle2", color: e.palette.success.main, children: i })] }), (0, we.jsx)(Ke.A, { spacing: { vertical: 2 }, children: (0, we.jsx)(je.A, { variant: "subtitle2", children: (0, we.jsx)(je.A, { style: { textDecoration: "none" }, component: "a", onClick: () => window.location.assign("https://app.organimi.com" + (0, ne.ri)()), children: "Login with another account" }) }) })] }) }) })] }) },
            kt = () => { const e = (0, $.useHistory)(),
                    { t: t } = (0, ie.B)(),
                    n = (0, ae.wA)(),
                    r = de(),
                    [o, i] = (0, a.useState)(""),
                    l = e => { i(e.message) },
                    { register: s, handleSubmit: c, errors: d } = (0, oe.mN)(),
                    [u, h] = (0, he.A)(c((async t => { const { error: r } = await n(me.ip.forgotPassword({ ...t, onError: l }));
                        r || e.push((0, ne.mF)()) }))); return (0, a.useEffect)((() => { document.title = "Organimi | Reset Your Password" }), []), (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: r }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsx)(ze, { children: (0, we.jsx)("form", { onSubmit: h, children: (0, we.jsxs)(Ke.A, { spacing: { all: 4 }, children: [(0, we.jsx)(Ke.A, { spacing: { vertical: 2 }, children: (0, we.jsx)(je.A, { variant: "h3", children: t("Auth.Forgot.Heading") }) }), (0, we.jsx)(Ke.A, { spacing: { bottom: 2 }, children: (0, we.jsx)(je.A, { variant: "body1", children: t("Auth.Forgot.Body") }) }), (0, we.jsx)(Ve.A, { fullWidth: !0, label: t("Auth.Label.Email"), type: "text", name: "email", inputRef: s({ required: "This email is invalid", pattern: fe.eT }), error: !!d.email }), (0, we.jsx)(Ke.A, { spacing: { bottom: 1 }, children: (0, we.jsx)(je.A, { variant: "caption", color: "error", children: o }) }), (0, we.jsx)(Oe.A, { children: d.email && d.email.message }), (0, we.jsx)($e.A, { fullWidth: !0, type: "submit", variant: "contained", color: "primary", disabled: u, children: t("Auth.Button.Forgot") }), (0, we.jsx)(Ke.A, { spacing: { vertical: 1 }, children: (0, we.jsx)(je.A, { variant: "subtitle2", children: (0, we.jsx)(X.N_, { to: "/login", children: t("Auth.Button.BackToLogin") }) }) })] }) }) }) })] }) },
            St = () => { const e = (0, $.useHistory)(),
                    { t: t } = (0, ie.B)(),
                    n = de(); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(qe, { container: !0, justifyContent: "center", align: "center", bgimagesrc: n }), (0, we.jsx)(se.A, { zIndex: 1, position: "relative", display: "flex", height: "100%", flexWrap: "wrap", justifyContent: "center", textAlign: "center", children: (0, we.jsx)(ze, { children: (0, we.jsxs)(Ke.A, { spacing: { all: 4, top: 3 }, children: [(0, we.jsx)(Ke.A, { spacing: { vertical: 2 }, children: (0, we.jsx)(je.A, { variant: "h3", children: t("Auth.ForgotRequested.Heading") }) }), (0, we.jsx)(Ke.A, { spacing: { top: 4 }, children: (0, we.jsx)(je.A, { variant: "body1", align: "left", children: t("Auth.ForgotRequested.Body1") }) }), (0, we.jsx)(Ke.A, { spacing: { top: 2, bottom: 4 }, children: (0, we.jsx)(je.A, { variant: "body1", align: "left", children: t("Auth.ForgotRequested.Body2") }) }), (0, we.jsx)(Ke.A, { spacing: { top: 2 }, children: (0, we.jsx)($e.A, { fullWidth: !0, variant: "contained", color: "primary", onClick: () => { e.push((0, ne.iD)()) }, children: t("Auth.Button.LoginNow") }) })] }) }) })] }) }; var Mt, Et, Ct = n(18073),
            Tt = n(40454),
            Ht = n(9989),
            Lt = n(72127),
            It = n(172),
            jt = n(27017),
            Vt = n(61071); const Ot = "active",
            Rt = (0, ee.Ay)(X.k2).attrs({ activeClassName: Ot })(Mt || (Mt = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n  &.".concat(Ot, " *, &:hover * {\n    color: ").concat(t.palette.primary.contrastText, ";\n    background: ").concat(t.palette.primary.main, ";\n  }") })),
            Pt = ee.Ay.div.attrs({ activeClassName: Ot })(Et || (Et = (0, J.A)(["\n  ", "\n"])), (e => { let { theme: t } = e; return "\n    cursor: pointer;\n    &.".concat(Ot, " *, &:hover * {\n      color: ").concat(t.palette.primary.contrastText, ";\n      background: ").concat(t.palette.primary.main, ";\n    }\n  ") })); var Dt, Ft = n(84); const Nt = (0, ee.Ay)(Tt.A)(Dt || (Dt = (0, J.A)(["\n  ", "\n  position: relative;\n  height: 100%;\n  overflow: hidden;\n"])), (e => { let { theme: t } = e; return "\n    background: ".concat(t.palette.dashboardNav.background, ";\n  ") })); var _t, Bt, Wt = n(78396),
            Ut = n(48853),
            qt = n(70520),
            Gt = n(41),
            Kt = n(86597); const Zt = (0, ee.Ay)(Ve.A)(_t || (_t = (0, J.A)(["\n  label {\n    color: ", ";\n    text-transform: uppercase;\n    font-size: 16px;\n  }\n  fieldset {\n    border-top: none;\n    border-left: none;\n    border-right: none;\n    border-radius: 0;\n  }\n  .MuiOutlinedInput-input {\n    margin-left: 8px;\n    margin-top: 12px;\n  }\n"])), (e => { let { theme: t } = e; return t.palette.primary.main }));
        (0, ee.Ay)(Ve.A)(Bt || (Bt = (0, J.A)(["\n  background: #ffffff;\n  margin-top: 0;\n  margin-bottom: 0;\n  .MuiSelect-nativeInput {\n    box-sizing: border-box;\n  }\n  .MuiSelect-select:focus {\n    background: transparent;\n  }\n  label {\n    color: ", ";\n    text-transform: uppercase;\n    font-size: 16px;\n    margin-top: 12px;\n    padding-top: 12px;\n  }\n  fieldset {\n    border-top: none;\n    border-left: none;\n    border-right: none;\n    border-radius: 0;\n  }\n  .MuiOutlinedInput-input {\n    margin-left: 8px;\n    margin-top: 24px;\n  }\n"])), (e => { let { theme: t } = e; return t.palette.primary.main })); var Yt; const Xt = (0, ee.Ay)(It.A)(Yt || (Yt = (0, J.A)(["\n  border-top: solid 1px #dddddd;\n"]))); var $t = n(43940); const Qt = () => { var e, t, n; const { onTourEventComplete: r } = (0, $t.M)({}), o = (0, Kt.A)(), [i, l] = (0, a.useState)(!1), { openDialog: s } = (0, Ft.A)("account"), c = (0, ae.d4)(Lt.VF), d = (0, a.useRef)(!0), u = (0, $.useHistory)(), { t: h } = (0, ie.B)(), m = (0, ae.d4)(Lt.Ok), { openDialog: p } = (0, Ft.A)("importFile"), { openDialog: f } = (0, Ft.A)("organizationMeta"), { openDialog: v } = (0, Ft.A)("demoSpace"), { userHasMinAccess: g } = (0, Ut.A)(), y = (0, ae.d4)(Lt.mz), b = !(null === c || void 0 === c || null === (e = c.featureSet) || void 0 === e || !e.ownershipCharts), { orgId: w } = (0, $.useParams)(), z = (null === u || void 0 === u || null === (t = u.location) || void 0 === t ? void 0 : t.pathname) === (0, ne.wi)({ orgId: w, view: "chart", resource: "ownership", base: "protected" }) || (null === u || void 0 === u || null === (n = u.location) || void 0 === n ? void 0 : n.pathname) === (0, ne.wi)({ orgId: w, view: "table", resource: "ownership", base: "protected" }) || (0, ne.wi)({ orgId: w, view: "builder", resource: "ownership", base: "protected" }), x = (0, a.useRef)();
            (0, a.useEffect)((() => { var e;
                w && d.current && (null === x || void 0 === x || null === (e = x.current) || void 0 === e || e.scrollIntoView({ behavior: "smooth", block: "center" }), d.current = !1) }), [w]); const A = (0, Gt.Q6)(c); return (0, we.jsxs)(we.Fragment, { children: [i && (0, we.jsx)(qt.A, { mode: "dialog", handleClose: () => { l(!1) } }), (0, we.jsxs)(Nt, { container: !0, direction: "column", alignItems: "stretch", style: { overflowY: "auto", borderRight: "solid 1px #cccccc" }, children: [(0, we.jsx)(Tt.A, { item: !0, xs: !0, children: (0, we.jsxs)(Ht.A, { component: "nav", "aria-label": "dashboard", children: [(0, we.jsx)(se.A, { py: 1, children: (0, we.jsxs)(Zt, { label: "Active Organization", select: !0, fullWidth: !0, variant: "outlined", value: null === o || void 0 === o ? void 0 : o.id, children: [m.map((e => (0, we.jsx)(Ze.A, { value: e.id, onClick: () => (e => { u.push("/dashboard/organizations/".concat(e, "/charts")) })(e.id), children: e.name }, "dashboard-org-list-nav-".concat(e.id)))), g(Wt.td.OWNER) && (0, we.jsx)(Ze.A, { value: "_new", onClick: () => { f({ redirectToOrganization: !0 }) }, children: (0, we.jsx)(je.A, { color: "primary", children: "+ New Organization" }) })] }) }), (b || (null === m || void 0 === m ? void 0 : m.length) > 1) && (0, we.jsx)(Rt, { exact: !0, to: (0, ne.YQ)(), children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_00", children: (0, we.jsxs)(se.A, { display: "flex", gridGap: 4, alignItems: "center", children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "GridView3" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: "All Organizations" }) })] }) }) }), g(Wt.td.ADMIN) && Boolean(null === y || void 0 === y ? void 0 : y.length) && (0, we.jsxs)(a.Fragment, { children: [(0, we.jsx)(Rt, { to: (0, ne.r2)({ orgId: w || (null === o || void 0 === o ? void 0 : o.id) }), isActive: (e, t) => !!e || t.pathname === (0, ne.wi)({ orgId: w || (null === o || void 0 === o ? void 0 : o.id), view: "chart", resource: "ownership", base: "protected" }) || t.pathname === (0, ne.wi)({ orgId: w || (null === o || void 0 === o ? void 0 : o.id), view: "table", resource: "ownership", base: "protected" }), children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_5", children: (0, we.jsxs)(se.A, { display: "flex", gridGap: 4, alignItems: "center", children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Home" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.OrgHome") }) })] }) }) }), (0, we.jsx)(Pt, { onClick: () => { p() }, children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_5", "data-tour-anchor": "dashboard-imports-btn", onClick: () => r({ event: "dashboard-imports-btn--click" }), children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "GetApp" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.DataImport") }) })] }) }) }), (0, we.jsx)(Rt, { to: "/dashboard/integrations", children: (0, we.jsx)(It.A, { "data-tooltip": "dashboard_tooltip_6", "data-tour-anchor": "dashboard-integrations-btn", onClick: () => r({ event: "dashboard-integrations-btn--click" }), children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Wrench" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.Integrations") }) })] }) }) }), (0, we.jsx)(Rt, { to: "/dashboard/reports?reportLevel=org", children: (0, we.jsx)(It.A, { children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, flex: 1, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Reports" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.Reports") }) })] }) }) }), (0, we.jsx)(Rt, { to: "/dashboard/audit", children: (0, we.jsx)(It.A, { children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, flex: 1, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Sales" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: "Audit" }) })] }) }) }), (0, we.jsx)(Rt, { to: "/dashboard/themes/".concat(null === o || void 0 === o ? void 0 : o.id), children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_7", children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Theme" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.Themes") }) })] }) }) }), (0, we.jsx)(Pt, { onClick: () => v({ source: "nav-menu" }), children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_5", children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.me, { size: "lg", name: "globe-pointer", variant: "light" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: "Organimi Explore" }) })] }) }) }), g(Wt.td.ADMIN) && Boolean(null === y || void 0 === y ? void 0 : y.length) && (0, we.jsxs)("div", { children: [(0, we.jsx)(Ke.A, { spacing: { left: 2, right: 2 }, children: (0, we.jsx)(ce.A, {}) }), w && !z ? (0, we.jsx)(Rt, { to: "/dashboard/organizations/".concat(w, "/settings/profile"), children: (0, we.jsx)(It.A, { "aria-describedby": "dashboard_tooltip_8", children: (0, we.jsxs)(se.A, { display: "flex", alignItems: "center", gridGap: 4, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { size: "lg", icon: "Settings" }) }), (0, we.jsx)(Vt.A, { children: (0, we.jsx)(je.A, { variant: "body1", children: h("Dashboard.Labels.Settings") }) })] }) }) }) : null] })] })] }) }), c && (0, we.jsxs)(Ht.A, { disablePadding: !0, children: [(0, we.jsx)(ce.A, {}), (0, we.jsxs)(It.A, { button: !0, onClick: s, children: [(0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { icon: "KeySolid" }) }), (0, we.jsx)(Vt.A, { primary: "My Account Access", secondary: A })] }), (0, we.jsxs)(Xt, { button: !0, onClick: () => { l(!0) }, "aria-describedby": "dashboard_tooltip_1", "data-tooltip": "dashboard_tooltip_1", children: [(0, we.jsx)(Ke.A, { spacing: { right: .1 }, children: (0, we.jsx)(jt.A, { children: (0, we.jsx)(Ee.Ay, { icon: "Billing", size: "lg" }) }) }), (0, we.jsx)(Vt.A, { primary: h("Dashboard.Labels.ChangeAccount"), secondary: (null === c || void 0 === c ? void 0 : c.companyName) || "" })] })] })] })] }) }; var Jt = n(42543),
            en = n(32422); const tn = () => { const e = (0, le.A)(); return (0, we.jsx)(we.Fragment, { children: (0, we.jsxs)(Tt.A, { container: !0, justifyContent: "flex-start", alignItems: "center", direction: "column", children: [(0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(Ee.Ay, { icon: "Activity", size: "x5", color: e.palette.primary.main }) }), (0, we.jsx)(se.A, { my: 2, children: (0, we.jsx)(je.A, { variant: "h1", color: "primary", children: "Coming Soon!" }) }), (0, we.jsx)(se.A, { my: 2, maxWidth: 980, width: "100%", display: "flex", flexDirection: "column", children: (0, we.jsxs)(en.A, { severity: "info", icon: !1, children: [(0, we.jsx)(je.A, { variant: "body1", align: "center", color: "primary", children: "Good things take time. In the meantime, tell us what you want to see here." }), (0, we.jsx)(se.A, { my: 2, display: "flex", alignItems: "center", justifyContent: "center", children: (0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", href: "mailto: <EMAIL>?subject=Reporting Suggestions", target: "_blank", children: "Tell us what you want to see!" }) })] }) })] }) }) }; var nn = n(43331),
            rn = n(45904),
            an = n(28814),
            on = n(52643),
            ln = n(85865); const sn = e => "viewer" === e || "editor" === e ? [{ label: "Charts", value: "charts" }, { label: "People", value: "people" }] : [{ label: "Charts", value: "charts" }, { label: "People", value: "people" }, { label: "Users", value: "permissions" }, { label: "Fields", value: "fields" }, { label: "Backups", value: "snapshots" }, { label: "Settings", value: "settings" }],
            cn = e => { let { userType: t } = e; const n = (0, $.useHistory)(),
                    { resource: r, orgId: o } = (0, $.useParams)(),
                    [i, l] = (0, a.useState)(sn(t)); return (0, a.useEffect)((() => { l(sn(t)) }), [t]), (0, we.jsx)(an.A, { value: r, indicatorColor: "primary", textColor: "primary", onChange: (e, t) => { const r = (0, ne.K7)({ orgId: o, resource: t });
                        n.push(r) }, "aria-label": "Organization Sections", children: (i || []).map((e => (0, we.jsx)(on.A, { label: (0, we.jsx)(ln.A, { variant: "subheadingMD", color: "inherit", fontWeight: 600, children: e.label }), value: e.value, id: "orgpanel-".concat(e.value) }, e.value))) }) }; var dn = n(24115),
            un = n(94916),
            hn = n(66434),
            mn = n(9368),
            pn = n(26019),
            fn = n(90930); const vn = () => { const { openDialog: e } = (0, Ft.A)("organizationMeta"), t = (0, ae.d4)(fn.BF), { userHasMinAccess: n } = (0, Ut.A)(); return (0, we.jsx)(we.Fragment, { children: (0, we.jsx)(pn.Jv, { ...t, handleEditOrg: () => e({ mode: "edit" }), isAdmin: n(Wt.td.ADMIN) }) }) }; var gn, yn = n(43005),
            bn = n(16853),
            wn = n(9579),
            zn = n(19227),
            xn = n(59691),
            An = n(14370),
            kn = n(64759),
            Sn = n(75687),
            Mn = n(64418),
            En = n(53462),
            Cn = n(10621),
            Tn = n(59177),
            Hn = n(94363),
            Ln = n(77887),
            In = n(695),
            jn = n(64021),
            Vn = n(37259),
            On = n(25747),
            Rn = n(54460),
            Pn = n(67264),
            Dn = n(31777),
            Fn = n(72220),
            Nn = n(18885); const _n = (0, ee.Ay)(Nn.A)(gn || (gn = (0, J.A)(["\n  > * {\n    ", "\n  }\n"])), (e => { let { $isDefault: t } = e; return "\n    ".concat(t ? "color: rgba(0,0,0, 0.5);" : "", "\n  ") })),
            Bn = [{ name: "label", label: "Field label", align: "left", sortable: !0 }, { name: "createdDateOrDefault", label: "", align: "left", sortable: !0 }, { name: "model", label: "Applies to", align: "left", sortable: !0 }, { name: "type", label: "Type", align: "left", sortable: !0 }, { name: "typeMetadata", label: "Additional info", align: "left", sortable: !0 }],
            Wn = () => { const { userHasMinAccess: e } = (0, Ut.A)(), { t: t } = (0, ie.B)(), n = (0, ae.wA)(), { orgId: r } = (0, $.useParams)(), { confirmAction: o } = (0, Mn.A)(), { openDialog: i } = (0, Ft.A)("customFields"), { openDialog: l } = (0, Ft.A)("copyCustomFields"), { rows: s, order: c, createSortHandler: d, totalCount: u, handleSearch: h } = (0, Sn.s)({ isPaginated: !1, dataSelector: Fn.gn, defaultValues: { order: "desc", orderBy: "isDefault", rowsPerPage: 10 } }), [m, p] = (0, a.useState)(), { isItemSelected: f, handleSelectItem: v, selected: g, resetSelected: y } = (0, Sn.U)({ selectField: "id" }), b = (e, t) => { if (t && t.id) i({ mode: "edit", field: { ...t } });
                    else if (1 === g.length) { let e = s.find((e => e.id === g[0]));
                        e && i({ mode: "edit", field: { ...e } }) } }, w = () => { i() }, { handleButtonActionClick: z } = (0, Pn.A)({ delete: () => { if (null === g || void 0 === g || !g.length) return; const e = g[0]; let t = "This will delete the custom field along with data stored in the field"; const a = s.filter((t => { var n, r, a; return (null === t || void 0 === t || null === (n = t.typeMetadata) || void 0 === n || null === (r = n.rollup) || void 0 === r || null === (a = r.field) || void 0 === a ? void 0 : a.id) === e })).map((e => e.label)).join(", ");
                        a.length && (t += "including the rollup field ".concat(a)), o({ title: "Delete Custom Field", message: t, execFunc: async () => { y(), await n(On.g.deleteCustomField({ orgId: r, fieldId: e })) } }) }, edit: b }); return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsxs)(se.A, { display: "flex", alignItems: "center", justifyContent: "space-between", my: 2, mx: 2, children: [(0, we.jsx)(se.A, { children: (0, we.jsx)(Dn.A, { buttonGroup: "organization_customfieldslist", selectedCount: g.length, handleActionClick: z }) }), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, alignItems: "center", children: [(0, we.jsx)(se.A, { minWidth: 200, children: (0, we.jsx)(En.A, { handleSearch: e => { var t; let n = null === e || void 0 === e || null === (t = e.target) || void 0 === t ? void 0 : t.value;
                                        p(n), h(e) }, handleClear: () => { p(""), h() }, query: m, placeholder: "Search Fields" }) }), (0, we.jsx)(Ge.A, { fullWidth: !0, variant: "contained", color: "secondary", onClick: () => { l({ title: "Copy Custom Fields from...", orgId: r }) }, children: "Import Fields" }), (0, we.jsx)(Ge.A, { fullWidth: !0, variant: "contained", color: "primary", onClick: w, children: "Add New Field" })] })] }), (0, we.jsxs)(se.A, { borderTop: "1px #ddd solid", flex: 1, overflow: "auto", height: "100%", children: [(0, we.jsxs)(Hn.A, { "aria-label": "dashboard", stickyHeader: !0, style: { tableLayout: "auto" }, children: [(0, we.jsx)(kn.A, { children: (0, we.jsxs)(In.A, { children: [e(Wt.td.ADMIN) && (0, we.jsx)(Ln.A, { padding: "checkbox" }), Bn.map((e => e.sortable ? (0, we.jsx)(Ln.A, { align: e.align || "left", children: (0, we.jsx)(An.A, { onClick: d(e.name), direction: c, children: e.label }) }, "custom-fields-headcell-".concat(e.name)) : (0, we.jsx)(Ln.A, { align: e.align || "left", children: e.label }, "custom-fields-headcell-".concat(e.name))))] }) }), u > 0 && (0, we.jsx)(xn.A, { children: s.map((t => { var n, r; const a = (0, Cn._Y)(null === t || void 0 === t ? void 0 : t.type),
                                        o = (null === a || void 0 === a ? void 0 : a.name) || (null === a || void 0 === a ? void 0 : a.type) || (null === t || void 0 === t ? void 0 : t.type),
                                        i = null === a || void 0 === a ? void 0 : a.icon,
                                        l = (null === t || void 0 === t || null === (n = t.typeMetadata) || void 0 === n || null === (r = n.url) || void 0 === r ? void 0 : r.template) || null; let c = null; return "rollup" === (null === t || void 0 === t ? void 0 : t.type) && (c = s.find((e => { var n, r, a; return e.id === (null === t || void 0 === t || null === (n = t.typeMetadata) || void 0 === n || null === (r = n.rollup) || void 0 === r || null === (a = r.field) || void 0 === a ? void 0 : a.id) }))), (0, we.jsxs)(_n, { onClick: e => b(0, t), $isDefault: t.isDefault, hover: !t.isDefault, children: [e(Wt.td.ADMIN) && (0, we.jsx)(Ln.A, { padding: "checkbox", children: (0, we.jsx)(bn.A, { checked: f(t), onClick: v(t), inputProps: { "aria-label": "Select field ".concat(t.label) }, size: "small", disabled: t.isDefault }) }), Bn.map((e => { return "label" === e.name ? (0, we.jsx)(Ln.A, { scope: "row", children: (0, we.jsx)(wn.Ay, { title: "private" === (null === t || void 0 === t ? void 0 : t.access) ? "This is a private field" : "", placement: "bottom", arrow: !0, children: (0, we.jsx)(se.A, { children: t[e.name] }) }) }, "custom-fields-bodyrow-".concat(t.id, "-cell-").concat(e.name)) : "model" === e.name ? (0, we.jsx)(Ln.A, { scope: "row", children: (0, Tn.ZH)(t[e.name] === Cn.A2.MEMBER ? "Person" : t[e.name]) }, "custom-fields-bodyrow-".concat(t.id, "-cell-").concat(e.name)) : "type" === e.name ? (0, we.jsx)(Ln.A, { scope: "row", children: (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "10px" }, children: [i && (0, we.jsx)(Ee.Ay, { icon: i }), o] }) }, "custom-fields-bodyrow-".concat(t.id, "-cell-").concat(e.name)) : "typeMetadata" === e.name ? (0, we.jsxs)(Ln.A, { scope: "row", children: ["currency" === (null === t || void 0 === t ? void 0 : t.type) && (0, we.jsxs)(we.Fragment, { children: ["Currency: ", (0, Tn.ZH)((null === t || void 0 === t || null === (n = t.typeMetadata) || void 0 === n || null === (r = n.currency) || void 0 === r ? void 0 : r.abbr) || "")] }), "tags" === (null === t || void 0 === t ? void 0 : t.type) && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "5px" }, children: ["Choices:", null === t || void 0 === t || null === (a = t.typeMetadata) || void 0 === a || null === (s = a.tags) || void 0 === s || null === (d = s.choices) || void 0 === d ? void 0 : d.map(((e, t) => t < 5 && (0, we.jsx)(zn.A, { label: e, size: "small", variant: "outlined" }))), (null === t || void 0 === t || null === (u = t.typeMetadata) || void 0 === u || null === (h = u.tags) || void 0 === h || null === (m = h.choices) || void 0 === m ? void 0 : m.length) > 5 && (0, we.jsx)(we.Fragment, { children: "..." })] }), "location" === (null === t || void 0 === t ? void 0 : t.type) && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "5px" }, children: ["Choices:", null === t || void 0 === t || null === (p = t.typeMetadata) || void 0 === p || null === (f = p.location) || void 0 === f || null === (v = f.choices) || void 0 === v ? void 0 : v.map(((e, t) => t < 5 && (0, we.jsx)(zn.A, { label: null === e || void 0 === e ? void 0 : e.nickname, size: "small", variant: "outlined" }))), (null === t || void 0 === t || null === (g = t.typeMetadata) || void 0 === g || null === (y = g.location) || void 0 === y || null === (b = y.choices) || void 0 === b ? void 0 : b.length) > 5 && (0, we.jsx)(we.Fragment, { children: "..." })] }), "computed" === (null === t || void 0 === t ? void 0 : t.type) && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "3px" }, children: [(0, we.jsx)(je.A, { children: "Formula:" }), (0, we.jsx)(se.A, { children: (0, we.jsx)(Rn.A, { tokens: null === t || void 0 === t || null === (w = t.typeMetadata) || void 0 === w || null === (z = w.computed) || void 0 === z ? void 0 : z.tokens }) })] }), "rollup" === (null === t || void 0 === t ? void 0 : t.type) && c && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "3px" }, children: [(0, we.jsx)(je.A, { children: "Rollup values of field:" }), null === (x = c) || void 0 === x ? void 0 : x.label] }), "url" === (null === t || void 0 === t ? void 0 : t.type) && l && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "3px" }, children: [(0, we.jsx)(je.A, { children: "Smart URL Template:" }), (0, we.jsx)(je.A, { style: { overflowWrap: "anywhere" }, children: l })] }), "iconPicklist" === (null === t || void 0 === t ? void 0 : t.type) && (null === t || void 0 === t || null === (A = t.typeMetadata) || void 0 === A || null === (k = A.iconPicklist) || void 0 === k ? void 0 : k.iconPackage) && (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "3px" }, children: [(0, we.jsx)(je.A, { children: "Icon set:" }), (0, we.jsx)(je.A, { style: { overflowWrap: "anywhere" }, children: (0, Tn.ZH)(null === t || void 0 === t || null === (S = t.typeMetadata) || void 0 === S || null === (M = S.iconPicklist) || void 0 === M ? void 0 : M.iconPackage) })] })] }, "custom-fields-bodyrow-".concat(t.id, "-cell-").concat(e.name)) : (0, we.jsx)(Ln.A, { scope: "row", children: t[e.name] }, "custom-fields-bodyrow-".concat(t.id, "-cell-").concat(e.name)); var n, r, a, s, d, u, h, m, p, f, v, g, y, b, w, z, x, A, k, S, M }))] }, "custom-fields-bodyrow-".concat(t.id)) })) })] }), !u && (0, we.jsx)(se.A, { flexGrow: 1, display: "flex", justifyContent: "flex-start", mt: 4, flexDirection: "column", children: (0, we.jsx)(jn.A, { text: t("General.Text.NothingHere"), image: Vn.A, handleClick: w, buttonText: t("General.Text.CreatYourFirstField"), isButtonVisible: e(Wt.td.ADMIN) }) })] })] }) }; var Un = n(66588),
            qn = n(79882),
            Gn = n(30458),
            Kn = n(38325),
            Zn = n(67503),
            Yn = n(8064); const Xn = [{ name: "id", label: "#", align: "left", sortable: !0, variant: "link" }, { name: "createdAt", label: "Taken On", align: "left", sortable: !0 }, { name: "type", label: "Type", align: "left", sortable: !0 }, { name: "chartsCount", label: "Charts", align: "left", sortable: !0 }, { name: "membersCount", label: "People", align: "left", sortable: !0 }, { name: "fieldsCount", label: "Custom Fields", align: "left", sortable: !0 }],
            $n = "If disabled, we will not take backup snapshot of your organization automatically every month",
            Qn = "By enabling monthly backups, we will take a backup snapshot of your organization automatically every month",
            Jn = "Take a backup snapshot of the current state of your organization",
            er = () => { const e = (0, $.useHistory)(),
                    { t: t } = (0, ie.B)(),
                    n = (0, ae.wA)(),
                    { show: r } = (0, Un.A)(),
                    o = (0, a.useRef)(),
                    i = (0, a.useRef)(0),
                    [l, s] = (0, a.useState)(null),
                    [c, d] = (0, a.useState)(!1),
                    u = (0, ae.d4)(qn.al),
                    h = (0, ae.d4)(qn.Ar),
                    m = (0, ae.d4)(qn.Nb),
                    p = (0, ae.d4)(qn.VF),
                    { closeDialog: f, openDialog: v } = (0, Ft.A)("snapshotDetails"),
                    { openDialog: g } = (0, Ft.A)("backgroundJobIndicator"),
                    { openDialog: y, closeDialog: b } = (0, Ft.A)("progressDialog"),
                    { openDialog: w } = (0, Ft.A)("upgradeRequiredDialog"),
                    { confirmAction: z } = (0, Mn.A)(),
                    x = (null === u || void 0 === u ? void 0 : u.autoSnapshotsEnabled) || !1,
                    A = x && null !== u && void 0 !== u && u.nextSnapshotDate ? new Date(u.nextSnapshotDate).toDateString() : null,
                    k = { license: p, resources: ["snapshots"], isLicenseOverLimit: !1, isOwnerOrAdmin: ["owner", "admin"].includes(null === p || void 0 === p ? void 0 : p.accessLevel), showAccountSwitcher: !1 },
                    { rows: S, page: M, order: E, createSortHandler: C, totalCount: T, rowsPerPage: H, handleChangePage: L, handleChangeRowsPerPage: I } = (0, Sn.s)({ dataSelector: qn.aK, defaultValues: { order: "asc", orderBy: "createdAt", rowsPerPage: 25 } }),
                    { isItemSelected: j, handleSelectAll: V, handleSelectItem: O, selected: R, resetSelected: P } = (0, Sn.U)({ selectField: "id" }),
                    D = async () => { h && (d(!0), await n(qn.cm.getSnapshotsList({ orgId: h })), d(!1)) }, F = async () => { m ? h && z({ title: "Taking Backup Snapshot", message: "Do you want to take a backup snapshot of your organization now?", cancelButtonText: "Cancel", confirmButtonText: "Confirm", execFunc: async () => { var e; const { payload: t, error: a } = await n(qn.cm.takeSnapshot({ orgId: h, ignoreErrorHandler: !0 })); if (a) { const e = "LimitReached" === (null === t || void 0 === t ? void 0 : t.error) ? "Reached backup snapshots limit. Check all organizations and delete old backups in order to create more." : "Something went wrong. Contact Support";
                                    r(e, "error", 1e4) } else s({ id: (null === t || void 0 === t || null === (e = t.result) || void 0 === e ? void 0 : e.jobId) || null, type: "createSnapshot" }), P(), ve.A.trackEvent({ eventName: "SNAPSHOT_BACKUP_NOW", extraParams: { orgId: h, licenseId: p.id } }) } }) : w(k) }, N = async (e, t) => { if (!m) return void w(k); const r = e || R[0];
                        h && r && z({ title: "Restoring Backup Snapshot", message: "Are you sure you want to restore the selected backup snapshot and charts?", cancelButtonText: "Cancel", confirmButtonText: "Restore", execFunc: async () => { const { payload: e, error: a } = await n(qn.cm.restoreSnapshot({ orgId: h, snapshotId: r, chartIds: t })); var o;
                                a || (s({ id: (null === e || void 0 === e || null === (o = e.result) || void 0 === o ? void 0 : o.jobId) || null, type: "restoreSnapshot" }), P(), ve.A.trackEvent({ eventName: "SNAPSHOT_RESTORED", extraParams: { orgId: h, licenseId: p.id } })) } }) }, { handleButtonActionClick: _ } = (0, Pn.A)({ delete: async () => { h && R.length && z({ title: "Deleting Backup Snapshot", message: "Are you sure you want to delete selected (".concat(R.length, ") snapshot?"), cancelButtonText: "Cancel", confirmButtonText: "Delete", execFunc: async () => { await n(qn.cm.deleteSnapshot({ orgId: h, snapshotIds: R })), P(), ve.A.trackEvent({ eventName: "SNAPSHOT_DELETED", extraParams: { orgId: h, licenseId: p.id } }) } }) }, restore: async e => { const t = "string" === typeof e && e || null,
                                r = t || R[0];
                            r && (await n((0, qn.xd)({ snapshotId: r })), v({ closeDialog: f, restoreSnapshot: N, snapshotId: t })) } }), B = "createSnapshot" === (null === l || void 0 === l ? void 0 : l.type) ? "Taking" : "Restoring", W = "".concat(B, " Backup Snapshot..."), U = async t => { var a; const o = null === t || void 0 === t || null === (a = t.job) || void 0 === a ? void 0 : a.jobType; if ("createSnapshot" === o && (await D(), r("Backup snapshot created successfully", "success", 5e3)), "restoreSnapshot" === o) { var i; const a = (null === t || void 0 === t || null === (i = t.job) || void 0 === i ? void 0 : i.results) || {},
                                { newOrgId: o, newOrgName: l, accessLevel: s } = a;
                            n((0, me.F3)({ organization: { id: o, name: l, accessLevel: s } })), e.push((0, ne.r2)({ orgId: o })), r("Backup snapshot restored successfully", "success", 5e3) } s(null) }; return (0, a.useEffect)((() => { D() }), [h]), (0, a.useEffect)((() => (null !== l && void 0 !== l && l.id && (o.current = setInterval((() => {
                    (async () => { if (i.current > 120) return b(), r("Too many attempts, aborting", "error", 5e3), clearInterval(o.current), void(i.current = 0);
                        y({ title: "".concat(B, " Backup Snapshot"), message: "We are working on this. This may take a few minutes...", handleBackground: () => { g({ jobId: null === l || void 0 === l ? void 0 : l.id, title: W, alertBodyMessage: "You will receive a notification when the job is complete." }), f(), b() } }); const { error: e, payload: t } = await n(Gn.zU.getJobStatus({ jobId: null === l || void 0 === l ? void 0 : l.id })); var a, s, c, d; if (e) clearInterval(o.current), b(), r(e.message, "error", 5e3);
                        else if ("failed" === (null === t || void 0 === t || null === (a = t.data) || void 0 === a ? void 0 : a.status)) clearInterval(o.current), b(), r("".concat(B, " Backup Snapshot Failed"), "error");
                        else if ("queued" === (null === t || void 0 === t || null === (s = t.data) || void 0 === s ? void 0 : s.status)) i.current++;
                        else if ("dequeued" === (null === t || void 0 === t || null === (c = t.data) || void 0 === c ? void 0 : c.status)) i.current++;
                        else if ("complete" === (null === t || void 0 === t || null === (d = t.data) || void 0 === d ? void 0 : d.status)) try { clearInterval(o.current), b(), await U(t) } catch (u) { r("".concat(B, " Backup Snapshot Failed"), "error") } })() }), 1e3)), () => { clearInterval(o.current) })), [l]), (0, we.jsxs)(dn.A, { loading: c, children: [(0, we.jsx)(se.A, { p: 2, borderBottom: "solid 1px #ddd", children: (0, we.jsxs)(Tt.A, { container: !0, justifyContent: "space-between", alignItems: "center", children: [(0, we.jsx)(se.A, { item: !0, children: (0, we.jsx)(Dn.A, { buttonGroup: "organization_snapshots", selectedCount: R.length, handleActionClick: _, getIsButtonValid: () => !(0 === R.length || 0 === (null === S || void 0 === S ? void 0 : S.length)) }) }), (0, we.jsxs)(se.A, { item: !0, display: "flex", alignItems: "center", gridGap: 12, children: [x && A && (0, we.jsxs)(je.A, { children: ["Next scheduled backup on: ", A] }), x && (0, we.jsx)(wn.Ay, { title: $n, placement: "bottom", arrow: !0, children: (0, we.jsx)(se.A, { children: (0, we.jsx)(Ge.A, { variant: "outlined", color: "error", onClick: async () => { h && z({ title: "Disable Backup Snapshot", message: "By disabling auto snapshots, Organimi will no longer take a backup of your organization monthly", cancelButtonText: "Cancel", confirmButtonText: "Disable", execFunc: async () => { const { error: e } = await n(qn.cm.disableAutoSnapshots({ orgId: h }));
                                                        e || (r("Monthly backup snapshots disabled", "success", 5e3), ve.A.trackEvent({ eventName: "SNAPSHOT_AUTO_DISABLED", extraParams: { orgId: h, licenseId: p.id } })) } }) }, children: "Disable Monthly Backups" }) }) }), !x && (0, we.jsx)(wn.Ay, { title: Qn, placement: "bottom", arrow: !0, children: (0, we.jsx)(se.A, { children: (0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", onClick: async () => { m ? h && z({ title: "Enabling Backup Snapshot", message: "By enabling auto snapshots, Organimi will take a backup of your organization monthly", cancelButtonText: "Cancel", confirmButtonText: "Enable", execFunc: async () => { const { error: e } = await n(qn.cm.enableAutoSnapshots({ orgId: h }));
                                                        e || (r("Monthly backup snapshots successfully enabled", "success", 5e3), ve.A.trackEvent({ eventName: "SNAPSHOT_AUTO_ENABLED", extraParams: { orgId: h, licenseId: p.id } })) } }) : w(k) }, children: "Enable Monthly Backups" }) }) }), (0, we.jsx)(wn.Ay, { title: Jn, placement: "bottom", arrow: !0, children: (0, we.jsx)(se.A, { children: (0, we.jsx)(Ge.A, { variant: "contained", color: "primary", onClick: F, children: "Take Backup Now" }) }) })] })] }) }), T > 0 && (0, we.jsx)(se.A, { flex: 1, overflow: "auto", height: "100%", children: (0, we.jsxs)(Zn.A, { "aria-label": "dashboard", size: "small", stickyHeader: !0, children: [(0, we.jsx)(kn.A, { children: (0, we.jsxs)(In.A, { children: [(0, we.jsx)(Ln.A, { padding: "checkbox", children: (0, we.jsx)(bn.A, { indeterminate: R.length > 0 && R.length < S.length, checked: R.length === S.length, onClick: V(S), inputProps: { "aria-label": "Select all charts" }, size: "small" }) }), Xn.map((e => e.sortable ? (0, we.jsx)(Ln.A, { align: e.align || "left", children: (0, we.jsx)(An.A, { onClick: C(e.name), direction: E, children: e.label }) }, "organization-chartlist-headcell-".concat(e.name)) : (0, we.jsx)(Ln.A, { align: e.align || "left", children: e.label }, "organization-chartlist-headcell-".concat(e.name))))] }) }), (0, we.jsx)(xn.A, { children: S.map(((e, t) => { var n, r; return (0, we.jsx)(Yn.f, { hover: !0, style: { position: "relative" }, children: (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(Ln.A, { padding: "checkbox", style: { paddingLeft: null !== (n = e.alias) && void 0 !== n && n.roleId ? 28 : 18, borderLeft: null !== (r = e.alias) && void 0 !== r && r.roleId ? "solid 4px #3CD3C2" : "none" }, children: (0, we.jsx)(bn.A, { checked: j(e), onClick: O(e), inputProps: { "aria-label": "Select chart ".concat(e.name) }, size: "small" }) }), Xn.map((n => { let r = e[n.name]; var a;
                                                ("id" === n.name && (r = t + 1), "createdAt" === n.name && r) && (r = (null === (a = new Date(r)) || void 0 === a ? void 0 : a.toDateString()) || r); return (0, we.jsx)(Ln.A, { scope: "row", children: r }, "organization-chartlist-bodyrow-".concat(e.id, "-cell-").concat(n.name)) }))] }) }, "organization-chartlist-bodyrow-".concat(e.id)) })) })] }) }), T > H && (0, we.jsx)(se.A, { position: "sticky", left: 0, right: 0, bottom: 0, bgcolor: "#ffffff", borderTop: "solid 1px #ddd", children: (0, we.jsx)(Kn.A, { rowsPerPageOptions: [25, 50, 100, 250], component: "div", count: T, rowsPerPage: H, page: M, backIconButtonProps: { "aria-label": t("Common.Tables.backIconButtonText") }, nextIconButtonProps: { "aria-label": t("Common.Tables.nextIconButtonText") }, onPageChange: L, onRowsPerPageChange: I, labelRowsPerPage: t("Common.Tables.labelRowsPerPage"), backIconButtonText: t("Common.Tables.backIconButtonText"), nextIconButtonText: t("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: n, to: r, count: a } = e; return "".concat(n, "-").concat(r, " ").concat(t("Common.Tables.of"), " ").concat(a) } }) }), !T && (0, we.jsx)(Yn.X, { headCells: Xn, text: "Mistakes happen, create backups to restore your data later", image: Vn.A, handleClick: F, buttonText: "Take Backup Now", isButtonVisible: !0 })] }) }; var tr = n(84373); const nr = () => (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, we.jsxs)(se.A, { display: "flex", flexDirection: "row", p: 4, alignItems: "flex-start", mb: 2, children: [(0, we.jsx)(tr.A, { variant: "circle", width: 80, height: 80 }), (0, we.jsxs)(se.A, { px: 2, children: [(0, we.jsx)(tr.A, { variant: "text", width: 200, height: 50 }), (0, we.jsx)(tr.A, { variant: "text", width: 600 }), (0, we.jsx)(tr.A, { variant: "text", width: 600 })] })] }), (0, we.jsx)(ce.A, {}), (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", children: [(0, we.jsxs)(se.A, { display: "flex", gridGap: 16, m: 2, children: [(0, we.jsx)(tr.A, { variant: "text", width: 50 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 })] }), (0, we.jsx)(ce.A, {}), (0, we.jsxs)(se.A, { display: "flex", gridGap: 16, m: 2, children: [(0, we.jsx)(tr.A, { variant: "text", width: 50 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 }), (0, we.jsx)(tr.A, { variant: "text", width: 100 })] })] })] }); var rr = n(43781); const ar = a.lazy((() => (0, hn.D)((() => n.e(9530).then(n.bind(n, 89530)))))),
            or = a.lazy((() => (0, hn.D)((() => n.e(1636).then(n.bind(n, 61636)))))),
            ir = a.lazy((() => (0, hn.D)((() => n.e(7725).then(n.bind(n, 97725)))))),
            lr = { charts: (0, we.jsx)(ar, {}), settings: (0, we.jsx)(or, {}), people: (0, we.jsx)(ir, {}), permissions: (0, we.jsx)(yn.A, {}), fields: (0, we.jsx)(Wn, {}), snapshots: (0, we.jsx)(er, {}) },
            sr = () => { const [e] = (0, un.A)("activeLicenseId", null, !0), [t, n] = (0, un.A)("license.recent.organization.".concat(e)), r = (0, $.useHistory)(), [o, i] = (0, a.useState)(!0), [l, s] = (0, a.useState)(!0), c = (0, ae.wA)(), [d, u] = (0, a.useState)(!1), { userType: h } = (0, Ut.A)(), m = (0, mn.A)(), p = (0, Q.A)(), { params: { resource: f, orgId: v } } = (0, re.u)((0, ne.K7)()), g = (0, rr.A)({ orgId: v });
                (0, a.useEffect)((() => { v && (n(v), async function() { const { error: e } = await c(nn.UY.get({ orgId: v }));
                        i(!1), e && v === t && (n(null), r.replace((0, ne.ze)())), e || u(!0) }()) }), [v]), (0, a.useEffect)((() => { v && !m && async function() { s(!0), await Promise.all([c(nn.UY.getCharts({ orgId: v })), !m && c(me.lf.getUserTours({ orgId: v, utilizeTemplateTour: null === p || void 0 === p ? void 0 : p.utilizeTemplateTour }))]), s(!1) }() }), [v]); const y = lr[f]; return (0, we.jsx)(dn.A, { loading: !d, FallbackComponent: nr, children: (0, we.jsxs)(se.A, { display: "flex", flexDirection: "column", height: "100%", children: [(0, we.jsx)(se.A, { p: 2, children: (0, we.jsx)(vn, {}) }), (0, we.jsx)(se.A, { px: 2, borderBottom: "solid 1px #ddd", borderTop: "solid 1px #ddd", children: (0, we.jsx)(cn, { userType: h }) }), (0, we.jsx)(a.Suspense, { fallback: (0, we.jsx)(se.A, { alignItems: "center", justifyContent: "center", display: "flex", children: (0, we.jsx)(rn.A, {}) }), children: (0, we.jsx)(dn.A, { loading: l || o || g, children: y }) })] }) }) }; var cr, dr = n(42887),
            ur = n(44676),
            hr = n(70318),
            mr = n(70128),
            pr = n(86913); const fr = (0, ee.Ay)(je.A)(cr || (cr = (0, J.A)(["\n  font-style: italic;\n  color: #999;\n  font-size: 16px;\n"]))),
            vr = (e, t) => { const n = document.createElement("a");
                n.href = "data:text/csv;charset=utf-8," + encodeURI(e), n.target = "_blank", n.download = "".concat(t, ".csv"), n.click(), n.remove() },
            gr = () => { const e = (0, ae.wA)(),
                    { t: t } = (0, ie.B)(),
                    n = (0, le.A)(),
                    r = (0, ae.d4)(dr.Zl),
                    o = (0, ae.d4)(dr.jO),
                    i = (0, ae.d4)(dr.vm),
                    [l, s] = (0, a.useState)(null),
                    [c, d] = (0, a.useState)(null),
                    [u, h] = (0, a.useState)(!1),
                    [m, p] = (0, a.useState)(),
                    [f, v] = (0, a.useState)(),
                    g = [{ id: "createdAt", label: "Date of Report", align: "left" }, ...o.slice(0, 6)],
                    { rows: y, order: b, createSortHandler: w, totalCount: z, page: x, rowsPerPage: A, handleChangePage: k, handleChangeRowsPerPage: S } = (0, Sn.s)({ dataSelector: dr.v3, defaultValues: { order: "desc", orderBy: "createdAt", rowsPerPage: 10 } });
                (0, a.useEffect)((() => { const e = null === y || void 0 === y ? void 0 : y.find((e => (null === e || void 0 === e ? void 0 : e.createdAt) === (null === r || void 0 === r ? void 0 : r.startDate))),
                        t = null === y || void 0 === y ? void 0 : y.find((t => (null === t || void 0 === t ? void 0 : t.createdAt) === (null === r || void 0 === r ? void 0 : r.endDate) && (null === t || void 0 === t ? void 0 : t.id) !== (null === e || void 0 === e ? void 0 : e.id)));
                    p(e), v(t) }), [y, null === r || void 0 === r ? void 0 : r.startDate, null === r || void 0 === r ? void 0 : r.endDate]); const M = (t, n) => { const r = null === t || void 0 === t ? void 0 : t.createdAt; "first" !== n ? "second" !== n || e((0, pr.oB)({ which: "endDate", value: r })) : e((0, pr.oB)({ which: "startDate", value: r })) },
                    E = async function() { var t; let n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "single",
                            a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                        h(!0); const o = "org" === (null === r || void 0 === r ? void 0 : r.reportLevel) ? pr.fz.exportOrgReport : pr.fz.exportChartReport,
                            l = null === r || void 0 === r ? void 0 : r.reportLevel,
                            s = null === (t = y[(null === y || void 0 === y ? void 0 : y.length) - 1]) || void 0 === t ? void 0 : t.createdAt,
                            c = (0, mr.sq)(new Date),
                            { payload: d } = await e(o({ reportLevel: l, mode: n, startDate: s, endDate: c, reportId: null === a || void 0 === a ? void 0 : a.id, orgId: null === r || void 0 === r ? void 0 : r.orgId, chartIds: [null === r || void 0 === r ? void 0 : r.chartId], orgName: null === r || void 0 === r ? void 0 : r.orgName, chartName: null === r || void 0 === r ? void 0 : r.chartName }));
                        null !== d && void 0 !== d && d.csv && vr(null === d || void 0 === d ? void 0 : d.csv, null === d || void 0 === d ? void 0 : d.title), ve.A.trackEvent({ eventName: mr.hK.download, extraParams: { ...i, mode: n, reportLevel: l, startDate: s, endDate: c } }), h(!1) }, C = e => t => { t && (null === t || void 0 === t || t.stopPropagation()), s(null === t || void 0 === t ? void 0 : t.currentTarget), d(e) }, T = () => { s(null), d(null) }, H = e => { const t = (e => ({ download: () => (T(), E("single", e)), email: () => { T() }, setFirstReport: () => (T(), M(e, "first")), setSecondReport: () => (T(), M(e, "second")) }))(e); return null !== y && void 0 !== y && y.length ? (0, we.jsxs)(Ln.A, { align: "center", children: [(0, we.jsx)(hr.A, { size: "small", onClick: C(e), children: (0, we.jsx)(Ee.Ay, { icon: "EllipsesV" }) }), (0, we.jsxs)(ur.A, { id: "".concat(null === e || void 0 === e ? void 0 : e.id, "-context-menu"), anchorEl: l, getContentAnchorEl: null, anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "left" }, keepMounted: !0, open: c === e && null !== l, onClose: () => T(), children: [(0, we.jsx)(Ze.A, { onClick: t.download, children: (0, we.jsx)(je.A, { children: "Download" }) }), (0, we.jsx)(Ze.A, { onClick: t.setFirstReport, children: (0, we.jsx)(je.A, { children: "Add to Compare (First)" }) }), (0, we.jsx)(Ze.A, { onClick: t.setSecondReport, children: (0, we.jsx)(je.A, { children: "Add to Compare (Second)" }) })] })] }) : (0, we.jsx)(we.Fragment, {}) }; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { children: (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "20px" }, children: [(0, we.jsx)(je.A, { variant: "h3", children: "Report History" }), u || (null === r || void 0 === r ? void 0 : r.loadingCounter) > 0 ? (0, we.jsx)(rn.A, { size: 30 }) : (0, we.jsx)(we.Fragment, {}), (0, we.jsx)(se.A, { marginLeft: "auto", minWidth: 200, children: (0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", onClick: () => E("multiple"), fullWidth: !0, disabled: u || (null === r || void 0 === r ? void 0 : r.loadingCounter) > 0 || !(null !== y && void 0 !== y && y.length), children: "Export History" }) })] }) }), (null === r || void 0 === r ? void 0 : r.loadingCounter) > 0 ? (0, we.jsx)(we.Fragment, {}) : (0, we.jsxs)(we.Fragment, { children: [null !== y && void 0 !== y && y.length ? (0, we.jsx)(we.Fragment, {}) : (0, we.jsx)(se.A, { my: 1, py: 3, borderBottom: "solid 1px #ccc", children: (0, we.jsx)(fr, { children: "No history available" }) }), (0, we.jsxs)(Hn.A, { "aria-label": "dashboard", size: "small", style: { tableLayout: "auto" }, children: [(0, we.jsx)(kn.A, { children: (0, we.jsxs)(In.A, { children: [(0, we.jsx)(Ln.A, { padding: "checkbox" }), g.map((e => (0, we.jsx)(Ln.A, { align: (null === e || void 0 === e ? void 0 : e.align) || "right", children: (0, we.jsx)(An.A, { onClick: w(null === e || void 0 === e ? void 0 : e.id), direction: b, children: null === e || void 0 === e ? void 0 : e.label }) }, "report-history-headcell-".concat(null === e || void 0 === e ? void 0 : e.id)))), (0, we.jsx)(Ln.A, { align: "center" })] }) }), (0, we.jsxs)(xn.A, { children: [y.length ? (0, we.jsx)(we.Fragment, {}) : [...Array(5).keys()].map((e => (0, we.jsxs)(In.A, { children: [(0, we.jsx)(Ln.A, {}), (0, we.jsx)(Ln.A, { align: "center", children: (0, we.jsx)(je.A, { children: "-" }) }), [...Array(6).keys()].map((e => (0, we.jsx)(Ln.A, { align: "right", children: (0, we.jsx)(je.A, { children: "-" }) }, e))), (0, we.jsx)(Ln.A, {})] }, "report-history-bodyrow-".concat(e)))), y.map((e => { return (0, we.jsxs)(In.A, { children: [(t = null === e || void 0 === e ? void 0 : e.id, t === (null === m || void 0 === m ? void 0 : m.id) ? (0, we.jsx)(Ln.A, { padding: "checkbox", children: (0, we.jsx)("div", { children: (0, we.jsx)(Ee.Ay, { icon: "FavouriteCircle", size: "sm", color: n.palette.secondary.main }) }) }) : t === (null === f || void 0 === f ? void 0 : f.id) ? (0, we.jsx)(Ln.A, { padding: "checkbox", children: (0, we.jsx)("div", { children: (0, we.jsx)(Ee.Ay, { icon: "FavouriteCircle", size: "sm", color: n.palette.primary.main }) }) }) : (0, we.jsx)(Ln.A, { padding: "checkbox" })), g.map((t => (0, we.jsx)(Ln.A, { scope: "row", align: (null === t || void 0 === t ? void 0 : t.align) || "right", children: (0, we.jsx)(je.A, { children: (0, mr.Wo)(null === e || void 0 === e ? void 0 : e[null === t || void 0 === t ? void 0 : t.id]) ? null === e || void 0 === e ? void 0 : e[null === t || void 0 === t ? void 0 : t.id] : "-" }) }, "report-history-bodyrow-".concat(null === e || void 0 === e ? void 0 : e.id, "-cell-").concat(null === t || void 0 === t ? void 0 : t.id)))), H(e)] }, "report-history-bodyrow-".concat(null === e || void 0 === e ? void 0 : e.id)); var t }))] })] }), z > 0 && (0, we.jsx)(Kn.A, { rowsPerPageOptions: [5, 10, 25, 50, 100, 250], component: "div", count: z, rowsPerPage: A, page: x, onPageChange: k, onRowsPerPageChange: S, labelRowsPerPage: t("Common.Tables.labelRowsPerPage"), backIconButtonText: t("Common.Tables.backIconButtonText"), nextIconButtonText: t("Common.Tables.nextIconButtonText"), labelDisplayedRows: e => { let { from: n, to: r, count: a } = e; return "".concat(n, "-").concat(r, " ").concat(t("Common.Tables.of"), " ").concat(a) } })] })] }) }; var yr = n(85279),
            br = n(97563); const wr = e => (0, mr.Wo)(e) ? e : "-",
            zr = e => { var t, n, r, a; let { value: o } = e; const i = (0, le.A)(),
                    l = o > 0 ? null === i || void 0 === i || null === (t = i.palette) || void 0 === t || null === (n = t.success) || void 0 === n ? void 0 : n.main : o < 0 ? null === i || void 0 === i || null === (r = i.palette) || void 0 === r || null === (a = r.warning) || void 0 === a ? void 0 : a.dark : "",
                    s = o > 0 ? "+ ".concat(wr(o)) : wr(o); return (0, we.jsx)(je.A, { align: "center", weight: "bold", color: l, children: s }) },
            xr = e => { let { row: t, isGroup: n = !1, open: r } = e; return (0, we.jsxs)(Tt.A, { container: !0, style: { borderBottom: "1px solid #e7e7e7" }, children: [(0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsxs)(Tt.A, { container: !0, style: { gap: 10 }, children: [n && r && (0, we.jsx)(se.A, { children: (0, we.jsx)(Ee.Ay, { icon: "CaretDown", size: "lg" }) }), n && !r && (0, we.jsx)(se.A, { children: (0, we.jsx)(Ee.Ay, { icon: "CaretRight", size: "lg" }) }), (0, we.jsx)(se.A, { ml: 5 * ((null === t || void 0 === t ? void 0 : t.indent) || 0), children: (0, we.jsx)(je.A, { weight: n ? "bold" : "body1", children: wr(null === t || void 0 === t ? void 0 : t.label) }) })] }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(je.A, { align: "center", children: wr(null === t || void 0 === t ? void 0 : t.startValue) }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(je.A, { align: "center", children: wr(null === t || void 0 === t ? void 0 : t.endValue) }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(zr, { value: null === t || void 0 === t ? void 0 : t.difference }) })] }, null === t || void 0 === t ? void 0 : t.id) },
            Ar = e => { var t, n, r; let { row: a, filters: o, isOpen: i, updateGroupOpenState: l } = e; return (0, we.jsxs)(we.Fragment, { children: [!(null !== a && void 0 !== a && null !== (t = a.data) && void 0 !== t && t.length) && (0, we.jsx)(xr, { row: a }), (null === a || void 0 === a || null === (n = a.data) || void 0 === n ? void 0 : n.length) && (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { my: 1, onClick: () => l(null === a || void 0 === a ? void 0 : a.label, !i), style: { cursor: "pointer" }, children: (0, we.jsx)(xr, { row: a, isGroup: !0, open: i }, null === a || void 0 === a ? void 0 : a.label) }), (0, we.jsx)(br.A, { in: i, timeout: "auto", unmountOnExit: !0, children: null === a || void 0 === a || null === (r = a.data) || void 0 === r ? void 0 : r.map(((e, t) => { var n; return (null === o || void 0 === o || null === (n = o.metrics) || void 0 === n ? void 0 : n.find((t => (null === t || void 0 === t ? void 0 : t.id) === (null === e || void 0 === e ? void 0 : e.metricId)))) && (0, we.jsx)(se.A, { my: 1, children: (0, we.jsx)(xr, { row: e }) }, "".concat(null === a || void 0 === a ? void 0 : a.label, "-").concat(null === e || void 0 === e ? void 0 : e.label, "-").concat(null === e || void 0 === e ? void 0 : e.startValue, "-").concat(null === e || void 0 === e ? void 0 : e.endValue, "-").concat(t)) })) })] })] }) },
            kr = () => { const e = (0, ae.wA)(),
                    t = (0, ae.d4)(dr.Zl),
                    { data: n } = (0, ae.d4)(dr.fc),
                    r = (0, ae.d4)(dr.PW),
                    o = (0, ae.d4)(dr.TF),
                    i = (0, ae.d4)(dr.vm),
                    [l, s] = (0, un.A)("reports.groups.openState", {}),
                    [c, d] = (0, a.useState)(!1),
                    u = (e, n) => { const r = null === t || void 0 === t ? void 0 : t.reportLevel;
                        r && s({ ...l, [r]: { ...(null === l || void 0 === l ? void 0 : l[r]) || {}, [e]: n } }) },
                    h = (t, n) => { var r; const a = new Date(null === (r = new Date(n)) || void 0 === r ? void 0 : r.setHours(0, 0, 0, 0)),
                            o = (0, mr.sq)(a);
                        e((0, pr.oB)({ which: t, value: o })) }; return (0, we.jsxs)(we.Fragment, { children: [(0, we.jsx)(se.A, { children: (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", style: { gap: "20px" }, children: [(0, we.jsx)(je.A, { variant: "h3", children: "Compare Reports" }), (c || (null === t || void 0 === t ? void 0 : t.loadingCounter) > 0) && (0, we.jsx)(rn.A, { size: 30 }), (0, we.jsx)(se.A, { marginLeft: "auto", minWidth: 200, children: (0, we.jsx)(Ge.A, { variant: "outlined", color: "primary", onClick: () => (async () => { d(!0); const n = "org" === (null === t || void 0 === t ? void 0 : t.reportLevel) ? pr.fz.exportOrgReport : pr.fz.exportChartReport,
                                            a = "comparison",
                                            o = null === t || void 0 === t ? void 0 : t.reportLevel,
                                            l = null === t || void 0 === t ? void 0 : t.startDate,
                                            s = null === t || void 0 === t ? void 0 : t.endDate,
                                            { payload: c } = await e(n({ reportLevel: o, mode: a, startDate: l, endDate: s, orgId: null === t || void 0 === t ? void 0 : t.orgId, chartIds: [null === t || void 0 === t ? void 0 : t.chartId], orgName: null === t || void 0 === t ? void 0 : t.orgName, chartName: null === t || void 0 === t ? void 0 : t.chartName, comparisonReport: r }));
                                        null !== c && void 0 !== c && c.csv && vr(null === c || void 0 === c ? void 0 : c.csv, null === c || void 0 === c ? void 0 : c.title), ve.A.trackEvent({ eventName: mr.hK.download, extraParams: { ...i, mode: a, reportLevel: o, startDate: l, endDate: s } }), d(!1) })(), fullWidth: !0, disabled: c || (null === t || void 0 === t ? void 0 : t.loadingCounter) > 0 || !(null !== o && void 0 !== o && o.length) || !(null !== t && void 0 !== t && t.startDate) || !(null !== t && void 0 !== t && t.endDate), children: "Export Comparison" }) })] }) }), (null === t || void 0 === t ? void 0 : t.loadingCounter) > 0 && (0, we.jsx)(se.A, { p: 20 }), (null === t || void 0 === t ? void 0 : t.loadingCounter) <= 0 && (0, we.jsxs)(we.Fragment, { children: [0 === (null === o || void 0 === o ? void 0 : o.length) && (0, we.jsx)(se.A, { my: 1, borderBottom: "solid 1px #ccc", py: 3, children: (0, we.jsx)(fr, { children: "No reports to compare" }) }), (0, we.jsx)(se.A, { my: 1, py: 2, children: (0, we.jsxs)(Tt.A, { container: !0, alignItems: "center", children: [(0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(je.A, { weight: "bold", children: "Metric" }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(yr.A, { label: "First Report", clearable: !1, type: "date", value: null === t || void 0 === t ? void 0 : t.startDate, fullWidth: !1, justifyContent: "center", onChange: e => h("startDate", e), format: "DD", disabled: !(null !== o && void 0 !== o && o.length) }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, children: (0, we.jsx)(yr.A, { label: "Second Report", clearable: !1, type: "date", value: null === t || void 0 === t ? void 0 : t.endDate, fullWidth: !1, justifyContent: "center", onChange: e => h("endDate", e), format: "DD", disabled: !(null !== o && void 0 !== o && o.length) }) }), (0, we.jsx)(se.A, { style: { flex: 1 }, align: "center", children: (0, we.jsx)(je.A, { weight: "bold", children: "Difference" }) })] }) }), (0, we.jsx)(se.A, { maxHeight: "50vh", overflow: "auto", style: { borderTop: "1px solid #e7e7e7" }, children: null === n || void 0 === n ? void 0 : n.map(((e, n) => { var r, a, o, i; const s = (null === l || void 0 === l ? void 0 : l[null === t || void 0 === t ? void 0 : t.reportLevel]) || {},
                                    c = null !== (r = Object.keys(s)) && void 0 !== r && r.length ? (null === s || void 0 === s ? void 0 : s[null === e || void 0 === e ? void 0 : e.label]) || !1 : 0 === n; return (null !== e && void 0 !== e && null !== (a = e.data) && void 0 !== a && a.length ? !(null === t || void 0 === t || null === (o = t.metrics) || void 0 === o || !o.find((t => (null === t || void 0 === t ? void 0 : t.groupLabel) === (null === e || void 0 === e ? void 0 : e.label)))) : !(null === t || void 0 === t || null === (i = t.metrics) || void 0 === i || !i.find((t => (null === t || void 0 === t ? void 0 : t.id) === (null === e || void 0 === e ? void 0 : e.metricId))))) && (0, we.jsx)(se.A, { my: 3, children: (0, we.jsx)(Ar, { row: e, filters: t, isOpen: c, updateGroupOpenState: u }) }, "".concat(null === e || void 0 === e ? void 0 : e.label, "-").concat(n)) })) })] })] }) }; var Sr = n(77325); const Mr = () => { var e; const t = (0, ae.wA)(),
                n = (0, $.useHistory)(),
                r = (0, Q.A)(),
                o = (0, Kt.A)(),
                [i, l] = (0, un.A)("reports.filters"),
                s = (0, a.useRef)(!0),
                c = (0, ae.d4)(dr.Zl),
                d = (0, ae.d4)(dr.KE),
                u = (0, ae.d4)(dr.tU),
                h = (0, ae.d4)(dr.Su),
                m = [{ id: "org", label: "Organization Report" }, { id: "chart", label: "Chart Report" }],
                p = function() { let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                        t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; const a = { ...r, ...e };
                    t && Object.keys(e).forEach((e => { delete a[e] })); const o = "?".concat(Object.keys(a).map((e => "".concat(e, "=").concat(a[e]))).join("&"));
                    n.push({ pathname: (0, ne.Io)(), search: o }) },
