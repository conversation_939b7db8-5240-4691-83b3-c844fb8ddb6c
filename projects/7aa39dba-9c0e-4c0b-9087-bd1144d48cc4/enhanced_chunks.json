[{"id": "enhanced_chunk_0", "content": "\n\n// === html.js ===\n\n/**\n * @license\n * Copyright (c) 2018 <PERSON>\n * Released under the MIT License.\n *\n * Licensed under the MIT License.\n * http://opensource.org/licenses/mit-license\n */\n\nimport { jsPDF } from \"../jspdf.js\";\nimport { normalizeFontFace } from \"../libs/fontFace.js\";\nimport { globalObject } from \"../libs/globalObject.js\";\n\n/**\n * jsPDF html PlugIn\n *\n * @name html\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n\n  function loadHtml2Canvas() {\n    return (function() {\n      if (globalObject[\"html2canvas\"]) {\n        return Promise.resolve(globalObject[\"html", "metadata": {"token_count": 150, "start_token": 0, "end_token": 150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_0"}}, {"id": "enhanced_chunk_1", "content": "Html2Canvas() {\n    return (function() {\n      if (globalObject[\"html2canvas\"]) {\n        return Promise.resolve(globalObject[\"html2canvas\"]);\n      }\n\n\n      if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n        return new Promise(function(resolve, reject) {\n          try {\n            resolve(require(\"html2canvas\"));\n          } catch (e) {\n            reject(e);\n          }\n        });\n      }\n      if (typeof define === \"function\" && define.amd) {\n        return new Promise(function(resolve, reject) {\n          try {\n            require([\"html2canvas\"], resolve);\n          } catch (e) {\n            reject(e);\n          }\n        });\n      }\n      return Promise.reject(new Error", "metadata": {"token_count": 150, "start_token": 120, "end_token": 270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_1"}}, {"id": "enhanced_chunk_2", "content": "([\"html2canvas\"], resolve);\n          } catch (e) {\n            reject(e);\n          }\n        });\n      }\n      return Promise.reject(new Error(\"Could not load html2canvas\"));\n    })()\n      .catch(function(e) {\n        return Promise.reject(new Error(\"Could not load html2canvas: \" + e));\n      })\n      .then(function(html2canvas) {\n        return html2canvas.default ? html2canvas.default : html2canvas;\n      });\n  }\n\n  function loadDomPurify() {\n    return (function() {\n      if (globalObject[\"DOMPurify\"]) {\n        return Promise.resolve(globalObject[\"DOMPurify\"]);\n      }\n\n\n      if (typeof exports === \"object\" && typeof module", "metadata": {"token_count": 150, "start_token": 240, "end_token": 390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_2"}}, {"id": "enhanced_chunk_3", "content": "urify\"]) {\n        return Promise.resolve(globalObject[\"DOMPurify\"]);\n      }\n\n\n      if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n        return new Promise(function(resolve, reject) {\n          try {\n            resolve(require(\"dompurify\"));\n          } catch (e) {\n            reject(e);\n          }\n        });\n      }\n      if (typeof define === \"function\" && define.amd) {\n        return new Promise(function(resolve, reject) {\n          try {\n            require([\"dompurify\"], resolve);\n          } catch (e) {\n            reject(e);\n          }\n        });\n      }\n      return Promise.reject(new Error(\"Could not load dompurify\"));\n    })()\n      .catch(function(e)", "metadata": {"token_count": 150, "start_token": 360, "end_token": 510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_3"}}, {"id": "enhanced_chunk_4", "content": ");\n          }\n        });\n      }\n      return Promise.reject(new Error(\"Could not load dompurify\"));\n    })()\n      .catch(function(e) {\n        return Promise.reject(new Error(\"Could not load dompurify: \" + e));\n      })\n      .then(function(dompurify) {\n        return dompurify.default ? dompurify.default : dompurify;\n      });\n  }\n\n  /**\n   * Determine the type of a variable/object.\n   *\n   * @private\n   * @ignore\n   */\n  var objType = function(obj) {\n    var type = typeof obj;\n    if (type === \"undefined\") return \"undefined\";\n    else if (type === \"string\" || obj instanceof String) return", "metadata": {"token_count": 150, "start_token": 480, "end_token": 630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_4"}}, {"id": "enhanced_chunk_5", "content": " typeof obj;\n    if (type === \"undefined\") return \"undefined\";\n    else if (type === \"string\" || obj instanceof String) return \"string\";\n    else if (type === \"number\" || obj instanceof Number) return \"number\";\n    else if (type === \"function\" || obj instanceof Function) return \"function\";\n    else if (!!obj && obj.constructor === Array) return \"array\";\n    else if (obj && obj.nodeType === 1) return \"element\";\n    else if (type === \"object\") return \"object\";\n    else return \"unknown\";\n  };\n\n  /**\n   * Create an HTML element with optional className, innerHTML, and style.\n   *\n   * @private\n   * @ignore", "metadata": {"token_count": 150, "start_token": 600, "end_token": 750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_5"}}, {"id": "enhanced_chunk_6", "content": " };\n\n  /**\n   * Create an HTML element with optional className, innerHTML, and style.\n   *\n   * @private\n   * @ignore\n   */\n  var createElement = function(tagName, opt) {\n    var el = document.createElement(tagName);\n    if (opt.className) el.className = opt.className;\n    if (opt.innerHTML && opt.dompurify) {\n      el.innerHTML = opt.dompurify.sanitize(opt.innerHTML);\n    }\n    for (var key in opt.style) {\n      el.style[key] = opt.style[key];\n    }\n    return el;\n  };\n\n  /**\n   * Deep-clone a node and preserve contents/properties.\n   *\n   * @private\n   * @ignore\n  ", "metadata": {"token_count": 150, "start_token": 720, "end_token": 870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_6"}}, {"id": "enhanced_chunk_7", "content": "  };\n\n  /**\n   * Deep-clone a node and preserve contents/properties.\n   *\n   * @private\n   * @ignore\n   */\n  var cloneNode = function(node, javascriptEnabled) {\n    // Recursively clone the node.\n    var clone =\n      node.nodeType === 3\n        ? document.createTextNode(node.nodeValue)\n        : node.cloneNode(false);\n    for (var child = node.firstChild; child; child = child.nextSibling) {\n      if (\n        javascriptEnabled === true ||\n        child.nodeType !== 1 ||\n        child.nodeName !== \"SCRIPT\"\n      ) {\n        clone.appendChild(cloneNode(child, javascriptEnabled));\n      }\n    }\n\n    if (node.nodeType === 1) {\n      // Preserve contents/properties", "metadata": {"token_count": 150, "start_token": 840, "end_token": 990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_7"}}, {"id": "enhanced_chunk_8", "content": "        clone.appendChild(cloneNode(child, javascriptEnabled));\n      }\n    }\n\n    if (node.nodeType === 1) {\n      // Preserve contents/properties of special nodes.\n      if (node.nodeName === \"CANVAS\") {\n        clone.width = node.width;\n        clone.height = node.height;\n        clone.getContext(\"2d\").drawImage(node, 0, 0);\n      } else if (node.nodeName === \"TEXTAREA\" || node.nodeName === \"SELECT\") {\n        clone.value = node.value;\n      }\n\n      // Preserve the node's scroll position when it loads.\n      clone.addEventListener(\n        \"load\",\n        function() {\n          clone.scrollTop = node.scrollTop;\n          clone.scrollLeft = node.scrollLeft;\n        },\n        true\n", "metadata": {"token_count": 150, "start_token": 960, "end_token": 1110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_8"}}, {"id": "enhanced_chunk_9", "content": "(\n        \"load\",\n        function() {\n          clone.scrollTop = node.scrollTop;\n          clone.scrollLeft = node.scrollLeft;\n        },\n        true\n      );\n    }\n\n    // Return the cloned node.\n    return clone;\n  };\n\n  /* ----- CONSTRUCTOR ----- */\n\n  var Worker = function Worker(opt) {\n    // Create the root parent for the proto chain, and the starting Worker.\n    var root = Object.assign(\n      Worker.convert(Promise.resolve()),\n      JSON.parse(JSON.stringify(Worker.template))\n    );\n    var self = Worker.convert(Promise.resolve(), root);\n\n    // Set progress, optional settings, and return.\n    self = self.setProgress(1, Worker, 1, [Worker]);\n    self = self.set(opt);\n", "metadata": {"token_count": 150, "start_token": 1080, "end_token": 1230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_9"}}, {"id": "enhanced_chunk_10", "content": ", optional settings, and return.\n    self = self.setProgress(1, Worker, 1, [Worker]);\n    self = self.set(opt);\n    return self;\n  };\n\n  // Boilerplate for subclassing Promise.\n  Worker.prototype = Object.create(Promise.prototype);\n  Worker.prototype.constructor = Worker;\n\n  // Converts/casts promises into Workers.\n  Worker.convert = function convert(promise, inherit) {\n    // Uses prototypal inheritance to receive changes made to ancestors' properties.\n    promise.__proto__ = inherit || Worker.prototype;\n    return promise;\n  };\n\n  Worker.template = {\n    prop: {\n      src: null,\n      container: null,\n      overlay: null,\n      canvas: null,\n      img: null,\n", "metadata": {"token_count": 150, "start_token": 1200, "end_token": 1350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_10"}}, {"id": "enhanced_chunk_11", "content": " {\n    prop: {\n      src: null,\n      container: null,\n      overlay: null,\n      canvas: null,\n      img: null,\n      pdf: null,\n      pageSize: null,\n      callback: function() {}\n    },\n    progress: {\n      val: 0,\n      state: null,\n      n: 0,\n      stack: []\n    },\n    opt: {\n      filename: \"file.pdf\",\n      margin: [0, 0, 0, 0],\n      enableLinks: true,\n      x: 0,\n      y: 0,\n      html2canvas: {},\n      jsPDF: {},\n      backgroundColor: \"transparent\"\n    }\n  };\n\n  /* ----- FROM / TO ----- */\n\n  Worker", "metadata": {"token_count": 150, "start_token": 1320, "end_token": 1470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_11"}}, {"id": "enhanced_chunk_12", "content": " html2canvas: {},\n      jsPDF: {},\n      backgroundColor: \"transparent\"\n    }\n  };\n\n  /* ----- FROM / TO ----- */\n\n  Worker.prototype.from = function from(src, type) {\n    function getType(src) {\n      switch (objType(src)) {\n        case \"string\":\n          return \"string\";\n        case \"element\":\n          return src.nodeName.toLowerCase() === \"canvas\" ? \"canvas\" : \"element\";\n        default:\n          return \"unknown\";\n      }\n    }\n\n    return this.then(function from_main() {\n      type = type || getType(src);\n      switch (type) {\n        case \"string\":\n          return this.then(loadDomPurify).then(function(dompurify) {\n            return this.set({\n", "metadata": {"token_count": 150, "start_token": 1440, "end_token": 1590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_12"}}, {"id": "enhanced_chunk_13", "content": ") {\n        case \"string\":\n          return this.then(loadDomPurify).then(function(dompurify) {\n            return this.set({\n              src: createElement(\"div\", {\n                innerHTML: src,\n                dompurify: dompurify\n              })\n            });\n          });\n        case \"element\":\n          return this.set({ src: src });\n        case \"canvas\":\n          return this.set({ canvas: src });\n        case \"img\":\n          return this.set({ img: src });\n        default:\n          return this.error(\"Unknown source type.\");\n      }\n    });\n  };\n\n  Worker.prototype.to = function to(target) {\n    // Route the 'to' request to the appropriate method.\n    switch (target) {\n      case", "metadata": {"token_count": 150, "start_token": 1560, "end_token": 1710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_13"}}, {"id": "enhanced_chunk_14", "content": " Worker.prototype.to = function to(target) {\n    // Route the 'to' request to the appropriate method.\n    switch (target) {\n      case \"container\":\n        return this.toContainer();\n      case \"canvas\":\n        return this.toCanvas();\n      case \"img\":\n        return this.toImg();\n      case \"pdf\":\n        return this.toPdf();\n      default:\n        return this.error(\"Invalid target.\");\n    }\n  };\n\n  Worker.prototype.toContainer = function toContainer() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkSrc() {\n        return (\n          this.prop.src || this.error(\"Cannot duplicate - no source HTML.\")\n        );\n      },\n      function checkPageSize() {\n        return this.prop.pageSize", "metadata": {"token_count": 150, "start_token": 1680, "end_token": 1830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_14"}}, {"id": "enhanced_chunk_15", "content": "          this.prop.src || this.error(\"Cannot duplicate - no source HTML.\")\n        );\n      },\n      function checkPageSize() {\n        return this.prop.pageSize || this.setPageSize();\n      }\n    ];\n    return this.thenList(prereqs).then(function toContainer_main() {\n      // Define the CSS styles for the container and its overlay parent.\n      var overlayCSS = {\n        position: \"fixed\",\n        overflow: \"hidden\",\n        zIndex: 1000,\n        left: \"-100000px\",\n        right: 0,\n        bottom: 0,\n        top: 0\n      };\n      var containerCSS = {\n        position: \"relative\",\n        display: \"inline-block\",\n        width:\n          Math.max(\n            this", "metadata": {"token_count": 150, "start_token": 1800, "end_token": 1950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_15"}}, {"id": "enhanced_chunk_16", "content": "      };\n      var containerCSS = {\n        position: \"relative\",\n        display: \"inline-block\",\n        width:\n          Math.max(\n            this.prop.src.clientWidth,\n            this.prop.src.scrollWidth,\n            this.prop.src.offsetWidth\n          ) + \"px\",\n        left: 0,\n        right: 0,\n        top: 0,\n        margin: \"auto\",\n        backgroundColor: this.opt.backgroundColor\n      }; // Set the overlay to hidden (could be changed in the future to provide a print preview).\n\n      var source = cloneNode(\n        this.prop.src,\n        this.opt.html2canvas.javascriptEnabled\n      );\n\n      if (source.tagName === \"BODY\") {\n        containerCSS.height =\n          Math.max(\n            document", "metadata": {"token_count": 150, "start_token": 1920, "end_token": 2070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_16"}}, {"id": "enhanced_chunk_17", "content": ".html2canvas.javascriptEnabled\n      );\n\n      if (source.tagName === \"BODY\") {\n        containerCSS.height =\n          Math.max(\n            document.body.scrollHeight,\n            document.body.offsetHeight,\n            document.documentElement.clientHeight,\n            document.documentElement.scrollHeight,\n            document.documentElement.offsetHeight\n          ) + \"px\";\n      }\n\n      this.prop.overlay = createElement(\"div\", {\n        className: \"html2pdf__overlay\",\n        style: overlayCSS\n      });\n      this.prop.container = createElement(\"div\", {\n        className: \"html2pdf__container\",\n        style: containerCSS\n      });\n      this.prop.container.appendChild(source);\n      this.prop.container.firstChild.appendChild(\n        createElement(\"div\", {\n          style: {\n            clear: \"both\",\n            border:", "metadata": {"token_count": 150, "start_token": 2040, "end_token": 2190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_17"}}, {"id": "enhanced_chunk_18", "content": ".container.appendChild(source);\n      this.prop.container.firstChild.appendChild(\n        createElement(\"div\", {\n          style: {\n            clear: \"both\",\n            border: \"0 none transparent\",\n            margin: 0,\n            padding: 0,\n            height: 0\n          }\n        })\n      );\n      this.prop.container.style.float = \"none\";\n      this.prop.overlay.appendChild(this.prop.container);\n      document.body.appendChild(this.prop.overlay);\n      this.prop.container.firstChild.style.position = \"relative\";\n      this.prop.container.height =\n        Math.max(\n          this.prop.container.firstChild.clientHeight,\n          this.prop.container.firstChild.scrollHeight,\n          this.prop.container.firstChild.offsetHeight\n        ) + \"px\";\n    });\n  };\n\n  Worker.prototype.toCanvas = function toCanvas() {\n   ", "metadata": {"token_count": 150, "start_token": 2160, "end_token": 2310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_18"}}, {"id": "enhanced_chunk_19", "content": ",\n          this.prop.container.firstChild.offsetHeight\n        ) + \"px\";\n    });\n  };\n\n  Worker.prototype.toCanvas = function toCanvas() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkContainer() {\n        return (\n          document.body.contains(this.prop.container) || this.toContainer()\n        );\n      }\n    ];\n\n    // Fulfill prereqs then create the canvas.\n    return this.thenList(prereqs)\n      .then(loadHtml2Canvas)\n      .then(function toCanvas_main(html2canvas) {\n        // Handle old-fashioned 'onrendered' argument.\n        var options = Object.assign({}, this.opt.html2canvas);\n        delete options.onrendered;\n\n        return html2canvas(this.prop.container", "metadata": {"token_count": 150, "start_token": 2280, "end_token": 2430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_19"}}, {"id": "enhanced_chunk_20", "content": " argument.\n        var options = Object.assign({}, this.opt.html2canvas);\n        delete options.onrendered;\n\n        return html2canvas(this.prop.container, options);\n      })\n      .then(function toCanvas_post(canvas) {\n        // Handle old-fashioned 'onrendered' argument.\n        var onRendered = this.opt.html2canvas.onrendered || function() {};\n        onRendered(canvas);\n\n        this.prop.canvas = canvas;\n        document.body.removeChild(this.prop.overlay);\n      });\n  };\n\n  Worker.prototype.toContext2d = function toContext2d() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkContainer() {\n        return (\n          document.body.contains(this.prop.container) || this.toContainer()\n", "metadata": {"token_count": 150, "start_token": 2400, "end_token": 2550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_20"}}, {"id": "enhanced_chunk_21", "content": " prerequisites.\n    var prereqs = [\n      function checkContainer() {\n        return (\n          document.body.contains(this.prop.container) || this.toContainer()\n        );\n      }\n    ];\n\n    // Fulfill prereqs then create the canvas.\n    return this.thenList(prereqs)\n      .then(loadHtml2Canvas)\n      .then(function toContext2d_main(html2canvas) {\n        // Handle old-fashioned 'onrendered' argument.\n\n        var pdf = this.opt.jsPDF;\n        var fontFaces = this.opt.fontFaces;\n        var options = Object.assign(\n          {\n            async: true,\n            allowTaint: true,\n            scale: 1,\n            scrollX: this.opt.scrollX || 0,\n            scroll", "metadata": {"token_count": 150, "start_token": 2520, "end_token": 2670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_21"}}, {"id": "enhanced_chunk_22", "content": ": true,\n            allowTaint: true,\n            scale: 1,\n            scrollX: this.opt.scrollX || 0,\n            scrollY: this.opt.scrollY || 0,\n            backgroundColor: \"#ffffff\",\n            imageTimeout: 15000,\n            logging: true,\n            proxy: null,\n            removeContainer: true,\n            foreignObjectRendering: false,\n            useCORS: false\n          },\n          this.opt.html2canvas\n        );\n        delete options.onrendered;\n\n        pdf.context2d.autoPaging = true;\n        pdf.context2d.posX = this.opt.x;\n        pdf.context2d.posY = this.opt.y;\n        pdf.context2d.fontFaces = fontFaces;\n\n        if", "metadata": {"token_count": 150, "start_token": 2640, "end_token": 2790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_22"}}, {"id": "enhanced_chunk_23", "content": " = this.opt.x;\n        pdf.context2d.posY = this.opt.y;\n        pdf.context2d.fontFaces = fontFaces;\n\n        if (fontFaces) {\n          for (var i = 0; i < fontFaces.length; ++i) {\n            var font = fontFaces[i];\n            var src = font.src.find(function(src) {\n              return src.format === \"truetype\";\n            });\n\n            if (src) {\n              pdf.addFont(src.url, font.ref.name, font.ref.style);\n            }\n          }\n        }\n\n        options.windowHeight = options.windowHeight || 0;\n        options.windowHeight =\n          options.windowHeight == 0\n            ? Math.max(\n                this.prop.container.clientHeight,\n                this.prop", "metadata": {"token_count": 150, "start_token": 2760, "end_token": 2910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_23"}}, {"id": "enhanced_chunk_24", "content": " 0;\n        options.windowHeight =\n          options.windowHeight == 0\n            ? Math.max(\n                this.prop.container.clientHeight,\n                this.prop.container.scrollHeight,\n                this.prop.container.offsetHeight\n              )\n            : options.windowHeight;\n\n        return html2canvas(this.prop.container, options);\n      })\n      .then(function toContext2d_post(canvas) {\n        // Handle old-fashioned 'onrendered' argument.\n        var onRendered = this.opt.html2canvas.onrendered || function() {};\n        onRendered(canvas);\n\n        this.prop.canvas = canvas;\n        document.body.removeChild(this.prop.overlay);\n      });\n  };\n\n  Worker.prototype.toImg = function toImg() {\n    // Set up function prerequisites.\n    var prereqs =", "metadata": {"token_count": 150, "start_token": 2880, "end_token": 3030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_24"}}, {"id": "enhanced_chunk_25", "content": ".prop.overlay);\n      });\n  };\n\n  Worker.prototype.toImg = function toImg() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkCanvas() {\n        return this.prop.canvas || this.toCanvas();\n      }\n    ];\n\n    // Fulfill prereqs then create the image.\n    return this.thenList(prereqs).then(function toImg_main() {\n      var imgData = this.prop.canvas.toDataURL(\n        \"image/\" + this.opt.image.type,\n        this.opt.image.quality\n      );\n      this.prop.img = document.createElement(\"img\");\n      this.prop.img.src = imgData;\n    });\n  };\n\n  Worker.prototype.toPdf = function toPdf() {\n    // Set up function prerequisites.\n    var", "metadata": {"token_count": 150, "start_token": 3000, "end_token": 3150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_25"}}, {"id": "enhanced_chunk_26", "content": ".img.src = imgData;\n    });\n  };\n\n  Worker.prototype.toPdf = function toPdf() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkContext2d() {\n        return this.toContext2d();\n      }\n      //function checkCanvas() { return this.prop.canvas || this.toCanvas(); }\n    ];\n\n    // Fulfill prereqs then create the image.\n    return this.thenList(prereqs).then(function toPdf_main() {\n      // Create local copies of frequently used properties.\n      this.prop.pdf = this.prop.pdf || this.opt.jsPDF;\n    });\n  };\n\n  /* ----- OUTPUT / SAVE ----- */\n\n  Worker.prototype.output = function output(type, options, src) {\n    //", "metadata": {"token_count": 150, "start_token": 3120, "end_token": 3270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_26"}}, {"id": "enhanced_chunk_27", "content": "PDF;\n    });\n  };\n\n  /* ----- OUTPUT / SAVE ----- */\n\n  Worker.prototype.output = function output(type, options, src) {\n    // Redirect requests to the correct function (outputPdf / outputImg).\n    src = src || \"pdf\";\n    if (src.toLowerCase() === \"img\" || src.toLowerCase() === \"image\") {\n      return this.outputImg(type, options);\n    } else {\n      return this.outputPdf(type, options);\n    }\n  };\n\n  Worker.prototype.outputPdf = function outputPdf(type, options) {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkPdf() {\n        return this.prop.pdf || this.toPdf();\n      }\n    ];\n\n    // Fulfill prereqs then", "metadata": {"token_count": 150, "start_token": 3240, "end_token": 3390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_27"}}, {"id": "enhanced_chunk_28", "content": "qs = [\n      function checkPdf() {\n        return this.prop.pdf || this.toPdf();\n      }\n    ];\n\n    // Fulfill prereqs then perform the appropriate output.\n    return this.thenList(prereqs).then(function outputPdf_main() {\n      /* Currently implemented output types:\n       *    https://rawgit.com/MrRio/jsPDF/master/docs/jspdf.js.html#line992\n       *  save(options), arraybuffer, blob, bloburi/bloburl,\n       *  datauristring/dataurlstring, dataurlnewwindow, datauri/dataurl\n       */\n      return this.prop.pdf.output(type, options);\n    });\n  };\n\n  Worker.prototype.outputImg = function outputImg(type) {\n    // Set up function", "metadata": {"token_count": 150, "start_token": 3360, "end_token": 3510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_28"}}, {"id": "enhanced_chunk_29", "content": " return this.prop.pdf.output(type, options);\n    });\n  };\n\n  Worker.prototype.outputImg = function outputImg(type) {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkImg() {\n        return this.prop.img || this.toImg();\n      }\n    ];\n\n    // Fulfill prereqs then perform the appropriate output.\n    return this.thenList(prereqs).then(function outputImg_main() {\n      switch (type) {\n        case undefined:\n        case \"img\":\n          return this.prop.img;\n        case \"datauristring\":\n        case \"dataurlstring\":\n          return this.prop.img.src;\n        case \"datauri\":\n        case \"dataurl\":\n          return (document.location.href = this.prop.img", "metadata": {"token_count": 150, "start_token": 3480, "end_token": 3630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_29"}}, {"id": "enhanced_chunk_30", "content": "\":\n          return this.prop.img.src;\n        case \"datauri\":\n        case \"dataurl\":\n          return (document.location.href = this.prop.img.src);\n        default:\n          throw 'Image output type \"' + type + '\" is not supported.';\n      }\n    });\n  };\n\n  Worker.prototype.save = function save(filename) {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkPdf() {\n        return this.prop.pdf || this.toPdf();\n      }\n    ];\n\n    // Fulfill prereqs, update the filename (if provided), and save the PDF.\n    return this.thenList(prereqs)\n      .set(filename ? { filename: filename } : null)\n      .then(function save_main() {\n        this", "metadata": {"token_count": 150, "start_token": 3600, "end_token": 3750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_30"}}, {"id": "enhanced_chunk_31", "content": " this.thenList(prereqs)\n      .set(filename ? { filename: filename } : null)\n      .then(function save_main() {\n        this.prop.pdf.save(this.opt.filename);\n      });\n  };\n\n  Worker.prototype.doCallback = function doCallback() {\n    // Set up function prerequisites.\n    var prereqs = [\n      function checkPdf() {\n        return this.prop.pdf || this.toPdf();\n      }\n    ];\n\n    // Fulfill prereqs, update the filename (if provided), and save the PDF.\n    return this.thenList(prereqs).then(function doCallback_main() {\n      this.prop.callback(this.prop.pdf);\n    });\n  };\n\n  /* ----- SET / GET ----- */\n\n  Worker.prototype.set = function set(opt) {\n", "metadata": {"token_count": 150, "start_token": 3720, "end_token": 3870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_31"}}, {"id": "enhanced_chunk_32", "content": "      this.prop.callback(this.prop.pdf);\n    });\n  };\n\n  /* ----- SET / GET ----- */\n\n  Worker.prototype.set = function set(opt) {\n    // TODO: Implement ordered pairs?\n\n    // Silently ignore invalid or empty input.\n    if (objType(opt) !== \"object\") {\n      return this;\n    }\n\n    // Build an array of setter functions to queue.\n    var fns = Object.keys(opt || {}).map(function(key) {\n      if (key in Worker.template.prop) {\n        // Set pre-defined properties.\n        return function set_prop() {\n          this.prop[key] = opt[key];\n        };\n      } else {\n        switch (key) {\n          case \"margin\":\n            return this.setMargin.bind(this,", "metadata": {"token_count": 150, "start_token": 3840, "end_token": 3990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_32"}}, {"id": "enhanced_chunk_33", "content": "] = opt[key];\n        };\n      } else {\n        switch (key) {\n          case \"margin\":\n            return this.setMargin.bind(this, opt.margin);\n          case \"jsPDF\":\n            return function set_jsPDF() {\n              this.opt.jsPDF = opt.jsPDF;\n              return this.setPageSize();\n            };\n          case \"pageSize\":\n            return this.setPageSize.bind(this, opt.pageSize);\n          default:\n            // Set any other properties in opt.\n            return function set_opt() {\n              this.opt[key] = opt[key];\n            };\n        }\n      }\n    }, this);\n\n    // Set properties within the promise chain.\n    return this.then(function set_main() {\n      return this.thenList(fns);\n    });\n  };\n\n ", "metadata": {"token_count": 150, "start_token": 3960, "end_token": 4110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_33"}}, {"id": "enhanced_chunk_34", "content": " // Set properties within the promise chain.\n    return this.then(function set_main() {\n      return this.thenList(fns);\n    });\n  };\n\n  Worker.prototype.get = function get(key, cbk) {\n    return this.then(function get_main() {\n      // Fetch the requested property, either as a predefined prop or in opt.\n      var val = key in Worker.template.prop ? this.prop[key] : this.opt[key];\n      return cbk ? cbk(val) : val;\n    });\n  };\n\n  Worker.prototype.setMargin = function setMargin(margin) {\n    return this.then(function setMargin_main() {\n      // Parse the margin property.\n      switch (objType(margin)) {\n        case \"number\":\n          margin = [", "metadata": {"token_count": 150, "start_token": 4080, "end_token": 4230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_34"}}, {"id": "enhanced_chunk_35", "content": " setMargin_main() {\n      // Parse the margin property.\n      switch (objType(margin)) {\n        case \"number\":\n          margin = [margin, margin, margin, margin];\n        // eslint-disable-next-line no-fallthrough\n        case \"array\":\n          if (margin.length === 2) {\n            margin = [margin[0], margin[1], margin[0], margin[1]];\n          }\n          if (margin.length === 4) {\n            break;\n          }\n        // eslint-disable-next-line no-fallthrough\n        default:\n          return this.error(\"Invalid margin array.\");\n      }\n\n      // Set the margin property, then update pageSize.\n      this.opt.margin = margin;\n    }).then(this.setPageSize", "metadata": {"token_count": 150, "start_token": 4200, "end_token": 4350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_35"}}, {"id": "enhanced_chunk_36", "content": "Invalid margin array.\");\n      }\n\n      // Set the margin property, then update pageSize.\n      this.opt.margin = margin;\n    }).then(this.setPageSize);\n  };\n\n  Worker.prototype.setPageSize = function setPageSize(pageSize) {\n    function toPx(val, k) {\n      return Math.floor(((val * k) / 72) * 96);\n    }\n\n    return this.then(function setPageSize_main() {\n      // Retrieve page-size based on jsPDF settings, if not explicitly provided.\n      pageSize = pageSize || jsPDF.getPageSize(this.opt.jsPDF);\n\n      // Add 'inner' field if not present.\n      if (!pageSize.hasOwnProperty(\"inner\")) {\n        pageSize.inner = {\n          width: pageSize.width - this.opt.margin[1", "metadata": {"token_count": 150, "start_token": 4320, "end_token": 4470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_36"}}, {"id": "enhanced_chunk_37", "content": " field if not present.\n      if (!pageSize.hasOwnProperty(\"inner\")) {\n        pageSize.inner = {\n          width: pageSize.width - this.opt.margin[1] - this.opt.margin[3],\n          height: pageSize.height - this.opt.margin[0] - this.opt.margin[2]\n        };\n        pageSize.inner.px = {\n          width: toPx(pageSize.inner.width, pageSize.k),\n          height: toPx(pageSize.inner.height, pageSize.k)\n        };\n        pageSize.inner.ratio = pageSize.inner.height / pageSize.inner.width;\n      }\n\n      // Attach pageSize to this.\n      this.prop.pageSize = pageSize;\n    });\n  };\n\n  Worker.prototype.setProgress = function setProgress(val, state, n, stack) {\n    // Immediately update all progress", "metadata": {"token_count": 150, "start_token": 4440, "end_token": 4590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_37"}}, {"id": "enhanced_chunk_38", "content": " = pageSize;\n    });\n  };\n\n  Worker.prototype.setProgress = function setProgress(val, state, n, stack) {\n    // Immediately update all progress values.\n    if (val != null) this.progress.val = val;\n    if (state != null) this.progress.state = state;\n    if (n != null) this.progress.n = n;\n    if (stack != null) this.progress.stack = stack;\n    this.progress.ratio = this.progress.val / this.progress.state;\n\n    // Return this for command chaining.\n    return this;\n  };\n\n  Worker.prototype.updateProgress = function updateProgress(\n    val,\n    state,\n    n,\n    stack\n  ) {\n    // Immediately update all progress values, using setProgress.\n   ", "metadata": {"token_count": 150, "start_token": 4560, "end_token": 4710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_38"}}, {"id": "enhanced_chunk_39", "content": "Progress(\n    val,\n    state,\n    n,\n    stack\n  ) {\n    // Immediately update all progress values, using setProgress.\n    return this.setProgress(\n      val ? this.progress.val + val : null,\n      state ? state : null,\n      n ? this.progress.n + n : null,\n      stack ? this.progress.stack.concat(stack) : null\n    );\n  };\n\n  /* ----- PROMISE MAPPING ----- */\n\n  Worker.prototype.then = function then(onFulfilled, onRejected) {\n    // Wrap `this` for encapsulation.\n    var self = this;\n\n    return this.thenCore(onFulfilled, onRejected, function then_main(\n      onFulfilled,\n      onRejected\n    ) {\n      // Update", "metadata": {"token_count": 150, "start_token": 4680, "end_token": 4830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_39"}}, {"id": "enhanced_chunk_40", "content": " return this.thenCore(onFulfilled, onRejected, function then_main(\n      onFulfilled,\n      onRejected\n    ) {\n      // Update progress while queuing, calling, and resolving `then`.\n      self.updateProgress(null, null, 1, [onFulfilled]);\n      return Promise.prototype.then\n        .call(this, function then_pre(val) {\n          self.updateProgress(null, onFulfilled);\n          return val;\n        })\n        .then(onFulfilled, onRejected)\n        .then(function then_post(val) {\n          self.updateProgress(1);\n          return val;\n        });\n    });\n  };\n\n  Worker.prototype.thenCore = function thenCore(\n    onFulfilled,\n    onRejected,\n    thenBase\n", "metadata": {"token_count": 150, "start_token": 4800, "end_token": 4950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_40"}}, {"id": "enhanced_chunk_41", "content": ";\n        });\n    });\n  };\n\n  Worker.prototype.thenCore = function thenCore(\n    onFulfilled,\n    onRejected,\n    thenBase\n  ) {\n    // Handle optional thenBase parameter.\n    thenBase = thenBase || Promise.prototype.then;\n\n    // Wrap `this` for encapsulation and bind it to the promise handlers.\n    var self = this;\n    if (onFulfilled) {\n      onFulfilled = onFulfilled.bind(self);\n    }\n    if (onRejected) {\n      onRejected = onRejected.bind(self);\n    }\n\n    // Cast self into a Promise to avoid polyfills recursively defining `then`.\n    var isNative =\n      Promise.toString().indexOf(\"[native code]\") !== -1 &&\n      Promise", "metadata": {"token_count": 150, "start_token": 4920, "end_token": 5070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_41"}}, {"id": "enhanced_chunk_42", "content": " Promise to avoid polyfills recursively defining `then`.\n    var isNative =\n      Promise.toString().indexOf(\"[native code]\") !== -1 &&\n      Promise.name === \"Promise\";\n    var selfPromise = isNative\n      ? self\n      : Worker.convert(Object.assign({}, self), Promise.prototype);\n\n    // Return the promise, after casting it into a Worker and preserving props.\n    var returnVal = thenBase.call(selfPromise, onFulfilled, onRejected);\n    return Worker.convert(returnVal, self.__proto__);\n  };\n\n  Worker.prototype.thenExternal = function thenExternal(\n    onFulfilled,\n    onRejected\n  ) {\n    // Call `then` and return a standard promise (exits the Worker chain).\n    return Promise.prototype", "metadata": {"token_count": 150, "start_token": 5040, "end_token": 5190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_42"}}, {"id": "enhanced_chunk_43", "content": ",\n    onRejected\n  ) {\n    // Call `then` and return a standard promise (exits the Worker chain).\n    return Promise.prototype.then.call(this, onFulfilled, onRejected);\n  };\n\n  Worker.prototype.thenList = function thenList(fns) {\n    // Queue a series of promise 'factories' into the promise chain.\n    var self = this;\n    fns.forEach(function thenList_forEach(fn) {\n      self = self.thenCore(fn);\n    });\n    return self;\n  };\n\n  Worker.prototype[\"catch\"] = function(onRejected) {\n    // Bind `this` to the promise handler, call `catch`, and return a Worker.\n    if (onRejected) {\n      onRejected = on", "metadata": {"token_count": 150, "start_token": 5160, "end_token": 5310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_43"}}, {"id": "enhanced_chunk_44", "content": " Bind `this` to the promise handler, call `catch`, and return a Worker.\n    if (onRejected) {\n      onRejected = onRejected.bind(this);\n    }\n    var returnVal = Promise.prototype[\"catch\"].call(this, onRejected);\n    return Worker.convert(returnVal, this);\n  };\n\n  Worker.prototype.catchExternal = function catchExternal(onRejected) {\n    // Call `catch` and return a standard promise (exits the Worker chain).\n    return Promise.prototype[\"catch\"].call(this, onRejected);\n  };\n\n  Worker.prototype.error = function error(msg) {\n    // Throw the error in the Promise chain.\n    return this.then(function error_main() {\n      throw new Error(msg);\n    });\n  };\n\n  /*", "metadata": {"token_count": 150, "start_token": 5280, "end_token": 5430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_44"}}, {"id": "enhanced_chunk_45", "content": " // Throw the error in the Promise chain.\n    return this.then(function error_main() {\n      throw new Error(msg);\n    });\n  };\n\n  /* ----- ALIASES ----- */\n\n  Worker.prototype.using = Worker.prototype.set;\n  Worker.prototype.saveAs = Worker.prototype.save;\n  Worker.prototype.export = Worker.prototype.output;\n  Worker.prototype.run = Worker.prototype.then;\n\n  // Get dimensions of a PDF page, as determined by jsPDF.\n  jsPDF.getPageSize = function(orientation, unit, format) {\n    // Decode options object\n    if (typeof orientation === \"object\") {\n      var options = orientation;\n      orientation = options.orientation;\n      unit = options.unit || unit;\n      format = options.format || format;\n   ", "metadata": {"token_count": 150, "start_token": 5400, "end_token": 5550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_45"}}, {"id": "enhanced_chunk_46", "content": " {\n      var options = orientation;\n      orientation = options.orientation;\n      unit = options.unit || unit;\n      format = options.format || format;\n    }\n\n    // Default options\n    unit = unit || \"mm\";\n    format = format || \"a4\";\n    orientation = (\"\" + (orientation || \"P\")).toLowerCase();\n    var format_as_string = (\"\" + format).toLowerCase();\n\n    // Size in pt of various paper formats\n    var pageFormats = {\n      a0: [2383.94, 3370.39],\n      a1: [1683.78, 2383.94],\n      a2: [1190.55, 1683.78],\n      a3: [841", "metadata": {"token_count": 150, "start_token": 5520, "end_token": 5670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_46"}}, {"id": "enhanced_chunk_47", "content": "78, 2383.94],\n      a2: [1190.55, 1683.78],\n      a3: [841.89, 1190.55],\n      a4: [595.28, 841.89],\n      a5: [419.53, 595.28],\n      a6: [297.64, 419.53],\n      a7: [209.76, 297.64],\n      a8: [147.4, 209.76],\n      a9: [104.88, 147.4],\n      a10: [73.7, 104.88],\n      b0: [2834.65, 4008", "metadata": {"token_count": 150, "start_token": 5640, "end_token": 5790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_47"}}, {"id": "enhanced_chunk_48", "content": ".4],\n      a10: [73.7, 104.88],\n      b0: [2834.65, 4008.19],\n      b1: [2004.09, 2834.65],\n      b2: [1417.32, 2004.09],\n      b3: [1000.63, 1417.32],\n      b4: [708.66, 1000.63],\n      b5: [498.9, 708.66],\n      b6: [354.33, 498.9],\n      b7: [249.45, 354.33],\n      b8: [175.75, 249.", "metadata": {"token_count": 150, "start_token": 5760, "end_token": 5910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_48"}}, {"id": "enhanced_chunk_49", "content": "498.9],\n      b7: [249.45, 354.33],\n      b8: [175.75, 249.45],\n      b9: [124.72, 175.75],\n      b10: [87.87, 124.72],\n      c0: [2599.37, 3676.54],\n      c1: [1836.85, 2599.37],\n      c2: [1298.27, 1836.85],\n      c3: [918.43, 1298.27],\n      c4: [649.13, 918.43],\n      c5: [459.21, 649.13", "metadata": {"token_count": 150, "start_token": 5880, "end_token": 6030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_49"}}, {"id": "enhanced_chunk_50", "content": ".27],\n      c4: [649.13, 918.43],\n      c5: [459.21, 649.13],\n      c6: [323.15, 459.21],\n      c7: [229.61, 323.15],\n      c8: [161.57, 229.61],\n      c9: [113.39, 161.57],\n      c10: [79.37, 113.39],\n      dl: [311.81, 623.62],\n      letter: [612, 792],\n      \"government-letter\": [576, 756],\n      legal: [612, 1008],\n      \"junior-legal", "metadata": {"token_count": 150, "start_token": 6000, "end_token": 6150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_50"}}, {"id": "enhanced_chunk_51", "content": " 792],\n      \"government-letter\": [576, 756],\n      legal: [612, 1008],\n      \"junior-legal\": [576, 360],\n      ledger: [1224, 792],\n      tabloid: [792, 1224],\n      \"credit-card\": [153, 243]\n    };\n\n    var k;\n    // Unit conversion\n    switch (unit) {\n      case \"pt\":\n        k = 1;\n        break;\n      case \"mm\":\n        k = 72 / 25.4;\n        break;\n      case \"cm\":\n        k = 72 / 2.54;\n        break;\n      case \"in\":\n        k = 72;\n       ", "metadata": {"token_count": 150, "start_token": 6120, "end_token": 6270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_51"}}, {"id": "enhanced_chunk_52", "content": " case \"cm\":\n        k = 72 / 2.54;\n        break;\n      case \"in\":\n        k = 72;\n        break;\n      case \"px\":\n        k = 72 / 96;\n        break;\n      case \"pc\":\n        k = 12;\n        break;\n      case \"em\":\n        k = 12;\n        break;\n      case \"ex\":\n        k = 6;\n        break;\n      default:\n        throw \"Invalid unit: \" + unit;\n    }\n    var pageHeight = 0;\n    var pageWidth = 0;\n\n    // Dimensions are stored as user units and converted to points on output\n    if (pageFormats.hasOwnProperty(format_as_string)) {\n      page", "metadata": {"token_count": 150, "start_token": 6240, "end_token": 6390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_52"}}, {"id": "enhanced_chunk_53", "content": "0;\n\n    // Dimensions are stored as user units and converted to points on output\n    if (pageFormats.hasOwnProperty(format_as_string)) {\n      pageHeight = pageFormats[format_as_string][1] / k;\n      pageWidth = pageFormats[format_as_string][0] / k;\n    } else {\n      try {\n        pageHeight = format[1];\n        pageWidth = format[0];\n      } catch (err) {\n        throw new Error(\"Invalid format: \" + format);\n      }\n    }\n\n    var tmp;\n    // Handle page orientation\n    if (orientation === \"p\" || orientation === \"portrait\") {\n      orientation = \"p\";\n      if (pageWidth > pageHeight) {\n        tmp =", "metadata": {"token_count": 150, "start_token": 6360, "end_token": 6510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_53"}}, {"id": "enhanced_chunk_54", "content": " === \"p\" || orientation === \"portrait\") {\n      orientation = \"p\";\n      if (pageWidth > pageHeight) {\n        tmp = pageWidth;\n        pageWidth = pageHeight;\n        pageHeight = tmp;\n      }\n    } else if (orientation === \"l\" || orientation === \"landscape\") {\n      orientation = \"l\";\n      if (pageHeight > pageWidth) {\n        tmp = pageWidth;\n        pageWidth = pageHeight;\n        pageHeight = tmp;\n      }\n    } else {\n      throw \"Invalid orientation: \" + orientation;\n    }\n\n    // Return information (k is the unit conversion ratio from pts)\n    var info = {\n      width: pageWidth,\n      height: pageHeight,\n     ", "metadata": {"token_count": 150, "start_token": 6480, "end_token": 6630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_54"}}, {"id": "enhanced_chunk_55", "content": " Return information (k is the unit conversion ratio from pts)\n    var info = {\n      width: pageWidth,\n      height: pageHeight,\n      unit: unit,\n      k: k,\n      orientation: orientation\n    };\n    return info;\n  };\n\n  /**\n   * @typedef FontFace\n   *\n   * The font-face type implements an interface similar to that of the font-face CSS rule,\n   * and is used by jsPDF to match fonts when the font property of CanvasRenderingContext2D\n   * is updated.\n   *\n   * All properties expect values similar to those in the font-face CSS rule. A difference\n   * is the font-family, which do not need to be enclosed in double-quotes when containing\n", "metadata": {"token_count": 150, "start_token": 6600, "end_token": 6750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_55"}}, {"id": "enhanced_chunk_56", "content": " the font-face CSS rule. A difference\n   * is the font-family, which do not need to be enclosed in double-quotes when containing\n   * spaces like in CSS.\n   *\n   * @property {string} family The name of the font-family.\n   * @property {string|undefined} style The style that this font-face defines, e.g. 'italic'.\n   * @property {string|number|undefined} weight The weight of the font, either as a string or a number (400, 500, 600, e.g.)\n   * @property {string|undefined} stretch The stretch of the font, e.g. condensed, normal, expanded.\n   * @property {Object[]} src A list", "metadata": {"token_count": 150, "start_token": 6720, "end_token": 6870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_56"}}, {"id": "enhanced_chunk_57", "content": "string|undefined} stretch The stretch of the font, e.g. condensed, normal, expanded.\n   * @property {Object[]} src A list of URLs from where fonts of various formats can be fetched.\n   * @property {string} [src] url A URL to a font of a specific format.\n   * @property {string} [src] format Format of the font referenced by the URL.\n   */\n\n  /**\n   * Generate a PDF from an HTML element or string using.\n   *\n   * @name html\n   * @function\n   * @param {HTMLElement|string} source The source HTMLElement or a string containing HTML.\n   * @param {Object} [options] Collection of settings\n   * @param {", "metadata": {"token_count": 150, "start_token": 6840, "end_token": 6990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_57"}}, {"id": "enhanced_chunk_58", "content": "} source The source HTMLElement or a string containing HTML.\n   * @param {Object} [options] Collection of settings\n   * @param {function} [options.callback] The mandatory callback-function gets as first parameter the current jsPDF instance\n   * @param {number|array} [options.margin] Array of margins [left, bottom, right, top]\n   * @param {string} [options.filename] name of the file\n   * @param {HTMLOptionImage} [options.image] image settings when converting HTML to image\n   * @param {Html2CanvasOptions} [options.html2canvas] html2canvas options\n   * @param {FontFace[]} [options.fontFaces] A list of", "metadata": {"token_count": 150, "start_token": 6960, "end_token": 7110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_58"}}, {"id": "enhanced_chunk_59", "content": "CanvasOptions} [options.html2canvas] html2canvas options\n   * @param {FontFace[]} [options.fontFaces] A list of font-faces to match when resolving fonts. Fonts will be added to the PDF based on the specified URL. If omitted, the font match algorithm falls back to old algorithm.\n   * @param {jsPDF} [options.jsPDF] jsPDF instance\n   * @param {number} [options.x] x position on the PDF document\n   * @param {number} [options.y] y position on the PDF document\n   *\n   * @example\n   * var doc = new jsPDF();\n   *\n   * doc.html(document.body, {\n   *    callback: function", "metadata": {"token_count": 150, "start_token": 7080, "end_token": 7230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_59"}}, {"id": "enhanced_chunk_60", "content": "   * @example\n   * var doc = new jsPDF();\n   *\n   * doc.html(document.body, {\n   *    callback: function (doc) {\n   *      doc.save();\n   *    },\n   *    x: 10,\n   *    y: 10\n   * });\n   */\n  jsPDFAPI.html = function(src, options) {\n    \"use strict\";\n\n    options = options || {};\n    options.callback = options.callback || function() {};\n    options.html2canvas = options.html2canvas || {};\n    options.html2canvas.canvas = options.html2canvas.canvas || this.canvas;\n    options.jsPDF = options.jsPDF || this;\n    options.fontFaces = options.fontFaces\n      ? options", "metadata": {"token_count": 150, "start_token": 7200, "end_token": 7350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_60"}}, {"id": "enhanced_chunk_61", "content": "2canvas.canvas || this.canvas;\n    options.jsPDF = options.jsPDF || this;\n    options.fontFaces = options.fontFaces\n      ? options.fontFaces.map(normalizeFontFace)\n      : null;\n\n    // Create a new worker with the given options.\n    var worker = new Worker(options);\n\n    if (!options.worker) {\n      // If worker is not set to true, perform the traditional 'simple' operation.\n      return worker.from(src).doCallback();\n    } else {\n      // Otherwise, return the worker for new Promise-based operation.\n      return worker;\n    }\n  };\n})(jsPDF.API);\n\n\n// === bidiEngine.js ===\n\n/**\n * @license\n * Unicode Bidi Engine based on the work of <PERSON>", "metadata": {"token_count": 150, "start_token": 7320, "end_token": 7470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_61"}}, {"id": "enhanced_chunk_62", "content": "})(jsPDF.API);\n\n\n// === bidiEngine.js ===\n\n/**\n * @license\n * Unicode Bidi Engine based on the work of <PERSON> (@asthensis)\n * MIT License\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n(function(jsPDF) {\n  \"use strict\";\n  /**\n   * Table of Unicode types.\n   *\n   * Generated by:\n   *\n   * var bidi = require(\"./bidi/index\");\n   * var bidi_accumulate = bidi.slice(0, 256).concat(bidi.slice(0x0500, 0x0500 + 256 * 3)).\n   * concat(bidi.slice(0x2000, 0x2000 + 256)).concat(b", "metadata": {"token_count": 150, "start_token": 7440, "end_token": 7590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_62"}}, {"id": "enhanced_chunk_63", "content": " + 256 * 3)).\n   * concat(bidi.slice(0x2000, 0x2000 + 256)).concat(bidi.slice(0xFB00, 0xFB00 + 256)).\n   * concat(bidi.slice(0xFE00, 0xFE00 + 2 * 256));\n   *\n   * for( var i = 0; i < bidi_accumulate.length; i++) {\n   * \tif(bidi_accumulate[i] === undefined || bidi_accumulate[i] === 'ON')\n   * \t\tbidi_accumulate[i] = 'N'; //mark as neutral to conserve space and substitute undefined\n   * }\n   * var bidiAccumulateStr = 'return [", "metadata": {"token_count": 150, "start_token": 7560, "end_token": 7710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_63"}}, {"id": "enhanced_chunk_64", "content": " = 'N'; //mark as neutral to conserve space and substitute undefined\n   * }\n   * var bidiAccumulateStr = 'return [ \"' + bidi_accumulate.toString().replace(/,/g, '\", \"') + '\" ];';\n   * require(\"fs\").writeFile('unicode-types.js', bidiAccumulateStr);\n   *\n   * Based on:\n   * https://github.com/mathiasbynens/unicode-8.0.0\n   */\n  var bidiUnicodeTypes = [\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"S\",\n    \"B\",\n    \"S\",\n", "metadata": {"token_count": 150, "start_token": 7680, "end_token": 7830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_64"}}, {"id": "enhanced_chunk_65", "content": "BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"S\",\n    \"B\",\n    \"S\",\n    \"WS\",\n    \"B\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"B\",\n    \"B\",\n    \"B\",\n    \"S\",\n    \"WS\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n", "metadata": {"token_count": 150, "start_token": 7800, "end_token": 7950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_65"}}, {"id": "enhanced_chunk_66", "content": "N\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ES\",\n    \"CS\",\n    \"ES\",\n    \"CS\",\n    \"CS\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"CS\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 7920, "end_token": 8070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_66"}}, {"id": "enhanced_chunk_67", "content": "N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 8040, "end_token": 8190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_67"}}, {"id": "enhanced_chunk_68", "content": "N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n", "metadata": {"token_count": 150, "start_token": 8160, "end_token": 8310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_68"}}, {"id": "enhanced_chunk_69", "content": "N\",\n    \"N\",\n    \"N\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"B\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"CS\",\n    \"N\",\n", "metadata": {"token_count": 150, "start_token": 8280, "end_token": 8430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_69"}}, {"id": "enhanced_chunk_70", "content": "BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"CS\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"BN\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"EN\",\n    \"EN\",\n    \"N\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"EN\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n", "metadata": {"token_count": 150, "start_token": 8400, "end_token": 8550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_70"}}, {"id": "enhanced_chunk_71", "content": "N\",\n    \"EN\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 8520, "end_token": 8670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_71"}}, {"id": "enhanced_chunk_72", "content": "L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 8640, "end_token": 8790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_72"}}, {"id": "enhanced_chunk_73", "content": "L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 8760, "end_token": 8910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_73"}}, {"id": "enhanced_chunk_74", "content": "L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 8880, "end_token": 9030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_74"}}, {"id": "enhanced_chunk_75", "content": "N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 9000, "end_token": 9150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_75"}}, {"id": "enhanced_chunk_76", "content": "L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 9120, "end_token": 9270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_76"}}, {"id": "enhanced_chunk_77", "content": "L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"N\",\n    \"NSM", "metadata": {"token_count": 150, "start_token": 9240, "end_token": 9390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_77"}}, {"id": "enhanced_chunk_78", "content": "N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM", "metadata": {"token_count": 150, "start_token": 9360, "end_token": 9510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_78"}}, {"id": "enhanced_chunk_79", "content": "\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n   ", "metadata": {"token_count": 150, "start_token": 9480, "end_token": 9630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_79"}}, {"id": "enhanced_chunk_80", "content": "\",\n    \"NSM\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n    \"NSM\",\n    \"R\",\n    \"NSM\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n   ", "metadata": {"token_count": 150, "start_token": 9600, "end_token": 9750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_80"}}, {"id": "enhanced_chunk_81", "content": "\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 9720, "end_token": 9870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_81"}}, {"id": "enhanced_chunk_82", "content": "\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"ET\",\n    \"ET\",\n    \"AL\",\n    \"CS\",\n    \"AL\",\n    \"N\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n", "metadata": {"token_count": 150, "start_token": 9840, "end_token": 9990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_82"}}, {"id": "enhanced_chunk_83", "content": "    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AL\",\n    \"AL\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"", "metadata": {"token_count": 150, "start_token": 9960, "end_token": 10110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_83"}}, {"id": "enhanced_chunk_84", "content": "    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NS", "metadata": {"token_count": 150, "start_token": 10080, "end_token": 10230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_84"}}, {"id": "enhanced_chunk_85", "content": "M\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"ET\",\n    \"AN\",\n    \"AN\",\n   ", "metadata": {"token_count": 150, "start_token": 10200, "end_token": 10350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_85"}}, {"id": "enhanced_chunk_86", "content": "\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"AN\",\n    \"ET\",\n    \"AN\",\n    \"AN\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n", "metadata": {"token_count": 150, "start_token": 10320, "end_token": 10470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_86"}}, {"id": "enhanced_chunk_87", "content": "AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n", "metadata": {"token_count": 150, "start_token": 10440, "end_token": 10590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_87"}}, {"id": "enhanced_chunk_88", "content": "AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n", "metadata": {"token_count": 150, "start_token": 10560, "end_token": 10710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_88"}}, {"id": "enhanced_chunk_89", "content": "AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AN\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"", "metadata": {"token_count": 150, "start_token": 10680, "end_token": 10830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_89"}}, {"id": "enhanced_chunk_90", "content": "    \"NSM\",\n    \"AN\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AL\",\n    \"AL\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n   ", "metadata": {"token_count": 150, "start_token": 10800, "end_token": 10950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_90"}}, {"id": "enhanced_chunk_91", "content": "\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n", "metadata": {"token_count": 150, "start_token": 10920, "end_token": 11070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_91"}}, {"id": "enhanced_chunk_92", "content": "AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n", "metadata": {"token_count": 150, "start_token": 11040, "end_token": 11190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_92"}}, {"id": "enhanced_chunk_93", "content": "    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n   ", "metadata": {"token_count": 150, "start_token": 11160, "end_token": 11310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_93"}}, {"id": "enhanced_chunk_94", "content": "    \"NSM\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n   ", "metadata": {"token_count": 150, "start_token": 11280, "end_token": 11430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_94"}}, {"id": "enhanced_chunk_95", "content": "\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n   ", "metadata": {"token_count": 150, "start_token": 11400, "end_token": 11550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_95"}}, {"id": "enhanced_chunk_96", "content": "\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n   ", "metadata": {"token_count": 150, "start_token": 11520, "end_token": 11670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_96"}}, {"id": "enhanced_chunk_97", "content": "\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"AL\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"", "metadata": {"token_count": 150, "start_token": 11640, "end_token": 11790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_97"}}, {"id": "enhanced_chunk_98", "content": "    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"", "metadata": {"token_count": 150, "start_token": 11760, "end_token": 11910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_98"}}, {"id": "enhanced_chunk_99", "content": "    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 11880, "end_token": 12030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_99"}}, {"id": "enhanced_chunk_100", "content": "\",\n    \"N\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"WS\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"L\",\n    \"R\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 12000, "end_token": 12150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_100"}}, {"id": "enhanced_chunk_101", "content": "\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"WS\",\n    \"B\",\n    \"LRE\",\n    \"RLE\",\n    \"PDF\",\n    \"LRO\",\n    \"RLO\",\n    \"CS\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 12120, "end_token": 12270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_101"}}, {"id": "enhanced_chunk_102", "content": "\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"CS\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 12240, "end_token": 12390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_102"}}, {"id": "enhanced_chunk_103", "content": "\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"WS\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"N\",\n    \"LRI\",\n    \"RLI\",\n    \"FSI\",\n    \"PDI\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"BN\",\n    \"EN\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"EN\",\n    \"EN\",\n   ", "metadata": {"token_count": 150, "start_token": 12360, "end_token": 12510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_103"}}, {"id": "enhanced_chunk_104", "content": "\",\n    \"BN\",\n    \"EN\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"ES\",\n    \"ES\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"ES\",\n    \"ES\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n   ", "metadata": {"token_count": 150, "start_token": 12480, "end_token": 12630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_104"}}, {"id": "enhanced_chunk_105", "content": "\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n   ", "metadata": {"token_count": 150, "start_token": 12600, "end_token": 12750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_105"}}, {"id": "enhanced_chunk_106", "content": "\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n   ", "metadata": {"token_count": 150, "start_token": 12720, "end_token": 12870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_106"}}, {"id": "enhanced_chunk_107", "content": "\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n   ", "metadata": {"token_count": 150, "start_token": 12840, "end_token": 12990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_107"}}, {"id": "enhanced_chunk_108", "content": " \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n", "metadata": {"token_count": 150, "start_token": 12960, "end_token": 13110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_108"}}, {"id": "enhanced_chunk_109", "content": "N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"NSM\",\n    \"R\",\n    \"R\",\n    \"R", "metadata": {"token_count": 150, "start_token": 13080, "end_token": 13230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_109"}}, {"id": "enhanced_chunk_110", "content": "N\",\n    \"N\",\n    \"N\",\n    \"R\",\n    \"NSM\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"ES\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"N", "metadata": {"token_count": 150, "start_token": 13200, "end_token": 13350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_110"}}, {"id": "enhanced_chunk_111", "content": " \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"N\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"R\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13320, "end_token": 13470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_111"}}, {"id": "enhanced_chunk_112", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13440, "end_token": 13590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_112"}}, {"id": "enhanced_chunk_113", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13560, "end_token": 13710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_113"}}, {"id": "enhanced_chunk_114", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13680, "end_token": 13830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_114"}}, {"id": "enhanced_chunk_115", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13800, "end_token": 13950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_115"}}, {"id": "enhanced_chunk_116", "content": " \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 13920, "end_token": 14070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_116"}}, {"id": "enhanced_chunk_117", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n   ", "metadata": {"token_count": 150, "start_token": 14040, "end_token": 14190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_117"}}, {"id": "enhanced_chunk_118", "content": " \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"", "metadata": {"token_count": 150, "start_token": 14160, "end_token": 14310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_118"}}, {"id": "enhanced_chunk_119", "content": "NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"NSM\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N", "metadata": {"token_count": 150, "start_token": 14280, "end_token": 14430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_119"}}, {"id": "enhanced_chunk_120", "content": " \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"CS\",\n    \"N\",\n    \"CS\",\n    \"N\",\n    \"N\",\n    \"CS\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"ES\",\n    \"ES", "metadata": {"token_count": 150, "start_token": 14400, "end_token": 14550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_120"}}, {"id": "enhanced_chunk_121", "content": " \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"ES\",\n    \"ES\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"N\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 14520, "end_token": 14670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_121"}}, {"id": "enhanced_chunk_122", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 14640, "end_token": 14790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_122"}}, {"id": "enhanced_chunk_123", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 14760, "end_token": 14910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_123"}}, {"id": "enhanced_chunk_124", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 14880, "end_token": 15030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_124"}}, {"id": "enhanced_chunk_125", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL", "metadata": {"token_count": 150, "start_token": 15000, "end_token": 15150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_125"}}, {"id": "enhanced_chunk_126", "content": " \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"AL\",\n    \"N\",\n    \"N\",\n    \"BN\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ES\",\n    \"CS\",\n    \"ES\",\n    \"CS\",\n    \"CS\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN", "metadata": {"token_count": 150, "start_token": 15120, "end_token": 15270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_126"}}, {"id": "enhanced_chunk_127", "content": " \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"EN\",\n    \"CS\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15240, "end_token": 15390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_127"}}, {"id": "enhanced_chunk_128", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15360, "end_token": 15510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_128"}}, {"id": "enhanced_chunk_129", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15480, "end_token": 15630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_129"}}, {"id": "enhanced_chunk_130", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15600, "end_token": 15750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_130"}}, {"id": "enhanced_chunk_131", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15720, "end_token": 15870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_131"}}, {"id": "enhanced_chunk_132", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L", "metadata": {"token_count": 150, "start_token": 15840, "end_token": 15990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_132"}}, {"id": "enhanced_chunk_133", "content": " \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"L\",\n    \"L\",\n    \"L\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N", "metadata": {"token_count": 150, "start_token": 15960, "end_token": 16110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_133"}}, {"id": "enhanced_chunk_134", "content": " \"N\",\n    \"N\",\n    \"N\",\n    \"ET\",\n    \"ET\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\",\n    \"N\"\n  ];\n\n  /**\n   * Unicode Bidi algorithm compliant Bidi engine.\n   * For reference see http://unicode.org/reports/tr9/\n   */\n\n", "metadata": {"token_count": 150, "start_token": 16080, "end_token": 16230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_134"}}, {"id": "enhanced_chunk_135", "content": " ];\n\n  /**\n   * Unicode Bidi algorithm compliant Bidi engine.\n   * For reference see http://unicode.org/reports/tr9/\n   */\n\n  /**\n   * constructor ( options )\n   *\n   * Initializes Bidi engine\n   *\n   * @param {Object} See 'setOptions' below for detailed description.\n   * options are cashed between invocation of 'doBidiReorder' method\n   *\n   * sample usage pattern of BidiEngine:\n   * var opt = {\n   * \tisInputVisual: true,\n   * \tisInputRtl: false,\n   * \tisOutputVisual: false,\n   * \tisOutputRtl: false,\n   * \tisSymmetricSwapping: true", "metadata": {"token_count": 150, "start_token": 16200, "end_token": 16350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_135"}}, {"id": "enhanced_chunk_136", "content": ",\n   * \tisOutputVisual: false,\n   * \tisOutputRtl: false,\n   * \tisSymmetricSwapping: true\n   * }\n   * var sourceToTarget = [], levels = [];\n   * var bidiEng = Globalize.bidiEngine(opt);\n   * var src = \"text string to be reordered\";\n   * var ret = bidiEng.doBidiReorder(src, sourceToTarget, levels);\n   */\n\n  jsPDF.__bidiEngine__ = jsPDF.prototype.__bidiEngine__ = function(options) {\n    var _UNICODE_TYPES = _bidiUnicodeTypes;\n\n    var _STATE_TABLE_LTR = [\n      [0, 3, 0, 1, ", "metadata": {"token_count": 150, "start_token": 16320, "end_token": 16470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_136"}}, {"id": "enhanced_chunk_137", "content": " = _bidiUnicodeTypes;\n\n    var _STATE_TABLE_LTR = [\n      [0, 3, 0, 1, 0, 0, 0],\n      [0, 3, 0, 1, 2, 2, 0],\n      [0, 3, 0, 0x11, 2, 0, 1],\n      [0, 3, 5, 5, 4, 1, 0],\n      [0, 3, 0x15, 0x15, 4, 0, 1],\n      [0, 3, 5, 5, 4, 2", "metadata": {"token_count": 150, "start_token": 16440, "end_token": 16590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_137"}}, {"id": "enhanced_chunk_138", "content": "x15, 4, 0, 1],\n      [0, 3, 5, 5, 4, 2, 0]\n    ];\n\n    var _STATE_TABLE_RTL = [\n      [2, 0, 1, 1, 0, 1, 0],\n      [2, 0, 1, 1, 0, 2, 0],\n      [2, 0, 2, 1, 3, 2, 0],\n      [2, 0, 2, 0x21, 3, 1, 1]\n    ];\n\n    var _TYPE_NAMES_MAP = { L: 0,", "metadata": {"token_count": 150, "start_token": 16560, "end_token": 16710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_138"}}, {"id": "enhanced_chunk_139", "content": ", 0x21, 3, 1, 1]\n    ];\n\n    var _TYPE_NAMES_MAP = { L: 0, R: 1, EN: 2, AN: 3, N: 4, B: 5, S: 6 };\n\n    var _UNICODE_RANGES_MAP = {\n      0: 0,\n      5: 1,\n      6: 2,\n      7: 3,\n      0x20: 4,\n      0xfb: 5,\n      0xfe: 6,\n      0xff: 7\n    };\n\n    var _SWAP_TABLE = [\n      \"\\u0028\",\n      \"\\u", "metadata": {"token_count": 150, "start_token": 16680, "end_token": 16830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_139"}}, {"id": "enhanced_chunk_140", "content": " 6,\n      0xff: 7\n    };\n\n    var _SWAP_TABLE = [\n      \"\\u0028\",\n      \"\\u0029\",\n      \"\\u0028\",\n      \"\\u003C\",\n      \"\\u003E\",\n      \"\\u003C\",\n      \"\\u005B\",\n      \"\\u005D\",\n      \"\\u005B\",\n      \"\\u007B\",\n      \"\\u007D\",\n      \"\\u007B\",\n      \"\\u00AB\",\n      \"\\u00BB\",\n      \"\\u00AB\",\n      \"\\u2039\",\n      \"\\u203A\",\n      \"\\u2039\",\n      \"\\u2045\",\n      \"\\u2046\",\n      \"\\u2045\",\n      \"\\u", "metadata": {"token_count": 150, "start_token": 16800, "end_token": 16950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_140"}}, {"id": "enhanced_chunk_141", "content": "203A\",\n      \"\\u2039\",\n      \"\\u2045\",\n      \"\\u2046\",\n      \"\\u2045\",\n      \"\\u207D\",\n      \"\\u207E\",\n      \"\\u207D\",\n      \"\\u208D\",\n      \"\\u208E\",\n      \"\\u208D\",\n      \"\\u2264\",\n      \"\\u2265\",\n      \"\\u2264\",\n      \"\\u2329\",\n      \"\\u232A\",\n      \"\\u2329\",\n      \"\\uFE59\",\n      \"\\uFE5A\",\n      \"\\uFE59\",\n      \"\\uFE5B\",\n      \"\\uFE5C\",\n      \"\\uFE5B\",\n      \"\\uFE5D\",\n      \"\\uFE", "metadata": {"token_count": 150, "start_token": 16920, "end_token": 17070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_141"}}, {"id": "enhanced_chunk_142", "content": "uFE5B\",\n      \"\\uFE5C\",\n      \"\\uFE5B\",\n      \"\\uFE5D\",\n      \"\\uFE5E\",\n      \"\\uFE5D\",\n      \"\\uFE64\",\n      \"\\uFE65\",\n      \"\\uFE64\"\n    ];\n\n    var _LTR_RANGES_REG_EXPR = new RegExp(\n      /^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/\n    );\n\n    var _lastArabic = false,\n      _", "metadata": {"token_count": 150, "start_token": 17040, "end_token": 17190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_142"}}, {"id": "enhanced_chunk_143", "content": "[0-2]|21[569]|22[03489]|250)$/\n    );\n\n    var _lastArabic = false,\n      _hasUbatAl,\n      _hasUbatB,\n      _hasUbatS,\n      DIR_LTR = 0,\n      DIR_RTL = 1,\n      _isInVisual,\n      _isInRtl,\n      _isOutVisual,\n      _isOutRtl,\n      _isSymmetricSwapping,\n      _dir = DIR_LTR;\n\n    this.__bidiEngine__ = {};\n\n    var _init = function(text, sourceToTargetMap) {\n      if (sourceToTargetMap) {\n        for (var i = 0; i <", "metadata": {"token_count": 150, "start_token": 17160, "end_token": 17310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_143"}}, {"id": "enhanced_chunk_144", "content": " = function(text, sourceToTargetMap) {\n      if (sourceToTargetMap) {\n        for (var i = 0; i < text.length; i++) {\n          sourceToTargetMap[i] = i;\n        }\n      }\n      if (_isInRtl === undefined) {\n        _isInRtl = _isContextualDirRtl(text);\n      }\n      if (_isOutRtl === undefined) {\n        _isOutRtl = _isContextualDirRtl(text);\n      }\n    };\n\n    // for reference see 3.2 in http://unicode.org/reports/tr9/\n    //\n    var _getCharType = function(ch) {\n      var charCode = ch.charCodeAt(),\n", "metadata": {"token_count": 150, "start_token": 17280, "end_token": 17430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_144"}}, {"id": "enhanced_chunk_145", "content": " http://unicode.org/reports/tr9/\n    //\n    var _getCharType = function(ch) {\n      var charCode = ch.charCodeAt(),\n        range = charCode >> 8,\n        rangeIdx = _UNICODE_RANGES_MAP[range];\n\n      if (rangeIdx !== undefined) {\n        return _UNICODE_TYPES[rangeIdx * 256 + (charCode & 0xff)];\n      } else if (range === 0xfc || range === 0xfd) {\n        return \"AL\";\n      } else if (_LTR_RANGES_REG_EXPR.test(range)) {\n        //unlikely case\n        return \"L\";\n      } else if (range === 8) {\n        // even less likely\n        return \"R", "metadata": {"token_count": 150, "start_token": 17400, "end_token": 17550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_145"}}, {"id": "enhanced_chunk_146", "content": " //unlikely case\n        return \"L\";\n      } else if (range === 8) {\n        // even less likely\n        return \"R\";\n      }\n      return \"N\"; //undefined type, mark as neutral\n    };\n\n    var _isContextualDirRtl = function(text) {\n      for (var i = 0, charType; i < text.length; i++) {\n        charType = _getCharType(text.charAt(i));\n        if (charType === \"L\") {\n          return false;\n        } else if (charType === \"R\") {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    // for reference see 3.3.4 & 3.3", "metadata": {"token_count": 150, "start_token": 17520, "end_token": 17670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_146"}}, {"id": "enhanced_chunk_147", "content": "          return true;\n        }\n      }\n      return false;\n    };\n\n    // for reference see 3.3.4 & 3.3.5 in http://unicode.org/reports/tr9/\n    //\n    var _resolveCharType = function(chars, types, resolvedTypes, index) {\n      var cType = types[index],\n        wType,\n        nType,\n        i,\n        len;\n      switch (cType) {\n        case \"L\":\n        case \"R\":\n          _lastArabic = false;\n          break;\n        case \"N\":\n        case \"AN\":\n          break;\n\n        case \"EN\":\n          if (_lastArabic) {\n            cType = \"AN\";\n          }\n          break;\n\n", "metadata": {"token_count": 150, "start_token": 17640, "end_token": 17790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_147"}}, {"id": "enhanced_chunk_148", "content": "AN\":\n          break;\n\n        case \"EN\":\n          if (_lastArabic) {\n            cType = \"AN\";\n          }\n          break;\n\n        case \"AL\":\n          _lastArabic = true;\n          _hasUbatAl = true;\n          cType = \"R\";\n          break;\n\n        case \"WS\":\n          cType = \"N\";\n          break;\n\n        case \"CS\":\n          if (\n            index < 1 ||\n            index + 1 >= types.length ||\n            ((wType = resolvedTypes[index - 1]) !== \"EN\" && wType !== \"AN\") ||\n            ((nType = types[index + 1]) !== \"EN\" && nType !== \"AN\")\n          ) {\n           ", "metadata": {"token_count": 150, "start_token": 17760, "end_token": 17910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_148"}}, {"id": "enhanced_chunk_149", "content": " \"AN\") ||\n            ((nType = types[index + 1]) !== \"EN\" && nType !== \"AN\")\n          ) {\n            cType = \"N\";\n          } else if (_lastArabic) {\n            nType = \"AN\";\n          }\n          cType = nType === wType ? nType : \"N\";\n          break;\n\n        case \"ES\":\n          wType = index > 0 ? resolvedTypes[index - 1] : \"B\";\n          cType =\n            wType === \"EN\" &&\n            index + 1 < types.length &&\n            types[index + 1] === \"EN\"\n              ? \"EN\"\n              : \"N\";\n          break;\n\n        case \"ET\":\n         ", "metadata": {"token_count": 150, "start_token": 17880, "end_token": 18030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_149"}}, {"id": "enhanced_chunk_150", "content": "            types[index + 1] === \"EN\"\n              ? \"EN\"\n              : \"N\";\n          break;\n\n        case \"ET\":\n          if (index > 0 && resolvedTypes[index - 1] === \"EN\") {\n            cType = \"EN\";\n            break;\n          } else if (_lastArabic) {\n            cType = \"N\";\n            break;\n          }\n          i = index + 1;\n          len = types.length;\n          while (i < len && types[i] === \"ET\") {\n            i++;\n          }\n          if (i < len && types[i] === \"EN\") {\n            cType = \"EN\";\n          } else {\n            cType = \"N\";\n          }\n", "metadata": {"token_count": 150, "start_token": 18000, "end_token": 18150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_150"}}, {"id": "enhanced_chunk_151", "content": " len && types[i] === \"EN\") {\n            cType = \"EN\";\n          } else {\n            cType = \"N\";\n          }\n          break;\n\n        case \"NSM\":\n          if (_isInVisual && !_isInRtl) {\n            //V->L\n            len = types.length;\n            i = index + 1;\n            while (i < len && types[i] === \"NSM\") {\n              i++;\n            }\n            if (i < len) {\n              var c = chars[index];\n              var rtlCandidate = (c >= 0x0591 && c <= 0x08ff) || c === 0xfb1e;\n              wType = types[i];\n              if (", "metadata": {"token_count": 150, "start_token": 18120, "end_token": 18270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_151"}}, {"id": "enhanced_chunk_152", "content": "0591 && c <= 0x08ff) || c === 0xfb1e;\n              wType = types[i];\n              if (rtlCandidate && (wType === \"R\" || wType === \"AL\")) {\n                cType = \"R\";\n                break;\n              }\n            }\n          }\n          if (index < 1 || (wType = types[index - 1]) === \"B\") {\n            cType = \"N\";\n          } else {\n            cType = resolvedTypes[index - 1];\n          }\n          break;\n\n        case \"B\":\n          _lastArabic = false;\n          _hasUbatB = true;\n          cType = _dir;\n          break;\n\n        case \"S", "metadata": {"token_count": 150, "start_token": 18240, "end_token": 18390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_152"}}, {"id": "enhanced_chunk_153", "content": " _lastArabic = false;\n          _hasUbatB = true;\n          cType = _dir;\n          break;\n\n        case \"S\":\n          _hasUbatS = true;\n          cType = \"N\";\n          break;\n\n        case \"LRE\":\n        case \"RLE\":\n        case \"LRO\":\n        case \"RLO\":\n        case \"PDF\":\n          _lastArabic = false;\n          break;\n        case \"BN\":\n          cType = \"N\";\n          break;\n      }\n      return cType;\n    };\n\n    var _handleUbatS = function(types, levels, length) {\n      for (var i = 0; i < length; i++) {\n        if (types", "metadata": {"token_count": 150, "start_token": 18360, "end_token": 18510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_153"}}, {"id": "enhanced_chunk_154", "content": "S = function(types, levels, length) {\n      for (var i = 0; i < length; i++) {\n        if (types[i] === \"S\") {\n          levels[i] = _dir;\n          for (var j = i - 1; j >= 0; j--) {\n            if (types[j] === \"WS\") {\n              levels[j] = _dir;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n    };\n\n    var _invertString = function(text, sourceToTargetMap, levels) {\n      var charArray = text.split(\"\");\n      if (levels) {\n        _computeLevels(charArray, levels, { hiLevel: _dir });\n      }\n     ", "metadata": {"token_count": 150, "start_token": 18480, "end_token": 18630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_154"}}, {"id": "enhanced_chunk_155", "content": "Array = text.split(\"\");\n      if (levels) {\n        _computeLevels(charArray, levels, { hiLevel: _dir });\n      }\n      charArray.reverse();\n      sourceToTargetMap && sourceToTargetMap.reverse();\n      return charArray.join(\"\");\n    };\n\n    // For reference see 3.3 in http://unicode.org/reports/tr9/\n    //\n    var _computeLevels = function(chars, levels, params) {\n      var action,\n        condition,\n        i,\n        index,\n        newLevel,\n        prevState,\n        condPos = -1,\n        len = chars.length,\n        newState = 0,\n        resolvedTypes = [],\n        stateTable = _dir ? _STATE_TABLE_RTL : _STATE_TABLE_L", "metadata": {"token_count": 150, "start_token": 18600, "end_token": 18750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_155"}}, {"id": "enhanced_chunk_156", "content": ".length,\n        newState = 0,\n        resolvedTypes = [],\n        stateTable = _dir ? _STATE_TABLE_RTL : _STATE_TABLE_LTR,\n        types = [];\n\n      _lastArabic = false;\n      _hasUbatAl = false;\n      _hasUbatB = false;\n      _hasUbatS = false;\n      for (i = 0; i < len; i++) {\n        types[i] = _getCharType(chars[i]);\n      }\n      for (index = 0; index < len; index++) {\n        prevState = newState;\n        resolvedTypes[index] = _resolveCharType(\n          chars,\n          types,\n          resolvedTypes,\n          index\n        );\n        newState = state", "metadata": {"token_count": 150, "start_token": 18720, "end_token": 18870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_156"}}, {"id": "enhanced_chunk_157", "content": "        resolvedTypes[index] = _resolveCharType(\n          chars,\n          types,\n          resolvedTypes,\n          index\n        );\n        newState = stateTable[prevState][_TYPE_NAMES_MAP[resolvedTypes[index]]];\n        action = newState & 0xf0;\n        newState &= 0x0f;\n        levels[index] = newLevel = stateTable[newState][5];\n        if (action > 0) {\n          if (action === 0x10) {\n            for (i = condPos; i < index; i++) {\n              levels[i] = 1;\n            }\n            condPos = -1;\n          } else {\n            condPos = -1;\n          }\n        }\n        condition = stateTable[newState", "metadata": {"token_count": 150, "start_token": 18840, "end_token": 18990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_157"}}, {"id": "enhanced_chunk_158", "content": " }\n            condPos = -1;\n          } else {\n            condPos = -1;\n          }\n        }\n        condition = stateTable[newState][6];\n        if (condition) {\n          if (condPos === -1) {\n            condPos = index;\n          }\n        } else {\n          if (condPos > -1) {\n            for (i = condPos; i < index; i++) {\n              levels[i] = newLevel;\n            }\n            condPos = -1;\n          }\n        }\n        if (types[index] === \"B\") {\n          levels[index] = 0;\n        }\n        params.hiLevel |= newLevel;\n      }\n      if (_hasUbatS) {\n        _", "metadata": {"token_count": 150, "start_token": 18960, "end_token": 19110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_158"}}, {"id": "enhanced_chunk_159", "content": "[index] = 0;\n        }\n        params.hiLevel |= newLevel;\n      }\n      if (_hasUbatS) {\n        _handleUbatS(types, levels, len);\n      }\n    };\n\n    // for reference see 3.4 in http://unicode.org/reports/tr9/\n    //\n    var _invertByLevel = function(\n      level,\n      charArray,\n      sourceToTargetMap,\n      levels,\n      params\n    ) {\n      if (params.hiLevel < level) {\n        return;\n      }\n      if (level === 1 && _dir === DIR_RTL && !_hasUbatB) {\n        charArray.reverse();\n        sourceToTargetMap && sourceToTargetMap.reverse", "metadata": {"token_count": 150, "start_token": 19080, "end_token": 19230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_159"}}, {"id": "enhanced_chunk_160", "content": " _dir === DIR_RTL && !_hasUbatB) {\n        charArray.reverse();\n        sourceToTargetMap && sourceToTargetMap.reverse();\n        return;\n      }\n      var ch,\n        high,\n        end,\n        low,\n        len = charArray.length,\n        start = 0;\n\n      while (start < len) {\n        if (levels[start] >= level) {\n          end = start + 1;\n          while (end < len && levels[end] >= level) {\n            end++;\n          }\n          for (low = start, high = end - 1; low < high; low++, high--) {\n            ch = charArray[low];\n            charArray[low] = charArray[high];\n", "metadata": {"token_count": 150, "start_token": 19200, "end_token": 19350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_160"}}, {"id": "enhanced_chunk_161", "content": "; low < high; low++, high--) {\n            ch = charArray[low];\n            charArray[low] = charArray[high];\n            charArray[high] = ch;\n            if (sourceToTargetMap) {\n              ch = sourceToTargetMap[low];\n              sourceToTargetMap[low] = sourceToTargetMap[high];\n              sourceToTargetMap[high] = ch;\n            }\n          }\n          start = end;\n        }\n        start++;\n      }\n    };\n\n    // for reference see 7 & BD16 in http://unicode.org/reports/tr9/\n    //\n    var _symmetricSwap = function(charArray, levels, params) {\n      if (params.hiLevel !== ", "metadata": {"token_count": 150, "start_token": 19320, "end_token": 19470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_161"}}, {"id": "enhanced_chunk_162", "content": "/tr9/\n    //\n    var _symmetricSwap = function(charArray, levels, params) {\n      if (params.hiLevel !== 0 && _isSymmetricSwapping) {\n        for (var i = 0, index; i < charArray.length; i++) {\n          if (levels[i] === 1) {\n            index = _SWAP_TABLE.indexOf(charArray[i]);\n            if (index >= 0) {\n              charArray[i] = _SWAP_TABLE[index + 1];\n            }\n          }\n        }\n      }\n    };\n\n    var _reorder = function(text, sourceToTargetMap, levels) {\n      var charArray = text.split(\"\"),\n        params = { hiLevel:", "metadata": {"token_count": 150, "start_token": 19440, "end_token": 19590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_162"}}, {"id": "enhanced_chunk_163", "content": "reorder = function(text, sourceToTargetMap, levels) {\n      var charArray = text.split(\"\"),\n        params = { hiLevel: _dir };\n\n      if (!levels) {\n        levels = [];\n      }\n      _computeLevels(charArray, levels, params);\n      _symmetricSwap(charArray, levels, params);\n      _invertByLevel(DIR_RTL + 1, charArray, sourceToTargetMap, levels, params);\n      _invertByLevel(DIR_RTL, charArray, sourceToTargetMap, levels, params);\n      return charArray.join(\"\");\n    };\n\n    // doBidiReorder( text, sourceToTargetMap, levels )\n    // Performs Bidi reordering by implementing Unicode Bidi", "metadata": {"token_count": 150, "start_token": 19560, "end_token": 19710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_163"}}, {"id": "enhanced_chunk_164", "content": " };\n\n    // doBidiReorder( text, sourceToTargetMap, levels )\n    // Performs Bidi reordering by implementing Unicode Bidi algorithm.\n    // Returns reordered string\n    // @text [String]:\n    // - input string to be reordered, this is input parameter\n    // $sourceToTargetMap [Array] (optional)\n    // - resultant mapping between input and output strings, this is output parameter\n    // $levels [Array] (optional)\n    // - array of calculated Bidi levels, , this is output parameter\n    this.__bidiEngine__.doBidiReorder = function(\n      text,\n      sourceToTargetMap,\n      levels\n    ) {\n      _init(text, source", "metadata": {"token_count": 150, "start_token": 19680, "end_token": 19830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_164"}}, {"id": "enhanced_chunk_165", "content": "__.doBidiReorder = function(\n      text,\n      sourceToTargetMap,\n      levels\n    ) {\n      _init(text, sourceToTargetMap);\n      if (!_isInVisual && _isOutVisual && !_isOutRtl) {\n        // LLTR->VLTR, LRTL->VLTR\n        _dir = _isInRtl ? DIR_RTL : DIR_LTR;\n        text = _reorder(text, sourceToTargetMap, levels);\n      } else if (_isInVisual && _isOutVisual && _isInRtl ^ _isOutRtl) {\n        // VRTL->VLTR, VLTR->VRTL\n        _dir = _isInRtl", "metadata": {"token_count": 150, "start_token": 19800, "end_token": 19950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_165"}}, {"id": "enhanced_chunk_166", "content": " _isOutRtl) {\n        // VRTL->VLTR, VLTR->VRTL\n        _dir = _isInRtl ? DIR_RTL : DIR_LTR;\n        text = _invertString(text, sourceToTargetMap, levels);\n      } else if (!_isInVisual && _isOutVisual && _isOutRtl) {\n        // LLTR->VRTL, LRTL->VRTL\n        _dir = _isInRtl ? DIR_RTL : DIR_LTR;\n        text = _reorder(text, sourceToTargetMap, levels);\n        text = _invertString(text, sourceToTargetMap);\n      } else if (_isInVisual && !_isInRtl &&", "metadata": {"token_count": 150, "start_token": 19920, "end_token": 20070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_166"}}, {"id": "enhanced_chunk_167", "content": " levels);\n        text = _invertString(text, sourceToTargetMap);\n      } else if (_isInVisual && !_isInRtl && !_isOutVisual && !_isOutRtl) {\n        // VLTR->LLTR\n        _dir = DIR_LTR;\n        text = _reorder(text, sourceToTargetMap, levels);\n      } else if (_isInVisual && !_isOutVisual && _isInRtl ^ _isOutRtl) {\n        // VLTR->LRTL, VRTL->LLTR\n        text = _invertString(text, sourceToTargetMap);\n        if (_isInRtl) {\n          //LLTR -> VLTR\n          _dir = DIR_L", "metadata": {"token_count": 150, "start_token": 20040, "end_token": 20190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_167"}}, {"id": "enhanced_chunk_168", "content": "(text, sourceToTargetMap);\n        if (_isInRtl) {\n          //LLTR -> VLTR\n          _dir = DIR_LTR;\n          text = _reorder(text, sourceToTargetMap, levels);\n        } else {\n          //LRTL -> VRTL\n          _dir = DIR_RTL;\n          text = _reorder(text, sourceToTargetMap, levels);\n          text = _invertString(text, sourceToTargetMap);\n        }\n      } else if (_isInVisual && _isInRtl && !_isOutVisual && _isOutRtl) {\n        //  VRTL->LRTL\n        _dir = DIR_RTL;\n        text = _reorder(text, source", "metadata": {"token_count": 150, "start_token": 20160, "end_token": 20310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_168"}}, {"id": "enhanced_chunk_169", "content": "Rtl) {\n        //  VRTL->LRTL\n        _dir = DIR_RTL;\n        text = _reorder(text, sourceToTargetMap, levels);\n        text = _invertString(text, sourceToTargetMap);\n      } else if (!_isInVisual && !_isOutVisual && _isInRtl ^ _isOutRtl) {\n        // LRTL->LLTR, LLTR->LRTL\n        var isSymmetricSwappingOrig = _isSymmetricSwapping;\n        if (_isInRtl) {\n          //LRTL->LLTR\n          _dir = DIR_RTL;\n          text = _reorder(text, sourceToTargetMap, levels);\n          _dir =", "metadata": {"token_count": 150, "start_token": 20280, "end_token": 20430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_169"}}, {"id": "enhanced_chunk_170", "content": "LLTR\n          _dir = DIR_RTL;\n          text = _reorder(text, sourceToTargetMap, levels);\n          _dir = DIR_LTR;\n          _isSymmetricSwapping = false;\n          text = _reorder(text, sourceToTargetMap, levels);\n          _isSymmetricSwapping = isSymmetricSwappingOrig;\n        } else {\n          //LLTR->LRTL\n          _dir = DIR_LTR;\n          text = _reorder(text, sourceToTargetMap, levels);\n          text = _invertString(text, sourceToTargetMap);\n          _dir = DIR_RTL;\n          _isSymmetricSwapping = false;\n          text = _reorder(text, sourceTo", "metadata": {"token_count": 150, "start_token": 20400, "end_token": 20550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_170"}}, {"id": "enhanced_chunk_171", "content": "Map);\n          _dir = DIR_RTL;\n          _isSymmetricSwapping = false;\n          text = _reorder(text, sourceToTargetMap, levels);\n          _isSymmetricSwapping = isSymmetricSwappingOrig;\n          text = _invertString(text, sourceToTargetMap);\n        }\n      }\n      return text;\n    };\n\n    /**\n     * @name setOptions( options )\n     * @function\n     * Sets options for Bidi conversion\n     * @param {Object}:\n     * - isInputVisual {boolean} (defaults to false): allowed values: true(Visual mode), false(Logical mode)\n     * - isInputRtl {boolean}: allowed values true(Right-to-left", "metadata": {"token_count": 150, "start_token": 20520, "end_token": 20670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_171"}}, {"id": "enhanced_chunk_172", "content": " allowed values: true(Visual mode), false(Logical mode)\n     * - isInputRtl {boolean}: allowed values true(Right-to-left direction), false (Left-to-right directiion), undefined(Contectual direction, i.e.direction defined by first strong character of input string)\n     * - isOutputVisual {boolean} (defaults to false): allowed values: true(Visual mode), false(Logical mode)\n     * - isOutputRtl {boolean}: allowed values true(Right-to-left direction), false (Left-to-right directiion), undefined(Contectual direction, i.e.direction defined by first strong characterof input string)\n     * - isSymmetricSwapping {boolean} (defaults to false):", "metadata": {"token_count": 150, "start_token": 20640, "end_token": 20790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_172"}}, {"id": "enhanced_chunk_173", "content": " direction, i.e.direction defined by first strong characterof input string)\n     * - isSymmetricSwapping {boolean} (defaults to false): allowed values true(needs symmetric swapping), false (no need in symmetric swapping),\n     */\n    this.__bidiEngine__.setOptions = function(options) {\n      if (options) {\n        _isInVisual = options.isInputVisual;\n        _isOutVisual = options.isOutputVisual;\n        _isInRtl = options.isInputRtl;\n        _isOutRtl = options.isOutputRtl;\n        _isSymmetricSwapping = options.isSymmetricSwapping;\n      }\n    };\n\n    this.__bidiEngine__.setOptions(options);\n    return this.__", "metadata": {"token_count": 150, "start_token": 20760, "end_token": 20910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_173"}}, {"id": "enhanced_chunk_174", "content": "metricSwapping = options.isSymmetricSwapping;\n      }\n    };\n\n    this.__bidiEngine__.setOptions(options);\n    return this.__bidiEngine__;\n  };\n\n  var _bidiUnicodeTypes = bidiUnicodeTypes;\n\n  var bidiEngine = new jsPDF.__bidiEngine__({ isInputVisual: true });\n\n  var bidiEngineFunction = function(args) {\n    var text = args.text;\n    var x = args.x;\n    var y = args.y;\n    var options = args.options || {};\n    var mutex = args.mutex || {};\n    var lang = options.lang;\n    var tmpText = [];\n\n    options.isInputVisual =\n      typeof options.isInputVisual === \"boolean\" ? options.isInputVisual :", "metadata": {"token_count": 150, "start_token": 20880, "end_token": 21030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_174"}}, {"id": "enhanced_chunk_175", "content": ".lang;\n    var tmpText = [];\n\n    options.isInputVisual =\n      typeof options.isInputVisual === \"boolean\" ? options.isInputVisual : true;\n    bidiEngine.setOptions(options);\n\n    if (Object.prototype.toString.call(text) === \"[object Array]\") {\n      var i = 0;\n      tmpText = [];\n      for (i = 0; i < text.length; i += 1) {\n        if (Object.prototype.toString.call(text[i]) === \"[object Array]\") {\n          tmpText.push([\n            bidiEngine.doBidiReorder(text[i][0]),\n            text[i][1],\n            text[i][2]\n          ]);\n        } else {\n          tmpText.push([bidiEngine.doBidiReorder", "metadata": {"token_count": 150, "start_token": 21000, "end_token": 21150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_175"}}, {"id": "enhanced_chunk_176", "content": " text[i][1],\n            text[i][2]\n          ]);\n        } else {\n          tmpText.push([bidiEngine.doBidiReorder(text[i])]);\n        }\n      }\n      args.text = tmpText;\n    } else {\n      args.text = bidiEngine.doBidiReorder(text);\n    }\n    bidiEngine.setOptions({ isInputVisual: true });\n  };\n\n  jsPDF.API.events.push([\"postProcessText\", bidiEngineFunction]);\n})(jsPDF);\n\n\n// === split_text_to_size.js ===\n\n/** @license\n * MIT license.\n * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com\n *               2014 Diego <PERSON>, https://github.com/diegocr\n", "metadata": {"token_count": 150, "start_token": 21120, "end_token": 21270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_176"}}, {"id": "enhanced_chunk_177", "content": "2012 Willow Systems Corporation, willow-systems.com\n *               2014 <PERSON>, https://github.com/diegocr\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software", "metadata": {"token_count": 150, "start_token": 21240, "end_token": 21390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_177"}}, {"id": "enhanced_chunk_178", "content": "\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n * ====================================================================\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * js", "metadata": {"token_count": 150, "start_token": 21360, "end_token": 21510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_178"}}, {"id": "enhanced_chunk_179", "content": " THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n * ====================================================================\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF split_text_to_size plugin\n *\n * @name split_text_to_size\n * @module\n */\n(function(API) {\n  \"use strict\";\n  /**\n   * Returns an array of length matching length of the 'word' string, with each\n   * cell occupied by the width of the char in that position.\n   *\n   * @name getCharWidthsArray\n   * @function\n   * @param {string} text\n   * @param {Object} options\n   * @returns {Array}\n   */\n  var getCharWidthsArray = (API.get", "metadata": {"token_count": 150, "start_token": 21480, "end_token": 21630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_179"}}, {"id": "enhanced_chunk_180", "content": "\n   * @param {Object} options\n   * @returns {Array}\n   */\n  var getCharWidthsArray = (API.getCharWidthsArray = function(text, options) {\n    options = options || {};\n\n    var activeFont = options.font || this.internal.getFont();\n    var fontSize = options.fontSize || this.internal.getFontSize();\n    var charSpace = options.charSpace || this.internal.getCharSpace();\n\n    var widths = options.widths\n      ? options.widths\n      : activeFont.metadata.Unicode.widths;\n    var widthsFractionOf = widths.fof ? widths.fof : 1;\n    var kerning = options.kerning\n      ? options.kerning\n      : activeFont.metadata", "metadata": {"token_count": 150, "start_token": 21600, "end_token": 21750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_180"}}, {"id": "enhanced_chunk_181", "content": " widths.fof : 1;\n    var kerning = options.kerning\n      ? options.kerning\n      : activeFont.metadata.Unicode.kerning;\n    var kerningFractionOf = kerning.fof ? kerning.fof : 1;\n    var doKerning = options.doKerning === false ? false : true;\n    var kerningValue = 0;\n\n    var i;\n    var length = text.length;\n    var char_code;\n    var prior_char_code = 0; //for kerning\n    var default_char_width = widths[0] || widthsFractionOf;\n    var output = [];\n\n    for (i = 0; i < length; i++) {\n", "metadata": {"token_count": 150, "start_token": 21720, "end_token": 21870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_181"}}, {"id": "enhanced_chunk_182", "content": " = widths[0] || widthsFractionOf;\n    var output = [];\n\n    for (i = 0; i < length; i++) {\n      char_code = text.charCodeAt(i);\n\n      if (typeof activeFont.metadata.widthOfString === \"function\") {\n        output.push(\n          (activeFont.metadata.widthOfGlyph(\n            activeFont.metadata.characterToGlyph(char_code)\n          ) +\n            charSpace * (1000 / fontSize) || 0) / 1000\n        );\n      } else {\n        if (\n          doKerning &&\n          typeof kerning[char_code] === \"object\" &&\n          !isNaN(parseInt(kerning[char_code][prior_char_code], 10))\n        ) {\n          kerningValue =\n           ", "metadata": {"token_count": 150, "start_token": 21840, "end_token": 21990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_182"}}, {"id": "enhanced_chunk_183", "content": " \"object\" &&\n          !isNaN(parseInt(kerning[char_code][prior_char_code], 10))\n        ) {\n          kerningValue =\n            kerning[char_code][prior_char_code] / kerningFractionOf;\n        } else {\n          kerningValue = 0;\n        }\n        output.push(\n          (widths[char_code] || default_char_width) / widthsFractionOf +\n            kerningValue\n        );\n      }\n      prior_char_code = char_code;\n    }\n\n    return output;\n  });\n\n  /**\n   * Returns a widths of string in a given font, if the font size is set as 1 point.\n   *\n   * In other words, this is \"proportional\" value. For 1", "metadata": {"token_count": 150, "start_token": 21960, "end_token": 22110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_183"}}, {"id": "enhanced_chunk_184", "content": " the font size is set as 1 point.\n   *\n   * In other words, this is \"proportional\" value. For 1 unit of font size, the length\n   * of the string will be that much.\n   *\n   * Multiply by font size to get actual width in *points*\n   * Then divide by 72 to get inches or divide by (72/25.6) to get 'mm' etc.\n   *\n   * @name getStringUnitWidth\n   * @public\n   * @function\n   * @param {string} text\n   * @param {string} options\n   * @returns {number} result\n   */\n  var getStringUnitWidth = (API.getStringUnit", "metadata": {"token_count": 150, "start_token": 22080, "end_token": 22230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_184"}}, {"id": "enhanced_chunk_185", "content": "   * @param {string} options\n   * @returns {number} result\n   */\n  var getStringUnitWidth = (API.getStringUnitWidth = function(text, options) {\n    options = options || {};\n\n    var fontSize = options.fontSize || this.internal.getFontSize();\n    var font = options.font || this.internal.getFont();\n    var charSpace = options.charSpace || this.internal.getCharSpace();\n    var result = 0;\n\n    if (API.processArabic) {\n      text = API.processArabic(text);\n    }\n\n    if (typeof font.metadata.widthOfString === \"function\") {\n      result =\n        font.metadata.widthOfString(text, fontSize, charSpace) / fontSize;\n    } else {\n      result = getCharWidth", "metadata": {"token_count": 150, "start_token": 22200, "end_token": 22350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_185"}}, {"id": "enhanced_chunk_186", "content": "\") {\n      result =\n        font.metadata.widthOfString(text, fontSize, charSpace) / fontSize;\n    } else {\n      result = getCharWidthsArray\n        .apply(this, arguments)\n        .reduce(function(pv, cv) {\n          return pv + cv;\n        }, 0);\n    }\n    return result;\n  });\n\n  /**\n  returns array of lines\n  */\n  var splitLongWord = function(word, widths_array, firstLineMaxLen, maxLen) {\n    var answer = [];\n\n    // 1st, chop off the piece that can fit on the hanging line.\n    var i = 0,\n      l = word.length,\n      workingLen = 0;\n    while (i !== l &&", "metadata": {"token_count": 150, "start_token": 22320, "end_token": 22470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_186"}}, {"id": "enhanced_chunk_187", "content": " hanging line.\n    var i = 0,\n      l = word.length,\n      workingLen = 0;\n    while (i !== l && workingLen + widths_array[i] < firstLineMaxLen) {\n      workingLen += widths_array[i];\n      i++;\n    }\n    // this is first line.\n    answer.push(word.slice(0, i));\n\n    // 2nd. Split the rest into maxLen pieces.\n    var startOfLine = i;\n    workingLen = 0;\n    while (i !== l) {\n      if (workingLen + widths_array[i] > maxLen) {\n        answer.push(word.slice(startOfLine, i));\n        workingLen = 0;\n        startOfLine = i", "metadata": {"token_count": 150, "start_token": 22440, "end_token": 22590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_187"}}, {"id": "enhanced_chunk_188", "content": "] > maxLen) {\n        answer.push(word.slice(startOfLine, i));\n        workingLen = 0;\n        startOfLine = i;\n      }\n      workingLen += widths_array[i];\n      i++;\n    }\n    if (startOfLine !== i) {\n      answer.push(word.slice(startOfLine, i));\n    }\n\n    return answer;\n  };\n\n  // Note, all sizing inputs for this function must be in \"font measurement units\"\n  // By default, for PDF, it's \"point\".\n  var splitParagraphIntoLines = function(text, maxlen, options) {\n    // at this time works only on Western scripts, ones with space char\n    // separating the words. Feel free to expand.\n\n    if", "metadata": {"token_count": 150, "start_token": 22560, "end_token": 22710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_188"}}, {"id": "enhanced_chunk_189", "content": " {\n    // at this time works only on Western scripts, ones with space char\n    // separating the words. Feel free to expand.\n\n    if (!options) {\n      options = {};\n    }\n\n    var line = [],\n      lines = [line],\n      line_length = options.textIndent || 0,\n      separator_length = 0,\n      current_word_length = 0,\n      word,\n      widths_array,\n      words = text.split(\" \"),\n      spaceCharWidth = getCharWidthsArray.apply(this, [\" \", options])[0],\n      i,\n      l,\n      tmp,\n      lineIndent;\n\n    if (options.lineIndent === -1) {\n      lineIndent = words[0].length + 2;\n    } else", "metadata": {"token_count": 150, "start_token": 22680, "end_token": 22830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_189"}}, {"id": "enhanced_chunk_190", "content": " lineIndent;\n\n    if (options.lineIndent === -1) {\n      lineIndent = words[0].length + 2;\n    } else {\n      lineIndent = options.lineIndent || 0;\n    }\n    if (lineIndent) {\n      var pad = Array(lineIndent).join(\" \"),\n        wrds = [];\n      words.map(function(wrd) {\n        wrd = wrd.split(/\\s*\\n/);\n        if (wrd.length > 1) {\n          wrds = wrds.concat(\n            wrd.map(function(wrd, idx) {\n              return (idx && wrd.length ? \"\\n\" : \"\") + wrd;\n            })\n          );\n        } else {\n          wrds.push(wrd", "metadata": {"token_count": 150, "start_token": 22800, "end_token": 22950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_190"}}, {"id": "enhanced_chunk_191", "content": " (idx && wrd.length ? \"\\n\" : \"\") + wrd;\n            })\n          );\n        } else {\n          wrds.push(wrd[0]);\n        }\n      });\n      words = wrds;\n      lineIndent = getStringUnitWidth.apply(this, [pad, options]);\n    }\n\n    for (i = 0, l = words.length; i < l; i++) {\n      var force = 0;\n\n      word = words[i];\n      if (lineIndent && word[0] == \"\\n\") {\n        word = word.substr(1);\n        force = 1;\n      }\n      widths_array = getCharWidthsArray.apply(this, [word, options]);\n      current_word_length = widths_array.reduce(function", "metadata": {"token_count": 150, "start_token": 22920, "end_token": 23070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_191"}}, {"id": "enhanced_chunk_192", "content": "1;\n      }\n      widths_array = getCharWidthsArray.apply(this, [word, options]);\n      current_word_length = widths_array.reduce(function(pv, cv) {\n        return pv + cv;\n      }, 0);\n\n      if (\n        line_length + separator_length + current_word_length > maxlen ||\n        force\n      ) {\n        if (current_word_length > maxlen) {\n          // this happens when you have space-less long URLs for example.\n          // we just chop these to size. We do NOT insert hiphens\n          tmp = splitLongWord.apply(this, [\n            word,\n            widths_array,\n            maxlen - (line_length + separator_length),\n            maxlen\n          ]);\n          // first line we add to existing", "metadata": {"token_count": 150, "start_token": 23040, "end_token": 23190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_192"}}, {"id": "enhanced_chunk_193", "content": "            word,\n            widths_array,\n            maxlen - (line_length + separator_length),\n            maxlen\n          ]);\n          // first line we add to existing line object\n          line.push(tmp.shift()); // it's ok to have extra space indicator there\n          // last line we make into new line object\n          line = [tmp.pop()];\n          // lines in the middle we apped to lines object as whole lines\n          while (tmp.length) {\n            lines.push([tmp.shift()]); // single fragment occupies whole line\n          }\n          current_word_length = widths_array\n            .slice(word.length - (line[0] ? line[0].length : 0))\n            .reduce(function(pv, cv) {\n              return pv", "metadata": {"token_count": 150, "start_token": 23160, "end_token": 23310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_193"}}, {"id": "enhanced_chunk_194", "content": ".length - (line[0] ? line[0].length : 0))\n            .reduce(function(pv, cv) {\n              return pv + cv;\n            }, 0);\n        } else {\n          // just put it on a new line\n          line = [word];\n        }\n\n        // now we attach new line to lines\n        lines.push(line);\n        line_length = current_word_length + lineIndent;\n        separator_length = spaceCharWidth;\n      } else {\n        line.push(word);\n\n        line_length += separator_length + current_word_length;\n        separator_length = spaceCharWidth;\n      }\n    }\n\n    var postProcess;\n    if (lineIndent) {\n      postProcess = function(ln, idx) {\n        return", "metadata": {"token_count": 150, "start_token": 23280, "end_token": 23430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_194"}}, {"id": "enhanced_chunk_195", "content": "Width;\n      }\n    }\n\n    var postProcess;\n    if (lineIndent) {\n      postProcess = function(ln, idx) {\n        return (idx ? pad : \"\") + ln.join(\" \");\n      };\n    } else {\n      postProcess = function(ln) {\n        return ln.join(\" \");\n      };\n    }\n\n    return lines.map(postProcess);\n  };\n\n  /**\n   * Splits a given string into an array of strings. Uses 'size' value\n   * (in measurement units declared as default for the jsPDF instance)\n   * and the font's \"widths\" and \"Kerning\" tables, where available, to\n   * determine display length of a given string for a given font.\n   *\n  ", "metadata": {"token_count": 150, "start_token": 23400, "end_token": 23550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_195"}}, {"id": "enhanced_chunk_196", "content": " and \"Kerning\" tables, where available, to\n   * determine display length of a given string for a given font.\n   *\n   * We use character's 100% of unit size (height) as width when Width\n   * table or other default width is not available.\n   *\n   * @name splitTextToSize\n   * @public\n   * @function\n   * @param {string} text Unencoded, regular JavaScript (Unicode, UTF-16 / UCS-2) string.\n   * @param {number} size Nominal number, measured in units default to this instance of jsPDF.\n   * @param {Object} options Optional flags needed for chopper to do the right thing.\n", "metadata": {"token_count": 150, "start_token": 23520, "end_token": 23670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_196"}}, {"id": "enhanced_chunk_197", "content": " in units default to this instance of jsPDF.\n   * @param {Object} options Optional flags needed for chopper to do the right thing.\n   * @returns {Array} array Array with strings chopped to size.\n   */\n  API.splitTextToSize = function(text, maxlen, options) {\n    \"use strict\";\n\n    options = options || {};\n\n    var fsize = options.fontSize || this.internal.getFontSize(),\n      newOptions = function(options) {\n        var widths = {\n            0: 1\n          },\n          kerning = {};\n\n        if (!options.widths || !options.kerning) {\n          var f = this.internal.getFont(options.fontName, options.fontStyle),\n            encoding = \"Unicode\";\n          //", "metadata": {"token_count": 150, "start_token": 23640, "end_token": 23790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_197"}}, {"id": "enhanced_chunk_198", "content": " !options.kerning) {\n          var f = this.internal.getFont(options.fontName, options.fontStyle),\n            encoding = \"Unicode\";\n          // NOT UTF8, NOT UTF16BE/LE, NOT UCS2BE/LE\n          // Actual JavaScript-native String's 16bit char codes used.\n          // no multi-byte logic here\n\n          if (f.metadata[encoding]) {\n            return {\n              widths: f.metadata[encoding].widths || widths,\n              kerning: f.metadata[encoding].kerning || kerning\n            };\n          } else {\n            return {\n              font: f.metadata,\n              fontSize: this.internal.getFontSize(),\n              charSpace: this.internal.getCharSpace()\n            };\n          }\n        }", "metadata": {"token_count": 150, "start_token": 23760, "end_token": 23910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_198"}}, {"id": "enhanced_chunk_199", "content": "              font: f.metadata,\n              fontSize: this.internal.getFontSize(),\n              charSpace: this.internal.getCharSpace()\n            };\n          }\n        } else {\n          return {\n            widths: options.widths,\n            kerning: options.kerning\n          };\n        }\n      }.call(this, options);\n\n    // first we split on end-of-line chars\n    var paragraphs;\n    if (Array.isArray(text)) {\n      paragraphs = text;\n    } else {\n      paragraphs = String(text).split(/\\r?\\n/);\n    }\n\n    // now we convert size (max length of line) into \"font size units\"\n    // at present time, the \"font size unit\" is always 'point'\n    // 'proportional", "metadata": {"token_count": 150, "start_token": 23880, "end_token": 24030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_199"}}, {"id": "enhanced_chunk_200", "content": ") into \"font size units\"\n    // at present time, the \"font size unit\" is always 'point'\n    // 'proportional' means, \"in proportion to font size\"\n    var fontUnit_maxLen = (1.0 * this.internal.scaleFactor * maxlen) / fsize;\n    // at this time, fsize is always in \"points\" regardless of the default measurement unit of the doc.\n    // this may change in the future?\n    // until then, proportional_maxlen is likely to be in 'points'\n\n    // If first line is to be indented (shorter or longer) than maxLen\n    // we indicate that by using CSS-style \"text-indent\" option.\n    // here", "metadata": {"token_count": 150, "start_token": 24000, "end_token": 24150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_200"}}, {"id": "enhanced_chunk_201", "content": " indented (shorter or longer) than maxLen\n    // we indicate that by using CSS-style \"text-indent\" option.\n    // here it's in font units too (which is likely 'points')\n    // it can be negative (which makes the first line longer than maxLen)\n    newOptions.textIndent = options.textIndent\n      ? (options.textIndent * 1.0 * this.internal.scaleFactor) / fsize\n      : 0;\n    newOptions.lineIndent = options.lineIndent;\n\n    var i,\n      l,\n      output = [];\n    for (i = 0, l = paragraphs.length; i < l; i++) {\n      output = output.concat(\n        splitParagraphIntoLines.apply(this", "metadata": {"token_count": 150, "start_token": 24120, "end_token": 24270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_201"}}, {"id": "enhanced_chunk_202", "content": "i = 0, l = paragraphs.length; i < l; i++) {\n      output = output.concat(\n        splitParagraphIntoLines.apply(this, [\n          paragraphs[i],\n          fontUnit_maxLen,\n          newOptions\n        ])\n      );\n    }\n\n    return output;\n  };\n})(jsPDF.API);\n\n\n// === total_pages.js ===\n\n/**\n * @license\n * ====================================================================\n * Copyright (c) 2013 <PERSON>, <EMAIL>\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation", "metadata": {"token_count": 150, "start_token": 24240, "end_token": 24390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_202"}}, {"id": "enhanced_chunk_203", "content": " * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO", "metadata": {"token_count": 150, "start_token": 24360, "end_token": 24510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_203"}}, {"id": "enhanced_chunk_204", "content": ",\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n * ====================================================================\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF total_pages plugin\n * @name total_pages\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n  /**\n   * @name putTotalPages\n   * @function\n   * @", "metadata": {"token_count": 150, "start_token": 24480, "end_token": 24630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_204"}}, {"id": "enhanced_chunk_205", "content": "(function(jsPDFAPI) {\n  \"use strict\";\n  /**\n   * @name putTotalPages\n   * @function\n   * @param {string} pageExpression Regular Expression\n   * @returns {jsPDF} jsPDF-instance\n   */\n\n  jsPDFAPI.putTotalPages = function(pageExpression) {\n    \"use strict\";\n\n    var replaceExpression;\n    var totalNumberOfPages = 0;\n    if (parseInt(this.internal.getFont().id.substr(1), 10) < 15) {\n      replaceExpression = new RegExp(pageExpression, \"g\");\n      totalNumberOfPages = this.internal.getNumberOfPages();\n    } else {\n      replaceExpression = new RegExp(\n        this.pdfEscape16(pageExpression, this", "metadata": {"token_count": 150, "start_token": 24600, "end_token": 24750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_205"}}, {"id": "enhanced_chunk_206", "content": " totalNumberOfPages = this.internal.getNumberOfPages();\n    } else {\n      replaceExpression = new RegExp(\n        this.pdfEscape16(pageExpression, this.internal.getFont()),\n        \"g\"\n      );\n      totalNumberOfPages = this.pdfEscape16(\n        this.internal.getNumberOfPages() + \"\",\n        this.internal.getFont()\n      );\n    }\n\n    for (var n = 1; n <= this.internal.getNumberOfPages(); n++) {\n      for (var i = 0; i < this.internal.pages[n].length; i++) {\n        this.internal.pages[n][i] = this.internal.pages[n][i].replace(\n          replaceExpression,\n          totalNumberOfPages\n        );\n      }\n    }\n\n    return this;\n  };\n})(jsPDF", "metadata": {"token_count": 150, "start_token": 24720, "end_token": 24870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_206"}}, {"id": "enhanced_chunk_207", "content": "[n][i].replace(\n          replaceExpression,\n          totalNumberOfPages\n        );\n      }\n    }\n\n    return this;\n  };\n})(jsPDF.API);\n\n\n// === viewerpreferences.js ===\n\n/**\n * @license\n * jsPDF viewerPreferences Plugin\n * <AUTHOR> (github.com/arasabbasi)\n * Licensed under the MIT License.\n * http://opensource.org/licenses/mit-license\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * Adds the ability to set ViewerPreferences and by thus\n * controlling the way the document is to be presented on the\n * screen or in print.\n * @name viewerpreferences\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n ", "metadata": {"token_count": 150, "start_token": 24840, "end_token": 24990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_207"}}, {"id": "enhanced_chunk_208", "content": " * screen or in print.\n * @name viewerpreferences\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n  /**\n   * Set the ViewerPreferences of the generated PDF\n   *\n   * @name viewerPreferences\n   * @function\n   * @public\n   * @param {Object} options Array with the ViewerPreferences<br />\n   * Example: doc.viewerPreferences({\"FitWindow\":true});<br />\n   * <br />\n   * You can set following preferences:<br />\n   * <br/>\n   * <b>HideToolbar</b> <i>(boolean)</i><br />\n   * Default value: false<br />\n   * <br />\n   * <b>Hide", "metadata": {"token_count": 150, "start_token": 24960, "end_token": 25110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_208"}}, {"id": "enhanced_chunk_209", "content": "b> <i>(boolean)</i><br />\n   * Default value: false<br />\n   * <br />\n   * <b>HideMenubar</b> <i>(boolean)</i><br />\n   * Default value: false.<br />\n   * <br />\n   * <b>HideWindowUI</b> <i>(boolean)</i><br />\n   * Default value: false.<br />\n   * <br />\n   * <b>FitWindow</b> <i>(boolean)</i><br />\n   * Default value: false.<br />\n   * <br />\n   * <b>CenterWindow</b> <i>(boolean)</i><br />\n   * Default value: false", "metadata": {"token_count": 150, "start_token": 25080, "end_token": 25230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_209"}}, {"id": "enhanced_chunk_210", "content": "   * <br />\n   * <b>CenterWindow</b> <i>(boolean)</i><br />\n   * Default value: false<br />\n   * <br />\n   * <b>DisplayDocTitle</b> <i>(boolean)</i><br />\n   * Default value: false.<br />\n   * <br />\n   * <b>NonFullScreenPageMode</b> <i>(string)</i><br />\n   * Possible values: UseNone, UseOutlines, UseThumbs, UseOC<br />\n   * Default value: UseNone<br/>\n   * <br />\n   * <b>Direction</b> <i>(string)</i><br />\n   * Possible values: L", "metadata": {"token_count": 150, "start_token": 25200, "end_token": 25350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_210"}}, {"id": "enhanced_chunk_211", "content": "/>\n   * <br />\n   * <b>Direction</b> <i>(string)</i><br />\n   * Possible values: L2R, R2L<br />\n   * Default value: L2R.<br />\n   * <br />\n   * <b>ViewArea</b> <i>(string)</i><br />\n   * Possible values: MediaBox, CropBox, TrimBox, BleedBox, ArtBox<br />\n   * Default value: CropBox.<br />\n   * <br />\n   * <b>ViewClip</b> <i>(string)</i><br />\n   * Possible values: MediaBox, CropBox, TrimBox, BleedBox, ArtBox<br", "metadata": {"token_count": 150, "start_token": 25320, "end_token": 25470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_211"}}, {"id": "enhanced_chunk_212", "content": " <i>(string)</i><br />\n   * Possible values: MediaBox, CropBox, TrimBox, BleedBox, ArtBox<br />\n   * Default value: CropBox<br />\n   * <br />\n   * <b>PrintArea</b> <i>(string)</i><br />\n   * Possible values: MediaBox, CropBox, TrimBox, BleedBox, ArtBox<br />\n   * Default value: CropBox<br />\n   * <br />\n   * <b>PrintClip</b> <i>(string)</i><br />\n   * Possible values: MediaBox, CropBox, TrimBox, BleedBox, ArtBox<br />\n   * Default value: CropBox.<br", "metadata": {"token_count": 150, "start_token": 25440, "end_token": 25590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_212"}}, {"id": "enhanced_chunk_213", "content": " * Possible values: <PERSON>Box, CropBox, TrimBox, <PERSON>leedBox, ArtBox<br />\n   * Default value: CropBox.<br />\n   * <br />\n   * <b>PrintScaling</b> <i>(string)</i><br />\n   * Possible values: App<PERSON><PERSON><PERSON>, None<br />\n   * Default value: AppDefault.<br />\n   * <br />\n   * <b>Duplex</b> <i>(string)</i><br />\n   * Possible values: <PERSON>x, <PERSON><PERSON><PERSON><PERSON><PERSON>ongEdge, DuplexFlipShortEdge\n   * Default value: none<br />\n   * <br />\n   * <b>PickTrayByPDFSize</b> <i>(", "metadata": {"token_count": 150, "start_token": 25560, "end_token": 25710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_213"}}, {"id": "enhanced_chunk_214", "content": "   * Default value: none<br />\n   * <br />\n   * <b>PickTrayByPDFSize</b> <i>(boolean)</i><br />\n   * Default value: false<br />\n   * <br />\n   * <b>PrintPageRange</b> <i>(Array)</i><br />\n   * Example: [[1,5], [7,9]]<br />\n   * Default value: as defined by PDF viewer application<br />\n   * <br />\n   * <b>NumCopies</b> <i>(Number)</i><br />\n   * Possible values: 1, 2, 3, 4, 5<br />\n   * Default value:", "metadata": {"token_count": 150, "start_token": 25680, "end_token": 25830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_214"}}, {"id": "enhanced_chunk_215", "content": "i><br />\n   * Possible values: 1, 2, 3, 4, 5<br />\n   * Default value: 1<br />\n   * <br />\n   * For more information see the PDF Reference, sixth edition on Page 577\n   * @param {boolean} doReset True to reset the settings\n   * @function\n   * @returns jsPDF jsPDF-instance\n   * @example\n   * var doc = new jsPDF()\n   * doc.text('This is a test', 10, 10)\n   * doc.viewerPreferences({'FitWindow': true}, true)\n   * doc.save(\"viewerPreferences.pdf\")\n   *\n   * // Example printing 10 copies, using", "metadata": {"token_count": 150, "start_token": 25800, "end_token": 25950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_215"}}, {"id": "enhanced_chunk_216", "content": "Preferences({'FitWindow': true}, true)\n   * doc.save(\"viewerPreferences.pdf\")\n   *\n   * // Example printing 10 copies, using cropbox, and hiding UI.\n   * doc.viewerPreferences({\n   *   'HideWindowUI': true,\n   *   'PrintArea': 'CropBox',\n   *   'NumCopies': 10\n   * })\n   */\n  jsPDFAPI.viewerPreferences = function(options, doReset) {\n    options = options || {};\n    doReset = doReset || false;\n\n    var configuration;\n    var configurationTemplate = {\n      HideToolbar: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet", "metadata": {"token_count": 150, "start_token": 25920, "end_token": 26070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_216"}}, {"id": "enhanced_chunk_217", "content": "      HideToolbar: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      HideMenubar: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      HideWindowUI: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      FitWindow: {\n        defaultValue", "metadata": {"token_count": 150, "start_token": 26040, "end_token": 26190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_217"}}, {"id": "enhanced_chunk_218", "content": ": false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      FitWindow: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      CenterWindow: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.3\n      },\n      DisplayDocTitle: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [", "metadata": {"token_count": 150, "start_token": 26160, "end_token": 26310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_218"}}, {"id": "enhanced_chunk_219", "content": "Title: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.4\n      },\n      NonFullScreenPageMode: {\n        defaultValue: \"UseNone\",\n        value: \"UseNone\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"UseNone\", \"UseOutlines\", \"UseThumbs\", \"UseOC\"],\n        pdfVersion: 1.3\n      },\n      Direction: {\n        defaultValue: \"L2R\",\n        value: \"L2R\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"L2", "metadata": {"token_count": 150, "start_token": 26280, "end_token": 26430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_219"}}, {"id": "enhanced_chunk_220", "content": "2R\",\n        value: \"L2R\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"L2R\", \"R2L\"],\n        pdfVersion: 1.3\n      },\n      ViewArea: {\n        defaultValue: \"CropBox\",\n        value: \"CropBox\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n        pdfVersion: 1.4\n      },\n      ViewClip: {\n        defaultValue: \"CropBox\",\n        value: \"CropBox\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet", "metadata": {"token_count": 150, "start_token": 26400, "end_token": 26550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_220"}}, {"id": "enhanced_chunk_221", "content": " {\n        defaultValue: \"CropBox\",\n        value: \"CropBox\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n        pdfVersion: 1.4\n      },\n      PrintArea: {\n        defaultValue: \"CropBox\",\n        value: \"CropBox\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n        pdfVersion: 1.4\n      },\n      PrintClip: {\n        defaultValue: \"CropBox\",\n        value: \"CropBox", "metadata": {"token_count": 150, "start_token": 26520, "end_token": 26670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_221"}}, {"id": "enhanced_chunk_222", "content": "\"],\n        pdfVersion: 1.4\n      },\n      PrintClip: {\n        defaultValue: \"CropBox\",\n        value: \"CropBox\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n        pdfVersion: 1.4\n      },\n      PrintScaling: {\n        defaultValue: \"AppDefault\",\n        value: \"AppDefault\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\"AppDefault\", \"None\"],\n        pdfVersion: 1.6\n      },\n      Duplex: {\n        defaultValue: \"\",\n        value: \"none\",\n        type", "metadata": {"token_count": 150, "start_token": 26640, "end_token": 26790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_222"}}, {"id": "enhanced_chunk_223", "content": "None\"],\n        pdfVersion: 1.6\n      },\n      Duplex: {\n        defaultValue: \"\",\n        value: \"none\",\n        type: \"name\",\n        explicitSet: false,\n        valueSet: [\n          \"Simplex\",\n          \"DuplexFlipShortEdge\",\n          \"DuplexFlipLongEdge\",\n          \"none\"\n        ],\n        pdfVersion: 1.7\n      },\n      PickTrayByPDFSize: {\n        defaultValue: false,\n        value: false,\n        type: \"boolean\",\n        explicitSet: false,\n        valueSet: [true, false],\n        pdfVersion: 1.7\n      },\n      PrintPageRange: {\n        defaultValue: \"\",\n        value: \"\",\n        type", "metadata": {"token_count": 150, "start_token": 26760, "end_token": 26910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_223"}}, {"id": "enhanced_chunk_224", "content": ", false],\n        pdfVersion: 1.7\n      },\n      PrintPageRange: {\n        defaultValue: \"\",\n        value: \"\",\n        type: \"array\",\n        explicitSet: false,\n        valueSet: null,\n        pdfVersion: 1.7\n      },\n      NumCopies: {\n        defaultValue: 1,\n        value: 1,\n        type: \"integer\",\n        explicitSet: false,\n        valueSet: null,\n        pdfVersion: 1.7\n      }\n    };\n\n    var configurationKeys = Object.keys(configurationTemplate);\n\n    var rangeArray = [];\n    var i = 0;\n    var j = 0;\n    var k = 0;\n    var isValid;\n\n    var method", "metadata": {"token_count": 150, "start_token": 26880, "end_token": 27030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_224"}}, {"id": "enhanced_chunk_225", "content": " = [];\n    var i = 0;\n    var j = 0;\n    var k = 0;\n    var isValid;\n\n    var method;\n    var value;\n\n    function arrayContainsElement(array, element) {\n      var iterator;\n      var result = false;\n\n      for (iterator = 0; iterator < array.length; iterator += 1) {\n        if (array[iterator] === element) {\n          result = true;\n        }\n      }\n      return result;\n    }\n\n    if (this.internal.viewerpreferences === undefined) {\n      this.internal.viewerpreferences = {};\n      this.internal.viewerpreferences.configuration = JSON.parse(\n        JSON.stringify(configurationTemplate)\n      );\n      this.internal.viewerpreferences.isSubscribed = false;\n    }\n   ", "metadata": {"token_count": 150, "start_token": 27000, "end_token": 27150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_225"}}, {"id": "enhanced_chunk_226", "content": ".internal.viewerpreferences.configuration = JSON.parse(\n        JSON.stringify(configurationTemplate)\n      );\n      this.internal.viewerpreferences.isSubscribed = false;\n    }\n    configuration = this.internal.viewerpreferences.configuration;\n\n    if (options === \"reset\" || doReset === true) {\n      var len = configurationKeys.length;\n\n      for (k = 0; k < len; k += 1) {\n        configuration[configurationKeys[k]].value =\n          configuration[configurationKeys[k]].defaultValue;\n        configuration[configurationKeys[k]].explicitSet = false;\n      }\n    }\n\n    if (typeof options === \"object\") {\n      for (method in options) {\n        value = options[method];\n        if (\n          arrayContainsElement(configurationKeys, method) &&\n", "metadata": {"token_count": 150, "start_token": 27120, "end_token": 27270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_226"}}, {"id": "enhanced_chunk_227", "content": "object\") {\n      for (method in options) {\n        value = options[method];\n        if (\n          arrayContainsElement(configurationKeys, method) &&\n          value !== undefined\n        ) {\n          if (\n            configuration[method].type === \"boolean\" &&\n            typeof value === \"boolean\"\n          ) {\n            configuration[method].value = value;\n          } else if (\n            configuration[method].type === \"name\" &&\n            arrayContainsElement(configuration[method].valueSet, value)\n          ) {\n            configuration[method].value = value;\n          } else if (\n            configuration[method].type === \"integer\" &&\n            Number.isInteger(value)\n          ) {\n            configuration[method].value = value;\n          } else if (configuration[method].type === \"", "metadata": {"token_count": 150, "start_token": 27240, "end_token": 27390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_227"}}, {"id": "enhanced_chunk_228", "content": "\" &&\n            Number.isInteger(value)\n          ) {\n            configuration[method].value = value;\n          } else if (configuration[method].type === \"array\") {\n            for (i = 0; i < value.length; i += 1) {\n              isValid = true;\n              if (value[i].length === 1 && typeof value[i][0] === \"number\") {\n                rangeArray.push(String(value[i] - 1));\n              } else if (value[i].length > 1) {\n                for (j = 0; j < value[i].length; j += 1) {\n                  if (typeof value[i][j] !== \"number\") {\n                    isValid = false;\n                  }\n                }\n               ", "metadata": {"token_count": 150, "start_token": 27360, "end_token": 27510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_228"}}, {"id": "enhanced_chunk_229", "content": " j += 1) {\n                  if (typeof value[i][j] !== \"number\") {\n                    isValid = false;\n                  }\n                }\n                if (isValid === true) {\n                  rangeArray.push([value[i][0] - 1, value[i][1] - 1].join(\" \"));\n                }\n              }\n            }\n            configuration[method].value = \"[\" + rangeArray.join(\" \") + \"]\";\n          } else {\n            configuration[method].value = configuration[method].defaultValue;\n          }\n\n          configuration[method].explicitSet = true;\n        }\n      }\n    }\n\n    if (this.internal.viewerpreferences.isSubscribed === false) {\n      this.internal.events.subscribe(\"putCatalog\", function() {\n        var pdfDict = [];\n        var", "metadata": {"token_count": 150, "start_token": 27480, "end_token": 27630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_229"}}, {"id": "enhanced_chunk_230", "content": ".internal.viewerpreferences.isSubscribed === false) {\n      this.internal.events.subscribe(\"putCatalog\", function() {\n        var pdfDict = [];\n        var vPref;\n        for (vPref in configuration) {\n          if (configuration[vPref].explicitSet === true) {\n            if (configuration[vPref].type === \"name\") {\n              pdfDict.push(\"/\" + vPref + \" /\" + configuration[vPref].value);\n            } else {\n              pdfDict.push(\"/\" + vPref + \" \" + configuration[vPref].value);\n            }\n          }\n        }\n        if (pdfDict.length !== 0) {\n          this.internal.write(\n            \"/ViewerPreferences\\n<<\\n\" + pdfDict.join(\"\\n\") +", "metadata": {"token_count": 150, "start_token": 27600, "end_token": 27750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_230"}}, {"id": "enhanced_chunk_231", "content": "pdfDict.length !== 0) {\n          this.internal.write(\n            \"/ViewerPreferences\\n<<\\n\" + pdfDict.join(\"\\n\") + \"\\n>>\"\n          );\n        }\n      });\n      this.internal.viewerpreferences.isSubscribed = true;\n    }\n\n    this.internal.viewerpreferences.configuration = configuration;\n    return this;\n  };\n})(jsPDF.API);\n\n\n// === outline.js ===\n\n/**\n * @license\n * Copyright (c) 2014 <PERSON> (TwelveTone LLC)  <EMAIL>\n *\n * Licensed under the MIT License.\n * http://opensource.org/licenses/mit-license\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF Outline PlugIn\n", "metadata": {"token_count": 150, "start_token": 27720, "end_token": 27870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_231"}}, {"id": "enhanced_chunk_232", "content": ".\n * http://opensource.org/licenses/mit-license\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF Outline PlugIn\n *\n * Generates a PDF Outline\n * @name outline\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n\n  var namesOid;\n  //var destsGoto = [];\n\n  jsPDFAPI.events.push([\n    \"postPutResources\",\n    function() {\n      var pdf = this;\n      var rx = /^(\\d+) 0 obj$/;\n\n      // Write action goto objects for each page\n      // this.outline.destsGoto = [];\n      // for (var i = 0; i < totalPages; i++) {\n     ", "metadata": {"token_count": 150, "start_token": 27840, "end_token": 27990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_232"}}, {"id": "enhanced_chunk_233", "content": "\n      // this.outline.destsGoto = [];\n      // for (var i = 0; i < totalPages; i++) {\n      // var id = pdf.internal.newObject();\n      // this.outline.destsGoto.push(id);\n      // pdf.internal.write(\"<</D[\" + (i * 2 + 3) + \" 0 R /XYZ null\n      // null null]/S/GoTo>> endobj\");\n      // }\n      //\n      // for (var i = 0; i < dests.length; i++) {\n      // pdf.internal.write(\"(page_\" + (i + 1) + \")\" + dests[i] + \" 0\n      // R\");\n      // }\n", "metadata": {"token_count": 150, "start_token": 27960, "end_token": 28110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_233"}}, {"id": "enhanced_chunk_234", "content": ".write(\"(page_\" + (i + 1) + \")\" + dests[i] + \" 0\n      // R\");\n      // }\n      //\n      if (this.outline.root.children.length > 0) {\n        var lines = pdf.outline.render().split(/\\r\\n/);\n        for (var i = 0; i < lines.length; i++) {\n          var line = lines[i];\n          var m = rx.exec(line);\n          if (m != null) {\n            var oid = m[1];\n            pdf.internal.newObjectDeferredBegin(oid, false);\n          }\n          pdf.internal.write(line);\n        }\n      }\n\n      // This code will write named destination for each page reference\n      // (page", "metadata": {"token_count": 150, "start_token": 28080, "end_token": 28230, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_234"}}, {"id": "enhanced_chunk_235", "content": ");\n          }\n          pdf.internal.write(line);\n        }\n      }\n\n      // This code will write named destination for each page reference\n      // (page_1, etc)\n      if (this.outline.createNamedDestinations) {\n        var totalPages = this.internal.pages.length;\n        // WARNING: this assumes jsPDF starts on page 3 and pageIDs\n        // follow 5, 7, 9, etc\n        // Write destination objects for each page\n        var dests = [];\n        for (var i = 0; i < totalPages; i++) {\n          var id = pdf.internal.newObject();\n          dests.push(id);\n          var info = pdf.internal.getPageInfo(i + 1);\n          pdf.internal.write", "metadata": {"token_count": 150, "start_token": 28200, "end_token": 28350, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_235"}}, {"id": "enhanced_chunk_236", "content": " id = pdf.internal.newObject();\n          dests.push(id);\n          var info = pdf.internal.getPageInfo(i + 1);\n          pdf.internal.write(\n            \"<< /D[\" + info.objId + \" 0 R /XYZ null null null]>> endobj\"\n          );\n        }\n\n        // assign a name for each destination\n        var names2Oid = pdf.internal.newObject();\n        pdf.internal.write(\"<< /Names [ \");\n        for (var i = 0; i < dests.length; i++) {\n          pdf.internal.write(\"(page_\" + (i + 1) + \")\" + dests[i] + \" 0 R\");\n        }\n        pdf.internal.write(\" ] >>\", \"endobj\");\n\n       ", "metadata": {"token_count": 150, "start_token": 28320, "end_token": 28470, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_236"}}, {"id": "enhanced_chunk_237", "content": "1) + \")\" + dests[i] + \" 0 R\");\n        }\n        pdf.internal.write(\" ] >>\", \"endobj\");\n\n        // var kids = pdf.internal.newObject();\n        // pdf.internal.write('<< /Kids [ ' + names2Oid + ' 0 R');\n        // pdf.internal.write(' ] >>', 'endobj');\n\n        namesOid = pdf.internal.newObject();\n        pdf.internal.write(\"<< /Dests \" + names2Oid + \" 0 R\");\n        pdf.internal.write(\">>\", \"endobj\");\n      }\n    }\n  ]);\n\n  jsPDFAPI.events.push([\n    \"putCatalog\",\n    function() {\n      var pdf = this;\n      if (pdf.out", "metadata": {"token_count": 150, "start_token": 28440, "end_token": 28590, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_237"}}, {"id": "enhanced_chunk_238", "content": "  ]);\n\n  jsPDFAPI.events.push([\n    \"putCatalog\",\n    function() {\n      var pdf = this;\n      if (pdf.outline.root.children.length > 0) {\n        pdf.internal.write(\n          \"/Outlines\",\n          this.outline.makeRef(this.outline.root)\n        );\n        if (this.outline.createNamedDestinations) {\n          pdf.internal.write(\"/Names \" + namesOid + \" 0 R\");\n        }\n        // Open with Bookmarks showing\n        // pdf.internal.write(\"/PageMode /UseOutlines\");\n      }\n    }\n  ]);\n\n  jsPDFAPI.events.push([\n    \"initialized\",\n    function() {\n      var pdf = this;\n\n      pdf.outline = {\n        create", "metadata": {"token_count": 150, "start_token": 28560, "end_token": 28710, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_238"}}, {"id": "enhanced_chunk_239", "content": "  jsPDFAPI.events.push([\n    \"initialized\",\n    function() {\n      var pdf = this;\n\n      pdf.outline = {\n        createNamedDestinations: false,\n        root: {\n          children: []\n        }\n      };\n\n      /**\n       * Options: pageNumber\n       */\n      pdf.outline.add = function(parent, title, options) {\n        var item = {\n          title: title,\n          options: options,\n          children: []\n        };\n        if (parent == null) {\n          parent = this.root;\n        }\n        parent.children.push(item);\n        return item;\n      };\n\n      pdf.outline.render = function() {\n        this.ctx = {};\n        this.ctx.val = \"\";\n        this.ctx.pdf = pdf;\n\n        this", "metadata": {"token_count": 150, "start_token": 28680, "end_token": 28830, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_239"}}, {"id": "enhanced_chunk_240", "content": " };\n\n      pdf.outline.render = function() {\n        this.ctx = {};\n        this.ctx.val = \"\";\n        this.ctx.pdf = pdf;\n\n        this.genIds_r(this.root);\n        this.renderRoot(this.root);\n        this.renderItems(this.root);\n\n        return this.ctx.val;\n      };\n\n      pdf.outline.genIds_r = function(node) {\n        node.id = pdf.internal.newObjectDeferred();\n        for (var i = 0; i < node.children.length; i++) {\n          this.genIds_r(node.children[i]);\n        }\n      };\n\n      pdf.outline.renderRoot = function(node) {\n        this.objStart(node);\n        this.line(\"/Type /Outlines\");\n        if (node.children.length > 0) {\n          this", "metadata": {"token_count": 150, "start_token": 28800, "end_token": 28950, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_240"}}, {"id": "enhanced_chunk_241", "content": ") {\n        this.objStart(node);\n        this.line(\"/Type /Outlines\");\n        if (node.children.length > 0) {\n          this.line(\"/First \" + this.makeRef(node.children[0]));\n          this.line(\n            \"/Last \" + this.makeRef(node.children[node.children.length - 1])\n          );\n        }\n        this.line(\n          \"/Count \" +\n            this.count_r(\n              {\n                count: 0\n              },\n              node\n            )\n        );\n        this.objEnd();\n      };\n\n      pdf.outline.renderItems = function(node) {\n        var getVerticalCoordinateString = this.ctx.pdf.internal\n          .getVerticalCoordinateString;\n        for (var i = 0; i < node.children.length", "metadata": {"token_count": 150, "start_token": 28920, "end_token": 29070, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_241"}}, {"id": "enhanced_chunk_242", "content": "VerticalCoordinateString = this.ctx.pdf.internal\n          .getVerticalCoordinateString;\n        for (var i = 0; i < node.children.length; i++) {\n          var item = node.children[i];\n          this.objStart(item);\n\n          this.line(\"/Title \" + this.makeString(item.title));\n\n          this.line(\"/Parent \" + this.makeRef(node));\n          if (i > 0) {\n            this.line(\"/Prev \" + this.makeRef(node.children[i - 1]));\n          }\n          if (i < node.children.length - 1) {\n            this.line(\"/Next \" + this.makeRef(node.children[i + 1]));\n          }\n          if (item.children.length > 0) {\n            this.line(\"/First \"", "metadata": {"token_count": 150, "start_token": 29040, "end_token": 29190, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_242"}}, {"id": "enhanced_chunk_243", "content": " + this.makeRef(node.children[i + 1]));\n          }\n          if (item.children.length > 0) {\n            this.line(\"/First \" + this.makeRef(item.children[0]));\n            this.line(\n              \"/Last \" + this.makeRef(item.children[item.children.length - 1])\n            );\n          }\n\n          var count = (this.count = this.count_r(\n            {\n              count: 0\n            },\n            item\n          ));\n          if (count > 0) {\n            this.line(\"/Count \" + count);\n          }\n\n          if (item.options) {\n            if (item.options.pageNumber) {\n              // Explicit Destination\n              //WARNING this assumes page ids are 3,5,7, etc.\n             ", "metadata": {"token_count": 150, "start_token": 29160, "end_token": 29310, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_243"}}, {"id": "enhanced_chunk_244", "content": " (item.options.pageNumber) {\n              // Explicit Destination\n              //WARNING this assumes page ids are 3,5,7, etc.\n              var info = pdf.internal.getPageInfo(item.options.pageNumber);\n              this.line(\n                \"/Dest \" +\n                  \"[\" +\n                  info.objId +\n                  \" 0 R /XYZ 0 \" +\n                  getVerticalCoordinateString(0) +\n                  \" 0]\"\n              );\n              // this line does not work on all clients (pageNumber instead of page ref)\n              //this.line('/Dest ' + '[' + (item.options.pageNumber - 1) + ' /XYZ 0 ' + this.ctx.pdf.internal.pageSize.getHeight() + ' 0]');\n\n              // Named Destination\n              //", "metadata": {"token_count": 150, "start_token": 29280, "end_token": 29430, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_244"}}, {"id": "enhanced_chunk_245", "content": "1) + ' /XYZ 0 ' + this.ctx.pdf.internal.pageSize.getHeight() + ' 0]');\n\n              // Named Destination\n              // this.line('/Dest (page_' + (item.options.pageNumber) + ')');\n\n              // Action Destination\n              // var id = pdf.internal.newObject();\n              // pdf.internal.write('<</D[' + (item.options.pageNumber - 1) + ' /XYZ null null null]/S/GoTo>> endobj');\n              // this.line('/A ' + id + ' 0 R' );\n            }\n          }\n          this.objEnd();\n        }\n        for (var z = 0; z < node.children.length; z++) {\n          this.renderItems(node.children[z]);\n", "metadata": {"token_count": 150, "start_token": 29400, "end_token": 29550, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_245"}}, {"id": "enhanced_chunk_246", "content": "End();\n        }\n        for (var z = 0; z < node.children.length; z++) {\n          this.renderItems(node.children[z]);\n        }\n      };\n\n      pdf.outline.line = function(text) {\n        this.ctx.val += text + \"\\r\\n\";\n      };\n\n      pdf.outline.makeRef = function(node) {\n        return node.id + \" 0 R\";\n      };\n\n      pdf.outline.makeString = function(val) {\n        return \"(\" + pdf.internal.pdfEscape(val) + \")\";\n      };\n\n      pdf.outline.objStart = function(node) {\n        this.ctx.val += \"\\r\\n\" + node.id + \" 0 obj\" + \"\\r\\n<<\\r\\n\";\n      };\n\n      pdf.outline.obj", "metadata": {"token_count": 150, "start_token": 29520, "end_token": 29670, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_246"}}, {"id": "enhanced_chunk_247", "content": " += \"\\r\\n\" + node.id + \" 0 obj\" + \"\\r\\n<<\\r\\n\";\n      };\n\n      pdf.outline.objEnd = function() {\n        this.ctx.val += \">> \\r\\n\" + \"endobj\" + \"\\r\\n\";\n      };\n\n      pdf.outline.count_r = function(ctx, node) {\n        for (var i = 0; i < node.children.length; i++) {\n          ctx.count++;\n          this.count_r(ctx, node.children[i]);\n        }\n        return ctx.count;\n      };\n    }\n  ]);\n\n  return this;\n})(jsPDF.API);\n\n\n// === jpeg_support.js ===\n\n/**\n * @license\n *\n * Licensed under the MIT License.\n * http://opensource.org", "metadata": {"token_count": 150, "start_token": 29640, "end_token": 29790, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_247"}}, {"id": "enhanced_chunk_248", "content": "})(jsPDF.API);\n\n\n// === jpeg_support.js ===\n\n/**\n * @license\n *\n * Licensed under the MIT License.\n * http://opensource.org/licenses/mit-license\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF jpeg Support PlugIn\n *\n * @name jpeg_support\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n\n  /**\n   * 0xc0 (SOF) <PERSON><PERSON>man  - Baseline DCT\n   * 0xc1 (SOF) <PERSON><PERSON><PERSON>  - Extended sequential DCT\n   * 0xc2 Progressive DCT (SOF2)\n   * 0xc3 Spatial (sequential) lossless (SOF3)\n   *", "metadata": {"token_count": 150, "start_token": 29760, "end_token": 29910, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_248"}}, {"id": "enhanced_chunk_249", "content": "0xc2 Progressive DCT (SOF2)\n   * 0xc3 Spatial (sequential) lossless (SOF3)\n   * 0xc4 Differential sequential DCT (SOF5)\n   * 0xc5 Differential progressive DCT (SOF6)\n   * 0xc6 Differential spatial (SOF7)\n   * 0xc7\n   */\n  var markers = [0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7];\n\n  //takes a string imgData containing the raw bytes of\n  //a jpeg image and returns [width, height]\n ", "metadata": {"token_count": 150, "start_token": 29880, "end_token": 30030, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_249"}}, {"id": "enhanced_chunk_250", "content": "0xc7];\n\n  //takes a string imgData containing the raw bytes of\n  //a jpeg image and returns [width, height]\n  //Algorithm from: http://www.64lines.com/jpeg-width-height\n  var getJpegInfo = function(imgData) {\n    var width, height, numcomponents;\n    var blockLength = imgData.charCodeAt(4) * 256 + imgData.charCodeAt(5);\n    var len = imgData.length;\n    var result = { width: 0, height: 0, numcomponents: 1 };\n    for (var i = 4; i < len; i += 2) {\n      i += blockLength;\n      if (markers.indexOf(imgData.charCodeAt(i", "metadata": {"token_count": 150, "start_token": 30000, "end_token": 30150, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_250"}}, {"id": "enhanced_chunk_251", "content": " i = 4; i < len; i += 2) {\n      i += blockLength;\n      if (markers.indexOf(imgData.charCodeAt(i + 1)) !== -1) {\n        height = imgData.charCodeAt(i + 5) * 256 + imgData.charCodeAt(i + 6);\n        width = imgData.charCodeAt(i + 7) * 256 + imgData.charCodeAt(i + 8);\n        numcomponents = imgData.charCodeAt(i + 9);\n        result = { width: width, height: height, numcomponents: numcomponents };\n        break;\n      } else {\n        blockLength =\n          imgData.charCodeAt(i + 2) * 256 + imgData.charCodeAt(i + 3);\n      }\n   ", "metadata": {"token_count": 150, "start_token": 30120, "end_token": 30270, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_251"}}, {"id": "enhanced_chunk_252", "content": " else {\n        blockLength =\n          imgData.charCodeAt(i + 2) * 256 + imgData.charCodeAt(i + 3);\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @ignore\n   */\n  jsPDFAPI.processJPEG = function(\n    data,\n    index,\n    alias,\n    compression,\n    dataAsBinaryString,\n    colorSpace\n  ) {\n    var filter = this.decode.DCT_DECODE,\n      bpc = 8,\n      dims,\n      result = null;\n\n    if (\n      typeof data === \"string\" ||\n      this.__addimage__.isArrayBuffer(data) ||\n      this.__addimage__.isArrayBufferView(data)\n    ) {\n      // if we already have a", "metadata": {"token_count": 150, "start_token": 30240, "end_token": 30390, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_252"}}, {"id": "enhanced_chunk_253", "content": ".__addimage__.isArray<PERSON>uffer(data) ||\n      this.__addimage__.isArrayBufferView(data)\n    ) {\n      // if we already have a stored binary string rep use that\n      data = dataAsBinaryString || data;\n      data = this.__addimage__.isArrayBuffer(data)\n        ? new Uint8Array(data)\n        : data;\n      data = this.__addimage__.isArrayBufferView(data)\n        ? this.__addimage__.arrayBufferToBinaryString(data)\n        : data;\n\n      dims = getJpegInfo(data);\n      switch (dims.numcomponents) {\n        case 1:\n          colorSpace = this.color_spaces.DEVICE_GRAY;\n          break;\n        case 4:\n          colorSpace = this.color_spaces", "metadata": {"token_count": 150, "start_token": 30360, "end_token": 30510, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_253"}}, {"id": "enhanced_chunk_254", "content": " case 1:\n          colorSpace = this.color_spaces.DEVICE_GRAY;\n          break;\n        case 4:\n          colorSpace = this.color_spaces.DEVICE_CMYK;\n          break;\n        case 3:\n          colorSpace = this.color_spaces.DEVICE_RGB;\n          break;\n      }\n\n      result = {\n        data: data,\n        width: dims.width,\n        height: dims.height,\n        colorSpace: colorSpace,\n        bitsPerComponent: bpc,\n        filter: filter,\n        index: index,\n        alias: alias\n      };\n    }\n    return result;\n  };\n})(jsPDF.API);\n\n\n// === javascript.js ===\n\n/**\n * @license\n * ====================================================================\n * Copyright (c) 2013 Yousse", "metadata": {"token_count": 150, "start_token": 30480, "end_token": 30630, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_254"}}, {"id": "enhanced_chunk_255", "content": " };\n})(jsPDF.API);\n\n\n// === javascript.js ===\n\n/**\n * @license\n * ====================================================================\n * Copyright (c) 2013 Youssef <PERSON>, <EMAIL>\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall", "metadata": {"token_count": 150, "start_token": 30600, "end_token": 30750, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_255"}}, {"id": "enhanced_chunk_256", "content": " persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n * ====================================================================\n */\n\nimport", "metadata": {"token_count": 150, "start_token": 30720, "end_token": 30870, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_256"}}, {"id": "enhanced_chunk_257", "content": " OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n * ====================================================================\n */\n\nimport { jsPDF } from \"../jspdf.js\";\n\n/**\n * jsPDF JavaScript plugin\n *\n * @name javascript\n * @module\n */\n(function(jsPDFAPI) {\n  \"use strict\";\n  var jsNamesObj, jsJsObj, text;\n  /**\n   * @name addJS\n   * @function\n   * @param {string} javascript The javascript to be embedded into the PDF-file.\n   * @returns {jsPDF}\n   */\n  jsPDFAPI.addJS = function(javascript) {\n    text = javascript;\n    this.internal.events.subscribe(\"postPut", "metadata": {"token_count": 150, "start_token": 30840, "end_token": 30990, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_257"}}, {"id": "enhanced_chunk_258", "content": "PDF}\n   */\n  jsPDFAPI.addJS = function(javascript) {\n    text = javascript;\n    this.internal.events.subscribe(\"postPutResources\", function() {\n      jsNamesObj = this.internal.newObject();\n      this.internal.out(\"<<\");\n      this.internal.out(\"/Names [(EmbeddedJS) \" + (jsNamesObj + 1) + \" 0 R]\");\n      this.internal.out(\">>\");\n      this.internal.out(\"endobj\");\n\n      jsJsObj = this.internal.newObject();\n      this.internal.out(\"<<\");\n      this.internal.out(\"/S /JavaScript\");\n      this.internal.out(\"/JS (\" + text + \")\");\n      this.internal.out(\">>\");\n      this.internal.out(\"endobj\");\n    });\n    this.internal", "metadata": {"token_count": 150, "start_token": 30960, "end_token": 31110, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_258"}}, {"id": "enhanced_chunk_259", "content": " this.internal.out(\"/JS (\" + text + \")\");\n      this.internal.out(\">>\");\n      this.internal.out(\"endobj\");\n    });\n    this.internal.events.subscribe(\"putCatalog\", function() {\n      if (jsNamesObj !== undefined && jsJsObj !== undefined) {\n        this.internal.out(\"/Names <</JavaScript \" + jsNamesObj + \" 0 R>>\");\n      }\n    });\n    return this;\n  };\n})(jsPDF.API);\n", "metadata": {"token_count": 91, "start_token": 31080, "end_token": 31171, "chunk_type": "enhanced", "original_chunk_id": "direct_chunk_259"}}]