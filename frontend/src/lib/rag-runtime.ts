import { useExternalStoreRuntime } from "@assistant-ui/react";
import { type ChatModelAdapter, type ThreadMessage } from "@assistant-ui/react";

interface RAGMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  metadata?: any;
}

interface ConversationState {
  messages: ThreadMessage[];
  isRunning: boolean;
  projectId: string | null;
  conversationId: string | null;
}

class RAGRuntimeStore {
  private state: ConversationState = {
    messages: [],
    isRunning: false,
    projectId: null,
    conversationId: null,
  };
  
  private listeners = new Set<() => void>();
  private abortController: AbortController | null = null;

  constructor() {
    this.loadInitialState();
  }

  private async loadInitialState() {
    // Load current project from localStorage or URL
    const projectId = localStorage.getItem('currentProjectId');
    if (projectId) {
      this.state.projectId = projectId;
      await this.loadProjectConversations();
    }
  }

  private async loadProjectConversations() {
    if (!this.state.projectId) return;

    try {
      const response = await fetch(`/api/projects/${this.state.projectId}/conversations`);
      const data = await response.json();
      
      // If there are existing conversations, load the most recent one
      if (data.conversations && data.conversations.length > 0) {
        const mostRecent = data.conversations[0];
        await this.loadConversation(mostRecent.id);
      }
    } catch (error) {
      console.error('Failed to load project conversations:', error);
    }
  }

  async setProject(projectId: string) {
    this.state.projectId = projectId;
    this.state.conversationId = null;
    this.state.messages = [];
    localStorage.setItem('currentProjectId', projectId);
    
    await this.loadProjectConversations();
    this.notifyListeners();
  }

  async createNewConversation() {
    if (!this.state.projectId) return;

    try {
      const response = await fetch(`/api/projects/${this.state.projectId}/conversations`, {
        method: 'POST',
      });
      const data = await response.json();
      
      this.state.conversationId = data.conversation_id;
      this.state.messages = [];
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  }

  async loadConversation(conversationId: string) {
    if (!this.state.projectId) return;

    try {
      const response = await fetch(`/api/projects/${this.state.projectId}/conversations/${conversationId}`);
      const data = await response.json();
      
      this.state.conversationId = conversationId;
      this.state.messages = this.convertRAGMessagesToThreadMessages(data.messages || []);
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to load conversation:', error);
    }
  }

  async deleteConversation(conversationId: string) {
    if (!this.state.projectId) return;

    try {
      await fetch(`/api/projects/${this.state.projectId}/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      
      // If this was the current conversation, clear it
      if (this.state.conversationId === conversationId) {
        this.state.conversationId = null;
        this.state.messages = [];
        this.notifyListeners();
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  }

  private convertRAGMessagesToThreadMessages(ragMessages: RAGMessage[]): ThreadMessage[] {
    return ragMessages.map((msg, index) => ({
      id: msg.id,
      role: msg.role,
      content: [
        {
          type: "text" as const,
          text: msg.content,
        },
      ],
      createdAt: new Date(msg.timestamp),
    }));
  }

  private convertThreadMessageToRAGMessage(message: ThreadMessage): string {
    return message.content
      .filter((part) => part.type === "text")
      .map((part) => part.text)
      .join("\n");
  }

  async sendMessage(message: string) {
    if (!this.state.projectId) {
      throw new Error("No project selected");
    }

    // Create conversation if needed
    if (!this.state.conversationId) {
      await this.createNewConversation();
    }

    // Add user message
    const userMessage: ThreadMessage = {
      id: `user-${Date.now()}`,
      role: "user",
      content: [{ type: "text", text: message }],
      createdAt: new Date(),
    };

    this.state.messages = [...this.state.messages, userMessage];
    this.state.isRunning = true;
    this.notifyListeners();

    // Create abort controller for this request
    this.abortController = new AbortController();

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          project_id: this.state.projectId,
          conversation_id: this.state.conversationId,
          model: "claude-3-5-sonnet-20241022",
          max_chunks: 5,
        }),
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Add assistant message
      const assistantMessage: ThreadMessage = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: [{ type: "text", text: data.answer }],
        createdAt: new Date(),
      };

      this.state.messages = [...this.state.messages, assistantMessage];
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        // Request was cancelled
        return;
      }
      
      console.error("Failed to send message:", error);
      
      // Add error message
      const errorMessage: ThreadMessage = {
        id: `error-${Date.now()}`,
        role: "assistant",
        content: [{ type: "text", text: "Sorry, I encountered an error. Please try again." }],
        createdAt: new Date(),
      };

      this.state.messages = [...this.state.messages, errorMessage];
    } finally {
      this.state.isRunning = false;
      this.abortController = null;
      this.notifyListeners();
    }
  }

  cancelMessage() {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
      this.state.isRunning = false;
      this.notifyListeners();
    }
  }

  getState() {
    return this.state;
  }

  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener());
  }
}

// Global store instance
const ragStore = new RAGRuntimeStore();

export function useRAGRuntime() {
  const adapter: ChatModelAdapter = {
    async run({ messages, abortSignal }) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage?.role !== "user") {
        throw new Error("Last message must be from user");
      }

      const content = lastMessage.content
        .filter((part) => part.type === "text")
        .map((part) => part.text)
        .join("\n");

      await ragStore.sendMessage(content);

      return {
        content: [{ type: "text", text: "" }], // Content is handled by the store
      };
    },
  };

  return useExternalStoreRuntime({
    store: ragStore,
    adapters: {
      chatModel: adapter,
    },
  });
}

// Export store for direct access
export { ragStore };