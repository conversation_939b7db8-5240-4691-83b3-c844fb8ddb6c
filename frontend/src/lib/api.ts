// API client for the RAG backend
const API_BASE_URL = 'http://localhost:8000/api';

export interface Project {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  files: Array<any>;
  total_chunks: number;
  total_tokens: number;
  total_chars: number;
  indexed: boolean;
  chunk_count?: number;
  enhanced_chunks?: boolean;
  processing_status?: string; // idle, processing, chunking, indexing, ready, error
}

export interface ProjectStatus {
  project_id: string;
  processing_status: string;
  chunk_count: number;
  indexed: boolean;
  files_count: number;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface ChatResponse {
  answer: string;
  sources: Array<{
    content: string;
    metadata: {
      filename: string;
      chunk_index: number;
      function_name?: string;
    };
  }>;
  conversation_id: string;
  model_used: string;
  tokens_used?: number;
}

class RAGApiClient {
  async getProjects(): Promise<Project[]> {
    const response = await fetch(`${API_BASE_URL}/projects`);
    if (!response.ok) {
      throw new Error('Failed to fetch projects');
    }
    return response.json();
  }

  async createProject(name: string): Promise<Project> {
    const response = await fetch(`${API_BASE_URL}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name }),
    });
    if (!response.ok) {
      throw new Error('Failed to create project');
    }
    return response.json();
  }

  async uploadFile(projectId: string, file: File): Promise<{ message: string; chunks_created: number }> {
    const formData = new FormData();
    formData.append('files', file);

    const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {
      method: 'POST',
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to upload file');
    }
    return response.json();
  }

  async uploadFiles(projectId: string, files: File[]): Promise<{ uploaded_files: any[] }> {
    const formData = new FormData();
    
    // Append all files with the same field name 'files'
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload files');
    }
    return response.json();
  }

  async chat(projectId: string, message: string, history: ChatMessage[] = []): Promise<ChatResponse> {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: projectId,
        message: message,
        conversation_id: null, // Let backend create new conversation
        model: "claude-3-5-sonnet-20250106",
        max_chunks: 5,
      }),
    });
    if (!response.ok) {
      throw new Error('Failed to send chat message');
    }
    return response.json();
  }

  async deleteProject(projectId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/projects/${projectId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error('Failed to delete project');
    }
  }

  async getProjectStatus(projectId: string): Promise<ProjectStatus> {
    const response = await fetch(`${API_BASE_URL}/projects/${projectId}/status`);
    
    if (!response.ok) {
      if (response.status === 404) {
        // Project might be temporarily unavailable during indexing
        // Return a default status indicating processing
        return {
          project_id: projectId,
          processing_status: 'processing',
          chunk_count: 0,
          indexed: false,
          files_count: 0
        };
      }
      throw new Error(`Failed to get project status: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async processProject(projectId: string): Promise<{ message: string; project_id: string }> {
    const response = await fetch(`${API_BASE_URL}/projects/${projectId}/process`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to start project processing');
    }

    return response.json();
  }
}

export const apiClient = new RAGApiClient();