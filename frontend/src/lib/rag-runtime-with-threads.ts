import { useState, useCallback, useMemo } from 'react';
import {
  useExternalStoreRuntime,
  ThreadMessageLike,
  AppendMessage,
  ExternalStoreThreadListAdapter,
} from '@assistant-ui/react';

interface RAGMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

interface ConversationData {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}

export function useRAGRuntimeWithThreads(projectId: string) {
  const [currentThreadId, setCurrentThreadId] = useState<string>('default');
  const [threads, setThreads] = useState<Map<string, ThreadMessageLike[]>>(
    new Map([['default', []]])
  );
  const [threadList, setThreadList] = useState<Array<{ threadId: string; status: 'regular' | 'archived'; title: string }>>([
    { threadId: 'default', status: 'regular', title: 'New Chat' }
  ]);
  const [isRunning, setIsRunning] = useState(false);

  // Get messages for current thread
  const currentMessages = threads.get(currentThreadId) || [];

  // Convert RAG message to ThreadMessageLike
  const convertRAGMessage = useCallback((message: RAGMessage): ThreadMessageLike => {
    // Ensure content is properly structured
    if (!message.content || typeof message.content !== 'string') {
      console.error('Invalid message content detected:', message);
      return {
        role: message.role,
        content: [{ type: 'text', text: 'Invalid message content' }],
        id: `msg-${Date.now()}-${Math.random()}`,
        createdAt: new Date(),
      };
    }

    // Additional safety check for empty content
    const safeContent = (message.content && typeof message.content === 'string')
      ? (message.content.trim ? message.content.trim() || 'Empty message' : message.content || 'Empty message')
      : 'Empty message';

    // Handle timestamp conversion safely
    let createdAt = new Date();
    if (message.timestamp) {
      try {
        // Handle both string and number timestamps
        const timestamp = typeof message.timestamp === 'string'
          ? parseFloat(message.timestamp)
          : message.timestamp;
        
        // Convert from seconds to milliseconds if needed
        const timestampMs = timestamp < 1e12 ? timestamp * 1000 : timestamp;
        createdAt = new Date(timestampMs);
        
        // Validate the date
        if (isNaN(createdAt.getTime())) {
          createdAt = new Date();
        }
      } catch (error) {
        console.warn('Invalid timestamp:', message.timestamp, error);
        createdAt = new Date();
      }
    }

    return {
      role: message.role,
      content: [{ type: 'text', text: safeContent }],
      id: `msg-${Date.now()}-${Math.random()}`,
      createdAt,
    };
  }, []);

  // Load conversations from backend
  const loadConversations = useCallback(async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/projects/${projectId}/conversations`);
      const data = await response.json();
      
      if (data.conversations) {
        const newThreadList = data.conversations.map((conv: ConversationData) => ({
          threadId: conv.id,
          status: 'regular' as const,
          title: conv.title,
        }));
        
        setThreadList(newThreadList);
        
        // Load messages for each conversation
        const newThreads = new Map<string, ThreadMessageLike[]>();
        for (const conv of data.conversations) {
          try {
            const messagesResponse = await fetch(
              `http://localhost:8000/api/projects/${projectId}/conversations/${conv.id}`
            );
            const messagesData = await messagesResponse.json();
            
            if (messagesData.messages) {
              const threadMessages = messagesData.messages.map(convertRAGMessage);
              newThreads.set(conv.id, threadMessages);
            }
          } catch (error) {
            console.error(`Failed to load messages for conversation ${conv.id}:`, error);
            newThreads.set(conv.id, []);
          }
        }
        
        setThreads(newThreads);
      }
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  }, [projectId, convertRAGMessage]);

  // Create new conversation
  const createNewConversation = useCallback(async (): Promise<string> => {
    try {
      const response = await fetch(`http://localhost:8000/api/projects/${projectId}/conversations`, {
        method: 'POST',
      });
      const data = await response.json();
      return data.conversation_id;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      return `thread-${Date.now()}`;
    }
  }, [projectId]);

  // Send message to RAG backend
  const sendMessage = useCallback(async (message: AppendMessage, threadId: string) => {
    if (message.content[0]?.type !== 'text') {
      throw new Error('Only text messages are supported');
    }

    const userContent = message.content[0]?.text;
    if (!userContent || typeof userContent !== 'string') {
      throw new Error('Message content is required and must be a string');
    }
    
    try {
      const response = await fetch('http://localhost:8000/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userContent,
          project_id: projectId,
          conversation_id: threadId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }, [projectId]);

  // ThreadList adapter
  const threadListAdapter: ExternalStoreThreadListAdapter = useMemo(() => ({
    threadId: currentThreadId,
    threads: threadList.filter((t) => t.status === 'regular').map(t => ({
      threadId: t.threadId,
      title: t.title,
      status: 'regular' as const,
    })),
    archivedThreads: threadList.filter((t) => t.status === 'archived').map(t => ({
      threadId: t.threadId,
      title: t.title,
      status: 'archived' as const,
    })),

    onSwitchToNewThread: async () => {
      const newId = await createNewConversation();
      setThreadList((prev) => [
        ...prev,
        {
          threadId: newId,
          status: 'regular',
          title: 'New Chat',
        },
      ]);
      setThreads((prev) => new Map(prev).set(newId, []));
      setCurrentThreadId(newId);
    },

    onSwitchToThread: (threadId) => {
      setCurrentThreadId(threadId);
    },

    onRename: async (threadId, newTitle) => {
      try {
        const formData = new FormData();
        formData.append('title', newTitle);
        
        await fetch(`http://localhost:8000/api/projects/${projectId}/conversations/${threadId}/rename`, {
          method: 'PUT',
          body: formData,
        });
        
        setThreadList((prev) =>
          prev.map((t) =>
            t.threadId === threadId ? { ...t, title: newTitle } : t
          )
        );
      } catch (error) {
        console.error('Failed to rename conversation:', error);
      }
    },

    onArchive: async (threadId) => {
      try {
        await fetch(`http://localhost:8000/api/projects/${projectId}/conversations/${threadId}/archive`, {
          method: 'PUT',
        });
        
        setThreadList((prev) =>
          prev.map((t) =>
            t.threadId === threadId ? { ...t, status: 'archived' } : t
          )
        );
      } catch (error) {
        console.error('Failed to archive conversation:', error);
      }
    },

    onDelete: async (threadId) => {
      try {
        await fetch(`http://localhost:8000/api/projects/${projectId}/conversations/${threadId}`, {
          method: 'DELETE',
        });
        
        setThreadList((prev) => prev.filter((t) => t.threadId !== threadId));
        setThreads((prev) => {
          const next = new Map(prev);
          next.delete(threadId);
          return next;
        });
        
        if (currentThreadId === threadId) {
          const remainingThreads = threadList.filter(t => t.threadId !== threadId);
          if (remainingThreads.length > 0) {
            setCurrentThreadId(remainingThreads[0].threadId);
          } else {
            // Create a new default thread
            const newId = await createNewConversation();
            setCurrentThreadId(newId);
            setThreadList([{
              threadId: newId,
              status: 'regular',
              title: 'New Chat',
            }]);
            setThreads(new Map([[newId, []]]));
          }
        }
      } catch (error) {
        console.error('Failed to delete conversation:', error);
      }
    },
  }), [currentThreadId, threadList, projectId, createNewConversation]);

  // Handle new messages
  const onNew = useCallback(async (message: AppendMessage) => {
    // Add user message
    const userMessage: ThreadMessageLike = {
      role: 'user',
      content: message.content,
      id: `user-${Date.now()}`,
      createdAt: new Date(),
    };

    setThreads((prev) => {
      const updated = new Map(prev);
      const currentMessages = updated.get(currentThreadId) || [];
      updated.set(currentThreadId, [...currentMessages, userMessage]);
      return updated;
    });

    // Update thread title if it's the first message
    if (currentMessages.length === 0 && message.content[0]?.type === 'text') {
      const messageText = message.content[0]?.text;
      if (messageText && typeof messageText === 'string') {
        const title = messageText.slice(0, 50) +
          (messageText.length > 50 ? '...' : '');
        
        setThreadList((prev) =>
          prev.map((t) =>
            t.threadId === currentThreadId ? { ...t, title } : t
          )
        );
      }
    }

    // Generate response
    setIsRunning(true);
    try {
      const response = await sendMessage(message, currentThreadId);
      
      const assistantMessage: ThreadMessageLike = {
        role: 'assistant',
        content: [{ type: 'text', text: response }],
        id: `assistant-${Date.now()}`,
        createdAt: new Date(),
      };

      setThreads((prev) => {
        const updated = new Map(prev);
        const currentMessages = updated.get(currentThreadId) || [];
        updated.set(currentThreadId, [...currentMessages, assistantMessage]);
        return updated;
      });
    } catch (error) {
      console.error('Failed to get response:', error);
      // Add error message
      const errorMessage: ThreadMessageLike = {
        role: 'assistant',
        content: [{ type: 'text', text: 'Sorry, I encountered an error processing your request.' }],
        id: `error-${Date.now()}`,
        createdAt: new Date(),
      };

      setThreads((prev) => {
        const updated = new Map(prev);
        const currentMessages = updated.get(currentThreadId) || [];
        updated.set(currentThreadId, [...currentMessages, errorMessage]);
        return updated;
      });
    } finally {
      setIsRunning(false);
    }
  }, [currentThreadId, currentMessages.length, sendMessage]);

  // Create the runtime - since we're using ThreadMessageLike directly, no conversion needed
  const runtime = useExternalStoreRuntime({
    messages: currentMessages,
    setMessages: (messages) => {
      setThreads((prev) => new Map(prev).set(currentThreadId, messages));
    },
    isRunning,
    onNew,
    convertMessage: (message) => message, // No conversion needed since we're already using ThreadMessageLike
    adapters: {
      threadList: threadListAdapter,
    },
  });

  return {
    runtime,
    loadConversations,
    currentThreadId,
    threadList,
  };
}