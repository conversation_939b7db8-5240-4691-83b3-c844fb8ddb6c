"use client";

import { useState } from "react";
import { ProjectManager } from "@/components/ProjectManager";
import { RAGChatWithThreads } from "@/components/RAGChatWithThreads";
import { ProcessingStatus } from "@/components/ProcessingStatus";
import { Project } from "@/lib/api";

export default function Home() {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <ProjectManager
          selectedProject={selectedProject}
          onProjectSelect={handleProjectSelect}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {selectedProject && selectedProject.id ? (
          <>
            {selectedProject.processing_status === 'ready' || selectedProject.indexed ? (
              <RAGChatWithThreads projectId={selectedProject.id} />
            ) : (
              <ProcessingStatus project={selectedProject} />
            )}
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-white">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No project selected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Select a project from the sidebar to start chatting about your code.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
