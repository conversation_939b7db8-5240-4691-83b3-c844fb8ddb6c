'use client';

import React, { useEffect } from 'react';
import { AssistantRuntimeProvider } from '@assistant-ui/react';
import { Thread } from '@/components/assistant-ui/thread';
import { ThreadList } from '@/components/assistant-ui/thread-list';
import { useRAGRuntimeWithThreads } from '@/lib/rag-runtime-with-threads';

interface RAGChatWithThreadsProps {
  projectId: string;
}

export function RAGChatWithThreads({ projectId }: RAGChatWithThreadsProps) {
  const { runtime, loadConversations } = useRAGRuntimeWithThreads(projectId);

  // Load conversations when component mounts
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="flex h-full">
        {/* ThreadList Sidebar */}
        <div className="w-80 border-r border-gray-200 bg-gray-50 p-4">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
            <p className="text-sm text-gray-600">Manage your chat history</p>
          </div>
          <ThreadList />
        </div>
        
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          <div className="border-b border-gray-200 bg-white px-6 py-4">
            <h1 className="text-xl font-semibold text-gray-900">RAG Code Analysis Chat</h1>
            <p className="text-sm text-gray-600">Ask questions about your code and get intelligent responses powered by RAG</p>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <Thread />
          </div>
        </div>
      </div>
    </AssistantRuntimeProvider>
  );
}