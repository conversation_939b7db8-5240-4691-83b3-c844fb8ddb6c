"use client";

import { useState, useEffect, useRef } from "react";
import { Send, Upload, Trash2, Plus } from "lucide-react";
import { apiClient, Project, ChatMessage } from "@/lib/api";

interface RAGMessage extends ChatMessage {
  id: string;
  timestamp: Date;
  sources?: Array<{
    content: string;
    metadata: {
      filename: string;
      chunk_index: number;
      function_name?: string;
    };
  }>;
}

export default function RAGChat() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [messages, setMessages] = useState<RAGMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [newProjectName, setNewProjectName] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadProjects();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadProjects = async () => {
    try {
      const projectList = await apiClient.getProjects();
      setProjects(projectList);
      if (projectList.length > 0 && !selectedProject) {
        setSelectedProject(projectList[0]);
      }
    } catch (error) {
      console.error("Failed to load projects:", error);
    }
  };

  const createProject = async () => {
    if (!newProjectName.trim()) return;
    
    try {
      const project = await apiClient.createProject(newProjectName);
      setProjects([...projects, project]);
      setSelectedProject(project);
      setNewProjectName("");
      setIsCreatingProject(false);
    } catch (error) {
      console.error("Failed to create project:", error);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !selectedProject) return;

    try {
      setIsLoading(true);
      const result = await apiClient.uploadFile(selectedProject.id, file);
      
      // Add system message about successful upload
      const systemMessage: RAGMessage = {
        id: `system-${Date.now()}`,
        role: "assistant",
        content: `✅ Successfully uploaded ${file.name} and created ${result.chunks_created} code chunks. You can now ask questions about this code!`,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, systemMessage]);
      
      // Refresh project list to update chunk count
      await loadProjects();
    } catch (error) {
      console.error("Failed to upload file:", error);
      const errorMessage: RAGMessage = {
        id: `error-${Date.now()}`,
        role: "assistant",
        content: `❌ Failed to upload ${file.name}. Please try again.`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !selectedProject || isLoading) return;

    const userMessage: RAGMessage = {
      id: `user-${Date.now()}`,
      role: "user",
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    try {
      const history: ChatMessage[] = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      const response = await apiClient.chat(selectedProject.id, inputMessage, history);
      
      const assistantMessage: RAGMessage = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: response.response,
        timestamp: new Date(),
        sources: response.sources,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Failed to send message:", error);
      const errorMessage: RAGMessage = {
        id: `error-${Date.now()}`,
        role: "assistant",
        content: "Sorry, I encountered an error while processing your request. Please try again.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  const deleteProject = async (projectId: string) => {
    try {
      await apiClient.deleteProject(projectId);
      const updatedProjects = projects.filter(p => p.id !== projectId);
      setProjects(updatedProjects);
      
      if (selectedProject?.id === projectId) {
        setSelectedProject(updatedProjects[0] || null);
        setMessages([]);
      }
    } catch (error) {
      console.error("Failed to delete project:", error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900">RAG Code Assistant</h1>
          <p className="text-sm text-gray-600 mt-1">Intelligent code analysis with AI</p>
        </div>

        {/* Project Creation */}
        <div className="p-4 border-b border-gray-200">
          {isCreatingProject ? (
            <div className="space-y-2">
              <input
                type="text"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                placeholder="Project name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                onKeyPress={(e) => e.key === "Enter" && createProject()}
              />
              <div className="flex gap-2">
                <button
                  onClick={createProject}
                  className="flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  Create
                </button>
                <button
                  onClick={() => setIsCreatingProject(false)}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setIsCreatingProject(true)}
              className="w-full flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
            >
              <Plus size={16} />
              New Project
            </button>
          )}
        </div>

        {/* Projects List */}
        <div className="flex-1 overflow-y-auto">
          {projects.map((project) => (
            <div
              key={project.id}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedProject?.id === project.id ? "bg-blue-50 border-blue-200" : ""
              }`}
              onClick={() => {
                setSelectedProject(project);
                setMessages([]);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 truncate">{project.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {project.file_count} files • {project.chunk_count} chunks
                  </p>
                  <p className="text-xs text-gray-400">
                    {new Date(project.created_at).toLocaleDateString()}
                  </p>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteProject(project.id);
                  }}
                  className="p-1 text-gray-400 hover:text-red-600"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedProject ? (
          <>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
              <div>
                <h2 className="font-semibold text-gray-900">{selectedProject.name}</h2>
                <p className="text-sm text-gray-600">
                  {selectedProject.file_count} files indexed • {selectedProject.chunk_count} chunks available
                </p>
              </div>
              <div className="flex gap-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileUpload}
                  accept=".js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.h,.cs,.php,.rb,.go,.rs,.swift,.kt"
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isLoading}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
                >
                  <Upload size={16} />
                  Upload Code
                </button>
                <button
                  onClick={clearChat}
                  className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                >
                  <Trash2 size={16} />
                  Clear Chat
                </button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-gray-500 mt-8">
                  <p className="text-lg font-medium">Welcome to RAG Code Assistant!</p>
                  <p className="mt-2">Upload code files and start asking questions about your codebase.</p>
                  <div className="mt-4 text-sm">
                    <p>Try asking:</p>
                    <ul className="mt-2 space-y-1">
                      <li>• "What functions are available in this code?"</li>
                      <li>• "How does the authentication work?"</li>
                      <li>• "Show me the error handling patterns"</li>
                      <li>• "Explain the main data structures"</li>
                    </ul>
                  </div>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-3xl rounded-lg px-4 py-2 ${
                      message.role === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-white border border-gray-200"
                    }`}
                  >
                    <div className="whitespace-pre-wrap">{message.content}</div>
                    
                    {message.sources && message.sources.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="text-sm font-medium text-gray-700 mb-2">Sources:</p>
                        <div className="space-y-1">
                          {message.sources.map((source, index) => (
                            <div key={index} className="text-xs text-gray-600">
                              📄 {source.metadata.filename}
                              {source.metadata.function_name && (
                                <span className="ml-1 text-blue-600">
                                  ({source.metadata.function_name})
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="text-xs opacity-70 mt-2">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-gray-600">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
