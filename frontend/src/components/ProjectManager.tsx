"use client";

import { useState, useEffect } from "react";
import { Upload, Plus, Trash2, F<PERSON><PERSON><PERSON><PERSON>, Clock, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { apiClient, Project, ProjectStatus } from "@/lib/api";

interface ProjectManagerProps {
  onProjectSelect: (project: Project) => void;
  selectedProject: Project | null;
}

export function ProjectManager({ onProjectSelect, selectedProject }: ProjectManagerProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [newProjectName, setNewProjectName] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [processingProjects, setProcessingProjects] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      const projectList = await apiClient.getProjects();
      setProjects(projectList);
      if (projectList.length > 0 && !selectedProject) {
        onProjectSelect(projectList[0]);
      }
    } catch (error) {
      console.error("Failed to load projects:", error);
    }
  };

  const createProject = async () => {
    if (!newProjectName.trim()) return;
    
    try {
      const project = await apiClient.createProject(newProjectName);
      setProjects([...projects, project]);
      onProjectSelect(project);
      setNewProjectName("");
      setIsCreating(false);
    } catch (error) {
      console.error("Failed to create project:", error);
    }
  };

  const deleteProject = async (projectId: string) => {
    try {
      await apiClient.deleteProject(projectId);
      const updatedProjects = projects.filter(p => p.id !== projectId);
      setProjects(updatedProjects);
      
      if (selectedProject?.id === projectId) {
        onProjectSelect(updatedProjects[0] || null);
      }
    } catch (error) {
      console.error("Failed to delete project:", error);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !selectedProject) return;

    try {
      setIsUploading(true);
      
      // Convert FileList to Array
      const fileArray = Array.from(files);
      
      if (fileArray.length === 1) {
        // Single file upload
        console.log(`Uploading single file: ${fileArray[0].name}`);
        await apiClient.uploadFile(selectedProject.id, fileArray[0]);
      } else {
        // Multiple file upload
        console.log(`Uploading ${fileArray.length} files`);
        await apiClient.uploadFiles(selectedProject.id, fileArray);
      }
      
      // Refresh project list to update file count
      await loadProjects();
      console.log(`Successfully uploaded ${fileArray.length} file(s)`);
      
      // Start processing automatically
      await startProcessing(selectedProject.id);
      
    } catch (error) {
      console.error("Failed to upload files:", error);
    } finally {
      setIsUploading(false);
      // Reset file input
      event.target.value = "";
    }
  };

  const startProcessing = async (projectId: string) => {
    try {
      setProcessingProjects(prev => new Set(prev).add(projectId));
      
      // Start processing
      await apiClient.processProject(projectId);
      
      // Poll for status updates
      pollProcessingStatus(projectId);
      
    } catch (error) {
      console.error("Failed to start processing:", error);
      setProcessingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const pollProcessingStatus = async (projectId: string) => {
    let retryCount = 0;
    const maxRetries = 3;
    
    const pollInterval = setInterval(async () => {
      try {
        const status = await apiClient.getProjectStatus(projectId);
        
        // Reset retry count on successful request
        retryCount = 0;
        
        // Update project in the list
        setProjects(prev => prev.map(p =>
          p.id === projectId
            ? { ...p, processing_status: status.processing_status, chunk_count: status.chunk_count, indexed: status.indexed }
            : p
        ));
        
        // Stop polling when processing is complete
        if (status.processing_status === 'ready' || status.processing_status === 'error') {
          clearInterval(pollInterval);
          setProcessingProjects(prev => {
            const newSet = new Set(prev);
            newSet.delete(projectId);
            return newSet;
          });
          
          // Refresh the full project list
          await loadProjects();
        }
      } catch (error) {
        retryCount++;
        console.warn(`Failed to get processing status (attempt ${retryCount}/${maxRetries}):`, error);
        
        // Only stop polling after max retries with non-404 errors
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (retryCount >= maxRetries && !errorMessage.includes('404')) {
          console.error("Max retries reached, stopping status polling for project:", projectId);
          clearInterval(pollInterval);
          setProcessingProjects(prev => {
            const newSet = new Set(prev);
            newSet.delete(projectId);
            return newSet;
          });
        }
        // For 404 errors or within retry limit, continue polling
      }
    }, 2000); // Poll every 2 seconds
  };

  const getProcessingStatusDisplay = (project: Project) => {
    const status = project.processing_status || 'idle';
    const isProcessing = processingProjects.has(project.id);
    
    switch (status) {
      case 'processing':
        return (
          <div className="flex items-center gap-1 text-blue-600">
            <Loader2 size={12} className="animate-spin" />
            <span className="text-xs">Processing files...</span>
          </div>
        );
      case 'chunking':
        return (
          <div className="flex items-center gap-1 text-blue-600">
            <Loader2 size={12} className="animate-spin" />
            <span className="text-xs">Creating chunks...</span>
          </div>
        );
      case 'indexing':
        return (
          <div className="flex items-center gap-1 text-blue-600">
            <Loader2 size={12} className="animate-spin" />
            <span className="text-xs">Indexing chunks...</span>
          </div>
        );
      case 'ready':
        return (
          <div className="flex items-center gap-1 text-green-600">
            <CheckCircle size={12} />
            <span className="text-xs">Ready for chat</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center gap-1 text-red-600">
            <AlertCircle size={12} />
            <span className="text-xs">Processing error</span>
          </div>
        );
      default:
        if (project.files.length > 0 && project.chunk_count === 0) {
          return (
            <div className="flex items-center gap-1 text-yellow-600">
              <Clock size={12} />
              <span className="text-xs">Needs processing</span>
            </div>
          );
        }
        return null;
    }
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h1 className="text-xl font-semibold text-gray-900">RAG Code Assistant</h1>
        <p className="text-sm text-gray-600 mt-1">Intelligent code analysis with AI</p>
      </div>

      {/* Project Creation */}
      <div className="p-4 border-b border-gray-200">
        {isCreating ? (
          <div className="space-y-2">
            <input
              type="text"
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              placeholder="Project name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === "Enter" && createProject()}
              autoFocus
            />
            <div className="flex gap-2">
              <button
                onClick={createProject}
                className="flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
              >
                Create
              </button>
              <button
                onClick={() => setIsCreating(false)}
                className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={() => setIsCreating(true)}
            className="w-full flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 transition-colors"
          >
            <Plus size={16} />
            New Project
          </button>
        )}
      </div>

      {/* File Upload */}
      {selectedProject && (
        <div className="p-4 border-b border-gray-200">
          <input
            type="file"
            onChange={handleFileUpload}
            accept=".js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.h,.cs,.php,.rb,.go,.rs,.swift,.kt"
            className="hidden"
            id="file-upload"
            disabled={isUploading}
            multiple
          />
          <label
            htmlFor="file-upload"
            className={`w-full flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm cursor-pointer transition-colors ${
              isUploading
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "hover:bg-gray-50"
            }`}
          >
            <Upload size={16} />
            {isUploading ? "Uploading..." : "Upload Code Files"}
          </label>
          {!isUploading && (
            <p className="text-xs text-gray-500 mt-1">
              Select multiple files with Ctrl+Click (Cmd+Click on Mac)
            </p>
          )}
        </div>
      )}

      {/* Projects List */}
      <div className="flex-1 overflow-y-auto">
        {projects.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <FolderOpen size={48} className="mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No projects yet</p>
            <p className="text-xs text-gray-400 mt-1">Create your first project to get started</p>
          </div>
        ) : (
          projects.map((project) => (
            <div
              key={project.id}
              className={`p-4 border-b border-gray-100 cursor-pointer transition-colors ${
                selectedProject?.id === project.id 
                  ? "bg-blue-50 border-blue-200" 
                  : "hover:bg-gray-50"
              }`}
              onClick={() => onProjectSelect(project)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{project.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {project.files.length} files • {project.chunk_count || project.total_chunks || 0} chunks
                  </p>
                  {getProcessingStatusDisplay(project)}
                  <p className="text-xs text-gray-400">
                    {new Date(project.created_at).toLocaleDateString()}
                  </p>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteProject(project.id);
                  }}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}