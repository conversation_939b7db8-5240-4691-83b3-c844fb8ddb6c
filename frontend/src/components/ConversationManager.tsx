"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}

interface ConversationContextType {
  conversations: Conversation[];
  currentConversationId: string | null;
  currentProjectId: string | null;
  setCurrentProject: (projectId: string) => void;
  createNewConversation: () => Promise<void>;
  loadConversation: (conversationId: string) => Promise<void>;
  deleteConversation: (conversationId: string) => Promise<void>;
  refreshConversations: () => Promise<void>;
}

const ConversationContext = createContext<ConversationContextType | null>(null);

export function useConversations() {
  const context = useContext(ConversationContext);
  if (!context) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
}

interface ConversationProviderProps {
  children: ReactNode;
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);

  const refreshConversations = async () => {
    if (!currentProjectId) return;

    try {
      const response = await fetch(`/api/projects/${currentProjectId}/conversations`);
      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  const setCurrentProject = async (projectId: string) => {
    setCurrentProjectId(projectId);
    setCurrentConversationId(null);
    localStorage.setItem('currentProjectId', projectId);
    
    // Load conversations for this project
    try {
      const response = await fetch(`/api/projects/${projectId}/conversations`);
      const data = await response.json();
      setConversations(data.conversations || []);
      
      // Auto-select the most recent conversation if available
      if (data.conversations && data.conversations.length > 0) {
        setCurrentConversationId(data.conversations[0].id);
      }
    } catch (error) {
      console.error('Failed to load project conversations:', error);
    }
  };

  const createNewConversation = async () => {
    if (!currentProjectId) return;

    try {
      const response = await fetch(`/api/projects/${currentProjectId}/conversations`, {
        method: 'POST',
      });
      const data = await response.json();
      
      setCurrentConversationId(data.conversation_id);
      await refreshConversations();
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  const loadConversation = async (conversationId: string) => {
    setCurrentConversationId(conversationId);
    
    // Trigger a refresh of the chat interface
    window.dispatchEvent(new CustomEvent('conversationChanged', { 
      detail: { conversationId, projectId: currentProjectId } 
    }));
  };

  const deleteConversation = async (conversationId: string) => {
    if (!currentProjectId) return;

    try {
      await fetch(`/api/projects/${currentProjectId}/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      
      // If this was the current conversation, clear it
      if (currentConversationId === conversationId) {
        setCurrentConversationId(null);
      }
      
      await refreshConversations();
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };

  // Load initial state
  useEffect(() => {
    const savedProjectId = localStorage.getItem('currentProjectId');
    if (savedProjectId) {
      setCurrentProject(savedProjectId);
    }
  }, []);

  const value: ConversationContextType = {
    conversations,
    currentConversationId,
    currentProjectId,
    setCurrentProject,
    createNewConversation,
    loadConversation,
    deleteConversation,
    refreshConversations,
  };

  return (
    <ConversationContext.Provider value={value}>
      {children}
    </ConversationContext.Provider>
  );
}