"use client";

import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON>cle, Alert<PERSON>ircle, Clock, Upload } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Project } from "@/lib/api";

interface ProcessingStatusProps {
  project: Project;
}

export function ProcessingStatus({ project }: ProcessingStatusProps) {
  const getStatusContent = () => {
    const status = project.processing_status || 'idle';
    
    switch (status) {
      case 'processing':
        return {
          icon: <Loader2 className="h-12 w-12 animate-spin text-blue-500" />,
          title: "Processing files...",
          description: "Please wait while we process your uploaded files..."
        };
      case 'chunking':
        return {
          icon: <Loader2 className="h-12 w-12 animate-spin text-blue-500" />,
          title: "Creating chunks...",
          description: "Creating enhanced chunks for better search..."
        };
      case 'indexing':
        return {
          icon: <Loader2 className="h-12 w-12 animate-spin text-blue-500" />,
          title: "Indexing chunks...",
          description: "Building search index for your code..."
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-12 w-12 text-red-500" />,
          title: "Processing failed",
          description: "There was an error processing your files. Please try uploading again."
        };
      default:
        if (project.files.length > 0 && project.chunk_count === 0) {
          return {
            icon: <Clock className="h-12 w-12 text-yellow-500" />,
            title: "Waiting to process files",
            description: "Files are uploaded but need to be processed before you can chat."
          };
        }
        return {
          icon: <Upload className="h-12 w-12 text-gray-400" />,
          title: "No files uploaded yet",
          description: "Upload some code files to start chatting about your project."
        };
    }
  };

  const { icon, title, description } = getStatusContent();

  const isProcessing = project.processing_status === 'processing' ||
                      project.processing_status === 'chunking' ||
                      project.processing_status === 'indexing';

  const isError = project.processing_status === 'error';

  return (
    <div className="flex-1 flex items-center justify-center bg-white p-6">
      <div className="text-center max-w-md mx-auto space-y-6">
        <div className="mx-auto mb-4 flex justify-center">
          {icon}
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">
            {title}
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            {description}
          </p>
        </div>

        {isProcessing && (
          <div className="space-y-2">
            <Progress value={60} className="w-full" />
            <p className="text-xs text-gray-500">Processing your files...</p>
          </div>
        )}

        {isError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Processing Error</AlertTitle>
            <AlertDescription>
              There was an error processing your files. Please try uploading again.
            </AlertDescription>
          </Alert>
        )}

        {project.files.length === 0 && (
          <Alert>
            <Upload className="h-4 w-4" />
            <AlertTitle>Ready to Upload</AlertTitle>
            <AlertDescription>
              Upload some code files to start chatting about your project.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}