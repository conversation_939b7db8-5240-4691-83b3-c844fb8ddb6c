"use client";

import { useState, useCallback, useEffect, ReactNode } from "react";
import {
  useExternalStoreRuntime,
  ThreadMessageLike,
  AppendMessage,
  AssistantRuntimeProvider,
} from "@assistant-ui/react";
import { apiClient, ChatMessage } from "@/lib/api";

// Convert our ChatMessage format to assistant-ui ThreadMessageLike format
const convertMessage = (message: ChatMessage, index: number): ThreadMessageLike => {
  // Ensure content is valid and not undefined/null
  const safeContent = message.content && typeof message.content === 'string' && message.content.trim
    ? message.content.trim() || 'Empty message'
    : 'Invalid message content';
    
  if (!message.content || typeof message.content !== 'string') {
    console.error('Invalid ChatMessage content detected:', message);
  }
  
  return {
    role: message.role,
    content: [{ type: "text", text: safeContent }],
    id: `msg-${index}`,
    createdAt: new Date(),
  };
};

interface RAGProviderProps {
  children: ReactNode;
  projectId: string | null;
}

export function RAGProvider({ children, projectId }: RAGProviderProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  const onNew = useCallback(
    async (message: AppendMessage) => {
      if (!projectId) {
        throw new Error("No project selected");
      }

      if (message.content[0]?.type !== "text") {
        throw new Error("Only text messages are supported");
      }

      const input = message.content[0]?.text;
      if (!input || typeof input !== 'string' || !input.trim()) {
        console.warn("Empty or invalid message input detected");
        return;
      }
      
      // Add user message to state
      const userMessage: ChatMessage = { role: "user", content: input };
      setMessages((currentMessages) => [...currentMessages, userMessage]);

      setIsRunning(true);
      try {
        // Call our RAG backend
        const response = await apiClient.chat(projectId, input, messages);
        
        // Add assistant response to state
        const assistantMessage: ChatMessage = {
          role: "assistant",
          content: response.answer
        };
        setMessages((currentMessages) => [...currentMessages, assistantMessage]);
      } catch (error) {
        console.error("Chat error:", error);
        // Add error message
        const errorMessage: ChatMessage = { 
          role: "assistant", 
          content: "Sorry, I encountered an error while processing your request. Please try again." 
        };
        setMessages((currentMessages) => [...currentMessages, errorMessage]);
      } finally {
        setIsRunning(false);
      }
    },
    [projectId, messages]
  );

  const onEdit = useCallback(
    async (message: AppendMessage) => {
      if (!projectId) {
        throw new Error("No project selected");
      }

      if (message.content[0]?.type !== "text") {
        throw new Error("Only text messages are supported");
      }

      // Find the parent message index
      const parentIndex = messages.findIndex((m, i) => `msg-${i}` === message.parentId);
      if (parentIndex === -1) return;

      // Keep messages up to the parent
      const newMessages = messages.slice(0, parentIndex + 1);
      
      // Add the edited message
      const editedContent = message.content[0]?.text || "";
      const editedMessage: ChatMessage = { role: "user", content: editedContent };
      newMessages.push(editedMessage);
      
      setMessages(newMessages);

      // Generate new response
      setIsRunning(true);
      try {
        const messageText = message.content[0]?.text || "";
        const response = await apiClient.chat(projectId, messageText, newMessages.slice(0, -1));
        const assistantMessage: ChatMessage = {
          role: "assistant",
          content: response.answer
        };
        setMessages([...newMessages, assistantMessage]);
      } catch (error) {
        console.error("Edit chat error:", error);
        const errorMessage: ChatMessage = { 
          role: "assistant", 
          content: "Sorry, I encountered an error while processing your edited message. Please try again." 
        };
        setMessages([...newMessages, errorMessage]);
      } finally {
        setIsRunning(false);
      }
    },
    [projectId, messages]
  );

  const onReload = useCallback(
    async (parentId: string | null) => {
      if (!projectId) {
        throw new Error("No project selected");
      }

      // Find the parent message
      const parentIndex = parentId ? messages.findIndex((m, i) => `msg-${i}` === parentId) : -1;
      const historyMessages = parentIndex >= 0 ? messages.slice(0, parentIndex + 1) : messages;
      
      // Get the last user message
      const lastUserMessage = historyMessages.filter(m => m.role === "user").pop();
      if (!lastUserMessage) return;

      // Remove the last assistant message if it exists
      const messagesWithoutLastAssistant = historyMessages.filter((m, i) => {
        if (i === historyMessages.length - 1 && m.role === "assistant") {
          return false;
        }
        return true;
      });

      setMessages(messagesWithoutLastAssistant);

      setIsRunning(true);
      try {
        const response = await apiClient.chat(
          projectId, 
          lastUserMessage.content, 
          messagesWithoutLastAssistant.filter(m => m.role === "user" || m.role === "assistant")
        );
        
        const assistantMessage: ChatMessage = {
          role: "assistant",
          content: response.answer
        };
        setMessages([...messagesWithoutLastAssistant, assistantMessage]);
      } catch (error) {
        console.error("Reload chat error:", error);
        const errorMessage: ChatMessage = { 
          role: "assistant", 
          content: "Sorry, I encountered an error while regenerating the response. Please try again." 
        };
        setMessages([...messagesWithoutLastAssistant, errorMessage]);
      } finally {
        setIsRunning(false);
      }
    },
    [projectId, messages]
  );

  const onCancel = useCallback(async () => {
    setIsRunning(false);
    // In a real implementation, you might want to cancel the ongoing request
  }, []);

  // Clear messages when project changes
  useEffect(() => {
    setMessages([]);
  }, [projectId]);

  const runtime = useExternalStoreRuntime({
    messages,
    isRunning,
    convertMessage,
    onNew,
    onEdit,
    onReload,
    onCancel,
    setMessages: (newMessages: ChatMessage[]) => setMessages(newMessages),
  });

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      {children}
    </AssistantRuntimeProvider>
  );
}