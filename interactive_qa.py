#!/usr/bin/env python3
"""
Interactive Q&A Interface for Code RAG System
Provides a simple command-line interface for querying indexed code chunks.
"""

import os
import sys
from code_indexer import CodeIndexer

def print_banner():
    """Print welcome banner"""
    print("=" * 60)
    print("🔍 Code RAG - Interactive Q&A System")
    print("=" * 60)
    print("Ask questions about your codebase!")
    print("Type 'quit', 'exit', or 'q' to exit")
    print("Type 'stats' to see collection statistics")
    print("Type 'help' for more commands")
    print("-" * 60)

def print_help():
    """Print help information"""
    print("\nAvailable commands:")
    print("  help, h          - Show this help message")
    print("  stats, s         - Show collection statistics")
    print("  quit, exit, q    - Exit the program")
    print("  clear, cls       - Clear the screen")
    print("  results <n>      - Set number of results (default: 5)")
    print("\nExample queries:")
    print("  'Where does the token refresh logic live?'")
    print("  'How is error handling implemented?'")
    print("  'Show me the authentication code'")
    print("  'Find functions that handle HTTP requests'")
    print()

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def format_result_summary(result):
    """Format a single result for display"""
    chunk_id = result['chunk_id']
    file_path = result['file_path']
    distance = result['distance']
    token_count = result['token_count']
    
    return f"[{chunk_id}] {file_path} (similarity: {1-distance:.3f}, tokens: {token_count})"

def main():
    """Main interactive loop"""
    try:
        # Initialize indexer with local embeddings by default
        print("Initializing Code RAG system...")
        indexer = CodeIndexer(use_local_embeddings=True)
        
        # Check if collection has data
        stats = indexer.get_collection_stats()
        if stats['total_chunks'] == 0:
            print("\n❌ No indexed chunks found!")
            print("Please run: python code_indexer.py --index --local")
            print("This will index your code chunks for searching.")
            return 1
        
        print(f"✅ Loaded {stats['total_chunks']} indexed code chunks")
        
    except Exception as e:
        print(f"❌ Error initializing system: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you've run: python code_indexer.py --index --local")
        print("2. Check that chunk_*.js files exist in the current directory")
        print("3. Ensure all dependencies are installed")
        return 1
    
    print_banner()
    
    # Configuration
    num_results = 5
    
    while True:
        try:
            # Get user input
            query = input("\n🔍 Ask a question: ").strip()
            
            if not query:
                continue
            
            # Handle commands
            query_lower = query.lower()
            
            if query_lower in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            elif query_lower in ['help', 'h']:
                print_help()
                continue
            
            elif query_lower in ['stats', 's']:
                stats = indexer.get_collection_stats()
                print(f"\n📊 Collection Statistics:")
                print(f"   Total chunks: {stats['total_chunks']}")
                print(f"   Collection: {stats['collection_name']}")
                continue
            
            elif query_lower in ['clear', 'cls']:
                clear_screen()
                print_banner()
                continue
            
            elif query_lower.startswith('results '):
                try:
                    new_num = int(query_lower.split()[1])
                    if 1 <= new_num <= 20:
                        num_results = new_num
                        print(f"✅ Set number of results to {num_results}")
                    else:
                        print("❌ Number of results must be between 1 and 20")
                except (IndexError, ValueError):
                    print("❌ Usage: results <number>")
                continue
            
            # Process query
            print(f"\n🔍 Searching for: '{query}'")
            print("⏳ Processing...")
            
            results = indexer.query(query, n_results=num_results)
            formatted_results = results['formatted_results']
            
            if not formatted_results:
                print("❌ No results found. Try rephrasing your question.")
                continue
            
            print(f"\n📋 Found {len(formatted_results)} relevant code chunks:")
            print("-" * 60)
            
            # Show summary of all results
            for i, result in enumerate(formatted_results, 1):
                summary = format_result_summary(result)
                print(f"{i}. {summary}")
            
            print("-" * 60)
            
            # Show detailed view of top result
            top_result = formatted_results[0]
            print(f"\n📖 Top Result Details:")
            print(f"File: {top_result['file_path']}")
            print(f"Chunk: {top_result['chunk_id']}")
            print(f"Similarity: {1-top_result['distance']:.3f}")
            print(f"Size: {top_result['token_count']} tokens, {top_result['char_count']} chars")
            print("\n📄 Code Preview:")
            print("-" * 40)
            print(top_result['preview'])
            print("-" * 40)
            
            # Ask if user wants to see more details
            while True:
                choice = input("\n📋 Options: [f]ull content, [n]ext result, [b]ack to search: ").strip().lower()
                
                if choice in ['b', 'back', '']:
                    break
                elif choice in ['f', 'full']:
                    print(f"\n📄 Full Content of {top_result['chunk_id']}:")
                    print("=" * 60)
                    print(top_result['full_content'])
                    print("=" * 60)
                    break
                elif choice in ['n', 'next']:
                    if len(formatted_results) > 1:
                        # Show next result
                        for i, result in enumerate(formatted_results[1:], 2):
                            print(f"\n📖 Result #{i}:")
                            print(f"File: {result['file_path']}")
                            print(f"Chunk: {result['chunk_id']}")
                            print(f"Similarity: {1-result['distance']:.3f}")
                            print("\n📄 Code Preview:")
                            print("-" * 40)
                            print(result['preview'])
                            print("-" * 40)
                            
                            show_more = input(f"\n📋 Show [f]ull content, [n]ext result, or [b]ack? ").strip().lower()
                            if show_more in ['f', 'full']:
                                print(f"\n📄 Full Content of {result['chunk_id']}:")
                                print("=" * 60)
                                print(result['full_content'])
                                print("=" * 60)
                            elif show_more in ['b', 'back']:
                                break
                        break
                    else:
                        print("❌ No more results to show")
                else:
                    print("❌ Invalid choice. Use 'f' for full, 'n' for next, or 'b' for back")
        
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error processing query: {e}")
            print("Please try again or type 'help' for assistance.")
    
    return 0

if __name__ == "__main__":
    exit(main())