#!/usr/bin/env python3
"""
Project Manager for Code RAG System
Handles multiple projects, file uploads, and project-specific vector databases.
Enhanced-only chunking approach for optimal performance.
"""

import os
import json
import uuid
import shutil
import hashlib
import tiktoken
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import chromadb
from chromadb.config import Settings
from code_indexer import CodeIndexer

class ProjectManager:
    def __init__(self, projects_dir: str = "projects"):
        """
        Initialize the Project Manager
        
        Args:
            projects_dir: Directory to store all projects
        """
        self.projects_dir = Path(projects_dir)
        self.projects_dir.mkdir(exist_ok=True)
        self.projects_file = self.projects_dir / "projects.json"
        self.projects = self._load_projects()
    
    def _load_projects(self) -> Dict[str, Any]:
        """Load projects metadata from file"""
        if self.projects_file.exists():
            with open(self.projects_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_projects(self):
        """Save projects metadata to file"""
        with open(self.projects_file, 'w') as f:
            json.dump(self.projects, f, indent=2, default=str)
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file content"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def create_project(self, name: str, description: str = "") -> str:
        """
        Create a new project
        
        Args:
            name: Project name
            description: Project description
            
        Returns:
            project_id: Unique project identifier
        """
        project_id = str(uuid.uuid4())
        project_dir = self.projects_dir / project_id
        project_dir.mkdir(exist_ok=True)
        
        # Create project subdirectories
        (project_dir / "files").mkdir(exist_ok=True)
        (project_dir / "chroma_db").mkdir(exist_ok=True)
        
        project_data = {
            "id": project_id,
            "name": name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "files": [],
            "chunk_count": 0,
            "total_tokens": 0,
            "total_chars": 0,
            "indexed": False,
            "enhanced_chunks": True,  # Always use enhanced chunks
            "processing_status": "idle"  # idle, processing, chunking, indexing, ready, error
        }
        
        self.projects[project_id] = project_data
        self._save_projects()
        
        return project_id
    
    def upload_file(self, project_id: str, file_path: str, original_filename: str) -> Dict[str, Any]:
        """
        Upload a file to a project
        
        Args:
            project_id: Project identifier
            file_path: Path to the uploaded file
            original_filename: Original filename
            
        Returns:
            File metadata
        """
        if project_id not in self.projects:
            raise ValueError(f"Project {project_id} not found")
        
        project_dir = self.projects_dir / project_id
        files_dir = project_dir / "files"
        
        # Calculate file hash and check for duplicates
        file_hash = self._calculate_file_hash(file_path)
        
        # Check if file already exists in project
        for existing_file in self.projects[project_id]["files"]:
            if existing_file["hash"] == file_hash:
                return existing_file
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = Path(original_filename).suffix
        stored_filename = f"{file_id}{file_extension}"
        stored_path = files_dir / stored_filename
        
        # Copy file to project directory
        shutil.copy2(file_path, stored_path)
        
        # Get file stats
        file_stats = os.stat(stored_path)
        
        file_data = {
            "id": file_id,
            "original_filename": original_filename,
            "stored_filename": stored_filename,
            "hash": file_hash,
            "size": file_stats.st_size,
            "uploaded_at": datetime.now().isoformat(),
            "processed": False
        }
        
        self.projects[project_id]["files"].append(file_data)
        self.projects[project_id]["updated_at"] = datetime.now().isoformat()
        self.projects[project_id]["indexed"] = False  # Mark as needing re-indexing
        self._save_projects()
        
        return file_data
    
    def create_enhanced_chunks(self, content: str, target_size: int = 150) -> List[Dict[str, Any]]:
        """
        Create enhanced chunks directly from content using token-based splitting
        
        Args:
            content: Combined file content
            target_size: Target tokens per chunk
            
        Returns:
            List of enhanced chunks
        """
        encoding = tiktoken.get_encoding('cl100k_base')
        tokens = encoding.encode(content)
        
        chunks = []
        chunk_id = 0
        overlap = 30  # 30 token overlap for context preservation
        
        start = 0
        while start < len(tokens):
            end = min(start + target_size, len(tokens))
            chunk_tokens = tokens[start:end]
            chunk_content = encoding.decode(chunk_tokens)
            
            chunks.append({
                'id': f'enhanced_chunk_{chunk_id}',
                'content': chunk_content,
                'metadata': {
                    'token_count': len(chunk_tokens),
                    'start_token': start,
                    'end_token': end,
                    'chunk_type': 'enhanced',
                    'original_chunk_id': f'direct_chunk_{chunk_id}'
                }
            })
            
            chunk_id += 1
            start += target_size - overlap
        
        return chunks
    
    def update_processing_status(self, project_id: str, status: str):
        """Update the processing status of a project"""
        if project_id in self.projects:
            self.projects[project_id]["processing_status"] = status
            self.projects[project_id]["updated_at"] = datetime.now().isoformat()
            self._save_projects()
    
    def process_project_files(self, project_id: str) -> Dict[str, Any]:
        """
        Process all files in a project using enhanced-only chunking
        
        Args:
            project_id: Project identifier
            
        Returns:
            Processing results
        """
        if project_id not in self.projects:
            raise ValueError(f"Project {project_id} not found")
        
        try:
            # Set status to processing
            self.update_processing_status(project_id, "processing")
            
            project_dir = self.projects_dir / project_id
            files_dir = project_dir / "files"
            
            # Combine all JavaScript files
            combined_content = ""
            processed_files = []
            
            for file_data in self.projects[project_id]["files"]:
                file_path = files_dir / file_data["stored_filename"]
                if file_path.suffix.lower() in ['.js', '.jsx', '.ts', '.tsx']:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        combined_content += f"\n\n// === {file_data['original_filename']} ===\n\n"
                        combined_content += content
                        processed_files.append(file_data["original_filename"])
            
            if not combined_content.strip():
                self.update_processing_status(project_id, "error")
                raise ValueError("No JavaScript/TypeScript files found to process")
            
            # Set status to chunking
            self.update_processing_status(project_id, "chunking")
            
            # Create enhanced chunks directly
            enhanced_chunks = self.create_enhanced_chunks(combined_content, target_size=150)
            
            # Save enhanced chunks to file
            enhanced_chunks_file = project_dir / 'enhanced_chunks.json'
            with open(enhanced_chunks_file, 'w', encoding='utf-8') as f:
                json.dump(enhanced_chunks, f, indent=2)
            
            # Calculate statistics
            total_tokens = sum(chunk['metadata']['token_count'] for chunk in enhanced_chunks)
            total_chars = len(combined_content)
            
            # Update project metadata
            self.projects[project_id]["chunk_count"] = len(enhanced_chunks)
            self.projects[project_id]["total_tokens"] = total_tokens
            self.projects[project_id]["total_chars"] = total_chars
            self.projects[project_id]["updated_at"] = datetime.now().isoformat()
            self.projects[project_id]["enhanced_chunks"] = True
            
            # Mark files as processed
            for file_data in self.projects[project_id]["files"]:
                file_data["processed"] = True
            
            # Set status to chunked (ready for indexing)
            self.update_processing_status(project_id, "chunked")
            
            self._save_projects()
            
            return {
                "processed_files": processed_files,
                "enhanced_chunks": len(enhanced_chunks),
                "total_tokens": total_tokens,
                "total_chars": total_chars,
                "combined_file_size": len(combined_content),
                "enhanced_chunks_file": str(enhanced_chunks_file)
            }
        
        except Exception as e:
            self.update_processing_status(project_id, "error")
            raise e
    
    def index_project(self, project_id: str, use_local_embeddings: bool = True) -> Dict[str, Any]:
        """
        Index a project's enhanced chunks for RAG
        
        Args:
            project_id: Project identifier
            use_local_embeddings: Use local embeddings instead of OpenAI
            
        Returns:
            Indexing results
        """
        if project_id not in self.projects:
            raise ValueError(f"Project {project_id} not found")
        
        try:
            # Set status to indexing
            self.update_processing_status(project_id, "indexing")
            
            project_dir = self.projects_dir / project_id
            enhanced_chunks_file = project_dir / 'enhanced_chunks.json'
            chroma_db_dir = project_dir / "chroma_db"
            
            if not enhanced_chunks_file.exists():
                self.update_processing_status(project_id, "error")
                raise ValueError("No enhanced chunks found. Process files first.")
            
            # Load enhanced chunks
            with open(enhanced_chunks_file, 'r', encoding='utf-8') as f:
                enhanced_chunks = json.load(f)
            
            # Initialize project-specific indexer
            indexer = ProjectCodeIndexer(
                project_id=project_id,
                chroma_db_path=str(chroma_db_dir),
                use_local_embeddings=use_local_embeddings
            )
            
            # Index enhanced chunks
            indexer.index_enhanced_chunks(enhanced_chunks)
            
            # Update project metadata
            self.projects[project_id]["indexed"] = True
            self.projects[project_id]["updated_at"] = datetime.now().isoformat()
            
            # Set status to ready
            self.update_processing_status(project_id, "ready")
            
            self._save_projects()
            
            return {
                "indexed_chunks": len(enhanced_chunks),
                "project_id": project_id,
                "chunk_type": "enhanced"
            }
        
        except Exception as e:
            self.update_processing_status(project_id, "error")
            raise e
    
    def get_project_indexer(self, project_id: str, use_local_embeddings: bool = True) -> 'ProjectCodeIndexer':
        """Get an indexer instance for a specific project"""
        if project_id not in self.projects:
            raise ValueError(f"Project {project_id} not found")
        
        project_dir = self.projects_dir / project_id
        chroma_db_dir = project_dir / "chroma_db"
        
        return ProjectCodeIndexer(
            project_id=project_id,
            chroma_db_path=str(chroma_db_dir),
            use_local_embeddings=use_local_embeddings
        )
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects"""
        return list(self.projects.values())
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get project by ID"""
        return self.projects.get(project_id)
    
    def delete_project(self, project_id: str) -> bool:
        """Delete a project and all its data"""
        if project_id not in self.projects:
            return False
        
        project_dir = self.projects_dir / project_id
        if project_dir.exists():
            shutil.rmtree(project_dir)
        
        del self.projects[project_id]
        self._save_projects()
        
        return True


class ProjectCodeIndexer(CodeIndexer):
    """Extended CodeIndexer for project-specific operations with enhanced chunks"""
    
    def __init__(self, project_id: str, chroma_db_path: str, use_local_embeddings: bool = True):
        self.project_id = project_id
        self.use_local_embeddings = use_local_embeddings
        
        # Initialize ChromaDB client with project-specific path
        self.client = chromadb.PersistentClient(path=chroma_db_path)
        
        # Create or get collection with project-specific name
        self.collection = self.client.get_or_create_collection(
            name=f"project_{project_id}",
            metadata={"description": f"Enhanced chunks for project {project_id}"}
        )
        
        # Initialize embedding model
        if not use_local_embeddings:
            import os
            from openai import OpenAI
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OpenAI API key required for cloud embeddings")
            self.openai_client = OpenAI(api_key=api_key)
        else:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Initialize tokenizer
        import tiktoken
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    def index_enhanced_chunks(self, enhanced_chunks: List[Dict[str, Any]]):
        """
        Index enhanced chunks in ChromaDB
        
        Args:
            enhanced_chunks: List of enhanced chunk dictionaries
        """
        print(f"Indexing {len(enhanced_chunks)} enhanced chunks...")
        
        # Prepare data for ChromaDB
        documents = []
        metadatas = []
        ids = []
        
        for chunk in enhanced_chunks:
            documents.append(chunk['content'])
            metadatas.append({
                'chunk_id': chunk['id'],
                'token_count': chunk['metadata']['token_count'],
                'chunk_type': chunk['metadata']['chunk_type'],
                'project_id': self.project_id
            })
            ids.append(chunk['id'])
        
        # Get embeddings
        if self.use_local_embeddings:
            embeddings = self.embedding_model.encode(documents, show_progress_bar=True)
            embeddings = embeddings.tolist()
        else:
            embeddings = []
            batch_size = 100
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                response = self.openai_client.embeddings.create(
                    input=batch,
                    model="text-embedding-3-small"
                )
                batch_embeddings = [item.embedding for item in response.data]
                embeddings.extend(batch_embeddings)
        
        # Add to ChromaDB in batches
        batch_size = 100
        for i in range(0, len(documents), batch_size):
            end_idx = min(i + batch_size, len(documents))
            
            self.collection.add(
                documents=documents[i:end_idx],
                metadatas=metadatas[i:end_idx],
                ids=ids[i:end_idx],
                embeddings=embeddings[i:end_idx]
            )
            
            print(f"Indexed batch {i//batch_size + 1}/{(len(documents) + batch_size - 1)//batch_size}")
        
        print(f"Successfully indexed {len(enhanced_chunks)} enhanced chunks!")
    
    def search(self, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant chunks using semantic similarity
        
        Args:
            query_text: The search query
            top_k: Number of top results to return
            
        Returns:
            List of search results with metadata
        """
        # Get query embedding
        query_embedding = self.get_embedding(query_text)
        
        # Search in ChromaDB
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=top_k
        )
        
        # Format results
        formatted_results = []
        if results['documents'] and results['documents'][0]:
            documents = results['documents'][0]
            metadatas = results['metadatas'][0] if results['metadatas'] else [{}] * len(documents)
            distances = results['distances'][0] if results['distances'] else [0] * len(documents)
            ids = results['ids'][0] if results['ids'] else list(range(len(documents)))
            
            for i, (doc, metadata, distance, chunk_id) in enumerate(zip(documents, metadatas, distances, ids)):
                formatted_results.append({
                    'rank': i + 1,
                    'chunk_id': chunk_id,
                    'chunk_type': metadata.get('chunk_type', 'enhanced'),
                    'token_count': metadata.get('token_count', 0),
                    'distance': distance,
                    'similarity': 1 - distance,  # Convert distance to similarity
                    'preview': doc[:400] + "..." if len(doc) > 400 else doc,
                    'full_content': doc
                })
        
        return formatted_results
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text"""
        if self.use_local_embeddings:
            return self.embedding_model.encode([text])[0].tolist()
        else:
            response = self.openai_client.embeddings.create(
                input=[text],
                model="text-embedding-3-small"
            )
            return response.data[0].embedding