Based on my comprehensive research of over 70 sources on RAG systems, token limit management, and production-ready solutions, I can now provide you with a detailed evaluation of your architecture and pragmatic solutions to your token limit problem.

# RAG Token Limit Management: Architecture Evaluation & Production Solutions

## Architecture Viability Assessment

Your current architecture is **fundamentally sound** and follows production-ready RAG patterns. The modular design with separate chunking, embedding, and retrieval components is exactly what industry leaders recommend[1][2]. However, you've identified the critical bottleneck that breaks most RAG systems in production: **token limit management at the context formatting stage**.

## The Core Problem: Context Formatting vs. Token Limits

Your diagnosis is accurate. The issue isn't with your chunking strategy or retrieval quality—it's with the `_format_code_context()` method that naively concatenates large code chunks without considering token constraints[3][4]. Even with sophisticated semantic chunking, individual chunks from massive codebases can exceed entire context windows.

## Production-Ready Solutions

### 1. **Hierarchical Content Selection** (Most Pragmatic)

Instead of truncating code chunks, implement a **hierarchical approach** that mirrors how developers actually read code[5][6]:

```python
def _format_code_context_hierarchical(self, sources: List[Dict[str, Any]], max_tokens: int = 150000) -> str:
    """Format retrieved code chunks with hierarchical content selection"""
    context = "=== RELEVANT CODE CHUNKS ===\n\n"
    current_tokens = self._count_tokens(context)
    
    for i, source in enumerate(sources, 1):
        # Create chunk header (always include)
        chunk_header = f"## Chunk {i}: {source['chunk_id']}\n"
        chunk_header += f"**File:** {source['file_path']}\n"
        chunk_header += f"**Similarity:** {1 - source['distance']:.3f}\n\n"
        
        # Calculate available space
        header_tokens = self._count_tokens(chunk_header)
        available_tokens = max_tokens - current_tokens - header_tokens - 500  # Buffer for next chunks
        
        if available_tokens <= 0:
            break
            
        # Hierarchical content selection
        formatted_content = self._select_hierarchical_content(
            source['full_content'], 
            available_tokens,
            source['file_path']
        )
        
        chunk_content = f"``````\n\n"
        context += chunk_header + chunk_content
        current_tokens += header_tokens + self._count_tokens(chunk_content)
    
    return context

def _select_hierarchical_content(self, content: str, max_tokens: int, file_path: str) -> str:
    """Select most relevant parts of large code chunks"""
    content_tokens = self._count_tokens(content)
    
    if content_tokens <= max_tokens:
        return content
    
    # Priority-based selection (adjust based on your code analysis needs)
    priorities = [
        self._extract_function_signatures(content),  # Function/method signatures
        self._extract_class_definitions(content),    # Class definitions  
        self._extract_imports_exports(content),      # Import/export statements
        self._extract_comments(content),             # Key comments/documentation
        self._extract_core_logic(content)            # Core implementation logic
    ]
    
    # Build context from highest to lowest priority
    selected_content = []
    used_tokens = 0
    
    for priority_content in priorities:
        for item in priority_content:
            item_tokens = self._count_tokens(item)
            if used_tokens + item_tokens <= max_tokens:
                selected_content.append(item)
                used_tokens += item_tokens
            else:
                break
    
    if not selected_content:
        # Fallback: truncate intelligently at function boundaries
        return self._truncate_at_boundaries(content, max_tokens)
    
    return "\n".join(selected_content)
```

### 2. **Multi-Pass RAG with Context Refinement** (Enterprise Pattern)

Implement a **multi-step retrieval approach** that progressively narrows context[7][8]:

```python
def query_with_progressive_rag(self, query: str, max_iterations: int = 3) -> str:
    """Multi-pass RAG that refines context across iterations"""
    conversation_context = []
    
    for iteration in range(max_iterations):
        # Retrieve relevant chunks for current context
        if iteration == 0:
            search_query = query
        else:
            # Use previous iteration results to refine search
            search_query = self._refine_query_with_context(query, conversation_context)
        
        sources = self._retrieve_sources(search_query, max_chunks=3)  # Fewer chunks per iteration
        
        # Format context for current iteration (manageable size)
        context = self._format_code_context_bounded(sources, max_tokens=50000)
        
        # Generate intermediate response
        response = self._generate_response(query, context, iteration_context=conversation_context)
        conversation_context.append({
            'iteration': iteration,
            'context': context,
            'response': response,
            'sources': sources
        })
        
        # Check if response is complete or needs more context
        if self._is_response_complete(response, query):
            break
    
    return self._synthesize_final_response(conversation_context)
```

### 3. **Smart Content Summarization** (For Massive Chunks)

Use a **two-tier approach** where large chunks are summarized first, then full content provided for the most relevant ones[4][9]:

```python
def _format_code_context_with_summarization(self, sources: List[Dict[str, Any]]) -> str:
    """Format context using summarization for large chunks"""
    context = "=== RELEVANT CODE ANALYSIS ===\n\n"
    
    # Categorize sources by size
    large_sources = [s for s in sources if s['token_count'] > 10000]
    manageable_sources = [s for s in sources if s['token_count'] <= 10000]
    
    # Include full content for manageable sources
    for source in manageable_sources[:2]:  # Top 2 manageable chunks
        context += self._format_full_chunk(source)
    
    # Summarize large sources with focus on code structure
    for source in large_sources[:3]:  # Top 3 large chunks
        summary = self._summarize_code_chunk(source)
        context += f"## Summary: {source['chunk_id']}\n"
        context += f"**File:** {source['file_path']}\n"
        context += f"**Key Functions/Classes:** {summary['key_elements']}\n"
        context += f"**Purpose:** {summary['purpose']}\n"
        context += f"**Notable Patterns:** {summary['patterns']}\n\n"
    
    return context

def _summarize_code_chunk(self, source: Dict[str, Any]) -> Dict[str, str]:
    """Generate focused summary of large code chunks"""
    # Use a smaller model or local analysis for summarization
    # This avoids token limits in the summarization step
    content = source['full_content']
    
    return {
        'key_elements': self._extract_key_elements(content),
        'purpose': self._infer_code_purpose(content),
        'patterns': self._identify_patterns(content)
    }
```

### 4. **Adaptive Token Budget Management** (Production Standard)

Implement **dynamic token allocation** based on query complexity and chunk relevance[10][4]:

```python
class TokenBudgetManager:
    def __init__(self, model_limit: int = 200000):
        self.model_limit = model_limit
        self.system_prompt_tokens = 1000  # Reserve for system prompt
        self.response_tokens = 4000       # Reserve for response
        self.available_tokens = model_limit - self.system_prompt_tokens - self.response_tokens
    
    def allocate_tokens(self, sources: List[Dict], query_complexity: str) -> List[Dict]:
        """Allocate token budget based on source relevance and query needs"""
        
        # Adjust allocation based on query type
        allocation_strategies = {
            'simple': {'max_chunks': 3, 'tokens_per_chunk': 15000},
            'complex': {'max_chunks': 5, 'tokens_per_chunk': 8000},
            'exploratory': {'max_chunks': 8, 'tokens_per_chunk': 4000}
        }
        
        strategy = allocation_strategies.get(query_complexity, allocation_strategies['simple'])
        
        # Rank sources by relevance and allocate tokens
        ranked_sources = sorted(sources, key=lambda x: x['distance'])
        allocated_sources = []
        
        total_allocated = 0
        for source in ranked_sources[:strategy['max_chunks']]:
            chunk_tokens = min(source['token_count'], strategy['tokens_per_chunk'])
            if total_allocated + chunk_tokens <= self.available_tokens:
                source['allocated_tokens'] = chunk_tokens
                allocated_sources.append(source)
                total_allocated += chunk_tokens
            else:
                break
        
        return allocated_sources
```

## Why This Architecture IS Pragmatic

Your current architecture is **more viable** than alternatives because:

1. **Semantic chunking preserves code context** - Better than arbitrary truncation[11][12]
2. **Vector search works well for code** - Embeddings capture semantic relationships[13][14]
3. **Modular design allows targeted fixes** - You only need to fix the formatting layer[1]
4. **Handles large codebases** - 71 chunks from 10.5MB shows good processing capability

## Alternative Approaches (Less Recommended)

### Re-chunking Strategy
Breaking your 71 chunks into smaller pieces would **lose semantic context** that's crucial for code analysis[15][16]. Your current chunk sizes likely preserve function/class boundaries.

### Long-Context Models  
While Claude 3.5 Sonnet has 200K tokens, **performance degrades** significantly with very long contexts, especially for reasoning tasks[17][18]. Your multi-pass approach is more reliable.

### Vector-Only Approach
Some suggest abandoning chunking entirely for long-context models[19], but this **ignores cost and latency** concerns in production systems[20].

## Implementation Priority

1. **Immediate**: Implement hierarchical content selection (Solution #1)
2. **Short-term**: Add token budget management (Solution #4)  
3. **Long-term**: Build multi-pass RAG for complex queries (Solution #2)

Your architecture is **production-ready** with these token management improvements. The core RAG pipeline is solid—you just need smarter context formatting to stay within LLM limits while preserving code semantics.

Sources
[1] RAG in Production: Deployment Strategies & Practical Considerations https://coralogix.com/ai-blog/rag-in-production-deployment-strategies-and-practical-considerations/
[2] Architecting Production-Ready RAG Systems: A Comprehensive ... https://ai-marketinglabs.com/lab-experiments/architecting-production-ready-rag-systems-a-comprehensive-guide-to-pinecone
[3] Strategies for Mitigating Token Limits Issue in RAG - LinkedIn https://www.linkedin.com/pulse/strategies-mitigating-token-limits-issue-rag-farhan-naqvi-pz7lf
[4] [Gen AI] Controlling token usage in RAG - LinkedIn https://www.linkedin.com/pulse/gen-ai-controlling-token-usage-rag-tony-liu-qd90c
[5] Exploring Hierarchical RAG: An Advanced Technique for Robust ... https://www.sahaj.ai/exploring-hierarchical-rag-an-advanced-technique-for-robust-information-retrieval/
[6] HiRAG: Retrieval-Augmented Generation with Hierarchical Knowledge https://arxiv.org/abs/2503.10150
[7] What is Multi-Step RAG (A Complete Guide) - F22 Labs https://www.f22labs.com/blogs/what-is-multi-step-rag-a-complete-guide/
[8] What is multi-step retrieval in RAG? Multi-step retrieval, or multi-hop ... https://milvus.io/ai-quick-reference/what-is-multistep-retrieval-or-multihop-retrieval-in-the-context-of-rag-and-can-you-give-an-example-of-a-question-that-would-require-this-approach
[9] AI Summarization of Long Documents - Codesphere https://codesphere.com/articles/ai-summarization
[10] Top techniques to Manage Context Lengths in LLMs - Agenta https://agenta.ai/blog/top-6-techniques-to-manage-context-length-in-llms
[11] Semantic Chunking for RAG: Better Context, Better Results https://www.multimodal.dev/post/semantic-chunking-for-rag
[12] Semantic Chunking | VectorHub by Superlinked https://superlinked.com/vectorhub/articles/semantic-chunking
[13] Understanding And Querying Code: A RAG powered approach https://medium.aiplanet.com/understanding-and-querying-code-a-rag-powered-approach-b85cf6f30d11
[14] 3 RAG Applications: From Code Review to Knowledge Discovery https://www.qodo.ai/blog/rag-applications-and-examples/
[15] Chunk Size Optimization: The Goldilocks Problem in Document ... https://www.linkedin.com/pulse/day-4-chunk-size-optimization-goldilocks-problem-document-marques-xkrke
[16] Q: How do I choose the right chunk size for my document processing ... https://hamel.dev/blog/posts/evals-faq/how-do-i-choose-the-right-chunk-size-for-my-document-processing-tasks.html
[17] Long Context RAG Performance of LLMs | Databricks Blog https://www.databricks.com/blog/long-context-rag-performance-llms
[18] Context Window Limitations of LLMs - Perplexity https://www.perplexity.ai/page/context-window-limitations-of-FKpx7M_ITz2rKXLFG1kNiQ
[19] RAG in the Era of LLMs with 10 Million Token Context Windows | F5 https://www.f5.com/company/blog/rag-in-the-era-of-llms-with-10-million-token-context-windows
[20] RAG vs Long Context Models [Discussion] : r/MachineLearning https://www.reddit.com/r/MachineLearning/comments/1ax6j73/rag_vs_long_context_models_discussion/
[21] Chunk documents in vector search - Azure AI Search - Microsoft Learn https://learn.microsoft.com/en-us/azure/search/vector-search-how-to-chunk-documents
[22] Introducing a new hyper-parameter for RAG: Context Window ... - arXiv https://arxiv.org/html/2407.19794v2
[23] 7 Chunking Strategies in RAG You Need To Know - F22 Labs https://www.f22labs.com/blogs/7-chunking-strategies-in-rag-you-need-to-know/
[24] RAG vs. Long-context LLMs - SuperAnnotate https://www.superannotate.com/blog/rag-vs-long-context-llms
[25] Ever expanding Token Limit's impact on RAG for Software Testing https://www.linkedin.com/pulse/ever-expanding-token-limits-impact-rag-software-testing-shekhar-pjnee
[26] Chunking strategies for RAG tutorial using Granite - IBM https://www.ibm.com/think/tutorials/chunking-strategies-for-rag-with-langchain-watsonx-ai
[27] Optimizing LLM Accuracy - OpenAI API https://platform.openai.com/docs/guides/optimizing-llm-accuracy
[28] Navigating Token Limits - Pathway https://pathway.com/bootcamps/rag-and-llms/coursework/module-3-prompt-engineering-and-token-limits/navigating-token-limits
[29] Mastering Chunking Strategies for RAG - Databricks Community https://community.databricks.com/t5/technical-blog/the-ultimate-guide-to-chunking-strategies-for-rag-applications/ba-p/113089
[30] Chunking Strategies for LLM Applications - Pinecone https://www.pinecone.io/learn/chunking-strategies/
[31] How do you manage your context tokens for RAG? - API https://community.openai.com/t/how-do-you-manage-your-context-tokens-for-rag/655695
[32] RAG 101: Chunking Strategies - Towards Data Science https://towardsdatascience.com/rag-101-chunking-strategies-fdc6f6c2aaec/
[33] Retrieval Augmented Generation (RAG) for LLMs https://www.promptingguide.ai/research/rag
[34] 5 Approaches to Solve LLM Token Limits - Deepchecks https://www.deepchecks.com/5-approaches-to-solve-llm-token-limits/
[35] Mastering RAG: Advanced Chunking Techniques for LLM Applications https://galileo.ai/blog/mastering-rag-advanced-chunking-techniques-for-llm-applications
[36] Evaluating Multi-turn Conversations - Ragas https://docs.ragas.io/en/stable/howtos/applications/evaluating_multi_turn_conversations/
[37] HM-RAG: Hierarchical Multi-Agent Multimodal Retrieval Augmented ... https://arxiv.org/html/2504.12330v1
[38] Multi-Turn Conversational Chat Bot - GitHub Pages https://nvidia.github.io/GenerativeAIExamples/0.5.0/multi-turn.html
[39] What chunking strategies work best for document indexing? - Milvus https://milvus.io/ai-quick-reference/what-chunking-strategies-work-best-for-document-indexing
[40] Hybrid AI for Responsive Multi-Turn Online Conversations with ... https://aclanthology.org/2025.knowledgenlp-1.20/
[41] MTRAG: A Multi-Turn Conversational Benchmark for Evaluating ... https://arxiv.org/abs/2501.03468
[42] unstablebrainiac/Hierarchical-RAG - GitHub https://github.com/unstablebrainiac/Hierarchical-RAG
[43] A Visual Exploration of Semantic Text Chunking https://towardsdatascience.com/a-visual-exploration-of-semantic-text-chunking-6bb46f728e30/
[44] Multi turn conversation and RAG : r/LocalLLaMA - Reddit https://www.reddit.com/r/LocalLLaMA/comments/1fi1kex/multi_turn_conversation_and_rag/
[45] Building Hierarchical Multi-Agent RAG Systems - YouTube https://www.youtube.com/watch?v=XUfyPxw7yp0
[46] LexRAG: Benchmarking Retrieval-Augmented Generation in Multi ... https://arxiv.org/abs/2502.20640
[47] Hierarchical Retrieval Augmented Generation (HRAG) - Turingbots https://turingbots.ai/hierarchical-retrieval-augmented-generation-hrag/
[48] Semantic Chunking - 3 Methods for Better RAG - YouTube https://www.youtube.com/watch?v=7JS0pqXvha8
[49] lost in conversation. All models get lost easily in multi-turn ... https://community.openai.com/t/interesting-research-lost-in-conversation-all-models-get-lost-easily-in-multi-turn-conversations/1266901
[50] How to truncate context for LLMs with small context windows - Telnyx https://telnyx.com/resources/truncate-context
[51] Build a Retrieval Augmented Generation (RAG) App: Part 1 https://python.langchain.com/docs/tutorials/rag/
[52] Building Performant RAG Applications for Production - LlamaIndex https://docs.llamaindex.ai/en/stable/optimizing/production_rag/
[53] Chunk Twice, Embed Once: A Systematic Study of Segmentation ... https://arxiv.org/html/2506.17277v1
[54] 2 Approaches For Extending Context Windows in LLMs https://supermemory.ai/blog/extending-context-windows-in-llms/
[55] Embedding content in PostgreSQL using Python https://www.postgresql.fastware.com/blog/embedding-content-in-postgresql-using-python
[56] LLM Context Window Paradox: 5 Ways to Solve the Problem https://datasciencedojo.com/blog/the-llm-context-window-paradox/
[57] SlimRAG: Retrieval without Graphs via Entity-Aware Context Selection https://arxiv.org/abs/2506.17288
[58] Best Practices for Production-Scale RAG Systems - Orkes https://orkes.io/blog/rag-best-practices/
[59] How To Overcome Context Limits in Large Language Models https://relevanceai.com/blog/how-to-overcome-context-limits-in-large-language-models
[60] Introducing Contextual Retrieval - Anthropic https://www.anthropic.com/news/contextual-retrieval
[61] LLM Summarization of Large Documents: How to Make It Work https://belitsoft.com/llm-summarization
[62] Summarize Text | 🦜️   LangChain https://python.langchain.com/docs/tutorials/summarization/
[63] Multi-Head RAG: Solving Multi-Aspect Problems with LLMs - arXiv https://arxiv.org/html/2406.05085v1
[64] How to summarize large Research articles? - API https://community.openai.com/t/how-to-summarize-large-research-articles/142730
[65] An Easy Introduction to Multimodal Retrieval-Augmented Generation https://developer.nvidia.com/blog/an-easy-introduction-to-multimodal-retrieval-augmented-generation/
[66] Parse and chunk documents | AI Applications - Google Cloud https://cloud.google.com/generative-ai-app-builder/docs/parse-chunk-documents
[67] Summarization on long documents - Hugging Face Forums https://discuss.huggingface.co/t/summarization-on-long-documents/920
[68] Multi-agent RAG System - Hugging Face Open-Source AI Cookbook https://huggingface.co/learn/cookbook/multiagent_rag_system
[69] Chunking Idea: Summarize Chunks for better retrieval : r/LangChain https://www.reddit.com/r/LangChain/comments/1bbdgpj/chunking_idea_summarize_chunks_for_better/
[70] Optimizing RAG Chunk Size: Your Definitive Guide to Better ... https://www.machinelearningplus.com/gen-ai/optimizing-rag-chunk-size-your-definitive-guide-to-better-retrieval-accuracy/
[71] Optimizing RAG Context: Chunking and Summarization for ... https://dev.to/oleh-halytskyi/optimizing-rag-context-chunking-and-summarization-for-technical-docs-3pel
[72] Retrieval Augmented Generation (RAG) in Azure AI Search https://learn.microsoft.com/en-us/azure/search/retrieval-augmented-generation-overview
