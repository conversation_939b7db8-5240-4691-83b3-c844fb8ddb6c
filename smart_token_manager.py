#!/usr/bin/env python3
"""
Smart Token Manager for RAG Systems
Handles intelligent token allocation and content truncation for LLM context windows.
"""

import tiktoken
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod


@dataclass
class TokenBudget:
    """Manages token allocation across different parts of the prompt"""
    total_limit: int
    system_prompt: int = 2000
    user_query: int = 1000
    response_buffer: int = 4000
    
    @property
    def available_for_context(self) -> int:
        return self.total_limit - self.system_prompt - self.user_query - self.response_buffer


class TokenCounter:
    """Handles token counting for different models"""
    def __init__(self, model: str = "claude-3"):
        self.model = model
        if "gpt" in model.lower():
            self.encoding = tiktoken.encoding_for_model("gpt-4")
        else:
            # Approximate for <PERSON> using GPT-4 encoding
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))


class CodeTruncationStrategy(ABC):
    """Base class for different truncation strategies"""
    
    @abstractmethod
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        pass


class FunctionTruncation(CodeTruncationStrategy):
    """Truncates functions intelligently, preserving signature and key logic"""
    
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        lines = code.split('\n')
        result = []
        counter = TokenCounter()
        current_tokens = 0
        
        # Phase 1: Always include function signature and opening
        signature_found = False
        for i, line in enumerate(lines):
            result.append(line)
            current_tokens += counter.count(line + '\n')
            
            # Look for function signature patterns
            if any(pattern in line for pattern in ['{', '=>', 'function', 'def ', 'class ']):
                signature_found = True
                if i < len(lines) - 1:
                    # Include first line of body
                    result.append(lines[i + 1])
                    current_tokens += counter.count(lines[i + 1] + '\n')
                break
        
        if current_tokens >= max_tokens * 0.8:
            result.append('    // ... function body truncated ...')
            return '\n'.join(result)
        
        # Phase 2: Include important patterns
        important_patterns = [
            r'return\s+',
            r'throw\s+',
            r'export\s+',
            r'async\s+',
            r'await\s+',
            r'\.then\(',
            r'\.catch\(',
            r'console\.',
            r'if\s*\(',
            r'for\s*\(',
            r'while\s*\(',
        ]
        
        # Find important lines
        important_lines = []
        for i, line in enumerate(lines[len(result):], start=len(result)):
            if any(re.search(pattern, line) for pattern in important_patterns):
                important_lines.append((i, line))
        
        # Add important lines within budget
        for idx, line in important_lines:
            line_tokens = counter.count(line + '\n')
            if current_tokens + line_tokens < max_tokens * 0.9:
                # Add with context indicator
                result.append(f"    // Line {idx}: {line.strip()}")
                current_tokens += line_tokens
        
        if len(result) < len(lines):
            result.append('    // ... rest of function truncated ...')
        
        return '\n'.join(result)


class GeneralTruncation(CodeTruncationStrategy):
    """General truncation strategy for non-function code"""
    
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        counter = TokenCounter()
        
        if counter.count(code) <= max_tokens:
            return code
        
        lines = code.split('\n')
        result = []
        current_tokens = 0
        
        # Include lines until we hit the limit
        for line in lines:
            line_tokens = counter.count(line + '\n')
            if current_tokens + line_tokens > max_tokens * 0.9:
                result.append('// ... content truncated ...')
                break
            result.append(line)
            current_tokens += line_tokens
        
        return '\n'.join(result)


class SmartTokenManager:
    """Main class for intelligent token management in RAG systems"""
    
    def __init__(self, model_limit: int = 150000):
        self.budget = TokenBudget(total_limit=model_limit)
        self.counter = TokenCounter()
        self.function_truncator = FunctionTruncation()
        self.general_truncator = GeneralTruncation()
    
    def format_code_context_smart(self, sources: List[Dict[str, Any]], query: str = "") -> str:
        """
        Format retrieved code chunks with intelligent token management
        
        Args:
            sources: List of code chunks with metadata
            query: User query for context-aware prioritization
            
        Returns:
            Formatted context string within token limits
        """
        context = "=== RELEVANT CODE CHUNKS ===\n\n"
        current_tokens = self.counter.count(context)
        
        # Reserve tokens for query context
        query_tokens = self.counter.count(query) if query else 0
        available_tokens = self.budget.available_for_context - query_tokens
        
        # Sort sources by relevance (distance)
        sorted_sources = sorted(sources, key=lambda x: x.get('distance', 1.0))
        
        # Allocate tokens per chunk based on relevance
        total_chunks = len(sorted_sources)
        if total_chunks == 0:
            return context
        
        # Progressive token allocation: more tokens for more relevant chunks
        token_allocations = self._calculate_token_allocations(sorted_sources, available_tokens)
        
        for i, (source, allocated_tokens) in enumerate(zip(sorted_sources, token_allocations)):
            chunk_header = self._create_chunk_header(source, i + 1)
            header_tokens = self.counter.count(chunk_header)
            
            # Calculate available tokens for content
            content_tokens = allocated_tokens - header_tokens
            if content_tokens <= 0:
                break
            
            # Smart truncation based on code type
            truncated_content = self._truncate_content_intelligently(
                source.get('full_content', ''),
                content_tokens,
                source
            )
            
            # Build chunk
            chunk_text = chunk_header
            chunk_text += f"```javascript\n{truncated_content}\n```\n\n"
            
            # Check if adding this chunk would exceed our budget
            chunk_tokens = self.counter.count(chunk_text)
            if current_tokens + chunk_tokens > available_tokens:
                # If this is the first chunk, include it anyway (truncated)
                if i == 0:
                    # Emergency truncation
                    emergency_tokens = available_tokens - current_tokens - header_tokens - 50
                    if emergency_tokens > 0:
                        emergency_content = self._emergency_truncate(
                            source.get('full_content', ''), 
                            emergency_tokens
                        )
                        chunk_text = chunk_header + f"```javascript\n{emergency_content}\n```\n\n"
                        context += chunk_text
                break
            
            context += chunk_text
            current_tokens += chunk_tokens
        
        return context
    
    def _calculate_token_allocations(self, sources: List[Dict[str, Any]], total_tokens: int) -> List[int]:
        """Calculate token allocation for each chunk based on relevance"""
        if not sources:
            return []
        
        # Progressive allocation: first chunk gets more tokens
        allocations = []
        remaining_tokens = total_tokens
        
        for i, source in enumerate(sources):
            if i == 0:
                # First chunk gets 40% of available tokens
                allocation = min(int(total_tokens * 0.4), remaining_tokens)
            elif i == 1:
                # Second chunk gets 30%
                allocation = min(int(total_tokens * 0.3), remaining_tokens)
            elif i == 2:
                # Third chunk gets 20%
                allocation = min(int(total_tokens * 0.2), remaining_tokens)
            else:
                # Remaining chunks share the rest
                remaining_chunks = len(sources) - i
                allocation = remaining_tokens // remaining_chunks if remaining_chunks > 0 else 0
            
            allocations.append(max(allocation, 500))  # Minimum 500 tokens per chunk
            remaining_tokens -= allocation
            
            if remaining_tokens <= 0:
                break
        
        return allocations
    
    def _create_chunk_header(self, source: Dict[str, Any], chunk_num: int) -> str:
        """Create header for a code chunk"""
        header = f"## Chunk {chunk_num}: {source.get('chunk_id', 'unknown')}\n"
        header += f"**File:** {source.get('file_path', 'unknown')}\n"
        header += f"**Relevance:** {1 - source.get('distance', 1.0):.3f}\n"
        
        # Add size info if available
        if 'token_count' in source:
            header += f"**Original Size:** {source['token_count']} tokens\n"
        
        header += "\n"
        return header
    
    def _truncate_content_intelligently(self, content: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        """Apply intelligent truncation based on content type"""
        if not content:
            return ""
        
        # Detect content type
        if self._is_function_like(content):
            return self.function_truncator.truncate(content, max_tokens, metadata)
        else:
            return self.general_truncator.truncate(content, max_tokens, metadata)
    
    def _is_function_like(self, content: str) -> bool:
        """Detect if content is primarily a function or method"""
        function_indicators = [
            'function ',
            'def ',
            'class ',
            '=>',
            'async function',
            'export function',
            'export const',
            'export default'
        ]
        
        # Check first few lines for function indicators
        first_lines = content.split('\n')[:5]
        first_text = '\n'.join(first_lines).lower()
        
        return any(indicator in first_text for indicator in function_indicators)
    
    def _emergency_truncate(self, content: str, max_tokens: int) -> str:
        """Emergency truncation when we're really tight on tokens"""
        if not content:
            return ""
        
        # Simple character-based truncation with buffer
        char_limit = max_tokens * 3  # Rough approximation: 1 token ≈ 3-4 chars
        
        if len(content) <= char_limit:
            return content
        
        # Try to truncate at a reasonable boundary
        truncated = content[:char_limit]
        
        # Find last complete line
        last_newline = truncated.rfind('\n')
        if last_newline > char_limit * 0.7:  # If we can keep most content
            truncated = truncated[:last_newline]
        
        return truncated + '\n// ... [EMERGENCY TRUNCATION] ...'
    
    def estimate_context_tokens(self, sources: List[Dict[str, Any]], query: str = "") -> int:
        """Estimate total tokens that would be used for context"""
        # Quick estimation without full formatting
        total = 0
        total += self.counter.count("=== RELEVANT CODE CHUNKS ===\n\n")
        total += self.counter.count(query) if query else 0
        
        for i, source in enumerate(sources[:5]):  # Estimate for first 5 chunks
            header = self._create_chunk_header(source, i + 1)
            total += self.counter.count(header)
            
            content = source.get('full_content', '')
            # Rough estimation: assume 50% of content will be included
            content_tokens = self.counter.count(content) * 0.5
            total += content_tokens
            
            total += 20  # Code block markers and spacing
        
        return int(total)