#!/usr/bin/env python3
"""
LLM RAG Integration
Combines retrieval-augmented generation with various LLM providers for code analysis.
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import asyncio

# LLM Provider imports
from anthropic import Anthropic
from openai import OpenAI

from project_manager import ProjectManager
from smart_token_manager import SmartTokenManager


class LLMProvider(Enum):
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet-20241022"
    CLAUDE_3_HAIKU = "claude-3-haiku-20240307"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"


@dataclass
class RAGResponse:
    answer: str
    sources: List[Dict[str, Any]]
    query: str
    model_used: str
    tokens_used: Optional[int] = None


class LLMRAGSystem:
    def __init__(self, project_manager: ProjectManager):
        """
        Initialize LLM RAG System
        
        Args:
            project_manager: ProjectManager instance
        """
        self.project_manager = project_manager
        
        # Initialize LLM clients
        self.anthropic_client = None
        self.openai_client = None
        
        # Initialize Smart Token Manager
        self.token_manager = SmartTokenManager(model_limit=150000)  # Conservative limit for Claude
        
        # Initialize clients if API keys are available
        if os.getenv('ANTHROPIC_API_KEY'):
            self.anthropic_client = Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
        
        if os.getenv('OPENAI_API_KEY'):
            self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    def _get_system_prompt(self) -> str:
        """Get optimized system prompt for code analysis with token efficiency"""
        return """You are an expert JavaScript engineer and code analysis assistant. Your primary goal is to answer developer queries by selecting only the most relevant sections of retrieved code.

RESPONSE STRATEGY:
1. **Token Efficiency**: Before including any code, provide a high-level outline of your answer in ≤100 tokens
2. **Code Presentation**: When showing code snippets:
   - Wrap code in ```javascript ... ``` blocks
   - Precede each snippet with: "The following function handles X by..."
   - After each snippet, explain line-by-line what's happening in plain English
   - If a snippet is too large, truncate to core logic and append "(truncated for brevity)"
3. **Context Prioritization**: Always drop low-relevance lines (comments, boilerplate imports) first
4. **Chunk Management**: Include only the top 2-3 most relevant code snippets, each capped at 500 tokens

RESPONSE FORMAT:
• **Quick Summary** (≤100 tokens): High-level answer overview
• **Key Code Sections**: Most relevant snippets with explanations
• **Analysis**: Step-by-step reasoning in bullet form
• **Recommendations**: Best practices and improvements if applicable

INTERACTION RULES:
- If a user's request is ambiguous, ask ONE clarifying question before providing the final answer
- If context exceeds limits, respond: "I've summarized the rest to stay within token limits—see below"
- Reference specific chunk numbers when citing code (e.g., "from chunk_54")
- Be thorough but concise—focus on actionable insights

You will receive code chunks with metadata. Use this context to provide accurate, developer-focused answers that help understand, debug, and improve the codebase."""

    def _format_code_context(self, sources: List[Dict[str, Any]], query: str = "") -> str:
        """
        Format retrieved code chunks for LLM context with intelligent token management
        
        Args:
            sources: List of code chunks with metadata
            query: User query for context-aware prioritization
            
        Returns:
            Formatted context string within token limits
        """
        return self.token_manager.format_code_context_smart(sources, query)
    
    async def _query_claude(self, prompt: str, model: str = LLMProvider.CLAUDE_3_5_SONNET.value) -> Dict[str, Any]:
        """Query Claude API"""
        if not self.anthropic_client:
            raise ValueError("Anthropic API key not configured")
        
        try:
            response = self.anthropic_client.messages.create(
                model=model,
                max_tokens=4000,
                temperature=0.1,
                system=self._get_system_prompt(),
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return {
                "content": response.content[0].text,
                "tokens": response.usage.input_tokens + response.usage.output_tokens,
                "model": model
            }
        except Exception as e:
            raise Exception(f"Claude API error: {str(e)}")
    
    async def _query_openai(self, prompt: str, model: str = LLMProvider.GPT_4O.value) -> Dict[str, Any]:
        """Query OpenAI API"""
        if not self.openai_client:
            raise ValueError("OpenAI API key not configured")
        
        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.1
            )
            
            return {
                "content": response.choices[0].message.content,
                "tokens": response.usage.total_tokens,
                "model": model
            }
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    async def query_with_rag(
        self,
        project_id: str,
        question: str,
        model: LLMProvider = LLMProvider.CLAUDE_3_5_SONNET,
        max_chunks: int = 5,
        use_local_embeddings: bool = True
    ) -> RAGResponse:
        """
        Query the RAG system with LLM integration
        
        Args:
            project_id: Project identifier
            question: User question
            model: LLM model to use
            max_chunks: Maximum number of code chunks to retrieve
            use_local_embeddings: Use local embeddings for retrieval
            
        Returns:
            RAGResponse with answer and sources
        """
        # Get project indexer
        try:
            indexer = self.project_manager.get_project_indexer(
                project_id, 
                use_local_embeddings=use_local_embeddings
            )
        except Exception as e:
            raise ValueError(f"Failed to get project indexer: {str(e)}")
        
        # Retrieve relevant code chunks
        try:
            results = indexer.query(question, n_results=max_chunks)
            sources = results['formatted_results']
        except Exception as e:
            raise ValueError(f"Failed to retrieve code chunks: {str(e)}")
        
        if not sources:
            return RAGResponse(
                answer="I couldn't find any relevant code chunks for your question. Please make sure the project is properly indexed.",
                sources=[],
                query=question,
                model_used=model.value
            )
        
        # Format context for LLM
        code_context = self._format_code_context(sources, question)
        
        # Create prompt
        prompt = f"""Based on the following code chunks from the codebase, please answer this question:

**Question:** {question}

{code_context}

Please provide a comprehensive answer based on the code context above."""
        
        # Query LLM
        try:
            if model.value.startswith("claude"):
                llm_response = await self._query_claude(prompt, model.value)
            elif model.value.startswith("gpt"):
                llm_response = await self._query_openai(prompt, model.value)
            else:
                raise ValueError(f"Unsupported model: {model.value}")
            
            return RAGResponse(
                answer=llm_response["content"],
                sources=sources,
                query=question,
                model_used=llm_response["model"],
                tokens_used=llm_response["tokens"]
            )
            
        except Exception as e:
            raise Exception(f"LLM query failed: {str(e)}")
    
    def get_available_models(self) -> List[LLMProvider]:
        """Get list of available models based on configured API keys"""
        available = []
        
        if self.anthropic_client:
            available.extend([
                LLMProvider.CLAUDE_3_5_SONNET,
                LLMProvider.CLAUDE_3_HAIKU
            ])
        
        if self.openai_client:
            available.extend([
                LLMProvider.GPT_4O,
                LLMProvider.GPT_4O_MINI
            ])
        
        return available
    
    def get_model_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about available models"""
        return {
            LLMProvider.CLAUDE_3_5_SONNET.value: {
                "name": "Claude 3.5 Sonnet",
                "provider": "Anthropic",
                "description": "Best for complex code analysis and reasoning",
                "context_window": 200000,
                "recommended": True
            },
            LLMProvider.CLAUDE_3_HAIKU.value: {
                "name": "Claude 3 Haiku",
                "provider": "Anthropic", 
                "description": "Fast and efficient for simple queries",
                "context_window": 200000,
                "recommended": False
            },
            LLMProvider.GPT_4O.value: {
                "name": "GPT-4o",
                "provider": "OpenAI",
                "description": "Strong general-purpose model",
                "context_window": 128000,
                "recommended": True
            },
            LLMProvider.GPT_4O_MINI.value: {
                "name": "GPT-4o Mini",
                "provider": "OpenAI",
                "description": "Cost-effective option for simple queries",
                "context_window": 128000,
                "recommended": False
            }
        }


class ConversationManager:
    """Manages conversation history for chat interface with project-based persistence"""
    
    def __init__(self, project_manager: Optional[ProjectManager] = None):
        self.conversations: Dict[str, List[Dict[str, Any]]] = {}
        self.conversation_to_project: Dict[str, str] = {}  # Maps conversation_id to project_id
        self.project_manager = project_manager
        
        # Initialize conversation mapping from existing projects
        self._rebuild_conversation_mapping()
    
    def _rebuild_conversation_mapping(self):
        """Rebuild conversation-to-project mapping from all existing projects"""
        if not self.project_manager:
            return
            
        try:
            # Get all projects
            projects = self.project_manager.list_projects()
            
            for project in projects:
                project_id = project['id']
                # Load conversations for this project
                project_conversations = self._load_project_conversations(project_id)
                
                # Update mapping for each conversation
                for conv_id in project_conversations.keys():
                    self.conversation_to_project[conv_id] = project_id
                    
            print(f"Rebuilt conversation mapping for {len(self.conversation_to_project)} conversations")
        except Exception as e:
            print(f"Error rebuilding conversation mapping: {e}")
    
    def _get_conversations_file(self, project_id: str) -> Optional[str]:
        """Get the conversations file path for a project"""
        if not self.project_manager:
            return None
        project_dir = self.project_manager.projects_dir / project_id
        return str(project_dir / "conversations.json")
    
    def _load_project_conversations(self, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Load conversations from project file"""
        conversations_file = self._get_conversations_file(project_id)
        if not conversations_file or not os.path.exists(conversations_file):
            return {}
        
        try:
            with open(conversations_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading conversations for project {project_id}: {e}")
            return {}
    
    def _save_project_conversations(self, project_id: str, conversations: Dict[str, List[Dict[str, Any]]]):
        """Save conversations to project file"""
        conversations_file = self._get_conversations_file(project_id)
        if not conversations_file:
            return
        
        try:
            # Ensure project directory exists
            os.makedirs(os.path.dirname(conversations_file), exist_ok=True)
            
            with open(conversations_file, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving conversations for project {project_id}: {e}")
    
    def start_conversation(self, project_id: str) -> str:
        """Start a new conversation for a project"""
        import uuid
        conversation_id = str(uuid.uuid4())
        
        # Track which project this conversation belongs to
        self.conversation_to_project[conversation_id] = project_id
        
        # Initialize empty conversation
        self.conversations[conversation_id] = []
        
        return conversation_id
    
    def add_message(self, conversation_id: str, role: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a message to conversation history and persist it"""
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = []
        
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        
        self.conversations[conversation_id].append(message)
        
        # Persist to project file if we have project manager
        project_id = self.conversation_to_project.get(conversation_id)
        if project_id and self.project_manager:
            # Load existing conversations for this project
            project_conversations = self._load_project_conversations(project_id)
            
            # Update with current conversation
            project_conversations[conversation_id] = self.conversations[conversation_id]
            
            # Save back to file
            self._save_project_conversations(project_id, project_conversations)
    
    def get_conversation(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get conversation history, loading from disk if needed"""
        # If conversation is in memory, return it
        if conversation_id in self.conversations:
            return self.conversations[conversation_id]
        
        # Try to load from project files
        project_id = self.conversation_to_project.get(conversation_id)
        if project_id and self.project_manager:
            project_conversations = self._load_project_conversations(project_id)
            if conversation_id in project_conversations:
                # Load into memory for faster access
                self.conversations[conversation_id] = project_conversations[conversation_id]
                return self.conversations[conversation_id]
        
        return []
    
    def get_project_conversations(self, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get all conversations for a project"""
        if not self.project_manager:
            # Return conversations from memory that belong to this project
            project_conversations = {}
            for conv_id, proj_id in self.conversation_to_project.items():
                if proj_id == project_id and conv_id in self.conversations:
                    project_conversations[conv_id] = self.conversations[conv_id]
            return project_conversations
        
        # Load conversations from disk and rebuild mapping
        project_conversations = self._load_project_conversations(project_id)
        
        # Rebuild conversation_to_project mapping for loaded conversations
        for conv_id in project_conversations.keys():
            self.conversation_to_project[conv_id] = project_id
        
        return project_conversations
    
    def clear_conversation(self, conversation_id: str):
        """Clear conversation history"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
        
        # Also remove from project file
        project_id = self.conversation_to_project.get(conversation_id)
        if project_id and self.project_manager:
            project_conversations = self._load_project_conversations(project_id)
            if conversation_id in project_conversations:
                del project_conversations[conversation_id]
                self._save_project_conversations(project_id, project_conversations)
        
        # Remove from mapping
        if conversation_id in self.conversation_to_project:
            del self.conversation_to_project[conversation_id]
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation (alias for clear_conversation)"""
        if conversation_id not in self.conversations:
            return False
        
        self.clear_conversation(conversation_id)
        return True
    
    def delete_project_conversations(self, project_id: str):
        """Delete all conversations for a project"""
        # Remove from memory
        conversations_to_remove = [
            conv_id for conv_id, proj_id in self.conversation_to_project.items()
            if proj_id == project_id
        ]
        
        for conv_id in conversations_to_remove:
            if conv_id in self.conversations:
                del self.conversations[conv_id]
            if conv_id in self.conversation_to_project:
                del self.conversation_to_project[conv_id]
        
        # Remove conversations file
        conversations_file = self._get_conversations_file(project_id)
        if conversations_file and os.path.exists(conversations_file):
            try:
                os.remove(conversations_file)
            except Exception as e:
                print(f"Error deleting conversations file for project {project_id}: {e}")


# Example usage and testing
async def test_llm_rag():
    """Test the LLM RAG integration"""
    project_manager = ProjectManager()
    rag_system = LLMRAGSystem(project_manager)
    
    # List available models
    available_models = rag_system.get_available_models()
    print(f"Available models: {[model.value for model in available_models]}")
    
    # Get model info
    model_info = rag_system.get_model_info()
    for model, info in model_info.items():
        print(f"{model}: {info['name']} - {info['description']}")


if __name__ == "__main__":
    asyncio.run(test_llm_rag())