#!/usr/bin/env python3
"""
Fix Collection Name for Enhanced Chunks
Moves enhanced chunks from 'code_chunks' collection to project-specific collection.
"""

import os
import chromadb
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_collection_name(project_id: str):
    """
    Move enhanced chunks from 'code_chunks' to project-specific collection
    
    Args:
        project_id: Project ID to fix
    """
    project_dir = f'projects/{project_id}'
    chroma_path = os.path.join(project_dir, 'chroma_db')
    
    if not os.path.exists(chroma_path):
        logger.error(f"ChromaDB not found at {chroma_path}")
        return False
    
    # Initialize ChromaDB client
    client = chromadb.PersistentClient(path=chroma_path)
    
    # Check existing collections
    collections = client.list_collections()
    collection_names = [c.name for c in collections]
    
    logger.info(f"Found collections: {collection_names}")
    
    # Check if we have the wrong collection name
    if "code_chunks" in collection_names:
        logger.info("Found 'code_chunks' collection, moving to project-specific collection...")
        
        # Get the source collection
        source_collection = client.get_collection("code_chunks")
        source_count = source_collection.count()
        logger.info(f"Source collection has {source_count} documents")
        
        # Get all data from source collection
        all_data = source_collection.get(include=['documents', 'metadatas', 'embeddings'])
        
        # Create target collection with project-specific name
        target_collection_name = f"project_{project_id}"
        target_collection = client.get_or_create_collection(
            name=target_collection_name,
            metadata={"description": f"Enhanced code chunks for project {project_id}"}
        )
        
        # Add all data to target collection
        if all_data['ids']:
            target_collection.add(
                ids=all_data['ids'],
                documents=all_data['documents'],
                metadatas=all_data['metadatas'],
                embeddings=all_data['embeddings']
            )
            
            target_count = target_collection.count()
            logger.info(f"Successfully moved {target_count} documents to '{target_collection_name}' collection")
            
            # Delete the old collection
            client.delete_collection("code_chunks")
            logger.info("Deleted old 'code_chunks' collection")
            
            return True
        else:
            logger.warning("No data found in source collection")
            return False
    
    elif f"project_{project_id}" in collection_names:
        project_collection = client.get_collection(f"project_{project_id}")
        count = project_collection.count()
        logger.info(f"Project collection already exists with {count} documents")
        return True
    
    else:
        logger.error("No suitable collection found")
        return False

if __name__ == "__main__":
    # Fix the specific project
    project_id = "d8bd02a2-4fba-4b08-839f-dd60738e4bcf"
    success = fix_collection_name(project_id)
    
    if success:
        print(f"✅ Successfully fixed collection name for project {project_id}")
    else:
        print(f"❌ Failed to fix collection name for project {project_id}")