#!/usr/bin/env python3
"""
Code Indexer - Build a lightweight retrieval-augmented pipeline for code Q&A
Embeds code chunks and stores them in ChromaDB for interactive querying.
"""

import os
import glob
import chromadb
from chromadb.config import Settings
import tiktoken
from openai import OpenAI
import json
from typing import List, Dict, Any
import argparse

class CodeIndexer:
    def __init__(self, openai_api_key: str = None, use_local_embeddings: bool = False):
        """
        Initialize the Code Indexer
        
        Args:
            openai_api_key: OpenAI API key for embeddings (optional if using local)
            use_local_embeddings: Use local all-MiniLM-L6-v2 model instead of OpenAI
        """
        self.use_local_embeddings = use_local_embeddings
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(path="./chroma_db")
        
        # Create or get collection
        self.collection = self.client.get_or_create_collection(
            name="code_chunks",
            metadata={"description": "Code chunks for RAG Q&A"}
        )
        
        # Initialize OpenAI client if using OpenAI embeddings
        if not use_local_embeddings:
            if not openai_api_key:
                openai_api_key = os.getenv('OPENAI_API_KEY')
            if not openai_api_key:
                raise ValueError("OpenAI API key required. Set OPENAI_API_KEY environment variable or pass as parameter.")
            self.openai_client = OpenAI(api_key=openai_api_key)
        else:
            # For local embeddings, we'll use sentence-transformers
            try:
                from sentence_transformers import SentenceTransformer
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            except ImportError:
                raise ImportError("sentence-transformers required for local embeddings. Install with: pip install sentence-transformers")
        
        # Initialize tokenizer for counting tokens
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using OpenAI or local model"""
        if self.use_local_embeddings:
            return self.embedding_model.encode(text).tolist()
        else:
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.tokenizer.encode(text))
    
    def load_chunks(self, chunk_pattern: str = "chunk_*.js") -> List[Dict[str, Any]]:
        """Load all code chunks from files"""
        chunk_files = glob.glob(chunk_pattern)
        chunk_files.sort(key=lambda x: int(x.split('_')[1].split('.')[0]))  # Sort by chunk number
        
        chunks = []
        for file_path in chunk_files:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            chunk_id = os.path.splitext(os.path.basename(file_path))[0]
            token_count = self.count_tokens(content)
            
            chunks.append({
                'id': chunk_id,
                'content': content,
                'file_path': file_path,
                'token_count': token_count,
                'char_count': len(content)
            })
        
        return chunks
    
    def index_chunks(self, chunks: List[Dict[str, Any]], batch_size: int = 10):
        """Index chunks in ChromaDB with embeddings"""
        print(f"Indexing {len(chunks)} chunks...")
        
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            
            # Prepare batch data
            ids = [chunk['id'] for chunk in batch]
            documents = [chunk['content'] for chunk in batch]
            metadatas = [{
                'file_path': chunk['file_path'],
                'token_count': chunk['token_count'],
                'char_count': chunk['char_count']
            } for chunk in batch]
            
            # Get embeddings for batch
            print(f"Processing batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")
            embeddings = []
            for doc in documents:
                embedding = self.get_embedding(doc)
                embeddings.append(embedding)
            
            # Add to collection
            self.collection.add(
                ids=ids,
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings
            )
        
        print(f"Successfully indexed {len(chunks)} chunks!")
    
    def query(self, query_text: str, n_results: int = 5) -> Dict[str, Any]:
        """Query the indexed chunks"""
        results = self.collection.query(
            query_texts=[query_text],
            n_results=n_results
        )
        
        return {
            'query': query_text,
            'results': results,
            'formatted_results': self._format_results(results)
        }
    
    def _format_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format query results for display"""
        formatted = []
        
        if not results['documents'] or not results['documents'][0]:
            return formatted
        
        documents = results['documents'][0]
        metadatas = results['metadatas'][0] if results['metadatas'] else [{}] * len(documents)
        distances = results['distances'][0] if results['distances'] else [0] * len(documents)
        ids = results['ids'][0] if results['ids'] else list(range(len(documents)))
        
        for i, (doc, metadata, distance, chunk_id) in enumerate(zip(documents, metadatas, distances, ids)):
            formatted.append({
                'rank': i + 1,
                'chunk_id': chunk_id,
                'file_path': metadata.get('file_path', 'unknown'),
                'token_count': metadata.get('token_count', 0),
                'char_count': metadata.get('char_count', 0),
                'distance': distance,
                'preview': doc[:400] + "..." if len(doc) > 400 else doc,
                'full_content': doc
            })
        
        return formatted
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the indexed collection"""
        count = self.collection.count()
        return {
            'total_chunks': count,
            'collection_name': self.collection.name
        }

def main():
    parser = argparse.ArgumentParser(description='Index code chunks for RAG Q&A')
    parser.add_argument('--index', action='store_true', help='Index the code chunks')
    parser.add_argument('--query', type=str, help='Query the indexed chunks')
    parser.add_argument('--local', action='store_true', help='Use local embeddings instead of OpenAI')
    parser.add_argument('--results', type=int, default=5, help='Number of results to return (default: 5)')
    parser.add_argument('--stats', action='store_true', help='Show collection statistics')
    
    args = parser.parse_args()
    
    try:
        # Initialize indexer
        indexer = CodeIndexer(use_local_embeddings=args.local)
        
        if args.stats:
            stats = indexer.get_collection_stats()
            print(f"Collection Statistics:")
            print(f"  Total chunks: {stats['total_chunks']}")
            print(f"  Collection name: {stats['collection_name']}")
            return
        
        if args.index:
            # Load and index chunks
            chunks = indexer.load_chunks()
            if not chunks:
                print("No chunk files found. Make sure to run split.js first.")
                return
            
            print(f"Found {len(chunks)} chunks to index")
            total_tokens = sum(chunk['token_count'] for chunk in chunks)
            total_chars = sum(chunk['char_count'] for chunk in chunks)
            print(f"Total tokens: {total_tokens:,}")
            print(f"Total characters: {total_chars:,}")
            
            indexer.index_chunks(chunks)
            
        elif args.query:
            # Query the collection
            results = indexer.query(args.query, n_results=args.results)
            
            print(f"\nQuery: {results['query']}")
            print("=" * 50)
            
            for result in results['formatted_results']:
                print(f"\n[{result['rank']}] {result['chunk_id']} (distance: {result['distance']:.4f})")
                print(f"File: {result['file_path']}")
                print(f"Tokens: {result['token_count']}, Chars: {result['char_count']}")
                print(f"Preview:\n{result['preview']}")
                print("-" * 40)
        
        else:
            print("Use --index to index chunks or --query 'your question' to search")
            print("Example: python code_indexer.py --index")
            print("Example: python code_indexer.py --query 'Where does the token refresh logic live?'")
    
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())