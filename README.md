# Code RAG System - Interactive Q&A for Large Codebases

A lightweight retrieval-augmented generation (RAG) pipeline that enables interactive Q&A on large JavaScript codebases. This system chunks your code, creates embeddings, and allows you to query specific functionality without hitting context limits.

## 🚀 Quick Start

### 1. Chunk Your Code
```bash
node split.js
```
This splits your `path.js` file into manageable chunks (~2000 lines or ~150k chars each).

### 2. Install Dependencies
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install chromadb tiktoken openai sentence-transformers
```

### 3. Index the Code Chunks
```bash
# Using local embeddings (recommended for privacy)
python code_indexer.py --index --local

# Or using OpenAI embeddings (requires OPENAI_API_KEY)
export OPENAI_API_KEY="your-api-key"
python code_indexer.py --index
```

### 4. Query Your Code
```bash
# Command line queries
python code_indexer.py --query "Where does the token refresh logic live?" --local

# Interactive interface
python interactive_qa.py
```

## 📁 Files Created

- **71 chunk files** (`chunk_0.js` to `chunk_70.js`) - Your code split into searchable pieces
- **`chroma_db/`** - Vector database storing embeddings and metadata
- **`code_indexer.py`** - Main indexing and querying script
- **`interactive_qa.py`** - User-friendly interactive interface
- **`test_rag_system.py`** - Test script demonstrating functionality

## 🔍 Usage Examples

### Command Line Queries
```bash
# Find authentication code
python code_indexer.py --query "Show me authentication code" --local --results 5

# Find error handling
python code_indexer.py --query "How is error handling implemented?" --local

# Find HTTP request functions
python code_indexer.py --query "Find functions that handle HTTP requests" --local
```

### Interactive Mode
```bash
python interactive_qa.py
```

Interactive commands:
- `help` - Show available commands
- `stats` - Show collection statistics
- `results <n>` - Set number of results (1-20)
- `quit` - Exit the program

## 🛠️ System Architecture

1. **Code Chunking** (`split.js`)
   - Parses JavaScript with Babel AST
   - Splits into ~2000 lines or ~150k character chunks
   - Preserves function boundaries

2. **Embedding & Indexing** (`code_indexer.py`)
   - Uses sentence-transformers (all-MiniLM-L6-v2) for local embeddings
   - Or OpenAI text-embedding-3-small for cloud embeddings
   - Stores in ChromaDB vector database

3. **Retrieval & Query** 
   - Semantic search finds relevant code chunks
   - Returns top-k most similar chunks
   - Shows previews and full content on demand

## 📊 Statistics

- **Total chunks**: 71
- **Total tokens**: 3,919,595
- **Total characters**: 10,514,820
- **Average chunk size**: ~55k tokens, ~148k chars

## 🔧 Configuration Options

### Embedding Models
- **Local**: `all-MiniLM-L6-v2` (384 dimensions, runs offline)
- **Cloud**: `text-embedding-3-small` (1536 dimensions, requires API key)

### Query Parameters
- `--results <n>` - Number of results to return (default: 5)
- `--local` - Use local embeddings instead of OpenAI
- `--stats` - Show collection statistics

## 💡 Benefits

✅ **No Context Limits** - Only relevant chunks are passed to analysis  
✅ **Fast Semantic Search** - Find code by meaning, not just keywords  
✅ **Privacy-First** - Local embeddings keep your code private  
✅ **Interactive** - Easy-to-use command line interface  
✅ **Scalable** - Handles large codebases efficiently  

## 🔍 Example Queries That Work Well

- "Where does the token refresh logic live?"
- "How is error handling implemented?"
- "Show me authentication code"
- "Find functions that handle HTTP requests"
- "Where are React components defined?"
- "How is state management implemented?"
- "Find API endpoint definitions"
- "Show me database connection code"

## 🚨 Troubleshooting

### No chunks found
```bash
# Make sure you've run the chunking step
node split.js
```

### No indexed chunks
```bash
# Run the indexing step
python code_indexer.py --index --local
```

### Import errors
```bash
# Activate virtual environment and install dependencies
source venv/bin/activate
pip install chromadb tiktoken openai sentence-transformers
```

## 🎯 Next Steps

1. **Try the interactive interface**: `python interactive_qa.py`
2. **Experiment with different queries** to understand your codebase
3. **Integrate with your development workflow** for code exploration
4. **Extend to other file types** by modifying the chunking logic

---

*Built with ChromaDB, sentence-transformers, and ❤️ for better code understanding*