# RAG System Token Limit Problem - Technical Inquiry

## Problem Summary
We have a fully functional web-based RAG (Retrieval-Augmented Generation) system for code analysis that successfully processes large files and creates semantic chunks, but we're hitting LLM token limits when trying to query the system. Even with only 5 chunks, our prompts exceed <PERSON>'s 200K token limit (213,193 tokens observed).

## Current Technical Architecture

### Core Components
1. **JavaScript Code Splitter** (`split.js`)
   - Uses Babel AST parsing to create semantic code chunks
   - Processes large files (tested with 10.5MB `path.js` → 71 chunks)
   - Maintains code structure and context

2. **Python RAG Backend** (`llm_rag_integration.py`, `code_indexer.py`)
   - ChromaDB for vector storage with local sentence-transformers embeddings
   - FastAPI web server with file upload and chat endpoints
   - Multi-project management with isolated databases
   - Support for Claude 3.5 Sonnet (200K tokens) and GPT-4o (128K tokens)

3. **Web Interface** (`web_backend.py`, HTML frontend)
   - Project management UI
   - File upload with background processing
   - Real-time chat interface with RAG integration
   - Progress tracking and status updates

### Data Flow
```
Large JS File → split.js → Semantic Chunks → ChromaDB Embeddings → Vector Search → LLM Context
```

### Current Token Limits
- **Claude 3.5 Sonnet**: 200,000 tokens maximum
- **GPT-4o**: 128,000 tokens maximum
- **Observed Issue**: 213,193 tokens with just 5 chunks from large file

## Specific Technical Problem

### Location of Issue
File: `llm_rag_integration.py`, method `_format_code_context()` (lines 80-93)

```python
def _format_code_context(self, sources: List[Dict[str, Any]]) -> str:
    """Format retrieved code chunks for LLM context"""
    context = "=== RELEVANT CODE CHUNKS ===\n\n"
    
    for i, source in enumerate(sources, 1):
        context += f"## Chunk {i}: {source['chunk_id']}\n"
        context += f"**File:** {source['file_path']}\n"
        context += f"**Similarity:** {1 - source['distance']:.3f}\n"
        context += f"**Size:** {source['token_count']} tokens\n\n"
        context += "```javascript\n"
        context += source['full_content']  # THIS IS THE PROBLEM LINE
        context += "\n```\n\n"
    
    return context
```

### Root Cause Analysis
- Individual chunks from large files (10.5MB) are extremely large themselves
- Even limiting to 5 chunks via `max_chunks` parameter still exceeds token limits
- The `source['full_content']` contains massive code blocks that overwhelm LLM context windows
- No token counting or truncation logic currently implemented

### Current Request Flow
```
POST /chat → query_with_rag(max_chunks=5) → _format_code_context() → LLM API → 400 Error
```

## What We Need Solutions For

### Primary Question
**How do production RAG systems handle extremely large code chunks that exceed LLM token limits?**

### Specific Technical Challenges
1. **Token Management**: How to implement intelligent truncation without losing semantic meaning?
2. **Chunk Optimization**: Should we re-chunk large files into smaller pieces, or truncate at query time?
3. **Content Prioritization**: How to select the most relevant parts of large chunks?
4. **Multi-turn Strategies**: Should we use multiple smaller queries instead of one large context?

### Architecture Considerations
- We have 71 chunks from a 10.5MB file - some individual chunks are massive
- Vector search is working correctly (retrieving most relevant chunks)
- Need to maintain code context and semantic meaning
- System should work with both Claude and GPT models

## Current Working Components ✅
- File processing and chunking
- Vector embeddings and search
- Web interface and project management
- LLM integration (when under token limits)
- Multi-model support (Claude/GPT)

## Failed Component ❌
- Token limit management in `_format_code_context()`

## Environment Details
- **Backend**: Python 3.13, FastAPI, ChromaDB, sentence-transformers
- **Frontend**: HTML/CSS/JavaScript
- **LLMs**: Claude 3.5 Sonnet, GPT-4o
- **File Types**: JavaScript (extensible to other languages)
- **Scale**: 10.5MB files → 71 chunks, individual chunks can be 40K+ tokens

## Attempted Solutions
- Limiting `max_chunks` to 5 (still exceeds limits)
- Considering token counting and truncation
- Need production-ready approach that maintains code quality

## Search Keywords for Solutions
- "RAG token limit management"
- "Large code chunk truncation strategies"
- "LLM context window optimization"
- "Production RAG systems code analysis"
- "Semantic code chunking best practices"
- "Multi-turn RAG conversations"
- "Token-aware content selection"

## Multi-File Cross-Relational Queries ✅

**YES! The system already supports multiple files per project and cross-file relational queries:**

### Current Multi-File Architecture
- **Project Structure**: Each project can contain multiple files
- **Unified Vector Database**: All files in a project share the same ChromaDB collection
- **Cross-File Search**: Vector search retrieves relevant chunks from ANY file in the project
- **File Metadata**: Each chunk includes `file_path` metadata for source tracking

### How Cross-File Queries Work
1. **Upload Multiple Files**: Web interface supports multiple file upload per project
2. **Unified Processing**: All files are chunked and indexed into the same vector database
3. **Semantic Search**: Queries search across ALL files simultaneously
4. **Contextual Results**: LLM receives chunks from multiple files with source attribution

### Example Multi-File Scenarios
- **"How does authentication work across the frontend and backend?"** → Returns chunks from both `auth.js` and `auth.py`
- **"Show me the data flow from API to UI"** → Returns relevant chunks from multiple layers
- **"Find all error handling patterns"** → Searches across all project files

### Technical Implementation
```python
# Each chunk stores file metadata
chunk_metadata = {
    "file_path": "src/components/Login.js",
    "chunk_id": "chunk_5",
    "token_count": 1250
}

# Vector search returns chunks from multiple files
results = collection.query(
    query_texts=["authentication flow"],
    n_results=5  # May include chunks from different files
)
```

## Expected Solution Types
1. **Truncation Strategies**: Smart ways to cut large chunks while preserving meaning
2. **Re-chunking Approaches**: Better initial chunking strategies for large files
3. **Multi-query Patterns**: Breaking large contexts into multiple smaller queries
4. **Content Summarization**: Using smaller models to summarize large chunks first
5. **Hierarchical RAG**: Multi-level retrieval systems for large codebases

---

*This system is 98% functional with full multi-file support - we just need the final piece to handle token limits intelligently.*