Looking at your RAG system, you've built an impressive architecture that's hitting a common but critical issue. Let me analyze your problem and provide both immediate solutions and architectural recommendations.

## Immediate Solutions for Token Limit Management

### 1. **Smart Truncation with Context Preservation**

Here's an enhanced `_format_code_context()` that implements intelligent truncation:

```python
import tiktoken  # or use a simple approximation

def _format_code_context(self, sources: List[Dict[str, Any]], max_tokens: int = 150000) -> str:
    """Format retrieved code chunks with intelligent token management"""
    # Reserve tokens for system prompt, user query, and response
    context_budget = max_tokens - 20000  # Conservative buffer
    
    context = "=== RELEVANT CODE CHUNKS ===\n\n"
    context_tokens = self._count_tokens(context)
    
    for i, source in enumerate(sources, 1):
        chunk_header = f"## Chunk {i}: {source['chunk_id']}\n"
        chunk_header += f"**File:** {source['file_path']}\n"
        chunk_header += f"**Similarity:** {1 - source['distance']:.3f}\n"
        
        # Calculate available tokens for this chunk
        header_tokens = self._count_tokens(chunk_header + "```javascript\n\n```\n\n")
        available_tokens = (context_budget - context_tokens) // (len(sources) - i + 1)
        
        # Smart truncation of content
        truncated_content = self._truncate_code_intelligently(
            source['full_content'], 
            available_tokens - header_tokens,
            source.get('ast_type', 'unknown')
        )
        
        chunk_text = chunk_header + f"**Size:** {len(truncated_content)} chars (truncated)\n\n"
        chunk_text += "```javascript\n" + truncated_content + "\n```\n\n"
        
        context += chunk_text
        context_tokens += self._count_tokens(chunk_text)
        
        if context_tokens > context_budget:
            break
    
    return context

def _truncate_code_intelligently(self, code: str, max_tokens: int, ast_type: str) -> str:
    """Intelligently truncate code while preserving structure"""
    if self._count_tokens(code) <= max_tokens:
        return code
    
    # Strategy 1: For functions, prioritize signature and early logic
    if ast_type == 'function':
        lines = code.split('\n')
        # Keep function signature and opening
        result = []
        token_count = 0
        
        # Always include function signature
        for i, line in enumerate(lines):
            result.append(line)
            token_count += self._count_tokens(line + '\n')
            if '{' in line or '=>' in line:
                break
        
        # Add body lines until budget exhausted
        for line in lines[i+1:]:
            line_tokens = self._count_tokens(line + '\n')
            if token_count + line_tokens > max_tokens * 0.9:  # Leave some buffer
                result.append('    // ... truncated ...')
                break
            result.append(line)
            token_count += line_tokens
        
        return '\n'.join(result)
    
    # Strategy 2: For other types, use sliding window
    else:
        # Simple character-based truncation with ellipsis
        char_limit = max_tokens * 4  # Rough approximation
        if len(code) > char_limit:
            return code[:char_limit-20] + '\n// ... truncated ...'
        return code
```

### 2. **Hierarchical Chunk Strategy**

Instead of storing massive chunks, create a hierarchy:

```python
class HierarchicalChunker:
    def process_large_file(self, content: str, file_path: str):
        # Level 1: Semantic chunks (your current approach)
        semantic_chunks = self.create_semantic_chunks(content)
        
        # Level 2: Sub-chunks for large semantic chunks
        all_chunks = []
        for chunk in semantic_chunks:
            if self._count_tokens(chunk['content']) > 5000:  # Threshold
                sub_chunks = self.create_sub_chunks(chunk)
                all_chunks.extend(sub_chunks)
            else:
                all_chunks.append(chunk)
        
        return all_chunks
    
    def create_sub_chunks(self, large_chunk: dict) -> List[dict]:
        """Break large chunks into smaller, overlapping pieces"""
        content = large_chunk['content']
        chunks = []
        
        # Use sliding window with overlap
        window_size = 2000  # tokens
        overlap = 200  # tokens
        
        lines = content.split('\n')
        current_chunk = []
        current_tokens = 0
        
        for line in lines:
            line_tokens = self._count_tokens(line)
            if current_tokens + line_tokens > window_size:
                chunks.append({
                    **large_chunk,
                    'content': '\n'.join(current_chunk),
                    'chunk_id': f"{large_chunk['chunk_id']}_sub_{len(chunks)}",
                    'is_sub_chunk': True
                })
                # Keep overlap
                current_chunk = current_chunk[-10:]  # Last 10 lines
                current_tokens = self._count_tokens('\n'.join(current_chunk))
            
            current_chunk.append(line)
            current_tokens += line_tokens
        
        # Don't forget the last chunk
        if current_chunk:
            chunks.append({
                **large_chunk,
                'content': '\n'.join(current_chunk),
                'chunk_id': f"{large_chunk['chunk_id']}_sub_{len(chunks)}",
                'is_sub_chunk': True
            })
        
        return chunks
```

### 3. **Two-Stage RAG Approach**

Implement a more sophisticated query strategy:

```python
async def query_with_staged_rag(self, query: str, project_id: str):
    """Two-stage retrieval for better token management"""
    
    # Stage 1: Get relevant chunks with metadata only
    chunks_metadata = await self.vector_search(
        query, 
        project_id, 
        max_results=20,  # Cast wider net
        include_content=False  # Just metadata
    )
    
    # Stage 2: Intelligent content selection
    selected_content = []
    token_budget = 150000
    used_tokens = 0
    
    for chunk_meta in chunks_metadata:
        # Estimate importance based on similarity and code type
        importance_score = self._calculate_importance(chunk_meta, query)
        
        if importance_score > 0.7:  # High importance - include more
            content = await self._get_chunk_content(chunk_meta['chunk_id'])
            content_preview = self._get_relevant_section(content, query, max_tokens=3000)
        else:  # Lower importance - include less
            content = await self._get_chunk_content(chunk_meta['chunk_id'])
            content_preview = self._get_relevant_section(content, query, max_tokens=1000)
        
        preview_tokens = self._count_tokens(content_preview)
        if used_tokens + preview_tokens < token_budget:
            selected_content.append({
                **chunk_meta,
                'content': content_preview
            })
            used_tokens += preview_tokens
    
    # Generate response with selected content
    return await self._generate_response(query, selected_content)

def _get_relevant_section(self, content: str, query: str, max_tokens: int) -> str:
    """Extract most relevant section of code based on query"""
    # Simple implementation - can be enhanced with more sophisticated matching
    lines = content.split('\n')
    query_terms = query.lower().split()
    
    # Score each line based on query relevance
    line_scores = []
    for i, line in enumerate(lines):
        score = sum(1 for term in query_terms if term in line.lower())
        line_scores.append((i, score))
    
    # Find the highest scoring region
    if not any(score > 0 for _, score in line_scores):
        # No matches, return beginning
        return self._truncate_to_tokens('\n'.join(lines), max_tokens)
    
    # Get the best matching region
    best_idx = max(line_scores, key=lambda x: x[1])[0]
    start = max(0, best_idx - 20)  # Context before
    end = min(len(lines), best_idx + 50)  # More context after
    
    relevant_section = '\n'.join(lines[start:end])
    return self._truncate_to_tokens(relevant_section, max_tokens)
```

## Architectural Evaluation and Recommendations

### Current Architecture Strengths ✅
1. **Semantic chunking with AST** - Excellent approach for maintaining code structure
2. **Vector search with ChromaDB** - Good choice for scalability
3. **Multi-project support** - Clean isolation
4. **Web interface** - User-friendly

### Areas for Improvement 🔧

1. **Chunk Size Strategy**
   - Your 71 chunks from 10.5MB means ~150KB per chunk average
   - This is too large for most LLM contexts
   - **Recommendation**: Target 2-4KB chunks (500-1000 tokens) for better granularity

2. **Retrieval Strategy**
   ```python
   # Enhanced retrieval with re-ranking
   class EnhancedRAG:
       def retrieve_and_rerank(self, query: str, project_id: str):
           # Step 1: Initial retrieval (cast wide net)
           candidates = self.vector_search(query, top_k=50)
           
           # Step 2: Re-rank with cross-encoder
           reranked = self.cross_encoder_rerank(query, candidates, top_k=10)
           
           # Step 3: Context window optimization
           optimized = self.optimize_for_context_window(reranked, max_tokens=150000)
           
           return optimized
   ```

3. **Caching Strategy**
   ```python
   # Add caching for common queries
   from functools import lru_cache
   
   @lru_cache(maxsize=1000)
   def get_cached_embeddings(self, text_hash: str):
       return self.embed_text(text)
   ```

4. **Progressive Disclosure Pattern**
   ```python
   # Instead of dumping all code, start with summaries
   def format_progressive_context(self, chunks: List[Dict]):
       context = "=== CODE OVERVIEW ===\n\n"
       
       # First pass: Just signatures and summaries
       for chunk in chunks:
           context += f"**{chunk['file_path']}** - {chunk['function_name']}:\n"
           context += f"  Purpose: {chunk['summary']}\n"
           context += f"  Relevance: {chunk['similarity']:.2f}\n\n"
       
       context += "\n=== DETAILED CODE (Most Relevant) ===\n\n"
       
       # Second pass: Full code for top 3
       for chunk in chunks[:3]:
           context += format_full_chunk(chunk)
       
       return context
   ```

## Production-Ready Solution

Here's a complete, production-ready token management system:## Alternative Architectural Patterns

### 1. **Streaming RAG Pattern**
Instead of loading everything at once, stream chunks to the LLM:

```python
async def streaming_rag_response(self, query: str, chunks: List[Dict]):
    """Generate response by streaming chunks in multiple LLM calls"""
    
    # Initial context with query
    context = {"role": "system", "content": "You're analyzing code. I'll provide chunks sequentially."}
    messages = [context, {"role": "user", "content": query}]
    
    # Process chunks in batches
    for i in range(0, len(chunks), 3):
        batch = chunks[i:i+3]
        batch_context = self._format_code_context(batch)
        
        messages.append({"role": "user", "content": f"Here's code context batch {i//3 + 1}:\n{batch_context}"})
        
        response = await self.llm.generate(messages)
        messages.append({"role": "assistant", "content": response})
    
    # Final synthesis
    messages.append({"role": "user", "content": "Based on all the code context provided, please provide a comprehensive answer to the original query."})
    final_response = await self.llm.generate(messages)
    
    return final_response
```

### 2. **Hybrid Search with BM25**
Combine vector search with keyword matching:

```python
class HybridSearch:
    def search(self, query: str, project_id: str, top_k: int = 10):
        # Vector search
        vector_results = self.vector_store.search(query, top_k * 2)
        
        # BM25 keyword search
        bm25_results = self.bm25_search(query, project_id, top_k * 2)
        
        # Combine and re-rank
        combined = self._reciprocal_rank_fusion(vector_results, bm25_results)
        
        return combined[:top_k]
```

### 3. **Summary-First Approach**
Pre-generate summaries of large chunks:

```python
class ChunkSummarizer:
    def __init__(self):
        self.summary_model = "gpt-3.5-turbo"  # Smaller, faster model
    
    async def process_chunk(self, chunk: Dict) -> Dict:
        if chunk['token_count'] > 2000:
            summary = await self._generate_summary(chunk['full_content'])
            chunk['summary'] = summary
            chunk['has_summary'] = True
        return chunk
    
    def format_with_summaries(self, chunks: List[Dict]) -> str:
        context = ""
        for chunk in chunks:
            if chunk.get('has_summary'):
                context += f"**Summary of {chunk['chunk_id']}**: {chunk['summary']}\n\n"
                # Include partial code if relevant
                if chunk['similarity'] > 0.9:
                    context += f"**Key excerpt**:\n```javascript\n{chunk['full_content'][:500]}...\n```\n\n"
            else:
                # Small chunk - include fully
                context += self._format_full_chunk(chunk)
        return context
```

## Final Recommendations

1. **Immediate Fix**: Implement the `SmartCodeContextFormatter` from the artifact above. It's a drop-in replacement that will solve your token limit issues.

2. **Re-chunking Strategy**: Consider re-processing your files with smaller chunk targets (2-4KB). This gives you more granular control.

3. **Caching Layer**: Add Redis or similar for caching embeddings and common query results.

4. **Monitoring**: Add token usage tracking to optimize your token allocation over time.

5. **Progressive Enhancement**: Start with summaries, then drill down to specific code sections based on user interaction.

Your architecture is solid - you just need better token management. The solution in the artifact provides production-ready token management that maintains code quality while respecting LLM limits.



import tiktoken
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import re
from abc import ABC, abstractmethod

@dataclass
class TokenBudget:
    """Manages token allocation across different parts of the prompt"""
    total_limit: int
    system_prompt: int = 2000
    user_query: int = 1000
    response_buffer: int = 4000
    
    @property
    def available_for_context(self) -> int:
        return self.total_limit - self.system_prompt - self.user_query - self.response_buffer

class TokenCounter:
    """Handles token counting for different models"""
    def __init__(self, model: str = "claude-3"):
        self.model = model
        if "gpt" in model.lower():
            self.encoding = tiktoken.encoding_for_model("gpt-4")
        else:
            # Approximate for Claude
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count(self, text: str) -> int:
        return len(self.encoding.encode(text))

class CodeTruncationStrategy(ABC):
    """Base class for different truncation strategies"""
    
    @abstractmethod
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        pass

class FunctionTruncation(CodeTruncationStrategy):
    """Truncates functions intelligently, preserving signature and key logic"""
    
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        lines = code.split('\n')
        result = []
        counter = TokenCounter()
        current_tokens = 0
        
        # Phase 1: Always include function signature
        signature_found = False
        for i, line in enumerate(lines):
            result.append(line)
            current_tokens += counter.count(line + '\n')
            
            if any(pattern in line for pattern in ['{', '=>', 'function']):
                signature_found = True
                if i < len(lines) - 1:
                    # Include first line of body
                    result.append(lines[i + 1])
                    current_tokens += counter.count(lines[i + 1] + '\n')
                break
        
        if current_tokens >= max_tokens * 0.8:
            result.append('    // ... function body truncated ...')
            return '\n'.join(result)
        
        # Phase 2: Include important patterns
        important_patterns = [
            r'return\s+',
            r'throw\s+',
            r'export\s+',
            r'async\s+',
            r'await\s+',
            r'\.then\(',
            r'\.catch\(',
        ]
        
        important_lines = []
        for i, line in enumerate(lines[len(result):], start=len(result)):
            if any(re.search(pattern, line) for pattern in important_patterns):
                important_lines.append((i, line))
        
        # Phase 3: Add important lines within budget
        for idx, line in important_lines:
            line_tokens = counter.count(line + '\n')
            if current_tokens + line_tokens < max_tokens * 0.9:
                result.insert(-1 if '// ... function body truncated ...' in result[-1] else len(result), 
                            f"    // Line {idx}: {line.strip()}")
                current_tokens += line_tokens
        
        if len(result) < len(lines):
            result.append('    // ... rest of function truncated ...')
        
        return '\n'.join(result)

class ClassTruncation(CodeTruncationStrategy):
    """Truncates classes, preserving structure and key methods"""
    
    def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
        lines = code.split('\n')
        result = []
        counter = TokenCounter()
        current_tokens = 0
        
        in_method = False
        method_lines = []
        current_method_name = ""
        
        for line in lines:
            # Always include class declaration and structure
            if 'class ' in line or line.strip().startswith(('constructor', 'static', 'get ', 'set ')):
                if method_lines and current_method_name:
                    # Truncate previous method if needed
                    method_tokens = sum(counter.count(l + '\n') for l in method_lines)
                    if current_tokens + method_tokens > max_tokens:
                        result.append(f"    {current_method_name}() {{ /* truncated */ }}")
                    else:
                        result.extend(method_lines)
                        current_tokens += method_tokens
                    method_lines = []
                
                result.append(line)
                current_tokens += counter.count(line + '\n')
                
                if any(pattern in line for pattern in ['(', 'get ', 'set ']):
                    in_method = True
                    current_method_name = self._extract_method_name(line)
                    method_lines = [line]
                    
            elif in_method:
                method_lines.append(line)
                if line.strip() == '}':
                    in_method = False
            else:
                result.append(line)
                current_tokens += counter.count(line + '\n')
            
            if current_tokens > max_tokens * 0.9:
                result.append('    // ... rest of class truncated ...')
                break
        
        return '\n'.join(result)
    
    def _extract_method_name(self, line: str) -> str:
        # Extract method name from line
        match = re.search(r'(\w+)\s*\(', line)
        return match.group(1) if match else "method"

class SmartCodeContextFormatter:
    """Production-ready context formatter with intelligent token management"""
    
    def __init__(self, model: str = "claude-3", token_limit: int = 200000):
        self.model = model
        self.token_counter = TokenCounter(model)
        self.token_budget = TokenBudget(total_limit=token_limit)
        
        # Strategy mapping
        self.truncation_strategies = {
            'function': FunctionTruncation(),
            'class': ClassTruncation(),
            'default': self.DefaultTruncation()
        }
    
    class DefaultTruncation(CodeTruncationStrategy):
        """Default truncation for unknown code types"""
        
        def truncate(self, code: str, max_tokens: int, metadata: Dict[str, Any]) -> str:
            counter = TokenCounter()
            tokens = counter.count(code)
            
            if tokens <= max_tokens:
                return code
            
            # Character-based approximation
            char_ratio = max_tokens / tokens
            char_limit = int(len(code) * char_ratio * 0.9)  # 90% to be safe
            
            if metadata.get('query_match_line'):
                # Try to include the relevant section
                match_line = metadata['query_match_line']
                lines = code.split('\n')
                if 0 <= match_line < len(lines):
                    start = max(0, match_line - 20)
                    end = min(len(lines), match_line + 30)
                    relevant_section = '\n'.join(lines[start:end])
                    
                    if counter.count(relevant_section) <= max_tokens:
                        prefix = "// ... previous code truncated ...\n" if start > 0 else ""
                        suffix = "\n// ... remaining code truncated ..." if end < len(lines) else ""
                        return prefix + relevant_section + suffix
            
            # Fallback to simple truncation
            return code[:char_limit] + "\n// ... truncated ..."
    
    def format_code_context(self, chunks: List[Dict[str, Any]], query: str) -> Tuple[str, Dict[str, int]]:
        """
        Format code chunks with intelligent token management
        
        Returns:
            Tuple of (formatted_context, token_stats)
        """
        available_tokens = self.token_budget.available_for_context
        
        # Pre-process chunks to estimate token distribution
        chunk_priorities = self._prioritize_chunks(chunks, query)
        token_allocation = self._allocate_tokens(chunk_priorities, available_tokens)
        
        context = "=== RELEVANT CODE CONTEXT ===\n\n"
        context_tokens = self.token_counter.count(context)
        
        stats = {
            'total_chunks': len(chunks),
            'included_chunks': 0,
            'total_tokens': context_tokens,
            'truncated_chunks': 0
        }
        
        for i, (chunk, priority) in enumerate(chunk_priorities):
            if context_tokens >= available_tokens * 0.95:
                break
            
            allocated_tokens = token_allocation.get(i, 0)
            if allocated_tokens == 0:
                continue
            
            # Format chunk with allocated token budget
            chunk_text = self._format_single_chunk(
                chunk, 
                i + 1, 
                allocated_tokens,
                query
            )
            
            chunk_tokens = self.token_counter.count(chunk_text)
            if context_tokens + chunk_tokens <= available_tokens:
                context += chunk_text
                context_tokens += chunk_tokens
                stats['included_chunks'] += 1
                
                if "truncated" in chunk_text:
                    stats['truncated_chunks'] += 1
        
        stats['total_tokens'] = context_tokens
        
        # Add summary if we had to skip chunks
        if stats['included_chunks'] < stats['total_chunks']:
            summary = f"\n_Note: Included {stats['included_chunks']} of {stats['total_chunks']} relevant code chunks due to token limits._\n"
            context += summary
        
        return context, stats
    
    def _prioritize_chunks(self, chunks: List[Dict[str, Any]], query: str) -> List[Tuple[Dict, float]]:
        """Calculate priority scores for chunks based on relevance and content type"""
        prioritized = []
        
        for chunk in chunks:
            # Base priority from similarity score
            priority = 1 - chunk.get('distance', 0.5)
            
            # Boost for exact query term matches
            query_terms = query.lower().split()
            content_lower = chunk.get('full_content', '').lower()
            exact_matches = sum(1 for term in query_terms if term in content_lower)
            priority += exact_matches * 0.1
            
            # Boost for certain code types
            if chunk.get('ast_type') == 'function':
                priority += 0.05
            elif chunk.get('ast_type') == 'class':
                priority += 0.03
            
            # Penalty for very large chunks
            if chunk.get('token_count', 0) > 10000:
                priority *= 0.8
            
            prioritized.append((chunk, min(priority, 1.0)))
        
        return sorted(prioritized, key=lambda x: x[1], reverse=True)
    
    def _allocate_tokens(self, prioritized_chunks: List[Tuple[Dict, float]], total_tokens: int) -> Dict[int, int]:
        """Allocate tokens to chunks based on priority"""
        allocation = {}
        remaining_tokens = total_tokens
        
        # Minimum tokens per chunk
        min_tokens = 500
        
        # First pass: allocate proportionally by priority
        total_priority = sum(priority for _, priority in prioritized_chunks)
        
        for i, (chunk, priority) in enumerate(prioritized_chunks):
            if remaining_tokens <= 0:
                break
            
            # Calculate proportional allocation
            proportional = int((priority / total_priority) * total_tokens)
            
            # Apply minimum and maximum constraints
            allocated = max(min_tokens, min(proportional, 5000))
            
            # Don't allocate more than the chunk needs
            chunk_tokens = chunk.get('token_count', float('inf'))
            allocated = min(allocated, chunk_tokens)
            
            if allocated <= remaining_tokens:
                allocation[i] = allocated
                remaining_tokens -= allocated
        
        # Second pass: distribute remaining tokens to high-priority chunks
        if remaining_tokens > 0:
            for i, (chunk, priority) in enumerate(prioritized_chunks[:3]):  # Top 3
                if i in allocation and priority > 0.8:
                    extra = min(remaining_tokens, 1000)
                    allocation[i] += extra
                    remaining_tokens -= extra
                    
                    if remaining_tokens <= 0:
                        break
        
        return allocation
    
    def _format_single_chunk(self, chunk: Dict[str, Any], index: int, max_tokens: int, query: str) -> str:
        """Format a single chunk with token limit"""
        # Header
        header = f"## Chunk {index}: {chunk.get('chunk_id', 'unknown')}\n"
        header += f"**File:** {chunk.get('file_path', 'unknown')}\n"
        header += f"**Type:** {chunk.get('ast_type', 'code')}\n"
        header += f"**Relevance:** {(1 - chunk.get('distance', 0)):.1%}\n"
        
        # Calculate tokens for header and wrapper
        wrapper_tokens = self.token_counter.count(header + "\n```javascript\n\n```\n\n")
        content_budget = max_tokens - wrapper_tokens
        
        # Get content
        content = chunk.get('full_content', '')
        content_tokens = self.token_counter.count(content)
        
        # Truncate if necessary
        if content_tokens > content_budget:
            strategy = self.truncation_strategies.get(
                chunk.get('ast_type', 'default'),
                self.truncation_strategies['default']
            )
            
            # Add query match information for better truncation
            metadata = {
                **chunk,
                'query': query,
                'query_match_line': self._find_query_match_line(content, query)
            }
            
            content = strategy.truncate(content, content_budget, metadata)
            header += f"**Note:** Truncated from {content_tokens} to ~{content_budget} tokens\n"
        
        # Combine
        formatted = header + "\n```javascript\n" + content + "\n```\n\n"
        return formatted
    
    def _find_query_match_line(self, content: str, query: str) -> Optional[int]:
        """Find the line number with the best query match"""
        lines = content.split('\n')
        query_terms = query.lower().split()
        
        best_line = None
        best_score = 0
        
        for i, line in enumerate(lines):
            line_lower = line.lower()
            score = sum(1 for term in query_terms if term in line_lower)
            
            if score > best_score:
                best_score = score
                best_line = i
        
        return best_line

# Usage example
def enhanced_format_code_context(sources: List[Dict[str, Any]], query: str, model: str = "claude-3") -> str:
    """Drop-in replacement for your current _format_code_context"""
    formatter = SmartCodeContextFormatter(model=model)
    context, stats = formatter.format_code_context(sources, query)
    
    # Log stats for monitoring
    print(f"Context formatting stats: {stats}")
    
    return context