Below is a toolkit‑style workflow that practitioners use when they have to reason about a **very large, minified JavaScript bundle** without loading the whole thing into one LLM context window. Most steps rely on free, off‑the‑shelf developer tools; the “AI” piece comes in later once the code has been split and indexed.

---

## 1  Get the best possible source representation first

| Goal                                                  | How                                                                                                                                                                                                                                          |
| ----------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Un‑minify & re‑indent**                             | You’ve already pretty‑printed it (e.g. with Prettier or Chrome DevTools ⇢ Sources ⇢ ⟂ icon). Confirm the result is valid JS by running `node --check file.js`.                                                                               |
| **Recover original names (if you have a source map)** | Look for a `//# sourceMappingURL=` comment or a separate `.map` file in the webapp distribution. Tools such as **`source-map-explorer`** or **Chrome DevTools ⇢ Sources ⇢ Overrides ⇢ Add source map…** will reinflate readable identifiers. |
| **De‑obfuscate when no map is available**             | Run the bundle through **JSNice**, **Prettier‑plugin‑rename**, or **de‑obfuscator.io**. They use statistical models and AST heuristics to rename `a`, `b`, `c` to `getUser`, `setToken`, etc.                                                |

---

## 2  Generate a *structural* map of the file

Instead of reading 52 k lines line‑by‑line, let tools answer *“where is the logic actually concentrated?”*

```bash
# Install once
npm i -g madge @babel/parser
```

| Task                                  | Command / Tool                                                                                                                                                  | What you learn                                                                                                 |
| ------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------- |
| **Top‑level modules & circular deps** | `madge --extension js --circular file.js`                                                                                                                       | Shows logical “chunks” (if it was bundled by Webpack/Rollup) and flags tight coupling.                         |
| **Function catalogue**                | `npx astexplorer` (online) or small Node script with `@babel/parser` → dump every `FunctionDeclaration` & `VariableDeclarator` that contains an arrow function. | Gives you a CSV of *function name → start line → size → parameter list*. Sort by size to spot “god functions”. |
| **Call graph**                        | `madge --image graph.svg file.js` or VS Code plugin *CodeGraph*                                                                                                 | Visual entry points and hot paths.                                                                             |
| **String/constants search**           | `ripgrep "https\?://" file.js`                                                                                                                                  | Quickly surfaces API endpoints, feature flags, analytics keys, etc.                                            |

Save those artefacts (CSV, SVG) – these are perfect, *small* chunks you can feed to an LLM to begin building a mental model.

---

## 3  Let the runtime tell you what matters

Static inspection only goes so far with heavily bundled apps. Run it.

| Technique                           | Tooling                                                                                                                                | What to look at                                                                                              |
| ----------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| **Browser DevTools break‑on‑start** | In Chrome/Edge, open the app, press `⌘/Ctrl + Shift + I` → *Sources* → place a breakpoint inside the first IIFE or at `window.onload`. | Observe which functions fire first; note stack traces.                                                       |
| **Network interception**            | DevTools ⇢ Network or `mitmproxy` in front of the app.                                                                                 | Map outbound requests back to the constant strings you grepped in step 2.                                    |
| **Code coverage**                   | DevTools ⇢ *Performance* → Record, reload page, stop, then *Coverage*.                                                                 | Usually 30–60 % of a production bundle is never executed on first paint. You can ignore huge swathes safely. |
| **Console → debugger statements**   | Insert `debugger;` lines (or a one‑liner Babel transform that injects them) where you believe “core” logic lives; reload.              | Halting execution is often faster than reading.                                                              |

---

## 4  Slice the bundle into LLM‑sized chunks

Even GPT‑4o’s 128 k‑token window is \~400 k chars, so 10.5 M chars must be sharded.

1. **AST‑based splitter (recommended)**

   ```js
   // split.js (Node)
   const fs = require('fs');
   const { parse } = require('@babel/parser');
   const traverse = require('@babel/traverse').default;

   const code = fs.readFileSync('file.js', 'utf8');
   const ast  = parse(code, { sourceType: 'script' });

   let part = 0, out = [];
   traverse(ast, {
     FunctionDeclaration(path) {
       const start = path.node.start;
       const end   = path.node.end;
       out.push({ start, end });
     }
   });

   // chunk each ~2000 lines or ~150 k chars
   ```
2. **Greedy line‑count splitter** – simpler but loses function boundaries:

   ```bash
   split -l 2000 file.js chunks/part_
   ```

Each chunk can now be fed to an LLM for **“write a 10‑line summary”**.
Combine those summaries with a *second* LLM pass (“map/reduce”) to obtain an overall description.

---

## 5  Index chunks for interactive Q\&A

Build a lightweight retrieval‑augmented pipeline on your laptop:

```bash
pip install chromadb tiktoken openai # or ollama/llama.cpp for local models
```

1. **Embed** each chunk (OpenAI `text-embedding-3-small` or `all-MiniLM-L6-v2`).
2. **Store** (id → embedding, plus the raw text).
3. **Query**:

   ```python
   query = "Where does the token refresh logic live?"
   results = collection.query(query_texts=[query], n_results=5)
   print(results['documents'][0][:400])
   ```

Because only the relevant 1‑2 k‐line snippet is passed into the model at answer time, you never hit context limits.

---

## 6  Automated renaming & type inference for deeper dives

* Optional but powerful when the codebase is ≥10 k lines.

| Tool                                              | What it does                                                                                                               |
| ------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- |
| **`ts-migrate` + TypeScript LS**                  | Convert to `file.ts`, annotate obvious `any` types, then let VS Code’s “Go to definition” and refactor tools work for you. |
| **`flow-remove-types` → Flow**                    | Similar if you prefer Flow for type hints.                                                                                 |
| **`eslint --fix` with cognitive‑complexity rule** | Flags extremely tangled functions you should read first.                                                                   |

---

## 7  Security & behaviour audits (if that’s your end goal)

Run the (smaller) pieces through:

* **Semgrep** rulesets (`semgrepjs`) for XSS, insecure crypto, direct DOM manipulation.
* **NodeProf** or **Jalangi2** for dynamic taint analysis (track where user input ultimately hits `eval`, `innerHTML`, etc.).
* **Snyk Code** for a quick SaaS scan.

---

## 8  Putting it all together – a sample one‑day schedule

| Time  | Activity                                                            |
| ----- | ------------------------------------------------------------------- |
| 09:00 | Un‑minify, attempt to locate source map                             |
| 09:30 | Run `madge` & AST script → generate call graph & function CSV       |
| 10:30 | DevTools coverage run; mark cold code grey                          |
| 11:00 | Write Node splitter; chunk hot code into ≤8 k‑token files           |
| 13:00 | LLM “chunk summaries” batch job (parallel requests)                 |
| 15:00 | Reduce summaries into 2‑page design doc                             |
| 16:00 | Interactive embed‑and‑query loop to answer your remaining questions |
| 17:30 | Semgrep / security scan on hot code only                            |

---

### Key takeaways

* **Structure first, content later** – generating maps and graphs lets you ignore 70 % of the noise.
* **Let the browser/runtime tell you where to focus** via coverage and breakpoints.
* **Hierarchical summarisation** (chunk → summary → meta‑summary) is the only practical LLM technique for multi‑million‑char files today.
* **Self‑serve retrieval** (embeddings) means you never paste the whole file into ChatGPT again; you paste only the *2 k* characters that matter for each specific question.

With this toolbox you can usually go from “10 MB black box” to “I have a solid mental model and a two‑page architecture note” in a day or two—even on consumer hardware.
