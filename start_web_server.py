#!/usr/bin/env python3
"""
Startup script for the Code RAG Web System
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import anthropic
        import chromadb
        import sentence_transformers
        print("✅ All Python dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install fastapi uvicorn anthropic chromadb sentence-transformers python-multipart aiofiles")
        return False

def check_node_dependencies():
    """Check if Node.js and required packages are available"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js is installed: {result.stdout.strip()}")
        else:
            print("❌ Node.js is not installed")
            return False
        
        # Check if @babel packages are installed
        try:
            result = subprocess.run(['npm', 'list', '@babel/parser'], capture_output=True, text=True)
            if '@babel/parser' in result.stdout:
                print("✅ Babel packages are installed")
            else:
                print("⚠️  Babel packages not found, installing...")
                subprocess.run(['npm', 'install', '@babel/parser', '@babel/traverse'], check=True)
                print("✅ Babel packages installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install Babel packages")
            return False
        
        return True
    except FileNotFoundError:
        print("❌ Node.js is not installed")
        return False

def setup_environment():
    """Setup environment variables and directories"""
    # Create necessary directories
    Path("projects").mkdir(exist_ok=True)
    print("✅ Created projects directory")
    
    # Check for API keys
    api_keys_found = []
    if os.getenv('ANTHROPIC_API_KEY'):
        api_keys_found.append('Anthropic (Claude)')
    if os.getenv('OPENAI_API_KEY'):
        api_keys_found.append('OpenAI (GPT)')
    
    if api_keys_found:
        print(f"✅ Found API keys for: {', '.join(api_keys_found)}")
    else:
        print("⚠️  No API keys found. Set ANTHROPIC_API_KEY or OPENAI_API_KEY environment variables")
        print("   The system will work with local embeddings only")
    
    return True

def main():
    """Main startup function"""
    print("🚀 Starting Code RAG Web System")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    if not check_node_dependencies():
        sys.exit(1)
    
    if not setup_environment():
        sys.exit(1)
    
    print("\n🌐 Starting web server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🔍 API documentation at: http://localhost:8000/docs")
    print("\n💡 Usage:")
    print("1. Open http://localhost:8000 in your browser")
    print("2. Create a new project and upload JavaScript files")
    print("3. Wait for processing to complete")
    print("4. Start chatting with your code!")
    print("\n" + "=" * 50)
    
    # Start the server
    try:
        import uvicorn
        uvicorn.run("web_backend:app", host="0.0.0.0", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()