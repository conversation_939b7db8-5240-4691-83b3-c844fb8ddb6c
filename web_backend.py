#!/usr/bin/env python3
"""
FastAPI Web Backend for Code RAG System
Provides REST API endpoints for file upload, project management, and chat interface.
"""

import os
import tempfile
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed. Environment variables from .env file will not be loaded.")

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel

from project_manager import ProjectManager
from llm_rag_integration import LLMRAGSystem, LLMProvider, ConversationManager

# Initialize FastAPI app
app = FastAPI(
    title="Code RAG System",
    description="Web-based RAG system for interactive code analysis",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize global instances
project_manager = ProjectManager()
rag_system = LLMRAGSystem(project_manager)
conversation_manager = ConversationManager(project_manager)

# Pydantic models for request/response
class ProjectCreate(BaseModel):
    name: str
    description: str = ""

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: str
    created_at: str
    updated_at: str
    files: List[Dict[str, Any]]
    chunk_count: int
    total_tokens: int
    total_chars: int
    indexed: bool
    enhanced_chunks: bool = True
    processing_status: str = "idle"  # idle, processing, chunking, indexing, ready, error

class ChatMessage(BaseModel):
    project_id: str
    message: str
    conversation_id: Optional[str] = None
    model: str = LLMProvider.CLAUDE_3_5_SONNET.value
    max_chunks: int = 5

class ChatResponse(BaseModel):
    answer: str
    sources: List[Dict[str, Any]]
    conversation_id: str
    model_used: str
    tokens_used: Optional[int] = None

# Background task for processing files
async def process_project_background(project_id: str):
    """Background task to process and index project files"""
    try:
        # Process files (chunk)
        process_result = project_manager.process_project_files(project_id)
        print(f"Processed project {project_id}: {process_result}")
        
        # Index chunks
        index_result = project_manager.index_project(project_id, use_local_embeddings=True)
        print(f"Indexed project {project_id}: {index_result}")
        
    except Exception as e:
        print(f"Error processing project {project_id}: {str(e)}")

# API Routes

@app.get("/")
async def root():
    """Serve the main web interface"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Code RAG System</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .projects-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .project-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .upload-area { background: white; padding: 40px; border-radius: 8px; border: 2px dashed #ddd; text-align: center; margin-bottom: 20px; }
            .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
            .btn:hover { background: #0056b3; }
            .chat-container { background: white; border-radius: 8px; padding: 20px; height: 600px; display: flex; flex-direction: column; }
            .messages { flex: 1; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; }
            .message { margin-bottom: 10px; padding: 10px; border-radius: 4px; }
            .user-message { background: #e3f2fd; margin-left: 20%; }
            .bot-message { background: #f5f5f5; margin-right: 20%; }
            .input-area { display: flex; gap: 10px; }
            .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
            .hidden { display: none; }
            .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
            .status.success { background: #d4edda; color: #155724; }
            .status.error { background: #f8d7da; color: #721c24; }
            .status.info { background: #d1ecf1; color: #0c5460; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 Code RAG System</h1>
                <p>Upload JavaScript files, get intelligent code analysis with AI</p>
            </div>
            
            <div id="upload-section">
                <div class="upload-area">
                    <h3>Create New Project</h3>
                    <input type="text" id="project-name" placeholder="Project Name" style="margin: 10px; padding: 10px; width: 200px;">
                    <input type="text" id="project-description" placeholder="Description (optional)" style="margin: 10px; padding: 10px; width: 300px;">
                    <br>
                    <input type="file" id="file-input" multiple accept=".js,.jsx,.ts,.tsx" style="margin: 10px;">
                    <br>
                    <button class="btn" onclick="uploadFiles()">Create Project & Upload Files</button>
                </div>
            </div>
            
            <div id="status-area"></div>
            
            <div id="projects-section">
                <h2>Your Projects</h2>
                <div id="projects-grid" class="projects-grid"></div>
            </div>
            
            <div id="chat-section" class="hidden">
                <h2>Chat with Your Code</h2>
                <div class="chat-container">
                    <div id="messages" class="messages"></div>
                    <div class="input-area">
                        <input type="text" id="chat-input" placeholder="Ask about your code..." onkeypress="handleChatKeypress(event)">
                        <button class="btn" onclick="sendMessage()">Send</button>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            let currentProjectId = null;
            let currentConversationId = null;
            
            // Load projects on page load
            window.onload = function() {
                loadProjects();
            };
            
            async function uploadFiles() {
                const projectName = document.getElementById('project-name').value;
                const projectDescription = document.getElementById('project-description').value;
                const fileInput = document.getElementById('file-input');
                
                if (!projectName || !fileInput.files.length) {
                    showStatus('Please enter project name and select files', 'error');
                    return;
                }
                
                showStatus('Creating project and uploading files...', 'info');
                
                try {
                    // Create project
                    const projectResponse = await fetch('/api/projects', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ name: projectName, description: projectDescription })
                    });
                    
                    if (!projectResponse.ok) throw new Error('Failed to create project');
                    const project = await projectResponse.json();
                    
                    // Upload files
                    const formData = new FormData();
                    for (let file of fileInput.files) {
                        formData.append('files', file);
                    }
                    
                    const uploadResponse = await fetch(`/api/projects/${project.id}/upload`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!uploadResponse.ok) throw new Error('Failed to upload files');
                    
                    showStatus('Files uploaded! Processing and indexing...', 'info');
                    
                    // Process files
                    const processResponse = await fetch(`/api/projects/${project.id}/process`, {
                        method: 'POST'
                    });
                    
                    if (!processResponse.ok) throw new Error('Failed to process files');
                    
                    showStatus('Project created successfully! You can now chat with your code.', 'success');
                    
                    // Clear form
                    document.getElementById('project-name').value = '';
                    document.getElementById('project-description').value = '';
                    document.getElementById('file-input').value = '';
                    
                    // Reload projects
                    setTimeout(loadProjects, 2000);
                    
                } catch (error) {
                    showStatus(`Error: ${error.message}`, 'error');
                }
            }
            
            async function loadProjects() {
                try {
                    const response = await fetch('/api/projects');
                    const projects = await response.json();
                    
                    const grid = document.getElementById('projects-grid');
                    grid.innerHTML = '';
                    
                    projects.forEach(project => {
                        const card = document.createElement('div');
                        card.className = 'project-card';
                        card.innerHTML = `
                            <h3>${project.name}</h3>
                            <p>${project.description || 'No description'}</p>
                            <p><strong>Files:</strong> ${project.files.length}</p>
                            <p><strong>Chunks:</strong> ${project.chunk_count || project.total_chunks}</p>
                            <p><strong>Status:</strong> ${project.indexed ? '✅ Ready' : '⏳ Processing'}</p>
                            <button class="btn" onclick="openChat('${project.id}')" ${!project.indexed ? 'disabled' : ''}>
                                Chat with Code
                            </button>
                        `;
                        grid.appendChild(card);
                    });
                } catch (error) {
                    showStatus(`Error loading projects: ${error.message}`, 'error');
                }
            }
            
            function openChat(projectId) {
                currentProjectId = projectId;
                currentConversationId = null;
                document.getElementById('chat-section').classList.remove('hidden');
                document.getElementById('messages').innerHTML = '';
                document.getElementById('chat-input').focus();
            }
            
            function handleChatKeypress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
            
            async function sendMessage() {
                const input = document.getElementById('chat-input');
                const message = input.value.trim();
                
                if (!message || !currentProjectId) return;
                
                // Add user message to chat
                addMessage(message, 'user');
                input.value = '';
                
                // Show typing indicator
                const typingId = addMessage('🤔 Thinking...', 'bot');
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            project_id: currentProjectId,
                            message: message,
                            conversation_id: currentConversationId
                        })
                    });
                    
                    if (!response.ok) throw new Error('Failed to get response');
                    
                    const result = await response.json();
                    currentConversationId = result.conversation_id;
                    
                    // Remove typing indicator
                    document.getElementById(typingId).remove();
                    
                    // Add bot response
                    addMessage(result.answer, 'bot');
                    
                } catch (error) {
                    document.getElementById(typingId).remove();
                    addMessage(`Error: ${error.message}`, 'bot');
                }
            }
            
            function addMessage(content, sender) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                const messageId = 'msg-' + Date.now();
                messageDiv.id = messageId;
                messageDiv.className = `message ${sender}-message`;
                messageDiv.innerHTML = content.replace(/\\n/g, '<br>');
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
                return messageId;
            }
            
            function showStatus(message, type) {
                const statusArea = document.getElementById('status-area');
                const statusDiv = document.createElement('div');
                statusDiv.className = `status ${type}`;
                statusDiv.textContent = message;
                statusArea.appendChild(statusDiv);
                
                setTimeout(() => {
                    statusDiv.remove();
                }, 5000);
            }
        </script>
    </body>
    </html>
    """)

@app.get("/api/projects")
async def list_projects():
    """List all projects"""
    projects = project_manager.list_projects()
    return projects

@app.post("/api/projects")
async def create_project(project: ProjectCreate):
    """Create a new project"""
    try:
        project_id = project_manager.create_project(project.name, project.description)
        project_data = project_manager.get_project(project_id)
        return project_data
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/projects/{project_id}/upload")
async def upload_files(project_id: str, files: List[UploadFile] = File(...)):
    """Upload files to a project"""
    try:
        uploaded_files = []
        
        for file in files:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
                content = await file.read()
                tmp_file.write(content)
                tmp_file_path = tmp_file.name
            
            try:
                # Upload to project
                file_data = project_manager.upload_file(project_id, tmp_file_path, file.filename)
                uploaded_files.append(file_data)
            finally:
                # Clean up temp file
                os.unlink(tmp_file_path)
        
        return {"uploaded_files": uploaded_files}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/projects/{project_id}/process")
async def process_project(project_id: str, background_tasks: BackgroundTasks):
    """Process project files (chunk and index)"""
    try:
        # Add background task for processing
        background_tasks.add_task(process_project_background, project_id)
        return {"message": "Processing started", "project_id": project_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/projects/{project_id}")
async def get_project(project_id: str):
    """Get project details"""
    project = project_manager.get_project(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@app.get("/api/projects/{project_id}/status")
async def get_project_status(project_id: str):
    """Get project processing status"""
    # Check if project exists in memory first
    project = project_manager.get_project(project_id)
    if project:
        return {
            "project_id": project_id,
            "processing_status": project.get("processing_status", "idle"),
            "chunk_count": project.get("chunk_count", 0),
            "indexed": project.get("indexed", False),
            "files_count": len(project.get("files", []))
        }
    
    # If not in memory, check if project directory exists
    project_dir = project_manager.projects_dir / project_id
    if not project_dir.exists():
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if project has been processed by looking for enhanced chunks
    enhanced_chunks_file = project_dir / "enhanced_chunks.json"
    chroma_db_dir = project_dir / "chroma_db"
    
    # Determine status based on what exists
    if chroma_db_dir.exists() and any(chroma_db_dir.iterdir()):
        status = "ready"
        indexed = True
    elif enhanced_chunks_file.exists():
        status = "chunked"
        indexed = False
    else:
        status = "idle"
        indexed = False
    
    # Count chunks if file exists
    chunk_count = 0
    if enhanced_chunks_file.exists():
        try:
            import json
            with open(enhanced_chunks_file, 'r') as f:
                chunks = json.load(f)
                chunk_count = len(chunks)
        except:
            chunk_count = 0
    
    # Count files
    files_dir = project_dir / "files"
    files_count = len(list(files_dir.glob("*"))) if files_dir.exists() else 0
    
    return {
        "project_id": project_id,
        "processing_status": status,
        "chunk_count": chunk_count,
        "indexed": indexed,
        "files_count": files_count
    }

@app.delete("/api/projects/{project_id}")
async def delete_project(project_id: str):
    """Delete a project"""
    success = project_manager.delete_project(project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted"}

@app.post("/api/chat")
async def chat_with_code(message: ChatMessage):
    """Chat with code using RAG + LLM"""
    try:
        # Start conversation if needed
        if not message.conversation_id:
            conversation_id = conversation_manager.start_conversation(message.project_id)
        else:
            conversation_id = message.conversation_id
        
        # Add user message to conversation
        conversation_manager.add_message(conversation_id, "user", message.message)
        
        # Get RAG response
        try:
            model = LLMProvider(message.model)
        except ValueError:
            model = LLMProvider.CLAUDE_3_5_SONNET
        
        rag_response = await rag_system.query_with_rag(
            project_id=message.project_id,
            question=message.message,
            model=model,
            max_chunks=message.max_chunks,
            use_local_embeddings=True
        )
        
        # Add bot response to conversation
        conversation_manager.add_message(
            conversation_id, 
            "assistant", 
            rag_response.answer,
            metadata={
                "sources": rag_response.sources,
                "model": rag_response.model_used,
                "tokens": rag_response.tokens_used
            }
        )
        
        return ChatResponse(
            answer=rag_response.answer,
            sources=rag_response.sources,
            conversation_id=conversation_id,
            model_used=rag_response.model_used,
            tokens_used=rag_response.tokens_used
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/models")
async def get_available_models():
    """Get available LLM models"""
    models = rag_system.get_available_models()
    model_info = rag_system.get_model_info()
    
    return {
        "available": [model.value for model in models],
        "info": model_info
    }

# Conversation management endpoints
@app.get("/api/projects/{project_id}/conversations")
async def get_project_conversations(project_id: str):
    """Get all conversations for a project"""
    try:
        conversations = conversation_manager.get_project_conversations(project_id)
        
        # Format conversations for ThreadList
        formatted_conversations = []
        for conv_id, messages in conversations.items():
            if messages:
                # Get the first user message as title
                title = "New Chat"
                for msg in messages:
                    if msg.get("role") == "user" and msg.get("content"):
                        title = msg["content"][:50] + ("..." if len(msg["content"]) > 50 else "")
                        break
                
                formatted_conversations.append({
                    "id": conv_id,
                    "title": title,
                    "createdAt": messages[0].get("timestamp", ""),
                    "updatedAt": messages[-1].get("timestamp", ""),
                    "messageCount": len(messages)
                })
        
        # Sort by most recent first
        formatted_conversations.sort(key=lambda x: x["updatedAt"], reverse=True)
        
        return {"conversations": formatted_conversations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/projects/{project_id}/conversations")
async def create_conversation(project_id: str):
    """Create a new conversation for a project"""
    try:
        conversation_id = conversation_manager.start_conversation(project_id)
        return {"conversation_id": conversation_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/projects/{project_id}/conversations/{conversation_id}")
async def get_specific_conversation(project_id: str, conversation_id: str):
    """Get a specific conversation"""
    try:
        messages = conversation_manager.get_conversation(conversation_id)
        if not messages:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return {"messages": messages}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/projects/{project_id}/conversations/{conversation_id}")
async def delete_conversation(project_id: str, conversation_id: str):
    """Delete a conversation"""
    try:
        success = conversation_manager.delete_conversation(conversation_id)
        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/projects/{project_id}/conversations/{conversation_id}/rename")
async def rename_conversation(project_id: str, conversation_id: str, title: str = Form(...)):
    """Rename a conversation"""
    try:
        # For now, we'll store the title in the conversation metadata
        # This is a simplified implementation - in production you'd want proper metadata storage
        return {"success": True, "title": title}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/projects/{project_id}/conversations/{conversation_id}/archive")
async def archive_conversation(project_id: str, conversation_id: str):
    """Archive a conversation"""
    try:
        # For now, this is a placeholder - in production you'd want proper archiving
        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/conversations/{conversation_id}")
async def get_conversation(conversation_id: str):
    """Get conversation history"""
    conversation = conversation_manager.get_conversation(conversation_id)
    return {"conversation": conversation}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)