import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import axios from 'axios';

/**
 * Advanced React Component demonstrating modern patterns
 * This component showcases hooks, performance optimization, and API integration
 */
const AdvancedDataTable = ({ 
  apiEndpoint, 
  initialFilters = {}, 
  pageSize = 20,
  enableVirtualization = false,
  onRowClick,
  customColumns = []
}) => {
  // State management with multiple pieces of state
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filters, setFilters] = useState(initialFilters);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRows, setSelectedRows] = useState(new Set());

  // Memoized computed values
  const filteredData = useMemo(() => {
    if (!data.length) return [];
    
    let filtered = data.filter(item => {
      // Apply search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = Object.values(item).some(value => 
          String(value).toLowerCase().includes(searchLower)
        );
        if (!matchesSearch) return false;
      }
      
      // Apply custom filters
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        return item[key] === value;
      });
    });

    // Apply sorting
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.key];
        const bVal = b[sortConfig.key];
        
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [data, searchTerm, filters, sortConfig]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchTerm(term);
      setCurrentPage(1); // Reset to first page on search
    }, 300),
    []
  );

  // API call function with error handling
  const fetchData = useCallback(async (page = 1, additionalParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = {
        page,
        limit: pageSize,
        ...filters,
        ...additionalParams
      };
      
      const response = await axios.get(apiEndpoint, { params });
      
      if (response.data && response.data.items) {
        setData(response.data.items);
        setTotalPages(Math.ceil(response.data.total / pageSize));
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch data');
      console.error('Data fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [apiEndpoint, pageSize, filters]);

  // Effect for initial data load and filter changes
  useEffect(() => {
    fetchData(currentPage);
  }, [fetchData, currentPage]);

  // Effect for handling filter changes
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      setCurrentPage(1);
      fetchData(1);
    }
  }, [filters, fetchData]);

  // Handlers
  const handleSort = useCallback((key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const handleFilterChange = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const handleRowSelection = useCallback((rowId, isSelected) => {
    setSelectedRows(prev => {
      const newSet = new Set(prev);
      if (isSelected) {
        newSet.add(rowId);
      } else {
        newSet.delete(rowId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((isSelected) => {
    if (isSelected) {
      setSelectedRows(new Set(filteredData.map(item => item.id)));
    } else {
      setSelectedRows(new Set());
    }
  }, [filteredData]);

  const handlePageChange = useCallback((newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  }, [totalPages]);

  // Render helpers
  const renderTableHeader = () => (
    <thead className="table-header">
      <tr>
        <th>
          <input
            type="checkbox"
            checked={selectedRows.size === filteredData.length && filteredData.length > 0}
            onChange={(e) => handleSelectAll(e.target.checked)}
          />
        </th>
        {customColumns.map(column => (
          <th 
            key={column.key}
            onClick={() => column.sortable && handleSort(column.key)}
            className={column.sortable ? 'sortable' : ''}
          >
            {column.label}
            {sortConfig.key === column.key && (
              <span className="sort-indicator">
                {sortConfig.direction === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </th>
        ))}
      </tr>
    </thead>
  );

  const renderTableBody = () => (
    <tbody>
      {filteredData.map((row, index) => (
        <tr 
          key={row.id || index}
          className={selectedRows.has(row.id) ? 'selected' : ''}
          onClick={() => onRowClick && onRowClick(row)}
        >
          <td>
            <input
              type="checkbox"
              checked={selectedRows.has(row.id)}
              onChange={(e) => handleRowSelection(row.id, e.target.checked)}
              onClick={(e) => e.stopPropagation()}
            />
          </td>
          {customColumns.map(column => (
            <td key={column.key}>
              {column.render ? column.render(row[column.key], row) : row[column.key]}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  );

  const renderPagination = () => (
    <div className="pagination">
      <button 
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </button>
      
      <span className="page-info">
        Page {currentPage} of {totalPages}
      </span>
      
      <button 
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        Next
      </button>
    </div>
  );

  // Main render
  return (
    <div className="advanced-data-table">
      <div className="table-controls">
        <input
          type="text"
          placeholder="Search..."
          onChange={(e) => debouncedSearch(e.target.value)}
          className="search-input"
        />
        
        <div className="filter-controls">
          {Object.keys(initialFilters).map(filterKey => (
            <select
              key={filterKey}
              value={filters[filterKey] || ''}
              onChange={(e) => handleFilterChange(filterKey, e.target.value)}
            >
              <option value="">All {filterKey}</option>
              {/* Options would be populated based on data */}
            </select>
          ))}
        </div>
      </div>

      {error && (
        <div className="error-message">
          Error: {error}
          <button onClick={() => fetchData(currentPage)}>Retry</button>
        </div>
      )}

      {loading ? (
        <div className="loading-spinner">Loading...</div>
      ) : (
        <>
          <table className="data-table">
            {renderTableHeader()}
            {renderTableBody()}
          </table>
          
          {totalPages > 1 && renderPagination()}
        </>
      )}

      <div className="table-footer">
        <span>
          {selectedRows.size} of {filteredData.length} rows selected
        </span>
      </div>
    </div>
  );
};

export default AdvancedDataTable;